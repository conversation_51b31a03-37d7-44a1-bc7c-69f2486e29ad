<template>
    <div class="validation-result" :class="{ compact: compact }">
      <!-- 加载状态 -->
      <div v-if="loading" class="result-loading">
        <el-skeleton :rows="3" animated />
        <div class="loading-text">正在验证盲收权限...</div>
      </div>
  
      <!-- 验证结果 -->
      <div v-else-if="result" class="result-content">
        <!-- 结果头部 -->
        <div class="result-header">
          <div class="result-status" :class="getStatusClass()">
            <el-icon class="status-icon" :class="getStatusIconClass()">
              <component :is="getStatusIcon()" />
            </el-icon>
            <div class="status-content">
              <h4 class="status-title">{{ getStatusTitle() }}</h4>
              <p class="status-message">{{ result.validationMessage }}</p>
            </div>
          </div>
          
          <div class="result-actions" v-if="!compact">
            <el-button
              size="small"
              :icon="Refresh"
              @click="handleRetry"
            >
              重新验证
            </el-button>
            <el-button
              size="small"
              :icon="View"
              @click="handleViewConfig"
              v-if="hasPermission('wms:blind-config:view')"
            >
              查看配置
            </el-button>
          </div>
        </div>
  
        <!-- 详细信息 -->
        <div class="result-details" v-if="!compact">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="验证策略">
              <el-tag
                :type="getStrategyTagType(result.strategy)"
                size="small"
                effect="plain"
              >
                {{ getStrategyName(result.strategy) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="配置层级">
              <el-tag
                :type="getConfigLevelTagType(result.configLevel)"
                size="small"
                effect="plain"
              >
                {{ getConfigLevelName(result.configLevel) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="审批要求" v-if="result.requiresApproval">
              <el-tag type="warning" size="small" effect="plain">
                需要审批
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="数量限制" v-if="result.maxQuantityLimit">
              <span>最大 {{ formatNumber(result.maxQuantityLimit) }}</span>
            </el-descriptions-item>
            
            <el-descriptions-item label="补录截止时间" v-if="result.supplementDeadline">
              <span class="deadline-time">
                {{ formatDateTime(result.supplementDeadline) }}
              </span>
            </el-descriptions-item>
            
            <el-descriptions-item label="审批角色" v-if="result.approvalUserRoles?.length">
              <div class="role-tags">
                <el-tag
                  v-for="role in result.approvalUserRoles"
                  :key="role"
                  size="small"
                  effect="plain"
                >
                  {{ role }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
  
        <!-- 操作指引 -->
        <div class="result-guidance" v-if="!result.isAllowed || result.requiresApproval">
          <div class="guidance-header">
            <el-icon class="guidance-icon"><InfoFilled /></el-icon>
            <span class="guidance-title">操作指引</span>
          </div>
          
          <div class="guidance-content">
            <div v-if="!result.isAllowed" class="guidance-item forbidden">
              <el-icon class="item-icon"><Warning /></el-icon>
              <div class="item-content">
                <div class="item-title">禁止盲收</div>
                <div class="item-desc">
                  根据当前配置策略，不允许进行盲收操作。请先创建对应的入库通知单。
                </div>
              </div>
            </div>
            
            <div v-else-if="result.requiresApproval" class="guidance-item approval">
              <el-icon class="item-icon"><User /></el-icon>
              <div class="item-content">
                <div class="item-title">需要审批</div>
                <div class="item-desc">
                  请联系以下角色的用户进行审批：
                  <span class="approval-roles">{{ result.approvalUserRoles?.join('、') }}</span>
                </div>
              </div>
            </div>
            
            <div v-if="result.strategy === 'SUPPLEMENT'" class="guidance-item supplement">
              <el-icon class="item-icon"><Clock /></el-icon>
              <div class="item-content">
                <div class="item-title">补录提醒</div>
                <div class="item-desc">
                  请在 {{ formatDateTime(result.supplementDeadline) }} 前完成入库通知单的补录工作。
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 成功指引 -->
        <div class="result-guidance success" v-else-if="result.isAllowed">
          <div class="guidance-header">
            <el-icon class="guidance-icon success"><SuccessFilled /></el-icon>
            <span class="guidance-title">允许盲收</span>
          </div>
          
          <div class="guidance-content">
            <div class="guidance-item">
              <el-icon class="item-icon"><Check /></el-icon>
              <div class="item-content">
                <div class="item-title">可以进行盲收操作</div>
                <div class="item-desc">
                  根据当前配置策略，允许进行盲收操作。
                  <span v-if="result.strategy === 'SUPPLEMENT'">
                    请记得在规定时间内补录入库通知单。
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 流程状态 -->
        <div class="result-process" v-if="hasProcessInfo()">
          <div class="process-header">
            <span class="process-title">流程状态</span>
          </div>
          
          <div class="process-content">
            <div class="process-item" v-if="result.approvalStatus">
              <span class="process-label">审批状态:</span>
              <el-tag
                :type="getApprovalStatusTagType(result.approvalStatus)"
                size="small"
                effect="plain"
              >
                {{ getApprovalStatusName(result.approvalStatus) }}
              </el-tag>
            </div>
            
            <div class="process-item" v-if="result.supplementStatus">
              <span class="process-label">补录状态:</span>
              <el-tag
                :type="getSupplementStatusTagType(result.supplementStatus)"
                size="small"
                effect="plain"
              >
                {{ getSupplementStatusName(result.supplementStatus) }}
              </el-tag>
            </div>
            
            <div class="process-item" v-if="result.processedAt">
              <span class="process-label">处理时间:</span>
              <span>{{ formatDateTime(result.processedAt) }}</span>
            </div>
            
            <div class="process-item" v-if="result.processedByName">
              <span class="process-label">处理人:</span>
              <span>{{ result.processedByName }}</span>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 无结果状态 -->
      <div v-else class="result-empty">
        <el-empty
          description="暂无验证结果"
          :image-size="compact ? 60 : 100"
        >
          <el-button type="primary" @click="handleRetry">
            开始验证
          </el-button>
        </el-empty>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'
  import {
    Refresh,
    View,
    InfoFilled,
    Warning,
    User,
    Clock,
    SuccessFilled,
    Check,
    CircleCheckFilled,
    CircleCloseFilled,
    WarningFilled
  } from '@element-plus/icons-vue'
  import { usePermission } from '@/hooks/usePermission'
  import type {
    BlindReceivingValidationVO,
    BlindReceivingStrategy,
    ConfigLevel,
    ApprovalStatus,
    SupplementStatus
  } from '@/types/wms/blindReceivingConfig'
  import {
    CONFIG_LEVEL_OPTIONS,
    STRATEGY_OPTIONS,
    APPROVAL_STATUS_OPTIONS,
    SUPPLEMENT_STATUS_OPTIONS
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    result?: BlindReceivingValidationVO | null
    loading?: boolean
    compact?: boolean
  }
  
  const props = withDefaults(defineProps<Props>(), {
    result: null,
    loading: false,
    compact: false
  })
  
  // Emits
  const emit = defineEmits<{
    retry: []
    'view-config': []
  }>()
  
  // 权限检查
  const { hasPermission } = usePermission()
  
  // 工具函数
  const getStatusClass = () => {
    if (!props.result) return ''
    
    if (!props.result.isAllowed) return 'status-forbidden'
    if (props.result.requiresApproval) return 'status-approval'
    return 'status-allowed'
  }
  
  const getStatusIconClass = () => {
    if (!props.result) return ''
    
    if (!props.result.isAllowed) return 'status-icon-forbidden'
    if (props.result.requiresApproval) return 'status-icon-warning'
    return 'status-icon-success'
  }
  
  const getStatusIcon = () => {
    if (!props.result) return CircleCheckFilled
    
    if (!props.result.isAllowed) return CircleCloseFilled
    if (props.result.requiresApproval) return WarningFilled
    return CircleCheckFilled
  }
  
  const getStatusTitle = () => {
    if (!props.result) return ''
    
    if (!props.result.isAllowed) return '禁止盲收'
    if (props.result.requiresApproval) return '需要审批'
    return '允许盲收'
  }
  
  const getStrategyName = (strategy: BlindReceivingStrategy) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: BlindReceivingStrategy) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getConfigLevelName = (level: ConfigLevel) => {
    return CONFIG_LEVEL_OPTIONS.find(item => item.value === level)?.label || level
  }
  
  const getConfigLevelTagType = (level: ConfigLevel) => {
    const typeMap = {
      'SYSTEM': 'info',
      'WAREHOUSE': 'warning',
      'CLIENT': 'success',
      'USER': 'primary'
    }
    return typeMap[level] || 'info'
  }
  
  const getApprovalStatusName = (status: ApprovalStatus) => {
    return APPROVAL_STATUS_OPTIONS.find(item => item.value === status)?.label || status
  }
  
  const getApprovalStatusTagType = (status: ApprovalStatus) => {
    return APPROVAL_STATUS_OPTIONS.find(item => item.value === status)?.color || 'info'
  }
  
  const getSupplementStatusName = (status: SupplementStatus) => {
    return SUPPLEMENT_STATUS_OPTIONS.find(item => item.value === status)?.label || status
  }
  
  const getSupplementStatusTagType = (status: SupplementStatus) => {
    return SUPPLEMENT_STATUS_OPTIONS.find(item => item.value === status)?.color || 'info'
  }
  
  const formatNumber = (num: number) => {
    return num.toLocaleString()
  }
  
  const formatDateTime = (dateStr: string) => {
    return new Date(dateStr).toLocaleString()
  }
  
  const hasProcessInfo = () => {
    return props.result && (
      props.result.approvalStatus ||
      props.result.supplementStatus ||
      props.result.processedAt ||
      props.result.processedByName
    )
  }
  
  // 事件处理
  const handleRetry = () => {
    emit('retry')
  }
  
  const handleViewConfig = () => {
    emit('view-config')
  }
  </script>
  
  <style scoped lang="scss">
  .validation-result {
    .result-loading {
      text-align: center;
      padding: 20px;
      
      .loading-text {
        margin-top: 12px;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .result-content {
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        
        .result-status {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          flex: 1;
          
          .status-icon {
            font-size: 24px;
            margin-top: 2px;
            
            &.status-icon-success {
              color: #67c23a;
            }
            
            &.status-icon-warning {
              color: #e6a23c;
            }
            
            &.status-icon-forbidden {
              color: #f56c6c;
            }
          }
          
          .status-content {
            flex: 1;
            
            .status-title {
              margin: 0 0 4px 0;
              color: #303133;
              font-size: 16px;
              font-weight: 600;
            }
            
            .status-message {
              margin: 0;
              color: #606266;
              font-size: 14px;
              line-height: 1.4;
            }
          }
        }
        
        .result-actions {
          display: flex;
          gap: 8px;
          flex-shrink: 0;
        }
      }
      
      .result-details {
        margin-bottom: 16px;
        
        .deadline-time {
          color: #e6a23c;
          font-weight: 500;
        }
        
        .role-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
      }
      
      .result-guidance {
        border: 1px solid #ebeef5;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 16px;
        
        &.success {
          background-color: #f0f9ff;
          border-color: #b3e5fc;
        }
        
        .guidance-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          
          .guidance-icon {
            font-size: 18px;
            
            &.success {
              color: #67c23a;
            }
          }
          
          .guidance-title {
            font-weight: 600;
            color: #303133;
          }
        }
        
        .guidance-content {
          .guidance-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .item-icon {
              font-size: 16px;
              margin-top: 2px;
              color: #909399;
            }
            
            .item-content {
              flex: 1;
              
              .item-title {
                font-weight: 500;
                color: #303133;
                margin-bottom: 4px;
              }
              
              .item-desc {
                color: #606266;
                font-size: 14px;
                line-height: 1.4;
                
                .approval-roles {
                  color: #e6a23c;
                  font-weight: 500;
                }
              }
            }
            
            &.forbidden .item-icon {
              color: #f56c6c;
            }
            
            &.approval .item-icon {
              color: #e6a23c;
            }
            
            &.supplement .item-icon {
              color: #409eff;
            }
          }
        }
      }
      
      .result-process {
        border-top: 1px solid #ebeef5;
        padding-top: 16px;
        
        .process-header {
          margin-bottom: 12px;
          
          .process-title {
            font-weight: 600;
            color: #303133;
          }
        }
        
        .process-content {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
          
          .process-item {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .process-label {
              color: #909399;
              font-size: 14px;
              min-width: 70px;
            }
          }
        }
      }
    }
    
    .result-empty {
      text-align: center;
      padding: 40px 20px;
    }
    
    // 紧凑模式样式
    &.compact {
      .result-loading {
        padding: 12px;
        
        .loading-text {
          margin-top: 8px;
          font-size: 13px;
        }
      }
      
      .result-content {
        .result-header {
          margin-bottom: 12px;
          
          .result-status {
            .status-icon {
              font-size: 20px;
            }
            
            .status-content {
              .status-title {
                font-size: 14px;
              }
              
              .status-message {
                font-size: 13px;
              }
            }
          }
        }
        
        .result-guidance {
          padding: 12px;
          margin-bottom: 12px;
          
          .guidance-header {
            margin-bottom: 8px;
            
            .guidance-icon {
              font-size: 16px;
            }
          }
          
          .guidance-content .guidance-item {
            margin-bottom: 8px;
            
            .item-icon {
              font-size: 14px;
            }
            
            .item-content {
              .item-title {
                font-size: 13px;
                margin-bottom: 2px;
              }
              
              .item-desc {
                font-size: 13px;
              }
            }
          }
        }
        
        .result-process {
          padding-top: 12px;
          
          .process-content {
            gap: 8px;
            
            .process-item {
              .process-label {
                font-size: 13px;
                min-width: 60px;
              }
            }
          }
        }
      }
      
      .result-empty {
        padding: 20px 12px;
      }
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .validation-result {
      .result-content {
        .result-header {
          flex-direction: column;
          gap: 12px;
          
          .result-actions {
            align-self: stretch;
            justify-content: center;
          }
        }
        
        .result-process .process-content {
          grid-template-columns: 1fr;
        }
      }
    }
  }
  </style>