package service

import (
	"context"
	std_errors "errors"
	"fmt"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"backend/internal/model/vo"
	"backend/pkg/config"
	"backend/pkg/constant"
	"backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/util"
)

// TokenService JWT 令牌服务接口
type TokenService interface {
	// GenerateTokens 生成访问令牌和刷新令牌
	GenerateTokens(ctx context.Context, userID uint, username string, roleIDs []uint, rememberMe bool, isAdmin bool) (*vo.TokenVO, error)

	// ParseAccessToken 解析访问令牌
	ParseAccessToken(ctx context.Context, tokenString string) (*util.CustomClaims, error)

	// ParseRefreshToken 解析并验证刷新令牌（包括缓存检查）
	ParseRefreshToken(ctx context.Context, tokenString string) (uint, error) // Returns UserID

	// RevokeRefreshToken 使指定的刷新令牌失效
	RevokeRefreshToken(ctx context.Context, tokenString string) error

	// GetServiceManager 获取服务管理器 (辅助方法，方便中间件访问)
	GetServiceManager() *ServiceManager
}

// CustomClaims defines the structure for JWT claims.
// Duplicated here for clarity, should ideally use util.CustomClaims
// type CustomClaims struct {
// 	UserID   uint   `json:"userId"`
// 	Username string `json:"username"`
// 	RoleIDs  []uint `json:"roleIds"`
// 	Remember bool   `json:"remember"`
// 	IsAdmin  bool   `json:"isAdmin"`
// 	jwt.RegisteredClaims
// }

// TokenServiceImpl JWT 令牌服务实现
type TokenServiceImpl struct {
	*BaseServiceImpl // Embed BaseServiceImpl for access to ServiceManager
	appCfg           *config.Configuration
	jwtCfg           *config.JWTConfig
}

// NewTokenService 创建令牌服务实例
func NewTokenService(serviceManager *ServiceManager, appCfg *config.Configuration) TokenService {
	return &TokenServiceImpl{
		BaseServiceImpl: NewBaseService(serviceManager),
		appCfg:          appCfg,
		jwtCfg:          &appCfg.JWT,
	}
}

// GetServiceManager returns the embedded service manager.
func (s *TokenServiceImpl) GetServiceManager() *ServiceManager {
	return s.BaseServiceImpl.GetServiceManager()
}

// GenerateTokens generates both access and refresh tokens and stores refresh token state.
func (s *TokenServiceImpl) GenerateTokens(ctx context.Context, userID uint, username string, roleIDs []uint, rememberMe bool, isAdmin bool) (*vo.TokenVO, error) {
	log := logger.WithContext(ctx)
	now := time.Now()

	// --- Access Token ---
	accessExpireDuration := time.Duration(s.jwtCfg.Expire) * time.Hour
	accessExpireTime := now.Add(accessExpireDuration)
	accessClaims := util.CustomClaims{ // Use util.CustomClaims
		UserID:   userID,
		Username: username,
		RoleIDs:  roleIDs,
		Remember: rememberMe,
		IsAdmin:  isAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(accessExpireTime),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    s.jwtCfg.Issuer,
			Subject:   fmt.Sprintf("%d", userID),
		},
	}
	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString([]byte(s.jwtCfg.Secret))
	if err != nil {
		log.Error("生成 Access Token 失败", logger.WithError(err))
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "生成访问令牌失败").WithCause(err)
	}

	// --- Refresh Token ---
	var refreshExpireDuration time.Duration
	if rememberMe {
		refreshExpireDuration = time.Duration(s.jwtCfg.RefreshExpire) * 24 * time.Hour
		log.Debugf("生成长效 Refresh Token (RememberMe=true), 有效期: %d 天", s.jwtCfg.RefreshExpire)
	} else {
		refreshExpireDuration = time.Duration(s.jwtCfg.Expire) * time.Hour
		log.Debugf("生成标准 Refresh Token (RememberMe=false), 有效期: %d 小时", s.jwtCfg.Expire)
	}
	refreshExpireTime := now.Add(refreshExpireDuration)

	// Use util function to generate refresh token string
	refreshTokenString, err := util.GenerateRefreshToken(userID, rememberMe) // Pass rememberMe
	if err != nil {
		log.Error("生成 Refresh Token 失败", logger.WithError(err))
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "生成刷新令牌失败").WithCause(err)
	}

	// <<< Store Refresh Token in Cache >>>
	cacheService := s.GetServiceManager().GetCacheService()
	if cacheService != nil {
		// Use a simple key format for now
		key := "refresh_token:" + refreshTokenString
		ttl := refreshExpireTime.Sub(now) // Calculate TTL based on expiry time

		// Store userID as string in cache
		userIDStr := strconv.FormatUint(uint64(userID), 10)

		cacheErr := cacheService.Set(ctx, key, []byte(userIDStr), ttl)
		if cacheErr != nil {
			// Log warning, but don't fail the token generation
			log.Warn("存储 Refresh Token 到缓存失败", logger.WithError(cacheErr), logger.WithField("key", key))
		}
	} else {
		log.Warn("CacheService 不可用，无法存储 Refresh Token 状态")
	}
	// <<< End Store Refresh Token >>>

	// 返回包含两个 Token 的结构
	tokenVO := &vo.TokenVO{
		AccessToken:     accessTokenString,
		RefreshToken:    refreshTokenString,
		AccessExpiresAt: accessExpireTime.UnixMilli(),
	}

	log.Info("令牌对生成成功", logger.WithField("userId", userID))
	return tokenVO, nil
}

// ParseAccessToken 实现解析访问令牌的逻辑
func (s *TokenServiceImpl) ParseAccessToken(ctx context.Context, tokenString string) (*util.CustomClaims, error) {
	log := logger.WithContext(ctx)
	log.Debug("开始解析访问令牌")

	claims, err := util.ParseToken(tokenString)
	if err != nil {
		// Let util.ParseToken handle logging and error types (like ErrExpiredToken)
		return claims, err // Return claims even if expired
	}

	log.Debug("访问令牌解析成功", logger.WithField("userId", claims.UserID))
	return claims, nil
}

// ParseRefreshToken checks cache first, then validates the JWT itself.
func (s *TokenServiceImpl) ParseRefreshToken(ctx context.Context, tokenString string) (uint, error) {
	log := logger.WithContext(ctx)
	log.Debug("开始解析和验证刷新令牌")

	cacheService := s.GetServiceManager().GetCacheService()
	if cacheService == nil {
		log.Error("CacheService 不可用，无法验证 Refresh Token 状态")
		return 0, errors.NewSystemError(errors.CODE_SYSTEM_SERVICE_UNAVAILABLE, "缓存服务不可用")
	}

	key := "refresh_token:" + tokenString

	// 1. Check cache first
	cacheValue, found, cacheErr := cacheService.Get(ctx, key)
	if cacheErr != nil {
		log.Error("验证刷新令牌缓存失败", logger.WithError(cacheErr))
		// Wrap the cache error appropriately
		return 0, errors.WrapError(cacheErr, errors.CODE_SYSTEM_CACHE_ERROR, "验证刷新令牌缓存失败")
	}
	if !found {
		log.Warn("刷新令牌在缓存中未找到", logger.WithField("key_prefix", "refresh_token:"))
		return 0, errors.ErrAuthTokenInvalid // Treat not found as invalid
	}

	// 2. Convert cached value (should be userID as bytes)
	var cachedUserID uint
	if userIDBytes, ok := cacheValue.([]byte); ok {
		parsedID, parseErr := strconv.ParseUint(string(userIDBytes), 10, 64)
		if parseErr != nil {
			log.Error("无法解析缓存中的用户ID", logger.WithError(parseErr), logger.WithField("value", string(userIDBytes)))
			_ = cacheService.Delete(ctx, key) // Best effort delete corrupted cache entry
			return 0, errors.ErrAuthTokenInvalid
		}
		cachedUserID = uint(parsedID)
	} else {
		// Try converting from string if it's stored as string (e.g., Redis Get result)
		if valStr, okStr := cacheValue.(string); okStr {
			parsedID, parseErr := strconv.ParseUint(valStr, 10, 64)
			if parseErr != nil {
				log.Error("无法解析缓存中的用户ID (string)", logger.WithError(parseErr), logger.WithField("value", valStr))
				_ = cacheService.Delete(ctx, key) // Best effort delete
				return 0, errors.ErrAuthTokenInvalid
			}
			cachedUserID = uint(parsedID)
		} else {
			// 如果类型既不是 []byte 也不是 string，则记录错误
			log.Error("缓存中的用户ID类型不正确", logger.WithField("type", fmt.Sprintf("%T", cacheValue)))
			_ = cacheService.Delete(ctx, key) // Best effort delete corrupted cache entry
			return 0, errors.ErrAuthTokenInvalid
		}
	}

	// 3. Mandatory JWT validation
	jwtUserID, jwtErr := util.ParseRefreshToken(tokenString)
	if jwtErr != nil {
		log.Warn("刷新令牌缓存存在但 JWT 验证失败", logger.WithError(jwtErr), logger.WithField("cachedUserID", cachedUserID))
		delErr := cacheService.Delete(ctx, key)
		if delErr != nil {
			log.Warn("清理无效刷新令牌缓存失败", logger.WithError(delErr), logger.WithField("key_prefix", "refresh_token:"))
		}
		// Return specific JWT error type
		if errors.IsError(jwtErr, constant.TOKEN_EXPIRED) || std_errors.Is(jwtErr, util.ErrExpiredToken) {
			return 0, errors.ErrAuthTokenExpired
		}
		// Use the original JWT error if it's already a CustomError, otherwise wrap it
		if _, ok := jwtErr.(*errors.CustomError); ok {
			return 0, jwtErr // Return original CustomError
		} else {
			return 0, errors.WrapError(jwtErr, errors.CODE_AUTH_TOKEN_INVALID, "刷新令牌 JWT 验证失败")
		}
	}

	// 4. Cross-validate UserID
	if cachedUserID != jwtUserID {
		log.Error("缓存的用户ID与JWT声明中的用户ID不匹配", logger.WithField("cachedUserID", cachedUserID), logger.WithField("jwtUserID", jwtUserID))
		_ = cacheService.Delete(ctx, key) // Data inconsistency, clear cache
		return 0, errors.ErrAuthTokenInvalid
	}

	log.Debug("刷新令牌缓存和 JWT 验证成功", logger.WithField("userId", cachedUserID))
	return cachedUserID, nil
}

// RevokeRefreshToken removes the refresh token from the cache.
func (s *TokenServiceImpl) RevokeRefreshToken(ctx context.Context, tokenString string) error {
	log := logger.WithContext(ctx)
	log.Debug("开始撤销刷新令牌")

	cacheService := s.GetServiceManager().GetCacheService()
	if cacheService == nil {
		log.Error("CacheService 不可用，无法撤销 Refresh Token")
		return errors.NewSystemError(errors.CODE_SYSTEM_SERVICE_UNAVAILABLE, "缓存服务不可用")
	}

	key := "refresh_token:" + tokenString

	err := cacheService.Delete(ctx, key)
	if err != nil {
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			log.Warn("尝试撤销的刷新令牌在缓存中未找到", logger.WithField("key_prefix", "refresh_token:"))
			return nil // Not found is acceptable for revoke
		}
		log.Error("撤销刷新令牌缓存失败", logger.WithError(err))
		return errors.WrapError(err, errors.CODE_SYSTEM_CACHE_ERROR, "撤销刷新令牌缓存失败")
	}

	log.Info("刷新令牌已成功撤销", logger.WithField("key_prefix", "refresh_token:"))
	return nil
}
