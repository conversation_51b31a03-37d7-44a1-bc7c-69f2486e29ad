<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="picking-dialog-container" v-if="currentDetail">
      <!-- 物料信息 -->
      <el-card class="item-info-card" shadow="never">
        <template #header>
          <span>物料信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">物料编码:</span>
              <span class="value">{{ currentDetail.itemCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">物料名称:</span>
              <span class="value">{{ currentDetail.itemName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">规格:</span>
              <span class="value">{{ currentDetail.itemSpec || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">源库位:</span>
              <span class="value">{{ currentDetail.sourceLocationCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">批次号:</span>
              <span class="value">{{ currentDetail.batchNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">单位:</span>
              <span class="value">{{ currentDetail.unitOfMeasure || 'PCS' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 拣货信息 -->
      <el-card class="picking-info-card" shadow="never">
        <template #header>
          <span>拣货信息</span>
        </template>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="需求数量">
                <el-input
                  :value="currentDetail.requiredQty"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="已拣数量">
                <el-input
                  :value="currentDetail.pickedQty || 0"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="剩余数量">
                <el-input
                  :value="currentDetail.requiredQty - (currentDetail.pickedQty || 0)"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="本次拣货数量" prop="pickingQty">
                <el-input-number
                  v-model="formData.pickingQty"
                  :min="0"
                  :max="maxPickingQty"
                  :precision="0"
                  style="width: 100%"
                  placeholder="请输入拣货数量"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目标库位" prop="targetLocationCode">
                <div class="input-with-button">
                  <el-input
                    v-model="formData.targetLocationCode"
                    placeholder="请选择目标库位"
                    readonly
                  />
                  <el-button @click="handleSelectLocation">
                    选择
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 拣货历史 -->
      <el-card class="picking-history-card" shadow="never" v-if="pickingHistory.length > 0">
        <template #header>
          <span>拣货历史</span>
        </template>
        
        <el-table :data="pickingHistory" size="small">
          <el-table-column prop="pickingTime" label="拣货时间" width="150" />
          <el-table-column prop="pickingQty" label="拣货数量" width="100" />
          <el-table-column prop="targetLocationCode" label="目标库位" width="120" />
          <el-table-column prop="operator" label="操作人" width="100" />
          <el-table-column prop="remark" label="备注" />
        </el-table>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="!formData.pickingQty || formData.pickingQty <= 0"
        >
          确认拣货
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElInputNumber, ElTable, ElTableColumn } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { WmsPickingTaskDetailResp } from '@/api/wms/pickingTask'

// 组件接口定义
interface PickingDialogEmits {
  confirm: [data: any]
  cancel: []
}

// Emits
const emit = defineEmits<PickingDialogEmits>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const mode = ref<'pick' | 'adjust'>('pick')
const currentDetail = ref<WmsPickingTaskDetailResp | null>(null)
const pickingHistory = ref<any[]>([])

// 表单数据
const formData = reactive({
  pickingQty: 0,
  targetLocationCode: '',
  remark: ''
})

// 表单验证规则
const formRules: FormRules = {
  pickingQty: [
    { required: true, message: '请输入拣货数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '拣货数量必须大于0', trigger: 'blur' }
  ],
  targetLocationCode: [
    { required: true, message: '请选择目标库位', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return mode.value === 'adjust' ? '调整拣货' : '确认拣货'
})

const maxPickingQty = computed(() => {
  if (!currentDetail.value) return 0
  const remaining = currentDetail.value.requiredQty - (currentDetail.value.pickedQty || 0)
  return mode.value === 'adjust' ? currentDetail.value.requiredQty : remaining
})

// 表单引用
const formRef = ref<FormInstance>()

// 方法
const loadPickingHistory = async (detailId: number) => {
  try {
    // 这里应该调用API获取拣货历史
    pickingHistory.value = []
  } catch (error) {
    console.error('加载拣货历史失败:', error)
  }
}

const handleOpen = () => {
  if (currentDetail.value) {
    // 设置默认值
    if (mode.value === 'pick') {
      formData.pickingQty = currentDetail.value.requiredQty - (currentDetail.value.pickedQty || 0)
    } else {
      formData.pickingQty = currentDetail.value.pickedQty || 0
    }
    formData.targetLocationCode = currentDetail.value.targetLocationCode || ''
    formData.remark = ''
    
    // 加载拣货历史
    loadPickingHistory(currentDetail.value.id)
  }
}

const handleClose = () => {
  resetForm()
}

const resetForm = () => {
  formData.pickingQty = 0
  formData.targetLocationCode = ''
  formData.remark = ''
  formRef.value?.clearValidate()
}

const handleSelectLocation = () => {
  // 打开库位选择器
  ElMessage.info('库位选择功能开发中...')
}

const handleConfirm = async () => {
  if (!formRef.value || !currentDetail.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const resultData = {
      detailId: currentDetail.value.id,
      pickingQty: mode.value === 'adjust' ? formData.pickingQty : (currentDetail.value.pickedQty || 0) + formData.pickingQty,
      targetLocationCode: formData.targetLocationCode,
      remark: formData.remark,
      mode: mode.value
    }
    
    emit('confirm', resultData)
    dialogVisible.value = false
  } catch (error) {
    console.error('拣货确认失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (detail: WmsPickingTaskDetailResp, openMode: 'pick' | 'adjust' = 'pick') => {
  currentDetail.value = detail
  mode.value = openMode
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.picking-dialog-container {
  padding: 16px 0;
}

.item-info-card,
.picking-info-card,
.picking-history-card {
  margin-bottom: 16px;
}

.item-info-card :deep(.el-card__header),
.picking-info-card :deep(.el-card__header),
.picking-history-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.input-with-button {
  display: flex;
  gap: 8px;
}

.input-with-button .el-input {
  flex: 1;
}

.dialog-footer {
  text-align: right;
}
</style>
