package dto

// BaseDTO 基础数据传输对象
// 包含实体的基本信息，用于创建和更新操作
type BaseDTO struct {
	ID uint `json:"id,omitempty"` // ID：记录的唯一标识，创建时不需要，更新时必须
}

// TenantDTO 租户数据传输对象
// 包含租户相关的基本信息，用于多租户系统
type TenantDTO struct {
	BaseDTO
	TenantID uint `json:"tenantId,omitempty"` // 租户ID：记录所属的租户ID，可选，不提供则使用当前用户的租户ID
}

// AccountBookDTO 传输对象
type AccountBookDTO struct {
	TenantDTO
	AccountBookID uint `json:"accountBookId"` // 账套ID：标识数据所属的账套，用于实现多账套数据隔离
}

// BaseQueryReq 基础查询请求
type BaseQueryReq struct {
	Page     int    `json:"page" binding:"omitempty,min=1" label:"页码"`                // 页码，从1开始
	PageSize int    `json:"pageSize" binding:"omitempty,min=1,max=1000" label:"每页大小"` // 每页大小，最大1000
	OrderBy  string `json:"orderBy" label:"排序字段"`                                     // 排序字段
	OrderDir string `json:"orderDir" binding:"omitempty,oneof=ASC DESC" label:"排序方向"` // 排序方向：ASC 或 DESC
}
