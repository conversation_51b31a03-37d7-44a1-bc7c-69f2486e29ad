package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util"
	"context"
	"reflect"
	"strconv"
	"time"
)

// EmployeeService defines the interface for employee business logic.
// It embeds BaseService for common functionalities.
type EmployeeService interface {
	BaseService // 嵌入基础服务接口

	CreateEmployee(ctx context.Context, req dto.EmployeeCreateOrUpdateDTO) (*vo.EmployeeVO, error)
	UpdateEmployee(ctx context.Context, id uint, req dto.EmployeeCreateOrUpdateDTO) (*vo.EmployeeVO, error)
	DeleteEmployees(ctx context.Context, ids []uint) error
	GetEmployeeByID(ctx context.Context, id uint) (*vo.EmployeeVO, error)
	GetEmployeeByCode(ctx context.Context, code string) (*vo.EmployeeVO, error)
	GetEmployeePage(ctx context.Context, query dto.EmployeePageQueryDTO) (*response.PageResult, error)
	UpdateEmployeeStatus(ctx context.Context, id uint, status int) error
	GetEmployeeSimpleList(ctx context.Context, query dto.EmployeeSimpleListQueryDTO) ([]*vo.EmployeeSimpleVO, error)
}

// employeeServiceImpl implements the EmployeeService interface.
type employeeServiceImpl struct {
	*BaseServiceImpl // 嵌入基础服务实现
}

// NewEmployeeService creates a new instance of EmployeeService.
// It takes a ServiceManager instance to access other services and repositories.
func NewEmployeeService(sm *ServiceManager) EmployeeService {
	return &employeeServiceImpl{
		BaseServiceImpl: NewBaseService(sm),
	}
}

// getEmployeeRepository is a helper function to get the employee repository from the service manager.
func (s *employeeServiceImpl) getEmployeeRepository() repository.EmployeeRepository {
	return s.GetServiceManager().GetRepositoryManager().GetEmployeeRepository()
}

// populateEmployeeVOEnhancements populates the enhanced text fields in EmployeeVO.
func (s *employeeServiceImpl) populateEmployeeVOEnhancements(ctx context.Context, employeeVO *vo.EmployeeVO, employeeEntity *entity.Employee) {
	log := logger.WithContext(ctx)
	sm := s.GetServiceManager()
	dictService := sm.GetDictionaryService()
	orgNodeService := sm.GetOrganizationNodeService()

	// Helper function to get dictionary item label
	getLabel := func(typeCode string, value interface{}) string {
		var strValue string
		switch v := value.(type) {
		case int:
			strValue = strconv.Itoa(v)
		case string:
			strValue = v
		default:
			log.Warn(ctx, "getLabel: unsupported value type", logger.WithField("typeCode", typeCode), logger.WithField("value", value))
			return ""
		}
		if strValue == "" && typeCode != "emp_job_grade" && typeCode != "emp_job_sub_level" && typeCode != "emp_work_category" { // Allow empty string for new fields if not set
			// For numeric types that might be 0 and represent "Unknown" or not set,
			// we might return "" or a specific text. Here, if it's an empty string after conversion (e.g. int 0 becoming "0" and then if we need to treat "0" as empty for some)
			// or if the original value was an empty string for string types.
			// Let's assume for now numeric 0 will try to find a "0" in dict, and empty string value is legitimate for string type dicts.
		}

		items, err := dictService.GetDictItemsByTypeCode(ctx, typeCode)
		if err != nil {
			log.Warn(ctx, "获取字典项失败", logger.WithError(err), logger.WithField("typeCode", typeCode))
			return ""
		}
		for _, item := range items {
			if item.Value == strValue {
				return item.Label
			}
		}
		// Only log warning if strValue was not empty, otherwise it's a valid "not set" case for new string fields
		if strValue != "" {
			log.Warn(ctx, "未找到匹配的字典项标签", logger.WithField("typeCode", typeCode), logger.WithField("value", strValue))
		}
		return ""
	}

	// Populate text fields from dictionary
	employeeVO.EmployeeGenderText = getLabel("gender", employeeEntity.EmployeeGender) // Reusing global 'gender'
	employeeVO.EmployeeStatusText = getLabel("emp_status", employeeEntity.EmployeeStatus)
	employeeVO.EmployeeEducationText = getLabel("emp_education", employeeEntity.EmployeeEducation)
	employeeVO.EmployeeMaritalStatusText = getLabel("emp_marital_status", employeeEntity.EmployeeMaritalStatus)
	employeeVO.EmployeePoliticalStatusText = getLabel("emp_political_status", employeeEntity.EmployeePoliticalStatus)
	employeeVO.EmployeeIDCardTypeText = getLabel("emp_id_card_type", employeeEntity.EmployeeIDCardType)
	employeeVO.EmployeeWorkTypeText = getLabel("emp_work_type", employeeEntity.EmployeeWorkType)
	employeeVO.EmployeeHealthStatusText = getLabel("emp_health_status", employeeEntity.EmployeeHealthStatus)
	employeeVO.EmployeeJobGradeValueText = getLabel("emp_job_grade", employeeEntity.EmployeeJobGradeValue)
	employeeVO.EmployeeJobSubLevelValueText = getLabel("emp_job_sub_level", employeeEntity.EmployeeJobSubLevelValue)
	employeeVO.EmployeeWorkCategoryValueText = getLabel("emp_work_category", employeeEntity.EmployeeWorkCategoryValue)
	employeeVO.EmployeeDegreeText = getLabel("emp_degree", employeeEntity.EmployeeDegree)
	employeeVO.EmployeeJobTitleText = getLabel("emp_professional_title", employeeEntity.EmployeeJobTitle)
	employeeVO.EmployeeNationText = getLabel("emp_nation", employeeEntity.EmployeeNation)
	employeeVO.EmployeeEntrySourceText = getLabel("emp_entry_source", employeeEntity.EmployeeEntrySource)
	employeeVO.EmployeeEmergencyContactRelationshipText = getLabel("emp_emergency_contact_relationship", employeeEntity.EmployeeEmergencyContactRelationship)

	// Populate department and position names
	if employeeEntity.EmployeeDepartmentID > 0 {
		deptVO, err := orgNodeService.GetNodeByID(ctx, employeeEntity.EmployeeDepartmentID)
		if err != nil {
			log.Warn(ctx, "获取部门名称失败", logger.WithError(err), logger.WithField("departmentID", employeeEntity.EmployeeDepartmentID))
		} else if deptVO != nil {
			employeeVO.EmployeeDepartmentName = deptVO.Name
		}
	}

	if employeeEntity.EmployeePositionID > 0 {
		posVO, err := orgNodeService.GetNodeByID(ctx, employeeEntity.EmployeePositionID)
		if err != nil {
			log.Warn(ctx, "获取职位名称失败", logger.WithError(err), logger.WithField("positionID", employeeEntity.EmployeePositionID))
		} else if posVO != nil {
			employeeVO.EmployeePositionName = posVO.Name
		}
	}
}

// --- CRUD Methods ---

// CreateEmployee creates a new employee.
func (s *employeeServiceImpl) CreateEmployee(ctx context.Context, req dto.EmployeeCreateOrUpdateDTO) (*vo.EmployeeVO, error) {
	log := logger.WithContext(ctx)
	employeeRepo := s.getEmployeeRepository()

	// 检查员工代码是否已存在
	exists, err := employeeRepo.IsCodeExist(ctx, req.EmployeeCode, 0)
	if err != nil {
		log.WithError(err).Error("检查员工代码是否存在失败")
		return nil, err
	}
	if exists {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "员工代码已存在")
	}

	// Date logic validation
	if req.EmployeeProbationEndDate != nil && !req.EmployeeInDate.IsZero() && req.EmployeeProbationEndDate.Time.Before(req.EmployeeInDate.Time) {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "试用期结束日期不能早于入职日期")
	}
	if req.EmployeeContractEndDate != nil && req.EmployeeContractStartDate != nil &&
		!req.EmployeeContractStartDate.IsZero() &&
		req.EmployeeContractEndDate.Time.Before(req.EmployeeContractStartDate.Time) {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "合同结束日期不能早于合同开始日期")
	}

	// Manual mapping from DTO to Entity (req has util.JsonDate)
	employee := &entity.Employee{
		EmployeeCode:                         req.EmployeeCode,
		EmployeeName:                         req.EmployeeName,
		EmployeeInDate:                       req.EmployeeInDate.Time,
		EmployeeNamePinyin:                   req.EmployeeNamePinyin,
		EmployeeNameEn:                       req.EmployeeNameEn,
		EmployeeIDCard:                       req.EmployeeIDCard,
		EmployeeMobile:                       req.EmployeeMobile,
		EmployeeEmail:                        req.EmployeeEmail,
		EmployeeGender:                       req.EmployeeGender,
		EmployeeAvatar:                       req.EmployeeAvatar,
		EmployeeAddress:                      req.EmployeeAddress,
		EmployeeStatus:                       req.EmployeeStatus,
		EmployeeRemark:                       req.EmployeeRemark,
		EmployeeEducation:                    req.EmployeeEducation,
		EmployeeMaritalStatus:                req.EmployeeMaritalStatus,
		EmployeeNationality:                  req.EmployeeNationality,
		EmployeeNation:                       req.EmployeeNation,
		EmployeeNativePlace:                  req.EmployeeNativePlace,
		EmployeePoliticalStatus:              req.EmployeePoliticalStatus,
		EmployeeIDCardType:                   req.EmployeeIDCardType,
		EmployeeIDCardValidityPeriod:         req.EmployeeIDCardValidityPeriod,
		EmployeeDepartmentID:                 req.EmployeeDepartmentID,
		EmployeePositionID:                   req.EmployeePositionID,
		EmployeeJobTitle:                     req.EmployeeJobTitle,
		EmployeeWorkType:                     req.EmployeeWorkType,
		EmployeeWorkLocation:                 req.EmployeeWorkLocation,
		EmployeeEmergencyContactName:         req.EmployeeEmergencyContactName,
		EmployeeEmergencyContactMobile:       req.EmployeeEmergencyContactMobile,
		EmployeeEmergencyContactRelationship: req.EmployeeEmergencyContactRelationship,
		EmployeeBankName:                     req.EmployeeBankName,
		EmployeeBankAccount:                  req.EmployeeBankAccount,
		EmployeeSocialSecurityNumber:         req.EmployeeSocialSecurityNumber,
		EmployeeHousingFundNumber:            req.EmployeeHousingFundNumber,
		EmployeeJobGradeValue:                req.EmployeeJobGradeValue,
		EmployeeJobSubLevelValue:             req.EmployeeJobSubLevelValue,
		EmployeeWorkCategoryValue:            req.EmployeeWorkCategoryValue,
		EmployeeHouseholdAddress:             req.EmployeeHouseholdAddress,
		EmployeeCurrentAddress:               req.EmployeeCurrentAddress,
		EmployeePostCode:                     req.EmployeePostCode,
		EmployeeDegree:                       req.EmployeeDegree,
		EmployeeMajor:                        req.EmployeeMajor,
		EmployeeSchool:                       req.EmployeeSchool,
		EmployeeEntrySource:                  req.EmployeeEntrySource,
		EmployeeReferralName:                 req.EmployeeReferralName,
		EmployeeHobby:                        req.EmployeeHobby,
		EmployeeSkills:                       req.EmployeeSkills,
		EmployeeCertificates:                 req.EmployeeCertificates,
		EmployeeHealthStatus:                 req.EmployeeHealthStatus,
		EmployeeBloodType:                    req.EmployeeBloodType,
	}

	if req.EmployeeBirthday != nil {
		employee.EmployeeBirthday = req.EmployeeBirthday.Time
	}
	if req.EmployeeOutDate != nil {
		employee.EmployeeOutDate = &req.EmployeeOutDate.Time
	}
	if req.EmployeeProbationEndDate != nil {
		employee.EmployeeProbationEndDate = &req.EmployeeProbationEndDate.Time
	}
	if req.EmployeeContractStartDate != nil {
		employee.EmployeeContractStartDate = &req.EmployeeContractStartDate.Time
	}
	if req.EmployeeContractEndDate != nil {
		employee.EmployeeContractEndDate = &req.EmployeeContractEndDate.Time
	}
	if req.EmployeeSalary != nil {
		employee.EmployeeSalary = *req.EmployeeSalary
	}
	if req.EmployeeWorkYears != nil {
		employee.EmployeeWorkYears = *req.EmployeeWorkYears
	}
	if req.EmployeeFirstWorkDate != nil {
		employee.EmployeeFirstWorkDate = &req.EmployeeFirstWorkDate.Time
	}
	if req.EmployeeGraduationDate != nil {
		employee.EmployeeGraduationDate = &req.EmployeeGraduationDate.Time
	}
	if req.EmployeeHeight != nil {
		employee.EmployeeHeight = *req.EmployeeHeight
	}

	tid64, err := s.GetAccountBookIDFromContext(ctx)
	if err != nil {
		log.WithError(err).Warn("创建员工时无法从上下文中获取AccountBookID (TenantID)")
	}
	employee.TenantID = uint(tid64)

	uid64, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		log.WithError(err).Warn("创建员工时无法从上下文中获取UserID")
	}
	log.Infof("[CreateEmployee] GetUserIDFromContext returned: uid64=%d, err=%v", uid64, err)
	employee.CreatedBy = uint(uid64)
	employee.UpdatedBy = uint(uid64)

	if err := employeeRepo.Create(ctx, employee); err != nil {
		log.WithError(err).Error("创建员工失败")
		return nil, err
	}

	// Manual mapping from Entity to VO (VO has util.JsonDate)
	createdVo := &vo.EmployeeVO{
		EmployeeCode:                         employee.EmployeeCode,
		EmployeeName:                         employee.EmployeeName,
		EmployeeInDate:                       util.JsonDate{Time: employee.EmployeeInDate},
		EmployeeNamePinyin:                   employee.EmployeeNamePinyin,
		EmployeeNameEn:                       employee.EmployeeNameEn,
		EmployeeIDCard:                       employee.EmployeeIDCard,
		EmployeeMobile:                       employee.EmployeeMobile,
		EmployeeEmail:                        employee.EmployeeEmail,
		EmployeeGender:                       employee.EmployeeGender,
		EmployeeAvatar:                       employee.EmployeeAvatar,
		EmployeeAddress:                      employee.EmployeeAddress,
		EmployeeStatus:                       employee.EmployeeStatus,
		EmployeeRemark:                       employee.EmployeeRemark,
		EmployeeEducation:                    employee.EmployeeEducation,
		EmployeeMaritalStatus:                employee.EmployeeMaritalStatus,
		EmployeeNationality:                  employee.EmployeeNationality,
		EmployeeNation:                       employee.EmployeeNation,
		EmployeeNativePlace:                  employee.EmployeeNativePlace,
		EmployeePoliticalStatus:              employee.EmployeePoliticalStatus,
		EmployeeIDCardType:                   employee.EmployeeIDCardType,
		EmployeeIDCardValidityPeriod:         employee.EmployeeIDCardValidityPeriod,
		EmployeeDepartmentID:                 employee.EmployeeDepartmentID,
		EmployeePositionID:                   employee.EmployeePositionID,
		EmployeeJobTitle:                     employee.EmployeeJobTitle,
		EmployeeWorkType:                     employee.EmployeeWorkType,
		EmployeeWorkLocation:                 employee.EmployeeWorkLocation,
		EmployeeEmergencyContactName:         employee.EmployeeEmergencyContactName,
		EmployeeEmergencyContactMobile:       employee.EmployeeEmergencyContactMobile,
		EmployeeEmergencyContactRelationship: employee.EmployeeEmergencyContactRelationship,
		EmployeeBankName:                     employee.EmployeeBankName,
		EmployeeBankAccount:                  employee.EmployeeBankAccount,
		EmployeeSocialSecurityNumber:         employee.EmployeeSocialSecurityNumber,
		EmployeeHousingFundNumber:            employee.EmployeeHousingFundNumber,
		EmployeeJobGradeValue:                employee.EmployeeJobGradeValue,
		EmployeeJobSubLevelValue:             employee.EmployeeJobSubLevelValue,
		EmployeeWorkCategoryValue:            employee.EmployeeWorkCategoryValue,
		EmployeeHouseholdAddress:             employee.EmployeeHouseholdAddress,
		EmployeeCurrentAddress:               employee.EmployeeCurrentAddress,
		EmployeePostCode:                     employee.EmployeePostCode,
		EmployeeDegree:                       employee.EmployeeDegree,
		EmployeeMajor:                        employee.EmployeeMajor,
		EmployeeSchool:                       employee.EmployeeSchool,
		EmployeeEntrySource:                  employee.EmployeeEntrySource,
		EmployeeReferralName:                 employee.EmployeeReferralName,
		EmployeeHobby:                        employee.EmployeeHobby,
		EmployeeSkills:                       employee.EmployeeSkills,
		EmployeeCertificates:                 employee.EmployeeCertificates,
		EmployeeHealthStatus:                 employee.EmployeeHealthStatus,
		EmployeeBloodType:                    employee.EmployeeBloodType,
	}
	createdVo.ID = employee.ID
	createdVo.TenantID = employee.TenantID
	createdVo.CreatedAt = employee.CreatedAt
	createdVo.UpdatedAt = employee.UpdatedAt

	if !employee.EmployeeBirthday.IsZero() {
		createdVo.EmployeeBirthday = &util.JsonDate{Time: employee.EmployeeBirthday}
	}
	if employee.EmployeeOutDate != nil {
		createdVo.EmployeeOutDate = &util.JsonDate{Time: *employee.EmployeeOutDate}
	}
	if employee.EmployeeProbationEndDate != nil {
		createdVo.EmployeeProbationEndDate = &util.JsonDate{Time: *employee.EmployeeProbationEndDate}
	}
	if employee.EmployeeContractStartDate != nil {
		createdVo.EmployeeContractStartDate = &util.JsonDate{Time: *employee.EmployeeContractStartDate}
	}
	if employee.EmployeeContractEndDate != nil {
		createdVo.EmployeeContractEndDate = &util.JsonDate{Time: *employee.EmployeeContractEndDate}
	}
	if employee.EmployeeSalary != 0 {
		salary := employee.EmployeeSalary
		createdVo.EmployeeSalary = &salary
	}
	if employee.EmployeeWorkYears != 0 {
		workYears := employee.EmployeeWorkYears
		createdVo.EmployeeWorkYears = &workYears
	}
	if employee.EmployeeFirstWorkDate != nil {
		createdVo.EmployeeFirstWorkDate = &util.JsonDate{Time: *employee.EmployeeFirstWorkDate}
	}
	if employee.EmployeeGraduationDate != nil {
		createdVo.EmployeeGraduationDate = &util.JsonDate{Time: *employee.EmployeeGraduationDate}
	}
	if employee.EmployeeHeight != 0 {
		h := employee.EmployeeHeight
		createdVo.EmployeeHeight = &h
	}

	s.populateEmployeeVOEnhancements(ctx, createdVo, employee)
	return createdVo, nil
}

// UpdateEmployee updates an existing employee.
func (s *employeeServiceImpl) UpdateEmployee(ctx context.Context, id uint, req dto.EmployeeCreateOrUpdateDTO) (*vo.EmployeeVO, error) {
	log := logger.WithContext(ctx)
	employeeRepo := s.getEmployeeRepository()

	existingEmployee, err := employeeRepo.FindByID(ctx, id)
	if err != nil {
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "员工不存在")
		}
		log.WithError(err).Error("更新员工前查询员工失败")
		return nil, err
	}

	// Date logic validation (req has util.JsonDate)
	var inDateForValidation time.Time
	if !req.EmployeeInDate.IsZero() {
		inDateForValidation = req.EmployeeInDate.Time
	} else if !existingEmployee.EmployeeInDate.IsZero() {
		inDateForValidation = existingEmployee.EmployeeInDate
	}
	if req.EmployeeProbationEndDate != nil && !req.EmployeeProbationEndDate.IsZero() &&
		!inDateForValidation.IsZero() && req.EmployeeProbationEndDate.Time.Before(inDateForValidation) {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "试用期结束日期不能早于入职日期")
	}
	var contractStartDateForValidation time.Time
	if req.EmployeeContractStartDate != nil && !req.EmployeeContractStartDate.IsZero() {
		contractStartDateForValidation = req.EmployeeContractStartDate.Time
	} else if existingEmployee.EmployeeContractStartDate != nil && !existingEmployee.EmployeeContractStartDate.IsZero() {
		contractStartDateForValidation = *existingEmployee.EmployeeContractStartDate
	}
	if req.EmployeeContractEndDate != nil && !req.EmployeeContractEndDate.IsZero() &&
		!contractStartDateForValidation.IsZero() && req.EmployeeContractEndDate.Time.Before(contractStartDateForValidation) {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "合同结束日期不能早于合同开始日期")
	}

	// Check if employee code is being changed and if the new code already exists
	if req.EmployeeCode != existingEmployee.EmployeeCode {
		exists, errCheckCode := employeeRepo.IsCodeExist(ctx, req.EmployeeCode, id) // pass id to exclude self
		if errCheckCode != nil {
			log.WithError(errCheckCode).Error("检查员工代码是否存在失败")
			return nil, errCheckCode
		}
		if exists {
			return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "员工代码已存在")
		}
		existingEmployee.EmployeeCode = req.EmployeeCode
	}

	// Manual field updates (req has util.JsonDate)
	existingEmployee.EmployeeName = req.EmployeeName
	existingEmployee.EmployeeNamePinyin = req.EmployeeNamePinyin
	existingEmployee.EmployeeNameEn = req.EmployeeNameEn
	existingEmployee.EmployeeIDCard = req.EmployeeIDCard
	existingEmployee.EmployeeMobile = req.EmployeeMobile
	existingEmployee.EmployeeEmail = req.EmployeeEmail
	existingEmployee.EmployeeGender = req.EmployeeGender
	existingEmployee.EmployeeAvatar = req.EmployeeAvatar
	existingEmployee.EmployeeAddress = req.EmployeeAddress
	existingEmployee.EmployeeStatus = req.EmployeeStatus
	existingEmployee.EmployeeRemark = req.EmployeeRemark
	existingEmployee.EmployeeEducation = req.EmployeeEducation
	existingEmployee.EmployeeMaritalStatus = req.EmployeeMaritalStatus
	existingEmployee.EmployeeNationality = req.EmployeeNationality
	existingEmployee.EmployeeNation = req.EmployeeNation
	existingEmployee.EmployeeNativePlace = req.EmployeeNativePlace
	existingEmployee.EmployeePoliticalStatus = req.EmployeePoliticalStatus
	existingEmployee.EmployeeIDCardType = req.EmployeeIDCardType
	existingEmployee.EmployeeIDCardValidityPeriod = req.EmployeeIDCardValidityPeriod
	existingEmployee.EmployeeDepartmentID = req.EmployeeDepartmentID
	existingEmployee.EmployeePositionID = req.EmployeePositionID
	existingEmployee.EmployeeJobTitle = req.EmployeeJobTitle
	existingEmployee.EmployeeWorkType = req.EmployeeWorkType
	existingEmployee.EmployeeWorkLocation = req.EmployeeWorkLocation
	existingEmployee.EmployeeEmergencyContactName = req.EmployeeEmergencyContactName
	existingEmployee.EmployeeEmergencyContactMobile = req.EmployeeEmergencyContactMobile
	existingEmployee.EmployeeEmergencyContactRelationship = req.EmployeeEmergencyContactRelationship
	existingEmployee.EmployeeBankName = req.EmployeeBankName
	existingEmployee.EmployeeBankAccount = req.EmployeeBankAccount
	existingEmployee.EmployeeSocialSecurityNumber = req.EmployeeSocialSecurityNumber
	existingEmployee.EmployeeHousingFundNumber = req.EmployeeHousingFundNumber
	existingEmployee.EmployeeJobGradeValue = req.EmployeeJobGradeValue
	existingEmployee.EmployeeJobSubLevelValue = req.EmployeeJobSubLevelValue
	existingEmployee.EmployeeWorkCategoryValue = req.EmployeeWorkCategoryValue
	existingEmployee.EmployeeHouseholdAddress = req.EmployeeHouseholdAddress
	existingEmployee.EmployeeCurrentAddress = req.EmployeeCurrentAddress
	existingEmployee.EmployeePostCode = req.EmployeePostCode
	existingEmployee.EmployeeDegree = req.EmployeeDegree
	existingEmployee.EmployeeMajor = req.EmployeeMajor
	existingEmployee.EmployeeSchool = req.EmployeeSchool
	existingEmployee.EmployeeEntrySource = req.EmployeeEntrySource
	existingEmployee.EmployeeReferralName = req.EmployeeReferralName
	existingEmployee.EmployeeHobby = req.EmployeeHobby
	existingEmployee.EmployeeSkills = req.EmployeeSkills
	existingEmployee.EmployeeCertificates = req.EmployeeCertificates
	existingEmployee.EmployeeHealthStatus = req.EmployeeHealthStatus
	existingEmployee.EmployeeBloodType = req.EmployeeBloodType

	if req.EmployeeBirthday != nil {
		existingEmployee.EmployeeBirthday = req.EmployeeBirthday.Time
	}
	if !req.EmployeeInDate.IsZero() {
		existingEmployee.EmployeeInDate = req.EmployeeInDate.Time
	}
	if req.EmployeeOutDate != nil {
		existingEmployee.EmployeeOutDate = &req.EmployeeOutDate.Time
	} else {
		existingEmployee.EmployeeOutDate = nil
	}
	if req.EmployeeProbationEndDate != nil {
		existingEmployee.EmployeeProbationEndDate = &req.EmployeeProbationEndDate.Time
	} else {
		existingEmployee.EmployeeProbationEndDate = nil
	}
	if req.EmployeeContractStartDate != nil {
		existingEmployee.EmployeeContractStartDate = &req.EmployeeContractStartDate.Time
	} else {
		existingEmployee.EmployeeContractStartDate = nil
	}
	if req.EmployeeContractEndDate != nil {
		existingEmployee.EmployeeContractEndDate = &req.EmployeeContractEndDate.Time
	} else {
		existingEmployee.EmployeeContractEndDate = nil
	}
	if req.EmployeeSalary != nil {
		existingEmployee.EmployeeSalary = *req.EmployeeSalary
	}
	if req.EmployeeWorkYears != nil {
		existingEmployee.EmployeeWorkYears = *req.EmployeeWorkYears
	}
	if req.EmployeeFirstWorkDate != nil {
		existingEmployee.EmployeeFirstWorkDate = &req.EmployeeFirstWorkDate.Time
	}
	if req.EmployeeGraduationDate != nil {
		existingEmployee.EmployeeGraduationDate = &req.EmployeeGraduationDate.Time
	}

	uid64, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		log.WithError(err).Warn("更新员工时无法从上下文中获取UserID")
	}
	log.Infof("[UpdateEmployee] GetUserIDFromContext returned: uid64=%d, err=%v", uid64, err)
	existingEmployee.UpdatedBy = uint(uid64)

	if err := employeeRepo.Update(ctx, existingEmployee); err != nil {
		log.WithError(err).Error("更新员工失败")
		return nil, err
	}

	// Manual mapping from updated Entity to VO (VO has util.JsonDate)
	updatedVo := &vo.EmployeeVO{
		EmployeeInDate:                       util.JsonDate{Time: existingEmployee.EmployeeInDate},
		EmployeeName:                         existingEmployee.EmployeeName,
		EmployeeNamePinyin:                   existingEmployee.EmployeeNamePinyin,
		EmployeeNameEn:                       existingEmployee.EmployeeNameEn,
		EmployeeIDCard:                       existingEmployee.EmployeeIDCard,
		EmployeeMobile:                       existingEmployee.EmployeeMobile,
		EmployeeEmail:                        existingEmployee.EmployeeEmail,
		EmployeeGender:                       existingEmployee.EmployeeGender,
		EmployeeAvatar:                       existingEmployee.EmployeeAvatar,
		EmployeeAddress:                      existingEmployee.EmployeeAddress,
		EmployeeStatus:                       existingEmployee.EmployeeStatus,
		EmployeeRemark:                       existingEmployee.EmployeeRemark,
		EmployeeEducation:                    existingEmployee.EmployeeEducation,
		EmployeeMaritalStatus:                existingEmployee.EmployeeMaritalStatus,
		EmployeeNationality:                  existingEmployee.EmployeeNationality,
		EmployeeNation:                       existingEmployee.EmployeeNation,
		EmployeeNativePlace:                  existingEmployee.EmployeeNativePlace,
		EmployeePoliticalStatus:              existingEmployee.EmployeePoliticalStatus,
		EmployeeIDCardType:                   existingEmployee.EmployeeIDCardType,
		EmployeeIDCardValidityPeriod:         existingEmployee.EmployeeIDCardValidityPeriod,
		EmployeeDepartmentID:                 existingEmployee.EmployeeDepartmentID,
		EmployeePositionID:                   existingEmployee.EmployeePositionID,
		EmployeeJobTitle:                     existingEmployee.EmployeeJobTitle,
		EmployeeWorkType:                     existingEmployee.EmployeeWorkType,
		EmployeeWorkLocation:                 existingEmployee.EmployeeWorkLocation,
		EmployeeEmergencyContactName:         existingEmployee.EmployeeEmergencyContactName,
		EmployeeEmergencyContactMobile:       existingEmployee.EmployeeEmergencyContactMobile,
		EmployeeEmergencyContactRelationship: existingEmployee.EmployeeEmergencyContactRelationship,
		EmployeeBankName:                     existingEmployee.EmployeeBankName,
		EmployeeBankAccount:                  existingEmployee.EmployeeBankAccount,
		EmployeeSocialSecurityNumber:         existingEmployee.EmployeeSocialSecurityNumber,
		EmployeeHousingFundNumber:            existingEmployee.EmployeeHousingFundNumber,
		EmployeeJobGradeValue:                existingEmployee.EmployeeJobGradeValue,
		EmployeeJobSubLevelValue:             existingEmployee.EmployeeJobSubLevelValue,
		EmployeeWorkCategoryValue:            existingEmployee.EmployeeWorkCategoryValue,
		EmployeeHouseholdAddress:             existingEmployee.EmployeeHouseholdAddress,
		EmployeeCurrentAddress:               existingEmployee.EmployeeCurrentAddress,
		EmployeePostCode:                     existingEmployee.EmployeePostCode,
		EmployeeDegree:                       existingEmployee.EmployeeDegree,
		EmployeeMajor:                        existingEmployee.EmployeeMajor,
		EmployeeSchool:                       existingEmployee.EmployeeSchool,
		EmployeeEntrySource:                  existingEmployee.EmployeeEntrySource,
		EmployeeReferralName:                 existingEmployee.EmployeeReferralName,
		EmployeeHobby:                        existingEmployee.EmployeeHobby,
		EmployeeSkills:                       existingEmployee.EmployeeSkills,
		EmployeeCertificates:                 existingEmployee.EmployeeCertificates,
		EmployeeHealthStatus:                 existingEmployee.EmployeeHealthStatus,
		EmployeeBloodType:                    existingEmployee.EmployeeBloodType,
	}
	updatedVo.ID = existingEmployee.ID
	updatedVo.TenantID = existingEmployee.TenantID
	updatedVo.CreatedAt = existingEmployee.CreatedAt
	updatedVo.UpdatedAt = existingEmployee.UpdatedAt

	if !existingEmployee.EmployeeBirthday.IsZero() {
		updatedVo.EmployeeBirthday = &util.JsonDate{Time: existingEmployee.EmployeeBirthday}
	}
	if existingEmployee.EmployeeOutDate != nil {
		updatedVo.EmployeeOutDate = &util.JsonDate{Time: *existingEmployee.EmployeeOutDate}
	}
	if existingEmployee.EmployeeProbationEndDate != nil {
		updatedVo.EmployeeProbationEndDate = &util.JsonDate{Time: *existingEmployee.EmployeeProbationEndDate}
	}
	if existingEmployee.EmployeeContractStartDate != nil {
		updatedVo.EmployeeContractStartDate = &util.JsonDate{Time: *existingEmployee.EmployeeContractStartDate}
	}
	if existingEmployee.EmployeeContractEndDate != nil {
		updatedVo.EmployeeContractEndDate = &util.JsonDate{Time: *existingEmployee.EmployeeContractEndDate}
	}
	if existingEmployee.EmployeeSalary != 0 {
		salary := existingEmployee.EmployeeSalary
		updatedVo.EmployeeSalary = &salary
	}
	if existingEmployee.EmployeeWorkYears != 0 {
		workYears := existingEmployee.EmployeeWorkYears
		updatedVo.EmployeeWorkYears = &workYears
	}
	if existingEmployee.EmployeeFirstWorkDate != nil {
		updatedVo.EmployeeFirstWorkDate = &util.JsonDate{Time: *existingEmployee.EmployeeFirstWorkDate}
	}
	if existingEmployee.EmployeeGraduationDate != nil {
		updatedVo.EmployeeGraduationDate = &util.JsonDate{Time: *existingEmployee.EmployeeGraduationDate}
	}
	if existingEmployee.EmployeeHeight != 0 {
		h := existingEmployee.EmployeeHeight
		updatedVo.EmployeeHeight = &h
	}

	s.populateEmployeeVOEnhancements(ctx, updatedVo, existingEmployee)
	return updatedVo, nil
}

// DeleteEmployees deletes one or more employees by their IDs.
func (s *employeeServiceImpl) DeleteEmployees(ctx context.Context, ids []uint) error {
	log := logger.WithContext(ctx)
	if len(ids) == 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "至少需要一个员工ID才能删除")
	}

	employeeRepo := s.getEmployeeRepository()
	if err := employeeRepo.Delete(ctx, ids); err != nil {
		log.WithError(err).Error("删除员工失败")
		return err
	}
	return nil
}

// GetEmployeeByID retrieves an employee by their ID.
func (s *employeeServiceImpl) GetEmployeeByID(ctx context.Context, id uint) (*vo.EmployeeVO, error) {
	log := logger.WithContext(ctx)
	employeeRepo := s.getEmployeeRepository()

	employee, err := employeeRepo.FindByID(ctx, id)
	if err != nil {
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "员工不存在")
		}
		log.WithError(err).Error("按ID查询员工失败")
		return nil, err
	}

	// Manual mapping from Entity to VO
	employeeVo := &vo.EmployeeVO{
		EmployeeCode:                         employee.EmployeeCode,
		EmployeeName:                         employee.EmployeeName,
		EmployeeInDate:                       util.JsonDate{Time: employee.EmployeeInDate},
		EmployeeNamePinyin:                   employee.EmployeeNamePinyin,
		EmployeeNameEn:                       employee.EmployeeNameEn,
		EmployeeIDCard:                       employee.EmployeeIDCard,
		EmployeeMobile:                       employee.EmployeeMobile,
		EmployeeEmail:                        employee.EmployeeEmail,
		EmployeeGender:                       employee.EmployeeGender,
		EmployeeAvatar:                       employee.EmployeeAvatar,
		EmployeeAddress:                      employee.EmployeeAddress,
		EmployeeStatus:                       employee.EmployeeStatus,
		EmployeeRemark:                       employee.EmployeeRemark,
		EmployeeEducation:                    employee.EmployeeEducation,
		EmployeeMaritalStatus:                employee.EmployeeMaritalStatus,
		EmployeeNationality:                  employee.EmployeeNationality,
		EmployeeNation:                       employee.EmployeeNation,
		EmployeeNativePlace:                  employee.EmployeeNativePlace,
		EmployeePoliticalStatus:              employee.EmployeePoliticalStatus,
		EmployeeIDCardType:                   employee.EmployeeIDCardType,
		EmployeeIDCardValidityPeriod:         employee.EmployeeIDCardValidityPeriod,
		EmployeeDepartmentID:                 employee.EmployeeDepartmentID,
		EmployeePositionID:                   employee.EmployeePositionID,
		EmployeeJobTitle:                     employee.EmployeeJobTitle,
		EmployeeWorkType:                     employee.EmployeeWorkType,
		EmployeeWorkLocation:                 employee.EmployeeWorkLocation,
		EmployeeEmergencyContactName:         employee.EmployeeEmergencyContactName,
		EmployeeEmergencyContactMobile:       employee.EmployeeEmergencyContactMobile,
		EmployeeEmergencyContactRelationship: employee.EmployeeEmergencyContactRelationship,
		EmployeeBankName:                     employee.EmployeeBankName,
		EmployeeBankAccount:                  employee.EmployeeBankAccount,
		EmployeeSocialSecurityNumber:         employee.EmployeeSocialSecurityNumber,
		EmployeeHousingFundNumber:            employee.EmployeeHousingFundNumber,
		EmployeeJobGradeValue:                employee.EmployeeJobGradeValue,
		EmployeeJobSubLevelValue:             employee.EmployeeJobSubLevelValue,
		EmployeeWorkCategoryValue:            employee.EmployeeWorkCategoryValue,
		EmployeeHouseholdAddress:             employee.EmployeeHouseholdAddress,
		EmployeeCurrentAddress:               employee.EmployeeCurrentAddress,
		EmployeePostCode:                     employee.EmployeePostCode,
		EmployeeDegree:                       employee.EmployeeDegree,
		EmployeeMajor:                        employee.EmployeeMajor,
		EmployeeSchool:                       employee.EmployeeSchool,
		EmployeeEntrySource:                  employee.EmployeeEntrySource,
		EmployeeReferralName:                 employee.EmployeeReferralName,
		EmployeeHobby:                        employee.EmployeeHobby,
		EmployeeSkills:                       employee.EmployeeSkills,
		EmployeeCertificates:                 employee.EmployeeCertificates,
		EmployeeHealthStatus:                 employee.EmployeeHealthStatus,
		EmployeeBloodType:                    employee.EmployeeBloodType,
	}
	employeeVo.ID = employee.ID
	employeeVo.TenantID = employee.TenantID
	employeeVo.CreatedAt = employee.CreatedAt
	employeeVo.UpdatedAt = employee.UpdatedAt

	if !employee.EmployeeBirthday.IsZero() {
		employeeVo.EmployeeBirthday = &util.JsonDate{Time: employee.EmployeeBirthday}
	}
	if employee.EmployeeOutDate != nil {
		employeeVo.EmployeeOutDate = &util.JsonDate{Time: *employee.EmployeeOutDate}
	}
	if employee.EmployeeProbationEndDate != nil {
		employeeVo.EmployeeProbationEndDate = &util.JsonDate{Time: *employee.EmployeeProbationEndDate}
	}
	if employee.EmployeeContractStartDate != nil {
		employeeVo.EmployeeContractStartDate = &util.JsonDate{Time: *employee.EmployeeContractStartDate}
	}
	if employee.EmployeeContractEndDate != nil {
		employeeVo.EmployeeContractEndDate = &util.JsonDate{Time: *employee.EmployeeContractEndDate}
	}
	if employee.EmployeeSalary != 0 {
		salary := employee.EmployeeSalary
		employeeVo.EmployeeSalary = &salary
	}
	if employee.EmployeeWorkYears != 0 {
		workYears := employee.EmployeeWorkYears
		employeeVo.EmployeeWorkYears = &workYears
	}
	if employee.EmployeeFirstWorkDate != nil {
		employeeVo.EmployeeFirstWorkDate = &util.JsonDate{Time: *employee.EmployeeFirstWorkDate}
	}
	if employee.EmployeeGraduationDate != nil {
		employeeVo.EmployeeGraduationDate = &util.JsonDate{Time: *employee.EmployeeGraduationDate}
	}
	if employee.EmployeeHeight != 0 {
		h := employee.EmployeeHeight
		employeeVo.EmployeeHeight = &h
	}

	s.populateEmployeeVOEnhancements(ctx, employeeVo, employee)

	return employeeVo, nil
}

// GetEmployeeByCode retrieves an employee by their code.
func (s *employeeServiceImpl) GetEmployeeByCode(ctx context.Context, code string) (*vo.EmployeeVO, error) {
	log := logger.WithContext(ctx)
	employeeRepo := s.getEmployeeRepository()

	employee, err := employeeRepo.FindByCode(ctx, code)
	if err != nil {
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "员工不存在")
		}
		log.WithError(err).Error("按员工代码查询员工失败")
		return nil, err
	}

	// Manual mapping from Entity to VO
	employeeVo := &vo.EmployeeVO{
		EmployeeCode:                         employee.EmployeeCode,
		EmployeeName:                         employee.EmployeeName,
		EmployeeInDate:                       util.JsonDate{Time: employee.EmployeeInDate},
		EmployeeNamePinyin:                   employee.EmployeeNamePinyin,
		EmployeeNameEn:                       employee.EmployeeNameEn,
		EmployeeIDCard:                       employee.EmployeeIDCard,
		EmployeeMobile:                       employee.EmployeeMobile,
		EmployeeEmail:                        employee.EmployeeEmail,
		EmployeeGender:                       employee.EmployeeGender,
		EmployeeAvatar:                       employee.EmployeeAvatar,
		EmployeeAddress:                      employee.EmployeeAddress,
		EmployeeStatus:                       employee.EmployeeStatus,
		EmployeeRemark:                       employee.EmployeeRemark,
		EmployeeEducation:                    employee.EmployeeEducation,
		EmployeeMaritalStatus:                employee.EmployeeMaritalStatus,
		EmployeeNationality:                  employee.EmployeeNationality,
		EmployeeNation:                       employee.EmployeeNation,
		EmployeeNativePlace:                  employee.EmployeeNativePlace,
		EmployeePoliticalStatus:              employee.EmployeePoliticalStatus,
		EmployeeIDCardType:                   employee.EmployeeIDCardType,
		EmployeeIDCardValidityPeriod:         employee.EmployeeIDCardValidityPeriod,
		EmployeeDepartmentID:                 employee.EmployeeDepartmentID,
		EmployeePositionID:                   employee.EmployeePositionID,
		EmployeeJobTitle:                     employee.EmployeeJobTitle,
		EmployeeWorkType:                     employee.EmployeeWorkType,
		EmployeeWorkLocation:                 employee.EmployeeWorkLocation,
		EmployeeEmergencyContactName:         employee.EmployeeEmergencyContactName,
		EmployeeEmergencyContactMobile:       employee.EmployeeEmergencyContactMobile,
		EmployeeEmergencyContactRelationship: employee.EmployeeEmergencyContactRelationship,
		EmployeeBankName:                     employee.EmployeeBankName,
		EmployeeBankAccount:                  employee.EmployeeBankAccount,
		EmployeeSocialSecurityNumber:         employee.EmployeeSocialSecurityNumber,
		EmployeeHousingFundNumber:            employee.EmployeeHousingFundNumber,
		EmployeeJobGradeValue:                employee.EmployeeJobGradeValue,
		EmployeeJobSubLevelValue:             employee.EmployeeJobSubLevelValue,
		EmployeeWorkCategoryValue:            employee.EmployeeWorkCategoryValue,
		EmployeeHouseholdAddress:             employee.EmployeeHouseholdAddress,
		EmployeeCurrentAddress:               employee.EmployeeCurrentAddress,
		EmployeePostCode:                     employee.EmployeePostCode,
		EmployeeDegree:                       employee.EmployeeDegree,
		EmployeeMajor:                        employee.EmployeeMajor,
		EmployeeSchool:                       employee.EmployeeSchool,
		EmployeeEntrySource:                  employee.EmployeeEntrySource,
		EmployeeReferralName:                 employee.EmployeeReferralName,
		EmployeeHobby:                        employee.EmployeeHobby,
		EmployeeSkills:                       employee.EmployeeSkills,
		EmployeeCertificates:                 employee.EmployeeCertificates,
		EmployeeHealthStatus:                 employee.EmployeeHealthStatus,
		EmployeeBloodType:                    employee.EmployeeBloodType,
	}
	employeeVo.ID = employee.ID
	employeeVo.TenantID = employee.TenantID
	employeeVo.CreatedAt = employee.CreatedAt
	employeeVo.UpdatedAt = employee.UpdatedAt

	if !employee.EmployeeBirthday.IsZero() {
		employeeVo.EmployeeBirthday = &util.JsonDate{Time: employee.EmployeeBirthday}
	}
	if employee.EmployeeOutDate != nil {
		employeeVo.EmployeeOutDate = &util.JsonDate{Time: *employee.EmployeeOutDate}
	}
	if employee.EmployeeProbationEndDate != nil {
		employeeVo.EmployeeProbationEndDate = &util.JsonDate{Time: *employee.EmployeeProbationEndDate}
	}
	if employee.EmployeeContractStartDate != nil {
		employeeVo.EmployeeContractStartDate = &util.JsonDate{Time: *employee.EmployeeContractStartDate}
	}
	if employee.EmployeeContractEndDate != nil {
		employeeVo.EmployeeContractEndDate = &util.JsonDate{Time: *employee.EmployeeContractEndDate}
	}
	if employee.EmployeeSalary != 0 {
		salary := employee.EmployeeSalary
		employeeVo.EmployeeSalary = &salary
	}
	if employee.EmployeeWorkYears != 0 {
		workYears := employee.EmployeeWorkYears
		employeeVo.EmployeeWorkYears = &workYears
	}
	if employee.EmployeeFirstWorkDate != nil {
		employeeVo.EmployeeFirstWorkDate = &util.JsonDate{Time: *employee.EmployeeFirstWorkDate}
	}
	if employee.EmployeeGraduationDate != nil {
		employeeVo.EmployeeGraduationDate = &util.JsonDate{Time: *employee.EmployeeGraduationDate}
	}
	if employee.EmployeeHeight != 0 {
		h := employee.EmployeeHeight
		employeeVo.EmployeeHeight = &h
	}

	s.populateEmployeeVOEnhancements(ctx, employeeVo, employee)

	return employeeVo, nil
}

// GetEmployeePage retrieves a paginated list of employees based on query criteria.
func (s *employeeServiceImpl) GetEmployeePage(ctx context.Context, query dto.EmployeePageQueryDTO) (*response.PageResult, error) {
	log := logger.WithContext(ctx)
	employeeRepo := s.getEmployeeRepository()

	// 调用仓库层的 FindPage 方法，它直接接收 dto.EmployeePageQueryDTO 并返回 *response.PageResult
	pagedDataFromRepo, err := employeeRepo.FindPage(ctx, query)
	if err != nil {
		log.WithError(err).Error("分页查询员工失败")
		return nil, err
	}

	// pagedDataFromRepo.List 是 interface{}，需要类型断言为 []*entity.Employee
	entities, ok := pagedDataFromRepo.List.([]*entity.Employee)
	if !ok {
		if pagedDataFromRepo.List != nil { // 只有在 List 非 nil 但断言失败时才记录错误并返回
			log.Error("分页查询员工结果列表类型断言失败", logger.WithField("actualType", reflect.TypeOf(pagedDataFromRepo.List).String()))
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "服务器内部错误: 数据表示失败")
		}
		entities = []*entity.Employee{} // 如果 List 为 nil，则初始化为空列表，避免后续的 nil panic
	}

	employeeVos := make([]*vo.EmployeeVO, 0, len(entities))
	for _, employeeEntity := range entities {
		// Manual mapping from Entity to VO for each item in the list
		employeeVo := &vo.EmployeeVO{
			EmployeeInDate:                       util.JsonDate{Time: employeeEntity.EmployeeInDate},
			EmployeeCode:                         employeeEntity.EmployeeCode,
			EmployeeName:                         employeeEntity.EmployeeName,
			EmployeeNamePinyin:                   employeeEntity.EmployeeNamePinyin,
			EmployeeNameEn:                       employeeEntity.EmployeeNameEn,
			EmployeeIDCard:                       employeeEntity.EmployeeIDCard,
			EmployeeMobile:                       employeeEntity.EmployeeMobile,
			EmployeeEmail:                        employeeEntity.EmployeeEmail,
			EmployeeGender:                       employeeEntity.EmployeeGender,
			EmployeeAvatar:                       employeeEntity.EmployeeAvatar,
			EmployeeAddress:                      employeeEntity.EmployeeAddress,
			EmployeeStatus:                       employeeEntity.EmployeeStatus,
			EmployeeRemark:                       employeeEntity.EmployeeRemark,
			EmployeeEducation:                    employeeEntity.EmployeeEducation,
			EmployeeMaritalStatus:                employeeEntity.EmployeeMaritalStatus,
			EmployeeNationality:                  employeeEntity.EmployeeNationality,
			EmployeeNation:                       employeeEntity.EmployeeNation,
			EmployeeNativePlace:                  employeeEntity.EmployeeNativePlace,
			EmployeePoliticalStatus:              employeeEntity.EmployeePoliticalStatus,
			EmployeeIDCardType:                   employeeEntity.EmployeeIDCardType,
			EmployeeIDCardValidityPeriod:         employeeEntity.EmployeeIDCardValidityPeriod,
			EmployeeDepartmentID:                 employeeEntity.EmployeeDepartmentID,
			EmployeePositionID:                   employeeEntity.EmployeePositionID,
			EmployeeJobTitle:                     employeeEntity.EmployeeJobTitle,
			EmployeeWorkType:                     employeeEntity.EmployeeWorkType,
			EmployeeWorkLocation:                 employeeEntity.EmployeeWorkLocation,
			EmployeeEmergencyContactName:         employeeEntity.EmployeeEmergencyContactName,
			EmployeeEmergencyContactMobile:       employeeEntity.EmployeeEmergencyContactMobile,
			EmployeeEmergencyContactRelationship: employeeEntity.EmployeeEmergencyContactRelationship,
			EmployeeBankName:                     employeeEntity.EmployeeBankName,
			EmployeeBankAccount:                  employeeEntity.EmployeeBankAccount,
			EmployeeSocialSecurityNumber:         employeeEntity.EmployeeSocialSecurityNumber,
			EmployeeHousingFundNumber:            employeeEntity.EmployeeHousingFundNumber,
			EmployeeJobGradeValue:                employeeEntity.EmployeeJobGradeValue,
			EmployeeJobSubLevelValue:             employeeEntity.EmployeeJobSubLevelValue,
			EmployeeWorkCategoryValue:            employeeEntity.EmployeeWorkCategoryValue,
			EmployeeHouseholdAddress:             employeeEntity.EmployeeHouseholdAddress,
			EmployeeCurrentAddress:               employeeEntity.EmployeeCurrentAddress,
			EmployeePostCode:                     employeeEntity.EmployeePostCode,
			EmployeeDegree:                       employeeEntity.EmployeeDegree,
			EmployeeMajor:                        employeeEntity.EmployeeMajor,
			EmployeeSchool:                       employeeEntity.EmployeeSchool,
			EmployeeEntrySource:                  employeeEntity.EmployeeEntrySource,
			EmployeeReferralName:                 employeeEntity.EmployeeReferralName,
			EmployeeHobby:                        employeeEntity.EmployeeHobby,
			EmployeeSkills:                       employeeEntity.EmployeeSkills,
			EmployeeCertificates:                 employeeEntity.EmployeeCertificates,
			EmployeeHealthStatus:                 employeeEntity.EmployeeHealthStatus,
			EmployeeBloodType:                    employeeEntity.EmployeeBloodType,
		}
		employeeVo.ID = employeeEntity.ID
		employeeVo.TenantID = employeeEntity.TenantID
		employeeVo.CreatedAt = employeeEntity.CreatedAt
		employeeVo.UpdatedAt = employeeEntity.UpdatedAt

		if !employeeEntity.EmployeeBirthday.IsZero() {
			employeeVo.EmployeeBirthday = &util.JsonDate{Time: employeeEntity.EmployeeBirthday}
		}
		if employeeEntity.EmployeeOutDate != nil {
			employeeVo.EmployeeOutDate = &util.JsonDate{Time: *employeeEntity.EmployeeOutDate}
		}
		if employeeEntity.EmployeeProbationEndDate != nil {
			employeeVo.EmployeeProbationEndDate = &util.JsonDate{Time: *employeeEntity.EmployeeProbationEndDate}
		}
		if employeeEntity.EmployeeContractStartDate != nil {
			employeeVo.EmployeeContractStartDate = &util.JsonDate{Time: *employeeEntity.EmployeeContractStartDate}
		}
		if employeeEntity.EmployeeContractEndDate != nil {
			employeeVo.EmployeeContractEndDate = &util.JsonDate{Time: *employeeEntity.EmployeeContractEndDate}
		}
		if employeeEntity.EmployeeSalary != 0 {
			salary := employeeEntity.EmployeeSalary
			employeeVo.EmployeeSalary = &salary
		}
		if employeeEntity.EmployeeWorkYears != 0 {
			workYears := employeeEntity.EmployeeWorkYears
			employeeVo.EmployeeWorkYears = &workYears
		}
		if employeeEntity.EmployeeFirstWorkDate != nil {
			employeeVo.EmployeeFirstWorkDate = &util.JsonDate{Time: *employeeEntity.EmployeeFirstWorkDate}
		}
		if employeeEntity.EmployeeGraduationDate != nil {
			employeeVo.EmployeeGraduationDate = &util.JsonDate{Time: *employeeEntity.EmployeeGraduationDate}
		}
		if employeeEntity.EmployeeHeight != 0 {
			h := employeeEntity.EmployeeHeight
			employeeVo.EmployeeHeight = &h
		}

		s.populateEmployeeVOEnhancements(ctx, employeeVo, employeeEntity)
		employeeVos = append(employeeVos, employeeVo)
	}

	// 使用从仓库层获取的分页信息，并将转换后的 VO 列表放入新的 PageResult
	return &response.PageResult{
		List:     employeeVos, // 这是转换后的 VO 列表
		Total:    pagedDataFromRepo.Total,
		PageNum:  pagedDataFromRepo.PageNum,  // 使用仓库层返回的 PageNum
		PageSize: pagedDataFromRepo.PageSize, // 使用仓库层返回的 PageSize
		Pages:    pagedDataFromRepo.Pages,    // 使用仓库层返回的 Pages
		Sort:     pagedDataFromRepo.Sort,     // 使用仓库层返回的 Sort (如果有)
	}, nil
}

// UpdateEmployeeStatus updates the status of an employee.
func (s *employeeServiceImpl) UpdateEmployeeStatus(ctx context.Context, id uint, status int) error {
	log := logger.WithContext(ctx)
	employeeRepo := s.getEmployeeRepository()

	_, err := employeeRepo.FindByID(ctx, id)
	if err != nil {
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "员工不存在")
		}
		log.WithError(err).Error("更新员工状态前查询失败")
		return err
	}

	if err := employeeRepo.UpdateStatus(ctx, id, status); err != nil {
		log.WithError(err).Error("更新员工状态失败")
		return err
	}
	return nil
}

// GetEmployeeSimpleList retrieves a simple list of employees.
func (s *employeeServiceImpl) GetEmployeeSimpleList(ctx context.Context, query dto.EmployeeSimpleListQueryDTO) ([]*vo.EmployeeSimpleVO, error) {
	log := logger.WithContext(ctx)
	opName := "GetEmployeeSimpleList"
	employeeRepo := s.getEmployeeRepository()

	entities, err := employeeRepo.FindSimpleList(ctx, query)
	if err != nil {
		log.WithError(err).Error(opName, "调用 Repository 查询员工简单列表失败")
		return nil, err
	}

	vos := make([]*vo.EmployeeSimpleVO, 0, len(entities))
	for _, empEntity := range entities {
		simpleVO := &vo.EmployeeSimpleVO{
			ID:           empEntity.ID,
			EmployeeCode: empEntity.EmployeeCode,
			EmployeeName: empEntity.EmployeeName,
		}
		vos = append(vos, simpleVO)
	}

	log.Info(opName, "成功获取员工简单列表", logger.WithField("count", len(vos)))
	return vos, nil
}

// --- Helper Methods (if any) ---

// GetTenantIDFromContext (示例，实际已在 BaseServiceImpl 中提供)
// func (s *employeeServiceImpl) GetTenantIDFromContext(ctx context.Context) (uint, error) {
// 	 tidStr, ok := ctx.Value("tenantId").(string)
// 	 if !ok || tidStr == "" {
// 		 return 0, apperrors.NewUnauthenticatedError("无法从上下文中获取租户ID")
// 	 }
// 	 tid, err := strconv.ParseUint(tidStr, 10, 64)
// 	 if err != nil {
// 		 return 0, apperrors.NewUnauthenticatedError("租户ID格式无效")
// 	 }
// 	 return uint(tid), nil
// }
