package repository

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"sync"

	"backend/pkg/logger"

	"gorm.io/gorm"
)

// RepositoryManager 管理所有仓库实例 (懒汉式加载 + 缓存)
type RepositoryManager struct {
	db    *gorm.DB // 数据库连接 (可能是原始连接或事务连接)
	cache sync.Map // Repository 实例缓存 (并发安全, 每个 manager 实例独立)
}

// NewRepositoryManager 创建仓库管理器
func NewRepositoryManager(db *gorm.DB) *RepositoryManager {
	return &RepositoryManager{
		db: db,
		// cache 初始化为空的 sync.Map (由 sync.Map 零值自动处理)
	}
}

// GetRepository 获取泛型 Repository 实例 (线程安全)
// R: Repository 接口类型
// factory: 创建该 Repository 实例的工厂函数，接收 *gorm.DB 参数
// rm: RepositoryManager 实例
func GetRepository[R any](rm *RepositoryManager, factory func(db *gorm.DB) R) R {
	// 使用接口类型 R 的反射类型作为缓存键
	typeOfR := reflect.TypeOf((*R)(nil)).Elem()

	// 1. 检查缓存
	if repo, ok := rm.cache.Load(typeOfR); ok {
		return repo.(R) // 类型断言是安全的，因为存入时保证了类型
	}

	// 2. 缓存未命中，使用工厂函数创建实例
	// 注意：工厂函数需要接收当前的 db 连接 (可能是原始的或事务的)
	newRepo := factory(rm.db)

	// 3. 存入缓存 (使用 LoadOrStore 确保并发安全下的原子性)
	actualRepo, _ := rm.cache.LoadOrStore(typeOfR, newRepo)

	return actualRepo.(R) // 返回实际存入缓存的实例
}

// DB 返回当前的数据库连接 (可能是原始连接或事务连接)
func (rm *RepositoryManager) DB() *gorm.DB {
	return rm.db
}

// Transaction 执行数据库事务
// ctx: 上下文
// fc: 在事务中执行的函数，接收一个绑定到事务的 RepositoryManager
// opts: 可选的事务选项 (例如隔离级别)
func (rm *RepositoryManager) Transaction(ctx context.Context, fc func(txRepoMgr *RepositoryManager) error, opts ...*sql.TxOptions) error {
	// 将 TxOptions 传递给 GORM 的 Transaction 方法
	return rm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 为事务创建一个新的、只包含事务 db 和空缓存的 RepositoryManager 实例
		txRepoMgr := rm.cloneWithTx(tx) // <<< 使用 cloneWithTx
		// 执行回调函数，传入事务性管理器
		err := fc(txRepoMgr)
		if err != nil {
			// 如果 fc 返回错误，事务将自动回滚 (由 GORM 处理)
			logger.WithContext(context.Background()).WithError(err).Error("事务执行失败，正在回滚...") // 使用背景上下文进行日志记录
			return fmt.Errorf("transaction failed: %w", err)                                // 返回包装后的错误
		}
		logger.WithContext(context.Background()).Debug("事务执行成功，正在提交...") // 如果 fc 成功执行，事务将自动提交
		return nil
	}, opts...)
}

// cloneWithTx 创建一个新的 RepositoryManager 实例，使用事务 DB
func (rm *RepositoryManager) cloneWithTx(tx *gorm.DB) *RepositoryManager {
	// 注意：新实例拥有独立的 cache，和事务 tx
	return &RepositoryManager{
		db: tx, // 使用事务 DB
		// cache 初始化为空的 sync.Map
	}
}

// --- Getters 使用泛型 GetRepository 实现懒加载 ---

// GetUserRepository 获取用户仓库
func (rm *RepositoryManager) GetUserRepository() UserRepository {
	return GetRepository(rm, NewUserRepository) // <<< 使用 GetRepository 和工厂函数
}

// GetRoleRepository 获取角色仓库
func (rm *RepositoryManager) GetRoleRepository() RoleRepository {
	return GetRepository(rm, NewRoleRepository) // <<< 使用 GetRepository 和工厂函数
}

// GetMenuRepository 获取菜单仓库
func (rm *RepositoryManager) GetMenuRepository() MenuRepository {
	return GetRepository(rm, NewMenuRepository) // <<< 使用 GetRepository 和工厂函数
}

// GetAccountBookRepository 获取账套仓库
func (rm *RepositoryManager) GetAccountBookRepository() AccountBookRepository {
	return GetRepository(rm, NewAccountBookRepository) // <<< 使用 GetRepository 和工厂函数
}

// GetUserRoleRepository 获取用户角色关联仓库
func (rm *RepositoryManager) GetUserRoleRepository() UserRoleRepository {
	return GetRepository(rm, NewUserRoleRepository) // <<< 使用 GetRepository 和工厂函数
}

// GetRoleMenuRepository 获取角色菜单关联仓库
func (rm *RepositoryManager) GetRoleMenuRepository() RoleMenuRepository {
	return GetRepository(rm, NewRoleMenuRepository) // <<< 使用 GetRepository 和工厂函数
}

// GetUserAccountBookRepository 获取用户账套关联仓库
func (rm *RepositoryManager) GetUserAccountBookRepository() UserAccountBookRepository {
	return GetRepository(rm, NewUserAccountBookRepository) // <<< 使用 GetRepository 和工厂函数
}

// GetFileMetadataRepository 获取文件元数据仓库
func (rm *RepositoryManager) GetFileMetadataRepository() FileMetadataRepository {
	return GetRepository(rm, NewFileMetadataRepository)
}

// GetDB 获取当前管理器使用的数据库连接 (可能是事务连接)
// 注意：这个方法现在和 DB() 方法重复，可以考虑保留一个
func (rm *RepositoryManager) GetDB() *gorm.DB {
	return rm.db
}

// GetSystemParameterRepository 获取系统参数仓库
func (rm *RepositoryManager) GetSystemParameterRepository() SystemParameterRepository {
	return GetRepository(rm, NewSystemParameterRepository)
}

// GetDictionaryTypeRepository 获取字典类型仓库
func (rm *RepositoryManager) GetDictionaryTypeRepository() DictionaryTypeRepository {
	return GetRepository(rm, NewDictionaryTypeRepository)
}

// GetDictionaryItemRepository 获取字典项仓库
func (rm *RepositoryManager) GetDictionaryItemRepository() DictionaryItemRepository {
	return GetRepository(rm, NewDictionaryItemRepository)
}

// GetOrganizationNodeRepository 获取组织节点仓库
func (rm *RepositoryManager) GetOrganizationNodeRepository() OrganizationNodeRepository {
	return GetRepository(rm, NewOrganizationNodeRepository)
}

// GetEmployeeRepository 获取员工仓库
func (rm *RepositoryManager) GetEmployeeRepository() EmployeeRepository {
	return GetRepository(rm, NewEmployeeRepository) // NewEmployeeRepository 已经在 hr_employee_repository_impl.go 中定义
}

// GetFinCurrencyRepository 获取币种仓库
func (rm *RepositoryManager) GetFinCurrencyRepository() FinCurrencyRepository {
	return GetRepository(rm, NewFinCurrencyRepository)
}

// GetFinTaxRateRepository 获取税率仓库
func (rm *RepositoryManager) GetFinTaxRateRepository() FinTaxRateRepository {
	return GetRepository(rm, NewFinTaxRateRepository)
}

// GetFinExchangeRateRepository 获取汇率仓库
func (rm *RepositoryManager) GetFinExchangeRateRepository() FinExchangeRateRepository {
	return GetRepository(rm, NewFinExchangeRateRepository)
}

// GetFiscalPeriodRepository 获取会计期间仓库
func (rm *RepositoryManager) GetFiscalPeriodRepository() FiscalPeriodRepository {
	return GetRepository(rm, NewFiscalPeriodRepository)
}

// GetWmsLocationRepository 获取库位仓库
func (rm *RepositoryManager) GetWmsLocationRepository() WmsLocationRepository {
	return GetRepository(rm, NewWmsLocationRepository)
}

// GetMtlItemRepository 获取物料仓库
func (rm *RepositoryManager) GetMtlItemRepository() MtlItemRepository {
	return GetRepository(rm, NewMtlItemRepository)
}

// GetSysCodeRuleRepository 获取编码规则仓库
func (rm *RepositoryManager) GetSysCodeRuleRepository() SysCodeRuleRepository {
	return GetRepository(rm, NewSysCodeRuleRepository)
}

// GetSysCodeFormatComponentRepository 获取编码格式组件仓库
func (rm *RepositoryManager) GetSysCodeFormatComponentRepository() SysCodeFormatComponentRepository {
	return GetRepository(rm, NewSysCodeFormatComponentRepository)
}

// GetSysCodeGenerationLogRepository 获取编码生成历史仓库
func (rm *RepositoryManager) GetSysCodeGenerationLogRepository() SysCodeGenerationLogRepository {
	return GetRepository(rm, NewSysCodeGenerationLogRepository)
}

// GetCrmCustomerRepository 获取CRM客户仓库
func (rm *RepositoryManager) GetCrmCustomerRepository() CrmCustomerRepository {
	return GetRepository(rm, NewCrmCustomerRepository)
}

// GetScmSupplierRepository 获取SCM供应商仓库
func (rm *RepositoryManager) GetScmSupplierRepository() ScmSupplierRepository {
	return GetRepository(rm, NewScmSupplierRepository)
}

// GetSequenceRepository 获取序列号生成仓库 (如果 SequenceRepository 和 NewSequenceRepository 已定义)
// func (rm *RepositoryManager) GetSequenceRepository() SequenceRepository {
// 	return GetRepository(rm, NewSequenceRepository)
// }

// GetWmsInboundNotificationRepository 获取入库通知单仓库
func (rm *RepositoryManager) GetWmsInboundNotificationRepository() WmsInboundNotificationRepository {
	return GetRepository(rm, NewWmsInboundNotificationRepository)
}

// GetWmsReceivingRecordRepository 获取收货记录仓库
func (rm *RepositoryManager) GetWmsReceivingRecordRepository() WmsReceivingRecordRepository {
	return GetRepository(rm, NewWmsReceivingRecordRepository)
}

// GetWmsReceivingRecordDetailRepository 获取收货记录明细仓库
func (rm *RepositoryManager) GetWmsReceivingRecordDetailRepository() WmsReceivingRecordDetailRepository {
	return GetRepository(rm, NewWmsReceivingRecordDetailRepository)
}

// GetWmsPutawayTaskRepository 获取上架任务仓库
func (rm *RepositoryManager) GetWmsPutawayTaskRepository() WmsPutawayTaskRepository {
	return GetRepository(rm, NewWmsPutawayTaskRepository)
}

// GetWmsPutawayTaskDetailRepository 获取上架任务明细仓库
func (rm *RepositoryManager) GetWmsPutawayTaskDetailRepository() WmsPutawayTaskDetailRepository {
	return GetRepository(rm, NewWmsPutawayTaskDetailRepository)
}

// GetWmsBlindReceivingConfigRepository 获取盲收配置仓库
func (rm *RepositoryManager) GetWmsBlindReceivingConfigRepository() WmsBlindReceivingConfigRepository {
	return GetRepository(rm, NewWmsBlindReceivingConfigRepository)
}

// GetWmsBlindReceivingValidationRepository 获取盲收验证记录仓库
func (rm *RepositoryManager) GetWmsBlindReceivingValidationRepository() WmsBlindReceivingValidationRepository {
	return GetRepository(rm, NewWmsBlindReceivingValidationRepository)
}

// --- WMS出库流程相关Repository ---

// GetWmsOutboundNotificationRepository 获取出库通知单仓库
func (rm *RepositoryManager) GetWmsOutboundNotificationRepository() WmsOutboundNotificationRepository {
	return GetRepository(rm, NewWmsOutboundNotificationRepository)
}

// GetWmsOutboundNotificationDetailRepository 获取出库通知单明细仓库
func (rm *RepositoryManager) GetWmsOutboundNotificationDetailRepository() WmsOutboundNotificationDetailRepository {
	return GetRepository(rm, NewWmsOutboundNotificationDetailRepository)
}

// GetWmsInventoryAllocationRepository 获取库存分配仓库
func (rm *RepositoryManager) GetWmsInventoryAllocationRepository() WmsInventoryAllocationRepository {
	return GetRepository(rm, NewWmsInventoryAllocationRepository)
}

// GetWmsPickingTaskRepository 获取拣货任务仓库
func (rm *RepositoryManager) GetWmsPickingTaskRepository() WmsPickingTaskRepository {
	return GetRepository(rm, NewWmsPickingTaskRepository)
}

// GetWmsPickingTaskDetailRepository 获取拣货任务明细仓库
func (rm *RepositoryManager) GetWmsPickingTaskDetailRepository() WmsPickingTaskDetailRepository {
	return GetRepository(rm, NewWmsPickingTaskDetailRepository)
}

// GetWmsShipmentRepository 获取发运单仓库
func (rm *RepositoryManager) GetWmsShipmentRepository() WmsShipmentRepository {
	return GetRepository(rm, NewWmsShipmentRepository)
}

// ===== 库存管理模块Repository =====

// GetWmsInventoryRepository 获取库存仓库
func (rm *RepositoryManager) GetWmsInventoryRepository() WmsInventoryRepository {
	return GetRepository(rm, NewWmsInventoryRepository)
}

// GetWmsInventoryTransactionLogRepository 获取库存事务日志仓库
func (rm *RepositoryManager) GetWmsInventoryTransactionLogRepository() WmsInventoryTransactionLogRepository {
	return GetRepository(rm, NewWmsInventoryTransactionLogRepository)
}

// GetWmsInventoryAdjustmentRepository 获取库存调整仓库
func (rm *RepositoryManager) GetWmsInventoryAdjustmentRepository() WmsInventoryAdjustmentRepository {
	return GetRepository(rm, NewWmsInventoryAdjustmentRepository)
}

// GetWmsInventoryMovementRepository 获取库存移动仓库
func (rm *RepositoryManager) GetWmsInventoryMovementRepository() WmsInventoryMovementRepository {
	return GetRepository(rm, NewWmsInventoryMovementRepository)
}

// GetWmsCycleCountPlanRepository 获取盘点计划仓库
func (rm *RepositoryManager) GetWmsCycleCountPlanRepository() WmsCycleCountPlanRepository {
	return GetRepository(rm, NewWmsCycleCountPlanRepository)
}

// GetWmsCycleCountTaskRepository 获取盘点任务仓库
func (rm *RepositoryManager) GetWmsCycleCountTaskRepository() WmsCycleCountTaskRepository {
	return GetRepository(rm, NewWmsCycleCountTaskRepository)
}

// GetWmsInventoryAlertRuleRepository 获取库存预警规则仓库
func (rm *RepositoryManager) GetWmsInventoryAlertRuleRepository() WmsInventoryAlertRuleRepository {
	return GetRepository(rm, NewWmsInventoryAlertRuleRepository)
}

// GetWmsInventoryAlertLogRepository 获取库存预警日志仓库
func (rm *RepositoryManager) GetWmsInventoryAlertLogRepository() WmsInventoryAlertLogRepository {
	return GetRepository(rm, NewWmsInventoryAlertLogRepository)
}
