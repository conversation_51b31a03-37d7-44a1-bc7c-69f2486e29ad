package dto

import (
	"backend/internal/model/entity"
	"backend/pkg/response"
	"time"
)

// WmsInventoryMovementCreateReq 库存移动创建请求
type WmsInventoryMovementCreateReq struct {
	AccountBookID  uint                            `json:"accountBookId" binding:"required" comment:"账套ID"`
	ItemID         uint                            `json:"itemId" binding:"required" comment:"物料ID"`
	FromLocationID uint                            `json:"fromLocationId" binding:"required" comment:"源库位ID"`
	ToLocationID   uint                            `json:"toLocationId" binding:"required" comment:"目标库位ID"`
	BatchNo        *string                         `json:"batchNo" comment:"批次号"`
	Quantity       float64                         `json:"quantity" binding:"required,gt=0" comment:"移动数量"`
	UnitOfMeasure  string                          `json:"unitOfMeasure" binding:"required" comment:"计量单位"`
	MovementType   entity.WmsInventoryMovementType `json:"movementType" binding:"required" comment:"移动类型"`
	MovementReason string                          `json:"movementReason" comment:"移动原因"`
	OperatorID     uint                            `json:"operatorId" binding:"required" comment:"操作员ID"`
}

// WmsInventoryMovementUpdateReq 库存移动更新请求
type WmsInventoryMovementUpdateReq struct {
	Quantity       *float64 `json:"quantity" comment:"移动数量"`
	MovementReason *string  `json:"movementReason" comment:"移动原因"`
}

// WmsInventoryMovementQueryReq 库存移动查询请求
type WmsInventoryMovementQueryReq struct {
	response.PageQuery
	MovementNo     *string                            `json:"movementNo" comment:"移动单号"`
	ItemID         *uint                              `json:"itemId" comment:"物料ID"`
	ItemSku        *string                            `json:"itemSku" comment:"物料SKU"`
	FromLocationID *uint                              `json:"fromLocationId" comment:"源库位ID"`
	ToLocationID   *uint                              `json:"toLocationId" comment:"目标库位ID"`
	BatchNo        *string                            `json:"batchNo" comment:"批次号"`
	MovementType   *entity.WmsInventoryMovementType   `json:"movementType" comment:"移动类型"`
	Status         *entity.WmsInventoryMovementStatus `json:"status" comment:"移动状态"`
	OperatorID     *uint                              `json:"operatorId" comment:"操作员ID"`
	CreatedStart   *time.Time                         `json:"createdStart" comment:"创建开始时间"`
	CreatedEnd     *time.Time                         `json:"createdEnd" comment:"创建结束时间"`
	WarehouseID    *uint                              `json:"warehouseId" comment:"仓库ID"`
}

// WmsInventoryMovementStartReq 库存移动开始请求
type WmsInventoryMovementStartReq struct {
	OperatorID uint `json:"operatorId" binding:"required" comment:"操作员ID"`
}

// WmsInventoryMovementCompleteReq 库存移动完成请求
type WmsInventoryMovementCompleteReq struct {
	OperatorID uint `json:"operatorId" binding:"required" comment:"操作员ID"`
}

// WmsInventoryMovementCancelReq 库存移动取消请求
type WmsInventoryMovementCancelReq struct {
	Reason string `json:"reason" binding:"required" comment:"取消原因"`
}

// WmsInventoryMovementBatchCreateReq 批量库存移动创建请求
type WmsInventoryMovementBatchCreateReq struct {
	Movements []*WmsInventoryMovementCreateReq `json:"movements" binding:"required,dive" comment:"移动列表"`
}

// WmsInventoryMovementBatchStartReq 批量库存移动开始请求
type WmsInventoryMovementBatchStartReq struct {
	IDs        []uint `json:"ids" binding:"required" comment:"移动ID列表"`
	OperatorID uint   `json:"operatorId" binding:"required" comment:"操作员ID"`
}

// WmsInventoryMovementBatchCompleteReq 批量库存移动完成请求
type WmsInventoryMovementBatchCompleteReq struct {
	IDs        []uint `json:"ids" binding:"required" comment:"移动ID列表"`
	OperatorID uint   `json:"operatorId" binding:"required" comment:"操作员ID"`
}

// WmsInventoryMovementHistoryReq 库存移动历史查询请求
type WmsInventoryMovementHistoryReq struct {
	response.PageQuery
	ItemID      *uint      `json:"itemId" comment:"物料ID"`
	LocationID  *uint      `json:"locationId" comment:"库位ID"`
	BatchNo     *string    `json:"batchNo" comment:"批次号"`
	DateStart   *time.Time `json:"dateStart" comment:"开始日期"`
	DateEnd     *time.Time `json:"dateEnd" comment:"结束日期"`
	WarehouseID *uint      `json:"warehouseId" comment:"仓库ID"`
}

// WmsInventoryMovementStatsReq 库存移动统计请求
type WmsInventoryMovementStatsReq struct {
	WarehouseID *uint      `json:"warehouseId" comment:"仓库ID"`
	ItemID      *uint      `json:"itemId" comment:"物料ID"`
	DateStart   *time.Time `json:"dateStart" comment:"开始日期"`
	DateEnd     *time.Time `json:"dateEnd" comment:"结束日期"`
	GroupBy     *string    `json:"groupBy" comment:"分组方式: day/week/month"`
}
