package entity

import (
	"time"
)

// 库存分配状态枚举
type WmsInventoryAllocationStatus string

const (
	AllocationStatusAllocated WmsInventoryAllocationStatus = "ALLOCATED" // 已分配
	AllocationStatusPicked    WmsInventoryAllocationStatus = "PICKED"    // 已拣货
	AllocationStatusReleased  WmsInventoryAllocationStatus = "RELEASED"  // 已释放
)

// WmsInventoryAllocation 对应数据库中的 wms_inventory_allocation 表
// 存储库存分配信息，记录出库明细与具体库存的分配关系
type WmsInventoryAllocation struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	OutboundDetailID    uint      `gorm:"column:outbound_detail_id;not null;comment:出库明细ID" json:"outboundDetailId"`       // 关联的出库通知单明细ID
	InventoryID         uint      `gorm:"column:inventory_id;not null;comment:库存ID" json:"inventoryId"`                   // 关联的库存ID
	AllocatedQty        float64   `gorm:"column:allocated_qty;type:numeric(12,4);not null;comment:分配数量" json:"allocatedQty"`
	PickedQty           float64   `gorm:"column:picked_qty;type:numeric(12,4);default:0;comment:已拣数量" json:"pickedQty"`
	Status              string    `gorm:"column:status;size:20;default:'ALLOCATED';comment:分配状态" json:"status"`
	AllocationTime      time.Time `gorm:"column:allocation_time;autoCreateTime;comment:分配时间" json:"allocationTime"`
	PickingTaskDetailID *uint     `gorm:"column:picking_task_detail_id;comment:关联拣货任务明细ID" json:"pickingTaskDetailId"` // 关联的拣货任务明细ID

	// 分配策略信息
	AllocationStrategy *string `gorm:"column:allocation_strategy;size:20;comment:分配策略" json:"allocationStrategy"` // FIFO, LIFO, FEFO等
	AllocationReason   *string `gorm:"column:allocation_reason;size:100;comment:分配原因" json:"allocationReason"`   // 分配原因说明

	Remark *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`

	// 关联字段
	OutboundDetail    WmsOutboundNotificationDetail `gorm:"foreignKey:OutboundDetailID;references:ID" json:"outboundDetail,omitempty"`    // 关联的出库明细
	Inventory         WmsInventory                  `gorm:"foreignKey:InventoryID;references:ID" json:"inventory,omitempty"`             // 关联的库存
	PickingTaskDetail *WmsPickingTaskDetail         `gorm:"foreignKey:PickingTaskDetailID;references:ID" json:"pickingTaskDetail,omitempty"` // 关联的拣货任务明细
}

// TableName 指定数据库表名方法
func (WmsInventoryAllocation) TableName() string {
	return "wms_inventory_allocation"
}

// GetAvailableQty 获取可拣货数量
func (w *WmsInventoryAllocation) GetAvailableQty() float64 {
	return w.AllocatedQty - w.PickedQty
}

// IsFullyPicked 判断是否已完全拣货
func (w *WmsInventoryAllocation) IsFullyPicked() bool {
	return w.PickedQty >= w.AllocatedQty
}

// CanPick 判断是否可以拣货
func (w *WmsInventoryAllocation) CanPick() bool {
	return w.Status == string(AllocationStatusAllocated) && w.GetAvailableQty() > 0
}
