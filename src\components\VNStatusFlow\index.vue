<template>
  <div class="vn-status-flow" :class="{ 'is-compact': compact, 'is-readonly': readonly }">
    <!-- 组件标题 -->
    <div v-if="showTitle" class="status-flow-title">
      <h4>{{ title || '状态流程' }}</h4>
      <!-- 折叠按钮 -->
      <el-button
        v-if="props.collapsible"
        link
        :icon="collapsed ? Expand : Fold"
        @click="toggleCollapse"
        class="collapse-btn"
      />
    </div>

    <!-- 状态流程图 -->
    <div class="status-flow-container" :class="`is-${mode}`">
      <div class="status-flow-track" v-show="!collapsed">
        <div
          v-for="(status, index) in statusList"
          :key="status.value"
          class="status-item"
          :class="{
            'is-active': status.value === currentStatus,
            'is-completed': isStatusCompleted(status.value),
            'is-disabled': !canTransitionTo(status.value) && status.value !== currentStatus
          }"
        >
          <!-- 状态节点 -->
          <StatusNode
            :status="status"
            :active="status.value === currentStatus"
            :completed="isStatusCompleted(status.value)"
            :disabled="!canTransitionTo(status.value) && status.value !== currentStatus"
            :size="config.displayConfig.nodeSize"
            :mode="mode"
            @click="handleNodeClick(status)"
          />

          <!-- 状态标签 -->
          <div class="status-label">
            <span class="status-text">{{ status.label }}</span>
            <span v-if="status.value === currentStatus" class="current-badge">当前</span>
            <div 
              v-if="status.description && config.displayConfig.showDescription" 
              class="status-description"
            >
              {{ status.description }}
            </div>
          </div>

          <!-- 连接线 -->
          <StatusLine
            v-if="index < statusList.length - 1"
            :completed="isStatusCompleted(statusList[index + 1]?.value ?? '')"
            :style="config.displayConfig.lineStyle"
            :mode="mode"
          />
        </div>
      </div>

      <!-- 进度指示器 -->
      <div v-if="config.displayConfig.showProgress" class="progress-indicator">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        <span class="progress-text">{{ progressPercentage }}%</span>
      </div>
    </div>

    <!-- 状态操作区域 -->
    <StatusActions
      v-if="config.displayConfig.showActions && !readonly"
      :available-transitions="availableTransitions"
      :current-status="currentStatus"
      :readonly="readonly"
      :check-permission="checkPermission"
      :get-transition-label="getTransitionLabel"
      :get-button-type="getButtonType"
      :get-button-icon-name="getButtonIconName"
      @status-change="handleStatusChange"
    />

    <!-- 状态变更确认对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="`确认${selectedTransition?.label || '状态变更'}`"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="status-change-form">
        <div class="change-info">
          <p>
            <strong>当前状态：</strong>
            <el-tag :color="getCurrentStatusConfig().bgColor" :text-color="getCurrentStatusConfig().color">
              {{ getCurrentStatusConfig().label }}
            </el-tag>
          </p>
          <p>
            <strong>目标状态：</strong>
            <el-tag :color="getTargetStatusConfig().bgColor" :text-color="getTargetStatusConfig().color">
              {{ getTargetStatusConfig().label }}
            </el-tag>
          </p>
        </div>
        
        <el-form
          ref="formRef"
          :model="changeForm"
          :rules="formRules"
          label-width="80px"
        >
          <el-form-item
            label="备注"
            prop="remark"
            :required="selectedTransition?.requireRemark"
          >
            <el-input
              v-model="changeForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入状态变更备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="confirmStatusChange"
          >
            确认变更
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Fold, Expand } from '@element-plus/icons-vue'
import StatusNode from './components/StatusNode.vue'
import StatusLine from './components/StatusLine.vue'  
import StatusActions from './components/StatusActions.vue'
import { useStatusFlow, statusFlowUtils } from './hooks/useStatusFlow'
import type { 
  VNStatusFlowProps, 
  VNStatusFlowEmits,
  StatusOption,
  StatusTransition
} from './types'

// ==================== Props & Emits ====================

const props = withDefaults(defineProps<VNStatusFlowProps>(), {
  readonly: false,
  compact: false,
  mode: 'horizontal',
  showTitle: false,
  collapsible: false,
  defaultCollapsed: false
})

const emit = defineEmits<VNStatusFlowEmits>()

// ==================== 状态流程管理 ====================

const {
  config,
  currentStatus,
  statusList,
  availableTransitions,
  canTransitionTo,
  isStatusCompleted,
  getStatusConfig,
  changeStatus,
  getTransitionLabel,
  getButtonType,
  getButtonIconName
} = useStatusFlow(
  props.businessType,
  props.currentStatus,
  props.config
)

// 监听外部状态变更
watch(() => props.currentStatus, (newStatus) => {
  if (newStatus !== currentStatus.value) {
    currentStatus.value = newStatus
  }
})

// ==================== 计算属性 ====================

// 进度百分比
const progressPercentage = computed(() => {
  return statusFlowUtils.calculateProgress(currentStatus.value, statusList.value)
})

// 获取当前状态配置
const getCurrentStatusConfig = (): StatusOption => {
  return getStatusConfig(currentStatus.value) || statusList.value[0] as StatusOption
}

// 获取目标状态配置
const getTargetStatusConfig = (): StatusOption => {
  return getStatusConfig(selectedTransition.value?.to || '') || statusList.value[0] as StatusOption
}

// ==================== 对话框状态 ====================

const dialogVisible = ref(false)
const submitting = ref(false)

// 选中的状态转换
const selectedTransition = ref<{
  to: string
  label: string
  requireRemark?: boolean
} | null>(null)

// 状态变更表单
const changeForm = reactive({
  remark: ''
})

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = computed(() => ({
  remark: [
    {
      required: selectedTransition.value?.requireRemark || false,
      message: '请输入状态变更备注',
      trigger: 'blur'
    }
  ]
}))

// ==================== 权限检查 ====================

const checkPermission = (permission: string): boolean => {
  // 这里实现权限检查逻辑
  // 可以从父组件传入权限检查函数，或者调用全局权限检查方法
  return true // 临时返回true，实际应该实现权限检查
}

// ==================== 折叠控制 ====================
const collapsed = ref<boolean>(props.defaultCollapsed)
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

// ==================== 事件处理 ====================

// 处理节点点击
const handleNodeClick = (status: StatusOption) => {
  if (props.readonly) return
  
  // 如果是当前状态，不处理
  if (status.value === currentStatus.value) return
  
  // 如果可以转换到目标状态，触发状态变更
  if (canTransitionTo(status.value)) {
    handleStatusChange(status.value)
  }
}

// 处理状态变更
const handleStatusChange = (newStatus: string) => {
  // 发出状态变更前事件
  emit('beforeStatusChange', newStatus, currentStatus.value)
  
  selectedTransition.value = {
    to: newStatus,
    label: getTransitionLabel(currentStatus.value, newStatus),
    requireRemark: ['CANCELLED', 'PARTIALLY_RECEIVED'].includes(newStatus)
  }
  
  // 重置表单
  changeForm.remark = ''
  
  // 显示确认对话框
  dialogVisible.value = true
}

// 确认状态变更
const confirmStatusChange = async () => {
  if (!selectedTransition.value) return
  
  try {
    // 表单验证
    if (formRef.value) {
      await formRef.value.validate()
    }
    
    submitting.value = true
    
    // 执行状态变更
    const result = await changeStatus(
      selectedTransition.value.to,
      changeForm.remark || undefined,
      props.businessData
    )
    
    if (result.success) {
      // 发出状态变更事件
      emit('statusChange', selectedTransition.value.to, currentStatus.value, changeForm.remark || undefined)
      
      // 发出状态变更后事件
      emit('afterStatusChange', selectedTransition.value.to, currentStatus.value)
      
      // 关闭对话框
      dialogVisible.value = false
      
      ElMessage.success(result.message || '状态变更成功')
    } else {
      ElMessage.error(result.message || '状态变更失败')
    }
    
  } catch (error: any) {
    console.error('状态变更失败:', error)
    if (error !== 'validation failed') {
      ElMessage.error('状态变更失败')
    }
  } finally {
    submitting.value = false
  }
}

// ==================== 暴露给父组件的方法 ====================

defineExpose({
  // 刷新状态
  refresh: () => {
    currentStatus.value = props.currentStatus
  },
  // 获取当前状态
  getCurrentStatus: () => currentStatus.value,
  // 获取可用转换
  getAvailableTransitions: () => availableTransitions.value,
  // 检查是否可以转换
  canTransitionTo,
  // 手动触发状态变更
  triggerStatusChange: handleStatusChange
})
</script>

<style scoped lang="scss">
.vn-status-flow {
  .status-flow-title {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    
    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .status-flow-container {
    &.is-horizontal {
      .status-flow-track {
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow-x: auto;
        padding: 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 8px;
        position: relative;
        
        .status-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          min-width: 100px;
          flex-shrink: 0;
          
          .status-label {
            margin-top: 8px;
            text-align: center;
            
            .status-text {
              display: block;
              font-size: 14px;
              color: #606266;
              font-weight: 500;
              transition: all 0.3s ease;
            }
            
            .current-badge {
              display: inline-block;
              margin-top: 4px;
              padding: 2px 8px;
              background: #409eff;
              color: white;
              font-size: 12px;
              border-radius: 10px;
            }
            
            .status-description {
              margin-top: 4px;
              font-size: 12px;
              color: #909399;
              max-width: 120px;
              line-height: 1.4;
            }
          }
          
          &.is-active {
            .status-label .status-text {
              color: #409eff;
              font-weight: 600;
            }
          }
          
          &.is-completed {
            .status-label .status-text {
              color: #67c23a;
            }
          }
          
          &.is-disabled {
            opacity: 0.5;
            
            .status-label .status-text {
              color: #c0c4cc;
            }
          }
        }
      }
    }
    
    &.is-vertical {
      .status-flow-track {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 8px;
        
        .status-item {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          width: 100%;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .status-label {
            margin-left: 16px;
            
            .status-text {
              font-size: 14px;
              color: #606266;
              font-weight: 500;
            }
            
            .current-badge {
              margin-left: 8px;
              padding: 2px 8px;
              background: #409eff;
              color: white;
              font-size: 12px;
              border-radius: 10px;
            }
            
            .status-description {
              margin-top: 4px;
              font-size: 12px;
              color: #909399;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
  
  .progress-indicator {
    margin-top: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .progress-bar {
      flex: 1;
      height: 6px;
      background: #e4e7ed;
      border-radius: 3px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }
    
    .progress-text {
      font-size: 12px;
      color: #909399;
      min-width: 35px;
    }
  }
  
  // 紧凑模式
  &.is-compact {
    .status-flow-container {
      .status-flow-track {
        padding: 12px;
        
        .status-item {
          min-width: 80px;
          
          .status-label {
            .status-text {
              font-size: 12px;
            }
            
            .status-description {
              display: none;
            }
          }
        }
      }
    }
  }
  
  // 只读模式
  &.is-readonly {
    .status-item {
      cursor: not-allowed;
    }
  }
}

.status-change-form {
  .change-info {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    
    p {
      margin: 8px 0;
      display: flex;
      align-items: center;
      gap: 8px;
      
      strong {
        min-width: 80px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .vn-status-flow {
    .status-flow-container.is-horizontal {
      .status-flow-track {
        padding: 16px;
        
        .status-item {
          min-width: 80px;
          
          .status-label {
            .status-text {
              font-size: 12px;
            }
            
            .status-description {
              display: none;
            }
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.status-item.is-active :deep(.status-node) {
  animation: pulse 2s infinite;
}
</style> 