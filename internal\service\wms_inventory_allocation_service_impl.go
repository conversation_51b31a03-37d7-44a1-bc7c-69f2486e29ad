package service

import (
	"context"
	"fmt"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsInventoryAllocationService 定义库存分配服务接口
type WmsInventoryAllocationService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsInventoryAllocationCreateReq) (*vo.WmsInventoryAllocationVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsInventoryAllocationUpdateReq) (*vo.WmsInventoryAllocationVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsInventoryAllocationVO, error)
	GetPage(ctx context.Context, req *dto.WmsInventoryAllocationQueryReq) (*vo.PageResult[vo.WmsInventoryAllocationListVO], error)

	// 批量操作
	BatchCreate(ctx context.Context, req *dto.WmsInventoryAllocationBatchCreateReq) ([]*vo.WmsInventoryAllocationVO, error)
	BatchRelease(ctx context.Context, req *dto.WmsInventoryAllocationBatchReleaseReq) error
	BatchPick(ctx context.Context, req *dto.WmsInventoryAllocationBatchPickReq) error

	// 自动分配
	AutoAllocate(ctx context.Context, req *dto.WmsInventoryAllocationAutoReq) ([]*vo.WmsInventoryAllocationVO, error)
	BatchAutoAllocate(ctx context.Context, req *dto.WmsInventoryAllocationBatchAutoReq) ([]*vo.WmsInventoryAllocationVO, error)

	// 分配优化
	OptimizeAllocation(ctx context.Context, req *dto.WmsInventoryAllocationOptimizeReq) error

	// 拣货确认
	PickConfirm(ctx context.Context, req *dto.WmsInventoryAllocationPickReq) error

	// 释放分配
	ReleaseAllocation(ctx context.Context, req *dto.WmsInventoryAllocationReleaseReq) error

	// 库存可用性检查
	CheckAvailability(ctx context.Context, req *dto.WmsInventoryAvailabilityCheckReq) (*vo.WmsInventoryAvailabilityVO, error)
	BatchCheckAvailability(ctx context.Context, req *dto.WmsInventoryAvailabilityBatchCheckReq) ([]*vo.WmsInventoryAvailabilityVO, error)

	// 库存预占
	ReserveInventory(ctx context.Context, req *dto.WmsInventoryReservationReq) (*vo.WmsInventoryReservationVO, error)
	ReleaseReservation(ctx context.Context, req *dto.WmsInventoryReservationReleaseReq) error

	// 统计分析
	GetStats(ctx context.Context, req *dto.WmsInventoryAllocationStatsReq) (*vo.WmsInventoryAllocationStatsVO, error)

	// 导出
	ExportToExcel(ctx context.Context, req *dto.WmsInventoryAllocationExportReq) ([]byte, error)

	// 业务查询
	GetByOutboundDetailID(ctx context.Context, outboundDetailID uint) ([]*vo.WmsInventoryAllocationVO, error)
	GetByInventoryID(ctx context.Context, inventoryID uint) ([]*vo.WmsInventoryAllocationVO, error)
	GetAllocationSummary(ctx context.Context, outboundDetailID uint) (*vo.WmsInventoryAllocationSummaryVO, error)
}

// wmsInventoryAllocationServiceImpl 库存分配服务实现
type wmsInventoryAllocationServiceImpl struct {
	BaseServiceImpl
}

// NewWmsInventoryAllocationService 创建库存分配服务
func NewWmsInventoryAllocationService(sm *ServiceManager) WmsInventoryAllocationService {
	return &wmsInventoryAllocationServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建库存分配
func (s *wmsInventoryAllocationServiceImpl) Create(ctx context.Context, req *dto.WmsInventoryAllocationCreateReq) (*vo.WmsInventoryAllocationVO, error) {
	var allocation *entity.WmsInventoryAllocation
	var voResult *vo.WmsInventoryAllocationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryAllocationRepository()

		// 验证出库明细是否存在
		detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()
		_, err := detailRepo.FindByID(ctx, req.OutboundDetailID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库明细不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库明细失败").WithCause(err)
		}

		// TODO: 验证库存是否存在且可用
		// TODO: 验证分配数量是否合理

		allocation = &entity.WmsInventoryAllocation{}
		copier.Copy(allocation, req)
		allocation.Status = string(entity.AllocationStatusAllocated)
		allocation.AllocationTime = time.Now()

		// 强制从上下文获取账套ID - 账套强绑定
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空")
		}
		allocation.AccountBookID = uint(accountBookID)

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}
		allocation.CreatedBy = uint(userID)

		// 创建分配记录
		if err := repo.Create(ctx, allocation); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建库存分配失败").WithCause(err)
		}

		// 更新出库明细的分配数量
		if err := s.updateDetailAllocatedQty(ctx, txRepoMgr, req.OutboundDetailID); err != nil {
			return fmt.Errorf("更新明细分配数量失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, allocation)
	return voResult, nil
}

// AutoAllocate 自动分配库存
func (s *wmsInventoryAllocationServiceImpl) AutoAllocate(ctx context.Context, req *dto.WmsInventoryAllocationAutoReq) ([]*vo.WmsInventoryAllocationVO, error) {
	var results []*vo.WmsInventoryAllocationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		// 获取出库明细
		detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()
		detail, err := detailRepo.FindByID(ctx, req.OutboundDetailID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库明细不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库明细失败").WithCause(err)
		}

		// 检查是否已有分配记录
		allocationRepo := txRepoMgr.GetWmsInventoryAllocationRepository()
		existingAllocations, err := allocationRepo.FindByOutboundDetailID(ctx, req.OutboundDetailID)
		if err != nil {
			return fmt.Errorf("查询现有分配记录失败: %w", err)
		}

		// 计算已分配数量
		allocatedQty := float64(0)
		for _, allocation := range existingAllocations {
			if allocation.Status == string(entity.AllocationStatusAllocated) {
				allocatedQty += allocation.AllocatedQty
			}
		}

		// 计算剩余需要分配的数量
		remainingQty := detail.RequiredQty - allocatedQty
		if remainingQty <= 0 {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "该明细已完全分配")
		}

		// 根据策略查找可用库存
		availableInventories, err := s.findAvailableInventories(ctx, txRepoMgr, detail, req.AllocationStrategy, remainingQty)
		if err != nil {
			return fmt.Errorf("查找可用库存失败: %w", err)
		}

		if len(availableInventories) == 0 {
			if req.ForceAllocate {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "没有可用库存，无法强制分配")
			}
			return nil // 没有可用库存，返回空结果
		}

		// 执行分配
		allocations, err := s.performAllocation(ctx, txRepoMgr, detail, availableInventories, remainingQty, req)
		if err != nil {
			return fmt.Errorf("执行分配失败: %w", err)
		}

		// 转换为VO
		for _, allocation := range allocations {
			vo := &vo.WmsInventoryAllocationVO{}
			copier.Copy(vo, allocation)
			results = append(results, vo)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return results, nil
}

// BatchAutoAllocate 批量自动分配库存
func (s *wmsInventoryAllocationServiceImpl) BatchAutoAllocate(ctx context.Context, req *dto.WmsInventoryAllocationBatchAutoReq) ([]*vo.WmsInventoryAllocationVO, error) {
	var allResults []*vo.WmsInventoryAllocationVO

	for _, detailID := range req.OutboundDetailIDs {
		autoReq := &dto.WmsInventoryAllocationAutoReq{
			OutboundDetailID:   detailID,
			AllocationStrategy: req.AllocationStrategy,
			ForceAllocate:      req.ForceAllocate,
			MaxAllocations:     req.MaxAllocations,
		}

		results, err := s.AutoAllocate(ctx, autoReq)
		if err != nil {
			// 记录错误但继续处理其他明细
			continue
		}

		allResults = append(allResults, results...)
	}

	return allResults, nil
}

// PickConfirm 拣货确认
func (s *wmsInventoryAllocationServiceImpl) PickConfirm(ctx context.Context, req *dto.WmsInventoryAllocationPickReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryAllocationRepository()

		// 检查分配记录是否存在
		allocation, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库存分配记录不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存分配记录失败").WithCause(err)
		}

		// 检查状态是否允许拣货
		if allocation.Status != string(entity.AllocationStatusAllocated) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已分配状态的记录才能拣货")
		}

		// 验证拣货数量
		if req.PickedQty > allocation.AllocatedQty {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货数量不能超过分配数量")
		}

		// 更新拣货数量和状态
		if err := repo.UpdatePickedQty(ctx, req.ID, req.PickedQty); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新拣货数量失败").WithCause(err)
		}

		if err := repo.UpdateStatus(ctx, req.ID, string(entity.AllocationStatusPicked)); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新分配状态失败").WithCause(err)
		}

		// 更新出库明细的拣货数量
		if err := s.updateDetailPickedQty(ctx, txRepoMgr, allocation.OutboundDetailID); err != nil {
			return fmt.Errorf("更新明细拣货数量失败: %w", err)
		}

		return nil
	})
}

// ReleaseAllocation 释放分配
func (s *wmsInventoryAllocationServiceImpl) ReleaseAllocation(ctx context.Context, req *dto.WmsInventoryAllocationReleaseReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryAllocationRepository()

		// 检查分配记录是否存在
		allocation, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库存分配记录不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存分配记录失败").WithCause(err)
		}

		// 检查状态是否允许释放
		if allocation.Status == string(entity.AllocationStatusReleased) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "分配记录已经是释放状态")
		}
		if allocation.Status == string(entity.AllocationStatusPicked) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已拣货的分配记录不能释放")
		}

		// 释放分配
		reason := ""
		if req.Reason != nil {
			reason = *req.Reason
		}

		if err := repo.ReleaseAllocation(ctx, req.ID, reason); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "释放库存分配失败").WithCause(err)
		}

		// 更新出库明细的分配数量
		if err := s.updateDetailAllocatedQty(ctx, txRepoMgr, allocation.OutboundDetailID); err != nil {
			return fmt.Errorf("更新明细分配数量失败: %w", err)
		}

		return nil
	})
}

// GetByOutboundDetailID 根据出库明细ID获取分配记录
func (s *wmsInventoryAllocationServiceImpl) GetByOutboundDetailID(ctx context.Context, outboundDetailID uint) ([]*vo.WmsInventoryAllocationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryAllocationRepository()

	allocations, err := repo.FindByOutboundDetailID(ctx, outboundDetailID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存分配记录失败").WithCause(err)
	}

	var results []*vo.WmsInventoryAllocationVO
	for _, allocation := range allocations {
		vo := &vo.WmsInventoryAllocationVO{}
		copier.Copy(vo, allocation)

		// 填充扩展信息
		s.fillExtendedInfo(ctx, vo)

		results = append(results, vo)
	}

	return results, nil
}

// BatchCreate 批量创建库存分配
func (s *wmsInventoryAllocationServiceImpl) BatchCreate(ctx context.Context, req *dto.WmsInventoryAllocationBatchCreateReq) ([]*vo.WmsInventoryAllocationVO, error) {
	var results []*vo.WmsInventoryAllocationVO

	for _, allocation := range req.Allocations {
		result, err := s.Create(ctx, &allocation)
		if err != nil {
			// 记录错误但继续处理其他分配
			continue
		}
		results = append(results, result)
	}

	return results, nil
}

// BatchRelease 批量释放分配
func (s *wmsInventoryAllocationServiceImpl) BatchRelease(ctx context.Context, req *dto.WmsInventoryAllocationBatchReleaseReq) error {
	for _, id := range req.IDs {
		releaseReq := &dto.WmsInventoryAllocationReleaseReq{
			ID:     id,
			Reason: req.Reason,
		}

		if err := s.ReleaseAllocation(ctx, releaseReq); err != nil {
			// 记录错误但继续处理其他分配
			continue
		}
	}

	return nil
}

// BatchPick 批量拣货确认
func (s *wmsInventoryAllocationServiceImpl) BatchPick(ctx context.Context, req *dto.WmsInventoryAllocationBatchPickReq) error {
	for _, pick := range req.Picks {
		pickReq := &dto.WmsInventoryAllocationPickReq{
			ID:        pick.ID,
			PickedQty: pick.PickedQty,
			Remark:    pick.Remark,
		}

		if err := s.PickConfirm(ctx, pickReq); err != nil {
			// 记录错误但继续处理其他分配
			continue
		}
	}

	return nil
}

// OptimizeAllocation 分配优化
func (s *wmsInventoryAllocationServiceImpl) OptimizeAllocation(ctx context.Context, req *dto.WmsInventoryAllocationOptimizeReq) error {
	// TODO: 实现分配优化逻辑
	return nil
}

// 注意：CheckAvailability 和 BatchCheckAvailability 方法已在 helper 文件中实现

// ReserveInventory 库存预占
func (s *wmsInventoryAllocationServiceImpl) ReserveInventory(ctx context.Context, req *dto.WmsInventoryReservationReq) (*vo.WmsInventoryReservationVO, error) {
	// TODO: 实现库存预占逻辑
	result := &vo.WmsInventoryReservationVO{}
	return result, nil
}

// ReleaseReservation 释放库存预占
func (s *wmsInventoryAllocationServiceImpl) ReleaseReservation(ctx context.Context, req *dto.WmsInventoryReservationReleaseReq) error {
	// TODO: 实现释放库存预占逻辑
	return nil
}

// GetStats 获取统计数据
func (s *wmsInventoryAllocationServiceImpl) GetStats(ctx context.Context, req *dto.WmsInventoryAllocationStatsReq) (*vo.WmsInventoryAllocationStatsVO, error) {
	// TODO: 实现统计分析逻辑
	result := &vo.WmsInventoryAllocationStatsVO{}
	return result, nil
}

// ExportToExcel 导出到Excel
func (s *wmsInventoryAllocationServiceImpl) ExportToExcel(ctx context.Context, req *dto.WmsInventoryAllocationExportReq) ([]byte, error) {
	// TODO: 实现Excel导出逻辑
	return []byte{}, nil
}
