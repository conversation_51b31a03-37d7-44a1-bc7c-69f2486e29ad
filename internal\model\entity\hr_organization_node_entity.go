package entity

const (
	NodeTypeCompany    = "company"
	NodeTypeDepartment = "department"
	NodeTypePosition   = "position"
)

// OrganizationNode 组织节点实体
// 代表组织架构中的一个单元，可以是部门或岗位
type OrganizationNode struct {
	TenantEntity                         // 嵌入租户实体 (包含 BaseEntity 和 TenantID)
	AccountBookID     uint               `gorm:"column:account_book_id;not null;index;comment:所属账套ID" json:"accountBookId"` // 所属账套ID (外键, 关联 sys_account_books.id)
	ParentID          *uint               `gorm:"column:parent_id;index;comment:上级节点ID" json:"parentId"`                     // 上级节点ID (nil 表示顶层节点)
	NodeType          string             `gorm:"column:node_type;size:20;not null;index;comment:节点类型" json:"nodeType"`      // 节点类型 (department, position)
	Name              string             `gorm:"column:name;size:100;not null;comment:节点名称" json:"name"`                    // 节点名称 (部门或岗位名称)
	Code              string             `gorm:"column:code;size:50;index;comment:节点编码" json:"code,omitempty"`              // 节点编码 (可选)
	LeaderUserID      *uint              `gorm:"column:leader_user_id;comment:负责人用户ID" json:"leaderUserId,omitempty"`       // 负责人用户ID (可选, 通常用于部门)
	Level             int                `gorm:"column:level;comment:节点层级" json:"level"`                                    // 节点层级 (从顶层部门开始计算)
	OrderNum          int                `gorm:"column:order_num;default:0;comment:排序号" json:"orderNum"`                    // 同级排序号
	Weight            *int               `gorm:"column:weight;comment:岗位权重" json:"weight,omitempty"`                        // 岗位权重 (仅用于岗位, 值越小级别越高)
	IsVirtual         bool               `gorm:"column:is_virtual;default:false;comment:是否虚拟节点" json:"isVirtual"`           // 是否虚拟节点 (部门或岗位)
	StandardHeadcount int                `gorm:"column:standard_headcount;comment:标配人数" json:"standardHeadcount,omitempty"` // 标配人数 (仅用于非虚拟岗位)
	Status            int                `gorm:"column:status;default:1;comment:状态" json:"status"`                          // 状态 (例如: 1=启用, 0=禁用)
	Remarks           string             `gorm:"column:remarks;size:500;comment:备注" json:"remarks,omitempty"`               // 备注信息
	CompanyID         *uint               `gorm:"column:company_id;index;comment:所属公司ID" json:"companyId"`                   // 所属公司
	Parent            *OrganizationNode  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
	Children          []OrganizationNode `gorm:"foreignKey:ParentID" json:"children,omitempty"`
}

// TableName 指定表名
func (OrganizationNode) TableName() string {
	return "hr_organization_node"
}
