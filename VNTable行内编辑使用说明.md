# VNTable 行内编辑功能使用说明

## 🎯 功能概述

VNTable 组件支持两种编辑模式：

- **行编辑模式** (`editMode: 'row'`)：点击编辑按钮后整行进入编辑状态
- **单元格编辑模式** (`editMode: 'cell'`)：点击单元格直接编辑

## 📋 配置要求

### 1. 基础配置

```typescript
const tableProps = {
  editable: true, // 启用编辑功能
  editMode: "row", // 设置编辑模式：'row' | 'cell'
  showOperations: true, // 显示操作列（行编辑模式必需）
  operationWidth: 150, // 操作列宽度
  rowKey: "id", // 行唯一标识
};
```

### 2. 列配置

```typescript
const columns = [
  {
    prop: "sku",
    label: "物料SKU",
    editable: true, // 该列可编辑
    editComponent: "input", // 编辑组件类型
    editComponentProps: {
      // 编辑组件属性
      placeholder: "输入SKU",
      clearable: true,
    },
    rules: [
      // 验证规则（可选）
      { required: true, message: "请输入SKU" },
    ],
  },
];
```

## 🔧 支持的编辑组件

### 1. 输入框 (`input`)

```typescript
{
  editComponent: 'input',
  editComponentProps: {
    placeholder: '请输入',
    clearable: true,
    maxlength: 50
  }
}
```

### 2. 数字输入框 (`input-number`)

```typescript
{
  editComponent: 'input-number',
  editComponentProps: {
    min: 0,
    max: 999999,
    precision: 2,
    controlsPosition: 'right'
  }
}
```

### 3. 下拉选择 (`select`)

```typescript
{
  editComponent: 'select',
  editComponentProps: {
    placeholder: '请选择',
    filterable: true,
    clearable: true,
    options: [
      { label: '选项1', value: 'value1' },
      { label: '选项2', value: 'value2' }
    ]
  }
}
```

### 4. 日期选择器 (`datepicker`)

```typescript
{
  editComponent: 'datepicker',
  editComponentProps: {
    type: 'date',
    placeholder: '选择日期',
    valueFormat: 'YYYY-MM-DD'
  }
}
```

### 5. 开关 (`switch`)

```typescript
{
  editComponent: 'switch',
  editComponentProps: {
    activeText: '是',
    inactiveText: '否'
  }
}
```

## 🎮 操作流程

### 行编辑模式操作步骤：

1. **进入编辑模式**

   - 点击操作列的"编辑"按钮（铅笔图标）
   - 整行进入编辑状态，可编辑列变为输入控件

2. **编辑数据**

   - 在各个可编辑单元格中修改数据
   - 支持 Tab 键切换到下一个可编辑字段
   - 实时验证输入数据

3. **保存或取消**
   - 点击"保存"按钮（对勾图标）：验证并保存数据
   - 点击"取消"按钮（叉号图标）：恢复原始数据

### 单元格编辑模式操作步骤：

1. **直接编辑**

   - 点击任意可编辑单元格
   - 单元格立即变为编辑状态

2. **保存数据**
   - 按 Enter 键或点击其他地方自动保存
   - 失去焦点时触发验证

## 📝 事件处理

```typescript
// 在 VNTable 上监听编辑事件
<VNTable
  @row-save="handleRowSave"
  @row-cancel="handleRowCancel"
  @cell-edit="handleCellEdit"
/>

// 事件处理方法
const handleRowSave = (row: any, index: number, oldRow: any) => {
  console.log('保存行数据:', row);
  // 这里可以调用 API 保存数据
};

const handleRowCancel = (row: any, index: number) => {
  console.log('取消编辑:', row);
};

const handleCellEdit = (payload: { row: any, column: any, value: any, oldValue: any }) => {
  console.log('单元格编辑:', payload);
};
```

## ✅ 数据验证

### 1. 列级验证规则

```typescript
{
  prop: 'email',
  label: '邮箱',
  editable: true,
  editComponent: 'input',
  rules: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ]
}
```

### 2. 自定义验证

```typescript
{
  prop: 'age',
  label: '年龄',
  editable: true,
  editComponent: 'input-number',
  rules: [
    {
      validator: (rule: any, value: any, callback: Function) => {
        if (value < 18 || value > 65) {
          callback(new Error('年龄必须在18-65之间'));
        } else {
          callback();
        }
      }
    }
  ]
}
```

## 🚨 常见问题

### 1. 编辑按钮不显示

**原因**：`showOperations` 未设置为 `true` 或 `editable` 为 `false`
**解决**：确保配置正确

```typescript
{
  editable: true,
  showOperations: true,
  editMode: 'row'
}
```

### 2. 单元格无法编辑

**原因**：列配置中 `editable` 未设置为 `true`
**解决**：在列配置中添加 `editable: true`

### 3. 验证不生效

**原因**：缺少验证规则或规则格式错误
**解决**：检查 `rules` 配置是否正确

### 4. 编辑组件不显示

**原因**：`editComponent` 类型错误或 `editComponentProps` 配置问题
**解决**：检查组件类型和属性配置

## 💡 最佳实践

1. **合理设置操作列宽度**：根据按钮数量调整 `operationWidth`
2. **提供清晰的验证提示**：使用有意义的错误消息
3. **优化用户体验**：设置合适的 placeholder 和默认值
4. **处理异步保存**：在 `row-save` 事件中处理 API 调用
5. **数据备份**：编辑前备份原始数据，便于取消操作

## 🔗 相关组件

- **VNForm**：集成了 VNTable 的表单组件，支持主表+明细表模式
- **VNSearchForm**：搜索表单组件，常与 VNTable 配合使用

---

通过以上配置和操作，您可以充分利用 VNTable 的行内编辑功能，为用户提供流畅的数据编辑体验。
