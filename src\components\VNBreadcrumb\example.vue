<template>
  <div class="breadcrumb-examples">
    <h2>VNBreadcrumb 面包屑组件示例</h2>
    
    <div class="example-card">
      <h3>基础用法</h3>
      <VNBreadcrumb :items="basicItems" />
      <el-divider />
      <pre>{{ JSON.stringify(basicItems, null, 2) }}</pre>
    </div>
    
    <div class="example-card">
      <h3>带路由跳转</h3>
      <VNBreadcrumb :items="routerItems" />
      <el-divider />
      <pre>{{ JSON.stringify(routerItems, null, 2) }}</pre>
    </div>
    
    <div class="example-card">
      <h3>带点击事件</h3>
      <VNBreadcrumb :items="eventItems" />
      <el-divider />
      <pre>{{ JSON.stringify(eventItemsDisplay, null, 2) }}</pre>
      <p v-if="lastClicked">最后点击: {{ lastClicked }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElDivider } from 'element-plus';
import VNBreadcrumb from './index.vue';
import type { BreadcrumbItem } from './types';
import { useRouter } from 'vue-router';

const router = useRouter();
const lastClicked = ref('');

// 基础示例
const basicItems = ref<BreadcrumbItem[]>([
  { label: '首页' },
  { label: '产品管理' },
  { label: '产品列表' }
]);

// 路由跳转示例
const routerItems = ref<BreadcrumbItem[]>([
  { label: '首页', to: { name: 'VNTableExample' } },
  { label: '表单示例', to: { name: 'VNFormExample' } },
  { label: '当前页面' }
]);

// 点击事件示例
const eventItems = ref<BreadcrumbItem[]>([
  { 
    label: '首页', 
    handler: () => {
      lastClicked.value = '首页';
      router.push('/');
    } 
  },
  { 
    label: '产品管理', 
    handler: () => {
      lastClicked.value = '产品管理';
    } 
  },
  { 
    label: '产品详情', 
    handler: () => {
      lastClicked.value = '产品详情';
    } 
  }
]);

// 用于显示的对象（移除handler函数）
const eventItemsDisplay = computed(() => {
  return eventItems.value.map(item => ({
    label: item.label,
    handler: item.handler ? '() => {...}' : undefined
  }));
});
</script>

<style scoped>
.breadcrumb-examples {
  padding: 20px;
}

.example-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

h2 {
  margin-bottom: 20px;
}

h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  overflow: auto;
}
</style> 