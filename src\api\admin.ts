import request from '@/utils/request';

// 请求体类型
interface ExecuteSqlDTO {
  sql: string;
}

// API 响应数据类型 (与后端 /admin/sql-tool 返回的 data 部分匹配)
// 注意：这应该与 src/views/system/sql-tool/index.vue 中的 SqlResponseData 保持一致
export interface SqlResponseData {
  resultType: 'QueryResult' | 'ExecResult';
  columns?: string[];
  rows?: any[][];
  executedSql?: string;
  durationMs?: number;
  message: string;
}

/**
 * 执行 SQL 语句 (管理员权限)
 * @param data 包含 SQL 查询语句的对象
 * @returns Promise<SqlResponseData>
 */
export function executeSql(data: ExecuteSqlDTO): Promise<SqlResponseData> {
  // 假设后端接口是 POST /api/v1/admin/sql-tool
  // 并且响应拦截器会返回 res.data
  // 因此，不需要额外的 .then() 来提取 data
  return request.post<SqlResponseData>('/admin/sql-tool', data, {
    // 可以为高风险操作设置更短的超时时间，如果需要的话
    // timeout: 10000, // 例如 10 秒
  }) as any;
} 