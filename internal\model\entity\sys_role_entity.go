package entity

//"time"

// Role 角色实体
// 用于存储系统角色信息，实现基于角色的访问控制(RBAC)
// 继承自TenantEntity，支持多租户功能
type Role struct {
	TenantEntity        // 继承租户实体基类，包含ID、创建时间、更新时间等基础字段
	ID           uint   `gorm:"primarykey" json:"id"`                                                 // 主键ID
	Name         string `gorm:"column:name;size:50;not null" json:"name" sortable:"true"` // 角色名称：角色的显示名称，如"系统管理员"，不能为空且唯一
	Code         string `gorm:"column:code;size:50;not null" json:"code" sortable:"true"` // 角色编码：角色的唯一标识符，如"ADMIN"，用于程序中引用角色，不能为空且唯一
	Status       int    `gorm:"column:status;default:1;index" json:"status" sortable:"true"`          // 状态：0-禁用（角色不可用），1-正常（角色可用），建立索引提高查询效率
	DataScope    int    `gorm:"column:data_scope;default:1" json:"data_scope" sortable:"true"`        // 数据权限范围：控制角色可访问的数据范围，1-全部数据，2-自定义数据，3-本部门数据，4-本部门及以下数据，5-仅本人数据
	Remark       string `gorm:"column:remark;size:500" json:"remark"`                                 // 备注：关于角色的附加说明信息
	IsSystem  bool `gorm:"column:is_system;default:false" json:"isSystem"`    // 是否是系统角色：true-系统内置角色（不可删除），false-自定义角色
	IsDefault bool `gorm:"column:is_default;default:false" json:"is_default"` // 是否是默认角色：true-新用户默认分配此角色，false-非默认角色

	// 关联字段
	Users []User `gorm:"many2many:sys_user_role;foreignKey:ID;joinForeignKey:RoleID;References:ID;joinReferences:UserID" json:"users,omitempty"` // 角色用户：拥有该角色的用户列表，多对多关系
	// many2many:user_role - 指定中间表名为sys_user_role
	// foreignKey:ID - 指定Role表的外键为ID
	// joinForeignKey:RoleID - 指定中间表中关联Role的外键为RoleID
	// References:ID - 指定User表的引用键为ID
	// joinReferences:UserID - 指定中间表中关联User的引用键为UserID
	// json:"users,omitempty" - JSON序列化时，如果users为空则忽略此字段

	Menus []Menu `gorm:"many2many:sys_role_menu;foreignKey:ID;joinForeignKey:RoleID;References:ID;joinReferences:MenuID" json:"menus,omitempty"` // 角色菜单：该角色可访问的菜单列表，多对多关系
	// many2many:role_menu - 指定中间表名为sys_role_menu
	// foreignKey:ID - 指定Role表的外键为ID
	// joinForeignKey:RoleID - 指定中间表中关联Role的外键为RoleID
	// References:ID - 指定Menu表的引用键为ID
	// joinReferences:MenuID - 指定中间表中关联Menu的引用键为MenuID
	// json:"menus,omitempty" - JSON序列化时，如果menus为空则忽略此字段
}

// TableName 指定角色表名
// 当前被注释掉，使用GORM默认的表名规则
// 取消注释后可自定义表名为"sys_role"
func (Role) TableName() string {
	return "sys_role"
}
