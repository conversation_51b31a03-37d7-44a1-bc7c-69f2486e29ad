<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'none'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="80"
      @refresh="loadData"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
    >
      <!-- Status Column Slot -->
      <template #column-status="{ row }">
        <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
          {{ row.status === 'success' ? '成功' : '失败' }}
        </el-tag>
      </template>

      <!-- Operation Buttons Slot -->
      <template #operation="{ row }">
        <el-tooltip content="查看详情" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)"></el-button>
        </el-tooltip>
      </template>

    </VNTable>

    <!-- Details Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="60%"
      :close-on-click-modal="false"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2"
        :label-width="'120px'"
        :show-submit-buttons="false"
        group-title-type="h4"
        disabled
      >
        <!-- Slots for JSON content display -->
        <template #form-item-oldValue="{ field }">
          <pre class="json-viewer">{{ formatJson(formData[field.field as keyof AuditLogVO]) }}</pre>
        </template>
        <template #form-item-newValue="{ field }">
          <pre class="json-viewer">{{ formatJson(formData[field.field as keyof AuditLogVO]) }}</pre>
        </template>
        <template #form-item-details="{ field }">
           <pre class="json-viewer">{{ formatJson(formData[field.field as keyof AuditLogVO]) }}</pre>
        </template>

         <template #actions> 
            <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, PaginationConfig, ActionButton } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElTag, ElButton, ElTooltip } from 'element-plus';
import { View } from '@element-plus/icons-vue';
import { getAuditLogList } from '@/api/audit';
import type { AuditLogVO, AuditLogQuery, AuditLogListResponse } from '@/api/audit';

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const loading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref('');

// --- Reactive State ---
const tableData = ref<AuditLogVO[]>([]);
const pagination = reactive<PaginationConfig>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const queryParams = reactive<AuditLogQuery>({
    username: undefined,
    action: undefined,
    resourceType: undefined,
    clientIp: undefined,
    traceId: undefined,
    status: undefined,
    startDate: undefined,
    endDate: undefined,
    sort: 'timestamp:desc', // Default sort
});
const formData = ref<Partial<AuditLogVO>>({});

// --- Table Configuration ---
const toolbarConfig = reactive({
  add: false,
  batchDelete: false,
  import: false,
  export: false,
  filter: true,
  refresh: true,
});

const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'id', label: 'ID', sortable: true, width: 80 },
  { prop: 'username', label: '用户名', sortable: true, filterable: true, filterType: 'text', width: 120 },
  { prop: 'action', label: '操作', sortable: true, filterable: true, filterType: 'text', width: 150 },
  { prop: 'resourceType', label: '资源类型', sortable: true, filterable: true, filterType: 'text', width: 150 },
  { prop: 'resourceId', label: '资源ID', sortable: true, width: 150 },
  { prop: 'status', label: '状态', sortable: true, slotName: 'column-status', filterable: true, 
    filterType: 'select', filterOptions: [{label: '成功', value: 'success'}, {label: '失败', value: 'failure'}], width: 100 },
  { prop: 'clientIp', label: '客户端IP', sortable: false, filterable: true, filterType: 'text', width: 130 },
  { prop: 'timestamp', label: '时间戳', sortable: true, width: 180, formatter: (row: AuditLogVO) => formatDateTime(row.timestamp) },
  { prop: 'durationMs', label: '耗时(ms)', sortable: true, width: 120 },
  { prop: 'traceId', label: 'TraceID', sortable: false, filterable: true, filterType: 'text' },
]);

const operationButtons = computed<ActionButton[]>(() => [
  { label: '查看', icon: 'View', action: 'view' },
]);

// --- Form Fields for Details Dialog ---
const formFields = computed<HeaderField[]>(() => [
  { group: "基本信息", fields: [
      { field: 'id', label: 'ID' },
      { field: 'traceId', label: 'Trace ID' },
      { field: 'userId', label: '用户ID' },
      { field: 'username', label: '用户名' },
      { field: 'action', label: '操作' },
      { field: 'status', label: '状态' },
      { field: 'timestamp', label: '时间戳' },
      { field: 'durationMs', label: '耗时(ms)' },
  ]},
  { group: "请求信息", fields: [
      { field: 'method', label: '请求方法' },
      { field: 'requestUri', label: '请求URI' },
      { field: 'clientIp', label: '客户端IP' },
      { field: 'userAgent', label: 'User Agent', md: 24 },
  ]},
  { group: "资源信息", fields: [
      { field: 'resourceType', label: '资源类型' },
      { field: 'resourceId', label: '资源ID' },
      { field: 'details', label: '详情', type: 'slot', md: 24 },
  ]},
  { group: "变更内容", fields: [
      { field: 'oldValue', label: '旧值', type: 'slot', md: 24},
      { field: 'newValue', label: '新值', type: 'slot', md: 24},
  ]},
].flatMap(g => g.fields.map(f => ({ ...f, group: g.group }))) as HeaderField[]);

// --- Lifecycle Hooks ---
onMounted(() => {
  loadData();
});

// --- Methods ---
const loadData = async () => {
  loading.value = true;
  try {
    const params: AuditLogQuery = {
      ...queryParams,
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
    };
    const response: AuditLogListResponse = await getAuditLogList(params);
    tableData.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to load audit log list:', error);
    ElMessage.error('加载审计日志列表失败');
  } finally {
    loading.value = false;
  }
};

const handleView = (row: AuditLogVO) => {
  dialogTitle.value = `查看日志详情 (ID: ${row.id})`;
  formData.value = { ...row };
  dialogVisible.value = true;
};

// --- Table Event Handlers ---
const handleFilterChange = (filters: any) => {
    Object.entries(filters).forEach(([key, value]) => {
        queryParams[key as keyof AuditLogQuery] = value === '' ? undefined : value as any;
    });
    pagination.currentPage = 1;
    loadData();
};

const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

const handleSortChange = (params: { prop: string; order: 'ascending' | 'descending' | null }) => {
    if (params.order) {
        queryParams.sort = `${params.prop}:${params.order === 'ascending' ? 'asc' : 'desc'}`;
    } else {
        queryParams.sort = 'timestamp:desc'; // Default sort when cancelled
    }
    loadData();
};

// --- Helper Functions ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return '-';
  }
};

const formatJson = (value: any): string => {
  if (!value) return 'N/A';

  try {
    // If it's already an object, stringify it.
    // If it's a JSON string, parse and then stringify it for pretty printing.
    const obj = typeof value === 'string' ? JSON.parse(value) : value;
    return JSON.stringify(obj, null, 2);
  } catch (e) {
    // If it's a non-JSON string or another primitive, return it as is.
    return String(value);
  }
};

</script>

<style scoped>
.json-viewer {
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', Courier, monospace;
}
</style> 