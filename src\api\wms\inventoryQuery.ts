import request from '@/utils/request'
import type {
  WmsInventoryQueryReq,
  WmsInventoryPageVO,
  WmsInventoryDetailVO,
  WmsInventoryVO,
  ApiResponse
} from '@/types/wms/inventory'

// 库存查询API接口

/**
 * 分页查询库存
 * @param params 查询参数
 * @returns 库存分页数据
 */
export function getInventoryPage(params: WmsInventoryQueryReq): Promise<ApiResponse<WmsInventoryPageVO>> {
  return request({
    url: '/api/wms/inventory/query',
    method: 'post',
    data: params
  })
}

/**
 * 获取库存详情
 * @param id 库存ID
 * @returns 库存详情
 */
export function getInventoryDetail(id: number): Promise<ApiResponse<WmsInventoryDetailVO>> {
  return request({
    url: `/api/wms/inventory/query/${id}`,
    method: 'get'
  })
}

/**
 * 按库位查询库存
 * @param locationId 库位ID
 * @returns 库存列表
 */
export function getInventoryByLocation(locationId: number): Promise<ApiResponse<WmsInventoryVO[]>> {
  return request({
    url: `/api/wms/inventory/query/location/${locationId}`,
    method: 'get'
  })
}

/**
 * 按物料查询库存
 * @param itemId 物料ID
 * @returns 库存列表
 */
export function getInventoryByItem(itemId: number): Promise<ApiResponse<WmsInventoryVO[]>> {
  return request({
    url: `/api/wms/inventory/query/item/${itemId}`,
    method: 'get'
  })
}

/**
 * 获取库存汇总统计
 * @param params 汇总参数
 * @returns 汇总数据
 */
export function getInventorySummary(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/summary',
    method: 'post',
    data: params
  })
}

/**
 * 检查库存可用性
 * @param params 检查参数
 * @returns 可用性结果
 */
export function checkInventoryAvailability(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/availability',
    method: 'post',
    data: params
  })
}

/**
 * 获取库存周转分析
 * @param params 分析参数
 * @returns 周转分析数据
 */
export function getInventoryTurnover(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/turnover',
    method: 'post',
    data: params
  })
}

/**
 * 获取库存预警
 * @param params 预警查询参数
 * @returns 预警列表
 */
export function getInventoryAlerts(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/alerts',
    method: 'post',
    data: params
  })
}

/**
 * 导出库存数据
 * @param params 导出参数
 * @returns 文件数据
 */
export function exportInventory(params: any): Promise<Blob> {
  return request({
    url: '/api/wms/inventory/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/**
 * 批量更新库存
 * @param params 更新参数
 * @returns 更新结果
 */
export function batchUpdateInventory(params: any): Promise<ApiResponse<void>> {
  return request({
    url: '/api/wms/inventory/batch-update',
    method: 'post',
    data: params
  })
}

// 库存调整相关API

/**
 * 分页查询库存调整记录
 * @param params 查询参数
 * @returns 调整记录分页数据
 */
export function getInventoryAdjustmentPage(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/adjustment/page',
    method: 'post',
    data: params
  })
}

/**
 * 创建库存调整
 * @param params 调整参数
 * @returns 创建结果
 */
export function createInventoryAdjustment(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/adjustment',
    method: 'post',
    data: params
  })
}

/**
 * 审批库存调整
 * @param id 调整ID
 * @param params 审批参数
 * @returns 审批结果
 */
export function approveInventoryAdjustment(id: number, params: any): Promise<ApiResponse<void>> {
  return request({
    url: `/api/wms/inventory/adjustment/${id}/approve`,
    method: 'post',
    data: params
  })
}

/**
 * 执行库存调整
 * @param params 执行参数
 * @returns 执行结果
 */
export function executeInventoryAdjustment(params: any): Promise<ApiResponse<void>> {
  return request({
    url: '/api/wms/inventory/adjustment/execute',
    method: 'post',
    data: params
  })
}

// 库存移动相关API

/**
 * 分页查询库存移动记录
 * @param params 查询参数
 * @returns 移动记录分页数据
 */
export function getInventoryMovementPage(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/movement/page',
    method: 'post',
    data: params
  })
}

/**
 * 创建库存移动
 * @param params 移动参数
 * @returns 创建结果
 */
export function createInventoryMovement(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/movement',
    method: 'post',
    data: params
  })
}

/**
 * 开始库存移动
 * @param id 移动ID
 * @returns 开始结果
 */
export function startInventoryMovement(id: number): Promise<ApiResponse<void>> {
  return request({
    url: `/api/wms/inventory/movement/${id}/start`,
    method: 'post'
  })
}

/**
 * 完成库存移动
 * @param id 移动ID
 * @returns 完成结果
 */
export function completeInventoryMovement(id: number): Promise<ApiResponse<void>> {
  return request({
    url: `/api/wms/inventory/movement/${id}/complete`,
    method: 'post'
  })
}

// 盘点管理相关API

/**
 * 分页查询盘点计划
 * @param params 查询参数
 * @returns 盘点计划分页数据
 */
export function getCycleCountPlanPage(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/cycle-count/plan/page',
    method: 'post',
    data: params
  })
}

/**
 * 创建盘点计划
 * @param params 计划参数
 * @returns 创建结果
 */
export function createCycleCountPlan(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/cycle-count/plan',
    method: 'post',
    data: params
  })
}

/**
 * 审批盘点计划
 * @param id 计划ID
 * @param params 审批参数
 * @returns 审批结果
 */
export function approveCycleCountPlan(id: number, params: any): Promise<ApiResponse<void>> {
  return request({
    url: `/api/wms/inventory/cycle-count/plan/${id}/approve`,
    method: 'post',
    data: params
  })
}

/**
 * 开始盘点计划
 * @param id 计划ID
 * @returns 开始结果
 */
export function startCycleCountPlan(id: number): Promise<ApiResponse<void>> {
  return request({
    url: `/api/wms/inventory/cycle-count/plan/${id}/start`,
    method: 'post'
  })
}

/**
 * 分页查询盘点任务
 * @param params 查询参数
 * @returns 盘点任务分页数据
 */
export function getCycleCountTaskPage(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/wms/inventory/cycle-count/task/page',
    method: 'post',
    data: params
  })
}

/**
 * 完成盘点任务
 * @param id 任务ID
 * @param params 完成参数
 * @returns 完成结果
 */
export function completeCycleCountTask(id: number, params: any): Promise<ApiResponse<void>> {
  return request({
    url: `/api/wms/inventory/cycle-count/task/${id}/complete`,
    method: 'post',
    data: params
  })
}

// 辅助API

/**
 * 获取仓库列表
 * @returns 仓库列表
 */
export function getWarehouseOptions(): Promise<ApiResponse<any[]>> {
  return request({
    url: '/api/wms/warehouse/options',
    method: 'get'
  })
}

/**
 * 获取库位列表
 * @param warehouseId 仓库ID
 * @returns 库位列表
 */
export function getLocationOptions(warehouseId?: number): Promise<ApiResponse<any[]>> {
  return request({
    url: '/api/wms/location/options',
    method: 'get',
    params: { warehouseId }
  })
}

/**
 * 获取物料列表
 * @param keyword 搜索关键词
 * @returns 物料列表
 */
export function getItemOptions(keyword?: string): Promise<ApiResponse<any[]>> {
  return request({
    url: '/api/wms/item/options',
    method: 'get',
    params: { keyword }
  })
}
