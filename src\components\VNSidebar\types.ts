import type { Component } from 'vue';
import type { RouteLocationRaw } from 'vue-router';

export interface SidebarItem {
  index?: string; // Unique key or path for the menu item
  title: string;
  label?: string; // Keep label as optional if still used elsewhere potentially
  icon?: string | Component; // Icon component name or the component itself
  to?: RouteLocationRaw; // Optional route target for el-menu-item
  disabled?: boolean;
  children?: SidebarItem[];
  permission?: string | string[];
  hidden?: boolean;
}

export interface VNSidebarProps {
  items: SidebarItem[]; // 侧边栏菜单项配置数组
  defaultOpeneds?: string[]; // 可选，默认展开的 sub-menu 的 index 的数组
  collapse?: boolean; // 可选，是否水平折叠收起菜单
  uniqueOpened?: boolean; // 可选，是否只保持一个子菜单的展开
  backgroundColor?: string; // 可选，菜单背景色
  textColor?: string; // 可选，菜单文字颜色
  activeTextColor?: string; // 可选，当前激活菜单项的文字颜色
} 