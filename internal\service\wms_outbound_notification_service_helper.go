package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"gorm.io/gorm"
)

// generateNotificationNo 生成出库通知单号
func (s *wmsOutboundNotificationServiceImpl) generateNotificationNo(ctx context.Context, req *dto.WmsOutboundNotificationCreateReq) (string, error) {
	// 使用编码生成服务
	codeGenService := s.GetServiceManager().GetCodeGenerationService()
	if codeGenService == nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "编码生成服务不可用")
	}

	// 构建编码生成请求
	generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
		BusinessType: "OUTBOUND_NOTIFICATION",
		ContextData: map[string]interface{}{
			"warehouseId": req.<PERSON>house<PERSON>,
			"clientId":    req.ClientID,
		},
	})
	if err != nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "出库通知单号生成失败").WithCause(err)
	}

	return generatedCode.GeneratedCode, nil
}

// validateNotificationDetail 验证出库通知单明细
func (s *wmsOutboundNotificationServiceImpl) validateNotificationDetail(ctx context.Context, txRepoMgr *repository.RepositoryManager, detail *dto.WmsOutboundNotificationDetailCreateReq) error {
	// 验证行号
	if detail.LineNo <= 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "行号必须大于0")
	}

	// 验证物料ID
	if detail.ItemID == 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "物料ID不能为空")
	}

	// 验证数量
	if detail.RequiredQty <= 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "要求数量必须大于0")
	}

	// 验证单位
	if strings.TrimSpace(detail.UnitOfMeasure) == "" {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "单位不能为空")
	}

	// 验证日期逻辑
	if detail.RequiredProductionDate != nil && detail.RequiredExpiryDate != nil {
		if *detail.RequiredProductionDate > *detail.RequiredExpiryDate {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "生产日期不能晚于过期日期")
		}
	}

	// TODO: 验证物料是否存在
	// TODO: 验证库存是否充足

	return nil
}

// updateNotificationDetails 处理明细表的差异更新（增删改）
func (s *wmsOutboundNotificationServiceImpl) updateNotificationDetails(
	ctx context.Context,
	tx *gorm.DB,
	notification *entity.WmsOutboundNotification,
	detailReqs []dto.WmsOutboundNotificationDetailUpdateReq,
) error {
	// 1. 构建现有明细的映射（ID -> Detail）
	existingDetails := make(map[uint]*entity.WmsOutboundNotificationDetail)
	for i := range notification.Details {
		detail := &notification.Details[i]
		existingDetails[detail.ID] = detail
	}

	// 2. 构建请求明细的映射，分为更新和新增
	var toUpdate []dto.WmsOutboundNotificationDetailUpdateReq
	var toCreate []dto.WmsOutboundNotificationDetailUpdateReq
	requestedIDs := make(map[uint]bool)

	for _, detailReq := range detailReqs {
		// 数据验证
		if err := s.validateDetailUpdateRequest(&detailReq); err != nil {
			return fmt.Errorf("明细数据验证失败: %w", err)
		}

		if detailReq.ID != nil && *detailReq.ID > 0 {
			// 存在ID，标记为更新
			toUpdate = append(toUpdate, detailReq)
			requestedIDs[*detailReq.ID] = true
		} else {
			// 无ID，标记为新增
			toCreate = append(toCreate, detailReq)
		}
	}

	// 3. 删除操作：删除未在请求中的现有明细
	var toDeleteIDs []uint
	for id := range existingDetails {
		if !requestedIDs[id] {
			toDeleteIDs = append(toDeleteIDs, id)
		}
	}

	if len(toDeleteIDs) > 0 {
		if err := tx.Where("id IN ?", toDeleteIDs).Delete(&entity.WmsOutboundNotificationDetail{}).Error; err != nil {
			return fmt.Errorf("删除明细失败: %w", err)
		}
	}

	// 4. 更新操作：更新现有明细
	for _, detailReq := range toUpdate {
		detailID := *detailReq.ID
		existingDetail, exists := existingDetails[detailID]
		if !exists {
			return fmt.Errorf("要更新的明细ID %d 不存在", detailID)
		}

		// 构建更新字段
		updates := make(map[string]interface{})
		if detailReq.LineNo != 0 {
			updates["line_no"] = detailReq.LineNo
		}
		if detailReq.ItemID != 0 {
			updates["item_id"] = detailReq.ItemID
		}
		if detailReq.RequiredQty != 0 {
			updates["required_qty"] = detailReq.RequiredQty
		}
		if detailReq.UnitOfMeasure != "" {
			updates["unit_of_measure"] = detailReq.UnitOfMeasure
		}
		if detailReq.RequiredBatchNo != nil {
			updates["required_batch_no"] = *detailReq.RequiredBatchNo
		}
		if detailReq.RequiredProductionDate != nil {
			updates["required_production_date"] = *detailReq.RequiredProductionDate
		}
		if detailReq.RequiredExpiryDate != nil {
			updates["required_expiry_date"] = *detailReq.RequiredExpiryDate
		}
		if detailReq.Remark != nil {
			updates["remark"] = *detailReq.Remark
		}

		if len(updates) > 0 {
			updates["updated_by"] = notification.UpdatedBy
			if err := tx.Model(existingDetail).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新明细ID %d 失败: %w", detailID, err)
			}
		}
	}

	// 5. 创建操作：新增明细
	if len(toCreate) > 0 {
		var newDetails []entity.WmsOutboundNotificationDetail
		for _, detailReq := range toCreate {
			detail := entity.WmsOutboundNotificationDetail{
				NotificationID: notification.ID,
				LineNo:         detailReq.LineNo,
				ItemID:         detailReq.ItemID,
				RequiredQty:    detailReq.RequiredQty,
				UnitOfMeasure:  detailReq.UnitOfMeasure,
			}
			detail.AccountBookID = notification.AccountBookID
			detail.CreatedBy = notification.CreatedBy

			if detailReq.RequiredBatchNo != nil {
				detail.RequiredBatchNo = detailReq.RequiredBatchNo
			}
			if detailReq.RequiredProductionDate != nil {
				// 将字符串转换为时间
				if prodDate, err := time.Parse("2006-01-02", *detailReq.RequiredProductionDate); err == nil {
					detail.RequiredProductionDate = &prodDate
				}
			}
			if detailReq.RequiredExpiryDate != nil {
				// 将字符串转换为时间
				if expDate, err := time.Parse("2006-01-02", *detailReq.RequiredExpiryDate); err == nil {
					detail.RequiredExpiryDate = &expDate
				}
			}
			if detailReq.Remark != nil {
				detail.Remark = detailReq.Remark
			}

			newDetails = append(newDetails, detail)
		}

		if err := tx.Create(&newDetails).Error; err != nil {
			return fmt.Errorf("创建新明细失败: %w", err)
		}
	}

	return nil
}

// validateDetailUpdateRequest 验证明细更新请求
func (s *wmsOutboundNotificationServiceImpl) validateDetailUpdateRequest(req *dto.WmsOutboundNotificationDetailUpdateReq) error {
	if req.LineNo != 0 && req.LineNo <= 0 {
		return fmt.Errorf("行号必须大于0")
	}
	if req.RequiredQty != 0 && req.RequiredQty <= 0 {
		return fmt.Errorf("要求数量必须大于0")
	}
	if req.UnitOfMeasure != "" && strings.TrimSpace(req.UnitOfMeasure) == "" {
		return fmt.Errorf("单位不能为空")
	}
	if req.RequiredProductionDate != nil && req.RequiredExpiryDate != nil && *req.RequiredProductionDate > *req.RequiredExpiryDate {
		return fmt.Errorf("生产日期不能晚于过期日期")
	}
	return nil
}

// validateNotificationForApproval 验证通知单是否可以审核
func (s *wmsOutboundNotificationServiceImpl) validateNotificationForApproval(ctx context.Context, txRepoMgr *repository.RepositoryManager, notification *entity.WmsOutboundNotification) error {
	// 检查是否有明细
	detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()
	details, err := detailRepo.FindByNotificationID(ctx, notification.ID)
	if err != nil {
		return fmt.Errorf("查询明细失败: %w", err)
	}

	if len(details) == 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "出库通知单必须包含明细")
	}

	// 验证明细数据完整性
	for _, detail := range details {
		if detail.RequiredQty <= 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("行号 %d 的要求数量必须大于0", detail.LineNo))
		}
		if strings.TrimSpace(detail.UnitOfMeasure) == "" {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("行号 %d 的单位不能为空", detail.LineNo))
		}
		// TODO: 验证物料是否存在
		// TODO: 验证库存是否充足
	}

	return nil
}

// releaseInventoryAllocations 释放库存分配
func (s *wmsOutboundNotificationServiceImpl) releaseInventoryAllocations(ctx context.Context, txRepoMgr *repository.RepositoryManager, notificationID uint) error {
	// 获取出库通知单的所有明细
	detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()
	details, err := detailRepo.FindByNotificationID(ctx, notificationID)
	if err != nil {
		return fmt.Errorf("查询出库明细失败: %w", err)
	}

	// 释放每个明细的库存分配
	allocationRepo := txRepoMgr.GetWmsInventoryAllocationRepository()
	for _, detail := range details {
		allocations, err := allocationRepo.FindByOutboundDetailID(ctx, detail.ID)
		if err != nil {
			return fmt.Errorf("查询库存分配失败: %w", err)
		}

		var allocationIDs []uint
		for _, allocation := range allocations {
			if allocation.Status == string(entity.AllocationStatusAllocated) {
				allocationIDs = append(allocationIDs, allocation.ID)
			}
		}

		if len(allocationIDs) > 0 {
			if err := allocationRepo.BatchReleaseAllocation(ctx, allocationIDs, "出库通知单取消"); err != nil {
				return fmt.Errorf("释放库存分配失败: %w", err)
			}
		}
	}

	return nil
}

// fillExtendedInfo 填充扩展信息
func (s *wmsOutboundNotificationServiceImpl) fillExtendedInfo(ctx context.Context, vo *vo.WmsOutboundNotificationVO) error {
	// TODO: 填充客户名称、仓库名称、承运商名称等扩展信息
	// TODO: 计算统计信息（总物料种类数、总数量、分配进度等）
	return nil
}

// fillListExtendedInfo 填充列表扩展信息
func (s *wmsOutboundNotificationServiceImpl) fillListExtendedInfo(ctx context.Context, vo *vo.WmsOutboundNotificationListVO) {
	// TODO: 填充客户名称、仓库名称、承运商名称等扩展信息
	// TODO: 计算统计信息（总物料种类数、总数量、分配进度等）
}
