package entity

import (
	"time"
)

// TransactionType 定义库存事务类型常量
type TransactionType string

const (
	TxTypeReceipt           TransactionType = "RECEIPT"         // 收货入库
	TxTypePutaway           TransactionType = "PUTAWAY"         // 上架
	TxTypePicking           TransactionType = "PICKING"         // 拣货
	TxTypeShipment          TransactionType = "SHIPMENT"        // 发运出库
	TxTypeAdjustmentGain    TransactionType = "ADJUSTMENT_GAIN" // 库存调整增加 (盘盈, 损坏减少的负数等)
	TxTypeAdjustmentLoss    TransactionType = "ADJUSTMENT_LOSS" // 库存调整减少 (盘亏, 丢失, 损坏等)
	TxTypeTransferOut       TransactionType = "TRANSFER_OUT"    // 移库转出
	TxTypeTransferIn        TransactionType = "TRANSFER_IN"     // 移库转入
	TxTypeStocktakeGain     TransactionType = "STOCKTAKE_GAIN"  // 盘点盘盈 (也可归入 Adjustment)
	TxTypeStocktakeLoss     TransactionType = "STOCKTAKE_LOSS"  // 盘点盘亏 (也可归入 Adjustment)
	TxTypeMoveOut           TransactionType = "MOVE_OUT"
	TxTypeMoveIn            TransactionType = "MOVE_IN"
	TxTypeStatusChange      TransactionType = "STATUS_CHANGE"
	TxTypeReceiptCorrection TransactionType = "RECEIPT_CORRECTION"
	TxTypeReceiptCancelled  TransactionType = "RECEIPT_CANCELLED"
	TxTypeFreeze            TransactionType = "FREEZE"     // 冻结
	TxTypeUnfreeze          TransactionType = "UNFREEZE"   // 解冻
	TxTypeAllocate          TransactionType = "ALLOCATE"   // 分配
	TxTypeDeallocate        TransactionType = "DEALLOCATE" // 取消分配
	// 可根据需要添加其他类型, 例如 QC_HOLD, QC_RELEASE
)

// WmsInventoryTransactionLog 对应数据库中的 wms_inventory_transaction_logs 表
// 存储库存变动流水日志
type WmsInventoryTransactionLog struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	TransactionTime    time.Time       `gorm:"column:transaction_time;not null;default:CURRENT_TIMESTAMP;index;comment:事务时间" json:"transactionTime"`           // 事务发生时间
	TransactionType    TransactionType `gorm:"column:transaction_type;type:wms_inventory_transaction_type;not null;index;comment:事务类型" json:"transactionType"` // 事务类型
	ItemID             uint            `gorm:"column:item_id;not null;index;comment:物料ID" json:"itemId"`                                                       // 涉及的物料ID
	WarehouseID        uint            `gorm:"column:warehouse_id;not null;index;comment:仓库ID" json:"warehouseId"`                                             // 涉及的仓库ID
	LocationID         uint            `gorm:"column:location_id;not null;index;comment:库位ID" json:"locationId"`                                               // 涉及的库位ID
	ClientID           uint            `gorm:"column:client_id;not null;index;comment:客户ID" json:"clientId"`                                                   // 涉及的客户/货主ID
	BatchNo            *string         `gorm:"column:batch_no;type:varchar(100);index;comment:批次号" json:"batchNo"`                                             // 涉及的批次号
	InventoryStatus    string          `gorm:"column:inventory_status;type:varchar(20);not null;comment:库存状态" json:"inventoryStatus"`                          // 涉及的库存状态
	QuantityChange     float64         `gorm:"column:quantity_change;type:numeric(12,4);not null;comment:数量变化" json:"quantityChange"`                          // 本次事务导致的数量变化 (正数增加, 负数减少)
	UnitOfMeasure      string          `gorm:"column:unit_of_measure;type:varchar(20);not null;comment:数量单位" json:"unitOfMeasure"`                             // 数量单位
	BalanceBefore      float64         `gorm:"column:balance_before;type:numeric(12,4);not null;comment:事务前数量" json:"balanceBefore"`                           // 事务发生前该维度库存的数量
	BalanceAfter       float64         `gorm:"column:balance_after;type:numeric(12,4);not null;comment:事务后数量" json:"balanceAfter"`                             // 事务发生后该维度库存的数量
	ReferenceDocType   *string         `gorm:"column:reference_doc_type;type:varchar(50);index;comment:关联单据类型" json:"referenceDocType"`                        // 关联的业务单据类型 (例如: RECEIVING_RECORD, PUTAWAY_TASK, PICKING_TASK, SHIPMENT, ADJUSTMENT, STOCKTAKE_PLAN)
	ReferenceDocID     *uint           `gorm:"column:reference_doc_id;index;comment:关联单据ID" json:"referenceDocId"`                                             // 关联的业务单据ID
	ReferenceDocLineID *uint           `gorm:"column:reference_doc_line_id;index;comment:关联单据行ID" json:"referenceDocLineId"`                                   // 关联的业务单据行ID (可选)
	OperatorID         *uint           `gorm:"column:operator_id;index;comment:操作员ID" json:"operatorId"`                                                       // 操作员ID (关联用户表)
	Remark             *string         `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                                               // 备注
}

// TableName 指定数据库表名方法
func (WmsInventoryTransactionLog) TableName() string {
	return "wms_inventory_transaction_log"
}

// GetTransactionTypeName 获取事务类型名称
func (w *WmsInventoryTransactionLog) GetTransactionTypeName() string {
	switch w.TransactionType {
	case TxTypeReceipt:
		return "收货入库"
	case TxTypePutaway:
		return "上架"
	case TxTypePicking:
		return "拣货"
	case TxTypeShipment:
		return "发运出库"
	case TxTypeAdjustmentGain:
		return "库存调整增加"
	case TxTypeAdjustmentLoss:
		return "库存调整减少"
	case TxTypeTransferOut:
		return "移库转出"
	case TxTypeTransferIn:
		return "移库转入"
	case TxTypeStocktakeGain:
		return "盘点盘盈"
	case TxTypeStocktakeLoss:
		return "盘点盘亏"
	case TxTypeMoveOut:
		return "移动转出"
	case TxTypeMoveIn:
		return "移动转入"
	case TxTypeStatusChange:
		return "状态变更"
	case TxTypeReceiptCorrection:
		return "收货更正"
	case TxTypeReceiptCancelled:
		return "收货取消"
	case TxTypeFreeze:
		return "冻结"
	case TxTypeUnfreeze:
		return "解冻"
	case TxTypeAllocate:
		return "分配"
	case TxTypeDeallocate:
		return "取消分配"
	default:
		return string(w.TransactionType)
	}
}
