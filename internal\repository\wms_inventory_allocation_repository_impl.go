package repository

import (
	"context"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsInventoryAllocationRepository 定义库存分配仓库接口
type WmsInventoryAllocationRepository interface {
	BaseRepository[entity.WmsInventoryAllocation, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInventoryAllocationQueryReq) (*response.PageResult, error)

	// 根据出库明细ID查询分配记录
	FindByOutboundDetailID(ctx context.Context, outboundDetailID uint) ([]*entity.WmsInventoryAllocation, error)

	// 根据库存ID查询分配记录
	FindByInventoryID(ctx context.Context, inventoryID uint) ([]*entity.WmsInventoryAllocation, error)

	// 根据拣货任务明细ID查询分配记录
	FindByPickingTaskDetailID(ctx context.Context, pickingTaskDetailID uint) ([]*entity.WmsInventoryAllocation, error)

	// 批量创建分配记录
	BatchCreate(ctx context.Context, allocations []*entity.WmsInventoryAllocation) error

	// 批量更新分配记录
	BatchUpdate(ctx context.Context, allocations []*entity.WmsInventoryAllocation) error

	// 更新拣货数量
	UpdatePickedQty(ctx context.Context, id uint, pickedQty float64) error

	// 批量更新拣货数量
	BatchUpdatePickedQty(ctx context.Context, updates map[uint]float64) error

	// 更新状态
	UpdateStatus(ctx context.Context, id uint, status string) error

	// 批量更新状态
	BatchUpdateStatus(ctx context.Context, ids []uint, status string) error

	// 释放分配记录
	ReleaseAllocation(ctx context.Context, id uint, reason string) error

	// 批量释放分配记录
	BatchReleaseAllocation(ctx context.Context, ids []uint, reason string) error

	// 根据分配策略查询
	FindByAllocationStrategy(ctx context.Context, strategy string) ([]*entity.WmsInventoryAllocation, error)

	// 获取指定状态的分配记录
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsInventoryAllocation, error)

	// 获取可拣货的分配记录
	GetAvailableForPicking(ctx context.Context, outboundDetailID uint) ([]*entity.WmsInventoryAllocation, error)

	// 获取已完成拣货的分配记录
	GetPickedAllocations(ctx context.Context, outboundDetailID uint) ([]*entity.WmsInventoryAllocation, error)

	// 根据日期范围查询分配记录
	GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryAllocation, error)

	// 获取分配统计信息
	GetAllocationStats(ctx context.Context, outboundDetailID uint) (map[string]interface{}, error)

	// 检查库存是否已分配
	IsInventoryAllocated(ctx context.Context, inventoryID uint) (bool, error)

	// 获取库存的总分配数量
	GetTotalAllocatedQty(ctx context.Context, inventoryID uint) (float64, error)

	// 获取出库明细的分配汇总
	GetAllocationSummary(ctx context.Context, outboundDetailID uint) (map[string]interface{}, error)
}

// wmsInventoryAllocationRepository 库存分配仓库实现
type wmsInventoryAllocationRepository struct {
	BaseRepositoryImpl[entity.WmsInventoryAllocation, uint]
}

// NewWmsInventoryAllocationRepository 创建库存分配仓库
func NewWmsInventoryAllocationRepository(db *gorm.DB) WmsInventoryAllocationRepository {
	return &wmsInventoryAllocationRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsInventoryAllocation, uint]{
			db: db,
		},
	}
}

// GetPage 获取库存分配分页数据
func (r *wmsInventoryAllocationRepository) GetPage(ctx context.Context, query *dto.WmsInventoryAllocationQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.OutboundDetailID != nil {
		conditions = append(conditions, NewEqualCondition("outbound_detail_id", *query.OutboundDetailID))
	}
	if query.InventoryID != nil {
		conditions = append(conditions, NewEqualCondition("inventory_id", *query.InventoryID))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.AllocationStrategy != nil && *query.AllocationStrategy != "" {
		conditions = append(conditions, NewEqualCondition("allocation_strategy", *query.AllocationStrategy))
	}
	if query.AllocationTimeStart != nil && query.AllocationTimeEnd != nil {
		conditions = append(conditions, NewBetweenCondition("allocation_time", *query.AllocationTimeStart, *query.AllocationTimeEnd))
	}

	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// FindByOutboundDetailID 根据出库明细ID查询分配记录
func (r *wmsInventoryAllocationRepository) FindByOutboundDetailID(ctx context.Context, outboundDetailID uint) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("outbound_detail_id", outboundDetailID),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByInventoryID 根据库存ID查询分配记录
func (r *wmsInventoryAllocationRepository) FindByInventoryID(ctx context.Context, inventoryID uint) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("inventory_id", inventoryID),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByPickingTaskDetailID 根据拣货任务明细ID查询分配记录
func (r *wmsInventoryAllocationRepository) FindByPickingTaskDetailID(ctx context.Context, pickingTaskDetailID uint) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("picking_task_detail_id", pickingTaskDetailID),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// BatchCreate 批量创建分配记录
func (r *wmsInventoryAllocationRepository) BatchCreate(ctx context.Context, allocations []*entity.WmsInventoryAllocation) error {
	if len(allocations) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(allocations, 100) // 每批100条
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建库存分配记录失败: count=%d", len(allocations))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建库存分配记录失败").WithCause(result.Error)
	}

	return nil
}

// BatchUpdate 批量更新分配记录
func (r *wmsInventoryAllocationRepository) BatchUpdate(ctx context.Context, allocations []*entity.WmsInventoryAllocation) error {
	if len(allocations) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for _, allocation := range allocations {
		if err := db.Save(allocation).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新库存分配记录失败: id=%d", allocation.ID)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新库存分配记录失败").WithCause(err)
		}
	}

	return nil
}

// UpdatePickedQty 更新拣货数量
func (r *wmsInventoryAllocationRepository) UpdatePickedQty(ctx context.Context, id uint, pickedQty float64) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsInventoryAllocation{}).Where("id = ?", id).Update("picked_qty", pickedQty)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新分配记录拣货数量失败: id=%d, pickedQty=%f", id, pickedQty)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新分配记录拣货数量失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "分配记录不存在")
	}

	return nil
}

// BatchUpdatePickedQty 批量更新拣货数量
func (r *wmsInventoryAllocationRepository) BatchUpdatePickedQty(ctx context.Context, updates map[uint]float64) error {
	if len(updates) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for id, qty := range updates {
		if err := db.Model(&entity.WmsInventoryAllocation{}).Where("id = ?", id).Update("picked_qty", qty).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新分配记录拣货数量失败: id=%d, qty=%f", id, qty)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新分配记录拣货数量失败").WithCause(err)
		}
	}

	return nil
}

// UpdateStatus 更新状态
func (r *wmsInventoryAllocationRepository) UpdateStatus(ctx context.Context, id uint, status string) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsInventoryAllocation{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新分配记录状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新分配记录状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "分配记录不存在")
	}

	return nil
}

// BatchUpdateStatus 批量更新状态
func (r *wmsInventoryAllocationRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsInventoryAllocation{}).Where("id IN ?", ids).Update("status", status)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量更新分配记录状态失败: ids=%v, status=%s", ids, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新分配记录状态失败").WithCause(result.Error)
	}

	return nil
}

// ReleaseAllocation 释放分配记录
func (r *wmsInventoryAllocationRepository) ReleaseAllocation(ctx context.Context, id uint, reason string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{
		"status": string(entity.AllocationStatusReleased),
		"remark": reason,
	}
	result := db.Model(&entity.WmsInventoryAllocation{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("释放分配记录失败: id=%d, reason=%s", id, reason)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "释放分配记录失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "分配记录不存在")
	}

	return nil
}

// BatchReleaseAllocation 批量释放分配记录
func (r *wmsInventoryAllocationRepository) BatchReleaseAllocation(ctx context.Context, ids []uint, reason string) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	updates := map[string]interface{}{
		"status": string(entity.AllocationStatusReleased),
		"remark": reason,
	}
	result := db.Model(&entity.WmsInventoryAllocation{}).Where("id IN ?", ids).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量释放分配记录失败: ids=%v, reason=%s", ids, reason)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量释放分配记录失败").WithCause(result.Error)
	}

	return nil
}

// FindByAllocationStrategy 根据分配策略查询
func (r *wmsInventoryAllocationRepository) FindByAllocationStrategy(ctx context.Context, strategy string) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("allocation_strategy", strategy),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByStatus 获取指定状态的分配记录
func (r *wmsInventoryAllocationRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetAvailableForPicking 获取可拣货的分配记录
func (r *wmsInventoryAllocationRepository) GetAvailableForPicking(ctx context.Context, outboundDetailID uint) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("outbound_detail_id", outboundDetailID),
		NewEqualCondition("status", string(entity.AllocationStatusAllocated)),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetPickedAllocations 获取已完成拣货的分配记录
func (r *wmsInventoryAllocationRepository) GetPickedAllocations(ctx context.Context, outboundDetailID uint) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("outbound_detail_id", outboundDetailID),
		NewEqualCondition("status", string(entity.AllocationStatusPicked)),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByDateRange 根据日期范围查询分配记录
func (r *wmsInventoryAllocationRepository) GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryAllocation, error) {
	conditions := []QueryCondition{
		NewBetweenCondition("allocation_time", startDate, endDate),
	}
	sortInfos := []response.SortInfo{
		{Field: "allocation_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetAllocationStats 获取分配统计信息
func (r *wmsInventoryAllocationRepository) GetAllocationStats(ctx context.Context, outboundDetailID uint) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var stats struct {
		TotalCount     int64   `json:"total_count"`
		TotalQty       float64 `json:"total_qty"`
		PickedQty      float64 `json:"picked_qty"`
		AllocatedCount int64   `json:"allocated_count"`
		PickedCount    int64   `json:"picked_count"`
		ReleasedCount  int64   `json:"released_count"`
	}

	// 基础统计
	if err := db.Model(&entity.WmsInventoryAllocation{}).
		Where("outbound_detail_id = ?", outboundDetailID).
		Select("COUNT(*) as total_count, SUM(allocated_qty) as total_qty, SUM(picked_qty) as picked_qty").
		Scan(&stats).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取分配统计信息失败: outboundDetailID=%d", outboundDetailID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取分配统计信息失败").WithCause(err)
	}

	// 各状态统计
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := db.Model(&entity.WmsInventoryAllocation{}).
		Select("status, COUNT(*) as count").
		Where("outbound_detail_id = ?", outboundDetailID).
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取分配状态统计失败: outboundDetailID=%d", outboundDetailID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取分配状态统计失败").WithCause(err)
	}

	// 填充状态统计
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(entity.AllocationStatusAllocated):
			stats.AllocatedCount = sc.Count
		case string(entity.AllocationStatusPicked):
			stats.PickedCount = sc.Count
		case string(entity.AllocationStatusReleased):
			stats.ReleasedCount = sc.Count
		}
	}

	result := map[string]interface{}{
		"total_count":     stats.TotalCount,
		"total_qty":       stats.TotalQty,
		"picked_qty":      stats.PickedQty,
		"allocated_count": stats.AllocatedCount,
		"picked_count":    stats.PickedCount,
		"released_count":  stats.ReleasedCount,
	}

	return result, nil
}

// IsInventoryAllocated 检查库存是否已分配
func (r *wmsInventoryAllocationRepository) IsInventoryAllocated(ctx context.Context, inventoryID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("inventory_id", inventoryID),
		NewInCondition("status", []interface{}{
			string(entity.AllocationStatusAllocated),
			string(entity.AllocationStatusPicked),
		}),
	}

	return r.Exists(ctx, conditions)
}

// GetTotalAllocatedQty 获取库存的总分配数量
func (r *wmsInventoryAllocationRepository) GetTotalAllocatedQty(ctx context.Context, inventoryID uint) (float64, error) {
	db := r.GetDB(ctx)
	var totalQty float64

	result := db.Model(&entity.WmsInventoryAllocation{}).
		Where("inventory_id = ? AND status IN ?", inventoryID, []string{
			string(entity.AllocationStatusAllocated),
			string(entity.AllocationStatusPicked),
		}).
		Select("COALESCE(SUM(allocated_qty), 0)").
		Scan(&totalQty)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("获取库存总分配数量失败: inventoryID=%d", inventoryID)
		return 0, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存总分配数量失败").WithCause(result.Error)
	}

	return totalQty, nil
}

// GetAllocationSummary 获取出库明细的分配汇总
func (r *wmsInventoryAllocationRepository) GetAllocationSummary(ctx context.Context, outboundDetailID uint) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var summary struct {
		TotalAllocations  int64   `json:"total_allocations"`
		TotalAllocatedQty float64 `json:"total_allocated_qty"`
		TotalPickedQty    float64 `json:"total_picked_qty"`
		AvgAllocationQty  float64 `json:"avg_allocation_qty"`
		MaxAllocationQty  float64 `json:"max_allocation_qty"`
		MinAllocationQty  float64 `json:"min_allocation_qty"`
	}

	if err := db.Model(&entity.WmsInventoryAllocation{}).
		Where("outbound_detail_id = ?", outboundDetailID).
		Select(`
			COUNT(*) as total_allocations,
			COALESCE(SUM(allocated_qty), 0) as total_allocated_qty,
			COALESCE(SUM(picked_qty), 0) as total_picked_qty,
			COALESCE(AVG(allocated_qty), 0) as avg_allocation_qty,
			COALESCE(MAX(allocated_qty), 0) as max_allocation_qty,
			COALESCE(MIN(allocated_qty), 0) as min_allocation_qty
		`).
		Scan(&summary).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取分配汇总信息失败: outboundDetailID=%d", outboundDetailID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取分配汇总信息失败").WithCause(err)
	}

	result := map[string]interface{}{
		"total_allocations":   summary.TotalAllocations,
		"total_allocated_qty": summary.TotalAllocatedQty,
		"total_picked_qty":    summary.TotalPickedQty,
		"avg_allocation_qty":  summary.AvgAllocationQty,
		"max_allocation_qty":  summary.MaxAllocationQty,
		"min_allocation_qty":  summary.MinAllocationQty,
	}

	return result, nil
}
