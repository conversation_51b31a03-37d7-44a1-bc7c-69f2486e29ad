package controller

import (
	"fmt"
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/service"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util"
)

// BaseController 基础控制器接口
type BaseController interface {
	// GetServiceManager 获取服务管理器
	GetServiceManager() *service.ServiceManager

	// GetLogger 获取日志记录器
	GetLogger() logger.Logger

	// GetAccountBookIDFromContext 从 Iris 上下文中安全地获取账套 ID
	// 返回账套ID (uint64) 和错误
	GetAccountBookIDFromContext(ctx iris.Context) (uint64, error)

	// GetUserIDFromContext 从 Iris 上下文中安全地获取用户 ID
	// 返回用户ID (uint64) 和错误
	GetUserIDFromContext(ctx iris.Context) (uint64, error)

	// GetUsernameFromContext 从 Iris 上下文中安全地获取用户名
	GetUsernameFromContext(ctx iris.Context) (string, error)

	// GetRoleIDsFromContext 从 Iris 上下文中安全地获取角色ID列表
	GetRoleIDsFromContext(ctx iris.Context) ([]uint, error)

	// IsAdminFromContext 从 Iris 上下文中安全地判断用户是否为管理员
	IsAdminFromContext(ctx iris.Context) (bool, error)

	// GetPathUintParamWithError 从 Iris 路径参数中安全地获取 uint64 类型的值
	GetPathUintParamWithError(ctx iris.Context, paramName string) (uint64, error)
}

// BaseControllerImpl 基础控制器实现
type BaseControllerImpl struct {
	controllerManager *ControllerManager
	logger            logger.Logger
}

// NewBaseController 创建基础控制器
// 参数:
//   - controllerManager: 控制器管理器
//
// 返回:
//   - *BaseControllerImpl: 基础控制器实例
func NewBaseController(controllerManager *ControllerManager) *BaseControllerImpl {
	return &BaseControllerImpl{
		controllerManager: controllerManager,
		logger:            controllerManager.GetLogger(),
	}
}

// GetServiceManager 获取服务管理器
// 返回:
//   - *service.ServiceManager: 服务管理器实例
func (c *BaseControllerImpl) GetServiceManager() *service.ServiceManager {
	return c.controllerManager.GetServiceManager()
}

// GetLogger 获取日志记录器
// 返回:
//   - logger.Logger: 日志记录器
func (c *BaseControllerImpl) GetLogger() logger.Logger {
	return c.logger
}

// Success 返回成功响应
// 参数:
//   - ctx: Iris上下文
//   - data: 响应数据
func (c *BaseControllerImpl) Success(ctx iris.Context, data interface{}) {
	response.Success(ctx, data)
}

// SuccessWithMessage 返回带消息的成功响应
// 参数:
//   - ctx: Iris上下文
//   - message: 成功消息
//   - data: 响应数据
func (c *BaseControllerImpl) SuccessWithMessage(ctx iris.Context, message string, data interface{}) {
	response.SuccessWithMessage(ctx, message, data)
}

// Fail 返回失败响应
// 参数:
//   - ctx: Iris上下文
//   - code: 错误码
//   - message: 错误消息
func (c *BaseControllerImpl) Fail(ctx iris.Context, code int, message string) {
	response.Fail(ctx, code, message)
}

// FailWithError 返回带错误的失败响应
// 参数:
//   - ctx: Iris上下文
//   - err: 错误对象
func (c *BaseControllerImpl) FailWithError(ctx iris.Context, err error) {
	response.FailWithError(ctx, err)
}

// HandleError 统一处理错误
// 参数:
//   - ctx: Iris上下文
//   - err: 错误对象
//   - opName: 操作名称
func (c *BaseControllerImpl) HandleError(ctx iris.Context, err error, opName string) {
	if err != nil {
		c.logger.Error(ctx.Request().Context(), opName+"失败",
			logger.WithError(err))
		c.FailWithError(ctx, err)
	}
}

// GetAccountBookIDFromContext 从 Iris 上下文中安全地获取账套 ID
// 返回账套ID (uint64) 和错误
func (c *BaseControllerImpl) GetAccountBookIDFromContext(ctx iris.Context) (uint64, error) {
	// 调用 pkg/util 中的新函数
	return util.GetAccountBookIDFromIrisContext(ctx)
}

// GetUserIDFromContext 从 Iris 上下文中安全地获取用户 ID
// 返回用户ID (uint64) 和错误
func (c *BaseControllerImpl) GetUserIDFromContext(ctx iris.Context) (uint64, error) {
	// 调用 pkg/util 中的新函数
	return util.GetUserIDFromIrisContext(ctx)
}

// GetUsernameFromContext 从 Iris 上下文中安全地获取用户名
func (c *BaseControllerImpl) GetUsernameFromContext(ctx iris.Context) (string, error) {
	// 调用 pkg/util 中的新函数
	return util.GetUsernameFromIrisContext(ctx)
}

// GetRoleIDsFromContext 从 Iris 上下文中安全地获取角色ID列表
func (c *BaseControllerImpl) GetRoleIDsFromContext(ctx iris.Context) ([]uint, error) {
	// 调用 pkg/util 中的新函数
	return util.GetRoleIDsFromIrisContext(ctx)
}

// IsAdminFromContext 从 Iris 上下文中安全地判断用户是否为管理员
func (c *BaseControllerImpl) IsAdminFromContext(ctx iris.Context) (bool, error) {
	// 调用 pkg/util 中的新函数
	return util.IsAdminFromIrisContext(ctx)
}

// GetPathUintParamWithError 从 Iris 路径参数中安全地获取 uint64 类型的值
// 返回 uint64 值和错误
func (c *BaseControllerImpl) GetPathUintParamWithError(ctx iris.Context, paramName string) (uint64, error) {
	paramValueStr := ctx.Params().Get(paramName)
	if paramValueStr == "" {
		// 注意：确保 apperrors 和 fmt 已导入
		err := apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, fmt.Sprintf("路径参数 '%s' 缺失", paramName))
		// 使用 c.logger 记录日志，而不是全局 logger
		c.logger.Warn(ctx.Request().Context(), "获取路径参数失败", logger.WithError(err), logger.WithField("paramName", paramName))
		return 0, err
	}

	value, err := strconv.ParseUint(paramValueStr, 10, 64)
	if err != nil {
		// 使用 apperrors.NewParamError 并包装原始错误
		parseErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("路径参数 '%s' 无效，期望 uint64 类型，得到 '%s'", paramName, paramValueStr)).WithCause(err)
		// 使用 c.logger 记录日志
		c.logger.Warn(ctx.Request().Context(), "解析路径参数为 uint64 失败", logger.WithError(parseErr), logger.WithField("paramName", paramName), logger.WithField("paramValue", paramValueStr))
		return 0, parseErr
	}
	return value, nil
}
