package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"
	"strings"

	"github.com/kataras/iris/v12"

	"backend/pkg/constant"
	"backend/pkg/errors"
	"backend/pkg/logger"
	CustomResponse "backend/pkg/response"
)

// RecoveryConfig 恢复中间件配置
type RecoveryConfig struct {
	// 是否启用
	Enabled bool `json:"enabled" yaml:"enabled"`
	// 是否在响应中显示错误详情
	ShowDetails bool `json:"showDetails" yaml:"showDetails"`
	// 是否记录堆栈信息
	LogStack bool `json:"logStack" yaml:"logStack"`
	// 是否通知管理员
	NotifyAdmin bool `json:"notifyAdmin" yaml:"notifyAdmin"`
	// 通知邮箱
	NotifyEmail string `json:"notifyEmail" yaml:"notifyEmail"`
	// 错误响应模板
	ErrorTemplate string `json:"errorTemplate" yaml:"errorTemplate"`
	// 自定义错误处理函数
	ErrorHandler func(ctx iris.Context, err interface{}) `json:"-" yaml:"-"`
}

// 默认恢复配置
var defaultRecoveryConfig = RecoveryConfig{
	Enabled:     true,
	ShowDetails: false,
	LogStack:    true,
	NotifyAdmin: false,
	NotifyEmail: "<EMAIL>",
}

// RecoveryMiddleware 恢复中间件
// 参数:
//   - config: 可选的恢复配置，不提供则使用默认配置
//
// 返回:
//   - iris.Handler: Iris中间件处理函数
func RecoveryMiddleware(config ...RecoveryConfig) iris.Handler {
	// 使用默认配置
	cfg := defaultRecoveryConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(ctx iris.Context) {
		// 如果未启用，则跳过
		if !cfg.Enabled {
			ctx.Next()
			return
		}

		// 创建恢复函数
		defer func() {
			if rec := recover(); rec != nil {
				// 获取堆栈信息
				stack := debug.Stack()

				// 格式化错误信息
				var message string
				switch err := rec.(type) {
				case string:
					message = err
				case error:
					message = err.Error()
				default:
					message = fmt.Sprintf("%v", err)
				}

				// 记录错误信息
				fields := logger.Fields{
					"request_uri": ctx.Request().RequestURI,
					"method":      ctx.Method(),
					"remote_addr": ctx.RemoteAddr(),
					"error":       message,
					"panic":       true,
				}

				// 记录堆栈信息
				if cfg.LogStack {
					// 清理堆栈信息
					cleanStack := cleanStackTrace(string(stack))
					fields["stack"] = cleanStack
				}

				// 记录错误日志
				logger := logger.WithFields(fields)
				logger.Error("Panic recovered")

				// 通知管理员
				if cfg.NotifyAdmin && cfg.NotifyEmail != "" {
					go notifyAdmin(cfg.NotifyEmail, message, ctx.Request(), string(stack))
				}

				// 使用自定义错误处理函数
				if cfg.ErrorHandler != nil {
					cfg.ErrorHandler(ctx, rec)
					return
				}

				// 默认错误处理
				errorMessage := "服务器内部错误"
				if cfg.ShowDetails {
					errorMessage = message
				}

				// 返回错误响应
				customErr := errors.NewSystemError(constant.INTERNAL_SERVER_ERROR, errorMessage)
				CustomResponse.FailWithError(ctx, customErr)
			}
		}()

		// 处理请求
		ctx.Next()
	}
}

// cleanStackTrace 清理堆栈信息
// 参数:
//   - stack: 原始堆栈信息
//
// 返回:
//   - string: 清理后的堆栈信息
func cleanStackTrace(stack string) string {
	// 移除路径前缀
	lines := strings.Split(stack, "\n")
	var result []string

	for _, line := range lines {
		// 跳过与恢复中间件相关的堆栈行
		if strings.Contains(line, "recovery_middleware.go") {
			continue
		}
		result = append(result, line)
	}

	return strings.Join(result, "\n")
}

// notifyAdmin 通知管理员
// 参数:
//   - email: 管理员邮箱
//   - message: 错误消息
//   - r: HTTP请求
//   - stack: 堆栈信息
func notifyAdmin(email, message string, r *http.Request, stack string) {
	// 实际项目中，这里应该发送邮件或其他通知
	// 为简化起见，这里只记录日志
	logger.WithFields(logger.Fields{
		"email":       email,
		"error":       message,
		"request_uri": r.RequestURI,
		"stack":       stack,
	}).Info("Admin notification sent")
}
