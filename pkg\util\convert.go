package util

import (
	"database/sql"
	"strconv"
	"strings"
	"time"
)

// JsonDate is a custom type to handle YYYY-MM-DD date format in JSON.
// It embeds time.Time to allow using time.Time methods directly if needed,
// but customizes JSON marshaling and unmarshaling.
type JsonDate struct {
	time.Time
}

const jsonDateFormat = "2006-01-02"

// UnmarshalJSON implements the json.Unmarshaler interface.
// It parses a YYYY-MM-DD string into a JsonDate.
func (jd *JsonDate) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	if s == "" || s == "null" { // Handles empty string or literal "null" string
		jd.Time = time.Time{} // Treat as zero date
		return nil
	}
	t, err := time.Parse(jsonDateFormat, s)
	if err != nil {
		return err // Propagate parsing error
	}
	jd.Time = t
	return nil
}

// MarshalJSON implements the json.Marshaler interface.
// It formats a JsonDate into a YYYY-MM-DD string.
func (jd JsonDate) MarshalJSON() ([]byte, error) {
	return []byte("\"" + jd.Time.Format(jsonDateFormat) + "\""), nil
}

// TimeToNullTime converts a non-zero time.Time to sql.NullTime.
// If the input time is zero, it returns an invalid sql.NullTime.
func TimeToNullTime(t *time.Time) sql.NullTime {
	if t != nil && !t.IsZero() {
		return sql.NullTime{
			Time:  *t,
			Valid: true,
		}
	}
	return sql.NullTime{Valid: false}
}

// NullTimeToTimePtr converts sql.NullTime to *time.Time.
// If the sql.NullTime is not valid, it returns nil.
func NullTimeToTimePtr(nt sql.NullTime) *time.Time {
	if nt.Valid {
		return &nt.Time
	}
	return nil
}

// UintPtrToNullInt64 converts a *uint to sql.NullInt64.
func UintPtrToNullInt64(u *uint) sql.NullInt64 {
	if u != nil {
		return sql.NullInt64{
			Int64: int64(*u),
			Valid: true,
		}
	}
	return sql.NullInt64{Valid: false}
}

// SqlNullInt64ToUintPtr converts sql.NullInt64 to *uint.
func SqlNullInt64ToUintPtr(ni sql.NullInt64) *uint {
	if ni.Valid {
		val := uint(ni.Int64)
		return &val
	}
	return nil
}

// StringPtrToNullString converts a *string to sql.NullString.
func StringPtrToNullString(s *string) sql.NullString {
	if s != nil && *s != "" {
		return sql.NullString{
			String: *s,
			Valid:  true,
		}
	}
	return sql.NullString{Valid: false}
}

// SqlNullStringToStringPtr converts sql.NullString to *string.
func SqlNullStringToStringPtr(ns sql.NullString) *string {
	if ns.Valid {
		return &ns.String
	}
	return nil
}

// UniqueUintSlice removes duplicate values from a uint slice.
func UniqueUintSlice(slice []uint) []uint {
	if len(slice) == 0 {
		return slice
	}
	keys := make(map[uint]bool)
	list := []uint{}
	for _, entry := range slice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

// StringToUint converts a string to uint. Returns 0 if conversion fails.
func StringToUint(s string) uint {
	v, err := strconv.ParseUint(s, 10, 32) // Use ParseUint for unsigned integers
	if err != nil {
		return 0 // Or handle error as needed
	}
	return uint(v)
}

// UintToNullInt64 converts uint to sql.NullInt64.
// If the input uint is 0, it returns a NullInt64 with Valid=false.
func UintToNullInt64(val uint) sql.NullInt64 {
	if val == 0 {
		return sql.NullInt64{Int64: 0, Valid: false}
	}
	return sql.NullInt64{Int64: int64(val), Valid: true}
}

// SqlNullFloat64ToFloat64Ptr converts sql.NullFloat64 to *float64.
// If the sql.NullFloat64 is not valid, it returns nil.
func SqlNullFloat64ToFloat64Ptr(nf sql.NullFloat64) *float64 {
	if nf.Valid {
		return &nf.Float64
	}
	return nil
}

// SqlNullBoolToBoolPtr converts sql.NullBool to *bool.
// If the sql.NullBool is not valid, it returns nil.
func SqlNullBoolToBoolPtr(nb sql.NullBool) *bool {
	if nb.Valid {
		return &nb.Bool
	}
	return nil
}

// MapKeysToUintSlice converts map[uint]struct{} keys to a []uint slice.
func MapKeysToUintSlice(m map[uint]struct{}) []uint {
	slice := make([]uint, 0, len(m))
	for k := range m {
		slice = append(slice, k)
	}
	return slice
}
