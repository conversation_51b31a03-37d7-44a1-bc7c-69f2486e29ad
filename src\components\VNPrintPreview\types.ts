/**
 * 可选的纸张尺寸
 */
export type PaperSize = 'A4' | 'Letter' | 'A3' | 'A5'; // 可以根据需要添加更多

/**
 * 打印方向
 */
export type PrintOrientation = 'portrait' | 'landscape'; // 纵向 | 横向

/**
 * 页边距对象 (单位: mm)
 */
export interface PrintMargins {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

/**
 * VNPrintPreview 组件的 Props 类型定义
 */
export interface VNPrintPreviewProps {
  /**
   * Controls the visibility of the preview overlay, supports v-model.
   * @default false
   */
  modelValue: boolean;
  // title?: string; // Title is no longer used by the overlay component
  defaultPaperSize?: PaperSize;
  defaultOrientation?: PrintOrientation;
  defaultMargins?: PrintMargins;
  previewBackgroundColor?: string;
   /**
    * 是否显示边距控制线 (可视化)
    * @default true
    */
  showMarginLines?: boolean;
}

/**
 * VNPrintPreview 组件的 Emits 类型定义
 */
export interface VNPrintPreviewEmits {
  /**
   * 更新 modelValue 的事件，用于 v-model
   * @param value 更新后的布尔值
   */
  (e: 'update:modelValue', value: boolean): void;

  /**
   * Dialog 关闭时触发的事件
   */
  (e: 'closed'): void;

  /**
   * 点击打印按钮后，实际调用 window.print() 之前触发，可用于准备打印数据
   */
  (e: 'before-print'): void;

  /**
   * 调用 window.print() 之后触发 (注意：无法准确知道打印是完成还是取消)
   */
  (e: 'after-print'): void;
}

// --- 内部使用的常量或类型 ---

// 纸张尺寸 (单位: mm) - 用于计算长宽比和缩放
export const PAPER_DIMENSIONS: Record<PaperSize, { width: number; height: number }> = {
  A4: { width: 210, height: 297 },
  Letter: { width: 215.9, height: 279.4 },
  A3: { width: 297, height: 420 },
  A5: { width: 148, height: 210 },
};

// 如果未来有更复杂的类型，例如打印内容的特定数据结构，也可以在这里定义
// export interface PrintableData { ... } 