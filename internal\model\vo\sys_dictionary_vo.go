package vo

// --- Dictionary Type VOs ---

// DictionaryTypeVO 字典类型视图对象 (用于详情和管理列表)
type DictionaryTypeVO struct {
	BaseVO           // 嵌入基础 VO (ID, CreatedAt, UpdatedAt 等)
	Code      string `json:"code"`      // 类型编码
	Name      string `json:"name"`      // 类型名称
	Status    int    `json:"status"`    // 状态 (1:启用, 0:禁用)
	IsSystem  bool   `json:"isSystem"`  // 新增：是否系统内置
	Remark    string `json:"remark"`    // 备注
	ItemCount int64  `json:"itemCount"` // 该类型下的字典项数量 (可选, 可能需要在 Service 层额外查询)
}

// DictionaryTypeSimpleVO 字典类型简要视图对象 (用于下拉列表等)
type DictionaryTypeSimpleVO struct {
	ID   uint   `json:"id"`   // ID
	Code string `json:"code"` // 类型编码
	Name string `json:"name"` // 类型名称
}

// --- Dictionary Item VOs ---

// DictionaryItemVO 字典项视图对象 (用于详情和管理列表)
type DictionaryItemVO struct {
	BaseVO                // 嵌入基础 VO (ID, CreatedAt, UpdatedAt 等)
	DictionaryTypeID uint `json:"dictionaryTypeId"` // 关联的字典类型ID
	// DictionaryTypeCode string `json:"dictionaryTypeCode"` // 关联的字典类型编码 (可选, 可能需要在 Service 层填充)
	// DictionaryTypeName string `json:"dictionaryTypeName"` // 关联的字典类型名称 (可选, 可能需要在 Service 层填充)
	Label     string `json:"label"`     // 标签
	Value     string `json:"value"`     // 值
	SortOrder int    `json:"sortOrder"` // 排序值
	Status    int    `json:"status"`    // 状态 (1:启用, 0:禁用)
	IsSystem  bool   `json:"isSystem"`  // 是否系统内置
	Remark    string `json:"remark"`    // 备注
}

// DictDataVO 字典数据视图对象 (用于公共接口 /items/list?typeCode=XXX)
type DictDataVO struct {
	Label string `json:"label"` // 标签 (用于显示)
	Value string `json:"value"` // 值 (用于提交)
	// 可以根据需要添加其他字段，如 SortOrder, ID 等
}

// PageDictTypeVO 字典类型分页视图对象
type PageDictTypeVO struct {
	PageVO                     // 嵌入通用分页 VO (Total, List, PageNum, PageSize)
	List   []*DictionaryTypeVO `json:"list"` // 替换 PageVO 中的 List 类型
}

// PageDictItemVO 字典项分页视图对象
type PageDictItemVO struct {
	PageVO                     // 嵌入通用分页 VO
	List   []*DictionaryItemVO `json:"list"` // 替换 PageVO 中的 List 类型
}
