package repository

import (
	"time" // For EndDate adjustment

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response" // For response.PageResult
	// "backend/pkg/util"     // For CalculatePages if used directly, or rely on PageResult methods (util is not directly used here, response.CalculatePages is)
	// "reflect" // If implementing column validation for sorting like UserRepository
	// "backend/pkg/constant" // If using default sort constants
)

// AuditLogRepository 定义了审计日志仓库的接口
type AuditLogRepository interface {
	Create(log *entity.AuditLog) error
	// Find now returns a PageResult containing entity.AuditLog and total count information
	Find(query dto.AuditLogQueryDTO) (*response.PageResult, error)
}

// auditLogRepositoryImpl 是 AuditLogRepository 的 GORM 实现
type auditLogRepositoryImpl struct {
	db *gorm.DB
	// BaseRepository[entity.AuditLog, uint64] // Could embed if BaseRepository is generic enough
}

// NewAuditLogRepository 是 AuditLogRepository 的构造函数
// 注意：计划书中提到 "Renamed from NewAuditLogRepositoryImpl"，但通常构造函数返回接口类型，命名为 New<InterfaceName>
func NewAuditLogRepository(db *gorm.DB) AuditLogRepository {
	return &auditLogRepositoryImpl{db: db}
}

// Create 创建一条新的审计日志记录
func (r *auditLogRepositoryImpl) Create(log *entity.AuditLog) error {
	return r.db.Create(log).Error
}

// Find 根据查询条件查找审计日志，并进行分页
func (r *auditLogRepositoryImpl) Find(query dto.AuditLogQueryDTO) (*response.PageResult, error) {
	var logs []*entity.AuditLog // Slice of pointers to match user repo example
	var total int64
	dbQuery := r.db.Model(&entity.AuditLog{})

	// Apply filter conditions from query DTO
	if query.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", query.UserID)
	}
	if query.Username != "" {
		dbQuery = dbQuery.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.Action != "" {
		dbQuery = dbQuery.Where("action = ?", query.Action)
	}
	if query.ResourceType != "" {
		dbQuery = dbQuery.Where("resource_type = ?", query.ResourceType)
	}
	if query.ClientIP != "" {
		dbQuery = dbQuery.Where("client_ip = ?", query.ClientIP)
	}
	if query.TraceID != "" {
		dbQuery = dbQuery.Where("trace_id = ?", query.TraceID)
	}
	if query.Status != "" {
		dbQuery = dbQuery.Where("status = ?", query.Status)
	}
	if !query.StartDate.IsZero() {
		dbQuery = dbQuery.Where("timestamp >= ?", query.StartDate)
	}
	if !query.EndDate.IsZero() {
		// Adjust EndDate to include the whole day
		endDateEndOfDay := query.EndDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
		dbQuery = dbQuery.Where("timestamp <= ?", endDateEndOfDay)
	}

	// Count total records matching filters
	err := dbQuery.Count(&total).Error
	if err != nil {
		// return nil, apperrors.Wrap(err, apperrors.CODE_DATA_QUERY_FAILED, "counting audit logs failed")
		return nil, err // Propagate GORM error directly for now
	}

	// Prepare PageResult (even if total is 0, PageNum/Size are needed)
	pageResult := &response.PageResult{
		Total:    total,
		PageNum:  query.Pagination.PageNum,
		PageSize: query.Pagination.PageSize,
		Sort:     query.Pagination.Sort, // Pass Sort Info from DTO's Pagination
	}
	pageResult.Pages = response.CalculatePages(total, query.Pagination.PageSize)

	// If no records, return early with pagination info but empty list
	if total == 0 {
		pageResult.List = []*entity.AuditLog{} // Ensure List is not nil but an empty slice
		return pageResult, nil
	}

	// Apply sorting from query.Pagination
	// response.PageQuery.OrderBy() should generate the SQL ORDER BY clause string
	// including handling of default sort order if query.Pagination.Sort is empty.
	orderByClause := query.Pagination.OrderBy()
	if orderByClause != "" {
		dbQuery = dbQuery.Order(orderByClause)
	} else {
		// Fallback if OrderBy() somehow returns empty (it should provide a default)
		dbQuery = dbQuery.Order("timestamp desc") // Default sort order
	}

	// Apply pagination from query.Pagination
	// query.Pagination.Offset() and Limit() are provided by response.PageQuery
	dbQuery = dbQuery.Offset(query.Pagination.Offset()).Limit(query.Pagination.Limit())

	// Find the records
	err = dbQuery.Find(&logs).Error
	if err != nil {
		// return nil, apperrors.Wrap(err, apperrors.CODE_DATA_QUERY_FAILED, "finding audit logs failed")
		return nil, err // Propagate GORM error directly for now
	}

	pageResult.List = logs
	return pageResult, nil
}
