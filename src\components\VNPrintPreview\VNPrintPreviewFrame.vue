<template>
  <div class="preview-layout">
    <!-- === 控制面板 (左侧) === -->
    <div class="control-panel">
      <el-scrollbar>
        <el-form label-position="top" size="small" style="padding: 0 15px;">
          <!-- 纸张大小 -->
          <el-form-item label="纸张大小">
            <el-select
              v-model="selectedPaperSize"
              placeholder="选择纸张"
              style="width: 100%;"
            >
              <el-option label="A4 (210mm x 297mm)" value="A4"></el-option>
              <el-option label="Letter (215.9mm x 279.4mm)" value="Letter"></el-option>
              <el-option label="A3 (297mm x 420mm)" value="A3"></el-option>
              <el-option label="A5 (148mm x 210mm)" value="A5"></el-option>
            </el-select>
          </el-form-item>

          <!-- 打印方向 -->
          <el-form-item label="打印方向">
            <el-radio-group v-model="selectedOrientation" style="width: 100%;">
              <el-radio-button value="portrait" style="width: 50%; text-align: center;">纵向</el-radio-button>
              <el-radio-button value="landscape" style="width: 50%; text-align: center;">横向</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 页边距 (新布局) -->
          <el-form-item label="页边距 (mm)">
            <div class="margin-layout">
              <div class="margin-top">
                <el-input-number v-model="currentMargins.top" :min="0" :step="1" controls-position="right" placeholder="上">
                </el-input-number>
              </div>
              <div class="margin-middle">
                <el-input-number v-model="currentMargins.left" :min="0" :step="1" controls-position="right" placeholder="左">
                </el-input-number>
                <el-input-number v-model="currentMargins.right" :min="0" :step="1" controls-position="right" placeholder="右">
                </el-input-number>
              </div>
              <div class="margin-bottom">
                <el-input-number v-model="currentMargins.bottom" :min="0" :step="1" controls-position="right" placeholder="下">
                </el-input-number>
              </div>
            </div>
          </el-form-item>

           <!-- 缩放 (暂未实现逻辑) -->
          <el-form-item label="缩放比例">
            <el-select v-model="scaleOption" placeholder="选择缩放" style="width: 100%;" disabled>
              <el-option label="适应页面" value="fit"></el-option>
              <el-option label="实际大小" value="actual"></el-option>
            </el-select>
          </el-form-item>

           <!-- 添加打印按钮 -->
           <el-form-item>
              <el-button type="primary" @click="print" :loading="isPrinting" style="width: 100%;">打 印</el-button>
           </el-form-item>

        </el-form>
      </el-scrollbar>
    </div>

    <!-- === 预览区域 (右侧) === -->
    <div ref="previewAreaRef" class="preview-area" :style="{ backgroundColor: previewBackgroundColor }">
       <el-scrollbar>
        <div class="paper-preview-wrapper"> 
          <!-- 模拟纸张 -->
          <div class="paper-preview" :style="paperStyle" ref="paperPreviewRef">
            <!-- Apply padding and styles directly to actual-content for preview -->
            <div 
              ref="printContentRef" 
              class="actual-content" 
              :style="actualContentPreviewStyle" 
            >
              <slot name="content"></slot>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  defineProps,
  defineEmits,
  withDefaults,
  defineExpose
} from 'vue';
import {
  ElScrollbar, ElForm, ElFormItem,
  ElSelect, ElOption, ElRadioGroup, ElRadioButton, ElInputNumber, ElMessage,
  ElButton
} from 'element-plus';
import type { PaperSize, PrintOrientation, PrintMargins } from './types';
import { PAPER_DIMENSIONS } from './types';

// --- Props for VNPrintPreviewFrame --- 
const props = withDefaults(defineProps<{
  defaultPaperSize?: PaperSize;
  defaultOrientation?: PrintOrientation;
  defaultMargins?: PrintMargins;
  previewBackgroundColor?: string;
  showMarginLines?: boolean;
  printStyles?: string;
}>(), {
  defaultPaperSize: 'A4',
  defaultOrientation: 'portrait',
  defaultMargins: () => ({ top: 20, right: 15, bottom: 20, left: 15 }),
  previewBackgroundColor: '#e0e0e0',
  showMarginLines: true,
  printStyles: '',
});

// --- Emits (Frame 自身可能不需要太多 emits，主要由外部控制) ---
// const emit = defineEmits([]);

// --- State Refs (与之前 index.vue 内部逻辑相同) ---
const printContentRef = ref<HTMLElement | null>(null);
const previewAreaRef = ref<HTMLElement | null>(null);
const paperPreviewRef = ref<HTMLElement | null>(null);

const selectedPaperSize = ref<PaperSize>(props.defaultPaperSize);
const selectedOrientation = ref<PrintOrientation>(props.defaultOrientation);
const currentMargins = ref<PrintMargins>(JSON.parse(JSON.stringify(props.defaultMargins)));
const scaleOption = ref('fit');
const previewScale = ref(1.0);
const isPrinting = ref(false);

// --- Computed Properties (与之前 index.vue 内部逻辑相同) ---
const currentPaperDimensions = computed(() => {
  const dims = PAPER_DIMENSIONS[selectedPaperSize.value];
  return {
    width: selectedOrientation.value === 'portrait' ? dims.width : dims.height,
    height: selectedOrientation.value === 'portrait' ? dims.height : dims.width,
  };
});

const paperStyle = computed<Record<string, string>>(() => {
  const dims = currentPaperDimensions.value;
  const scale = previewScale.value;
  const style = {
    width: `${dims.width}mm`,
    height: `${dims.height}mm`,
    boxShadow: '0 0 10px rgba(0,0,0,0.15)',
    transform: `scale(${scale})`,
    transformOrigin: 'top center',
  };
  return style;
});

const marginLineStyle = computed(() => ({
  top: { top: `${currentMargins.value.top}mm`, left: `${currentMargins.value.left}mm`, right: `${currentMargins.value.right}mm` },
  right: { right: `${currentMargins.value.right}mm`, top: `${currentMargins.value.top}mm`, bottom: `${currentMargins.value.bottom}mm` },
  bottom: { bottom: `${currentMargins.value.bottom}mm`, left: `${currentMargins.value.left}mm`, right: `${currentMargins.value.right}mm` },
  left: { left: `${currentMargins.value.left}mm`, top: `${currentMargins.value.top}mm`, bottom: `${currentMargins.value.bottom}mm` },
}));

const calculatePreviewScale = () => {
    if (!previewAreaRef.value || !paperPreviewRef.value) {
        return;
    }
    nextTick(() => {
        const previewAreaWidth = previewAreaRef.value!.clientWidth - 40; 
        const paperRenderedWidth = paperPreviewRef.value!.offsetWidth;

        if (previewAreaWidth <= 0 || paperRenderedWidth <= 0) {
            previewScale.value = 1.0;
            return;
        }
        const scale = Math.min(1.0, previewAreaWidth / paperRenderedWidth);
        previewScale.value = scale;
    });
};

const actualContentPreviewStyle = computed<Record<string, string>>(() => ({
  padding: `${currentMargins.value.top}mm ${currentMargins.value.right}mm ${currentMargins.value.bottom}mm ${currentMargins.value.left}mm`,
  boxSizing: 'border-box',
}));

// --- Methods (大部分与之前 index.vue 内部逻辑相同) ---
// --- New print method using IFrame ---
const print = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isPrinting.value) {
      console.warn('Printing already in progress.');
      return resolve();
    }
    if (!printContentRef.value) {
        ElMessage.error('无法找到打印内容区域');
        return reject(new Error('Print content area not found.'));
    }

    isPrinting.value = true;

    // 1. Get the HTML content
    const printContentHTML = printContentRef.value!.innerHTML;

    // 2. Generate @page CSS rules
    const pageStyle = `
      @page {
        size: ${selectedPaperSize.value} ${selectedOrientation.value};
        margin: ${currentMargins.value.top}mm ${currentMargins.value.right}mm ${currentMargins.value.bottom}mm ${currentMargins.value.left}mm;
      }
      body { margin: 0; padding: 0; }
    `;

    // --- IFrame Creation and Printing ---
    let iframe: HTMLIFrameElement | null = null;
    try {
      // 3. Create hidden iframe
      iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.width = '0';
      iframe.style.height = '0';
      iframe.style.border = '0';
      iframe.style.left = '-9999px'; // Position off-screen
      iframe.style.top = '-9999px';

      document.body.appendChild(iframe);

      // 4. Get iframe's document and write content + styles
      const iframeDoc = iframe.contentWindow?.document || iframe.contentDocument;
      if (!iframeDoc) {
        throw new Error('Could not access iframe document.');
      }

      iframeDoc.open();
      iframeDoc.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <title>打印</title>
            <style type="text/css">
              ${pageStyle}
              /* --- Inject styles passed via prop --- */
              ${props.printStyles}
            </style>
          </head>
          <body>
            ${printContentHTML}
          </body>
        </html>
      `);
      iframeDoc.close();

      // 5. Trigger print on iframe window
      const iframeWindow = iframe.contentWindow;
      if (!iframeWindow) {
          throw new Error('Could not access iframe window.');
      }

      // Add a small delay before printing to allow content rendering
      setTimeout(() => {
          try {
              iframeWindow.focus(); // Focus needed for some browsers
              iframeWindow.print();
              // Printing is blocking, promise resolves/rejects after cleanup timeout

              // 6. Cleanup (using timeout as before)
              setTimeout(() => {
                  if (iframe && iframe.parentNode) {
                      iframe.parentNode.removeChild(iframe);
                      console.log('Removed print iframe.');
                  }
                  isPrinting.value = false;
                  resolve();
              }, 500); // Timeout for cleanup

          } catch (printError) {
              console.error("Error during iframe printing:", printError);
              ElMessage.error('打印过程中发生错误');
               // Cleanup immediately on print error
              if (iframe && iframe.parentNode) {
                  iframe.parentNode.removeChild(iframe);
              }
              isPrinting.value = false;
              reject(printError);
          }
      }, 100); // Short delay before calling iframe.print()

    } catch (setupError) {
      console.error("Error setting up iframe for printing:", setupError);
      ElMessage.error('准备打印时发生错误');
      // Cleanup on setup error
      if (iframe && iframe.parentNode) {
        iframe.parentNode.removeChild(iframe);
      }
      isPrinting.value = false;
      reject(setupError);
    }
  });
};

// --- Watchers (与之前 index.vue 内部逻辑相同) ---
watch([selectedPaperSize, selectedOrientation], () => {
  nextTick(calculatePreviewScale);
});

watch(currentMargins, () => {
   console.log("Margins changed:", currentMargins.value);
}, { deep: true });

// --- Lifecycle and Resize (与之前 index.vue 内部逻辑相同) ---
let resizeObserver: ResizeObserver | null = null;

onMounted(() => {
  nextTick(calculatePreviewScale);
  if (previewAreaRef.value) {
    resizeObserver = new ResizeObserver(calculatePreviewScale);
    resizeObserver.observe(previewAreaRef.value);
  }
});

onUnmounted(() => {
  if (resizeObserver && previewAreaRef.value) {
    resizeObserver.unobserve(previewAreaRef.value);
  }
   // Remove cleanup for printStyleElement as it's no longer used
   /*
   if (printStyleElement && printStyleElement.parentNode) {
       document.head.removeChild(printStyleElement);
       printStyleElement = null;
   }
   */
    // Ensure is-printing class is removed if component is destroyed mid-print
    document.body.classList.remove('is-printing'); 
});

// --- Expose the print method AND isPrinting state --- 
defineExpose({ 
  print, 
  isPrinting // Expose the reactive ref
});

</script>

<style scoped>
/* --- Scoped 样式 (与之前 index.vue 内部逻辑基本相同，但移除了 Dialog 相关样式) --- */
.preview-layout {
  display: flex;
  height: 100%; /* Frame 需要能撑满容器 */
  background-color: var(--el-bg-color-page);
  overflow: hidden;
}

.control-panel {
  width: 280px;
  flex-shrink: 0;
  border-right: 1px solid var(--el-border-color);
  background-color: var(--el-bg-color-overlay);
  height: 100%;
  display: flex;
  flex-direction: column;
}
.control-panel .el-scrollbar {
  flex-grow: 1;
}
.control-panel .el-form-item {
  margin-bottom: 15px;
}
.margin-layout {
  display: flex;
  flex-direction: column;
  align-items: center; 
  gap: 8px; 
}
.margin-middle {
  display: flex;
  justify-content: space-between; 
  width: 100%; 
  gap: 10px; 
}
.margin-top .el-input-number,
.margin-bottom .el-input-number {
  width: 60%; 
}
.margin-middle .el-input-number {
  width: calc(50% - 5px); 
}
.margin-layout :deep(.el-input__inner) {
  text-align: center;
}

.preview-area {
  flex-grow: 1;
  overflow: hidden; 
  height: 100%; 
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: v-bind(previewBackgroundColor);
}
.preview-area .el-scrollbar {
  flex-grow: 1; 
  height: 100%; 
}

.paper-preview-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  box-sizing: border-box;
}

.paper-preview {
  position: relative;
  background-color: #fff;
  margin: 20px auto; /* Center the paper */
  transition: transform 0.3s ease-out;
  overflow: hidden; /* Try applying overflow here */
}

.margin-line {
  position: absolute;
  border: 1px dashed rgba(255, 0, 0, 0.5);
  z-index: 1;
  pointer-events: none;
}
.margin-line.top { border-width: 1px 0 0 0; left: 0; right: 0; }
.margin-line.right { border-width: 0 1px 0 0; top: 0; bottom: 0; }
.margin-line.bottom { border-width: 0 0 1px 0; left: 0; right: 0; }
.margin-line.left { border-width: 0 0 0 1px; top: 0; bottom: 0; }

.actual-content {
  background-color: #fff;
  overflow: visible;
  position: static !important; 
  /* Let padding be handled by actualContentPreviewStyle */
  /* Let width/height flow naturally or be set by content */
  width: auto;
  height: auto;
}

/* --- 移除 .dialog-footer 样式 --- */

</style>

<!-- 将打印样式移到 Frame 组件内部 -->
<!-- <style>
@media print {
  ...
}
</style> --> 