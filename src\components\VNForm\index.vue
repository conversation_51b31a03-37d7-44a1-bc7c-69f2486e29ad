<template>
  <div class="vn-form">
    <!-- Validation Overlay -->
    <div
      v-if="isValidating"
      class="validation-overlay"
      v-loading="isValidating"
      element-loading-text="校验中..."
      element-loading-background="rgba(255, 255, 255, 0.7)"
    >
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :size="size"
      :disabled="props.disabled || isValidating"
      :inline="inline"
      @submit.prevent
    >
      <!-- 表单头部区域，包含标题和状态标签 -->
      <div v-if="title || (statusTags && statusTags.some(tag => tag.visible))" class="form-header-container">
        <h3 v-if="title">{{ title }}</h3>
        <template v-for="(tag, index) in statusTags" :key="index">
          <el-tag
            v-if="tag.visible"
            :type="tag.type"
            effect="light"
            size="small"
            class="status-tag"
          >
            {{ tag.content }}
          </el-tag>
        </template>
      </div>
      
      <!-- 表单头部字段 - 按组渲染 -->
      <div v-for="group in groupedHeaderFields" :key="group.groupName || '__default__'" class="form-group-section">
        <!-- Render group title based on prop -->
        <template v-if="group.groupName && props.groupTitleType !== 'none'">
          <h4 v-if="props.groupTitleType === 'h4'" class="group-title">{{ group.groupName }}</h4>
          <el-divider v-else-if="props.groupTitleType === 'divider'">{{ group.groupName }}</el-divider>
        </template>
        
        <el-row :gutter="20">
          <el-col
            v-for="item in group.fields"
            :key="item.field"
            :xs="24"
            :sm="24"
            :md="item.md ?? (item.type === 'textarea' ? 24 : defaultSpanMd)"
            :lg="item.lg ?? (item.type === 'textarea' ? 24 : defaultSpanLg)"
            :xl="item.xl ?? (item.type === 'textarea' ? 24 : defaultSpanXl)"
          >
            <el-form-item
              :label="item.label"
              :prop="item.field"
              :rules="item.rules"
            >
              <!-- 输入框 -->
              <template v-if="!item.type || item.type === 'text'">
                <el-input
                  v-if="item.disabled && typeof item.formatter === 'function'"
                  :model-value="item.formatter(formData[item.field], item, formData)"
                  :placeholder="item.placeholder"
                  disabled
                  :clearable="false"
                  v-bind="item.props || {}"
                />
                <el-input
                  v-else
                  v-model="formData[item.field]"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  :clearable="item.clearable ?? true"
                  v-bind="item.props || {}"
                />
              </template>

              <!-- 密码输入框 -->
              <el-input
                v-else-if="item.type === 'password'"
                v-model="formData[item.field]"
                type="password"
                :placeholder="item.placeholder"
                :disabled="item.disabled"
                :show-password="item.props?.['showPassword'] ?? true"
                :clearable="item.clearable ?? true"
                v-bind="item.props || {}"
              />

              <!-- 文本域 -->
              <el-input
                v-else-if="item.type === 'textarea'"
                v-model="formData[item.field]"
                type="textarea"
                :rows="item.rows"
                :autosize="item.autosize"
                :placeholder="item.placeholder"
                :disabled="item.disabled"
                :clearable="item.clearable"
                v-bind="item.props || {}"
              />

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="item.type === 'number'"
                v-model="formData[item.field]"
                :min="item.min"
                :max="item.max"
                :precision="item.precision"
                :step="item.step"
                :placeholder="item.placeholder"
                :disabled="item.disabled"
                v-bind="item.props || {}"
              />

              <!-- 选择器 -->
              <template v-else-if="item.type === 'select'">
                <el-select
                  v-if="!props.disabled" 
                  v-model="formData[item.field]"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled" 
                  :clearable="item.clearable"
                  v-bind="item.props || {}"
                >
                  <el-option
                    v-for="option in getActualOptions(item.options)"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    :disabled="option.disabled"
                  />
                </el-select>
                <div v-else class="disabled-select-text-wrapper">
                  <el-text class="disabled-select-text">
                    {{ getSelectLabel(formData[item.field], item.options) }}
                  </el-text>
                </div>
              </template>

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="item.type === 'date'"
                v-model="formData[item.field]"
                :type="item.props?.['type'] || 'date'"
                :format="item.format"
                :value-format="item.valueFormat"
                :placeholder="item.placeholder"
                :disabled="item.disabled"
                :clearable="item.clearable"
                v-bind="item.props || {}"
              />

              <!-- 单选框组 -->
              <el-radio-group
                v-else-if="item.type === 'radio'"
                v-model="formData[item.field]"
                :disabled="item.disabled"
                v-bind="item.props || {}"
              >
                <el-radio
                  v-for="option in item.options"
                  :key="option.value"
                  :value="option.value"
                  :disabled="option.disabled"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>

              <!-- 复选框组 -->
              <el-checkbox-group
                v-else-if="item.type === 'checkbox-group'"
                v-model="formData[item.field]"
                :disabled="item.disabled"
                v-bind="item.props || {}"
              >
                <el-checkbox
                  v-for="option in item.options"
                  :key="option.value"
                  :label="option.value"
                  :disabled="option.disabled"
                >
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>

              <!-- 单个复选框 (Boolean) -->
              <el-checkbox
                v-else-if="item.type === 'checkbox'"
                v-model="formData[item.field]"
                :disabled="item.disabled"
                :true-label="item.props?.['trueLabel'] ?? true"
                :false-label="item.props?.['falseLabel'] ?? false"
                v-bind="item.props || {}"
              />

              <!-- 开关 -->
              <el-switch
                v-else-if="item.type === 'switch'"
                v-model="formData[item.field]"
                :active-text="item.props?.['activeText']"
                :inactive-text="item.props?.['inactiveText']"
                :active-value="item.props?.['activeValue'] ?? true"
                :inactive-value="item.props?.['inactiveValue'] ?? false"
                :inline-prompt="item.props?.['inlinePrompt']"
                :disabled="item.disabled"
                v-bind="item.props || {}"
              />

              <!-- 上传组件 -->
              <el-upload
                v-else-if="item.type === 'upload'"
                :action="item.action"
                :headers="item.headers"
                :multiple="item.multiple"
                :limit="item.limit"
                v-bind="item.props || {}"
              >
                <el-button type="primary">点击上传</el-button>
              </el-upload>

              <!-- 自定义插槽 -->
              <slot
                v-else-if="item.type === 'slot'"
                :name="'form-item-' + item.field"
                :field="item"
                :form-data="formData"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 明细表区域 -->
      <div v-if="hasDetailTables" class="form-details">
        <el-tabs v-if="processedDetailTables.length > 1" v-model="activeDetailTabName">
          <el-tab-pane
            v-for="(detail, index) in processedDetailTables"
            :key="detail.title || index"
            :label='detail.title || `明细 ${index + 1}`'
            :name="detail.title || index.toString()"
            lazy
          >
            <div class="detail-table-section">
                        <!-- eslint-disable-next-line vue/valid-v-bind-sync vue/require-component-is -->
          <!-- @vue-ignore -->
          <VNTable
            :key="`detail-table-${index}-${tableRefreshKey}`"
            v-bind="detail.tableProps"
            :data="formData.details?.[index] || []"
            :ref="el => { if (el) detailTableRefs[index] = el as InstanceType<typeof VNTable> }"
            @add="() => handleDetailAdd(index)" 
            @delete="(row: any, rowIndex: number) => handleDetailDelete(index, row, rowIndex)"
            @row-save="(row: any, rowIndex: number, oldRow: any) => handleDetailRowSave(index, row)"
            @row-delete="(row: any, rowIndex: number) => handleDetailRowDelete(index, row, rowIndex)"
            @edit="(row: any, rowIndex: number) => handleDetailEdit(index, row, rowIndex)"
            @cell-click="(row: any, column: any, cell: any, event: Event) => handleDetailCellClick(index, row, column, cell, event)"
            @cell-dblclick="(row: any, column: any, cell: any, event: Event) => emit('cell-dblclick', index, row, column, cell, event)"
          >
            <!-- 透传明细表插槽 -->
            <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
              <slot :name="slotName" v-bind="slotProps || {}"></slot>
            </template>
          </VNTable>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div v-else-if="processedDetailTables.length === 1" class="detail-table-section">
          <h4>{{ processedDetailTables[0]?.title || '明细信息' }}</h4>
          <!-- eslint-disable-next-line vue/valid-v-bind-sync vue/require-component-is -->
          <!-- @vue-ignore -->
          <VNTable
            :key="`detail-table-0-${tableRefreshKey}`"
            v-bind="processedDetailTables[0].tableProps"
            :data="formData.details?.[0] || []"
            :ref="el => { if (el) detailTableRefs[0] = el as InstanceType<typeof VNTable> }"
            @add="() => handleDetailAdd(0)" 
            @delete="(row: any, rowIndex: number) => handleDetailDelete(0, row, rowIndex)"
            @row-save="(row: any, rowIndex: number, oldRow: any) => handleDetailRowSave(0, row)"
            @row-delete="(row: any, rowIndex: number) => handleDetailRowDelete(0, row, rowIndex)"
            @edit="(row: any, rowIndex: number) => handleDetailEdit(0, row, rowIndex)"
            @cell-click="(row: any, column: any, cell: any, event: Event) => handleDetailCellClick(0, row, column, cell, event)"
            @cell-dblclick="(row: any, column: any, cell: any, event: Event) => emit('cell-dblclick', 0, row, column, cell, event)"
          >
            <!-- 透传明细表插槽 -->
            <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
              <slot :name="slotName" v-bind="slotProps || {}"></slot>
            </template>
          </VNTable>
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div v-if="showSubmitButtons && !props.disabled" class="form-actions">
        <slot name="actions" :form-data="formData" :submit="submitForm" :reset="resetForm">
          <el-button
            type="primary"
            :loading="loading"
            @click="submitForm"
          >
            {{ submitButtonText }}
          </el-button>
          <el-button v-if="showCancelButton" @click="resetForm">{{ cancelButtonText }}</el-button>
        </slot>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits, shallowRef, nextTick, isRef } from 'vue';
import type { FormInstance, FormItemRule } from 'element-plus';
import VNTable from '../VNTable/index.vue';
import type { VNTableProps } from '../VNTable/types'; // Ensure VNTableProps is imported if needed by Partial<VNTableProps>
import type {
  HeaderField,
  DetailTableConfig,
  VNFormProps,
  VNFormEmits,
  VNFormMethods
} from './types';
import {
  ElForm, ElRow, ElCol, ElFormItem, ElInput, ElInputNumber, ElSelect,
  ElOption, ElDatePicker, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox,
  ElUpload, ElButton, ElTabs, ElTabPane, ElDivider, ElTag, ElSwitch, ElText
} from 'element-plus';
import { cloneDeep } from 'lodash-es'; // <<< Import cloneDeep

// --- Default Config for Embedded VNTable ---
const defaultTableProps = {
  selectionType: 'multiple', // Default WITH selection column
  showOperations: true,
  showIndex: true, // Default WITH index column
  toolbarConfig: {
    add: true,
    batchDelete: true, // Default WITH batch delete button
    filter: false,
    expandAll: false,
    collapseAll: false,
    layoutSwitch: false,
    refresh: false,
    density: false,
    columnSetting: false,
    fullscreen: false,
    import: false,
    export: false,
  },
  // Default operation buttons (only Delete, Edit is handled by VNTable row edit mode)
  operationButtons: [
    // {
    //   label: '编辑',
    //   icon: 'Edit',
    //   type: 'primary',
    //   action: 'edit'
    // },
    {
      label: '删除',
      icon: 'Delete',
      type: 'danger',
      action: 'delete'
    }
  ]
};

// Props 定义
const props = withDefaults(defineProps<VNFormProps>(), {
  title: '',
  headerFields: () => [],
  modelValue: () => ({}),
  detailTables: () => [],
  groupTitleType: 'h4', // Default group title type
  labelWidth: '100px',
  labelPosition: 'right',
  size: 'default',
  inline: false,
  disabled: false,
  showSubmitButtons: true,
  showCancelButton: true,
  submitButtonText: '提交',
  cancelButtonText: '取消',
  loading: false,
  defaultColumns: 3,
  statusTags: () => [],
});

// Emits 定义
const emit = defineEmits<VNFormEmits>();

// Refs
const formRef = ref<FormInstance>();
const formData = ref<Record<string, any>>({}); // Internal state
const detailTableRefs = shallowRef<InstanceType<typeof VNTable>[]>([]);
const activeDetailTabName = ref<string | number>('');
const isValidating = ref(false);
const tableRefreshKey = ref(0);

// Computed
const hasDetailTables = computed(() => props.detailTables?.length > 0);

const visibleHeaderFields = computed(() => {
  return props.headerFields.filter(field => {
    if (typeof field.visible === 'function') {
      return field.visible(formData.value);
    }
    return field.visible !== false;
  });
});

// Group header fields based on the 'group' property
const groupedHeaderFields = computed(() => {
  const groups: Record<string, HeaderField[]> = {};
  const result: { groupName?: string; fields: HeaderField[] }[] = [];
  const defaultGroupName = '__default__'; // Internal key for ungrouped fields

  visibleHeaderFields.value.forEach(field => {
    const groupName = field.group || defaultGroupName;
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(field);
  });

  // Ensure ungrouped fields appear first, or handle order differently if needed
  if (groups[defaultGroupName]) {
    result.push({ groupName: undefined, fields: groups[defaultGroupName] });
  }

  Object.keys(groups).forEach(groupName => {
    if (groupName !== defaultGroupName) {
      if (groups[groupName]) {
        result.push({ groupName: groupName, fields: groups[groupName] });
      }
    }
  });

  return result;
});

const formRules = computed(() => {
  const rules: Record<string, any> = {};
  props.headerFields.forEach(field => {
    if (field.rules) {
      rules[field.field] = field.rules;
    }
  });
  return rules;
});

// Process detail tables to merge default props with user-provided props
const processedDetailTables = computed(() => {
  return props.detailTables.map(detail => {
    // Deep merge toolbarConfig, shallow merge the rest
    const mergedToolbarConfig = {
      ...defaultTableProps.toolbarConfig,
      ...(detail.tableProps?.toolbarConfig || {}), // User's toolbarConfig overrides defaults
    };
    const mergedProps = {
      ...defaultTableProps, // Apply general defaults first
      ...(detail.tableProps || {}), // Apply user props, overwriting general defaults
      toolbarConfig: mergedToolbarConfig, // Apply the specifically merged toolbarConfig
    };
    return {
      ...detail, // Keep other properties like title
      tableProps: mergedProps,
    };
  });
});

// <<< 新增：计算默认栅格跨度
const calculateSpan = (totalColumns: number): number => {
  if (totalColumns <= 1) return 24;
  if (totalColumns === 2) return 12;
  if (totalColumns === 3) return 8;
  // 对于4列及以上，可以根据需要调整，这里暂定为6 (即4列)
  // 或者可以抛出警告/错误，或默认回退到更少列数
  if (totalColumns >= 4) return 6; 
  return 12; // 默认回退到2列
};

const defaultSpanMd = computed(() => calculateSpan(props.defaultColumns));
// 简单处理 lg/xl，可以根据需要实现更复杂的响应式逻辑
// 例如，可以允许 props.defaultColumnsLg 等，或根据屏幕尺寸动态调整
const defaultSpanLg = computed(() => calculateSpan(props.defaultColumns)); 
const defaultSpanXl = computed(() => calculateSpan(props.defaultColumns));

// 新增辅助函数，用于获取真实的选项数组 (处理 Ref 情况)
const getActualOptions = (optionsConfig: HeaderField['options']): Array<{ label: string, value: any, disabled?: boolean }> => {
  if (!optionsConfig) return [];
  if (isRef(optionsConfig)) {
    return optionsConfig.value || [];
  }
  return Array.isArray(optionsConfig) ? optionsConfig : [];
};

// 新增或修改辅助函数，用于获取 Select 的 Label 值
const getSelectLabel = (currentValue: any, optionsConfig: HeaderField['options']): string => {
  const actualOpts = getActualOptions(optionsConfig);
  
  // 检查值是否为空 (null, undefined, 或空字符串)
  const isEmptyValue = currentValue === null || currentValue === undefined || String(currentValue).trim() === '';

  if (actualOpts.length === 0) { // 如果没有选项
    return isEmptyValue ? '' : String(currentValue); // 空值返回''，否则返回值本身
  }

  if (isEmptyValue) { // 如果值为空，直接返回空字符串
    return '';
  }

  const stringValue = String(currentValue);
  const foundOption = actualOpts.find(opt => String(opt.value) === stringValue);
  
  return foundOption ? foundOption.label : String(currentValue); // 如果找不到标签，返回原始值，确保不返回 undefined
};

// --- Watch for external modelValue changes --- (Keep this watch)
watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(formData.value)) {
    console.log('VNForm: modelValue changed externally, updating internal formData.');
    formData.value = cloneDeep(newValue);
  }
}, { deep: true, immediate: true });

// --- NEW: Watch for internal formData changes and emit update --- 
watch(formData, (newFormData) => {
  // Only emit if the internal change wasn't triggered by the external watch
  // Comparing stringified values helps prevent infinite loops if objects are involved
  if (JSON.stringify(newFormData) !== JSON.stringify(props.modelValue)) {
      console.log('VNForm: Internal formData changed, emitting update:modelValue');
      emit('update:modelValue', cloneDeep(newFormData)); // Emit the entire updated object
  }
}, { deep: true });

// Methods
// --- Field Change Handling (Now only relevant if specifically needed, but no component uses it) ---
// We can keep the function definition in case it's needed later for specific components
// or remove it entirely if we are sure 'field-change' event is not needed.
/*
const handleFieldChange = (field: string, value: any) => {
  console.log(`VNForm: Field changed via @change: ${field}, emitting field-change`);
  emit('field-change', { field, value, formData: formData.value }); 
};
*/

// --- Validation Methods ---
const validateHeaderForm = async (): Promise<boolean> => {
  if (!formRef.value) return true; // If no form, consider it valid
  try {
    await formRef.value.validate();
    return true;
  } catch (error) {
    // Element Plus validate rejects with errors, so catch is expected for invalid form
    console.warn("Header form validation failed:", error);
    return false;
  }
};

const validateDetailTables = async (): Promise<boolean> => {
  if (!hasDetailTables.value) return true; // No tables, valid

  let allTablesValid = true;
  for (const tableRef of detailTableRefs.value) {
    if (tableRef && typeof tableRef.ValidateTable === 'function') {
      try {
        const tableValid = await tableRef.ValidateTable();
        if (!tableValid) {
          allTablesValid = false;
          // Optional: Stop on first invalid table?
          // return false;
        }
      } catch (error) {
        console.error("Error during detail table validation:", error);
        allTablesValid = false;
        // Optional: Stop on error?
        // return false;
      }
    } else {
       console.warn("ValidateTable method not found on a detail table ref.");
       // Consider how to handle this case - fail validation?
       // allTablesValid = false; 
    }
  }
  return allTablesValid;
};

// Combined validation method
const validateForm = async (): Promise<boolean> => {
  isValidating.value = true; // Show overlay during combined validation
  try {
    // Run validations in parallel or sequence? Parallel might be faster.
    const [headerValid, detailsValid] = await Promise.all([
      validateHeaderForm(),
      validateDetailTables()
    ]);
    const overallValid = headerValid && detailsValid;
    if (!overallValid) {
      // Optionally scroll to the first error? ElForm does this for header.
      // Need similar logic for table errors if desired.
      console.log("Overall form validation failed.");
    }
    return overallValid;
  } catch (error) {
     console.error("Error during combined validation process:", error);
     return false;
  } finally {
     isValidating.value = false; // Hide overlay after validation finishes
  }
};

// --- Form Submission ---
const submitForm = async () => {
  if (isValidating.value || props.loading || props.disabled) return;

  // Note: validateForm now handles the isValidating state internally
  const overallValid = await validateForm(); // Call the combined validation
  
  if (overallValid) {
    // isValidating will be set to false by validateForm's finally block
    try {
      const details = detailTableRefs.value.map((tableRef, index) => {
        if (tableRef && typeof tableRef.GetData === 'function') {
          return tableRef.GetData(); // Assume GetData doesn't throw significant errors after validation
        } else {
          return props.detailTables[index]?.tableProps?.data || [];
        }
      });
      
      emit('submit', {
        header: { ...formData.value, details: undefined }, // 移除 details 避免重复
        details: details
      });
    } catch (error) { // Catch potential errors from GetData in the submission phase
       console.error("Error during GetData after successful validation:", error);
       // Depending on severity, you might still want to emit or show an error
    }
  } 
  // If !overallValid, validateForm already handled isValidating=false and potentially showed errors
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  detailTableRefs.value.forEach(tableRef => {
    if (tableRef && typeof tableRef.resetData === 'function') {
      tableRef.resetData();
    }
  });
  // Reset form data based on initial modelValue after resetFields clears it
  // This ensures fields not part of resetFields (like details) are also reset
  nextTick(() => {
    formData.value = cloneDeep(props.modelValue);
    tableRefreshKey.value++; // Force detail table re-render if needed
  });
  emit('cancel');
};

const clearValidate = (fields?: string | string[]) => {
  if (formRef.value) {
    formRef.value.clearValidate(fields);
  }
};

const scrollToField = (field: string) => {
  if (formRef.value) {
    formRef.value.scrollToField(field);
  }
};

// --- Detail Table Interaction Handlers (Modified) ---
const handleDetailAdd = async (tableIndex: number) => {
  if (props.disabled) return;
  if (typeof props.onRowAdd === 'function') {
    let newRowData = null;
    try {
      newRowData = await props.onRowAdd(tableIndex);
    } catch (error) {
      console.error(`[VNForm] Error calling onRowAdd for table index ${tableIndex}:`, error);
      return;
    }

    if (newRowData) {
      // 1. Ensure internal details array exists
      if (!Array.isArray(formData.value['details'])) {
        formData.value['details'] = [];
      }
      while (formData.value['details'].length <= tableIndex) {
        formData.value['details'].push([]);
      }
      if (!Array.isArray(formData.value['details'][tableIndex])){
          formData.value['details'][tableIndex] = [];
      }
      
      // 2. Directly modify internal formData (push is reactive)
      formData.value['details'][tableIndex].push(cloneDeep(newRowData));
      console.log('[VNForm] Internal details updated via push.');
      
      // 3. Emit the updated internal state
      emit('update:modelValue', cloneDeep(formData.value));
      
      // 4. 新增：自动进入编辑模式
      await nextTick(); // 等待 DOM 更新
      const tableRef = detailTableRefs.value[tableIndex];
      if (tableRef && typeof tableRef.HandleEditRow === 'function') {
        try {
          // 获取刚添加的行数据（最后一行）
          const addedRow = formData.value['details'][tableIndex][formData.value['details'][tableIndex].length - 1];
          tableRef.HandleEditRow(addedRow); // 进入编辑模式
          console.log('[VNForm] Auto-entered edit mode for new row:', addedRow);
        } catch (error) {
          console.warn('[VNForm] Failed to auto-enter edit mode:', error);
        }
      } else {
        console.warn('[VNForm] Table ref or HandleEditRow method not available for auto-edit');
      }
    }
  } else {
    console.warn(`[VNForm] Received 'add' event for table ${tableIndex} but 'onRowAdd' prop is not provided.`);
  }
};

const handleDetailDelete = async (tableIndex: number, row: any, rowIndex: number) => {
  if (props.disabled) return;
  if (typeof props.onRowDelete === 'function') {
    let deleteSuccess = false;
    try {
      deleteSuccess = await props.onRowDelete(tableIndex, row, rowIndex);
    } catch (error) {
      console.error(`[VNForm] Error calling onRowDelete for table index ${tableIndex}:`, error);
      return;
    }

    if (deleteSuccess) {
      // 1. Validate internal details structure
      if (!Array.isArray(formData.value['details']) || !Array.isArray(formData.value['details'][tableIndex])) {
        console.warn(`[VNForm] Cannot delete row: internal details data for table ${tableIndex} is missing or not an array.`);
        return;
      }
      const internalTableData = formData.value['details'][tableIndex];

      // 2. Determine rowKey
      let rowKeyProp = 'id'; 
      const detailConfig = processedDetailTables.value[tableIndex];
      const tableProps = detailConfig?.tableProps;
      if (tableProps && typeof tableProps.rowKey === 'string') {
          rowKeyProp = tableProps.rowKey;
      } 
      // ... (warning for function rowKey omitted for brevity)

      // 3. Find index in internal data
      const actualIndex = internalTableData.findIndex(item =>
          item && row &&
          item.hasOwnProperty(rowKeyProp) && row.hasOwnProperty(rowKeyProp) &&
          item[rowKeyProp] === row[rowKeyProp]
      );

      if (actualIndex > -1) {
        // 4. Directly modify internal formData using splice (reactive)
        internalTableData.splice(actualIndex, 1);
        console.log('[VNForm] Internal details updated via splice (delete).');

        // 5. Emit the updated internal state
        emit('update:modelValue', cloneDeep(formData.value));
      } else {
        console.warn(`[VNForm] Row to delete not found in internal table ${tableIndex} data using rowKey '${rowKeyProp}':`, row);
      }
    }
  } else {
    console.warn(`[VNForm] Received 'delete' event for table ${tableIndex} but 'onRowDelete' prop is not provided.`);
  }
};

// <<< 修改：处理明细表行内保存事件 >>>
const handleDetailRowSave = (tableIndex: number, newRowData: any) => {
  if (props.disabled) return;
  
  // 1. Validate internal details structure
  if (!Array.isArray(formData.value['details']) || !Array.isArray(formData.value['details'][tableIndex])) {
    console.warn(`[VNForm] Cannot update row: internal details data for table ${tableIndex} is missing or not an array.`);
    return;
  }
  const internalTableData = formData.value['details'][tableIndex];

  // 2. Determine rowKey
  let rowKeyProp = 'id';
  const detailConfig = processedDetailTables.value[tableIndex];
  const tableProps = detailConfig?.tableProps;
  if (tableProps && typeof tableProps.rowKey === 'string') {
    rowKeyProp = tableProps.rowKey;
  } 
  // ... (warning for function rowKey omitted for brevity)

  // 3. Find index in internal data
  const actualIndex = internalTableData.findIndex(item =>
    item && newRowData &&
    item.hasOwnProperty(rowKeyProp) && newRowData.hasOwnProperty(rowKeyProp) &&
    item[rowKeyProp] === newRowData[rowKeyProp]
  );

  if (actualIndex > -1) {
    // 4. Directly update internal formData using splice (reactive)
    internalTableData.splice(actualIndex, 1, cloneDeep(newRowData));
    console.log('[VNForm] Internal details updated via splice (save).');

    // 5. Emit the updated internal state
    emit('update:modelValue', cloneDeep(formData.value));

    // 6. Remove the key refresh for now, rely on reactivity
    // nextTick(() => { 
    //     tableRefreshKey.value++;
    // });

  } else {
    console.warn(`[VNForm] Row to update not found in internal data for table ${tableIndex} using rowKey '${rowKeyProp}':`, newRowData);
  }
};

// <<< 新增：处理明细表编辑事件 (通常由卡片/列表视图触发) >>>
const handleDetailEdit = (tableIndex: number, row: any, rowIndex: number) => {
  console.log(`[VNForm] Received 'edit' event from detail table ${tableIndex}, row:`, row);
  // 直接将事件冒泡给父组件
  emit('edit', tableIndex, row);
};

// <<< 新增：处理明细表单元格点击事件 >>>
const handleDetailCellClick = (tableIndex: number, row: any, column: any, cell: any, event: Event) => {
  console.log(`[VNForm] Received 'cell-click' event from detail table ${tableIndex}, row:`, row, 'column:', column);
  // 将事件冒泡给父组件
  emit('cell-click', tableIndex, row, column, cell, event);
};

// <<< 新增：处理明细表行删除事件 (新增空行取消时触发) >>>
const handleDetailRowDelete = (tableIndex: number, row: any, rowIndex: number) => {
  console.log(`[VNForm] Received 'row-delete' event from detail table ${tableIndex}, row:`, row);
  
  // 1. Validate internal details structure
  if (!Array.isArray(formData.value['details']) || !Array.isArray(formData.value['details'][tableIndex])) {
    console.warn(`[VNForm] Cannot delete row: internal details data for table ${tableIndex} is missing or not an array.`);
    return;
  }
  const internalTableData = formData.value['details'][tableIndex];

  // 2. Determine rowKey
  let rowKeyProp = 'id';
  const detailConfig = processedDetailTables.value[tableIndex];
  const tableProps = detailConfig?.tableProps;
  if (tableProps && typeof tableProps.rowKey === 'string') {
    rowKeyProp = tableProps.rowKey;
  }

  // 3. Find index in internal data
  const actualIndex = internalTableData.findIndex(item =>
    item && row &&
    item.hasOwnProperty(rowKeyProp) && row.hasOwnProperty(rowKeyProp) &&
    item[rowKeyProp] === row[rowKeyProp]
  );

  if (actualIndex > -1) {
    // 4. Directly modify internal formData using splice (reactive)
    internalTableData.splice(actualIndex, 1);
    console.log('[VNForm] Internal details updated via splice (row-delete).');

    // 5. Emit the updated internal state
    emit('update:modelValue', cloneDeep(formData.value));
  } else {
    console.warn(`[VNForm] Row to delete not found in internal table ${tableIndex} data using rowKey '${rowKeyProp}':`, row);
  }
};

// 新增：获取内部 VNTable 实例的方法
const getDetailTable = (index: number): InstanceType<typeof VNTable> | undefined => {
  return detailTableRefs.value[index];
};

// 暴露方法
defineExpose<VNFormMethods>({
  resetForm,
  validateForm,
  clearValidate,
  scrollToField,
  getDetailTable
});
</script>

<style scoped>
.vn-form {
  position: relative; /* Needed for absolute positioning of the overlay */
  padding: 20px;
  background-color: #fff;
  --vnform-fixed-input-width: 240px; /* Define fixed width for inputs */
}

.vn-form h3 {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin: 0; /* Ensure no default margin interferes */
}

.form-header-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.status-tag {
  vertical-align: middle;
}

.form-details {
  margin-top: 24px;
}

.detail-table-section {
  margin-top: 16px;
}

.detail-table-section h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.form-actions {
  margin-top: 24px;
  text-align: right;
}

@media (max-width: 767px) {
  .form-actions {
    text-align: center;
    margin-top: 30px;
  }

  .form-actions :deep(.el-button) {
    display: block;
    width: 100%;
    margin-left: 0 !important;
    margin-bottom: 10px;
  }

  .form-actions :deep(.el-button:last-child) {
    margin-bottom: 0;
  }
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  color: #606266;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* Remove fixed width, let inputs fill their column */
.vn-form :deep(.el-form-item .el-input:not(.el-textarea)), 
.vn-form :deep(.el-form-item .el-select),
.vn-form :deep(.el-form-item .el-input-number),
.vn-form :deep(.el-form-item .el-date-picker) {
   width: 100%; /* Let the input fill the column width */
   /* Remove max-width or keep if necessary for edge cases */
   /* max-width: 100%; */
}

/* Ensure Textarea still takes full width of its container */
.vn-form :deep(.el-form-item .el-textarea) {
  width: 100%;
}

/* Ensure Radio/Checkbox groups size naturally */
.vn-form :deep(.el-form-item .el-radio-group),
.vn-form :deep(.el-form-item .el-checkbox-group) {
   width: auto;
}

.form-group-section {
  margin-bottom: 20px; /* Add some space between groups */
  /* Optional: Add border for visual separation */
  /* border: 1px solid #eee; */
  /* padding: 15px; */
  /* border-radius: 4px; */
}

.group-title {
  margin-bottom: 18px;
  font-size: 14px; /* <<< Reduce font size */
  font-weight: 600;
  color: #A8ABB2; /* Element Plus placeholder text color (lighter gray) */
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
  text-align: left;
}

/* NEW STYLES for disabled select text wrapper */
.disabled-select-text-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: var(--el-component-size, 32px); /* Adjust to match ElInput height */
  padding: 1px 11px;
  background-color: var(--el-disabled-bg-color, #f5f7fa);
  border: 1px solid var(--el-disabled-border-color, #e4e7ed);
  border-radius: var(--el-border-radius-base, 4px);
  box-shadow: 0 0 0 0 transparent;
  cursor: not-allowed;
  overflow: hidden; /* Prevent text from overflowing the border */
}

.disabled-select-text-wrapper .disabled-select-text {
  color: var(--el-text-color-placeholder, #a8abb2); /* Use placeholder color or disabled text color */
  font-size: inherit;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%; /* Ensure text uses available space for ellipsis to work */
}

/* Adjustments if using el-divider */
:deep(.el-divider--horizontal) {
  /* Optional: Adjust divider margin if needed */
  margin-top: 0; 
  margin-bottom: 24px;
}
:deep(.el-divider__text) {
  /* Optional: Style divider text if needed */
  font-weight: 600;
  color: #A8ABB2; /* Match h4 color or choose another */
}

.validation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000; /* Ensure it's above form elements, adjust if needed */
  /* Background is handled by element-loading-background */
}

/* --- VNForm Specific VNTable Style Overrides --- */

/* Make toolbar buttons smaller when VNTable is inside VNForm */
.vn-form :deep(.vn-table-container .table-toolbar .el-button) {
  width: 28px;  /* Smaller width */
  height: 28px; /* Smaller height */
  font-size: 14px; /* Slightly smaller icon size */
}

/* Ensure icons inside the smaller buttons remain centered (VNTable might already handle this) */
/* Just in case, we can re-apply centering styles */
.vn-form :deep(.vn-table-container .table-toolbar .el-button .el-icon) {
   font-size: inherit; /* Inherit the smaller font-size */
   /* display: flex; */ /* Likely inherited or handled by VNTable */
   /* justify-content: center; */
   /* align-items: center; */
   /* width: 100%; */
   /* height: 100%; */
}
</style> 