package dto

import (
	"backend/internal/model/entity"
	"backend/pkg/response"
	"time"
)

// WmsInventoryAdjustmentCreateReq 库存调整创建请求
type WmsInventoryAdjustmentCreateReq struct {
	InventoryID       uint                              `json:"inventoryId" binding:"required" comment:"库存ID"`
	AdjustmentType    entity.WmsInventoryAdjustmentType `json:"adjustmentType" binding:"required" comment:"调整类型"`
	QuantityChange    float64                           `json:"quantityChange" binding:"required" comment:"调整数量"`
	StatusAfter       *string                           `json:"statusAfter" comment:"调整后状态"`
	ReasonCode        *string                           `json:"reasonCode" comment:"原因代码"`
	ReasonDescription *string                           `json:"reasonDescription" comment:"原因描述"`
}

// WmsInventoryAdjustmentUpdateReq 库存调整更新请求
type WmsInventoryAdjustmentUpdateReq struct {
	QuantityChange    *float64 `json:"quantityChange" comment:"调整数量"`
	StatusAfter       *string  `json:"statusAfter" comment:"调整后状态"`
	ReasonCode        *string  `json:"reasonCode" comment:"原因代码"`
	ReasonDescription *string  `json:"reasonDescription" comment:"原因描述"`
}

// WmsInventoryAdjustmentQueryReq 库存调整查询请求
type WmsInventoryAdjustmentQueryReq struct {
	response.PageQuery
	AdjustmentNo   *string                              `json:"adjustmentNo" comment:"调整单号"`
	InventoryID    *uint                                `json:"inventoryId" comment:"库存ID"`
	ItemSku        *string                              `json:"itemSku" comment:"物料SKU"`
	AdjustmentType *entity.WmsInventoryAdjustmentType   `json:"adjustmentType" comment:"调整类型"`
	ApprovalStatus *entity.WmsInventoryAdjustmentStatus `json:"approvalStatus" comment:"审批状态"`
	OperatorID     *uint                                `json:"operatorId" comment:"操作员ID"`
	CreatedStart   *time.Time                           `json:"createdStart" comment:"创建开始时间"`
	CreatedEnd     *time.Time                           `json:"createdEnd" comment:"创建结束时间"`
}

// WmsInventoryAdjustmentApprovalReq 库存调整审批请求
type WmsInventoryAdjustmentApprovalReq struct {
	ApprovalStatus entity.WmsInventoryAdjustmentStatus `json:"approvalStatus" binding:"required" comment:"审批状态"`
	ApprovalRemark *string                             `json:"approvalRemark" comment:"审批备注"`
}

// WmsInventoryAdjustmentBatchCreateReq 批量库存调整创建请求
type WmsInventoryAdjustmentBatchCreateReq struct {
	Adjustments []WmsInventoryAdjustmentCreateReq `json:"adjustments" binding:"required,dive" comment:"调整列表"`
}

// WmsInventoryAdjustmentExecuteReq 库存调整执行请求
type WmsInventoryAdjustmentExecuteReq struct {
	AdjustmentIDs []uint  `json:"adjustmentIds" binding:"required" comment:"调整ID列表"`
	ExecuteRemark *string `json:"executeRemark" comment:"执行备注"`
}

// WmsInventoryAdjustmentStatsReq 库存调整统计请求
type WmsInventoryAdjustmentStatsReq struct {
	WarehouseID *uint      `json:"warehouseId" comment:"仓库ID"`
	ItemID      *uint      `json:"itemId" comment:"物料ID"`
	DateStart   *time.Time `json:"dateStart" comment:"开始日期"`
	DateEnd     *time.Time `json:"dateEnd" comment:"结束日期"`
	GroupBy     *string    `json:"groupBy" comment:"分组方式: day/week/month"`
}

// WmsInventoryAdjustmentReasonReq 调整原因查询请求
type WmsInventoryAdjustmentReasonReq struct {
	AdjustmentType *entity.WmsInventoryAdjustmentType `json:"adjustmentType" comment:"调整类型"`
	Active         *bool                              `json:"active" comment:"是否启用"`
}

// WmsInventoryAdjustmentImportReq 库存调整导入请求
type WmsInventoryAdjustmentImportReq struct {
	FileData []byte `json:"fileData" binding:"required" comment:"文件数据"`
	FileName string `json:"fileName" binding:"required" comment:"文件名"`
}

// WmsInventoryAdjustmentExportReq 库存调整导出请求
type WmsInventoryAdjustmentExportReq struct {
	WmsInventoryAdjustmentQueryReq
	ExportFields []string `json:"exportFields" comment:"导出字段"`
	ExportFormat string   `json:"exportFormat" comment:"导出格式: excel/csv"`
}

// GetDefaultExportFields 获取默认导出字段
func (req *WmsInventoryAdjustmentExportReq) GetDefaultExportFields() []string {
	if len(req.ExportFields) > 0 {
		return req.ExportFields
	}

	return []string{
		"adjustmentNo",
		"itemSku",
		"itemName",
		"adjustmentType",
		"quantityBefore",
		"quantityAfter",
		"quantityChange",
		"approvalStatus",
		"operatorName",
		"createdAt",
	}
}

// GetExportFormat 获取导出格式
func (req *WmsInventoryAdjustmentExportReq) GetExportFormat() string {
	if req.ExportFormat == "" {
		return "excel"
	}
	return req.ExportFormat
}

// WmsInventoryAdjustmentRejectReq 库存调整拒绝请求
type WmsInventoryAdjustmentRejectReq struct {
	ApprovedBy uint   `json:"approvedBy" binding:"required" comment:"审批人ID"`
	Reason     string `json:"reason" binding:"required" comment:"拒绝原因"`
}

// WmsInventoryAdjustmentBatchApproveReq 批量审批库存调整请求
type WmsInventoryAdjustmentBatchApproveReq struct {
	IDs        []uint `json:"ids" binding:"required" comment:"调整ID列表"`
	ApprovedBy uint   `json:"approvedBy" binding:"required" comment:"审批人ID"`
}
