package vo

import (
	"backend/internal/model/entity"

	"github.com/shopspring/decimal"
)

// FinTaxRateItemRes 单个税率响应
type FinTaxRateItemRes struct {
	BaseVO
	Name        string          `json:"name"`
	Rate        decimal.Decimal `json:"rate"`
	Description string          `json:"description"`
	IsEnabled   bool            `json:"isEnabled"`
}

func FromTaxRateEntity(e *entity.FinTaxRate) *FinTaxRateItemRes {
	if e == nil {
		return nil
	}
	return &FinTaxRateItemRes{
		BaseVO: BaseVO{
			ID:        e.ID,
			CreatedAt: e.CreatedAt,
			UpdatedAt: e.UpdatedAt,
			CreatedBy: e.CreatedBy,
			UpdatedBy: e.UpdatedBy,
		},
		Name:        e.Name,
		Rate:        e.Rate,
		Description: e.Description,
		IsEnabled:   e.IsEnabled,
	}
}

func FromTaxRateEntities(entities []*entity.FinTaxRate) []*FinTaxRateItemRes {
	res := make([]*FinTaxRateItemRes, len(entities))
	for i, e := range entities {
		res[i] = FromTaxRateEntity(e)
	}
	return res
}
