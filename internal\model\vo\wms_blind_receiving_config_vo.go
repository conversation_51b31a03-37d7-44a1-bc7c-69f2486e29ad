package vo

import (
	"time"
)

// WmsBlindReceivingConfigVO 盲收配置详细视图对象
type WmsBlindReceivingConfigVO struct {
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	CreatedBy     uint      `json:"createdBy"`
	UpdatedBy     uint      `json:"updatedBy"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	ConfigLevel          string   `json:"configLevel"`          // 配置级别: WAREHOUSE/CLIENT/USER
	ConfigTargetID       *uint    `json:"configTargetId"`       // 配置目标ID（仓库ID/客户ID/用户ID）
	ConfigTargetName     string   `json:"configTargetName"`     // 配置目标名称
	Strategy             string   `json:"strategy"`             // 盲收策略: STRICT/SUPPLEMENT/FULL
	SupplementTimeLimit  *int     `json:"supplementTimeLimit"`  // 补录时限（小时）
	RequiresApproval     bool     `json:"requiresApproval"`     // 是否需要审批
	ApprovalUserRoles    []string `json:"approvalUserRoles"`    // 审批用户角色 - 修复：改为数组类型
	MaxBlindReceivingQty *float64 `json:"maxBlindReceivingQty"` // 最大盲收数量
	IsActive             bool     `json:"isActive"`             // 是否激活
	Priority             int      `json:"priority"`             // 优先级

	// 新增扩展字段
	StrategyName     string  `json:"strategyName"`     // 策略显示名称
	CreatedByName    string  `json:"createdByName"`    // 创建者姓名
	UpdatedByName    string  `json:"updatedByName"`    // 更新者姓名
	UsageCount       int     `json:"usageCount"`       // 使用次数统计
	LastUsedAt       *string `json:"lastUsedAt"`       // 最后使用时间
	EffectiveRanking int     `json:"effectiveRanking"` // 有效配置排名
}

// WmsBlindReceivingConfigSimpleVO 盲收配置简单视图对象（用于列表显示）
type WmsBlindReceivingConfigSimpleVO struct {
	ID               uint      `json:"id"`
	ConfigLevel      string    `json:"configLevel"`
	ConfigTargetName string    `json:"configTargetName"`
	Strategy         string    `json:"strategy"`
	IsActive         bool      `json:"isActive"`
	Priority         int       `json:"priority"`
	CreatedAt        time.Time `json:"createdAt"`

	// 新增扩展字段
	StrategyName  string `json:"strategyName"`  // 策略显示名称
	CreatedByName string `json:"createdByName"` // 创建者姓名
}

// WmsBlindReceivingConfigPageResult 盲收配置分页结果
type WmsBlindReceivingConfigPageResult = PageResult[WmsBlindReceivingConfigSimpleVO]
