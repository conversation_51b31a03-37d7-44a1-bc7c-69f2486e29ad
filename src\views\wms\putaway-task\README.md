# 上架任务移动端功能

## 概述

上架任务移动端功能是基于拣货移动端功能模板开发的，为仓库操作员提供便捷的移动端上架操作界面。

## 功能特性

### 📱 移动端主页面 (`mobile.vue`)

**核心功能：**
- 任务信息展示（任务编号、收货记录号、上架策略、分配人员）
- 实时进度跟踪（完成进度条、统计信息）
- 上架明细列表（物料信息、建议库位、上架数量）
- 任务状态管理（开始、暂停、取消、完成）

**操作流程：**
1. 查看任务基本信息和进度
2. 点击明细项进行上架操作
3. 支持快速操作按钮（开始上架、完成上架、调整）
4. 完成所有明细后可完成整个任务

### 🔧 上架操作对话框 (`MobilePutawayDialog.vue`)

**功能特点：**
- 物料信息展示（编码、名称、建议库位、批次号）
- 数量信息统计（上架数量、已上架数量、剩余数量）
- 灵活的数量输入（手动输入、快捷按钮、全部按钮）
- 库位扫描功能（扫码或手动输入实际库位）
- 异常处理机制（库位已满、货物损坏、库位错误等）

**操作模式：**
- `start`: 开始上架
- `complete`: 完成上架  
- `adjust`: 调整上架

### 📷 扫码功能 (`MobileScanDialog.vue`)

**扫码能力：**
- 物料条码识别
- 库位条码识别
- 扫描历史记录
- 批量扫码上架
- 扫码统计分析

**智能匹配：**
- 自动识别条码类型
- 匹配任务中的物料
- 显示匹配结果和详情
- 支持快速上架操作

## 技术架构

### API层 (`/api/wms/putawayTask.ts`)

**新增移动端API：**
```typescript
// 任务控制
startPutawayTask(id: number)           // 开始任务
completePutawayTaskWithRemark(id, remark) // 完成任务
pausePutawayTask(id, remark)           // 暂停任务
cancelPutawayTask(id, reason)          // 取消任务

// 明细操作
updatePutawayDetail(taskId, detailId, data) // 更新明细
batchPutaway(taskId, putaways)         // 批量上架
```

**扩展的数据类型：**
```typescript
interface WmsPutawayTaskDetailResp {
  // 基础字段
  id: number
  itemSku: string
  itemName: string
  putawayQuantity: number
  
  // 移动端扩展字段
  completedQuantity?: number
  suggestedLocation?: string
  actualLocation?: string
  batchNo?: string
  barcode?: string
  status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
}
```

### Store层 (`/store/wms/putawayTask.ts`)

**移动端专用方法：**
```typescript
// 任务管理
fetchDetail(id)        // 获取详情
startTask(id)          // 开始任务
completeTask(id, remark) // 完成任务
pauseTask(id, remark)  // 暂停任务
cancelTask(id, reason) // 取消任务

// 明细操作
updateDetail(taskId, detailId, data) // 更新明细
batchPutawayItems(taskId, putaways)  // 批量上架
```

### 路由配置

**移动端路由：**
```typescript
{
  path: 'putaway-mobile/:id',
  name: 'WmsPutawayTaskMobile',
  component: () => import('@/views/wms/putaway-task/mobile.vue'),
  meta: { 
    title: '移动端上架', 
    requiresAuth: true, 
    permission: 'wms:inbound:putaway:mobile',
    hideInMenu: true 
  }
}
```

## 组件结构

```
putaway-task/
├── index.vue                    # 主列表页面
├── mobile.vue                   # 移动端主页面
├── components/
│   ├── MobilePutawayDialog.vue  # 上架操作对话框
│   ├── MobileScanDialog.vue     # 扫码对话框
│   └── ...                      # 其他组件
└── README.md                    # 说明文档
```

## 使用方式

### 1. 从主页面进入移动端

在上架任务列表页面，对于状态为"已分配"或"进行中"的任务，点击"移动端"按钮：

```vue
<el-button 
  type="info" 
  size="small" 
  @click="handleMobileEntry(row.id)"
  v-if="['ASSIGNED', 'IN_PROGRESS'].includes(row.status)"
>
  <el-icon><Cellphone /></el-icon>
  移动端
</el-button>
```

### 2. 移动端操作流程

1. **查看任务信息**：显示任务基本信息和进度
2. **选择明细项**：点击要上架的物料明细
3. **执行上架**：
   - 输入上架数量
   - 扫描或选择实际库位
   - 添加备注信息
   - 确认上架
4. **完成任务**：所有明细完成后，点击"完成任务"

### 3. 扫码上架

点击扫码按钮进入扫码模式：
- 扫描物料条码自动匹配
- 输入上架数量
- 快速完成上架操作

## 样式特点

### 移动端适配
- 响应式设计，适配各种屏幕尺寸
- 大按钮设计，便于触摸操作
- 清晰的视觉层次和状态指示

### 主题色彩
- 待上架：灰色边框 (`#909399`)
- 上架中：橙色边框 (`#e6a23c`)
- 已上架：绿色边框 (`#67c23a`)

### 交互体验
- 流畅的动画过渡
- 直观的状态反馈
- 便捷的快捷操作

## 权限控制

**所需权限：**
- `wms:inbound:putaway:view` - 查看上架任务
- `wms:inbound:putaway:mobile` - 使用移动端功能
- `wms:inbound:putaway:execute` - 执行上架操作

## 与拣货移动端的对比

| 功能 | 拣货移动端 | 上架移动端 |
|------|------------|------------|
| 主要操作 | 拣货 | 上架 |
| 核心流程 | 出库通知单 → 拣货任务 | 收货记录 → 上架任务 |
| 库位操作 | 从库位拣货 | 到库位上架 |
| 扫码重点 | 物料确认 | 库位确认 |
| 数量管理 | 拣货数量 | 上架数量 |
| 异常处理 | 缺货、损坏 | 库位满、位置错误 |

## 开发说明

### 基于模板开发
本功能完全基于拣货移动端功能模板开发，保持了：
- 相同的代码结构和命名规范
- 一致的用户体验和交互模式
- 统一的样式设计和组件架构
- 相似的API设计和数据流

### 定制化调整
针对上架业务特点进行了以下调整：
- 业务术语和字段名称
- 操作流程和状态管理
- 异常处理和错误提示
- 扫码逻辑和匹配规则

这种基于模板的开发方式确保了代码的一致性和可维护性，同时大大提高了开发效率。
