import request from '@/utils/request'

// ==================== 类型定义 ====================

// 分配策略枚举
export type WmsAllocationStrategy = 
  | 'FIFO'            // 先进先出
  | 'LIFO'            // 后进先出
  | 'BATCH_PRIORITY'  // 批次优先
  | 'EXPIRY_DATE_PRIORITY' // 过期日期优先
  | 'LOCATION_PRIORITY'    // 库位优先

// 分配状态枚举
export type WmsAllocationStatus = 
  | 'PENDING'         // 待分配
  | 'ALLOCATED'       // 已分配
  | 'PICKED'          // 已拣货
  | 'RELEASED'        // 已释放

// 库存分配响应类型（单个分配记录）
export interface WmsInventoryAllocationResp {
  id: number
  outboundDetailId: number
  inventoryId: number
  allocatedQty: number
  pickedQty?: number
  allocationStrategy: WmsAllocationStrategy
  status: WmsAllocationStatus
  allocatedAt: string
  pickedAt?: string
  releasedAt?: string
  remark?: string
  // 扩展字段
  itemCode?: string
  itemName?: string
  batchNo?: string
  locationCode?: string
  availableQty?: number
  productionDate?: string
  expiryDate?: string
}

// 库存分配汇总响应类型（用于列表页面）
export interface WmsInventoryAllocationSummaryResp {
  id: number
  notificationId: number
  notificationNo: string
  customerName?: string
  allocationStatus: 'PENDING' | 'PARTIAL' | 'ALLOCATED' | 'FAILED'
  allocationProgress: number
  strategy: WmsAllocationStrategy
  totalItems: number
  allocatedItems: number
  totalQty: number
  allocatedQty: number
  createdAt: string
  allocatedAt?: string
  remark?: string
}

// 库存分配创建请求类型
export interface WmsInventoryAllocationCreateReq {
  outboundDetailId: number
  inventoryId: number
  allocatedQty: number
  allocationStrategy: WmsAllocationStrategy
  remark?: string
}

// 库存分配更新请求类型
export interface WmsInventoryAllocationUpdateReq {
  allocatedQty?: number
  allocationStrategy?: WmsAllocationStrategy
  remark?: string
}

// 库存分配查询请求类型
export interface WmsInventoryAllocationQueryReq {
  pageNum?: number
  pageSize?: number
  outboundDetailId?: number
  inventoryId?: number
  itemId?: number
  itemCode?: string
  locationId?: number
  locationCode?: string
  batchNo?: string
  allocationStrategy?: WmsAllocationStrategy
  status?: WmsAllocationStatus
  allocatedAtStart?: string
  allocatedAtEnd?: string
}

// 自动分配请求类型
export interface WmsAutoAllocationReq {
  notificationId: number
  strategy: WmsAllocationStrategy
  forceReallocation?: boolean
  remark?: string
}

// 手动分配请求类型
export interface WmsManualAllocationReq {
  outboundDetailId: number
  allocations: WmsManualAllocationDetailReq[]
  remark?: string
}

// 手动分配明细请求类型
export interface WmsManualAllocationDetailReq {
  inventoryId: number
  allocatedQty: number
}

// 批量释放请求类型
export interface WmsBatchReleaseAllocationReq {
  notificationId?: number
  allocationIds?: number[]
  reason?: string
}

// 库存可用性响应类型
export interface WmsInventoryAvailabilityResp {
  itemId: number
  itemCode: string
  itemName: string
  totalAvailableQty: number
  allocatedQty: number
  reservedQty: number
  actualAvailableQty: number
  locations: WmsInventoryLocationAvailabilityResp[]
}

// 库存库位可用性响应类型
export interface WmsInventoryLocationAvailabilityResp {
  locationId: number
  locationCode: string
  availableQty: number
  batchNo?: string
  productionDate?: string
  expiryDate?: string
}

// 分配状态响应类型
export interface WmsAllocationStatusResp {
  notificationId: number
  notificationNo: string
  totalRequiredQty: number
  totalAllocatedQty: number
  totalUnallocatedQty: number
  allocationProgress: number
  details: WmsAllocationStatusDetailResp[]
}

// 分配状态明细响应类型
export interface WmsAllocationStatusDetailResp {
  outboundDetailId: number
  lineNo: number
  itemId: number
  itemCode: string
  itemName: string
  requiredQty: number
  allocatedQty: number
  unallocatedQty: number
  allocationProgress: number
  allocationStatus: 'PENDING' | 'PARTIAL' | 'FULL'
  allocations: WmsInventoryAllocationResp[]
}

// 分页响应类型
export interface WmsInventoryAllocationPageResp {
  list: WmsInventoryAllocationSummaryResp[]
  total: number
}

// ==================== API接口 ====================

// 库存分配基础CRUD接口
export const getAllocationPage = (params: WmsInventoryAllocationQueryReq): Promise<WmsInventoryAllocationPageResp> => {
  return request<WmsInventoryAllocationPageResp>({
    url: '/wms/inventory-allocations',
    method: 'get',
    params
  }) as unknown as Promise<WmsInventoryAllocationPageResp>
}

export const getAllocationDetail = (id: number): Promise<WmsInventoryAllocationResp> => {
  return request<WmsInventoryAllocationResp>({
    url: `/wms/inventory-allocations/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsInventoryAllocationResp>
}

export const getInventoryAllocationById = (id: number): Promise<WmsInventoryAllocationResp> => {
  return request<WmsInventoryAllocationResp>({
    url: `/wms/inventory-allocations/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsInventoryAllocationResp>
}

export const createInventoryAllocation = (data: WmsInventoryAllocationCreateReq): Promise<WmsInventoryAllocationResp> => {
  return request<WmsInventoryAllocationResp>({
    url: '/wms/inventory-allocations',
    method: 'post',
    data
  }) as unknown as Promise<WmsInventoryAllocationResp>
}

export const updateInventoryAllocation = (id: number, data: WmsInventoryAllocationUpdateReq): Promise<WmsInventoryAllocationResp> => {
  return request<WmsInventoryAllocationResp>({
    url: `/wms/inventory-allocations/${id}`,
    method: 'put',
    data
  }) as unknown as Promise<WmsInventoryAllocationResp>
}

export const deleteInventoryAllocation = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/${id}`,
    method: 'delete'
  }) as unknown as Promise<void>
}

// 自动分配接口
export const autoAllocate = (data: WmsAutoAllocationReq): Promise<WmsAllocationStatusResp> => {
  return request<WmsAllocationStatusResp>({
    url: '/wms/inventory-allocations/auto-allocate',
    method: 'post',
    data
  }) as unknown as Promise<WmsAllocationStatusResp>
}

// 手动分配接口
export const manualAllocate = (data: WmsManualAllocationReq): Promise<WmsInventoryAllocationResp[]> => {
  return request<WmsInventoryAllocationResp[]>({
    url: '/wms/inventory-allocations/manual-allocate',
    method: 'post',
    data
  }) as unknown as Promise<WmsInventoryAllocationResp[]>
}

// 释放分配接口
export const releaseAllocation = (id: number, data: { reason?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/${id}/release`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const batchReleaseAllocation = (data: WmsBatchReleaseAllocationReq): Promise<void> => {
  return request<void>({
    url: '/wms/inventory-allocations/batch-release',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 库存可用性检查接口
export const checkInventoryAvailability = (itemId: number, warehouseId?: number): Promise<WmsInventoryAvailabilityResp> => {
  return request<WmsInventoryAvailabilityResp>({
    url: `/wms/inventory-allocations/availability/${itemId}`,
    method: 'get',
    params: { warehouseId }
  }) as unknown as Promise<WmsInventoryAvailabilityResp>
}

export const batchCheckInventoryAvailability = (itemIds: number[], warehouseId?: number): Promise<WmsInventoryAvailabilityResp[]> => {
  return request<WmsInventoryAvailabilityResp[]>({
    url: '/wms/inventory-allocations/batch-availability',
    method: 'post',
    data: { itemIds, warehouseId }
  }) as unknown as Promise<WmsInventoryAvailabilityResp[]>
}

// 新增缺失的业务接口
export const executeAllocation = (id: number, data: { strategy?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/${id}/execute`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const batchAllocate = (ids: number[], data: { strategy: string }): Promise<void> => {
  return request<void>({
    url: '/wms/inventory-allocations/batch-allocate',
    method: 'post',
    data: { ids, ...data }
  }) as unknown as Promise<void>
}

export const batchRelease = (ids: number[]): Promise<void> => {
  return request<void>({
    url: '/wms/inventory-allocations/batch-release',
    method: 'post',
    data: { ids }
  }) as unknown as Promise<void>
}

export const retryAllocation = (id: number, data: { strategy?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/${id}/retry`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const getAvailableInventory = (itemId: number, warehouseId?: number): Promise<any[]> => {
  return request<any[]>({
    url: `/wms/inventory-allocations/available-inventory/${itemId}`,
    method: 'get',
    params: { warehouseId }
  }) as unknown as Promise<any[]>
}

export const checkInventoryAvailability = (items: any[]): Promise<any> => {
  return request<any>({
    url: '/wms/inventory-allocations/check-availability',
    method: 'post',
    data: { items }
  }) as unknown as Promise<any>
}

export const reserveInventory = (allocationId: number, details: any[]): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/${allocationId}/reserve`,
    method: 'post',
    data: { details }
  }) as unknown as Promise<void>
}

export const releaseReservation = (allocationId: number): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/${allocationId}/release-reservation`,
    method: 'post'
  }) as unknown as Promise<void>
}

export const getAllocationSuggestion = (notificationId: number, strategy: string): Promise<any> => {
  return request<any>({
    url: `/wms/inventory-allocations/suggestion/${notificationId}`,
    method: 'get',
    params: { strategy }
  }) as unknown as Promise<any>
}

export const manualAllocate = (allocationId: number, details: any[]): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/${allocationId}/manual-allocate`,
    method: 'post',
    data: { details }
  }) as unknown as Promise<void>
}

export const getAllocationHistory = (allocationId: number): Promise<any[]> => {
  return request<any[]>({
    url: `/wms/inventory-allocations/${allocationId}/history`,
    method: 'get'
  }) as unknown as Promise<any[]>
}

export const exportAllocationResult = (ids: number[]): Promise<void> => {
  return request<void>({
    url: '/wms/inventory-allocations/export',
    method: 'post',
    data: { ids },
    responseType: 'blob'
  }) as unknown as Promise<void>
}

export const getAllocationStats = (params?: any): Promise<any> => {
  return request<any>({
    url: '/wms/inventory-allocations/stats',
    method: 'get',
    params
  }) as unknown as Promise<any>
}

export const optimizeAllocation = (allocationId: number, options?: any): Promise<any> => {
  return request<any>({
    url: `/wms/inventory-allocations/${allocationId}/optimize`,
    method: 'post',
    data: options
  }) as unknown as Promise<any>
}

// 分配状态查询接口
export const getAllocationStatus = (notificationId: number): Promise<WmsAllocationStatusResp> => {
  return request<WmsAllocationStatusResp>({
    url: `/wms/inventory-allocations/status/${notificationId}`,
    method: 'get'
  }) as unknown as Promise<WmsAllocationStatusResp>
}

// 分配优化接口
export const optimizeAllocation = (notificationId: number, strategy?: WmsAllocationStrategy): Promise<WmsAllocationStatusResp> => {
  return request<WmsAllocationStatusResp>({
    url: `/wms/inventory-allocations/optimize/${notificationId}`,
    method: 'post',
    data: { strategy }
  }) as unknown as Promise<WmsAllocationStatusResp>
}

// 预占管理接口
export const reserveInventory = (data: { itemId: number; qty: number; warehouseId?: number; remark?: string }): Promise<{ reservationId: number }> => {
  return request<{ reservationId: number }>({
    url: '/wms/inventory-allocations/reserve',
    method: 'post',
    data
  }) as unknown as Promise<{ reservationId: number }>
}

export const releaseReservation = (reservationId: number, data: { reason?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/inventory-allocations/release-reservation/${reservationId}`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 导出接口
export const exportInventoryAllocations = (params: WmsInventoryAllocationQueryReq): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/inventory-allocations/export',
    method: 'get',
    params,
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}
