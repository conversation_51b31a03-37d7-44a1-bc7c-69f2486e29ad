package repository

import (
	"context"
	stdErrors "errors" // 统一标准 errors 别名
	"reflect"          // 引入 reflect 包
	"regexp"           // 引入 regexp 包
	"strings"
	"sync" // 引入 sync 包

	"backend/pkg/constant" // 添加database包导入
	"backend/pkg/database"
	apperrors "backend/pkg/errors" // 统一自定义 errors 别名
	"backend/pkg/logger"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// BaseRepository 基础仓库接口
// 泛型参数:
//   - T: 实体类型
//   - ID: 主键类型
type BaseRepository[T any, ID any] interface {
	// Create 创建实体
	// 参数:
	//   - ctx: 上下文
	//   - entity: 待创建的实体指针
	// 返回:
	//   - error: 错误信息
	Create(ctx context.Context, entity *T) error

	// Delete 根据ID删除实体
	// 参数:
	//   - ctx: 上下文
	//   - id: 实体ID
	// 返回:
	//   - error: 错误信息
	Delete(ctx context.Context, id ID) error

	// Update 更新实体
	// 参数:
	//   - ctx: 上下文
	//   - entity: 待更新的实体指针
	// 返回:
	//   - error: 错误信息
	Update(ctx context.Context, entity *T) error

	// FindByID 根据ID查询实体
	// 参数:
	//   - ctx: 上下文
	//   - id: 实体ID
	// 返回:
	//   - *T: 实体指针
	//   - error: 错误信息
	FindByID(ctx context.Context, id ID) (*T, error)

	// FindByIds 根据ID列表查询实体
	// 参数:
	//   - ctx: 上下文
	//   - ids: ID列表
	// 返回:
	//   - []*T: 实体列表
	//   - error: 错误信息
	FindByIds(ctx context.Context, ids []ID) ([]*T, error)

	// FindByPage 分页查询
	// 参数:
	//   - ctx: 上下文
	//   - pageQuery: 分页和排序参数
	//   - conditions: 查询条件列表 (实现 QueryCondition 接口)
	// 返回:
	//   - *response.PageResult: 分页结果
	//   - error: 错误信息
	FindByPage(ctx context.Context, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error)

	// FindByCondition 根据条件查询 (返回列表)
	// 参数:
	//   - ctx: 上下文
	//   - conditions: 查询条件列表 (实现 QueryCondition 接口)
	//   - sortInfos: 排序规则列表
	// 返回:
	//   - []*T: 实体列表
	//   - error: 错误信息
	FindByCondition(ctx context.Context, conditions []QueryCondition, sortInfos []response.SortInfo) ([]*T, error)

	// FindOneByCondition 根据条件查询单个实体
	// 参数:
	//   - ctx: 上下文
	//   - conditions: 查询条件列表 (实现 QueryCondition 接口)
	// 返回:
	//   - *T: 实体指针 (如果找到)
	//   - error: 错误信息 (包括未找到记录的错误)
	FindOneByCondition(ctx context.Context, conditions []QueryCondition) (*T, error)

	// Exists 根据条件判断是否存在
	// 参数:
	//   - ctx: 上下文
	//   - conditions: 查询条件列表 (实现 QueryCondition 接口)
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	Exists(ctx context.Context, conditions []QueryCondition) (bool, error)

	// GetDB 获取数据库连接
	// 参数:
	//   - ctx: 上下文
	// 返回:
	//   - *gorm.DB: 数据库连接
	GetDB(ctx context.Context) *gorm.DB
}

// BaseRepositoryImpl 基础仓库实现
type BaseRepositoryImpl[T any, ID any] struct {
	db *gorm.DB
}

// NewBaseRepository 创建基础Repository
// 参数:
//   - db: 数据库连接
//
// 返回:
//   - BaseRepository[T, ID]: 基础仓库接口
func NewBaseRepository[T any, ID any](db *gorm.DB) BaseRepository[T, ID] {
	return &BaseRepositoryImpl[T, ID]{
		db: db,
	}
}

// Create 创建实体
func (r *BaseRepositoryImpl[T, ID]) Create(ctx context.Context, entity *T) error {
	result := r.GetDB(ctx).Create(entity)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("创建实体失败")
		// 确保使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "创建数据失败").WithCause(result.Error)
	}
	return nil
}

// Delete 根据ID删除实体
func (r *BaseRepositoryImpl[T, ID]) Delete(ctx context.Context, id ID) error {
	result := r.GetDB(ctx).Delete(new(T), id)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("删除实体失败")
		// 确保使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除数据失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		// 确保使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "数据不存在")
	}

	return nil
}

// Update 更新实体
func (r *BaseRepositoryImpl[T, ID]) Update(ctx context.Context, entity *T) error {
	result := r.GetDB(ctx).Save(entity)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("更新实体失败")
		// 确保使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新数据失败").WithCause(result.Error)
	}
	return nil
}

// FindByID 根据ID查询实体
func (r *BaseRepositoryImpl[T, ID]) FindByID(ctx context.Context, id ID) (*T, error) {
	var entity T
	result := r.GetDB(ctx).First(&entity, id)
	if result.Error != nil {
		// 确保使用 stdErrors.Is 和 apperrors
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "数据不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("查询实体失败")
		// 确保使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询数据失败").WithCause(result.Error)
	}
	return &entity, nil
}

// FindByIds 根据ID列表查询实体
func (r *BaseRepositoryImpl[T, ID]) FindByIds(ctx context.Context, ids []ID) ([]*T, error) {
	if len(ids) == 0 {
		return []*T{}, nil
	}

	var entities []*T
	result := r.GetDB(ctx).Find(&entities, ids)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("根据ID列表查询实体失败")
		// 确保使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询数据失败").WithCause(result.Error)
	}
	return entities, nil
}

// --- 缓存和反射辅助函数 ---

var sortableColumnsCache sync.Map // 缓存: reflect.Type -> map[string]string (API Field Name -> DB Column Name)

// toSnakeCase 将驼峰式字符串转换为 snake_case
func toSnakeCase(str string) string {
	matchFirstCap := regexp.MustCompile("(.)([A-Z][a-z]+)")
	matchAllCap := regexp.MustCompile("([a-z0-9])([A-Z])")
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

// getSortableColumns 通过反射获取实体允许排序的列
// 返回 map[API字段名]数据库列名
func getSortableColumns(entityType reflect.Type) map[string]string {
	if cached, ok := sortableColumnsCache.Load(entityType); ok {
		return cached.(map[string]string)
	}

	allowed := make(map[string]string)
	if entityType.Kind() == reflect.Ptr {
		entityType = entityType.Elem() // 获取指针指向的类型
	}

	for i := 0; i < entityType.NumField(); i++ {
		field := entityType.Field(i)

		// 递归处理嵌入的结构体 (例如 BaseEntity)
		if field.Anonymous && field.Type.Kind() == reflect.Struct {
			embeddedAllowed := getSortableColumns(field.Type)
			for k, v := range embeddedAllowed {
				if _, exists := allowed[k]; !exists { // 避免覆盖外层同名 json tag
					allowed[k] = v
				}
			}
			continue // 继续下一个字段
		}

		sortableTag := field.Tag.Get("sortable")
		if sortableTag == "true" {
			gormTag := field.Tag.Get("gorm")
			jsonTag := field.Tag.Get("json")

			// 提取数据库列名
			columnName := ""
			if gormTag != "" {
				parts := strings.Split(gormTag, ";")
				for _, part := range parts {
					if strings.HasPrefix(part, "column:") {
						columnName = strings.TrimPrefix(part, "column:")
						break
					}
				}
			}
			if columnName == "" || columnName == "-" { // 如果 gorm tag 没有 column 或为 -
				columnName = toSnakeCase(field.Name) // 使用字段名的 snake_case
			}

			// 提取 API 字段名 (json tag)
			apiFieldName := ""
			if jsonTag != "" {
				parts := strings.Split(jsonTag, ",")
				if len(parts) > 0 && parts[0] != "-" {
					apiFieldName = parts[0]
				}
			}
			// 如果没有 json tag 或 json tag 为 "-", 则此字段不允许通过 API 排序
			// （或者可以考虑用 Go 字段名作为 fallback，但用 json tag 更明确）

			if apiFieldName != "" && columnName != "" {
				allowed[apiFieldName] = columnName
			}
		}
	}
	sortableColumnsCache.Store(entityType, allowed)
	logger.Debugf("Cached sortable columns for type %s: %v", entityType.Name(), allowed) // 添加日志方便调试
	return allowed
}

// ApplyConditions 将查询条件应用到 GORM 查询构建器上 (原 applyConditions)
func ApplyConditions(query *gorm.DB, conditions []QueryCondition) *gorm.DB {
	for _, condition := range conditions {
		if condition != nil { // 确保 condition 不是 nil
			query = condition.Apply(query)
		}
	}
	return query
}

// applySortInfos 将排序规则应用到 GORM 查询 (修改后)
// 使用白名单 allowedColumns (map[API字段名]数据库列名) 验证排序字段
func applySortInfos(db *gorm.DB, sortInfos []response.SortInfo, allowedColumns map[string]string) *gorm.DB {
	if len(sortInfos) == 0 {
		return db // 没有请求排序，直接返回
	}

	var orderByClauses []string
	processedFields := make(map[string]bool) // 防止重复处理同一API字段

	for _, s := range sortInfos {
		apiFieldName := s.Field
		if apiFieldName == "" {
			continue // 跳过空字段
		}

		if _, processed := processedFields[apiFieldName]; processed {
			continue // 跳过已处理的字段
		}

		if dbColumnName, ok := allowedColumns[apiFieldName]; ok {
			order := strings.ToLower(s.Order)
			// 确保排序方向有效
			if order != constant.SORT_ASC && order != constant.SORT_DESC {
				order = constant.DEFAULT_SORT_ORDER // 无效则使用默认 (来自 constant)
			}
			orderByClauses = append(orderByClauses, dbColumnName+" "+order) // 使用验证后的数据库列名
			processedFields[apiFieldName] = true
		} else {
			// 如果请求的排序字段不在白名单中，记录警告日志并忽略该字段
			logger.Warnf("Invalid or non-sortable field requested for sorting: %s", apiFieldName)
			processedFields[apiFieldName] = true // 标记为已处理，避免重复警告
		}
	}

	if len(orderByClauses) > 0 {
		return db.Order(strings.Join(orderByClauses, ", "))
	}

	// 如果没有有效的排序字段（或请求的字段都不在白名单中），则不应用 Order 子句
	return db
}

// FindByPage 分页查询 (修改后)
func (r *BaseRepositoryImpl[T, ID]) FindByPage(ctx context.Context, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error) {
	page := pageQuery.PageNum
	size := pageQuery.PageSize
	if page < 1 {
		page = constant.DEFAULT_PAGE_NUM
	}
	if size < 1 {
		size = constant.DEFAULT_PAGE_SIZE
	}
	if size > constant.MAX_PAGE_SIZE {
		size = constant.MAX_PAGE_SIZE
	}

	var total int64
	var list []*T
	entityType := reflect.TypeOf(new(T)).Elem()          // 获取实体类型用于反射
	allowedSortColumns := getSortableColumns(entityType) // 获取允许排序的列

	queryBase := r.GetDB(ctx).Model(new(T))
	queryCount := ApplyConditions(queryBase, conditions)

	if err := queryCount.Count(&total).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("分页查询总数失败")
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询总数失败")
	}

	if total == 0 {
		return &response.PageResult{Total: 0, List: []*T{}, PageNum: page, PageSize: size}, nil
	}

	offset := (page - 1) * size
	// queryData := ApplyConditions(r.GetDB(ctx).Model(new(T)), conditions)
	// 修改：直接在 queryCount 的基础上继续构建查询，而不是重新开始
	queryData := queryCount
	// 将允许排序的列传递给 applySortInfos
	queryData = applySortInfos(queryData, pageQuery.Sort, allowedSortColumns)

	if err := queryData.Offset(offset).Limit(size).Find(&list).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("分页查询列表失败")
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询列表失败")
	}

	return &response.PageResult{Total: total, List: list, PageNum: page, PageSize: size}, nil
}

// FindByCondition 根据条件查询 (返回列表) (修改后)
func (r *BaseRepositoryImpl[T, ID]) FindByCondition(ctx context.Context, conditions []QueryCondition, sortInfos []response.SortInfo) ([]*T, error) {
	var entities []*T
	entityType := reflect.TypeOf(new(T)).Elem()          // 获取实体类型
	allowedSortColumns := getSortableColumns(entityType) // 获取允许排序的列

	db := r.GetDB(ctx).Model(new(T))
	db = ApplyConditions(db, conditions)
	// 将允许排序的列传递给 applySortInfos
	db = applySortInfos(db, sortInfos, allowedSortColumns)

	result := db.Find(&entities)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("根据条件查询列表失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询数据失败").WithCause(result.Error)
	}
	return entities, nil
}

// FindOneByCondition 根据条件查询单个实体
func (r *BaseRepositoryImpl[T, ID]) FindOneByCondition(ctx context.Context, conditions []QueryCondition) (*T, error) {
	var entity T
	db := r.GetDB(ctx).Model(new(T))
	db = ApplyConditions(db, conditions)

	result := db.First(&entity)
	if result.Error != nil {
		// 确保使用 stdErrors.Is 和 apperrors
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // "未找到"在这里不应被视为一个错误
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据条件查询单个实体失败")
		// 确保使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询数据失败").WithCause(result.Error)
	}
	return &entity, nil
}

// Exists 根据条件判断是否存在
func (r *BaseRepositoryImpl[T, ID]) Exists(ctx context.Context, conditions []QueryCondition) (bool, error) {
	var count int64
	db := r.GetDB(ctx).Model(new(T))
	db = ApplyConditions(db, conditions)

	result := db.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查实体是否存在失败")
		// 确保使用 apperrors
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查数据是否存在失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// GetDB 获取数据库连接
func (r *BaseRepositoryImpl[T, ID]) GetDB(ctx context.Context) *gorm.DB {
	db := r.db.WithContext(ctx)

	// 检查实体是否包含 AccountBookID 字段，如果包含则自动应用账套scope
	var entity T
	entityType := reflect.TypeOf(entity)
	if entityType.Kind() == reflect.Ptr {
		entityType = entityType.Elem()
	}

	// 检查是否有 AccountBookID 字段 (来自 AccountBookEntity)
	if _, ok := entityType.FieldByName("AccountBookID"); ok {
		// 实体包含 AccountBookID 字段，尝试应用账套scope
		scopedDB, err := database.ApplyAccountBookScope(ctx, db)
		if err != nil {
			// 如果无法应用账套scope，记录警告但继续执行
			// 这可能发生在某些系统级操作中
			logger.WithContext(ctx).WithError(err).Warn("无法应用账套scope，将继续执行无账套过滤的查询")
			return db
		}
		return scopedDB
	}

	return db
}
