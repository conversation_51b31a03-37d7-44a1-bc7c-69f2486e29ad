package vo

import (
	"backend/pkg/util"
)

// EmployeeVO 员工视图对象 (用于列表或详情)
// 可根据需要调整暴露的字段，或添加转换后的字段 (如枚举转文字)
type EmployeeVO struct {
	TenantVO
	EmployeeCode                 string         `json:"employeeCode"`
	EmployeeName                 string         `json:"employeeName"`
	EmployeeNamePinyin           string         `json:"employeeNamePinyin,omitempty"`
	EmployeeNameEn               string         `json:"employeeNameEn,omitempty"`
	EmployeeIDCard               string         `json:"employeeIDCard"`
	EmployeeMobile               string         `json:"employeeMobile"`
	EmployeeEmail                string         `json:"employeeEmail,omitempty"`
	EmployeeGender               int            `json:"employeeGender"`
	EmployeeBirthday             *util.JsonDate `json:"employeeBirthday,omitempty"`
	EmployeeAvatar               string         `json:"employeeAvatar,omitempty"`
	EmployeeAddress              string         `json:"employeeAddress,omitempty"`
	EmployeeHouseholdAddress     string         `json:"employeeHouseholdAddress,omitempty"`
	EmployeeCurrentAddress       string         `json:"employeeCurrentAddress,omitempty"`
	EmployeePostCode             string         `json:"employeePostCode,omitempty"`
	EmployeeInDate               util.JsonDate  `json:"employeeInDate"`
	EmployeeOutDate              *util.JsonDate `json:"employeeOutDate,omitempty"`
	EmployeeFirstWorkDate        *util.JsonDate `json:"employeeFirstWorkDate,omitempty"`
	EmployeeStatus               int            `json:"employeeStatus"`
	EmployeeRemark               string         `json:"employeeRemark,omitempty"`
	EmployeeEducation            int            `json:"employeeEducation,omitempty"`
	EmployeeDegree               string         `json:"employeeDegree,omitempty"`
	EmployeeMajor                string         `json:"employeeMajor,omitempty"`
	EmployeeSchool               string         `json:"employeeSchool,omitempty"`
	EmployeeGraduationDate       *util.JsonDate `json:"employeeGraduationDate,omitempty"`
	EmployeeMaritalStatus        int            `json:"employeeMaritalStatus,omitempty"`
	EmployeeNationality          string         `json:"employeeNationality,omitempty"`
	EmployeeNation               string         `json:"employeeNation,omitempty"`
	EmployeeNativePlace          string         `json:"employeeNativePlace,omitempty"`
	EmployeePoliticalStatus      int            `json:"employeePoliticalStatus,omitempty"`
	EmployeeIDCardType           int            `json:"employeeIDCardType,omitempty"`
	EmployeeIDCardValidityPeriod string         `json:"employeeIDCardValidityPeriod,omitempty"`
	EmployeeDepartmentID         uint           `json:"employeeDepartmentId"`
	EmployeePositionID           uint           `json:"employeePositionId"`
	// 可以关联查询部门和职位名称并在此处添加
	// DepartmentName string `json:"departmentName,omitempty"`
	// PositionName   string `json:"positionName,omitempty"`
	EmployeeJobTitle                     string         `json:"employeeJobTitle,omitempty"`
	EmployeeWorkType                     int            `json:"employeeWorkType,omitempty"`
	EmployeeWorkLocation                 string         `json:"employeeWorkLocation,omitempty"`
	EmployeeEmergencyContactName         string         `json:"employeeEmergencyContactName,omitempty"`
	EmployeeEmergencyContactMobile       string         `json:"employeeEmergencyContactMobile,omitempty"`
	EmployeeEmergencyContactRelationship string         `json:"employeeEmergencyContactRelationship,omitempty"`
	EmployeeBankName                     string         `json:"employeeBankName,omitempty"`
	EmployeeBankAccount                  string         `json:"employeeBankAccount,omitempty"`
	EmployeeSocialSecurityNumber         string         `json:"employeeSocialSecurityNumber,omitempty"`
	EmployeeHousingFundNumber            string         `json:"employeeHousingFundNumber,omitempty"`
	EmployeeProbationEndDate             *util.JsonDate `json:"employeeProbationEndDate,omitempty"`
	EmployeeContractStartDate            *util.JsonDate `json:"employeeContractStartDate,omitempty"`
	EmployeeContractEndDate              *util.JsonDate `json:"employeeContractEndDate,omitempty"`
	EmployeeSalary                       *float64       `json:"employeeSalary,omitempty"`
	EmployeeWorkYears                    *int           `json:"employeeWorkYears,omitempty"`
	EmployeeSkills                       string         `json:"employeeSkills,omitempty"`
	EmployeeCertificates                 string         `json:"employeeCertificates,omitempty"`
	EmployeeHealthStatus                 int            `json:"employeeHealthStatus,omitempty"`
	EmployeeBloodType                    string         `json:"employeeBloodType,omitempty"`
	EmployeeHeight                       *float64       `json:"employeeHeight,omitempty"`
	EmployeeWeight                       *float64       `json:"employeeWeight,omitempty"`
	EmployeeQQ                           string         `json:"employeeQQ,omitempty"`
	EmployeeEntrySource                  string         `json:"employeeEntrySource,omitempty"`
	EmployeeReferralName                 string         `json:"employeeReferralName,omitempty"`
	EmployeeHobby                        string         `json:"employeeHobby,omitempty"`
	EmployeeJobGradeValue                string         `json:"employeeJobGradeValue,omitempty"`
	EmployeeJobSubLevelValue             string         `json:"employeeJobSubLevelValue,omitempty"`
	EmployeeWorkCategoryValue            string         `json:"employeeWorkCategoryValue,omitempty"`

	// Enhanced display fields
	EmployeeGenderText                       string `json:"employeeGenderText,omitempty"`                       // 性别文字描述
	EmployeeStatusText                       string `json:"employeeStatusText,omitempty"`                       // 状态文字描述
	EmployeeEducationText                    string `json:"employeeEducationText,omitempty"`                    // 学历文字描述
	EmployeeMaritalStatusText                string `json:"employeeMaritalStatusText,omitempty"`                // 婚姻状况文字描述
	EmployeePoliticalStatusText              string `json:"employeePoliticalStatusText,omitempty"`              // 政治面貌文字描述
	EmployeeIDCardTypeText                   string `json:"employeeIDCardTypeText,omitempty"`                   // 证件类型文字描述
	EmployeeWorkTypeText                     string `json:"employeeWorkTypeText,omitempty"`                     // 工作类型文字描述
	EmployeeHealthStatusText                 string `json:"employeeHealthStatusText,omitempty"`                 // 健康状况文字描述
	EmployeeDegreeText                       string `json:"employeeDegreeText,omitempty"`                       // 学位文字描述
	EmployeeJobTitleText                     string `json:"employeeJobTitleText,omitempty"`                     // 职称文字描述
	EmployeeNationText                       string `json:"employeeNationText,omitempty"`                       // 新增：民族文字描述
	EmployeeEntrySourceText                  string `json:"employeeEntrySourceText,omitempty"`                  // 入职来源文字描述
	EmployeeEmergencyContactRelationshipText string `json:"employeeEmergencyContactRelationshipText,omitempty"` // 与本人关系文字描述
	EmployeeJobGradeValueText                string `json:"employeeJobGradeValueText,omitempty"`                // 员工职级文字描述
	EmployeeJobSubLevelValueText             string `json:"employeeJobSubLevelValueText,omitempty"`             // 员工职等文字描述
	EmployeeWorkCategoryValueText            string `json:"employeeWorkCategoryValueText,omitempty"`            // 员工性质文字描述

	// Associated information (to be filled by Service layer)
	EmployeeDepartmentName string `json:"employeeDepartmentName,omitempty"` // 部门名称
	EmployeePositionName   string `json:"employeePositionName,omitempty"`   // 职位名称
}

// EmployeeSimpleVO 员工简单视图对象 (用于下拉列表等)
type EmployeeSimpleVO struct {
	ID           uint   `json:"id"`
	EmployeeCode string `json:"employeeCode"`
	EmployeeName string `json:"employeeName"`
}
