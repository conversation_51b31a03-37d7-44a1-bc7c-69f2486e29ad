import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  WmsOutboundNotificationResp, 
  WmsOutboundNotificationQueryReq,
  WmsOutboundNotificationStatus,
  WmsOutboundNotificationCreateReq,
  WmsOutboundNotificationUpdateReq,
  WmsOutboundNotificationPageResp
} from '@/api/wms/outboundNotification'
import type { OutboundNotificationFormData } from '@/types/wms/outboundNotification'
import * as outboundNotificationApi from '@/api/wms/outboundNotification'

export const useOutboundNotificationStore = defineStore('outboundNotification', () => {
  // ==================== 状态数据 ====================
  
  // 列表数据
  const list = ref<WmsOutboundNotificationResp[]>([])
  const total = ref(0)
  const loading = ref(false)
  
  // 当前记录
  const currentRecord = ref<WmsOutboundNotificationResp | null>(null)
  
  // 分页信息
  const pagination = ref({
    pageNum: 1,
    pageSize: 10
  })
  
  // 搜索表单
  const searchForm = ref<WmsOutboundNotificationQueryReq>({
    pageNum: 1,
    pageSize: 10
  })
  
  // 表单数据
  const formData = ref<OutboundNotificationFormData>({
    clientId: null,
    warehouseId: null,
    priority: 3,
    details: [],
    _mode: 'create'
  })
  
  // 缓存数据
  const cache = ref<Map<number, WmsOutboundNotificationResp>>(new Map())
  
  // ==================== 计算属性 ====================
  
  // 是否有数据
  const hasData = computed(() => list.value.length > 0)
  
  // 是否为空
  const isEmpty = computed(() => !loading.value && list.value.length === 0)
  
  // 状态统计
  const statusStats = computed(() => {
    const stats = {
      DRAFT: 0,
      PLANNED: 0,
      APPROVED: 0,
      ALLOCATING: 0,
      ALLOCATED: 0,
      PICKING: 0,
      PICKED: 0,
      SHIPPED: 0,
      COMPLETED: 0,
      CANCELLED: 0
    }
    list.value.forEach(item => {
      stats[item.status]++
    })
    return stats
  })
  
  // 优先级统计
  const priorityStats = computed(() => {
    const stats = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    list.value.forEach(item => {
      stats[item.priority]++
    })
    return stats
  })
  
  // 当前表单是否可编辑
  const canEdit = computed(() => {
    if (!currentRecord.value) return true
    return ['DRAFT', 'PLANNED'].includes(currentRecord.value.status)
  })
  
  // 当前表单是否可删除
  const canDelete = computed(() => {
    if (!currentRecord.value) return false
    return ['DRAFT', 'PLANNED'].includes(currentRecord.value.status)
  })
  
  // ==================== 异步操作 ====================
  
  // 获取列表
  const fetchList = async (params?: WmsOutboundNotificationQueryReq) => {
    loading.value = true
    try {
      const queryParams = {
        ...searchForm.value,
        ...params
      }
      
      // 过滤掉空值
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === '' || queryParams[key] === null || queryParams[key] === undefined) {
          delete queryParams[key]
        }
      })
      
      const result = await outboundNotificationApi.getOutboundNotificationPage(queryParams)
      list.value = result.list
      total.value = result.total
      
      // 更新分页信息
      pagination.value.pageNum = queryParams.pageNum || 1
      pagination.value.pageSize = queryParams.pageSize || 10
      
      // 更新缓存
      result.list.forEach(item => {
        cache.value.set(item.id, item)
      })
    } catch (error) {
      console.error('获取出库通知单列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取详情
  const fetchDetail = async (id: number) => {
    try {
      // 先从缓存获取
      if (cache.value.has(id)) {
        currentRecord.value = cache.value.get(id)!
        return currentRecord.value
      }
      
      const result = await outboundNotificationApi.getOutboundNotificationById(id)
      currentRecord.value = result
      
      // 更新缓存
      cache.value.set(id, result)
      
      return result
    } catch (error) {
      console.error('获取出库通知单详情失败:', error)
      throw error
    }
  }
  
  // 创建记录
  const create = async (data: WmsOutboundNotificationCreateReq) => {
    try {
      const result = await outboundNotificationApi.createOutboundNotification(data)
      
      // 添加到列表开头
      list.value.unshift(result)
      total.value += 1
      
      // 更新缓存
      cache.value.set(result.id, result)
      
      // 设置为当前记录
      currentRecord.value = result
      
      return result
    } catch (error) {
      console.error('创建出库通知单失败:', error)
      throw error
    }
  }
  
  // 更新记录
  const update = async (id: number, data: WmsOutboundNotificationUpdateReq) => {
    try {
      const result = await outboundNotificationApi.updateOutboundNotification(id, data)
      
      // 更新列表中的记录
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1) {
        list.value[index] = result
      }
      
      // 更新缓存
      cache.value.set(id, result)
      
      // 更新当前记录
      if (currentRecord.value?.id === id) {
        currentRecord.value = result
      }
      
      return result
    } catch (error) {
      console.error('更新出库通知单失败:', error)
      throw error
    }
  }
  
  // 删除记录
  const remove = async (id: number) => {
    try {
      await outboundNotificationApi.deleteOutboundNotification(id)
      
      // 从列表中移除
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1) {
        list.value.splice(index, 1)
        total.value -= 1
      }
      
      // 从缓存中移除
      cache.value.delete(id)
      
      // 清除当前记录
      if (currentRecord.value?.id === id) {
        currentRecord.value = null
      }
    } catch (error) {
      console.error('删除出库通知单失败:', error)
      throw error
    }
  }
  
  // 更新状态
  const updateStatus = async (id: number, status: WmsOutboundNotificationStatus, remark?: string) => {
    try {
      await outboundNotificationApi.updateOutboundNotificationStatus(id, { status, remark })
      
      // 更新列表中的记录状态
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1 && list.value[index]) {
        list.value[index].status = status
      }
      
      // 更新缓存中的记录状态
      if (cache.value.has(id)) {
        const cachedItem = cache.value.get(id)!
        cachedItem.status = status
        cache.value.set(id, cachedItem)
      }
      
      // 更新当前记录状态
      if (currentRecord.value?.id === id) {
        currentRecord.value.status = status
      }
    } catch (error) {
      console.error('更新出库通知单状态失败:', error)
      throw error
    }
  }
  
  // 审核通过
  const approve = async (id: number, remark?: string) => {
    try {
      await outboundNotificationApi.approveOutboundNotification(id, { remark })
      await updateStatus(id, 'APPROVED', remark)
    } catch (error) {
      console.error('审核出库通知单失败:', error)
      throw error
    }
  }
  
  // 取消
  const cancel = async (id: number, reason?: string) => {
    try {
      await outboundNotificationApi.cancelOutboundNotification(id, { reason })
      await updateStatus(id, 'CANCELLED', reason)
    } catch (error) {
      console.error('取消出库通知单失败:', error)
      throw error
    }
  }
  
  // 库存分配
  const allocateInventory = async (id: number, strategy?: string) => {
    try {
      await outboundNotificationApi.allocateInventory(id, { strategy })
      await updateStatus(id, 'ALLOCATED')
    } catch (error) {
      console.error('库存分配失败:', error)
      throw error
    }
  }
  
  // 生成拣货任务
  const generatePickingTask = async (id: number, strategy?: string) => {
    try {
      const result = await outboundNotificationApi.generatePickingTask(id, { strategy })
      await updateStatus(id, 'PICKING')
      return result
    } catch (error) {
      console.error('生成拣货任务失败:', error)
      throw error
    }
  }
  
  // ==================== 批量操作 ====================
  
  // 批量删除
  const batchRemove = async (ids: number[]) => {
    try {
      await outboundNotificationApi.batchDeleteOutboundNotifications(ids)
      
      // 从列表中移除
      ids.forEach(id => {
        const index = list.value.findIndex(item => item.id === id)
        if (index !== -1) {
          list.value.splice(index, 1)
          total.value -= 1
        }
        cache.value.delete(id)
      })
      
      // 清除当前记录（如果被删除）
      if (currentRecord.value && ids.includes(currentRecord.value.id)) {
        currentRecord.value = null
      }
    } catch (error) {
      console.error('批量删除出库通知单失败:', error)
      throw error
    }
  }
  
  // 批量更新状态
  const batchUpdateStatus = async (ids: number[], status: WmsOutboundNotificationStatus, remark?: string) => {
    try {
      await outboundNotificationApi.batchUpdateOutboundNotificationStatus({ ids, status, remark })
      
      // 更新列表中的记录状态
      ids.forEach(id => {
        const index = list.value.findIndex(item => item.id === id)
        if (index !== -1 && list.value[index]) {
          list.value[index].status = status
        }
        
        // 更新缓存
        if (cache.value.has(id)) {
          const cachedItem = cache.value.get(id)!
          cachedItem.status = status
          cache.value.set(id, cachedItem)
        }
      })
      
      // 更新当前记录状态
      if (currentRecord.value && ids.includes(currentRecord.value.id)) {
        currentRecord.value.status = status
      }
    } catch (error) {
      console.error('批量更新出库通知单状态失败:', error)
      throw error
    }
  }
  
  // 批量审核
  const batchApprove = async (ids: number[], remark?: string) => {
    try {
      await outboundNotificationApi.batchApproveOutboundNotifications({ ids, remark })
      await batchUpdateStatus(ids, 'APPROVED', remark)
    } catch (error) {
      console.error('批量审核出库通知单失败:', error)
      throw error
    }
  }
  
  // 批量库存分配
  const batchAllocateInventory = async (ids: number[], strategy?: string) => {
    try {
      await outboundNotificationApi.batchAllocateInventory({ ids, strategy })
      await batchUpdateStatus(ids, 'ALLOCATED')
    } catch (error) {
      console.error('批量库存分配失败:', error)
      throw error
    }
  }
  
  // ==================== 工具方法 ====================
  
  // 重置搜索表单
  const resetSearchForm = () => {
    searchForm.value = {
      pageNum: 1,
      pageSize: 10
    }
  }
  
  // 重置表单数据
  const resetFormData = () => {
    formData.value = {
      clientId: null,
      warehouseId: null,
      priority: 3,
      details: [],
      _mode: 'create'
    }
  }
  
  // 清除缓存
  const clearCache = () => {
    cache.value.clear()
  }
  
  // 重置所有状态
  const reset = () => {
    list.value = []
    total.value = 0
    currentRecord.value = null
    loading.value = false
    resetSearchForm()
    resetFormData()
    clearCache()
  }
  
  // ==================== 返回值 ====================
  
  return {
    // 状态
    list,
    total,
    loading,
    currentRecord,
    pagination,
    searchForm,
    formData,
    
    // 计算属性
    hasData,
    isEmpty,
    statusStats,
    priorityStats,
    canEdit,
    canDelete,
    
    // 异步操作
    fetchList,
    fetchDetail,
    create,
    update,
    remove,
    updateStatus,
    approve,
    cancel,
    allocateInventory,
    generatePickingTask,
    
    // 批量操作
    batchRemove,
    batchUpdateStatus,
    batchApprove,
    batchAllocateInventory,
    
    // 工具方法
    resetSearchForm,
    resetFormData,
    clearCache,
    reset
  }
})
