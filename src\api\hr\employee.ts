import request from '@/utils/request';

// --- 类型定义 (根据后端实际的 VO 和 DTO 进行精确化) ---

// 员工列表项 (大致对应后端 vo.EmployeeVO)
export interface EmployeeListItem {
  id: number; // From TenantVO
  employeeCode: string;
  employeeName: string;
  employeeGender?: number;
  employeeGenderText?: string;
  employeeMobile?: string;
  employeeEmail?: string; // Added
  employeeDepartmentId?: number | null; // Corrected name
  employeeDepartmentName?: string;
  employeePositionId?: number | null; // Corrected name
  employeePositionName?: string;
  employeeInDate?: string | null;
  employeeOutDate?: string | null; // Added
  employeeStatus?: number;
  employeeStatusText?: string;
  employeeAvatar?: string; // Added
  employeeEducationText?: string; // Added
  employeeMaritalStatusText?: string; // Added
  employeePoliticalStatusText?: string; // Added
  employeeIDCardTypeText?: string; // Added
  employeeWorkTypeText?: string; // Added
  employeeHealthStatusText?: string; // Added
  createTime?: string; // From TenantVO
  updateTime?: string; // From TenantVO
}

// 员工列表响应
export interface EmployeeListResponse {
  list: EmployeeListItem[];
  total: number;
}

// 员工查询参数 (对应后端 dto.EmployeePageQueryDTO)
export interface EmployeeQueryParams {
  pageNum: number;
  pageSize: number;
  employeeCode?: string;
  employeeName?: string;
  employeeMobile?: string;
  employeeStatus?: number;
  employeeDepartmentId?: number | null; // Corrected name
  employeePositionId?: number | null; // Corrected name
  sort?: string;
}

// 员工创建/更新数据 (对应后端 dto.EmployeeCreateOrUpdateDTO)
export interface EmployeeFormData {
  id?: number;
  employeeCode: string;
  employeeName: string;
  employeeNamePinyin?: string | null; // Matching DTO
  employeeNameEn?: string | null;     // Matching DTO
  employeeIDCard: string;
  employeeMobile: string;
  employeeEmail?: string | null;
  employeeGender?: number | null; // DTO uses int, TS can be number | null
  employeeBirthday?: string | null; // util.JsonDate maps to string
  employeeAvatar?: string | null;
  employeeAddress?: string | null; // General address
  employeeHouseholdAddress?: string | null;
  employeeCurrentAddress?: string | null;
  employeeInDate: string; // util.JsonDate maps to string
  employeeOutDate?: string | null;
  employeeFirstWorkDate?: string | null;
  employeeStatus: number;
  employeeRemark?: string | null;
  employeeEducation?: number | null;
  employeeMaritalStatus?: number | null;
  employeeNationality?: string | null;
  employeeNation?: string | null; // 新增：民族
  employeeNativePlace?: string | null;
  employeePoliticalStatus?: number | null;
  employeeIDCardType?: number | null;
  employeeIDCardValidityPeriod?: string | null;
  employeeDepartmentId?: number | null; // DTO uses uint
  employeePositionId?: number | null;   // DTO uses uint
  employeeJobTitle?: string | null;
  employeeWorkType?: number | null;
  employeeWorkLocation?: string | null;
  employeeEmergencyContactName?: string | null;
  employeeEmergencyContactMobile?: string | null;
  employeeEmergencyContactRelationship?: string | null;
  employeeBankName?: string | null;
  employeeBankAccount?: string | null;
  employeeSocialSecurityNumber?: string | null;
  employeeHousingFundNumber?: string | null;
  employeeProbationEndDate?: string | null;
  employeeContractStartDate?: string | null;
  employeeContractEndDate?: string | null;
  employeeSalary?: number | null;
  employeeWorkYears?: number | null;
  employeeSkills?: string | null;
  employeeCertificates?: string | null;
  employeeHealthStatus?: number | null;
  employeeBloodType?: string | null;
  employeeHeight?: number | null;
  employeeWeight?: number | null;
  employeeQQ?: string | null;
  employeePostCode?: string | null;
  employeeEntrySource?: string | null;
  employeeReferralName?: string | null;
  employeeHobby?: string | null;
  employeeJobGradeValue?: string | null;
  employeeJobSubLevelValue?: string | null;
  employeeWorkCategoryValue?: string | null;
  employeeDegree?: string | null;
  employeeMajor?: string | null;
  employeeSchool?: string | null;
  employeeGraduationDate?: string | null;
  // No *Text fields, no createTime/updateTime here
}

// 员工详情 (通常比 EmployeeFormData 多一些关联文本，并包含ID)
export interface EmployeeDetail { // Does not extend EmployeeFormData in this new definition
  // Fields from TenantVO (like ID, CreateTime, UpdateTime)
  id: number; // From TenantVO in backend VO
  createTime?: string;
  updateTime?: string;

  // All data fields corresponding to EmployeeVO
  employeeCode: string;
  employeeName: string;
  employeeNamePinyin?: string;
  employeeNameEn?: string;
  employeeIDCard: string;
  employeeMobile: string;
  employeeEmail?: string;
  employeeGender?: number; // VO has int
  employeeBirthday?: string | null;
  employeeAvatar?: string;
  employeeAddress?: string;
  employeeHouseholdAddress?: string;
  employeeCurrentAddress?: string;
  employeePostCode?: string;
  employeeInDate: string;
  employeeOutDate?: string | null;
  employeeFirstWorkDate?: string | null;
  employeeStatus?: number; // VO has int
  employeeRemark?: string;
  employeeEducation?: number;
  employeeDegree?: string;
  employeeMajor?: string;
  employeeSchool?: string;
  employeeGraduationDate?: string | null;
  employeeMaritalStatus?: number;
  employeeNationality?: string;
  employeeNation?: string; // 新增：民族
  employeeNativePlace?: string;
  employeePoliticalStatus?: number;
  employeeIDCardType?: number;
  employeeIDCardValidityPeriod?: string;
  employeeDepartmentId?: number; // VO has uint
  employeePositionId?: number;   // VO has uint
  employeeJobTitle?: string;
  employeeWorkType?: number;
  employeeWorkLocation?: string;
  employeeEmergencyContactName?: string;
  employeeEmergencyContactMobile?: string;
  employeeEmergencyContactRelationship?: string;
  employeeBankName?: string;
  employeeBankAccount?: string;
  employeeSocialSecurityNumber?: string;
  employeeHousingFundNumber?: string;
  employeeProbationEndDate?: string | null;
  employeeContractStartDate?: string | null;
  employeeContractEndDate?: string | null;
  employeeSalary?: number | null;
  employeeWorkYears?: number | null;
  employeeSkills?: string;
  employeeCertificates?: string;
  employeeHealthStatus?: number;
  employeeBloodType?: string;
  employeeHeight?: number | null;
  employeeWeight?: number | null;
  employeeQQ?: string;
  employeeEntrySource?: string;
  employeeReferralName?: string;
  employeeHobby?: string;
  employeeJobGradeValue?: string;
  employeeJobSubLevelValue?: string;
  employeeWorkCategoryValue?: string;

  // Enhanced display fields from VO
  employeeGenderText?: string;
  employeeStatusText?: string;
  employeeEducationText?: string;
  employeeMaritalStatusText?: string;
  employeePoliticalStatusText?: string;
  employeeIDCardTypeText?: string;
  employeeWorkTypeText?: string;
  employeeHealthStatusText?: string;
  employeeDegreeText?: string;
  employeeJobTitleText?: string;
  employeeEntrySourceText?: string;
  employeeEmergencyContactRelationshipText?: string;
  employeeJobGradeValueText?: string;
  employeeJobSubLevelValueText?: string;
  employeeWorkCategoryValueText?: string;
  employeeNationText?: string; // 新增：民族文本

  // Associated information from VO
  employeeDepartmentName?: string;
  employeePositionName?: string;
}

// 员工精简列表项 (对应后端 vo.EmployeeSimpleVO)
export interface EmployeeSimpleItem {
  id: number; // Backend VO has ID (uint)
  employeeCode: string;
  employeeName: string;
}

// 员工精简列表查询参数 (对应后端 dto.EmployeeSimpleListQueryDTO)
export interface EmployeeSimpleListQueryParams {
  employeeCode?: string;
  employeeName?: string;
  status?: number;
}

// 员工状态更新 (对应后端 dto.EmployeeStatusUpdateDTO)
export interface EmployeeStatusUpdatePayload {
  status: number;
}

// 头像上传响应
export interface AvatarUploadResponse {
  avatarUrl: string;
}


// --- API 函数定义 ---

const API_PREFIX = '/hr/employees';

/**
 * 获取员工分页列表
 * @param params 查询参数
 */
export function getEmployeeList(params: EmployeeQueryParams): Promise<EmployeeListResponse> {
  return request.get<EmployeeListResponse>(`${API_PREFIX}/page`, { params }) as unknown as Promise<EmployeeListResponse>;
}

/**
 * 创建新员工
 * @param data 员工数据
 */
export function createEmployee(data: EmployeeFormData): Promise<EmployeeListItem> { // Assuming backend returns the created EmployeeVO
  return request.post<EmployeeListItem>(API_PREFIX, data) as unknown as Promise<EmployeeListItem>;
}

/**
 * 更新员工信息
 * @param id 员工ID
 * @param data 员工数据
 */
export function updateEmployee(id: number, data: Partial<EmployeeFormData>): Promise<EmployeeListItem> { // Assuming backend returns the updated EmployeeVO
  return request.put<EmployeeListItem>(`${API_PREFIX}/${id}`, data) as unknown as Promise<EmployeeListItem>;
}

/**
 * 删除单个员工
 * @param id 员工ID
 */
export function deleteEmployee(id: number): Promise<null> { // Typically returns no content or a success indicator
  return request.delete<null>(`${API_PREFIX}/${id}`) as unknown as Promise<null>;
}

/**
 * 批量删除员工
 * @param ids 员工ID数组
 */
export function batchDeleteEmployees(ids: number[]): Promise<null> {
  return request.post<null>(`${API_PREFIX}/batch-delete`, { ids }) as unknown as Promise<null>;
}

/**
 * 获取员工详情 (by ID)
 * @param id 员工ID
 */
export function getEmployeeDetail(id: number): Promise<EmployeeDetail> {
  return request.get<EmployeeDetail>(`${API_PREFIX}/${id}`) as unknown as Promise<EmployeeDetail>;
}

/**
 * 获取员工详情 (by Code)
 * @param code 员工代码
 */
export function getEmployeeByCode(code: string): Promise<EmployeeDetail> {
  return request.get<EmployeeDetail>(`${API_PREFIX}/code/${code}`) as unknown as Promise<EmployeeDetail>;
}

/**
 * 获取员工精简列表 (用于选择框等)
 * @param params 查询参数
 */
export function getEmployeeSimpleList(params?: EmployeeSimpleListQueryParams): Promise<{ list: EmployeeSimpleItem[] }> {
  return request.get<{ list: EmployeeSimpleItem[] }>(`${API_PREFIX}/simple-list`, { params }) as unknown as Promise<{ list: EmployeeSimpleItem[] }>;
}

/**
 * 更新员工状态
 * @param id 员工ID
 * @param payload 状态数据 { status: number }
 */
export function updateEmployeeStatus(id: number, payload: EmployeeStatusUpdatePayload): Promise<null> {
  return request.put<null>(`${API_PREFIX}/${id}/status`, payload) as unknown as Promise<null>;
}

/**
 * 上传员工头像
 * @param file 文件对象
 */
export function uploadEmployeeAvatar(file: File): Promise<AvatarUploadResponse> {
  const formData = new FormData();
  formData.append('file', file);
  return request.post<AvatarUploadResponse>(`${API_PREFIX}/avatar/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }) as unknown as Promise<AvatarUploadResponse>;
}

// --- 组织和字典相关API (通常在各自的API文件中，这里列出仅为参考) ---

/**
 * 获取岗位列表 (OrganizationNode 列表，类型为 Position)
 * @param params 查询参数，例如 { nodeType: 'Position', status: 1 }
 */
// export function getPositionList(params: any): Promise<any> {
//   return request.get<any>('/hr/organization/nodes', { params });
// }

/**
 * 获取组织树 (部门树)
 * @param params 查询参数，例如 { companyId: 1 }
 */
// export function getDepartmentTree(params?: any): Promise<any> {
//   return request.get<any>('/hr/organization/tree', { params });
// }

/**
 * 根据字典类型编码获取字典项列表
 * @param typeCode 字典类型编码
 */
// export function getDictItemsByTypeCode(typeCode: string): Promise<any> {
//   return request.get<any>(`/sys/dict/items/list?typeCode=${typeCode}`);
// }
