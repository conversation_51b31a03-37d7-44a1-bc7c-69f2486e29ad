package repository

import (
	"context"
	stdErrors "errors"
	"reflect"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// UserRoleRepository 用户角色关联仓库接口
// 负责管理用户与角色之间的多对多关系
type UserRoleRepository interface {
	// AddRoleToUser 为用户添加角色
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - roleID: 角色ID
	// 返回:
	//   - error: 错误信息
	AddRoleToUser(ctx context.Context, userID, roleID uint) error

	// RemoveRoleFromUser 从用户移除角色
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - roleID: 角色ID
	// 返回:
	//   - error: 错误信息
	RemoveRoleFromUser(ctx context.Context, userID, roleID uint) error

	// GetUserRoles 获取用户的所有角色
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.Role: 角色列表
	//   - error: 错误信息
	GetUserRoles(ctx context.Context, userID uint, sortInfos []response.SortInfo) ([]*entity.Role, error)

	// GetRoleUsers 获取拥有特定角色的所有用户
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.User: 用户列表
	//   - error: 错误信息
	GetRoleUsers(ctx context.Context, roleID uint, sortInfos []response.SortInfo) ([]*entity.User, error)

	// HasRole 检查用户是否拥有特定角色
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - roleID: 角色ID
	// 返回:
	//   - bool: 是否拥有角色
	//   - error: 错误信息
	HasRole(ctx context.Context, userID, roleID uint) (bool, error)

	// SetUserRoles 设置用户的角色（替换现有角色）
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - roleIDs: 角色ID列表
	// 返回:
	//   - error: 错误信息
	SetUserRoles(ctx context.Context, userID uint, roleIDs []uint) error

	// SetRoleUsers 设置角色的用户（替换现有用户）
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - userIDs: 用户ID列表
	// 返回:
	//   - error: 错误信息
	SetRoleUsers(ctx context.Context, roleID uint, userIDs []uint) error

	// CountUsersByRole 统计拥有特定角色的用户数量
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	// 返回:
	//   - int64: 用户数量
	//   - error: 错误信息
	CountUsersByRole(ctx context.Context, roleID uint) (int64, error)

	// CountRolesByUser 统计用户拥有的角色数量
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	// 返回:
	//   - int64: 角色数量
	//   - error: 错误信息
	CountRolesByUser(ctx context.Context, userID uint) (int64, error)

	// FindUsersNotInRole 查询不属于特定角色的用户列表 (分页)
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - pageQuery: 分页查询参数
	//   - conditions: 过滤条件
	// 返回:
	//   - *response.PageResult: 分页结果
	//   - error: 错误信息
	FindUsersNotInRole(ctx context.Context, roleID uint, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error)

	// FindUsersInRole 查询属于特定角色的用户列表 (分页)
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - pageQuery: 分页查询参数
	//   - conditions: 过滤条件
	// 返回:
	//   - *response.PageResult: 分页结果
	//   - error: 错误信息
	FindUsersInRole(ctx context.Context, roleID uint, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error)

	// DeleteUserRolesByUserID 删除用户的所有角色关联
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	// 返回:
	//   - error: 错误信息
	DeleteUserRolesByUserID(ctx context.Context, userID uint) error

	// DeleteRoleUsersByRoleID 删除角色的所有用户关联
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	// 返回:
	//   - error: 错误信息
	DeleteRoleUsersByRoleID(ctx context.Context, roleID uint) error

	// CountUsersByRoleIDs 批量统计多个角色的用户数量
	// 参数:
	//   - ctx: 上下文
	//   - roleIDs: 角色ID列表
	// 返回:
	//   - map[uint]int64: 角色ID到用户数量的映射
	//   - error: 错误信息
	CountUsersByRoleIDs(ctx context.Context, roleIDs []uint) (map[uint]int64, error)
}

// UserRoleRepositoryImpl 用户角色关联仓库实现
type UserRoleRepositoryImpl struct {
	db *gorm.DB // 直接持有 DB 连接
}

// NewUserRoleRepository 创建用户角色关联仓库
// 参数:
//   - db: 数据库连接
//
// 返回:
//   - UserRoleRepository: 用户角色关联仓库接口
func NewUserRoleRepository(db *gorm.DB) UserRoleRepository {
	return &UserRoleRepositoryImpl{
		db: db, // 直接赋值
	}
}

// getDB 获取数据库连接
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - *gorm.DB: 数据库连接
func (r *UserRoleRepositoryImpl) getDB(ctx context.Context) *gorm.DB {
	// 直接使用注入的 r.db。如果 RepositoryManager.Transaction 启动了事务，
	// 那么 r.db 已经是事务性的 *gorm.DB。
	// 使用 WithContext 将当前请求的上下文与数据库操作关联起来。
	if r.db == nil {
		// 理论上不应该发生，因为 RepositoryManager 会注入 DB
		logger.WithContext(ctx).Error("UserRoleRepositoryImpl: database connection is nil")
		panic("UserRoleRepositoryImpl: database connection is nil")
	}
	if ctx != nil {
		return r.db.WithContext(ctx)
	}
	// 如果 ctx 为 nil，则返回原始的 db 连接（不常见，但作为回退）
	return r.db
}

// AddRoleToUser 为用户添加角色 (使用 entity.UserRole)
func (r *UserRoleRepositoryImpl) AddRoleToUser(ctx context.Context, userID, roleID uint) error {
	exists, err := r.HasRole(ctx, userID, roleID)
	if err != nil {
		return err
	}
	if exists {
		logger.WithContext(ctx).Warnf("AddRoleToUser: 关联已存在 (UserID: %d, RoleID: %d)", userID, roleID)
		return nil
	}

	newUserRole := entity.UserRole{
		UserID: userID,
		RoleID: roleID,
	}

	result := r.getDB(ctx).Create(&newUserRole)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("AddRoleToUser: 创建用户角色关联失败 (UserID: %d, RoleID: %d)", userID, roleID)
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "添加用户角色关联失败").WithCause(result.Error)
	}

	logger.WithContext(ctx).Debugf("AddRoleToUser: 成功添加关联 (UserID: %d, RoleID: %d)", userID, roleID)
	return nil
}

// RemoveRoleFromUser 从用户移除角色 (使用 entity.UserRole)
func (r *UserRoleRepositoryImpl) RemoveRoleFromUser(ctx context.Context, userID, roleID uint) error {
	result := r.getDB(ctx).Where("user_id = ? AND role_id = ?", userID, roleID).Delete(&entity.UserRole{})

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("RemoveRoleFromUser: 删除用户角色关联失败 (UserID: %d, RoleID: %d)", userID, roleID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "移除用户角色关联失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		logger.WithContext(ctx).Warnf("RemoveRoleFromUser: 未找到要删除的关联 (UserID: %d, RoleID: %d)", userID, roleID)
	} else {
		logger.WithContext(ctx).Debugf("RemoveRoleFromUser: 成功删除关联 (UserID: %d, RoleID: %d)", userID, roleID)
	}

	return nil
}

// GetUserRoles 获取用户的所有角色 (使用 entity.UserRole 和 JOIN)
func (r *UserRoleRepositoryImpl) GetUserRoles(ctx context.Context, userID uint, sortInfos []response.SortInfo) ([]*entity.Role, error) {
	var roles []*entity.Role

	db := r.getDB(ctx).Table("sys_role AS r").
		Select("r.*").
		Joins("JOIN sys_user_role AS ur ON r.id = ur.role_id").
		Where("ur.user_id = ?", userID)

	// 获取 Role 实体的允许排序字段
	entityType := reflect.TypeOf(entity.Role{})
	allowedColumns := getSortableColumns(entityType)
	// 添加表别名 'r.'
	aliasedAllowedColumns := make(map[string]string)
	for k, v := range allowedColumns {
		aliasedAllowedColumns[k] = "r." + v
	}
	// 应用排序，传入允许的带别名的列
	db = applySortInfos(db, sortInfos, aliasedAllowedColumns)

	result := db.Find(&roles)

	if result.Error != nil {
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return []*entity.Role{}, nil
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询用户角色失败 (UserID: %d)", userID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询用户角色失败").WithCause(result.Error)
	}

	return roles, nil
}

// GetRoleUsers 获取拥有特定角色的所有用户 (使用 entity.UserRole 和 JOIN)
func (r *UserRoleRepositoryImpl) GetRoleUsers(ctx context.Context, roleID uint, sortInfos []response.SortInfo) ([]*entity.User, error) {
	var users []*entity.User

	db := r.getDB(ctx).Table("sys_user AS u").
		Select("u.*, ur.created_at as assigned_at").
		Joins("JOIN sys_user_role AS ur ON u.id = ur.user_id").
		Where("ur.role_id = ?", roleID)

	// 获取 User 实体的允许排序字段
	entityType := reflect.TypeOf(entity.User{})
	allowedColumns := getSortableColumns(entityType)
	// 添加表别名 'u.'
	aliasedAllowedColumns := make(map[string]string)
	for k, v := range allowedColumns {
		aliasedAllowedColumns[k] = "u." + v
	}
	// 应用排序，传入允许的带别名的列
	db = applySortInfos(db, sortInfos, aliasedAllowedColumns)

	result := db.Find(&users)

	if result.Error != nil {
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return []*entity.User{}, nil
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询角色用户失败 (RoleID: %d)", roleID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询角色用户失败").WithCause(result.Error)
	}

	return users, nil
}

// HasRole 检查用户是否拥有特定角色
func (r *UserRoleRepositoryImpl) HasRole(ctx context.Context, userID, roleID uint) (bool, error) {
	count, err := r.CountRolesByUserAndRoleID(ctx, userID, roleID)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// 辅助方法：根据 UserID 和 RoleID 计数
func (r *UserRoleRepositoryImpl) CountRolesByUserAndRoleID(ctx context.Context, userID, roleID uint) (int64, error) {
	var count int64
	result := r.getDB(ctx).Model(&entity.UserRole{}).Where("user_id = ? AND role_id = ?", userID, roleID).Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("检查用户角色关联计数失败 (UserID: %d, RoleID: %d)", userID, roleID)
		return 0, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查用户角色关联失败").WithCause(result.Error)
	}
	return count, nil
}

// SetUserRoles 设置用户的角色（替换现有角色）(使用 entity.UserRole)
func (r *UserRoleRepositoryImpl) SetUserRoles(ctx context.Context, userID uint, roleIDs []uint) error {
	db := r.getDB(ctx)

	// 1. 删除现有角色
	if err := db.Where("user_id = ?", userID).Delete(&entity.UserRole{}).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("SetUserRoles: 删除用户旧角色关联失败 (UserID: %d)", userID)
		return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "设置用户角色失败")
	}

	// 2. 添加新角色（如果列表不为空）
	if len(roleIDs) > 0 {
		newUserRoles := make([]entity.UserRole, 0, len(roleIDs))
		processedIDs := make(map[uint]struct{}, len(roleIDs))

		for _, roleID := range roleIDs {
			if roleID == 0 {
				logger.WithContext(ctx).Warnf("SetUserRoles: 跳过无效的角色ID 0 (UserID: %d)", userID)
				continue
			}
			if _, exists := processedIDs[roleID]; exists {
				logger.WithContext(ctx).Warnf("SetUserRoles: 跳过重复的角色ID %d (UserID: %d)", roleID, userID)
				continue
			}

			newUserRoles = append(newUserRoles, entity.UserRole{UserID: userID, RoleID: roleID})
			processedIDs[roleID] = struct{}{}
		}

		// 只有在处理后有有效角色时才插入
		if len(newUserRoles) > 0 {
			if err := db.Create(&newUserRoles).Error; err != nil {
				logger.WithContext(ctx).WithError(err).Errorf("SetUserRoles: 添加用户新角色关联失败 (UserID: %d)", userID)
				return apperrors.WrapError(err, apperrors.CODE_DATA_CREATE_FAILED, "设置用户角色失败")
			}
		}
	}

	logger.WithContext(ctx).Debugf("SetUserRoles: 成功设置用户角色 (UserID: %d, RoleIDs: %v)", userID, roleIDs)
	return nil
}

// SetRoleUsers 设置角色的用户（替换现有用户）(使用 entity.UserRole)
func (r *UserRoleRepositoryImpl) SetRoleUsers(ctx context.Context, roleID uint, userIDs []uint) error {
	db := r.getDB(ctx)

	// 1. 删除现有用户
	if err := db.Where("role_id = ?", roleID).Delete(&entity.UserRole{}).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("SetRoleUsers: 删除角色旧用户关联失败 (RoleID: %d)", roleID)
		return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "设置角色用户失败")
	}

	// 2. 添加新用户（如果列表不为空）
	if len(userIDs) > 0 {
		newRoleUsers := make([]entity.UserRole, 0, len(userIDs))
		processedIDs := make(map[uint]struct{}, len(userIDs))

		for _, userID := range userIDs {
			if userID == 0 {
				logger.WithContext(ctx).Warnf("SetRoleUsers: 跳过无效的用户ID 0 (RoleID: %d)", roleID)
				continue
			}
			if _, exists := processedIDs[userID]; exists {
				logger.WithContext(ctx).Warnf("SetRoleUsers: 跳过重复的用户ID %d (RoleID: %d)", userID, roleID)
				continue
			}

			newRoleUsers = append(newRoleUsers, entity.UserRole{UserID: userID, RoleID: roleID})
			processedIDs[userID] = struct{}{}
		}

		// 只有在处理后有有效用户时才插入
		if len(newRoleUsers) > 0 {
			if err := db.Create(&newRoleUsers).Error; err != nil {
				logger.WithContext(ctx).WithError(err).Errorf("SetRoleUsers: 添加角色新用户关联失败 (RoleID: %d)", roleID)
				return apperrors.WrapError(err, apperrors.CODE_DATA_CREATE_FAILED, "设置角色用户失败")
			}
		}
	}

	logger.WithContext(ctx).Debugf("SetRoleUsers: 成功设置角色用户 (RoleID: %d, UserIDs: %v)", roleID, userIDs)
	return nil
}

// CountUsersByRole 统计拥有特定角色的用户数量
func (r *UserRoleRepositoryImpl) CountUsersByRole(ctx context.Context, roleID uint) (int64, error) {
	var count int64
	result := r.getDB(ctx).Model(&entity.UserRole{}).Where("role_id = ?", roleID).Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("统计角色用户数量失败 (RoleID: %d)", roleID)
		return 0, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "统计角色用户失败").WithCause(result.Error)
	}
	return count, nil
}

// CountRolesByUser 统计用户拥有的角色数量
func (r *UserRoleRepositoryImpl) CountRolesByUser(ctx context.Context, userID uint) (int64, error) {
	var count int64
	result := r.getDB(ctx).Model(&entity.UserRole{}).Where("user_id = ?", userID).Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("统计用户角色数量失败 (UserID: %d)", userID)
		return 0, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "统计用户角色失败").WithCause(result.Error)
	}
	return count, nil
}

// CountUsersByRoleIDs 批量统计多个角色的用户数量
func (r *UserRoleRepositoryImpl) CountUsersByRoleIDs(ctx context.Context, roleIDs []uint) (map[uint]int64, error) {
	results := make(map[uint]int64)
	if len(roleIDs) == 0 {
		return results, nil
	}

	// Define a struct to scan the result into
	type RoleUserCount struct {
		RoleID uint  `gorm:"column:role_id"`
		Count  int64 `gorm:"column:count"`
	}

	var counts []RoleUserCount

	db := r.getDB(ctx).Model(&entity.UserRole{}).
		Select("role_id, COUNT(*) as count").
		Where("role_id IN ?", roleIDs).
		Group("role_id")

	if err := db.Scan(&counts).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("批量统计角色用户数量失败 (RoleIDs: %v)", roleIDs)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "批量统计角色用户失败").WithCause(err)
	}

	// Populate the map
	for _, c := range counts {
		results[c.RoleID] = c.Count
	}

	// Ensure all requested roleIDs are in the map, even if count is 0
	for _, id := range roleIDs {
		if _, exists := results[id]; !exists {
			results[id] = 0
		}
	}

	return results, nil
}

// FindUsersNotInRole 查询不属于特定角色的用户列表 (分页)
func (r *UserRoleRepositoryImpl) FindUsersNotInRole(ctx context.Context, roleID uint, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error) {
	var total int64
	var users []*entity.User
	db := r.getDB(ctx).Model(&entity.User{})

	// 基础查询：用户不在给定的 roleID 关联中
	subQuery := r.getDB(ctx).Model(&entity.UserRole{}).Select("user_id").Where("role_id = ?", roleID)
	db = db.Where("id NOT IN (?)", subQuery)

	// 应用额外的过滤条件 (例如，按用户名、状态等过滤)
	db = ApplyConditions(db, conditions)

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("查询非角色用户总数失败 (RoleID: %d)", roleID)
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户总数失败")
	}

	// 处理分页和排序
	page := pageQuery.PageNum
	size := pageQuery.PageSize
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = 10
	}
	offset := (page - 1) * size
	// 获取 User 实体的允许排序字段
	entityType := reflect.TypeOf(entity.User{})
	allowedColumns := getSortableColumns(entityType)
	// 直接传递，因为排序作用于主表 sys_user (无别名)
	db = applySortInfos(db, pageQuery.Sort, allowedColumns)

	// 查询数据
	if err := db.Offset(offset).Limit(size).Find(&users).Error; err != nil {
		// Find 不会返回 ErrRecordNotFound
		logger.WithContext(ctx).WithError(err).Errorf("分页查询非角色用户列表失败 (RoleID: %d)", roleID)
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户列表失败")
	}

	return &response.PageResult{
		Total:    total,
		List:     users,
		PageNum:  page,
		PageSize: size,
	}, nil
}

// FindUsersInRole 查询属于特定角色的用户列表 (分页)
func (r *UserRoleRepositoryImpl) FindUsersInRole(ctx context.Context, roleID uint, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error) {
	var total int64
	var users []*entity.User
	// SELECT u.* FROM sys_user u JOIN sys_user_role ur ON u.id = ur.user_id WHERE ur.role_id = ? AND [conditions]
	// 注意：这里的 Model(&entity.User{}) 隐式地将主表设为 sys_user
	db := r.getDB(ctx).Model(&entity.User{}).Joins("JOIN sys_user_role ur ON ur.user_id = sys_user.id").Where("ur.role_id = ?", roleID)

	// 应用额外的过滤条件 (作用于 sys_user 表)
	db = ApplyConditions(db, conditions)

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("查询角色用户总数失败 (RoleID: %d)", roleID)
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户总数失败")
	}

	// 处理分页和排序
	page := pageQuery.PageNum
	size := pageQuery.PageSize
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = 10
	}
	offset := (page - 1) * size
	// 获取 User 实体的允许排序字段
	entityType := reflect.TypeOf(entity.User{})
	allowedColumns := getSortableColumns(entityType)
	// 直接传递，因为排序作用于主表 sys_user (无别名)
	db = applySortInfos(db, pageQuery.Sort, allowedColumns)

	// 查询数据
	if err := db.Offset(offset).Limit(size).Find(&users).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("分页查询角色用户列表失败 (RoleID: %d)", roleID)
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户列表失败")
	}

	return &response.PageResult{
		Total:    total,
		List:     users,
		PageNum:  page,
		PageSize: size,
	}, nil
}

// DeleteUserRolesByUserID 删除用户的所有角色关联
func (r *UserRoleRepositoryImpl) DeleteUserRolesByUserID(ctx context.Context, userID uint) error {
	result := r.getDB(ctx).Where("user_id = ?", userID).Delete(&entity.UserRole{})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("删除用户所有角色关联失败 (UserID: %d)", userID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除用户角色关联失败").WithCause(result.Error)
	}
	logger.WithContext(ctx).Debugf("成功删除用户 %d 的 %d 个角色关联", userID, result.RowsAffected)
	return nil
}

// DeleteRoleUsersByRoleID 删除角色的所有用户关联
func (r *UserRoleRepositoryImpl) DeleteRoleUsersByRoleID(ctx context.Context, roleID uint) error {
	result := r.getDB(ctx).Where("role_id = ?", roleID).Delete(&entity.UserRole{})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("删除角色所有用户关联失败 (RoleID: %d)", roleID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除角色用户关联失败").WithCause(result.Error)
	}
	logger.WithContext(ctx).Debugf("成功删除角色 %d 的 %d 个用户关联", roleID, result.RowsAffected)
	return nil
}
