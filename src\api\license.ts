import service from '@/utils/request'; // 导入配置好的 Axios 实例
// import { useUserStoreHook } from '@/store/modules/user'; // 通常 API 文件不需要直接使用 store

// 定义后端返回的授权信息结构（根据控制器返回调整）
export interface LicenseDetails {
  status: 'active' | 'expired' | 'not_found' | string; // 增加 string 以应对未来可能的其他状态
  message: string;
  licensedTo?: string;
  expiresAt?: string | null;
  licenseType?: string;
  features?: string[];
  issueDate?: string;
  // allDetails?: any; // 如果后端发送了完整详情，可以按需定义
}

/**
 * 获取当前授权信息
 * @returns Promise 返回包含授权详情的 API 响应
 */
export const getLicenseInfo = async (): Promise<LicenseDetails> => {
  try {
    // 运行时日志表明拦截器已提取 data。Linter 可能未正确推断拦截器修改后的返回类型。
    // 使用类型断言以符合运行时行为并尝试平息 Linter。
    const response = await service.get<LicenseDetails>('/admin/license/info'); 
    return response as unknown as LicenseDetails; 
  } catch (error) {
    console.error('获取授权信息失败 (已由拦截器处理):', error);
    throw error;
  }
};

/**
 * 上传授权文件
 * @param licenseFile 要上传的授权文件
 * @returns Promise 返回 API 的响应 (也应该是 LicenseDetails 类型)
 */
export const uploadLicense = async (licenseFile: File): Promise<LicenseDetails> => {
  const formData = new FormData();
  formData.append('licenseFile', licenseFile);

  // Token 通常由请求拦截器处理，如果需要显式检查用户登录状态，应在调用此API的组件层面进行
  // const userStore = useUserStoreHook();
  // if (!userStore.token) {
  //   throw new Error('用户未登录，无法上传。');
  // }

  try {
    // 运行时日志表明拦截器已提取 data。
    const response = await service.post<LicenseDetails>('/admin/license/upload', formData);
    return response as unknown as LicenseDetails;
  } catch (error) {
    console.error('上传授权文件失败 (已由拦截器处理): ', error);
    throw error;
  }
}; 