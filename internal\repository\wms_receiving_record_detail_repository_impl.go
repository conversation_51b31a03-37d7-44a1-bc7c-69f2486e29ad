package repository

import (
	"backend/internal/model/entity"
	"context"

	"gorm.io/gorm"
)

type WmsReceivingRecordDetailRepository interface {
	BaseRepository[entity.WmsReceivingRecordDetail, uint]
	FindByReceivingRecordID(ctx context.Context, receivingRecordID uint) ([]*entity.WmsReceivingRecordDetail, error)
}

type wmsReceivingRecordDetailRepository struct {
	BaseRepositoryImpl[entity.WmsReceivingRecordDetail, uint]
}

func NewWmsReceivingRecordDetailRepository(db *gorm.DB) WmsReceivingRecordDetailRepository {
	return &wmsReceivingRecordDetailRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsReceivingRecordDetail, uint]{db: db},
	}
}

func (r *wmsReceivingRecordDetailRepository) FindByReceivingRecordID(ctx context.Context, receivingRecordID uint) ([]*entity.WmsReceivingRecordDetail, error) {
	var details []*entity.WmsReceivingRecordDetail
	err := r.db.WithContext(ctx).Where("receiving_record_id = ?", receivingRecordID).Find(&details).Error
	return details, err
}
