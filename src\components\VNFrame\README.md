# VNFrame 组件

一个通用的页面框架/布局组件，用于快速搭建经典的 **左右布局** 后台管理界面。

## 布局结构

*   **左侧:** 可折叠的垂直菜单栏 (`VNSidebar`)。
*   **右侧:** 包含三个区域：
    *   顶部: 面包屑导航区域 (`VNBreadcrumb`)。
    *   中部: 主要内容区域 (可滚动)。
    *   底部: 状态栏区域 (`VNStatusBar`)。

## 功能

*   提供经典的左右后台布局结构。
*   通过 Props 控制各个区域（侧边栏、面包屑、状态栏）的显示与隐藏。
*   集成 `VNSidebar`, `VNBreadcrumb`, `VNStatusBar` 作为默认实现，并允许通过插槽覆盖。
*   支持侧边栏的展开与折叠 (`v-model:sidebarCollapsed`)。
*   通过作用域插槽定制各个区域。

## Props

| Prop Name             | Type             | Description                        | Default      |
| --------------------- | ---------------- | ---------------------------------- | ------------ |
| `showSidebar`         | `boolean`        | 是否显示侧边栏                     | `true`       |
| `sidebarItems`        | `SidebarItem[]`  | 传递给 `VNSidebar` 的菜单项        | `undefined`  |
| `sidebarWidth`        | `string`         | 侧边栏展开宽度                     | `'200px'`    |
| `sidebarCollapsedWidth` | `string`       | 侧边栏折叠宽度                     | `'64px'`     |
| `sidebarCollapsed`    | `boolean`        | 控制侧边栏折叠状态 (v-model)       | `false`      |
| `showBreadcrumb`      | `boolean`        | 是否显示面包屑区域                 | `true`       |
| `breadcrumbItems`     | `BreadcrumbItem[]` | 传递给 `VNBreadcrumb` 的项         | `undefined`  |
| `breadcrumbHeight`    | `string`         | 面包屑区域的高度                   | `'50px'`     |
| `showStatusBar`       | `boolean`        | 是否显示底部状态栏                 | `true`       |
| `statusBarItems`      | `StatusBarItem[]`| 传递给 `VNStatusBar` 的项          | `undefined`  |
| `footerHeight`        | `string`         | 底部状态栏高度                     | `'30px'`     |

## Emits

*   `update:sidebarCollapsed(value: boolean)`: 当侧边栏折叠状态改变时触发。

## Slots

*   `default`: 主内容区域 (右侧中间部分)，通常放置 `<router-view>`。
*   `sidebar`: 自定义整个左侧边栏区域。
*   `breadcrumb`: 自定义右侧顶部的面包屑区域。
*   `footer`: 自定义右侧底部的状态栏区域。

## 使用示例

```vue
<!-- In your App.vue or layout component -->
<template>
  <VNFrame
    :sidebar-items="menuData"
    :breadcrumb-items="breadcrumbs"
    :status-bar-items="statusInfo"
    v-model:sidebarCollapsed="isCollapsed"
  >
    <!-- Main content goes here -->
    <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
            <component :is="Component" />
        </transition>
    </router-view>
  </VNFrame>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import VNFrame from '@/components/VNFrame/index.vue';
import type { SidebarItem } from '@/components/VNSidebar/types';
import type { BreadcrumbItem } from '@/components/VNBreadcrumb/types';
import type { StatusBarItem } from '@/components/VNStatusBar/types';
import { HomeFilled, Setting } from '@element-plus/icons-vue';

const isCollapsed = ref(false);
const route = useRoute();

// Example data for child components
const menuData = ref<SidebarItem[]>([
  { label: '首页', icon: HomeFilled, index: '/', to: { path: '/' } },
  { label: '设置', icon: Setting, index: '/settings', to: { path: '/settings' } },
]);

const breadcrumbs = computed<BreadcrumbItem[]>(() => {
  // Logic to generate breadcrumbs based on current route
  return route.matched.map(r => ({ label: r.meta.title as string || 'Unknown', to: r.path }))
                     .filter(b => b.label !== 'Unknown');
});

const statusInfo = ref<StatusBarItem[]>([
  { id: 'user', value: '当前用户: admin', align: 'right' },
  { id: 'time', value: new Date().toLocaleTimeString(), align: 'right' },
]);

// Update time periodically
setInterval(() => {
  const timeItem = statusInfo.value.find(i => i.id === 'time');
  if (timeItem) {
    timeItem.value = new Date().toLocaleTimeString();
  }
}, 1000);

</script>

<style>
/* Basic transition for router-view */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
```

## 注意事项

*   组件依赖 `VNSidebar`, `VNBreadcrumb`, `VNStatusBar`, `element-plus` 和 `@element-plus/icons-vue`。
*   `AppNav` 不再是此布局的默认组成部分。
*   可以通过 `v-model:sidebarCollapsed` 双向绑定侧边栏的折叠状态。
*   主内容区域 (`default` slot) 会自动处理垂直滚动。 