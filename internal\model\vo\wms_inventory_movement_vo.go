package vo

import (
	"backend/internal/model/entity"
	"fmt"
	"time"
)

// WmsInventoryMovementVO 库存移动视图对象
type WmsInventoryMovementVO struct {
	ID               uint                              `json:"id"`
	MovementNo       string                            `json:"movementNo"`
	ItemID           uint                              `json:"itemId"`
	FromLocationID   uint                              `json:"fromLocationId"`
	ToLocationID     uint                              `json:"toLocationId"`
	BatchNo          *string                           `json:"batchNo"`
	Quantity         float64                           `json:"quantity"`
	UnitOfMeasure    string                            `json:"unitOfMeasure"`
	MovementType     entity.WmsInventoryMovementType   `json:"movementType"`
	MovementTypeName string                            `json:"movementTypeName"`
	MovementReason   string                            `json:"movementReason"`
	Status           entity.WmsInventoryMovementStatus `json:"status"`
	StatusName       string                            `json:"statusName"`
	OperatorID       uint                              `json:"operatorId"`
	OperatorName     string                            `json:"operatorName"`
	StartedAt        *time.Time                        `json:"startedAt"`
	CompletedAt      *time.Time                        `json:"completedAt"`
	CreatedAt        time.Time                         `json:"createdAt"`
	UpdatedAt        time.Time                         `json:"updatedAt"`

	// 关联信息
	ItemSku          string `json:"itemSku"`
	ItemName         string `json:"itemName"`
	FromLocationCode string `json:"fromLocationCode"`
	FromLocationName string `json:"fromLocationName"`
	ToLocationCode   string `json:"toLocationCode"`
	ToLocationName   string `json:"toLocationName"`
	WarehouseName    string `json:"warehouseName"`

	// 业务状态
	CanStart    bool   `json:"canStart"`
	CanComplete bool   `json:"canComplete"`
	CanCancel   bool   `json:"canCancel"`
	Duration    *int64 `json:"duration"` // 执行时长(秒)
}

// WmsInventoryMovementDetailVO 库存移动详情视图对象
type WmsInventoryMovementDetailVO struct {
	WmsInventoryMovementVO

	// 源库存信息
	FromInventory *WmsInventoryVO `json:"fromInventory"`

	// 目标库存信息
	ToInventory *WmsInventoryVO `json:"toInventory"`

	// 操作历史
	OperationLogs []WmsInventoryMovementLogVO `json:"operationLogs"`

	// 相关事务
	TransactionLogs []WmsInventoryTransactionLogVO `json:"transactionLogs"`
}

// WmsInventoryMovementLogVO 库存移动操作日志视图对象
type WmsInventoryMovementLogVO struct {
	ID           uint      `json:"id"`
	Operation    string    `json:"operation"`
	OperatorID   uint      `json:"operatorId"`
	OperatorName string    `json:"operatorName"`
	Description  string    `json:"description"`
	CreatedAt    time.Time `json:"createdAt"`
}

// WmsInventoryMovementStatsVO 库存移动统计视图对象
type WmsInventoryMovementStatsVO struct {
	// 基础统计
	TotalCount      int64 `json:"totalCount"`
	PendingCount    int64 `json:"pendingCount"`
	InProgressCount int64 `json:"inProgressCount"`
	CompletedCount  int64 `json:"completedCount"`
	CancelledCount  int64 `json:"cancelledCount"`

	// 数量统计
	TotalQuantity     float64 `json:"totalQuantity"`
	CompletedQuantity float64 `json:"completedQuantity"`
	PendingQuantity   float64 `json:"pendingQuantity"`

	// 按类型统计
	ManualCount        int64 `json:"manualCount"`
	SystemCount        int64 `json:"systemCount"`
	ReplenishmentCount int64 `json:"replenishmentCount"`

	// 效率统计
	AvgDuration    float64 `json:"avgDuration"`    // 平均执行时长(秒)
	CompletionRate float64 `json:"completionRate"` // 完成率

	// 时间范围
	DateStart *time.Time `json:"dateStart"`
	DateEnd   *time.Time `json:"dateEnd"`

	// 趋势数据
	TrendData []WmsInventoryMovementTrendVO `json:"trendData"`
}

// WmsInventoryMovementTrendVO 库存移动趋势数据
type WmsInventoryMovementTrendVO struct {
	Date           string  `json:"date"`
	Count          int64   `json:"count"`
	CompletedCount int64   `json:"completedCount"`
	TotalQuantity  float64 `json:"totalQuantity"`
	AvgDuration    float64 `json:"avgDuration"`
}

// WmsInventoryMovementSummaryVO 库存移动汇总视图对象
type WmsInventoryMovementSummaryVO struct {
	ItemID        uint   `json:"itemId"`
	ItemSku       string `json:"itemSku"`
	ItemName      string `json:"itemName"`
	WarehouseID   uint   `json:"warehouseId"`
	WarehouseName string `json:"warehouseName"`

	// 移动统计
	MovementCount    int64   `json:"movementCount"`
	TotalQuantity    float64 `json:"totalQuantity"`
	InboundQuantity  float64 `json:"inboundQuantity"`
	OutboundQuantity float64 `json:"outboundQuantity"`

	// 最近移动
	LastMovementAt   *time.Time `json:"lastMovementAt"`
	LastMovementType *string    `json:"lastMovementType"`

	// 当前库存
	CurrentQuantity   float64 `json:"currentQuantity"`
	AvailableQuantity float64 `json:"availableQuantity"`
}

// WmsInventoryMovementPageVO 库存移动分页视图对象
type WmsInventoryMovementPageVO struct {
	List  []WmsInventoryMovementVO `json:"list"`
	Total int64                    `json:"total"`
}

// WmsInventoryMovementImportResultVO 库存移动导入结果视图对象
type WmsInventoryMovementImportResultVO struct {
	TotalCount   int                           `json:"totalCount"`
	SuccessCount int                           `json:"successCount"`
	FailureCount int                           `json:"failureCount"`
	Errors       []WmsInventoryMovementErrorVO `json:"errors"`
}

// WmsInventoryMovementErrorVO 库存移动错误视图对象
type WmsInventoryMovementErrorVO struct {
	Row     int    `json:"row"`
	Field   string `json:"field"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// NewWmsInventoryMovementVO 创建库存移动视图对象
func NewWmsInventoryMovementVO(movement *entity.WmsInventoryMovement) *WmsInventoryMovementVO {
	vo := &WmsInventoryMovementVO{
		ID:               movement.ID,
		MovementNo:       movement.MovementNo,
		ItemID:           movement.ItemID,
		FromLocationID:   movement.FromLocationID,
		ToLocationID:     movement.ToLocationID,
		BatchNo:          movement.BatchNo,
		Quantity:         movement.Quantity,
		UnitOfMeasure:    movement.UnitOfMeasure,
		MovementType:     movement.MovementType,
		MovementTypeName: movement.GetMovementTypeName(),
		MovementReason:   getStringValue(movement.MovementReason),
		Status:           movement.Status,
		StatusName:       movement.GetStatusName(),
		OperatorID:       movement.OperatorID,
		StartedAt:        movement.StartedAt,
		CompletedAt:      movement.CompletedAt,
		CreatedAt:        movement.CreatedAt,
		UpdatedAt:        movement.UpdatedAt,
		CanStart:         movement.CanStart(),
		CanComplete:      movement.CanComplete(),
		CanCancel:        movement.CanCancel(),
	}

	// 计算执行时长
	if movement.StartedAt != nil && movement.CompletedAt != nil {
		duration := int64(movement.CompletedAt.Sub(*movement.StartedAt).Seconds())
		vo.Duration = &duration
	}

	// 填充关联信息
	if movement.Item != nil {
		vo.ItemSku = movement.Item.Sku
		vo.ItemName = movement.Item.Name
	}

	if movement.FromLocation != nil {
		vo.FromLocationCode = movement.FromLocation.Code
		if movement.FromLocation.Name != nil {
			vo.FromLocationName = *movement.FromLocation.Name
		}
		// 仓库信息需要通过 WarehouseID 单独查询
		// WmsLocation 实体中没有 Warehouse 关联字段
	}

	if movement.ToLocation != nil {
		vo.ToLocationCode = movement.ToLocation.Code
		if movement.ToLocation.Name != nil {
			vo.ToLocationName = *movement.ToLocation.Name
		}
	}

	// 填充操作员信息
	if movement.Operator != nil {
		vo.OperatorName = movement.Operator.Username
	}

	return vo
}

// NewWmsInventoryMovementDetailVO 创建库存移动详情视图对象
func NewWmsInventoryMovementDetailVO(movement *entity.WmsInventoryMovement) *WmsInventoryMovementDetailVO {
	vo := &WmsInventoryMovementDetailVO{
		WmsInventoryMovementVO: *NewWmsInventoryMovementVO(movement),
	}

	return vo
}

// GetMovementDirection 获取移动方向描述
func (vo *WmsInventoryMovementVO) GetMovementDirection() string {
	return vo.FromLocationCode + " → " + vo.ToLocationCode
}

// GetDurationText 获取执行时长文本
func (vo *WmsInventoryMovementVO) GetDurationText() string {
	if vo.Duration == nil {
		return "-"
	}

	duration := *vo.Duration
	if duration < 60 {
		return fmt.Sprintf("%d秒", duration)
	} else if duration < 3600 {
		return fmt.Sprintf("%d分%d秒", duration/60, duration%60)
	} else {
		hours := duration / 3600
		minutes := (duration % 3600) / 60
		return fmt.Sprintf("%d小时%d分", hours, minutes)
	}
}

// IsCompleted 是否已完成
func (vo *WmsInventoryMovementVO) IsCompleted() bool {
	return vo.Status == entity.MovementStatusCompleted
}

// IsPending 是否待处理
func (vo *WmsInventoryMovementVO) IsPending() bool {
	return vo.Status == entity.MovementStatusPending
}

// IsInProgress 是否进行中
func (vo *WmsInventoryMovementVO) IsInProgress() bool {
	return vo.Status == entity.MovementStatusInProgress
}

// getStringValue 函数已在 wms_cycle_count_vo.go 中定义
