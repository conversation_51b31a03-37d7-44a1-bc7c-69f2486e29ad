package middleware

import (
	"backend/pkg/license" // Adjust import path based on your go.mod
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"sync"

	"github.com/kataras/iris/v12"
	log "github.com/sirupsen/logrus"

	"backend/pkg/constant"
	CustomResponse "backend/pkg/response"
	"backend/pkg/security"
)

// LicenseMiddlewareConfig 授权中间件配置
type LicenseMiddlewareConfig struct {
	// 是否启用
	Enabled bool `json:"enabled" yaml:"enabled"`
	// 授权文件路径
	LicensePath string `json:"licensePath" yaml:"licensePath"`
	// 密钥
	SecretKey string `json:"secretKey" yaml:"secretKey"`
	// 排除的路径（即使授权无效，这些路径也可以访问）
	ExcludedPaths []string `json:"excludedPaths" yaml:"excludedPaths"`
}

// 默认授权中间件配置
var defaultLicenseMiddlewareConfig = LicenseMiddlewareConfig{
	Enabled:       true,
	LicensePath:   security.DEFAULT_LICENSE_PATH,
	ExcludedPaths: []string{"/api/v1/auth/login", "/api/v1/auth/license", "/api/v1/auth/status", "/health", "/swagger"},
}

var (
	licenseInitOnce sync.Once // 保留 sync.Once 用于确保 InitLicense 只执行一次
)

// InitLicense loads and verifies the license file using the embedded public key.
// It should be called once during application startup.
func InitLicense(licenseFilePath string) {
	licenseInitOnce.Do(func() { // 使用新的 sync.Once 变量
		logger := log.WithContext(context.Background())
		logger.Infof("正在加载和验证授权文件: %s", licenseFilePath)

		if licenseFilePath == "" {
			license.UpdateRuntimeLicense(nil, errors.New("授权文件路径未配置")) // 调用 pkg/license 中的函数
			logger.Error("授权文件路径未配置")
			return
		}

		if _, err := os.Stat(licenseFilePath); errors.Is(err, os.ErrNotExist) {
			err := fmt.Errorf("授权文件不存在: %s", licenseFilePath)
			license.UpdateRuntimeLicense(nil, err) // 调用 pkg/license 中的函数
			logger.Error(err)
			return
		}

		// 从 pkg/license 获取公钥字符串来验证文件
		info, err := license.LoadAndVerifyLicenseFile(licenseFilePath, []byte(license.GetEmbeddedPublicKeyPEM()))
		if err != nil {
			err := fmt.Errorf("授权文件无效或验证失败: %w", err)
			license.UpdateRuntimeLicense(nil, err) // 调用 pkg/license 中的函数
			logger.Errorf("授权错误: %v", err)
			return
		}

		var initErr error
		if info.IsExpired() {
			initErr = errors.New("授权已过期")
			logger.Warn("警告: 授权已过期!")
		}
		license.UpdateRuntimeLicense(info, initErr) // 调用 pkg/license 中的函数

		if initErr == nil {
			logger.Info("授权文件验证成功")
			logger.Infof("  客户: %s", info.CustomerName)
			logger.Infof("  类型: %s", info.LicenseType)
			if !info.ExpiryDate.IsZero() {
				logger.Infof("  到期日期: %s", info.ExpiryDate.Format("2006-01-02"))
			}
		}
	})
}

// LicenseCheckMiddleware returns an Iris middleware handler that checks license validity.
// It can optionally check for required features.
func LicenseCheckMiddleware(requiredFeatures ...string) iris.Handler {
	return func(ctx iris.Context) {
		// 从 pkg/license 获取当前运行时状态
		currentLicenseInfo, currentLicenseError := license.GetRuntimeLicenseState()

		// 1. 检查运行时状态错误
		if currentLicenseError != nil {
			log.Warnf("拒绝访问 (授权错误): %v, Path: %s", currentLicenseError, ctx.Path())
			ctx.StopWithError(http.StatusForbidden, fmt.Errorf("访问被拒绝: %w", currentLicenseError))
			return
		}
		if currentLicenseInfo == nil {
			log.Warnf("拒绝访问 (授权信息未加载), Path: %s", ctx.Path())
			ctx.StopWithError(http.StatusForbidden, errors.New("访问被拒绝: 授权信息不可用"))
			return
		}

		// 2. 检查是否过期
		if currentLicenseInfo.IsExpired() {
			log.Warnf("拒绝访问 (授权已过期), Path: %s", ctx.Path())
			ctx.StopWithError(http.StatusForbidden, errors.New("访问被拒绝: 授权已过期"))
			return
		}

		// 3. 检查所需功能
		if len(requiredFeatures) > 0 {
			for _, feature := range requiredFeatures {
				if !currentLicenseInfo.HasFeature(feature) {
					errMsg := fmt.Sprintf("缺少必要功能授权: %s", feature)
					log.Warnf("拒绝访问 (%s), Path: %s", errMsg, ctx.Path())
					ctx.StopWithError(http.StatusForbidden, errors.New("访问被拒绝: "+errMsg))
					return
				}
			}
		}

		// 所有检查通过
		ctx.Next()
	}
}

// GetLicenseInfo allows other parts of the application to safely access the verified license info.
// Returns nil if the license is not loaded or invalid.
func GetLicenseInfo() *license.LicenseInfo {
	// 从 pkg/license 获取当前运行时状态
	info, err := license.GetRuntimeLicenseState()
	if err != nil {
		return nil // 如果有错误，则认为授权无效
	}
	return info // 返回获取到的授权信息 (可能是 nil)
}

// LicenseMiddleware 授权验证中间件
// 参数:
//   - config: 可选的授权中间件配置
//
// 返回:
//   - iris.Handler: 中间件处理函数
func LicenseMiddleware(config ...LicenseMiddlewareConfig) iris.Handler {
	// 使用默认配置
	cfg := defaultLicenseMiddlewareConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	// 创建授权配置
	licenseConfig := security.LicenseConfig{
		LicensePath: cfg.LicensePath,
		SecretKey:   cfg.SecretKey,
	}

	return func(ctx iris.Context) {
		// 如果未启用，则跳过
		if !cfg.Enabled {
			ctx.Next()
			return
		}

		// 检查是否为排除的路径
		for _, path := range cfg.ExcludedPaths {
			if MatchPath(ctx.Path(), path) {
				ctx.Next()
				return
			}
		}

		// 检查授权状态
		status, err := security.CheckLicenseStatus(licenseConfig)
		if err != nil || status.Status != security.LICENSE_STATUS_VALID {
			// 授权无效，返回未授权响应
			CustomResponse.Unauthorized(ctx, status.Message)
			return
		}

		// 将授权信息保存到上下文中
		ctx.Values().Set(constant.CONTEXT_LICENSE_STATUS, status)

		// 继续处理请求
		ctx.Next()
	}
}

// CheckLicenseStatus 检查授权状态
// 参数:
//   - ctx: Iris上下文
//
// 返回:
//   - bool: 授权是否有效
func CheckLicenseStatus(ctx iris.Context) bool {
	statusValue := ctx.Values().Get(constant.CONTEXT_LICENSE_STATUS)
	if statusValue == nil {
		return false
	}

	status, ok := statusValue.(*security.LicenseStatus)
	if !ok {
		return false
	}

	return status.Status == security.LICENSE_STATUS_VALID
}

// GetLicenseStatus 获取授权状态
// 参数:
//   - ctx: Iris上下文
//
// 返回:
//   - *security.LicenseStatus: 授权状态
func GetLicenseStatus(ctx iris.Context) *security.LicenseStatus {
	statusValue := ctx.Values().Get(constant.CONTEXT_LICENSE_STATUS)
	if statusValue == nil {
		return nil
	}

	status, ok := statusValue.(*security.LicenseStatus)
	if !ok {
		return nil
	}

	return status
}
