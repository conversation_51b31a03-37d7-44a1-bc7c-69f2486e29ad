<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建波次"
    width="60%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="波次编号" prop="waveNo">
            <el-input
              v-model="formData.waveNo"
              placeholder="留空自动生成"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分配人员" prop="assignedUserId">
            <el-select
              v-model="formData.assignedUserId"
              placeholder="请选择分配人员"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="user in pickingUsers"
                :key="user.id"
                :label="user.nickname"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="预计时长" prop="estimatedDuration">
            <el-input-number
              v-model="formData.estimatedDuration"
              :min="1"
              :max="480"
              placeholder="分钟"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select
              v-model="formData.priority"
              placeholder="请选择优先级"
              style="width: 100%"
            >
              <el-option
                v-for="option in priorityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <!-- 任务预览 -->
      <el-form-item label="包含任务">
        <div class="task-preview">
          <el-tag
            v-for="taskId in selectedTasks"
            :key="taskId"
            closable
            @close="handleRemoveTask(taskId)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            任务 #{{ taskId }}
          </el-tag>
          <div v-if="selectedTasks.length === 0" class="empty-tasks">
            暂无选中的任务
          </div>
        </div>
      </el-form-item>
      
      <!-- 波次统计 -->
      <el-form-item label="波次统计">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="任务数量" :value="selectedTasks.length" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="预计物料数" :value="estimatedItemCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="预计时长" :value="totalEstimatedDuration" suffix="分钟" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="平均优先级" :value="averagePriority" :precision="1" />
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          :disabled="selectedTasks.length === 0"
          @click="handleConfirm"
        >
          创建波次
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, onMounted } from 'vue'
import { ElDialog, ElButton, ElMessage, ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElRow, ElCol, ElTag, ElStatistic } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { WmsUserResp } from '@/api/wms/pickingTask'
import { getPickingUsers } from '@/api/wms/pickingTask'

// 组件接口定义
interface CreateWaveDialogProps {
  selectedTasks: number[]
}

interface CreateWaveDialogEmits {
  confirm: [waveData: any]
  cancel: []
}

// Props 和 Emits
const props = defineProps<CreateWaveDialogProps>()
const emit = defineEmits<CreateWaveDialogEmits>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const pickingUsers = ref<WmsUserResp[]>([])

// 表单数据
const formData = reactive({
  waveNo: '',
  assignedUserId: null as number | null,
  estimatedDuration: null as number | null,
  priority: 3,
  remark: ''
})

// 表单验证规则
const formRules: FormRules = {
  assignedUserId: [
    { required: true, message: '请选择分配人员', trigger: 'change' }
  ],
  estimatedDuration: [
    { required: true, message: '请输入预计时长', trigger: 'blur' }
  ]
}

// 优先级选项
const priorityOptions = [
  { label: '最高', value: 1 },
  { label: '高', value: 2 },
  { label: '中', value: 3 },
  { label: '低', value: 4 },
  { label: '最低', value: 5 }
]

// 计算属性
const selectedTasks = computed(() => props.selectedTasks)

const estimatedItemCount = computed(() => {
  // 这里应该根据实际任务数据计算物料数量
  return selectedTasks.value.length * 5 // 假设每个任务平均5个物料
})

const totalEstimatedDuration = computed(() => {
  // 这里应该根据实际任务数据计算总时长
  return selectedTasks.value.length * 30 // 假设每个任务平均30分钟
})

const averagePriority = computed(() => {
  if (selectedTasks.value.length === 0) return 0
  // 这里应该根据实际任务数据计算平均优先级
  return 3 // 假设平均优先级为3
})

// 表单引用
const formRef = ref<FormInstance>()

// 方法
const loadPickingUsers = async () => {
  try {
    const result = await getPickingUsers()
    pickingUsers.value = result
  } catch (error) {
    ElMessage.error('加载拣货人员失败')
    console.error(error)
  }
}

const handleOpen = () => {
  loadPickingUsers()
  // 自动设置预计时长
  if (formData.estimatedDuration === null) {
    formData.estimatedDuration = totalEstimatedDuration.value
  }
}

const handleClose = () => {
  resetForm()
}

const handleRemoveTask = (taskId: number) => {
  // 这里需要通知父组件移除任务
  ElMessage.info('请在任务列表中取消选择该任务')
}

const handleReset = () => {
  resetForm()
}

const resetForm = () => {
  formData.waveNo = ''
  formData.assignedUserId = null
  formData.estimatedDuration = null
  formData.priority = 3
  formData.remark = ''
  formRef.value?.clearValidate()
}

const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (selectedTasks.value.length === 0) {
      ElMessage.warning('请选择要创建波次的任务')
      return
    }
    
    submitting.value = true
    
    const waveData = {
      waveNo: formData.waveNo || undefined, // 空字符串转为undefined，让后端自动生成
      assignedUserId: formData.assignedUserId,
      estimatedDuration: formData.estimatedDuration,
      priority: formData.priority,
      remark: formData.remark
    }
    
    emit('confirm', waveData)
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.task-preview {
  min-height: 60px;
  padding: 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.empty-tasks {
  color: #999;
  text-align: center;
  line-height: 44px;
}

.dialog-footer {
  text-align: right;
}
</style>
