# WMS出库流程模块

## 概述

WMS出库流程模块是仓库管理系统的核心组件之一，提供完整的出库业务流程管理，包括出库通知单管理、拣货任务管理、库存分配管理和发运单管理等功能。

## 模块架构

```
WMS出库流程模块
├── 出库通知单管理 (Outbound Notification)
├── 拣货任务管理 (Picking Task)
├── 库存分配管理 (Inventory Allocation)
└── 发运单管理 (Shipment)
```

## 核心功能

### 1. 出库通知单管理
- **功能描述**: 管理客户的出库需求，包括订单信息、收货人信息、物料明细等
- **主要特性**:
  - 支持单个和批量创建出库通知单
  - 多级审核流程
  - 自动库存分配
  - 拣货任务生成
  - 状态跟踪和监控
  - Excel导入导出
  - 统计分析

### 2. 拣货任务管理
- **功能描述**: 管理仓库拣货作业，优化拣货路径，提高拣货效率
- **主要特性**:
  - 多种拣货策略（按订单、按物料、按库位）
  - 波次管理
  - 任务分配和调度
  - 拣货路径优化
  - 异常处理
  - Mobile端支持
  - 绩效统计

### 3. 库存分配管理
- **功能描述**: 管理库存的分配和预占，确保库存的合理利用
- **主要特性**:
  - 多种分配策略（FIFO、LIFO、批次优先等）
  - 自动分配算法
  - 库存可用性检查
  - 分配优化
  - 预占管理
  - 分配跟踪

### 4. 发运单管理
- **功能描述**: 管理货物的发运和配送，跟踪物流状态
- **主要特性**:
  - 发运单生成
  - 承运商管理
  - 运费计算
  - 标签打印
  - 跟踪管理
  - 签收确认

## 业务流程

### 标准出库流程

```mermaid
graph TD
    A[创建出库通知单] --> B[审核通知单]
    B --> C[分配库存]
    C --> D[生成拣货任务]
    D --> E[分配拣货员]
    E --> F[开始拣货]
    F --> G[执行拣货]
    G --> H[完成拣货]
    H --> I[创建发运单]
    I --> J[打包]
    J --> K[发运确认]
    K --> L[跟踪配送]
    L --> M[签收确认]
```

### 状态流转

#### 出库通知单状态流转
```
PENDING → APPROVED → ALLOCATED → PICKING → PICKED → SHIPPED → DELIVERED
    ↓         ↓          ↓          ↓        ↓        ↓
CANCELLED CANCELLED  CANCELLED  CANCELLED CANCELLED RETURNED
```

#### 拣货任务状态流转
```
PENDING → ASSIGNED → IN_PROGRESS → COMPLETED
    ↓         ↓           ↓
CANCELLED CANCELLED   CANCELLED
```

#### 库存分配状态流转
```
ALLOCATED → PICKED
    ↓
RELEASED
```

#### 发运单状态流转
```
PREPARING → PACKED → READY → SHIPPED → IN_TRANSIT → DELIVERED
    ↓          ↓       ↓        ↓          ↓
CANCELLED  CANCELLED CANCELLED CANCELLED  RETURNED
```

## 技术架构

### 分层架构
```
┌─────────────────────────────────────┐
│           Controller Layer          │  ← REST API接口层
├─────────────────────────────────────┤
│            Service Layer            │  ← 业务逻辑层
├─────────────────────────────────────┤
│          Repository Layer           │  ← 数据访问层
├─────────────────────────────────────┤
│            Entity Layer             │  ← 实体模型层
└─────────────────────────────────────┘
```

### 核心组件

#### Controller层
- `WmsOutboundNotificationController`: 出库通知单控制器
- `WmsPickingTaskController`: 拣货任务控制器
- `WmsInventoryAllocationController`: 库存分配控制器
- `WmsShipmentController`: 发运单控制器

#### Service层
- `WmsOutboundNotificationService`: 出库通知单业务服务
- `WmsPickingTaskService`: 拣货任务业务服务
- `WmsInventoryAllocationService`: 库存分配业务服务
- `WmsShipmentService`: 发运单业务服务

#### Repository层
- `WmsOutboundNotificationRepository`: 出库通知单数据访问
- `WmsPickingTaskRepository`: 拣货任务数据访问
- `WmsInventoryAllocationRepository`: 库存分配数据访问
- `WmsShipmentRepository`: 发运单数据访问

#### Entity层
- `WmsOutboundNotification`: 出库通知单实体
- `WmsPickingTask`: 拣货任务实体
- `WmsInventoryAllocation`: 库存分配实体
- `WmsShipment`: 发运单实体

## 数据模型

### 核心实体关系
```
WmsOutboundNotification (出库通知单)
    ├── WmsOutboundNotificationDetail (出库通知单明细)
    ├── WmsPickingTask (拣货任务)
    │   └── WmsPickingTaskDetail (拣货任务明细)
    ├── WmsInventoryAllocation (库存分配)
    └── WmsShipment (发运单)
```

### 关键字段

#### 出库通知单
- `notification_no`: 通知单号
- `client_id`: 客户ID
- `client_order_no`: 客户订单号
- `warehouse_id`: 仓库ID
- `status`: 状态
- `required_ship_date`: 要求发货日期

#### 拣货任务
- `task_no`: 任务号
- `notification_id`: 关联出库通知单ID
- `picking_strategy`: 拣货策略
- `wave_no`: 波次号
- `assigned_user_id`: 分配拣货员ID

#### 库存分配
- `outbound_detail_id`: 出库明细ID
- `inventory_id`: 库存ID
- `allocated_qty`: 分配数量
- `allocation_strategy`: 分配策略

#### 发运单
- `shipment_no`: 发运单号
- `picking_task_id`: 关联拣货任务ID
- `carrier_id`: 承运商ID
- `tracking_no`: 运单号
- `shipping_method`: 运输方式

## API文档

### 文档结构
- `docs/api/wms_outbound_api.md`: 完整API文档
- `docs/api/wms_outbound_api_quick_reference.md`: API快速参考
- `docs/api/WMS_Outbound_Postman_Collection.json`: Postman测试集合

### 主要接口

#### 出库通知单
- `POST /api/v1/wms/outbound-notifications`: 创建出库通知单
- `GET /api/v1/wms/outbound-notifications`: 分页查询
- `POST /api/v1/wms/outbound-notifications/{id}/approve`: 审核
- `POST /api/v1/wms/outbound-notifications/{id}/allocate-inventory`: 分配库存

#### 拣货任务
- `POST /api/v1/wms/picking-tasks`: 创建拣货任务
- `POST /api/v1/wms/picking-tasks/{id}/assign`: 分配任务
- `POST /api/v1/wms/picking-tasks/execute-picking`: 执行拣货

#### 库存分配
- `POST /api/v1/wms/inventory-allocations/auto-allocate`: 自动分配
- `POST /api/v1/wms/inventory-allocations/check-availability`: 检查可用性

#### 发运单
- `POST /api/v1/wms/shipments`: 创建发运单
- `POST /api/v1/wms/shipments/{id}/pack`: 打包
- `POST /api/v1/wms/shipments/{id}/ship`: 发运确认

## 配置说明

### 分配策略配置
- `FIFO`: 先进先出
- `LIFO`: 后进先出
- `BATCH_PRIORITY`: 批次优先
- `EXPIRY_DATE_PRIORITY`: 过期日期优先
- `LOCATION_PRIORITY`: 库位优先

### 拣货策略配置
- `BY_ORDER`: 按订单拣货
- `BY_ITEM`: 按物料拣货
- `BY_LOCATION`: 按库位拣货
- `BATCH_PICKING`: 批量拣货

## 部署说明

### 环境要求
- Go 1.19+
- MySQL 8.0+
- Redis 6.0+

### 配置文件
```yaml
# config/config.yaml
wms:
  outbound:
    auto_allocation: true
    picking_strategy: "BY_ORDER"
    wave_size: 50
    max_picking_tasks_per_user: 10
```

### 数据库迁移
```bash
# 执行数据库迁移
go run cmd/migrate.go
```

## 使用示例

### 完整出库流程示例

```bash
# 1. 创建出库通知单
curl -X POST /api/v1/wms/outbound-notifications \
  -H "Authorization: Bearer {token}" \
  -d '{"clientId": 1, "clientOrderNo": "CO001", ...}'

# 2. 审核通知单
curl -X POST /api/v1/wms/outbound-notifications/1/approve \
  -H "Authorization: Bearer {token}"

# 3. 分配库存
curl -X POST /api/v1/wms/outbound-notifications/1/allocate-inventory \
  -H "Authorization: Bearer {token}"

# 4. 生成拣货任务
curl -X POST /api/v1/wms/outbound-notifications/1/generate-picking-task \
  -H "Authorization: Bearer {token}"

# 5. 执行拣货
curl -X POST /api/v1/wms/picking-tasks/execute-picking \
  -H "Authorization: Bearer {token}" \
  -d '{"detailId": 1, "pickedQty": 100}'

# 6. 创建发运单
curl -X POST /api/v1/wms/shipments \
  -H "Authorization: Bearer {token}" \
  -d '{"pickingTaskId": 1, "carrierId": 1, ...}'

# 7. 发运确认
curl -X POST /api/v1/wms/shipments/1/ship \
  -H "Authorization: Bearer {token}"
```

## 监控和日志

### 关键指标
- 出库通知单处理时间
- 拣货任务完成率
- 库存分配成功率
- 发运准时率

### 日志记录
- 所有业务操作都有审计日志
- 错误日志记录详细的错误信息
- 性能日志记录关键操作的执行时间

## 故障排除

### 常见问题
1. **库存分配失败**: 检查库存可用性和分配策略
2. **拣货任务无法分配**: 检查用户权限和任务状态
3. **发运单创建失败**: 检查拣货任务状态和承运商信息

### 调试工具
- API测试工具: Postman集合
- 日志查看: 系统日志和审计日志
- 数据库查询: 直接查询相关表数据

## 扩展开发

### 添加新的分配策略
1. 在`AllocationStrategy`枚举中添加新策略
2. 在`InventoryAllocationService`中实现策略逻辑
3. 更新配置文件和文档

### 添加新的拣货策略
1. 在`PickingStrategy`枚举中添加新策略
2. 在`PickingTaskService`中实现策略逻辑
3. 更新路径优化算法

## 版本历史

- **v1.0.0**: 初始版本，包含基础出库流程功能
- **v1.1.0**: 添加波次管理和路径优化
- **v1.2.0**: 添加Mobile端支持和统计分析

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request
5. 代码审查
6. 合并代码

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
