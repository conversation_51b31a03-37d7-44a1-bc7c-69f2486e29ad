# SkuSelector 物料 SKU 选择器组件

## 概述

SkuSelector 是一个全局的物料 SKU 选择器组件，提供了物料搜索、筛选和选择功能。支持单选和多选模式，集成了字典数据显示和库位信息展示。

## 功能特性

- ✅ 物料搜索和筛选（SKU、名称、物料组、物料分类、状态）
- ✅ 单选/多选模式切换
- ✅ 字典数据显示（物料组、物料分类）
- ✅ 库位信息显示（编码-名称格式）
- ✅ 分页支持
- ✅ 双击快速选择
- ✅ 响应式设计

## 使用方法

### 基本用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="openSkuSelector">选择物料</el-button>

    <!-- SKU选择器 -->
    <SkuSelector
      ref="skuSelectorRef"
      :multiple="false"
      @confirm="handleSkuConfirm"
      @cancel="handleSkuCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import SkuSelector from "@/components/SkuSelector/index.vue";
import type { MtlItemVO } from "@/api/wms/item";

const skuSelectorRef = ref();

const openSkuSelector = () => {
  skuSelectorRef.value.open();
};

const handleSkuConfirm = (selectedItems: MtlItemVO[]) => {
  console.log("选中的物料:", selectedItems);
};

const handleSkuCancel = () => {
  console.log("取消选择");
};
</script>
```

### 多选模式

```vue
<template>
  <SkuSelector
    ref="skuSelectorRef"
    :multiple="true"
    :selected-skus="preSelectedSkus"
    @confirm="handleMultipleConfirm"
  />
</template>

<script setup lang="ts">
const preSelectedSkus = ref(["SKU001", "SKU002"]);

const handleMultipleConfirm = (selectedItems: MtlItemVO[]) => {
  console.log("选中的多个物料:", selectedItems);
};
</script>
```

### 带筛选条件

```vue
<template>
  <SkuSelector
    ref="skuSelectorRef"
    :filters="{ groupCode: 'RAW_MATERIAL', status: 'ACTIVE' }"
    @confirm="handleConfirm"
  />
</template>
```

## Props 属性

| 属性名       | 类型                | 默认值 | 说明              |
| ------------ | ------------------- | ------ | ----------------- |
| multiple     | boolean             | false  | 是否启用多选模式  |
| selectedSkus | string[]            | []     | 预选中的 SKU 列表 |
| filters      | Record<string, any> | {}     | 初始筛选条件      |

## Events 事件

| 事件名  | 参数                       | 说明           |
| ------- | -------------------------- | -------------- |
| confirm | selectedItems: MtlItemVO[] | 确认选择时触发 |
| cancel  | -                          | 取消选择时触发 |

## Methods 方法

| 方法名 | 参数 | 说明             |
| ------ | ---- | ---------------- |
| open   | -    | 打开选择器对话框 |

## 数据结构

### MtlItemVO 物料对象

```typescript
interface MtlItemVO {
  id: number;
  sku: string;
  name: string;
  specification?: string;
  groupCode?: string;
  categoryCode?: string;
  baseUnit: string;
  batchManaged: boolean;
  serialManaged: boolean;
  status: string;
  defaultLocationId?: number;
  // ... 其他字段
}
```

## 样式定制

组件使用了 Element Plus 的设计规范，支持通过 CSS 变量进行样式定制：

```scss
// 自定义样式
.sku-selector {
  --el-dialog-width: 90%; // 调整对话框宽度
}
```

## 注意事项

1. 组件依赖以下 API：

   - `getMtlItemPage` - 获取物料分页数据
   - `getLocationTree` - 获取库位树数据
   - `useDictionary` - 字典数据管理

2. 确保已正确配置字典数据：

   - `material_group` - 物料组字典
   - `material_category` - 物料分类字典

3. 组件会自动加载字典数据和库位数据，首次打开可能有轻微延迟

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基本的物料搜索和选择功能
- 集成字典数据显示
- 支持单选/多选模式
