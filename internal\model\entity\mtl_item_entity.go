package entity

// MtlItemStatus 定义物料状态常量 (确保与 database_schema.md 中的 mtl_item_status ENUM 一致)
type MtlItemStatus string

const (
	ItemStatusActive   MtlItemStatus = "ACTIVE"
	ItemStatusInactive MtlItemStatus = "INACTIVE"
)

// MtlItem 对应数据库中的 mtl_item 表
// 存储物料基础信息
type MtlItem struct {
	AccountBookEntity
	Sku               string        `gorm:"column:sku;type:varchar(100);not null;index;comment:物料编码" json:"sku"`                         // 物料编码 (SKU) - 移除GORM唯一索引，使用数据库部分唯一索引
	Name              string        `gorm:"column:name;type:varchar(255);not null;comment:物料名称" json:"name"`                             // 物料名称
	Description       *string       `gorm:"column:description;type:text;comment:描述" json:"description"`                                  // 描述
	Specification     *string       `gorm:"column:specification;type:varchar(255);comment:规格型号" json:"specification"`                    // 规格型号
	CategoryCode      *string       `gorm:"column:category_code;type:varchar(100);index;comment:分类代码" json:"categoryCode"`               // 分类代码 (存储字典值)
	GroupCode         *string       `gorm:"column:group_code;type:varchar(100);index;comment:物料组代码" json:"groupCode"`                    // 物料组代码 (存储字典值)
	DefaultCustomerID *uint         `gorm:"column:default_customer_id;index;comment:默认客户ID" json:"defaultCustomerId"`                    // 默认客户ID (可能关联 wms_clients 表)
	DefaultSupplierID *uint         `gorm:"column:default_supplier_id;index;comment:默认供应商ID" json:"defaultSupplierId"`                   // 默认供应商ID (预留字段，供应商功能待开发)
	DefaultLocationID *uint         `gorm:"column:default_location_id;index;comment:默认仓库位置ID" json:"defaultLocationId"`                  // 默认仓库位置ID (关联 wms_location 表)
	BaseUnit          string        `gorm:"column:base_unit;type:varchar(20);not null;comment:基本单位" json:"baseUnit"`                     // 基本单位 (如 EA, KG, M)
	ShelfLifeDays     *int          `gorm:"column:shelf_life_days;type:integer;comment:保质期天数" json:"shelfLifeDays"`                      // 保质期天数 (0 表示不管理)
	BatchManaged      bool          `gorm:"column:batch_managed;not null;default:false;comment:是否批次管理" json:"batchManaged"`              // 是否批次管理 (必须)
	SerialManaged     bool          `gorm:"column:serial_managed;not null;default:false;comment:是否序列号管理" json:"serialManaged"`           // 是否序列号管理 (必须)
	StorageCondition  *string       `gorm:"column:storage_condition;type:varchar(100);comment:存储条件" json:"storageCondition"`             // 存储条件
	ImageUrl          *string       `gorm:"column:image_url;type:varchar(500);comment:图片URL" json:"imageUrl"`                            // 图片URL
	WeightKg          *float64      `gorm:"column:weight_kg;type:numeric(10,3);comment:单件重量(kg)" json:"weightKg"`                        // 重量 (kg)
	VolumeM3          *float64      `gorm:"column:volume_m3;type:numeric(10,6);comment:单件体积(m³)" json:"volumeM3"`                        // 体积 (m³)
	LengthM           *float64      `gorm:"column:length_m;type:numeric(10,3);comment:长度(m)" json:"lengthM"`                             // 长度 (m)
	WidthM            *float64      `gorm:"column:width_m;type:numeric(10,3);comment:宽度(m)" json:"widthM"`                               // 宽度 (m)
	HeightM           *float64      `gorm:"column:height_m;type:numeric(10,3);comment:高度(m)" json:"heightM"`                             // 高度 (m)
	Status            MtlItemStatus `gorm:"column:status;type:mtl_item_status;not null;default:'ACTIVE';index;comment:状态" json:"status"` // 状态 (使用正确的ENUM类型名)
	Remark            *string       `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                            // 备注

	PackageUnits []*MtlItemPackageUnit `gorm:"foreignKey:ItemID" json:"packageUnits,omitempty"` // 关联的包装单位信息
}

// TableName 指定数据库表名方法
func (MtlItem) TableName() string {
	return "mtl_item"
}

// 注意: WmsItemUnitConversion 结构定义已从此文件移除，将在步骤 1.3 中创建。
