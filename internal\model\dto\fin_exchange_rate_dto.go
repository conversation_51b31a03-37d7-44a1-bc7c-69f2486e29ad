package dto

import "encoding/json"

// FinExchangeRatePageReq 分页查询请求参数
type FinExchangeRatePageReq struct {
	SourceCurrencyID *uint `json:"fromCurrencyId" form:"fromCurrencyId"` // 源币种ID
	TargetCurrencyID *uint `json:"toCurrencyId" form:"toCurrencyId"`     // 目标币种ID
	Page             int   `json:"page" form:"page"`                     // 页码
	PageSize         int   `json:"pageSize" form:"pageSize"`             // 每页数量
}

// FinExchangeRateCreateReq 创建请求参数
type FinExchangeRateCreateReq struct {
	TenantDTO
	SourceCurrencyID uint        `json:"fromCurrencyId" binding:"required"`
	TargetCurrencyID uint        `json:"toCurrencyId" binding:"required"`
	Rate             json.Number `json:"rate" binding:"required"`
	RateDate         string      `json:"rateDate" binding:"required,datetime=2006-01-02"`
}

// FinExchangeRateUpdateReq 更新请求参数
type FinExchangeRateUpdateReq struct {
	ID uint `json:"id" binding:"required"`
	TenantDTO
	SourceCurrencyID uint        `json:"fromCurrencyId" binding:"required"`
	TargetCurrencyID uint        `json:"toCurrencyId" binding:"required"`
	Rate             json.Number `json:"rate" binding:"required"`
	RateDate         string      `json:"rateDate" binding:"required,datetime=2006-01-02"`
}

// LatestExchangeRateReq 最新汇率查询请求
type LatestExchangeRateReq struct {
	SourceCode string `json:"sourceCode" form:"sourceCode" binding:"required"`
	TargetCode string `json:"targetCode" form:"targetCode" binding:"required"`
	Date       string `json:"date" form:"date"` // 可选，默认为今天
}

// FinExchangeRateLatestReq 查询最新汇率请求
type FinExchangeRateLatestReq struct {
	FromCode string `json:"fromCode" binding:"required"`
	ToCode   string `json:"toCode" binding:"required"`
	Date     string `json:"date" binding:"omitempty"` // 格式 YYYY-MM-DD
}
