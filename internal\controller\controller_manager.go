package controller

import (
	"context"
	"fmt"
	"sync"

	"backend/internal/service"
	"backend/pkg/logger"
)

// ControllerManager 控制器管理器
// 用于集中管理控制器实例和共享资源
type ControllerManager struct {
	// 服务管理器
	serviceManager *service.ServiceManager

	// 控制器缓存
	controllers sync.Map

	// 日志记录器
	logger logger.Logger

	// 上下文
	ctx context.Context
}

// NewControllerManager 创建控制器管理器
// 参数:
//   - serviceManager: 服务管理器
//
// 返回:
//   - *ControllerManager: 控制器管理器实例
func NewControllerManager(serviceManager *service.ServiceManager) *ControllerManager {
	return &ControllerManager{
		serviceManager: serviceManager,
		controllers:    sync.Map{},
		logger:         serviceManager.GetLogger(),
		ctx:            context.Background(),
	}
}

// WithContext 设置上下文
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - *ControllerManager: 控制器管理器实例
func (m *ControllerManager) WithContext(ctx context.Context) *ControllerManager {
	if ctx == nil {
		ctx = context.Background()
	}

	return &ControllerManager{
		serviceManager: m.serviceManager.WithContext(ctx),
		controllers:    sync.Map{},
		logger:         m.logger,
		ctx:            ctx,
	}
}

// GetServiceManager 获取服务管理器
// 返回:
//   - *service.ServiceManager: 服务管理器实例
func (m *ControllerManager) GetServiceManager() *service.ServiceManager {
	return m.serviceManager
}

// GetLogger 获取日志记录器
// 返回:
//   - logger.Logger: 日志记录器
func (m *ControllerManager) GetLogger() logger.Logger {
	return m.logger
}

// SetLogger 设置日志记录器
// 参数:
//   - logger: 日志记录器
//
// 返回:
//   - *ControllerManager: 控制器管理器实例
func (m *ControllerManager) SetLogger(logger logger.Logger) *ControllerManager {
	m.logger = logger
	return m
}

// GetContext 获取上下文
// 返回:
//   - context.Context: 上下文
func (m *ControllerManager) GetContext() context.Context {
	return m.ctx
}

// GetController 获取泛型控制器
// 参数:
//   - factory: 控制器工厂函数
//
// 返回:
//   - C: 控制器实例
func GetController[C any](m *ControllerManager, factory func(*ControllerManager) C) C {
	// 使用类型名称作为缓存键
	typeName := fmt.Sprintf("%T", (*C)(nil))

	// 从缓存中获取
	if controller, ok := m.controllers.Load(typeName); ok {
		return controller.(C)
	}

	// 创建新实例
	controller := factory(m)
	m.controllers.Store(typeName, controller)

	return controller
}

// --- APP Controller Getters --- (Add APP getters here as they are implemented)
// --- 程序框架（非业务层面（包含系统））必须的控制器功能 ↓---

/*
// GetAuthController 获取 AuthController (新增)
// 返回:
//   - *AuthControllerImpl: 认证控制器实例
// NOTE: Temporarily commented out because ServiceManager does not currently provide AuditLogService.
// AuthController is instantiated manually in main.go. This method needs to be revisited
// when AuditLogService is properly integrated into ServiceManager.
func (m *ControllerManager) GetAuthController() *AuthControllerImpl {
	return GetController(m, func(cm *ControllerManager) *AuthControllerImpl {
		// 在工厂函数内部获取 AuthControllerImpl 所需的依赖
		serviceManager := cm.GetServiceManager()

		// 假设 ServiceManager 提供了获取配置的方法 (如果 GetConfig 不存在, 可能需要添加或直接访问字段)
		// 或者 appCfg 可以直接从创建 ControllerManager 的地方传入？
		// 当前 AuthControllerImpl 构造函数需要 appCfg，我们先尝试从 ServiceManager 获取
		// 注意：如果 ServiceManager 没有 GetConfig 方法，这里需要调整
		appCfg := serviceManager.GetConfig() // 假设存在 GetConfig()，或需要添加
		if appCfg == nil {
			cm.GetLogger().Fatal(context.Background(), "无法从 ServiceManager 获取应用配置 (appCfg is nil)")
		}

		// 获取 CaptchaService (AuthService 的依赖)
		captchaService := serviceManager.GetCaptchaService() // 假设 GetCaptchaService 存在
		// 注意: NewAuthService 内部应该处理 captchaService 为 nil 的情况 (如果验证码未启用)

		// 获取 AuthService
		authSvc := service.NewAuthService(serviceManager, appCfg, captchaService) // NewAuthService 接收 CaptchaService

		if authSvc == nil {
			cm.GetLogger().Fatal(context.Background(), "无法获取 AuthService 实例")
		}

		// 获取 AuditLogService, 以匹配 NewAuthControllerImpl 的新签名
		// 这遵循了从 ServiceManager 获取依赖项的既定模式。
		auditSvc := serviceManager.GetAuditLogService()
		if auditSvc == nil {
			cm.GetLogger().Fatal(context.Background(), "无法从 ServiceManager 获取 AuditLogService 实例")
		}

		// 调用 AuthControllerImpl 的构造函数
		return NewAuthControllerImpl(cm, appCfg, authSvc, auditSvc)
	})
}
*/

// GetCaptchaController 获取 CaptchaController (新增)
// 返回:
//   - *CaptchaControllerImpl: 验证码控制器实例
func (m *ControllerManager) GetCaptchaController() *CaptchaControllerImpl {
	return GetController(m, func(cm *ControllerManager) *CaptchaControllerImpl {
		// 工厂函数内部获取依赖
		serviceManager := cm.GetServiceManager()
		appCfg := serviceManager.GetConfig()
		if appCfg == nil {
			cm.GetLogger().Fatal(context.Background(), "为 CaptchaController 获取应用配置失败 (appCfg is nil)")
		}

		// NewCaptchaController 需要 CacheService
		cacheSvc := serviceManager.GetCacheService()
		// 注意：NewCaptchaController 内部会根据 appCfg.Captcha.Enable 判断 captchaSvc 是否必需
		// 这里只做基本获取，构造函数内部会处理 Fatal 错误（如果需要）
		if cacheSvc == nil && appCfg.Captcha.Enable && appCfg.Captcha.Store != "memory" { // 如果启用且存储不是内存，cacheSvc 通常是必须的
			cm.GetLogger().Warn(context.Background(), "CacheService is nil, CaptchaController might fail if not using memory store")
		}

		// 调用 CaptchaController 的构造函数
		return NewCaptchaControllerImpl(cm, appCfg, cacheSvc)
	})
}

// GetLicenseController 获取 LicenseController (新增)
// 返回:
//   - *LicenseControllerImpl: 许可证控制器实例
func (m *ControllerManager) GetLicenseController() *LicenseControllerImpl {
	// 直接使用 NewLicenseController 作为工厂函数，因为它接收 *ControllerManager
	// 并且内部已经处理了 LicenseService 的依赖获取
	return GetController(m, NewLicenseControllerImpl)
}

// GetFileController 获取 FileController (新增)
// 返回:
//   - *FileControllerImpl: 文件控制器实例
func (m *ControllerManager) GetFileController() *FileControllerImpl {
	// 使用重构后的 NewFileControllerImpl 作为工厂函数
	return GetController(m, NewFileControllerImpl)
}

// GetFinCurrencyController 获取币种控制器 (新增)
func (m *ControllerManager) GetFinCurrencyController() *FinCurrencyControllerImpl {
	return GetController(m, NewFinCurrencyControllerImpl)
}

// GetFinTaxRateController 获取税率控制器
func (m *ControllerManager) GetFinTaxRateController() *FinTaxRateControllerImpl {
	return GetController(m, NewFinTaxRateControllerImpl)
}

// GetFinExchangeRateController 获取汇率控制器
func (m *ControllerManager) GetFinExchangeRateController() *FinExchangeRateControllerImpl {
	return GetController(m, NewFinExchangeRateControllerImpl)
}

// GetFiscalPeriodController 获取 FiscalPeriodController (新增)
// 返回:
//   - *FiscalPeriodControllerImpl: 会计期间控制器实例
func (m *ControllerManager) GetFiscalPeriodController() *FiscalPeriodControllerImpl {
	return GetController(m, NewFiscalPeriodControllerImpl)
}

// GetWmsLocationController 获取 WMS 库位控制器
func (m *ControllerManager) GetWmsLocationController() *WmsLocationControllerImpl {
	return GetController(m, NewWmsLocationControllerImpl)
}

// --- SYS Controller Getters ↓--- (Add SYS getters here as they are implemented)

// --- WMS Controller Getters ↓--- (Add WMS getters here as they are implemented)

// GetMtlItemController 获取物料控制器
func (m *ControllerManager) GetMtlItemController() MtlItemController {
	return GetController(m, NewMtlItemController)
}

// GetCrmCustomerController 获取CRM客户控制器
func (m *ControllerManager) GetCrmCustomerController() CrmCustomerController {
	return GetController(m, NewCrmCustomerController)
}

// GetScmSupplierController 获取SCM供应商控制器
func (m *ControllerManager) GetScmSupplierController() ScmSupplierController {
	return GetController(m, NewScmSupplierController)
}

// GetWmsInboundNotificationController 获取WMS入库通知单控制器
func (m *ControllerManager) GetWmsInboundNotificationController() *WmsInboundNotificationControllerImpl {
	return GetController(m, NewWmsInboundNotificationControllerImpl)
}

// GetWmsReceivingRecordController 获取WMS收货记录控制器
func (m *ControllerManager) GetWmsReceivingRecordController() *WmsReceivingRecordControllerImpl {
	return GetController(m, NewWmsReceivingRecordControllerImpl)
}

// GetWmsPutawayTaskController 获取WMS上架任务控制器
func (m *ControllerManager) GetWmsPutawayTaskController() *WmsPutawayTaskControllerImpl {
	return GetController(m, NewWmsPutawayTaskControllerImpl)
}

// GetWmsPutawayTaskDetailController 获取WMS上架任务明细控制器
func (m *ControllerManager) GetWmsPutawayTaskDetailController() *WmsPutawayTaskDetailControllerImpl {
	return GetController(m, NewWmsPutawayTaskDetailControllerImpl)
}

// GetWmsBlindReceivingConfigController 获取盲收配置控制器
func (m *ControllerManager) GetWmsBlindReceivingConfigController() WmsBlindReceivingConfigController {
	return GetController(m, NewWmsBlindReceivingConfigController)
}

// GetWmsBlindReceivingValidationController 获取盲收验证记录控制器
func (m *ControllerManager) GetWmsBlindReceivingValidationController() WmsBlindReceivingValidationController {
	return GetController(m, NewWmsBlindReceivingValidationController)
}

// ==================== 出库流程模块 Controller 获取方法 ====================

// GetWmsOutboundNotificationController 获取出库通知单控制器
func (m *ControllerManager) GetWmsOutboundNotificationController() *WmsOutboundNotificationControllerImpl {
	return GetController(m, NewWmsOutboundNotificationControllerImpl)
}

// GetWmsPickingTaskController 获取拣货任务控制器
func (m *ControllerManager) GetWmsPickingTaskController() *WmsPickingTaskControllerImpl {
	return GetController(m, NewWmsPickingTaskControllerImpl)
}

// GetWmsInventoryAllocationController 获取库存分配控制器
func (m *ControllerManager) GetWmsInventoryAllocationController() *WmsInventoryAllocationControllerImpl {
	return GetController(m, NewWmsInventoryAllocationControllerImpl)
}

// GetWmsShipmentController 获取发运单控制器
func (m *ControllerManager) GetWmsShipmentController() *WmsShipmentControllerImpl {
	return GetController(m, NewWmsShipmentControllerImpl)
}

// ===== 库存管理模块控制器 =====

// GetWmsInventoryQueryController 获取库存查询控制器
func (m *ControllerManager) GetWmsInventoryQueryController() *WmsInventoryQueryControllerImpl {
	return GetController(m, NewWmsInventoryQueryControllerImpl)
}

// GetWmsInventoryAdjustmentController 获取库存调整控制器
func (m *ControllerManager) GetWmsInventoryAdjustmentController() *WmsInventoryAdjustmentControllerImpl {
	return GetController(m, NewWmsInventoryAdjustmentControllerImpl)
}

// GetWmsCycleCountController 获取盘点控制器
func (m *ControllerManager) GetWmsCycleCountController() *WmsCycleCountControllerImpl {
	return GetController(m, NewWmsCycleCountControllerImpl)
}

// GetWmsInventoryAlertController 获取库存预警控制器
func (m *ControllerManager) GetWmsInventoryAlertController() *WmsInventoryAlertControllerImpl {
	return GetController(m, NewWmsInventoryAlertControllerImpl)
}

// GetWmsInventoryController 获取基础库存控制器
func (m *ControllerManager) GetWmsInventoryController() *WmsInventoryControllerImpl {
	return GetController(m, NewWmsInventoryControllerImpl)
}

// GetWmsInventoryTransactionLogController 获取库存事务日志控制器
func (m *ControllerManager) GetWmsInventoryTransactionLogController() *WmsInventoryTransactionLogControllerImpl {
	return GetController(m, NewWmsInventoryTransactionLogControllerImpl)
}
