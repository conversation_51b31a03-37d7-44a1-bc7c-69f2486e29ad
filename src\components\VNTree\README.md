# VNTree 组件

一个基于 Element Plus `el-tree` 封装的通用树形控件组件，简化了数据结构定义和常用功能的集成（如过滤、懒加载），并提供了更好的类型支持。

## Props

继承了 `el-tree` 的大部分 Props (做了部分排除和简化)，并将它们设为可选。常用和自定义的 Props 包括：

| Prop Name             | Type                                                     | Description                                                                 | Default                                                          |
| --------------------- | -------------------------------------------------------- | --------------------------------------------------------------------------- | ---------------------------------------------------------------- |
| `data`                | `TreeNode[]`                                             | 展示数据，**必须提供**。                                                    | `[]`                                                             |
| `nodeKey`             | `string`                                                 | 每个树节点用来作为唯一标识的属性，整棵树唯一。                                | `'id'`                                                           |
| `treeProps`           | `TreeOptionProps`                                        | 配置选项，指定 label, children, disabled, isLeaf 的字段名。                 | `{ label: 'label', children: 'children', disabled: 'disabled', isLeaf: 'isLeaf' }` |
| `showCheckbox`        | `boolean`                                                | 节点是否可被选择 (显示复选框)。                                            | `false`                                                          |
| `defaultExpandAll`    | `boolean`                                                | 是否默认展开所有节点。                                                      | `false`                                                          |
| `filterText`          | `string`                                                 | 传入此 prop 会自动调用 `el-tree` 的 `filter` 方法，使用内置的按 `label` 过滤逻辑。 | `undefined`                                                      |
| `lazyLoad`            | `(node: Node, resolve: (data: TreeNode[]) => void) => void` | 加载子树数据的方法，仅当设置了 `lazy` (通过 `:lazy="!!lazyLoad"` 自动判断)。   | `undefined`                                                      |
| `expandOnClickNode`   | `boolean`                                                | 是否在点击节点的时候展开或者收缩节点。                                        | `true`                                                           |
| `checkOnClickNode`    | `boolean`                                                | 是否在点击节点的时候选中节点。                                              | `false`                                                          |
| `checkStrictly`       | `boolean`                                                | 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法。                    | `false`                                                          |
| `accordion`           | `boolean`                                                | 是否每次只打开一个同级树节点展开。                                            | `false`                                                          |
| `draggable`           | `boolean`                                                | 是否开启拖拽节点功能。                                                      | `false`                                                          |
| ...                   |                                                          | 其他继承自 `el-tree` 的 props 如 `defaultExpandedKeys`, `icon`, `indent` 等。 | (参考 Element Plus 文档)                                          |

### TreeNode 接口

`data` prop 中的每个对象应至少符合 `TreeNode` 接口：

```typescript
interface TreeNode {
  id: string | number; // 节点的唯一标识 (对应 nodeKey, 默认 'id')
  label: string; // 节点显示的文本 (对应 treeProps.label, 默认 'label')
  children?: TreeNode[]; // 子节点 (对应 treeProps.children, 默认 'children')
  disabled?: boolean; // 是否禁用 (对应 treeProps.disabled, 默认 'disabled')
  isLeaf?: boolean; // 是否叶子节点 (用于懒加载) (对应 treeProps.isLeaf, 默认 'isLeaf')
  [key: string]: any; // 允许其他自定义属性
}
```

## Emits

转发了 `el-tree` 的常用事件：

*   `node-click(data, node, event)`
*   `node-contextmenu(event, data, node)`
*   `check-change(data, checked, indeterminate)`
*   `check(data, checkedInfo)`
*   `current-change(data, node)`
*   `node-expand(data, node)`
*   `node-collapse(data, node)`

## Slots

*   `default({ node, data })`: 自定义树节点的内容。`node` 是 Node 对象，`data` 是当前节点的数据。
*   `empty`: 自定义数据为空时的展示内容。

## Expose

组件通过 `defineExpose` 暴露了 `getTreeInstance()` 方法，用于获取内部 `el-tree` 组件的实例。这允许父组件调用 `el-tree` 的所有方法，例如：

```typescript
const treeComponentRef = ref<InstanceType<typeof VNTree> | null>(null);

const getChecked = () => {
  const treeInstance = treeComponentRef.value?.getTreeInstance();
  if (treeInstance) {
    const checkedNodes = treeInstance.getCheckedNodes();
    const checkedKeys = treeInstance.getCheckedKeys();
    console.log('Checked Nodes:', checkedNodes);
    console.log('Checked Keys:', checkedKeys);
  }
};

const setChecked = () => {
   const treeInstance = treeComponentRef.value?.getTreeInstance();
   treeInstance?.setCheckedKeys([/* key array */]);
}
```

## 使用示例

```vue
<template>
  <div>
    <el-input v-model="filterInput" placeholder="Filter keyword" />
    <VNTree 
      ref="vnTreeRef"
      :data="treeData"
      show-checkbox 
      node-key="code" 
      :default-expanded-keys="[1, 2]"
      :filter-text="filterInput"
      :tree-props="{ label: 'name', children: 'subItems' }"
      @check="handleTreeCheck"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNTree from '@/components/VNTree/index.vue';
import type { TreeNode } from '@/components/VNTree/types';
import { ElInput } from 'element-plus';

const filterInput = ref('');
const vnTreeRef = ref<InstanceType<typeof VNTree> | null>(null);

const treeData = ref<TreeNode[]>([
  {
    code: 1,
    name: 'Level one 1',
    subItems: [
      {
        code: 11,
        name: 'Level two 1-1',
        subItems: [
          { code: 111, name: 'Level three 1-1-1' }
        ]
      }
    ]
  },
  // ... more data
]);

const handleTreeCheck = (data: TreeNode, checkedInfo: any) => {
  console.log('Check event:', data, checkedInfo);
  // Get checked keys using exposed instance
  const checkedKeys = vnTreeRef.value?.getTreeInstance()?.getCheckedKeys();
  console.log('Current checked keys:', checkedKeys);
};
</script>
```

## 注意事项

*   组件基于 `element-plus` 的 `ElTree`。
*   `data` prop 必须提供，且数组中的对象应符合 `TreeNode` 接口或通过 `treeProps` 指定字段名。
*   `nodeKey` 属性对性能和节点操作（如 `setCheckedKeys`）至关重要，请确保它是唯一的。
*   通过 `filterText` prop 可以快速实现基于节点 `label` 的前端过滤。
*   懒加载通过 `lazyLoad` prop 实现，需要同时在 `el-tree` 上设置 `:lazy="!!lazyLoad"`。
*   可以使用默认插槽来自定义节点的渲染内容。
*   通过 `ref` 和暴露的 `getTreeInstance()` 方法可以访问 `el-tree` 的所有实例方法。 