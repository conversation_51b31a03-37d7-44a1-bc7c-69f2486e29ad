# 仓储管理系统 (WMS) 功能规划方案

## 核心目标

实现对仓库内 "货" (Items/SKUs)、"位" (Locations) 和 "单" (Documents/Transactions) 的精细化、可视化、实时化管理，提高作业效率，确保库存准确。

## 整体规划思路

按照"基础数据 -> 入库流程 -> 出库流程 -> 库内管理"的顺序规划模块，确保依赖关系清晰。每个环节的核心是库存的准确更新。

## 模块规划与讨论点

### 1. 基础数据模块 (Foundation Data) （已完成）

- **区域与库位管理 (Location Management):** （已完成）
  - **业务需求:** 统一管理仓库内所有物理和逻辑位置，支持灵活的层级结构。
  - **仓库表示说明:** 系统不使用单独的 `WmsWarehouse` 实体。顶层的仓库信息通过 `Location` 实体中 `type` 字段设置为 `'WAREHOUSE'` 的记录来表示。所有与仓库相关的操作（如查询仓库列表、获取仓库名称）都应通过操作 `WmsLocation` 实现。
  - **核心模型 (`model`): `Location` (单一递归模型)**
    - `id`: (PK) 唯一标识
    - `code`: (UK) 位置编码 (唯一，按规则生成)
    - `name`: (Optional) 位置名称
    - `type`: (String, Enum) **位置类型** (关键，区分层级与功能)。例如:
      - `WAREHOUSE`: 仓库 (最高层级)
      - `ZONE`: 存储区/库区 (主要基于物理布局、存储条件或商品特性划分，如 A 区, 冷藏区)
      - `AREA`: 区域 (可选，ZONE 内的进一步细分)
      - `AISLE`: 通道
      - `RACK`: 货架/排
      - `LEVEL`: 层
      - `BIN`: **库位** (货架格子或地面区域)
      - `SLOT`: **储位** (库位内细分单元，**最小库存记录单元**)
      - `FLOOR_SLOT`: 地面储位 (直接记录库存)
      - `DOCK_DOOR`: 月台
      - `STAGING_AREA`: 暂存区
      - `QC_AREA`: 待检区
      - _(允许自定义添加其他类型)_
    - `parent_id`: (FK, Nullable) 指向父级 Location 的 ID (仓库层级为 NULL)。
    - `warehouse_id`: (FK, Not Null) 指向所属顶层仓库 (`type`='WAREHOUSE') 的 ID。
    - `status`: (String, Enum) **状态** (通用属性: `ACTIVE`, `INACTIVE`, `MAINTENANCE`, `COUNTING`)。
    - `remark`: (Text, Nullable) **备注** (通用属性)。
    - **特殊属性 (通用属性，按需在最低层级校验):**
      - `temperature_zone`: (String, Nullable) 温区
      - `hazard_level`: (String, Nullable) 危险品/防火等级
      - `security_level`: (String, Nullable) 安全级别
      - `weight_class`: (String, Nullable) 承重等级
      - `storage_type`: (String, Nullable) 存储类型 (更适用 BIN/SLOT)
      - `requires_equipment`: (String, Nullable) 所需设备
      - `is_pickable`: (Boolean, Default: false) 是否可拣货位置 (通常 SLOT/FLOOR_SLOT 为 true)
      - `is_putaway`: (Boolean, Default: false) 是否可上架位置 (通常 SLOT/FLOOR_SLOT 为 true)
    - **容量定义 (通用属性，主要在 SLOT 级别精确使用):**
      - `max_weight_kg`: (Decimal, Nullable)
      - `max_volume_m3`: (Decimal, Nullable)
      - `max_pallets`: (Integer, Nullable)
      - `max_length_m`, `max_width_m`, `max_height_m`: (Decimal, Nullable)
      - `max_item_units`: (Integer, Nullable)
    - **占用情况 (动态更新，主要在 SLOT 级别计算):**
      - `current_weight_kg`: (Decimal, Default: 0)
      - `current_volume_m3`: (Decimal, Default: 0)
      - `current_pallets`: (Integer, Default: 0)
      - `(可选) current_inventory_count`: (Integer, Default: 0)
    - `created_at`, `updated_at`, `deleted_at`: 标准时间戳
    - `created_by`, `updated_by`: 操作人信息
  - **核心功能/API (`controller`, `service`):** CRUD (适用于所有层级), 批量创建, 树状结构展示, 根据类型/属性/容量查询。
  - **关键逻辑:**
    - **库存记录点:** 库存 (`Inventory`) 始终关联到**允许存放库存**的 `Location.id` (其 `type` 通常是 `SLOT`, `FLOOR_SLOT`, `QC_AREA` 等，而非结构性类型如 `WAREHOUSE`, `ZONE`, `RACK`)。业务逻辑需识别这些类型。
    - **动态层级:** 支持不同仓库定义不同深度的层级结构。
    - **属性继承/校验:** 业务逻辑可实现高层级属性向下继承，并在最低库存单元执行校验。
    - **编码规则:** 实现分段式层级编码，支持自动生成和校验。
- **物料主数据 (Item Master - 必须):** （已完成）
  - **业务需求:** 管理仓库中存储的物料信息。
  - **核心实体/模型 (`model`):**
    - `Item`: 物料。字段：`id`, `sku` (物料编码, 唯一), `name`, `description`, `specification` (规格型号), `category_id` (分类), `base_unit` (基本单位: 如 件, 个), `package_units` (包装单位及换算率: 列表或关联表，支持多级包装如 箱、托盘，需定义与 base_unit 的换算关系), `default_customer_id` (默认客户, 可选), `shelf_life_days` (保质期天数, 0 表示不管理), `batch_managed` (批次管理标识, bool, **必须**), `serial_managed` (序列号管理标识, bool, **必须**), `storage_condition` (存储条件), `image_url`, `weight_kg`, `volume_m3`, `length_m`, `width_m`, `height_m`, `status` (状态: 启用, 禁用)。
  - **核心功能/API (`controller`, `service`):** 物料的 CRUD。
  - **关键点:**
    - **多单位:** 必须支持基本单位和多种包装单位之间的转换。
    - **批次/序列号:** 明确指定物料是否需要按批次或序列号进行跟踪。
    - **基本单位 (Base Unit):**
      - 这是 WMS 中跟踪库存的**最小、最基础**的单位。所有库存数量最终都会换算成这个单位来记录和计算。
      - **定义:** 在 `WmsItem` 实体的 `base_unit` 字段（例如: "PCS", "EA", "KG", "M"）。
      - **作用:** 提供统一标准，确保库存数量一致性，便于计算总库存。核心 `Inventory` 表记录的数量**必须**基于 `base_unit`。
    - **包装单位 (Package Unit):**
      - 为方便操作（收货、上架、存储、拣货、发货）而使用的、包含**多个基本单位**的更大单位。反映物料实际流通和存储中的包装形式。
      - **定义:** 在 `WmsItemPackageUnit` 实体中，关联 `WmsItem`。包含 `unit_name` (如 "BOX", "CTN", "PLT") 和 `conversion_factor` (换算率)。
      - **作用:** 方便用户按实际包装操作，提高效率。
      - **关键:** 每个包装单位**必须**有相对于**基本单位**的明确**换算率 (Conversion Factor)**。例如，1 "BOX" = 12 "PCS"。
      - **WMS 流程中的应用:**
        - **入库:** 操作员可按包装单位录入，系统换算成基本单位记录库存 (Inventory)。
        - **库存查询:** 系统内部存储基本单位，界面可换算回包装单位展示。
        - **出库:** 订单可按包装单位下达，系统换算成基本单位分配库存和扣减。
        - **盘点:** 操作员可按包装单位盘点，系统换算成基本单位比较差异。

### 2. 入库流程模块 (Inbound Process)

#### 详细流程说明

WMS 的入库管理旨在高效、准确地接收货物，并将其放置到合适的存储位置，同时实时更新库存信息。典型的流程包含以下几个主要阶段：

1.  **入库预报/通知 (Inbound Notification / ASN - Advanced Shipping Notice):**

    - **目的:** 让仓库提前知道即将到达的货物信息，以便规划收货资源（人力、月台、设备）和存储空间。
    - **来源:** 通常由供应商、货主（委托客户）或内部系统（如采购系统）创建。可以通过系统接口、EDI、文件导入或手动创建。
    - **核心信息:** 包含预计到货日期、供应商/来源、物料清单（SKU、预期数量、单位、批次号、生产日期等）、关联的采购订单号或客户指令号。
    - **系统状态:** `已计划/待收货 (Planned/Pending Receipt)`

2.  **收货确认 (Goods Receiving):**

    - **目的:** 货物实际到达仓库后，进行物理清点、核对和记录。
    - **触发:** 车辆到达指定月台。
    - **操作:** 仓库人员核对 ASN 单（或 PO 单），卸货，清点实收数量，检查外包装是否完好。将实收信息（包括数量、批次、生产日期等）录入 WMS（通常通过 PDA/RF 设备扫描或在工作站操作）。
    - **关键点:**
      - **差异处理:** 记录并处理实收数量与预期数量的差异（多收、少收）。根据预设规则可能需要上报或审批。
      - **无预报处理:** 对于没有 ASN 的到货（盲收），根据策略处理：拒收、允许现场补录 ASN 后收货、或执行专门的盲收流程。
      - **标签打印 (可选):** 系统可生成并打印收货标签（包含物料信息、批次、数量、收货日期、唯一标识符如 LPN/托盘号），贴在货物或托盘上。
    - **系统状态:** `收货中 (Receiving)` -> `已收货/待检验 (Received/Pending Inspection)` 或 `已收货/待上架 (Received/Pending Putaway)`（如果免检）。
    - **库存影响:** 在收货暂存区（如 `STAGING_AREA` 或 `DOCK_DOOR`）生成临时库存记录，状态为 `待检` 或 `待上架`。

3.  **质量检验 (Quality Inspection - 可选):**

    - **目的:** 对需要检验的物料进行质量检查。
    - **触发:** 收货完成后，如果物料或 ASN 标记需要检验，系统生成检验任务或更新收货记录状态。
    - **操作:** 检验员根据检验标准进行检查，并在 WMS 中记录检验结果（合格、不合格、让步接收等）和不合格原因。
    - **关键点:**
      - **检验规则:** 系统根据预设规则（按物料、供应商、是否新品等）确定是否需要检验以及检验标准。
      - **不合格品处理:** 不合格品通常会被移到指定的不合格品区（如 `QC_AREA` 的特定子区域或 `DAMAGE_ZONE`），库存状态标记为 `冻结` 或 `不合格`，等待后续处理（退货、报废等）。
    - **系统状态:** `待检验 (Pending Inspection)` -> `检验中 (Inspecting)` -> `合格/待上架 (Qualified/Pending Putaway)` 或 `不合格 (Unqualified)`。

4.  **上架 (Putaway):**
    - **目的:** 将已确认收货且合格（或免检）的货物从收货区/检验区移动到最终的存储库位。
    - **触发:** 收货/检验完成后，系统根据收货记录生成上架任务 (`PutawayTask`)。
    - **操作:**
      - **系统推荐库位:** WMS 根据预设的上架策略（如就近原则、固定库位、按区存放、先进先出考虑、库位容量/属性匹配等）推荐一个或多个目标库位。
      - **执行上架:** 操作员（通常使用 PDA/RF）领取上架任务，扫描货物/托盘标签，系统显示推荐库位。操作员将货物运到指定库位，扫描库位条码，扫描货物/托盘条码确认放置，输入或确认数量。
      - **异常处理:** 如果推荐库位已满或不适用，操作员可以在设备上请求新的推荐，或（根据权限）手动选择其他可用库位（系统需校验合法性）。
    - **系统状态:** `待上架 (Pending Putaway)` -> `上架中 (Putting Away)` -> `已完成 (Completed)`。
    - **库存影响:**
      - 扣减收货暂存区的临时库存。
      - **增加目标存储库位的库存**，库存状态更新为 `可用 (Available)`。
      - 记录详细的库存流水 (`InventoryTransaction`)。

---

- **入库通知单 (Inbound Notification / ASN):**
  - **业务需求:** 接收客户的入库指令/通知，预报即将到达仓库的货物信息，用于收货准备。
  - **核心实体/模型 (`model`):**
    - `InboundNotification` (主表): 单号 (唯一)、**委托客户 ID (Client ID)**、**关联客户指令号/源单据号 (Associated Client Instruction/Source Doc No)**、供应商/发货方、预期到货日期、状态 (**例如: 已计划/待收货, 已到货, 收货中, 部分收货, 收货完成, 已关闭, 已取消**)、创建信息、备注。
    - `InboundNotificationDetail` (明细): 关联主表 ID、物料、预期数量、单位、批次号(可选)、生产日期(可选)、备注。
  - **核心功能/API (`controller`, `service`):** CRUD, 状态查询, 可基于客户指令接口/文件创建。
  - **关键要求与配置:**
    - **状态流转逻辑:** 需要明确定义 `InboundNotification` 的生命周期状态及其转换规则。
    - **无预报入库处理 (可配置):** 系统应提供配置项，允许选择如何处理无预报的到货：
      - 选项 1: **严格模式** - 拒收无预报货物。
      - 选项 2: **补录模式** - 允许授权人员在到货后补录 `InboundNotification`。
      - 选项 3: **盲收模式** - 提供专门的盲收功能，允许直接创建 `ReceivingRecord` (需明确所需信息和流程)。
- **暂收送检单 / 收货记录 (Receiving Record / Inspection - 可选):**
  - **业务需求:** 货物实际到达后清点、记录。检验步骤根据物料或策略可选。如果跳过检验，此单据主要作为收货确认。
  - **核心实体/模型 (`model`):**
    - `ReceivingRecord` (主表): 单号 (唯一)、关联通知单号(可选)、供应商、实际到货日期、状态 (**待收货**, **收货中**, **待检验(可选)**, **检验中(可选)**, **合格/收货完成**, **不合格/部分收货**, 已入库)、创建信息、备注。
    - `ReceivingRecordDetail` (明细): 关联主表 ID、物料、实收数量、单位、批次号、生产日期、检验状态(可选)、检验员信息(可选)、收货库位(通常是 `STAGING_AREA` 或 `DOCK_DOOR`)。
  - **核心功能/API (`controller`, `service`):** 创建(可基于通知单)、记录实收信息、(可选)触发或更新检验状态、打印收货标签、(可选)打印送检单。
  - **关键要求与配置:**
    - **检验规则配置:** 系统必须支持灵活配置检验规则 (例如，按物料、供应商、是否新品、抽检比例等决定是否需要检验)。
    - **不合格品处理流程:** 必须定义清晰的不合格品处理流程 (例如，转移至指定不合格品库位 `QC_AREA`/`DAMAGE_ZONE`，更新库存状态，生成处理建议/通知)。
    - **收货差异处理规则:** 必须支持配置收货差异处理规则 (例如，设定差异容忍度，超出时需要审批或记录原因)。
    - **暂存区临时库存:** 货物收货后，必须在对应的收货库位 (`STAGING_AREA`, `DOCK_DOOR`等) 记录临时库存，库存状态反映其实际状态 (如 `RECEIVED`, `PENDING_INSPECTION`, `PENDING_PUTAWAY`)。
- **入库执行单 (Inbound Execution / Putaway):**
  - **业务需求:** 将**已确认收货** (合格/免检) 的货物上架到存储库位，正式增库存。
  - **核心实体/模型 (`model`):**
    - `PutawayTask` (主表): 任务单号 (唯一)、关联**收货记录单号 (`ReceivingRecord.id`)**、状态 (待执行, 执行中, 已完成, 异常挂起)、建议库位、执行人信息。
    - `PutawayTaskDetail` (明细): 关联主表 ID、物料、数量、单位、批次号、生产日期、源库位(收货库位)、**目标库位(实际存储位)**、执行数量。
  - **核心功能/API (`controller`, `service`):**
    - 生成任务 (基于**收货完成**的 `ReceivingRecord`)。
    - 系统推荐库位 (基于物料属性、库位规则、当前占用等)。
    - **执行上架操作 (支持多种模式):**
      - 操作员通过 PDA/RF 设备扫描任务条码领取并执行。
      - 操作员在设备上查看可执行的任务列表，选择任务并执行。
      - 操作员扫描货物/托盘上的条码，系统反向查找对应的上架任务并指导执行。
      - 执行过程中确认目标库位和实际放置数量。
    - **更新库存表 (Inventory)** 和记录库存流水 (InventoryTransaction)。
  - **异常处理 (必须支持):**
    - **库位放不下:** 操作员通过 PDA/RF 上报 -> 系统尝试重新推荐库位 / 允许操作员选择有效替代库位 (需校验) / 任务挂起并通知主管 -> 记录异常及最终库位。
    - **发现货物损坏:** 操作员通过 PDA/RF 上报损坏详情 -> 系统指示将损坏货物移至指定隔离区 (`Location` type 如 `DAMAGE_ZONE` 或 `QC_AREA`) -> 不增加"可用"库存，在隔离库位记录库存并标记为"损坏"或"待处理"状态 -> 调整原上架任务明细 -> (可选)触发后续质损报告/处理流程 -> 记录事件。
    - **上架策略配置 (灵活配置与组合):**
      - 系统应支持配置不同的基础策略（例如：就近空库位优先、固定库位优先、按物料分区/ABC 分类存放、满库位优先填充等）。
      - **允许灵活组合和设置优先级：**
        - 可为不同的**物料组 (Category / ABC Class)** 或特定**物料**设置不同的默认上架策略。
        - 可为仓库的不同**区域 (Zone / Area)** 设置不同的上架策略。
        - 可定义**规则的优先级**，当多个条件或规则适用时，明确执行顺序。
        - 可创建"**规则链**"，系统按顺序尝试不同的规则，直至找到合适库位或无法找到。
      - 系统在推荐库位时，将依据这些详细配置的策略和规则执行。

#### 具体操作示例 (以 PDA/RF 设备为例)

**收货操作:**

1.  **登录:** 操作员登录 WMS 移动端。
2.  **选择功能:** 进入"收货"或"ASN 收货"菜单。
3.  **查找 ASN:**
    - 扫描送货单上的 ASN 条码。
    - 或输入 ASN 单号/PO 号/车牌号等信息查询。
4.  **核对信息:** 系统显示 ASN 详情（物料、预期数量）。
5.  **扫描物料:** 扫描箱子或产品上的条码 (SKU)。
6.  **输入数量:** 系统提示输入实收数量。对于需要批次/序列号管理的物料，还需扫描或输入批次号/序列号。
7.  **处理差异:** 如果实收数量与预期不符，系统可能提示选择差异原因或需要授权。
8.  **重复 5-7:** 直到所有物料清点完毕。
9.  **确认收货:** 点击"完成收货"或类似按钮。
10. **打印标签 (可选):** 系统提示打印收货标签或托盘标签。
11. **放置暂存区:** 将货物移动到指定的收货暂存区或待检区。

**上架操作:**

1.  **登录:** 操作员登录 WMS 移动端。
2.  **选择功能:** 进入"上架"或"上架任务"菜单。
3.  **领取任务:**
    - 系统自动分配一个任务。
    - 或操作员扫描暂存区托盘/货物标签，系统反向查找关联的上架任务。
4.  **查看任务:** 系统显示任务详情：物料、数量、源库位（暂存区）、**推荐目标库位**。
5.  **前往源库位:** 操作员到指定的暂存区。
6.  **扫描确认取货:** 扫描暂存区库位条码（可选），扫描要上架的货物/托盘标签。
7.  **前往目标库位:** 系统可能显示导航路径。操作员将货物运往推荐的目标库位。
8.  **扫描确认放置:** 扫描目标库位条码。
9.  **扫描确认货物:** 再次扫描货物/托盘标签。
10. **确认数量:** 输入或确认上架数量。
11. **完成任务:** 点击"确认上架"或类似按钮。系统更新库存，任务完成。如果还有其他明细，继续执行或领取新任务。
12. **处理异常:** 如库位放不下，点击"库位异常"按钮，系统重新推荐或允许选择新库位（需扫描新库位确认）。

### 3. 出库流程模块 (Outbound Process)

#### 详细流程说明

WMS 的出库管理旨在根据客户订单或内部调拨指令，准确、高效地从仓库中拣选出正确的货物，并完成打包、发运等后续操作，同时实时扣减库存。典型的流程包含以下几个主要阶段：

1.  **出库指令接收/订单创建 (Outbound Notification / Shipment Order):**

    - **目的:** 接收来自外部系统（如 OMS、ERP）或内部请求（如生产领料、调拨）的出库指令，明确需要发运的货物信息。
    - **来源:** 通常由销售订单 (SO)、客户订单、调拨单、生产工单等触发，通过系统接口、EDI、文件导入或手动创建。
    - **核心信息:** 包含订单号、委托客户 ID、最终收货人信息（姓名、地址、联系方式）、要求的发货日期、运输方式、物料清单（SKU、要求数量、单位、指定批次/序列号等）、关联的客户订单号。
    - **系统状态:** `已创建/待处理 (Created/Pending Processing)` 或 `待分配/待拣货 (Pending Allocation/Pending Picking)`。

2.  **库存分配/预占 (Inventory Allocation / Reservation):**

    - **目的:** 根据出库订单的需求，在现有库存中查找并预留满足条件的货物。
    - **触发:** 订单创建后，系统自动或手动触发。
    - **操作:** 系统根据预设的库存分配策略（如 **FIFO 先进先出、LIFO 后进先出、FEFO 先到期先出、指定批次/序列号优先、就近库位** 等）在 `AVAILABLE` 状态的库存中查找匹配的物料。找到后，系统会预占这部分库存（逻辑上将其与该订单绑定），可能更新库存状态为 `已分配/待拣货 (Allocated/Pending Pick)`。
    - **关键点:**
      - **分配策略配置:** 必须支持灵活配置分配策略，并可能按客户、物料或订单类型进行覆盖。
      - **库存不足处理:** 如果可用库存不足以满足订单需求，系统应能标记订单为"部分分配"或"缺货等待"，并可能触发补货或异常处理流程。
    - **系统状态:** （订单）`待分配` -> `已分配/待拣货` 或 `部分分配/缺货`。 (库存) `Available` -> `Allocated/Pending Pick`。

3.  **拣货任务生成 (Picking Task Generation):**

    - **目的:** 将分配好的出库订单明细转化为具体的操作任务，指导仓库人员进行拣货。
    - **触发:** 库存分配成功后，系统根据拣货策略生成拣货任务 (`PickingTask`)。
    - **操作:**
      - **拣货策略:** 系统根据配置的拣货策略（如 **按单拣货、分区拣货、批量拣货、波次拣货**）组织和生成任务。例如，波次拣货会将多个订单按特定规则（如路线、时间窗口、承运商）组合在一起，生成一个波次的拣货任务。
      - **路径优化 (可选):** 系统可以根据仓库布局和任务涉及的库位，优化拣货路径，提高效率。
    - **系统状态:** （订单）`已分配/待拣货` -> `拣货中 (Picking)`。（任务）`待执行 (Pending)`。

4.  **拣货执行 (Picking Execution):**

    - **目的:** 操作人员根据拣货任务，从存储库位取出指定的货物。
    - **触发:** 操作员通过 PDA/RF 设备领取或被分配拣货任务。
    - **操作:** 操作员按照系统指示（或优化后的路径）到达指定库位，扫描库位，扫描物料（可能包括批次/序列号），确认拣货数量。将拣选出的货物放置到指定的容器（如拣货车、料箱）中。
    - **关键点:**
      - **RF/PDA 支持:** 必须支持手持终端操作，包括扫描确认。
      - **异常处理:**
        - **库位无货/数量不足 (Short Pick):** 操作员上报 -> 系统记录差异，可能推荐其他库位，或允许短拣（需配置）。
        - **发现货物破损:** 操作员上报 -> 系统记录破损，可能指示将破损品移至隔离区。
      - **单位转换:** 如果订单单位与库存单位不同，系统应能处理转换。
    - **系统状态:** （任务）`待执行` -> `执行中 (In Progress)` -> `已完成 (Completed)`。
    - **库存影响:**
      - **扣减存储库位的库存**（或更新状态为 `已拣选/待发运 (Picked/Pending Shipment)`）。
      - 在拣货容器或集货区（如 `PACKING_AREA`, `STAGING_AREA`）中可能生成临时库存记录。
      - 记录详细的库存流水 (`InventoryTransaction`)，标记为 `PICKING_CONFIRMATION` 或类似类型。

5.  **复核与打包 (Packing & Verification - 可选但常见):**

    - **目的:** 对拣选出的货物进行二次核对，确保准确无误，并进行打包。
    - **触发:** 拣货完成后，货物被送到复核打包区。
    - **操作:** 复核人员扫描订单号或拣货容器号，逐一扫描货物，系统核对与订单是否一致。核对无误后，选择合适的包装材料进行打包，可能需要称重、量体积，并将包裹信息（如包裹号、重量、尺寸）录入系统。打印装箱单和发货标签。
    - **关键点:**
      - **差异处理:** 复核发现差异时，需要有流程进行处理（查找原因、重新拣货等）。
      - **多包裹处理:** 一个订单可能需要分成多个包裹发出。
    - **系统状态:** （订单/任务）`已拣货/待复核` -> `复核中` -> `已打包/待发运`。

6.  **发运确认 (Shipping Confirmation):**
    - **目的:** 确认货物已装车并发运。
    - **触发:** 包裹打包完成，等待装车或承运商取货。
    - **操作:** 操作员在发运区扫描包裹上的发货标签，或扫描订单号/波次号，确认装车。录入承运商信息和运单号（如果之前未录入）。完成发运操作。
    - **关键点:**
      - **交接:** 与承运商的交接确认。
      - **单据:** 生成发货清单、与承运商的交接单等。
    - **系统状态:** （订单）`已打包/待发运` -> `已发运 (Shipped)`。
    - **库存影响:** （如果之前库存状态是 `Picked` 而非直接扣减）此时正式扣减库存或将库存状态更新为 `Shipped`。

---

- **出库通知单 (Outbound Notification / Shipment Order):**
  - **业务需求:** 接收**委托客户 (Client)** 的出库指令 (通常源自其 **销售订单 SO**)，准备拣货。
  - **核心实体/模型 (`model`):**
    - `OutboundNotification` (主表): 单号 (唯一)、**委托客户 ID (Client ID)**、**关联客户订单号 (Associated Client Order No)**、要求发货日期、状态 (待拣货, 部分拣货, 完成, 已发运, 已取消)、最终收货人信息 (姓名, 地址, 电话等)、运输方式、创建信息。
    - `OutboundNotificationDetail` (明细): 关联主表 ID、物料、要求数量、单位、指定批次(可选)、备注。
  - **核心功能/API (`controller`, `service`):** CRUD, 状态查询, 通常基于客户 SO 接口/文件创建, 库存预占/分配(可选)。
  - **关键策略与配置:**
    - **出库优先级规则 (可配置):** 系统应允许配置订单处理的优先级规则 (例如，基于要求发货日期、客户等级、订单类型、运输方式、接收时间等)，用于决定订单处理顺序。
    - **合并订单处理 (可配置):** 系统应提供选项以启用/禁用订单合并功能，并允许配置合并规则 (例如，基于同一最终收货人、同一路线/区域、要求发货日期区间等)，以优化拣货和发运。
    - **库存分配策略 (可配置):** 系统必须允许配置默认的库存分配策略 (例如 **FIFO, LIFO, FEFO, 指定批次优先**)，并可能支持按**委托客户 (Client)** 或 **物料 (Item)** 进行覆盖配置。系统在生成拣货建议或任务时遵循此策略。
- **出库执行单 (Outbound Execution / Picking & Shipping):**
  - **业务需求:** 生成拣货任务，指导拣货，记录发运。
  - **核心实体/模型 (`model`):**
    - `PickingTask` (主表): 任务单号 (唯一)、关联出库通知单号、状态 (待拣货, 拣货中, 完成, 已复核, 已打包, 已发运)、拣货员信息、复核/打包/发运信息。
    - `PickingTaskDetail` (明细): 关联主表 ID、物料、应拣数量、**实拣数量**、单位、**源库位(从哪里拣)**、批次号、序列号。
    - `Shipment` (可选, 发运信息): 关联单号、承运商、运单号、发运日期、签收状态。
  - **核心功能/API (`controller`, `service`):** 生成任务、系统推荐路径/库位、执行拣货操作（确认库位、数量、批次/序列号）、**更新/扣减库存表**、复核、打包、打印单据、记录发运信息。
  - **关键执行策略与支持:**
    - **拣货策略 (可配置):** 系统必须支持配置不同的拣货策略（例如：**按单拣货、分区拣货、批量拣货、波次拣货**）。系统依据选定策略生成、组织和分配拣货任务 (`PickingTask`)。
    - **波次拣货 (作为策略之一):** 如果选择波次拣货策略，系统应支持创建、管理和释放拣货波次。
    - **路径优化 (可选能力):** 系统**可以**提供拣货路径优化功能。如果启用，系统会根据仓库布局和任务库位推荐最优拣货路径；否则按默认顺序（如库位编码顺序）展示。
    - **RF/PDA 支持 (必须):** 必须支持通过 RF/PDA 手持终端进行拣货、复核、打包、发运等操作（如扫描库位、商品、序列号、运单等）。**同时，必须保留在 PC 端系统界面直接处理相关任务和订单的功能。**
    - **缺货处理 (可配置):** 系统必须允许配置拣货时遇到缺货或库存异常的处理逻辑，例如：
      - 选项 1: **允许短拣** - 记录实际拣货数量，报告差异。
      - 选项 2: **推荐替代库位** - 系统查找并推荐同一物料的其他可用库位。
      - 选项 3: **任务挂起/异常上报** - 暂停任务并通知主管处理。

#### 具体操作示例 (以 PDA/RF 设备为例)

假设操作员使用手持终端执行拣货：

1.  **登录:** 操作员登录 WMS 移动端。
2.  **选择功能:** 进入"拣货任务"或"波次拣货"菜单。
3.  **领取/分配任务:**
    - 系统显示可领取的任务列表（按优先级排序）。
    - 或系统直接推送一个任务给操作员。
4.  **查看任务/路径:** 系统显示任务详情：订单号(或波次号)、第一个拣货库位、物料、数量、批次/序列号（如果需要）。如果启用了路径优化，会按优化顺序显示。
5.  **前往库位:** 操作员根据指示前往第一个拣货库位。
6.  **扫描库位:** 到达后，扫描库位条码进行确认。
7.  **扫描物料:** 扫描指定物料的条码。如果需要特定批次/序列号，系统会提示扫描或显示需要拣选的批次/序列号。
8.  **确认数量:** 输入或扫描确认拣选的数量。
9.  **放置到容器:** 将拣选的货物放入指定的拣货车或料箱。
10. **重复 5-9:** 系统引导至下一个库位和物料，直至任务完成。
11. **处理异常:**
    - **短拣:** 如果库位库存不足，点击"短拣"或"异常"按钮，输入实际拣货数量，选择原因（如库存不准、找不到货）。系统根据配置决定下一步（结束任务、去其他库位补货、或标记异常）。
    - **破损:** 发现货物破损，点击"破损上报"，记录情况。
12. **任务完成:** 所有明细拣选完毕后，点击"完成任务"或类似按钮。
13. **送至集货/打包区:** 将装满货物的拣货车/料箱送到指定的集货区或打包站。

### 4. 库存管理模块 (Inventory Management)

- **核心实体/模型 (`model`):**
  - `Inventory` (库存快照/余额 - **核心**):
    - **关键标识:** `item_id` (FK), `location_id` (FK), `batch_no` (Nullable), `serial_no` (Nullable), `client_id` (FK, 3PL 必须).
    - **数量与单位:** `quantity` (Decimal), `unit_of_measure`.
    - **库存状态 (需明确定义):** `status` (Enum). **系统应预定义全面的状态列表**，例如:
      - `AVAILABLE` (可用)
      - `QUALITY_INSPECTION` (待检)
      - `HOLD` (通用冻结)
      - `FROZEN_QC` (质检冻结), `FROZEN_COUNT` (盘点冻结), `FROZEN_CUSTOMER` (客户冻结)
      - `DAMAGED` (损坏)
      - `EXPIRED` (过期)
      - `PENDING_PUTAWAY` (待上架)
      - `PENDING_PICK` (待拣货/已分配)
      - `IN_TRANSIT` (库内移动中)
      - `PACKING` (打包中)
      - _(允许按需启用或扩展)_
    - **时间属性:** `inbound_date`, `production_date` (Nullable), `expiry_date` (Nullable).
    - **其他:** `warehouse_id` (FK), `created_at`, `updated_at`.
  - `InventoryTransaction` (库存流水 - **核心审计与追溯**):
    - **关键标识:** `id` (PK), `transaction_time` (Timestamp, Indexed), `item_id` (FK), `location_id` (FK), `batch_no`, `serial_no`, `client_id` (FK, 3PL 必须).
    - **变动类型:** `transaction_type` (Enum: 如 `RECEIPT`, `PUTAWAY`, `PICKING_ALLOCATION`, `PICKING_CONFIRMATION`, `ADJUSTMENT_IN`, `ADJUSTMENT_OUT`, `CYCLE_COUNT_GAIN`, `CYCLE_COUNT_LOSS`, `MOVE_OUT`, `MOVE_IN`, `STATUS_CHANGE` 等)。
    - **数量:** `quantity_before` (变动前数量, Decimal), `quantity_change` (变动量, Decimal, 正增负减), `quantity_after` (变动后数量, Decimal), `unit_of_measure`.
    - **状态:** `inventory_status_before` (变动前状态, Enum, Nullable), `inventory_status_after` (变动后状态, Enum).
    - **关联信息:** `associated_document_type` (关联单据类型 Enum), `associated_document_id` (关联单据头 ID), `associated_document_line_id` (关联单据行 ID, Nullable).
    - **操作信息:** `user_id` (操作人, FK), `reason_code` (原因代码, Nullable, 特别用于调整)。
    - **其他:** `warehouse_id` (FK), `created_at`.
- **核心功能/API (`controller`, `service`):** 实时库存查询 (多维度)、库存预警、提供内部接口安全更新库存、(可能)库存移动 API。
- **关键技术要求:**
  - **事务一致性 (必须):** 所有引起 `Inventory` 变更的操作 (收货、上架、拣货、调整、移动、状态变更等) 必须在数据库事务中执行，确保数据操作的原子性 (要么全部成功，要么全部回滚)，严格保证库存数据的准确性。
  - **流水表设计 (`InventoryTransaction`):**
    - 字段设计需全面，满足审计和追溯需求。
    - 设计应考虑未来数据量增长，对 `InventoryTransaction` 表进行**分区 (Partitioning)** 的可能性。虽然初期不一定实施分区，但表结构和查询应支持未来按 **时间范围** (如按月/季度) 或 **委托客户 ID (`client_id`)** 进行分区。 _(具体分区策略需根据数据库类型和预期数据量进一步规划)_
- **盘点计划单 (Cycle Count Plan):**
  - **业务需求:** 安排库存盘点任务。
  - **核心实体/模型 (`model`):**
    - `CycleCountPlan` (主表): 计划单号(唯一)、盘点类型(按库位/物料/动态)、计划日期、状态(计划中, 执行中, 完成, 取消)、负责人、备注。
    - `CycleCountPlanDetail` (明细): 关联主表 ID、盘点范围(库位 ID/物料 ID)、备注。
  - **核心功能/API (`controller`, `service`):** CRUD, 生成计划明细。
  - **关键配置与策略:**
    - **盘点类型 (可配置):** 系统必须支持在创建盘点计划或任务时，选择**明盘 (Revealed Count)** 或 **盲盘 (Blind Count)** 方式。
    - **盘点频率 (可配置):** 系统应允许配置循环盘点的频率规则，例如基于 **物料 ABC 分类、库位属性、固定周期** 等，并能根据规则生成盘点建议或计划。
    - **动态盘点触发 (可配置):** 系统应提供配置项，允许启用/禁用动态盘点，并配置触发条件（例如：**拣货缺货、库位清零、负库存出现** 等）。当触发时，系统自动生成相应的盘点任务。
- **盘点执行单 (Cycle Count Execution):**
  - **业务需求:** 执行盘点，记录实盘数，生成差异报告，调整库存。
  - **核心实体/模型 (`model`):**
    - `CycleCountTask` (盘点任务): 任务单号(唯一)、关联计划单号(可选)、盘点库位/物料、账面数量(明盘时显示)、**实盘数量**、差异数量、盘点员信息、复盘员(可选)、状态(待盘点, 已盘点, 待复核, 已复核, 待调整, 已调整)。
    - `InventoryAdjustment` (库存调整单): 调整单号、关联任务号、调整类型(盘盈/盘亏)、物料、库位、批次、序列号、数量、**调整原因代码 (必须)**、审批人(可选)、执行人信息。
  - **核心功能/API (`controller`, `service`):** 生成/领取任务、记录实盘数、计算差异、(可能)复盘、生成调整单、**审批通过后更新库存表和流水**。
  - **关键流程与配置:**
    - **RF/PDA 支持 (必须):** 必须支持通过 RF/PDA 设备执行盘点任务（扫描库位/物料、输入实盘数量）。
    - **差异处理流程 (可配置):** 系统应允许配置发现盘点差异后的处理流程，例如：
      - 是否需要**复盘 (Recount)** (总是需要 / 超过阈值需要 / 不需要)。
      - 由谁进行复盘 (同一人 / 不同人)。
      - 是否自动生成库存调整建议单。
    - **调整审批流程 (可配置):** 系统应允许配置库存调整（特别是盘点差异调整）是否需要审批，以及：
      - 审批触发条件 (总是需要 / 超过金额或数量阈值需要)。
      - 审批层级和审批角色/用户。
      - 审批通过/拒绝后的系统动作。
    - **调整原因代码 (可配置 & 必须):** 系统需提供可配置的标准调整原因代码列表，并在创建 `InventoryAdjustment` 时，**强制要求**选择一个原因代码。

## 技术和架构考虑

- **数据库设计:** 实体关系、索引优化 (库存表/流水表)、事务保证。
- **API 设计:** RESTful, 清晰请求/响应, 版本控制。
- **权限控制:** 基于现有平台 RBAC, 细化权限, 定义仓库角色。
- **后台任务:** 考虑任务生成、预警等。
- **可扩展性:** 考虑 RF/PDA, 波次, 计费, WCS/TMS 对接等。
- **性能:** 库存查询、单据处理性能。

- **配置管理方案 (待讨论处理):**

  - **问题:** 如何结合静态配置文件 (`config.yaml`) 和动态后台管理系统（数据字典/系统参数）来管理 WMS 的配置项？
  - **核心思路:** 区分启动/基础配置（静态）和业务/操作配置（动态）。
  - **主要方案讨论:**
    - **静态默认 + 动态覆盖 (推荐方案):** `config.yaml` 提供默认值，后台系统（数据字典/系统参数）提供覆盖值。引入 `ConfigService` 统一访问，并实现缓存机制。
    - **完全动态化:** 所有配置存入后台数据库。
    - **明确分离关注点:** 静态配置放 `yaml`，业务配置放后台。
  - **需要明确:** 哪些配置需要动态化？使用数据字典还是系统参数？实时性要求？

- **前端开发规范建议:**

  为 WMS 开发前端（特别是面向仓库操作人员的界面，如 PDA/RF 端或工作站界面）时，应遵循以下规范和建议：

  1.  **UI/UX 设计原则:**

      - **简洁直观:** 界面元素清晰，避免信息过载。操作流程符合直觉，减少学习成本。
      - **任务导向:** 每个界面专注于完成一个核心任务（如收货、上架、拣货、盘点）。**出库拣货界面尤其要突出库位、物料、数量、批次/序列号等关键信息。**
      - **移动优先/响应式:** 优先考虑小屏幕、触摸操作的场景。按钮、输入框等元素要足够大，易于点击。字体清晰可读。能适应不同尺寸的 PDA 屏幕和工作站显示器。
      - **高对比度与清晰字体:** 仓库环境光线可能复杂，确保界面在不同光照下都清晰可见。
      - **明确的状态指示:** 使用颜色、图标、文字清晰地标示单据状态、库存状态、任务状态等。
      - **减少输入:** 尽可能通过扫描完成操作。必须输入时，优化键盘（如数字键盘），提供下拉选择、步进器（数量加减）。利用默认值和历史记录。**出库数量确认要极其便捷。**
      - **及时反馈:** 操作成功或失败都要给出明确、即时的视觉反馈。
      - **高效导航:** 层级不宜过深，常用功能易于访问。
      - **路径指示 (可选):** 如果后端提供拣货路径优化，前端应能清晰展示拣货顺序。
      - **异常处理入口明显:** 拣货时的"短拣"、"破损"、"库位错误"等异常上报按钮要易于找到和使用。
      - **策略适配:** 界面需要能适配不同的拣货/出库策略（按单、波次、批量），UI 流程可能不同。
      - **复核打包界面:** 清晰对比订单与实物，高亮差异，方便录入包裹信息和打印。
      - **发运界面:** 简洁聚焦于扫描确认和运单录入。

  2.  **功能性要求:**

      - **强大的扫描支持:** 与硬件扫描头或摄像头扫描无缝集成。扫描后自动填充、自动跳到下一输入框或自动提交。支持连续扫描。
      - **健壮的错误处理:**
        - 前端输入验证：在提交前进行基本格式、类型、必填项校验。
        - 清晰的错误提示：后端返回错误时，用用户能理解的语言显示错误信息和建议操作，而不是直接显示错误码或技术细节。
        - 错误定位：如果表单校验失败，能高亮显示错误字段。
        - **出库异常处理:** 前端需支持短拣、破损等异常上报，并根据后端指令执行下一步。
      - **性能:**
        - 快速加载：优化代码、资源，减少首次加载时间。
        - 流畅交互：避免卡顿，异步操作（如 API 请求）期间提供加载指示。
        - 高效 API 调用：按需加载数据，避免一次性请求过多数据。合理使用分页、节流/防抖。
      - **离线/弱网考虑 (可选但推荐):** 对于网络不稳定的区域，考虑：
        - 缓存基础数据（如库位信息、物料信息）。
        - 允许离线执行部分操作（如盘点录入），联网后同步。这会显著增加复杂度。
      - **实时性:** 对于需要实时更新的数据（如任务列表、库存看板），考虑使用 WebSocket 或定时轮询（注意性能影响）。

  3.  **技术与架构:**
      - **现代前端框架:** 使用稳定且生态成熟的框架，如 Vue.js, React, 或 Angular，利用其组件化、状态管理等能力。
      - **状态管理:** 对于复杂应用，引入状态管理库（Vuex, Redux, Zustand 等）来统一管理应用状态。
      - **组件库:** 使用或定制一套统一风格、适配移动端的 UI 组件库（如 Ant Design Mobile, Vant UI 等），提高开发效率和一致性。
      - **API 设计协同:** 与后端开发者紧密合作，定义清晰、高效的 API 接口（推荐 RESTful 或 GraphQL）。明确请求参数、响应数据结构和错误码。**出库需要获取任务列表、详情、确认拣货、上报异常、确认打包、确认发运等 API。**
      - **代码规范与质量:**
        - 遵循团队统一的编码规范（ESLint, Prettier）。
        - 编写可维护、可读性高的代码，适当添加注释。
        - 进行单元测试和集成测试。
      - **构建与部署:** 使用 Webpack/Vite 等工具进行打包优化，实现自动化构建和部署流程。

## 已确认开发优先级

1.  **基础数据模块 (Foundation Data)**
2.  **入库流程模块 (Inbound Process)**
3.  **库存管理模块 (Inventory Management)**
4.  **出库流程模块 (Outbound Process)**
5.  **盘点管理模块 (Cycle Count)**
