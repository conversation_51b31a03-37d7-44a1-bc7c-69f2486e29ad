package security

import (
	"bytes"
	"html"
	"io"
	"net/http"
	"regexp"
	"strings"
	"sync"
)

// XSS常量
const (
	// XSS攻击类型
	XSS_ATTACK_TYPE_REFLECTED = "reflected" // 反射型XSS
	XSS_ATTACK_TYPE_STORED    = "stored"    // 存储型XSS
	XSS_ATTACK_TYPE_DOM       = "dom"       // DOM型XSS

	// XSS防护模式
	XSS_MODE_ESCAPE    = "escape"    // 转义模式
	XSS_MODE_SANITIZE  = "sanitize"  // 净化模式
	XSS_MODE_WHITELIST = "whitelist" // 白名单模式
	XSS_MODE_CSP       = "csp"       // CSP模式
	XSS_MODE_HTTP_ONLY = "http_only" // HttpOnly Cookie模式
	XSS_MODE_SECURE    = "secure"    // 安全模式 (组合多种防护)
)

// XSSConfig XSS配置
type XSSConfig struct {
	Enabled             bool                                     `json:"enabled" yaml:"enabled"`                     // 是否启用XSS防护
	Mode                string                                   `json:"mode" yaml:"mode"`                           // 防护模式
	AllowedTags         []string                                 `json:"allowedTags" yaml:"allowedTags"`             // 允许的标签
	AllowedAttributes   []string                                 `json:"allowedAttributes" yaml:"allowedAttributes"` // 允许的属性
	AllowedURLSchemes   []string                                 `json:"allowedUrlSchemes" yaml:"allowedUrlSchemes"` // 允许的URL方案
	SanitizeParams      bool                                     `json:"sanitizeParams" yaml:"sanitizeParams"`       // 是否净化请求参数
	SanitizeBody        bool                                     `json:"sanitizeBody" yaml:"sanitizeBody"`           // 是否净化请求体
	SanitizeHeaders     []string                                 `json:"sanitizeHeaders" yaml:"sanitizeHeaders"`     // 需要净化的头
	EscapeJSON          bool                                     `json:"escapeJson" yaml:"escapeJson"`               // 是否转义JSON
	BlockIFrames        bool                                     `json:"blockIFrames" yaml:"blockIFrames"`           // 是否阻止iframe
	EnableCSP           bool                                     `json:"enableCsp" yaml:"enableCsp"`                 // 是否启用CSP
	CSPDirectives       string                                   `json:"cspDirectives" yaml:"cspDirectives"`         // CSP指令
	EnableXSSAuditor    bool                                     `json:"enableXssAuditor" yaml:"enableXssAuditor"`   // 是否启用XSS审计器
	EnableContentType   bool                                     `json:"enableContentType" yaml:"enableContentType"` // 是否设置Content-Type
	ExcludedPaths       []string                                 `json:"excludedPaths" yaml:"excludedPaths"`         // 排除的路径
	CustomSanitizers    []Sanitizer                              `json:"-" yaml:"-"`                                 // 自定义净化器
	CustomEscapers      []Escaper                                `json:"-" yaml:"-"`                                 // 自定义转义器
	OnXSSAttackDetected func(r *http.Request, attackType string) `json:"-" yaml:"-"`                                 // XSS攻击检测回调
}

// Sanitizer 净化器接口
type Sanitizer interface {
	// Sanitize 净化内容
	Sanitize(content string) string
}

// Escaper 转义器接口
type Escaper interface {
	// Escape 转义内容
	Escape(content string) string
}

// XSS XSS防护
type XSS struct {
	config       XSSConfig
	sanitizers   []Sanitizer
	escapers     []Escaper
	allowedTags  map[string]bool
	allowedAttrs map[string]bool
	allowedURLs  map[string]bool
}

// XSSOption XSS选项函数
type XSSOption func(*XSSConfig)

var (
	defaultXSS *XSS
	xssOnce    sync.Once
)

// 默认的XSS配置
var defaultXSSConfig = XSSConfig{
	Enabled:           true,
	Mode:              XSS_MODE_SANITIZE,
	AllowedTags:       []string{"a", "b", "blockquote", "br", "code", "div", "em", "h1", "h2", "h3", "h4", "h5", "h6", "hr", "i", "li", "ol", "p", "pre", "span", "strong", "table", "tbody", "td", "th", "thead", "tr", "ul"},
	AllowedAttributes: []string{"class", "id", "style", "href", "title", "target", "rel"},
	AllowedURLSchemes: []string{"http", "https", "mailto", "tel"},
	SanitizeParams:    true,
	SanitizeBody:      true,
	SanitizeHeaders:   []string{"Referer", "User-Agent"},
	EscapeJSON:        true,
	BlockIFrames:      true,
	EnableCSP:         true,
	CSPDirectives:     "default-src 'self'; script-src 'self'; object-src 'none'; style-src 'self';",
	EnableXSSAuditor:  true,
	EnableContentType: true,
	ExcludedPaths:     []string{"/api/"},
}

// NewXSS 创建XSS防护
func NewXSS(options ...XSSOption) *XSS {
	config := defaultXSSConfig

	// 应用选项
	for _, option := range options {
		option(&config)
	}

	// 初始化允许的标签、属性和URL方案
	allowedTags := make(map[string]bool)
	allowedAttrs := make(map[string]bool)
	allowedURLs := make(map[string]bool)

	for _, tag := range config.AllowedTags {
		allowedTags[tag] = true
	}

	for _, attr := range config.AllowedAttributes {
		allowedAttrs[attr] = true
	}

	for _, scheme := range config.AllowedURLSchemes {
		allowedURLs[scheme] = true
	}

	// 初始化净化器和转义器
	sanitizers := []Sanitizer{&HTMLSanitizer{
		allowedTags:  allowedTags,
		allowedAttrs: allowedAttrs,
		allowedURLs:  allowedURLs,
	}}

	escapers := []Escaper{&HTMLEscaper{}}

	// 添加自定义净化器和转义器
	sanitizers = append(sanitizers, config.CustomSanitizers...)
	escapers = append(escapers, config.CustomEscapers...)

	return &XSS{
		config:       config,
		sanitizers:   sanitizers,
		escapers:     escapers,
		allowedTags:  allowedTags,
		allowedAttrs: allowedAttrs,
		allowedURLs:  allowedURLs,
	}
}

// Init 初始化XSS防护
func InitXSS(options ...XSSOption) {
	xssOnce.Do(func() {
		defaultXSS = NewXSS(options...)
	})
}

// GetXSS 获取XSS防护实例
func GetXSS() *XSS {
	if defaultXSS == nil {
		InitXSS()
	}
	return defaultXSS
}

// WithMode 设置防护模式
func WithMode(mode string) XSSOption {
	return func(c *XSSConfig) {
		c.Mode = mode
	}
}

// WithAllowedTags 设置允许的标签
func WithAllowedTags(tags ...string) XSSOption {
	return func(c *XSSConfig) {
		c.AllowedTags = tags
	}
}

// WithAllowedAttributes 设置允许的属性
func WithAllowedAttributes(attrs ...string) XSSOption {
	return func(c *XSSConfig) {
		c.AllowedAttributes = attrs
	}
}

// WithAllowedURLSchemes 设置允许的URL方案
func WithAllowedURLSchemes(schemes ...string) XSSOption {
	return func(c *XSSConfig) {
		c.AllowedURLSchemes = schemes
	}
}

// WithCSP 设置CSP
func WithCSP(enabled bool, directives string) XSSOption {
	return func(c *XSSConfig) {
		c.EnableCSP = enabled
		c.CSPDirectives = directives
	}
}

// WithCustomSanitizers 设置自定义净化器
func WithCustomSanitizers(sanitizers ...Sanitizer) XSSOption {
	return func(c *XSSConfig) {
		c.CustomSanitizers = sanitizers
	}
}

// WithCustomEscapers 设置自定义转义器
func WithCustomEscapers(escapers ...Escaper) XSSOption {
	return func(c *XSSConfig) {
		c.CustomEscapers = escapers
	}
}

// WithXSSAttackHandler 设置XSS攻击处理器
func WithXSSAttackHandler(handler func(r *http.Request, attackType string)) XSSOption {
	return func(c *XSSConfig) {
		c.OnXSSAttackDetected = handler
	}
}

// Sanitize 净化内容
func (x *XSS) Sanitize(content string) string {
	if !x.config.Enabled {
		return content
	}

	if x.config.Mode == XSS_MODE_ESCAPE {
		return x.Escape(content)
	}

	for _, sanitizer := range x.sanitizers {
		content = sanitizer.Sanitize(content)
	}
	return content
}

// Escape 转义内容
func (x *XSS) Escape(content string) string {
	if !x.config.Enabled {
		return content
	}

	for _, escaper := range x.escapers {
		content = escaper.Escape(content)
	}
	return content
}

// EscapeHTML 转义HTML
func (x *XSS) EscapeHTML(content string) string {
	return html.EscapeString(content)
}

// EscapeJSON 转义JSON
func (x *XSS) EscapeJSON(content string) string {
	// 这里实现JSON转义，实际项目中可能使用更复杂的处理
	content = strings.Replace(content, "<", "\\u003c", -1)
	content = strings.Replace(content, ">", "\\u003e", -1)
	content = strings.Replace(content, "&", "\\u0026", -1)
	content = strings.Replace(content, "'", "\\u0027", -1)
	content = strings.Replace(content, "\"", "\\u0022", -1)
	return content
}

// Middleware XSS中间件
func (x *XSS) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 如果未启用，直接通过
		if !x.config.Enabled {
			next.ServeHTTP(w, r)
			return
		}

		// 检查是否是排除的路径
		if x.IsExcludedPath(r.URL.Path) {
			next.ServeHTTP(w, r)
			return
		}

		// 设置安全头
		if x.config.EnableCSP {
			w.Header().Set("Content-Security-Policy", x.config.CSPDirectives)
		}
		if x.config.EnableXSSAuditor {
			w.Header().Set("X-XSS-Protection", "1; mode=block")
		}
		if x.config.EnableContentType {
			w.Header().Set("X-Content-Type-Options", "nosniff")
		}
		if x.config.BlockIFrames {
			w.Header().Set("X-Frame-Options", "SAMEORIGIN")
		}

		// 净化请求参数
		if x.config.SanitizeParams {
			r.URL.RawQuery = x.SanitizeQueryParams(r.URL.RawQuery)
		}

		// 净化请求体
		if x.config.SanitizeBody && r.Body != nil {
			body, err := io.ReadAll(r.Body)
			if err == nil {
				sanitizedBody := x.Sanitize(string(body))
				r.Body = io.NopCloser(bytes.NewBufferString(sanitizedBody))
				r.ContentLength = int64(len(sanitizedBody))
			}
			_ = r.Body.Close()
		}

		// 净化头
		for _, header := range x.config.SanitizeHeaders {
			if value := r.Header.Get(header); value != "" {
				r.Header.Set(header, x.Sanitize(value))
			}
		}

		// 创建响应包装器
		responseWrapper := &responseWriter{
			ResponseWriter: w,
			xss:            x,
		}

		// 继续处理请求
		next.ServeHTTP(responseWrapper, r)
	})
}

// 包装响应写入器，用于净化响应体
type responseWriter struct {
	http.ResponseWriter
	xss *XSS
}

// Write 重写写入方法，用于净化响应体
func (rw *responseWriter) Write(b []byte) (int, error) {
	// 检查Content-Type，根据类型进行不同处理
	contentType := rw.Header().Get("Content-Type")

	// 如果是JSON，尝试转义JSON内容
	if rw.xss.config.EscapeJSON && strings.Contains(contentType, "application/json") {
		sanitized := rw.xss.EscapeJSON(string(b))
		return rw.ResponseWriter.Write([]byte(sanitized))
	}

	// 如果是HTML，尝试净化HTML内容
	if strings.Contains(contentType, "text/html") {
		sanitized := rw.xss.Sanitize(string(b))
		return rw.ResponseWriter.Write([]byte(sanitized))
	}

	// 其他内容类型直接写入
	return rw.ResponseWriter.Write(b)
}

// 检查是否是排除的路径
func (x *XSS) IsExcludedPath(path string) bool {
	for _, excludedPath := range x.config.ExcludedPaths {
		if strings.HasPrefix(path, excludedPath) {
			return true
		}
	}
	return false
}

// 净化查询参数
func (x *XSS) SanitizeQueryParams(query string) string {
	parts := strings.Split(query, "&")
	for i, part := range parts {
		kv := strings.SplitN(part, "=", 2)
		if len(kv) == 2 {
			kv[1] = x.Sanitize(kv[1])
			parts[i] = kv[0] + "=" + kv[1]
		}
	}
	return strings.Join(parts, "&")
}

// HTMLSanitizer HTML净化器
type HTMLSanitizer struct {
	allowedTags  map[string]bool
	allowedAttrs map[string]bool
	allowedURLs  map[string]bool
}

// scriptPattern 敏感脚本模式
var scriptPattern = regexp.MustCompile(`(?i)<script[\s\S]*?>[\s\S]*?</script>`)
var eventPattern = regexp.MustCompile(`(?i)(on\w+)=["']?(.*?)["']?(\s|>)`)
var stylePattern = regexp.MustCompile(`(?i)style=["']?.*?(expression|javascript|behavior).*?["']?`)
var javascriptPattern = regexp.MustCompile(`(?i)javascript:`)
var iframePattern = regexp.MustCompile(`(?i)<iframe[\s\S]*?>[\s\S]*?</iframe>`)
var objectPattern = regexp.MustCompile(`(?i)<object[\s\S]*?>[\s\S]*?</object>`)
var embedPattern = regexp.MustCompile(`(?i)<embed[\s\S]*?>[\s\S]*?</embed>`)

// Sanitize 净化HTML内容
func (s *HTMLSanitizer) Sanitize(content string) string {
	// 移除危险的脚本标签
	content = scriptPattern.ReplaceAllString(content, "")

	// 移除事件属性
	content = eventPattern.ReplaceAllString(content, " ")

	// 移除危险的样式
	content = stylePattern.ReplaceAllString(content, " ")

	// 移除JavaScript协议
	content = javascriptPattern.ReplaceAllString(content, "disabled:")

	// 移除iframe标签
	content = iframePattern.ReplaceAllString(content, "")

	// 移除object标签
	content = objectPattern.ReplaceAllString(content, "")

	// 移除embed标签
	content = embedPattern.ReplaceAllString(content, "")

	// 这里可以添加更多的净化规则...
	// 实际项目中应该使用专业的HTML解析库进行更复杂的净化

	return content
}

// HTMLEscaper HTML转义器
type HTMLEscaper struct{}

// Escape 转义HTML内容
func (e *HTMLEscaper) Escape(content string) string {
	return html.EscapeString(content)
}

// 包级函数，方便直接使用

// Sanitize 净化内容
func Sanitize(content string) string {
	return GetXSS().Sanitize(content)
}

// Escape 转义内容
func Escape(content string) string {
	return GetXSS().Escape(content)
}

// EscapeHTML 转义HTML
func EscapeHTML(content string) string {
	return GetXSS().EscapeHTML(content)
}

// EscapeJSON 转义JSON
func EscapeJSON(content string) string {
	return GetXSS().EscapeJSON(content)
}

// Middleware 获取XSS中间件
func XSSMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return GetXSS().Middleware(next)
	}
}

