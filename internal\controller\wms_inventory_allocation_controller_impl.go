package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
)

// WmsInventoryAllocationController 定义库存分配控制器接口
type WmsInventoryAllocationController interface {
	// 基础CRUD操作
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 批量操作
	BatchCreate(ctx iris.Context)
	BatchRelease(ctx iris.Context)
	BatchPick(ctx iris.Context)

	// 自动分配
	AutoAllocate(ctx iris.Context)
	BatchAutoAllocate(ctx iris.Context)

	// 分配优化
	OptimizeAllocation(ctx iris.Context)

	// 拣货确认
	PickConfirm(ctx iris.Context)

	// 释放分配
	ReleaseAllocation(ctx iris.Context)

	// 库存可用性检查
	CheckAvailability(ctx iris.Context)
	BatchCheckAvailability(ctx iris.Context)

	// 库存预占
	ReserveInventory(ctx iris.Context)
	ReleaseReservation(ctx iris.Context)

	// 统计分析
	GetStats(ctx iris.Context)

	// 导出
	ExportToExcel(ctx iris.Context)

	// 业务查询
	GetByOutboundDetailID(ctx iris.Context)
	GetByInventoryID(ctx iris.Context)
	GetAllocationSummary(ctx iris.Context)
}

// wmsInventoryAllocationControllerImpl 库存分配控制器实现
type WmsInventoryAllocationControllerImpl struct {
	BaseControllerImpl
	wmsInventoryAllocationService service.WmsInventoryAllocationService
}

// NewWmsInventoryAllocationControllerImpl 创建库存分配控制器
func NewWmsInventoryAllocationControllerImpl(cm *ControllerManager) *WmsInventoryAllocationControllerImpl {
	return &WmsInventoryAllocationControllerImpl{
		BaseControllerImpl:            *NewBaseController(cm),
		wmsInventoryAllocationService: cm.GetServiceManager().GetWmsInventoryAllocationService(),
	}
}

// Create 创建库存分配
// POST /api/v1/wms/inventory-allocations
func (ctrl *WmsInventoryAllocationControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsInventoryAllocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	var req dto.WmsInventoryAllocationCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsInventoryAllocationService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新库存分配
// PUT /api/v1/wms/inventory-allocations/{id:uint}
func (ctrl *WmsInventoryAllocationControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsInventoryAllocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsInventoryAllocationUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsInventoryAllocationService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除库存分配
// DELETE /api/v1/wms/inventory-allocations/{id:uint}
func (ctrl *WmsInventoryAllocationControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsInventoryAllocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsInventoryAllocationService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个库存分配
// GET /api/v1/wms/inventory-allocations/{id:uint}
func (ctrl *WmsInventoryAllocationControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsInventoryAllocationByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsInventoryAllocationService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取库存分配分页
// GET /api/v1/wms/inventory-allocations
func (ctrl *WmsInventoryAllocationControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsInventoryAllocationsPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	var req dto.WmsInventoryAllocationQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)
	req.PageQuery.PageNum = pageQueryFromBuild.PageNum
	req.PageQuery.PageSize = pageQueryFromBuild.PageSize
	req.PageQuery.Sort = pageQueryFromBuild.Sort

	pageResult, err := ctrl.wmsInventoryAllocationService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// AutoAllocate 自动分配库存
// POST /api/v1/wms/inventory-allocations/auto-allocate
func (ctrl *WmsInventoryAllocationControllerImpl) AutoAllocate(ctx iris.Context) {
	const opName = "AutoAllocateWmsInventory"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	var req dto.WmsInventoryAllocationAutoReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsInventoryAllocationService.AutoAllocate(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// BatchAutoAllocate 批量自动分配库存
// POST /api/v1/wms/inventory-allocations/batch-auto-allocate
func (ctrl *WmsInventoryAllocationControllerImpl) BatchAutoAllocate(ctx iris.Context) {
	const opName = "BatchAutoAllocateWmsInventory"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	var req dto.WmsInventoryAllocationBatchAutoReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsInventoryAllocationService.BatchAutoAllocate(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// PickConfirm 拣货确认
// POST /api/v1/wms/inventory-allocations/{id:uint}/pick-confirm
func (ctrl *WmsInventoryAllocationControllerImpl) PickConfirm(ctx iris.Context) {
	const opName = "PickConfirmWmsInventoryAllocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsInventoryAllocationPickReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsInventoryAllocationService.PickConfirm(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// ReleaseAllocation 释放分配
// POST /api/v1/wms/inventory-allocations/{id:uint}/release
func (ctrl *WmsInventoryAllocationControllerImpl) ReleaseAllocation(ctx iris.Context) {
	const opName = "ReleaseWmsInventoryAllocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsInventoryAllocationReleaseReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsInventoryAllocationService.ReleaseAllocation(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// CheckAvailability 检查库存可用性
// POST /api/v1/wms/inventory-allocations/check-availability
func (ctrl *WmsInventoryAllocationControllerImpl) CheckAvailability(ctx iris.Context) {
	const opName = "CheckAvailabilityWmsInventory"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	var req dto.WmsInventoryAvailabilityCheckReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsInventoryAllocationService.CheckAvailability(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// BatchCheckAvailability 批量检查库存可用性
// POST /api/v1/wms/inventory-allocations/batch-check-availability
func (ctrl *WmsInventoryAllocationControllerImpl) BatchCheckAvailability(ctx iris.Context) {
	const opName = "BatchCheckAvailabilityWmsInventory"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	var req dto.WmsInventoryAvailabilityBatchCheckReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsInventoryAllocationService.BatchCheckAvailability(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetByOutboundDetailID 根据出库明细ID获取分配记录
// GET /api/v1/wms/inventory-allocations/by-outbound-detail/{outboundDetailID:uint}
func (ctrl *WmsInventoryAllocationControllerImpl) GetByOutboundDetailID(ctx iris.Context) {
	const opName = "GetWmsInventoryAllocationByOutboundDetailID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	outboundDetailID, err := ctrl.GetPathUintParamWithError(ctx, "outboundDetailID")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	results, err := ctrl.wmsInventoryAllocationService.GetByOutboundDetailID(ctx.Request().Context(), uint(outboundDetailID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetByInventoryID 根据库存ID获取分配记录
// GET /api/v1/wms/inventory-allocations/by-inventory/{inventoryID:uint}
func (ctrl *WmsInventoryAllocationControllerImpl) GetByInventoryID(ctx iris.Context) {
	const opName = "GetWmsInventoryAllocationByInventoryID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	inventoryID, err := ctrl.GetPathUintParamWithError(ctx, "inventoryID")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	results, err := ctrl.wmsInventoryAllocationService.GetByInventoryID(ctx.Request().Context(), uint(inventoryID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetAllocationSummary 获取分配汇总
// GET /api/v1/wms/inventory-allocations/summary/{outboundDetailID:uint}
func (ctrl *WmsInventoryAllocationControllerImpl) GetAllocationSummary(ctx iris.Context) {
	const opName = "GetWmsInventoryAllocationSummary"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory_allocation")

	outboundDetailID, err := ctrl.GetPathUintParamWithError(ctx, "outboundDetailID")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	result, err := ctrl.wmsInventoryAllocationService.GetAllocationSummary(ctx.Request().Context(), uint(outboundDetailID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}
