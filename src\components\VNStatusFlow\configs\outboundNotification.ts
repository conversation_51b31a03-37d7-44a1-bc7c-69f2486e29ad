/**
 * 出库通知单状态流程配置
 */
import type { StatusFlowConfig } from '../types'

// 出库通知单状态流程配置
export const OUTBOUND_NOTIFICATION_CONFIG: StatusFlowConfig = {
  businessType: 'OUTBOUND_NOTIFICATION',
  
  // 状态选项配置
  statusOptions: [
    {
      value: 'DRAFT',
      label: '草稿',
      color: '#909399',
      bgColor: '#f4f4f5',
      icon: 'Edit',
      description: '通知单草稿状态，可以编辑和修改'
    },
    {
      value: 'PLANNED',
      label: '已计划',
      color: '#409eff',
      bgColor: '#ecf5ff',
      icon: 'Calendar',
      description: '已提交计划，等待审核'
    },
    {
      value: 'APPROVED',
      label: '已审核',
      color: '#67c23a',
      bgColor: '#f0f9ff',
      icon: 'CircleCheck',
      description: '审核通过，可以进行库存分配'
    },
    {
      value: 'ALLOCATING',
      label: '库存分配中',
      color: '#e6a23c',
      bgColor: '#fdf6ec',
      icon: 'Loading',
      description: '正在进行库存分配'
    },
    {
      value: 'ALLOCATED',
      label: '已分配',
      color: '#67c23a',
      bgColor: '#f0f9ff',
      icon: 'Box',
      description: '库存分配完成，可以生成拣货任务'
    },
    {
      value: 'PICKING',
      label: '拣货中',
      color: '#e6a23c',
      bgColor: '#fdf6ec',
      icon: 'Goods',
      description: '正在进行拣货作业'
    },
    {
      value: 'PICKED',
      label: '已拣货',
      color: '#67c23a',
      bgColor: '#f0f9ff',
      icon: 'Select',
      description: '拣货完成，可以进行发运'
    },
    {
      value: 'SHIPPED',
      label: '已发运',
      color: '#409eff',
      bgColor: '#ecf5ff',
      icon: 'Van',
      description: '货物已发运，在途中'
    },
    {
      value: 'COMPLETED',
      label: '已完成',
      color: '#67c23a',
      bgColor: '#f0f9ff',
      icon: 'CircleCheckFilled',
      description: '出库流程全部完成'
    },
    {
      value: 'CANCELLED',
      label: '已取消',
      color: '#f56c6c',
      bgColor: '#fef0f0',
      icon: 'CircleCloseFilled',
      description: '出库通知单已取消'
    }
  ],

  // 状态转换规则
  statusTransitions: [
    {
      from: 'DRAFT',
      to: ['PLANNED', 'CANCELLED'],
      permission: 'outbound:notification:submit'
    },
    {
      from: 'PLANNED',
      to: ['APPROVED', 'DRAFT', 'CANCELLED'],
      permission: 'outbound:notification:approve'
    },
    {
      from: 'APPROVED',
      to: ['ALLOCATING', 'PLANNED', 'CANCELLED'],
      permission: 'outbound:notification:allocate'
    },
    {
      from: 'ALLOCATING',
      to: ['ALLOCATED', 'APPROVED', 'CANCELLED'],
      permission: 'outbound:notification:allocate'
    },
    {
      from: 'ALLOCATED',
      to: ['PICKING', 'APPROVED', 'CANCELLED'],
      permission: 'outbound:notification:picking'
    },
    {
      from: 'PICKING',
      to: ['PICKED', 'ALLOCATED', 'CANCELLED'],
      permission: 'outbound:notification:picking'
    },
    {
      from: 'PICKED',
      to: ['SHIPPED', 'PICKING', 'CANCELLED'],
      permission: 'outbound:notification:ship'
    },
    {
      from: 'SHIPPED',
      to: ['COMPLETED', 'PICKED'],
      permission: 'outbound:notification:complete'
    },
    {
      from: 'COMPLETED',
      to: [], // 完成状态不能转换到其他状态
      permission: ''
    },
    {
      from: 'CANCELLED',
      to: [], // 取消状态不能转换到其他状态
      permission: ''
    }
  ],

  // 显示配置
  displayConfig: {
    showActions: true,
    showProgress: true,
    allowManualTransition: true,
    showDescription: true,
    showIcon: true,
    nodeSize: 'medium',
    lineStyle: 'solid'
  },

  // 操作权限配置
  permissionConfig: {
    'DRAFT_TO_PLANNED': 'outbound:notification:submit',
    'PLANNED_TO_APPROVED': 'outbound:notification:approve',
    'APPROVED_TO_ALLOCATING': 'outbound:notification:allocate',
    'ALLOCATING_TO_ALLOCATED': 'outbound:notification:allocate',
    'ALLOCATED_TO_PICKING': 'outbound:notification:picking',
    'PICKING_TO_PICKED': 'outbound:notification:picking',
    'PICKED_TO_SHIPPED': 'outbound:notification:ship',
    'SHIPPED_TO_COMPLETED': 'outbound:notification:complete',
    'ANY_TO_CANCELLED': 'outbound:notification:cancel'
  },

  // 状态转换中文标签
  transitionLabels: {
    'DRAFT_TO_PLANNED': '提交计划',
    'PLANNED_TO_APPROVED': '审核通过',
    'APPROVED_TO_ALLOCATING': '开始分配',
    'ALLOCATING_TO_ALLOCATED': '分配完成',
    'ALLOCATED_TO_PICKING': '生成拣货任务',
    'PICKING_TO_PICKED': '拣货完成',
    'PICKED_TO_SHIPPED': '确认发运',
    'SHIPPED_TO_COMPLETED': '确认完成',
    'PLANNED_TO_DRAFT': '退回草稿',
    'APPROVED_TO_PLANNED': '退回计划',
    'ALLOCATED_TO_APPROVED': '退回审核',
    'PICKED_TO_PICKING': '退回拣货',
    'SHIPPED_TO_PICKED': '退回拣货',
    'ANY_TO_CANCELLED': '取消'
  },

  // 按钮类型映射
  buttonTypes: {
    'DRAFT_TO_PLANNED': 'primary',
    'PLANNED_TO_APPROVED': 'success',
    'APPROVED_TO_ALLOCATING': 'warning',
    'ALLOCATING_TO_ALLOCATED': 'success',
    'ALLOCATED_TO_PICKING': 'warning',
    'PICKING_TO_PICKED': 'success',
    'PICKED_TO_SHIPPED': 'primary',
    'SHIPPED_TO_COMPLETED': 'success',
    'PLANNED_TO_DRAFT': 'info',
    'APPROVED_TO_PLANNED': 'info',
    'ALLOCATED_TO_APPROVED': 'info',
    'PICKED_TO_PICKING': 'info',
    'SHIPPED_TO_PICKED': 'info',
    'ANY_TO_CANCELLED': 'danger'
  },

  // 按钮图标映射
  buttonIcons: {
    'DRAFT_TO_PLANNED': 'Promotion',
    'PLANNED_TO_APPROVED': 'CircleCheck',
    'APPROVED_TO_ALLOCATING': 'Box',
    'ALLOCATING_TO_ALLOCATED': 'Select',
    'ALLOCATED_TO_PICKING': 'Goods',
    'PICKING_TO_PICKED': 'CircleCheck',
    'PICKED_TO_SHIPPED': 'Van',
    'SHIPPED_TO_COMPLETED': 'CircleCheckFilled',
    'PLANNED_TO_DRAFT': 'Back',
    'APPROVED_TO_PLANNED': 'Back',
    'ALLOCATED_TO_APPROVED': 'Back',
    'PICKED_TO_PICKING': 'Back',
    'SHIPPED_TO_PICKED': 'Back',
    'ANY_TO_CANCELLED': 'Close'
  },

  // API配置
  apiConfig: {
    changeStatusApi: '/wms/outbound-notifications/{id}/status',
    checkPermissionApi: '/wms/outbound-notifications/{id}/permissions'
  }
}

// 导出配置
export default OUTBOUND_NOTIFICATION_CONFIG

// 状态进度计算函数
export const calculateProgress = (status: string): number => {
  const progressMap: Record<string, number> = {
    'DRAFT': 10,
    'PLANNED': 20,
    'APPROVED': 30,
    'ALLOCATING': 40,
    'ALLOCATED': 50,
    'PICKING': 70,
    'PICKED': 80,
    'SHIPPED': 90,
    'COMPLETED': 100,
    'CANCELLED': 0
  }
  return progressMap[status] || 0
}

// 状态颜色获取函数
export const getStatusColor = (status: string): string => {
  const config = OUTBOUND_NOTIFICATION_CONFIG.statusOptions.find(option => option.value === status)
  return config?.color || '#909399'
}

// 状态标签获取函数
export const getStatusLabel = (status: string): string => {
  const config = OUTBOUND_NOTIFICATION_CONFIG.statusOptions.find(option => option.value === status)
  return config?.label || '未知状态'
}

// 检查状态是否可以转换
export const canTransitionTo = (fromStatus: string, toStatus: string): boolean => {
  const transition = OUTBOUND_NOTIFICATION_CONFIG.statusTransitions.find(t => t.from === fromStatus)
  return transition ? transition.to.includes(toStatus) : false
}

// 获取可转换的状态列表
export const getAvailableTransitions = (currentStatus: string): string[] => {
  const transition = OUTBOUND_NOTIFICATION_CONFIG.statusTransitions.find(t => t.from === currentStatus)
  return transition ? transition.to : []
}

// 检查是否为终态
export const isFinalStatus = (status: string): boolean => {
  return ['COMPLETED', 'CANCELLED'].includes(status)
}

// 检查是否可以编辑
export const canEdit = (status: string): boolean => {
  return ['DRAFT', 'PLANNED'].includes(status)
}

// 检查是否可以删除
export const canDelete = (status: string): boolean => {
  return ['DRAFT', 'PLANNED'].includes(status)
}

// 获取状态描述
export const getStatusDescription = (status: string): string => {
  const config = OUTBOUND_NOTIFICATION_CONFIG.statusOptions.find(option => option.value === status)
  return config?.description || ''
}

// 获取状态图标
export const getStatusIcon = (status: string): string => {
  const config = OUTBOUND_NOTIFICATION_CONFIG.statusOptions.find(option => option.value === status)
  return config?.icon || 'Document'
}
