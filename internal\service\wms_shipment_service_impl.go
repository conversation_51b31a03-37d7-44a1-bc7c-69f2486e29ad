package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsShipmentService 定义发运单服务接口
type WmsShipmentService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsShipmentCreateReq) (*vo.WmsShipmentVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsShipmentUpdateReq) (*vo.WmsShipmentVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsShipmentVO, error)
	GetPage(ctx context.Context, req *dto.WmsShipmentQueryReq) (*vo.PageResult[vo.WmsShipmentListVO], error)

	// 批量操作
	BatchCreate(ctx context.Context, req *dto.WmsShipmentBatchCreateReq) ([]*vo.WmsShipmentVO, error)
	BatchShip(ctx context.Context, req *dto.WmsShipmentBatchShipReq) error

	// 发运流程
	PackShipment(ctx context.Context, req *dto.WmsShipmentPackReq) error
	ShipConfirm(ctx context.Context, req *dto.WmsShipmentShipReq) error
	UpdateTrackingStatus(ctx context.Context, req *dto.WmsShipmentTrackReq) error
	DeliveryConfirm(ctx context.Context, req *dto.WmsShipmentDeliverReq) error

	// 运费管理
	CalculateShippingCost(ctx context.Context, req *dto.WmsShipmentCalculateCostReq) (*vo.WmsShipmentCostVO, error)

	// 标签打印
	PrintShippingLabel(ctx context.Context, req *dto.WmsShipmentLabelPrintReq) ([]*vo.WmsShipmentLabelVO, error)

	// 统计分析
	GetStats(ctx context.Context, req *dto.WmsShipmentStatsReq) (*vo.WmsShipmentStatsVO, error)

	// 导出
	ExportToExcel(ctx context.Context, req *dto.WmsShipmentExportReq) ([]byte, error)

	// 业务查询
	GetByShipmentNo(ctx context.Context, shipmentNo string) (*vo.WmsShipmentVO, error)
	GetByTrackingNo(ctx context.Context, trackingNo string) (*vo.WmsShipmentVO, error)
	GetByPickingTaskID(ctx context.Context, pickingTaskID uint) ([]*vo.WmsShipmentVO, error)
	GetPendingPacking(ctx context.Context) ([]*vo.WmsShipmentListVO, error)
	GetReadyToShip(ctx context.Context) ([]*vo.WmsShipmentListVO, error)
	GetInTransit(ctx context.Context) ([]*vo.WmsShipmentListVO, error)
	GetOverdueShipments(ctx context.Context) ([]*vo.WmsShipmentListVO, error)

	// 承运商管理
	GetCarriers(ctx context.Context, req *dto.WmsCarrierQueryReq) ([]*vo.WmsCarrierVO, error)
}

// wmsShipmentServiceImpl 发运单服务实现
type wmsShipmentServiceImpl struct {
	BaseServiceImpl
}

// NewWmsShipmentService 创建发运单服务
func NewWmsShipmentService(sm *ServiceManager) WmsShipmentService {
	return &wmsShipmentServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建发运单
func (s *wmsShipmentServiceImpl) Create(ctx context.Context, req *dto.WmsShipmentCreateReq) (*vo.WmsShipmentVO, error) {
	var shipment *entity.WmsShipment
	var voResult *vo.WmsShipmentVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsShipmentRepository()

		// 验证拣货任务是否存在且状态正确
		pickingTaskRepo := txRepoMgr.GetWmsPickingTaskRepository()
		pickingTask, err := pickingTaskRepo.FindByID(ctx, req.PickingTaskID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务失败").WithCause(err)
		}

		if pickingTask.Status != string(entity.PickingTaskStatusCompleted) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已完成的拣货任务才能创建发运单")
		}

		// 生成发运单号
		shipmentNo, err := s.generateShipmentNo(ctx, req)
		if err != nil {
			return err
		}

		shipment = &entity.WmsShipment{}
		copier.Copy(shipment, req)
		shipment.ShipmentNo = shipmentNo
		shipment.Status = string(entity.ShipmentStatusPreparing) // 新增时默认为准备中状态

		// 强制从上下文获取账套ID - 账套强绑定
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空")
		}
		shipment.AccountBookID = uint(accountBookID)

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}
		shipment.CreatedBy = uint(userID)

		// 创建发运单
		if err := repo.Create(ctx, shipment); err != nil {
			if apperrors.IsDuplicateKeyError(err) {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "发运单号已存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建发运单失败").WithCause(err)
		}

		// 更新出库通知单状态为已打包
		notificationRepo := txRepoMgr.GetWmsOutboundNotificationRepository()
		if err := notificationRepo.UpdateStatus(ctx, pickingTask.NotificationID, string(entity.OutboundStatusPacked), "已创建发运单"); err != nil {
			return fmt.Errorf("更新出库通知单状态失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, shipment)
	return voResult, nil
}

// Update 更新发运单
func (s *wmsShipmentServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsShipmentUpdateReq) (*vo.WmsShipmentVO, error) {
	var voResult *vo.WmsShipmentVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsShipmentRepository()

		// 获取当前记录
		current, err := repo.FindByID(ctx, id)
		if err != nil {
			return fmt.Errorf("查询记录失败: %w", err)
		}

		// 检查状态是否允许修改
		if current.Status == string(entity.ShipmentStatusShipped) ||
			current.Status == string(entity.ShipmentStatusInTransit) ||
			current.Status == string(entity.ShipmentStatusDelivered) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已发运、运输中或已送达的发运单不能修改")
		}

		// 更新字段
		updates := make(map[string]interface{})
		if req.CarrierID != nil {
			updates["carrier_id"] = *req.CarrierID
		}
		if req.CarrierName != nil {
			updates["carrier_name"] = *req.CarrierName
		}
		if req.TrackingNo != nil {
			updates["tracking_no"] = *req.TrackingNo
		}
		if req.ShippingMethod != nil {
			updates["shipping_method"] = *req.ShippingMethod
		}
		if req.ShipmentDate != nil {
			updates["shipment_date"] = *req.ShipmentDate
		}
		if req.EstimatedDelivery != nil {
			updates["estimated_delivery"] = *req.EstimatedDelivery
		}
		if req.ActualDelivery != nil {
			updates["actual_delivery"] = *req.ActualDelivery
		}
		if req.PackageCount != 0 {
			updates["package_count"] = req.PackageCount
		}
		if req.TotalWeight != nil {
			updates["total_weight"] = *req.TotalWeight
		}
		if req.TotalVolume != nil {
			updates["total_volume"] = *req.TotalVolume
		}
		if req.ShippingCost != nil {
			updates["shipping_cost"] = *req.ShippingCost
		}
		if req.InsuranceAmount != nil {
			updates["insurance_amount"] = *req.InsuranceAmount
		}
		if req.ConsigneeName != "" {
			updates["consignee_name"] = req.ConsigneeName
		}
		if req.ConsigneePhone != nil {
			updates["consignee_phone"] = *req.ConsigneePhone
		}
		if req.ConsigneeAddress != nil {
			updates["consignee_address"] = *req.ConsigneeAddress
		}
		if req.Remark != nil {
			updates["remark"] = *req.Remark
		}

		if len(updates) > 0 {
			// 强制从上下文获取用户ID - 用户强绑定
			userID, err := s.GetUserIDFromContext(ctx)
			if err != nil {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
			}
			if userID == 0 {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
			}
			updates["updated_by"] = uint(userID)

			if err := repo.GetDB(ctx).Model(current).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新发运单失败: %w", err)
			}
		}

		// 重新查询完整数据返回
		var result entity.WmsShipment
		if err := repo.GetDB(ctx).First(&result, id).Error; err != nil {
			return fmt.Errorf("查询更新后数据失败: %w", err)
		}

		copier.Copy(&voResult, &result)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return voResult, nil
}

// Delete 删除发运单
func (s *wmsShipmentServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsShipmentRepository()

		shipment, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "发运单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询发运单失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if shipment.Status != string(entity.ShipmentStatusPreparing) &&
			shipment.Status != string(entity.ShipmentStatusReady) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有准备中或待发运状态的发运单才能删除")
		}

		// 删除发运单
		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除发运单失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取发运单
func (s *wmsShipmentServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsShipmentVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipment, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "发运单不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询发运单失败").WithCause(err)
	}

	voResult := &vo.WmsShipmentVO{}
	if err := copier.Copy(voResult, shipment); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "转换数据失败").WithCause(err)
	}

	// 填充扩展信息
	if err := s.fillExtendedInfo(ctx, voResult); err != nil {
		return nil, err
	}

	return voResult, nil
}

// GetPage 获取发运单分页数据
func (s *wmsShipmentServiceImpl) GetPage(ctx context.Context, req *dto.WmsShipmentQueryReq) (*vo.PageResult[vo.WmsShipmentListVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	// 调用Repository的分页查询
	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询发运单分页数据失败").WithCause(err)
	}

	// 转换为VO
	voResult := &vo.PageResult[vo.WmsShipmentListVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsShipmentListVO, len(pageResult.List.([]*entity.WmsShipment))),
	}

	shipments := pageResult.List.([]*entity.WmsShipment)
	for i, shipment := range shipments {
		listVO := &vo.WmsShipmentListVO{}
		copier.Copy(listVO, shipment)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		voResult.List[i] = listVO
	}

	return voResult, nil
}
