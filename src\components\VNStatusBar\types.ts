import type { VNode } from 'vue';

// 定义单个状态项的类型
export interface StatusBarItem {
  id: string | number; // 唯一标识符
  label?: string; // 显示的文本标签
  value?: string | number | VNode; // 显示的值，可以是文本、数字或 VNode (用于自定义渲染)
  icon?: string | object; // 可选图标 (Element Plus 图标组件名或对象)
  tooltip?: string; // 可选，鼠标悬停提示
  separator?: boolean; // 可选，是否在此项后显示分隔符
  onClick?: () => void; // 可选，点击事件处理
  component?: object; // 可选，直接渲染一个自定义组件
  componentProps?: Record<string, any>; // 传递给自定义组件的 props
  align?: 'left' | 'center' | 'right'; // 可选，该项在状态栏中的对齐方式（默认左对齐）
}

// 定义状态栏组件的 Props
export interface VNStatusBarProps {
  items: StatusBarItem[]; // 状态项数组
  height?: string; // 可选，状态栏高度，默认 '30px'
  backgroundColor?: string; // 可选，背景颜色
  textColor?: string; // 可选，文字颜色
  separator?: string | VNode; // 可选，全局分隔符，默认 '|'
  leftTitle?: string; // 可选，左侧区域标题
  centerTitle?: string; // 可选，中间区域标题
  rightTitle?: string; // 可选，右侧区域标题
} 