package repository

import (
	"context"

	"gorm.io/gorm"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsOutboundNotificationDetailRepository 定义出库通知单明细仓库接口
type WmsOutboundNotificationDetailRepository interface {
	BaseRepository[entity.WmsOutboundNotificationDetail, uint]

	// 根据通知单ID查询明细
	FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsOutboundNotificationDetail, error)

	// 根据通知单ID和行号查询明细
	FindByNotificationIDAndLineNo(ctx context.Context, notificationID uint, lineNo int) (*entity.WmsOutboundNotificationDetail, error)

	// 根据物料ID查询明细
	FindByItemID(ctx context.Context, itemID uint) ([]*entity.WmsOutboundNotificationDetail, error)

	// 批量创建明细
	BatchCreate(ctx context.Context, details []*entity.WmsOutboundNotificationDetail) error

	// 批量更新明细
	BatchUpdate(ctx context.Context, details []*entity.WmsOutboundNotificationDetail) error

	// 批量删除明细
	BatchDeleteByNotificationID(ctx context.Context, notificationID uint) error

	// 更新分配数量
	UpdateAllocatedQty(ctx context.Context, id uint, allocatedQty float64) error

	// 更新拣货数量
	UpdatePickedQty(ctx context.Context, id uint, pickedQty float64) error

	// 批量更新分配数量
	BatchUpdateAllocatedQty(ctx context.Context, updates map[uint]float64) error

	// 批量更新拣货数量
	BatchUpdatePickedQty(ctx context.Context, updates map[uint]float64) error

	// 获取未完全分配的明细
	GetUnallocatedDetails(ctx context.Context, notificationID uint) ([]*entity.WmsOutboundNotificationDetail, error)

	// 获取未完全拣货的明细
	GetUnpickedDetails(ctx context.Context, notificationID uint) ([]*entity.WmsOutboundNotificationDetail, error)

	// 根据批次要求查询明细
	FindByBatchRequirement(ctx context.Context, batchNo string) ([]*entity.WmsOutboundNotificationDetail, error)

	// 获取明细统计信息
	GetDetailStats(ctx context.Context, notificationID uint) (map[string]interface{}, error)
}

// wmsOutboundNotificationDetailRepository 出库通知单明细仓库实现
type wmsOutboundNotificationDetailRepository struct {
	BaseRepositoryImpl[entity.WmsOutboundNotificationDetail, uint]
}

// NewWmsOutboundNotificationDetailRepository 创建出库通知单明细仓库
func NewWmsOutboundNotificationDetailRepository(db *gorm.DB) WmsOutboundNotificationDetailRepository {
	return &wmsOutboundNotificationDetailRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsOutboundNotificationDetail, uint]{
			db: db,
		},
	}
}

// FindByNotificationID 根据通知单ID查询明细
func (r *wmsOutboundNotificationDetailRepository) FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsOutboundNotificationDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("notification_id", notificationID),
	}
	sortInfos := []response.SortInfo{
		{Field: "line_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByNotificationIDAndLineNo 根据通知单ID和行号查询明细
func (r *wmsOutboundNotificationDetailRepository) FindByNotificationIDAndLineNo(ctx context.Context, notificationID uint, lineNo int) (*entity.WmsOutboundNotificationDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("notification_id", notificationID),
		NewEqualCondition("line_no", lineNo),
	}

	return r.FindOneByCondition(ctx, conditions)
}

// FindByItemID 根据物料ID查询明细
func (r *wmsOutboundNotificationDetailRepository) FindByItemID(ctx context.Context, itemID uint) ([]*entity.WmsOutboundNotificationDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("item_id", itemID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// BatchCreate 批量创建明细
func (r *wmsOutboundNotificationDetailRepository) BatchCreate(ctx context.Context, details []*entity.WmsOutboundNotificationDetail) error {
	if len(details) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(details, 100) // 每批100条
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建出库通知单明细失败: count=%d", len(details))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建出库通知单明细失败").WithCause(result.Error)
	}

	return nil
}

// BatchUpdate 批量更新明细
func (r *wmsOutboundNotificationDetailRepository) BatchUpdate(ctx context.Context, details []*entity.WmsOutboundNotificationDetail) error {
	if len(details) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for _, detail := range details {
		if err := db.Save(detail).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新出库通知单明细失败: id=%d", detail.ID)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新出库通知单明细失败").WithCause(err)
		}
	}

	return nil
}

// BatchDeleteByNotificationID 批量删除明细
func (r *wmsOutboundNotificationDetailRepository) BatchDeleteByNotificationID(ctx context.Context, notificationID uint) error {
	db := r.GetDB(ctx)
	result := db.Where("notification_id = ?", notificationID).Delete(&entity.WmsOutboundNotificationDetail{})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量删除出库通知单明细失败: notificationID=%d", notificationID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "批量删除出库通知单明细失败").WithCause(result.Error)
	}

	return nil
}

// UpdateAllocatedQty 更新分配数量
func (r *wmsOutboundNotificationDetailRepository) UpdateAllocatedQty(ctx context.Context, id uint, allocatedQty float64) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsOutboundNotificationDetail{}).Where("id = ?", id).Update("allocated_qty", allocatedQty)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新明细分配数量失败: id=%d, allocatedQty=%f", id, allocatedQty)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新明细分配数量失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "明细不存在")
	}

	return nil
}

// UpdatePickedQty 更新拣货数量
func (r *wmsOutboundNotificationDetailRepository) UpdatePickedQty(ctx context.Context, id uint, pickedQty float64) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsOutboundNotificationDetail{}).Where("id = ?", id).Update("picked_qty", pickedQty)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新明细拣货数量失败: id=%d, pickedQty=%f", id, pickedQty)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新明细拣货数量失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "明细不存在")
	}

	return nil
}

// BatchUpdateAllocatedQty 批量更新分配数量
func (r *wmsOutboundNotificationDetailRepository) BatchUpdateAllocatedQty(ctx context.Context, updates map[uint]float64) error {
	if len(updates) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for id, qty := range updates {
		if err := db.Model(&entity.WmsOutboundNotificationDetail{}).Where("id = ?", id).Update("allocated_qty", qty).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新明细分配数量失败: id=%d, qty=%f", id, qty)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新明细分配数量失败").WithCause(err)
		}
	}

	return nil
}

// BatchUpdatePickedQty 批量更新拣货数量
func (r *wmsOutboundNotificationDetailRepository) BatchUpdatePickedQty(ctx context.Context, updates map[uint]float64) error {
	if len(updates) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for id, qty := range updates {
		if err := db.Model(&entity.WmsOutboundNotificationDetail{}).Where("id = ?", id).Update("picked_qty", qty).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新明细拣货数量失败: id=%d, qty=%f", id, qty)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新明细拣货数量失败").WithCause(err)
		}
	}

	return nil
}

// GetUnallocatedDetails 获取未完全分配的明细
func (r *wmsOutboundNotificationDetailRepository) GetUnallocatedDetails(ctx context.Context, notificationID uint) ([]*entity.WmsOutboundNotificationDetail, error) {
	db := r.GetDB(ctx)
	var details []*entity.WmsOutboundNotificationDetail

	result := db.Where("notification_id = ? AND allocated_qty < required_qty", notificationID).
		Order("line_no ASC").
		Find(&details)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("获取未完全分配明细失败: notificationID=%d", notificationID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取未完全分配明细失败").WithCause(result.Error)
	}

	return details, nil
}

// GetUnpickedDetails 获取未完全拣货的明细
func (r *wmsOutboundNotificationDetailRepository) GetUnpickedDetails(ctx context.Context, notificationID uint) ([]*entity.WmsOutboundNotificationDetail, error) {
	db := r.GetDB(ctx)
	var details []*entity.WmsOutboundNotificationDetail

	result := db.Where("notification_id = ? AND picked_qty < required_qty", notificationID).
		Order("line_no ASC").
		Find(&details)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("获取未完全拣货明细失败: notificationID=%d", notificationID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取未完全拣货明细失败").WithCause(result.Error)
	}

	return details, nil
}

// FindByBatchRequirement 根据批次要求查询明细
func (r *wmsOutboundNotificationDetailRepository) FindByBatchRequirement(ctx context.Context, batchNo string) ([]*entity.WmsOutboundNotificationDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("required_batch_no", batchNo),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetDetailStats 获取明细统计信息
func (r *wmsOutboundNotificationDetailRepository) GetDetailStats(ctx context.Context, notificationID uint) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var stats struct {
		TotalItems     int64   `json:"total_items"`
		TotalQty       float64 `json:"total_qty"`
		AllocatedQty   float64 `json:"allocated_qty"`
		PickedQty      float64 `json:"picked_qty"`
		AllocatedItems int64   `json:"allocated_items"`
		PickedItems    int64   `json:"picked_items"`
	}

	// 基础统计
	if err := db.Model(&entity.WmsOutboundNotificationDetail{}).
		Where("notification_id = ?", notificationID).
		Select("COUNT(*) as total_items, SUM(required_qty) as total_qty, SUM(allocated_qty) as allocated_qty, SUM(picked_qty) as picked_qty").
		Scan(&stats).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取明细统计信息失败: notificationID=%d", notificationID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取明细统计信息失败").WithCause(err)
	}

	// 已分配物料种类数
	if err := db.Model(&entity.WmsOutboundNotificationDetail{}).
		Where("notification_id = ? AND allocated_qty > 0", notificationID).
		Count(&stats.AllocatedItems).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取已分配物料种类数失败: notificationID=%d", notificationID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取已分配物料种类数失败").WithCause(err)
	}

	// 已拣货物料种类数
	if err := db.Model(&entity.WmsOutboundNotificationDetail{}).
		Where("notification_id = ? AND picked_qty > 0", notificationID).
		Count(&stats.PickedItems).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取已拣货物料种类数失败: notificationID=%d", notificationID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取已拣货物料种类数失败").WithCause(err)
	}

	result := map[string]interface{}{
		"total_items":     stats.TotalItems,
		"total_qty":       stats.TotalQty,
		"allocated_qty":   stats.AllocatedQty,
		"picked_qty":      stats.PickedQty,
		"allocated_items": stats.AllocatedItems,
		"picked_items":    stats.PickedItems,
	}

	return result, nil
}
