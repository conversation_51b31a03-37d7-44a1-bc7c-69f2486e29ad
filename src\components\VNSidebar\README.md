# VNSidebar 组件

一个基于 Element Plus `el-menu` 的通用侧边导航栏组件，支持多级菜单、图标、路由跳转和折叠功能。

## Props

| Prop Name         | Type                 | Description                             | Default     |
| ----------------- | -------------------- | --------------------------------------- | ----------- |
| `items`           | `SidebarItem[]`      | 侧边栏菜单项配置数组，必须提供。       | `[]`        |
| `defaultOpeneds`  | `string[]`           | 可选，默认展开的 sub-menu 的 index 的数组 | `[]`        |
| `collapse`        | `boolean`            | 可选，是否水平折叠收起菜单            | `false`     |
| `backgroundColor` | `string`             | 可选，菜单背景色 (CSS 颜色值)         | (Element Plus 默认) |
| `textColor`       | `string`             | 可选，菜单文字颜色 (CSS 颜色值)         | (Element Plus 默认) |
| `activeTextColor` | `string`             | 可选，当前激活菜单项的文字颜色         | (Element Plus 默认) |

### SidebarItem 接口

每个 `items` 数组中的对象应符合 `SidebarItem` 接口：

```typescript
import type { RouteLocationRaw } from 'vue-router';

interface SidebarItem {
  label: string; // 菜单项显示的文本
  icon?: string | object; // 可选，菜单项图标 (Element Plus 图标组件名或组件对象)
  to?: RouteLocationRaw; // 可选，vue-router 的路由对象或路径字符串
  children?: SidebarItem[]; // 可选，子菜单项
  index?: string; // 可选，唯一标志，通常用路由路径，用于 el-menu 的 default-active 和 default-openeds
}
```

## 使用示例

```vue
<template>
  <div style="display: flex;">
    <VNSidebar :items="menuItems" :collapse="isCollapsed" style="height: 100vh;" />
    <div style="padding: 20px;">
      <el-button @click="isCollapsed = !isCollapsed">
        {{ isCollapsed ? '展开' : '折叠' }}
      </el-button>
      <router-view></router-view> <!-- 用于显示子路由页面 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNSidebar from '@/components/VNSidebar/index.vue';
import type { SidebarItem } from '@/components/VNSidebar/types';
import { HomeFilled, Setting, Goods } from '@element-plus/icons-vue'; // 引入图标

const isCollapsed = ref(false);

const menuItems = ref<SidebarItem[]>([
  {
    label: '首页',
    icon: HomeFilled,
    index: '/home', // 必须提供 index 用于激活状态和展开
    to: { path: '/home' }
  },
  {
    label: '产品管理',
    icon: Goods,
    index: '/products', // 父菜单 index
    children: [
      {
        label: '产品列表',
        index: '/products/list', // 子菜单 index
        to: { path: '/products/list' }
      },
      {
        label: '添加产品',
        index: '/products/add',
        to: { path: '/products/add' }
      }
    ]
  },
  {
    label: '系统设置',
    icon: Setting,
    index: '/settings', // 父菜单 index
    to: { path: '/settings' }
  }
]);
</script>
```

## 注意事项

* 组件依赖 `element-plus` 和 `vue-router`。
* 为了使 `default-active` 和 `default-openeds` 生效，以及路由激活状态正确高亮，建议为每个 `SidebarItem`（包括父菜单和子菜单）提供唯一的 `index` 属性，通常使用对应的路由路径。
* 如果 `SidebarItem` 有 `children`，它会被渲染为 `el-sub-menu`；否则渲染为 `el-menu-item`。
* `el-menu` 的 `router` 属性已启用，因此带有 `to` 属性的菜单项点击后会自动导航。
* 图标可以通过传递 Element Plus 图标库中的组件对象 (如 `HomeFilled`) 或图标组件的字符串名称来指定。
* 组件的宽度在未折叠时默认为 `200px`，可以通过修改 `style scoped` 中的样式来调整。
