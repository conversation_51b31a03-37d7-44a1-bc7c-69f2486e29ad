# VNDrawer 组件

一个基于 Element Plus `el-drawer` 封装的通用抽屉组件，简化了 `v-model` 控制，并提供了常用的插槽和可配置对齐方式的默认底部按钮。

## Props

继承了 `el-drawer` 的大部分 Props (除了 `modelValue`, `close`)，并将它们设为可选。常用的包括：

*   `direction` ('rtl' | 'ltr' | 'ttb' | 'btt'): Drawer 打开的方向，默认 `'rtl'` (右到左)。
*   `size` (string | number): Drawer 窗体的大小, 当使用 number 类型时, 以像素为单位, 当使用 string 类型时, 请传入 '%'。
*   `title` (string): Drawer 的标题，也可通过具名 slot `header` 覆盖。
*   `with-header` (boolean): 是否显示 header 区域，默认 `true`。
*   `modal` (boolean): 是否需要遮罩层，默认 `true`。
*   `append-to-body` (boolean): Drawer 自身是否插入至 body 元素上。
*   `lock-scroll` (boolean): 是否在 Drawer 出现时将 body 滚动锁定。
*   `close-on-click-modal` (boolean): 是否可以通过点击 modal 关闭 Drawer，默认 `true`。
*   `close-on-press-escape` (boolean): 是否可以通过按下 ESC 关闭 Drawer，默认 `true`。
*   `show-close` (boolean): 是否显示关闭按钮，默认 `true`。
*   `destroy-on-close` (boolean): 关闭时销毁 Drawer 中的元素。

**自定义 Props:**

| Prop Name              | Type                        | Description                            | Default   |
| ---------------------- | --------------------------- | -------------------------------------- | --------- |
| `modelValue`           | `boolean`                   | 控制抽屉显示与隐藏 (v-model)           | `false`   |
| `showConfirmButton`    | `boolean`                   | 是否显示默认确认按钮 (footer slot 优先) | `false`   |
| `showCancelButton`     | `boolean`                   | 是否显示默认取消按钮 (footer slot 优先) | `true`    |
| `confirmButtonText`    | `string`                    | 确认按钮文字                           | `'确认'`   |
| `cancelButtonText`     | `string`                    | 取消按钮文字                           | `'取消'`   |
| `confirmButtonLoading` | `boolean`                   | 确认按钮加载状态                       | `false`   |
| `footerAlign`          | `'left' \| 'center' \| 'right'` | 默认底部按钮对齐方式                   | `'right'`  |

## Emits

*   `update:modelValue(value: boolean)`: `v-model` 值改变时触发。
*   `open`: Drawer 打开的回调。
*   `opened`: Drawer 打开动画结束时的回调。
*   `close`: Drawer 关闭的回调 (点击关闭按钮或遮罩层时触发)。
*   `closed`: Drawer 关闭动画结束时的回调。
*   `confirm`: 点击默认确认按钮时触发。**注意：默认确认按钮不会自动关闭抽屉。**
*   `cancel`: 点击默认取消按钮时触发 (默认行为会同时关闭抽屉)。

## Slots

*   `default`: 抽屉主体内容区域。
*   `header`: 自定义抽屉标题区域。如果使用此插槽，`title` prop 将被忽略。
*   `footer`: 自定义抽屉底部区域。如果使用此插槽，默认的确认和取消按钮将不会显示。

## 使用示例

```vue
<template>
  <div>
    <el-button @click="basicDrawerVisible = true">打开基础抽屉 (右侧)</el-button>
    <el-button @click="customDrawerVisible = true">打开自定义底部抽屉 (左侧)</el-button>

    <VNDrawer 
      v-model="basicDrawerVisible"
      title="基础抽屉"
      size="40%"
      @confirm="handleBasicConfirm"
      :show-confirm-button="true" 
    >
      <p>一些抽屉内容...</p>
    </VNDrawer>

    <VNDrawer
      v-model="customDrawerVisible"
      direction="ltr"
      title="自定义底部和头部"
      size="500px"
    >
      <template #header>
        <h4><i class="el-icon-setting"></i> 设置</h4>
      </template>
      <p>主要内容区域</p>
       <p>...</p>
      <template #footer>
         <div style="display: flex; justify-content: space-between;">
            <el-button type="danger">危险操作</el-button>
            <div>
                <el-button @click="customDrawerVisible = false">关闭</el-button>
                <el-button type="primary">保存</el-button>
            </div>
         </div>
      </template>
    </VNDrawer>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNDrawer from '@/components/VNDrawer/index.vue';
import { ElButton, ElMessage } from 'element-plus';

const basicDrawerVisible = ref(false);
const customDrawerVisible = ref(false);

const handleBasicConfirm = () => {
  ElMessage.success('点击了确认');
  basicDrawerVisible.value = false; // 手动关闭
};
</script>
```

## 注意事项

*   组件基于 `element-plus` 的 `ElDrawer` 和 `ElButton`。
*   `footer` 插槽的实现方式与 `VNDialog` 不同，如果使用了 `default` 插槽，`footer` 会渲染在 `default` 内容之后。如果需要固定底部的效果，可能需要额外的 CSS 处理。
*   默认情况下，抽屉不显示确认按钮 (`showConfirmButton: false`)，但显示取消按钮 (`showCancelButton: true`)。
*   监听 `@confirm` 事件处理确认逻辑，并 **记得在处理函数中将 `v-model` 的值设为 `false`** 来关闭抽屉。
*   点击默认取消按钮或遮罩层或右上角关闭按钮会触发 `close` 事件并关闭抽屉。 