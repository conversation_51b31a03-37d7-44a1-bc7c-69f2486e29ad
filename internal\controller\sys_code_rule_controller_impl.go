package controller

import (
	"backend/internal/model/dto"
	"backend/internal/model/vo"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/validator"

	"github.com/kataras/iris/v12"
)

// SysCodeRuleControllerImpl 编码规则管理控制器实现
type SysCodeRuleControllerImpl struct {
	*BaseControllerImpl
	codeRuleService       service.SysCodeRuleService
	codeGenerationService service.CodeGenerationService
}

// NewSysCodeRuleControllerImpl 创建编码规则管理控制器实例
func NewSysCodeRuleControllerImpl(cm *ControllerManager) *SysCodeRuleControllerImpl {
	base := NewBaseController(cm)
	codeRuleService := cm.GetServiceManager().GetSysCodeRuleService()
	codeGenerationService := cm.GetServiceManager().GetCodeGenerationService()

	if codeRuleService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 SysCodeRuleService 实例")
	}
	if codeGenerationService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 CodeGenerationService 实例")
	}

	return &SysCodeRuleControllerImpl{
		BaseControllerImpl:    base,
		codeRuleService:       codeRuleService,
		codeGenerationService: codeGenerationService,
	}
}

// CreateCodeRule 创建编码规则
// @Summary 创建编码规则
// @Description 创建新的编码规则
// @Tags 编码规则管理
// @Accept json
// @Produce json
// @Param request body dto.SysCodeRuleCreateReq true "创建请求"
// @Success 200 {object} response.Response{data=vo.SysCodeRuleVO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-rules [post]
func (c *SysCodeRuleControllerImpl) CreateCodeRule(ctx iris.Context) {
	opName := "创建编码规则"
	var req dto.SysCodeRuleCreateReq

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 验证请求参数
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	result, err := c.codeRuleService.Create(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, result)
}

// UpdateCodeRule 更新编码规则
// @Summary 更新编码规则
// @Description 更新指定ID的编码规则
// @Tags 编码规则管理
// @Accept json
// @Produce json
// @Param id path int true "规则ID"
// @Param request body dto.SysCodeRuleUpdateReq true "更新请求"
// @Success 200 {object} response.Response{data=vo.SysCodeRuleVO} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "规则不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-rules/{id} [put]
func (c *SysCodeRuleControllerImpl) UpdateCodeRule(ctx iris.Context) {
	opName := "更新编码规则"

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	ruleIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的规则ID参数").WithCause(err), opName)
		return
	}
	ruleID := uint(ruleIDUint64)

	var req dto.SysCodeRuleUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 验证请求参数
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败",
			logger.WithField("ruleId", ruleID),
			logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	result, err := c.codeRuleService.Update(ctx.Request().Context(), ruleID, &req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, result)
}

// DeleteCodeRule 删除编码规则
// @Summary 删除编码规则
// @Description 删除指定ID的编码规则
// @Tags 编码规则管理
// @Produce json
// @Param id path int true "规则ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "规则不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-rules/{id} [delete]
func (c *SysCodeRuleControllerImpl) DeleteCodeRule(ctx iris.Context) {
	opName := "删除编码规则"

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	ruleIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的规则ID参数").WithCause(err), opName)
		return
	}
	ruleID := uint(ruleIDUint64)

	err = c.codeRuleService.Delete(ctx.Request().Context(), ruleID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetCodeRule 获取编码规则详情
// @Summary 获取编码规则详情
// @Description 根据ID获取编码规则详情
// @Tags 编码规则管理
// @Produce json
// @Param id path int true "规则ID"
// @Success 200 {object} response.Response{data=vo.SysCodeRuleVO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "规则不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-rules/{id} [get]
func (c *SysCodeRuleControllerImpl) GetCodeRule(ctx iris.Context) {
	opName := "获取编码规则详情"

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	ruleIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的规则ID参数").WithCause(err), opName)
		return
	}
	ruleID := uint(ruleIDUint64)

	result, err := c.codeRuleService.GetByID(ctx.Request().Context(), ruleID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, result)
}

// GetCodeRulePage 分页查询编码规则
// @Summary 分页查询编码规则
// @Description 分页查询编码规则列表
// @Tags 编码规则管理
// @Produce json
// @Param pageNum query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param ruleCode query string false "规则编码"
// @Param ruleName query string false "规则名称"
// @Param businessType query string false "业务类型"
// @Param status query string false "状态"
// @Param isDefault query bool false "是否默认规则"
// @Success 200 {object} response.Response{data=vo.PageResult[vo.SysCodeRuleVO]} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-rules [get]
func (c *SysCodeRuleControllerImpl) GetCodeRulePage(ctx iris.Context) {
	opName := "分页查询编码规则"
	var req dto.SysCodeRuleQueryReq

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	// 从查询参数绑定到结构体
	if err := ctx.ReadQuery(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取查询参数失败").WithCause(err), opName)
		return
	}

	// 验证请求参数
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	result, err := c.codeRuleService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, result)
}

// SetAsDefault 设置为默认规则
// @Summary 设置为默认规则
// @Description 将指定规则设置为业务类型的默认规则
// @Tags 编码规则管理
// @Produce json
// @Param id path int true "规则ID"
// @Success 200 {object} response.Response "设置成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "规则不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-rules/{id}/set-default [post]
func (c *SysCodeRuleControllerImpl) SetAsDefault(ctx iris.Context) {
	opName := "设置默认规则"

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	ruleIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的规则ID参数").WithCause(err), opName)
		return
	}
	ruleID := uint(ruleIDUint64)

	err = c.codeRuleService.SetAsDefault(ctx.Request().Context(), ruleID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.SuccessWithMessage(ctx, "设置默认规则成功", nil)
}

// GenerateCode 生成编码
// @Summary 生成编码
// @Description 根据业务类型生成编码
// @Tags 编码生成
// @Accept json
// @Produce json
// @Param request body dto.CodeGenerationReq true "生成请求"
// @Success 200 {object} response.Response{data=dto.CodeGenerationRes} "生成成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-generation/generate [post]
func (c *SysCodeRuleControllerImpl) GenerateCode(ctx iris.Context) {
	opName := "生成编码"
	var req dto.CodeGenerationReq

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_generation")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 验证请求参数
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	result, err := c.codeGenerationService.GenerateCode(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, result)
}

// PreviewCode 预览编码
// @Summary 预览编码
// @Description 预览编码生成效果
// @Tags 编码生成
// @Accept json
// @Produce json
// @Param request body dto.CodePreviewReq true "预览请求"
// @Success 200 {object} response.Response{data=dto.CodePreviewRes} "预览成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-generation/preview [post]
func (c *SysCodeRuleControllerImpl) PreviewCode(ctx iris.Context) {
	opName := "预览编码"
	var req dto.CodePreviewReq

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 验证请求参数
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	result, err := c.codeGenerationService.PreviewCode(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, result)
}

// ResetSequence 重置序号
// @Summary 重置序号
// @Description 重置指定规则的序号
// @Tags 编码规则管理
// @Accept json
// @Produce json
// @Param request body dto.ResetSequenceReq true "重置请求"
// @Success 200 {object} response.Response "重置成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/code-generation/reset-sequence [post]
func (c *SysCodeRuleControllerImpl) ResetSequence(ctx iris.Context) {
	opName := "重置序号"
	var req dto.ResetSequenceReq

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 验证请求参数
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	err := c.codeGenerationService.ResetSequence(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.SuccessWithMessage(ctx, "重置序号成功", nil)
}

// GetBusinessTypeOptions 获取业务类型选项
// @Summary 获取业务类型选项
// @Description 获取可用的业务类型选项列表
// @Tags 编码规则管理
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.BusinessTypeOptionVO} "获取成功"
// @Router /api/v1/sys/code-rules/business-type-options [get]
func (c *SysCodeRuleControllerImpl) GetBusinessTypeOptions(ctx iris.Context) {
	options := vo.GetBusinessTypeOptions()

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	c.Success(ctx, options)
}

// GetResetFrequencyOptions 获取重置频率选项
// @Summary 获取重置频率选项
// @Description 获取可用的重置频率选项列表
// @Tags 编码规则管理
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.ResetFrequencyOptionVO} "获取成功"
// @Router /api/v1/sys/code-rules/reset-frequency-options [get]
func (c *SysCodeRuleControllerImpl) GetResetFrequencyOptions(ctx iris.Context) {
	options := vo.GetResetFrequencyOptions()

	// 设置审计日志资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "code_rule")

	c.Success(ctx, options)
}
