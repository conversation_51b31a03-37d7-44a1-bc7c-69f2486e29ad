package controller

import (
	"strconv"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	"backend/pkg/response"

	"github.com/kataras/iris/v12"
)

// WmsInventoryMovementController 库存移动控制器
type WmsInventoryMovementController struct {
	movementService service.WmsInventoryMovementService
}

// NewWmsInventoryMovementController 创建库存移动控制器实例
func NewWmsInventoryMovementController(movementService service.WmsInventoryMovementService) *WmsInventoryMovementController {
	return &WmsInventoryMovementController{
		movementService: movementService,
	}
}

// CreateMovement 创建库存移动
// @Summary 创建库存移动
// @Description 创建库存移动记录
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryMovementCreateReq true "移动信息"
// @Success 200 {object} response.Response{data=vo.WmsInventoryMovementVO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement [post]
func (c *WmsInventoryMovementController) CreateMovement(ctx iris.Context) {
	var req dto.WmsInventoryMovementCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 调用服务
	result, err := c.movementService.CreateMovement(ctx, &req)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "创建库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, result)
}

// UpdateMovement 更新库存移动
// @Summary 更新库存移动
// @Description 更新库存移动记录
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param id path int true "移动ID"
// @Param request body dto.WmsInventoryMovementUpdateReq true "更新信息"
// @Success 200 {object} response.Response{data=vo.WmsInventoryMovementVO} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/{id} [put]
func (c *WmsInventoryMovementController) UpdateMovement(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "无效的移动ID")
		return
	}

	var req dto.WmsInventoryMovementUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 调用服务
	result, err := c.movementService.Update(ctx, uint(id), &req)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "更新库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, result)
}

// DeleteMovement 删除库存移动
// @Summary 删除库存移动
// @Description 删除库存移动记录
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param id path int true "移动ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/{id} [delete]
func (c *WmsInventoryMovementController) DeleteMovement(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "无效的移动ID")
		return
	}

	// 调用服务
	err = c.movementService.Delete(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "删除库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetMovementDetail 获取库存移动详情
// @Summary 获取库存移动详情
// @Description 根据ID获取库存移动详细信息
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param id path int true "移动ID"
// @Success 200 {object} response.Response{data=vo.WmsInventoryMovementVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/{id} [get]
func (c *WmsInventoryMovementController) GetMovementDetail(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "无效的移动ID")
		return
	}

	// 调用服务
	result, err := c.movementService.GetByID(ctx, uint(id))
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "获取库存移动详情失败: "+err.Error())
		return
	}

	if result == nil {
		response.Fail(ctx, constant.DATA_NOT_FOUND, "库存移动记录不存在")
		return
	}

	response.Success(ctx, result)
}

// GetMovementPage 分页查询库存移动
// @Summary 分页查询库存移动
// @Description 根据条件分页查询库存移动记录
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryMovementQueryReq true "查询条件"
// @Success 200 {object} response.Response{data=vo.WmsInventoryMovementPageVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/page [post]
func (c *WmsInventoryMovementController) GetMovementPage(ctx iris.Context) {
	var req dto.WmsInventoryMovementQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 调用服务
	result, err := c.movementService.GetPage(ctx, &req)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "查询库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, result)
}

// StartMovement 开始移动
// @Summary 开始移动
// @Description 开始执行库存移动
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param id path int true "移动ID"
// @Param request body dto.WmsInventoryMovementStartReq true "开始信息"
// @Success 200 {object} response.Response "开始成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/{id}/start [post]
func (c *WmsInventoryMovementController) StartMovement(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "无效的移动ID")
		return
	}

	var req dto.WmsInventoryMovementStartReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 调用服务
	err = c.movementService.StartMovement(ctx, uint(id), req.OperatorID)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "开始库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// CompleteMovement 完成移动
// @Summary 完成移动
// @Description 完成库存移动
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param id path int true "移动ID"
// @Param request body dto.WmsInventoryMovementCompleteReq true "完成信息"
// @Success 200 {object} response.Response "完成成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/{id}/complete [post]
func (c *WmsInventoryMovementController) CompleteMovement(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "无效的移动ID")
		return
	}

	var req dto.WmsInventoryMovementCompleteReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 调用服务
	err = c.movementService.CompleteMovement(ctx, uint(id), req.OperatorID)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "完成库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// CancelMovement 取消移动
// @Summary 取消移动
// @Description 取消库存移动
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param id path int true "移动ID"
// @Param request body dto.WmsInventoryMovementCancelReq true "取消信息"
// @Success 200 {object} response.Response "取消成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/{id}/cancel [post]
func (c *WmsInventoryMovementController) CancelMovement(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "无效的移动ID")
		return
	}

	var req dto.WmsInventoryMovementCancelReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 调用服务
	err = c.movementService.CancelMovement(ctx, uint(id), req.Reason)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "取消库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetPendingMovements 获取待处理移动任务
// @Summary 获取待处理移动任务
// @Description 获取待处理的库存移动任务列表
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param warehouseId query int false "仓库ID"
// @Param operatorId query int false "操作员ID"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryMovementVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/pending [get]
func (c *WmsInventoryMovementController) GetPendingMovements(ctx iris.Context) {
	// 解析查询参数
	var warehouseId *uint
	if warehouseIdStr := ctx.URLParam("warehouseId"); warehouseIdStr != "" {
		if id, err := strconv.ParseUint(warehouseIdStr, 10, 32); err == nil {
			warehouseIdUint := uint(id)
			warehouseId = &warehouseIdUint
		}
	}

	var operatorId *uint
	if operatorIdStr := ctx.URLParam("operatorId"); operatorIdStr != "" {
		if id, err := strconv.ParseUint(operatorIdStr, 10, 32); err == nil {
			operatorIdUint := uint(id)
			operatorId = &operatorIdUint
		}
	}

	// 调用服务
	result, err := c.movementService.GetPendingMovements(ctx, warehouseId, operatorId)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "获取待处理移动任务失败: "+err.Error())
		return
	}

	response.Success(ctx, result)
}

// BatchCreateMovement 批量创建库存移动
// @Summary 批量创建库存移动
// @Description 批量创建库存移动记录
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryMovementBatchCreateReq true "批量创建信息"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryMovementVO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/batch [post]
func (c *WmsInventoryMovementController) BatchCreateMovement(ctx iris.Context) {
	var req dto.WmsInventoryMovementBatchCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 调用服务
	result, err := c.movementService.BatchCreate(ctx, req.Movements)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "批量创建库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, result)
}

// BatchStartMovement 批量开始移动
// @Summary 批量开始移动
// @Description 批量开始库存移动
// @Tags 库存移动
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryMovementBatchStartReq true "批量开始信息"
// @Success 200 {object} response.Response "开始成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/movement/batch/start [post]
func (c *WmsInventoryMovementController) BatchStartMovement(ctx iris.Context) {
	var req dto.WmsInventoryMovementBatchStartReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, constant.PARAM_FORMAT_ERROR, "请求参数格式错误")
		return
	}

	// 调用服务
	err := c.movementService.BatchStart(ctx, req.IDs, req.OperatorID)
	if err != nil {
		response.Fail(ctx, constant.OPERATION_FAILED, "批量开始库存移动失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}
