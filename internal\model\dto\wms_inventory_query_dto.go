package dto

import (
	"backend/pkg/response"
	"time"
)

// WmsInventoryQueryReq 库存查询请求
type WmsInventoryQueryReq struct {
	response.PageQuery
	WarehouseID     *uint      `json:"warehouseId" comment:"仓库ID"`
	LocationID      *uint      `json:"locationId" comment:"库位ID"`
	ItemID          *uint      `json:"itemId" comment:"物料ID"`
	ItemSku         *string    `json:"itemSku" comment:"物料SKU"`
	ItemName        *string    `json:"itemName" comment:"物料名称"`
	BatchNo         *string    `json:"batchNo" comment:"批次号"`
	Status          *string    `json:"status" comment:"库存状态"`
	QuantityMin     *float64   `json:"quantityMin" comment:"最小数量"`
	QuantityMax     *float64   `json:"quantityMax" comment:"最大数量"`
	ExpiryDateStart *time.Time `json:"expiryDateStart" comment:"过期日期开始"`
	ExpiryDateEnd   *time.Time `json:"expiryDateEnd" comment:"过期日期结束"`
	IncludeZero     *bool      `json:"includeZero" comment:"是否包含零库存"`
	OnlyAvailable   *bool      `json:"onlyAvailable" comment:"仅显示可用库存"`
}

// WmsInventorySummaryReq 库存汇总请求
type WmsInventorySummaryReq struct {
	WarehouseID *uint   `json:"warehouseId" comment:"仓库ID"`
	ItemID      *uint   `json:"itemId" comment:"物料ID"`
	GroupBy     *string `json:"groupBy" comment:"分组方式: warehouse/item/location/status"`
}

// WmsInventoryTurnoverReq 库存周转分析请求
type WmsInventoryTurnoverReq struct {
	WarehouseID *uint      `json:"warehouseId" comment:"仓库ID"`
	ItemID      *uint      `json:"itemId" comment:"物料ID"`
	DateStart   *time.Time `json:"dateStart" comment:"开始日期"`
	DateEnd     *time.Time `json:"dateEnd" comment:"结束日期"`
	Period      *string    `json:"period" comment:"周期: day/week/month/quarter/year"`
}

// WmsInventoryAvailabilityReq 库存可用性检查请求
type WmsInventoryAvailabilityReq struct {
	ItemID      uint    `json:"itemId" binding:"required" comment:"物料ID"`
	WarehouseID *uint   `json:"warehouseId" comment:"仓库ID"`
	RequiredQty float64 `json:"requiredQty" binding:"required,gt=0" comment:"需求数量"`
	BatchNo     *string `json:"batchNo" comment:"指定批次号"`
	ExcludeIDs  []uint  `json:"excludeIds" comment:"排除的库存ID列表"`
}

// WmsInventoryTransactionReq 库存事务查询请求
type WmsInventoryTransactionReq struct {
	response.PageQuery
	InventoryID     *uint      `json:"inventoryId" comment:"库存ID"`
	TransactionType *string    `json:"transactionType" comment:"事务类型"`
	ReferenceType   *string    `json:"referenceType" comment:"关联类型"`
	ReferenceID     *uint      `json:"referenceId" comment:"关联ID"`
	DateStart       *time.Time `json:"dateStart" comment:"开始日期"`
	DateEnd         *time.Time `json:"dateEnd" comment:"结束日期"`
}

// WmsInventoryABCAnalysisReq ABC分析请求
type WmsInventoryABCAnalysisReq struct {
	WarehouseID *uint      `json:"warehouseId" comment:"仓库ID"`
	DateStart   *time.Time `json:"dateStart" comment:"开始日期"`
	DateEnd     *time.Time `json:"dateEnd" comment:"结束日期"`
	AnalysisBy  *string    `json:"analysisBy" comment:"分析维度: value/quantity/frequency"`
}

// WmsInventoryAlertReq 库存预警查询请求
type WmsInventoryAlertReq struct {
	response.PageQuery
	WarehouseID *uint   `json:"warehouseId" comment:"仓库ID"`
	ItemID      *uint   `json:"itemId" comment:"物料ID"`
	AlertType   *string `json:"alertType" comment:"预警类型"`
	AlertLevel  *string `json:"alertLevel" comment:"预警级别"`
	Status      *string `json:"status" comment:"处理状态"`
}

// WmsInventoryExportReq 库存导出请求
type WmsInventoryExportReq struct {
	WmsInventoryQueryReq
	ExportFields []string `json:"exportFields" comment:"导出字段"`
	ExportFormat string   `json:"exportFormat" comment:"导出格式: excel/csv"`
	IncludeImage bool     `json:"includeImage" comment:"是否包含图片"`
}

// WmsInventoryImportReq 库存导入请求
type WmsInventoryImportReq struct {
	FileData []byte `json:"fileData" binding:"required" comment:"文件数据"`
	FileName string `json:"fileName" binding:"required" comment:"文件名"`
	Mode     string `json:"mode" comment:"导入模式: create/update/upsert"`
}

// WmsInventoryBatchUpdateReq 批量更新库存请求
type WmsInventoryBatchUpdateReq struct {
	InventoryIDs []uint                        `json:"inventoryIds" binding:"required" comment:"库存ID列表"`
	Updates      WmsInventoryBatchUpdateFields `json:"updates" binding:"required" comment:"更新字段"`
}

// WmsInventoryBatchUpdateFields 批量更新字段
type WmsInventoryBatchUpdateFields struct {
	Status     *string `json:"status" comment:"状态"`
	LocationID *uint   `json:"locationId" comment:"库位ID"`
	Remark     *string `json:"remark" comment:"备注"`
}

// GetDefaultExportFields 获取默认导出字段
func (req *WmsInventoryExportReq) GetDefaultExportFields() []string {
	if len(req.ExportFields) > 0 {
		return req.ExportFields
	}

	return []string{
		"itemSku",
		"itemName",
		"warehouseName",
		"locationCode",
		"batchNo",
		"quantity",
		"availableQty",
		"frozenQty",
		"allocatedQty",
		"status",
		"unitOfMeasure",
		"productionDate",
		"expiryDate",
		"lastUpdated",
	}
}

// GetExportFormat 获取导出格式
func (req *WmsInventoryExportReq) GetExportFormat() string {
	if req.ExportFormat == "" {
		return "excel"
	}
	return req.ExportFormat
}

// GetImportMode 获取导入模式
func (req *WmsInventoryImportReq) GetImportMode() string {
	if req.Mode == "" {
		return "create"
	}
	return req.Mode
}
