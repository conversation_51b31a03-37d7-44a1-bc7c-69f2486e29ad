{"info": {"name": "WMS出库流程模块API", "description": "WMS出库流程模块的完整API集合，包括出库通知单、拣货任务、库存分配和发运单管理", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api/v1/wms", "type": "string"}, {"key": "token", "value": "your_jwt_token_here", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "1. 出库通知单管理", "item": [{"name": "创建出库通知单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"clientId\": 1,\n  \"clientOrderNo\": \"CO20240101001\",\n  \"warehouseId\": 1,\n  \"requiredShipDate\": \"2024-01-15\",\n  \"priority\": 5,\n  \"consigneeName\": \"张三\",\n  \"consigneePhone\": \"13800138000\",\n  \"consigneeAddress\": \"北京市朝阳区xxx街道xxx号\",\n  \"carrierId\": 1,\n  \"shippingMethod\": \"快递\",\n  \"remark\": \"测试订单\",\n  \"details\": [\n    {\n      \"lineNo\": 1,\n      \"itemId\": 1,\n      \"requiredQty\": 100.0,\n      \"unitOfMeasure\": \"PCS\",\n      \"requiredBatchNo\": \"BATCH001\",\n      \"remark\": \"明细备注\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/outbound-notifications", "host": ["{{baseUrl}}"], "path": ["outbound-notifications"]}}}, {"name": "获取出库通知单详情", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/outbound-notifications/1", "host": ["{{baseUrl}}"], "path": ["outbound-notifications", "1"]}}}, {"name": "分页查询出库通知单", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/outbound-notifications?pageNum=1&pageSize=20&status=PENDING", "host": ["{{baseUrl}}"], "path": ["outbound-notifications"], "query": [{"key": "pageNum", "value": "1"}, {"key": "pageSize", "value": "20"}, {"key": "status", "value": "PENDING"}]}}}, {"name": "审核出库通知单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"remark\": \"审核通过\"\n}"}, "url": {"raw": "{{baseUrl}}/outbound-notifications/1/approve", "host": ["{{baseUrl}}"], "path": ["outbound-notifications", "1", "approve"]}}}, {"name": "分配库存", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"allocationStrategy\": \"FIFO\",\n  \"forceAllocate\": false\n}"}, "url": {"raw": "{{baseUrl}}/outbound-notifications/1/allocate-inventory", "host": ["{{baseUrl}}"], "path": ["outbound-notifications", "1", "allocate-inventory"]}}}, {"name": "生成拣货任务", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pickingStrategy\": \"BY_ORDER\",\n  \"waveNo\": \"WAVE001\",\n  \"priority\": 5\n}"}, "url": {"raw": "{{baseUrl}}/outbound-notifications/1/generate-picking-task", "host": ["{{baseUrl}}"], "path": ["outbound-notifications", "1", "generate-picking-task"]}}}]}, {"name": "2. 拣货任务管理", "item": [{"name": "创建拣货任务", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"notificationId\": 1,\n  \"pickingStrategy\": \"BY_ORDER\",\n  \"waveNo\": \"WAVE001\",\n  \"priority\": 5,\n  \"assignedUserId\": 1,\n  \"remark\": \"拣货任务备注\"\n}"}, "url": {"raw": "{{baseUrl}}/picking-tasks", "host": ["{{baseUrl}}"], "path": ["picking-tasks"]}}}, {"name": "获取拣货任务详情", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/picking-tasks/1", "host": ["{{baseUrl}}"], "path": ["picking-tasks", "1"]}}}, {"name": "分配拣货任务", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"assignedUserId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/picking-tasks/1/assign", "host": ["{{baseUrl}}"], "path": ["picking-tasks", "1", "assign"]}}}, {"name": "开始拣货任务", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/picking-tasks/1/start", "host": ["{{baseUrl}}"], "path": ["picking-tasks", "1", "start"]}}}, {"name": "执行拣货", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"detailId\": 1,\n  \"pickedQty\": 100.0\n}"}, "url": {"raw": "{{baseUrl}}/picking-tasks/execute-picking", "host": ["{{baseUrl}}"], "path": ["picking-tasks", "execute-picking"]}}}, {"name": "完成拣货任务", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"remark\": \"拣货完成\"\n}"}, "url": {"raw": "{{baseUrl}}/picking-tasks/1/complete", "host": ["{{baseUrl}}"], "path": ["picking-tasks", "1", "complete"]}}}]}, {"name": "3. 库存分配管理", "item": [{"name": "自动分配库存", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"outboundDetailId\": 1,\n  \"allocationStrategy\": \"FIFO\",\n  \"forceAllocate\": false,\n  \"maxAllocations\": 5\n}"}, "url": {"raw": "{{baseUrl}}/inventory-allocations/auto-allocate", "host": ["{{baseUrl}}"], "path": ["inventory-allocations", "auto-allocate"]}}}, {"name": "检查库存可用性", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"itemId\": 1,\n  \"requiredQty\": 100.0,\n  \"requiredBatchNo\": \"BATCH001\",\n  \"warehouseId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/inventory-allocations/check-availability", "host": ["{{baseUrl}}"], "path": ["inventory-allocations", "check-availability"]}}}, {"name": "拣货确认", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"pickedQty\": 50.0\n}"}, "url": {"raw": "{{baseUrl}}/inventory-allocations/1/pick-confirm", "host": ["{{baseUrl}}"], "path": ["inventory-allocations", "1", "pick-confirm"]}}}]}, {"name": "4. 发运单管理", "item": [{"name": "创建发运单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pickingTaskId\": 1,\n  \"carrierId\": 1,\n  \"shippingMethod\": \"快递\",\n  \"estimatedShipDate\": \"2024-01-15\",\n  \"consigneeName\": \"张三\",\n  \"consigneePhone\": \"13800138000\",\n  \"consigneeAddress\": \"北京市朝阳区xxx街道xxx号\",\n  \"packageCount\": 2,\n  \"totalWeight\": 10.5,\n  \"totalVolume\": 0.5,\n  \"remark\": \"发运单备注\"\n}"}, "url": {"raw": "{{baseUrl}}/shipments", "host": ["{{baseUrl}}"], "path": ["shipments"]}}}, {"name": "获取发运单详情", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/shipments/1", "host": ["{{baseUrl}}"], "path": ["shipments", "1"]}}}, {"name": "打包发运单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"packageCount\": 2,\n  \"totalWeight\": 10.5,\n  \"totalVolume\": 0.5,\n  \"remark\": \"打包完成\"\n}"}, "url": {"raw": "{{baseUrl}}/shipments/1/pack", "host": ["{{baseUrl}}"], "path": ["shipments", "1", "pack"]}}}, {"name": "发运确认", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"trackingNo\": \"TN20240101001\",\n  \"actualShipDate\": \"2024-01-15T10:00:00Z\",\n  \"estimatedDeliveryDate\": \"2024-01-17T18:00:00Z\",\n  \"remark\": \"已发运\"\n}"}, "url": {"raw": "{{baseUrl}}/shipments/1/ship", "host": ["{{baseUrl}}"], "path": ["shipments", "1", "ship"]}}}, {"name": "计算运费", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"carrierId\": 1,\n  \"shippingMethod\": \"快递\",\n  \"totalWeight\": 10.5,\n  \"totalVolume\": 0.5,\n  \"destinationAddress\": \"北京市朝阳区xxx街道xxx号\"\n}"}, "url": {"raw": "{{baseUrl}}/shipments/calculate-cost", "host": ["{{baseUrl}}"], "path": ["shipments", "calculate-cost"]}}}]}]}