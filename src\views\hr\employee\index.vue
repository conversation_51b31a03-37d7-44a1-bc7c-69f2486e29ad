<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="180" 
      @refresh="loadData"
      @add="handleAdd"
      @batch-delete="handleBatchDelete"
      @import="handleImport"
      @export="handleExport"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <template #column-employeeStatusText="{ row }">
        <el-tag :type="row.employeeStatus === 1 ? 'success' : (row.employeeStatus === 0 ? 'danger' : 'warning')">
          {{ row.employeeStatusText }}
        </el-tag>
      </template>

      <template #card-footer="{ row }">
        <el-tooltip content="查看" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)" v-if="hasPermission('hr:employee:list')"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEdit(row)" v-if="hasPermission('hr:employee:edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button circle :icon="Delete" type="danger" size="small" @click="handleDelete(row)" v-if="hasPermission('hr:employee:delete')"></el-button>
        </el-tooltip>
      </template>

    </VNTable>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="75%" 
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
      top="5vh" 
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="4"
        :label-width="'120px'" 
        :loading="formLoading"
        group-title-type="h4"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <template #form-item-employeeAvatar="{ field, formData: formModel }">
          <el-upload
            v-if="!isViewing"
            class="avatar-uploader"
            action="#" 
            :show-file-list="false"
            :http-request="handleAvatarUpload"
            :before-upload="beforeAvatarUpload"
          >
            <el-image
              v-if="formModel[field.field]"
              :src="formModel[field.field]"
              class="avatar"
              style="width: 100px; height: 100px; border: 1px dashed #dcdfe6; border-radius: 6px;"
              fit="cover"
            />
            <el-icon v-else class="avatar-uploader-icon" style="width: 100px; height: 100px; border: 1px dashed #dcdfe6; border-radius: 6px; font-size: 28px;"><Plus /></el-icon>
          </el-upload>
          <el-image
            v-if="isViewing && formModel[field.field]"
            style="width: 100px; height: 100px; border: 1px dashed #dcdfe6; border-radius: 6px; vertical-align: top;"
            :src="formModel[field.field]"
            :preview-src-list="formModel[field.field] ? [formModel[field.field]] : []"
            :initial-index="0"
            fit="cover"
          >
            <template #error>
              <div class="image-slot" style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center; border: 1px dashed #dcdfe6; border-radius: 6px;">无头像</div>
            </template>
          </el-image>
          <div v-if="isViewing && !formModel[field.field]" class="image-slot" style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center; border: 1px dashed #dcdfe6; border-radius: 6px;">无头像</div>
        </template>

        <template #form-item-employeeDepartmentId="{ field, formData: formModel }">
          <el-tree-select
            v-if="!isViewing"
            v-model="formModel[field.field]"
            :data="departmentTreeForSelect"
            :props="{ value: 'id', label: 'name', children: 'children', disabled: isDepartmentDisabled }"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择所属部门"
            clearable
            style="width: 100%;"
          >
            <template #default="{ data }">
              <el-icon style="margin-right: 5px; vertical-align: middle;">
                <OfficeBuilding v-if="data.nodeType === 'company'" />
                <Tickets v-else-if="data.nodeType === 'department'" />
                <User v-else-if="data.nodeType === 'position'" />
              </el-icon>
              <span style="vertical-align: middle;">{{ data.name }}</span>
            </template>
          </el-tree-select>
          <div v-else class="custom-disabled-text-wrapper">
            <el-text class="custom-disabled-text">
              {{ formModel['employeeDepartmentName'] || '-' }}
            </el-text>
          </div>
        </template>

        <template #form-item-employeePositionId="{ field, formData: formModel }">
          <el-tree-select
            v-if="!isViewing"
            v-model="formModel[field.field]"
            :data="fullOrganizationTreeOptions"
            :props="{ value: 'id', label: 'name', children: 'children', disabled: isPositionDisabled }"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择担任岗位"
            clearable
            style="width: 100%;"
          >
            <template #default="{ data }">
              <el-icon style="margin-right: 5px; vertical-align: middle;">
                <OfficeBuilding v-if="data.nodeType === 'company'" />
                <Tickets v-else-if="data.nodeType === 'department'" />
                <User v-else-if="data.nodeType === 'position'" />
              </el-icon>
              <span style="vertical-align: middle;">{{ data.name }}</span>
            </template>
          </el-tree-select>
          <div v-else class="custom-disabled-text-wrapper">
            <el-text class="custom-disabled-text">
              {{ formModel['employeePositionName'] || '-' }}
            </el-text>
          </div>
        </template>

        <template #form-item-employeeSalary="{ field, formData: formModel }">
          <el-input-number
            v-if="!isViewing"
            v-model="formModel[field.field]"
            :precision="2"
            :step="100"
            placeholder="请输入薪资"
            clearable
            style="width: 100%;"
          />
          <div v-else class="custom-disabled-text-wrapper">
            <el-text class="custom-disabled-text">
              {{ formModel[field.field] === null || formModel[field.field] === undefined ? '' : formModel[field.field] }}
            </el-text>
          </div>
        </template>

        <template #actions>
          <template v-if="isViewing">
            <el-button
              v-if="hasPermission('hr:employee:printpreview')"
              :icon="View"
              @click="handlePrintPreview"
            >
              打印预览
            </el-button>
            <el-button
              v-if="hasPermission('hr:employee:print')"
              type="primary"
              :icon="Printer"
              @click="handlePrint"
            >
              打印
            </el-button>
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="submitForm" :loading="formLoading">提交</el-button>
            <el-button @click="handleCloseDialog">取消</el-button>
          </template>
        </template>
      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, PaginationConfig, ActionButton } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton, ElImage, ElText, ElUpload, ElIcon, ElInputNumber, ElTreeSelect } from 'element-plus';
import type { UploadProps, UploadRequestHandler, UploadRawFile } from 'element-plus';
import { View, Edit, Delete, Plus, Printer, OfficeBuilding, Tickets, User } from '@element-plus/icons-vue';

import {
  getEmployeeList,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeeDetail,
  getEmployeeSimpleList,
  batchDeleteEmployees,
  uploadEmployeeAvatar,
} from '@/api/hr/employee';
import type {
  EmployeeListItem,
  EmployeeQueryParams,
  EmployeeFormData,
} from '@/api/hr/employee';

import { getOrganizationNodeList, getOrganizationNodeTree } from '@/api/hr/organizationNode';
import type { OrganizationNodeVO } from '@/api/hr/organizationNode';
import { getDictDataByCode } from '@/api/system/systemDict';
import type { DictDataVO } from '@/api/system/systemDict';

import { hasPermission } from '@/hooks/usePermission';

const vnTableRef = ref<InstanceType<typeof VNTable> | null>(null);
const vnFormRef = ref<InstanceType<typeof VNForm> | null>(null);

const tableData = ref<EmployeeListItem[]>([]);
const loading = ref(false);
const formLoading = ref(false);
const pagination = reactive<PaginationConfig>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const queryParams = reactive<Partial<EmployeeQueryParams>>({
  pageNum: pagination.currentPage,
  pageSize: pagination.pageSize,
  sort: undefined,
});

const dialogVisible = ref(false);
const dialogTitle = ref('');
const formMode = ref<'add' | 'edit' | 'view'>('add');
const formData = ref<Partial<EmployeeFormData>>({});
const currentRow = ref<EmployeeListItem | null>(null);

const isViewing = computed(() => formMode.value === 'view');
const initialLoadDone = ref(false);

const genderOptions = ref<DictDataVO[]>([]);
const idCardTypeOptions = ref<DictDataVO[]>([]);
const employeeStatusOptions = ref<DictDataVO[]>([]);
const workTypeOptions = ref<DictDataVO[]>([]);
const nationOptions = ref<DictDataVO[]>([]);
const politicalStatusOptions = ref<DictDataVO[]>([]);
const maritalStatusOptions = ref<DictDataVO[]>([]);
const educationOptions = ref<DictDataVO[]>([]);
const healthStatusOptions = ref<DictDataVO[]>([]);
const degreeOptions = ref<DictDataVO[]>([]);
const entrySourceOptions = ref<DictDataVO[]>([]);
const emergencyContactRelationshipOptions = ref<DictDataVO[]>([]);

const jobGradeOptions = ref<DictDataVO[]>([]);
const jobSubLevelOptions = ref<DictDataVO[]>([]);
const workCategoryOptions = ref<DictDataVO[]>([]);

const professionalTitleOptions = ref<DictDataVO[]>([]);

const campusSchoolOptions = ref<DictDataVO[]>([]);
const headhunterOptions = ref<DictDataVO[]>([]);
const laborDispatchOptions = ref<DictDataVO[]>([]);
const employeeSimpleListOptions = ref<{ label: string, value: any }[]>([]);

const toolbarConfig = computed<Record<string, any>>(() => ({
  title: '员工管理',
  refresh: true,
  add: hasPermission('hr:employee:add'),
  batchDelete: hasPermission('hr:employee:delete'),
  filter: hasPermission('hr:employee:search'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('hr:employee:import'),
  export: hasPermission('hr:employee:export'),
}));

const tableColumns = reactive<TableColumn[]>([
  { prop: 'employeeCode', label: '工号', minWidth: 120, sortable: 'custom' },
  { prop: 'employeeName', label: '姓名', minWidth: 120, sortable: 'custom' },
  { prop: 'employeeGenderText', label: '性别', minWidth: 80 },
  { prop: 'employeeMobile', label: '手机号码', minWidth: 120 },
  { prop: 'employeeDepartmentName', label: '部门', minWidth: 150 },
  { prop: 'employeePositionName', label: '岗位', minWidth: 150 },
  { prop: 'employeeJobGradeValueText', label: '职级', minWidth: 120 },
  { prop: 'employeeJobSubLevelValueText', label: '职等', minWidth: 120 },
  { prop: 'employeeWorkCategoryValueText', label: '员工性质', minWidth: 130 },
  { prop: 'employeeInDate', label: '入职日期', minWidth: 120, formatter: (row) => formatDateTime(row.employeeInDate, 'YYYY-MM-DD') },
  { prop: 'employeeStatusText', label: '状态', minWidth: 100 },
  { prop: 'employeeBankName', label: '开户银行', minWidth: 150 },
  { prop: 'employeeBankAccount', label: '银行账号', minWidth: 180 },
  { prop: 'createTime', label: '创建时间', minWidth: 160, formatter: (row) => formatDateTime(row.createdAt) },
]);

const operationButtons = computed<ActionButton[]>(() => [
  { label: '查看', icon: 'View', handler: handleView, permission: 'hr:employee:list', show: true },
  { label: '编辑', icon: 'Edit', type: 'primary', handler: handleEdit, permission: 'hr:employee:edit', show: true },
  { label: '删除', icon: 'Delete', type: 'danger', handler: handleDelete, permission: 'hr:employee:delete', show: true },
]);

const departmentOptions = ref<OrganizationNodeVO[]>([]);
const positionOptions = ref<OrganizationNodeVO[]>([]);
const fullOrganizationTreeOptions = ref<OrganizationNodeVO[]>([]);

// 因为departmentOptions提示未使用，所以故意使用一下，避免被优化掉
// 因为很奇怪，我把 const departmentOptions = ref<OrganizationNodeVO[]>([]);删除掉，前端就不能正常显示了
// 这个需要仔细研究一下
console.log('departmentOptions', departmentOptions.value);

// New computed property to filter out 'Position' nodes for the department dropdown
const departmentTreeForSelect = computed(() => {
  const filterPositions = (nodes: OrganizationNodeVO[]): OrganizationNodeVO[] => {
    if (!nodes || nodes.length === 0) {
      return [];
    }
    return nodes
      .filter(node => node.nodeType !== 'position')
      .map((node: OrganizationNodeVO) => ({
        ...node,
        children: (node as any).children ? filterPositions((node as any).children) : [], 
      }));
  };
  return filterPositions(fullOrganizationTreeOptions.value);
});

const formatDateTime = (dateString: string | null | undefined, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-';
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
        console.warn('formatDateTime received invalid date string after initial check:', dateString);
        return '-';
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    if (format === 'YYYY-MM-DD') {
      return `${year}-${month}-${day}`;
    }
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error('Error formatting date:', dateString, '\nError:', e);
    return '-';
  }
};

const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

const formFields = computed<HeaderField[]>(() => ([
  // Group 1: 基本信息
  { field: 'employeeCode', label: '工号', props: { placeholder: '请输入工号' }, rules: [{ required: true, message: '工号不能为空', trigger: 'blur' }], group: '基本信息', disabled: formMode.value === 'edit' || isViewing.value },
  { field: 'employeeName', label: '姓名', props: { placeholder: '请输入姓名' }, rules: [{ required: true, message: '姓名不能为空', trigger: 'blur' }], group: '基本信息', disabled: isViewing.value },
  { field: 'employeeMobile', label: '手机号码', props: { placeholder: '请输入手机号码' }, rules: [{ required: true, message: '手机号码不能为空', trigger: 'blur' } /*, { pattern: /^1[3-9]\\d{9}$/, message: '手机号格式不正确', trigger: 'blur' } */ ], group: '基本信息', disabled: isViewing.value },
  { field: 'employeeGender', label: '性别', type: 'select', options: genderOptions, props: { placeholder: '请选择性别', clearable: true }, rules: [{ required: true, message: '性别不能为空', trigger: 'blur' }], group: '基本信息', disabled: isViewing.value },
  { field: 'employeeIDCardType', label: '证件类型', type: 'select', options: idCardTypeOptions, props: { placeholder: '请选择证件类型', clearable: true }, rules: [{ required: true, message: '证件类型不能为空', trigger: 'blur' }], group: '基本信息', disabled: isViewing.value },
  { field: 'employeeIDCard', label: '证件号码', props: { placeholder: '请输入证件号码' }, rules: [{ required: true, message: '证件号码不能为空', trigger: 'blur' } /*, custom idcard validator */], group: '基本信息', disabled: isViewing.value },
  { field: 'employeeBirthday', label: '出生日期', type: 'date', props: { placeholder: '请选择出生日期', valueFormat: 'YYYY-MM-DD' }, rules: [{ required: true, message: '出生日期不能为空', trigger: 'blur' }], group: '基本信息', disabled: isViewing.value },
  { field: 'employeeEmail', label: '电子邮箱', props: { placeholder: '请输入电子邮箱' }, rules: [/* { type: 'email', message: '邮箱格式不正确', trigger: 'blur' } */], group: '基本信息', disabled: isViewing.value },
  
  // Group 2: 职位信息
  // For slot types, the disabled state is handled within the slot template usually
  { field: 'employeeDepartmentId', label: '所属部门', type: 'slot', group: '职位信息' }, // disabled will be handled by v-if/v-else in slot
  { field: 'employeePositionId', label: '担任岗位', type: 'slot', group: '职位信息' },   // disabled will be handled by v-if/v-else in slot
  { field: 'employeeJobGradeValue', label: '员工职级', type: 'select', options: jobGradeOptions, props: { placeholder: '请选择员工职级', clearable: true }, group: '职位信息', disabled: isViewing.value },
  { field: 'employeeJobSubLevelValue', label: '员工职等', type: 'select', options: jobSubLevelOptions, props: { placeholder: '请选择员工职等', clearable: true }, group: '职位信息', disabled: isViewing.value },
  { field: 'employeeJobTitle', label: '职称', type: 'select', options: professionalTitleOptions, props: { placeholder: '请选择职称', clearable: true }, group: '职位信息', disabled: isViewing.value },
  { field: 'employeeInDate', label: '入职日期', type: 'date', props: { placeholder: '请选择入职日期', valueFormat: 'YYYY-MM-DD' }, rules: [{ required: true, message: '入职日期不能为空', trigger: 'change' }], group: '职位信息', disabled: isViewing.value },
  { field: 'employeeProbationEndDate', label: '转正日期', type: 'date', props: { placeholder: '请选择转正日期', valueFormat: 'YYYY-MM-DD' }, group: '职位信息', disabled: isViewing.value },
  { field: 'employeeWorkCategoryValue', label: '员工性质', type: 'select', options: workCategoryOptions, props: { placeholder: '请选择员工性质', clearable: true }, group: '职位信息', disabled: isViewing.value },
  { field: 'employeeStatus', label: '员工状态', type: 'select', options: employeeStatusOptions, props: { placeholder: '请选择员工状态' }, rules: [{ required: true, message: '员工状态不能为空', trigger: 'change' }], group: '职位信息', disabled: isViewing.value },
  { field: 'employeeWorkType', label: '用工性质', type: 'select', options: workTypeOptions, props: { placeholder: '请选择用工性质', clearable: true }, group: '职位信息', disabled: isViewing.value },
  { field: 'employeeFirstWorkDate', label: '首次参加工作', type: 'date', props: { placeholder: '请选择首次参加工作日期', valueFormat: 'YYYY-MM-DD' }, group: '职位信息', disabled: isViewing.value },
  { field: 'employeeOutDate', label: '离职日期', type: 'date', props: { placeholder: '请选择离职日期', valueFormat: 'YYYY-MM-DD' }, group: '职位信息', disabled: isViewing.value },
  
  // Group 3: 合同与薪资
  { field: 'employeeContractStartDate', label: '合同开始日', type: 'date', props: { placeholder: '选择日期', valueFormat: 'YYYY-MM-DD' }, group: '合同与薪资', disabled: isViewing.value },
  { field: 'employeeContractEndDate', label: '合同结束日', type: 'date', props: { placeholder: '选择日期', valueFormat: 'YYYY-MM-DD' }, group: '合同与薪资', disabled: isViewing.value },
  { field: 'employeeSalary', label: '薪资', type: 'slot', group: '合同与薪资' }, // disabled will be handled by v-if/v-else in slot
  { field: 'employeeBankName', label: '开户银行', props: { placeholder: '请输入工资卡开户银行' }, group: '合同与薪资', disabled: isViewing.value },
  { field: 'employeeBankAccount', label: '银行账号', props: { placeholder: '请输入工资卡银行账号' }, group: '合同与薪资', disabled: isViewing.value },
  
  // Group 4: 个人详细信息
  { field: 'employeeAvatar', label: '员工头像', type: 'slot', visible: false, span: 3, group: '个人详细信息' }, // disabled will be handled by v-if/v-else in slot
  { field: 'employeeNation', label: '民族', type: 'select', options: nationOptions, props: { placeholder: '请选择民族', clearable: true }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeePoliticalStatus', label: '政治面貌', type: 'select', options: politicalStatusOptions, props: { placeholder: '请选择政治面貌', clearable: true }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeMaritalStatus', label: '婚姻状况', type: 'select', options: maritalStatusOptions, props: { placeholder: '请选择婚姻状况', clearable: true }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeEducation', label: '学历', type: 'select', options: educationOptions, props: { placeholder: '请选择学历', clearable: true }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeDegree', label: '学位', type: 'select', options: degreeOptions, props: { placeholder: '请选择学位', clearable: true }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeSchool', label: '毕业院校', props: { placeholder: '请输入毕业院校' }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeMajor', label: '专业', props: { placeholder: '请输入专业' }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeGraduationDate', label: '毕业日期', type: 'date', props: { placeholder: '请选择毕业日期', valueFormat: 'YYYY-MM-DD' }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeHealthStatus', label: '健康状况', type: 'select', options: healthStatusOptions, props: { placeholder: '请选择健康状况', clearable: true /*, visible: false */ }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeEmergencyContactName', label: '紧急联系人', props: { placeholder: '请输入姓名' }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeEmergencyContactMobile', label: '紧急联系电话', props: { placeholder: '请输入电话' }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeEmergencyContactRelationship', label: '与本人关系', type: 'select', options: emergencyContactRelationshipOptions, props: { placeholder: '请选择与本人关系', clearable: true }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeHouseholdAddress', label: '户籍地址', props: { placeholder: '请输入户籍地址' }, span: 2, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeCurrentAddress', label: '居住地址', props: { placeholder: '请输入居住地址' }, span: 2, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeePostCode', label: '邮政编码', props: { placeholder: '请输入邮政编码' }, visible: false, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeEntrySource', label: '入职来源', type: 'select', options: entrySourceOptions, props: { placeholder: '请选择入职来源', clearable: true }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeReferralName', label: '推荐人', props: { placeholder: '请输入推荐人姓名' }, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeHobby', label: '爱好特长', props: { placeholder: '请输入爱好特长' }, visible: false, span: 2, group: '个人详细信息', disabled: isViewing.value },
  { field: 'employeeRemark', label: '备注', props: { placeholder: '请输入备注' }, visible: false, span: 2, group: '个人详细信息', disabled: isViewing.value },
]));

const loadData = async () => {
  loading.value = true;
  queryParams.pageNum = pagination.currentPage;
  queryParams.pageSize = pagination.pageSize;
  try {
    const res = await getEmployeeList(queryParams as EmployeeQueryParams);
    tableData.value = res.list;
    pagination.total = res.total;
  } catch (error) {
    console.error('Error loading employee list:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const loadPositionOptions = async () => {
    try {
        const res = await getOrganizationNodeList({ nodeType: 'Position', pageSize: 1000 });
        positionOptions.value = (res.data?.list || []).map((item: OrganizationNodeVO) => ({
            ...item,
            value: item.id,
            label: item.name
        }));
    } catch (error) {
        console.error('Error loading position list:', error);
        positionOptions.value = [];
        const errorMessage = getApiErrorMessage(error);
        ElMessage({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: errorMessage,
          showClose: true,
          duration: 5 * 1000
        });
    }
};

const loadGenderOptions = async () => {
  try {
    const res = await getDictDataByCode('gender');
    genderOptions.value = res || [];
  } catch (error) {
    console.error('Error loading gender options:', error);
    genderOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  }
};

const loadIdCardTypeOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_id_card_type');
    idCardTypeOptions.value = res || [];
  } catch (error) {
    console.error('Error loading ID card type options:', error);
    idCardTypeOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  }
};

const loadEmployeeStatusOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_status');
    employeeStatusOptions.value = res || [];
  } catch (error) {
    console.error('Error loading employee status options:', error);
    employeeStatusOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  }
};

const loadWorkTypeOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_work_type');
    workTypeOptions.value = res || [];
  } catch (error) {
    console.error('Error loading work type options:', error);
    workTypeOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  }
};

const loadNationOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_nation');
    nationOptions.value = res || [];
  } catch (error) {
    console.error('Error loading nation options:', error);
    nationOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadPoliticalStatusOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_political_status');
    politicalStatusOptions.value = res || [];
  } catch (error) {
    console.error('Error loading political status options:', error);
    politicalStatusOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadMaritalStatusOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_marital_status');
    maritalStatusOptions.value = res || [];
  } catch (error) {
    console.error('Error loading marital status options:', error);
    maritalStatusOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadEducationOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_education');
    educationOptions.value = res || [];
  } catch (error) {
    console.error('Error loading education options:', error);
    educationOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadHealthStatusOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_health_status');
    healthStatusOptions.value = res || [];
  } catch (error) {
    console.error('Error loading health status options:', error);
    healthStatusOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadDegreeOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_degree');
    degreeOptions.value = res || [];
  } catch (error) {
    console.error('Error loading degree options:', error);
    degreeOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadEntrySourceOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_entry_source');
    entrySourceOptions.value = res || [];
  } catch (error) {
    console.error('Error loading entry source options:', error);
    entrySourceOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadEmergencyContactRelationshipOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_emergency_contact_relationship');
    emergencyContactRelationshipOptions.value = res || [];
  } catch (error) {
    console.error('Error loading emergency contact relationship options:', error);
    emergencyContactRelationshipOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadJobGradeOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_job_grade');
    jobGradeOptions.value = res || [];
  } catch (error) {
    console.error('Error loading job grade options:', error);
    jobGradeOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadJobSubLevelOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_job_sub_level');
    jobSubLevelOptions.value = res || [];
  } catch (error) {
    console.error('Error loading job sub-level options:', error);
    jobSubLevelOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadWorkCategoryOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_work_category');
    workCategoryOptions.value = res || [];
  } catch (error) {
    console.error('Error loading work category options:', error);
    workCategoryOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadProfessionalTitleOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_professional_title');
    professionalTitleOptions.value = res || [];
  } catch (error) {
    console.error('Error loading professional title options:', error);
    professionalTitleOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  }
};

const loadCampusSchoolOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_campus_recruitment_school');
    campusSchoolOptions.value = res || [];
  } catch (error) {
    console.error('Error loading campus school options:', error);
    campusSchoolOptions.value = [];
    // ElMessage({ type: 'error', message: '加载校招学校选项失败' });
  }
};

const loadHeadhunterOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_headhunter_company');
    headhunterOptions.value = res || [];
  } catch (error) {
    console.error('Error loading headhunter options:', error);
    headhunterOptions.value = [];
    // ElMessage({ type: 'error', message: '加载猎头公司选项失败' });
  }
};

const loadLaborDispatchOptions = async () => {
  try {
    const res = await getDictDataByCode('emp_labor_dispatch_company');
    laborDispatchOptions.value = res || [];
  } catch (error) {
    console.error('Error loading labor dispatch options:', error);
    laborDispatchOptions.value = [];
    // ElMessage({ type: 'error', message: '加载劳务公司选项失败' });
  }
};

const loadEmployeeSimpleListOptions = async () => {
  try {
    const res = await getEmployeeSimpleList({}); 
    employeeSimpleListOptions.value = (res.list || []).map(emp => ({
      label: `${emp.employeeCode} - ${emp.employeeName}`,
      value: emp.id, 
    }));
  } catch (error) {
    console.error('Error loading employee simple list options:', error);
    employeeSimpleListOptions.value = [];
    // ElMessage({ type: 'error', message: '加载员工列表选项失败' });
  }
};

const resetForm = () => {
  formData.value = {
    employeeCode: '',
    employeeName: '',
    employeeIDCard: '',
    employeeMobile: '',
    employeeInDate: '',
    employeeStatus: undefined,
    employeeGender: null,
    employeeBirthday: null,
  };
  if (vnFormRef.value) {
    vnFormRef.value.clearValidate();
  }
};

const handleAdd = async () => {
  resetForm();
  formMode.value = 'add';
  dialogTitle.value = '新增员工';
  formLoading.value = true;
  try {
    // await loadAllEditViewDictionaries(); // 已移至 onMounted
    // if (!departmentOptions.value.length) await loadDepartmentTree(); // 已被 fullOrganizationTreeOptions 替代
  } catch (error) {
    console.error("Error loading dictionaries for add:", error);
  } finally {
    formLoading.value = false;
  }
  dialogVisible.value = true;
};

// 新增：辅助函数，将特定字段的数字值转换为字符串，以匹配字典选项的value类型
const convertNumericDictValuesToString = (data: Partial<EmployeeFormData>): Partial<EmployeeFormData> => {
  // 这些字段从后端来是数字，前端select的选项value通常是字符串形式的数字
  const fieldsToConvert: (keyof EmployeeFormData)[] = [
    'employeeGender',
    'employeeStatus',
    'employeeEducation',
    'employeeMaritalStatus',
    'employeePoliticalStatus',
    'employeeIDCardType',
    'employeeWorkType',
    'employeeHealthStatus',
    // 以下字段后端本身就是string或由其他逻辑处理，不在此处转string
    // 'employeeDegree',
    // 'employeeNation',
    // 'employeeEntrySource',
    // 'employeeEmergencyContactRelationship'
  ];

  const convertedData = { ...data };
  for (const field of fieldsToConvert) {
    if (convertedData[field] !== null && convertedData[field] !== undefined) {
      // 确保只转换确实是数字类型的值，或者可以安全转换为字符串表示的数字的值
      if (typeof convertedData[field] === 'number') {
         (convertedData as any)[field] = String(convertedData[field]);
      } else if (typeof convertedData[field] === 'string') {
        // 如果已经是字符串，检查它是否是纯数字字符串，如果是，则通常无需操作
        // 如果不是纯数字字符串（例如 "未知"），则此转换不适用，但通常这类值不会出现在此列表中
      } else {
        console.warn(`[convertNumericDictValuesToString] Field ${String(field)} value '${convertedData[field]}' (type: ${typeof convertedData[field]}) was expected to be number for string conversion.`);
      }
    }
  }
  return convertedData;
};

const handleEdit = async (row: EmployeeListItem) => {
  resetForm();
  formMode.value = 'edit';
  dialogTitle.value = '编辑员工';
  currentRow.value = row;
  formLoading.value = true;
  try {
    // await loadAllEditViewDictionaries(); // 已移至 onMounted
    // if (!departmentOptions.value.length) await loadDepartmentTree(); // 已被 fullOrganizationTreeOptions 替代
    const res = await getEmployeeDetail(row.id);
    console.log('[DEBUG handleEdit] Raw API response (res):', JSON.parse(JSON.stringify(res)));

    formData.value = convertNumericDictValuesToString({ ...res });
    console.log('[DEBUG handleEdit] formData after conversion:', JSON.parse(JSON.stringify(formData.value)));
    // console.log('[DEBUG handleEdit] 毕业院校:', formData.value.employeeSchool, '(API val:', res.employeeSchool, ')');
    // console.log('[DEBUG handleEdit] 专业:', formData.value.employeeMajor, '(API val:', res.employeeMajor, ')');
    // console.log('[DEBUG handleEdit] 紧急联系人:', formData.value.employeeEmergencyContactName, '(API val:', res.employeeEmergencyContactName, ')');
    // console.log('[DEBUG handleEdit] 紧急联系电话:', formData.value.employeeEmergencyContactMobile, '(API val:', res.employeeEmergencyContactMobile, ')');
    // console.log('[DEBUG handleEdit] 户籍地址:', formData.value.employeeHouseholdAddress, '(API val:', res.employeeHouseholdAddress, ')');
    // console.log('[DEBUG handleEdit] 居住地址:', formData.value.employeeCurrentAddress, '(API val:', res.employeeCurrentAddress, ')');

    // 新增调试日志，用于追踪推荐人字段的回显问题
    console.log('[DEBUG handleEdit] Current employeeEntrySource:', formData.value.employeeEntrySource);
    console.log('[DEBUG handleEdit] Current employeeReferralName:', formData.value.employeeReferralName, 'Type:', typeof formData.value.employeeReferralName);

    if (formData.value.employeeEntrySource === '1') { // 校园招聘
      console.log('[DEBUG handleEdit] campusSchoolOptions:', JSON.parse(JSON.stringify(campusSchoolOptions.value)));
      const matchingOption = campusSchoolOptions.value.find(opt => opt.value === formData.value.employeeReferralName);
      console.log('[DEBUG handleEdit] Matching campusSchoolOption:', matchingOption);
    } else if (formData.value.employeeEntrySource === '4') { // 猎头推荐
      console.log('[DEBUG handleEdit] headhunterOptions:', JSON.parse(JSON.stringify(headhunterOptions.value)));
      const matchingOption = headhunterOptions.value.find(opt => opt.value === formData.value.employeeReferralName);
      console.log('[DEBUG handleEdit] Matching headhunterOption:', matchingOption);
    } else if (formData.value.employeeEntrySource === '6') { // 劳务派遣
      console.log('[DEBUG handleEdit] laborDispatchOptions:', JSON.parse(JSON.stringify(laborDispatchOptions.value)));
      const matchingOption = laborDispatchOptions.value.find(opt => opt.value === formData.value.employeeReferralName);
      console.log('[DEBUG handleEdit] Matching laborDispatchOption:', matchingOption);
    } else if (formData.value.employeeEntrySource === '3') { // 内部推荐
      // 对于内部推荐，我们已在watch中将选项value转为字符串，所以这里也查找字符串
      console.log('[DEBUG handleEdit] employeeSimpleListOptions (original, value might be number):', JSON.parse(JSON.stringify(employeeSimpleListOptions.value)));
      const matchingOption = employeeSimpleListOptions.value.find(opt => String(opt.value) === formData.value.employeeReferralName);
      console.log('[DEBUG handleEdit] Matching employeeSimpleListOption (after String(opt.value)):', matchingOption);
    }

    // 检查岗位选项是否需要加载
    if (formData.value.employeePositionId && (!positionOptions.value.length || !positionOptions.value.some(p => p.id === formData.value.employeePositionId))) {
        await loadPositionOptions();
    }
    dialogVisible.value = true;
    await nextTick();
    if (vnFormRef.value) {
      vnFormRef.value.clearValidate();
    }
  } catch (error) {
    console.error('Error fetching employee detail or dictionaries for edit:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    formLoading.value = false;
  }
};

const handleView = async (row: EmployeeListItem) => {
  // console.log('handleView called. Row:', row); // 移除旧日志
  resetForm();
  formMode.value = 'view';
  // console.log('formMode set to:', formMode.value, 'isViewing is now:', isViewing.value); // 移除旧日志
  dialogTitle.value = '查看员工详情';
  currentRow.value = row;
  formLoading.value = true;
  try {
    // await loadAllEditViewDictionaries(); // 已移至 onMounted
    // if (!departmentOptions.value.length) await loadDepartmentTree(); // 已被 fullOrganizationTreeOptions 替代
    const res = await getEmployeeDetail(row.id);
    console.log('[DEBUG handleView] Raw API response (res):', JSON.parse(JSON.stringify(res)));

    formData.value = convertNumericDictValuesToString({ ...res }); // 类型转换
    console.log('[DEBUG handleView] formData after conversion:', JSON.parse(JSON.stringify(formData.value)));
    console.log('[DEBUG handleView] 毕业院校:', formData.value.employeeSchool, '(API val:', res.employeeSchool, ')');
    console.log('[DEBUG handleView] 专业:', formData.value.employeeMajor, '(API val:', res.employeeMajor, ')');
    console.log('[DEBUG handleView] 紧急联系人:', formData.value.employeeEmergencyContactName, '(API val:', res.employeeEmergencyContactName, ')');
    console.log('[DEBUG handleView] 紧急联系电话:', formData.value.employeeEmergencyContactMobile, '(API val:', res.employeeEmergencyContactMobile, ')');
    console.log('[DEBUG handleView] 户籍地址:', formData.value.employeeHouseholdAddress, '(API val:', res.employeeHouseholdAddress, ')');
    console.log('[DEBUG handleView] 居住地址:', formData.value.employeeCurrentAddress, '(API val:', res.employeeCurrentAddress, ')');

    if (formData.value.employeePositionId && (!positionOptions.value.length || !positionOptions.value.some(p => p.id === formData.value.employeePositionId))) {
        await loadPositionOptions();
    }
    dialogVisible.value = true;
    // console.log('dialogVisible set to true. formMode:', formMode.value, 'isViewing:', isViewing.value); // 移除旧日志
    await nextTick();
    if (vnFormRef.value) {
      vnFormRef.value.clearValidate();
    }
  } catch (error) {
    console.error('Error fetching employee detail for view:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    formLoading.value = false;
  }
};

const submitForm = async () => {
  if (!vnFormRef.value) return;
  try {
    const isValid = await vnFormRef.value.validateForm();
    if (!isValid) {
        ElMessage.warning('表单校验失败，请检查输入项');
        return;
    }
    formLoading.value = true;

    // Create a deep copy for submission to avoid mutating the original reactive formData
    const dataToSubmit: Partial<EmployeeFormData> = JSON.parse(JSON.stringify(formData.value));

    // Fields that are numbers in backend DTO but might be strings from form due to select options
    const fieldsToConvert: (keyof EmployeeFormData)[] = [
      'employeeGender',
      'employeeStatus',
      'employeeEducation',
      'employeeMaritalStatus',
      'employeePoliticalStatus',
      'employeeIDCardType',
      'employeeWorkType',
      'employeeHealthStatus',
      'employeeDepartmentId', 
      'employeePositionId',
      // 'employeeNation',
    ];

    for (const field of fieldsToConvert) {
      const value = dataToSubmit[field];
      if (value !== null && value !== undefined) {
        if (String(value).trim() === '') {
          // dataToSubmit[field] = null; 
          (dataToSubmit as any)[field] = undefined; // Use undefined for fields typed as ?: number or ?: string
        } else {
          const numValue = Number(value);
          if (isNaN(numValue)) {
            console.warn(`Field ${String(field)} with value "${value}" is not a valid number. Setting to undefined.`);
            // dataToSubmit[field] = null;
            (dataToSubmit as any)[field] = undefined;
          } else {
            (dataToSubmit as any)[field] = numValue;
          }
        }
      } else {
        if (dataToSubmit.hasOwnProperty(field)) { 
            // dataToSubmit[field] = null;
            (dataToSubmit as any)[field] = undefined;
        }
      }
    }

    if (dataToSubmit.employeeSalary !== null && dataToSubmit.employeeSalary !== undefined) {
      if (String(dataToSubmit.employeeSalary).trim() === '') {
        // dataToSubmit.employeeSalary = null; 
        dataToSubmit.employeeSalary = undefined;
      } else {
        const salaryValue = parseFloat(String(dataToSubmit.employeeSalary));
        if (isNaN(salaryValue)) {
          console.warn(`Employee salary "${dataToSubmit.employeeSalary}" is not a valid number. Setting to undefined.`);
          // dataToSubmit.employeeSalary = null; 
          dataToSubmit.employeeSalary = undefined;
        } else {
          dataToSubmit.employeeSalary = salaryValue;
        }
      }
    } else {
        if (dataToSubmit.hasOwnProperty('employeeSalary')) {
            // dataToSubmit.employeeSalary = null;
            dataToSubmit.employeeSalary = undefined;
        }
    }

    if (dataToSubmit.employeeDepartmentId !== null && dataToSubmit.employeeDepartmentId !== undefined) {
        const deptId = Number(dataToSubmit.employeeDepartmentId);
        // if (isNaN(deptId)) dataToSubmit.employeeDepartmentId = null; else dataToSubmit.employeeDepartmentId = deptId;
        if (isNaN(deptId)) dataToSubmit.employeeDepartmentId = undefined; else dataToSubmit.employeeDepartmentId = deptId;
    }
    if (dataToSubmit.employeePositionId !== null && dataToSubmit.employeePositionId !== undefined) {
        const posId = Number(dataToSubmit.employeePositionId);
        // if (isNaN(posId)) dataToSubmit.employeePositionId = null; else dataToSubmit.employeePositionId = posId;
        if (isNaN(posId)) dataToSubmit.employeePositionId = undefined; else dataToSubmit.employeePositionId = posId;
    }

    if (formMode.value === 'add') {
      await createEmployee(dataToSubmit as EmployeeFormData);
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && formData.value.id) {
      await updateEmployee(formData.value.id, dataToSubmit as Partial<EmployeeFormData>);
      ElMessage.success('更新成功');
    }
    dialogVisible.value = false;
    loadData();
  } catch (error) {
    console.error('Error submitting form:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    formLoading.value = false;
  }
};

const handleDelete = (row: EmployeeListItem) => {
  ElMessageBox.confirm(`确定要删除员工 "${row.employeeName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      loading.value = true;
      try {
        await deleteEmployee(row.id);
        ElMessage.success('删除成功');
        loadData();
      } catch (error) {
        console.error('Error deleting employee:', error);
        const errorMessage = getApiErrorMessage(error);
        ElMessage({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: errorMessage,
          showClose: true,
          duration: 5 * 1000
        });
      } finally {
        loading.value = false;
      }
    })
    .catch(() => { 
      ElMessage.info('已取消删除'); 
    });
};

const selectedIds = ref<number[]>([]);
const handleSelectionChange = (selection: EmployeeListItem[]) => {
  selectedIds.value = selection.map((item) => item.id);
};

const handleBatchDelete = () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请至少选择一条数据进行删除');
    return;
  }
  ElMessageBox.confirm(`确定要批量删除选中的 ${selectedIds.value.length} 条员工数据吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      loading.value = true;
      try {
        await batchDeleteEmployees(selectedIds.value);
        ElMessage.success('批量删除成功');
        loadData();
        vnTableRef.value?.clearSelection();
      } catch (error) {
        console.error('Error batch deleting employees:', error);
        const errorMessage = getApiErrorMessage(error);
        ElMessage({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: errorMessage,
          showClose: true,
          duration: 5 * 1000
        });
      } finally {
        loading.value = false;
      }
    })
    .catch(() => { 
      ElMessage.info('已取消批量删除');
    });
};

const handleCloseDialog = () => {
  dialogVisible.value = false;
  resetForm();
};

const handleFilterChange = (filters: Record<string, any>) => {
  Object.assign(queryParams, filters);
  loadData();
};

const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

const handleSortChange = (params: { prop: string; order: 'ascending' | 'descending' | null }) => {
  queryParams.sort = params.prop ? `${params.prop},${params.order === 'ascending' ? 'asc' : 'desc'}` : undefined;
  loadData();
};

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile: UploadRawFile) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
  const maxSizeMB = 5;
  if (!allowedTypes.includes(rawFile.type)) {
    ElMessage.error('头像图片只支持 JPG/PNG/GIF 格式!');
    return false;
  }
  if (rawFile.size / 1024 / 1024 > maxSizeMB) {
    ElMessage.error(`头像图片大小不能超过 ${maxSizeMB}MB!`);
    return false;
  }
  return true;
};

const handleAvatarUpload: UploadRequestHandler = async (options) => {
  formLoading.value = true;
  try {
    const res = await uploadEmployeeAvatar(options.file as File);
    if (formData.value) {
      (formData.value as any).employeeAvatar = res.avatarUrl;
    }
    ElMessage.success('头像上传成功');
  } catch (error) {
    console.error('Avatar upload error:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    formLoading.value = false;
  }
  return Promise.resolve();
};

onMounted(() => {
  loadData();
  // Department tree and most dictionaries are loaded on demand in handleAdd/Edit/View
  loadPositionOptions(); // Positions might be needed for filtering or can be loaded on demand too
  loadFullOrganizationTree(); 
  
  // --- 将原 loadAllEditViewDictionaries 中的字典加载移到此处 ---
  loadGenderOptions();
  loadIdCardTypeOptions();
  loadEmployeeStatusOptions();
  loadWorkTypeOptions();
  loadNationOptions();
  loadPoliticalStatusOptions();
  loadMaritalStatusOptions();
  loadEducationOptions();
  loadHealthStatusOptions();
  loadDegreeOptions();
  loadEntrySourceOptions();
  loadEmergencyContactRelationshipOptions();
  loadJobGradeOptions();
  loadJobSubLevelOptions();
  loadWorkCategoryOptions();
  loadProfessionalTitleOptions();
  // --- 结束移动 ---
  
  // Linkage-based dictionaries can be loaded here or on demand
  loadCampusSchoolOptions();
  loadHeadhunterOptions();
  loadLaborDispatchOptions();
  loadEmployeeSimpleListOptions();
  initialLoadDone.value = true;
});

watch(dialogVisible, (newVal) => {
  if (newVal) {
    // Specific on-demand loading, like for position, if not already handled or if formData changes
    if ((formMode.value === 'edit' || formMode.value === 'view') && formData.value.employeePositionId) {
      if (!positionOptions.value.length || !positionOptions.value.some(p => p.id === formData.value.employeePositionId)) {
        loadPositionOptions(); 
      }
    }
  } else if (!newVal) {
    vnFormRef.value?.clearValidate();
  }
});

watch(() => formData.value.employeeEntrySource, async (newSourceValue, oldSourceValue) => {
  if (!initialLoadDone.value && (formMode.value === 'edit' || formMode.value === 'view')) {
    return;
  }
  
  if (newSourceValue !== oldSourceValue) {
    // Only reset employeeReferralName if it's not the initial data load for an existing record
    // where oldSourceValue was undefined due to resetForm.
    // This means if oldSourceValue is undefined AND we are in edit/view mode, we preserve the incoming employeeReferralName.
    // Otherwise (add mode, or user genuinely changed the select), we reset it.
    const isInitialLoadInEditOrView = (formMode.value === 'edit' || formMode.value === 'view') && oldSourceValue === undefined;
    if (!isInitialLoadInEditOrView) {
      formData.value.employeeReferralName = null;
    }
  }

  const referralField = formFields.value.find(f => f.field === 'employeeReferralName');
  if (!referralField) return;

  if (!referralField.props) {
    referralField.props = {};
  }
  
  let referralType: HeaderField['type'] = 'text';
  let referralOptions: any[] = [];
  let placeholder = '请输入推荐人姓名';
  let filterable: boolean | undefined = undefined;

  switch (newSourceValue) {
    case '1': // 校园招聘
      if (!campusSchoolOptions.value.length) await loadCampusSchoolOptions(); 
      referralType = 'select';
      referralOptions = campusSchoolOptions.value;
      placeholder = '请选择招聘院校';
      filterable = true;
      break;
    case '4': // 猎头推荐
      if (!headhunterOptions.value.length) await loadHeadhunterOptions();
      referralType = 'select';
      referralOptions = headhunterOptions.value;
      placeholder = '请选择猎头公司';
      filterable = true;
      break;
    case '6': // 劳务派遣
      if (!laborDispatchOptions.value.length) await loadLaborDispatchOptions();
      referralType = 'select';
      referralOptions = laborDispatchOptions.value;
      placeholder = '请选择劳务公司';
      filterable = true;
      break;
    case '3': // 内部推荐
      if (!employeeSimpleListOptions.value.length) await loadEmployeeSimpleListOptions();
      referralType = 'select';
      referralOptions = employeeSimpleListOptions.value.map(opt => ({
        ...opt,
        value: String(opt.value) 
      }));
      placeholder = '请选择推荐人(员工)';
      filterable = true; 
      break;
    default:
      referralType = 'text'; 
      referralOptions = [];
      placeholder = '请输入推荐人姓名/渠道';
      break;
  }

  referralField.type = referralType;
  referralField.options = referralOptions;
  
  if (!referralField.props) {
    referralField.props = {};
  }
  referralField.props['placeholder'] = placeholder;
  referralField.props['clearable'] = true;
  referralField.props['filterable'] = filterable;
  
  if (newSourceValue !== oldSourceValue && vnFormRef.value) {
    await nextTick(); 
    vnFormRef.value.clearValidate(['employeeReferralName']);
  }
}, { deep: false });

const handleImport = () => {
  ElMessage.info('导入功能待实现');
};
const handleExport = () => {
  ElMessage.info('导出功能待实现');
};

// --- 新增：打印处理函数（占位）---
const handlePrintPreview = () => {
  if (!currentRow.value) return;
  console.log('触发打印预览，员工:', currentRow.value);
  ElMessage.info('打印预览功能待实现');
  // TODO: Implement print preview logic
};

const handlePrint = () => {
  if (!currentRow.value) return;
  console.log('触发打印，员工:', currentRow.value);
  ElMessage.info('打印功能待实现');
  // TODO: Implement print logic
};

const loadFullOrganizationTree = async () => {
  try {
    const res = await getOrganizationNodeTree({}); 
    fullOrganizationTreeOptions.value = res || [];
  } catch (error) {
    console.error('Error loading full organization tree:', error);
    fullOrganizationTreeOptions.value = [];
  }
};

const isDepartmentDisabled = (data: OrganizationNodeVO): boolean => {
  // Company nodes should also be disabled for department selection
  return data.nodeType !== 'department'; 
};

const isPositionDisabled = (data: OrganizationNodeVO): boolean => {
  // console.log(`[isPositionDisabled] Node Name: "${data.name}", NodeType: "${data.nodeType}"`); 
  // Only 'position' type nodes are selectable. All others (company, department) are disabled.
  const isDisabled = data.nodeType !== 'position'; // Changed 'Position' to 'position'
  // if (!isDisabled) {
  //   console.log(`[isPositionDisabled] Node "${data.name}" (Type: "${data.nodeType}") is considered ENABLED.`);
  // }
  return isDisabled; 
};

</script>

<style scoped lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* Styles for the disabled salary field text wrapper */
.custom-disabled-text-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: var(--el-component-size, 32px); /* Match Element Plus input height */
  padding: 1px 11px; /* Simulate el-input padding */
  background-color: var(--el-disabled-bg-color, #f5f7fa); /* Simulate disabled bg color */
  border: 1px solid var(--el-disabled-border-color, #e4e7ed); /* Simulate disabled border color */
  border-radius: var(--el-border-radius-base, 4px); /* Simulate border radius */
  box-shadow: 0 0 0 0 transparent; /* Ensure no other shadows */
  cursor: not-allowed; /* Simulate disabled cursor */
  overflow: hidden; /* Prevent text from overflowing */
}

.custom-disabled-text-wrapper .custom-disabled-text {
  color: var(--el-text-color-placeholder, #a8abb2); /* Use placeholder or disabled text color */
  font-size: inherit;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%; /* Ensure text uses available space for ellipsis to work */
  /* For numbers, you might want to text-align: right; */
  /* text-align: right; */ 
}
</style> 