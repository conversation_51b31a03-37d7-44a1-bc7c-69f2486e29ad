package service

import (
	"context"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
)

// WmsInventoryService 库存管理服务接口
type WmsInventoryService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsInventoryCreateReq) (*vo.WmsInventoryVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsInventoryUpdateReq) (*vo.WmsInventoryVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsInventoryVO, error)
	GetPage(ctx context.Context, req *dto.WmsInventoryQueryReq) (*vo.PageResult[vo.WmsInventoryVO], error)

	// 库存操作
	Reserve(ctx context.Context, req *dto.WmsInventoryReserveReq) error
	Release(ctx context.Context, req *dto.WmsInventoryReleaseReq) error
	Freeze(ctx context.Context, req *dto.WmsInventoryFreezeReq) error
	Unfreeze(ctx context.Context, req *dto.WmsInventoryUnfreezeReq) error

	// 库存调整
	Adjust(ctx context.Context, req *dto.WmsInventoryAdjustReq) (*vo.WmsInventoryVO, error)
	BatchAdjust(ctx context.Context, req *dto.WmsInventoryBatchAdjustReq) ([]*vo.WmsInventoryVO, error)

	// 库存查询
	GetByLocation(ctx context.Context, locationId uint) ([]*vo.WmsInventoryVO, error)
	GetByItem(ctx context.Context, itemId uint) ([]*vo.WmsInventoryVO, error)
	GetByWarehouse(ctx context.Context, warehouseId uint) ([]*vo.WmsInventoryVO, error)
	GetAvailable(ctx context.Context, itemId uint, warehouseId *uint, requiredQty float64) ([]*vo.WmsInventoryVO, error)

	// 库存统计
	GetSummary(ctx context.Context, req *dto.WmsInventorySummaryReq) (*vo.WmsInventorySummaryVO, error)
	GetStats(ctx context.Context, warehouseId *uint) (*vo.WmsInventoryStatsVO, error)

	// 库存验证
	CheckAvailability(ctx context.Context, itemId uint, requiredQty float64, warehouseId *uint) (bool, error)
	ValidateOperation(ctx context.Context, req *dto.WmsInventoryOperationReq) error
}

// wmsInventoryServiceImpl 库存管理服务实现
type wmsInventoryServiceImpl struct {
	BaseServiceImpl
}

// NewWmsInventoryService 创建库存管理服务
func NewWmsInventoryService(sm *ServiceManager) WmsInventoryService {
	return &wmsInventoryServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建库存记录
func (s *wmsInventoryServiceImpl) Create(ctx context.Context, req *dto.WmsInventoryCreateReq) (*vo.WmsInventoryVO, error) {
	var result *vo.WmsInventoryVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryRepository()

		// 创建库存实体
		inventory := &entity.WmsInventory{
			ItemID:         req.ItemID,
			LocationID:     req.LocationID,
			BatchNo:        req.BatchNo,
			Quantity:       req.Quantity,
			AllocatedQty:   0,
			FrozenQty:      0,
			UnitOfMeasure:  req.UnitOfMeasure,
			Status:         string(req.Status),
			ExpiryDate:     req.ExpiryDate,
			ProductionDate: req.ProductionDate,
			Remark:         req.Remark,
		}

		if err := repo.Create(ctx, inventory); err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_CREATE_FAILED, "创建库存记录失败").WithCause(err)
		}

		// 转换为VO
		result = &vo.WmsInventoryVO{}
		if err := copier.Copy(result, inventory); err != nil {
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// Update 更新库存记录
func (s *wmsInventoryServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsInventoryUpdateReq) (*vo.WmsInventoryVO, error) {
	var result *vo.WmsInventoryVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryRepository()

		// 获取现有记录
		inventory, err := repo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存记录失败").WithCause(err)
		}
		if inventory == nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存记录不存在")
		}

		// 更新字段
		if req.Quantity != nil {
			inventory.Quantity = *req.Quantity
		}
		if req.Status != nil {
			inventory.Status = string(*req.Status)
		}
		if req.ExpiryDate != nil {
			inventory.ExpiryDate = req.ExpiryDate
		}
		if req.Remark != nil {
			inventory.Remark = req.Remark
		}

		if err := repo.Update(ctx, inventory); err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_UPDATE_FAILED, "更新库存记录失败").WithCause(err)
		}

		// 转换为VO
		result = &vo.WmsInventoryVO{}
		if err := copier.Copy(result, inventory); err != nil {
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// Delete 删除库存记录
func (s *wmsInventoryServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryRepository()

		// 检查库存是否可以删除
		inventory, err := repo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存记录失败").WithCause(err)
		}
		if inventory == nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存记录不存在")
		}

		if inventory.Quantity > 0 {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "库存数量大于0，不能删除")
		}

		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_DELETE_FAILED, "删除库存记录失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取库存记录
func (s *wmsInventoryServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventory, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存记录失败").WithCause(err)
	}
	if inventory == nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存记录不存在")
	}

	result := &vo.WmsInventoryVO{}
	if err := copier.Copy(result, inventory); err != nil {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
	}

	return result, nil
}

// GetPage 分页查询库存记录
func (s *wmsInventoryServiceImpl) GetPage(ctx context.Context, req *dto.WmsInventoryQueryReq) (*vo.PageResult[vo.WmsInventoryVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存记录失败").WithCause(err)
	}

	// 转换为VO
	result := &vo.PageResult[vo.WmsInventoryVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsInventoryVO, 0),
	}

	// 类型断言获取实体列表
	entities, ok := pageResult.List.([]*entity.WmsInventory)
	if !ok {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "分页结果类型转换失败")
	}

	for _, record := range entities {
		vo := &vo.WmsInventoryVO{}
		if err := copier.Copy(vo, record); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
		}
		result.List = append(result.List, vo)
	}

	return result, nil
}

// Reserve 预占库存
func (s *wmsInventoryServiceImpl) Reserve(ctx context.Context, req *dto.WmsInventoryReserveReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryRepository()

		return repo.ReserveInventory(ctx, req.InventoryID, req.ReservedQty)
	})
}

// Release 释放库存预占
func (s *wmsInventoryServiceImpl) Release(ctx context.Context, req *dto.WmsInventoryReleaseReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryRepository()

		return repo.ReleaseReservation(ctx, req.InventoryID, req.ReleasedQty)
	})
}

// Freeze 冻结库存
func (s *wmsInventoryServiceImpl) Freeze(ctx context.Context, req *dto.WmsInventoryFreezeReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryRepository()

		// 查询库存记录
		inventory, err := repo.FindByID(ctx, req.InventoryID)
		if err != nil {
			return err
		}
		if inventory == nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存记录不存在")
		}

		// 检查可用数量
		availableQty := inventory.AvailableQuantity()
		if req.FrozenQty > availableQty {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "冻结数量超过可用库存")
		}

		// 更新冻结数量
		inventory.FrozenQty += req.FrozenQty
		return repo.Update(ctx, inventory)
	})
}

// Unfreeze 解冻库存
func (s *wmsInventoryServiceImpl) Unfreeze(ctx context.Context, req *dto.WmsInventoryUnfreezeReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryRepository()

		// 查询库存记录
		inventory, err := repo.FindByID(ctx, req.InventoryID)
		if err != nil {
			return err
		}
		if inventory == nil {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存记录不存在")
		}

		// 检查冻结数量
		if req.UnfrozenQty > inventory.FrozenQty {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "解冻数量超过冻结库存")
		}

		// 更新冻结数量
		inventory.FrozenQty -= req.UnfrozenQty
		return repo.Update(ctx, inventory)
	})
}

// Adjust 库存调整
func (s *wmsInventoryServiceImpl) Adjust(ctx context.Context, req *dto.WmsInventoryAdjustReq) (*vo.WmsInventoryVO, error) {
	// 创建调整记录请求
	adjustmentReq := &dto.WmsInventoryAdjustmentCreateReq{
		InventoryID:       req.InventoryID,
		AdjustmentType:    req.AdjustmentType,
		QuantityChange:    req.QuantityChange,
		StatusAfter:       req.StatusAfter,
		ReasonCode:        req.ReasonCode,
		ReasonDescription: req.ReasonDescription,
	}

	// 使用调整服务创建并执行调整
	adjustmentService := s.GetServiceManager().GetWmsInventoryAdjustmentService()
	adjustmentVO, err := adjustmentService.Create(ctx, adjustmentReq)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "创建库存调整失败").WithCause(err)
	}

	// 如果调整不需要审批，直接执行
	if !adjustmentVO.IsApprovalRequired {
		if err := adjustmentService.ExecuteSingle(ctx, adjustmentVO.ID); err != nil {
			return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "执行库存调整失败").WithCause(err)
		}
	}

	// 返回更新后的库存信息
	return s.GetByID(ctx, req.InventoryID)
}

// BatchAdjust 批量库存调整
func (s *wmsInventoryServiceImpl) BatchAdjust(ctx context.Context, req *dto.WmsInventoryBatchAdjustReq) ([]*vo.WmsInventoryVO, error) {
	var results []*vo.WmsInventoryVO

	// 逐个处理调整
	for _, adjustReq := range req.Adjustments {
		result, err := s.Adjust(ctx, &adjustReq)
		if err != nil {
			return nil, err
		}
		results = append(results, result)
	}

	return results, nil
}

// GetByLocation 根据库位获取库存
func (s *wmsInventoryServiceImpl) GetByLocation(ctx context.Context, locationId uint) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindByLocationID(ctx, locationId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存失败").WithCause(err)
	}

	var results []*vo.WmsInventoryVO
	for _, inventory := range inventories {
		vo := &vo.WmsInventoryVO{}
		if err := copier.Copy(vo, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
		}
		results = append(results, vo)
	}

	return results, nil
}

// GetByItem 根据物料获取库存
func (s *wmsInventoryServiceImpl) GetByItem(ctx context.Context, itemId uint) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindByItemID(ctx, itemId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存失败").WithCause(err)
	}

	var results []*vo.WmsInventoryVO
	for _, inventory := range inventories {
		vo := &vo.WmsInventoryVO{}
		if err := copier.Copy(vo, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
		}
		results = append(results, vo)
	}

	return results, nil
}

// GetByWarehouse 根据仓库获取库存
func (s *wmsInventoryServiceImpl) GetByWarehouse(ctx context.Context, warehouseId uint) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindByWarehouseID(ctx, warehouseId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存失败").WithCause(err)
	}

	var results []*vo.WmsInventoryVO
	for _, inventory := range inventories {
		vo := &vo.WmsInventoryVO{}
		if err := copier.Copy(vo, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
		}
		results = append(results, vo)
	}

	return results, nil
}

// GetAvailable 获取可用库存
func (s *wmsInventoryServiceImpl) GetAvailable(ctx context.Context, itemId uint, warehouseId *uint, requiredQty float64) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindAvailableByItem(ctx, itemId, warehouseId, requiredQty)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询可用库存失败").WithCause(err)
	}

	var results []*vo.WmsInventoryVO
	for _, inventory := range inventories {
		vo := &vo.WmsInventoryVO{}
		if err := copier.Copy(vo, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "数据转换失败").WithCause(err)
		}
		results = append(results, vo)
	}

	return results, nil
}

// GetSummary 获取库存汇总
func (s *wmsInventoryServiceImpl) GetSummary(ctx context.Context, req *dto.WmsInventorySummaryReq) (*vo.WmsInventorySummaryVO, error) {
	// TODO: 实现库存汇总逻辑
	return &vo.WmsInventorySummaryVO{}, nil
}

// GetStats 获取库存统计
func (s *wmsInventoryServiceImpl) GetStats(ctx context.Context, warehouseId *uint) (*vo.WmsInventoryStatsVO, error) {
	// TODO: 实现库存统计逻辑
	return &vo.WmsInventoryStatsVO{}, nil
}

// ValidateOperation 验证库存操作
func (s *wmsInventoryServiceImpl) ValidateOperation(ctx context.Context, req *dto.WmsInventoryOperationReq) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	// 查询库存记录
	inventory, err := repo.FindByID(ctx, req.InventoryID)
	if err != nil {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存失败").WithCause(err)
	}
	if inventory == nil {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存记录不存在")
	}

	// 根据操作类型进行验证
	availableQty := inventory.AvailableQuantity()
	switch req.OperationType {
	case "RESERVE":
		if req.Quantity > availableQty {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "预占数量超过可用库存")
		}
	case "FREEZE":
		if req.Quantity > availableQty {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "冻结数量超过可用库存")
		}
	case "ADJUST":
		if req.Quantity < 0 && (-req.Quantity) > inventory.Quantity {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "调整后库存不能为负数")
		}
	default:
		return apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, "不支持的操作类型")
	}

	return nil
}

// CheckAvailability 检查库存可用性
func (s *wmsInventoryServiceImpl) CheckAvailability(ctx context.Context, itemId uint, requiredQty float64, warehouseId *uint) (bool, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	return repo.CheckAvailability(ctx, itemId, requiredQty, warehouseId)
}
