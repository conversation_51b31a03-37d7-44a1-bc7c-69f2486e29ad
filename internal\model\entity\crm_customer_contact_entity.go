package entity

import (
	"time"
)

// CrmCustomerContact 客户联系人实体
// 继承自AccountBookEntity，提供多账套数据隔离
// 用于管理客户的联系人信息，支持一个客户多个联系人
type CrmCustomerContact struct {
	AccountBookEntity

	// 关联信息
	CustomerID uint `gorm:"column:customer_id;not null;index;comment:客户ID" json:"customerId" validation:"required"` // 客户ID：关联的客户主记录

	// 联系人基本信息
	ContactName  string  `gorm:"column:contact_name;size:100;not null;comment:联系人姓名" json:"contactName" validation:"required,max=100"` // 联系人姓名：联系人的真实姓名
	ContactTitle *string `gorm:"column:contact_title;size:100;comment:职务" json:"contactTitle" validation:"omitempty,max=100"`          // 职务：联系人在客户公司的职务
	Department   *string `gorm:"column:department;size:100;comment:所属部门" json:"department" validation:"omitempty,max=100"`             // 所属部门：联系人所在的部门

	// 联系方式
	Phone  *string `gorm:"column:phone;size:50;comment:电话" json:"phone" validation:"omitempty,max=50"`         // 电话：固定电话号码
	Mobile *string `gorm:"column:mobile;size:50;comment:手机" json:"mobile" validation:"omitempty,max=50"`       // 手机：移动电话号码
	Email  *string `gorm:"column:email;size:255;comment:邮箱" json:"email" validation:"omitempty,email,max=255"` // 邮箱：电子邮件地址
	QQ     *string `gorm:"column:qq;size:50;comment:QQ号" json:"qq" validation:"omitempty,max=50"`              // QQ号：QQ即时通讯号码
	Wechat *string `gorm:"column:wechat;size:100;comment:微信号" json:"wechat" validation:"omitempty,max=100"`    // 微信号：微信账号

	// 地址信息
	Address    *string `gorm:"column:address;type:text;comment:联系地址" json:"address"`                                    // 联系地址：联系人的详细地址
	PostalCode *string `gorm:"column:postal_code;size:20;comment:邮政编码" json:"postalCode" validation:"omitempty,max=20"` // 邮政编码：地址对应的邮政编码

	// 个人信息
	Birthday *time.Time `gorm:"column:birthday;comment:生日" json:"birthday"` // 生日：联系人的出生日期
	Gender   *string    `gorm:"column:gender;comment:性别" json:"gender"`     // 性别：引用字典 gender

	// 业务信息
	ContactRole *string `gorm:"column:contact_role;size:50;comment:联系人角色" json:"contactRole"` // 联系人角色：引用字典 contact_role
	Remark      *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`             // 备注：其他相关信息

	// 关联关系 - 反向关联到客户
	Customer *CrmCustomer `gorm:"foreignKey:CustomerID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;comment:所属客户" json:"customer,omitempty"` // 所属客户：多对一关系
}

// TableName 设置表名
func (CrmCustomerContact) TableName() string {
	return "crm_customer_contact"
}
