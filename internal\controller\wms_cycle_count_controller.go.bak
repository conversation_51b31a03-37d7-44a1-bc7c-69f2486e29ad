package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
	"strconv"

	"github.com/kataras/iris/v12"
)

// WmsCycleCountController 盘点控制器
type WmsCycleCountController struct {
	cycleCountService service.WmsCycleCountService
}

// NewWmsCycleCountController 创建盘点控制器实例
func NewWmsCycleCountController(cycleCountService service.WmsCycleCountService) *WmsCycleCountController {
	return &WmsCycleCountController{
		cycleCountService: cycleCountService,
	}
}

// CreatePlan 创建盘点计划
// @Summary 创建盘点计划
// @Description 创建盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param request body dto.WmsCycleCountPlanCreateReq true "计划信息"
// @Success 200 {object} response.Response{data=vo.WmsCycleCountPlanVO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan [post]
func (c *WmsCycleCountController) CreatePlan(ctx iris.Context) {
	var req dto.WmsCycleCountPlanCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	result, err := c.cycleCountService.CreatePlan(ctx, &req)
	if err != nil {
		response.FailWithMessage("创建盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// UpdatePlan 更新盘点计划
// @Summary 更新盘点计划
// @Description 更新盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Param request body dto.WmsCycleCountPlanUpdateReq true "更新信息"
// @Success 200 {object} response.Response{data=vo.WmsCycleCountPlanVO} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/{id} [put]
func (c *WmsCycleCountController) UpdatePlan(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	var req dto.WmsCycleCountPlanUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	result, err := c.cycleCountService.UpdatePlan(ctx, uint(id), &req)
	if err != nil {
		response.FailWithMessage("更新盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// DeletePlan 删除盘点计划
// @Summary 删除盘点计划
// @Description 删除盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/{id} [delete]
func (c *WmsCycleCountController) DeletePlan(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.DeletePlan(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("删除盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// GetPlan 获取盘点计划详情
// @Summary 获取盘点计划详情
// @Description 根据ID获取盘点计划详细信息
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Success 200 {object} response.Response{data=vo.WmsCycleCountPlanVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/{id} [get]
func (c *WmsCycleCountController) GetPlan(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	// 调用服务
	result, err := c.cycleCountService.GetPlan(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("获取盘点计划详情失败: "+err.Error(), ctx)
		return
	}

	if result == nil {
		response.FailWithMessage("盘点计划不存在", ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetPlanPage 分页查询盘点计划
// @Summary 分页查询盘点计划
// @Description 根据条件分页查询盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param request body dto.WmsCycleCountPlanQueryReq true "查询条件"
// @Success 200 {object} response.Response{data=vo.WmsCycleCountPlanPageVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/page [post]
func (c *WmsCycleCountController) GetPlanPage(ctx iris.Context) {
	var req dto.WmsCycleCountPlanQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 调用服务
	result, err := c.cycleCountService.GetPlanPage(ctx, &req)
	if err != nil {
		response.FailWithMessage("查询盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// ApprovePlan 审批盘点计划
// @Summary 审批盘点计划
// @Description 审批盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Param request body dto.WmsCycleCountPlanApprovalReq true "审批信息"
// @Success 200 {object} response.Response "审批成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/{id}/approve [post]
func (c *WmsCycleCountController) ApprovePlan(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	var req dto.WmsCycleCountPlanApprovalReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.ApprovePlan(ctx, uint(id), req.ApprovedBy, req.Remark)
	if err != nil {
		response.FailWithMessage("审批盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// RejectPlan 拒绝盘点计划
// @Summary 拒绝盘点计划
// @Description 拒绝盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Param request body dto.WmsCycleCountPlanRejectReq true "拒绝信息"
// @Success 200 {object} response.Response "拒绝成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/{id}/reject [post]
func (c *WmsCycleCountController) RejectPlan(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	var req dto.WmsCycleCountPlanRejectReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.RejectPlan(ctx, uint(id), req.ApprovedBy, req.Reason)
	if err != nil {
		response.FailWithMessage("拒绝盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// StartPlan 开始盘点计划
// @Summary 开始盘点计划
// @Description 开始执行盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Success 200 {object} response.Response "开始成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/{id}/start [post]
func (c *WmsCycleCountController) StartPlan(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.StartPlan(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("开始盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// CompletePlan 完成盘点计划
// @Summary 完成盘点计划
// @Description 完成盘点计划
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Success 200 {object} response.Response "完成成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/plan/{id}/complete [post]
func (c *WmsCycleCountController) CompletePlan(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.CompletePlan(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("完成盘点计划失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// GetTask 获取盘点任务详情
// @Summary 获取盘点任务详情
// @Description 根据ID获取盘点任务详细信息
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} response.Response{data=vo.WmsCycleCountTaskVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/task/{id} [get]
func (c *WmsCycleCountController) GetTask(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的任务ID", ctx)
		return
	}

	// 调用服务
	result, err := c.cycleCountService.GetTask(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("获取盘点任务详情失败: "+err.Error(), ctx)
		return
	}

	if result == nil {
		response.FailWithMessage("盘点任务不存在", ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetTaskPage 分页查询盘点任务
// @Summary 分页查询盘点任务
// @Description 根据条件分页查询盘点任务
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param request body dto.WmsCycleCountTaskQueryReq true "查询条件"
// @Success 200 {object} response.Response{data=vo.WmsCycleCountTaskPageVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/task/page [post]
func (c *WmsCycleCountController) GetTaskPage(ctx iris.Context) {
	var req dto.WmsCycleCountTaskQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 调用服务
	result, err := c.cycleCountService.GetTaskPage(ctx, &req)
	if err != nil {
		response.FailWithMessage("查询盘点任务失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// SubmitCount 提交盘点结果
// @Summary 提交盘点结果
// @Description 提交盘点结果
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param request body dto.WmsCycleCountSubmitReq true "盘点结果"
// @Success 200 {object} response.Response "提交成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/task/submit [post]
func (c *WmsCycleCountController) SubmitCount(ctx iris.Context) {
	var req dto.WmsCycleCountSubmitReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err := c.cycleCountService.SubmitCount(ctx, &req)
	if err != nil {
		response.FailWithMessage("提交盘点结果失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// StartTask 开始盘点任务
// @Summary 开始盘点任务
// @Description 开始执行盘点任务
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Param request body dto.WmsCycleCountTaskStartReq true "开始信息"
// @Success 200 {object} response.Response "开始成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/task/{id}/start [post]
func (c *WmsCycleCountController) StartTask(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的任务ID", ctx)
		return
	}

	var req dto.WmsCycleCountTaskStartReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.StartTask(ctx, uint(id), req.CounterUserID)
	if err != nil {
		response.FailWithMessage("开始盘点任务失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// AssignTasks 分配盘点任务
// @Summary 分配盘点任务
// @Description 分配盘点任务给操作员
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param request body dto.WmsCycleCountTaskAssignReq true "分配信息"
// @Success 200 {object} response.Response "分配成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/task/assign [post]
func (c *WmsCycleCountController) AssignTasks(ctx iris.Context) {
	var req dto.WmsCycleCountTaskAssignReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err := c.cycleCountService.AssignTasks(ctx, req.TaskIDs, req.CounterUserID)
	if err != nil {
		response.FailWithMessage("分配盘点任务失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// ConfirmVariance 确认差异
// @Summary 确认差异
// @Description 确认盘点差异
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Param request body dto.WmsCycleCountVarianceConfirmReq true "确认信息"
// @Success 200 {object} response.Response "确认成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/task/{id}/confirm-variance [post]
func (c *WmsCycleCountController) ConfirmVariance(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的任务ID", ctx)
		return
	}

	var req dto.WmsCycleCountVarianceConfirmReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.ConfirmVariance(ctx, uint(id), req.Reason)
	if err != nil {
		response.FailWithMessage("确认差异失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// CreateAdjustmentFromVariance 根据差异创建调整
// @Summary 根据差异创建调整
// @Description 根据盘点差异创建库存调整
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Param request body dto.WmsCycleCountCreateAdjustmentReq true "创建调整信息"
// @Success 200 {object} response.Response "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/cycle-count/task/{id}/create-adjustment [post]
func (c *WmsCycleCountController) CreateAdjustmentFromVariance(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的任务ID", ctx)
		return
	}

	var req dto.WmsCycleCountCreateAdjustmentReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err = c.cycleCountService.CreateAdjustmentFromVariance(ctx, uint(id), req.OperatorID)
	if err != nil {
		response.FailWithMessage("创建调整失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// GetPlanStatistics 获取盘点计划统计
// @Summary 获取盘点计划统计
// @Description 获取盘点计划统计信息
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Success 200 {object} response.Response{data=vo.WmsCycleCountPlanStatsVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/cycle-count/plan/{id}/statistics [get]
func (c *WmsCycleCountController) GetPlanStatistics(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	// 调用服务
	result, err := c.cycleCountService.GetPlanStatistics(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("获取盘点计划统计失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetVarianceReport 获取差异报告
// @Summary 获取差异报告
// @Description 获取盘点差异报告
// @Tags 盘点管理
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Success 200 {object} response.Response{data=[]vo.WmsCycleCountVarianceVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/cycle-count/plan/{id}/variance-report [get]
func (c *WmsCycleCountController) GetVarianceReport(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的计划ID", ctx)
		return
	}

	// 调用服务
	result, err := c.cycleCountService.GetVarianceReport(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("获取差异报告失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}
