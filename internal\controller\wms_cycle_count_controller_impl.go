package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
)

// WmsCycleCountController 定义盘点控制器接口
type WmsCycleCountController interface {
	// 盘点计划管理
	CreatePlan(ctx iris.Context)
	UpdatePlan(ctx iris.Context)
	DeletePlan(ctx iris.Context)
	GetPlan(ctx iris.Context)
	GetPlanPage(ctx iris.Context)

	// 盘点计划审批流程
	ApprovePlan(ctx iris.Context)
	RejectPlan(ctx iris.Context)

	// 盘点任务管理
	CreateTask(ctx iris.Context)
	UpdateTask(ctx iris.Context)
	DeleteTask(ctx iris.Context)
	GetTask(ctx iris.Context)
	GetTaskPage(ctx iris.Context)

	// 盘点执行
	StartPlan(ctx iris.Context)
	CompletePlan(ctx iris.Context)
	StartTask(ctx iris.Context)
	CompleteTask(ctx iris.Context)
	SubmitCount(ctx iris.Context)
	AssignTasks(ctx iris.Context)

	// 盘点差异处理
	ConfirmVariance(ctx iris.Context)
	RejectVariance(ctx iris.Context)
	CreateAdjustmentFromVariance(ctx iris.Context)
	GenerateAdjustment(ctx iris.Context)

	// 批量操作
	BatchCreateTasks(ctx iris.Context)
	BatchAssignCounter(ctx iris.Context)

	// 统计分析
	GetPlanStats(ctx iris.Context)
	GetPlanStatistics(ctx iris.Context)
	GetVarianceReport(ctx iris.Context)
	GetCounterStats(ctx iris.Context)
}

// wmsCycleCountControllerImpl 盘点控制器实现
type WmsCycleCountControllerImpl struct {
	BaseControllerImpl
	cycleCountService service.WmsCycleCountService
}

// NewWmsCycleCountControllerImpl 创建盘点控制器实例
func NewWmsCycleCountControllerImpl(cm *ControllerManager) *WmsCycleCountControllerImpl {
	return &WmsCycleCountControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		cycleCountService:  cm.GetServiceManager().GetWmsCycleCountService(),
	}
}

// CreatePlan 创建盘点计划
func (c *WmsCycleCountControllerImpl) CreatePlan(ctx iris.Context) {
	var req dto.WmsCycleCountPlanCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.cycleCountService.CreatePlan(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// UpdatePlan 更新盘点计划
func (c *WmsCycleCountControllerImpl) UpdatePlan(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	var req dto.WmsCycleCountPlanUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.cycleCountService.UpdatePlan(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// DeletePlan 删除盘点计划
func (c *WmsCycleCountControllerImpl) DeletePlan(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	err = c.cycleCountService.DeletePlan(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetPlan 根据ID获取盘点计划
func (c *WmsCycleCountControllerImpl) GetPlan(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	result, err := c.cycleCountService.GetPlan(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// GetPlanPage 分页查询盘点计划
func (c *WmsCycleCountControllerImpl) GetPlanPage(ctx iris.Context) {
	var req dto.WmsCycleCountPlanQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.cycleCountService.GetPlanPage(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// CreateTask 创建盘点任务
func (c *WmsCycleCountControllerImpl) CreateTask(ctx iris.Context) {
	var req dto.WmsCycleCountTaskCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.cycleCountService.CreateTask(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// UpdateTask 更新盘点任务
func (c *WmsCycleCountControllerImpl) UpdateTask(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	var req dto.WmsCycleCountTaskUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.cycleCountService.UpdateTask(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// DeleteTask 删除盘点任务
func (c *WmsCycleCountControllerImpl) DeleteTask(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	err = c.cycleCountService.DeleteTask(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetTask 根据ID获取盘点任务
func (c *WmsCycleCountControllerImpl) GetTask(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	result, err := c.cycleCountService.GetTask(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// GetTaskPage 分页查询盘点任务
func (c *WmsCycleCountControllerImpl) GetTaskPage(ctx iris.Context) {
	var req dto.WmsCycleCountTaskQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.cycleCountService.GetTaskPage(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// StartPlan 启动盘点计划
func (c *WmsCycleCountControllerImpl) StartPlan(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	err = c.cycleCountService.StartPlan(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// CompletePlan 完成盘点计划
func (c *WmsCycleCountControllerImpl) CompletePlan(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	err = c.cycleCountService.CompletePlan(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// StartTask 开始盘点任务
func (c *WmsCycleCountControllerImpl) StartTask(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	var req dto.WmsCycleCountTaskStartReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	err = c.cycleCountService.StartTask(ctx.Request().Context(), uint(id), req.CounterUserID)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// ConfirmVariance 确认差异
func (c *WmsCycleCountControllerImpl) ConfirmVariance(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	var req dto.WmsCycleCountVarianceReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	err = c.cycleCountService.ConfirmVariance(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// ApprovePlan 审批盘点计划
func (c *WmsCycleCountControllerImpl) ApprovePlan(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	_, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	// TODO: 实现审批逻辑，暂时返回成功
	response.Success(ctx, nil)
}

// RejectPlan 拒绝盘点计划
func (c *WmsCycleCountControllerImpl) RejectPlan(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	_, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	// TODO: 实现拒绝逻辑，暂时返回成功
	response.Success(ctx, nil)
}

// GetPlanStatistics 获取盘点计划统计
func (c *WmsCycleCountControllerImpl) GetPlanStatistics(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	result, err := c.cycleCountService.GetPlanStats(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}

// GetVarianceReport 获取差异报告
func (c *WmsCycleCountControllerImpl) GetVarianceReport(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	_, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	// TODO: 实现差异报告获取逻辑，暂时返回空数组
	response.Success(ctx, []interface{}{})
}

// SubmitCount 提交盘点结果
func (c *WmsCycleCountControllerImpl) SubmitCount(ctx iris.Context) {
	var req dto.WmsCycleCountSubmitReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	// TODO: 实现提交盘点结果逻辑，暂时返回成功
	response.Success(ctx, nil)
}

// AssignTasks 分配盘点任务
func (c *WmsCycleCountControllerImpl) AssignTasks(ctx iris.Context) {
	var req dto.WmsCycleCountTaskAssignReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	err := c.cycleCountService.BatchAssignCounter(ctx.Request().Context(), req.TaskIDs, req.CounterUserID)
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// RejectVariance 拒绝差异
func (c *WmsCycleCountControllerImpl) RejectVariance(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	_, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	// TODO: 实现拒绝差异逻辑，暂时返回成功
	response.Success(ctx, nil)
}

// CreateAdjustmentFromVariance 从差异创建调整单
func (c *WmsCycleCountControllerImpl) CreateAdjustmentFromVariance(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5004, "无效的ID参数")
		return
	}

	result, err := c.cycleCountService.GenerateAdjustment(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 1000, err.Error())
		return
	}

	response.Success(ctx, result)
}
