// Below is the code of d:\lasoft\backend\pkg\database\interface.go
// 数据库接口，用于管理数据库连接、事务等操作。
package database

import (
	"context"
	"database/sql" // Import standard sql

	// Removed config and logger imports as they are not needed for interfaces
	"gorm.io/gorm"
)

// DBClient 数据库客户端接口
// 定义了与数据库连接交互的基本操作。
// 具体的实现 (如 *MySQLClient, *PostgreSQLClient) 负责处理特定数据库的细节。
type DBClient interface {
	Connect() (*gorm.DB, error)     // 建立或确认数据库连接，可能包括数据库创建逻辑
	Disconnect() error              // 断开数据库连接
	Ping(ctx context.Context) error // 测试数据库连接是否活跃
	GetDB() *gorm.DB                // 获取 GORM 数据库实例 (确保已连接)
}

// TxFunc 定义在事务中执行的函数类型
// 这个类型在 transaction.go 中也有定义，放在这里或 transaction.go 都可以，但放在这里更通用
type TxFunc func(tx *gorm.DB) error

// TransactionMiddleware GORM事务中间件函数类型
// 这个类型在 transaction.go 中也有定义
type TransactionMiddleware func(db *gorm.DB, next TxFunc) error

// TxManager 事务管理器接口
// 定义了执行数据库事务的操作。
// 通常由 *TransactionManager 实现。
type TxManager interface {
	WithTransaction(fn func(txRepoManager *RepositoryManager) error) error
	WithTransactionByLevel(level string, fn func(txRepoManager *RepositoryManager) error) error
	Begin() (*RepositoryManager, error)
	Commit() error
	Rollback() error
	IsInTransaction() bool
}

// DBManager 数据库管理器接口
// 组合了数据库客户端和事务管理器的能力，提供一个统一的数据库操作入口。
// 具体的实现 (如 *MySQLManager, *PostgreSQLManager) 会嵌入对应的 DBClient 实现和 *TransactionManager。
type DBManager interface {
	// Embed DBClient methods (Connect, Disconnect, Ping, GetDB)
	DBClient
	// Note: TxManager is often implemented at the RepositoryManager level,
	// so it might not be directly embedded here unless DBManager also handles transactions.

	// AutoMigrate performs automatic database migration
	AutoMigrate(ctx context.Context) error

	// SeedData seeds the database with initial data
	SeedData(ctx context.Context) error

	// GetSQLDB 获取底层的 *sql.DB 连接池对象
	GetSQLDB() (*sql.DB, error)
}

// RepositoryManager interface (Placeholder, assuming it's defined elsewhere, possibly in repository package)
// Used by TxManager definition above.
type RepositoryManager interface {
	// Define methods expected by TxManager, e.g., GetTxDB() *gorm.DB
	// Or adjust TxManager interface if RepositoryManager is not defined here.
}

// --- Concrete Manager Implementations are moved to manager_impl.go ---
/*
// PostgreSQLManager 数据库管理器实现
type PostgreSQLManager struct {
    ...
}
func NewPostgreSQLManager(cfg *config.PostgreSQLConfig) DBManager {
    ...
}

// MySQLManager 数据库管理器实现
type MySQLManager struct {
    ...
}
func NewMySQLManager(cfg *config.MySQLConfig) DBManager {
    ...
}
*/
