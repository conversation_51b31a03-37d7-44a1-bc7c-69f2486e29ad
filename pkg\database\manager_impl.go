package database

import (
	"backend/pkg/config"
	"context"
	"database/sql"
	"errors"
	// Add other necessary imports like logger if needed
)

// PostgreSQLManager 数据库管理器实现
// 嵌入了 PostgreSQL 客户端和通用事务管理器。
type PostgreSQLManager struct {
	*PostgreSQLClient   // 嵌入具体的 PostgreSQL 客户端实现
	*TransactionManager // 嵌入通用的事务管理器实现
}

// NewPostgreSQLManager 创建 PostgreSQL 数据库管理器
// 它初始化 PostgreSQL 客户端和事务管理器，并返回一个满足 DBManager 接口的实例。
func NewPostgreSQLManager(cfg *config.PostgreSQLConfig) DBManager {
	// 创建具体的 PostgreSQL 客户端
	client := NewPostgreSQLClient(cfg)

	// 注意: 此时 client.db 可能还是 nil，因为 Connect() 还没调用
	// TransactionManager 需要一个有效的 *gorm.DB 实例。
	// 我们将在 Connect 方法成功后或在 InitDB 中初始化 TransactionManager。
	// 这里暂时可以传入 nil 或者一个临时的占位符，但更健壮的做法是延迟初始化。
	// 或者，修改 NewTransactionManager 允许传入 nil db，并在 Connect 后设置。
	// **目前的 NewTransactionManager 实现不允许 nil db，所以这里会产生问题。**
	// **解决方案：修改 NewTransactionManager 或在 Connect 后设置 txManager.db**
	// 为了简单起见，我们先假设 Connect 会在外部被调用，并成功设置 client.db
	// 然后，外部代码 (如 InitDB) 需要负责创建 TransactionManager。
	// 因此，这里的 NewTransactionManager 调用需要调整。

	// --- 调整后的逻辑 ---
	// 不在这里创建 TransactionManager，将其留给 InitDB 或 Connect 之后的步骤
	// txManager := NewTransactionManager(client.GetDB()) // 移动或延迟创建

	manager := &PostgreSQLManager{
		PostgreSQLClient: client, // 存储具体的客户端实例
		// TransactionManager: txManager, // 稍后设置
	}
	// 返回具体的类型指针，它满足 DBManager 接口
	// 因为 PostgreSQLClient 实现了 DBClient, TransactionManager 实现了 TxManager (虽然尚未完全初始化)
	return manager
}

// MySQLManager 数据库管理器实现
// 嵌入了 MySQL 客户端和通用事务管理器。
type MySQLManager struct {
	*MySQLClient        // 嵌入具体的 MySQL 客户端实现
	*TransactionManager // 嵌入通用的事务管理器实现
}

// NewMySQLManager 创建 MySQL 数据库管理器
// 它初始化 MySQL 客户端和事务管理器，并返回一个满足 DBManager 接口的实例。
func NewMySQLManager(cfg *config.MySQLConfig) DBManager {
	// 创建具体的 MySQL 客户端
	client := NewMySQLClient(cfg)

	// --- 调整后的逻辑 ---
	// 同上，不在这里创建 TransactionManager
	// txManager := NewTransactionManager(client.GetDB()) // 移动或延迟创建

	manager := &MySQLManager{
		MySQLClient: client, // 存储具体的客户端实例
		// TransactionManager: txManager, // 稍后设置
	}
	// 返回具体的类型指针，它满足 DBManager 接口
	return manager
}

// --- Helper methods to set TransactionManager after connection ---

// SetTransactionManager (for PostgreSQLManager)
func (m *PostgreSQLManager) SetTransactionManager(tm *TransactionManager) {
	m.TransactionManager = tm
}

// SetTransactionManager (for MySQLManager)
func (m *MySQLManager) SetTransactionManager(tm *TransactionManager) {
	m.TransactionManager = tm
}

// AutoMigrate for PostgreSQLManager
func (m *PostgreSQLManager) AutoMigrate(ctx context.Context) error {
	db := m.GetDB()
	if db == nil {
		return errors.New("database not connected for AutoMigrate")
	}
	// Call the migration function from init.go
	return runMigrations(db)
}

// SeedData for PostgreSQLManager
func (m *PostgreSQLManager) SeedData(ctx context.Context) error {
	db := m.GetDB()
	if db == nil {
		return errors.New("database not connected for SeedData")
	}
	// Call the seeding function from init.go
	// TODO: Get default admin password properly (e.g., from config or env)
	defaultAdminPassword := "password123" // Using temporary hardcoded password
	return seedData(db, defaultAdminPassword)
}

// GetSQLDB for PostgreSQLManager
func (m *PostgreSQLManager) GetSQLDB() (*sql.DB, error) {
	// Delegate to the embedded client's implementation
	return m.PostgreSQLClient.GetSQLDB()
}

// AutoMigrate for MySQLManager
func (m *MySQLManager) AutoMigrate(ctx context.Context) error {
	db := m.GetDB()
	if db == nil {
		return errors.New("database not connected for AutoMigrate")
	}
	// Call the migration function from init.go
	return runMigrations(db)
}

// SeedData for MySQLManager
func (m *MySQLManager) SeedData(ctx context.Context) error {
	db := m.GetDB()
	if db == nil {
		return errors.New("database not connected for SeedData")
	}
	// Call the seeding function from init.go
	// TODO: Get default admin password properly (e.g., from config or env)
	defaultAdminPassword := "password123" // Using temporary hardcoded password
	return seedData(db, defaultAdminPassword)
}

// GetSQLDB for MySQLManager
func (m *MySQLManager) GetSQLDB() (*sql.DB, error) {
	// Delegate to the embedded client's implementation
	return m.MySQLClient.GetSQLDB()
}
