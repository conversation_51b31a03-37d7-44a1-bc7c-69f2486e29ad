package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
	"strconv"

	"github.com/kataras/iris/v12"
)

// WmsInventoryAlertController 库存预警控制器
type WmsInventoryAlertController struct {
	alertService service.WmsInventoryAlertService
}

// NewWmsInventoryAlertController 创建库存预警控制器实例
func NewWmsInventoryAlertController(alertService service.WmsInventoryAlertService) *WmsInventoryAlertController {
	return &WmsInventoryAlertController{
		alertService: alertService,
	}
}

// CreateRule 创建预警规则
// @Summary 创建预警规则
// @Description 创建库存预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAlertRuleCreateReq true "规则信息"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAlertRuleVO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/rule [post]
func (c *WmsInventoryAlertController) CreateRule(ctx iris.Context) {
	var req dto.WmsInventoryAlertRuleCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	result, err := c.alertService.CreateRule(ctx, &req)
	if err != nil {
		response.FailWithMessage("创建预警规则失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// UpdateRule 更新预警规则
// @Summary 更新预警规则
// @Description 更新库存预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "规则ID"
// @Param request body dto.WmsInventoryAlertRuleUpdateReq true "更新信息"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAlertRuleVO} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/rule/{id} [put]
func (c *WmsInventoryAlertController) UpdateRule(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的规则ID", ctx)
		return
	}

	var req dto.WmsInventoryAlertRuleUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	result, err := c.alertService.UpdateRule(ctx, uint(id), &req)
	if err != nil {
		response.FailWithMessage("更新预警规则失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// DeleteRule 删除预警规则
// @Summary 删除预警规则
// @Description 删除库存预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "规则ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/rule/{id} [delete]
func (c *WmsInventoryAlertController) DeleteRule(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的规则ID", ctx)
		return
	}

	// 调用服务
	err = c.alertService.DeleteRule(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("删除预警规则失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// GetRule 获取预警规则详情
// @Summary 获取预警规则详情
// @Description 根据ID获取预警规则详细信息
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "规则ID"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAlertRuleVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/rule/{id} [get]
func (c *WmsInventoryAlertController) GetRule(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的规则ID", ctx)
		return
	}

	// 调用服务
	result, err := c.alertService.GetRule(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("获取预警规则详情失败: "+err.Error(), ctx)
		return
	}

	if result == nil {
		response.FailWithMessage("预警规则不存在", ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetRulePage 分页查询预警规则
// @Summary 分页查询预警规则
// @Description 根据条件分页查询预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAlertRuleQueryReq true "查询条件"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAlertRulePageVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/rule/page [post]
func (c *WmsInventoryAlertController) GetRulePage(ctx iris.Context) {
	var req dto.WmsInventoryAlertRuleQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 调用服务
	result, err := c.alertService.GetRulePage(ctx, &req)
	if err != nil {
		response.FailWithMessage("查询预警规则失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// ActivateRule 激活预警规则
// @Summary 激活预警规则
// @Description 激活库存预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "规则ID"
// @Success 200 {object} response.Response "激活成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/rule/{id}/activate [post]
func (c *WmsInventoryAlertController) ActivateRule(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的规则ID", ctx)
		return
	}

	// 调用服务
	err = c.alertService.ActivateRule(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("激活预警规则失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// DeactivateRule 停用预警规则
// @Summary 停用预警规则
// @Description 停用库存预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "规则ID"
// @Success 200 {object} response.Response "停用成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/rule/{id}/deactivate [post]
func (c *WmsInventoryAlertController) DeactivateRule(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的规则ID", ctx)
		return
	}

	// 调用服务
	err = c.alertService.DeactivateRule(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("停用预警规则失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// CheckAllRules 检查所有预警规则
// @Summary 检查所有预警规则
// @Description 手动触发检查所有预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Success 200 {object} response.Response "检查成功"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/check-all [post]
func (c *WmsInventoryAlertController) CheckAllRules(ctx iris.Context) {
	// 调用服务
	err := c.alertService.CheckAllRules(ctx)
	if err != nil {
		response.FailWithMessage("检查预警规则失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// CheckRulesByWarehouse 检查指定仓库的预警规则
// @Summary 检查指定仓库的预警规则
// @Description 手动触发检查指定仓库的预警规则
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param warehouseId path int true "仓库ID"
// @Success 200 {object} response.Response "检查成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/check-warehouse/{warehouseId} [post]
func (c *WmsInventoryAlertController) CheckRulesByWarehouse(ctx iris.Context) {
	// 解析路径参数
	warehouseIdStr := ctx.Params().Get("warehouseId")
	warehouseId, err := strconv.ParseUint(warehouseIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的仓库ID", ctx)
		return
	}

	// 调用服务
	err = c.alertService.CheckRulesByWarehouse(ctx, uint(warehouseId))
	if err != nil {
		response.FailWithMessage("检查仓库预警规则失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// GetAlert 获取预警日志详情
// @Summary 获取预警日志详情
// @Description 根据ID获取预警日志详细信息
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "预警ID"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAlertLogVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/log/{id} [get]
func (c *WmsInventoryAlertController) GetAlert(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的预警ID", ctx)
		return
	}

	// 调用服务
	result, err := c.alertService.GetAlert(ctx, uint(id))
	if err != nil {
		response.FailWithMessage("获取预警详情失败: "+err.Error(), ctx)
		return
	}

	if result == nil {
		response.FailWithMessage("预警记录不存在", ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetAlertPage 分页查询预警日志
// @Summary 分页查询预警日志
// @Description 根据条件分页查询预警日志
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAlertLogQueryReq true "查询条件"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAlertLogPageVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/log/page [post]
func (c *WmsInventoryAlertController) GetAlertPage(ctx iris.Context) {
	var req dto.WmsInventoryAlertLogQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 调用服务
	result, err := c.alertService.GetAlertPage(ctx, &req)
	if err != nil {
		response.FailWithMessage("查询预警日志失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// AcknowledgeAlert 确认预警
// @Summary 确认预警
// @Description 确认预警日志
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "预警ID"
// @Param request body dto.WmsInventoryAlertAcknowledgeReq true "确认信息"
// @Success 200 {object} response.Response "确认成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/log/{id}/acknowledge [post]
func (c *WmsInventoryAlertController) AcknowledgeAlert(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的预警ID", ctx)
		return
	}

	var req dto.WmsInventoryAlertAcknowledgeReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err = c.alertService.AcknowledgeAlert(ctx, uint(id), req.AcknowledgedBy, req.Remark)
	if err != nil {
		response.FailWithMessage("确认预警失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// ResolveAlert 解决预警
// @Summary 解决预警
// @Description 解决预警日志
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param id path int true "预警ID"
// @Param request body dto.WmsInventoryAlertResolveReq true "解决信息"
// @Success 200 {object} response.Response "解决成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/log/{id}/resolve [post]
func (c *WmsInventoryAlertController) ResolveAlert(ctx iris.Context) {
	// 解析路径参数
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的预警ID", ctx)
		return
	}

	var req dto.WmsInventoryAlertResolveReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 调用服务
	err = c.alertService.ResolveAlert(ctx, uint(id), req.Remark)
	if err != nil {
		response.FailWithMessage("解决预警失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// GetCriticalAlerts 获取严重预警
// @Summary 获取严重预警
// @Description 获取严重级别的预警列表
// @Tags 库存预警
// @Accept json
// @Produce json
// @Param warehouseId query int false "仓库ID"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryAlertLogVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alert/critical [get]
func (c *WmsInventoryAlertController) GetCriticalAlerts(ctx iris.Context) {
	// 解析查询参数
	var warehouseId *uint
	if warehouseIdStr := ctx.URLParam("warehouseId"); warehouseIdStr != "" {
		if id, err := strconv.ParseUint(warehouseIdStr, 10, 32); err == nil {
			warehouseIdUint := uint(id)
			warehouseId = &warehouseIdUint
		}
	}

	// 调用服务
	result, err := c.alertService.GetCriticalAlerts(ctx, warehouseId)
	if err != nil {
		response.FailWithMessage("获取严重预警失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}
