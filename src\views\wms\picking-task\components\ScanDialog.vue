<template>
  <el-dialog
    v-model="dialogVisible"
    title="扫码拣货"
    width="70%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="scan-dialog-container">
      <!-- 扫码区域 -->
      <el-card class="scan-area-card" shadow="never">
        <template #header>
          <span>扫码区域</span>
        </template>
        
        <div class="scan-input-area">
          <el-input
            ref="scanInputRef"
            v-model="scanCode"
            placeholder="请扫描条码或手动输入"
            size="large"
            @keyup.enter="handleScanConfirm"
            @input="handleScanInput"
            clearable
            autofocus
          >
            <template #prepend>
              <el-icon><Scan /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleScanConfirm" type="primary">
                确认
              </el-button>
            </template>
          </el-input>
          
          <div class="scan-tips">
            <el-alert
              title="扫码提示"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <ul>
                  <li>请扫描物料条码或库位条码</li>
                  <li>扫描后会自动匹配对应的拣货明细</li>
                  <li>支持连续扫描多个物料</li>
                </ul>
              </template>
            </el-alert>
          </div>
        </div>
      </el-card>
      
      <!-- 扫描结果 -->
      <el-card class="scan-result-card" shadow="never">
        <template #header>
          <div class="section-header">
            <span>扫描结果</span>
            <el-button
              type="danger"
              size="small"
              @click="handleClearAll"
              :disabled="scanResults.length === 0"
            >
              清空
            </el-button>
          </div>
        </template>
        
        <el-table
          :data="scanResults"
          size="small"
          max-height="300"
          empty-text="暂无扫描结果"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="scanCode" label="扫描码" width="150" />
          <el-table-column prop="scanType" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getScanTypeTagType(row.scanType)">
                {{ formatScanType(row.scanType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="itemCode" label="物料编码" width="120" />
          <el-table-column prop="itemName" label="物料名称" min-width="150" />
          <el-table-column prop="locationCode" label="库位" width="100" />
          <el-table-column prop="scanTime" label="扫描时间" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ formatStatus(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="{ row, $index }">
              <el-button
                type="danger"
                size="small"
                @click="handleRemoveResult($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 匹配的拣货明细 -->
      <el-card class="matched-details-card" shadow="never" v-if="matchedDetails.length > 0">
        <template #header>
          <span>匹配的拣货明细</span>
        </template>
        
        <el-table
          :data="matchedDetails"
          size="small"
          max-height="200"
        >
          <el-table-column prop="itemCode" label="物料编码" width="120" />
          <el-table-column prop="itemName" label="物料名称" min-width="150" />
          <el-table-column prop="requiredQty" label="需求数量" width="100" />
          <el-table-column prop="pickedQty" label="已拣数量" width="100" />
          <el-table-column prop="sourceLocationCode" label="源库位" width="100" />
          <el-table-column label="本次拣货" width="120">
            <template #default="{ row }">
              <el-input-number
                v-model="row.currentPickingQty"
                :min="0"
                :max="row.requiredQty - (row.pickedQty || 0)"
                :precision="0"
                size="small"
                style="width: 100px"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="matchedDetails.length === 0"
        >
          确认拣货
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, nextTick } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElInput, ElIcon, ElAlert, ElTable, ElTableColumn, ElTag, ElInputNumber } from 'element-plus'
import { Scan } from '@element-plus/icons-vue'

// 组件接口定义
interface ScanDialogEmits {
  confirm: [data: any]
  cancel: []
}

// Emits
const emit = defineEmits<ScanDialogEmits>()

// 响应式数据
const dialogVisible = ref(false)
const scanCode = ref('')
const scanResults = ref<any[]>([])
const matchedDetails = ref<any[]>([])

// 表单引用
const scanInputRef = ref<InstanceType<typeof ElInput>>()

// 方法
const handleOpen = () => {
  nextTick(() => {
    scanInputRef.value?.focus()
  })
}

const handleClose = () => {
  resetData()
}

const resetData = () => {
  scanCode.value = ''
  scanResults.value = []
  matchedDetails.value = []
}

const handleScanInput = (value: string) => {
  // 可以在这里添加实时验证逻辑
}

const handleScanConfirm = async () => {
  if (!scanCode.value.trim()) {
    ElMessage.warning('请输入扫描码')
    return
  }
  
  try {
    // 模拟扫码识别
    const scanResult = await processScanCode(scanCode.value.trim())
    
    if (scanResult) {
      scanResults.value.push({
        ...scanResult,
        scanTime: new Date().toLocaleString(),
        status: 'SUCCESS'
      })
      
      // 匹配拣货明细
      await matchPickingDetails(scanResult)
      
      ElMessage.success('扫描成功')
    } else {
      scanResults.value.push({
        scanCode: scanCode.value.trim(),
        scanType: 'UNKNOWN',
        scanTime: new Date().toLocaleString(),
        status: 'FAILED'
      })
      
      ElMessage.error('无法识别的条码')
    }
    
    // 清空输入框并重新聚焦
    scanCode.value = ''
    nextTick(() => {
      scanInputRef.value?.focus()
    })
  } catch (error) {
    ElMessage.error('扫描处理失败')
    console.error(error)
  }
}

const processScanCode = async (code: string) => {
  // 模拟条码识别逻辑
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 简单的条码识别规则（实际应该调用API）
  if (code.startsWith('ITEM_')) {
    return {
      scanCode: code,
      scanType: 'ITEM',
      itemCode: code.replace('ITEM_', ''),
      itemName: `物料${code.replace('ITEM_', '')}`,
      locationCode: '',
    }
  } else if (code.startsWith('LOC_')) {
    return {
      scanCode: code,
      scanType: 'LOCATION',
      itemCode: '',
      itemName: '',
      locationCode: code.replace('LOC_', ''),
    }
  } else {
    // 尝试作为物料编码处理
    return {
      scanCode: code,
      scanType: 'ITEM',
      itemCode: code,
      itemName: `物料${code}`,
      locationCode: '',
    }
  }
}

const matchPickingDetails = async (scanResult: any) => {
  // 模拟匹配拣货明细
  if (scanResult.scanType === 'ITEM') {
    const existingDetail = matchedDetails.value.find(d => d.itemCode === scanResult.itemCode)
    
    if (!existingDetail) {
      // 模拟从任务明细中查找匹配的物料
      const mockDetail = {
        id: Date.now(),
        itemCode: scanResult.itemCode,
        itemName: scanResult.itemName,
        requiredQty: 10,
        pickedQty: 0,
        sourceLocationCode: 'A01-01-01',
        currentPickingQty: 1
      }
      
      matchedDetails.value.push(mockDetail)
    }
  }
}

const handleRemoveResult = (index: number) => {
  const removedResult = scanResults.value[index]
  scanResults.value.splice(index, 1)
  
  // 如果删除的是物料扫描结果，也要从匹配明细中移除
  if (removedResult.scanType === 'ITEM') {
    const detailIndex = matchedDetails.value.findIndex(d => d.itemCode === removedResult.itemCode)
    if (detailIndex !== -1) {
      matchedDetails.value.splice(detailIndex, 1)
    }
  }
}

const handleClearAll = () => {
  scanResults.value = []
  matchedDetails.value = []
  ElMessage.success('已清空所有扫描结果')
}

const handleConfirm = () => {
  if (matchedDetails.value.length === 0) {
    ElMessage.warning('请先扫描物料')
    return
  }
  
  // 验证拣货数量
  const invalidDetails = matchedDetails.value.filter(detail => 
    !detail.currentPickingQty || detail.currentPickingQty <= 0
  )
  
  if (invalidDetails.length > 0) {
    ElMessage.warning('请输入有效的拣货数量')
    return
  }
  
  const confirmData = {
    scanResults: scanResults.value,
    pickingDetails: matchedDetails.value.map(detail => ({
      detailId: detail.id,
      itemCode: detail.itemCode,
      pickingQty: detail.currentPickingQty
    }))
  }
  
  emit('confirm', confirmData)
  dialogVisible.value = false
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const getScanTypeTagType = (type: string) => {
  switch (type) {
    case 'ITEM':
      return 'primary'
    case 'LOCATION':
      return 'success'
    case 'UNKNOWN':
      return 'danger'
    default:
      return 'info'
  }
}

const formatScanType = (type: string) => {
  switch (type) {
    case 'ITEM':
      return '物料'
    case 'LOCATION':
      return '库位'
    case 'UNKNOWN':
      return '未知'
    default:
      return '其他'
  }
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'SUCCESS':
      return 'success'
    case 'FAILED':
      return 'danger'
    default:
      return 'info'
  }
}

const formatStatus = (status: string) => {
  switch (status) {
    case 'SUCCESS':
      return '成功'
    case 'FAILED':
      return '失败'
    default:
      return '未知'
  }
}

// 暴露方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.scan-dialog-container {
  padding: 16px 0;
}

.scan-area-card,
.scan-result-card,
.matched-details-card {
  margin-bottom: 16px;
}

.scan-area-card :deep(.el-card__header),
.scan-result-card :deep(.el-card__header),
.matched-details-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.scan-input-area {
  text-align: center;
}

.scan-tips {
  margin-top: 16px;
  text-align: left;
}

.scan-tips ul {
  margin: 0;
  padding-left: 20px;
}

.scan-tips li {
  margin-bottom: 4px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}
</style>
