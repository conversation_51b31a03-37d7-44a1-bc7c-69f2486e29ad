package vo

import (
	"time"
	"backend/internal/model/entity"
)

// MtlItemVO 物料详细视图对象
type MtlItemVO struct {
	ID                uint                   `json:"id"`
	CreatedAt         time.Time              `json:"createdAt"`
	UpdatedAt         time.Time              `json:"updatedAt"`
	CreatedBy         uint                   `json:"createdBy"`
	UpdatedBy         uint                   `json:"updatedBy"`
	TenantID          uint                   `json:"tenantId"`
	AccountBookID     uint                   `json:"accountBookId"`
	Sku               string                 `json:"sku"`
	Name              string                 `json:"name"`
	Description       *string                `json:"description,omitempty"`
	Specification     *string                `json:"specification,omitempty"`
	CategoryCode      *string                `json:"categoryCode,omitempty"`
	GroupCode         *string                `json:"groupCode,omitempty"`
	BaseUnit          string                 `json:"baseUnit"`
	ShelfLifeDays     *int                   `json:"shelfLifeDays,omitempty"`
	BatchManaged      bool                   `json:"batchManaged"`
	SerialManaged     bool                   `json:"serialManaged"`
	StorageCondition  *string                `json:"storageCondition,omitempty"`
	ImageUrl          *string                `json:"imageUrl,omitempty"`
	WeightKg          *float64               `json:"weightKg,omitempty"`
	VolumeM3          *float64               `json:"volumeM3,omitempty"`
	LengthM           *float64               `json:"lengthM,omitempty"`
	WidthM            *float64               `json:"widthM,omitempty"`
	HeightM           *float64               `json:"heightM,omitempty"`
	Status            string                 `json:"status"`
	Remark            *string                `json:"remark,omitempty"`
	DefaultCustomerID *uint                  `json:"defaultCustomerId,omitempty"`
	DefaultSupplierID *uint                  `json:"defaultSupplierId,omitempty"`
	DefaultLocationID *uint                  `json:"defaultLocationId,omitempty"`
	PackageUnits      []MtlItemPackageUnitVO `json:"packageUnits,omitempty"`
}

// MtlItemPackageUnitVO 包装单位视图对象
type MtlItemPackageUnitVO struct {
	ID               uint     `json:"id"`
	UnitName         string   `json:"unitName"`
	ConversionFactor float64  `json:"conversionFactor"`
	PackageWeightKg  *float64 `json:"packageWeightKg,omitempty"`
	PackageVolumeM3  *float64 `json:"packageVolumeM3,omitempty"`
	PackageLengthM   *float64 `json:"packageLengthM,omitempty"`
	PackageWidthM    *float64 `json:"packageWidthM,omitempty"`
	PackageHeightM   *float64 `json:"packageHeightM,omitempty"`
}

// NewMtlItemVO 创建物料视图对象
func NewMtlItemVO(item *entity.MtlItem) *MtlItemVO {
	if item == nil {
		return nil
	}

	vo := &MtlItemVO{
		ID:                item.ID,
		CreatedAt:         item.CreatedAt,
		UpdatedAt:         item.UpdatedAt,
		CreatedBy:         item.CreatedBy,
		UpdatedBy:         item.UpdatedBy,
		TenantID:          item.TenantID,
		AccountBookID:     item.AccountBookID,
		Sku:               item.Sku,
		Name:              item.Name,
		Description:       item.Description,
		Specification:     item.Specification,
		CategoryCode:      item.CategoryCode,
		GroupCode:         item.GroupCode,
		BaseUnit:          item.BaseUnit,
		ShelfLifeDays:     item.ShelfLifeDays,
		BatchManaged:      item.BatchManaged,
		SerialManaged:     item.SerialManaged,
		StorageCondition:  item.StorageCondition,
		ImageUrl:          item.ImageUrl,
		WeightKg:          item.WeightKg,
		VolumeM3:          item.VolumeM3,
		LengthM:           item.LengthM,
		WidthM:            item.WidthM,
		HeightM:           item.HeightM,
		Status:            string(item.Status),
		Remark:            item.Remark,
		DefaultCustomerID: item.DefaultCustomerID,
		DefaultSupplierID: item.DefaultSupplierID,
		DefaultLocationID: item.DefaultLocationID,
	}

	// 转换包装单位
	if item.PackageUnits != nil {
		vo.PackageUnits = make([]MtlItemPackageUnitVO, len(item.PackageUnits))
		for i, unit := range item.PackageUnits {
			vo.PackageUnits[i] = MtlItemPackageUnitVO{
				ID:               unit.ID,
				UnitName:         unit.UnitName,
				ConversionFactor: unit.ConversionFactor,
				PackageWeightKg:  unit.PackageWeightKg,
				PackageVolumeM3:  unit.PackageVolumeM3,
				PackageLengthM:   unit.PackageLengthM,
				PackageWidthM:    unit.PackageWidthM,
				PackageHeightM:   unit.PackageHeightM,
			}
		}
	}

	return vo
}

// 注意：PageResult已在wms_location_vo.go中定义，无需重复定义
