import type { StatusFlowConfig } from '../types'

export const RECEIVING_RECORD_CONFIG: StatusFlowConfig = {
  businessType: 'RECEIVING_RECORD',

  statusOptions: [
    { value: 'PENDING',             label: '待收货',  color: '#409eff', bgColor: '#ecf5ff', icon: 'Clock',   description: '收货单草稿状态，可编辑修改' },
    { value: 'RECEIVING',           label: '收货中',  color: '#e6a23c', bgColor: '#fdf6ec', icon: 'Goods',   description: '正在进行收货作业' },
    { value: 'PENDING_INSPECTION',  label: '待检验',  color: '#f56c6c', bgColor: '#fef0f0', icon: 'Warning', description: '等待质检人员检验' },
    { value: 'INSPECTING',          label: '检验中',  color: '#e6a23c', bgColor: '#fdf6ec', icon: 'Search',  description: '质检正在进行' },
    { value: 'PARTIALLY_COMPLETED', label: '部分完成', color: '#e6a23c', bgColor: '#fdf6ec', icon: 'PieChart',description: '部分行已完成收货' },
    { value: 'COMPLETED',           label: '已完成',  color: '#67c23a', bgColor: '#f0f9ff', icon: 'Check',   description: '收货已全部完成' },
    { value: 'CLOSED',              label: '已关闭',  color: '#909399', bgColor: '#f4f4f5', icon: 'Lock',    description: '单据已关闭，不可再操作' },
    { value: 'CANCELLED',           label: '已取消',  color: '#f56c6c', bgColor: '#fef0f0', icon: 'Close',   description: '收货单已取消' }
  ],

  statusTransitions: [
    { from: 'PENDING',             to: ['RECEIVING','CANCELLED'] },
    { from: 'RECEIVING',           to: ['PENDING_INSPECTION','PARTIALLY_COMPLETED','CANCELLED'] },
    { from: 'PENDING_INSPECTION',  to: ['INSPECTING'] },
    { from: 'INSPECTING',          to: ['COMPLETED','PARTIALLY_COMPLETED'] },
    { from: 'PARTIALLY_COMPLETED', to: ['COMPLETED','CANCELLED'] },
    { from: 'COMPLETED',           to: ['CLOSED'] }
  ],

  displayConfig: {
    showActions: true,
    showProgress: true,
    allowManualTransition: true,
    showDescription: true,
    showIcon: true,
    nodeSize: 'medium',
    lineStyle: 'solid'
  },

  apiConfig: {
    changeStatusApi: '/api/v1/wms/receiving-records/{id}/status',
    checkPermissionApi: '/api/v1/auth/check-permission'
  },

  // 权限配置（可按需调整映射的权限码，不在本次迭代修改后端常量文件）
  permissionConfig: {
    'PENDING->RECEIVING': 'wms:receiving:start',
    'PENDING->CANCELLED': 'wms:receiving:cancel',
    'RECEIVING->PENDING_INSPECTION': 'wms:receiving:submit-inspection',
    'RECEIVING->PARTIALLY_COMPLETED': 'wms:receiving:partial-complete',
    'RECEIVING->CANCELLED': 'wms:receiving:cancel',
    'PENDING_INSPECTION->INSPECTING': 'wms:receiving:start-inspection',
    'INSPECTING->COMPLETED': 'wms:receiving:complete',
    'INSPECTING->PARTIALLY_COMPLETED': 'wms:receiving:partial-complete',
    'PARTIALLY_COMPLETED->COMPLETED': 'wms:receiving:complete',
    'PARTIALLY_COMPLETED->CANCELLED': 'wms:receiving:cancel',
    'COMPLETED->CLOSED': 'wms:receiving:close'
  }
}

// 状态转换标签映射
export const STATUS_TRANSITION_LABELS = {
  'PENDING->RECEIVING': '开始收货',
  'PENDING->CANCELLED': '取消单据',
  'RECEIVING->PENDING_INSPECTION': '提交检验',
  'RECEIVING->PARTIALLY_COMPLETED': '部分完成',
  'RECEIVING->CANCELLED': '取消单据',
  'PENDING_INSPECTION->INSPECTING': '开始检验',
  'INSPECTING->COMPLETED': '检验完成',
  'INSPECTING->PARTIALLY_COMPLETED': '部分完成',
  'PARTIALLY_COMPLETED->COMPLETED': '完成收货',
  'PARTIALLY_COMPLETED->CANCELLED': '取消单据',
  'COMPLETED->CLOSED': '关闭单据'
}

// 状态操作按钮类型映射
export const STATUS_BUTTON_TYPES = {
  'PENDING->RECEIVING': 'primary',
  'PENDING->CANCELLED': 'danger',
  'RECEIVING->PENDING_INSPECTION': 'warning',
  'RECEIVING->PARTIALLY_COMPLETED': 'warning',
  'RECEIVING->CANCELLED': 'danger',
  'PENDING_INSPECTION->INSPECTING': 'primary',
  'INSPECTING->COMPLETED': 'success',
  'INSPECTING->PARTIALLY_COMPLETED': 'warning',
  'PARTIALLY_COMPLETED->COMPLETED': 'success',
  'PARTIALLY_COMPLETED->CANCELLED': 'danger',
  'COMPLETED->CLOSED': 'info'
}

// 需要备注的状态转换
export const REQUIRE_REMARK_TRANSITIONS = [
  'CANCELLED',
  'PARTIALLY_COMPLETED'
]

// 业务规则验证
export const validateStatusTransition = (
  from: string,
  to: string,
  businessData?: Record<string, any>
): { valid: boolean; message?: string } => {
  // 基础规则验证
  const transition = RECEIVING_RECORD_CONFIG.statusTransitions.find(t => t.from === from)
  if (!transition || !transition.to.includes(to)) {
    return { valid: false, message: '不允许的状态转换' }
  }

  // 业务规则验证
  if (businessData) {
    // 检查明细数据
    if (to === 'RECEIVING' && (!businessData['details'] || businessData['details'].length === 0)) {
      return { valid: false, message: '开始收货前必须添加明细数据' }
    }

    // 检查收货数量完成度
    if (to === 'COMPLETED') {
      const hasUncompletedItems = businessData['details']?.some((item: any) =>
        (item.expectedQuantity || 0) > (item.receivedQuantity || 0)
      )
      if (hasUncompletedItems) {
        return { valid: false, message: '存在未完成收货的明细，请先完成收货或选择部分完成状态' }
      }
    }
  }

  return { valid: true }
}

// 附加 transitionLabels 属性
;(RECEIVING_RECORD_CONFIG as any).transitionLabels = STATUS_TRANSITION_LABELS;

// 按状态值映射按钮类型
export const STATUS_BUTTON_TYPES_BY_STATUS = {
  PENDING: 'primary',
  RECEIVING: 'warning',
  PENDING_INSPECTION: 'warning',
  INSPECTING: 'warning',
  PARTIALLY_COMPLETED: 'warning',
  COMPLETED: 'success',
  CLOSED: 'info',
  CANCELLED: 'danger'
} as const

// 按状态值映射按钮图标
export const STATUS_BUTTON_ICONS = {
  PENDING: 'Edit',
  RECEIVING: 'Loading',
  PENDING_INSPECTION: 'Warning',
  INSPECTING: 'Loading',
  PARTIALLY_COMPLETED: 'Warning',
  COMPLETED: 'Check',
  CLOSED: 'Lock',
  CANCELLED: 'CircleClose'
} as const

;(RECEIVING_RECORD_CONFIG as any).buttonTypes = STATUS_BUTTON_TYPES_BY_STATUS;
;(RECEIVING_RECORD_CONFIG as any).buttonIcons = STATUS_BUTTON_ICONS; 