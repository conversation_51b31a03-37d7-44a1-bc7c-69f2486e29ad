package entity

import (
	"time"

	"gorm.io/gorm"
)

// 发运状态枚举
type WmsShipmentStatus string

const (
	ShipmentStatusPreparing WmsShipmentStatus = "PREPARING"  // 准备中
	ShipmentStatusPacked    WmsShipmentStatus = "PACKED"     // 已打包
	ShipmentStatusReady     WmsShipmentStatus = "READY"      // 待发运
	ShipmentStatusShipped   WmsShipmentStatus = "SHIPPED"    // 已发运
	ShipmentStatusInTransit WmsShipmentStatus = "IN_TRANSIT" // 运输中
	ShipmentStatusDelivered WmsShipmentStatus = "DELIVERED"  // 已送达
	ShipmentStatusReturned  WmsShipmentStatus = "RETURNED"   // 已退回
)

// WmsShipment 对应数据库中的 wms_shipment 表
// 存储发运单信息
type WmsShipment struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	ShipmentNo     string `gorm:"column:shipment_no;size:50;not null;uniqueIndex:idx_shipment_no_account;comment:发运单号" json:"shipmentNo"`
	PickingTaskID  uint   `gorm:"column:picking_task_id;not null;comment:关联拣货任务ID" json:"pickingTaskId"`
	NotificationID uint   `gorm:"column:notification_id;not null;comment:关联出库通知单ID" json:"notificationId"`

	// 承运商信息
	CarrierID      *uint   `gorm:"column:carrier_id;comment:承运商ID" json:"carrierId"`
	CarrierName    *string `gorm:"column:carrier_name;size:100;comment:承运商名称" json:"carrierName"`
	TrackingNo     *string `gorm:"column:tracking_no;size:100;comment:运单号" json:"trackingNo"`
	ShippingMethod *string `gorm:"column:shipping_method;size:50;comment:运输方式" json:"shippingMethod"`

	// 发运信息
	ShipmentDate      *time.Time `gorm:"column:shipment_date;type:date;comment:发运日期" json:"-"`
	EstimatedDelivery *time.Time `gorm:"column:estimated_delivery;type:date;comment:预计送达日期" json:"-"`
	ActualDelivery    *time.Time `gorm:"column:actual_delivery;type:date;comment:实际送达日期" json:"-"`

	// 包装信息
	PackageCount int      `gorm:"column:package_count;default:1;comment:包裹数量" json:"packageCount"`
	TotalWeight  *float64 `gorm:"column:total_weight;type:numeric(10,3);comment:总重量(kg)" json:"totalWeight"`
	TotalVolume  *float64 `gorm:"column:total_volume;type:numeric(10,3);comment:总体积(m³)" json:"totalVolume"`

	// 费用信息
	ShippingCost    *float64 `gorm:"column:shipping_cost;type:numeric(10,2);comment:运费" json:"shippingCost"`
	InsuranceAmount *float64 `gorm:"column:insurance_amount;type:numeric(10,2);comment:保险金额" json:"insuranceAmount"`

	// 收货人信息（冗余存储，便于查询）
	ConsigneeName    string  `gorm:"column:consignee_name;size:100;not null;comment:收货人姓名" json:"consigneeName"`
	ConsigneePhone   *string `gorm:"column:consignee_phone;size:50;comment:收货人电话" json:"consigneePhone"`
	ConsigneeAddress *string `gorm:"column:consignee_address;size:500;comment:收货人地址" json:"consigneeAddress"`

	Status string  `gorm:"column:status;size:20;default:'PREPARING';comment:发运状态" json:"status"`
	Remark *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`

	// 扩展显示字段
	ShipmentDateStr      *string `gorm:"-" json:"shipmentDate"`      // 发运日期字符串格式
	EstimatedDeliveryStr *string `gorm:"-" json:"estimatedDelivery"` // 预计送达日期字符串格式
	ActualDeliveryStr    *string `gorm:"-" json:"actualDelivery"`    // 实际送达日期字符串格式

	// 关联字段
	PickingTask          WmsPickingTask          `gorm:"foreignKey:PickingTaskID;references:ID" json:"pickingTask,omitempty"`           // 关联的拣货任务
	OutboundNotification WmsOutboundNotification `gorm:"foreignKey:NotificationID;references:ID" json:"outboundNotification,omitempty"` // 关联的出库通知单
}

// TableName 指定数据库表名方法
func (WmsShipment) TableName() string {
	return "wms_shipment"
}

// BeforeCreate GORM钩子：创建前处理
func (w *WmsShipment) BeforeCreate(tx *gorm.DB) error {
	// 处理发运日期字段的字符串转换
	if w.ShipmentDateStr != nil && *w.ShipmentDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.ShipmentDateStr); err == nil {
			w.ShipmentDate = &parsedDate
		}
	}
	// 处理预计送达日期字段的字符串转换
	if w.EstimatedDeliveryStr != nil && *w.EstimatedDeliveryStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.EstimatedDeliveryStr); err == nil {
			w.EstimatedDelivery = &parsedDate
		}
	}
	// 处理实际送达日期字段的字符串转换
	if w.ActualDeliveryStr != nil && *w.ActualDeliveryStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.ActualDeliveryStr); err == nil {
			w.ActualDelivery = &parsedDate
		}
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前处理
func (w *WmsShipment) BeforeUpdate(tx *gorm.DB) error {
	// 处理发运日期字段的字符串转换
	if w.ShipmentDateStr != nil && *w.ShipmentDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.ShipmentDateStr); err == nil {
			w.ShipmentDate = &parsedDate
		}
	}
	// 处理预计送达日期字段的字符串转换
	if w.EstimatedDeliveryStr != nil && *w.EstimatedDeliveryStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.EstimatedDeliveryStr); err == nil {
			w.EstimatedDelivery = &parsedDate
		}
	}
	// 处理实际送达日期字段的字符串转换
	if w.ActualDeliveryStr != nil && *w.ActualDeliveryStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.ActualDeliveryStr); err == nil {
			w.ActualDelivery = &parsedDate
		}
	}
	return nil
}

// AfterFind GORM钩子：查询后处理
func (w *WmsShipment) AfterFind(tx *gorm.DB) error {
	// 将发运日期字段转换为字符串格式用于前端显示
	if w.ShipmentDate != nil {
		dateStr := w.ShipmentDate.Format("2006-01-02")
		w.ShipmentDateStr = &dateStr
	}
	// 将预计送达日期字段转换为字符串格式用于前端显示
	if w.EstimatedDelivery != nil {
		dateStr := w.EstimatedDelivery.Format("2006-01-02")
		w.EstimatedDeliveryStr = &dateStr
	}
	// 将实际送达日期字段转换为字符串格式用于前端显示
	if w.ActualDelivery != nil {
		dateStr := w.ActualDelivery.Format("2006-01-02")
		w.ActualDeliveryStr = &dateStr
	}
	return nil
}

// IsShipped 判断是否已发运
func (w *WmsShipment) IsShipped() bool {
	return w.Status == string(ShipmentStatusShipped) ||
		w.Status == string(ShipmentStatusInTransit) ||
		w.Status == string(ShipmentStatusDelivered)
}

// IsDelivered 判断是否已送达
func (w *WmsShipment) IsDelivered() bool {
	return w.Status == string(ShipmentStatusDelivered)
}

// CanShip 判断是否可以发运
func (w *WmsShipment) CanShip() bool {
	return w.Status == string(ShipmentStatusReady)
}
