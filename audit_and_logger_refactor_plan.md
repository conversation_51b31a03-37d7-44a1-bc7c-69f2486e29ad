# LoggerMiddleware 和 AuditMiddleware 重构与集成方案

## 1. 核心需求

- **LoggerMiddleware**:
  - **功能**: 详细记录 HTTP 请求/响应，用于调试和监控。
  - **输出**: **仅控制台**。
- **AuditMiddleware**:
  - **功能**: 记录关键用户操作和系统事件，用于安全审计。
  - **输出**: **数据库**，并提供后端 API 及前端界面展示。
- **登录/登出等操作**: 需要被审计和记录。

## 2. 中间件审查与设计决策

- **`IPMiddleware`**: 全局最先应用，提供客户端 IP。 (已完成)
- **`LoggerMiddleware`**: 全局应用，在 `IPMiddleware` 之后。记录所有请求，为后续提供 `TraceID` 和请求时间。
- **`AuthMiddleware`**: 在 `/api/v1` 级别应用，在 `LoggerMiddleware` 之后。负责认证并将用户信息放入上下文。
- **`LicenseCheckMiddleware`**: 在 `/api/v1` 级别应用，在 `AuthMiddleware` 之后。
- **`AuditMiddleware`**: 在 `/api/v1` 级别应用，在 `LicenseCheckMiddleware` 之后。利用之前所有中间件设置的上下文信息。登录等特定操作的审计需要特殊处理。

## 3. 详细实施步骤

### 第一部分：全局中间件配置 (`cmd/main.go`)

#### 1. `IPMiddleware`

- 保持现状，已正确配置为第一个自定义全局中间件。

#### 2. `LoggerMiddleware` (控制台输出)

- **配置文件 (`configs/config.yaml`)**:
  ```yaml
  app:
    log:
      output: "console" # 确保只有 console
      level: "debug" # 或期望的级别
      # ... 其他日志配置 ...
  ```
- **实例化 (`cmd/main.go`)**:
  ```go
  // 在 appLogger.Init(loggerCfg) 和 log := appLogger.GetLogger() 之后
  loggerMiddlewareInstance := middleware.LoggerMiddleware(middleware.LoggerConfig{
      Enabled:             true,
      LogRequestBody:      true,  // 按需调整
      LogResponseBody:     false, // 生产环境通常关闭
      MaxRequestBodySize:  10240,
      MaxResponseBodySize: 10240,
      ExcludedPaths:       []string{"/static", "/favicon.ico", "/health"},
      SensitiveHeaders:    []string{"Authorization", "Cookie", "Set-Cookie", "X-Api-Key"},
      SensitiveParams:     []string{"password", "token", "access_token", "refresh_token", "secret", "apiKey"},
  })
  log.Info("LoggerMiddleware (自定义) 实例创建完成")
  ```
- **注册 (`cmd/main.go` - app 初始化部分)**:

  ```go
  app := iris.New()
  app.Logger().SetLevel(cfg.App.IrisLogLevel)
  app.Use(recover.New()) // 1. Panic Recovery

  // app.Use(logger.New()) // <--- 移除 Iris 内置 logger

  // 2. IP Middleware (已存在)
  ipMiddleware := middleware.NewIPMiddleware()
  app.Use(ipMiddleware.Serve)
  log.Info("IPMiddleware 全局注册完成")

  // 3. 自定义 LoggerMiddleware
  app.Use(loggerMiddlewareInstance)
  log.Info("自定义 LoggerMiddleware 全局注册完成")
  ```

### 第二部分：`AuditMiddleware` 功能增强 (数据库存储和 API)

#### 1. 数据库模型 (`internal/model/entity/audit_log.go`)

```go
package entity

import (
    "time"
    "gorm.io/datatypes"
)

type AuditLog struct {
    ID           uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
    UserID       uint64         `json:"userId"`
    Username     string         `gorm:"size:100;index" json:"username"`
    Action       string         `gorm:"size:100;index" json:"action"`
    ResourceType string         `gorm:"size:100;index" json:"resourceType"`
    ResourceID   string         `gorm:"size:255;index" json:"resourceId"`
    Timestamp    time.Time      `gorm:"index" json:"timestamp"`
    ClientIP     string         `gorm:"size:100" json:"clientIp"`
    UserAgent    string         `gorm:"size:512" json:"userAgent"`
    RequestURI   string         `gorm:"size:1024" json:"requestUri"`
    Method       string         `gorm:"size:10" json:"method"`
    StatusCode   int            `json:"statusCode"`
    DurationMs   int64          `json:"durationMs"`
    TraceID      string         `gorm:"size:100;index" json:"traceId"`
    Status       string         `gorm:"size:50" json:"status"` // e.g., success, failure
    OldValue     datatypes.JSON `json:"oldValue,omitempty"`
    NewValue     datatypes.JSON `json:"newValue,omitempty"`
    Details      string         `gorm:"type:text" json:"details,omitempty"`
    CreatedAt    time.Time      `json:"createdAt"`
}

func (AuditLog) TableName() string {
    return "system_audit_log"
}
```

- **数据库迁移**: 在 `database/migration.go` (或类似文件) 中添加 `db.AutoMigrate(&entity.AuditLog{})`。

#### 2. DTO (`internal/model/dto/audit_log_dto.go`)

```go
package dto

import (
    "time"
    "backend/pkg/response" // Required for response.PageQuery
)

type AuditLogQueryDTO struct {
    // Filter fields
    UserID          uint64    `form:"userId" json:"userId,omitempty"`
    Username        string    `form:"username" json:"username,omitempty"`
    Action          string    `form:"action" json:"action,omitempty"`
    ResourceType    string    `form:"resourceType" json:"resourceType,omitempty"`
    ClientIP        string    `form:"clientIp" json:"clientIp,omitempty"`
    TraceID         string    `form:"traceId" json:"traceId,omitempty"`
    Status          string    `form:"status" json:"status,omitempty"`
    StartDate       time.Time `form:"startDate" json:"startDate,omitempty" time_format:"2006-01-02"`
    EndDate         time.Time `form:"endDate" json:"endDate,omitempty" time_format:"2006-01-02"`

    // Pagination and Sorting parameters.
    // This field is populated by the controller using response.BuildPageQuery(ctx).
    // Its sub-fields (pageNum, pageSize, sort) are read by BuildPageQuery from URL parameters.
    Pagination      response.PageQuery `json:"-"` // Excluded from direct JSON binding for the DTO itself.
}
```

#### 2.a. View Object (VO) Definition (`internal/model/vo/audit_log_vo.go`)

```go
package vo

import (
	"time"
	"gorm.io/datatypes" // If OldValue/NewValue are still JSON in VO
)

// AuditLogVO 审计日志视图对象
type AuditLogVO struct {
	ID           uint64         `json:"id"`
	UserID       uint64         `json:"userId,omitempty"` // omitempty if can be 0 for system events
	Username     string         `json:"username,omitempty"`
	Action       string         `json:"action"`
	ResourceType string         `json:"resourceType,omitempty"`
	ResourceID   string         `json:"resourceId,omitempty"`
	Timestamp    time.Time      `json:"timestamp"`
	ClientIP     string         `json:"clientIp,omitempty"`
	UserAgent    string         `json:"userAgent,omitempty"`
	RequestURI   string         `json:"requestUri,omitempty"`
	Method       string         `json:"method,omitempty"`
	StatusCode   int            `json:"statusCode,omitempty"`
	DurationMs   int64          `json:"durationMs,omitempty"`
	TraceID      string         `json:"traceId,omitempty"`
	Status       string         `json:"status,omitempty"` // e.g., success, failure
	OldValue     datatypes.JSON `json:"oldValue,omitempty"`
	NewValue     datatypes.JSON `json:"newValue,omitempty"`
	Details      string         `json:"details,omitempty"`
	CreatedAt    time.Time      `json:"createdAt"`
}
```

#### 3. Repository (`internal/repository/audit_log_repository.go`)

```go
package repository

import (
	"backend/internal/model/entity"
	"backend/internal/model/dto"
	"backend/pkg/response" // For response.PageResult
	"backend/pkg/util"     // For CalculatePages if used directly, or rely on PageResult methods
	"gorm.io/gorm"
	"time" // For EndDate adjustment
	// "reflect" // If implementing column validation for sorting like UserRepository
	// "backend/pkg/constant" // If using default sort constants
)

type AuditLogRepository interface {
	Create(log *entity.AuditLog) error
	// Find now returns a PageResult containing entity.AuditLog and total count information
	Find(query dto.AuditLogQueryDTO) (*response.PageResult, error)
}

type auditLogRepositoryImpl struct {
	db *gorm.DB
	// BaseRepository[entity.AuditLog, uint64] // Could embed if BaseRepository is generic enough
}

// NewAuditLogRepository constructor for AuditLogRepository
func NewAuditLogRepository(db *gorm.DB) AuditLogRepository { // Renamed from NewAuditLogRepositoryImpl
	return &auditLogRepositoryImpl{db: db}
}

func (r *auditLogRepositoryImpl) Create(log *entity.AuditLog) error {
	return r.db.Create(log).Error
}

func (r *auditLogRepositoryImpl) Find(query dto.AuditLogQueryDTO) (*response.PageResult, error) {
	var logs []*entity.AuditLog // Slice of pointers to match user repo example
	var total int64
	dbQuery := r.db.Model(&entity.AuditLog{})

	// Apply filter conditions from query DTO
	if query.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", query.UserID)
	}
	if query.Username != "" {
		dbQuery = dbQuery.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.Action != "" {
		dbQuery = dbQuery.Where("action = ?", query.Action)
	}
	if query.ResourceType != "" {
		dbQuery = dbQuery.Where("resource_type = ?", query.ResourceType)
	}
	if query.ClientIP != "" {
		dbQuery = dbQuery.Where("client_ip = ?", query.ClientIP)
	}
	if query.TraceID != "" {
		dbQuery = dbQuery.Where("trace_id = ?", query.TraceID)
	}
	if query.Status != "" {
		dbQuery = dbQuery.Where("status = ?", query.Status)
	}
	if !query.StartDate.IsZero() {
		dbQuery = dbQuery.Where("timestamp >= ?", query.StartDate)
	}
	if !query.EndDate.IsZero() {
		// Adjust EndDate to include the whole day
		endDateEndOfDay := query.EndDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
		dbQuery = dbQuery.Where("timestamp <= ?", endDateEndOfDay)
	}

	// Count total records matching filters
	err := dbQuery.Count(&total).Error
	if err != nil {
		// return nil, apperrors.Wrap(err, apperrors.CODE_DATA_QUERY_FAILED, "counting audit logs failed")
		return nil, err
	}

	// Prepare PageResult (even if total is 0, PageNum/Size are needed)
	pageResult := &response.PageResult{
		Total:    total,
		PageNum:  query.Pagination.PageNum,
		PageSize: query.Pagination.PageSize,
		Sort:     query.Pagination.Sort, // Pass Sort Info from DTO's Pagination
	}
    pageResult.Pages = response.CalculatePages(total, query.Pagination.PageSize)


	// If no records, return early with pagination info but empty list
	if total == 0 {
		pageResult.List = []*entity.AuditLog{}
		return pageResult, nil
	}

	// Apply sorting from query.Pagination
	// response.PageQuery.OrderBy() should generate the SQL ORDER BY clause string
	// including handling of default sort order if query.Pagination.Sort is empty.
	orderByClause := query.Pagination.OrderBy()
	if orderByClause != "" {
		dbQuery = dbQuery.Order(orderByClause)
	} else {
	    // Fallback if OrderBy() somehow returns empty (it should provide a default)
	    dbQuery = dbQuery.Order("timestamp desc")
	}

	// Apply pagination from query.Pagination
	// query.Pagination.Offset() and Limit() are provided by response.PageQuery
	dbQuery = dbQuery.Offset(query.Pagination.Offset()).Limit(query.Pagination.Limit())

	// Find the records
	err = dbQuery.Find(&logs).Error
	if err != nil {
		// return nil, apperrors.Wrap(err, apperrors.CODE_DATA_QUERY_FAILED, "finding audit logs failed")
		return nil, err
	}

	pageResult.List = logs
	return pageResult, nil
}
```

#### 4. Service (`internal/service/audit_log_service.go`)

```go
package service

import (
	"backend/internal/middleware" // Or model if AuditEvent moved
	"backend/internal/model/entity" // For converting from entity
	"backend/internal/model/dto"
	"backend/internal/model/vo"       // For AuditLogVO
	"backend/internal/repository"
	"backend/pkg/logger"
	"backend/pkg/response"           // For response.PageResult
	"backend/pkg/util" // For GetTraceIDFromStdContextDefault if needed in RecordEvent
	"encoding/json"
	"fmt"
	"gorm.io/datatypes"
	// "backend/pkg/errors" // For apperrors if needed for wrapping
)

type AuditLogService interface {
	RecordEvent(eventData *middleware.AuditEvent) error
	// ListAuditLogs now returns a PageResult containing AuditLogVOs
	ListAuditLogs(query dto.AuditLogQueryDTO) (*response.PageResult, error)
}

type auditLogServiceImpl struct {
	repo   repository.AuditLogRepository
	logger logger.Logger
}

// NewAuditLogService constructor for AuditLogService
func NewAuditLogService(repo repository.AuditLogRepository, log logger.Logger) AuditLogService { // Renamed
	return &auditLogServiceImpl{repo: repo, logger: log}
}

// convertToAuditLogVO converts an entity.AuditLog to a vo.AuditLogVO
func (s *auditLogServiceImpl) convertToAuditLogVO(logEntity *entity.AuditLog) *vo.AuditLogVO {
	if logEntity == nil {
		return nil
	}
	return &vo.AuditLogVO{
		ID:           logEntity.ID,
		UserID:       logEntity.UserID,
		Username:     logEntity.Username,
		Action:       logEntity.Action,
		ResourceType: logEntity.ResourceType,
		ResourceID:   logEntity.ResourceID,
		Timestamp:    logEntity.Timestamp,
		ClientIP:     logEntity.ClientIP,
		UserAgent:    logEntity.UserAgent,
		RequestURI:   logEntity.RequestURI,
		Method:       logEntity.Method,
		StatusCode:   logEntity.StatusCode,
		DurationMs:   logEntity.DurationMs,
		TraceID:      logEntity.TraceID,
		Status:       logEntity.Status,
		OldValue:     logEntity.OldValue, // Assuming datatypes.JSON is fine for VO
		NewValue:     logEntity.NewValue,
		Details:      logEntity.Details,
		CreatedAt:    logEntity.CreatedAt,
	}
}

// convertToAuditLogVOList converts a slice of entity.AuditLog to a slice of vo.AuditLogVO
func (s *auditLogServiceImpl) convertToAuditLogVOList(entityList []*entity.AuditLog) []*vo.AuditLogVO {
	voList := make([]*vo.AuditLogVO, 0, len(entityList))
	for _, entity := range entityList {
		voList = append(voList, s.convertToAuditLogVO(entity))
	}
	return voList
}

func (s *auditLogServiceImpl) RecordEvent(eventData *middleware.AuditEvent) error {
	logEntry := entity.AuditLog{ // DB model
		UserID:       uint64(eventData.UserID), // Ensure AuditEvent.UserID is compatible
		Username:     eventData.Username,
		Action:       eventData.Action,
		ResourceType: eventData.ResourceType,
		ResourceID:   eventData.ResourceID,
		Timestamp:    eventData.Time,
		ClientIP:     eventData.IP,
		UserAgent:    eventData.UserAgent,
		RequestURI:   eventData.RequestURI,
		Method:       eventData.Method,
		StatusCode:   eventData.StatusCode,
		DurationMs:   eventData.Duration,
		TraceID:      eventData.TraceID,
		Status:       eventData.Status,
	}

	if eventData.OldValue != "" { // Assuming OldValue/NewValue in AuditEvent are strings
		logEntry.OldValue = datatypes.JSON(eventData.OldValue)
	}
	if eventData.NewValue != "" {
		logEntry.NewValue = datatypes.JSON(eventData.NewValue)
	}

	if eventData.Details != nil { // Assuming Details is interface{}
		detailsBytes, err := json.Marshal(eventData.Details)
		if err == nil {
			logEntry.Details = string(detailsBytes)
		} else {
			s.logger.Warnf("Failed to marshal audit event details: %v. Falling back to fmt.Sprintf", err, logger.WithField("traceId", eventData.TraceID))
			logEntry.Details = fmt.Sprintf("%+v", eventData.Details)
		}
	}

	err := s.repo.Create(&logEntry)
	if err != nil {
		s.logger.Errorf("Failed to record audit event to DB: %v", err, logger.WithField("traceId", eventData.TraceID))
		// return apperrors.Wrap(err, apperrors.CODE_DATA_CREATE_FAILED, "failed to record audit event")
		return err
	}
	s.logger.Debugf("Audit event recorded: UserID=%d, Action=%s, Resource=%s/%s",
		logEntry.UserID, logEntry.Action, logEntry.ResourceType, logEntry.ResourceID,
		logger.WithField("traceId", eventData.TraceID))
	return nil
}

func (s *auditLogServiceImpl) ListAuditLogs(query dto.AuditLogQueryDTO) (*response.PageResult, error) {
	// Pagination defaults are handled by response.BuildPageQuery in controller
	pageResultEntity, err := s.repo.Find(query) // This now returns *response.PageResult with List of entities
	if err != nil {
		s.logger.Error("Error fetching audit logs from repository", logger.WithError(err))
		// return nil, apperrors.Wrap(err, apperrors.CODE_DATA_QUERY_FAILED, "failed to list audit logs from repository")
		return nil, err
	}

	// Type assertion for the list from PageResult.List, which should be []*entity.AuditLog
	entityList, ok := pageResultEntity.List.([]*entity.AuditLog)
	if !ok {
	    // This case implies that pageResultEntity.List was not nil but of the wrong type,
	    // or it was nil when it shouldn't have been (e.g., Find should return empty slice for no results).
	    if pageResultEntity.List == nil && pageResultEntity.Total == 0 {
	        entityList = []*entity.AuditLog{} // Correctly handle empty result
	    } else {
	        // If List is not nil but not []*entity.AuditLog, it's an unexpected type.
		    s.logger.Error("Repository Find returned PageResult.List with unexpected type", logger.Any("type",fmt.Sprintf("%T", pageResultEntity.List)))
		    // return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "Failed to process audit log data type")
            return nil, fmt.Errorf("unexpected list type from repository: %T", pageResultEntity.List)
	    }
	}

	voList := s.convertToAuditLogVOList(entityList)

	// Return a new PageResult with the VO list and other pagination info from repo's PageResult
	return &response.PageResult{
		List:     voList,
		Total:    pageResultEntity.Total,
		PageNum:  pageResultEntity.PageNum,
		PageSize: pageResultEntity.PageSize,
		Pages:    pageResultEntity.Pages, // Assuming repo's PageResult correctly calculates/sets this
        Sort:     pageResultEntity.Sort, // Pass Sort Info
	}, nil
}
```

#### 5. 重构 `AuditMiddleware` (`internal/middleware/audit.go`)

- **修改 `AuditEvent` 结构体**: 确保 `UserID` 是 `uint` (与 `AuditLog` 模型中的 `uint64` 对应，转换时注意)。
- **定义新的 Context Keys (`pkg/constant/constant.go`)**:
  ```go
  // pkg/constant/constant.go
  const (
      // ... other constants ...
      AUDIT_ACTION_CTX_KEY          = "audit_action"
      AUDIT_RESOURCE_TYPE_CTX_KEY   = "audit_resource_type"
      AUDIT_RESOURCE_ID_CTX_KEY     = "audit_resource_id"
      AUDIT_DETAILS_CTX_KEY         = "audit_details"
      AUDIT_OLD_VALUE_CTX_KEY       = "audit_old_value"
      AUDIT_NEW_VALUE_CTX_KEY       = "audit_new_value"
      AUDIT_USER_ID_CTX_KEY         = "audit_user_id" // For explicit override
      AUDIT_USERNAME_CTX_KEY        = "audit_username" // For explicit override
      AUDIT_EVENT_RECORDED_CTX_KEY  = "audit_event_recorded" // To skip middleware if event handled manually
  )
  ```
- **重构为结构体并注入 Service**:

  ```go
  package middleware

  import (
      "regexp"
      "backend/internal/service"
      // ... other imports from original audit.go ...
      "backend/pkg/constant" // Import new constants
      "backend/pkg/logger" // For logging within middleware
  )

  // AuditEvent struct definition (as is, ensure UserID type matches usage)
  // ...

  // AuditConfig struct definition (as is)
  // ...

  // defaultAuditConfig variable (as is, but see ExcludedPaths modification below)
  // ...

  type AuditMiddleware struct {
      config            AuditConfig
      auditService      service.AuditLogService
      resourceIDRegex   *regexp.Regexp
      resourceTypeRegex *regexp.Regexp
  }

  func NewAuditMiddleware(auditService service.AuditLogService, cfgOverrides ...AuditConfig) *AuditMiddleware {
      cfg := defaultAuditConfig
      if len(cfgOverrides) > 0 {
          // Logic to merge cfgOverrides with defaultAuditConfig if needed
          // For simplicity, direct assignment if one config is passed:
          cfg = cfgOverrides[0]
      }

      // Ensure login path is NOT excluded for login attempt auditing
      var newExcludedPaths []string
      isLoginPathExcluded := false
      loginPath := "/api/v1/auth/login" // Define your actual login path
      for _, p := range cfg.ExcludedPaths {
          if p == loginPath {
              isLoginPathExcluded = true
              continue // Skip adding it to newExcludedPaths
          }
          newExcludedPaths = append(newExcludedPaths, p)
      }
      cfg.ExcludedPaths = newExcludedPaths
      if isLoginPathExcluded {
           // Optionally log that it was removed, or ensure defaultAuditConfig doesn't have it
      }


      return &AuditMiddleware{
          config:            cfg,
          auditService:      auditService,
          resourceIDRegex:   regexp.MustCompile(cfg.ResourceIDRegex),
          resourceTypeRegex: regexp.MustCompile(cfg.ResourceTypeRegex),
      }
  }

  // Serve method - This is the core logic of the original AuditMiddleware function
  func (m *AuditMiddleware) Serve(ctx iris.Context) {
      // Initial checks (Enabled, ExcludedPaths, ExcludedMethods) - as in original
      // ... (copy from original AuditMiddleware function, adapt 'cfg' to 'm.config')

      startTime := time.Now()
      reqCtx := ctx.Request().Context() // Standard Go context

      // Handle case where event might have been recorded manually (e.g., in login controller)
      if ctx.Values().GetBoolDefault(constant.AUDIT_EVENT_RECORDED_CTX_KEY, false) {
          ctx.Next()
          return
      }

      // Defer the audit event recording
      defer func() {
          if r := recover(); r != nil {
              // Handle panic if necessary, maybe log a failed audit attempt due to panic
              logger.GetLogger().Ctx(reqCtx).Error("Panic recovered during audit middleware defer", logger.Any("panic", r))
              // Re-panic if you want Iris's recover middleware to handle it
              // panic(r)
          }

          // Check again if it was recorded by a handler during ctx.Next()
          if ctx.Values().GetBoolDefault(constant.AUDIT_EVENT_RECORDED_CTX_KEY, false) {
              return
          }

          duration := time.Since(startTime)
          statusCode := ctx.GetStatusCode() // Get status code after handler execution

          // Prepare AuditEvent
          event := &AuditEvent{
              Time:         startTime,
              UserAgent:    ctx.GetHeader("User-Agent"),
              RequestURI:   ctx.Path(), // Or FullRequestURI()
              Method:       ctx.Method(),
              StatusCode:   statusCode,
              Duration:     duration.Milliseconds(),
              TraceID:      util.GetTraceIDFromStdContextDefault(reqCtx, ""), // Get TraceID from LoggerMiddleware
              Status:       "success", // Default, can be overridden
          }

          // Get UserID, Username, ClientIP from context (set by previous middlewares)
          userID, _ := util.GetUserIDFromStdContext(reqCtx) // Error already logged by GetUserIDFromStdContext
          event.UserID = uint(userID) // Cast to uint, ensure AuditEvent struct uses uint
          event.Username, _ = util.GetUsernameFromStdContext(reqCtx)
          event.IP, _ = util.GetClientIPFromStdContext(reqCtx)


          // Override/enrich from context values set by handlers (e.g., login controller)
          if actionOverride := ctx.Values().GetString(constant.AUDIT_ACTION_CTX_KEY); actionOverride != "" {
              event.Action = actionOverride
          } else {
              event.Action = getAction(event.Method, m.config.ActionMappings)
          }

          if resTypeOverride := ctx.Values().GetString(constant.AUDIT_RESOURCE_TYPE_CTX_KEY); resTypeOverride != "" {
              event.ResourceType = resTypeOverride
          } else {
              event.ResourceType = getResourceType(event.RequestURI, m.resourceTypeRegex, m.config.IgnoredResourceTypes)
          }

          if resIDOverride := ctx.Values().GetString(constant.AUDIT_RESOURCE_ID_CTX_KEY); resIDOverride != "" {
              event.ResourceID = resIDOverride
          } else {
              event.ResourceID = getResourceID(event.RequestURI, m.resourceIDRegex)
          }

          if detailsOverride := ctx.Values().Get(constant.AUDIT_DETAILS_CTX_KEY); detailsOverride != nil {
               if detailsStr, ok := detailsOverride.(string); ok {
                  event.Details = detailsStr
               } else {
                  event.Details = fmt.Sprintf("%+v", detailsOverride)
               }
          }
          if oldValueFromCtx := ctx.Values().GetString(constant.AUDIT_OLD_VALUE_CTX_KEY); oldValueFromCtx != "" {
              event.OldValue = oldValueFromCtx
          }
          if newValueFromCtx := ctx.Values().GetString(constant.AUDIT_NEW_VALUE_CTX_KEY); newValueFromCtx != "" {
              event.NewValue = newValueFromCtx
          }

          // Override UserID/Username if explicitly set (e.g., by login handler for accuracy)
          if userIDOverride, ok := ctx.Values().Get(constant.AUDIT_USER_ID_CTX_KEY).(uint64); ok && userIDOverride > 0 {
              event.UserID = uint(userIDOverride)
          }
          if usernameOverride := ctx.Values().GetString(constant.AUDIT_USERNAME_CTX_KEY); usernameOverride != "" {
              event.Username = usernameOverride
          }


          if statusCode >= 400 {
              event.Status = "failure"
          }
          // Further refine status based on specific error codes if needed

          // Record Request Body if configured
          if m.config.LogRequestBody && ctx.Request().ContentLength > 0 && ctx.Request().ContentLength < int64(m.config.MaxRequestBodySize) {
              // Logic to get request body (careful with re-reading)
              // This part is complex if body already read. Iris's GetBody() might be cached.
              // Alternatively, controllers can put relevant request parts into AUDIT_DETAILS_CTX_KEY.
          }
          // Record Response Body if configured (also complex, might need custom response writer)


          // Send to service
          if m.auditService != nil {
              err := m.auditService.RecordEvent(event)
              if err != nil {
                  // Log error, but don't fail the request itself because of audit failure
                  logger.GetLogger().Ctx(reqCtx).Error("AuditMiddleware: Failed to record audit event via service", logger.WithError(err))
              }
          } else {
               logger.GetLogger().Ctx(reqCtx).Error("AuditMiddleware: AuditService is nil, cannot record event")
          }
      }()

      ctx.Next() // Execute subsequent handlers
  }
  // Helper functions like getAction, getResourceType, getResourceID, generateRandomID remain as in original.
  // ...
  ```

#### 6. Controller (`internal/controller/audit_log_controller_impl.go`)

```go
package controller

import (
    "backend/internal/model/dto"
    "backend/internal/service"
    "backend/pkg/response"
    "github.com/kataras/iris/v12"
)

type AuditLogControllerImpl struct {
    *BaseControllerImpl
    auditService service.AuditLogService
}

func NewAuditLogControllerImpl(cm *ControllerManager, auditService service.AuditLogService) *AuditLogControllerImpl {
    return &AuditLogControllerImpl{
        BaseControllerImpl: NewBaseController(cm),
        auditService:       auditService,
    }
}

// ListAuditLogs retrieves audit logs
// @Summary List audit logs
// @Description Get a paginated list of audit logs with filters
// @Tags Audit
// @Accept json
// @Produce json
// @Param pageNum query int false "Page number (default 1)" default(1)
// @Param pageSize query int false "Items per page (default 20, max value from constant.MAX_PAGE_SIZE)" default(20)
// @Param sort query string false "Sort order, comma-separated fields with :asc or :desc (e.g., timestamp:desc,username:asc). Default: system default sort"
// @Param userId query int false "Filter by User ID"
// @Param username query string false "Filter by Username (contains)"
// @Param action query string false "Filter by Action"
// @Param resourceType query string false "Filter by Resource Type"
// @Param clientIp query string false "Filter by Client IP"
// @Param traceId query string false "Filter by Trace ID"
// @Param status query string false "Filter by Status (success/failure)"
// @Param startDate query string false "Filter by start date (YYYY-MM-DD)"
// @Param endDate query string false "Filter by end date (YYYY-MM-DD)"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]vo.AuditLogVO}} "Success"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /admin/audit-logs [get]
func (c *AuditLogControllerImpl) ListAuditLogs(ctx iris.Context) {
    var queryDTO dto.AuditLogQueryDTO
    // Read filter-specific query parameters into queryDTO
    if err := ctx.ReadQuery(&queryDTO); err != nil {
        c.Fail(ctx, response.WithMsg("Invalid query filter parameters: "+err.Error()))
        return
    }

    // Build PageQuery for pagination and sorting parameters and assign to DTO
    // response.BuildPageQuery handles defaults for pageNum, pageSize, and sort.
    pageQueryInstance := response.BuildPageQuery(ctx)
    queryDTO.Pagination = *pageQueryInstance

    // Call the service, which now returns a PageResult with AuditLogVOs
    pageResultWithVOs, err := c.auditService.ListAuditLogs(queryDTO)
    if err != nil {
        c.HandleError(ctx, err, "ListAuditLogs") // Assuming HandleError exists for consistent error responses
        return
    }

    // The service already prepared the PageResult with VOs and correct total/page info.
    response.Success(ctx, pageResultWithVOs) // pageResultWithVOs is already a *response.PageResult
}
```

#### 7. Dependency Injection (`cmd/main.go`)

- **Create Services and Controllers**:

  ```go
  // ... after dbManager (gormDB) and log (appLogger) are initialized ...
  auditLogRepo := repository.NewAuditLogRepository(gormDB) // Updated constructor name
  auditLogService := service.NewAuditLogService(auditLogRepo, log) // Updated constructor name
  log.Info("AuditLogService 创建完成")

  auditLogController := controller.NewAuditLogControllerImpl(controllerManager, auditLogService)
  log.Info("AuditLogController 创建完成")

  // Create AuditMiddleware instance
  auditMiddlewareInstance := middleware.NewAuditMiddleware(auditLogService, middleware.AuditConfig{
      Enabled:             true,
      LogRequestBody:      false, // Keep lean for audit logs unless essential
      LogResponseBody:     false,
      MaxRequestBodySize:  2048, // Smaller limit for audit
      MaxResponseBodySize: 2048,
      // ExcludedPaths will be default minus login path
      // Other configs like ResourceIDRegex, ResourceTypeRegex can be default or customized
  })
  log.Info("AuditMiddleware 实例创建完成")
  ```

- **Pass to `router.SetupRoutes`**:
  ```go
  // router.SetupRoutes(..., auditMiddlewareInstance, auditLogController)
  ```

#### 8. Router Configuration (`internal/router/router.go`)

- **Modify `SetupRoutes` signature**:
  ```go
  func SetupRoutes(
      // ... other params ...
      licenseController *controller.LicenseControllerImpl, // Existing
      auditMiddleware *middleware.AuditMiddleware,       // New
      auditLogController *controller.AuditLogControllerImpl, // New
  ) { // ...
  ```
- **Apply `AuditMiddleware`**:

  ```go
  apiV1 := app.Party("/api/v1")
  {
      // ... public routes ...

      apiV1.Use(authMiddleware.Serve)                // 1. Auth
      apiV1.Use(middleware.LicenseCheckMiddleware()) // 2. License
      apiV1.Use(auditMiddleware.Serve)               // 3. Audit
      // ... protected routes ...
  }
  ```

- **Add Audit Log API Route**:
  ```go
  adminParty := apiV1.Party("/admin")
  adminParty.Use(middleware.AdminRequiredMiddleware())
  {
      // ... other admin routes ...
      auditLogsParty := adminParty.Party("/audit-logs")
      {
          auditLogsParty.Get("", auditLogController.ListAuditLogs)
      }
  }
  ```

#### 9. Handling Specific Audit Cases (e.g., Login)

对于如用户登录、登出等特定的操作，或者其他需要在控制器层面精确控制审计内容和时机的场景，**强烈推荐采用方案 2：控制器直接调用 `AuditLogService` 进行审计事件记录。**

这种方法具有以下优势：

- **精确性**: 可以在业务逻辑的关键节点（例如，成功验证密码后，或捕获到特定错误时）精确记录审计事件，并包含最准确的数据。
- **完整性**: 能够确保关键事件（如登录尝试，无论成功与否）都被记录，即使这些事件发生在 `AuthMiddleware` 等通用用户身份验证中间件之前或之外。
- **灵活性**: 控制器可以完全控制 `AuditEvent` 的所有字段，包括 `Action`, `ResourceType`, `ResourceID`, `Details`, `OldValue`, `NewValue`, `UserID`, `Username` 等，不受限于中间件的通用逻辑推断。

**方案 2: 控制器直接调用 `AuditLogService` (推荐)**

此方案要求将 `AuditLogService` 注入到相应的控制器中（例如 `AuthControllerImpl`）。控制器在执行完特定操作后，手动构建 `AuditEvent` 对象并调用 `auditLogService.RecordEvent()`。

为了防止 `AuditMiddleware` 在之后重复记录此事件，控制器在手动记录后，应在 Iris 上下文中设置一个标志：
`ctx.Values().Set(constant.AUDIT_EVENT_RECORDED_CTX_KEY, true)`

`AuditMiddleware` 在其执行逻辑的开始部分会检查此标志，如果为 `true`，则会跳过其自身的审计记录流程。

- **示例：在 `AuthControllerImpl.Login` 中记录登录审计**:

  ```go
  // 假设 AuthControllerImpl 已注入 auditService service.AuditLogService
  // ... 在 Login 方法内部 ...

  // 获取客户端IP和TraceID等通用信息 (通常从上下文中获取)
  clientIP, _ := util.GetClientIPFromStdContext(ctx.Request().Context())
  traceID := util.GetTraceIDFromStdContextDefault(ctx.Request().Context(), "") // 假设已由LoggerMiddleware设置

  // --- 登录成功 ---
  if err == nil { // 假设 err 为 nil 表示登录成功，loginVO 包含用户信息
      event := &middleware.AuditEvent{ // middleware.AuditEvent 可能需要调整路径或移至 model
          Action:       "login_success",
          UserID:       uint(loginVO.User.ID), // loginVO.User.ID 是 uint64 类型
          Username:     loginVO.User.Username,
          IP:           clientIP,
          UserAgent:    ctx.GetHeader("User-Agent"),
          RequestURI:   ctx.Path(),
          Method:       ctx.Method(),
          Time:         time.Now(), // 记录事件发生时间
          TraceID:      traceID,
          ResourceType: "authentication",
          ResourceID:   loginVO.User.Username, // 或 strconv.FormatUint(loginVO.User.ID, 10)
          Status:       "success",
          StatusCode:   http.StatusOK, // 或 ctx.GetStatusCode() 如果在成功响应之后记录
          // Details, OldValue, NewValue 可按需填充
      }
      go c.auditService.RecordEvent(event) // 使用 goroutine 异步记录，避免阻塞主流程
      ctx.Values().Set(constant.AUDIT_EVENT_RECORDED_CTX_KEY, true) // 通知 AuditMiddleware 跳过

      // ... 返回成功响应 ...
      return
  }

  // --- 登录失败 ---
  // 假设 err != nil 表示登录失败，loginDTO 包含用户尝试的登录信息
  failureEvent := &middleware.AuditEvent{
      Action:       "login_failure",
      Username:     loginDTO.Username, // 记录尝试登录的用户名，UserID此时可能未知或为0
      IP:           clientIP,
      UserAgent:    ctx.GetHeader("User-Agent"),
      RequestURI:   ctx.Path(),
      Method:       ctx.Method(),
      Time:         time.Now(),
      TraceID:      traceID,
      ResourceType: "authentication",
      ResourceID:   loginDTO.Username, // 记录尝试的用户名作为资源ID
      Status:       "failure",
      StatusCode:   ctx.GetStatusCode(), // 获取实际的失败状态码 (例如 400, 401)
      Details:      fmt.Sprintf("Login attempt failed for user '%s'. Reason: %s", loginDTO.Username, err.Error()),
  }
  go c.auditService.RecordEvent(failureEvent)
  ctx.Values().Set(constant.AUDIT_EVENT_RECORDED_CTX_KEY, true) // 通知 AuditMiddleware 跳过

  // ... 返回失败响应 ...
  ```

**关于 `AuditMiddleware` 的 `ExcludedPaths` 配置**:
即使我们采用方案 2 在控制器中手动记录登录等操作，通常建议**不要**将这些路径（如 `/api/v1/auth/login`）添加到 `AuditMiddleware` 的 `ExcludedPaths` 列表中。原因在于：

1.  `AUDIT_EVENT_RECORDED_CTX_KEY` 标志会确保 `AuditMiddleware` 不会重复记录这些已由控制器处理的事件。
2.  如果将来登录逻辑发生变化，或忘记在控制器中设置此标志，`AuditMiddleware` 仍然可以作为一种后备机制来尝试捕捉这些操作（尽管可能信息不如控制器层面精确）。

**方案 1: 控制器设置上下文值供 `AuditMiddleware` 读取 (作为备选)**

这种方法依赖于控制器在执行特定操作时，通过 `ctx.Values().Set()` 设置一系列预定义的上下文键（如 `constant.AUDIT_ACTION_CTX_KEY`, `constant.AUDIT_USER_ID_CTX_KEY` 等）。`AuditMiddleware` 在其 `defer` 函数中会读取这些键值来丰富或覆盖其自动推断的审计信息。

- **示例：在 `AuthControllerImpl.Login` 中设置上下文**:

  ```go
  // On successful login:
  ctx.Values().Set(constant.AUDIT_ACTION_CTX_KEY, "login_success")
  ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "authentication")
  ctx.Values().Set(constant.AUDIT_USER_ID_CTX_KEY, loginVO.User.ID) // loginVO.User.ID is uint64
  ctx.Values().Set(constant.AUDIT_USERNAME_CTX_KEY, loginVO.User.Username)
  ctx.Values().Set(constant.AUDIT_RESOURCE_ID_CTX_KEY, loginVO.User.Username) // Or UserID string

  // On failed login:
  ctx.Values().Set(constant.AUDIT_ACTION_CTX_KEY, "login_failure")
  ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "authentication")
  if loginDTO.Username != "" {
      ctx.Values().Set(constant.AUDIT_RESOURCE_ID_CTX_KEY, loginDTO.Username)
      ctx.Values().Set(constant.AUDIT_DETAILS_CTX_KEY, "Failed login attempt for: "+loginDTO.Username)
      // AUDIT_USERNAME_CTX_KEY 也可以在这里设置，如果希望审计日志中明确记录尝试的用户名
      ctx.Values().Set(constant.AUDIT_USERNAME_CTX_KEY, loginDTO.Username)
  }
  ```

  此方案对于简单的信息覆盖可能足够，但对于需要精确控制事件记录完整流程（包括时间、状态码等）或进行异步记录的复杂场景，方案 2 更为强大和推荐。

### 第三部分：前端显示审计日志

- **API Endpoint**: `GET /api/v1/admin/audit-logs` (supports filtering and pagination).
- **Frontend**:
  - **开发策略**: 建议复制现有的用户管理模块（例如 "User Management"）的前端代码作为基础模板，然后进行针对审计日志的适配和修改。这种方法有助于统一界面风格并加速开发进程。
  - **主要组件和页面**:
    - **路由**: 在前端路由配置中添加新的路由，例如 `/admin/audit-logs`，指向审计日志展示页面。
    - **API 服务层**: 创建或更新 API 服务文件 (例如，在 `src/api/` 目录下创建 `auditLog.ts` 或类似文件)，用于封装对后端 `GET /api/v1/admin/audit-logs` 接口的调用。此服务应处理请求参数的构造（包括筛选条件和分页参数）和响应数据的解析。
    - **页面组件**: 创建审计日志主页面组件 (例如 `src/views/system/audit-logs/index.vue`，具体路径和文件类型取决于项目使用的前端框架和约定)。
      - **筛选区域**: 实现一个表单或一组输入控件，用于用户输入筛选条件。这些条件应对应 `AuditLogQueryDTO` 中定义的字段，如：用户名 (`username`)、操作 (`action`)、资源类型 (`resourceType`)、客户端 IP (`clientIp`)、TraceID (`traceId`)、状态 (`status`)、开始日期 (`startDate`)、结束日期 (`endDate`)。日期选择器应允许选择日期范围。
      - **表格展示**: 使用数据表格组件（如 Element Plus ElTable, Ant Design Table 等）来显示审计日志列表。表格的列应根据 `AuditLogVO` 中的字段进行定义，至少应包括：ID, 用户 ID, 用户名, 操作, 资源类型, 资源 ID, 时间戳, 客户端 IP, 状态。其他字段如 UserAgent, RequestURI, Method, StatusCode, DurationMs, TraceID 可以根据需要选择性显示或在详情中展示。
      - **分页组件**: 集成项目中通用的分页组件，并将其与 API 返回的分页数据（`total`, `pageNum`, `pageSize`, `pages` 从 `response.PageResult` 获取）进行双向绑定，以实现数据分页加载和页码切换。
      - **详情展示**: 提供查看单条审计日志详细信息的功能。这通常可以通过点击表格中的某一行或行内的一个"查看详情"按钮来实现。详情可以显示在模态框 (Modal/Dialog) 或一个可展开的行内区域中，应完整展示 `AuditLogVO` 的所有字段，特别是 `OldValue`, `NewValue`, 和 `Details`。`OldValue` 和 `NewValue` (如果是 JSON 字符串) 可能需要格式化展示（如 JSON pretty print）。
    - **状态管理 (可选)**: 如果项目采用了如 Vuex, Pinia, Redux 等全局状态管理库，可以考虑为审计日志模块创建相应的 store/slice，用于管理筛选条件、加载状态、日志列表数据和分页信息等。
  - **适配要点 (基于用户管理模块模板进行修改)**:
    - **接口对接**: 更新 API 请求的 URL 指向审计日志接口。调整请求参数的构造逻辑以匹配 `AuditLogQueryDTO`。修改响应数据的处理逻辑以适配 `AuditLogVO` 和 `PageResult` 结构。
    - **筛选逻辑**: 修改筛选表单的字段，确保其与审计日志的筛选需求一致。更新筛选条件的校验和提交逻辑。
    - **表格列配置**: 重新配置表格的列定义（列名、对应的数据字段、数据格式化函数等），以正确显示审计日志信息。
    - **数据模型**: 如果在前端代码中有明确的数据模型或类型定义 (如 TypeScript interfaces/types)，需要根据 `AuditLogVO` 进行更新。
    - **详情视图**: 调整详情展示组件，使其能够正确显示审计日志的特定字段，如 `OldValue`, `NewValue`。
    - **权限控制**: 确保新添加的审计日志页面和相关操作（如查询）已纳入前端的权限控制体系，通常只有管理员角色的用户才能访问。
    - **本地化/国际化**: 如果项目支持多语言，确保所有新增的文本（如页面标题、表头、按钮文字等）都已添加到相应的语言资源文件中。(暂不处理)

### 第四部分：`OldValue` 和 `NewValue` 填充 (Advanced)

为了在审计日志中记录变更前后的数据快照，同时兼顾准确性和性能，我们采用以下策略填充 `OldValue` 和 `NewValue`：

1.  **`OldValue` 的获取与设置**:

    - **对于更新 (Update) 和 删除 (Delete) 操作**:
      - 服务层的方法 (例如 `UserService.UpdateUser`, `RoleService.DeleteRole` 等) 必须在其执行实际的数据库修改或删除逻辑**之前**，从数据库中查询出对应资源的当前完整状态。
      - 获取到实体后，应进行必要的 **脱敏处理** (例如，移除或屏蔽密码哈希、API 密钥等敏感字段，裁剪过大的文本字段，或使用专门为审计设计的 DTO 进行序列化)。
      - 将脱敏和序列化后的 JSON 字符串通过 `ctx.Values().Set(constant.AUDIT_OLD_VALUE_CTX_KEY, oldJsonString)` 存入 Iris 上下文。
      - 这样做可以确保 `OldValue` 准确地反映了资源在被修改或删除前的状态。
    - **对于创建 (Create) 操作**:
      - 由于资源在创建前不存在，服务层方法应将 `OldValue` 设置为 `"null"` 或一个空的 JSON 对象字符串 `"{}"`。
      - 例如：`ctx.Values().Set(constant.AUDIT_OLD_VALUE_CTX_KEY, "null")`。

2.  **`NewValue` 的获取与设置**:

    - **对于创建 (Create) 和 更新 (Update) 操作**:
      - **核心优化**: `AuditMiddleware` 将负责从 HTTP 响应体中捕获 `NewValue`，从而避免服务层在数据库操作后进行额外的查询。
      - **前提条件**: 对应的控制器方法 (例如 `UserControllerImpl.CreateUser`, `UserControllerImpl.UpdateUser`) 在成功处理请求后，必须在其 HTTP 响应体中完整地返回新创建或已更新的资源对象（JSON 格式）。
      - **`AuditMiddleware` 实现**:
        - 中间件需要确保开启响应体记录功能，例如在 Iris 中，可以在 `defer` 块执行前或早期通过 `ctx.RecordResponseBody(true)` 实现。
        - 在 `defer` 函数块中，中间件通过 `ctx.ResponseWriter().Body()` 读取响应体字节数组。
        - 如果响应成功 (例如 `statusCode == http.StatusCreated` 或 `http.StatusOK`) 并且响应体不为空，则将响应体字节数组（可能需要先转换为字符串）作为 `AuditEvent` 的 `NewValue` 字段。
        - **响应体脱敏**: 与 `OldValue` 类似，从响应体获取的 `NewValue` 在存入审计日志前，也可能需要进行脱敏处理。这可以在 `AuditMiddleware` 中完成，例如：将响应体 JSON 字符串反序列化到一个通用 `map[string]interface{}` 或特定结构体，进行字段屏蔽/修改，然后再序列化回 JSON 字符串存入 `AuditEvent`。
    - **对于删除 (Delete) 操作**:
      - 资源已被删除，因此 `NewValue` 通常是 `"null"` 或 `"{}"`。
      - 这可以由服务层在上下文中明确设置 `constant.AUDIT_NEW_VALUE_CTX_KEY`，或者由 `AuditMiddleware` 根据请求方法 (`http.MethodDelete`) 和成功的状态码（如 `http.StatusOK` 或 `http.StatusNoContent`）来推断并设置。

3.  **`AuditMiddleware` 的整合**:
    - `AuditMiddleware` 在其 `defer` 函数块中，会检查 Iris 上下文中是否存在 `constant.AUDIT_OLD_VALUE_CTX_KEY`。
    - 对于 `NewValue`，它会优先尝试从捕获的响应体中获取 (针对 C/U 操作)，如果响应体不适用或操作为 Delete，则可能依赖于上下文中由服务层设置的 `constant.AUDIT_NEW_VALUE_CTX_KEY`，或自行根据操作类型判断。

**总结此策略的考虑**:

- **性能优化**: 通过从响应体获取 `NewValue` (C/U 操作)，有效减少了不必要的数据库查询，降低了审计带来的性能开销。
- **数据准确性**: `OldValue` (U/D 操作) 仍然通过事前查询数据库来保证其准确反映操作前的状态。
- **依赖 API 设计**: `NewValue` 的准确捕获依赖于 C/U 操作的 API 端点在成功时能返回完整的资源表述。
- **脱敏处理**: 必须在服务层（针对`OldValue`）和/或`AuditMiddleware`（针对从响应体捕获的`NewValue`）中实施严格的数据脱敏逻辑，以防止敏感信息泄露到审计日志中。

## 5. 总结与后续

- Thoroughly test all middleware and API endpoints.
- Monitor performance, especially if request/response bodies are logged extensively.
- Refine `AuditConfig` (regexes, exclusions) based on API structure and audit requirements.
- Ensure proper error handling and logging within the new services and middleware.
- Implement the frontend for viewing audit logs.

This plan provides a comprehensive approach to integrating the enhanced Logger and Audit middlewares.
