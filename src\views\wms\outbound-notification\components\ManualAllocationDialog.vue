<template>
  <el-dialog
    v-model="dialogVisible"
    title="手动分配库存"
    width="80%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="manual-allocation-container">
      <!-- 物料信息 -->
      <div class="item-info" v-if="currentItem">
        <el-descriptions title="物料信息" :column="3" border>
          <el-descriptions-item label="物料编码">
            {{ currentItem.itemCode }}
          </el-descriptions-item>
          <el-descriptions-item label="物料名称">
            {{ currentItem.itemName }}
          </el-descriptions-item>
          <el-descriptions-item label="需求数量">
            {{ currentItem.requiredQty }}
          </el-descriptions-item>
          <el-descriptions-item label="已分配数量">
            {{ currentItem.allocatedQty || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="未分配数量">
            {{ currentItem.requiredQty - (currentItem.allocatedQty || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="单位">
            {{ currentItem.unitOfMeasure }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 可用库存列表 -->
      <div class="available-inventory">
        <h4>可用库存</h4>
        <VNTable
          ref="availableTableRef"
          :data="availableInventory"
          :columns="availableColumns"
          :loading="loading"
          :show-operations="true"
          :operation-width="150"
          operation-fixed="right"
          row-key="inventoryId"
          show-index
        >
          <template #column-expiryDate="{ row }">
            <span :class="{ 'text-warning': isNearExpiry(row.expiryDate), 'text-danger': isExpired(row.expiryDate) }">
              {{ row.expiryDate || '-' }}
            </span>
          </template>
          
          <template #operation="{ row }">
            <el-input-number
              v-model="row.allocateQty"
              :min="0"
              :max="Math.min(row.availableQty, remainingQty + (row.allocateQty || 0))"
              :precision="0"
              size="small"
              style="width: 100px"
              @change="handleAllocationChange"
            />
          </template>
        </VNTable>
      </div>
      
      <!-- 分配汇总 -->
      <div class="allocation-summary">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="需求数量" :value="currentItem?.requiredQty || 0" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已选择数量" :value="selectedQty" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="剩余数量" :value="remainingQty" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="完成率" :value="completionRate" suffix="%" />
          </el-col>
        </el-row>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button
          type="primary"
          :disabled="selectedQty === 0"
          @click="handleConfirm"
        >
          确定分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElDescriptions, ElDescriptionsItem, ElInputNumber, ElRow, ElCol, ElStatistic } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn } from '@/components/VNTable/types'
import * as inventoryAllocationApi from '@/api/wms/inventoryAllocation'

// 组件接口定义
interface ManualAllocationDialogEmits {
  confirm: [allocations: any[]]
  cancel: []
}

// Emits
const emit = defineEmits<ManualAllocationDialogEmits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const currentItem = ref<any>(null)
const availableInventory = ref<any[]>([])

// 表格列配置
const availableColumns = computed<TableColumn[]>(() => [
  { prop: 'locationCode', label: '库位', width: 100 },
  { prop: 'batchNo', label: '批次号', width: 120 },
  { prop: 'availableQty', label: '可用数量', width: 100 },
  { prop: 'productionDate', label: '生产日期', width: 120 },
  { prop: 'expiryDate', label: '过期日期', width: 120, slot: true },
  { prop: 'quality', label: '质量等级', width: 100 }
])

// 计算属性
const selectedQty = computed(() => {
  return availableInventory.value.reduce((sum, item) => sum + (item.allocateQty || 0), 0)
})

const remainingQty = computed(() => {
  if (!currentItem.value) return 0
  return currentItem.value.requiredQty - (currentItem.value.allocatedQty || 0) - selectedQty.value
})

const completionRate = computed(() => {
  if (!currentItem.value || currentItem.value.requiredQty === 0) return 0
  const totalAllocated = (currentItem.value.allocatedQty || 0) + selectedQty.value
  return Math.round((totalAllocated / currentItem.value.requiredQty) * 100)
})

// 方法
const loadAvailableInventory = async () => {
  if (!currentItem.value) return
  
  loading.value = true
  try {
    const result = await inventoryAllocationApi.checkInventoryAvailability(
      currentItem.value.itemId,
      currentItem.value.warehouseId
    )
    
    // 转换数据格式，添加分配数量字段
    availableInventory.value = result.locations.map(location => ({
      inventoryId: location.locationId, // 这里应该是实际的库存ID
      locationCode: location.locationCode,
      batchNo: location.batchNo,
      availableQty: location.availableQty,
      productionDate: location.productionDate,
      expiryDate: location.expiryDate,
      quality: 'A', // 默认质量等级
      allocateQty: 0 // 初始分配数量为0
    }))
  } catch (error) {
    ElMessage.error('加载可用库存失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  loadAvailableInventory()
}

const handleClose = () => {
  // 清理数据
  currentItem.value = null
  availableInventory.value = []
}

const handleAllocationChange = () => {
  // 分配数量变化时的处理逻辑
  // 可以在这里添加验证逻辑
}

const handleReset = () => {
  availableInventory.value.forEach(item => {
    item.allocateQty = 0
  })
}

const handleConfirm = async () => {
  if (!currentItem.value) return
  
  // 获取有分配数量的库存
  const allocations = availableInventory.value
    .filter(item => item.allocateQty > 0)
    .map(item => ({
      inventoryId: item.inventoryId,
      allocatedQty: item.allocateQty
    }))
  
  if (allocations.length === 0) {
    ElMessage.warning('请选择要分配的库存')
    return
  }
  
  try {
    // 调用手动分配API
    await inventoryAllocationApi.manualAllocate({
      outboundDetailId: currentItem.value.id,
      allocations,
      remark: '手动分配'
    })
    
    ElMessage.success('手动分配成功')
    emit('confirm', allocations)
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('手动分配失败')
    console.error(error)
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const isNearExpiry = (expiryDate: string) => {
  if (!expiryDate) return false
  const expiry = new Date(expiryDate)
  const now = new Date()
  const diffDays = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays <= 30 && diffDays > 0
}

const isExpired = (expiryDate: string) => {
  if (!expiryDate) return false
  const expiry = new Date(expiryDate)
  const now = new Date()
  return expiry < now
}

// 暴露方法
const open = (items: any[]) => {
  if (items && items.length > 0) {
    currentItem.value = items[0] // 目前只支持单个物料的手动分配
    dialogVisible.value = true
  }
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.manual-allocation-container {
  padding: 16px 0;
}

.item-info {
  margin-bottom: 20px;
}

.available-inventory {
  margin-bottom: 20px;
}

.available-inventory h4 {
  margin-bottom: 12px;
  color: #303133;
}

.allocation-summary {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
