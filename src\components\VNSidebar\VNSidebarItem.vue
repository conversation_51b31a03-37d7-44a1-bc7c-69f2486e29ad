<!-- src/components/VNSidebar/VNSidebarItem.vue -->
<template>
  <!-- 如果有子菜单且子菜单不为空 -->
  <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.index || item.title">
    <template #title>
      <el-icon v-if="item.icon">
        <component :is="item.icon" />
      </el-icon>
      <span>{{ item.title }}</span>
    </template>
    <!-- 递归调用自身来渲染子菜单 -->
    <VNSidebarItem v-for="child in item.children" :key="child.index || child.title" :item="child" />
  </el-sub-menu>

  <!-- 如果没有子菜单 (或者子菜单为空)，渲染为普通菜单项 -->
  <el-menu-item v-else :index="item.index || (item.to ? resolveRoute(item.to) : item.title)" :route="item.to">
    <el-icon v-if="item.icon">
      <component :is="item.icon" />
    </el-icon>
    <template #title>{{ item.title }}</template>
  </el-menu-item>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import type { RouteLocationRaw } from 'vue-router';
import { useRouter } from 'vue-router'; // Import useRouter for route resolution
import { ElMenuItem, ElSubMenu, ElIcon } from 'element-plus';
import type { SidebarItem } from './types'; // 假设类型定义在同级目录的 types.ts

// Define props for the component
const props = defineProps<{
  item: SidebarItem;
}>();

const router = useRouter();

// Helper function to resolve route path (copied from VNSidebar/index.vue, consider moving to a util if used elsewhere)
const resolveRoute = (to: RouteLocationRaw): string => {
  try {
    const resolved = router.resolve(to);
    return resolved.path;
  } catch (e) {
    console.error('Error resolving route in VNSidebarItem:', to, e);
    return typeof to === 'string' ? to : (to as any).path || '#';
  }
};

// Note: Since this component recursively uses itself,
// Vue automatically handles the component name resolution within the template.
// No need to explicitly import 'VNSidebarItem' inside itself in <script setup>.
</script>

<style scoped>
/* Add specific styles for VNSidebarItem if needed, e.g., indentation */
.el-menu-item,
.el-sub-menu {
  /* Example: Adjust indentation for nested items */
  /* You might need more specific selectors based on nesting level */
}
.el-icon {
  vertical-align: middle;
  margin-right: 5px;
}
</style> 