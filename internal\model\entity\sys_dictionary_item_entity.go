package entity

// DictionaryItem 字典项实体
// 存储具体的字典数据
type DictionaryItem struct {
	BaseEntity              // 继承基础实体 (ID, CreatedAt, UpdatedAt 等)
	DictionaryTypeID uint   `gorm:"column:dictionary_type_id;not null;index;comment:字典类型ID" json:"dictionaryTypeId"` // 关联的字典类型ID
	Label            string `gorm:"column:label;type:varchar(100);not null;comment:字典项标签" json:"label"`              // 字典项标签 (展示用)
	Value            string `gorm:"column:value;type:varchar(100);not null;comment:字典项值" json:"value"`               // 字典项值 (程序中使用或存储的值)
	SortOrder        int    `gorm:"column:sort_order;default:0;comment:排序值" json:"sortOrder"`                        // 排序值 (值越小越靠前)
	Status           int    `gorm:"column:status;type:smallint;default:1;comment:状态 (1:启用, 0:禁用)" json:"status"`     // 状态 (1:启用, 0:禁用)
	IsSystem         bool   `gorm:"column:is_system;default:false;comment:是否系统内置" json:"isSystem"`                   // 是否系统内置 (系统内置通常不允许删除)
	Remark           string `gorm:"column:remark;type:varchar(500);comment:备注" json:"remark"`                        // 备注

	// 关联字段 (多对一) - 可选，方便查询
	// DictionaryType DictionaryType `gorm:"foreignKey:DictionaryTypeID;references:ID" json:"dictionaryType,omitempty"`
}

// TableName 指定表名
func (DictionaryItem) TableName() string {
	return "sys_dictionary_item"
}
