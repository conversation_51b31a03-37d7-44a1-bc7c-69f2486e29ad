package vo

import (
	"time"
)

// WmsInventoryVO 库存详细视图对象
type WmsInventoryVO struct {
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	CreatedBy     uint      `json:"createdBy"`
	UpdatedBy     uint      `json:"updatedBy"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	WarehouseID    uint       `json:"warehouseId"`
	LocationID     uint       `json:"locationId"`
	ItemID         uint       `json:"itemId"`
	ClientID       uint       `json:"clientId"`
	BatchNo        *string    `json:"batchNo,omitempty"`
	ProductionDate *time.Time `json:"productionDate,omitempty"`
	ExpiryDate     *time.Time `json:"expiryDate,omitempty"`
	Quantity       float64    `json:"quantity"`
	AllocatedQty   float64    `json:"allocatedQty"`
	FrozenQty      float64    `json:"frozenQty"`
	UnitOfMeasure  string     `json:"unitOfMeasure"`
	Status         string     `json:"status"`
	Remark         *string    `json:"remark,omitempty"`
	LastUpdated    time.Time  `json:"lastUpdated"`

	// 计算字段
	AvailableQuantity float64 `json:"availableQuantity"`
}

// WmsInventorySimpleVO 库存简单视图对象（用于列表显示）
type WmsInventorySimpleVO struct {
	ID                uint       `json:"id"`
	WarehouseID       uint       `json:"warehouseId"`
	LocationID        uint       `json:"locationId"`
	ItemID            uint       `json:"itemId"`
	ClientID          uint       `json:"clientId"`
	BatchNo           *string    `json:"batchNo,omitempty"`
	Quantity          float64    `json:"quantity"`
	AllocatedQty      float64    `json:"allocatedQty"`
	FrozenQty         float64    `json:"frozenQty"`
	AvailableQuantity float64    `json:"availableQuantity"`
	UnitOfMeasure     string     `json:"unitOfMeasure"`
	Status            string     `json:"status"`
	ExpiryDate        *time.Time `json:"expiryDate,omitempty"`
	LastUpdated       time.Time  `json:"lastUpdated"`
}

// WmsInventoryAggregateVO 库存汇总视图对象（按物料和客户汇总）
type WmsInventoryAggregateVO struct {
	ItemID            uint       `json:"itemId"`
	ClientID          uint       `json:"clientId"`
	TotalQuantity     float64    `json:"totalQuantity"`
	TotalAllocatedQty float64    `json:"totalAllocatedQty"`
	TotalFrozenQty    float64    `json:"totalFrozenQty"`
	TotalAvailableQty float64    `json:"totalAvailableQty"`
	UnitOfMeasure     string     `json:"unitOfMeasure"`
	LocationCount     int        `json:"locationCount"`
	NearestExpiryDate *time.Time `json:"nearestExpiryDate,omitempty"`
	LastUpdated       time.Time  `json:"lastUpdated"`
}

// WmsInventoryTransactionLogVO 库存事务日志视图对象
type WmsInventoryTransactionLogVO struct {
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	TransactionTime    time.Time `json:"transactionTime"`
	TransactionType    string    `json:"transactionType"`
	ItemID             uint      `json:"itemId"`
	WarehouseID        uint      `json:"warehouseId"`
	LocationID         uint      `json:"locationId"`
	ClientID           uint      `json:"clientId"`
	BatchNo            *string   `json:"batchNo,omitempty"`
	InventoryStatus    string    `json:"inventoryStatus"`
	QuantityChange     float64   `json:"quantityChange"`
	UnitOfMeasure      string    `json:"unitOfMeasure"`
	BalanceBefore      float64   `json:"balanceBefore"`
	BalanceAfter       float64   `json:"balanceAfter"`
	ReferenceDocType   *string   `json:"referenceDocType,omitempty"`
	ReferenceDocID     *uint     `json:"referenceDocId,omitempty"`
	ReferenceDocLineID *uint     `json:"referenceDocLineId,omitempty"`
	OperatorID         *uint     `json:"operatorId,omitempty"`
	Remark             *string   `json:"remark,omitempty"`
}

// WmsInventoryPageResult 库存分页结果
type WmsInventoryPageResult = PageResult[WmsInventorySimpleVO]

// WmsInventoryTransactionLogPageResult 库存事务日志分页结果
type WmsInventoryTransactionLogPageResult = PageResult[WmsInventoryTransactionLogVO]
