package vo

import (
	"time"
)

// WmsInventorySimpleVO 库存简单视图对象（用于列表显示）
type WmsInventorySimpleVO struct {
	ID                uint       `json:"id"`
	WarehouseID       uint       `json:"warehouseId"`
	LocationID        uint       `json:"locationId"`
	ItemID            uint       `json:"itemId"`
	ClientID          uint       `json:"clientId"`
	BatchNo           *string    `json:"batchNo,omitempty"`
	Quantity          float64    `json:"quantity"`
	AllocatedQty      float64    `json:"allocatedQty"`
	FrozenQty         float64    `json:"frozenQty"`
	AvailableQuantity float64    `json:"availableQuantity"`
	UnitOfMeasure     string     `json:"unitOfMeasure"`
	Status            string     `json:"status"`
	ExpiryDate        *time.Time `json:"expiryDate,omitempty"`
	LastUpdated       time.Time  `json:"lastUpdated"`
}

// WmsInventoryAggregateVO 库存汇总视图对象（按物料和客户汇总）
type WmsInventoryAggregateVO struct {
	ItemID            uint       `json:"itemId"`
	ClientID          uint       `json:"clientId"`
	TotalQuantity     float64    `json:"totalQuantity"`
	TotalAllocatedQty float64    `json:"totalAllocatedQty"`
	TotalFrozenQty    float64    `json:"totalFrozenQty"`
	TotalAvailableQty float64    `json:"totalAvailableQty"`
	UnitOfMeasure     string     `json:"unitOfMeasure"`
	LocationCount     int        `json:"locationCount"`
	NearestExpiryDate *time.Time `json:"nearestExpiryDate,omitempty"`
	LastUpdated       time.Time  `json:"lastUpdated"`
}

// WmsInventoryPageResult 库存分页结果
type WmsInventoryPageResult = PageResult[WmsInventorySimpleVO]

// WmsInventoryTransactionLogPageResult 库存事务日志分页结果
type WmsInventoryTransactionLogPageResult = PageResult[WmsInventoryTransactionLogVO]
