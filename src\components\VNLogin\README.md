# VNLogin Component

A versatile login form component built with Vue 3, TypeScript, and Element Plus.

## Features

-   Centered login card layout (optional left panel).
-   Optional left panel for branding/information with background image.
-   Username and password inputs with icons.
-   Password visibility toggle.
-   Form validation (default: required fields).
-   Loading state display on the login button.
-   Error message display using `el-alert`.
-   Optional 'Remember Me' checkbox.
-   Optional 'Forgot Password' link.
-   Emits events for login submission and forgot password clicks.
-   Customizable header and footer via slots.

## Props

| Prop                        | Type                      | Default          | Description                                                    |
| :-------------------------- | :------------------------ | :--------------- | :------------------------------------------------------------- |
| `initialUsername`           | `string`                  | `''`             | Initial value for the username input.                          |
| `loading`                   | `boolean`                 | `false`          | Controls the loading state of the login button.                |
| `rules`                     | `FormRules` (El+)       | (Internal)       | Custom Element Plus validation rules to override defaults.     |
| `showRememberMe`            | `boolean`                 | `true`           | Whether to display the 'Remember Me' checkbox.                 |
| `showForgotPassword`        | `boolean`                 | `false`          | Whether to display the 'Forgot Password' link.                 |
| `showCaptcha`               | `boolean`                 | `false`          | Whether to show the captcha section.                           |
| `captchaImageUrl`           | `string`                  | `''`             | URL for the captcha image (required if `showCaptcha` is true). |
| `showAlternativeLogins`     | `boolean`                 | `false`          | Whether to show the alternative logins section (slot).        |
| `showLeftPanel`             | `boolean`                 | `true`           | Whether to display the left information panel.                 |
| `leftPanelBackgroundImageUrl`| `string`                 | (Default Image)  | Background image URL for the left panel.                       |
| `systemTitle`               | `string`                  | '后台管理系统' | System title in the left panel (default content).            |
| `version`                   | `string`                  | '1.0.0'          | Version number in the left panel (default content).            |
| `logoUrl`                   | `string`                  | `''`             | URL for the logo in the left panel (default content).          |
| `logoAlt`                   | `string`                  | 'Logo'           | Alt text for the logo.                                         |

## Events

| Event             | Payload Type          | Description                                                            |
| :---------------- | :-------------------- | :--------------------------------------------------------------------- |
| `login`           | `LoginFormModel`      | Emitted when the form is valid and the login button is clicked.        |
| `forgot-password` | `void`                | Emitted when the 'Forgot Password' link is clicked.                    |
| `refresh-captcha` | `void`                | Emitted when the user requests to refresh the captcha.                 |

## Slots

| Name                 | Description                                                      |
| :------------------- | :--------------------------------------------------------------- |
| `header`             | Custom content for the card header (replaces default title).     |
| `footer`             | Custom content below the login form (e.g., copyright, links).    |
| `alternative-logins` | Custom content for the alternative login methods section.        |
| `left-panel`         | Completely replaces the default left panel content.              |
| `left-panel-header`  | Custom content for the top area of the left panel (logo/title).  |
| `left-panel-body`    | Custom content for the middle area of the left panel.            |
| `left-panel-footer`  | Custom content for the bottom area of the left panel (version).  |

## Data Models

**LoginFormModel**

```typescript
interface LoginFormModel {
  username: string;
  password: string;
  rememberMe: boolean;
}
```

## Usage Example

```vue
<template>
  <VNLogin 
    :loading="isLoggingIn" 
    @login="handleLoginAttempt"
    @forgot-password="handleForgotPasswordPrompt"
    @refresh-captcha="handleRefreshCaptcha"
    :show-captcha="showCaptcha"
    :captcha-image-url="captchaUrl"
    :show-alternative-logins="true"
  >
    <template #header>
       <img src="/path/to/your/logo.png" alt="Logo" style="height: 40px;">
       <h2 style="margin-top: 10px;">My Application</h2>
    </template>
    <template #footer>
      © {{ new Date().getFullYear() }} My Company
    </template>
  </VNLogin>
</template>

<script setup>
import { ref } from 'vue';
import VNLogin from '@/components/VNLogin/index.vue'; 
import { ElMessage } from 'element-plus';

const isLoggingIn = ref(false);
const showCaptcha = ref(false);
const captchaUrl = ref('');

const handleLoginAttempt = async (loginData) => {
  isLoggingIn.value = true;
  try {
    console.log('Login attempt with:', loginData);
    await new Promise(resolve => setTimeout(resolve, 1500)); 
    console.log('Login successful (simulated)');
    if (loginData.username !== 'admin') {
        throw new Error('Invalid credentials');
    }
    ElMessage.success('登录成功！');

  } catch (error) {
    console.error('Login failed:', error);
    ElMessage.error(error.message || '登录失败，请稍后重试。');
    if (showCaptcha.value) {
        handleRefreshCaptcha();
    }
  } finally {
    isLoggingIn.value = false;
  }
};

const handleForgotPasswordPrompt = () => {
    console.log('Forgot password clicked');
    ElMessage.info('忘记密码处理逻辑触发。');
};

const handleRefreshCaptcha = () => {
    console.log('Refreshing captcha...');
    captchaUrl.value = '';
    setTimeout(() => {
        captchaUrl.value = `https://picsum.photos/seed/${Date.now()}/100/40`;
    }, 300);
};

</script>
``` 