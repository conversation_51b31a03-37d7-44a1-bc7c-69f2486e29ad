package repository

import (
	"fmt"
	"strings"
	"time"

	"backend/pkg/logger"

	"gorm.io/gorm"
)

// --- 新增：QueryCondition 接口 ---
type QueryCondition interface {
	Apply(db *gorm.DB) *gorm.DB
}

// ConditionOperator 条件操作符类型
type ConditionOperator string

// 条件操作符常量
const (
	OP_EQ          ConditionOperator = "="           // 等于
	OP_NE          ConditionOperator = "<>"          // 不等于
	OP_GT          ConditionOperator = ">"           // 大于
	OP_GTE         ConditionOperator = ">="          // 大于等于
	OP_LT          ConditionOperator = "<"           // 小于
	OP_LTE         ConditionOperator = "<="          // 小于等于
	OP_LIKE        ConditionOperator = "LIKE"        // 模糊匹配
	OP_NOT_LIKE    ConditionOperator = "NOT LIKE"    // 不匹配
	OP_IN          ConditionOperator = "IN"          // 包含
	OP_NOT_IN      ConditionOperator = "NOT IN"      // 不包含
	OP_BETWEEN     ConditionOperator = "BETWEEN"     // 范围
	OP_NOT_BETWEEN ConditionOperator = "NOT BETWEEN" // 不在范围
	OP_IS_NULL     ConditionOperator = "IS NULL"     // 为空
	OP_IS_NOT_NULL ConditionOperator = "IS NOT NULL" // 不为空
)

// Condition 查询条件
type Condition struct {
	Field    string            // 字段名
	Operator ConditionOperator // 操作符
	Value    interface{}       // 值
}

// --- 为 Condition 实现 Apply 方法 ---
func (c Condition) Apply(db *gorm.DB) *gorm.DB {
	switch c.Operator {
	case OP_EQ:
		return db.Where(fmt.Sprintf("%s = ?", c.Field), c.Value)
	case OP_NE:
		return db.Where(fmt.Sprintf("%s <> ?", c.Field), c.Value)
	case OP_GT:
		return db.Where(fmt.Sprintf("%s > ?", c.Field), c.Value)
	case OP_GTE:
		return db.Where(fmt.Sprintf("%s >= ?", c.Field), c.Value)
	case OP_LT:
		return db.Where(fmt.Sprintf("%s < ?", c.Field), c.Value)
	case OP_LTE:
		return db.Where(fmt.Sprintf("%s <= ?", c.Field), c.Value)
	case OP_LIKE:
		return db.Where(fmt.Sprintf("%s LIKE ?", c.Field), c.Value)
	case OP_NOT_LIKE:
		return db.Where(fmt.Sprintf("%s NOT LIKE ?", c.Field), c.Value)
	case OP_IN:
		// GORM 推荐直接使用 map 或 slice 作为 IN 的值
		return db.Where(fmt.Sprintf("%s IN ?", c.Field), c.Value)
	case OP_NOT_IN:
		return db.Where(fmt.Sprintf("%s NOT IN ?", c.Field), c.Value)
	case OP_IS_NULL:
		return db.Where(fmt.Sprintf("%s IS NULL", c.Field))
	case OP_IS_NOT_NULL:
		return db.Where(fmt.Sprintf("%s IS NOT NULL", c.Field))
	case OP_BETWEEN:
		if values, ok := c.Value.([]interface{}); ok && len(values) == 2 {
			return db.Where(fmt.Sprintf("%s BETWEEN ? AND ?", c.Field), values[0], values[1])
		}
		logger.Warnf("Invalid value for BETWEEN operator: %v", c.Value)
		return db // Or handle error appropriately
	case OP_NOT_BETWEEN:
		if values, ok := c.Value.([]interface{}); ok && len(values) == 2 {
			return db.Where(fmt.Sprintf("%s NOT BETWEEN ? AND ?", c.Field), values[0], values[1])
		}
		logger.Warnf("Invalid value for NOT BETWEEN operator: %v", c.Value)
		return db // Or handle error appropriately
	default:
		logger.Warnf("Unsupported Condition operator: %s", c.Operator)
		return db // Or handle error appropriately
	}
}

// NewCondition 创建查询条件
// 参数:
//   - field: 字段名
//   - operator: 操作符
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewCondition(field string, operator ConditionOperator, value interface{}) Condition {
	return Condition{
		Field:    field,
		Operator: operator,
		Value:    value,
	}
}

// NewEqualCondition 创建等于条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewEqualCondition(field string, value interface{}) Condition {
	return NewCondition(field, OP_EQ, value)
}

// NewNotEqualCondition 创建不等于条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewNotEqualCondition(field string, value interface{}) Condition {
	return NewCondition(field, OP_NE, value)
}

// NewGreaterThanCondition 创建大于条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewGreaterThanCondition(field string, value interface{}) Condition {
	return NewCondition(field, OP_GT, value)
}

// NewLessThanCondition 创建小于条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewLessThanCondition(field string, value interface{}) Condition {
	return NewCondition(field, OP_LT, value)
}

// NewGreaterThanEqualCondition 创建大于等于条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewGreaterThanEqualCondition(field string, value interface{}) Condition {
	return NewCondition(field, OP_GTE, value)
}

// NewLessThanEqualCondition 创建小于等于条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewLessThanEqualCondition(field string, value interface{}) Condition {
	return NewCondition(field, OP_LTE, value)
}

// NewLikeCondition 创建模糊匹配条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewLikeCondition(field string, value string) Condition {
	if !strings.Contains(value, "%") {
		value = "%" + value + "%"
	}
	return NewCondition(field, OP_LIKE, value)
}

// NewNotLikeCondition 创建不匹配条件
// 参数:
//   - field: 字段名
//   - value: 值
//
// 返回:
//   - Condition: 查询条件
func NewNotLikeCondition(field string, value string) Condition {
	if !strings.Contains(value, "%") {
		value = "%" + value + "%"
	}
	return NewCondition(field, OP_NOT_LIKE, value)
}

// NewStartsWithCondition 创建前缀匹配条件
// 参数:
//   - field: 字段名
//   - value: 前缀值
//
// 返回:
//   - Condition: 查询条件
func NewStartsWithCondition(field string, value string) Condition {
	return NewCondition(field, OP_LIKE, value+"%")
}

// NewEndsWithCondition 创建后缀匹配条件
// 参数:
//   - field: 字段名
//   - value: 后缀值
//
// 返回:
//   - Condition: 查询条件
func NewEndsWithCondition(field string, value string) Condition {
	return NewCondition(field, OP_LIKE, "%"+value)
}

// NewInCondition 创建IN条件
// 参数:
//   - field: 字段名
//   - values: 值集合
//
// 返回:
//   - Condition: 查询条件
func NewInCondition(field string, values interface{}) Condition {
	return NewCondition(field, OP_IN, values)
}

// NewNotInCondition 创建NOT IN条件
// 参数:
//   - field: 字段名
//   - values: 值集合
//
// 返回:
//   - Condition: 查询条件
func NewNotInCondition(field string, values interface{}) Condition {
	return NewCondition(field, OP_NOT_IN, values)
}

// NewBetweenCondition 创建BETWEEN条件
// 参数:
//   - field: 字段名
//   - start: 开始值
//   - end: 结束值
//
// 返回:
//   - Condition: 查询条件
func NewBetweenCondition(field string, start, end interface{}) Condition {
	return NewCondition(field, OP_BETWEEN, []interface{}{start, end})
}

// NewNotBetweenCondition 创建NOT BETWEEN条件
// 参数:
//   - field: 字段名
//   - start: 开始值
//   - end: 结束值
//
// 返回:
//   - Condition: 查询条件
func NewNotBetweenCondition(field string, start, end interface{}) Condition {
	return NewCondition(field, OP_NOT_BETWEEN, []interface{}{start, end})
}

// NewIsNullCondition 创建IS NULL条件
// 参数:
//   - field: 字段名
//
// 返回:
//   - Condition: 查询条件
func NewIsNullCondition(field string) Condition {
	return NewCondition(field, OP_IS_NULL, nil)
}

// NewIsNotNullCondition 创建IS NOT NULL条件
// 参数:
//   - field: 字段名
//
// 返回:
//   - Condition: 查询条件
func NewIsNotNullCondition(field string) Condition {
	return NewCondition(field, OP_IS_NOT_NULL, nil)
}

// NewDateBetweenCondition 创建日期范围条件
// 参数:
//   - field: 字段名
//   - startDate: 开始日期
//   - endDate: 结束日期
//
// 返回:
//   - Condition: 查询条件
func NewDateBetweenCondition(field string, startDate, endDate time.Time) Condition {
	return NewBetweenCondition(field, startDate, endDate)
}

// NewDateEqualCondition 创建日期相等条件
// 参数:
//   - field: 字段名
//   - date: 日期
//
// 返回:
//   - []Condition: 查询条件列表
func NewDateEqualCondition(field string, date time.Time) []Condition {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour).Add(-time.Nanosecond)
	return []Condition{
		NewGreaterThanEqualCondition(field, startOfDay),
		NewLessThanEqualCondition(field, endOfDay),
	}
}

// String 获取条件字符串表示
// 返回:
//   - string: 条件字符串
func (c Condition) String() string {
	switch c.Operator {
	case OP_IS_NULL, OP_IS_NOT_NULL:
		return fmt.Sprintf("%s %s", c.Field, c.Operator)
	case OP_BETWEEN, OP_NOT_BETWEEN:
		if values, ok := c.Value.([]interface{}); ok && len(values) == 2 {
			return fmt.Sprintf("%s %s %v AND %v", c.Field, c.Operator, values[0], values[1])
		}
		return fmt.Sprintf("%s %s (invalid values)", c.Field, c.Operator)
	case OP_IN, OP_NOT_IN:
		return fmt.Sprintf("%s %s (%v)", c.Field, c.Operator, c.Value)
	default:
		return fmt.Sprintf("%s %s %v", c.Field, c.Operator, c.Value)
	}
}

// --- 表达式条件 ---

// ExprCondition 代表一个原始 SQL 表达式条件
type ExprCondition struct {
	Expr string        // SQL 表达式字符串，例如 "quantity > allocated_qty + frozen_qty" 或 "LOWER(name) = ?"
	Args []interface{} // 表达式中 '?' 占位符对应的参数列表
}

// --- 为 ExprCondition 实现 Apply 方法 ---
func (c ExprCondition) Apply(db *gorm.DB) *gorm.DB {
	return db.Where(c.Expr, c.Args...)
}

// NewExprCondition 创建一个新的表达式条件
// 参数:
//   - expr: SQL 表达式字符串
//   - args: 可选的表达式参数
//
// 返回:
//   - ExprCondition: 表达式条件
func NewExprCondition(expr string, args ...interface{}) ExprCondition {
	return ExprCondition{
		Expr: expr,
		Args: args,
	}
}

// --- 结束：表达式条件 ---
