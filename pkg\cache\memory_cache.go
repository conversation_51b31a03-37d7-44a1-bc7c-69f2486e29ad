package cache

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"backend/pkg/errors"
)

// 缓存相关常量
const (
	CACHE_NEVER_EXPIRE   = time.Duration(-1) // 永不过期
	CACHE_DEFAULT_EXPIRE = time.Hour * 24    // 默认过期时间（24小时）
	CACHE_PREFIX_USER    = "user:"           // 用户缓存前缀
	CACHE_PREFIX_ROLE    = "role:"           // 角色缓存前缀
	CACHE_PREFIX_MENU    = "menu:"           // 菜单缓存前缀
	CACHE_PREFIX_DICT    = "dict:"           // 字典缓存前缀
	CACHE_PREFIX_CONFIG  = "config:"         // 配置缓存前缀
	CACHE_PREFIX_TOKEN   = "token:"          // 令牌缓存前缀
	CACHE_PREFIX_API     = "api:"            // API缓存前缀
	CACHE_PREFIX_PERM    = "perm:"           // 权限缓存前缀
)

// 缓存错误代码
const (
	ERROR_EMPTY_KEY     = "EMPTY_KEY"        // 空键错误
	ERROR_TYPE_ERROR    = "VALUE_TYPE_ERROR" // 类型错误
	ERROR_KEY_NOT_FOUND = "KEY_NOT_FOUND"    // 键不存在
	ERROR_CACHE_CLOSED  = "CACHE_CLOSED"     // 缓存已关闭
)

// CacheItem 缓存项结构
type CacheItem struct {
	Value      interface{} `json:"value"`      // 缓存值
	Expiration int64       `json:"expiration"` // 过期时间戳（毫秒）
	Created    int64       `json:"created"`    // 创建时间戳（毫秒）
}

// IsExpired 判断缓存项是否过期
func (item CacheItem) IsExpired() bool {
	if item.Expiration == 0 {
		return false
	}
	return time.Now().UnixMilli() > item.Expiration
}

// TimeToLive 获取缓存项的剩余生存时间
func (item CacheItem) TimeToLive() time.Duration {
	if item.Expiration == 0 {
		return CACHE_NEVER_EXPIRE
	}
	ttl := time.Duration(item.Expiration-time.Now().UnixMilli()) * time.Millisecond
	if ttl < 0 {
		return 0
	}
	return ttl
}

// Cache 缓存接口
type Cache interface {
	// 基本操作
	Set(key string, value interface{}, duration time.Duration) error
	Get(key string) (interface{}, bool)
	Delete(key string) bool
	Clear() error

	// 批量操作
	SetMany(items map[string]interface{}, duration time.Duration) error
	GetMany(keys []string) map[string]interface{}
	DeleteMany(keys []string) int

	// 特殊操作
	Increment(key string, value int64) (int64, error)
	Decrement(key string, value int64) (int64, error)
	GetOrSet(key string, getter func() (interface{}, error), duration time.Duration) (interface{}, error)

	// 其他操作
	Keys(pattern string) []string
	HasKey(key string) bool
	GetTTL(key string) time.Duration
	SetTTL(key string, duration time.Duration) bool
	Size() int
	Close() error
}

// MemoryCache 内存缓存实现
type MemoryCache struct {
	items             map[string]CacheItem // 缓存项
	mu                sync.RWMutex         // 读写锁
	janitor           *janitor             // 清理器
	defaultExpiration time.Duration        // 默认过期时间
	isClosed          bool                 // 是否已关闭
}

// Janitor 缓存清理器
type janitor struct {
	interval time.Duration // 清理间隔
	stop     chan bool     // 停止信号
}

// NewMemoryCache 创建新的内存缓存
// 参数:
//   - defaultExpiration: 默认过期时间，如果为CACHE_NEVER_EXPIRE则永不过期
//   - cleanInterval: 清理间隔，如果小于等于0则不清理
//
// 返回:
//   - *MemoryCache: 内存缓存实例
func NewMemoryCache(defaultExpiration, cleanInterval time.Duration) *MemoryCache {
	cache := &MemoryCache{
		items:             make(map[string]CacheItem),
		defaultExpiration: defaultExpiration,
		isClosed:          false,
	}

	// 如果清理间隔大于0，启动清理器
	if cleanInterval > 0 {
		cache.janitor = &janitor{
			interval: cleanInterval,
			stop:     make(chan bool),
		}
		go cache.janitorRun()
	}

	return cache
}

// janitorRun 运行清理器
func (c *MemoryCache) janitorRun() {
	ticker := time.NewTicker(c.janitor.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.deleteExpired()
		case <-c.janitor.stop:
			return
		}
	}
}

// StopJanitor 停止清理器
func (c *MemoryCache) StopJanitor() {
	if c.janitor != nil {
		c.janitor.stop <- true
		close(c.janitor.stop)
	}
}

// deleteExpired 删除过期项
func (c *MemoryCache) deleteExpired() {
	now := time.Now().UnixMilli()
	c.mu.Lock()
	defer c.mu.Unlock()

	for k, v := range c.items {
		if v.Expiration > 0 && now > v.Expiration {
			delete(c.items, k)
		}
	}
}

// Set 设置缓存
// 参数:
//   - key: 缓存键
//   - value: 缓存值
//   - duration: 过期时间，如果为CACHE_NEVER_EXPIRE则永不过期，如果为0则使用默认过期时间
//
// 返回:
//   - error: 错误信息
func (c *MemoryCache) Set(key string, value interface{}, duration time.Duration) error {
	if key == "" {
		return errors.NewParamError(errors.CODE_PARAMS_MISSING, "缓存键不能为空").
			WithMetadata("error_code", ERROR_EMPTY_KEY)
	}

	if c.isClosed {
		return errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "缓存已关闭").
			WithMetadata("error_code", ERROR_CACHE_CLOSED)
	}

	var expiration int64
	if duration == CACHE_NEVER_EXPIRE {
		expiration = 0
	} else if duration > 0 {
		expiration = time.Now().Add(duration).UnixMilli()
	} else {
		expiration = time.Now().Add(c.defaultExpiration).UnixMilli()
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	c.items[key] = CacheItem{
		Value:      value,
		Expiration: expiration,
		Created:    time.Now().UnixMilli(),
	}

	return nil
}

// Get 获取缓存
// 参数:
//   - key: 缓存键
//
// 返回:
//   - interface{}: 缓存值
//   - bool: 是否找到
func (c *MemoryCache) Get(key string) (interface{}, bool) {
	if c.isClosed {
		return nil, false
	}

	c.mu.RLock()
	item, found := c.items[key]
	c.mu.RUnlock()

	if !found {
		return nil, false
	}

	if item.IsExpired() {
		// 如果已过期，异步删除
		go c.Delete(key)
		return nil, false
	}

	return item.Value, true
}

// Delete 删除缓存
// 参数:
//   - key: 缓存键
//
// 返回:
//   - bool: 是否删除成功
func (c *MemoryCache) Delete(key string) bool {
	if c.isClosed {
		return false
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	if _, found := c.items[key]; found {
		delete(c.items, key)
		return true
	}

	return false
}

// Clear 清空缓存
// 返回:
//   - error: 错误信息
func (c *MemoryCache) Clear() error {
	if c.isClosed {
		return errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "缓存已关闭").
			WithMetadata("error_code", ERROR_CACHE_CLOSED)
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	c.items = make(map[string]CacheItem)
	return nil
}

// SetMany 批量设置缓存
// 参数:
//   - items: 键值对
//   - duration: 过期时间
//
// 返回:
//   - error: 错误信息
func (c *MemoryCache) SetMany(items map[string]interface{}, duration time.Duration) error {
	if c.isClosed {
		return errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "缓存已关闭").
			WithMetadata("error_code", ERROR_CACHE_CLOSED)
	}

	for k, v := range items {
		if err := c.Set(k, v, duration); err != nil {
			return err
		}
	}
	return nil
}

// GetMany 批量获取缓存
// 参数:
//   - keys: 键列表
//
// 返回:
//   - map[string]interface{}: 键值对
func (c *MemoryCache) GetMany(keys []string) map[string]interface{} {
	result := make(map[string]interface{})
	if c.isClosed {
		return result
	}

	for _, key := range keys {
		if val, found := c.Get(key); found {
			result[key] = val
		}
	}
	return result
}

// DeleteMany 批量删除缓存
// 参数:
//   - keys: 键列表
//
// 返回:
//   - int: 删除数量
func (c *MemoryCache) DeleteMany(keys []string) int {
	if c.isClosed {
		return 0
	}

	count := 0
	for _, key := range keys {
		if c.Delete(key) {
			count++
		}
	}
	return count
}

// Increment 增加缓存值
// 参数:
//   - key: 缓存键
//   - value: 增加值
//
// 返回:
//   - int64: 增加后的值
//   - error: 错误信息
func (c *MemoryCache) Increment(key string, value int64) (int64, error) {
	if c.isClosed {
		return 0, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "缓存已关闭").
			WithMetadata("error_code", ERROR_CACHE_CLOSED)
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	item, found := c.items[key]
	if !found {
		// 键不存在，默认为0然后增加
		c.items[key] = CacheItem{
			Value:      value,
			Expiration: 0,
			Created:    time.Now().UnixMilli(),
		}
		return value, nil
	}

	if item.IsExpired() {
		// 键已过期，删除并创建新值
		delete(c.items, key)
		c.items[key] = CacheItem{
			Value:      value,
			Expiration: 0,
			Created:    time.Now().UnixMilli(),
		}
		return value, nil
	}

	// 尝试将值转换为int64并增加
	switch val := item.Value.(type) {
	case int:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case int8:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case int16:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case int32:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case int64:
		newVal := val + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case uint:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case uint8:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case uint16:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case uint32:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case uint64:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case float32:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case float64:
		newVal := int64(val) + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	case string:
		// 尝试将字符串转换为int64
		intVal, err := ConvertStringToInt64(val)
		if err != nil {
			return 0, errors.NewParamError(errors.CODE_PARAMS_TYPE_ERROR,
				fmt.Sprintf("无法将字符串值转为整数: %s", val)).
				WithMetadata("error_code", ERROR_TYPE_ERROR)
		}
		newVal := intVal + value
		item.Value = newVal
		c.items[key] = item
		return newVal, nil
	default:
		return 0, errors.NewParamError(errors.CODE_PARAMS_TYPE_ERROR,
			"缓存值不是数字类型，无法增加").
			WithMetadata("error_code", ERROR_TYPE_ERROR)
	}
}

// Decrement 减少缓存值
// 参数:
//   - key: 缓存键
//   - value: 减少值
//
// 返回:
//   - int64: 减少后的值
//   - error: 错误信息
func (c *MemoryCache) Decrement(key string, value int64) (int64, error) {
	return c.Increment(key, -value)
}

// GetOrSet 获取缓存，如果不存在则设置
// 参数:
//   - key: 缓存键
//   - getter: 获取函数
//   - duration: 过期时间
//
// 返回:
//   - interface{}: 缓存值
//   - error: 错误信息
func (c *MemoryCache) GetOrSet(key string, getter func() (interface{}, error), duration time.Duration) (interface{}, error) {
	if c.isClosed {
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "缓存已关闭").
			WithMetadata("error_code", ERROR_CACHE_CLOSED)
	}

	// 先尝试获取
	if val, found := c.Get(key); found {
		return val, nil
	}

	// 缓存不存在，调用getter获取值
	value, err := getter()
	if err != nil {
		return nil, errors.WrapError(err, errors.CODE_SYSTEM_INTERNAL, "获取缓存数据失败")
	}

	// 设置缓存
	if err := c.Set(key, value, duration); err != nil {
		return nil, err
	}

	return value, nil
}

// Keys 获取匹配模式的所有键
// 参数:
//   - pattern: 模式，支持前缀匹配，* 表示任意字符
//
// 返回:
//   - []string: 键列表
func (c *MemoryCache) Keys(pattern string) []string {
	if c.isClosed {
		return []string{}
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	var keys []string
	if pattern == "" || pattern == "*" {
		// 获取所有键
		keys = make([]string, 0, len(c.items))
		for k, v := range c.items {
			if !v.IsExpired() {
				keys = append(keys, k)
			}
		}
	} else if strings.HasSuffix(pattern, "*") {
		// 前缀匹配
		prefix := strings.TrimSuffix(pattern, "*")
		keys = make([]string, 0)
		for k, v := range c.items {
			if !v.IsExpired() && strings.HasPrefix(k, prefix) {
				keys = append(keys, k)
			}
		}
	} else {
		// 精确匹配
		keys = make([]string, 0)
		for k, v := range c.items {
			if !v.IsExpired() && HasPrefix(k, pattern) {
				keys = append(keys, k)
			}
		}
	}

	return keys
}

// HasKey 检查键是否存在
// 参数:
//   - key: 缓存键
//
// 返回:
//   - bool: 是否存在
func (c *MemoryCache) HasKey(key string) bool {
	if c.isClosed {
		return false
	}
	_, exists := c.Get(key)
	return exists
}

// GetTTL 获取缓存剩余生存时间
// 参数:
//   - key: 缓存键
//
// 返回:
//   - time.Duration: 剩余时间，-2表示键不存在，-1表示永不过期
func (c *MemoryCache) GetTTL(key string) time.Duration {
	if c.isClosed {
		return -2
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	item, found := c.items[key]
	if !found {
		return -2 // 键不存在
	}

	if item.Expiration == 0 {
		return -1 // 永不过期
	}

	ttl := time.Duration(item.Expiration-time.Now().UnixMilli()) * time.Millisecond
	if ttl < 0 {
		// 已过期
		go c.Delete(key)
		return -2
	}

	return ttl
}

// SetTTL 设置缓存的过期时间
// 参数:
//   - key: 缓存键
//   - duration: 过期时间
//
// 返回:
//   - bool: 是否成功
func (c *MemoryCache) SetTTL(key string, duration time.Duration) bool {
	if c.isClosed {
		return false
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	item, found := c.items[key]
	if !found {
		return false
	}

	if item.IsExpired() {
		delete(c.items, key)
		return false
	}

	if duration == CACHE_NEVER_EXPIRE {
		item.Expiration = 0
	} else if duration > 0 {
		item.Expiration = time.Now().Add(duration).UnixMilli()
	} else {
		return false
	}

	c.items[key] = item
	return true
}

// Size 获取缓存大小
// 返回:
//   - int: 缓存项数量
func (c *MemoryCache) Size() int {
	if c.isClosed {
		return 0
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	return len(c.items)
}

// Close 关闭缓存
// 返回:
//   - error: 错误信息
func (c *MemoryCache) Close() error {
	if c.isClosed {
		return nil
	}

	c.StopJanitor()
	c.Clear()
	c.isClosed = true
	return nil
}

// MarshalJSON 序列化为JSON
// 返回:
//   - []byte: JSON数据
//   - error: 错误信息
func (c *MemoryCache) MarshalJSON() ([]byte, error) {
	if c.isClosed {
		return []byte("{}"), nil
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	items := make(map[string]CacheItem)
	now := time.Now().UnixMilli()

	for k, v := range c.items {
		if v.Expiration == 0 || now < v.Expiration {
			items[k] = v
		}
	}

	return json.Marshal(items)
}

// ConvertStringToInt64 将字符串转换为int64
// 参数:
//   - s: 字符串
//
// 返回:
//   - int64: 转换后的int64值
//   - error: 错误信息
func ConvertStringToInt64(s string) (int64, error) {
	var i int64
	_, err := fmt.Sscanf(s, "%d", &i)
	if err != nil {
		return 0, errors.NewParamError(errors.CODE_PARAMS_TYPE_ERROR,
			fmt.Sprintf("无法将字符串 '%s' 转换为整数", s))
	}
	return i, nil
}

// HasPrefix 检查字符串是否有指定前缀
// 参数:
//   - s: 字符串
//   - prefix: 前缀
//
// 返回:
//   - bool: 是否匹配
func HasPrefix(s, prefix string) bool {
	if prefix == "" || prefix == "*" {
		return true
	}

	// 如果前缀以*结尾，只判断前面部分
	if strings.HasSuffix(prefix, "*") {
		return strings.HasPrefix(s, strings.TrimSuffix(prefix, "*"))
	}

	// 简单前缀匹配
	return strings.HasPrefix(s, prefix)
}

// DefaultCache 默认缓存实例
var DefaultCache = NewMemoryCache(CACHE_DEFAULT_EXPIRE, time.Minute*5)
