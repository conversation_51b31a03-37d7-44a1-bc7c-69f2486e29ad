package dto

import (
	"backend/pkg/response"
	"time"
)

// WmsShipmentCreateReq 创建发运单请求
type WmsShipmentCreateReq struct {
	PickingTaskID     uint     `json:"pickingTaskId" validate:"required"`
	NotificationID    uint     `json:"notificationId" validate:"required"`
	CarrierID         *uint    `json:"carrierId"`
	CarrierName       *string  `json:"carrierName" validate:"omitempty,max=100"`
	TrackingNo        *string  `json:"trackingNo" validate:"omitempty,max=100"`
	ShippingMethod    *string  `json:"shippingMethod" validate:"omitempty,max=50"`
	ShipmentDate      *string  `json:"shipmentDate"`
	EstimatedDelivery *string  `json:"estimatedDelivery"`
	PackageCount      int      `json:"packageCount" validate:"min=1"`
	TotalWeight       *float64 `json:"totalWeight" validate:"omitempty,gt=0"`
	TotalVolume       *float64 `json:"totalVolume" validate:"omitempty,gt=0"`
	ShippingCost      *float64 `json:"shippingCost" validate:"omitempty,gte=0"`
	InsuranceAmount   *float64 `json:"insuranceAmount" validate:"omitempty,gte=0"`
	ConsigneeName     string   `json:"consigneeName" validate:"required,max=100"`
	ConsigneePhone    *string  `json:"consigneePhone" validate:"omitempty,max=50"`
	ConsigneeAddress  *string  `json:"consigneeAddress" validate:"omitempty,max=500"`
	Remark            *string  `json:"remark"`
}

// WmsShipmentUpdateReq 更新发运单请求
type WmsShipmentUpdateReq struct {
	ID                uint     `json:"id" validate:"required"`
	CarrierID         *uint    `json:"carrierId"`
	CarrierName       *string  `json:"carrierName" validate:"omitempty,max=100"`
	TrackingNo        *string  `json:"trackingNo" validate:"omitempty,max=100"`
	ShippingMethod    *string  `json:"shippingMethod" validate:"omitempty,max=50"`
	ShipmentDate      *string  `json:"shipmentDate"`
	EstimatedDelivery *string  `json:"estimatedDelivery"`
	ActualDelivery    *string  `json:"actualDelivery"`
	PackageCount      int      `json:"packageCount" validate:"min=1"`
	TotalWeight       *float64 `json:"totalWeight" validate:"omitempty,gt=0"`
	TotalVolume       *float64 `json:"totalVolume" validate:"omitempty,gt=0"`
	ShippingCost      *float64 `json:"shippingCost" validate:"omitempty,gte=0"`
	InsuranceAmount   *float64 `json:"insuranceAmount" validate:"omitempty,gte=0"`
	ConsigneeName     string   `json:"consigneeName" validate:"required,max=100"`
	ConsigneePhone    *string  `json:"consigneePhone" validate:"omitempty,max=50"`
	ConsigneeAddress  *string  `json:"consigneeAddress" validate:"omitempty,max=500"`
	Remark            *string  `json:"remark"`
}

// WmsShipmentQueryReq 查询发运单请求
type WmsShipmentQueryReq struct {
	PageQuery         response.PageQuery `json:"-"` // 分页查询参数，由Controller填充
	ShipmentNo        *string            `json:"shipmentNo"`
	PickingTaskID     *uint              `json:"pickingTaskId"`
	NotificationID    *uint              `json:"notificationId"`
	CarrierID         *uint              `json:"carrierId"`
	TrackingNo        *string            `json:"trackingNo"`
	ShippingMethod    *string            `json:"shippingMethod"`
	Status            *string            `json:"status"`
	ConsigneeName     *string            `json:"consigneeName"`
	ShipmentDateStart *time.Time         `json:"shipmentDateStart"`
	ShipmentDateEnd   *time.Time         `json:"shipmentDateEnd"`
	CreatedAtStart    *time.Time         `json:"createdAtStart"`
	CreatedAtEnd      *time.Time         `json:"createdAtEnd"`
}

// WmsShipmentPackReq 打包确认请求
type WmsShipmentPackReq struct {
	ID           uint     `json:"id" validate:"required"`
	PackageCount int      `json:"packageCount" validate:"min=1"`
	TotalWeight  *float64 `json:"totalWeight" validate:"omitempty,gt=0"`
	TotalVolume  *float64 `json:"totalVolume" validate:"omitempty,gt=0"`
	Remark       *string  `json:"remark"`
}

// WmsShipmentShipReq 发运确认请求
type WmsShipmentShipReq struct {
	ID             uint     `json:"id" validate:"required"`
	CarrierID      *uint    `json:"carrierId"`
	TrackingNo     *string  `json:"trackingNo" validate:"omitempty,max=100"`
	ShippingMethod *string  `json:"shippingMethod" validate:"omitempty,max=50"`
	ShipmentDate   *string  `json:"shipmentDate"`
	ShippingCost   *float64 `json:"shippingCost" validate:"omitempty,gte=0"`
	Remark         *string  `json:"remark"`
}

// WmsShipmentTrackReq 更新运输状态请求
type WmsShipmentTrackReq struct {
	ID             uint    `json:"id" validate:"required"`
	Status         string  `json:"status" validate:"required,oneof=IN_TRANSIT DELIVERED RETURNED"`
	TrackingInfo   *string `json:"trackingInfo"`
	ActualDelivery *string `json:"actualDelivery"`
	Remark         *string `json:"remark"`
}

// WmsShipmentDeliverReq 签收确认请求
type WmsShipmentDeliverReq struct {
	ID             uint    `json:"id" validate:"required"`
	ActualDelivery *string `json:"actualDelivery"`
	ReceiverName   *string `json:"receiverName" validate:"omitempty,max=100"`
	ReceiverPhone  *string `json:"receiverPhone" validate:"omitempty,max=50"`
	DeliveryProof  *string `json:"deliveryProof"`
	Remark         *string `json:"remark"`
}

// WmsShipmentBatchCreateReq 批量创建发运单请求
type WmsShipmentBatchCreateReq struct {
	Shipments []WmsShipmentCreateReq `json:"shipments" validate:"required,min=1,max=50,dive"`
}

// WmsShipmentBatchShipReq 批量发运确认请求
type WmsShipmentBatchShipReq struct {
	IDs            []uint  `json:"ids" validate:"required,min=1,max=100"`
	CarrierID      *uint   `json:"carrierId"`
	ShippingMethod *string `json:"shippingMethod"`
	ShipmentDate   *string `json:"shipmentDate"`
	Remark         *string `json:"remark"`
}

// WmsShipmentCalculateCostReq 运费计算请求
type WmsShipmentCalculateCostReq struct {
	ID             uint     `json:"id" validate:"required"`
	CarrierID      uint     `json:"carrierId" validate:"required"`
	ShippingMethod string   `json:"shippingMethod" validate:"required"`
	TotalWeight    float64  `json:"totalWeight" validate:"gt=0"`
	TotalVolume    float64  `json:"totalVolume" validate:"gt=0"`
	Distance       *float64 `json:"distance"`
}

// WmsShipmentStatsReq 发运单统计请求
type WmsShipmentStatsReq struct {
	DateStart      *time.Time `json:"dateStart"`
	DateEnd        *time.Time `json:"dateEnd"`
	GroupBy        string     `json:"groupBy" validate:"oneof=day week month status carrier method"`
	CarrierID      *uint      `json:"carrierId"`
	ShippingMethod *string    `json:"shippingMethod"`
}

// WmsShipmentExportReq 发运单导出请求
type WmsShipmentExportReq struct {
	WmsShipmentQueryReq
	ExportFormat string   `json:"exportFormat" validate:"required,oneof=excel pdf"`
	ExportFields []string `json:"exportFields"`
}

// WmsShipmentLabelPrintReq 发运标签打印请求
type WmsShipmentLabelPrintReq struct {
	IDs        []uint `json:"ids" validate:"required,min=1,max=100"`
	LabelType  string `json:"labelType" validate:"required,oneof=SHIPPING ADDRESS BARCODE"`
	PrintCount int    `json:"printCount" validate:"min=1,max=10"`
}

// WmsCarrierQueryReq 承运商查询请求
type WmsCarrierQueryReq struct {
	PageQuery      response.PageQuery `json:"-"` // 分页查询参数，由Controller填充
	CarrierName    *string            `json:"carrierName"`
	CarrierCode    *string            `json:"carrierCode"`
	Status         *string            `json:"status"`
	ShippingMethod *string            `json:"shippingMethod"`
}
