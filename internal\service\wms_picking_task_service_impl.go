package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsPickingTaskService 定义拣货任务服务接口
type WmsPickingTaskService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsPickingTaskCreateReq) (*vo.WmsPickingTaskVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsPickingTaskUpdateReq) (*vo.WmsPickingTaskVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsPickingTaskVO, error)
	GetPage(ctx context.Context, req *dto.WmsPickingTaskQueryReq) (*vo.PageResult[vo.WmsPickingTaskListVO], error)

	// 任务管理
	AssignTask(ctx context.Context, req *dto.WmsPickingTaskAssignReq) error
	BatchAssignTask(ctx context.Context, req *dto.WmsPickingTaskBatchAssignReq) error
	StartTask(ctx context.Context, req *dto.WmsPickingTaskStartReq) error
	CompleteTask(ctx context.Context, req *dto.WmsPickingTaskCompleteReq) error
	CancelTask(ctx context.Context, req *dto.WmsPickingTaskCancelReq) error

	// 拣货执行
	ExecutePicking(ctx context.Context, req *dto.WmsPickingExecuteReq) error
	BatchExecutePicking(ctx context.Context, req *dto.WmsPickingBatchExecuteReq) error
	HandleException(ctx context.Context, req *dto.WmsPickingExceptionReq) error

	// 波次管理
	CreateWave(ctx context.Context, req *dto.WmsPickingWaveCreateReq) (*vo.WmsPickingWaveVO, error)
	ReleaseWave(ctx context.Context, req *dto.WmsPickingWaveReleaseReq) error

	// 路径优化
	OptimizePickingPath(ctx context.Context, req *dto.WmsPickingTaskOptimizeReq) error
	BatchOptimizePickingPath(ctx context.Context, req *dto.WmsPickingTaskBatchOptimizeReq) error

	// 统计分析
	GetStats(ctx context.Context, req *dto.WmsPickingTaskStatsReq) (*vo.WmsPickingTaskStatsVO, error)

	// 导出
	ExportToExcel(ctx context.Context, req *dto.WmsPickingTaskExportReq) ([]byte, error)

	// 业务查询
	GetByTaskNo(ctx context.Context, taskNo string) (*vo.WmsPickingTaskVO, error)
	GetByNotificationID(ctx context.Context, notificationID uint) ([]*vo.WmsPickingTaskVO, error)
	GetPendingAssignment(ctx context.Context) ([]*vo.WmsPickingTaskListVO, error)
	GetInProgressTasks(ctx context.Context, userID *uint) ([]*vo.WmsPickingTaskListVO, error)
	GetOverdueTasks(ctx context.Context) ([]*vo.WmsPickingTaskListVO, error)

	// Mobile端接口
	GetMobileTaskList(ctx context.Context, req *dto.WmsMobilePickingTaskQueryReq) ([]*vo.WmsMobilePickingTaskVO, error)
	MobileExecutePicking(ctx context.Context, req *dto.WmsMobilePickingExecuteReq) error
	MobileConfirmTask(ctx context.Context, req *dto.WmsMobilePickingConfirmReq) error
}

// wmsPickingTaskServiceImpl 拣货任务服务实现
type wmsPickingTaskServiceImpl struct {
	BaseServiceImpl
}

// NewWmsPickingTaskService 创建拣货任务服务
func NewWmsPickingTaskService(sm *ServiceManager) WmsPickingTaskService {
	return &wmsPickingTaskServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建拣货任务
func (s *wmsPickingTaskServiceImpl) Create(ctx context.Context, req *dto.WmsPickingTaskCreateReq) (*vo.WmsPickingTaskVO, error) {
	var task *entity.WmsPickingTask
	var voResult *vo.WmsPickingTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		// 验证出库通知单是否存在且状态正确
		notificationRepo := txRepoMgr.GetWmsOutboundNotificationRepository()
		notification, err := notificationRepo.FindByID(ctx, req.NotificationID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
		}

		if notification.Status != string(entity.OutboundStatusAllocated) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已分配状态的通知单才能创建拣货任务")
		}

		// 生成拣货任务号
		taskNo, err := s.generateTaskNo(ctx, req)
		if err != nil {
			return err
		}

		task = &entity.WmsPickingTask{}
		copier.Copy(task, req)
		task.TaskNo = taskNo
		task.Status = string(entity.PickingTaskStatusPending) // 新增时默认为待分配状态

		// 强制从上下文获取账套ID - 账套强绑定
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空")
		}
		task.AccountBookID = uint(accountBookID)

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}
		task.CreatedBy = uint(userID)

		// 创建拣货任务
		if err := repo.Create(ctx, task); err != nil {
			if apperrors.IsDuplicateKeyError(err) {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务号已存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建拣货任务失败").WithCause(err)
		}

		// 生成拣货任务明细
		if err := s.generateTaskDetails(ctx, txRepoMgr, task, notification); err != nil {
			return fmt.Errorf("生成拣货任务明细失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	voResult = &vo.WmsPickingTaskVO{}
	copier.Copy(voResult, task)
	return voResult, nil
}

// Update 更新拣货任务
func (s *wmsPickingTaskServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsPickingTaskUpdateReq) (*vo.WmsPickingTaskVO, error) {
	var voResult *vo.WmsPickingTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		// 获取当前记录
		current, err := repo.FindByID(ctx, id)
		if err != nil {
			return fmt.Errorf("查询记录失败: %w", err)
		}

		// 检查状态是否允许修改
		if current.Status != string(entity.PickingTaskStatusPending) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待分配状态的任务才能修改")
		}

		// 更新字段
		updates := make(map[string]interface{})
		if req.PickingStrategy != "" {
			updates["picking_strategy"] = req.PickingStrategy
		}
		if req.WaveNo != nil {
			updates["wave_no"] = *req.WaveNo
		}
		if req.Priority != 0 {
			updates["priority"] = req.Priority
		}
		if req.AssignedUserID != nil {
			updates["assigned_user_id"] = *req.AssignedUserID
		}
		if req.Remark != nil {
			updates["remark"] = *req.Remark
		}

		if len(updates) > 0 {
			// 强制从上下文获取用户ID - 用户强绑定
			userID, err := s.GetUserIDFromContext(ctx)
			if err != nil {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
			}
			if userID == 0 {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
			}
			updates["updated_by"] = uint(userID)

			if err := repo.GetDB(ctx).Model(current).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新任务失败: %w", err)
			}
		}

		// 重新查询完整数据返回
		var result entity.WmsPickingTask
		if err := repo.GetDB(ctx).Preload("Details").First(&result, id).Error; err != nil {
			return fmt.Errorf("查询更新后数据失败: %w", err)
		}

		copier.Copy(&voResult, &result)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return voResult, nil
}

// Delete 删除拣货任务
func (s *wmsPickingTaskServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		task, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if task.Status != string(entity.PickingTaskStatusPending) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待分配状态的任务才能删除")
		}

		// 删除任务（GORM会自动处理明细表的级联删除）
		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除拣货任务失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取拣货任务
func (s *wmsPickingTaskServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsPickingTaskVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPickingTaskRepository()

	// 先获取基本信息
	task, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务失败").WithCause(err)
	}

	// 预加载明细
	db := repo.GetDB(ctx)
	if err := db.Preload("Details").Where("id = ?", id).First(task).Error; err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "加载任务明细失败").WithCause(err)
	}

	voResult := &vo.WmsPickingTaskVO{}
	if err := copier.Copy(voResult, task); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "转换数据失败").WithCause(err)
	}

	// 填充扩展信息
	if err := s.fillExtendedInfo(ctx, voResult); err != nil {
		return nil, err
	}

	return voResult, nil
}

// GetPage 获取拣货任务分页数据
func (s *wmsPickingTaskServiceImpl) GetPage(ctx context.Context, req *dto.WmsPickingTaskQueryReq) (*vo.PageResult[vo.WmsPickingTaskListVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPickingTaskRepository()

	// 调用Repository的分页查询
	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务分页数据失败").WithCause(err)
	}

	// 转换为VO
	voResult := &vo.PageResult[vo.WmsPickingTaskListVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsPickingTaskListVO, len(pageResult.List.([]*entity.WmsPickingTask))),
	}

	tasks := pageResult.List.([]*entity.WmsPickingTask)
	for i, task := range tasks {
		listVO := &vo.WmsPickingTaskListVO{}
		copier.Copy(listVO, task)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		voResult.List[i] = listVO
	}

	return voResult, nil
}

// ExecutePicking 执行拣货
func (s *wmsPickingTaskServiceImpl) ExecutePicking(ctx context.Context, req *dto.WmsPickingExecuteReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		detailRepo := txRepoMgr.GetWmsPickingTaskDetailRepository()

		// 检查明细是否存在
		detail, err := detailRepo.FindByID(ctx, req.TaskDetailID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货明细不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货明细失败").WithCause(err)
		}

		// 检查状态是否允许拣货
		if detail.Status != string(entity.PickingDetailStatusPending) &&
			detail.Status != string(entity.PickingDetailStatusInProgress) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待拣货或进行中状态的明细才能拣货")
		}

		// 验证拣货数量
		if req.PickedQty > detail.RequiredQty {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货数量不能超过要求数量")
		}

		// 更新拣货数量
		if err := detailRepo.UpdatePickedQty(ctx, req.TaskDetailID, req.PickedQty); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新拣货数量失败").WithCause(err)
		}

		// 更新拣货时间
		if err := detailRepo.UpdatePickedTime(ctx, req.TaskDetailID); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新拣货时间失败").WithCause(err)
		}

		// 更新状态
		newStatus := string(entity.PickingDetailStatusCompleted)
		if req.PickedQty < detail.RequiredQty {
			newStatus = string(entity.PickingDetailStatusInProgress)
		}

		if err := detailRepo.UpdateStatus(ctx, req.TaskDetailID, newStatus); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新明细状态失败").WithCause(err)
		}

		// 更新库存分配的拣货数量
		allocationService := s.GetServiceManager().GetWmsInventoryAllocationService()
		pickReq := &dto.WmsInventoryAllocationPickReq{
			ID:        detail.AllocationID,
			PickedQty: req.PickedQty,
		}

		if err := allocationService.PickConfirm(ctx, pickReq); err != nil {
			return fmt.Errorf("确认库存分配拣货失败: %w", err)
		}

		// 检查任务是否完成
		taskRepo := txRepoMgr.GetWmsPickingTaskRepository()
		if err := taskRepo.UpdateTaskProgress(ctx, detail.TaskID); err != nil {
			return fmt.Errorf("更新任务进度失败: %w", err)
		}

		return nil
	})
}

// BatchExecutePicking 批量执行拣货
func (s *wmsPickingTaskServiceImpl) BatchExecutePicking(ctx context.Context, req *dto.WmsPickingBatchExecuteReq) error {
	for _, executeReq := range req.Details {
		if err := s.ExecutePicking(ctx, &executeReq); err != nil {
			return fmt.Errorf("执行拣货明细ID %d 失败: %w", executeReq.TaskDetailID, err)
		}
	}
	return nil
}

// HandleException 处理拣货异常
func (s *wmsPickingTaskServiceImpl) HandleException(ctx context.Context, req *dto.WmsPickingExceptionReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		detailRepo := txRepoMgr.GetWmsPickingTaskDetailRepository()

		// 检查明细是否存在
		detail, err := detailRepo.FindByID(ctx, req.TaskDetailID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货明细不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货明细失败").WithCause(err)
		}

		// 记录异常信息
		updates := map[string]interface{}{
			"shortage_qty":     req.ShortageQty,
			"exception_type":   req.ExceptionType,
			"exception_reason": req.ExceptionDesc,
		}

		if err := detailRepo.GetDB(ctx).Model(detail).Updates(updates).Error; err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "记录异常信息失败").WithCause(err)
		}

		// TODO: 发送异常通知
		// TODO: 触发异常处理流程

		return nil
	})
}

// CreateWave 创建拣货波次
func (s *wmsPickingTaskServiceImpl) CreateWave(ctx context.Context, req *dto.WmsPickingWaveCreateReq) (*vo.WmsPickingWaveVO, error) {
	// TODO: 实现拣货波次创建逻辑
	result := &vo.WmsPickingWaveVO{}
	return result, nil
}

// ReleaseWave 释放拣货波次
func (s *wmsPickingTaskServiceImpl) ReleaseWave(ctx context.Context, req *dto.WmsPickingWaveReleaseReq) error {
	// TODO: 实现拣货波次释放逻辑
	return nil
}

// OptimizePickingPath 拣货路径优化
func (s *wmsPickingTaskServiceImpl) OptimizePickingPath(ctx context.Context, req *dto.WmsPickingTaskOptimizeReq) error {
	// TODO: 实现拣货路径优化逻辑
	return nil
}

// BatchOptimizePickingPath 批量拣货路径优化
func (s *wmsPickingTaskServiceImpl) BatchOptimizePickingPath(ctx context.Context, req *dto.WmsPickingTaskBatchOptimizeReq) error {
	// TODO: 实现批量拣货路径优化逻辑
	return nil
}

// GetStats 获取统计数据
func (s *wmsPickingTaskServiceImpl) GetStats(ctx context.Context, req *dto.WmsPickingTaskStatsReq) (*vo.WmsPickingTaskStatsVO, error) {
	// TODO: 实现统计分析逻辑
	result := &vo.WmsPickingTaskStatsVO{}
	return result, nil
}

// ExportToExcel 导出到Excel
func (s *wmsPickingTaskServiceImpl) ExportToExcel(ctx context.Context, req *dto.WmsPickingTaskExportReq) ([]byte, error) {
	// TODO: 实现Excel导出逻辑
	return []byte{}, nil
}

// GetMobileTaskList 获取移动端任务列表
func (s *wmsPickingTaskServiceImpl) GetMobileTaskList(ctx context.Context, req *dto.WmsMobilePickingTaskQueryReq) ([]*vo.WmsMobilePickingTaskVO, error) {
	// TODO: 实现移动端任务列表查询逻辑
	var results []*vo.WmsMobilePickingTaskVO
	return results, nil
}

// MobileExecutePicking 移动端执行拣货
func (s *wmsPickingTaskServiceImpl) MobileExecutePicking(ctx context.Context, req *dto.WmsMobilePickingExecuteReq) error {
	// TODO: 实现移动端拣货执行逻辑
	return nil
}

// MobileConfirmTask 移动端确认任务
func (s *wmsPickingTaskServiceImpl) MobileConfirmTask(ctx context.Context, req *dto.WmsMobilePickingConfirmReq) error {
	// TODO: 实现移动端任务确认逻辑
	return nil
}
