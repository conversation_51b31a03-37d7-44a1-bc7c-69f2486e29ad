package entity

import (
	"time"
)

// WmsPutawayTaskStatus 定义上架任务状态常量
type WmsPutawayTaskStatus string

const (
	PutawayTaskStatusPending    WmsPutawayTaskStatus = "PENDING"     // 待执行
	PutawayTaskStatusAssigned   WmsPutawayTaskStatus = "ASSIGNED"    // 已分配 (给特定用户/设备)
	PutawayTaskStatusInProgress WmsPutawayTaskStatus = "IN_PROGRESS" // 执行中
	PutawayTaskStatusCompleted  WmsPutawayTaskStatus = "COMPLETED"   // 已完成
	PutawayTaskStatusOnHold     WmsPutawayTaskStatus = "ON_HOLD"     // 异常挂起
	PutawayTaskStatusCancelled  WmsPutawayTaskStatus = "CANCELLED"   // 已取消
)

// WmsPutawayTask 对应数据库中的 wms_putaway_tasks 表
// 存储上架任务的主信息
type WmsPutawayTask struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	TaskNo            string         `gorm:"column:task_no;type:varchar(50);not null;uniqueIndex;comment:任务编号" json:"taskNo"`                        // 任务编号 (系统生成, 唯一)
	ReceivingRecordID uint           `gorm:"column:receiving_record_id;not null;index;comment:关联收货记录ID" json:"receivingRecordId"`                    // 关联的 wms_receiving_records ID
	AssignedToUserID  *uint          `gorm:"column:assigned_to_user_id;index;comment:负责人ID" json:"assignedToUserId"`                                 // 负责人ID (关联用户表, 可为空)
	Priority          int            `gorm:"column:priority;not null;default:5;comment:优先级" json:"priority"`                                         // 任务优先级 (数字越小越高)
	Status            string         `gorm:"column:status;type:wms_putaway_task_status;not null;default:'PENDING';index;comment:任务状态" json:"status"` // 任务状态 (使用数据库定义的 ENUM)
	Remark            *string        `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                                       // 备注
	CompletedAt       *time.Time     `gorm:"column:completed_at;comment:完成时间" json:"completedAt"`                                                    // 任务实际完成时间

	TaskDetails []WmsPutawayTaskDetail `gorm:"foreignKey:PutawayTaskID;references:ID" json:"taskDetails,omitempty"` // 关联的上架任务明细 (修正了 references)
}

// TableName 指定数据库表名方法
func (WmsPutawayTask) TableName() string {
	return "wms_putaway_task"
}
