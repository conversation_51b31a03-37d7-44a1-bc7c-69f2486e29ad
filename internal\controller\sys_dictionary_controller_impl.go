package controller

import (
	// For error formatting if needed
	"fmt"
	"strconv" // For parsing path parameters

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response" // 确保 response 包已导入

	// Assuming response package exists
	"github.com/kataras/iris/v12"
)

// DictionaryControllerImpl 数据字典控制器 (重命名)
type DictionaryControllerImpl struct { // <<< 重命名
	*BaseControllerImpl // 嵌入基础控制器实现
	dictService         service.DictionaryService
}

// NewDictionaryControllerImpl 创建数据字典控制器实例 (重命名)
func NewDictionaryControllerImpl(cm *ControllerManager) *DictionaryControllerImpl { // <<< 重命名并修改返回类型
	base := NewBaseController(cm)
	// 从 ServiceManager 获取 DictionaryService
	dictSvc := cm.GetServiceManager().GetDictionaryService() // Assumes GetDictionaryService exists in ServiceManager
	if dictSvc == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 DictionaryService 实例") // Or Panic
	}
	return &DictionaryControllerImpl{ // <<< 修改返回类型
		BaseControllerImpl: base,
		dictService:        dictSvc,
	}
}

// --- Dictionary Type Handlers ---

// CreateDictType 创建字典类型
// @Summary 创建字典类型
// @Description 创建一个新的字典类型
// @Tags Dictionary
// @Accept json
// @Produce json
// @Param type body dto.DictTypeCreateOrUpdateDTO true "字典类型创建信息" // <<< 修改 DTO
// @Success 200 {object} response.Response{data=vo.DictionaryTypeVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或编码已存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/types [post]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) CreateDictType(ctx iris.Context) { // <<< 修改接收者类型
	opName := "创建字典类型"
	var req dto.DictTypeCreateOrUpdateDTO // <<< 修改 DTO


	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_type")



	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 调用 Service 创建
	dictTypeVO, err := c.dictService.CreateDictType(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName) // Service层应返回包装好的错误
		return
	}
	c.Success(ctx, dictTypeVO)
}

// UpdateDictType 更新字典类型
// @Summary 更新字典类型
// @Description 根据ID更新字典类型信息
// @Tags Dictionary
// @Accept json
// @Produce json
// @Param id path uint true "字典类型ID"
// @Param type body dto.DictTypeCreateOrUpdateDTO true "字典类型更新信息" // <<< 修改 DTO
// @Success 200 {object} response.Response{data=vo.DictionaryTypeVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "字典类型未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/types/{id} [put]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) UpdateDictType(ctx iris.Context) { // <<< 修改接收者类型
	opName := "更新字典类型"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_type")



	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的字典类型ID参数").WithCause(err), opName)
		return
	}

	var req dto.DictTypeCreateOrUpdateDTO // <<< 修改 DTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 调用 Service 更新
	dictTypeVO, err := c.dictService.UpdateDictType(ctx.Request().Context(), id, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, dictTypeVO)
}

// DeleteDictType 删除字典类型
// @Summary 删除字典类型
// @Description 根据ID删除字典类型 (如果类型下有字典项则无法删除)
// @Tags Dictionary
// @Produce json
// @Param id path uint true "字典类型ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效或无法删除(存在字典项)"
// @Failure 404 {object} response.Response "字典类型未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/types/{id} [delete]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) DeleteDictType(ctx iris.Context) { // <<< 修改接收者类型
	opName := "删除字典类型"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_type")


	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的字典类型ID参数").WithCause(err), opName)
		return
	}

	// 调用 Service 删除
	err = c.dictService.DeleteDictType(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetDictTypeByID 获取字典类型详情
// @Summary 获取字典类型详情
// @Description 根据ID获取字典类型详细信息
// @Tags Dictionary
// @Produce json
// @Param id path uint true "字典类型ID"
// @Success 200 {object} response.Response{data=vo.DictionaryTypeVO} "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "字典类型未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/types/{id} [get]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) GetDictTypeByID(ctx iris.Context) { // <<< 修改接收者类型
	opName := "获取字典类型详情"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_type")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的字典类型ID参数").WithCause(err), opName)
		return
	}

	// 调用 Service 查询
	dictTypeVO, err := c.dictService.GetDictTypeByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, dictTypeVO)
}

// PageDictTypes 分页查询字典类型
// @Summary 分页查询字典类型
// @Description 获取字典类型列表，支持分页和按编码/名称/状态过滤
// @Tags Dictionary
// @Produce json
// @Param query query dto.DictTypePageQueryDTO false "查询参数" // <<< 修改 DTO
// @Success 200 {object} response.Response{data=vo.PageDictTypeVO} "成功响应"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/types/page [get]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) PageDictTypes(ctx iris.Context) { // <<< 修改接收者类型
	opName := "分页查询字典类型"
	var query dto.DictTypePageQueryDTO // <<< 修改 DTO

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_type")


	if err := ctx.ReadQuery(&query); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析查询参数失败").WithCause(err), opName)
		return
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)
	query.PageQuery.PageNum = pageQueryFromBuild.PageNum
	query.PageQuery.PageSize = pageQueryFromBuild.PageSize
	query.PageQuery.Sort = pageQueryFromBuild.Sort

	// 调用 Service 查询
	pageResult, err := c.dictService.PageDictTypes(ctx.Request().Context(), query)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, pageResult)
}

// ListEnabledDictTypes 获取所有启用的字典类型简要列表
// @Summary 获取启用字典类型列表
// @Description 获取所有状态为启用的字典类型列表 (ID, Code, Name)，通常用于前端下拉框
// @Tags Dictionary
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.DictionaryTypeSimpleVO} "成功响应"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/types/list [get]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) ListEnabledDictTypes(ctx iris.Context) { // <<< 修改接收者类型
	opName := "获取启用字典类型列表"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_type")

	simpleList, err := c.dictService.ListEnabledDictTypes(ctx.Request().Context())
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, simpleList)
}

// --- Dictionary Item Handlers ---

// CreateDictItem 创建字典项
// @Summary 创建字典项
// @Description 创建一个新的字典项，必须指定其所属的字典类型ID
// @Tags Dictionary
// @Accept json
// @Produce json
// @Param item body dto.DictItemCreateOrUpdateDTO true "字典项创建信息" // <<< 修改 DTO
// @Success 200 {object} response.Response{data=vo.DictionaryItemVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误、类型ID无效或值/标签已存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/items [post]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) CreateDictItem(ctx iris.Context) { // <<< 修改接收者类型
	opName := "创建字典项"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_item")

	var req dto.DictItemCreateOrUpdateDTO // <<< 修改 DTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 调用 Service 创建
	dictItemVO, err := c.dictService.CreateDictItem(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, dictItemVO)
}

// UpdateDictItem 更新字典项
// @Summary 更新字典项
// @Description 根据ID更新字典项信息
// @Tags Dictionary
// @Accept json
// @Produce json
// @Param id path uint true "字典项ID"
// @Param item body dto.DictItemCreateOrUpdateDTO true "字典项更新信息" // <<< 修改 DTO
// @Success 200 {object} response.Response{data=vo.DictionaryItemVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误、ID无效或值/标签已存在"
// @Failure 404 {object} response.Response "字典项未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/items/{id} [put]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) UpdateDictItem(ctx iris.Context) { // <<< 修改接收者类型
	opName := "更新字典项"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_item")


	// --- Add Log Here ---
	userIDValue := ctx.Request().Context().Value(constant.CONTEXT_USER_ID)
	c.GetLogger().Info("--- Controller Entry Log ---", logger.WithField("opName", opName), logger.WithField("userIDValue", userIDValue), logger.WithField("type", fmt.Sprintf("%T", userIDValue)))
	// --- End Log ---

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的字典项ID参数").WithCause(err), opName)
		return
	}

	var req dto.DictItemCreateOrUpdateDTO // <<< 修改 DTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 调用 Service 更新
	dictItemVO, err := c.dictService.UpdateDictItem(ctx.Request().Context(), id, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, dictItemVO)
}

// DeleteDictItem 删除字典项
// @Summary 删除字典项
// @Description 根据ID删除字典项 (系统内置项无法删除)
// @Tags Dictionary
// @Produce json
// @Param id path uint true "字典项ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效或无法删除(系统内置)"
// @Failure 404 {object} response.Response "字典项未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/items/{id} [delete]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) DeleteDictItem(ctx iris.Context) { // <<< 修改接收者类型
	opName := "删除字典项"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_item")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的字典项ID参数").WithCause(err), opName)
		return
	}

	// 调用 Service 删除
	err = c.dictService.DeleteDictItem(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetDictItemByID 获取字典项详情
// @Summary 获取字典项详情
// @Description 根据ID获取字典项详细信息
// @Tags Dictionary
// @Produce json
// @Param id path uint true "字典项ID"
// @Success 200 {object} response.Response{data=vo.DictionaryItemVO} "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "字典项未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/items/{id} [get]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) GetDictItemByID(ctx iris.Context) { // <<< 修改接收者类型
	opName := "获取字典项详情"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_item")


	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的字典项ID参数").WithCause(err), opName)
		return
	}

	// 调用 Service 查询
	dictItemVO, err := c.dictService.GetDictItemByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, dictItemVO)
}

// PageDictItems 分页查询字典项
// @Summary 分页查询字典项
// @Description 获取字典项列表，支持分页和按类型ID/标签/值/状态过滤
// @Tags Dictionary
// @Produce json
// @Param query query dto.DictItemPageQueryDTO false "查询参数" // <<< 修改 DTO
// @Success 200 {object} response.Response{data=vo.PageDictItemVO} "成功响应"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/items/page [get]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) PageDictItems(ctx iris.Context) { // <<< 修改接收者类型
	opName := "分页查询字典项"
	var query dto.DictItemPageQueryDTO // <<< 修改 DTO


	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_item")


	// 绑定 Query 参数
	if err := ctx.ReadQuery(&query); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析查询参数失败").WithCause(err), opName)
		return
	}

	// 手动处理可选的 dictionaryTypeId (如果 ReadQuery 不能很好地处理指针)
	if typeIdStr := ctx.URLParamTrim("dictionaryTypeId"); typeIdStr != "" {
		if typeId, err := strconv.ParseUint(typeIdStr, 10, 64); err == nil {
			typeIdUint := uint(typeId)
			query.DictionaryTypeID = &typeIdUint
		} else {
			c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的 dictionaryTypeId 参数").WithCause(err), opName)
			return
		}
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)
	query.PageQuery.PageNum = pageQueryFromBuild.PageNum
	query.PageQuery.PageSize = pageQueryFromBuild.PageSize
	query.PageQuery.Sort = pageQueryFromBuild.Sort

	// 调用 Service 查询
	pageResult, err := c.dictService.PageDictItems(ctx.Request().Context(), query)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, pageResult)
}

// --- Dictionary Data Retrieval Handler ---

// GetDictItemsByTypeCode 根据类型编码获取字典项列表 (公共接口)
// @Summary 根据类型编码获取字典数据
// @Description 根据字典类型编码获取所有启用的字典项列表 (通常用于前端下拉框填充)
// @Tags Dictionary, Public
// @Produce json
// @Param typeCode query string true "字典类型编码"
// @Success 200 {object} response.Response{data=[]vo.DictDataVO} "成功响应"
// @Failure 400 {object} response.Response "缺少 typeCode 参数"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/dict/items/list [get]
// @Security ApiKeyAuth
func (c *DictionaryControllerImpl) GetDictItemsByTypeCode(ctx iris.Context) { // <<< 修改接收者类型
	opName := "根据类型编码获取字典项"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "dict_item")

	typeCode := ctx.URLParamTrim("typeCode")
	if typeCode == "" {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "typeCode 不能为空"), opName)
		return
	}

	// 调用 Service 查询
	dataList, err := c.dictService.GetDictItemsByTypeCode(ctx.Request().Context(), typeCode)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, dataList)
}
