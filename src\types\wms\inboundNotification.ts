// ==================== 导入类型 ====================
import type {
  WmsInboundNotificationStatus,
  WmsInboundNotificationType,
  WmsItemRespV2
} from '@/api/wms/inboundNotification'

// ==================== 表单数据类型 ====================

// 明细行数据（用于表单编辑）
export interface DetailRowData {
  id?: number
  lineNo: number
  itemId?: number
  itemSku?: string
  itemName?: string
  itemSpec?: string
  expectedQuantity: number
  receivedQuantity?: number
  unitOfMeasure: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
  // 表单状态标识
  _isNew?: boolean
  _toDelete?: boolean
  _hasChanged?: boolean
  _editing?: boolean
}

// 入库通知单表单数据
export interface InboundNotificationFormData {
  // 主表数据
  id?: number
  notificationNo?: string
  notificationType: WmsInboundNotificationType | ''
  expectedArrivalDate: string
  clientId: number | null
  clientName?: string
  warehouseId: number | null
  warehouseName?: string
  supplierId?: number | null
  supplierName?: string
  sourceDocNo?: string
  remark?: string
  status?: WmsInboundNotificationStatus

  // 明细表数据
  details: DetailRowData[]

  // 表单状态
  _mode?: 'create' | 'edit' | 'view'
  _loading?: boolean
  _submitting?: boolean
}

// ==================== 组件Props类型 ====================

// SKU选择器组件Props
export interface SkuSelectorProps {
  visible: boolean
  multiple?: boolean
  selectedSkus?: string[]
  filters?: Record<string, any>
  onConfirm?: (selectedItems: WmsItemRespV2[]) => void
  onCancel?: () => void
}

// 状态流转组件Props
export interface StatusFlowProps {
  currentStatus: WmsInboundNotificationStatus
  notificationId: number
  readonly?: boolean
  onStatusChange?: (newStatus: WmsInboundNotificationStatus, remark?: string) => void
}

// 明细表组件Props
export interface DetailTableProps {
  data: DetailRowData[]
  readonly?: boolean
  loading?: boolean
  onAdd?: () => void
  onDelete?: (index: number) => void
  onEdit?: (index: number, field: string, value: any) => void
  onSkuSelect?: (index: number) => void
}

// ==================== 查询表单类型 ====================

// 搜索表单数据
export interface SearchFormData {
  notificationNo?: string
  notificationType?: WmsInboundNotificationType | ''
  status?: WmsInboundNotificationStatus | ''
  clientId?: number | null
  warehouseId?: number | null
  supplierId?: number | null
  expectedTimeRange?: [string, string]
  createdTimeRange?: [string, string]
}

// ==================== 列表相关类型 ====================

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  sortable?: boolean
  filterable?: boolean
  slot?: boolean
  showOverflowTooltip?: boolean
  fixed?: 'left' | 'right'
}

// 操作按钮配置
export interface OperationButton {
  label: string
  icon: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  handler: (row: WmsInboundNotificationResp) => void
  visible?: (row: WmsInboundNotificationResp) => boolean
  disabled?: (row: WmsInboundNotificationResp) => boolean
}

// ==================== 状态相关类型 ====================

// 状态转换规则
export interface StatusTransition {
  from: WmsInboundNotificationStatus
  to: WmsInboundNotificationStatus[]
  label: string
  color: string
  requireRemark?: boolean
}

// 状态显示配置
export interface StatusConfig {
  value: WmsInboundNotificationStatus
  label: string
  color: string
  bgColor: string
  icon?: string
}

// ==================== 字典数据类型 ====================

// 下拉选项
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  children?: SelectOption[]
}

// 客户选项
export interface ClientOption extends SelectOption {
  value: number
  code: string
}

// 仓库选项
export interface WarehouseOption extends SelectOption {
  value: number
  code: string
}

// 供应商选项
export interface SupplierOption extends SelectOption {
  value: number
  code: string
}

// ==================== 业务规则类型 ====================

// 验证规则
export interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  validator?: (rule: any, value: any, callback: any) => void
}

// 表单验证规则集合
export interface FormValidationRules {
  [key: string]: ValidationRule[]
}

// ==================== 工具类型 ====================

// 分页参数
export interface PaginationParams {
  pageNum: number
  pageSize: number
  total: number
}

// 排序参数
export interface SortParams {
  prop: string
  order: 'ascending' | 'descending'
}

// 筛选参数
export interface FilterParams {
  [key: string]: any
}

// ==================== 常量定义 ====================

// 状态选项
export const STATUS_OPTIONS: StatusConfig[] = [
  { value: 'DRAFT', label: '草稿', color: '#909399', bgColor: '#f4f4f5' },
  { value: 'PLANNED', label: '已计划', color: '#409eff', bgColor: '#ecf5ff' },
  { value: 'ARRIVED', label: '已到货', color: '#67c23a', bgColor: '#f0f9ff' },
  { value: 'RECEIVING', label: '收货中', color: '#e6a23c', bgColor: '#fdf6ec' },
  { value: 'PARTIALLY_RECEIVED', label: '部分收货', color: '#f56c6c', bgColor: '#fef0f0' },
  { value: 'RECEIVED', label: '收货完成', color: '#67c23a', bgColor: '#f0f9ff' },
  { value: 'CLOSED', label: '已关闭', color: '#909399', bgColor: '#f4f4f5' },
  { value: 'CANCELLED', label: '已取消', color: '#f56c6c', bgColor: '#fef0f0' }
]

// 通知类型选项
export const NOTIFICATION_TYPE_OPTIONS: SelectOption[] = [
  { value: 'PO_RECEIPT', label: '采购收货' },
  { value: 'RETURN_RECEIPT', label: '退货入库' },
  { value: 'TRANSFER_RECEIPT', label: '调拨入库' },
  { value: 'PRODUCTION_RECEIPT', label: '生产入库' },
  { value: 'OTHER_RECEIPT', label: '其他入库' }
]

// 状态转换规则
export const STATUS_TRANSITIONS: StatusTransition[] = [
  {
    from: 'DRAFT',
    to: ['PLANNED', 'CANCELLED'],
    label: '提交计划',
    color: '#409eff'
  },
  {
    from: 'PLANNED',
    to: ['ARRIVED', 'CANCELLED'],
    label: '确认到货',
    color: '#67c23a'
  },
  {
    from: 'ARRIVED',
    to: ['RECEIVING', 'CANCELLED'],
    label: '开始收货',
    color: '#e6a23c'
  },
  {
    from: 'RECEIVING',
    to: ['PARTIALLY_RECEIVED', 'RECEIVED', 'CANCELLED'],
    label: '收货完成',
    color: '#67c23a'
  },
  {
    from: 'PARTIALLY_RECEIVED',
    to: ['RECEIVED', 'CLOSED', 'CANCELLED'],
    label: '完成收货',
    color: '#67c23a'
  },
  {
    from: 'RECEIVED',
    to: ['CLOSED'],
    label: '关闭通知单',
    color: '#909399'
  }
]

// 入库通知单状态枚举
export type WmsInboundNotificationStatus =
  | 'DRAFT'           // 草稿
  | 'PLANNED'         // 已计划
  | 'ARRIVED'         // 已到货
  | 'RECEIVING'       // 收货中
  | 'PARTIALLY_RECEIVED' // 部分收货
  | 'RECEIVED'        // 收货完成
  | 'CLOSED'          // 已关闭
  | 'CANCELLED'       // 已取消

// 通知类型枚举
export type WmsInboundNotificationType =
  | 'PO_RECEIPT'        // 采购收货
  | 'RETURN_RECEIPT'    // 退货入库
  | 'TRANSFER_RECEIPT'  // 调拨入库
  | 'PRODUCTION_RECEIPT' // 生产入库
  | 'OTHER_RECEIPT'     // 其他入库

// ==================== 常量定义 ====================

// 状态选项
export const STATUS_OPTIONS: SelectOption[] = [
  { value: 'DRAFT', label: '草稿', color: '#909399' },
  { value: 'PLANNED', label: '已计划', color: '#409EFF' },
  { value: 'ARRIVED', label: '已到货', color: '#E6A23C' },
  { value: 'RECEIVING', label: '收货中', color: '#F56C6C' },
  { value: 'PARTIALLY_RECEIVED', label: '部分收货', color: '#E6A23C' },
  { value: 'RECEIVED', label: '收货完成', color: '#67C23A' },
  { value: 'CLOSED', label: '已关闭', color: '#909399' },
  { value: 'CANCELLED', label: '已取消', color: '#F56C6C' }
]

// 通知类型选项
export const NOTIFICATION_TYPE_OPTIONS: SelectOption[] = [
  { value: 'PO_RECEIPT', label: '采购收货' },
  { value: 'RETURN_RECEIPT', label: '退货入库' },
  { value: 'TRANSFER_RECEIPT', label: '调拨入库' },
  { value: 'PRODUCTION_RECEIPT', label: '生产入库' },
  { value: 'OTHER_RECEIPT', label: '其他入库' }
]

// 单位选项
export const UNIT_OPTIONS: SelectOption[] = [
  { value: 'PCS', label: '件' },
  { value: 'BOX', label: '箱' },
  { value: 'KG', label: '千克' },
  { value: 'G', label: '克' },
  { value: 'L', label: '升' },
  { value: 'ML', label: '毫升' },
  { value: 'M', label: '米' },
  { value: 'CM', label: '厘米' }
]

// ==================== 工具函数类型 ====================

// 状态格式化函数类型
export type StatusFormatter = (status: WmsInboundNotificationStatus) => string

// 状态颜色配置类型
export interface StatusColorConfig {
  color: string
  bgColor: string
  borderColor: string
}

// 状态颜色映射类型
export type StatusColorMap = Record<WmsInboundNotificationStatus, StatusColorConfig>

// 表单验证规则类型
export interface FormValidationRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  validator?: (rule: any, value: any, callback: any) => void
}

// 表单验证规则映射类型
export type FormValidationRules = Record<string, FormValidationRule[]>

// 批量操作类型
export interface BatchOperation {
  type: 'delete' | 'status' | 'export'
  label: string
  icon?: string
  permission?: string
  disabled?: boolean
  handler: (selectedRows: any[]) => void | Promise<void>
}

// 导入导出配置类型
export interface ImportExportConfig {
  templateUrl?: string
  maxFileSize?: number
  allowedExtensions?: string[]
  validateHeaders?: boolean
  requiredColumns?: string[]
}

// 打印配置类型
export interface PrintConfig {
  templateId?: string
  orientation?: 'portrait' | 'landscape'
  paperSize?: 'A4' | 'A5' | 'Letter'
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

// 入库通知单明细创建请求
export interface WmsInboundNotificationDetailCreateReq {
  lineNo: number
  itemId: number
  expectedQuantity: number
  unitOfMeasure: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
}

// 入库通知单明细更新请求
export interface WmsInboundNotificationDetailUpdateReq {
  id?: number
  lineNo?: number
  itemId?: number
  expectedQuantity?: number
  unitOfMeasure?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
}

// 入库通知单创建请求
export interface WmsInboundNotificationCreateReq {
  notificationNo?: string
  notificationType: WmsInboundNotificationType
  expectedArrivalDate: string
  clientId: number
  warehouseId: number
  supplierId?: number
  sourceDocNo?: string
  remark?: string
  details: WmsInboundNotificationDetailCreateReq[]
}

// 入库通知单更新请求
export interface WmsInboundNotificationUpdateReq {
  notificationNo?: string
  notificationType?: WmsInboundNotificationType
  expectedArrivalDate?: string
  clientId?: number
  warehouseId?: number
  supplierId?: number
  sourceDocNo?: string
  remark?: string
  details?: WmsInboundNotificationDetailUpdateReq[]
}

// 入库通知单查询请求
export interface WmsInboundNotificationQueryReq {
  pageNum?: number
  pageSize?: number
  notificationNo?: string
  notificationType?: WmsInboundNotificationType
  status?: WmsInboundNotificationStatus
  statuses?: WmsInboundNotificationStatus[]
  clientId?: number
  warehouseId?: number
  supplierId?: number
  expectedTimeFrom?: string
  expectedTimeTo?: string
  createdAtFrom?: string
  createdAtTo?: string
  sort?: string
  includeDetails?: boolean
}

// 入库通知单明细响应
export interface WmsInboundNotificationDetailResp {
  id: number
  createdAt: string
  updatedAt: string
  tenantId: number
  accountBookId: number
  notificationId: number
  lineNo: number
  itemId: number
  expectedQuantity: number
  unitOfMeasure: string
  packageQty?: number
  packageUnit?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
  // 扩展字段
  itemSku?: string
  itemName?: string
  itemSpecification?: string
}

// 入库通知单响应
export interface WmsInboundNotificationResp {
  id: number
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  tenantId: number
  accountBookId: number
  notificationNo: string
  notificationType: WmsInboundNotificationType
  warehouseId: number
  clientId: number
  sourceDocNo?: string
  supplierShipper?: string
  expectedArrivalDate?: string
  status: WmsInboundNotificationStatus
  remark?: string
  // 关联信息
  details?: WmsInboundNotificationDetailResp[]
  clientName?: string
  warehouseName?: string
  supplierName?: string
}

// 状态更新请求
export interface WmsInboundNotificationUpdateStatusReq {
  status: WmsInboundNotificationStatus
  remark?: string
}

// 物料响应接口
export interface WmsItemRespV2 {
  id: number
  sku: string
  name: string
  specification?: string
  baseUnit: string
  shelfLifeDays?: number
  batchManaged: boolean
  serialManaged: boolean
  status: 'ACTIVE' | 'INACTIVE'
  weightKg?: number
  volumeM3?: number
  lengthM?: number
  widthM?: number
  heightM?: number
} 