package entity

import (
	"time"
)

// Employee 员工实体
// 该实体用于存储系统中的员工信息，包括基本信息、身份信息、教育背景等
// 继承自TenantEntity，支持多租户功能
type Employee struct {
	TenantEntity
	EmployeeCode                         string     `gorm:"column:employee_code;size:50;not null" json:"employeeCode" sortable:"true"`                                    // 员工编号：员工在系统中的唯一标识，不可重复 (允许排序)
	EmployeeName                         string     `gorm:"column:employee_name;size:50;not null" json:"employeeName" sortable:"true"`                                    // 员工姓名：员工的中文姓名 (允许排序)
	EmployeeNamePinyin                   string     `gorm:"column:employee_name_pinyin;size:50" json:"employeeNamePinyin"`                                                // 姓名拼音：员工姓名的拼音，用于搜索和排序
	EmployeeNameEn                       string     `gorm:"column:employee_name_en;size:50" json:"employeeNameEn"`                                                        // 英文名：员工的英文名称
	EmployeeIDCard                       string     `gorm:"column:employee_id_card;size:50;not null" json:"employeeIDCard"`                                               // 身份证号：证件号码
	EmployeeMobile                       string     `gorm:"column:employee_mobile;size:50" json:"employeeMobile"`                                                         // 手机号码：员工的联系电话
	EmployeeEmail                        string     `gorm:"column:employee_email;size:50" json:"employeeEmail"`                                                           // 电子邮箱：员工的电子邮箱地址
	EmployeeGender                       int        `gorm:"column:employee_gender;not null" json:"employeeGender"`                                                        // 性别：员工性别（0-未知，1-男，2-女）
	EmployeeBirthday                     time.Time  `gorm:"column:employee_birthday" json:"employeeBirthday"`                                                             // 出生日期：员工的出生日期
	EmployeeAvatar                       string     `gorm:"column:employee_avatar;size:255" json:"employeeAvatar"`                                                        // 头像：员工头像的URL地址
	EmployeeAddress                      string     `gorm:"column:employee_address;size:255" json:"employeeAddress"`                                                      // 地址：员工的居住地址 (这个字段保留，可能用于通用地址，与户籍/现居住地分开)
	EmployeeHouseholdAddress             string     `gorm:"column:employee_household_address;size:255" json:"employeeHouseholdAddress,omitempty"`                         // 户籍地址 (新增)
	EmployeeCurrentAddress               string     `gorm:"column:employee_current_address;size:255" json:"employeeCurrentAddress,omitempty"`                             // 居住地址 (新增)
	EmployeePostCode                     string     `gorm:"column:employee_post_code;size:20" json:"employeePostCode,omitempty"`                                          // 邮政编码
	EmployeeInDate                       time.Time  `gorm:"column:employee_in_date;not null" json:"employeeInDate"`                                                       // 入职日期：员工的入职时间
	EmployeeOutDate                      *time.Time `gorm:"column:employee_out_date" json:"employeeOutDate,omitempty"`                                                    // 离职日期：员工的离职时间 (允许为空)
	EmployeeFirstWorkDate                *time.Time `gorm:"column:employee_first_work_date" json:"employeeFirstWorkDate,omitempty"`                                       // 首次参加工作日期 (新增，允许为空)
	EmployeeStatus                       int        `gorm:"column:employee_status;not null" json:"employeeStatus"`                                                        // 状态：员工状态（0-离职，1-在职，2-休假等）
	EmployeeRemark                       string     `gorm:"column:employee_remark;size:500" json:"employeeRemark,omitempty"`                                              // 备注：关于员工的附加说明信息 (允许为空)
	EmployeeEducation                    int        `gorm:"column:employee_education" json:"employeeEducation,omitempty"`                                                 // 学历：员工的教育程度（0-未知，1-高中，2-大专，3-本科，4-硕士，5-博士） (字典 emp_education)
	EmployeeDegree                       string     `gorm:"column:employee_degree;size:50" json:"employeeDegree,omitempty"`                                               // 学位 (如：学士，硕士，博士)
	EmployeeMajor                        string     `gorm:"column:employee_major;size:100" json:"employeeMajor,omitempty"`                                                // 专业
	EmployeeSchool                       string     `gorm:"column:employee_school;size:100" json:"employeeSchool,omitempty"`                                              // 毕业院校
	EmployeeGraduationDate               *time.Time `gorm:"column:employee_graduation_date" json:"employeeGraduationDate,omitempty"`                                      // 毕业日期 (允许为空)
	EmployeeMaritalStatus                int        `gorm:"column:employee_marital_status" json:"employeeMaritalStatus,omitempty"`                                        // 婚姻状况：员工的婚姻状态（0-未知，1-未婚，2-已婚，3-离异，4-丧偶） (字典 emp_marital_status)
	EmployeeNationality                  string     `gorm:"column:employee_nationality;size:50" json:"employeeNationality,omitempty"`                                     // 国籍：员工的国籍
	EmployeeNation                       string     `gorm:"column:employee_nation;size:50" json:"employeeNation,omitempty"`                                               // 民族 (新增)
	EmployeeNativePlace                  string     `gorm:"column:employee_native_place;size:50" json:"employeeNativePlace,omitempty"`                                    // 籍贯：员工的籍贯所在地
	EmployeePoliticalStatus              int        `gorm:"column:employee_political_status" json:"employeePoliticalStatus,omitempty"`                                    // 政治面貌：员工的政治面貌（0-未知，1-群众，2-共青团员，3-中共党员等）(字典 emp_political_status)
	EmployeeIDCardType                   int        `gorm:"column:employee_id_card_type" json:"employeeIDCardType,omitempty"`                                             // 证件类型：员工的证件类型（0-未知，1-身份证，2-护照等） (字典 emp_id_card_type)
	EmployeeIDCardValidityPeriod         string     `gorm:"column:employee_id_card_validity_period;size:50" json:"employeeIDCardValidityPeriod,omitempty"`                // 证件有效期：员工证件的有效期限
	EmployeeDepartmentID                 uint       `gorm:"column:employee_department_id;index" json:"employeeDepartmentId,omitempty"`                                    // 部门ID：员工所属部门 (允许为空，表示未分配)
	EmployeePositionID                   uint       `gorm:"column:employee_position_id;index" json:"employeePositionId,omitempty"`                                        // 职位ID：员工担任的职位 (允许为空)
	EmployeeJobTitle                     string     `gorm:"column:employee_job_title;size:50" json:"employeeJobTitle,omitempty"`                                          // 职称：员工的职称 (关联字典 emp_professional_title 的 Value)
	EmployeeWorkType                     int        `gorm:"column:employee_work_type" json:"employeeWorkType,omitempty"`                                                  // 工作类型：员工的工作类型（0-全职，1-兼职，2-实习等） (字典 emp_work_type)
	EmployeeWorkLocation                 string     `gorm:"column:employee_work_location;size:100" json:"employeeWorkLocation,omitempty"`                                 // 工作地点：员工的工作地点
	EmployeeEmergencyContactName         string     `gorm:"column:employee_emergency_contact_name;size:50" json:"employeeEmergencyContactName,omitempty"`                 // 紧急联系人姓名 (修改自 EmployeeEmergencyContact)
	EmployeeEmergencyContactMobile       string     `gorm:"column:employee_emergency_contact_mobile;size:20" json:"employeeEmergencyContactMobile,omitempty"`             // 紧急联系人电话 (修改自 EmployeeEmergencyPhone)
	EmployeeEmergencyContactRelationship string     `gorm:"column:employee_emergency_contact_relationship;size:20" json:"employeeEmergencyContactRelationship,omitempty"` // 紧急联系人关系
	EmployeeBankName                     string     `gorm:"column:employee_bank_name;size:50" json:"employeeBankName,omitempty"`                                          // 开户银行：员工的工资卡开户银行
	EmployeeBankAccount                  string     `gorm:"column:employee_bank_account;size:50" json:"employeeBankAccount,omitempty"`                                    // 银行账号：员工的工资卡账号
	EmployeeJobGradeValue                string     `gorm:"column:employee_job_grade_value;size:50" json:"employeeJobGradeValue,omitempty"`                               // 员工职级：关联字典 emp_job_grade 的 Value
	EmployeeJobSubLevelValue             string     `gorm:"column:employee_job_sub_level_value;size:50" json:"employeeJobSubLevelValue,omitempty"`                        // 员工职等：关联字典 emp_job_sub_level 的 Value
	EmployeeWorkCategoryValue            string     `gorm:"column:employee_work_category_value;size:50" json:"employeeWorkCategoryValue,omitempty"`                       // 员工性质：关联字典 emp_work_category 的 Value
	EmployeeSocialSecurityNumber         string     `gorm:"column:employee_social_security_number;size:50" json:"employeeSocialSecurityNumber,omitempty"`                 // 社保账号：员工的社会保险账号
	EmployeeHousingFundNumber            string     `gorm:"column:employee_housing_fund_number;size:50" json:"employeeHousingFundNumber,omitempty"`                       // 公积金账号：员工的住房公积金账号
	EmployeeProbationEndDate             *time.Time `gorm:"column:employee_probation_end_date" json:"employeeProbationEndDate,omitempty"`                                 // 试用期结束日期：员工的试用期结束时间 (允许为空)
	EmployeeContractStartDate            *time.Time `gorm:"column:employee_contract_start_date" json:"employeeContractStartDate,omitempty"`                               // 合同开始日期：员工劳动合同的开始时间 (允许为空)
	EmployeeContractEndDate              *time.Time `gorm:"column:employee_contract_end_date" json:"employeeContractEndDate,omitempty"`                                   // 合同结束日期：员工劳动合同的结束时间 (允许为空)
	EmployeeSalary                       float64    `gorm:"column:employee_salary" json:"employeeSalary,omitempty"`                                                       // 基本工资：员工的基本工资
	EmployeeWorkYears                    int        `gorm:"column:employee_work_years" json:"employeeWorkYears,omitempty"`                                                // 工作年限：员工的工作经验年限
	EmployeeSkills                       string     `gorm:"column:employee_skills;size:500" json:"employeeSkills,omitempty"`                                              // 技能特长：员工的技能和特长描述
	EmployeeCertificates                 string     `gorm:"column:employee_certificates;size:500" json:"employeeCertificates,omitempty"`                                  // 证书资质：员工获得的证书和资质
	EmployeeHealthStatus                 int        `gorm:"column:employee_health_status" json:"employeeHealthStatus,omitempty"`                                          // 健康状况：员工的健康状况（0-未知，1-良好，2-一般，3-较差等） (字典 emp_health_status)
	EmployeeBloodType                    string     `gorm:"column:employee_blood_type;size:10" json:"employeeBloodType,omitempty"`                                        // 血型：员工的血型
	EmployeeHeight                       float64    `gorm:"column:employee_height" json:"employeeHeight,omitempty"`                                                       // 身高：员工的身高（厘米）
	EmployeeWeight                       float64    `gorm:"column:employee_weight" json:"employeeWeight,omitempty"`                                                       // 体重：员工的体重（千克）
	EmployeeQQ                           string     `gorm:"column:employee_qq;size:20" json:"employeeQQ,omitempty"`                                                       // QQ号码：员工的QQ联系方式
	EmployeeWechat                       string     `gorm:"column:employee_wechat;size:50" json:"employeeWechat,omitempty"`                                               // 微信：员工的微信联系方式
	EmployeeEntrySource                  string     `gorm:"column:employee_entry_source;size:100" json:"employeeEntrySource,omitempty"`                                   // 入职来源/渠道
	EmployeeReferralName                 string     `gorm:"column:employee_referral_name;size:50" json:"employeeReferralName,omitempty"`                                  // 推荐人姓名
	EmployeeHobby                        string     `gorm:"column:employee_hobby;size:255" json:"employeeHobby,omitempty"`                                                // 爱好
}

func (Employee) TableName() string {
	return "hr_employee"
}
