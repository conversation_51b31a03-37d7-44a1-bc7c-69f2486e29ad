<template>
  <div class="print-preview-example">
    <h2>VNPrintPreview 示例</h2>
    <p>点击下面的按钮来打开打印预览对话框。</p>

    <el-button type="success" @click="showPreview = true">
      打开打印预览 (简单内容)
    </el-button>

    <el-button type="warning" @click="showComplexPreview = true" style="margin-left: 10px;">
      打开打印预览 (含表格)
    </el-button>

    <!-- 简单内容预览 -->
    <VNPrintPreview 
      v-model="showPreview" 
      title="简单报告预览" 
      :print-styles="printStyles"
    >
      <template #content>
        <div class="printable-area simple-content">
          <h1>简单报告</h1>
          <p><strong>报告日期:</strong> {{ new Date().toLocaleDateString() }}</p>
          <p>这是一个简单的段落，用于演示基本的打印预览功能。</p>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
          <img src="https://via.placeholder.com/150" alt="Placeholder Image" style="margin-top: 15px;">
          <p>报告结束。</p>
        </div>
      </template>
    </VNPrintPreview>

    <!-- 复杂内容预览 (含表格) -->
    <VNPrintPreview 
      v-model="showComplexPreview" 
      title="销售订单打印预览" 
      :print-styles="printStyles"
    >
      <template #content>
        <div class="printable-area complex-content">
          <h1 style="text-align: center; margin-bottom: 25px;">销售订单</h1>
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <span><strong>订单号:</strong> SO-20240415-001</span>
            <span><strong>日期:</strong> {{ new Date().toLocaleDateString() }}</span>
          </div>
          <div style="margin-bottom: 15px;">
            <strong>客户信息:</strong><br>
            <span>客户名称: 示例客户公司</span><br>
            <span>联系人: 张三</span><br>
            <span>地址: 示例省示例市示例区示例路 123 号</span>
          </div>
          <table class="order-table">
             <thead>
              <tr>
                <th>序号</th>
                <th>产品名称</th>
                <th>规格</th>
                <th>数量</th>
                <th>单价</th>
                <th>金额</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1</td>
                <td>高效打印机</td>
                <td>型号 A</td>
                <td>2</td>
                <td>1500.00</td>
                <td>3000.00</td>
              </tr>
              <tr>
                <td>2</td>
                <td>无线鼠标</td>
                <td>黑色</td>
                <td>5</td>
                <td>80.00</td>
                <td>400.00</td>
              </tr>
              <tr>
                <td>3</td>
                <td>机械键盘</td>
                <td>青轴</td>
                <td>2</td>
                <td>450.00</td>
                <td>900.00</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="5" style="text-align: right;"><strong>总计:</strong></td>
                <td><strong>4300.00</strong></td>
              </tr>
            </tfoot>
          </table>
          <div style="margin-top: 30px; display: flex; justify-content: space-between; font-size: 0.9em;">
            <span>制单人: ____________</span>
            <span>审核人: ____________</span>
          </div>
        </div>
      </template>
    </VNPrintPreview>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNPrintPreview from './index.vue'; // 引入打印预览组件
import { ElButton } from 'element-plus';

// 控制两个预览对话框的显示状态
const showPreview = ref(false);
const showComplexPreview = ref(false);

// --- Read the print styles --- 
// We can define it as a string constant directly
// Alternatively, use template refs if the style block needs to be dynamic
const printStyles = `
/* 这些样式将在预览和打印时都生效 */
.printable-area {
  font-family: 'SimSun', 'Songti SC', serif;
  color: #000;
  line-height: 1.6;
  /* padding: 10px; Remove padding for print */
}

/* 简单内容的特定样式 */
.printable-area.simple-content h1 {
  font-size: 1.5em;
  margin-bottom: 1em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5em;
}
.printable-area.simple-content p {
  margin-bottom: 0.8em;
}

/* 复杂内容的特定样式 (表格) */
.printable-area.complex-content h1 {
  font-size: 1.6em;
}
.printable-area.complex-content .order-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  font-size: 0.9em;
}
.printable-area.complex-content .order-table th,
.printable-area.complex-content .order-table td {
  border: 1px solid #666;
  padding: 6px 8px;
  text-align: center;
}
.printable-area.complex-content .order-table th {
  background-color: #e0e0e0 !important; /* Use !important if needed */
  font-weight: bold;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}
.printable-area.complex-content .order-table td:nth-child(2),
.printable-area.complex-content .order-table td:nth-child(3) {
  text-align: left;
}
.printable-area.complex-content .order-table tfoot td {
  font-weight: bold;
  border-top: 2px solid #333;
}

/* 移除 @media print 部分，因为它不再需要 */
`;

</script>

<style scoped>
.print-preview-example {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 5px;
}
.print-preview-example h2 {
  margin-bottom: 15px;
  color: #333;
}
.print-preview-example p {
  margin-bottom: 20px;
  color: #555;
}
</style>

<!-- Restore the styles for the content itself -->
<style>
/* These styles apply to the content inside the slot */
.printable-area {
  font-family: 'SimSun', 'Songti SC', serif;
  color: #000;
  line-height: 1.6;
}

/* Simple content specific styles */
.printable-area.simple-content h1 {
  font-size: 1.5em;
  margin-bottom: 1em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5em;
}
.printable-area.simple-content p {
  margin-bottom: 0.8em;
}

/* Complex content specific styles (table) */
.printable-area.complex-content h1 {
  font-size: 1.6em;
}
.printable-area.complex-content .order-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  font-size: 0.9em;
}
.printable-area.complex-content .order-table th,
.printable-area.complex-content .order-table td {
  border: 1px solid #666;
  padding: 6px 8px;
  text-align: center;
}
.printable-area.complex-content .order-table th {
  background-color: #e0e0e0 !important;
  font-weight: bold;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}
.printable-area.complex-content .order-table td:nth-child(2),
.printable-area.complex-content .order-table td:nth-child(3) {
  text-align: left;
}
.printable-area.complex-content .order-table tfoot td {
  font-weight: bold;
  border-top: 2px solid #333;
}
</style>
