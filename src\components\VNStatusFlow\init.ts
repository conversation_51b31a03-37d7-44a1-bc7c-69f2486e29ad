/**
 * VNStatusFlow 组件初始化
 * 在应用启动时调用，注册所有预设配置
 */

import { initializeStatusFlowConfigs } from './constants'

/**
 * 初始化 VNStatusFlow 组件
 * 应该在 main.ts 中调用
 */
export const initVNStatusFlow = () => {
  // 初始化所有状态流程配置
  initializeStatusFlowConfigs()
  
  // 可以在这里添加其他初始化逻辑
  // 例如：设置全局配置、注册插件等
}

// 导出核心功能供外部使用
export { registerStatusFlowConfig, getStatusFlowConfig } from './hooks/useStatusFlow'
export { useStatusFlow, statusFlowUtils } from './hooks/useStatusFlow'
export * from './types'
export * from './constants'

// 导出配置
export { INBOUND_NOTIFICATION_CONFIG } from './configs/inboundNotification'
export { RECEIVING_RECORD_CONFIG } from './configs/receivingRecord'
export { OUTBOUND_NOTIFICATION_CONFIG } from './configs/outboundNotification'