import request from '@/utils/request' // 假设您有这样一个封装好的请求工具

// --- Menu 相关类型定义 (基于您提供的 Go VO/DTO) ---

export interface MenuMetaVO {
  title: string
  icon?: string
  noCache?: boolean
  hidden?: boolean
  alwaysShow?: boolean
  breadcrumb?: boolean
  activeMenu?: string
}

// 基础 VO (参考 Go BaseVO/TenantVO)
export interface BaseVO {
  id: number
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
  tenantId?: number
}

export interface TenantVO extends BaseVO {
  // 可能包含Tenant特有字段，如果后端有的话
}

export interface MenuVO extends TenantVO {
  parentId: number // 在 VO 中 ParentID 已经是 uint
  name: string
  title: string
  path?: string
  component?: string
  redirect?: string
  icon?: string
  type: number // 1:目录 2:菜单 3:按钮
  permission?: string
  status: number // 0:禁用 1:启用
  sort: number
  hidden: boolean
  noCache: boolean
  alwaysShow: boolean
  breadcrumb: boolean
  activeMenu?: string
  remark?: string
  isExternal: boolean // 新增 isExternal
}

export interface MenuTreeVO {
  id: number
  parentId: number
  name: string
  title: string
  path?: string
  component?: string
  redirect?: string
  icon?: string
  type: number
  permission?: string
  sort: number
  status: number
  hidden: boolean
  noCache: boolean
  alwaysShow: boolean
  breadcrumb: boolean
  activeMenu?: string
  isExternal: boolean // 新增 isExternal
  children?: MenuTreeVO[]
  hasChildren?: boolean // 前端可能需要，用于 VTable
}

export interface MenuCreateDTO {
  parentId?: number // 0 或 null 表示顶级
  name: string
  title: string
  path?: string
  component?: string
  redirect?: string
  icon?: string
  type: number // 1, 2, 3
  permission?: string
  status?: number // 0, 1
  sort?: number
  hidden?: boolean
  noCache?: boolean
  alwaysShow?: boolean
  breadcrumb?: boolean
  activeMenu?: string
  remark?: string
  isExternal?: boolean // 新增 isExternal
  tenantId?: number // 如果需要租户 ID
}

export interface MenuUpdateDTO extends MenuCreateDTO {
  // Update 通常包含所有 Create 字段，ID 在 URL 中传递
}

// 查询 DTO
export interface MenuQueryDTO {
    name?: string
    title?: string
    status?: number // 0 或 1
    type?: number   // 1, 2, 3
    permission?: string
}

// 树查询 DTO
export interface MenuTreeQueryDTO {
    status?: number // 0 或 1
    type?: number   // 1, 2, 3
}


// --- API 函数定义 ---

const BASE_URL = '/sys/menus' // 修正基础路径

/**
 * 获取菜单树
 * @param params 查询参数 (status, type)
 */
export const getMenuTree = (params?: MenuTreeQueryDTO): Promise<MenuTreeVO[]> => {
  return request({
    url: `${BASE_URL}/tree`,
    method: 'get',
    params
  })
}

/**
 * 获取菜单列表 (扁平结构)
 * @param params 查询参数 (name, title, status, type, permission)
 */
export const getMenuList = (params?: MenuQueryDTO): Promise<MenuVO[]> => {
    return request({
        url: `${BASE_URL}/list`,
        method: 'get',
        params
    })
}

/**
 * 根据 ID 获取菜单详情
 * @param id 菜单 ID
 */
export const getMenuByID = (id: number): Promise<MenuVO> => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'get'
  })
}

/**
 * 新增菜单
 * @param data 菜单数据
 */
export const addMenu = (data: MenuCreateDTO): Promise<MenuVO> => {
  return request({
    url: BASE_URL,
    method: 'post',
    data
  })
}

/**
 * 更新菜单
 * @param id 菜单 ID
 * @param data 菜单数据
 */
export const updateMenu = (id: number, data: MenuUpdateDTO): Promise<MenuVO> => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除菜单
 * @param id 菜单 ID
 */
export const deleteMenu = (id: number): Promise<null> => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'delete'
  })
}

/**
 * 获取当前用户菜单树 (用于侧边栏) - 假设 API 路径
 */
export const getUserMenuTree = (): Promise<MenuTreeVO[]> => {
    return request({
        url: '/user/menus/tree', // 参考后端 Controller 定义
        method: 'get'
    })
}

/**
 * 获取当前用户权限标识 - 假设 API 路径
 */
export const getUserPermissions = (): Promise<string[]> => {
    return request({
        url: '/user/permissions', // 参考后端 Controller 定义
        method: 'get'
    })
}

// 如果需要分页接口
/**
 * 分页获取菜单列表
 * @param params 查询参数及分页参数 (pageNum, pageSize, name, title, status, type)
 */
// export const getMenuPage = (params: MenuQueryDTO & { pageNum: number; pageSize: number }): Promise<ResponseData<PageResult<MenuVO>>> => {
//   return request({
//     url: `${BASE_URL}/page`, // 假设后端有 /page 接口
//     method: 'get',
//     params
//   })
// } 