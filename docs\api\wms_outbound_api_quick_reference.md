# WMS出库流程模块 API快速参考

## 基础信息
- **基础URL**: `/api/v1/wms`
- **认证**: Bearer Token
- **内容类型**: `application/json`

## 1. 出库通知单 `/outbound-notifications`

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/` | 创建出库通知单 |
| PUT | `/{id}` | 更新出库通知单 |
| DELETE | `/{id}` | 删除出库通知单 |
| GET | `/{id}` | 获取出库通知单详情 |
| GET | `/` | 分页查询出库通知单 |
| POST | `/batch-create` | 批量创建 |
| POST | `/batch-approve` | 批量审核 |
| POST | `/batch-cancel` | 批量取消 |
| POST | `/{id}/approve` | 审核通知单 |
| POST | `/{id}/cancel` | 取消通知单 |
| POST | `/{id}/allocate-inventory` | 分配库存 |
| POST | `/batch-allocate-inventory` | 批量分配库存 |
| GET | `/{id}/allocation-status` | 获取分配状态 |
| POST | `/{id}/generate-picking-task` | 生成拣货任务 |
| POST | `/batch-generate-picking-task` | 批量生成拣货任务 |
| GET | `/stats` | 获取统计数据 |
| POST | `/import-excel` | Excel导入 |
| GET | `/export-excel` | Excel导出 |
| GET | `/by-notification-no/{notificationNo}` | 根据通知单号查询 |
| GET | `/by-client-order-no/{clientOrderNo}` | 根据客户订单号查询 |
| GET | `/pending-allocation` | 获取待分配通知单 |
| GET | `/ready-for-picking` | 获取待拣货通知单 |
| GET | `/overdue-shipments` | 获取逾期发运通知单 |

## 2. 拣货任务 `/picking-tasks`

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/` | 创建拣货任务 |
| PUT | `/{id}` | 更新拣货任务 |
| DELETE | `/{id}` | 删除拣货任务 |
| GET | `/{id}` | 获取拣货任务详情 |
| GET | `/` | 分页查询拣货任务 |
| POST | `/{id}/assign` | 分配任务 |
| POST | `/batch-assign` | 批量分配任务 |
| POST | `/{id}/start` | 开始任务 |
| POST | `/{id}/complete` | 完成任务 |
| POST | `/{id}/cancel` | 取消任务 |
| POST | `/execute-picking` | 执行拣货 |
| POST | `/batch-execute-picking` | 批量执行拣货 |
| POST | `/handle-exception` | 处理异常 |
| GET | `/by-task-no/{taskNo}` | 根据任务号查询 |
| GET | `/by-notification/{notificationID}` | 根据通知单查询 |
| GET | `/pending-assignment` | 获取待分配任务 |

## 3. 库存分配 `/inventory-allocations`

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/` | 创建库存分配 |
| PUT | `/{id}` | 更新库存分配 |
| DELETE | `/{id}` | 删除库存分配 |
| GET | `/{id}` | 获取库存分配详情 |
| GET | `/` | 分页查询库存分配 |
| POST | `/auto-allocate` | 自动分配库存 |
| POST | `/batch-auto-allocate` | 批量自动分配 |
| POST | `/{id}/pick-confirm` | 拣货确认 |
| POST | `/{id}/release` | 释放分配 |
| POST | `/check-availability` | 检查库存可用性 |
| POST | `/batch-check-availability` | 批量检查库存可用性 |
| GET | `/by-outbound-detail/{outboundDetailID}` | 根据出库明细ID查询 |
| GET | `/by-inventory/{inventoryID}` | 根据库存ID查询 |
| GET | `/summary/{outboundDetailID}` | 获取分配汇总 |

## 4. 发运单 `/shipments`

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/` | 创建发运单 |
| PUT | `/{id}` | 更新发运单 |
| DELETE | `/{id}` | 删除发运单 |
| GET | `/{id}` | 获取发运单详情 |
| GET | `/` | 分页查询发运单 |
| POST | `/{id}/pack` | 打包发运单 |
| POST | `/{id}/ship` | 发运确认 |
| POST | `/{id}/tracking` | 更新跟踪状态 |
| POST | `/{id}/delivery` | 签收确认 |
| POST | `/calculate-cost` | 计算运费 |
| POST | `/print-label` | 打印发运标签 |
| GET | `/by-shipment-no/{shipmentNo}` | 根据发运单号查询 |
| GET | `/by-tracking-no/{trackingNo}` | 根据运单号查询 |
| GET | `/by-picking-task/{pickingTaskID}` | 根据拣货任务ID查询 |
| GET | `/pending-packing` | 获取待打包发运单 |
| GET | `/ready-to-ship` | 获取待发运发运单 |

## 5. 状态枚举

### 出库通知单状态
- `PENDING`: 待审核
- `APPROVED`: 已审核
- `ALLOCATED`: 已分配
- `PICKING`: 拣货中
- `PICKED`: 已拣货
- `SHIPPED`: 已发运
- `DELIVERED`: 已送达
- `CANCELLED`: 已取消

### 拣货任务状态
- `PENDING`: 待分配
- `ASSIGNED`: 已分配
- `IN_PROGRESS`: 进行中
- `COMPLETED`: 已完成
- `CANCELLED`: 已取消

### 库存分配状态
- `ALLOCATED`: 已分配
- `PICKED`: 已拣货
- `RELEASED`: 已释放

### 发运单状态
- `PREPARING`: 准备中
- `PACKED`: 已打包
- `READY`: 待发运
- `SHIPPED`: 已发运
- `IN_TRANSIT`: 运输中
- `DELIVERED`: 已送达
- `RETURNED`: 已退回

## 6. 常用查询参数

### 分页参数
- `pageNum`: 页码（默认1）
- `pageSize`: 每页大小（默认20）

### 时间范围
- `createdAtStart`: 创建时间开始
- `createdAtEnd`: 创建时间结束

### 状态筛选
- `status`: 状态筛选

## 7. 典型业务流程

### 完整出库流程
1. `POST /outbound-notifications` - 创建出库通知单
2. `POST /outbound-notifications/{id}/approve` - 审核通知单
3. `POST /outbound-notifications/{id}/allocate-inventory` - 分配库存
4. `POST /outbound-notifications/{id}/generate-picking-task` - 生成拣货任务
5. `POST /picking-tasks/{id}/assign` - 分配拣货任务
6. `POST /picking-tasks/{id}/start` - 开始拣货
7. `POST /picking-tasks/execute-picking` - 执行拣货
8. `POST /picking-tasks/{id}/complete` - 完成拣货
9. `POST /shipments` - 创建发运单
10. `POST /shipments/{id}/pack` - 打包
11. `POST /shipments/{id}/ship` - 发运确认

### 库存分配流程
1. `POST /inventory-allocations/check-availability` - 检查库存可用性
2. `POST /inventory-allocations/auto-allocate` - 自动分配库存
3. `POST /inventory-allocations/{id}/pick-confirm` - 拣货确认
4. `POST /inventory-allocations/{id}/release` - 释放分配（如需要）

## 8. 错误处理

### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `422`: 业务逻辑错误
- `500`: 服务器错误

### 业务错误码
- `20001`: 出库通知单状态错误
- `20002`: 库存不足
- `20003`: 拣货任务已分配
- `20004`: 发运单已发运

## 9. 认证示例

```bash
# 请求头示例
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## 10. 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 5
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": "2024-01-01T00:00:00Z"
}
```
