<template>
  <el-dialog
    :model-value="modelValue" 
    @update:model-value="handleUpdateModelValue"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    :open-delay="openDelay"
    :close-delay="closeDelay"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="beforeClose"
    :draggable="draggable"
    :center="center"
    :align-center="alignCenter"
    :destroy-on-close="destroyOnClose"
  >
    <!-- Header 插槽 -->
    <template #header v-if="$slots.header">
      <slot name="header"></slot>
    </template>
    <!-- 默认内容插槽 -->
    <slot></slot>

    <!-- Footer 插槽 -->
    <template #footer>
      <slot name="footer">
        <!-- 默认底部按钮 -->
        <span class="dialog-footer">
          <el-button 
            v-if="showCancelButton"
            @click="handleCancel"
          >
            {{ cancelButtonText }}
          </el-button>
          <el-button 
            v-if="showConfirmButton" 
            type="primary" 
            @click="handleConfirm"
            :loading="confirmButtonLoading"
          >
            {{ confirmButtonText }}
          </el-button>
        </span>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, withDefaults } from 'vue';
import { ElDialog, ElButton } from 'element-plus';
import type { VNDialogProps, VNDialogEmits } from './types';

// 定义 Props，继承 ElDialog 的 Props 并添加自定义 Props
const props = withDefaults(defineProps<VNDialogProps>(), {
  // --- 自定义 Props 默认值 ---
  modelValue: false,
  showConfirmButton: true,
  showCancelButton: true,
  confirmButtonText: '确认',
  cancelButtonText: '取消',
  confirmButtonLoading: false,
  // --- 继承 ElDialog Props 的常见默认值 (与 ElDialog 保持一致或根据需要调整) ---
  title: '',
  width: '50%',
  fullscreen: false,
  top: '15vh',
  modal: true,
  appendToBody: false,
  lockScroll: true,
  openDelay: 0,
  closeDelay: 0,
  closeOnClickModal: true,
  closeOnPressEscape: true,
  showClose: true,
  draggable: false,
  center: false,
  alignCenter: false,
  destroyOnClose: false,
});

// 定义 Emits
const emit = defineEmits<VNDialogEmits>();

// 处理 v-model 更新
const handleUpdateModelValue = (value: boolean) => {
  emit('update:modelValue', value);
};

// 转发 ElDialog 的事件
const handleOpen = () => emit('open');
const handleOpened = () => emit('opened');
const handleClose = () => emit('close');
const handleClosed = () => emit('closed');

// 处理默认按钮点击
const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
  // 通常点击取消也会关闭对话框
  emit('update:modelValue', false);
};

</script>

<style scoped>
.dialog-footer .el-button + .el-button {
  margin-left: 10px;
}
/* 可以根据需要添加更多自定义样式 */
</style> 