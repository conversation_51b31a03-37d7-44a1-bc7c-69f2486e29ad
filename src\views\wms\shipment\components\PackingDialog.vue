<template>
  <el-dialog
    v-model="dialogVisible"
    title="发运单打包"
    width="70%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="packing-dialog-container" v-if="shipmentData">
      <!-- 发运单信息 -->
      <el-card class="shipment-info-card" shadow="never">
        <template #header>
          <span>发运单信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">发运单号:</span>
              <span class="value">{{ shipmentData.shipmentNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">出库单号:</span>
              <span class="value">{{ shipmentData.outboundNotificationNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">承运商:</span>
              <span class="value">{{ shipmentData.carrierName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">运输方式:</span>
              <span class="value">{{ shipmentData.shippingMethod }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 打包信息 -->
      <el-card class="packing-info-card" shadow="never">
        <template #header>
          <span>打包信息</span>
        </template>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="打包人员" prop="packingUser">
                <el-input
                  v-model="formData.packingUser"
                  placeholder="请输入打包人员"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="打包时间" prop="packingTime">
                <el-date-picker
                  v-model="formData.packingTime"
                  type="datetime"
                  placeholder="请选择打包时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="包装总数" prop="totalPackages">
                <el-input-number
                  v-model="formData.totalPackages"
                  :min="1"
                  :precision="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="总重量(kg)" prop="actualWeight">
                <el-input-number
                  v-model="formData.actualWeight"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="总体积(m³)" prop="actualVolume">
                <el-input-number
                  v-model="formData.actualVolume"
                  :min="0"
                  :precision="3"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="包装费用" prop="packingCost">
                <el-input-number
                  v-model="formData.packingCost"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="打包备注" prop="packingRemark">
            <el-input
              v-model="formData.packingRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入打包备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 包装明细 -->
      <el-card class="package-details-card" shadow="never">
        <template #header>
          <div class="section-header">
            <span>包装明细</span>
            <el-button
              type="primary"
              size="small"
              @click="handleAddPackage"
            >
              添加包装
            </el-button>
          </div>
        </template>
        
        <VNTable
          ref="packageTableRef"
          :data="packageDetails"
          :columns="packageColumns"
          :loading="false"
          :show-operations="true"
          :operation-width="100"
          operation-fixed="right"
          row-key="id"
          show-index
        >
          <template #column-packageType="{ row }">
            <el-select
              v-model="row.packageType"
              placeholder="包装类型"
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="option in packageTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
          
          <template #column-weight="{ row }">
            <el-input-number
              v-model="row.weight"
              :min="0"
              :precision="2"
              size="small"
              style="width: 100px"
            />
          </template>
          
          <template #column-volume="{ row }">
            <el-input-number
              v-model="row.volume"
              :min="0"
              :precision="3"
              size="small"
              style="width: 100px"
            />
          </template>
          
          <template #operation="{ row, index }">
            <el-button
              type="danger"
              size="small"
              @click="handleRemovePackage(index)"
            >
              删除
            </el-button>
          </template>
        </VNTable>
      </el-card>
      
      <!-- 照片上传 -->
      <el-card class="photo-upload-card" shadow="never">
        <template #header>
          <span>打包照片</span>
        </template>
        
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          list-type="picture-card"
          accept="image/*"
          multiple
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="!canConfirm"
        >
          确认打包
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElInputNumber, ElDatePicker, ElSelect, ElOption, ElUpload, ElIcon } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn } from '@/components/VNTable/types'
import { useShipmentStore } from '@/store/wms/shipment'

// 组件接口定义
interface PackingDialogEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<PackingDialogEmits>()

// Store
const shipmentStore = useShipmentStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const shipmentData = ref<any>(null)
const packageDetails = ref<any[]>([])
const fileList = ref<any[]>([])

// 表单数据
const formData = reactive({
  packingUser: '',
  packingTime: new Date(),
  totalPackages: 1,
  actualWeight: 0,
  actualVolume: 0,
  packingCost: 0,
  packingRemark: ''
})

// 表单验证规则
const formRules: FormRules = {
  packingUser: [
    { required: true, message: '请输入打包人员', trigger: 'blur' }
  ],
  packingTime: [
    { required: true, message: '请选择打包时间', trigger: 'change' }
  ],
  totalPackages: [
    { required: true, message: '请输入包装总数', trigger: 'blur' }
  ]
}

// 包装类型选项
const packageTypeOptions = [
  { label: '纸箱', value: 'CARTON' },
  { label: '木箱', value: 'WOODEN_BOX' },
  { label: '托盘', value: 'PALLET' },
  { label: '袋装', value: 'BAG' },
  { label: '泡沫箱', value: 'FOAM_BOX' }
]

// 包装明细表格列配置
const packageColumns = computed<TableColumn[]>(() => [
  { prop: 'packageNo', label: '包装编号', width: 120 },
  { prop: 'packageType', label: '包装类型', width: 120, slot: true },
  { prop: 'weight', label: '重量(kg)', width: 120, slot: true },
  { prop: 'volume', label: '体积(m³)', width: 120, slot: true },
  { prop: 'itemCount', label: '物料数量', width: 100 },
  { prop: 'remark', label: '备注', width: 150 }
])

// 计算属性
const canConfirm = computed(() => {
  return formData.packingUser && formData.packingTime && packageDetails.value.length > 0
})

// 上传配置
const uploadAction = ref('/api/upload/images')
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})

// 表单引用
const formRef = ref<FormInstance>()
const packageTableRef = ref<InstanceType<typeof VNTable>>()
const uploadRef = ref()

// 方法
const loadShipmentData = async (shipmentId: number) => {
  try {
    const data = await shipmentStore.fetchDetail(shipmentId)
    shipmentData.value = data
    
    // 初始化包装明细
    if (data.details && data.details.length > 0) {
      packageDetails.value = [
        {
          id: 1,
          packageNo: 'PKG001',
          packageType: 'CARTON',
          weight: data.totalWeight || 0,
          volume: data.totalVolume || 0,
          itemCount: data.details.length,
          remark: ''
        }
      ]
      
      formData.actualWeight = data.totalWeight || 0
      formData.actualVolume = data.totalVolume || 0
      formData.totalPackages = 1
    }
  } catch (error) {
    ElMessage.error('加载发运单数据失败')
    console.error(error)
  }
}

const handleOpen = () => {
  // 在open方法中会传入shipmentId
}

const handleClose = () => {
  resetData()
}

const resetData = () => {
  shipmentData.value = null
  packageDetails.value = []
  fileList.value = []
  Object.assign(formData, {
    packingUser: '',
    packingTime: new Date(),
    totalPackages: 1,
    actualWeight: 0,
    actualVolume: 0,
    packingCost: 0,
    packingRemark: ''
  })
  formRef.value?.clearValidate()
}

const handleAddPackage = () => {
  const newPackage = {
    id: Date.now(),
    packageNo: `PKG${String(packageDetails.value.length + 1).padStart(3, '0')}`,
    packageType: 'CARTON',
    weight: 0,
    volume: 0,
    itemCount: 0,
    remark: ''
  }
  packageDetails.value.push(newPackage)
  formData.totalPackages = packageDetails.value.length
}

const handleRemovePackage = (index: number) => {
  packageDetails.value.splice(index, 1)
  formData.totalPackages = packageDetails.value.length
  
  // 重新编号
  packageDetails.value.forEach((pkg, idx) => {
    pkg.packageNo = `PKG${String(idx + 1).padStart(3, '0')}`
  })
}

// 上传相关方法
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleUploadSuccess: UploadProps['onSuccess'] = (response, file) => {
  ElMessage.success('图片上传成功')
}

const handleUploadError: UploadProps['onError'] = (error) => {
  ElMessage.error('图片上传失败')
  console.error(error)
}

const handleConfirm = async () => {
  if (!formRef.value || !shipmentData.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const packingData = {
      shipmentId: shipmentData.value.id,
      packingUser: formData.packingUser,
      packingTime: formData.packingTime,
      totalPackages: formData.totalPackages,
      actualWeight: formData.actualWeight,
      actualVolume: formData.actualVolume,
      packingCost: formData.packingCost,
      packingRemark: formData.packingRemark,
      packageDetails: packageDetails.value,
      photos: fileList.value.map(file => file.response?.url || file.url)
    }
    
    await shipmentStore.packShipment(shipmentData.value.id, packingData)
    
    ElMessage.success('打包成功')
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('打包失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (shipmentId: number) => {
  dialogVisible.value = true
  loadShipmentData(shipmentId)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.packing-dialog-container {
  padding: 16px 0;
}

.shipment-info-card,
.packing-info-card,
.package-details-card,
.photo-upload-card {
  margin-bottom: 16px;
}

.shipment-info-card :deep(.el-card__header),
.packing-info-card :deep(.el-card__header),
.package-details-card :deep(.el-card__header),
.photo-upload-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}
</style>
