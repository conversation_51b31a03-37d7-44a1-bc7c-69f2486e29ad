// internal/model/dto/wms_putaway_dto.go
package dto

import (
	"time"

	"backend/internal/model/entity"
	"backend/pkg/response"
)

// WmsPutawayTaskExecuteReq 定义执行上架任务明细的请求体
type WmsPutawayTaskExecuteReq struct {
	ActualLocationID uint    `json:"actualLocationId" binding:"required"`    // 实际库位 ID
	ActualQuantity   float64 `json:"actualQuantity" binding:"required,gt=0"` // 实际上架数量 (必须大于0)
	// DetailID 通常从 URL 参数获取，而不是放在请求体中
}

// WmsPutawayTaskAssignReq 定义分配上架任务的请求体
type WmsPutawayTaskAssignReq struct {
	UserID uint `json:"userId" binding:"required,gt=0"` // 分配给的操作员ID
	// TaskID 通常从 URL 参数获取
}

// WmsPutawayTaskQueryReq 查询上架任务的请求结构
type WmsPutawayTaskQueryReq struct {
	TaskNo            *string    `json:"taskNo,omitempty" form:"taskNo"`                       // 任务单号 (模糊查询)
	ReceivingRecordNo *string    `json:"receivingRecordNo,omitempty" form:"receivingRecordNo"` // 关联收货单号 (需要 JOIN 查询)
	Status            *string    `json:"status,omitempty" form:"status"`                       // 状态
	Statuses          []string   `json:"statuses,omitempty" form:"statuses"`                   // 状态列表 (IN 查询)
	AssignedToUserID  *uint      `json:"assignedToUserId,omitempty" form:"assignedToUserId"`   // 分配给的操作员 ID
	WarehouseID       *uint      `json:"warehouseId,omitempty" form:"warehouseId"`             // 仓库ID (可能需要JOIN查询? 或从关联收货单获取)
	CreatedAtFrom     *time.Time `json:"createdAtFrom,omitempty" form:"createdAtFrom"`         // 创建时间范围 - 开始
	CreatedAtTo       *time.Time `json:"createdAtTo,omitempty" form:"createdAtTo"`             // 创建时间范围 - 结束
	CompletedAtFrom   *time.Time `json:"completedAtFrom,omitempty" form:"completedAtFrom"`     // 完成时间范围 - 开始
	CompletedAtTo     *time.Time `json:"completedAtTo,omitempty" form:"completedAtTo"`         // 完成时间范围 - 结束
	SortField         string     `json:"sortField,omitempty" form:"sortField"`                 // 排序字段
	SortOrder         string     `json:"sortOrder,omitempty" form:"sortOrder"`                 // 排序顺序 (asc/desc)

	Pagination response.PageQuery `json:"-"` // 分页参数，不参与JSON序列化
}

// WmsPutawayTaskDetailResp 上架任务明细的响应结构
type WmsPutawayTaskDetailResp struct {
	entity.WmsPutawayTaskDetail
	ItemSKU               string `json:"itemSku,omitempty"`               // 物料 SKU
	ItemName              string `json:"itemName,omitempty"`              // 物料名称
	SourceLocationCode    string `json:"sourceLocationCode,omitempty"`    // 源库位代码
	SuggestedLocationCode string `json:"suggestedLocationCode,omitempty"` // 建议库位代码
	ActualLocationCode    string `json:"actualLocationCode,omitempty"`    // 实际库位代码
}

// WmsPutawayTaskResp 上架任务的响应结构
type WmsPutawayTaskResp struct {
	entity.WmsPutawayTask
	Details            []WmsPutawayTaskDetailResp `json:"details"`                      // 明细列表
	ReceivingRecordNo  string                     `json:"receivingRecordNo,omitempty"`  // 关联收货单号
	WarehouseName      string                     `json:"warehouseName,omitempty"`      // 仓库名称 (需要查询)
	AssignedToUserName string                     `json:"assignedToUserName,omitempty"` // 分配的操作员名称 (需要查询)
	CreatedByUserName  string                     `json:"createdByUserName,omitempty"`  // 创建人名称 (需要查询)
}

// WmsPutawayTaskCreateReq 创建上架任务的请求结构
type WmsPutawayTaskCreateReq struct {
	ReceivingRecordID uint    `json:"receivingRecordId" binding:"required"` // 关联收货记录ID
	Priority          int     `json:"priority"`                             // 优先级，默认为1
	Remark            *string `json:"remark,omitempty"`                     // 备注
}

// WmsPutawayTaskUpdateReq 更新上架任务的请求结构
type WmsPutawayTaskUpdateReq struct {
	Priority         *int    `json:"priority,omitempty"`         // 优先级
	AssignedToUserID *uint   `json:"assignedToUserId,omitempty"` // 分配给的操作员ID
	Remark           *string `json:"remark,omitempty"`           // 备注
}

// ======================= 上架任务明细相关 DTO =======================

// WmsPutawayTaskDetailCreateReq 创建上架任务明细的请求结构
type WmsPutawayTaskDetailCreateReq struct {
	PutawayTaskID           uint       `json:"putawayTaskId,omitempty"`                          // 上架任务ID (单独创建时需要)
	ReceivingRecordDetailID uint       `json:"receivingRecordDetailId" validate:"required,gt=0"` // 关联收货明细ID
	LineNo                  int        `json:"lineNo" validate:"required,gt=0"`                  // 行号
	ItemID                  uint       `json:"itemId" validate:"required,gt=0"`                  // 物料ID
	PutawayQuantity         float64    `json:"putawayQuantity" validate:"required,gt=0"`         // 需上架数量
	UnitOfMeasure           string     `json:"unitOfMeasure" validate:"required"`                // 数量单位
	BatchNo                 string     `json:"batchNo,omitempty"`                                // 批次号 (可选)
	ProductionDate          *time.Time `json:"productionDate,omitempty"`                         // 生产日期 (可选)
	ExpiryDate              *time.Time `json:"expiryDate,omitempty"`                             // 过期日期 (可选)
	SourceLocationID        uint       `json:"sourceLocationId" validate:"required,gt=0"`        // 源库位ID
	SuggestedLocationID     *uint      `json:"suggestedLocationId,omitempty"`                    // 建议库位ID (可选)
	Remark                  string     `json:"remark,omitempty"`                                 // 备注 (可选)
}

// WmsPutawayTaskDetailUpdateReq 更新上架任务明细的请求结构
type WmsPutawayTaskDetailUpdateReq struct {
	LineNo                *int       `json:"lineNo,omitempty"`                // 行号
	PutawayQuantity       *float64   `json:"putawayQuantity,omitempty"`       // 需上架数量
	UnitOfMeasure         *string    `json:"unitOfMeasure,omitempty"`         // 数量单位
	BatchNo               *string    `json:"batchNo,omitempty"`               // 批次号
	ProductionDate        *time.Time `json:"productionDate,omitempty"`        // 生产日期
	ExpiryDate            *time.Time `json:"expiryDate,omitempty"`            // 过期日期
	SourceLocationID      *uint      `json:"sourceLocationId,omitempty"`      // 源库位ID
	SuggestedLocationID   *uint      `json:"suggestedLocationId,omitempty"`   // 建议库位ID
	ActualLocationID      *uint      `json:"actualLocationId,omitempty"`      // 实际库位ID
	ActualPutawayQuantity *float64   `json:"actualPutawayQuantity,omitempty"` // 实际上架数量
	Status                *string    `json:"status,omitempty"`                // 状态
	ExceptionReason       *string    `json:"exceptionReason,omitempty"`       // 异常原因
}

// WmsPutawayTaskDetailQueryReq 查询上架任务明细的请求结构
type WmsPutawayTaskDetailQueryReq struct {
	PutawayTaskID           *uint      `json:"putawayTaskId,omitempty" form:"putawayTaskId"`                     // 上架任务ID
	ReceivingRecordDetailID *uint      `json:"receivingRecordDetailId,omitempty" form:"receivingRecordDetailId"` // 收货明细ID
	ItemID                  *uint      `json:"itemId,omitempty" form:"itemId"`                                   // 物料ID
	BatchNo                 *string    `json:"batchNo,omitempty" form:"batchNo"`                                 // 批次号
	Status                  *string    `json:"status,omitempty" form:"status"`                                   // 状态
	Statuses                []string   `json:"statuses,omitempty" form:"statuses"`                               // 状态列表
	SourceLocationID        *uint      `json:"sourceLocationId,omitempty" form:"sourceLocationId"`               // 源库位ID
	SuggestedLocationID     *uint      `json:"suggestedLocationId,omitempty" form:"suggestedLocationId"`         // 建议库位ID
	ActualLocationID        *uint      `json:"actualLocationId,omitempty" form:"actualLocationId"`               // 实际库位ID
	QuantityMin             *float64   `json:"quantityMin,omitempty" form:"quantityMin"`                         // 最小数量
	QuantityMax             *float64   `json:"quantityMax,omitempty" form:"quantityMax"`                         // 最大数量
	ProductionDateFrom      *time.Time `json:"productionDateFrom,omitempty" form:"productionDateFrom"`           // 生产日期范围开始
	ProductionDateTo        *time.Time `json:"productionDateTo,omitempty" form:"productionDateTo"`               // 生产日期范围结束
	ExpiryDateFrom          *time.Time `json:"expiryDateFrom,omitempty" form:"expiryDateFrom"`                   // 过期日期范围开始
	ExpiryDateTo            *time.Time `json:"expiryDateTo,omitempty" form:"expiryDateTo"`                       // 过期日期范围结束
	SortField               string     `json:"sortField,omitempty" form:"sortField"`                             // 排序字段
	SortOrder               string     `json:"sortOrder,omitempty" form:"sortOrder"`                             // 排序顺序

	Pagination response.PageQuery `json:"-"` // 分页参数
}

// WmsPutawayTaskDetailBatchCreateReq 批量创建上架任务明细的请求结构
type WmsPutawayTaskDetailBatchCreateReq struct {
	PutawayTaskID uint                            `json:"putawayTaskId" validate:"required,gt=0"` // 上架任务ID
	Details       []WmsPutawayTaskDetailCreateReq `json:"details" validate:"required,dive"`       // 明细列表
}

// WmsPutawayTaskDetailBatchUpdateReq 批量更新上架任务明细的请求结构
type WmsPutawayTaskDetailBatchUpdateReq struct {
	Updates []struct {
		ID     uint                          `json:"id" validate:"required,gt=0"` // 明细ID
		Detail WmsPutawayTaskDetailUpdateReq `json:"detail" validate:"required"`  // 更新内容
	} `json:"updates" validate:"required,dive"`
}

// WmsPutawayTaskDetailExecuteReq 执行上架任务明细的请求结构
type WmsPutawayTaskDetailExecuteReq struct {
	ActualLocationID      uint    `json:"actualLocationId" validate:"required,gt=0"`      // 实际库位ID
	ActualPutawayQuantity float64 `json:"actualPutawayQuantity" validate:"required,gt=0"` // 实际上架数量
	ExceptionReason       *string `json:"exceptionReason,omitempty"`                      // 异常原因 (可选)
}

// PutawayTaskDetailExecuteItem 批量执行项
type PutawayTaskDetailExecuteItem struct {
	ID      uint                            `json:"id" validate:"required,gt=0"` // 明细ID
	Execute WmsPutawayTaskDetailExecuteReq `json:"execute" validate:"required"`  // 执行信息
}

// PutawayTaskDetailSummary 上架任务明细汇总信息
type PutawayTaskDetailSummary struct {
	TotalDetailCount     int     `json:"totalDetailCount"`     // 总明细数量
	PendingDetailCount   int     `json:"pendingDetailCount"`   // 待上架明细数量
	CompletedDetailCount int     `json:"completedDetailCount"` // 已完成明细数量
	ExceptionDetailCount int     `json:"exceptionDetailCount"` // 异常明细数量
	TotalPutawayQuantity float64 `json:"totalPutawayQuantity"` // 总计划上架数量
	TotalActualQuantity  float64 `json:"totalActualQuantity"`  // 总实际上架数量
	QuantityDifference   float64 `json:"quantityDifference"`   // 数量差异
	CompletionPercentage float64 `json:"completionPercentage"` // 完成百分比
}

// TODO: 添加其他上架任务相关的 DTO，例如查询请求/响应等
