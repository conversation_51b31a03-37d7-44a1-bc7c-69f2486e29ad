import request from '@/utils/request';

// --- 类型定义 (根据后端 fin_tax_rate VO/DTO) ---

// 税率列表项
export interface TaxRateListItem {
  id: number;
  name: string;
  rate: string; // 使用字符串以保持精度
  isEnabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 税率列表响应
export interface TaxRateListResponse {
  list: TaxRateListItem[];
  total: number;
}

// 税率查询参数
export interface TaxRateQueryParams {
  pageNum: number;
  pageSize: number;
  name?: string;
  isEnabled?: boolean;
  sort?: string;
}

// 税率创建/更新数据
export interface TaxRateFormData {
  name: string;
  rate: string; // 使用字符串以保持精度
  isEnabled: boolean;
  description?: string;
}

// --- API 函数定义 ---

const BASE_URL = '/fin/tax-rates';

/**
 * 获取税率列表 (分页)
 * @param params 查询参数
 */
export function getTaxRatePage(params: TaxRateQueryParams): Promise<TaxRateListResponse> {
  return request.get<TaxRateListResponse>(`${BASE_URL}/page`, { params }) as unknown as Promise<TaxRateListResponse>;
}

/**
 * 获取单个税率详情
 * @param id 税率ID
 */
export function getTaxRateDetail(id: number): Promise<TaxRateListItem> {
    return request.get<TaxRateListItem>(`${BASE_URL}/${id}`) as unknown as Promise<TaxRateListItem>;
}

/**
 * 新增税率
 * @param data 税率数据
 */
export function addTaxRate(data: TaxRateFormData) {
  return request.post<any>(BASE_URL, data);
}

/**
 * 更新税率信息
 * @param id 税率 ID
 * @param data 税率数据
 */
export function updateTaxRate(id: number, data: Partial<TaxRateFormData>) {
  return request.put<any>(`${BASE_URL}/${id}`, data);
}

/**
 * 删除税率
 * @param id 税率 ID
 */
export function deleteTaxRate(id: number) {
  return request.delete<any>(`${BASE_URL}/${id}`);
}

/**
 * 批量删除税率
 * @param ids 税率 ID 数组
 */
// export function batchDeleteTaxRates(ids: number[]) {
//   return request.delete<any>(BASE_URL, { data: { ids } });
// } 