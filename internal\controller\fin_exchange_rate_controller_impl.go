package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"

	"github.com/kataras/iris/v12"
)

// FinExchangeRateController 定义汇率控制器接口
type FinExchangeRateController interface {
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetPage(ctx iris.Context)
	GetLatest(ctx iris.Context)
	GetByID(ctx iris.Context)
}

// FinExchangeRateControllerImpl 是 FinExchangeRateController 的实现
type FinExchangeRateControllerImpl struct {
	*BaseControllerImpl
	finExchangeRateService service.FinExchangeRateService
}

// NewFinExchangeRateControllerImpl 创建一个新的汇率控制器
func NewFinExchangeRateControllerImpl(cm *ControllerManager) *FinExchangeRateControllerImpl {
	return &FinExchangeRateControllerImpl{
		BaseControllerImpl:     NewBaseController(cm),
		finExchangeRateService: cm.GetServiceManager().FinExchangeRateService(),
	}
}

// Create godoc
// @Summary      创建汇率
// @Description  创建一个新的汇率
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        exchange_rate  body      dto.FinExchangeRateCreateReq  true  "汇率信息"
// @Success      200      {object}  response.Response{data=vo.FinExchangeRateItemRes}
// @Router       /api/v1/fin/exchange-rates [post]
func (c *FinExchangeRateControllerImpl) Create(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_exchange_rate")
	var req dto.FinExchangeRateCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}

	result, err := c.finExchangeRateService.Create(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "创建汇率失败")
		return
	}
	c.Success(ctx, result)
}

// Update godoc
// @Summary      更新汇率
// @Description  更新一个已有的汇率
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id              path      int                  true  "汇率ID"
// @Param        exchange_rate  body      dto.FinExchangeRateUpdateReq  true  "汇率信息"
// @Success      200      {object}  response.Response{data=vo.FinExchangeRateItemRes}
// @Router       /api/v1/fin/exchange-rates/{id} [put]
func (c *FinExchangeRateControllerImpl) Update(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_exchange_rate")
	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	var req dto.FinExchangeRateUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}
	req.ID = uint(id)

	result, err := c.finExchangeRateService.Update(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "更新汇率失败")
		return
	}
	c.Success(ctx, result)
}

// Delete godoc
// @Summary      删除汇率
// @Description  根据ID删除一个汇率
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "汇率ID"
// @Success      200  {object}  response.Response
// @Router       /api/v1/fin/exchange-rates/{id} [delete]
func (c *FinExchangeRateControllerImpl) Delete(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_exchange_rate")
	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	err = c.finExchangeRateService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		c.HandleError(ctx, err, "删除汇率失败")
		return
	}
	c.Success(ctx, nil)
}

// GetByID godoc
// @Summary      获取汇率详情
// @Description  根据ID获取单个汇率的详细信息
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "汇率ID"
// @Success      200  {object}  response.Response{data=vo.FinExchangeRateItemRes}
// @Router       /api/v1/fin/exchange-rates/{id} [get]
func (c *FinExchangeRateControllerImpl) GetByID(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_exchange_rate")
	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}
	result, err := c.finExchangeRateService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		c.HandleError(ctx, err, "获取汇率详情失败")
		return
	}
	c.Success(ctx, result)
}

// GetPage godoc
// @Summary      分页查询汇率
// @Description  根据条件分页获取汇率列表
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        query  query     dto.FinExchangeRatePageReq  false  "查询参数"
// @Success      200    {object}  response.Response{data=response.PageResult}
// @Router       /api/v1/fin/exchange-rates/page [get]
func (c *FinExchangeRateControllerImpl) GetPage(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_exchange_rate")
	var req dto.FinExchangeRatePageReq
	if err := ctx.ReadQuery(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}

	result, err := c.finExchangeRateService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "查询汇率失败")
		return
	}
	c.Success(ctx, result)
}

// GetLatest godoc
// @Summary      获取最新汇率
// @Description  根据源币种、目标币种和日期获取最新有效汇率
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        query  body     dto.LatestExchangeRateReq  true  "查询参数"
// @Success      200  {object}  response.Response{data=vo.FinExchangeRateItemRes}
// @Router       /api/v1/fin/exchange-rates/latest [post]
func (c *FinExchangeRateControllerImpl) GetLatest(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_exchange_rate")
	var req dto.LatestExchangeRateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}

	result, err := c.finExchangeRateService.GetLatestRate(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "获取最新汇率失败")
		return
	}
	c.Success(ctx, result)
}
