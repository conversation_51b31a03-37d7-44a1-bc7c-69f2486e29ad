package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"

	"github.com/kataras/iris/v12"
)

// FinTaxRateController 定义税率控制器接口
type FinTaxRateController interface {
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetPage(ctx iris.Context)
	GetByID(ctx iris.Context)
}

// FinTaxRateControllerImpl 是 FinTaxRateController 的实现
type FinTaxRateControllerImpl struct {
	*BaseControllerImpl
	finTaxRateService service.FinTaxRateService
}

// NewFinTaxRateControllerImpl 创建一个新的税率控制器
func NewFinTaxRateControllerImpl(cm *ControllerManager) *FinTaxRateControllerImpl {
	return &FinTaxRateControllerImpl{
		BaseControllerImpl: NewBaseController(cm),
		finTaxRateService:  cm.GetServiceManager().FinTaxRateService(),
	}
}

// Create godoc
// @Summary      创建税率
// @Description  创建一个新的税率
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        tax_rate  body      dto.FinTaxRateCreateReq  true  "税率信息"
// @Success      200      {object}  response.Response{data=vo.FinTaxRateItemRes}
// @Router       /api/v1/fin/tax-rates [post]
func (c *FinTaxRateControllerImpl) Create(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_tax_rate")
	var req dto.FinTaxRateCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}

	result, err := c.finTaxRateService.Create(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "创建税率失败")
		return
	}
	c.Success(ctx, result)
}

// Update godoc
// @Summary      更新税率
// @Description  更新一个已有的税率
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id        path      int                  true  "税率ID"
// @Param        tax_rate  body      dto.FinTaxRateUpdateReq  true  "税率信息"
// @Success      200      {object}  response.Response{data=vo.FinTaxRateItemRes}
// @Router       /api/v1/fin/tax-rates/{id} [put]
func (c *FinTaxRateControllerImpl) Update(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_tax_rate")
	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	var req dto.FinTaxRateUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}
	req.ID = uint(id)

	result, err := c.finTaxRateService.Update(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "更新税率失败")
		return
	}
	c.Success(ctx, result)
}

// Delete godoc
// @Summary      删除税率
// @Description  根据ID删除一个税率
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "税率ID"
// @Success      200  {object}  response.Response
// @Router       /api/v1/fin/tax-rates/{id} [delete]
func (c *FinTaxRateControllerImpl) Delete(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_tax_rate")
	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	err = c.finTaxRateService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		c.HandleError(ctx, err, "删除税率失败")
		return
	}
	c.Success(ctx, nil)
}

// GetByID godoc
// @Summary      获取税率详情
// @Description  根据ID获取单个税率的详细信息
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "税率ID"
// @Success      200  {object}  response.Response{data=vo.FinTaxRateItemRes}
// @Router       /api/v1/fin/tax-rates/{id} [get]
func (c *FinTaxRateControllerImpl) GetByID(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_tax_rate")
	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	result, err := c.finTaxRateService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		c.HandleError(ctx, err, "获取税率详情失败")
		return
	}
	c.Success(ctx, result)
}

// GetPage godoc
// @Summary      分页查询税率
// @Description  根据条件分页获取税率列表
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        query  query     dto.FinTaxRatePageReq  false  "查询参数"
// @Success      200    {object}  response.Response{data=response.PageResult}
// @Router       /api/v1/fin/tax-rates/page [get]
func (c *FinTaxRateControllerImpl) GetPage(ctx iris.Context) {
	// 设置审计资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_tax_rate")
	var req dto.FinTaxRatePageReq
	if err := ctx.ReadQuery(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}

	result, err := c.finTaxRateService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "查询税率失败")
		return
	}
	c.Success(ctx, result)
}
