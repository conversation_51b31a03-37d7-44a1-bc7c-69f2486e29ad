package repository

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
	"context"

	"gorm.io/gorm"
)

type MtlItemRepository interface {
	BaseRepository[entity.MtlItem, uint]

	// 物料专用方法
	FindBySKU(ctx context.Context, sku string) (*entity.MtlItem, error)
	FindByItemCode(ctx context.Context, itemCode string) (*entity.MtlItem, error)
	GetPage(ctx context.Context, req *dto.MtlItemQueryReq) (*response.PageResult, error)

	// 包装单位相关方法
	FindPackageUnitsByItemID(ctx context.Context, itemID uint) ([]*entity.MtlItemPackageUnit, error)
	FindPackageUnitByID(ctx context.Context, unitID uint) (*entity.MtlItemPackageUnit, error)
	AddPackageUnit(ctx context.Context, unit *entity.MtlItemPackageUnit) error
	UpdatePackageUnit(ctx context.Context, unit *entity.MtlItemPackageUnit) error
	DeletePackageUnit(ctx context.Context, unitID uint) error
}

type mtlItemRepositoryImpl struct {
	BaseRepositoryImpl[entity.MtlItem, uint]
}

func NewMtlItemRepository(db *gorm.DB) MtlItemRepository {
	return &mtlItemRepositoryImpl{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.MtlItem, uint]{db: db},
	}
}

// FindBySKU 根据SKU查找物料 (自动处理账套scope)
func (r *mtlItemRepositoryImpl) FindBySKU(ctx context.Context, sku string) (*entity.MtlItem, error) {
	var item entity.MtlItem
	err := r.db.WithContext(ctx).Where("sku = ?", sku).First(&item).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "物料不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询物料失败").WithCause(err)
	}
	return &item, nil
}

// FindByItemCode 根据物料编码查找物料 (自动处理账套scope)
func (r *mtlItemRepositoryImpl) FindByItemCode(ctx context.Context, itemCode string) (*entity.MtlItem, error) {
	var item entity.MtlItem
	err := r.db.WithContext(ctx).Where("sku = ?", itemCode).First(&item).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "物料不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询物料失败").WithCause(err)
	}
	return &item, nil
}

// GetPage 使用现有分页机制实现分页查询 (参考WmsLocation实现)
func (r *mtlItemRepositoryImpl) GetPage(ctx context.Context, req *dto.MtlItemQueryReq) (*response.PageResult, error) {
	var conditions []QueryCondition

	if req.Sku != "" {
		conditions = append(conditions, NewLikeCondition("sku", req.Sku))
	}
	if req.Name != "" {
		conditions = append(conditions, NewLikeCondition("name", req.Name))
	}
	if req.CategoryCode != nil {
		conditions = append(conditions, NewEqualCondition("category_code", *req.CategoryCode))
	}
	if req.GroupCode != nil {
		conditions = append(conditions, NewEqualCondition("group_code", *req.GroupCode))
	}
	if req.DefaultSupplierID != nil {
		conditions = append(conditions, NewEqualCondition("default_supplier_id", *req.DefaultSupplierID))
	}
	if req.DefaultLocationID != nil {
		conditions = append(conditions, NewEqualCondition("default_location_id", *req.DefaultLocationID))
	}
	if req.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", req.Status))
	}
	if req.BatchManaged != nil {
		conditions = append(conditions, NewEqualCondition("batch_managed", *req.BatchManaged))
	}
	if req.SerialManaged != nil {
		conditions = append(conditions, NewEqualCondition("serial_managed", *req.SerialManaged))
	}

	// 使用BaseRepository的FindByPage方法 (自动处理账套scope)
	return r.FindByPage(ctx, &req.PageQuery, conditions)
}

// FindPackageUnitsByItemID 查询指定物料的所有包装单位
func (r *mtlItemRepositoryImpl) FindPackageUnitsByItemID(ctx context.Context, itemID uint) ([]*entity.MtlItemPackageUnit, error) {
	var units []*entity.MtlItemPackageUnit
	err := r.db.WithContext(ctx).Where("item_id = ?", itemID).Order("id asc").Find(&units).Error
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询包装单位失败").WithCause(err)
	}
	return units, nil
}

// FindPackageUnitByID 根据ID查询包装单位
func (r *mtlItemRepositoryImpl) FindPackageUnitByID(ctx context.Context, unitID uint) (*entity.MtlItemPackageUnit, error) {
	var unit entity.MtlItemPackageUnit
	err := r.db.WithContext(ctx).First(&unit, unitID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "包装单位不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询包装单位失败").WithCause(err)
	}
	return &unit, nil
}

// AddPackageUnit 添加包装单位
func (r *mtlItemRepositoryImpl) AddPackageUnit(ctx context.Context, unit *entity.MtlItemPackageUnit) error {
	err := r.db.WithContext(ctx).Create(unit).Error
	if err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "添加包装单位失败").WithCause(err)
	}
	return nil
}

// UpdatePackageUnit 更新包装单位
func (r *mtlItemRepositoryImpl) UpdatePackageUnit(ctx context.Context, unit *entity.MtlItemPackageUnit) error {
	err := r.db.WithContext(ctx).Save(unit).Error
	if err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新包装单位失败").WithCause(err)
	}
	return nil
}

// DeletePackageUnit 删除包装单位 (软删除)
func (r *mtlItemRepositoryImpl) DeletePackageUnit(ctx context.Context, unitID uint) error {
	err := r.db.WithContext(ctx).Delete(&entity.MtlItemPackageUnit{}, unitID).Error
	if err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除包装单位失败").WithCause(err)
	}
	return nil
}
