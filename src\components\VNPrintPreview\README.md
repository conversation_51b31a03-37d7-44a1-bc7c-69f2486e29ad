# VNPrintPreview 打印预览组件

一个通用的打印预览 Vue 组件，使用 Element Plus 的 Dialog 组件展示需要打印的内容，并提供打印功能。

## 使用方法

```vue
<template>
  <div>
    <el-button @click="showPreview = true">打开打印预览</el-button>

    <VNPrintPreview v-model="showPreview" title="自定义标题">
      <template #content>
        <!-- 放入你需要打印的 HTML 内容 -->
        <div class="printable-area">
          <h1>这是标题</h1>
          <p>这是内容段落。</p>
          <table>
            <!-- 表格内容 -->
          </table>
        </div>
      </template>
    </VNPrintPreview>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNPrintPreview from '@/components/VNPrintPreview/index.vue';
import { ElButton } from 'element-plus';

const showPreview = ref(false);
</script>

<style>
/* 为打印内容区域定义样式 */
.printable-area {
  /* ... 你的样式 ... */
}

/* 定义仅打印时生效的样式 */
@media print {
  .printable-area {
    /* ... 打印特定样式 ... */
  }
}
</style>
```

## Props

| Prop         | 类型      | 默认值     | 说明                                       |
|--------------|-----------|------------|--------------------------------------------|
| `modelValue` | `Boolean` | `false`    | 控制对话框的显示与隐藏，支持 `v-model`。     |
| `title`      | `String`  | `'打印预览'` | 对话框的标题。                             |

## Emits

| 事件名             | 参数          | 说明                                       |
|--------------------|---------------|--------------------------------------------|
| `update:modelValue`| `value: boolean` | 当对话框显示状态改变时触发，用于 `v-model`。 |
| `closed`           | -             | 当对话框关闭动画完成后触发。                 |

## Slots

| 插槽名    | 说明                                           |
|-----------|------------------------------------------------|
| `content` | 用于放置需要打印预览的具体 HTML 内容。         |

## 打印样式说明

组件内部包含一个 `@media print` 样式块，用于在打印时：

*   隐藏页面上除了打印内容之外的所有元素（包括对话框本身）。
*   将打印内容区域扩展至整个页面。
*   移除背景色、阴影等不适合打印的样式。
*   尝试优化分页。

你可以根据实际需要调整 `@media print` 中的样式，或者在你自己的 CSS 中添加更具体的打印样式（确保它们能覆盖组件的默认打印样式）。 