package errors

import (
	"backend/pkg/validator" // 确保导入 validator 包
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"
)

// 错误级别常量
const (
	LEVEL_DEBUG = "debug" // 调试级别
	LEVEL_INFO  = "info"  // 信息级别
	LEVEL_WARN  = "warn"  // 警告级别
	LEVEL_ERROR = "error" // 错误级别
	LEVEL_FATAL = "fatal" // 致命级别
)

// 错误代码范围常量
const (
	CODE_SYSTEM_ERROR   = 10000 // 系统错误代码起始值
	CODE_BUSINESS_ERROR = 20000 // 业务错误代码起始值
	CODE_AUTH_ERROR     = 30000 // 认证错误代码起始值
	CODE_PARAMS_ERROR   = 40000 // 参数错误代码起始值
	CODE_DATA_ERROR     = 50000 // 数据错误代码起始值
)

// 系统错误代码常量
const (
	CODE_SYSTEM_UNKNOWN             = CODE_SYSTEM_ERROR + iota // 未知系统错误
	CODE_SYSTEM_INTERNAL                                       // 内部系统错误
	CODE_SYSTEM_NOT_IMPLEMENTED                                // 未实现的功能
	CODE_SYSTEM_SERVICE_UNAVAILABLE                            // 服务不可用
	CODE_SYSTEM_TIMEOUT                                        // 系统超时
	CODE_SYSTEM_RESOURCE_EXHAUSTED                             // 资源耗尽
	CODE_SYSTEM_CANCELED                                       // 操作已取消
	CODE_SYSTEM_ABORTED                                        // 操作已终止
	CODE_SYSTEM_UNAVAILABLE                                    // 系统不可用 (补充)
	CODE_SYSTEM_CACHE_ERROR                                    // 缓存错误 (补充)
	CODE_CONTEXT_VALUE_MISSING                                 // 上下文关键值缺失 (新增)
)

// 业务错误代码常量 (新增文件上传相关)
const (
	CODE_BUSINESS_GENERIC             = CODE_BUSINESS_ERROR + iota // 通用业务错误
	CODE_UPLOAD_FILE_SIZE_EXCEEDED                                 // 文件大小超限
	CODE_UPLOAD_FILE_TYPE_NOT_ALLOWED                              // 不允许的文件类型
	CODE_LICENSE_INVALID              = CODE_BUSINESS_ERROR + 10   // 授权文件无效或签名错误
	CODE_LICENSE_EXPIRED              = CODE_BUSINESS_ERROR + 11   // 授权文件已过期
	CODE_BUSINESS_RESOURCE_NOT_FOUND  = CODE_BUSINESS_ERROR + 20   // 业务资源未找到 (例如: 库位)
)

// 认证错误代码常量
const (
	CODE_AUTH_UNAUTHORIZED      = CODE_AUTH_ERROR + iota // 未授权
	CODE_AUTH_FORBIDDEN                                  // 禁止访问
	CODE_AUTH_TOKEN_EXPIRED                              // 令牌过期
	CODE_AUTH_TOKEN_INVALID                              // 无效令牌
	CODE_AUTH_LOGIN_FAILED                               // 登录失败
	CODE_AUTH_ACCOUNT_LOCKED                             // 账户锁定
	CODE_AUTH_PERMISSION_DENIED                          // 权限拒绝
)

// 参数错误代码常量
const (
	CODE_PARAMS_INVALID      = CODE_PARAMS_ERROR + iota // 无效参数
	CODE_PARAMS_MISSING                                 // 缺失参数
	CODE_PARAMS_TYPE_ERROR                              // 参数类型错误
	CODE_PARAMS_FORMAT_ERROR                            // 参数格式错误
	CODE_PARAMS_VALUE_ERROR                             // 参数值错误
	CODE_PARAMS_RANGE_ERROR                             // 参数范围错误
)

// 数据错误代码常量
const (
	CODE_DATA_NOT_FOUND            = CODE_DATA_ERROR + iota // 数据不存在
	CODE_DATA_ALREADY_EXISTS                                // 数据已存在
	CODE_DATA_VALIDATION_FAILED                             // 数据验证失败
	CODE_DATA_INTEGRITY_ERROR                               // 数据完整性错误
	CODE_DATA_CONFLICT                                      // 数据冲突
	CODE_DATA_CORRUPTED                                     // 数据已损坏
	CODE_DATA_RELATION_ERROR                                // 数据关系错误
	CODE_DATA_CREATE_FAILED                                 // 数据创建失败
	CODE_DATA_UPDATE_FAILED                                 // 数据更新失败
	CODE_DATA_DELETE_FAILED                                 // 数据删除失败
	CODE_DATA_QUERY_FAILED                                  // 数据查询失败
	CODE_FILE_NOT_FOUND                                     // 文件未找到 (新增)
	CODE_DATA_INVENTORY_NOT_ENOUGH                          // 库存不足 (新增)
)

// ErrNotFound 是一个表示"记录未找到"的哨兵错误。
// 在仓库层返回这个错误，服务层可以使用 errors.Is(err, apperrors.ErrNotFound) 来检查。
var ErrNotFound = NewDataError(CODE_DATA_NOT_FOUND, "数据不存在")

// ErrorDetail 错误详细信息
type ErrorDetail struct {
	Field   string      `json:"field,omitempty"`   // 错误字段
	Value   interface{} `json:"value,omitempty"`   // 错误值
	Message string      `json:"message,omitempty"` // 错误消息
}

// CustomError 自定义错误结构
type CustomError struct {
	Code       int                    `json:"code"`                  // 错误代码
	Message    string                 `json:"message"`               // 错误消息
	Details    []ErrorDetail          `json:"details,omitempty"`     // 错误详情
	Caller     string                 `json:"caller,omitempty"`      // 调用者
	Stack      string                 `json:"-"`                     // 堆栈信息
	Level      string                 `json:"level"`                 // 错误级别
	Timestamp  int64                  `json:"timestamp"`             // 时间戳
	HttpStatus int                    `json:"http_status,omitempty"` // HTTP状态码
	Metadata   map[string]interface{} `json:"metadata,omitempty"`    // 元数据
	Cause      error                  `json:"-"`                     // 原始错误
}

// Error 实现error接口
func (e *CustomError) Error() string {
	if e == nil {
		return ""
	}

	if e.Details != nil && len(e.Details) > 0 {
		detailsJSON, _ := json.Marshal(e.Details)
		return fmt.Sprintf("[%d] %s - 详情: %s", e.Code, e.Message, string(detailsJSON))
	}

	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// WithDetail 添加错误详情
func (e *CustomError) WithDetail(field string, value interface{}, message string) *CustomError {
	if e == nil {
		return nil
	}

	detail := ErrorDetail{
		Field:   field,
		Value:   value,
		Message: message,
	}

	e.Details = append(e.Details, detail)
	return e
}

// WithCaller 设置调用者信息
func (e *CustomError) WithCaller() *CustomError {
	if e == nil {
		return nil
	}

	pc, file, line, ok := runtime.Caller(1)
	if !ok {
		return e
	}

	// 提取文件名（不包含路径）
	parts := strings.Split(file, "/")
	fileName := parts[len(parts)-1]

	// 获取函数名
	funcName := runtime.FuncForPC(pc).Name()
	funcParts := strings.Split(funcName, ".")
	funcName = funcParts[len(funcParts)-1]

	e.Caller = fmt.Sprintf("%s:%s:%d", fileName, funcName, line)
	return e
}

// WithStack 添加堆栈信息
func (e *CustomError) WithStack() *CustomError {
	if e == nil {
		return nil
	}

	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	e.Stack = string(buf[:n])
	return e
}

// WithLevel 设置错误级别
func (e *CustomError) WithLevel(level string) *CustomError {
	if e == nil {
		return nil
	}

	e.Level = level
	return e
}

// WithHttpStatus 设置HTTP状态码
func (e *CustomError) WithHttpStatus(status int) *CustomError {
	if e == nil {
		return nil
	}

	e.HttpStatus = status
	return e
}

// WithMetadata 添加元数据
func (e *CustomError) WithMetadata(key string, value interface{}) *CustomError {
	if e == nil {
		return nil
	}

	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}

	e.Metadata[key] = value
	return e
}

// WithCause 设置原始错误
func (e *CustomError) WithCause(err error) *CustomError {
	if e == nil {
		return nil
	}

	e.Cause = err
	return e
}

// Unwrap 实现errors.Unwrap接口
func (e *CustomError) Unwrap() error {
	if e == nil {
		return nil
	}
	return e.Cause
}

// ToMap 转换为Map
func (e *CustomError) ToMap() map[string]interface{} {
	if e == nil {
		return nil
	}

	result := map[string]interface{}{
		"code":      e.Code,
		"message":   e.Message,
		"level":     e.Level,
		"timestamp": e.Timestamp,
	}

	if len(e.Details) > 0 {
		result["details"] = e.Details
	}

	if e.Caller != "" {
		result["caller"] = e.Caller
	}

	if e.HttpStatus != 0 {
		result["http_status"] = e.HttpStatus
	}

	if e.Metadata != nil && len(e.Metadata) > 0 {
		result["metadata"] = e.Metadata
	}

	return result
}

// ToJSON 转换为JSON字符串
func (e *CustomError) ToJSON() string {
	if e == nil {
		return "{}"
	}

	data, err := json.Marshal(e.ToMap())
	if err != nil {
		return fmt.Sprintf("{\"code\":%d,\"message\":\"%s\"}", e.Code, e.Message)
	}

	return string(data)
}

// GetHttpStatus 获取HTTP状态码
func (e *CustomError) GetHttpStatus() int {
	if e == nil || e.HttpStatus == 0 {
		// 根据错误代码范围映射默认HTTP状态码
		switch {
		case e == nil:
			return http.StatusOK
		case e.Code >= CODE_SYSTEM_ERROR && e.Code < CODE_BUSINESS_ERROR:
			return http.StatusInternalServerError
		case e.Code >= CODE_BUSINESS_ERROR && e.Code < CODE_AUTH_ERROR:
			return http.StatusBadRequest
		case e.Code >= CODE_AUTH_ERROR && e.Code < CODE_PARAMS_ERROR:
			return http.StatusUnauthorized
		case e.Code >= CODE_PARAMS_ERROR && e.Code < CODE_DATA_ERROR:
			return http.StatusBadRequest
		case e.Code >= CODE_DATA_ERROR:
			switch e.Code {
			case CODE_DATA_NOT_FOUND:
				return http.StatusNotFound
			case CODE_DATA_ALREADY_EXISTS:
				return http.StatusConflict
			default:
				return http.StatusBadRequest
			}
		default:
			return http.StatusInternalServerError
		}
	}

	return e.HttpStatus
}

// NewError 创建新的自定义错误
func NewError(code int, message string) *CustomError {
	return &CustomError{
		Code:      code,
		Message:   message,
		Level:     LEVEL_ERROR,
		Timestamp: time.Now().Unix(),
		Details:   make([]ErrorDetail, 0),
	}
}

// 包装错误
func WrapError(err error, code int, message string) *CustomError {
	if err == nil {
		return nil
	}

	// 检查是否已经是自定义错误
	var customErr *CustomError
	if ce, ok := err.(*CustomError); ok {
		customErr = &CustomError{
			Code:       code,
			Message:    message,
			Level:      ce.Level,
			Timestamp:  time.Now().Unix(),
			HttpStatus: ce.HttpStatus,
			Cause:      ce,
			Details:    make([]ErrorDetail, 0),
		}
	} else {
		customErr = &CustomError{
			Code:      code,
			Message:   message,
			Level:     LEVEL_ERROR,
			Timestamp: time.Now().Unix(),
			Cause:     err,
			Details:   make([]ErrorDetail, 0),
		}
	}

	return customErr
}

// IsError 检查是否为特定错误代码
func IsError(err error, code int) bool {
	if err == nil {
		return false
	}

	// 检查是否为自定义错误
	if ce, ok := err.(*CustomError); ok {
		return ce.Code == code
	}

	return false
}

// GetErrorCode 从错误获取错误代码
func GetErrorCode(err error) int {
	if err == nil {
		return 0
	}

	// 检查是否为自定义错误
	if ce, ok := err.(*CustomError); ok {
		return ce.Code
	}

	return CODE_SYSTEM_UNKNOWN
}

// GetErrorMessage 从错误获取错误消息
func GetErrorMessage(err error) string {
	if err == nil {
		return ""
	}

	return err.Error()
}

// 预定义系统错误
var (
	ErrSystemInternal       = NewError(CODE_SYSTEM_INTERNAL, "系统内部错误").WithHttpStatus(http.StatusInternalServerError)
	ErrSystemUnavailable    = NewError(CODE_SYSTEM_SERVICE_UNAVAILABLE, "服务不可用").WithHttpStatus(http.StatusServiceUnavailable)
	ErrSystemTimeout        = NewError(CODE_SYSTEM_TIMEOUT, "系统操作超时").WithHttpStatus(http.StatusGatewayTimeout)
	ErrSystemNotImplemented = NewError(CODE_SYSTEM_NOT_IMPLEMENTED, "功能未实现").WithHttpStatus(http.StatusNotImplemented)
)

// 预定义认证错误
var (
	ErrAuthUnauthorized     = NewError(CODE_AUTH_UNAUTHORIZED, "未授权").WithHttpStatus(http.StatusUnauthorized)
	ErrAuthForbidden        = NewError(CODE_AUTH_FORBIDDEN, "禁止访问").WithHttpStatus(http.StatusForbidden)
	ErrAuthTokenExpired     = NewError(CODE_AUTH_TOKEN_EXPIRED, "令牌已过期").WithHttpStatus(http.StatusUnauthorized)
	ErrAuthTokenInvalid     = NewError(CODE_AUTH_TOKEN_INVALID, "无效的令牌").WithHttpStatus(http.StatusUnauthorized)
	ErrAuthLoginFailed      = NewError(CODE_AUTH_LOGIN_FAILED, "登录失败").WithHttpStatus(http.StatusUnauthorized)
	ErrAuthPermissionDenied = NewError(CODE_AUTH_PERMISSION_DENIED, "权限被拒绝").WithHttpStatus(http.StatusForbidden)
)

// 预定义参数错误
var (
	ErrParamsInvalid     = NewError(CODE_PARAMS_INVALID, "无效的参数").WithHttpStatus(http.StatusBadRequest)
	ErrParamsMissing     = NewError(CODE_PARAMS_MISSING, "缺少必要参数").WithHttpStatus(http.StatusBadRequest)
	ErrParamsTypeError   = NewError(CODE_PARAMS_TYPE_ERROR, "参数类型错误").WithHttpStatus(http.StatusBadRequest)
	ErrParamsFormatError = NewError(CODE_PARAMS_FORMAT_ERROR, "参数格式错误").WithHttpStatus(http.StatusBadRequest)
)

// 预定义数据错误
var (
	ErrDataNotFound         = NewError(CODE_DATA_NOT_FOUND, "数据不存在").WithHttpStatus(http.StatusNotFound)
	ErrDataAlreadyExists    = NewError(CODE_DATA_ALREADY_EXISTS, "数据已存在").WithHttpStatus(http.StatusConflict)
	ErrDataValidationFailed = NewError(CODE_DATA_VALIDATION_FAILED, "数据验证失败").WithHttpStatus(http.StatusBadRequest)
	ErrDataConflict         = NewError(CODE_DATA_CONFLICT, "数据冲突").WithHttpStatus(http.StatusConflict)
)

// 从普通错误创建自定义错误
func FromError(err error) *CustomError {
	if err == nil {
		return nil
	}

	// 已经是自定义错误则直接返回
	if ce, ok := err.(*CustomError); ok {
		return ce
	}

	return NewError(CODE_SYSTEM_UNKNOWN, err.Error()).WithCause(err)
}

// 根据错误代码范围创建对应类型的错误
func NewSystemError(code int, message string) *CustomError {
	return NewError(code, message).WithLevel(LEVEL_ERROR).WithHttpStatus(http.StatusInternalServerError)
}

// NewBusinessError 创建业务错误
func NewBusinessError(code int, message string) *CustomError {
	return NewError(code, message).WithLevel(LEVEL_WARN).WithHttpStatus(http.StatusBadRequest)
}

// NewBizError 是 NewBusinessError 的一个更简洁的别名
func NewBizError(code int, message string) *CustomError {
	return NewBusinessError(code, message)
}

func NewAuthError(code int, message string) *CustomError {
	return NewError(code, message).WithLevel(LEVEL_WARN).WithHttpStatus(http.StatusUnauthorized)
}

func NewParamError(code int, message string) *CustomError {
	return NewError(code, message).WithLevel(LEVEL_WARN).WithHttpStatus(http.StatusBadRequest)
}

func NewDataError(code int, message string) *CustomError {
	return NewError(code, message).WithLevel(LEVEL_ERROR).WithHttpStatus(http.StatusInternalServerError)
}

// 参数验证错误
func NewValidationError(field string, value interface{}, message string) *CustomError {
	return NewParamError(CODE_DATA_VALIDATION_FAILED, message).
		WithDetail(field, value, message).
		WithLevel(LEVEL_WARN).
		WithHttpStatus(http.StatusBadRequest)
}

// 检查是否为特定错误类型
func IsSystemError(err error) bool {
	code := GetErrorCode(err)
	return code >= CODE_SYSTEM_ERROR && code < CODE_BUSINESS_ERROR
}

func IsBusinessError(err error) bool {
	code := GetErrorCode(err)
	return code >= CODE_BUSINESS_ERROR && code < CODE_AUTH_ERROR
}

func IsAuthError(err error) bool {
	code := GetErrorCode(err)
	return code >= CODE_AUTH_ERROR && code < CODE_PARAMS_ERROR
}

func IsParamError(err error) bool {
	code := GetErrorCode(err)
	return code >= CODE_PARAMS_ERROR && code < CODE_DATA_ERROR
}

func IsDataError(err error) bool {
	code := GetErrorCode(err)
	return code >= CODE_DATA_ERROR
}

// 创建带堆栈跟踪的错误
func NewStackError(code int, message string) *CustomError {
	return NewError(code, message).WithStack().WithCaller()
}

// 创建带HTTP状态码的错误
func NewHttpError(code int, message string, httpStatus int) *CustomError {
	return NewError(code, message).WithHttpStatus(httpStatus)
}

// NewParamsError 创建参数错误实例 (新增)
// 通常用于检查函数入参时发现的问题
func NewParamsError(message string) *CustomError {
	return NewError(CODE_PARAMS_VALUE_ERROR, message). // 使用 CODE_PARAMS_VALUE_ERROR 作为默认代码
								WithLevel(LEVEL_WARN).
								WithHttpStatus(http.StatusBadRequest)
}

// NewValidationErrorFromValidator 从 validator.ValidationErrors 创建参数错误
func NewValidationErrorFromValidator(valErrs validator.ValidationErrors) *CustomError {
	// 使用已有的 CODE_PARAMS_INVALID 或 DATA_VALIDATION_FAILED
	// CODE_PARAMS_INVALID 更适合输入验证
	paramErr := NewParamError(CODE_PARAMS_INVALID, "参数验证失败")
	if valErrs == nil {
		return paramErr // 如果传入 nil，返回基础参数错误
	}

	details := make([]ErrorDetail, len(valErrs))
	for i, vErr := range valErrs {
		details[i] = ErrorDetail{
			Field:   vErr.Field,   // 字段名
			Value:   vErr.Value,   // 原始值 (字符串形式)
			Message: vErr.Message, // 我们翻译过的消息
			// Namespace: vErr.Namespace, // 可以考虑放入 Metadata
			// Tag:       vErr.Tag,       // 可以考虑放入 Metadata
		}
	}
	paramErr.Details = details // 将转换后的详情设置到 CustomError
	return paramErr
}

// IsDuplicateKeyError 检测是否为数据库重复键错误
func IsDuplicateKeyError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	// MySQL 重复键错误
	if strings.Contains(errStr, "duplicate entry") ||
		strings.Contains(errStr, "error 1062") {
		return true
	}

	// PostgreSQL 重复键错误
	if strings.Contains(errStr, "duplicate key value") ||
		strings.Contains(errStr, "unique constraint") {
		return true
	}

	return false
}

// NewDuplicateKeyError 创建重复键错误
func NewDuplicateKeyError(message string) *CustomError {
	return NewDataError(CODE_DATA_ALREADY_EXISTS, message).WithLevel(LEVEL_WARN)
}
