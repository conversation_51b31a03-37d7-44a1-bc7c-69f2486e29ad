import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getReceivingRecordPage,
  getReceivingRecordDetail,
  createReceivingRecord,
  updateReceivingRecord,
  deleteReceivingRecord,
  updateReceivingRecordStatus,
  createBlindReceivingRecord,
  createReceivingRecordFromAsn
} from '@/api/wms/receivingRecord'
import {
  getClientPageV2,
  getSupplierList,
  getInboundNotificationDetail
} from '@/api/wms/inboundNotification'
import { getLocationTree } from "@/api/wms/location"
import type {
  WmsReceivingRecordSimpleResp,
  WmsReceivingRecordQueryReq,
  WmsReceivingRecordCreateReq,
  WmsReceivingRecordUpdateReq,
  WmsReceivingRecordStatus,
  WmsReceivingRecordResp,
  WmsReceivingRecordDetailResp,
  WmsReceivingRecordBlindCreateReq,
  WmsReceivingRecordDetailCreateReq
} from '@/api/wms/receivingRecord'
import type { WmsInboundNotificationResp } from '@/api/wms/inboundNotification'
import type {
  ReceivingRecordFormData,
  DetailRowData,
  SearchFormData,
  PaginationParams,
  SelectOption
} from '@/types/wms/receivingRecord'
import type { WmsLocationTreeVO } from '@/api/wms/location'
import type { WmsInboundNotificationDetailResp } from '@/api/wms/inboundNotification'

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000 // 24 hours

export const useWmsReceivingRecordStore = defineStore('wmsReceivingRecord', () => {
  // ==================== 状态 ====================
  const list = ref<WmsReceivingRecordSimpleResp[]>([])
  const total = ref(0)
  const loading = ref(false)
  const dialogVisible = ref(false)
  const formMode = ref<'create' | 'edit' | 'view'>('create')
  const searchData = ref<SearchFormData>({})
  const pagination = ref<PaginationParams>({
    pageNum: 1,
    pageSize: 20,
    total: 0,
    currentPage: 1
  })
  const formData = ref<ReceivingRecordFormData>({
    receivingType: 'ASN',
    actualArrivalDate: '',
    clientId: null,
    warehouseId: null,
    supplementDeadline: undefined,
    details: []
  })
  const formLoading = ref(false)
  const currentId = ref<number | null>(null)

  // ==================== 缓存 ====================
  const clientOptions = ref<SelectOption[]>([])
  const warehouseOptions = ref<WmsLocationTreeVO[]>([])
  const supplierOptions = ref<SelectOption[]>([])
  const lastFetchTime = ref<Record<string, number>>({})

  // ==================== 计算属性 ====================
  const isReadonly = computed(() => formMode.value === 'view' || formData.value.status === 'COMPLETED' || formData.value.status === 'CANCELLED')
  const dialogTitle = computed(() => {
    const titles = {
      create: '新增收货记录',
      edit: '编辑收货记录',
      view: '查看收货记录'
    }
    return titles[formMode.value]
  })

  // ==================== 方法 ====================

  /** 检查缓存是否有效 */
  const isCacheValid = (key: string) => {
    const lastTime = lastFetchTime.value[key]
    return lastTime && Date.now() - lastTime < CACHE_EXPIRATION
  }

  /** 获取列表 */
  const fetchList = async () => {
    loading.value = true
    try {
      const { receivedAtRange, ...restSearchData } = searchData.value
      const params: WmsReceivingRecordQueryReq = {
        ...restSearchData,
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        receivedAtFrom: receivedAtRange?.[0],
        receivedAtTo: receivedAtRange?.[1],
        clientId: restSearchData.clientId || undefined,
        warehouseId: restSearchData.warehouseId || undefined
      }

      const response = await getReceivingRecordPage(params)
      list.value = response?.list || []
      pagination.value.total = response?.total || 0
    } finally {
      loading.value = false
    }
  }

  /** 处理搜索 */
  const handleSearch = () => {
    pagination.value.currentPage = 1
    fetchList()
  }

  /** 重置搜索 */
  const resetSearch = () => {
    searchData.value = {}
    handleSearch()
  }

  /** 打开弹窗 */
  const openDialog = async (config: {
    mode: 'create' | 'edit' | 'view'
    id?: number
    createType?: 'ASN' | 'BLIND'
    notificationId?: number
  }) => {
    const { mode, id, createType, notificationId } = config
    formMode.value = mode
    formLoading.value = true
    try {
      if (mode === 'create') {
        resetForm() // Start with a fresh form
        if (createType === 'BLIND') {
          formData.value.receivingType = 'BLIND'
        } else if (createType === 'ASN' && notificationId) {
          const notification = await getInboundNotificationDetail(notificationId)
          const today = new Date().toISOString().slice(0, 19).replace('T', ' ')
          formData.value = {
            receivingType: 'ASN',
            actualArrivalDate: today,
            notificationId: notification.id,
            notificationNo: notification.notificationNo,
            clientId: notification.clientId,
            warehouseId: notification.warehouseId,
            sourceDocNo: notification.sourceDocNo,
            supplierShipper: notification.supplierShipper,
            details: [(notification.details || []).map(
              (d: WmsInboundNotificationDetailResp): DetailRowData => ({
                id: undefined,
                lineNo: d.lineNo,
                itemId: d.itemId,
                itemSku: d.itemSku,
                itemName: d.itemName,
                expectedQuantity: d.expectedQuantity,
                receivedQuantity: d.expectedQuantity ?? 0,
                unitOfMeasure: d.unitOfMeasure,
                packageQty: d.packageQty,
                packageUnit: d.packageUnit,
                batchNo: d.batchNo,
                productionDate: d.productionDate,
                expiryDate: d.expiryDate,
                remark: d.remark
              })
            )],
            _mode: 'create'
          }
        }
      } else if (id) {
        // mode is 'edit' or 'view'
        const record = await getReceivingRecordDetail(id)
        formData.value = {
          ...record,
          details: [record.details || []],
          receivingType: record.isBlindReceiving ? 'BLIND' : 'ASN',
          actualArrivalDate: record.actualArrivalDate || '',
          _mode: mode
        }
      }
    } finally {
      formLoading.value = false
    }
    dialogVisible.value = true
  }

  const resetForm = () => {
    const today = new Date().toISOString().slice(0, 19).replace('T', ' ')
    formData.value = {
      receivingType: 'ASN',
      actualArrivalDate: today,
      clientId: null,
      warehouseId: null,
      details: [[]],
      _mode: 'create'
    }
  }

  /** 提交表单 */
  const submitForm = async () => {
    formLoading.value = true
    try {
      const data = formData.value
      if (formMode.value === 'create') {
        if (data.receivingType === 'BLIND') {
          const blindData: WmsReceivingRecordBlindCreateReq = {
            actualArrivalDate: data.actualArrivalDate,
            supplementDeadline: data.supplementDeadline,
            clientId: data.clientId!,
            warehouseId: data.warehouseId!,
            supplierShipper: data.supplierShipper,
            sourceDocNo: data.sourceDocNo,
            remark: data.remark,
            details: (data.details[0] || []) as WmsReceivingRecordDetailCreateReq[]
          }
          await createBlindReceivingRecord(blindData)
        } else if (data.notificationId) {
          await createReceivingRecordFromAsn({
            notificationId: data.notificationId,
            defaultReceivingLocationId: 0, // Placeholder, adjust if needed
            remark: data.remark
          })
        }
      } else if (formMode.value === 'edit' && data.id) {
        const updateData = {
          ...data,
          details: data.details[0] || []
        }
        await updateReceivingRecord(data.id, updateData as WmsReceivingRecordUpdateReq)
      }
      dialogVisible.value = false
      await fetchList()
    } finally {
      formLoading.value = false
    }
  }

  /** 删除记录 */
  const handleDelete = async (id: number) => {
    await ElMessageBox.confirm('确定删除该收货记录吗？', '提示', { type: 'warning' })
    await deleteReceivingRecord(id)
    ElMessage.success('删除成功')
    await fetchList()
  }

  /** 更新状态 */
  const handleUpdateStatus = async (id: number, status: WmsReceivingRecordStatus) => {
    await ElMessageBox.confirm(`确定将状态更新为 ${status} 吗？`, '提示', { type: 'warning' })
    await updateReceivingRecordStatus(id, { status })
    ElMessage.success('状态更新成功')
    await fetchList()
  }

  /**
   * 添加新的明细行
   * @returns 新增的明细行对象
   */
  const addDetailRow = () => {
    // Factory for creating a new detail row
    const newRow: DetailRowData = {
      id: undefined, // Let the backend assign ID
      lineNo: 0, // Will be set in the component
      itemSku: '',
      itemName: '',
      expectedQuantity: 0,
      receivedQuantity: 1, // Default to 1
      unitOfMeasure: '',
      batchNo: '',
      productionDate: '',
      expiryDate: '',
      remark: '',
      _isNew: true
    };
    return newRow;
  };

  /** 删除明细行 */
  const removeDetailRow = (index: number) => {
    if (formData.value.details && formData.value.details[0]) {
      formData.value.details[0].splice(index, 1);
      return true;
    }
    return false;
  };

  /** 获取客户选项 */
  const getClientOptions = async (force = false) => {
    if (!force && isCacheValid('clients')) return
    const res = await getClientPageV2({ pageNum: 1, pageSize: 999 })
    clientOptions.value = res.list.map((item) => ({ label: item.name, value: item.id }))
    lastFetchTime.value['clients'] = Date.now()
  }

  /** 获取仓库选项 */
  const getWarehouseOptions = async (force = false) => {
    if (!force && isCacheValid('warehouses')) return
    const response = await getLocationTree()
    warehouseOptions.value = response
    lastFetchTime.value['warehouses'] = Date.now()
  }
  
  /** 获取供应商选项 */
  const getSupplierOptions = async (force = false) => {
    if (!force && isCacheValid('suppliers')) return
    const res = await getSupplierList({ pageSize: 999 })
    supplierOptions.value = res.list.map((item) => ({ label: item.name, value: item.id }))
    lastFetchTime.value['suppliers'] = Date.now()
  }

  const getWarehouseName = (id: number): string | undefined => {
    const findInChildren = (nodes: WmsLocationTreeVO[]): WmsLocationTreeVO | undefined => {
        for (const node of nodes) {
            if (node.id === id) return node;
            if (node.children) {
                const found = findInChildren(node.children);
                if (found) return found;
            }
        }
        return undefined;
    };
    return findInChildren(warehouseOptions.value)?.name;
  }

  // ==================== Pagination ====================
  const handlePageChange = (page: number) => {
    pagination.value.pageNum = page
    fetchList()
  }

  const handleSizeChange = (size: number) => {
    pagination.value.pageSize = size
    fetchList()
  }

  return {
    list,
    total,
    loading,
    dialogVisible,
    formMode,
    isReadonly,
    dialogTitle,
    searchData,
    pagination,
    formData,
    formLoading,
    currentId,
    clientOptions,
    warehouseOptions,
    supplierOptions,
    fetchList,
    handleSearch,
    resetSearch,
    openDialog,
    submitForm,
    handleDelete,
    handleUpdateStatus,
    addDetailRow,
    removeDetailRow,
    getClientOptions,
    getWarehouseOptions,
    getSupplierOptions,
    getWarehouseName,
    handlePageChange,
    handleSizeChange,
    resetForm
  }
})
