package service

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response" // 需要导入 response 包以使用 SortInfo

	"github.com/jinzhu/copier" // 用于结构体复制
)

// OrganizationNodeService 组织节点服务接口
type OrganizationNodeService interface {
	BaseService // 嵌入基础服务接口

	// CreateNode 创建组织节点 (部门或岗位)
	CreateNode(ctx context.Context, req dto.OrganizationNodeCreateOrUpdateDTO) (*vo.OrganizationNodeVO, error)

	// UpdateNode 更新组织节点
	UpdateNode(ctx context.Context, id uint, req dto.OrganizationNodeCreateOrUpdateDTO) (*vo.OrganizationNodeVO, error)

	// DeleteNode 删除组织节点 (需要处理子节点逻辑)
	DeleteNode(ctx context.Context, id uint) error

	// GetNodeByID 获取单个节点信息
	GetNodeByID(ctx context.Context, id uint) (*vo.OrganizationNodeVO, error)

	// GetOrganizationTree 获取指定账套的完整组织树，可选按 CompanyID 过滤
	GetOrganizationTree(ctx context.Context, companyID *uint) ([]*vo.OrganizationNodeTreeVO, error)

	// GetSubTree 获取指定节点下的子树
	GetSubTree(ctx context.Context, nodeID uint) ([]*vo.OrganizationNodeTreeVO, error)

	// GetNodeList 获取节点列表 (可按条件过滤，类似分页，但组织通常不分页)
	GetNodeList(ctx context.Context, query dto.OrganizationNodePageQueryDTO) ([]*vo.OrganizationNodeVO, error)

	// UpdateNodeStatus 更新节点状态
	UpdateNodeStatus(ctx context.Context, id uint, status int) error
}

// organizationNodeServiceImpl 组织节点服务实现
type organizationNodeServiceImpl struct {
	*BaseServiceImpl
}

// NewOrganizationNodeService 创建组织节点服务
func NewOrganizationNodeService(sm *ServiceManager) OrganizationNodeService {
	return &organizationNodeServiceImpl{
		BaseServiceImpl: NewBaseService(sm), // 修正：移除不必要的类型断言
	}
}

// GetOrganizationNodeService 从服务管理器获取组织节点服务
func GetOrganizationNodeService(sm *ServiceManager) OrganizationNodeService {
	return GetService[OrganizationNodeService](
		sm,
		func(sm *ServiceManager) OrganizationNodeService {
			return NewOrganizationNodeService(sm)
		},
	)
}

// getOrganizationNodeRepository 获取组织节点仓库
func (s *organizationNodeServiceImpl) getOrganizationNodeRepository() repository.OrganizationNodeRepository {
	return s.GetServiceManager().GetRepositoryManager().GetOrganizationNodeRepository()
}

// recalculateAndUpdateParentHeadcountsRecursiveInTx 递归地重新计算并更新父节点的标配人数
// parentNodeID: 需要重新计算其标配人数的父节点ID（必须是公司或部门）
func (s *organizationNodeServiceImpl) recalculateAndUpdateParentHeadcountsRecursiveInTx(
	ctx context.Context,
	txServiceManager *ServiceManager, // 传入事务中的 ServiceManager
	parentNodeID uint,
) error {
	log := s.GetLogger()
	txRepo := txServiceManager.GetRepositoryManager().GetOrganizationNodeRepository()
	txDB := txServiceManager.GetRepositoryManager().GetDB() // 获取事务 DB

	// 1. 获取当前父节点信息
	parentNode, err := txRepo.GetNodeWithLock(ctx, parentNodeID) // 加锁确保数据一致性
	if err != nil {
		if errors.Is(err, apperrors.ErrDataNotFound) {
			log.Warn(ctx, "重新计算标配人数：父节点未找到，停止递归", logger.WithField("parentNodeID", parentNodeID))
			return nil // 父节点不存在，可能已被删除，停止递归
		}
		log.Error(ctx, "重新计算标配人数：获取父节点失败", logger.WithError(err), logger.WithField("parentNodeID", parentNodeID))
		return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "获取父节点信息失败").WithCause(err)
	}

	// 2. 仅对公司或部门节点计算标配人数
	if parentNode.NodeType != entity.NodeTypeCompany && parentNode.NodeType != entity.NodeTypeDepartment {
		log.Debug(ctx, "重新计算标配人数：父节点非公司或部门，跳过计算", logger.WithField("parentNodeID", parentNodeID), logger.WithField("nodeType", parentNode.NodeType))
		return nil
	}

	// 3. 获取所有直接子节点
	// 假设 FindByParentID 能够正确地在事务上下文中工作, 或者有一个 txRepo.FindByParentIDInTx(ctx, txDB, &parentNode.ID)
	directChildren, err := txRepo.FindByParentID(ctx, &parentNode.ID)
	if err != nil {
		log.Error(ctx, "重新计算标配人数：获取直接子节点失败", logger.WithError(err), logger.WithField("parentNodeID", parentNodeID))
		return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "获取子节点失败").WithCause(err)
	}

	// 4. 计算新的标配人数
	var newCalculatedHeadcount int = 0
	for _, child := range directChildren {
		if child.NodeType == entity.NodeTypePosition && !child.IsVirtual {
			newCalculatedHeadcount += child.StandardHeadcount // 非虚拟岗位的标配人数
		} else if child.NodeType == entity.NodeTypeDepartment || child.NodeType == entity.NodeTypeCompany {
			// 子部门/子公司的标配人数（假设它们已经是正确的，因为是自底向上更新）
			newCalculatedHeadcount += child.StandardHeadcount
		}
	}

	// 5. 如果计算出的标配人数与存储的不同，则更新
	if parentNode.StandardHeadcount != newCalculatedHeadcount {
		log.Info(ctx, "标配人数变更，执行更新",
			logger.WithField("nodeID", parentNode.ID),
			logger.WithField("oldHeadcount", parentNode.StandardHeadcount),
			logger.WithField("newHeadcount", newCalculatedHeadcount),
		)
		// UpdateStandardHeadcountInTx 是明确的事务性更新方法
		if err := txRepo.UpdateStandardHeadcountInTx(ctx, txDB, parentNode.ID, newCalculatedHeadcount); err != nil {
			log.Error(ctx, "重新计算标配人数：更新父节点标配人数失败", logger.WithError(err), logger.WithField("parentNodeID", parentNodeID))
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "更新父节点标配人数失败").WithCause(err)
		}
		parentNode.StandardHeadcount = newCalculatedHeadcount // 更新内存中的值以便继续递归
	} else {
		log.Debug(ctx, "标配人数未变更，无需更新数据库", logger.WithField("nodeID", parentNode.ID), logger.WithField("headcount", parentNode.StandardHeadcount))
		// 如果当前节点标配人数未变，则其父节点也不应受此分支影响，可以提前结束递归（优化点）
		return nil
	}

	// 6. 如果当前父节点还有父节点，则递归调用
	if parentNode.ParentID != nil && *parentNode.ParentID > 0 {
		// 确保传递的是事务内的 ServiceManager
		return s.recalculateAndUpdateParentHeadcountsRecursiveInTx(ctx, txServiceManager, *parentNode.ParentID)
	}

	return nil
}

// CreateNode 创建组织节点
func (s *organizationNodeServiceImpl) CreateNode(ctx context.Context, req dto.OrganizationNodeCreateOrUpdateDTO) (*vo.OrganizationNodeVO, error) {
	log := s.GetLogger()

	log.Debug(ctx, "创建组织节点开始", logger.WithField("name", req.Name), logger.WithField("type", req.NodeType))

	if req.NodeType != entity.NodeTypeCompany && req.NodeType != entity.NodeTypeDepartment && req.NodeType != entity.NodeTypePosition {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("无效的节点类型: %s", req.NodeType))
	}

	node := &entity.OrganizationNode{}
	if err := copier.Copy(node, &req); err != nil {
		log.Error(ctx, "DTO 复制到实体失败", logger.WithError(err), logger.WithField("req", req))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "节点信息复制失败").WithCause(err)
	}

	var createdNodeID uint
	var parentIDForRecalculation *uint

	txErr := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		txRepoManager := txServiceManager.GetRepositoryManager()
		repo := txRepoManager.GetOrganizationNodeRepository()
		txDB := txRepoManager.GetDB()

		// Set CreatedBy and UpdatedBy
		uid64, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			log.WithError(err).Warn("CreateNode: 无法从上下文中获取UserID")
			// Decide if this should be a fatal error or just a warning
		}
		node.CreatedBy = uint(uid64)
		node.UpdatedBy = uint(uid64)

		if req.ParentID != nil && *req.ParentID == 0 {
			node.ParentID = nil
		} else {
			node.ParentID = req.ParentID
		}

		if node.NodeType == entity.NodeTypeCompany || node.NodeType == entity.NodeTypeDepartment {
			node.StandardHeadcount = 0
		} else if node.NodeType == entity.NodeTypePosition {
			if req.StandardHeadcount == nil {
				node.StandardHeadcount = 0
			} else {
				node.StandardHeadcount = *req.StandardHeadcount
			}
			if req.IsVirtual == nil {
				node.IsVirtual = false
			} else {
				node.IsVirtual = *req.IsVirtual
			}
		}

		if node.ParentID != nil {
			parentNode, err := repo.GetNodeWithLock(ctx, *node.ParentID)
			if err != nil {
				if errors.Is(err, apperrors.ErrDataNotFound) {
					return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, fmt.Sprintf("父节点 [ID=%d] 不存在", *node.ParentID))
				}
				log.WithError(err).Error(ctx, "创建节点时查询父节点失败")
				return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "查询父节点失败").WithCause(err)
			}
			if parentNode.Status != 1 {
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, fmt.Sprintf("父节点 [ID=%d, Name=%s] 未启用，不能添加子节点", parentNode.ID, parentNode.Name))
			}

			node.CompanyID = parentNode.CompanyID

			switch parentNode.NodeType {
			case entity.NodeTypeCompany:
				log.Warn(ctx, entity.NodeTypeCompany)
			// 	if node.NodeType == entity.NodeTypePosition {
			// 		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "不能直接在公司下创建岗位")
			// 	}
			case entity.NodeTypeDepartment:
				if node.NodeType == entity.NodeTypeCompany {
					return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "不能在部门下创建公司")
				}
			case entity.NodeTypePosition:
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, fmt.Sprintf("不能在岗位 [%s] 下创建子节点", parentNode.Name))
			default:
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, fmt.Sprintf("未知的父节点类型: %s", parentNode.NodeType))
			}
		} else {
			if node.NodeType != entity.NodeTypeCompany {
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, fmt.Sprintf("只有 [%s] 类型的节点才能作为顶级节点", entity.NodeTypeCompany))
			}
			node.CompanyID = nil
		}

		if err := repo.CreateInTx(ctx, txDB, node); err != nil {
			log.Error(ctx, "创建组织节点数据库操作失败", logger.WithError(err))
			return err
		}
		createdNodeID = node.ID
		parentIDForRecalculation = node.ParentID

		if node.ParentID == nil && node.NodeType == entity.NodeTypeCompany {
			if node.ID == 0 {
				log.Error(ctx, "创建顶级公司后节点ID为空，无法回填CompanyID")
				return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "创建顶级公司后ID获取失败")
			}
			newCompanyIDVal := node.ID
			node.CompanyID = &newCompanyIDVal
			if err := repo.UpdateCompanyIDForNodeInTx(ctx, txDB, node.ID, &newCompanyIDVal); err != nil {
				log.Error(ctx, "回填顶级公司CompanyID失败", logger.WithError(err), logger.WithField("nodeID", node.ID))
				return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "回填顶级公司CompanyID失败").WithCause(err)
			}
			log.Info(ctx, "顶级公司CompanyID回填成功", logger.WithField("nodeID", node.ID), logger.WithField("companyID", newCompanyIDVal))
		}

		if parentIDForRecalculation != nil && *parentIDForRecalculation > 0 {
			log.Info(ctx, "新节点创建完成，触发父节点标配人数重算", logger.WithField("nodeID", createdNodeID), logger.WithField("parentNodeID", *parentIDForRecalculation))
			if err := s.recalculateAndUpdateParentHeadcountsRecursiveInTx(ctx, txServiceManager, *parentIDForRecalculation); err != nil {
				log.Error(ctx, "创建节点后，在事务中递归更新父节点标配人数失败", logger.WithError(err), logger.WithField("parentNodeID", *parentIDForRecalculation))
				return err
			}
		}
		return nil
	})

	if txErr != nil {
		return nil, txErr
	}

	log.Info(ctx, "创建组织节点成功", logger.WithField("id", createdNodeID))
	finalNode, getErr := s.GetNodeByID(ctx, createdNodeID)
	if getErr != nil {
		log.Error(ctx, "创建后获取节点信息失败", logger.WithError(getErr), logger.WithField("id", createdNodeID))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "创建成功但获取最新节点信息失败").WithCause(getErr)
	}
	return finalNode, nil
}

// UpdateNode 更新组织节点
func (s *organizationNodeServiceImpl) UpdateNode(ctx context.Context, id uint, req dto.OrganizationNodeCreateOrUpdateDTO) (*vo.OrganizationNodeVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "更新组织节点开始", logger.WithField("id", id))

	var oldParentIDValueForRecalc *uint
	var newParentIDValueForRecalc *uint
	var needsRecalculationForOldParent = false
	var needsRecalculationForNewParent = false

	txErr := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		txRepoManager := txServiceManager.GetRepositoryManager()
		repo := txRepoManager.GetOrganizationNodeRepository()
		txDB := txRepoManager.GetDB()

		nodeToUpdate, err := repo.GetNodeWithLock(ctx, id)
		if err != nil {
			log.Error(ctx, "更新前获取节点失败", logger.WithError(err), logger.WithField("id", id))
			if errors.Is(err, apperrors.ErrDataNotFound) {
				return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("待更新的组织节点 [ID=%d] 未找到", id)).WithCause(err)
			}
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "获取待更新节点信息失败").WithCause(err)
		}

		originalParentID := nodeToUpdate.ParentID
		originalCompanyID := nodeToUpdate.CompanyID
		originalStandardHeadcount := nodeToUpdate.StandardHeadcount
		originalIsVirtual := nodeToUpdate.IsVirtual
		originalNodeType := nodeToUpdate.NodeType

		tempNode := &entity.OrganizationNode{}
		if errCopier := copier.Copy(tempNode, nodeToUpdate); errCopier != nil {
			log.Error(ctx, "复制节点信息到临时节点失败", logger.WithError(errCopier), logger.WithField("id", id))
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "复制节点信息失败").WithCause(errCopier)
		}
		if errCopier := copier.Copy(tempNode, &req); errCopier != nil {
			log.Error(ctx, "从DTO复制信息到临时节点失败", logger.WithError(errCopier), logger.WithField("id", id))
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "应用更新信息失败").WithCause(errCopier)
		}
		tempNode.ID = nodeToUpdate.ID

		// Set UpdatedBy
		uid64Update, errUpdate := s.GetUserIDFromContext(ctx)
		if errUpdate != nil {
			log.WithError(errUpdate).Warn("UpdateNode: 无法从上下文中获取UserID")
			// Decide if this should be a fatal error or just a warning
		}
		tempNode.UpdatedBy = uint(uid64Update)

		if req.ParentID != nil {
			if *req.ParentID == 0 {
				tempNode.ParentID = nil
			} else {
				tempNode.ParentID = req.ParentID
			}
		}

		if (tempNode.NodeType == entity.NodeTypeCompany || tempNode.NodeType == entity.NodeTypeDepartment) &&
			req.StandardHeadcount != nil && *req.StandardHeadcount != originalStandardHeadcount {
			log.Warn(ctx, "不允许直接修改公司/部门的标配人数，将忽略DTO中的值", logger.WithField("id", id))
			tempNode.StandardHeadcount = originalStandardHeadcount
		}

		if tempNode.NodeType == entity.NodeTypePosition {
			if req.StandardHeadcount != nil {
				tempNode.StandardHeadcount = *req.StandardHeadcount
			}
			if req.IsVirtual != nil {
				tempNode.IsVirtual = *req.IsVirtual
			}
		}

		parentChanged := (tempNode.ParentID == nil && originalParentID != nil) ||
			(tempNode.ParentID != nil && originalParentID == nil) ||
			(tempNode.ParentID != nil && originalParentID != nil && *tempNode.ParentID != *originalParentID)

		nodeTypeChanged := tempNode.NodeType != originalNodeType

		isPositionPropertyChanged := false
		if tempNode.NodeType == entity.NodeTypePosition && originalNodeType == entity.NodeTypePosition {
			isPositionPropertyChanged = tempNode.StandardHeadcount != originalStandardHeadcount || tempNode.IsVirtual != originalIsVirtual
		}

		if parentChanged {
			if originalParentID != nil && *originalParentID > 0 {
				oldParentIDValueForRecalc = originalParentID
				needsRecalculationForOldParent = true
			}
			if tempNode.ParentID != nil && *tempNode.ParentID > 0 {
				newParentIDValueForRecalc = tempNode.ParentID
				needsRecalculationForNewParent = true
			}
		} else {
			if nodeTypeChanged || isPositionPropertyChanged {
				if originalParentID != nil && *originalParentID > 0 {
					newParentIDValueForRecalc = originalParentID
					needsRecalculationForNewParent = true
				}
			}
		}

		if err := repo.UpdateInTx(ctx, txDB, tempNode, originalParentID, originalCompanyID); err != nil {
			log.Error(ctx, "更新组织节点数据库操作失败", logger.WithError(err), logger.WithField("id", id))
			return err
		}

		if needsRecalculationForOldParent && oldParentIDValueForRecalc != nil && *oldParentIDValueForRecalc > 0 {
			log.Info(ctx, "节点更新，触发旧父节点标配人数重算", logger.WithField("nodeID", id), logger.WithField("oldParentID", *oldParentIDValueForRecalc))
			if errRecalc := s.recalculateAndUpdateParentHeadcountsRecursiveInTx(ctx, txServiceManager, *oldParentIDValueForRecalc); errRecalc != nil {
				log.Error(ctx, "更新节点后，在事务中递归更新旧父节点标配人数失败", logger.WithError(errRecalc))
				return errRecalc
			}
		}

		if needsRecalculationForNewParent && newParentIDValueForRecalc != nil && *newParentIDValueForRecalc > 0 {
			isSameParentAsOldProcessed := needsRecalculationForOldParent && oldParentIDValueForRecalc != nil &&
				newParentIDValueForRecalc != nil && *oldParentIDValueForRecalc == *newParentIDValueForRecalc

			if !isSameParentAsOldProcessed {
				log.Info(ctx, "节点更新，触发新父节点/当前父节点标配人数重算", logger.WithField("nodeID", id), logger.WithField("newOrCurrentParentID", *newParentIDValueForRecalc))
				if errRecalc := s.recalculateAndUpdateParentHeadcountsRecursiveInTx(ctx, txServiceManager, *newParentIDValueForRecalc); errRecalc != nil {
					log.Error(ctx, "更新节点后，在事务中递归更新新父节点/当前父节点标配人数失败", logger.WithError(errRecalc))
					return errRecalc
				}
			}
		}
		return nil
	})

	if txErr != nil {
		return nil, txErr
	}

	log.Info(ctx, "更新组织节点成功", logger.WithField("id", id))
	finalNode, getErr := s.GetNodeByID(ctx, id)
	if getErr != nil {
		log.Error(ctx, "更新后获取节点信息失败", logger.WithError(getErr), logger.WithField("id", id))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "更新成功但获取最新节点信息失败").WithCause(getErr)
	}
	return finalNode, nil
}

// DeleteNode 删除组织节点
func (s *organizationNodeServiceImpl) DeleteNode(ctx context.Context, id uint) error {
	log := s.GetLogger()
	log.Debug(ctx, "删除组织节点开始", logger.WithField("id", id))

	txErr := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		txRepoManager := txServiceManager.GetRepositoryManager()
		repo := txRepoManager.GetOrganizationNodeRepository()
		txDB := txRepoManager.GetDB()

		nodeToDelete, errGet := repo.GetNodeWithLock(ctx, id)
		if errGet != nil {
			if errors.Is(errGet, apperrors.ErrDataNotFound) {
				log.Warn(ctx, "尝试在事务中删除的节点未找到", logger.WithField("id", id))
				return nil
			}
			log.Error(ctx, "删除节点前在事务中获取节点信息失败", logger.WithError(errGet), logger.WithField("id", id))
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "获取待删除节点信息失败").WithCause(errGet)
		}
		nodeToDeleteParentID := nodeToDelete.ParentID

		children, errChildren := repo.FindByParentID(ctx, &id)
		if errChildren != nil {
			log.Error(ctx, "事务中查询子节点失败（删除前检查）", logger.WithError(errChildren), logger.WithField("parentID", id))
			return apperrors.WrapError(errChildren, apperrors.CODE_DATA_QUERY_FAILED, "检查子节点时出错")
		}
		if len(children) > 0 {
			log.Warn(ctx, "无法删除包含子节点的组织节点（事务内检查）", logger.WithField("id", id), logger.WithField("childCount", len(children)))
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "无法删除包含子节点的组织节点")
		}

		if err := repo.DeleteInTx(ctx, txDB, id); err != nil {
			log.Error(ctx, "删除组织节点数据库操作失败", logger.WithError(err), logger.WithField("id", id))
			return err
		}

		if nodeToDeleteParentID != nil && *nodeToDeleteParentID > 0 {
			log.Info(ctx, "节点删除完成，触发父节点标配人数重算", logger.WithField("deletedNodeID", id), logger.WithField("parentNodeID", *nodeToDeleteParentID))
			if errRecalc := s.recalculateAndUpdateParentHeadcountsRecursiveInTx(ctx, txServiceManager, *nodeToDeleteParentID); errRecalc != nil {
				log.Error(ctx, "删除节点后，在事务中递归更新父节点标配人数失败", logger.WithError(errRecalc), logger.WithField("parentNodeID", *nodeToDeleteParentID))
				return errRecalc
			}
		}
		return nil
	})

	if txErr != nil {
		return txErr
	}

	log.Info(ctx, "删除组织节点成功", logger.WithField("id", id))
	return nil
}

// GetNodeByID 获取单个节点信息
func (s *organizationNodeServiceImpl) GetNodeByID(ctx context.Context, id uint) (*vo.OrganizationNodeVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "根据 ID 获取组织节点", logger.WithField("id", id))
	repo := s.getOrganizationNodeRepository()
	node, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return s.convertToNodeVO(node), nil
}

// GetOrganizationTree 获取指定账套的完整组织树，可选按 CompanyID 过滤
func (s *organizationNodeServiceImpl) GetOrganizationTree(ctx context.Context, companyID *uint) ([]*vo.OrganizationNodeTreeVO, error) {
	log := s.GetLogger()

	debugMsg := "获取组织树"
	logKeyValues := []interface{}{}
	if companyID != nil && *companyID > 0 {
		logKeyValues = append(logKeyValues, "filterCompanyID", *companyID)
	}

	finalLogArgsForDebug := []interface{}{debugMsg}
	finalLogArgsForDebug = append(finalLogArgsForDebug, logKeyValues...)
	log.Debug(finalLogArgsForDebug...)

	conditions := []repository.Condition{}
	if companyID != nil && *companyID > 0 {
		conditions = append(conditions, repository.NewEqualCondition("company_id", *companyID))
	}

	repo := s.getOrganizationNodeRepository()

	queryConditions := make([]repository.QueryCondition, len(conditions))
	for i, c := range conditions {
		queryConditions[i] = c
	}
	sortInfos := []response.SortInfo{{Field: "order_num", Order: "asc"}}

	nodes, err := repo.FindByCondition(ctx, queryConditions, sortInfos)
	if err != nil {
		log.Error(ctx, "查询所有组织节点失败", logger.WithError(err))
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取组织数据失败").WithCause(err)
	}

	tree := s.buildTree(nodes)
	return tree, nil
}

// GetSubTree 获取指定节点下的子树
func (s *organizationNodeServiceImpl) GetSubTree(ctx context.Context, nodeID uint) ([]*vo.OrganizationNodeTreeVO, error) {
	log := s.GetLogger()
	log.Debug("获取组织子树", "rootNodeID", nodeID)

	repo := s.getOrganizationNodeRepository()
	rootNode, err := repo.FindByID(ctx, nodeID)
	if err != nil {
		return nil, err
	}

	conditions := []repository.Condition{
		repository.NewEqualCondition("company_id", rootNode.CompanyID),
	}

	queryConditions := make([]repository.QueryCondition, len(conditions))
	for i, c := range conditions {
		queryConditions[i] = c
	}
	sortInfos := []response.SortInfo{{Field: "order_num", Order: "asc"}}

	allNodes, err := repo.FindByCondition(ctx, queryConditions, sortInfos)
	if err != nil {
		log.Error(ctx, "查询公司下所有组织节点失败", logger.WithError(err), logger.WithField("companyID", rootNode.CompanyID))
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取组织数据失败").WithCause(err)
	}

	fullTreeMap := s.buildTreeMap(allNodes)

	var subTree []*vo.OrganizationNodeTreeVO
	if rootTreeVO, ok := fullTreeMap[nodeID]; ok {
		subTree = rootTreeVO.Children
	} else {
		subTree = []*vo.OrganizationNodeTreeVO{}
	}

	return subTree, nil
}

// GetNodeList 获取节点列表
func (s *organizationNodeServiceImpl) GetNodeList(ctx context.Context, query dto.OrganizationNodePageQueryDTO) ([]*vo.OrganizationNodeVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "获取组织节点列表", logger.WithField("query", fmt.Sprintf("%+v", query)))

	repoConditions := s.buildOrgNodeQueryConditions(query)
	repo := s.getOrganizationNodeRepository()

	queryConditions := make([]repository.QueryCondition, len(repoConditions))
	for i, c := range repoConditions {
		queryConditions[i] = c
	}

	var sortInfos []response.SortInfo
	sortField := "order_num"
	sortOrder := "asc"
	if query.SortField != "" {
		sortField = query.SortField
	}
	if query.SortOrder != "" {
		sortOrder = query.SortOrder
	}
	sortInfos = append(sortInfos, response.SortInfo{Field: sortField, Order: sortOrder})

	nodes, err := repo.FindByCondition(ctx, queryConditions, sortInfos)
	if err != nil {
		log.Error(ctx, "查询组织节点列表失败", logger.WithError(err))
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询组织节点列表失败").WithCause(err)
	}

	return s.convertToNodeVOList(nodes), nil
}

// UpdateNodeStatus 更新节点状态
func (s *organizationNodeServiceImpl) UpdateNodeStatus(ctx context.Context, id uint, status int) error {
	log := s.GetLogger()
	log.Debug(ctx, "更新组织节点状态", logger.WithField("id", id), logger.WithField("status", status))

	repo := s.getOrganizationNodeRepository()

	if err := repo.UpdateStatus(ctx, id, status); err != nil {
		log.Error(ctx, "更新组织节点状态数据库操作失败", logger.WithError(err))
		return err
	}

	log.Info(ctx, "更新组织节点状态成功", logger.WithField("id", id))
	return nil
}

// --- 辅助方法 --- //

func (s *organizationNodeServiceImpl) buildTree(nodes []*entity.OrganizationNode) []*vo.OrganizationNodeTreeVO {
	treeMap := s.buildTreeMap(nodes)

	var rootNodes []*vo.OrganizationNodeTreeVO
	for _, nodeVO := range treeMap {
		if nodeVO.ParentID == 0 {
			rootNodes = append(rootNodes, nodeVO)
		}
	}

	sort.Slice(rootNodes, func(i, j int) bool {
		return rootNodes[i].OrderNum < rootNodes[j].OrderNum
	})

	return rootNodes
}

func (s *organizationNodeServiceImpl) buildTreeMap(nodes []*entity.OrganizationNode) map[uint]*vo.OrganizationNodeTreeVO {
	nodeMap := make(map[uint]*vo.OrganizationNodeTreeVO)

	for _, node := range nodes {
		treeVO := &vo.OrganizationNodeTreeVO{
			OrganizationNodeVO: *s.convertToNodeVO(node),
			Children:           []*vo.OrganizationNodeTreeVO{},
		}
		nodeMap[node.ID] = treeVO
	}

	for _, node := range nodes {
		if node.ParentID != nil {
			if parentVO, ok := nodeMap[*node.ParentID]; ok {
				childVO := nodeMap[node.ID]
				parentVO.Children = append(parentVO.Children, childVO)
			}
		}
	}

	for _, nodeVO := range nodeMap {
		sort.Slice(nodeVO.Children, func(i, j int) bool {
			return nodeVO.Children[i].OrderNum < nodeVO.Children[j].OrderNum
		})
	}

	return nodeMap
}

func (s *organizationNodeServiceImpl) convertToNodeVO(node *entity.OrganizationNode) *vo.OrganizationNodeVO {
	if node == nil {
		return nil
	}
	voNode := &vo.OrganizationNodeVO{}

	voNode.ID = node.ID
	voNode.NodeType = node.NodeType
	voNode.Name = node.Name
	voNode.Code = node.Code
	voNode.LeaderUserID = node.LeaderUserID
	voNode.Level = node.Level
	voNode.OrderNum = node.OrderNum
	voNode.Weight = node.Weight
	voNode.IsVirtual = node.IsVirtual

	shc := node.StandardHeadcount
	voNode.StandardHeadcount = &shc

	voNode.Status = node.Status
	voNode.Remarks = node.Remarks
	voNode.CreatedAt = node.CreatedAt
	voNode.UpdatedAt = node.UpdatedAt
	voNode.CreatedBy = node.CreatedBy
	voNode.UpdatedBy = node.UpdatedBy

	if node.ParentID != nil {
		voNode.ParentID = *node.ParentID
	} else {
		voNode.ParentID = 0
	}

	return voNode
}

func (s *organizationNodeServiceImpl) convertToNodeVOList(nodes []*entity.OrganizationNode) []*vo.OrganizationNodeVO {
	voList := make([]*vo.OrganizationNodeVO, 0, len(nodes))
	for _, node := range nodes {
		voList = append(voList, s.convertToNodeVO(node))
	}
	return voList
}

func (s *organizationNodeServiceImpl) buildOrgNodeQueryConditions(query dto.OrganizationNodePageQueryDTO) []repository.Condition {
	conditions := []repository.Condition{}
	if query.Name != "" {
		conditions = append(conditions, repository.NewLikeCondition("name", query.Name))
	}
	if query.Code != "" {
		conditions = append(conditions, repository.NewLikeCondition("code", query.Code))
	}
	if query.NodeType != "" {
		conditions = append(conditions, repository.NewEqualCondition("node_type", query.NodeType))
	}
	if query.Status != nil {
		conditions = append(conditions, repository.NewEqualCondition("status", *query.Status))
	}

	if query.ParentID != nil {
		if *query.ParentID == 0 {
			conditions = append(conditions, repository.NewEqualCondition("parent_id", nil))
		} else {
			conditions = append(conditions, repository.NewEqualCondition("parent_id", *query.ParentID))
		}
	}
	return conditions
}

func (s *organizationNodeServiceImpl) validateParentNode(ctx context.Context, currentNodeID, newParentID uint) error {
	if currentNodeID == newParentID {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "不能将节点设置为自身的父节点")
	}
	repo := s.getOrganizationNodeRepository()

	parentIDToValidate := newParentID
	for {
		parent, err := repo.FindByID(ctx, parentIDToValidate)
		if err != nil {
			if errors.Is(err, apperrors.ErrDataNotFound) {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("指定的父节点ID [%d] 不存在", newParentID))
			}
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "验证父节点时出错").WithCause(err)
		}
		if parent.ID == currentNodeID {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "不能将节点移动到其子孙节点下")
		}
		if parent.ParentID == nil {
			break
		}
		parentIDToValidate = *parent.ParentID
		if parentIDToValidate == 0 {
			break
		}
	}
	return nil
}
