<template>
  <div 
    class="vn-status-bar"
    :style="{
      height: height,
      backgroundColor: backgroundColor,
      color: textColor
    }"
  >
    <div class="status-bar-section left">
      <span v-if="leftTitle" class="section-title">{{ leftTitle }}</span>
      <template v-for="(item, index) in leftItems" :key="item.id">
        <StatusBarItemRenderer :item="item" />
        <span 
          v-if="shouldShowSeparator(item, index, leftItems)"
          class="separator"
        >
          <component v-if="typeof separator === 'object'" :is="separator" />
          <template v-else>{{ separator }}</template>
        </span>
      </template>
    </div>
    <div class="status-bar-section center">
       <span v-if="centerTitle" class="section-title">{{ centerTitle }}</span>
      <template v-for="(item, index) in centerItems" :key="item.id">
        <StatusBarItemRenderer :item="item" />
        <span 
          v-if="shouldShowSeparator(item, index, centerItems)"
          class="separator"
        >
          <component v-if="typeof separator === 'object'" :is="separator" />
          <template v-else>{{ separator }}</template>
        </span>
      </template>
    </div>
    <div class="status-bar-section right">
       <span v-if="rightTitle" class="section-title">{{ rightTitle }}</span>
      <template v-for="(item, index) in rightItems" :key="item.id">
        <StatusBarItemRenderer :item="item" />
        <span 
          v-if="shouldShowSeparator(item, index, rightItems)"
          class="separator"
        >
          <component v-if="typeof separator === 'object'" :is="separator" />
          <template v-else>{{ separator }}</template>
        </span>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, withDefaults, computed, defineComponent, h } from 'vue';
import { ElIcon, ElTooltip } from 'element-plus';
import type { StatusBarItem, VNStatusBarProps } from './types';

const props = withDefaults(defineProps<VNStatusBarProps>(), {
  height: '30px',
  backgroundColor: '#f5f5f5',
  textColor: '#606266',
  separator: '|',
  leftTitle: '',
  centerTitle: '',
  rightTitle: ''
});

// Function to determine if separator should be shown
const shouldShowSeparator = (item: StatusBarItem, index: number, list: StatusBarItem[]) => {
  // Don't show separator if item explicitly requests it (separator: false)
  if (item.separator === false) {
    return false;
  }
  // Don't show separator after the last item in the section
  return index < list.length - 1;
};

// Internal component to render each status bar item
const StatusBarItemRenderer = defineComponent({
  props: {
    item: {
      type: Object as () => StatusBarItem,
      required: true
    }
  },
  setup(props) {
    const item = props.item;

    const renderContent = () => {
      if (item.component) {
        return h(item.component, item.componentProps || {});
      }

      const nodes = [];
      if (item.icon) {
        nodes.push(h(ElIcon, { class: 'status-icon', size: 14 }, () => h(item.icon as any)));
      }
      if (item.label) {
        nodes.push(h('span', { class: 'status-label' }, item.label + (item.value !== undefined ? ': ' : '')));
      }
      if (item.value !== undefined) {
        if (typeof item.value === 'object' && item.value !== null && '__v_isVNode' in item.value) {
          // Render VNode directly
          nodes.push(item.value);
        } else {
          nodes.push(h('span', { class: 'status-value' }, String(item.value)));
        }
      }
      return nodes;
    };

    const itemElement = h(
      'span',
      {
        class: 'status-item',
        onClick: item.onClick,
        style: { cursor: item.onClick ? 'pointer' : 'default' }
      },
      renderContent()
    );

    if (item.tooltip) {
      return () => h(ElTooltip, { content: item.tooltip, placement: 'top' }, () => itemElement);
    } else {
      return () => itemElement;
    }
  }
});

// Computed properties to filter items based on alignment
const leftItems = computed(() => props.items.filter(item => !item.align || item.align === 'left'));
const centerItems = computed(() => props.items.filter(item => item.align === 'center'));
const rightItems = computed(() => props.items.filter(item => item.align === 'right'));

</script>

<style scoped>
.vn-status-bar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 5px 10px;
  font-size: 12px;
  border-top: 1px solid #e0e0e0;
  box-sizing: border-box;
  flex-wrap: wrap;
  gap: 10px;
  background-color: v-bind(backgroundColor);
  color: v-bind(textColor);
  min-height: v-bind(height);
}

.status-bar-section {
  display: flex;
  align-items: center;
  flex-shrink: 1;
  flex-basis: auto;
  min-width: 0;
  flex-wrap: wrap;
  gap: 5px 10px;
}

.status-bar-section.left {
  justify-content: flex-start;
}

.status-bar-section.center {
  justify-content: center;
  margin: 0;
  overflow: visible;
}

.status-bar-section.right {
  justify-content: flex-end;
  margin-left: auto;
}

.section-title {
  font-weight: bold;
  margin-right: 8px;
  color: var(--el-text-color-secondary);
}

.status-item {
  display: inline-flex;
  align-items: center;
}

.separator {
  color: #dcdfe6;
  display: inline-flex;
  align-items: center;
}

.status-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.status-label {
  margin-right: 2px;
}

.status-value {
  font-weight: 500;
}

.status-item[style*="cursor: pointer"]:hover {
  opacity: 0.8;
}

@media (max-width: 767px) { 
  .vn-status-bar {
      display: flex !important;
      flex-direction: column !important;
      align-items: flex-start !important;
      gap: 8px !important;
      min-height: 0 !important;
      height: auto !important;
  }
  
  .status-bar-section {
      width: 100% !important;
      justify-content: flex-start !important;
      flex-wrap: wrap;
      gap: 5px 8px;
      margin-left: 0 !important;
      flex-grow: 0 !important;
      flex-shrink: 0 !important;
      flex-basis: auto !important;
  }
}
</style> 