import type { FormRules } from 'element-plus';

/**
 * Login component properties.
 */
export interface VNLoginProps {
  /**
   * Initial username value.
   * @default ''
   */
  initialUsername?: string;
  /**
   * Whether the login process is in loading state.
   * @default false
   */
  loading?: boolean;
  /**
   * Custom validation rules for the form.
   * Overrides default required rules.
   */
  rules?: FormRules;
  /**
   * Whether to show the 'Remember Me' checkbox.
   * @default true
   */
  showRememberMe?: boolean;
    /**
   * Whether to show the 'Forgot Password' link.
   * @default false 
   */
  showForgotPassword?: boolean;
  /**
   * Whether to show the captcha section.
   * @default false
   */
  showCaptcha?: boolean;
  /**
   * URL for the captcha image.
   * Required if showCaptcha is true.
   * @default ''
   */
  captchaImageUrl?: string;
  /**
   * Whether to show the alternative login methods section (slot).
   * The section will only render if this is true AND the slot is provided.
   * @default false
   */
  showAlternativeLogins?: boolean;

  // --- Left Panel Props ---
  /**
   * Whether to show the left information panel.
   * @default true
   */
  showLeftPanel?: boolean;
  /**
   * Background image URL for the left panel.
   * Provide a default image path or keep empty.
   * @default '' // Or path to a default image
   */
  leftPanelBackgroundImageUrl?: string;
   /**
   * System title displayed in the left panel (used if #left-panel-header slot is not provided).
   * @default '后台管理系统'
   */
  systemTitle?: string;
  /**
   * Version number displayed in the left panel (used if #left-panel-footer slot is not provided).
   * @default ''
   */
  version?: string;
  /**
   * URL for the company logo in the left panel (used if #left-panel-header slot is not provided).
   * @default ''
   */
  logoUrl?: string;
    /**
   * Alt text for the company logo.
   * @default 'Logo'
   */
  logoAlt?: string;
}

/**
 * Login form data structure.
 */
export interface LoginFormModel {
  username: string;
  password: string;
  rememberMe: boolean;
  captchaCode: string;
}

/**
 * Login component emitted events.
 */
export interface VNLoginEmits {
  /**
   * Emitted when the login button is clicked and form validation passes.
   * @param model The login form data.
   */
  (e: 'login', model: LoginFormModel): void;
  /**
   * Emitted when the 'Forgot Password' link is clicked.
   */
  (e: 'forgot-password'): void;
  /**
   * Emitted when the user requests to refresh the captcha.
   */
  (e: 'refresh-captcha'): void;
  /**
   * Emitted when the Mobile Login button/icon is clicked.
   */
  (e: 'mobile-login-click'): void;
  /**
   * Emitted when the DingTalk Login button/icon is clicked.
   */
  (e: 'dingtalk-login-click'): void;
} 