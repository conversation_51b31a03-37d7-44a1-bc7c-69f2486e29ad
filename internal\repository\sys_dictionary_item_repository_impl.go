package repository

import (
	"backend/internal/model/dto" // 用于查询 DTO
	"backend/internal/model/entity"
	"context"
	"errors" // For gorm.ErrRecordNotFound

	// For error wrapping, added this import
	// For error wrapping
	// For sort order check

	apperrors "backend/pkg/errors" // Assuming custom error package alias

	"gorm.io/gorm"
	// BaseRepository and NewBaseRepository are assumed to be defined in this package
)

// DictionaryItemRepository 字典项仓库接口
type DictionaryItemRepository interface {
	// 嵌入基础 CRUD
	BaseRepository[entity.DictionaryItem, uint]

	// 根据条件查询
	// FindByCondition(ctx context.Context, conditions []interface{}, orderBy string, orderDirection string) ([]*entity.DictionaryItem, error) // 移除与 BaseRepository 冲突的方法
	CountByCondition(ctx context.Context, conditions []QueryCondition) (int64, error) // 修改参数类型

	// 特殊查询
	FindByTypeID(ctx context.Context, typeID uint, onlyEnabled bool) ([]*entity.DictionaryItem, error)       // 根据类型ID查询项 (按 SortOrder, ID 排序)
	FindByTypeCode(ctx context.Context, typeCode string, onlyEnabled bool) ([]*entity.DictionaryItem, error) // 根据类型Code查询项 (需要关联查询或分步)
	CountByTypeID(ctx context.Context, typeID uint) (int64, error)                                           // 统计类型下的项数量
	FindPage(ctx context.Context, query dto.DictItemPageQueryDTO) ([]*entity.DictionaryItem, int64, error)   // 分页查询
	ExistsByValue(ctx context.Context, typeID uint, value string, excludeID uint) (bool, error)              // 检查同一类型下值是否存在 (excludeID 用于更新时排除自身)
	ExistsByLabel(ctx context.Context, typeID uint, label string, excludeID uint) (bool, error)              // 检查同一类型下标签是否存在 (excludeID 用于更新时排除自身)
}

// --- Implementation ---

type dictionaryItemRepository struct {
	BaseRepository[entity.DictionaryItem, uint]
	// db *gorm.DB // 移除多余的 db 字段
}

// NewDictionaryItemRepository 创建字典项仓库实例
func NewDictionaryItemRepository(db *gorm.DB) DictionaryItemRepository {
	baseRepo := NewBaseRepository[entity.DictionaryItem, uint](db)
	return &dictionaryItemRepository{
		BaseRepository: baseRepo,
	}
}

// getDB 获取带上下文的 DB 连接
func (r *dictionaryItemRepository) getDB(ctx context.Context) *gorm.DB {
	return r.BaseRepository.GetDB(ctx) // 直接调用嵌入的 BaseRepository 的 GetDB 方法
}

// CountByCondition 实现计数逻辑
func (r *dictionaryItemRepository) CountByCondition(ctx context.Context, conditions []QueryCondition) (int64, error) {
	var total int64
	db := r.getDB(ctx).Model(&entity.DictionaryItem{}) // 指定模型
	db = ApplyConditions(db, conditions)               // 调用 base_repository.go 中的 ApplyConditions (已导出)

	if err := db.Count(&total).Error; err != nil {
		return 0, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "按条件统计字典项数量失败")
	}
	return total, nil
}

// FindByCondition (Delegates to embedded BaseRepository)
// 移除此实现，因为它与 BaseRepository 冲突且签名不一致
// func (r *dictionaryItemRepository) FindByCondition(ctx context.Context, conditions []interface{}, sortField, sortOrder string) ([]*entity.DictionaryItem, error) {
// 	// Directly call the embedded BaseRepository's implementation
// 	return r.BaseRepository.FindByCondition(ctx, conditions, sortField, sortOrder)
// }

// FindByTypeID 根据类型ID查询项 (按 SortOrder, ID 排序)
func (r *dictionaryItemRepository) FindByTypeID(ctx context.Context, typeID uint, onlyEnabled bool) ([]*entity.DictionaryItem, error) {
	var items []*entity.DictionaryItem
	db := r.getDB(ctx).Where("dictionary_type_id = ?", typeID)
	if onlyEnabled {
		db = db.Where("status = ?", 1)
	}
	// 默认按 SortOrder 升序, ID 升序排序
	if err := db.Order("sort_order asc, id asc").Find(&items).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*entity.DictionaryItem{}, nil
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "按类型ID查询字典项失败")
	}
	return items, nil
}

// FindByTypeCode 根据类型Code查询项 (需要关联查询或分步)
// 实现方式1：分步查询 (更简单，但多一次 DB 查询)
func (r *dictionaryItemRepository) FindByTypeCode(ctx context.Context, typeCode string, onlyEnabled bool) ([]*entity.DictionaryItem, error) {
	// 1. 先根据 typeCode 查找 DictionaryType
	var dictType entity.DictionaryType
	if err := r.getDB(ctx).Model(&entity.DictionaryType{}).Where("code = ?", typeCode).First(&dictType).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*entity.DictionaryItem{}, nil // 类型不存在，返回空
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询字典类型失败")
	}
	// 2. 再根据找到的 typeID 查询 Items
	return r.FindByTypeID(ctx, dictType.ID, onlyEnabled)
}

/* // 实现方式2：使用 Join 查询 (更高效，但略复杂)
func (r *dictionaryItemRepository) FindByTypeCode(ctx context.Context, typeCode string, onlyEnabled bool) ([]*entity.DictionaryItem, error) {
	var items []*entity.DictionaryItem
	db := r.getDB(ctx).Model(&entity.DictionaryItem{}).
		Joins("JOIN sys_dictionary_type ON sys_dictionary_type.id = sys_dictionary_item.dictionary_type_id").
		Where("sys_dictionary_type.code = ?", typeCode)

	if onlyEnabled {
		db = db.Where("sys_dictionary_item.status = ?", 1)
	}

	// Select specific columns from DictionaryItem to avoid ambiguity
	if err := db.Select("sys_dictionary_item.*"`).Order("sys_dictionary_item.sort_order asc, sys_dictionary_item.id asc").Find(&items).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*entity.DictionaryItem{}, nil
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "按类型编码查询字典项失败")
	}
	return items, nil
}
*/

// CountByTypeID 统计类型下的项数量
func (r *dictionaryItemRepository) CountByTypeID(ctx context.Context, typeID uint) (int64, error) {
	var count int64
	db := r.getDB(ctx).Model(&entity.DictionaryItem{}).Where("dictionary_type_id = ?", typeID)
	if err := db.Count(&count).Error; err != nil {
		return 0, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "统计类型下字典项数量失败")
	}
	return count, nil
}

// FindPage 分页查询字典项 (使用 BaseRepository.FindByPage)
func (r *dictionaryItemRepository) FindPage(ctx context.Context, query dto.DictItemPageQueryDTO) ([]*entity.DictionaryItem, int64, error) {
	// 1. 将 DTO 中的过滤条件转换为 []QueryCondition
	var conditions []QueryCondition
	if query.DictionaryTypeID != nil {
		conditions = append(conditions, NewEqualCondition("dictionary_type_id", *query.DictionaryTypeID))
	}
	if query.Label != "" {
		conditions = append(conditions, NewLikeCondition("label", query.Label))
	}
	if query.Value != "" {
		// 假设值是精确匹配，如果需要模糊匹配，使用 NewLikeCondition
		conditions = append(conditions, NewEqualCondition("value", query.Value))
	}
	if query.Status != nil {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}

	// 2. 调用 BaseRepository.FindByPage
	// query.PageQuery 已经包含了排序信息，BaseRepository.FindByPage 会处理它
	// 假设 entity.DictionaryItem 的字段有正确的 'sortable:"true"' 和 'json' 标签
	pageResult, err := r.BaseRepository.FindByPage(ctx, &query.PageQuery, conditions)
	if err != nil {
		// BaseRepository.FindByPage 内部已处理错误日志和包装
		return nil, 0, err
	}

	// 类型断言
	items, ok := pageResult.List.([]*entity.DictionaryItem)
	if !ok {
		logger := r.BaseRepository.GetDB(ctx).Logger // Or global logger
		logger.Error(ctx, "分页结果类型断言失败，期望 []*entity.DictionaryItem，实际得到 %T", pageResult.List)
		return nil, 0, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "分页结果类型断言失败")
	}

	return items, pageResult.Total, nil
}

// ExistsByValue 检查同一类型下值是否存在 (excludeID 用于更新时排除自身)
func (r *dictionaryItemRepository) ExistsByValue(ctx context.Context, typeID uint, value string, excludeID uint) (bool, error) {
	var count int64
	db := r.getDB(ctx).Model(&entity.DictionaryItem{}).
		Where("dictionary_type_id = ?", typeID).
		Where("value = ?", value)
	if excludeID > 0 {
		db = db.Where("id <> ?", excludeID)
	}
	if err := db.Count(&count).Error; err != nil {
		return false, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "检查字典值唯一性失败")
	}
	return count > 0, nil
}

// ExistsByLabel 检查同一类型下标签是否存在 (excludeID 用于更新时排除自身)
func (r *dictionaryItemRepository) ExistsByLabel(ctx context.Context, typeID uint, label string, excludeID uint) (bool, error) {
	var count int64
	db := r.getDB(ctx).Model(&entity.DictionaryItem{}).
		Where("dictionary_type_id = ?", typeID).
		Where("label = ?", label)
	if excludeID > 0 {
		db = db.Where("id <> ?", excludeID)
	}
	if err := db.Count(&count).Error; err != nil {
		return false, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "检查字典标签唯一性失败")
	}
	return count > 0, nil
}
