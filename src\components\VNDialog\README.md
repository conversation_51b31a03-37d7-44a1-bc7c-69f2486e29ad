# VNDialog 组件

一个基于 Element Plus `el-dialog` 封装的通用对话框组件，简化了 `v-model` 控制，并提供了常用的插槽和默认按钮选项。

## Props

继承了 `el-dialog` 的大部分 Props (除了 `modelValue`)，并将它们设为可选。常用的包括：

*   `title` (string): 对话框标题。
*   `width` (string | number): 对话框宽度，默认 `'50%'`。
*   `fullscreen` (boolean): 是否为全屏对话框。
*   `top` (string): Dialog CSS 中的 margin-top 值，默认 `'15vh'`。
*   `modal` (boolean): 是否需要遮罩层，默认 `true`。
*   `append-to-body` (boolean): Dialog 自身是否插入至 body 元素上。
*   `lock-scroll` (boolean): 是否在 Dialog 出现时将 body 滚动锁定。
*   `close-on-click-modal` (boolean): 是否可以通过点击 modal 关闭 Dialog，默认 `true`。
*   `close-on-press-escape` (boolean): 是否可以通过按下 ESC 关闭 Dialog，默认 `true`。
*   `show-close` (boolean): 是否显示关闭按钮，默认 `true`。
*   `draggable` (boolean): 是否可拖拽。
*   `center` (boolean): 是否对头部和底部采用居中布局。
*   `destroy-on-close` (boolean): 关闭时销毁 Dialog 中的元素。

**自定义 Props:**

| Prop Name              | Type      | Description                | Default   |
| ---------------------- | --------- | -------------------------- | --------- |
| `modelValue`           | `boolean` | 控制对话框显示与隐藏 (v-model) | `false`   |
| `showConfirmButton`    | `boolean` | 是否显示默认的确认按钮       | `true`    |
| `showCancelButton`     | `boolean` | 是否显示默认的取消按钮       | `true`    |
| `confirmButtonText`    | `string`  | 确认按钮文字               | `'确认'`   |
| `cancelButtonText`     | `string`  | 取消按钮文字               | `'取消'`   |
| `confirmButtonLoading` | `boolean` | 确认按钮加载状态           | `false`   |

## Emits

*   `update:modelValue(value: boolean)`: `v-model` 值改变时触发。
*   `open`: Dialog 打开的回调。
*   `opened`: Dialog 打开动画结束时的回调。
*   `close`: Dialog 关闭的回调。
*   `closed`: Dialog 关闭动画结束时的回调。
*   `confirm`: 点击默认确认按钮时触发。 **注意：默认确认按钮不会自动关闭对话框，需要在父组件监听此事件后手动设置 `modelValue` 为 `false`。**
*   `cancel`: 点击默认取消按钮或右上角关闭图标时触发 (默认行为会同时关闭对话框)。

## Slots

*   `default`: 对话框主体内容区域。
*   `header`: 自定义对话框标题区域。如果使用此插槽，`title` prop 将被忽略。
*   `footer`: 自定义对话框底部区域。如果使用此插槽，默认的确认和取消按钮将不会显示。

## 使用示例

```vue
<template>
  <div>
    <el-button @click="showBasicDialog = true">打开基础对话框</el-button>
    <el-button @click="showCustomFooterDialog = true">打开自定义底部对话框</el-button>

    <VNDialog 
      v-model="showBasicDialog"
      title="基础对话框"
      @confirm="handleBasicConfirm"
      @cancel="handleBasicCancel"
    >
      <p>这是一段对话框内容。</p>
    </VNDialog>

    <VNDialog
      v-model="showCustomFooterDialog"
      title="自定义底部"
      :show-confirm-button="false" 
      :show-cancel-button="false"
    >
      <p>这个对话框使用了自定义的 footer 插槽。</p>
      <template #footer>
        <el-button @click="showCustomFooterDialog = false">自定义关闭</el-button>
        <el-button type="success" @click="handleCustomAction">自定义操作</el-button>
      </template>
    </VNDialog>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNDialog from '@/components/VNDialog/index.vue';
import { ElButton, ElMessage } from 'element-plus';

const showBasicDialog = ref(false);
const showCustomFooterDialog = ref(false);

const handleBasicConfirm = () => {
  ElMessage.success('点击了确认');
  showBasicDialog.value = false; // 手动关闭
};

const handleBasicCancel = () => {
  ElMessage.info('点击了取消或关闭');
  // 对话框已在内部处理关闭
};

const handleCustomAction = () => {
  ElMessage.warning('执行了自定义操作');
  showCustomFooterDialog.value = false;
};
</script>
```

## 注意事项

*   组件基于 `element-plus` 的 `ElDialog` 和 `ElButton`。
*   使用 `v-model` 控制对话框的显示和隐藏。
*   监听 `@confirm` 事件处理确认逻辑，并 **记得在处理函数中将 `v-model` 的值设为 `false`** 来关闭对话框。
*   监听 `@cancel` 事件可以处理取消或点击关闭图标的逻辑（对话框默认会关闭）。
*   可以通过 `showConfirmButton` 和 `showCancelButton` props 控制是否显示默认按钮。
*   使用 `#footer` 插槽可以完全自定义底部内容。 