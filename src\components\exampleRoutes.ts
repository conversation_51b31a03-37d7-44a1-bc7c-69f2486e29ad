import type { RouteRecordRaw } from 'vue-router'

// All component example routes are defined here
export const exampleRoutes: RouteRecordRaw[] = [
  {
    path: '/vntable-example',
    name: 'VNTableExample',
    component: () => import('./VNTable/example.vue')
  },
  {
    path: '/vnform',
    name: 'VNFormExample',
    component: () => import('./VNForm/example.vue')
  },
  {
    path: '/vnbreadcrumb',
    name: 'VNBreadcrumbExample',
    component: () => import('./VNBreadcrumb/example.vue')
  },
  {
    path: '/vnsidebar',
    name: 'VNSidebarExample',
    component: () => import('./VNSidebar/example.vue')
  },
  {
    path: '/vnstatusbar',
    name: 'VNStatusBarExample',
    component: () => import('./VNStatusBar/example.vue')
  },
  {
    path: '/ep-examples',
    name: 'EPExamplesPage',
    component: () => import('./EPExamples/example.vue')
  },
  {
    path: '/vnchart',
    name: 'VNChartExample',
    component: () => import('./VNChart/example.vue')
  },
  {
    path: '/vndialog',
    name: 'VNDialogExample',
    component: () => import('./VNDialog/example.vue')
  },
  {
    path: '/vndrawer',
    name: 'VNDrawerExample',
    component: () => import('./VNDrawer/example.vue')
  },
  {
    path: '/vntree',
    name: 'VNTreeExample',
    component: () => import('./VNTree/example.vue')
  },
  {
    path: '/vntabs',
    name: 'VNTabsExample',
    component: () => import('./VNTabs/example.vue')
  },
  {
    path: '/vnframe',
    name: 'VNFrameExample',
    component: () => import('./VNFrame/example.vue'),
    meta: { title: 'VNFrame 示例' }
  },
  {
    path: '/vnlogin',
    name: 'VNLoginExample',
    component: () => import('./VNLogin/example.vue'),
    meta: { title: 'VNLogin 示例' }
  },
  {
    path: '/vnprintpreview',
    name: 'VNPrintPreviewExample',
    component: () => import('./VNPrintPreview/example.vue'),
    meta: { title: '打印预览示例' }
  }
]; 