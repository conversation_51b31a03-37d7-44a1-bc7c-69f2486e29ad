<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择拣货员"
    width="70%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :show-operations="true"
      :operation-width="100"
      operation-fixed="right"
      row-key="id"
      :selection-type="multiple ? 'multiple' : 'single'"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @selection-change="handleSelectionChange"
      @filter-change="handleFilterChange"
      highlight-current-row
    >
      <template #column-status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ formatStatus(row.status) }}
        </el-tag>
      </template>
      
      <template #column-workload="{ row }">
        <el-progress
          :percentage="getWorkloadPercentage(row.id)"
          :color="getWorkloadColor(row.id)"
          :show-text="false"
          style="width: 80px"
        />
        <span style="margin-left: 8px; font-size: 12px;">
          {{ getUserTaskCount(row.id) }}
        </span>
      </template>
      
      <template #operation="{ row }">
        <el-button
          type="primary"
          size="small"
          :disabled="!isUserAvailable(row)"
          @click="handleSelect(row)"
        >
          选择
        </el-button>
      </template>
    </VNTable>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="selectedRows.length === 0"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, onMounted } from 'vue'
import { ElDialog, ElButton, ElMessage, ElTag, ElProgress } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import { getUserPage, getPickingUsers, type WmsUserResp } from '@/api/wms/pickingTask'
import { usePickingTaskStore } from '@/store/wms/pickingTask'

// 组件接口定义
interface UserSelectorProps {
  multiple?: boolean
  selectedUsers?: number[]
  roleFilter?: string[]
  showWorkload?: boolean
}

interface UserSelectorEmits {
  confirm: [users: WmsUserResp | WmsUserResp[]]
  cancel: []
}

// Props 和 Emits
const props = withDefaults(defineProps<UserSelectorProps>(), {
  multiple: false,
  selectedUsers: () => [],
  roleFilter: () => ['PICKER', 'WAREHOUSE_WORKER'],
  showWorkload: true
})

const emit = defineEmits<UserSelectorEmits>()

// Store
const pickingTaskStore = usePickingTaskStore()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const tableData = ref<WmsUserResp[]>([])
const selectedRows = ref<WmsUserResp[]>([])
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 表格配置
const vnTableRef = ref<InstanceType<typeof VNTable>>()

const tableColumns = computed<TableColumn[]>(() => {
  const columns: TableColumn[] = [
    { prop: 'username', label: '用户名', width: 120 },
    { prop: 'nickname', label: '姓名', width: 120 },
    { prop: 'department', label: '部门', width: 120 },
    { prop: 'role', label: '角色', width: 100 },
    { 
      prop: 'status', 
      label: '状态', 
      width: 80, 
      slot: true 
    }
  ]
  
  if (props.showWorkload) {
    columns.push({
      prop: 'workload',
      label: '工作负载',
      width: 120,
      slot: true
    })
  }
  
  columns.push(
    { prop: 'phone', label: '联系电话', width: 120 },
    { prop: 'email', label: '邮箱', minWidth: 150 }
  )
  
  return columns
})

const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showFilter: true,
  filterFields: [
    {
      prop: 'username',
      label: '用户名',
      type: 'input',
      placeholder: '请输入用户名'
    },
    {
      prop: 'nickname',
      label: '姓名',
      type: 'input',
      placeholder: '请输入姓名'
    },
    {
      prop: 'department',
      label: '部门',
      type: 'input',
      placeholder: '请输入部门'
    },
    {
      prop: 'role',
      label: '角色',
      type: 'select',
      options: [
        { label: '拣货员', value: 'PICKER' },
        { label: '仓库工人', value: 'WAREHOUSE_WORKER' },
        { label: '组长', value: 'TEAM_LEADER' },
        { label: '主管', value: 'SUPERVISOR' }
      ]
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      options: [
        { label: '在线', value: 'ONLINE' },
        { label: '离线', value: 'OFFLINE' },
        { label: '忙碌', value: 'BUSY' },
        { label: '休息', value: 'BREAK' }
      ]
    }
  ]
}))

// 计算属性
const userTaskStats = computed(() => pickingTaskStore.userTaskStats)

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 优先使用拣货用户接口
    if (props.roleFilter.includes('PICKER')) {
      const result = await getPickingUsers()
      tableData.value = result
      pagination.total = result.length
    } else {
      const params = {
        pageNum: pagination.currentPage,
        pageSize: pagination.pageSize,
        status: 'ACTIVE',
        role: props.roleFilter.join(',')
      }
      const res = await getUserPage(params)
      tableData.value = res?.list || []
      pagination.total = res?.total || 0
    }
  } catch (error) {
    ElMessage.error('加载用户列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  loadData()
  // 加载用户任务统计
  if (props.showWorkload) {
    pickingTaskStore.fetchList()
  }
}

const handleClose = () => {
  selectedRows.value = []
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleSelectionChange = (rows: WmsUserResp[]) => {
  selectedRows.value = rows
}

const handleFilterChange = (filters: Record<string, any>) => {
  pagination.currentPage = 1
  // 这里可以添加过滤逻辑
  loadData()
}

const handleSelect = (row: WmsUserResp) => {
  if (props.multiple) {
    const index = selectedRows.value.findIndex(user => user.id === row.id)
    if (index === -1) {
      selectedRows.value.push(row)
    } else {
      selectedRows.value.splice(index, 1)
    }
  } else {
    selectedRows.value = [row]
    handleConfirm()
  }
}

const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择用户')
    return
  }
  
  const result = props.multiple ? selectedRows.value : selectedRows.value[0]
  emit('confirm', result)
  dialogVisible.value = false
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'ONLINE':
      return 'success'
    case 'BUSY':
      return 'warning'
    case 'BREAK':
      return 'info'
    case 'OFFLINE':
      return 'danger'
    default:
      return 'info'
  }
}

const formatStatus = (status: string) => {
  switch (status) {
    case 'ONLINE':
      return '在线'
    case 'BUSY':
      return '忙碌'
    case 'BREAK':
      return '休息'
    case 'OFFLINE':
      return '离线'
    default:
      return '未知'
  }
}

const isUserAvailable = (user: WmsUserResp) => {
  return user.status === 'ONLINE' || user.status === 'BREAK'
}

const getUserTaskCount = (userId: number) => {
  const stats = userTaskStats.value.get(userId)
  if (!stats) return 0
  return stats.assigned + stats.inProgress
}

const getWorkloadPercentage = (userId: number) => {
  const taskCount = getUserTaskCount(userId)
  const maxTasks = 10 // 假设最大任务数为10
  return Math.min((taskCount / maxTasks) * 100, 100)
}

const getWorkloadColor = (userId: number) => {
  const percentage = getWorkloadPercentage(userId)
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 暴露方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})

// 生命周期
onMounted(() => {
  // 组件挂载时可以预加载一些数据
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
