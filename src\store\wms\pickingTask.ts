import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  WmsPickingTaskResp, 
  WmsPickingTaskQueryReq,
  WmsPickingTaskStatus,
  WmsPickingTaskCreateReq,
  WmsPickingTaskUpdateReq,
  WmsPickingTaskPageResp,
  WmsWaveResp,
  WmsWaveQueryReq,
  WmsWaveCreateReq,
  WmsUserResp,
  WmsPickingTaskMobileResp
} from '@/api/wms/pickingTask'
import type { PickingTaskFormData, WaveFormData } from '@/types/wms/pickingTask'
import * as pickingTaskApi from '@/api/wms/pickingTask'

export const usePickingTaskStore = defineStore('pickingTask', () => {
  // ==================== 状态数据 ====================
  
  // 拣货任务列表数据
  const list = ref<WmsPickingTaskResp[]>([])
  const total = ref(0)
  const loading = ref(false)
  
  // 当前记录
  const currentRecord = ref<WmsPickingTaskResp | null>(null)
  
  // 波次数据
  const waves = ref<WmsWaveResp[]>([])
  const wavesTotal = ref(0)
  const wavesLoading = ref(false)
  const currentWave = ref<WmsWaveResp | null>(null)
  
  // 用户数据
  const users = ref<WmsUserResp[]>([])
  const pickingUsers = ref<WmsUserResp[]>([])
  
  // 移动端数据
  const mobileTaskList = ref<WmsPickingTaskMobileResp[]>([])
  const currentMobileTask = ref<WmsPickingTaskMobileResp | null>(null)
  
  // 分页信息
  const pagination = ref({
    pageNum: 1,
    pageSize: 10
  })
  
  const wavesPagination = ref({
    pageNum: 1,
    pageSize: 10
  })
  
  // 搜索表单
  const searchForm = ref<WmsPickingTaskQueryReq>({
    pageNum: 1,
    pageSize: 10
  })
  
  const wavesSearchForm = ref<WmsWaveQueryReq>({
    pageNum: 1,
    pageSize: 10
  })
  
  // 表单数据
  const formData = ref<PickingTaskFormData>({
    notificationId: null,
    pickingStrategy: '',
    priority: 3,
    details: [],
    _mode: 'create'
  })
  
  const waveFormData = ref<WaveFormData>({
    taskIds: [],
    _mode: 'create'
  })
  
  // 缓存数据
  const cache = ref<Map<number, WmsPickingTaskResp>>(new Map())
  const waveCache = ref<Map<number, WmsWaveResp>>(new Map())
  
  // ==================== 计算属性 ====================
  
  // 是否有数据
  const hasData = computed(() => list.value.length > 0)
  const hasWaveData = computed(() => waves.value.length > 0)
  
  // 是否为空
  const isEmpty = computed(() => !loading.value && list.value.length === 0)
  const isWaveEmpty = computed(() => !wavesLoading.value && waves.value.length === 0)
  
  // 状态统计
  const statusStats = computed(() => {
    const stats = {
      PENDING: 0,
      ASSIGNED: 0,
      IN_PROGRESS: 0,
      COMPLETED: 0,
      CANCELLED: 0
    }
    list.value.forEach(item => {
      stats[item.status]++
    })
    return stats
  })
  
  // 按状态分组的任务
  const pendingTasks = computed(() => 
    list.value.filter(task => task.status === 'PENDING')
  )
  
  const assignedTasks = computed(() => 
    list.value.filter(task => task.status === 'ASSIGNED')
  )
  
  const inProgressTasks = computed(() => 
    list.value.filter(task => task.status === 'IN_PROGRESS')
  )
  
  const completedTasks = computed(() => 
    list.value.filter(task => task.status === 'COMPLETED')
  )
  
  // 用户任务统计
  const userTaskStats = computed(() => {
    const stats = new Map<number, { assigned: number, inProgress: number, completed: number }>()
    
    list.value.forEach(task => {
      if (task.assignedUserId) {
        const userId = task.assignedUserId
        if (!stats.has(userId)) {
          stats.set(userId, { assigned: 0, inProgress: 0, completed: 0 })
        }
        const userStats = stats.get(userId)!
        
        switch (task.status) {
          case 'ASSIGNED':
            userStats.assigned++
            break
          case 'IN_PROGRESS':
            userStats.inProgress++
            break
          case 'COMPLETED':
            userStats.completed++
            break
        }
      }
    })
    
    return stats
  })
  
  // 当前表单是否可编辑
  const canEdit = computed(() => {
    if (!currentRecord.value) return true
    return ['PENDING', 'ASSIGNED'].includes(currentRecord.value.status)
  })
  
  // 当前表单是否可删除
  const canDelete = computed(() => {
    if (!currentRecord.value) return false
    return ['PENDING', 'ASSIGNED'].includes(currentRecord.value.status)
  })
  
  // ==================== 异步操作 ====================
  
  // 获取拣货任务列表
  const fetchList = async (params?: WmsPickingTaskQueryReq) => {
    loading.value = true
    try {
      const queryParams = {
        ...searchForm.value,
        ...params
      }
      
      // 过滤掉空值
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === '' || queryParams[key] === null || queryParams[key] === undefined) {
          delete queryParams[key]
        }
      })
      
      const result = await pickingTaskApi.getPickingTaskPage(queryParams)
      list.value = result.list
      total.value = result.total
      
      // 更新分页信息
      pagination.value.pageNum = queryParams.pageNum || 1
      pagination.value.pageSize = queryParams.pageSize || 10
      
      // 更新缓存
      result.list.forEach(item => {
        cache.value.set(item.id, item)
      })
    } catch (error) {
      console.error('获取拣货任务列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取拣货任务详情
  const fetchDetail = async (id: number) => {
    try {
      // 先从缓存获取
      if (cache.value.has(id)) {
        currentRecord.value = cache.value.get(id)!
        return currentRecord.value
      }
      
      const result = await pickingTaskApi.getPickingTaskById(id)
      currentRecord.value = result
      
      // 更新缓存
      cache.value.set(id, result)
      
      return result
    } catch (error) {
      console.error('获取拣货任务详情失败:', error)
      throw error
    }
  }
  
  // 创建拣货任务
  const create = async (data: WmsPickingTaskCreateReq) => {
    try {
      const result = await pickingTaskApi.createPickingTask(data)
      
      // 添加到列表开头
      list.value.unshift(result)
      total.value += 1
      
      // 更新缓存
      cache.value.set(result.id, result)
      
      // 设置为当前记录
      currentRecord.value = result
      
      return result
    } catch (error) {
      console.error('创建拣货任务失败:', error)
      throw error
    }
  }
  
  // 更新拣货任务
  const update = async (id: number, data: WmsPickingTaskUpdateReq) => {
    try {
      const result = await pickingTaskApi.updatePickingTask(id, data)
      
      // 更新列表中的记录
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1) {
        list.value[index] = result
      }
      
      // 更新缓存
      cache.value.set(id, result)
      
      // 更新当前记录
      if (currentRecord.value?.id === id) {
        currentRecord.value = result
      }
      
      return result
    } catch (error) {
      console.error('更新拣货任务失败:', error)
      throw error
    }
  }
  
  // 删除拣货任务
  const remove = async (id: number) => {
    try {
      await pickingTaskApi.deletePickingTask(id)
      
      // 从列表中移除
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1) {
        list.value.splice(index, 1)
        total.value -= 1
      }
      
      // 从缓存中移除
      cache.value.delete(id)
      
      // 清除当前记录
      if (currentRecord.value?.id === id) {
        currentRecord.value = null
      }
    } catch (error) {
      console.error('删除拣货任务失败:', error)
      throw error
    }
  }
  
  // ==================== 拣货任务操作 ====================
  
  // 分配任务
  const assignTask = async (taskId: number, userId: number, remark?: string) => {
    try {
      await pickingTaskApi.assignPickingTask(taskId, { userId, remark })
      await refreshItem(taskId)
    } catch (error) {
      console.error('分配拣货任务失败:', error)
      throw error
    }
  }
  
  // 批量分配任务
  const batchAssignTasks = async (taskIds: number[], userId: number, remark?: string) => {
    try {
      await pickingTaskApi.batchAssignPickingTasks({ taskIds, userId, remark })
      // 刷新所有相关任务
      for (const taskId of taskIds) {
        await refreshItem(taskId)
      }
    } catch (error) {
      console.error('批量分配拣货任务失败:', error)
      throw error
    }
  }
  
  // 开始任务
  const startTask = async (taskId: number) => {
    try {
      await pickingTaskApi.startPickingTask(taskId)
      await refreshItem(taskId)
    } catch (error) {
      console.error('开始拣货任务失败:', error)
      throw error
    }
  }
  
  // 完成任务
  const completeTask = async (taskId: number, remark?: string) => {
    try {
      await pickingTaskApi.completePickingTask(taskId, { remark })
      await refreshItem(taskId)
    } catch (error) {
      console.error('完成拣货任务失败:', error)
      throw error
    }
  }
  
  // 取消任务
  const cancelTask = async (taskId: number, reason?: string) => {
    try {
      await pickingTaskApi.cancelPickingTask(taskId, { reason })
      await refreshItem(taskId)
    } catch (error) {
      console.error('取消拣货任务失败:', error)
      throw error
    }
  }
  
  // ==================== 波次管理 ====================
  
  // 获取波次列表
  const fetchWaves = async (params?: WmsWaveQueryReq) => {
    wavesLoading.value = true
    try {
      const queryParams = {
        ...wavesSearchForm.value,
        ...params
      }
      
      // 过滤掉空值
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === '' || queryParams[key] === null || queryParams[key] === undefined) {
          delete queryParams[key]
        }
      })
      
      const result = await pickingTaskApi.getWaveList(queryParams)
      waves.value = result.list
      wavesTotal.value = result.total
      
      // 更新分页信息
      wavesPagination.value.pageNum = queryParams.pageNum || 1
      wavesPagination.value.pageSize = queryParams.pageSize || 10
      
      // 更新缓存
      result.list.forEach(item => {
        waveCache.value.set(item.id, item)
      })
    } catch (error) {
      console.error('获取波次列表失败:', error)
      throw error
    } finally {
      wavesLoading.value = false
    }
  }
  
  // 创建波次
  const createWave = async (data: WmsWaveCreateReq) => {
    try {
      const result = await pickingTaskApi.createWave(data)
      
      // 添加到波次列表开头
      waves.value.unshift(result)
      wavesTotal.value += 1
      
      // 更新缓存
      waveCache.value.set(result.id, result)
      
      // 设置为当前波次
      currentWave.value = result
      
      return result
    } catch (error) {
      console.error('创建波次失败:', error)
      throw error
    }
  }
  
  // 添加任务到波次
  const addTasksToWave = async (waveId: number, taskIds: number[]) => {
    try {
      await pickingTaskApi.addTasksToWave(waveId, taskIds)
      // 刷新波次和任务
      await refreshWave(waveId)
      for (const taskId of taskIds) {
        await refreshItem(taskId)
      }
    } catch (error) {
      console.error('添加任务到波次失败:', error)
      throw error
    }
  }
  
  // ==================== 用户管理 ====================
  
  // 获取拣货用户
  const fetchPickingUsers = async () => {
    try {
      const result = await pickingTaskApi.getPickingUsers()
      pickingUsers.value = result
      return result
    } catch (error) {
      console.error('获取拣货用户失败:', error)
      throw error
    }
  }
  
  // ==================== 移动端接口 ====================
  
  // 获取移动端任务列表
  const fetchMobileTasks = async (userId: number) => {
    try {
      const result = await pickingTaskApi.getMobilePickingTasks(userId)
      mobileTaskList.value = result
      return result
    } catch (error) {
      console.error('获取移动端拣货任务失败:', error)
      throw error
    }
  }
  
  // 获取移动端任务详情
  const fetchMobileTaskDetail = async (id: number) => {
    try {
      const result = await pickingTaskApi.getMobilePickingTaskById(id)
      currentMobileTask.value = result
      return result
    } catch (error) {
      console.error('获取移动端拣货任务详情失败:', error)
      throw error
    }
  }
  
  // ==================== 工具方法 ====================
  
  // 刷新单个任务
  const refreshItem = async (id: number) => {
    try {
      const item = await pickingTaskApi.getPickingTaskById(id)
      cache.value.set(id, item)
      
      const index = list.value.findIndex(i => i.id === id)
      if (index !== -1) {
        list.value[index] = item
      }
      
      if (currentRecord.value?.id === id) {
        currentRecord.value = item
      }
    } catch (error) {
      console.error('刷新拣货任务失败:', error)
    }
  }
  
  // 刷新单个波次
  const refreshWave = async (waveId: number) => {
    try {
      const wave = await pickingTaskApi.getWaveById(waveId)
      waveCache.value.set(waveId, wave)
      
      const index = waves.value.findIndex(w => w.id === waveId)
      if (index !== -1) {
        waves.value[index] = wave
      }
      
      if (currentWave.value?.id === waveId) {
        currentWave.value = wave
      }
    } catch (error) {
      console.error('刷新波次失败:', error)
    }
  }
  
  // 重置搜索表单
  const resetSearchForm = () => {
    searchForm.value = {
      pageNum: 1,
      pageSize: 10
    }
  }
  
  // 重置波次搜索表单
  const resetWavesSearchForm = () => {
    wavesSearchForm.value = {
      pageNum: 1,
      pageSize: 10
    }
  }
  
  // 重置表单数据
  const resetFormData = () => {
    formData.value = {
      notificationId: null,
      pickingStrategy: '',
      priority: 3,
      details: [],
      _mode: 'create'
    }
  }
  
  // 重置波次表单数据
  const resetWaveFormData = () => {
    waveFormData.value = {
      taskIds: [],
      _mode: 'create'
    }
  }
  
  // 清除缓存
  const clearCache = () => {
    cache.value.clear()
    waveCache.value.clear()
  }
  
  // 重置所有状态
  const reset = () => {
    list.value = []
    total.value = 0
    waves.value = []
    wavesTotal.value = 0
    currentRecord.value = null
    currentWave.value = null
    loading.value = false
    wavesLoading.value = false
    resetSearchForm()
    resetWavesSearchForm()
    resetFormData()
    resetWaveFormData()
    clearCache()
  }
  
  // ==================== 返回值 ====================
  
  return {
    // 状态
    list,
    total,
    loading,
    currentRecord,
    waves,
    wavesTotal,
    wavesLoading,
    currentWave,
    users,
    pickingUsers,
    mobileTaskList,
    currentMobileTask,
    pagination,
    wavesPagination,
    searchForm,
    wavesSearchForm,
    formData,
    waveFormData,
    
    // 计算属性
    hasData,
    hasWaveData,
    isEmpty,
    isWaveEmpty,
    statusStats,
    pendingTasks,
    assignedTasks,
    inProgressTasks,
    completedTasks,
    userTaskStats,
    canEdit,
    canDelete,
    
    // 异步操作
    fetchList,
    fetchDetail,
    create,
    update,
    remove,
    assignTask,
    batchAssignTasks,
    startTask,
    completeTask,
    cancelTask,
    
    // 波次管理
    fetchWaves,
    createWave,
    addTasksToWave,
    
    // 用户管理
    fetchPickingUsers,
    
    // 移动端接口
    fetchMobileTasks,
    fetchMobileTaskDetail,
    
    // 工具方法
    refreshItem,
    refreshWave,
    resetSearchForm,
    resetWavesSearchForm,
    resetFormData,
    resetWaveFormData,
    clearCache,
    reset
  }
})
