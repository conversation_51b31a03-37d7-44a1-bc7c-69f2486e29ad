<template>
  <div 
    class="vn-frame-new" 
    :class="{ 
      'sidebar-collapsed': effectiveSidebarCollapsed && !isMobile, // Apply collapsed class only when inline sidebar is collapsed
      'is-mobile': isMobile, 
      'is-fullscreen': isFullscreen // ADD fullscreen class binding
    }"
  >
    <!-- Inline Sidebar (Non-Mobile) -->
    <div 
      v-if="showSidebar && !isMobile" 
      class="vn-frame-sidebar"
      :style="sidebarStyle"
    >
      <slot name="sidebar">
        <VNSidebar 
          v-if="sidebarItems" 
          :items="sidebarItems" 
          :collapse="effectiveSidebarCollapsed" 
          :unique-opened="uniqueOpened"
        />
      </slot>
    </div>

    <!-- Mobile Drawer Sidebar -->
     <el-drawer
        v-if="showSidebar && isMobile"
        v-model="mobileSidebarVisible"
        direction="ltr"
        :with-header="false"
        :size="'50%'"
        custom-class="vn-frame-mobile-drawer"
        :append-to-body="true" 
      >
         <slot name="sidebar">
            <VNSidebar 
              v-if="sidebarItems" 
              :items="sidebarItems" 
              :collapse="false"  
              :unique-opened="uniqueOpened"
            />
         </slot>
      </el-drawer>

    <!-- Right Panel (Breadcrumb + Content + Footer) -->
    <div class="vn-frame-right-panel">
      <!-- Top: Breadcrumb Area -->
      <div 
        v-if="showBreadcrumb"
        class="vn-frame-breadcrumb-area"
        :style="breadcrumbAreaStyle"
      >
        <el-button 
            v-if="showSidebar" 
            class="breadcrumb-collapse-button" 
            circle 
            @click="handleToggleClick"
            >
            <el-icon>
                <component :is="toggleIcon" />
            </el-icon>
        </el-button>
        <slot name="breadcrumb">
          <VNBreadcrumb :items="props.breadcrumbItems" />
        </slot>

        <!-- ADD: Right side container -->
        <div class="breadcrumb-right-actions">
             <!-- Account Book Selector -->
             <el-select 
                v-model="currentAccountBookId" 
                placeholder="选择帐套" 
                size="small" 
                style="margin-right: 10px; width: 150px;" 
                :loading="loadingAccountBooks"
                @change="handleAccountBookChange"
                filterable 
                :disabled="accountBooks.length === 0"
             >
                <el-option
                  v-for="item in accountBooks"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
             </el-select>

             <!-- Fullscreen Button -->
             <el-button 
                circle 
                title="全屏"
                @click="toggleFullscreen"
                >
                <el-icon><FullScreen /></el-icon>
            </el-button>

            <!-- User Dropdown -->
            <el-dropdown class="user-dropdown" @command="handleUserCommand">
                <span class="el-dropdown-link">
                    <!-- 更严格地判断头像 URL 是否有效 -->
                    <el-avatar v-if="userInfo?.avatar && userInfo.avatar.trim().length > 10" :size="24" :src="userInfo.avatar" class="user-avatar-img" /> 
                    
                    <span class="user-name">
                      {{ userInfo?.nickname ? userInfo.nickname : (userInfo?.username || '未知用户') }}
                      <template v-if="userInfo?.nickname && userInfo?.username"> ({{ userInfo.username }})</template>
                      <template v-else-if="!userInfo?.nickname && userInfo?.username"> ({{ userInfo.username }})</template>
                    </span>
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                      <template v-if="userInfo?.dropdownItems && userInfo.dropdownItems.length > 0">
                         <el-dropdown-item
                           v-for="item in userInfo.dropdownItems"
                           :key="item.command"
                           :command="item.command"
                           :divided="item.divided"
                          >{{ item.text }}</el-dropdown-item>
                      </template>
                      <template v-else>
                         <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                         <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                      </template>
                  </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
      </div>

      <!-- Middle: Main Content Area -->
      <div class="vn-frame-content-area">
        <slot name="default">
          <!-- Page content goes here -->
        </slot>
      </div>

      <!-- Bottom: Status Bar / Footer Area -->
      <div 
        v-if="showStatusBar"
        class="vn-frame-footer-area"
        :style="footerAreaStyle"
      >
        <slot name="footer">
          <VNStatusBar 
             v-if="statusBarItems" 
             :items="statusBarItems"
             :left-title="statusBarLeftTitle" 
             :center-title="statusBarCenterTitle" 
             :right-title="statusBarRightTitle" 
            />
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
    defineProps, defineEmits, withDefaults, computed, 
    ref, onMounted, onUnmounted, watch 
} from 'vue';
import type { VNFrameProps, VNFrameEmits, UserInfo } from './types';
import VNSidebar from '../VNSidebar/index.vue';
import VNStatusBar from '../VNStatusBar/index.vue';
import VNBreadcrumb from '../VNBreadcrumb/index.vue';
import { 
    ElIcon, ElDrawer, ElButton, 
    ElDropdown, ElDropdownMenu, ElDropdownItem, ElAvatar, 
    ElSelect, ElOption, ElMessage
} from 'element-plus';
import { 
    Fold, Expand, 
    FullScreen, User, ArrowDown 
} from '@element-plus/icons-vue';
import type { CSSProperties } from 'vue';
import { getUserAccountBooks, type AccountBookItem } from '@/api/user';
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';

// --- Constants ---
const MOBILE_BREAKPOINT = 768;
const TABLET_BREAKPOINT = 992;

// --- Props and Emits ---
const props = withDefaults(defineProps<VNFrameProps & {
    statusBarLeftTitle?: string;
    statusBarCenterTitle?: string;
    statusBarRightTitle?: string;
    username?: string;
    userInfo?: UserInfo;
    breadcrumbItems: any[];
    uniqueOpened?: boolean;
}>(), {
  showSidebar: true,
  sidebarWidth: '200px',
  sidebarCollapsedWidth: '64px',
  sidebarCollapsed: false,
  uniqueOpened: true,
  showBreadcrumb: true,
  breadcrumbHeight: '50px', 
  showStatusBar: true,
  footerHeight: '30px',
  statusBarLeftTitle: '',
  statusBarCenterTitle: '',
  statusBarRightTitle: '',
  username: '用户名',
  userInfo: () => ({ name: '用户名', dropdownItems: [] }),
  breadcrumbItems: () => [],
});

const emit = defineEmits<VNFrameEmits & {
    (e: 'profile-click'): void;
    (e: 'logout-click'): void;
    (e: 'command', command: string | number | object): void;
    (e: 'update:sidebarCollapsed', value: boolean): void;
}>();

// --- Responsive State ---
const isMobile = ref(false);
const isTablet = ref(false);
const mobileSidebarVisible = ref(false); // Controls the mobile drawer
const isFullscreen = ref(!!document.fullscreenElement); // ADD fullscreen state

const checkScreenWidth = () => {
  const width = window.innerWidth;
  isMobile.value = width < MOBILE_BREAKPOINT;
  isTablet.value = width >= MOBILE_BREAKPOINT && width < TABLET_BREAKPOINT;
  // Close drawer automatically if resizing from mobile to non-mobile
  if (!isMobile.value && mobileSidebarVisible.value) {
      mobileSidebarVisible.value = false;
  }
};

// ADD: Handle fullscreen change event
const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement;
    console.log('Fullscreen state changed:', isFullscreen.value);
};

// --- Account Book State ---
const userStore = useUserStore();
const router = useRouter();
const accountBooks = ref<AccountBookItem[]>([]);
const currentAccountBookId = ref<number | string | null>(userStore.currentAccountBookId);
const loadingAccountBooks = ref(false);

// --- Fetch Account Books ---
const fetchAccountBooks = async () => {
  loadingAccountBooks.value = true;
  try {
    const response = await getUserAccountBooks(); // response is { list: AccountBookItem[] }
    accountBooks.value = response.list || []; // Assign response.list to accountBooks.value
    // Set default if current is not in the list or null
    if (
        !currentAccountBookId.value || 
        !accountBooks.value.some(ab => ab.id === currentAccountBookId.value)
    ) {
        if (accountBooks.value.length > 0) {
            currentAccountBookId.value = accountBooks.value[0].id; // Select the first one as default
            handleAccountBookChange(currentAccountBookId.value); // Update store for the default
        } else {
            currentAccountBookId.value = null;
        }
    }
    console.log('Account books loaded:', accountBooks.value);
    console.log('Current account book ID:', currentAccountBookId.value);

  } catch (error) {
    console.error('Error fetching account books:', error);
    ElMessage.error('加载帐套列表失败');
    accountBooks.value = []; // Clear on error
    currentAccountBookId.value = null;
  } finally {
    loadingAccountBooks.value = false;
  }
};

// --- Handle Account Book Change ---
const handleAccountBookChange = (selectedId: number | string | null) => {
  if (selectedId !== null) {
    console.log('Account book changed to:', selectedId);
    // Get current path BEFORE updating the store, in case update triggers navigation
    const currentPath = router.currentRoute.value.path;

    // Update the value in the store
    userStore.SetCurrentAccountBook(selectedId);

    ElMessage.success(`帐套已切换`);

    if (!currentPath.startsWith('/system')) {
        console.log('Page reload logic removed. Consider alternative update methods.');
        // TODO: Implement logic to notify components/pages to refresh data if needed
        // Example: emit('account-book-changed', selectedId);
        // Or: userStore.notifyAccountBookChange(selectedId);
    } else {
        console.log('Skipping page reload for /system path.');
        // Optionally, you might want to emit an event or call a method
        // for system pages to react if necessary, without reloading.
    }

  } else {
      console.warn('Selected account book ID is null');
  }
};

onMounted(() => {
  checkScreenWidth();
  window.addEventListener('resize', checkScreenWidth);
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  fetchAccountBooks();
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenWidth);
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});

// Watch for external changes to sidebarCollapsed prop when on mobile
// If the drawer is open and the prop requests collapse, close the drawer.
watch(() => props.sidebarCollapsed, (newVal) => {
    if (isMobile.value && newVal && mobileSidebarVisible.value) {
        mobileSidebarVisible.value = false;
    }
});


// --- Computed Properties ---

// Determines the effective collapsed state for the *inline* sidebar
const effectiveSidebarCollapsed = computed(() => {
    // On tablet, always collapsed. Otherwise, respect the prop.
    // This doesn't apply on mobile as the inline sidebar isn't shown.
    return isTablet.value || props.sidebarCollapsed;
});

// Style for the inline sidebar
const sidebarStyle = computed<CSSProperties>(() => ({
  width: effectiveSidebarCollapsed.value ? props.sidebarCollapsedWidth : props.sidebarWidth,
  // Transition moved to CSS for potentially better performance/consistency
}));

const breadcrumbAreaStyle = computed<CSSProperties>(() => ({
    minHeight: props.breadcrumbHeight,
    lineHeight: props.breadcrumbHeight,
}));

const footerAreaStyle = computed<CSSProperties>(() => ({
  minHeight: props.footerHeight,
}));

// Icon for the toggle button
const toggleIcon = computed(() => {
    if (isMobile.value) {
        // On mobile, icon shows drawer state (Expand means drawer is closed, Fold means open)
        return mobileSidebarVisible.value ? Fold : Expand;
    } else {
        // On desktop/tablet, icon shows inline sidebar state
        return props.sidebarCollapsed ? Expand : Fold;
    }
});

// --- Methods ---

const handleToggleClick = () => {
  if (isMobile.value) {
    // On mobile, toggle the drawer visibility
    mobileSidebarVisible.value = !mobileSidebarVisible.value;
    // Optional: If drawer opens, ensure external state reflects non-collapsed if necessary
    // if (mobileSidebarVisible.value) emit('update:sidebarCollapsed', false);
  } else {
    // On desktop/tablet, emit event to toggle the inline sidebar state via v-model
    emit('update:sidebarCollapsed', !props.sidebarCollapsed);
  }
};

// UPDATE: Fullscreen toggle logic
const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
            .catch(err => {
                console.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
            });
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
    // State update is handled by the 'fullscreenchange' event listener
};

// UPDATE: Handler for user dropdown commands to emit events
const handleUserCommand = (command: string | number | object) => {
  console.log('VNFrame internal handleUserCommand:', command);

  // Emit the generic command event for the parent component to handle
  emit('command', command);

  // Optional: Keep specific emits if VNFrame needs to react internally or
  // if you prefer specific events over a generic command handler.
  // For simplicity now, we rely on the parent handling the generic command.
  /*
  if (command === 'profile') {
    emit('profile-click');
    console.log('Profile clicked inside VNFrame');
  } else if (command === 'logout') {
    emit('logout-click');
    console.log('Logout clicked inside VNFrame');
  } else if (command === 'settings') {
      console.log('Settings clicked inside VNFrame');
  }
  */
};

</script>

<style lang="scss" scoped>
.vn-frame-new {
  display: flex;
  height: 100vh;
  overflow: hidden; // Prevent scrollbars on the main container
  position: relative; // Needed for absolute positioning of drawer if not using append-to-body

  &.is-fullscreen {
    // Styles when the component itself is fullscreen, not just an element within it
    // This might be controlled externally or via the component's own fullscreen logic
    // Example: Ensure sidebar doesn't overlap content
    .vn-frame-sidebar {
       // Adjust z-index or position if necessary
    }
  }
}

.vn-frame-sidebar {
  background-color: #f4f6f9; // Example background
  // border-right: 1px solid #e0e0e0; // Example border
  overflow-x: hidden;
  overflow-y: auto;
  transition: width 0.28s; // Smooth collapse transition
  flex-shrink: 0; // Prevent sidebar from shrinking
}

.vn-frame-mobile-drawer {
    // Style the mobile drawer if needed
    // .el-drawer__body {
    //   padding: 0;
    // }
}

.vn-frame-right-panel {
  flex-grow: 1; // Take remaining space
  display: flex;
  flex-direction: column;
  overflow: hidden; // Prevent scrollbars here too initially
}

.vn-frame-breadcrumb-area {
  flex-shrink: 0; // Prevent shrinking
  display: flex; // Use flexbox for alignment
  align-items: center; // Vertically center items
  padding: 0 15px; // Add some padding
  border-bottom: 1px solid #e4e7ed; // Separator line
  background-color: #fff; // White background

  .breadcrumb-collapse-button {
    margin-right: 10px;
    // Ensure button aligns nicely with breadcrumb text
    vertical-align: middle; 
  }

  // Ensure slot content grows but right actions stay fixed
  :deep(.el-breadcrumb), // Target VNBreadcrumb's likely root
  > slot[name="breadcrumb"] > * { 
      flex-grow: 1; // Allow breadcrumb to take available space
      margin-right: 15px; // Space before right actions
      // Prevent breadcrumb from wrapping internally
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
  }

  .breadcrumb-right-actions {
    margin-left: auto; // Push actions to the right
    display: flex;
    align-items: center;
    gap: 10px; // Space between action items
    flex-shrink: 0; // Prevent actions container from shrinking
  }

  .user-dropdown {
    cursor: pointer;
    .el-dropdown-link {
      display: flex;
      align-items: center;
    }
    .user-avatar-img {
      margin-right: 8px;
    }
    .user-avatar {
       margin-right: 8px;
       font-size: 18px; // Adjust icon size if needed
    }
    .user-name {
       font-size: 14px;
    }
  }
}

.vn-frame-content-area {
  flex-grow: 1; // Take remaining vertical space
  padding: 15px;
  overflow-y: auto; // Allow content to scroll
  background-color: #f0f2f5; // Example content background
}

.vn-frame-footer-area {
  flex-shrink: 0; // Prevent shrinking
  // Styles for the footer/status bar area
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
}

// --- 响应式布局 --- 
@media (max-width: 768px) { // Adjust breakpoint as needed
  .vn-frame-breadcrumb-area {
    flex-wrap: wrap; 
    padding: 5px 10px; 
    line-height: normal; 

    .breadcrumb-collapse-button {
        // 保持显示
    }

    // 第一行：按钮和面包屑
    :deep(.el-breadcrumb),
    > slot[name="breadcrumb"] > * {
        // 不再设置 width: 100% 和 order
        // 移除 margin-bottom
        // 保留 flex-grow: 1; 让面包屑尽可能填充空间
        flex-grow: 1;
        margin-right: 10px; // 与右侧按钮的间距
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    // 第二行：右侧操作按钮区域
    .breadcrumb-right-actions {
      width: 100%; 
      order: 2; // 确保在第二行
      margin-left: 0; 
      justify-content: flex-start; // 靠左对齐
      padding-top: 5px; 
    }
  }

  .vn-frame-content-area {
      padding: 10px;
  }
}

</style> 