package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
	"strconv"

	"github.com/kataras/iris/v12"
)

// WmsInventoryQueryController 库存查询控制器
type WmsInventoryQueryController struct {
	inventoryQueryService service.WmsInventoryQueryService
}

// NewWmsInventoryQueryController 创建库存查询控制器实例
func NewWmsInventoryQueryController(inventoryQueryService service.WmsInventoryQueryService) *WmsInventoryQueryController {
	return &WmsInventoryQueryController{
		inventoryQueryService: inventoryQueryService,
	}
}

// GetInventoryPage 分页查询库存
// @Summary 分页查询库存
// @Description 支持多维度条件查询库存信息
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryQueryReq true "查询条件"
// @Success 200 {object} response.Response{data=vo.WmsInventoryPageVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/query [post]
func (c *WmsInventoryQueryController) GetInventoryPage(ctx iris.Context) {
	var req dto.WmsInventoryQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryQueryService.GetInventoryPage(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetInventoryDetail 获取库存详情
// @Summary 获取库存详情
// @Description 根据库存ID获取详细信息，包括历史记录和统计数据
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param id path int true "库存ID"
// @Success 200 {object} response.Response{data=vo.WmsInventoryDetailVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "库存不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/query/{id} [get]
func (c *WmsInventoryQueryController) GetInventoryDetail(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的库存ID", ctx)
		return
	}

	result, err := c.inventoryQueryService.GetInventoryDetail(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetInventoryByLocation 按库位查询库存
// @Summary 按库位查询库存
// @Description 根据库位ID查询该库位下的所有库存
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param locationId path int true "库位ID"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/query/location/{locationId} [get]
func (c *WmsInventoryQueryController) GetInventoryByLocation(ctx iris.Context) {
	locationIdStr := ctx.Params().Get("locationId")
	locationId, err := strconv.ParseUint(locationIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的库位ID", ctx)
		return
	}

	result, err := c.inventoryQueryService.GetInventoryByLocation(ctx.Request().Context(), uint(locationId))
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetInventoryByItem 按物料查询库存
// @Summary 按物料查询库存
// @Description 根据物料ID查询该物料在各库位的库存分布
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param itemId path int true "物料ID"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/query/item/{itemId} [get]
func (c *WmsInventoryQueryController) GetInventoryByItem(ctx iris.Context) {
	itemIdStr := ctx.Params().Get("itemId")
	itemId, err := strconv.ParseUint(itemIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的物料ID", ctx)
		return
	}

	result, err := c.inventoryQueryService.GetInventoryByItem(ctx.Request().Context(), uint(itemId))
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetInventorySummary 获取库存汇总
// @Summary 获取库存汇总
// @Description 按指定维度汇总库存数据
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param request body dto.WmsInventorySummaryReq true "汇总条件"
// @Success 200 {object} response.Response{data=vo.WmsInventorySummaryVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/summary [post]
func (c *WmsInventoryQueryController) GetInventorySummary(ctx iris.Context) {
	var req dto.WmsInventorySummaryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryQueryService.GetInventorySummary(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// CheckInventoryAvailability 检查库存可用性
// @Summary 检查库存可用性
// @Description 检查指定物料的库存是否满足需求数量
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAvailabilityReq true "可用性检查条件"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAvailabilityVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/availability [post]
func (c *WmsInventoryQueryController) CheckInventoryAvailability(ctx iris.Context) {
	var req dto.WmsInventoryAvailabilityReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryQueryService.CheckInventoryAvailability(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetInventoryTurnover 获取库存周转分析
// @Summary 获取库存周转分析
// @Description 分析库存周转率和相关指标
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryTurnoverReq true "周转分析条件"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryTurnoverVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/turnover [post]
func (c *WmsInventoryQueryController) GetInventoryTurnover(ctx iris.Context) {
	var req dto.WmsInventoryTurnoverReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryQueryService.GetInventoryTurnover(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetInventoryAlerts 获取库存预警
// @Summary 获取库存预警
// @Description 查询库存预警信息
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAlertReq true "预警查询条件"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryAlertLogVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/alerts [post]
func (c *WmsInventoryQueryController) GetInventoryAlerts(ctx iris.Context) {
	var req dto.WmsInventoryAlertReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryQueryService.GetInventoryAlerts(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// ExportInventory 导出库存数据
// @Summary 导出库存数据
// @Description 根据查询条件导出库存数据到Excel或CSV文件
// @Tags 库存查询
// @Accept json
// @Produce application/octet-stream
// @Param request body dto.WmsInventoryExportReq true "导出条件"
// @Success 200 {file} file "导出文件"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/export [post]
func (c *WmsInventoryQueryController) ExportInventory(ctx iris.Context) {
	var req dto.WmsInventoryExportReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	fileData, err := c.inventoryQueryService.ExportInventory(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("导出失败: "+err.Error(), ctx)
		return
	}

	// 设置响应头
	format := req.GetExportFormat()
	var contentType, fileName string
	if format == "csv" {
		contentType = "text/csv"
		fileName = "inventory_export.csv"
	} else {
		contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		fileName = "inventory_export.xlsx"
	}

	ctx.Header("Content-Type", contentType)
	ctx.Header("Content-Disposition", "attachment; filename="+fileName)
	ctx.Write(fileData)
}

// BatchUpdateInventory 批量更新库存
// @Summary 批量更新库存
// @Description 批量更新库存状态、库位等信息
// @Tags 库存查询
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryBatchUpdateReq true "批量更新条件"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/batch-update [post]
func (c *WmsInventoryQueryController) BatchUpdateInventory(ctx iris.Context) {
	var req dto.WmsInventoryBatchUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	err := c.inventoryQueryService.BatchUpdateInventory(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("批量更新失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("批量更新成功", ctx)
}
