package controller

import (
	"fmt"
	"io"
	"mime/multipart"
	"net/http"

	"github.com/kataras/iris/v12"
	// 修正 apperrors 的导入路径
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // 正确的路径
)

// LicenseController 处理授权相关操作
type LicenseControllerImpl struct {
	*BaseControllerImpl // 嵌入基础控制器实现
	// 可选: 如果需要特定的 LicenseService，可以在这里添加
	licenseService service.LicenseService // 假设存在 LicenseService
}

// NewLicenseController 创建 LicenseController
func NewLicenseControllerImpl(cm *ControllerManager) *LicenseControllerImpl {
	// base := NewBaseController(cm)
	// specificService := cm.GetServiceManager().GetLicenseService() // 假设有 GetLicenseService 方法
	return &LicenseControllerImpl{
		BaseControllerImpl: NewBaseController(cm),
		// licenseService: specificService,
		// 从 ServiceManager 获取 LicenseService (假设存在 GetLicenseService)
		licenseService: cm.GetServiceManager().GetLicenseService(),
	}
}

// GetLicenseInfo 获取当前授权信息
// GET /api/v1/admin/license/info
func (lc *LicenseControllerImpl) GetLicenseInfo(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "license")

	licenseInfo, err := lc.licenseService.GetLicenseDetails(ctx.Request().Context())

	if err != nil {
		if appErr, ok := err.(*apperrors.CustomError); ok && appErr.Code == apperrors.CODE_LICENSE_EXPIRED {
			responseData := iris.Map{
				"status":      "expired",
				"message":     appErr.Error(),
				"licensedTo":  "N/A", // 默认为 N/A，如果 licenseInfo 存在则覆盖
				"expiresAt":   nil,
				"licenseType": nil,
				"features":    nil,
				"issueDate":   nil,
			}
			if licenseInfo != nil { // licenseInfo 可能在过期错误时被 service 层一同返回
				responseData["licensedTo"] = licenseInfo.CustomerName
				responseData["expiresAt"] = licenseInfo.ExpiryDate.Format("2006-01-02")
				responseData["licenseType"] = licenseInfo.LicenseType
				responseData["features"] = licenseInfo.Features
				responseData["issueDate"] = licenseInfo.IssueDate.Format("2006-01-02")
				// responseData["allDetails"] = licenseInfo // 保持注释，除非明确需要
			}
			lc.Success(ctx, responseData) // 使用 Success 并通过 status 字段区分
			return
		} else if appErr, ok := err.(*apperrors.CustomError); ok && appErr.Code == apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND {
			lc.Success(ctx, iris.Map{ // 使用 Success 并通过 status 字段区分
				"status":    "not_found",
				"message":   appErr.Error(),
				"expiresAt": nil,
			})
			return
		}
		// 其他类型的错误，使用标准错误处理
		lc.HandleError(ctx, err, "获取授权信息")
		return
	}

	// 授权有效
	responseData := iris.Map{
		"status":      "active",
		"message":     "授权有效",
		"licensedTo":  licenseInfo.CustomerName,
		"expiresAt":   licenseInfo.ExpiryDate.Format("2006-01-02"),
		"licenseType": licenseInfo.LicenseType,
		"features":    licenseInfo.Features,
		"issueDate":   licenseInfo.IssueDate.Format("2006-01-02"),
	}
	lc.Success(ctx, responseData)
}

// UploadLicense 处理授权文件上传请求
// POST /api/v1/admin/license/upload
func (lc *LicenseControllerImpl) UploadLicense(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "license")

	// 1. 从 ctx.FormFile("licenseFile") 获取文件
	file, _, err := ctx.FormFile("licenseFile")
	if err != nil {
		if err == http.ErrMissingFile {
			lc.Fail(ctx, http.StatusBadRequest, "请选择要上传的授权文件 (字段名: licenseFile)")
		} else {
			lc.HandleError(ctx, fmt.Errorf("获取上传文件失败: %w", err), "上传授权文件")
		}
		return
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			lc.GetLogger().Errorf("关闭上传的授权文件失败: %v", err)
		}
	}(file) // 确保文件被关闭

	// 检查文件大小 (可选, 但推荐)
	// maxSize := int64(1 * 1024 * 1024) // 示例: 1MB
	// if info.Size > maxSize {
	// 	lc.Fail(ctx, http.StatusBadRequest, fmt.Sprintf("授权文件过大，请确保文件小于 %.2f MB", float64(maxSize)/1024/1024))
	// 	return
	// }

	// 2. 读取文件内容
	contentBytes, err := io.ReadAll(file)
	if err != nil {
		lc.HandleError(ctx, fmt.Errorf("读取授权文件内容失败: %w", err), "上传授权文件")
		return
	}

	if len(contentBytes) == 0 {
		lc.Fail(ctx, http.StatusBadRequest, "上传的授权文件内容为空")
		return
	}

	// 3. 调用 Service 层方法处理
	updatedLicenseInfo, err := lc.licenseService.UpdateLicenseFromUpload(ctx.Request().Context(), contentBytes)
	if err != nil {
		// Service 层应该返回具体的错误类型或消息
		lc.HandleError(ctx, err, "更新授权") // 使用 HandleError 记录日志并返回通用错误
		// 或者根据错误类型返回更具体的 HTTP 状态码和消息
		// response.Fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 4. 返回成功响应
	responseData := iris.Map{
		"customer": updatedLicenseInfo.CustomerName,
		"expiry":   updatedLicenseInfo.ExpiryDate.Format("2006-01-02"),
		"features": updatedLicenseInfo.Features,
		// 添加更多返回信息以便前端更新
		"licenseType": updatedLicenseInfo.LicenseType,
		"issueDate":   updatedLicenseInfo.IssueDate.Format("2006-01-02"),
		"message":     "授权文件上传并验证成功",
	}
	lc.SuccessWithMessage(ctx, "授权文件上传并验证成功", responseData)
}
