package repository

import (
	"backend/internal/model/dto" // 引入 DTO 用于 FindPage
	"backend/internal/model/entity"
	"context"
	"fmt"

	apperrors "backend/pkg/errors"

	"gorm.io/gorm"
)

// SystemParameterRepository 系统参数仓库接口
type SystemParameterRepository interface {
	BaseRepository[entity.SystemParameter, uint] // 嵌入基础 CRUD
	FindByKey(ctx context.Context, key string) (*entity.SystemParameter, error)
	FindPage(ctx context.Context, query dto.SystemParameterPageQueryDTO) ([]*entity.SystemParameter, int64, error)
}

type systemParameterRepository struct {
	BaseRepository[entity.SystemParameter, uint]
	// db *gorm.DB //不再需要单独持有，BaseRepository 已持有
}

// NewSystemParameterRepository 创建系统参数仓库实例
func NewSystemParameterRepository(db *gorm.DB) SystemParameterRepository {
	baseRepo := NewBaseRepository[entity.SystemParameter, uint](db)
	return &systemParameterRepository{
		BaseRepository: baseRepo,
	}
}

// FindByKey 根据参数键查询
func (r *systemParameterRepository) FindByKey(ctx context.Context, key string) (*entity.SystemParameter, error) {
	// 使用 r.FindOneByCondition 以便利用 BaseRepository 的错误处理和日志
	conditions := []QueryCondition{
		NewEqualCondition("param_key", key),
	}
	foundParam, err := r.BaseRepository.FindOneByCondition(ctx, conditions)
	if err != nil {
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("参数键 %s 未找到", key))
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "按参数键查询失败")
	}
	return foundParam, nil
}

// FindPage 分页查询
func (r *systemParameterRepository) FindPage(ctx context.Context, query dto.SystemParameterPageQueryDTO) ([]*entity.SystemParameter, int64, error) {
	// 1. 将 DTO 中的过滤条件转换为 []QueryCondition
	var conditions []QueryCondition
	if query.ParamKey != "" {
		conditions = append(conditions, NewLikeCondition("param_key", query.ParamKey))
	}
	if query.Name != "" {
		conditions = append(conditions, NewLikeCondition("name", query.Name))
	}
	if query.Status != nil {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.IsSystem != nil {
		conditions = append(conditions, NewEqualCondition("is_system", *query.IsSystem))
	}
	if query.ValueType != "" {
		conditions = append(conditions, NewEqualCondition("value_type", query.ValueType))
	}

	// 2. 调用 BaseRepository.FindByPage
	// 注意: query.PageQuery.Sort 中的 Field 应该直接是数据库列名。
	// 为了更安全和与 BaseRepository 的 getSortableColumns 机制一致，
	// 理想情况下，entity.SystemParameter 应有 'sortable:"true"' 和 'json' 标签，
	// 且 query.PageQuery.Sort.Field 应为 json 标签名。
	// 当前实现直接使用 Sort.Field 作为数据库列名，需确保调用方传入安全的列名。
	pageResult, err := r.BaseRepository.FindByPage(ctx, &query.PageQuery, conditions)
	if err != nil {
		// BaseRepository.FindByPage 内部已处理错误日志和包装
		return nil, 0, err
	}

	// 类型断言，因为 BaseRepository.FindByPage 返回 []*T
	params, ok := pageResult.List.([]*entity.SystemParameter)
	if !ok {
		// 这通常不应该发生，除非 BaseRepository.FindByPage 的实现有严重问题
		return nil, 0, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "分页结果类型断言失败")
	}

	return params, pageResult.Total, nil
}
