import request from '@/utils/request'

// 通用分页结果类型
export interface PageResult<T> {
  list: T[]
  total: number
}

// ==================== 类型定义 ====================

// 上架任务状态枚举
export type WmsPutawayTaskStatus =
  | 'PENDING'
  | 'ASSIGNED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED'
  | 'EXCEPTION'

// 上架任务查询请求
export interface WmsPutawayTaskQueryReq {
  pageNum?: number
  pageSize?: number
  taskNo?: string
  receivingRecordNo?: string
  status?: WmsPutawayTaskStatus
  assignedToUserId?: number
  warehouseId?: number
  createdAtFrom?: string
  createdAtTo?: string
  completedAtFrom?: string
  completedAtTo?: string
  sortField?: string
  sortOrder?: string
}

// 分配任务请求
export interface WmsPutawayTaskAssignReq {
  userId: number
}

// 上架任务明细响应
export interface WmsPutawayTaskDetailResp {
  id: number
  lineNo: number
  itemId: number
  itemSku: string
  itemName: string
  itemSpec?: string
  putawayQuantity: number
  completedQuantity?: number
  unitOfMeasure: string
  suggestedLocation?: string
  actualLocation?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  barcode?: string
  status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  sourceLocationId: number
  sourceLocationCode?: string
  suggestedLocationId?: number
  suggestedLocationCode?: string
  actualLocationId?: number
  actualLocationCode?: string
  actualPutawayQuantity?: number
  status: WmsPutawayTaskStatus
  remark?: string
}

// 上架任务响应
export interface WmsPutawayTaskResp {
  id: number
  createdAt: string
  updatedAt: string
  taskNo: string
  receivingRecordId: number
  receivingRecordNo?: string
  assignedToUserId?: number
  assignedToUserName?: string
  assignedUserName?: string  // 移动端显示用
  priority: number
  status: WmsPutawayTaskStatus
  putawayStrategy?: string  // 上架策略
  remark?: string
  completedAt?: string
  warehouseName?: string
  createdByUserName?: string
  details?: WmsPutawayTaskDetailResp[]

  // 移动端计算字段
  totalItems?: number
  completedItems?: number
  totalQuantity?: number
  completedQuantity?: number
  progress?: number  // 进度百分比
}

// 上架任务简要响应 (用于列表)
export interface WmsPutawayTaskSimpleResp {
    id: number
    createdAt: string
    taskNo: string
    receivingRecordId: number
    receivingRecordNo?: string
    assignedToUserId?: number
    assignedToUserName?: string
    priority: number
    status: WmsPutawayTaskStatus
    completedAt?: string
}


// ==================== API接口函数 ====================

export const getPutawayTaskPage = (params: WmsPutawayTaskQueryReq) => {
  return request<PageResult<WmsPutawayTaskSimpleResp>>({
    url: '/wms/putaway-tasks',
    method: 'get',
    params
  })
}

export const getPutawayTaskDetail = (id: number) => {
  return request<WmsPutawayTaskResp>({
    url: `/wms/putaway-tasks/${id}`,
    method: 'get'
  })
}

export const createPutawayTaskFromReceiving = (receivingId: number) => {
  return request<WmsPutawayTaskResp>({
    url: `/wms/putaway-tasks/from-receiving/${receivingId}`,
    method: 'post'
  })
}

export const assignUserToTask = (taskId: number, data: WmsPutawayTaskAssignReq) => {
  return request<void>({
    url: `/wms/putaway-tasks/${taskId}/assign`,
    method: 'put',
    data
  })
}

export const completePutawayTask = (taskId: number) => {
  return request<void>({
    url: `/wms/putaway-tasks/${taskId}/complete`,
    method: 'put'
  })
}

export const deletePutawayTask = (id: number) => {
  return request<void>({
    url: `/wms/putaway-tasks/${id}`,
    method: 'delete'
  })
}

// ==================== 移动端相关API ====================

// 开始上架任务
export const startPutawayTask = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/putaway-tasks/${id}/start`,
    method: 'post'
  }) as unknown as Promise<void>
}

// 完成上架任务（带备注）
export const completePutawayTaskWithRemark = (id: number, remark?: string): Promise<void> => {
  return request<void>({
    url: `/wms/putaway-tasks/${id}/complete`,
    method: 'put',
    data: { remark }
  }) as unknown as Promise<void>
}

// 暂停上架任务
export const pausePutawayTask = (id: number, remark?: string): Promise<void> => {
  return request<void>({
    url: `/wms/putaway-tasks/${id}/pause`,
    method: 'post',
    data: { remark }
  }) as unknown as Promise<void>
}

// 取消上架任务
export const cancelPutawayTask = (id: number, reason: string): Promise<void> => {
  return request<void>({
    url: `/wms/putaway-tasks/${id}/cancel`,
    method: 'post',
    data: { reason }
  }) as unknown as Promise<void>
}

// 上架明细操作
export const updatePutawayDetail = (taskId: number, detailId: number, data: {
  completedQuantity: number
  actualLocation?: string
  remark?: string
}): Promise<void> => {
  return request<void>({
    url: `/wms/putaway-tasks/${taskId}/details/${detailId}`,
    method: 'put',
    data
  }) as unknown as Promise<void>
}

// 批量上架
export const batchPutaway = (taskId: number, putaways: Array<{
  detailId: number
  completedQuantity: number
  actualLocation?: string
  remark?: string
}>): Promise<void> => {
  return request<void>({
    url: `/wms/putaway-tasks/${taskId}/batch-putaway`,
    method: 'post',
    data: { putaways }
  }) as unknown as Promise<void>
}