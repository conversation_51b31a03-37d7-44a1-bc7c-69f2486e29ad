package response

import (
	"io"
	"net/http"
	"time"

	"github.com/kataras/iris/v12"

	"backend/pkg/constant"
	"backend/pkg/errors"
)

// Response 统一响应结构
type Response struct {
	Success bool        `json:"success"`           // 是否成功
	Code    int         `json:"code"`              // 状态码，0表示成功，其他表示错误
	Message string      `json:"message"`           // 消息
	Data    interface{} `json:"data,omitempty"`    // 数据
	TraceID string      `json:"traceId,omitempty"` // 请求跟踪ID
	Time    int64       `json:"time,omitempty"`    // 响应时间（毫秒时间戳）
	Details interface{} `json:"details,omitempty"` // 错误详情，仅当Success为false时有值
}

// NewResponse 创建新的响应
// 返回:
//   - *Response: 响应对象
func NewResponse() *Response {
	return &Response{
		Success: true,
		Code:    constant.SUCCESS,
		Message: constant.GetCodeMessage(constant.SUCCESS),
		Time:    time.Now().UnixMilli(),
	}
}

// Success 返回成功响应
// 参数:
//   - ctx: Iris上下文
//   - data: 响应数据
func Success(ctx iris.Context, data interface{}) {
	resp := NewResponse()
	resp.Data = data

	// 获取请求追踪ID
	resp.TraceID = GetTraceID(ctx)

	ctx.StatusCode(http.StatusOK)
	ctx.JSON(resp)
}

// SuccessWithMessage 返回带消息的成功响应
// 参数:
//   - ctx: Iris上下文
//   - message: 响应消息
//   - data: 响应数据
func SuccessWithMessage(ctx iris.Context, message string, data interface{}) {
	resp := NewResponse()
	resp.Message = message
	resp.Data = data

	// 获取请求追踪ID
	resp.TraceID = GetTraceID(ctx)

	ctx.StatusCode(http.StatusOK)
	ctx.JSON(resp)
}

// Fail 返回失败响应
// 参数:
//   - ctx: Iris上下文
//   - code: 错误代码
//   - message: 错误消息
func Fail(ctx iris.Context, code int, message string) {
	resp := NewResponse()
	resp.Success = false
	resp.Code = code
	resp.Message = message

	// 获取请求追踪ID
	resp.TraceID = GetTraceID(ctx)

	ctx.StatusCode(http.StatusOK) // 默认仍返回200，但标记success为false
	ctx.JSON(resp)
}

// FailWithHttpStatus 返回带HTTP状态码的失败响应
// 参数:
//   - ctx: Iris上下文
//   - httpStatus: HTTP状态码
//   - code: 错误代码
//   - message: 错误消息
func FailWithHttpStatus(ctx iris.Context, httpStatus, code int, message string) {
	resp := NewResponse()
	resp.Success = false
	resp.Code = code
	resp.Message = message

	// 获取请求追踪ID
	resp.TraceID = GetTraceID(ctx)

	ctx.StatusCode(httpStatus)
	ctx.JSON(resp)
}

// FailWithError 使用错误对象返回失败响应
// 参数:
//   - ctx: Iris上下文
//   - err: 错误对象
func FailWithError(ctx iris.Context, err error) {
	var code int
	var message string
	var httpStatus int
	var details interface{}

	// 处理自定义错误
	if customErr, ok := err.(*errors.CustomError); ok {
		// 使用自定义错误
		code = customErr.Code
		message = customErr.Message
		httpStatus = customErr.GetHttpStatus()

		// 提取错误详情
		if len(customErr.Details) > 0 {
			details = customErr.Details
		} else if customErr.Metadata != nil && len(customErr.Metadata) > 0 {
			details = customErr.Metadata
		}
	} else {
		// 未知错误
		code = constant.SYS_ERROR
		message = err.Error()
		httpStatus = http.StatusInternalServerError
	}

	resp := NewResponse()
	resp.Success = false
	resp.Code = code
	resp.Message = message
	if details != nil {
		resp.Details = details
	}

	// 获取请求追踪ID
	if traceID := ctx.GetHeader("X-Request-ID"); traceID != "" {
		resp.TraceID = traceID
	} else if customErr, ok := err.(*errors.CustomError); ok {
		// 从自定义错误中尝试获取TraceID
		if metadata := customErr.Metadata; metadata != nil {
			if traceID, ok := metadata["traceId"].(string); ok && traceID != "" {
				resp.TraceID = traceID
			}
		}
	}

	ctx.StatusCode(httpStatus)
	ctx.JSON(resp)
}

// CustomSuccess 自定义成功响应
// 参数:
//   - ctx: Iris上下文
//   - code: 状态码
//   - message: 消息
//   - data: 响应数据
func CustomSuccess(ctx iris.Context, code int, message string, data interface{}) {
	resp := NewResponse()
	resp.Code = code
	resp.Message = message
	resp.Data = data

	// 获取请求追踪ID
	resp.TraceID = GetTraceID(ctx)

	ctx.StatusCode(http.StatusOK)
	ctx.JSON(resp)
}

// File 返回文件响应
// 参数:
//   - ctx: Iris上下文
//   - filepath: 文件路径
func File(ctx iris.Context, filepath string) {
	ctx.ServeFile(filepath)
}

// Download 返回文件下载响应
// 参数:
//   - ctx: Iris上下文
//   - filepath: 文件路径
//   - filename: 文件名
func Download(ctx iris.Context, filepath, filename string) {
	ctx.Header("Content-Disposition", "attachment; filename="+filename)
	ctx.ServeFile(filepath)
}

// XML 返回XML响应
// 参数:
//   - ctx: Iris上下文
//   - data: 响应数据
func XML(ctx iris.Context, data interface{}) {
	ctx.XML(data)
}

// YAML 返回YAML响应
// 参数:
//   - ctx: Iris上下文
//   - data: 响应数据
func YAML(ctx iris.Context, data interface{}) {
	ctx.YAML(data)
}

// JSONP 返回JSONP响应
// 参数:
//   - ctx: Iris上下文
//   - data: 响应数据
func JSONP(ctx iris.Context, data interface{}) {
	ctx.JSONP(data)
}

// StreamFile 流式传输文件
// 参数:
//   - ctx: Iris上下文
//   - reader: 文件读取器
//   - filename: 文件名
func StreamFile(ctx iris.Context, reader io.Reader, filename string) {
	ctx.Header("Content-Disposition", "attachment; filename="+filename)
	ctx.StreamWriter(func(w io.Writer) error {
		_, err := io.Copy(w, reader)
		return err
	})
}

// FailWithValidationError 返回验证错误响应
// 参数:
//   - ctx: Iris上下文
//   - field: 字段名
//   - message: 错误消息
func FailWithValidationError(ctx iris.Context, field, message string) {
	err := errors.NewValidationError(field, nil, message)
	FailWithError(ctx, err)
}

// GetTraceID 从上下文中获取TraceID
// 参数:
//   - ctx: Iris上下文
//
// 返回:
//   - string: TraceID
func GetTraceID(ctx iris.Context) string {
	if traceID := ctx.GetHeader("X-Request-ID"); traceID != "" {
		return traceID
	}
	if traceID := ctx.Values().GetString("traceId"); traceID != "" {
		return traceID
	}
	return ""
}

// FailWithCode 使用错误代码返回失败响应
// 参数:
//   - ctx: Iris上下文
//   - code: 错误代码
func FailWithCode(ctx iris.Context, code int) {
	message := constant.GetCodeMessage(code)
	Fail(ctx, code, message)
}

// JSON 返回自定义JSON响应
// 参数:
//   - ctx: Iris上下文
//   - statusCode: HTTP状态码
//   - data: 响应数据
func JSON(ctx iris.Context, statusCode int, data interface{}) {
	ctx.StatusCode(statusCode)
	ctx.JSON(data)
}

// Redirect 重定向
// 参数:
//   - ctx: Iris上下文
//   - url: 重定向URL
//   - statusCode: HTTP状态码
func Redirect(ctx iris.Context, url string, statusCode ...int) {
	if len(statusCode) > 0 {
		ctx.Redirect(url, statusCode[0])
	} else {
		ctx.Redirect(url, http.StatusFound)
	}
}

// NotFound 返回404响应
// 参数:
//   - ctx: Iris上下文
//   - message: 错误消息
func NotFound(ctx iris.Context, message string) {
	if message == "" {
		message = "资源不存在"
	}
	FailWithHttpStatus(ctx, http.StatusNotFound, constant.DATA_NOT_FOUND, message)
}

// Unauthorized 返回401响应
// 参数:
//   - ctx: Iris上下文
//   - message: 错误消息
func Unauthorized(ctx iris.Context, message string) {
	if message == "" {
		message = "未授权"
	}
	FailWithHttpStatus(ctx, http.StatusUnauthorized, constant.UNAUTHORIZED, message)
}

// Forbidden 返回403响应
// 参数:
//   - ctx: Iris上下文
//   - message: 错误消息
func Forbidden(ctx iris.Context, message string) {
	if message == "" {
		message = "禁止访问"
	}
	FailWithHttpStatus(ctx, http.StatusForbidden, constant.FORBIDDEN, message)
}

// ServerError 返回500响应
// 参数:
//   - ctx: Iris上下文
//   - message: 错误消息
func ServerError(ctx iris.Context, message string) {
	if message == "" {
		message = "服务器内部错误"
	}
	FailWithHttpStatus(ctx, http.StatusInternalServerError, constant.SYS_ERROR, message)
}

// FailWithMessage 返回带消息的失败响应
// 参数:
//   - message: 错误消息
//   - ctx: Iris上下文
func FailWithMessage(message string, ctx iris.Context) {
	Fail(ctx, constant.SYS_ERROR, message)
}

// OkWithData 返回带数据的成功响应
// 参数:
//   - data: 响应数据
//   - ctx: Iris上下文
func OkWithData(data interface{}, ctx iris.Context) {
	Success(ctx, data)
}

// Ok 返回简单成功响应
// 参数:
//   - ctx: Iris上下文
func Ok(ctx iris.Context) {
	Success(ctx, nil)
}

// ResponseToMap 转换响应对象为Map
// 参数:
//   - resp: 响应对象
//
// 返回:
//   - map[string]interface{}: Map形式的响应
func ResponseToMap(resp *Response) map[string]interface{} {
	result := map[string]interface{}{
		"success": resp.Success,
		"code":    resp.Code,
		"message": resp.Message,
		"time":    resp.Time,
	}

	if resp.Data != nil {
		result["data"] = resp.Data
	}

	if resp.TraceID != "" {
		result["traceId"] = resp.TraceID
	}

	if resp.Details != nil {
		result["details"] = resp.Details
	}

	return result
}
