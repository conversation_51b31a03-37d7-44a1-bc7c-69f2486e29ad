package vo

import (
	"time"

	"github.com/shopspring/decimal"
)

// CrmCustomerVO 客户详细视图对象
type CrmCustomerVO struct {
	// 基础字段
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	CreatedBy     uint      `json:"createdBy"`
	UpdatedBy     uint      `json:"updatedBy"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	// 客户基本信息
	CustomerCode        string           `json:"customerCode"`
	CustomerName        string           `json:"customerName"`
	CustomerType        string           `json:"customerType"`
	Industry            *string          `json:"industry,omitempty"`
	BusinessLicense     *string          `json:"businessLicense,omitempty"`
	TaxNumber           *string          `json:"taxNumber,omitempty"`
	LegalRepresentative *string          `json:"legalRepresentative,omitempty"`
	RegisteredCapital   *decimal.Decimal `json:"registeredCapital,omitempty"`

	// 联系信息
	ContactPerson *string `json:"contactPerson,omitempty"`
	ContactPhone  *string `json:"contactPhone,omitempty"`
	ContactEmail  *string `json:"contactEmail,omitempty"`
	Website       *string `json:"website,omitempty"`

	// 地址信息
	Country    *string `json:"country,omitempty"`
	Province   *string `json:"province,omitempty"`
	City       *string `json:"city,omitempty"`
	District   *string `json:"district,omitempty"`
	Address    *string `json:"address,omitempty"`
	PostalCode *string `json:"postalCode,omitempty"`

	// 财务信息
	CreditRating *string          `json:"creditRating,omitempty"`
	CreditLimit  *decimal.Decimal `json:"creditLimit,omitempty"`
	PaymentTerms *string          `json:"paymentTerms,omitempty"`
	CurrencyCode string           `json:"currencyCode"`

	// 业务信息
	CustomerLevel         string  `json:"customerLevel"`
	CustomerSource        *string `json:"customerSource,omitempty"`
	SalesRepresentativeID *uint   `json:"salesRepresentativeId,omitempty"`

	// 状态信息
	Status        string  `json:"status"`
	IsKeyCustomer bool    `json:"isKeyCustomer"`
	Remark        *string `json:"remark,omitempty"`

	// 关联信息
	Contacts []CrmCustomerContactVO `json:"contacts,omitempty"`

	// 扩展信息（可用于显示关联实体的名称）
	SalesRepresentativeName *string `json:"salesRepresentativeName,omitempty"`
}

// CrmCustomerListVO 客户列表视图对象（简化版，用于列表展示）
type CrmCustomerListVO struct {
	ID                      uint             `json:"id"`
	CustomerCode            string           `json:"customerCode"`
	CustomerName            string           `json:"customerName"`
	CustomerType            string           `json:"customerType"`
	Industry                *string          `json:"industry,omitempty"`
	ContactPerson           *string          `json:"contactPerson,omitempty"`
	ContactPhone            *string          `json:"contactPhone,omitempty"`
	ContactEmail            *string          `json:"contactEmail,omitempty"`
	CustomerLevel           string           `json:"customerLevel"`
	Status                  string           `json:"status"`
	IsKeyCustomer           bool             `json:"isKeyCustomer"`
	CreditLimit             *decimal.Decimal `json:"creditLimit,omitempty"`
	SalesRepresentativeID   *uint            `json:"salesRepresentativeId,omitempty"`
	SalesRepresentativeName *string          `json:"salesRepresentativeName,omitempty"`
	CreatedAt               time.Time        `json:"createdAt"`
	UpdatedAt               time.Time        `json:"updatedAt"`
}

// CrmCustomerContactVO 客户联系人详细视图对象
type CrmCustomerContactVO struct {
	// 基础字段
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	CreatedBy     uint      `json:"createdBy"`
	UpdatedBy     uint      `json:"updatedBy"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	// 关联信息
	CustomerID uint `json:"customerId"`

	// 联系人信息
	ContactName  string     `json:"contactName"`
	ContactTitle *string    `json:"contactTitle,omitempty"`
	Department   *string    `json:"department,omitempty"`
	Phone        *string    `json:"phone,omitempty"`
	Mobile       *string    `json:"mobile,omitempty"`
	Email        *string    `json:"email,omitempty"`
	QQ           *string    `json:"qq,omitempty"`
	Wechat       *string    `json:"wechat,omitempty"`
	Address      *string    `json:"address,omitempty"`
	PostalCode   *string    `json:"postalCode,omitempty"`
	Birthday     *time.Time `json:"birthday,omitempty"`
	Gender       *string    `json:"gender,omitempty"`
	ContactRole  *string    `json:"contactRole"`
	Remark       *string    `json:"remark,omitempty"`

	// 扩展信息
	CustomerName *string `json:"customerName,omitempty"`
}

// CrmCustomerContactListVO 客户联系人列表视图对象（简化版，用于列表展示）
type CrmCustomerContactListVO struct {
	ID           uint      `json:"id"`
	CustomerID   uint      `json:"customerId"`
	CustomerName *string   `json:"customerName,omitempty"`
	ContactName  string    `json:"contactName"`
	ContactTitle *string   `json:"contactTitle,omitempty"`
	Department   *string   `json:"department,omitempty"`
	Phone        *string   `json:"phone,omitempty"`
	Mobile       *string   `json:"mobile,omitempty"`
	Email        *string   `json:"email,omitempty"`
	ContactRole  *string   `json:"contactRole"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// CrmCustomerSummaryVO 客户摘要视图对象（用于统计和仪表板）
type CrmCustomerSummaryVO struct {
	TotalCustomers      int64 `json:"totalCustomers"`      // 客户总数
	ActiveCustomers     int64 `json:"activeCustomers"`     // 活跃客户数
	InactiveCustomers   int64 `json:"inactiveCustomers"`   // 停用客户数
	BlacklistCustomers  int64 `json:"blacklistCustomers"`  // 黑名单客户数
	VipCustomers        int64 `json:"vipCustomers"`        // VIP客户数
	GoldCustomers       int64 `json:"goldCustomers"`       // 金牌客户数
	SilverCustomers     int64 `json:"silverCustomers"`     // 银牌客户数
	NormalCustomers     int64 `json:"normalCustomers"`     // 普通客户数
	KeyCustomers        int64 `json:"keyCustomers"`        // 重点客户数
	CorporateCustomers  int64 `json:"corporateCustomers"`  // 企业客户数
	IndividualCustomers int64 `json:"individualCustomers"` // 个人客户数
}

// CrmCustomerSimpleVO 客户简单视图对象（用于下拉选择）
type CrmCustomerSimpleVO struct {
	ID           uint   `json:"id"`
	CustomerCode string `json:"customerCode"`
	CustomerName string `json:"customerName"`
	CustomerType string `json:"customerType"`
	Status       string `json:"status"`
}
