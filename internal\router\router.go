package router

import (
	"net/http"

	"github.com/kataras/iris/v12"

	"backend/internal/controller"
	"backend/internal/middleware"
	"backend/internal/repository"
)

// SetupRoutes configures the routes for the application.
func SetupRoutes(
	app *iris.Application,
	repoManager *repository.RepositoryManager,
	authController *controller.AuthControllerImpl,
	captchaController *controller.CaptchaControllerImpl,
	roleController *controller.RoleControllerImpl,
	menuController *controller.MenuControllerImpl,
	userController *controller.UserControllerImpl,
	accountBookController *controller.AccountBookControllerImpl,
	orgNodeController *controller.OrganizationNodeControllerImpl,
	userAccountBookController *controller.UserAccountBookControllerImpl,
	dictController *controller.DictionaryControllerImpl,
	employeeController *controller.EmployeeControllerImpl,
	authMiddleware *middleware.AuthMiddleware,
	systemParameterController *controller.SystemParameterControllerImpl,
	sqlToolController *controller.SQLToolControllerImpl,
	fileController *controller.FileControllerImpl,
	licenseController *controller.LicenseControllerImpl,
	auditLogController *controller.AuditLogControllerImpl,
	auditMiddleware *middleware.AuditMiddleware,
	fiscalPeriodController *controller.FiscalPeriodControllerImpl,
	finCurrencyController *controller.FinCurrencyControllerImpl,
	finTaxRateController *controller.FinTaxRateControllerImpl,
	finExchangeRateController *controller.FinExchangeRateControllerImpl,
	wmsLocationController *controller.WmsLocationControllerImpl,
	mtlItemController controller.MtlItemController,
	sysCodeRuleController *controller.SysCodeRuleControllerImpl,
	crmCustomerController controller.CrmCustomerController,
	scmSupplierController controller.ScmSupplierController,
	wmsInboundNotificationController *controller.WmsInboundNotificationControllerImpl,
	wmsReceivingRecordController *controller.WmsReceivingRecordControllerImpl,
	wmsPutawayTaskController *controller.WmsPutawayTaskControllerImpl,
	wmsBlindReceivingConfigController controller.WmsBlindReceivingConfigController,
	wmsBlindReceivingValidationController controller.WmsBlindReceivingValidationController,
	wmsOutboundNotificationController *controller.WmsOutboundNotificationControllerImpl,
	wmsPickingTaskController *controller.WmsPickingTaskControllerImpl,
	wmsInventoryAllocationController *controller.WmsInventoryAllocationControllerImpl,
	wmsShipmentController *controller.WmsShipmentControllerImpl,
	// 库存管理模块控制器
	wmsInventoryQueryController *controller.WmsInventoryQueryControllerImpl,
	wmsInventoryAdjustmentController *controller.WmsInventoryAdjustmentControllerImpl,
	wmsInventoryMovementController *controller.WmsInventoryMovementController,
	wmsCycleCountController *controller.WmsCycleCountControllerImpl,
	wmsInventoryAlertController *controller.WmsInventoryAlertControllerImpl,
) {
	apiV1 := app.Party("/api/v1")
	{
		// === 公共路由 (不需要认证) ===
		authParty := apiV1.Party("/auth")
		{
			authParty.Post("/login", authController.Login)
			authParty.Get("/captcha", captchaController.GetCaptcha)
		}

		// === 应用认证中间件 ===
		apiV1.Use(authMiddleware.Serve)

		// === 应用授权验证中间件 (新增) ===
		// 这将应用于所有后续的 /api/v1 路由
		// 如果某些路由需要特定功能，可以在其子路由组中再次应用此中间件并传入功能名称
		apiV1.Use(middleware.LicenseCheckMiddleware())

		// === 应用审计中间件 (新增) ===
		if auditMiddleware != nil {
			apiV1.Use(auditMiddleware.Serve)
		}

		// === 受保护的路由 (需要认证和授权) ===
		// --- 系统级资源 (不需要账套隔离) ---
		sysParty := apiV1.Party("/sys")
		{
			// --- User Management ---
			userParty := sysParty.Party("/users")
			{
				userParty.Post("", userController.CreateUser)
				userParty.Put("/{id:uint64}", userController.UpdateUser)
				userParty.Delete("/{id:uint64}", userController.DeleteUser)
				userParty.Get("/{id:uint64}", userController.GetUserByID)
				userParty.Get("/page", userController.PageUsers)
				userParty.Put("/{id:uint64}/password", userController.UpdatePassword)
				userParty.Put("/{id:uint64}/status", userController.UpdateStatus)
				userParty.Put("/{id:uint64}/admin-reset-password", userController.AdminResetPassword)
				userRoleParty := userParty.Party("/{id:uint64}/roles")
				{
					userRoleParty.Get("", userController.GetUserRoles)
					userRoleParty.Put("", userController.UpdateUserRoles)
				}
				userAccountBookParty := userParty.Party("/{userId:uint64}/account-books")
				{
					userAccountBookParty.Get("", userAccountBookController.GetUserAccountBooks)
					userAccountBookParty.Put("", userAccountBookController.UpdateUserAccountBooks)
				}
			}

			// --- Role Management (Global) ---
			roleParty := sysParty.Party("/roles")
			{
				roleParty.Post("", roleController.CreateRole)
				roleParty.Put("/{id:uint64}", roleController.UpdateRole)
				roleParty.Delete("/{id:uint64}", roleController.DeleteRole)
				roleParty.Get("/{id:uint64}", roleController.GetRoleByID)
				roleParty.Get("/page", roleController.PageRoles)
				roleMenuParty := roleParty.Party("/{id:uint64}/menus")
				{
					roleMenuParty.Get("", roleController.GetRoleMenus)
					roleMenuParty.Put("", roleController.UpdateRoleMenus)
				}
			}

			// --- Menu Management (Global) ---
			menuParty := sysParty.Party("/menus")
			{
				menuParty.Post("", menuController.CreateMenu)
				menuParty.Put("/{id:uint64}", menuController.UpdateMenu)
				menuParty.Delete("/{id:uint64}", menuController.DeleteMenu)
				menuParty.Get("/{id:uint64}", menuController.GetMenuByID)
				menuParty.Get("/list", menuController.ListMenus)
				menuParty.Get("/tree", menuController.GetMenuTree)
			}

			// --- Account Book Management (Global) ---
			accountBookParty := sysParty.Party("/account-books")
			{
				accountBookParty.Post("", accountBookController.CreateAccountBook)
				accountBookParty.Put("/{id:uint64}", accountBookController.UpdateAccountBook)
				accountBookParty.Delete("/{id:uint64}", accountBookController.DeleteAccountBook)
				accountBookParty.Get("/{id:uint64}", accountBookController.GetAccountBookByID)
				accountBookParty.Get("", accountBookController.PageAccountBooks)
				accountBookParty.Get("/list", accountBookController.ListAccountBooks)
				accountBookParty.Patch("/{id:uint64}/status", accountBookController.UpdateAccountBookStatus)
			}

			// --- Data Dictionary Management (系统级, 需要权限) ---
			dictParty := sysParty.Party("/dict")
			// TODO: 在此处或父级 sysParty 添加权限检查中间件
			// dictParty.Use(middleware.RequirePermission("dict:manage")) // 示例权限检查
			{
				// Dictionary Type Routes
				typeParty := dictParty.Party("/types")
				{
					typeParty.Post("", dictController.CreateDictType)
					typeParty.Put("/{id:uint}", dictController.UpdateDictType) // 使用 :uint 约束
					typeParty.Delete("/{id:uint}", dictController.DeleteDictType)
					typeParty.Get("/{id:uint}", dictController.GetDictTypeByID)
					typeParty.Get("/page", dictController.PageDictTypes)
					typeParty.Get("/list", dictController.ListEnabledDictTypes) // 获取启用列表
				}

				// Dictionary Item Routes
				itemParty := dictParty.Party("/items")
				{
					itemParty.Post("", dictController.CreateDictItem)
					itemParty.Put("/{id:uint}", dictController.UpdateDictItem)
					itemParty.Delete("/{id:uint}", dictController.DeleteDictItem)
					itemParty.Get("/{id:uint}", dictController.GetDictItemByID)
					itemParty.Get("/page", dictController.PageDictItems)
					// 公共接口：根据类型编码获取字典项列表
					itemParty.Get("/list", dictController.GetDictItemsByTypeCode)
				}
			} // end dictParty

			// --- System Parameter Management (系统级, 需要权限) ---
			paramParty := sysParty.Party("/parameters")
			// TODO: 添加权限检查中间件
			// paramParty.Use(middleware.RequirePermission("system:parameter:manage"))
			{
				// 显式注册 SystemParameterController 的路由
				paramParty.Post("", systemParameterController.Create)
				paramParty.Put("/{id:uint}", systemParameterController.Update)
				paramParty.Delete("/{id:uint}", systemParameterController.Delete)
				paramParty.Get("/{id:uint}", systemParameterController.GetByID)
				paramParty.Get("/page", systemParameterController.Page) // 使用空的相对路径代表 "/"
				// systemParameterController.BeforeActivation(paramParty) // 移除这行
			}

			// --- Code Rule Management (编码规则管理, 系统级) ---
			codeRuleParty := sysParty.Party("/code-rules")
			codeRuleParty.Use(middleware.AccountBookContextHandler(repoManager)) // 应用账套上下文中间件
			{
				// 编码规则 CRUD
				codeRuleParty.Post("", sysCodeRuleController.CreateCodeRule)
				codeRuleParty.Put("/{id:uint64}", sysCodeRuleController.UpdateCodeRule)
				codeRuleParty.Delete("/{id:uint64}", sysCodeRuleController.DeleteCodeRule)
				codeRuleParty.Get("/{id:uint64}", sysCodeRuleController.GetCodeRule)
				codeRuleParty.Get("", sysCodeRuleController.GetCodeRulePage)

				// 设置默认规则
				codeRuleParty.Post("/{id:uint64}/set-default", sysCodeRuleController.SetAsDefault)

				// 获取选项数据
				codeRuleParty.Get("/business-type-options", sysCodeRuleController.GetBusinessTypeOptions)
				codeRuleParty.Get("/reset-frequency-options", sysCodeRuleController.GetResetFrequencyOptions)
			}

			// --- Code Generation (编码生成服务) ---
			codeGenParty := sysParty.Party("/code-generation")
			{
				codeGenParty.Post("/generate", sysCodeRuleController.GenerateCode)
				codeGenParty.Post("/preview", sysCodeRuleController.PreviewCode)
				codeGenParty.Post("/reset-sequence", sysCodeRuleController.ResetSequence)
			}
		}

		// --- HR 资源 (不需要账套隔离) ---
		htParty := apiV1.Party("/hr")
		// htParty.Use(middleware.AccountBookContextHandler(repoManager))
		{

			// Organization Routes (不需要账套隔离)
			orgParty := htParty.Party("/organization")
			// orgParty.Use(middleware.AccountBookContextHandler(repoManager))
			{
				nodeParty := orgParty.Party("/nodes")
				{
					nodeParty.Post("", orgNodeController.CreateNode)
					nodeParty.Put("/{id:uint64}", orgNodeController.UpdateNode)
					nodeParty.Delete("/{id:uint64}", orgNodeController.DeleteNode)
					nodeParty.Get("/{id:uint64}", orgNodeController.GetNodeByID)
					nodeParty.Get("", orgNodeController.GetNodeList)
					nodeParty.Patch("/{id:uint64}/status", orgNodeController.UpdateNodeStatus)
				}
				// 获取完整组织树
				orgParty.Get("/tree", orgNodeController.GetOrganizationTree)
			}

			// Employee Routes (不需要账套隔离)
			employeeParty := htParty.Party("/employees")
			// employeeParty.Use(middleware.AccountBookContextHandler(repoManager)) // AccountBook隔离已根据用户要求移除
			{
				employeeParty.Post("", employeeController.CreateEmployee)
				employeeParty.Put("/{id:uint64}", employeeController.UpdateEmployee)
				employeeParty.Delete("/{id:uint64}", employeeController.DeleteEmployeeByID)
				employeeParty.Post("/batch-delete", employeeController.DeleteEmployees)
				employeeParty.Get("/{id:uint64}", employeeController.GetEmployeeByID)
				employeeParty.Get("/code/{code:string}", employeeController.GetEmployeeByCode)
				employeeParty.Get("/page", employeeController.GetEmployeePage)
				employeeParty.Get("/simple-list", employeeController.GetEmployeeSimpleList)
				employeeParty.Put("/{id:uint64}/status", employeeController.UpdateEmployeeStatus)
				// 新增员工头像上传路由
				employeeParty.Post("/avatar/upload", employeeController.UploadEmployeeAvatar)
			}
		}

		// --- 当前用户相关路由 (通常不需要账套头，基于用户自身信息) ---
		userSpecificParty := apiV1.Party("/user")
		{
			userSpecificParty.Get("/menus/tree", menuController.GetUserMenuTree)
			userSpecificParty.Get("/permissions", menuController.GetUserPermissions)
			userSpecificParty.Get("/account-books", userAccountBookController.GetCurrentUserAccountBooks)

			// +++ Add Profile Routes +++
			profileParty := userSpecificParty.Party("/profile")
			{
				profileParty.Get("", userController.GetProfile)    // GET /api/v1/user/profile
				profileParty.Put("", userController.UpdateProfile) // PUT /api/v1/user/profile
				// TODO: Add route for changing current user's password if needed
				// profileParty.Put("/password", userController.UpdateCurrentUserPassword)
			}
			// +++ End Profile Routes +++
			// 新增：用户头像上传路由
			userSpecificParty.Post("/profile/avatar", userController.UploadAvatar) // POST /api/v1/user/profile/avatar
		}

		// === 管理员特定路由 (需要认证和管理员权限) ===
		adminParty := apiV1.Party("/admin")
		{
			// --- SQL Tool (极度危险，仅限管理员) ---
			sqlToolParty := adminParty.Party("/sql-tool")
			adminParty.Use(middleware.AdminRequiredMiddleware())
			{
				sqlToolParty.Post("", sqlToolController.ExecuteSQL) // POST /api/v1/admin/sql-tool
			}

			// +++ Add License Upload Route +++
			licenseParty := adminParty.Party("/license")
			{
				licenseParty.Post("/upload", licenseController.UploadLicense)
				licenseParty.Get("/info", licenseController.GetLicenseInfo)
			}
			// +++ End License Upload Route +++

			// +++ Add Audit Log API Route (新增) +++
			if auditLogController != nil {
				auditLogsParty := adminParty.Party("/audit")
				{
					auditLogsParty.Get("", auditLogController.ListAuditLogs)
				}
			}
			// +++ End Audit Log API Route +++
		}

		// --- 财务资源 (需要账套隔离) ---
		finParty := apiV1.Party("/fin")
		{
			// 会计期间路由
			periodParty := finParty.Party("/periods")
			periodParty.Use(middleware.AccountBookContextHandler(repoManager)) // 应用账套上下文中间件
			{
				periodParty.Post("", fiscalPeriodController.GenerateForYear)
				periodParty.Put("/{id:uint64}/status", fiscalPeriodController.ChangeStatus)
				periodParty.Get("", fiscalPeriodController.List)
			}

			// 货币路由
			currencyParty := finParty.Party("/currencies")
			{
				currencyParty.Post("", finCurrencyController.Create)
				currencyParty.Put("/{id:uint}", finCurrencyController.Update)
				currencyParty.Delete("/{id:uint}", finCurrencyController.Delete)
				currencyParty.Get("/{id:uint}", finCurrencyController.GetByID)
				currencyParty.Get("/page", finCurrencyController.GetPage)
				currencyParty.Get("/list", finCurrencyController.GetList)
			}

			// 税率路由
			taxRateParty := finParty.Party("/tax-rates")
			{
				taxRateParty.Post("", finTaxRateController.Create)
				taxRateParty.Put("/{id:uint}", finTaxRateController.Update)
				taxRateParty.Delete("/{id:uint}", finTaxRateController.Delete)
				taxRateParty.Get("/{id:uint}", finTaxRateController.GetByID)
				taxRateParty.Get("/page", finTaxRateController.GetPage)
			}

			// 汇率路由
			exchangeRateParty := finParty.Party("/exchange-rates")
			{
				exchangeRateParty.Post("", finExchangeRateController.Create)
				exchangeRateParty.Put("/{id:uint}", finExchangeRateController.Update)
				exchangeRateParty.Delete("/{id:uint}", finExchangeRateController.Delete)
				exchangeRateParty.Get("/{id:uint}", finExchangeRateController.GetByID)
				exchangeRateParty.Get("/page", finExchangeRateController.GetPage)
				exchangeRateParty.Get("/latest", finExchangeRateController.GetLatest)
			}
		}

		// --- Warehouse Management System (WMS) 资源 (需要账套隔离) ---
		wmsParty := apiV1.Party("/wms")
		wmsParty.Use(middleware.AccountBookContextHandler(repoManager))
		{
			locationParty := wmsParty.Party("/locations")
			{
				locationParty.Post("", wmsLocationController.Create)
				locationParty.Put("/{id:uint}", wmsLocationController.Update)
				locationParty.Delete("/{id:uint}", wmsLocationController.Delete)
				locationParty.Get("/{id:uint}", wmsLocationController.GetByID)
				locationParty.Get("/page", wmsLocationController.GetPage)
				locationParty.Get("/tree", wmsLocationController.GetTree)
				locationParty.Get("/list", wmsLocationController.GetList)
			}

			itemParty := wmsParty.Party("/items")
			{
				itemParty.Post("", mtlItemController.Post)                 // 创建物料
				itemParty.Put("/{id:uint}", mtlItemController.PutBy)       // 更新物料
				itemParty.Delete("/{id:uint}", mtlItemController.DeleteBy) // 删除物料
				itemParty.Get("/{id:uint}", mtlItemController.GetBy)       // 获取物料详情
				itemParty.Get("", mtlItemController.Get)                   // 分页查询物料

				// 包装单位管理路由
				itemParty.Post("/{itemId:uint}/package-units", mtlItemController.AddPackageUnit)                    // 添加包装单位
				itemParty.Put("/{itemId:uint}/package-units/{unitId:uint}", mtlItemController.UpdatePackageUnit)    // 更新包装单位
				itemParty.Delete("/{itemId:uint}/package-units/{unitId:uint}", mtlItemController.RemovePackageUnit) // 删除包装单位
				itemParty.Get("/{itemId:uint}/package-units", mtlItemController.GetPackageUnits)                    // 获取包装单位列表
			}

			// --- 入库流程管理 ---
			// 入库通知单路由
			inboundNotificationParty := wmsParty.Party("/inbound-notifications")
			{
				inboundNotificationParty.Post("", wmsInboundNotificationController.Create)
				inboundNotificationParty.Put("/{id:uint}", wmsInboundNotificationController.Update)
				inboundNotificationParty.Delete("/{id:uint}", wmsInboundNotificationController.Delete)
				inboundNotificationParty.Get("/{id:uint}", wmsInboundNotificationController.GetByID)
				inboundNotificationParty.Get("", wmsInboundNotificationController.GetPage)
				inboundNotificationParty.Post("/batch-import", wmsInboundNotificationController.BatchImport)
				inboundNotificationParty.Put("/{id:uint}/status", wmsInboundNotificationController.UpdateStatus)
			}

			// 收货记录路由
			receivingRecordParty := wmsParty.Party("/receiving-records")
			{
				receivingRecordParty.Post("", wmsReceivingRecordController.Create)
				receivingRecordParty.Post("/from-notification/{notificationId:uint64}", wmsReceivingRecordController.CreateFromNotification)
				receivingRecordParty.Post("/blind-receiving", wmsReceivingRecordController.CreateBlindReceiving)
				receivingRecordParty.Put("/{id:uint64}", wmsReceivingRecordController.Update)
				receivingRecordParty.Delete("/{id:uint64}", wmsReceivingRecordController.Delete)
				receivingRecordParty.Get("/{id:uint64}", wmsReceivingRecordController.GetByID)
				receivingRecordParty.Get("", wmsReceivingRecordController.GetPage)
				receivingRecordParty.Get("/blind-receiving", wmsReceivingRecordController.GetBlindReceivingRecords)
				receivingRecordParty.Put("/{id:uint64}/status", wmsReceivingRecordController.UpdateStatus)
			}

			// 上架任务路由
			putawayTaskParty := wmsParty.Party("/putaway-tasks")
			{
				putawayTaskParty.Post("", wmsPutawayTaskController.Create)
				putawayTaskParty.Post("/from-receiving/{receivingId:uint64}", wmsPutawayTaskController.CreateFromReceiving)
				putawayTaskParty.Put("/{id:uint64}", wmsPutawayTaskController.Update)
				putawayTaskParty.Delete("/{id:uint64}", wmsPutawayTaskController.Delete)
				putawayTaskParty.Get("/{id:uint64}", wmsPutawayTaskController.GetByID)
				putawayTaskParty.Get("", wmsPutawayTaskController.GetPage)
				putawayTaskParty.Put("/{id:uint64}/assign", wmsPutawayTaskController.AssignToUser)
				putawayTaskParty.Put("/batch-assign", wmsPutawayTaskController.BatchAssignToUser)
				putawayTaskParty.Put("/{id:uint64}/complete", wmsPutawayTaskController.CompleteTask)
				putawayTaskParty.Get("/pending", wmsPutawayTaskController.GetPendingTasks)
				putawayTaskParty.Get("/by-user/{userId:uint64}", wmsPutawayTaskController.GetTasksByUser)
			}

			// --- 盲收配置管理 ---
			blindReceivingConfigParty := wmsParty.Party("/blind-receiving-configs")
			{
				// 基础CRUD操作
				blindReceivingConfigParty.Post("", wmsBlindReceivingConfigController.Create)
				blindReceivingConfigParty.Put("/{id:uint}", wmsBlindReceivingConfigController.Update)
				blindReceivingConfigParty.Delete("/{id:uint}", wmsBlindReceivingConfigController.Delete)
				blindReceivingConfigParty.Get("/{id:uint}", wmsBlindReceivingConfigController.GetByID)
				blindReceivingConfigParty.Get("", wmsBlindReceivingConfigController.GetPage)

				// 批量操作
				blindReceivingConfigParty.Post("/batch", wmsBlindReceivingConfigController.CreateBatch)
				blindReceivingConfigParty.Put("/batch", wmsBlindReceivingConfigController.UpdateBatch)

				// 核心业务接口
				blindReceivingConfigParty.Get("/effective", wmsBlindReceivingConfigController.GetEffectiveConfig)
				blindReceivingConfigParty.Post("/validate", wmsBlindReceivingConfigController.ValidateBlindReceiving)

				// 配置管理
				blindReceivingConfigParty.Get("/targets", wmsBlindReceivingConfigController.GetAvailableTargets)
				blindReceivingConfigParty.Get("/active", wmsBlindReceivingConfigController.GetActiveConfigs)
				blindReceivingConfigParty.Get("/by-strategy", wmsBlindReceivingConfigController.GetConfigsByStrategy)

				// 业务流程
				blindReceivingConfigParty.Post("/approval", wmsBlindReceivingConfigController.ProcessApproval)
				blindReceivingConfigParty.Post("/supplement", wmsBlindReceivingConfigController.ProcessSupplement)

				// 业务流程查询
				blindReceivingConfigParty.Get("/pending-approvals", wmsBlindReceivingConfigController.GetPendingApprovalList)
				blindReceivingConfigParty.Get("/pending-supplements", wmsBlindReceivingConfigController.GetPendingSupplementList)
				blindReceivingConfigParty.Get("/overdue-supplements", wmsBlindReceivingConfigController.GetOverdueSupplementList)

				// 统计分析
				blindReceivingConfigParty.Get("/stats", wmsBlindReceivingConfigController.GetStats)
			}

			// --- 盲收验证记录管理 ---
			blindReceivingValidationParty := wmsParty.Party("/blind-receiving-validation")
			{
				// 核心业务接口
				blindReceivingValidationParty.Post("/validate", wmsBlindReceivingValidationController.ValidateBlindReceiving)
				blindReceivingValidationParty.Post("/process", wmsBlindReceivingValidationController.ProcessValidation)

				// 基础CRUD操作
				blindReceivingValidationParty.Post("", wmsBlindReceivingValidationController.Create)
				blindReceivingValidationParty.Put("/{id:uint}", wmsBlindReceivingValidationController.Update)
				blindReceivingValidationParty.Delete("/{id:uint}", wmsBlindReceivingValidationController.Delete)
				blindReceivingValidationParty.Get("/{id:uint}", wmsBlindReceivingValidationController.GetByID)

				// 查询操作
				blindReceivingValidationParty.Get("/config/{configId:uint}", wmsBlindReceivingValidationController.GetByConfigID)
				blindReceivingValidationParty.Get("/warehouse-customer", wmsBlindReceivingValidationController.GetByWarehouseAndCustomer)
				blindReceivingValidationParty.Get("/pending", wmsBlindReceivingValidationController.GetPendingValidations)
				blindReceivingValidationParty.Post("/history", wmsBlindReceivingValidationController.GetValidationHistory)

				// 统计分析
				blindReceivingValidationParty.Post("/stats", wmsBlindReceivingValidationController.GetValidationStats)
			}

			// --- 出库流程管理 ---
			// 出库通知单路由
			outboundNotificationParty := wmsParty.Party("/outbound-notifications")
			{
				// 基础CRUD操作
				outboundNotificationParty.Post("", wmsOutboundNotificationController.Create)
				outboundNotificationParty.Put("/{id:uint}", wmsOutboundNotificationController.Update)
				outboundNotificationParty.Delete("/{id:uint}", wmsOutboundNotificationController.Delete)
				outboundNotificationParty.Get("/{id:uint}", wmsOutboundNotificationController.GetByID)
				outboundNotificationParty.Get("", wmsOutboundNotificationController.GetPage)

				// 批量操作
				outboundNotificationParty.Post("/batch-create", wmsOutboundNotificationController.BatchCreate)
				outboundNotificationParty.Post("/batch-approve", wmsOutboundNotificationController.BatchApprove)
				outboundNotificationParty.Post("/batch-cancel", wmsOutboundNotificationController.BatchCancel)

				// 状态管理
				outboundNotificationParty.Post("/{id:uint}/approve", wmsOutboundNotificationController.Approve)
				outboundNotificationParty.Post("/{id:uint}/cancel", wmsOutboundNotificationController.Cancel)

				// 库存分配
				outboundNotificationParty.Post("/{id:uint}/allocate-inventory", wmsOutboundNotificationController.AllocateInventory)
				outboundNotificationParty.Post("/batch-allocate-inventory", wmsOutboundNotificationController.BatchAllocateInventory)
				outboundNotificationParty.Get("/{id:uint}/allocation-status", wmsOutboundNotificationController.GetAllocationStatus)

				// 拣货任务生成
				outboundNotificationParty.Post("/{id:uint}/generate-picking-task", wmsOutboundNotificationController.GeneratePickingTask)
				outboundNotificationParty.Post("/batch-generate-picking-task", wmsOutboundNotificationController.BatchGeneratePickingTask)

				// 统计分析
				outboundNotificationParty.Get("/stats", wmsOutboundNotificationController.GetStats)

				// 导入导出
				outboundNotificationParty.Post("/import-excel", wmsOutboundNotificationController.ImportFromExcel)
				outboundNotificationParty.Get("/export-excel", wmsOutboundNotificationController.ExportToExcel)

				// 业务查询
				outboundNotificationParty.Get("/by-notification-no/{notificationNo:string}", wmsOutboundNotificationController.GetByNotificationNo)
				outboundNotificationParty.Get("/by-client-order-no/{clientOrderNo:string}", wmsOutboundNotificationController.GetByClientOrderNo)
				outboundNotificationParty.Get("/pending-allocation", wmsOutboundNotificationController.GetPendingAllocation)
				outboundNotificationParty.Get("/ready-for-picking", wmsOutboundNotificationController.GetReadyForPicking)
				outboundNotificationParty.Get("/overdue-shipments", wmsOutboundNotificationController.GetOverdueShipments)
			}

			// 拣货任务路由
			pickingTaskParty := wmsParty.Party("/picking-tasks")
			{
				// 基础CRUD操作
				pickingTaskParty.Post("", wmsPickingTaskController.Create)
				pickingTaskParty.Put("/{id:uint}", wmsPickingTaskController.Update)
				pickingTaskParty.Delete("/{id:uint}", wmsPickingTaskController.Delete)
				pickingTaskParty.Get("/{id:uint}", wmsPickingTaskController.GetByID)
				pickingTaskParty.Get("", wmsPickingTaskController.GetPage)

				// 任务管理
				pickingTaskParty.Post("/{id:uint}/assign", wmsPickingTaskController.AssignTask)
				pickingTaskParty.Post("/batch-assign", wmsPickingTaskController.BatchAssignTask)
				pickingTaskParty.Post("/{id:uint}/start", wmsPickingTaskController.StartTask)
				pickingTaskParty.Post("/{id:uint}/complete", wmsPickingTaskController.CompleteTask)
				pickingTaskParty.Post("/{id:uint}/cancel", wmsPickingTaskController.CancelTask)

				// 拣货执行
				pickingTaskParty.Post("/execute-picking", wmsPickingTaskController.ExecutePicking)
				pickingTaskParty.Post("/batch-execute-picking", wmsPickingTaskController.BatchExecutePicking)
				pickingTaskParty.Post("/handle-exception", wmsPickingTaskController.HandleException)

				// 业务查询
				pickingTaskParty.Get("/by-task-no/{taskNo:string}", wmsPickingTaskController.GetByTaskNo)
				pickingTaskParty.Get("/by-notification/{notificationID:uint}", wmsPickingTaskController.GetByNotificationID)
				pickingTaskParty.Get("/pending-assignment", wmsPickingTaskController.GetPendingAssignment)
			}

			// 库存分配路由
			inventoryAllocationParty := wmsParty.Party("/inventory-allocations")
			{
				// 基础CRUD操作
				inventoryAllocationParty.Post("", wmsInventoryAllocationController.Create)
				inventoryAllocationParty.Put("/{id:uint}", wmsInventoryAllocationController.Update)
				inventoryAllocationParty.Delete("/{id:uint}", wmsInventoryAllocationController.Delete)
				inventoryAllocationParty.Get("/{id:uint}", wmsInventoryAllocationController.GetByID)
				inventoryAllocationParty.Get("", wmsInventoryAllocationController.GetPage)

				// 自动分配
				inventoryAllocationParty.Post("/auto-allocate", wmsInventoryAllocationController.AutoAllocate)
				inventoryAllocationParty.Post("/batch-auto-allocate", wmsInventoryAllocationController.BatchAutoAllocate)

				// 拣货确认和释放分配
				inventoryAllocationParty.Post("/{id:uint}/pick-confirm", wmsInventoryAllocationController.PickConfirm)
				inventoryAllocationParty.Post("/{id:uint}/release", wmsInventoryAllocationController.ReleaseAllocation)

				// 库存可用性检查
				inventoryAllocationParty.Post("/check-availability", wmsInventoryAllocationController.CheckAvailability)
				inventoryAllocationParty.Post("/batch-check-availability", wmsInventoryAllocationController.BatchCheckAvailability)

				// 业务查询
				inventoryAllocationParty.Get("/by-outbound-detail/{outboundDetailID:uint}", wmsInventoryAllocationController.GetByOutboundDetailID)
				inventoryAllocationParty.Get("/by-inventory/{inventoryID:uint}", wmsInventoryAllocationController.GetByInventoryID)
				inventoryAllocationParty.Get("/summary/{outboundDetailID:uint}", wmsInventoryAllocationController.GetAllocationSummary)
			}

			// === 库存管理模块路由 ===
			// 库存查询路由
			if wmsInventoryQueryController != nil {
				inventoryQueryParty := wmsParty.Party("/inventory")
				{
					// 基础查询操作 - 匹配前端API调用
					inventoryQueryParty.Post("/query", wmsInventoryQueryController.GetPage)
					inventoryQueryParty.Get("/query/{id:uint}", wmsInventoryQueryController.GetByID)
					inventoryQueryParty.Get("/query/location/{locationId:uint}", wmsInventoryQueryController.GetByLocation)
					inventoryQueryParty.Get("/query/item/{itemId:uint}", wmsInventoryQueryController.GetByItem)

					// 统计和汇总 - 修改为POST方法匹配前端
					inventoryQueryParty.Post("/summary", wmsInventoryQueryController.GetSummary)
					inventoryQueryParty.Get("/statistics", wmsInventoryQueryController.GetStats)
					inventoryQueryParty.Post("/availability", wmsInventoryQueryController.CheckAvailability)

					// 可用库存查询
					inventoryQueryParty.Get("/available", wmsInventoryQueryController.GetAvailable)

					// 预警查询 - 使用已实现的方法
					inventoryQueryParty.Get("/low-stock", wmsInventoryQueryController.GetLowStockItems)
					inventoryQueryParty.Get("/expiring", wmsInventoryQueryController.GetExpiringItems)

					// TODO: 以下API待实现
					// inventoryQueryParty.Post("/turnover", wmsInventoryQueryController.GetTurnover)
					// inventoryQueryParty.Post("/export", wmsInventoryQueryController.ExportToExcel)
					// inventoryQueryParty.Post("/batch-update", wmsInventoryQueryController.BatchUpdate)
					// inventoryQueryParty.Post("/alerts", wmsInventoryQueryController.GetAlerts)
				}
			}

			// 库存调整路由
			if wmsInventoryAdjustmentController != nil {
				inventoryAdjustmentParty := wmsParty.Party("/inventory/adjustment")
				{
					// 基础CRUD操作
					inventoryAdjustmentParty.Post("", wmsInventoryAdjustmentController.Create)
					inventoryAdjustmentParty.Put("/{id:uint}", wmsInventoryAdjustmentController.Update)
					inventoryAdjustmentParty.Delete("/{id:uint}", wmsInventoryAdjustmentController.Delete)
					inventoryAdjustmentParty.Get("/{id:uint}", wmsInventoryAdjustmentController.GetByID)
					inventoryAdjustmentParty.Post("/page", wmsInventoryAdjustmentController.GetPage)

					// 审批流程
					inventoryAdjustmentParty.Post("/{id:uint}/approve", wmsInventoryAdjustmentController.Approve)
					inventoryAdjustmentParty.Post("/{id:uint}/reject", wmsInventoryAdjustmentController.Reject)
					inventoryAdjustmentParty.Post("/execute", wmsInventoryAdjustmentController.Execute)

					// 批量操作
					inventoryAdjustmentParty.Post("/batch", wmsInventoryAdjustmentController.BatchCreate)
					inventoryAdjustmentParty.Post("/batch/approve", wmsInventoryAdjustmentController.BatchApprove)

					// 统计分析
					inventoryAdjustmentParty.Post("/statistics", wmsInventoryAdjustmentController.GetStats)
				}
			}

			// 库存移动路由
			inventoryMovementParty := wmsParty.Party("/inventory/movement")
			{
				// 基础CRUD操作
				inventoryMovementParty.Post("", wmsInventoryMovementController.CreateMovement)
				inventoryMovementParty.Put("/{id:uint}", wmsInventoryMovementController.UpdateMovement)
				inventoryMovementParty.Delete("/{id:uint}", wmsInventoryMovementController.DeleteMovement)
				inventoryMovementParty.Get("/{id:uint}", wmsInventoryMovementController.GetMovementDetail)
				inventoryMovementParty.Post("/page", wmsInventoryMovementController.GetMovementPage)

				// 移动流程
				inventoryMovementParty.Post("/{id:uint}/start", wmsInventoryMovementController.StartMovement)
				inventoryMovementParty.Post("/{id:uint}/complete", wmsInventoryMovementController.CompleteMovement)
				inventoryMovementParty.Post("/{id:uint}/cancel", wmsInventoryMovementController.CancelMovement)

				// 批量操作
				inventoryMovementParty.Post("/batch", wmsInventoryMovementController.BatchCreateMovement)
				inventoryMovementParty.Post("/batch/start", wmsInventoryMovementController.BatchStartMovement)

				// 业务查询
				inventoryMovementParty.Get("/pending", wmsInventoryMovementController.GetPendingMovements)
			}

			// 盘点管理路由 - 修改路径匹配前端
			if wmsCycleCountController != nil {
				cycleCountParty := wmsParty.Party("/inventory/cycle-count")
				{
					// 盘点计划管理
					planParty := cycleCountParty.Party("/plan")
					{
						// 基础CRUD操作
						planParty.Post("", wmsCycleCountController.CreatePlan)
						planParty.Put("/{id:uint}", wmsCycleCountController.UpdatePlan)
						planParty.Delete("/{id:uint}", wmsCycleCountController.DeletePlan)
						planParty.Get("/{id:uint}", wmsCycleCountController.GetPlan)
						planParty.Post("/page", wmsCycleCountController.GetPlanPage)

						// 审批流程
						planParty.Post("/{id:uint}/approve", wmsCycleCountController.ApprovePlan)
						planParty.Post("/{id:uint}/reject", wmsCycleCountController.RejectPlan)
						planParty.Post("/{id:uint}/start", wmsCycleCountController.StartPlan)
						planParty.Post("/{id:uint}/complete", wmsCycleCountController.CompletePlan)

						// 统计分析
						planParty.Get("/{id:uint}/statistics", wmsCycleCountController.GetPlanStatistics)
						planParty.Get("/{id:uint}/variance-report", wmsCycleCountController.GetVarianceReport)
					}

					// 盘点任务管理
					taskParty := cycleCountParty.Party("/task")
					{
						// 基础操作
						taskParty.Get("/{id:uint}", wmsCycleCountController.GetTask)
						taskParty.Post("/page", wmsCycleCountController.GetTaskPage)

						// 任务执行
						taskParty.Post("/{id:uint}/start", wmsCycleCountController.StartTask)
						taskParty.Post("/submit", wmsCycleCountController.SubmitCount)
						taskParty.Post("/assign", wmsCycleCountController.AssignTasks)

						// 差异处理
						taskParty.Post("/{id:uint}/confirm-variance", wmsCycleCountController.ConfirmVariance)
						taskParty.Post("/{id:uint}/reject-variance", wmsCycleCountController.RejectVariance)
						taskParty.Post("/{id:uint}/create-adjustment", wmsCycleCountController.CreateAdjustmentFromVariance)
					}
				}
			}

			// 库存预警路由
			if wmsInventoryAlertController != nil {
				inventoryAlertParty := wmsParty.Party("/inventory/alert")
				{
					// 预警规则管理
					ruleParty := inventoryAlertParty.Party("/rule")
					{
						// 基础CRUD操作
						ruleParty.Post("", wmsInventoryAlertController.CreateRule)
						ruleParty.Put("/{id:uint}", wmsInventoryAlertController.UpdateRule)
						ruleParty.Delete("/{id:uint}", wmsInventoryAlertController.DeleteRule)
						ruleParty.Get("/{id:uint}", wmsInventoryAlertController.GetRule)
						ruleParty.Post("/page", wmsInventoryAlertController.GetRulePage)

						// 规则状态管理 - TODO: 待实现
						// ruleParty.Post("/{id:uint}/activate", wmsInventoryAlertController.ActivateRule)
						// ruleParty.Post("/{id:uint}/deactivate", wmsInventoryAlertController.DeactivateRule)
					}

					// 预警检查
					inventoryAlertParty.Post("/check-all", wmsInventoryAlertController.CheckAlerts)
					inventoryAlertParty.Post("/check-warehouse/{warehouseId:uint}", wmsInventoryAlertController.CheckAlerts)

					// 预警日志管理
					logParty := inventoryAlertParty.Party("/log")
					{
						// 基础查询
						logParty.Get("/{id:uint}", wmsInventoryAlertController.GetAlertLog)
						logParty.Post("/page", wmsInventoryAlertController.GetAlertLogPage)

						// 预警处理
						logParty.Post("/{id:uint}/acknowledge", wmsInventoryAlertController.AcknowledgeAlert)
						logParty.Post("/{id:uint}/resolve", wmsInventoryAlertController.ResolveAlert)
						// TODO: 批量操作待实现
						// logParty.Post("/batch/acknowledge", wmsInventoryAlertController.BatchAcknowledgeAlerts)
						// logParty.Post("/batch/resolve", wmsInventoryAlertController.BatchResolveAlerts)
					}

					// 预警统计 - TODO: 待实现
					// inventoryAlertParty.Get("/critical", wmsInventoryAlertController.GetCriticalAlerts)
					// inventoryAlertParty.Get("/summary", wmsInventoryAlertController.GetAlertSummary)

					// 兼容前端调用的预警查询接口
					inventoryAlertParty.Post("/alerts", wmsInventoryAlertController.GetAlertLogPage)
				}
			}

			// 发运单路由
			shipmentParty := wmsParty.Party("/shipments")
			{
				// 基础CRUD操作
				shipmentParty.Post("", wmsShipmentController.Create)
				shipmentParty.Put("/{id:uint}", wmsShipmentController.Update)
				shipmentParty.Delete("/{id:uint}", wmsShipmentController.Delete)
				shipmentParty.Get("/{id:uint}", wmsShipmentController.GetByID)
				shipmentParty.Get("", wmsShipmentController.GetPage)

				// 发运流程
				shipmentParty.Post("/{id:uint}/pack", wmsShipmentController.PackShipment)
				shipmentParty.Post("/{id:uint}/ship", wmsShipmentController.ShipConfirm)
				shipmentParty.Post("/{id:uint}/tracking", wmsShipmentController.UpdateTrackingStatus)
				shipmentParty.Post("/{id:uint}/delivery", wmsShipmentController.DeliveryConfirm)

				// 运费管理和标签打印
				shipmentParty.Post("/calculate-cost", wmsShipmentController.CalculateShippingCost)
				shipmentParty.Post("/print-label", wmsShipmentController.PrintShippingLabel)

				// 业务查询
				shipmentParty.Get("/by-shipment-no/{shipmentNo:string}", wmsShipmentController.GetByShipmentNo)
				shipmentParty.Get("/by-tracking-no/{trackingNo:string}", wmsShipmentController.GetByTrackingNo)
				shipmentParty.Get("/by-picking-task/{pickingTaskID:uint}", wmsShipmentController.GetByPickingTaskID)
				shipmentParty.Get("/pending-packing", wmsShipmentController.GetPendingPacking)
				shipmentParty.Get("/ready-to-ship", wmsShipmentController.GetReadyToShip)
			}
		}

		// --- Customer Relationship Management (CRM) 资源 (需要账套隔离) ---
		crmParty := apiV1.Party("/crm")
		crmParty.Use(middleware.AccountBookContextHandler(repoManager))
		{
			// 客户管理路由
			customerParty := crmParty.Party("/customers")
			{
				// 客户 CRUD 操作
				customerParty.Post("", crmCustomerController.Post)                                        // 创建客户
				customerParty.Put("/{id:uint}", crmCustomerController.PutBy)                              // 更新客户
				customerParty.Delete("/{id:uint}", crmCustomerController.DeleteBy)                        // 删除客户
				customerParty.Get("/{id:uint}", crmCustomerController.GetBy)                              // 获取客户详情
				customerParty.Get("", crmCustomerController.Get)                                          // 分页查询客户
				customerParty.Get("/simple", crmCustomerController.GetSimple)                             // 获取客户简单列表
				customerParty.Get("/summary", crmCustomerController.GetSummary)                           // 获取客户统计摘要
				customerParty.Get("/code/{customerCode:string}", crmCustomerController.GetByCustomerCode) // 根据客户编码获取客户

				// 客户联系人管理路由
				customerParty.Get("/{customerId:uint}/contacts", crmCustomerController.GetContacts)                                // 获取客户联系人列表
				customerParty.Post("/contacts", crmCustomerController.PostContact)                                                 // 创建客户联系人
				customerParty.Put("/contacts/{contactId:uint}", crmCustomerController.PutContact)                                  // 更新客户联系人
				customerParty.Delete("/contacts/{contactId:uint}", crmCustomerController.DeleteContact)                            // 删除客户联系人
				customerParty.Put("/{customerId:uint}/contacts/{contactId:uint}/primary", crmCustomerController.SetPrimaryContact) // 设置主要联系人
				customerParty.Get("/{customerId:uint}/contacts/primary", crmCustomerController.GetPrimaryContact)                  // 获取主要联系人

				// 业务验证路由
				customerParty.Get("/validate/customer-code", crmCustomerController.ValidateCustomerCode)       // 验证客户编码
				customerParty.Get("/validate/business-license", crmCustomerController.ValidateBusinessLicense) // 验证营业执照号
				customerParty.Get("/validate/tax-number", crmCustomerController.ValidateTaxNumber)             // 验证税务登记号
			}
		}

		// --- Supply Chain Management (SCM) 资源 (需要账套隔离) ---
		scmParty := apiV1.Party("/scm")
		scmParty.Use(middleware.AccountBookContextHandler(repoManager))
		{
			// 供应商管理路由
			supplierParty := scmParty.Party("/suppliers")
			{
				// 供应商 CRUD 操作
				supplierParty.Post("", scmSupplierController.Post)                                        // 创建供应商
				supplierParty.Put("/{id:uint}", scmSupplierController.PutBy)                              // 更新供应商
				supplierParty.Delete("/{id:uint}", scmSupplierController.DeleteBy)                        // 删除供应商
				supplierParty.Get("/{id:uint}", scmSupplierController.GetBy)                              // 获取供应商详情
				supplierParty.Get("", scmSupplierController.Get)                                          // 分页查询供应商
				supplierParty.Get("/simple", scmSupplierController.GetSimple)                             // 获取供应商简单列表
				supplierParty.Get("/summary", scmSupplierController.GetSummary)                           // 获取供应商统计摘要
				supplierParty.Get("/code/{supplierCode:string}", scmSupplierController.GetBySupplierCode) // 根据供应商编码获取供应商

				// 供应商联系人管理路由
				supplierParty.Get("/{supplierId:uint}/contacts", scmSupplierController.GetContacts)                                // 获取供应商联系人列表
				supplierParty.Post("/contacts", scmSupplierController.PostContact)                                                 // 创建供应商联系人
				supplierParty.Put("/contacts/{contactId:uint}", scmSupplierController.PutContact)                                  // 更新供应商联系人
				supplierParty.Delete("/contacts/{contactId:uint}", scmSupplierController.DeleteContact)                            // 删除供应商联系人
				supplierParty.Put("/{supplierId:uint}/contacts/{contactId:uint}/primary", scmSupplierController.SetPrimaryContact) // 设置主要联系人
				supplierParty.Get("/{supplierId:uint}/contacts/primary", scmSupplierController.GetPrimaryContact)                  // 获取主要联系人

				// 业务验证路由
				supplierParty.Get("/validate/supplier-code", scmSupplierController.ValidateSupplierCode)       // 验证供应商编码
				supplierParty.Get("/validate/business-license", scmSupplierController.ValidateBusinessLicense) // 验证营业执照号
				supplierParty.Get("/validate/tax-number", scmSupplierController.ValidateTaxNumber)             // 验证税务登记号
			}
		}
	}

	// 添加健康检查端点
	app.Get("/health", func(ctx iris.Context) {
		ctx.StatusCode(http.StatusOK)
		ctx.WriteString("OK")
	})
}
