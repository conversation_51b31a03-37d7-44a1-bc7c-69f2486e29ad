package dto

import "backend/pkg/response"

// AccountBookCreateOrUpdateDTO 创建账套 DTO
type AccountBookCreateOrUpdateDTO struct {
	TenantDTO
	Code           string `json:"code" validate:"required,max=50"`
	Name           string `json:"name" validate:"required,max=100"`
	CompanyName    string `json:"companyName,omitempty"`
	TaxID          string `json:"taxId,omitempty"`
	CompanyAddress string `json:"companyAddress,omitempty"`
	CompanyPhone   string `json:"companyPhone,omitempty"`
	BankName       string `json:"bankName,omitempty"`
	BankAccount    string `json:"bankAccount,omitempty"`
	IsGroup        *bool  `json:"isGroup"`                              // 使用指针以便区分未提供和 false
	IsVirtual      *bool  `json:"isVirtual"`                            // 使用指针
	Status         int    `json:"status" validate:"required,oneof=0 1"` // 假设 0=禁用, 1=启用
}

// AccountBookPageQueryDTO 查询账套 DTO
type AccountBookPageQueryDTO struct {
	PageQuery response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
	Code        string `json:"code,omitempty" query:"code"`
	Name        string `json:"name,omitempty" query:"name"`
	CompanyName string `json:"companyName,omitempty" query:"companyName"`
	Status      *int   `json:"status,omitempty" query:"status"`       // 使用指针接收可选参数
	IsGroup     *bool  `json:"isGroup,omitempty" query:"isGroup"`     // 使用指针
	IsVirtual   *bool  `json:"isVirtual,omitempty" query:"isVirtual"` // 使用指针
}

// AccountBookDeleteDTO 删除帐套 DTO
type AccountBookDeleteDTO struct {
	Ids []uint `json:"ids" validate:"required,min=1,dive,gt=0"` //
}

// AccountBookUpdateStatusDTO 更新账套状态 DTO
type AccountBookUpdateStatusDTO struct {
	Status int `json:"status" validate:"required,oneof=0 1"` // 0=禁用, 1=启用
}

// AccountBookUpdateIsGroupDTO 更新账套是否集团 DTO
type AccountBookUpdateIsGroupDTO struct {
	IsGroup *bool `json:"isGroup" validate:"required"` // 是否集团账套 (使用指针以区分 false 和未提供)
}

// AccountBookUpdateIsVirtualDTO 更新账套是否虚拟 DTO
type AccountBookUpdateIsVirtualDTO struct {
	IsVirtual *bool `json:"isVirtual" validate:"required"` // 是否虚拟账套 (使用指针以区分 false 和未提供)
}
