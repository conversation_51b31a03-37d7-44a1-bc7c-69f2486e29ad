package dto

import "backend/pkg/response"

// SystemParameterCreateDTO 创建系统参数 DTO
type SystemParameterCreateOrUpdateDTO struct {
	TenantDTO                 // 继承租户信息
	ParamKey   string `json:"paramKey" validate:"required,max=100"`
	ParamValue string `json:"paramValue" validate:"required"` // 值可能较长，校验可以放宽
	Name       string `json:"name" validate:"required,max=100"`
	Remark     string `json:"remark,omitempty" validate:"omitempty,max=500"`
	Status     *int   `json:"status" validate:"omitempty,oneof=0 1"` // Use pointer for optional, default handling in service
	IsSystem   *bool  `json:"isSystem" validate:"omitempty"`         // Use pointer for optional, default handling in service
	ValueType  string `json:"valueType" validate:"omitempty,max=50"` // 可选
}

// SystemParameterPageQueryDTO 查询系统参数 DTO
type SystemParameterPageQueryDTO struct {
	PageQuery response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
	ParamKey  string `url:"paramKey" validate:"omitempty"` // Use url tag for query parameters in Iris
	Name      string `url:"name" validate:"omitempty"`
	Status    *int   `url:"status" validate:"omitempty,oneof=0 1"`
	IsSystem  *bool  `url:"isSystem" validate:"omitempty,oneof=true false"` // oneof validation might need custom logic or specific tag based on validator version
	ValueType string `url:"valueType" validate:"omitempty"`
}

// SystemParameterDeleteDTO 删除系统参数 DTO
type SystemParameterDeleteDTO struct {
	Ids []uint `json:"ids" validate:"required,min=1,dive,gt=0"` // 系统参数ID列表：要删除的系统参数ID集合，必填，至少包含一个系统参数ID，且每个ID必须大于0
}

