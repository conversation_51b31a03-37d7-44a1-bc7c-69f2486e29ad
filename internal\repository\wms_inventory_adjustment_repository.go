package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsInventoryAdjustmentRepository 库存调整数据访问接口
type WmsInventoryAdjustmentRepository interface {
	BaseRepository[entity.WmsInventoryAdjustment, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInventoryAdjustmentQueryReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByInventoryID(ctx context.Context, inventoryId uint) ([]*entity.WmsInventoryAdjustment, error)
	FindByAdjustmentNo(ctx context.Context, adjustmentNo string) (*entity.WmsInventoryAdjustment, error)
	FindByOperatorID(ctx context.Context, operatorId uint) ([]*entity.WmsInventoryAdjustment, error)
	FindByApprovalStatus(ctx context.Context, status entity.WmsInventoryAdjustmentStatus) ([]*entity.WmsInventoryAdjustment, error)
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryAdjustment, error)

	// 统计查询
	CountByStatus(ctx context.Context, status entity.WmsInventoryAdjustmentStatus) (int64, error)
	CountByOperator(ctx context.Context, operatorId uint) (int64, error)
	SumQuantityChangeByType(ctx context.Context, adjustmentType entity.WmsInventoryAdjustmentType) (float64, error)

	// 批量操作
	BatchUpdate(ctx context.Context, ids []uint, updates map[string]interface{}) error
	BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsInventoryAdjustmentStatus, approverID uint) error
}

// wmsInventoryAdjustmentRepository 库存调整数据访问实现
type wmsInventoryAdjustmentRepository struct {
	BaseRepositoryImpl[entity.WmsInventoryAdjustment, uint]
}

// NewWmsInventoryAdjustmentRepository 创建库存调整数据访问实例
func NewWmsInventoryAdjustmentRepository(db *gorm.DB) WmsInventoryAdjustmentRepository {
	return &wmsInventoryAdjustmentRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsInventoryAdjustment, uint]{
			db: db,
		},
	}
}

// GetPage 分页查询库存调整
func (r *wmsInventoryAdjustmentRepository) GetPage(ctx context.Context, query *dto.WmsInventoryAdjustmentQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	// 构建查询条件
	if query.AdjustmentNo != nil && *query.AdjustmentNo != "" {
		conditions = append(conditions, NewEqualCondition("adjustment_no", *query.AdjustmentNo))
	}
	if query.InventoryID != nil {
		conditions = append(conditions, NewEqualCondition("inventory_id", *query.InventoryID))
	}
	if query.AdjustmentType != nil && *query.AdjustmentType != "" {
		conditions = append(conditions, NewEqualCondition("adjustment_type", *query.AdjustmentType))
	}
	if query.ApprovalStatus != nil && *query.ApprovalStatus != "" {
		conditions = append(conditions, NewEqualCondition("approval_status", *query.ApprovalStatus))
	}
	if query.OperatorID != nil {
		conditions = append(conditions, NewEqualCondition("operator_id", *query.OperatorID))
	}
	if query.CreatedStart != nil {
		conditions = append(conditions, NewGreaterThanEqualCondition("created_at", *query.CreatedStart))
	}
	if query.CreatedEnd != nil {
		conditions = append(conditions, NewLessThanEqualCondition("created_at", *query.CreatedEnd))
	}

	// 使用BaseRepository的分页查询
	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// FindByInventoryID 根据库存ID查询调整记录
func (r *wmsInventoryAdjustmentRepository) FindByInventoryID(ctx context.Context, inventoryId uint) ([]*entity.WmsInventoryAdjustment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("inventory_id", inventoryId),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// FindByAdjustmentNo 根据调整单号查询
func (r *wmsInventoryAdjustmentRepository) FindByAdjustmentNo(ctx context.Context, adjustmentNo string) (*entity.WmsInventoryAdjustment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("adjustment_no", adjustmentNo),
	}
	return r.BaseRepositoryImpl.FindOneByCondition(ctx, conditions)
}

// FindByOperatorID 根据操作员ID查询调整记录
func (r *wmsInventoryAdjustmentRepository) FindByOperatorID(ctx context.Context, operatorId uint) ([]*entity.WmsInventoryAdjustment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("operator_id", operatorId),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// FindByApprovalStatus 根据审批状态查询调整记录
func (r *wmsInventoryAdjustmentRepository) FindByApprovalStatus(ctx context.Context, status entity.WmsInventoryAdjustmentStatus) ([]*entity.WmsInventoryAdjustment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("approval_status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// FindByDateRange 根据日期范围查询调整记录
func (r *wmsInventoryAdjustmentRepository) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryAdjustment, error) {
	conditions := []QueryCondition{
		NewGreaterThanEqualCondition("created_at", startDate),
		NewLessThanEqualCondition("created_at", endDate),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// 统计查询方法
func (r *wmsInventoryAdjustmentRepository) CountByStatus(ctx context.Context, status entity.WmsInventoryAdjustmentStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("wms_inventory_adjustment").
		Where("approval_status = ?", status).
		Count(&count).Error
	return count, err
}

func (r *wmsInventoryAdjustmentRepository) CountByOperator(ctx context.Context, operatorId uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("wms_inventory_adjustment").
		Where("operator_id = ?", operatorId).
		Count(&count).Error
	return count, err
}

func (r *wmsInventoryAdjustmentRepository) SumQuantityChangeByType(ctx context.Context, adjustmentType entity.WmsInventoryAdjustmentType) (float64, error) {
	var sum float64
	err := r.db.WithContext(ctx).
		Table("wms_inventory_adjustment").
		Where("adjustment_type = ?", adjustmentType).
		Select("COALESCE(SUM(quantity_change), 0)").
		Scan(&sum).Error
	return sum, err
}

// 批量操作方法
func (r *wmsInventoryAdjustmentRepository) BatchUpdate(ctx context.Context, ids []uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).
		Model(&entity.WmsInventoryAdjustment{}).
		Where("id IN ?", ids).
		Updates(updates).Error
}

func (r *wmsInventoryAdjustmentRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsInventoryAdjustmentStatus, approverID uint) error {
	updates := map[string]interface{}{
		"approval_status": status,
		"approver_id":     approverID,
		"approved_at":     time.Now(),
	}
	return r.BatchUpdate(ctx, ids, updates)
}
