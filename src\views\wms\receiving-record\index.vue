<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="220"
      @refresh="loadData"
      @batch-delete="handleBatchDelete"
      @import="handleImport"
      @export="handleExport"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
    <template #toolbar-left>
        <div class="add-button-container">
          <el-tooltip content="新增" placement="top">
            <el-dropdown @command="handleCreateCommand"  v-if="checkPermission('wms:receiving-record:create')">
              <el-button :icon="Plus" circle plain />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="from_notification">从通知单创建</el-dropdown-item>
                  <el-dropdown-item command="blind">盲收创建</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-tooltip>
        </div>
      </template>

      <template #column-status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ formatStatus(row.status) }}
        </el-tag>
      </template>

      <template #column-receivingType="{ row }">
        <el-tag :type="row.receivingType === 'BLIND' ? 'warning' : 'primary'">
          {{ formatReceivingType(row.receivingType) }}
        </el-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)" v-if="!operationButtons.find(b => b.label === '查看')?.hidden"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEdit(row)" v-if="!operationButtons.find(b => b.label === '编辑')?.hidden && checkPermission('wms:inbound:edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            @click="handleDelete(row)" 
            v-if="!operationButtons.find(b => b.label === '删除')?.hidden && checkPermission('wms:inbound:delete')"
            :disabled="!canDelete(row)"
          ></el-button>
        </el-tooltip>
        <el-tooltip content="取消" placement="top">
          <el-button 
            circle 
            :icon="CloseBold" 
            type="warning" 
            size="small" 
            @click="handleCancel(row)" 
            v-if="!operationButtons.find(b => b.label === '取消')?.hidden && checkPermission('wms:inbound:cancel')"
            :disabled="!canCancel(row)"
          ></el-button>
        </el-tooltip>
      </template>

    </VNTable>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="85%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <!-- 状态流程区域 -->
      <div v-if="formMode !== 'add'" class="status-flow-section">
        <VNStatusFlow
          business-type="RECEIVING_RECORD"
          :current-status="formData.status || 'DRAFT'"
          :business-id="formData.id"
          :business-data="formData"
          :readonly="isViewing"
          show-title
          title="收货记录状态流程"
          collapsible
          :default-collapsed="false"
          @status-change="handleStatusChangeFromFlow"
          @before-status-change="handleBeforeStatusChange"
          @after-status-change="handleAfterStatusChange"
        />
      </div>

      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        :detail-tables="detailTablesConfig"
        v-model="formData"
        :default-columns="4"
        :label-width="'120px'"
        :loading="formLoading"
        group-title-type="h4"
        :on-row-add="handleDetailAdd"
        :on-row-delete="handleDetailDelete"
        @submit="submitForm"
        @cancel="handleCloseDialog"
        @cell-dblclick="handleDetailCellDoubleClick"
      >

        <!-- 仓库选择插槽 -->
        <template #form-item-warehouseId="{ field, formData: formModel }">
          <el-tree-select
            v-if="isFormEditable"
            v-model="formModel[field.field]"
            :data="warehouseOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择仓库"
            clearable
            style="width: 100%;"
          />
          <div v-else class="custom-disabled-text-wrapper">
            <el-text class="custom-disabled-text">
              {{ getWarehouseDisplayName(formModel[field.field]) || '-' }}
            </el-text>
          </div>
        </template>

        <!-- 明细表SKU列编辑器插槽 -->
        <template #editor-sku="{ row, index, column }">
          <!-- 编辑状态：显示可操作的输入框组件 -->
          <div class="sku-cell" @click="handleSkuSelect(index)" style="cursor: pointer;">
            <el-input
              :value="row.sku || ''"
              placeholder="点击选择物料"
              readonly
              style="width: 100%;"
            >
              <template #append>
                <el-button 
                  @click.stop="handleSkuSelect(index)"
                  :icon="Plus"
                  type="primary"
                  style="width: 40px;"
                />
              </template>
            </el-input>
          </div>
        </template>


        <template #actions="{ submit, reset }"> 
          <template v-if="isViewing">
            <el-button
              v-if="checkPermission('wms:inbound:print')"
              :icon="Printer"
              @click="handlePrint"
            >
              打印
            </el-button>
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else-if="isFormEditable"> 
            <!-- 可编辑状态：显示提交按钮 -->
            <el-button type="primary" @click="submit" :loading="formLoading">提交</el-button>
            <el-button @click="handleCloseDialog">取消</el-button>
          </template>
          <template v-else>
            <!-- 不可编辑状态：只显示关闭按钮 -->
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
        </template>

      </VNForm>

    </el-dialog>

    <!-- 新增对已有组件的引用 -->
    <InboundNotificationSelector
      ref="notificationSelectorRef"
      @confirm="handleNotificationSelected"
    />

    <!-- SKU选择器 -->
    <SkuSelector
      ref="skuSelectorRef"
      :multiple="false"
      @confirm="handleSkuSelectorConfirm"
      @cancel="handleSkuSelectorCancel"
    />

    <!-- 盲收验证对话框 -->
    <el-dialog
      v-model="showBlindReceivingValidation"
      title="盲收权限验证"
      width="800px"
      :close-on-click-modal="false"
      append-to-body
    >
      <BlindReceivingValidator
        :mode="'full'"
        :auto-validate="true"
        :show-history-button="true"
        @validation-complete="handleBlindReceivingValidationComplete"
        @validation-error="handleBlindReceivingValidationError"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBlindReceivingValidation = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 状态变更确认对话框 -->
    <el-dialog
      v-model="statusChangeDialogVisible"
      :title="`确认${getStatusLabel(targetChangeStatus)}`"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="status-change-form">
        <div class="change-info">
          <p>
            <strong>收货单号：</strong>{{ currentChangeRow?.receivingNo }}
          </p>
          <p>
            <strong>当前状态：</strong>
            <el-tag 
              :color="getStatusColorConfig(currentChangeRow?.status || '').bgColor" 
              :text-color="getStatusColorConfig(currentChangeRow?.status || '').color"
            >
              {{ getStatusLabel(currentChangeRow?.status || '') }}
            </el-tag>
          </p>
          <p>
            <strong>目标状态：</strong>
            <el-tag 
              :color="getStatusColorConfig(targetChangeStatus).bgColor" 
              :text-color="getStatusColorConfig(targetChangeStatus).color"
            >
              {{ getStatusLabel(targetChangeStatus) }}
            </el-tag>
          </p>
        </div>
        
        <el-form
          ref="statusChangeFormRef"
          :model="statusChangeForm"
          :rules="statusChangeFormRules"
          label-width="80px"
        >
          <el-form-item
            label="备注"
            prop="remark"
            :required="targetChangeStatus === 'CANCELLED'"
          >
            <el-input
              v-model="statusChangeForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入状态变更备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="statusChangeDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="statusChangeSubmitting"
            @click="confirmStatusChange"
          >
            确认变更
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
/* Styles for the disabled text field wrapper */
.custom-disabled-text-wrapper {
  box-sizing: border-box; /* Crucial for correct height calculation */
  display: flex;
  align-items: center;
  width: 100%;
  height: var(--el-input-height, 32px); /* Use the correct Element Plus variable for input height */
  padding: 1px 11px; /* Adjust padding to perfectly match el-input */
  background-color: var(--el-disabled-bg-color, #f5f7fa); /* Simulate disabled bg color */
  border: 1px solid var(--el-disabled-border-color, #e4e7ed); /* Simulate disabled border color */
  border-radius: var(--el-border-radius-base, 4px); /* Simulate border radius */
  box-shadow: 0 0 0 0 transparent; /* Ensure no other shadows */
  cursor: not-allowed; /* Simulate disabled cursor */
  overflow: hidden; /* Prevent text from overflowing */
}

.custom-disabled-text-wrapper .custom-disabled-text {
  color: var(--el-text-color-placeholder, #a8abb2); /* Use placeholder or disabled text color */
  font-size: inherit;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%; /* Ensure text uses available space for ellipsis to work */
}

.sku-selector-wrapper {
  width: 100%;
}
</style>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import SkuSelector from '@/components/SkuSelector/index.vue';
import InboundNotificationSelector from './components/InboundNotificationSelector.vue';
import VNStatusFlow from '@/components/VNStatusFlow/index.vue';
import BlindReceivingValidator from '@/views/wms/blind-receiving-validation/components/BlindReceivingValidator.vue';
import type { TableColumn, PaginationConfig, ActionButton } from '@/components/VNTable/types';
import type { HeaderField, DetailTableConfig } from '@/components/VNForm/types';
import type { BlindReceivingValidationVO } from '@/types/wms/blindReceivingConfig';
import {
  ElDialog, ElMessage, ElMessageBox, ElTag, ElButton, ElTooltip
} from 'element-plus';
import { 
  View, Edit, Delete, Plus, Upload, Download, 
  CloseBold, Printer, RefreshRight, Refresh, Tools, ArrowDown
} from '@element-plus/icons-vue';

// 导入API和类型
import {
  getReceivingRecordPage,
  createReceivingRecord,
  createBlindReceivingRecord,
  updateReceivingRecord,
  deleteReceivingRecord,
  getReceivingRecordDetail,
  updateReceivingRecordStatus
} from '@/api/wms/receivingRecord';
import { getInboundNotificationDetail } from '@/api/wms/inboundNotification';
import { getDictDataByCode } from '@/api/system/systemDict';
import { getLocationTree } from '@/api/wms/location';
import type { WmsLocationTreeVO } from '@/api/wms/location';
import { getSystemParameterByKey } from '@/api/system/systemParameter';
import { getCustomerList } from '@/api/crm/customer';
import type { CrmCustomerSimpleVO } from '@/api/crm/customer';
import { getSupplierList } from '@/api/scm/supplier';
import type { ScmSupplierSimpleVO } from '@/api/scm/supplier';
import type {
  WmsReceivingRecordQueryReq,
  WmsReceivingRecordResp,
  WmsReceivingRecordSimpleResp,
  WmsReceivingRecordCreateReq,
  WmsReceivingRecordUpdateReq,
  WmsReceivingRecordStatus,
  WmsReceivingRecordDetailResp,
  WmsReceivingRecordDetailCreateReq,
  WmsReceivingRecordDetailUpdateReq
} from '@/api/wms/receivingRecord';
import type { WmsInboundNotificationResp } from '@/api/wms/inboundNotification';
import { getMtlItemById, type MtlItemVO } from '@/api/wms/item';
import type { WmsReceivingRecordBlindCreateReq } from '@/api/wms/receivingRecord';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Helper: Date Formatting ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) { 
        return '-';
    }
    return date.toLocaleString();
  } catch (e) {
    return '-';
  }
};

// 权限检查函数
const checkPermission = (permission: string): boolean => {
  // 这里应该实现实际的权限检查逻辑
  return true; // 临时返回true
};

// 组件引用
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const skuSelectorRef = ref<InstanceType<typeof SkuSelector>>();
const notificationSelectorRef = ref<InstanceType<typeof InboundNotificationSelector>>();

// 响应式数据
const loading = ref(false);
const tableData = ref<WmsReceivingRecordSimpleResp[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});

// 对话框状态
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const formLoading = ref(false);

// 表单数据
const formData = ref<any>({});
const selectedRows = ref<WmsReceivingRecordSimpleResp[]>([]);
const currentRow = ref<WmsReceivingRecordSimpleResp | null>(null);

// 控制明细行是否自动换行
const showOverflowTooltip = ref(false);

// 客户、供应商、仓库数据
const customerList = ref<CrmCustomerSimpleVO[]>([]);
const supplierList = ref<ScmSupplierSimpleVO[]>([]);

// 下拉选项
const customerOptions = computed(() => 
  customerList.value.map(item => ({ label: `${item.customerCode} - ${item.customerName}`, value: item.id }))
);
const supplierOptions = computed(() => 
  supplierList.value.map(item => ({ label: `${item.supplierCode} - ${item.supplierName}`, value: item.supplierCode }))
);
const warehouseOptions = ref<WmsLocationTreeVO[]>([]);
// 收货类型选项（与后端 code 保持一致）
const receivingTypeOptions = ref([
  { label: '按通知收货', value: 'ASN' },
  { label: '盲收', value: 'BLIND' },
]);
// 收货状态选项（完整）
const statusOptions = ref([
  { label: '草稿', value: 'DRAFT' },
  { label: '待收货', value: 'PENDING' },
  { label: '收货中', value: 'RECEIVING' },
  { label: '待检验', value: 'PENDING_INSPECTION' },
  { label: '检验中', value: 'INSPECTING' },
  { label: '部分完成', value: 'PARTIALLY_COMPLETED' },
  { label: '收货完成', value: 'COMPLETED' },
  { label: '已关闭', value: 'CLOSED' },
  { label: '已取消', value: 'CANCELLED' },
]);

// 单位选项
const unitOptions = ref<{ label: string; value: string }[]>([]);

// 编码生成相关
const codeGenerating = ref(false);

// SKU选择器相关
const currentEditingRowIndex = ref(-1);
const quantityEditingRow = ref<any>(null);


const selectedItems = ref<MtlItemVO[]>([]);

// 计算属性
const isViewing = computed(() => formMode.value === 'view');
const isFormEditable = computed(() => {
  // 新增模式：可编辑
  if (formMode.value === 'add') return true;
  // 查看模式：不可编辑
  if (formMode.value === 'view') return false;
  // 编辑模式：只有草稿状态可编辑
  return formData.value.status === 'DRAFT';
});
const dialogTitle = computed(() => {
  const titles = {
    add: '新增收货记录',
    edit: '编辑收货记录',
    view: '查看收货记录'
  };
  return titles[formMode.value];
});

const toolbarConfig = computed(() => ({
  refresh: true,
  add: false,
  filter: true,
  columnSetting: true,
  density: true,
  fullscreen: true,
  batchDelete: checkPermission('wms:inbound:delete'),
  import: checkPermission('wms:inbound:import'),
  export: checkPermission('wms:inbound:export'),
}));

const operationButtons = computed<ActionButton[]>(() => [
  {
    label: '查看',
    icon: 'View',
    handler: (row) => handleView(row),
    hidden: !checkPermission('wms:inbound:view')
  },
  {
    label: '编辑',
    icon: 'Edit',
    handler: (row) => handleEdit(row),
    hidden: !checkPermission('wms:inbound:edit'),
    disabled: (row) => !canEdit(row)
  },
  {
    label: '删除',
    icon: 'Delete',
    handler: (row) => handleDelete(row),
    hidden: !checkPermission('wms:inbound:delete'),
    disabled: (row) => !canDelete(row)
  },
  {
    label: '取消',
    icon: 'CloseBold',
    handler: (row) => handleCancel(row),
    hidden: !checkPermission('wms:inbound:cancel'),
    disabled: (row) => !canCancel(row)
  },
]);

// 表格列定义
const tableColumns = ref<TableColumn[]>([
  { prop: 'receivingNo', label: '收货单号', minWidth: 180, sortable: true },
  { prop: 'receivingType', label: '收货类型', width: 120, slot: true },
  { prop: 'status', label: '状态', width: 110, slot: true },
  { 
    prop: 'actualArrivalDate', 
    label: '收货日期', 
    width: 160, 
    sortable: true
  },
  { prop: 'clientName', label: '委托客户', minWidth: 150 },
  { prop: 'warehouseName', label: '预入库仓库', minWidth: 150 },
  { prop: 'supplierName', label: '供应商/发货方', minWidth: 150 },
  { prop: 'remark', label: '备注', minWidth: 150 },
  { 
    prop: 'createdAt', 
    label: '创建时间', 
    width: 160, 
    sortable: true,
    formatter: (row) => formatDateTime(row.createdAt) 
  },
  { 
    prop: 'supplementDeadline', 
    label: '补货截止', 
    width: 160, 
    sortable: true,
    formatter: (row) => formatDateTime(row.supplementDeadline)
  },
]);

// 表单字段定义
const formFields = computed<HeaderField[]>(() => [
  { 
    field: 'receivingNo', 
    label: '收货单号', 
    group: '基本信息', 
    disabled: formMode.value === 'edit' || !isFormEditable.value, 
    props: { placeholder: '留空自动生成或手动输入' } 
  },
  { 
    field: 'receivingType', 
    label: '收货类型', 
    group: '基本信息', 
    type: 'select', 
    options: receivingTypeOptions.value, 
    rules: [{ required: true, message: '请选择收货类型' }], 
    props: { placeholder: '请选择收货类型' },
    disabled: true
  },
  { 
    field: 'actualArrivalDate', 
    label: '收货日期', 
    group: '基本信息', 
    type: 'date', 
    props: { 
      type: 'date', 
      valueFormat: 'YYYY-MM-DD',
      placeholder: '请选择收货日期'
    }, 
    disabled: !isFormEditable.value 
  },
  { 
    field: 'clientId', 
    label: '委托客户', 
    group: '基本信息', 
    type: 'select', 
    options: customerOptions.value, 
    rules: [{ required: true, message: '请选择委托客户' }], 
    props: { 
      filterable: true, 
      clearable: true,
      placeholder: '请选择委托客户',
    }, 
    disabled: !isFormEditable.value 
  },
  { 
    field: 'warehouseId', 
    label: '仓库', 
    group: '基本信息', 
    type: 'slot',
    rules: [{ required: true, message: '请选择仓库' }], 
    disabled: !isFormEditable.value 
  },
  { 
    field: 'supplierShipper', 
    label: '供应商/发货方', 
    group: '基本信息', 
    type: 'select', 
    options: supplierOptions.value, 
    props: { 
      filterable: true, 
      clearable: true,
      placeholder: '请选择供应商/发货方',
    }, 
    disabled: !isFormEditable.value 
  },
  { 
    field: 'sourceDocNo', 
    label: '源单据号', 
    group: '基本信息', 
    type: 'text', 
    props: { placeholder: '输入源单据号', clearable: true }, 
    disabled: !isFormEditable.value 
  },
  { 
    field: 'remark', 
    label: '备注', 
    group: '基本信息', 
    disabled: !isFormEditable.value 
  },
  { 
    field: 'supplementDeadline', 
    label: '补货截止日期', 
    group: '基本信息', 
    type: 'date', 
    props: { 
      type: 'date', 
      valueFormat: 'YYYY-MM-DD',
      placeholder: '请选择补货截止日期'
    }, 
    disabled: !isFormEditable.value 
  },
]);

// === 明细表格配置 ===
const detailTablesConfig = computed<DetailTableConfig[]>(() => [
  {
    title: '收货明细',
    tableProps: {
      rowKey: (row: any) => row.id ?? row.tempId,
      editable: isFormEditable.value,
      editMode: 'row',
      singleRowEdit: true,
      pagination: false,
      showIndex: false,
      showOperations: isFormEditable.value,
      operationWidth: 120,
      toolbarConfig: {
        add: isFormEditable.value,
        batchDelete: isFormEditable.value,
        filter: false,
        refresh: false,
        density: false,
        columnSetting: false,
        fullscreen: false,
      },
      columns: [
        {
          prop: 'lineNo',
          label: '行号',
          width: 70,
          editable: false,
          displayOnly: true,
        },
        {
          prop: 'sku',
          label: '物料SKU',
          width: 200,
          editable: true,
          editComponent: 'custom',
          showOverflowTooltip: showOverflowTooltip.value,
          rules: [{ required: true, message: '请选择物料' }]
        },
        {
          prop: 'itemName',
          label: '物料名称',
          width: 180,
          editable: false,
          displayOnly: true,
          showOverflowTooltip: showOverflowTooltip.value,
        },
        {
          prop: 'itemSpecification',
          label: '规格',
          width: 150,
          editable: false,
          displayOnly: true,
          showOverflowTooltip: showOverflowTooltip.value,
        },
        {
          prop: 'expectedQuantity',
          label: '预期量',
          width: 120,
          editable: false,
          displayOnly: true,
          align: 'right',
        },
        {
          prop: 'receivedQuantity',
          label: '实收数量',
          width: 120,
          editable: true,
          editComponent: 'input-number',
          editComponentProps: { 
            min: 0, 
            controls: false,
            style: "width: 100%",
            placeholder: "输入实收数"
          },
          rules: [{ required: true, type: 'number', min: 0, message: '实收数量必须大于等于0' }],
          align: 'right',
        },
        {
          prop: 'discrepancy',
          label: '差异',
          width: 100,
          editable: false,
          displayOnly: true,
          align: 'right',
          formatter: (row: any) => {
            const discrepancy = (row.receivedQuantity || 0) - (row.expectedQuantity || 0);
            return discrepancy;
          },
          cellStyle: (row: any) => {
            const discrepancy = (row.receivedQuantity || 0) - (row.expectedQuantity || 0);
            if (discrepancy < 0) {
              return { color: 'red', fontWeight: 'bold' };
            }
            if (discrepancy > 0) {
              return { color: 'orange', fontWeight: 'bold' };
            }
            return {};
          }
        },
        {
          prop: 'packageQty',
          label: '包装数量',
          width: 120,
          editable: (row: any) => !!row.packageUnit,
          editComponent: 'input-number',
          editComponentProps: { min: 0, controls: false, style: "width: 100%" },
        },
        {
          prop: 'packageUnit',
          label: '包装单位',
          width: 120,
          editable: isFormEditable.value,
          formatter: (row: any) => getUnitDisplayName(row.packageUnit),
          editComponent: 'select',
          editComponentProps: {
            placeholder: '选择单位',
            filterable: true,
            clearable: true,
            style: "width: 100%",
            options: (row: any) => row.packageUnitOptions || []
          },
        },
        {
          prop: 'unitOfMeasure',
          label: '基本单位',
          width: 100,
          editable: false,
          displayOnly: true,
          formatter: (row: any) => getUnitDisplayName(row.unitOfMeasure),
        },
        {
          prop: 'receivedAtLocationId',
          label: '收货库位',
          width: 180,
          editable: true,
          editComponent: 'input', 
          editComponentProps: { placeholder: '输入库位ID或编码' },
          rules: [{ required: true, message: '请指定收货库位' }]
        },
        {
          prop: 'batchNo',
          label: '外部批次号',
          width: 180,
          editable: true,
          editComponent: 'input',
          showOverflowTooltip: showOverflowTooltip.value,
          editComponentProps: { placeholder: '可选，手动输入外部批次号' }
        },
        {
          prop: 'internalBatchNo',
          label: '内部批次号',
          width: 180,
          editable: false,
          displayOnly: true,
        },
        {
          prop: 'productionDate',
          label: '生产日期',
          width: 150,
          editable: true,
          editComponent: 'datepicker',
          editComponentProps: { type: 'date', placeholder: '选择日期', style: "width: 100%", valueFormat: 'YYYY-MM-DD' }
        },
        {
          prop: 'expiryDate',
          label: '过期日期',
          width: 150,
          editable: true,
          editComponent: 'datepicker',
          editComponentProps: { type: 'date', placeholder: '选择日期', style: "width: 100%", valueFormat: 'YYYY-MM-DD' }
        },
        {
          prop: 'remark',
          label: '备注',
          minWidth: 200,
          editable: true,
          editComponent: 'input',
          editComponentProps: { placeholder: '输入备注', clearable: true }
        }
      ] as TableColumn[],
      operationButtons: [
        {
          label: '删除',
          icon: 'Delete',
          type: 'danger',
          size: 'small',
          handler: (row: any, index: number) => handleDetailDeleteConfirm(index),
          hidden: (row: any) => !isFormEditable.value
        }
      ]
    }
  }
]);

// 辅助函数
const formatStatus = (status: WmsReceivingRecordStatus): string => {
  return statusOptions.value.find(opt => opt.value === status)?.label || status;
};

const getStatusTagType = (status: WmsReceivingRecordStatus): 'success' | 'warning' | 'info' | 'danger' => {
  switch (status) {
    case 'PENDING':
      return 'info';
    case 'RECEIVING':
      return 'warning';
    case 'COMPLETED':
      return 'success';
    case 'CANCELLED':
      return 'danger';
    default:
      return 'info';
  }
};

const formatReceivingType = (type: string): string => {
  return type === 'BLIND' ? '盲收' : '按通知收货';
};

// 与 canEdit、canCancel 同一位置放置
const canDelete = (row: WmsReceivingRecordSimpleResp): boolean => {
  return row.status !== 'PENDING';
};

const canEdit = (row: WmsReceivingRecordSimpleResp): boolean => {
  return ['PENDING', 'RECEIVING'].includes(row.status);
};

const canCancel = (row: WmsReceivingRecordSimpleResp): boolean => {
  return row.status !== 'CANCELLED' && row.status !== 'COMPLETED';
};

// 递归查找节点
const findNodeInTree = (nodes: WmsLocationTreeVO[], nodeId: number): WmsLocationTreeVO | null => {
  for (const node of nodes) {
    if (node.id === nodeId) return node;
    if (node.children) {
      const found = findNodeInTree(node.children, nodeId);
      if (found) return found;
    }
  }
  return null;
};

// 获取仓库显示名称
const getWarehouseDisplayName = (warehouseId: number): string => {
  if (!warehouseId) return '-';
  const warehouse = findNodeInTree(warehouseOptions.value, warehouseId);
  return warehouse?.name || '-';
};

// 获取单位显示名称
const getUnitDisplayName = (unitCode: string): string => {
  if (!unitCode) return '-';
  const unit = unitOptions.value.find(u => u.value === unitCode);
  return unit?.label || unitCode; // 如果找不到对应的名称，就显示原始代码
};

// SKU选择相关方法
const handleSkuSelect = (rowIndex: number) => {
  console.log('handleSkuSelect called with rowIndex:', rowIndex);
  currentEditingRowIndex.value = rowIndex;
  console.log('skuSelectorRef.value:', skuSelectorRef.value);
  skuSelectorRef.value?.open();
};

const handleSkuSelectorConfirm = async (items: MtlItemVO[]) => {
  if (items.length > 0 && currentEditingRowIndex.value >= 0) {
    const item = items[0]; // 单选模式
    await handleSkuConfirm(item, currentEditingRowIndex.value);
  }
  currentEditingRowIndex.value = -1;
};

const handleSkuSelectorCancel = () => {
  currentEditingRowIndex.value = -1;
};

// 处理明细表单元格双击事件
const handleDetailCellDoubleClick = async (tableIndex: number, row: any, column: any) => {
  // 仅在表单可编辑且列本身可编辑时响应
  if (!isFormEditable.value || (column && column.editable === false)) {
    return;
  }

  try {
    // 从 VNForm 获取内部的 VNTable 实例
    const detailTable = vnFormRef.value?.getDetailTable(tableIndex);
    if (detailTable) {
      // 直接调用 VNTable 暴露的行编辑处理函数
      // VNTable 内部将根据 singleRowEdit 的值来决定行为
      detailTable.HandleEditRow(row);
    }
  } catch (error) {
    console.error('控制行编辑状态失败:', error);
  }
};

// 生命周期
onMounted(async () => {
  await loadDropdownOptions();      // 先加载下拉数据（含客户/仓库/供应商），便于后续名称映射
  await loadSystemParams();        // 加载系统参数
  loadData();                      // 最后拉取业务数据，保证名称可立即匹配
});

// 方法实现
const loadData = async () => {
  loading.value = true;
  try {
    const params: Partial<WmsReceivingRecordQueryReq> = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
    };
    const res = await getReceivingRecordPage(params);
    const rawList = res?.list || [];

    // 根据已加载的下拉缓存填充显示名称，避免表格空白
    const getClientNameById = (id: number): string => {
      const client = customerList.value.find(c => c.id === id);
      return client ? `${client.customerCode}-${client.customerName}` : '';
    };

    const getWarehouseNameById = (id: number): string => {
      const node = findNodeInTree(warehouseOptions.value, id);
      return node ? node.name : '';
    };

    const getSupplierName = (code?: string): string => {
      if (!code) return '';
      const supplier = supplierList.value.find(s => s.supplierCode === code);
      return supplier ? `${supplier.supplierCode}-${supplier.supplierName}` : code;
    };

    tableData.value = rawList.map(item => ({
      ...item,
      clientName: getClientNameById(item.clientId),
      warehouseName: getWarehouseNameById(item.warehouseId),
      supplierName: getSupplierName(item.supplierShipper)
    }));
    pagination.total = res?.total || 0;
  } catch (error) {
    console.error('加载数据失败:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
};

const loadSystemParams = async () => {
  try {
    const res = await getSystemParameterByKey('TableColumn.showOverflowTooltip');
    // 如果参数值为 'false'，则启用不换行样式
    showOverflowTooltip.value = res !== 'false';
  } catch (error) {
    console.error("获取系统参数 'TableColumn.showOverflowTooltip' 失败:", error);
    // 默认不换行
    showOverflowTooltip.value = true;
  }
};

const loadDropdownOptions = async () => {
  try {
    // 加载客户选项
    const customerRes = await getCustomerList();
    console.log('客户数据响应:', customerRes);
    customerList.value = customerRes;
    console.log('客户选项:', customerOptions.value);

    // 加载仓库选项
    const warehouseRes = await getLocationTree();
    console.log('仓库数据响应:', warehouseRes);
    let resData: WmsLocationTreeVO[] = [];
    if (Array.isArray(warehouseRes)) {
        resData = warehouseRes;
    } else if (warehouseRes && Array.isArray((warehouseRes as any).data)) {
        resData = (warehouseRes as any).data;
    }
    warehouseOptions.value = resData;
    console.log('仓库树形选项:', warehouseOptions.value);

    // 加载供应商选项
    const supplierRes = await getSupplierList();
    console.log('供应商数据响应:', supplierRes);
    supplierList.value = supplierRes;
    console.log('供应商选项:', supplierOptions.value);

    // 加载单位字典
    const unitRes = await getDictDataByCode('unit');
    console.log('单位字典响应:', unitRes);
    const unitData = unitRes || [];
    unitOptions.value = unitData.map((item: any) => ({
      label: item.label,
      value: item.value
    }));
    console.log('单位选项:', unitOptions.value);

  } catch (error) {
    console.error('加载下拉选项失败:', error);
  }
};

// 事件处理
const handleCreateCommand = (command: string) => {
  if (command === 'from_notification') {
    notificationSelectorRef.value?.open();
  } else if (command === 'blind') {
    // 打开盲收验证对话框
    showBlindReceivingValidation.value = true;
  }
};

const handleNotificationSelected = async (notification: WmsInboundNotificationResp) => {
  if (!notification?.id) return;
  
  formMode.value = 'add';
  dialogVisible.value = true;
  formLoading.value = true;

  try {
    const detailRes = await getInboundNotificationDetail(notification.id);
    
    // 将通知单数据转换为收货记录表单数据
    formData.value = {
      isBlindReceiving: false,
      receivingType: 'ASN_RECEIVING',
      status: 'PENDING',
      notificationId: detailRes.id,
      notificationNo: detailRes.notificationNo,
      clientId: detailRes.clientId,
      warehouseId: detailRes.warehouseId,
      supplierShipper: detailRes.supplierShipper,
      sourceDocNo: detailRes.sourceDocNo,
      // 核心转换逻辑
      details: [(detailRes.details || []).map((d: any, index: number) => ({
        ...d,
        tempId: d.id || `temp_${Date.now()}_${index}`,
        lineNo: index + 1, // 生成行号
        expectedQuantity: d.expectedQuantity, // 预期数量从通知单带入
        receivedQuantity: d.expectedQuantity, // 默认实收数量等于预期数量
        receivedAtLocationId: null, // 待用户填写的收货库位
        // 保留从通知单带入的其他物料信息
        sku: d.itemSku,
        itemName: d.itemName,
        itemSpecification: d.specification || '',
        unitOfMeasure: d.unitOfMeasure,
      }))]
    };
  } catch (e) {
    console.error("加载通知单详情失败:", e);
    ElMessage.error("加载通知单详情失败");
    // 如果失败，关闭对话框
    dialogVisible.value = false;
  } finally {
    formLoading.value = false;
  }
};

/**
 * 统一处理编辑和查看模式下的数据加载和增强
 * @param row 列表行数据
 * @param mode 'edit' 或 'view'
 */
const openDialogWithDetails = async (row: WmsReceivingRecordSimpleResp, mode: 'edit' | 'view') => {
  formMode.value = mode;
  currentRow.value = row;
  formLoading.value = true;
  dialogVisible.value = true;

  try {
    // 1. 获取基础详情数据
    const detailRes = await getReceivingRecordDetail(row.id);
    if (!detailRes) throw new Error('未能获取到收货记录详情');

    // 2. 将后端明细 VO → 前端行对象（初步映射）
    const rawDetails: WmsReceivingRecordDetailResp[] = detailRes.details || [];
    let processedDetails = rawDetails.map((d, idx) => mapDetailVoToRow(d, idx));

    // 3. 并行获取物料信息，补充包装单位选项 & 换算系数
    const itemDetailPromises = processedDetails.map(d => getMtlItemById(d.itemId));
    const itemDetails = await Promise.all(itemDetailPromises);

    processedDetails = processedDetails.map((row, idx) => {
      const itemDetail = itemDetails[idx];
      const packageUnitOptions = (itemDetail?.packageUnits || []).map((unit: any) => ({
        label: getUnitDisplayName(unit.unitName) || unit.unitName,
        value: unit.unitName,
        conversionFactor: unit.conversionFactor,
      }));
      const selectedUnit = packageUnitOptions.find(opt => opt.value === row.packageUnit);
      return {
        ...row,
        packageUnitOptions,
        conversionFactor: selectedUnit?.conversionFactor || 1,
      };
    });

    // 4. 设置最终表单数据
    formData.value = {
      ...detailRes,
      details: [processedDetails],
    };

  } catch (error) {
    console.error('打开详情失败:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
    // 失败时，关闭对话框或显示基础信息
    formData.value = { ...row, details: [[]] };
    handleCloseDialog();
  } finally {
    formLoading.value = false;
  }
};

const handleEdit = async (row: WmsReceivingRecordSimpleResp) => {
  await openDialogWithDetails(row, 'edit');
};

const handleView = async (row: WmsReceivingRecordSimpleResp) => {
  await openDialogWithDetails(row, 'view');
};

const handleDelete = async (row: WmsReceivingRecordSimpleResp) => {
  try {
    await ElMessageBox.confirm(`确定删除通知单 "${row.receivingNo}" 吗?`, '确认删除', {
      type: 'warning'
    });
    await deleteReceivingRecord(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    }
  }
};

const handleCancel = async (row: WmsReceivingRecordSimpleResp) => {
  // 使用标准状态变更弹窗
  showStatusChangeDialog(row, 'CANCELLED');
};

// 盲收验证事件处理
const handleBlindReceivingValidationComplete = (result: BlindReceivingValidationVO) => {
  blindReceivingValidationResult.value = result;
  
  if (result.isAllowed) {
    // 验证通过，关闭验证对话框，打开收货记录创建表单
    showBlindReceivingValidation.value = false;
    
    // 显示成功消息
    let message = '盲收权限验证通过';
    if (result.requiresApproval) {
      message += '，但需要审批';
    }
    if (result.strategy === 'SUPPLEMENT') {
      message += `，请在 ${new Date(result.supplementDeadline || '').toLocaleString()} 前完成补录`;
    }
    ElMessage.success(message);
    
    // 打开盲收创建表单
    formMode.value = 'add';
    formData.value = {
      isBlindReceiving: true,
      receivingType: 'BLIND_RECEIVING',
      status: 'PENDING',
      // 将验证参数传入表单
      warehouseId: result.warehouseId,
      clientId: result.clientId,
      // 记录验证结果ID用于关联
      blindValidationId: result.id,
      details: [[]]
    };
    dialogVisible.value = true;
  } else {
    // 验证失败，显示失败信息
    ElMessage.error(`盲收权限验证失败：${result.validationMessage}`);
  }
};

const handleBlindReceivingValidationError = (error: any) => {
  console.error('盲收验证失败:', error);
  ElMessage.error('盲收验证过程中出现错误，请重试');
};

// 盲收验证相关状态
const showBlindReceivingValidation = ref(false);
const blindReceivingValidationResult = ref<BlindReceivingValidationVO | null>(null);

// 状态变更弹窗相关状态
const statusChangeDialogVisible = ref(false);
const statusChangeForm = reactive({
  remark: ''
});
const statusChangeFormRef = ref();
const currentChangeRow = ref<WmsReceivingRecordSimpleResp | null>(null);
const targetChangeStatus = ref<string>('');
const statusChangeSubmitting = ref(false);

// 显示状态变更弹窗
const showStatusChangeDialog = (row: WmsReceivingRecordSimpleResp, targetStatus: string) => {
  currentChangeRow.value = row;
  targetChangeStatus.value = targetStatus;
  statusChangeForm.remark = '';
  statusChangeDialogVisible.value = true;
};

// 获取状态中文名称
const getStatusLabel = (status: string): string => {
  const statusMap: Record<string, string> = {
    'DRAFT': '草稿',
    'PENDING': '待收货',
    'RECEIVING': '收货中',
    'PENDING_INSPECTION': '待检验',
    'INSPECTING': '检验中',
    'PARTIALLY_COMPLETED': '部分完成',
    'COMPLETED': '收货完成',
    'CLOSED': '已关闭',
    'CANCELLED': '已取消'
  };
  return statusMap[status] || status;
};

// 获取状态颜色配置
const getStatusColorConfig = (status: string) => {
  const colorMap: Record<string, { color: string; bgColor: string }> = {
    'DRAFT': { color: '#909399', bgColor: '#f4f4f5' },
    'PENDING': { color: '#409eff', bgColor: '#ecf5ff' },
    'RECEIVING': { color: '#e6a23c', bgColor: '#fdf6ec' },
    'PENDING_INSPECTION': { color: '#409eff', bgColor: '#ecf5ff' },
    'INSPECTING': { color: '#e6a23c', bgColor: '#fdf6ec' },
    'PARTIALLY_COMPLETED': { color: '#67c23a', bgColor: '#f0f9ff' },
    'COMPLETED': { color: '#67c23a', bgColor: '#f0f9ff' },
    'CLOSED': { color: '#909399', bgColor: '#f4f4f5' },
    'CANCELLED': { color: '#f56c6c', bgColor: '#fef0f0' }
  };
  return colorMap[status] || { color: '#909399', bgColor: '#f4f4f5' };
};

// 确认状态变更
const confirmStatusChange = async () => {
  if (!currentChangeRow.value || !targetChangeStatus.value) return;
  
  try {
    // 表单验证
    if (statusChangeFormRef.value) {
      await statusChangeFormRef.value.validate();
    }
    
    statusChangeSubmitting.value = true;
    
    // 执行状态变更
    await updateReceivingRecordStatus(currentChangeRow.value.id, {
      status: targetChangeStatus.value as WmsReceivingRecordStatus,
      remark: statusChangeForm.remark || undefined
    });
    
    // 关闭对话框
    statusChangeDialogVisible.value = false;
    
    ElMessage.success('状态变更成功');
    loadData(); // 刷新列表数据
    
  } catch (error) {
    console.error('状态变更失败:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    statusChangeSubmitting.value = false;
  }
};

// 表单验证规则
const statusChangeFormRules = computed(() => ({
  remark: [
    {
      required: targetChangeStatus.value === 'CANCELLED',
      message: '取消操作需要填写备注',
      trigger: 'blur'
    }
  ]
}));

const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录');
    return;
  }
  
  try {
    await ElMessageBox.confirm(`确定删除选中的 ${selectedRows.value.length} 条记录吗?`, '确认删除', {
      type: 'warning'
    });
    
    for (const row of selectedRows.value) {
      await deleteReceivingRecord(row.id);
    }
    
    ElMessage.success('批量删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    }
  }
};

const handleImport = () => {
  ElMessage.info('导入功能开发中...');
};

const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};

const handlePrint = () => {
  ElMessage.info('打印功能开发中...');
};

const submitForm = async (submittedValue: Record<string, any>) => {
  if (isViewing.value) return;
  
  formLoading.value = true;
  try {
    // VNForm提交的数据结构：{ header: {...}, details: [[...], [...]] }
    const headerData = submittedValue['header'] || {};
    const detailData = submittedValue['details']?.[0] || [];
    
    console.log('submitForm - headerData:', headerData);
    console.log('submitForm - detailData:', detailData);
    
    // 验证明细数据
    if (!Array.isArray(detailData) || detailData.length === 0) {
      ElMessage.warning('请至少添加一条收货明细');
      formLoading.value = false;
      return;
    }
    
    // 过滤有效明细并重新分配行号，只保留API需要的字段
    const validDetails = detailData
      .filter((d: any) => d && d.sku && d.itemId) // 确保有SKU和itemId
      .map((d: any, index: number) => {
        // 创建一个干净的对象，只包含API需要的字段
        const apiDetail: Partial<WmsReceivingRecordDetailCreateReq> = {
          lineNo: d.lineNo || index + 1, // 使用现有的行号，如果没有则生成
          itemId: d.itemId,
          receivedQuantity: d.receivedQuantity,
          unitOfMeasure: d.unitOfMeasure,
          packageQty: d.packageQty,
          packageUnit: d.packageUnit,
          // 确保收货库位是数字
          receivedAtLocationId: d.receivedAtLocationId ? parseInt(d.receivedAtLocationId, 10) : undefined,
          expectedQuantity: d.expectedQuantity,
          batchNo: d.batchNo || undefined,
          productionDate: d.productionDate || undefined,
          expiryDate: d.expiryDate || undefined,
          remark: d.remark || undefined
        };
        // 校验转换后的数字
        if (isNaN(apiDetail.receivedAtLocationId as number)) {
          apiDetail.receivedAtLocationId = undefined;
        }
        return apiDetail;
      });
    
    if (validDetails.length === 0) {
      ElMessage.warning('请至少添加一条有效的收货明细');
      formLoading.value = false;
      return;
    }
    
    // 手动构建 payload，确保没有多余字段
    const payload: Partial<WmsReceivingRecordCreateReq> = {
      receivingType: headerData.receivingType,
      actualArrivalDate: headerData.actualArrivalDate,
      supplementDeadline: headerData.supplementDeadline,
      clientId: headerData.clientId,
      warehouseId: headerData.warehouseId,
      supplierShipper: headerData.supplierShipper,
      sourceDocNo: headerData.sourceDocNo,
      remark: headerData.remark,
      notificationId: headerData.notificationId, // 从通知单创建时会需要
      details: validDetails as WmsReceivingRecordDetailCreateReq[]
    };
    
    // 移除 undefined 或 null 的顶层字段，保持 payload 干净
    Object.keys(payload).forEach(key => {
      if ((payload as any)[key] === undefined || (payload as any)[key] === null) {
        delete (payload as any)[key];
      }
    });

    console.log('submitForm - final payload:', payload);
    
    if (formMode.value === 'add') {
      if (formData.value.isBlindReceiving) {
        const blindPayload: WmsReceivingRecordBlindCreateReq = {
          actualArrivalDate: payload.actualArrivalDate,
          supplementDeadline: payload.supplementDeadline,
          clientId: payload.clientId!,
          warehouseId: payload.warehouseId!,
          supplierShipper: payload.supplierShipper,
          sourceDocNo: payload.sourceDocNo,
          remark: payload.remark,
          details: payload.details as WmsReceivingRecordDetailCreateReq[]
        };
        await createBlindReceivingRecord(blindPayload);
        ElMessage.success('盲收记录创建成功');
      } else {
        await createReceivingRecord(payload as WmsReceivingRecordCreateReq);
        ElMessage.success('收货记录创建成功');
      }
    } else if (formMode.value === 'edit') {
      await updateReceivingRecord(currentRow.value!.id, payload as WmsReceivingRecordUpdateReq);
      ElMessage.success('更新成功');
    }

    handleCloseDialog();
    loadData();
  } catch (error: any) {
    console.error('提交失败:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    formLoading.value = false;
  }
};

const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentRow.value = null;
};

// 表格事件处理
const handleFilterChange = (filters: Record<string, any>) => {
  console.log('handleFilterChange - filters:', filters);
  loadData();
};

const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

const handleSortChange = (sort: any) => {
  console.log('handleSortChange - sort:', sort);
  loadData();
};

const handleSelectionChange = (rows: WmsReceivingRecordSimpleResp[]) => {
  selectedRows.value = rows;
};

// === 物料选择和包装单位处理 ===

// 获取包装单位选项
const getPackageUnitOptions = (row: any) => {
  console.log('getPackageUnitOptions - row:', row);
  if (!row.itemId) {
    console.log('getPackageUnitOptions - no itemId, returning empty array');
    return [];
  }
  
  // 从detailTablesConfig中获取该行的包装单位选项
  const detailTable = detailTablesConfig.value[0];
  if (!detailTable?.tableProps.columns) {
    console.log('getPackageUnitOptions - no columns, returning empty array');
    return [];
  }
  
  const packageUnitColumn = detailTable.tableProps.columns.find(col => col.prop === 'packageUnit');
  const props = packageUnitColumn?.editComponentProps;

  if (!props) {
    console.log('getPackageUnitOptions - no editComponentProps, returning empty array');
    return [];
  }

  // 解析 props，它可能是一个对象，也可能是一个返回对象的函数
  const resolvedProps = typeof props === 'function' ? props(row) : props;

  // options 属性本身也可能是一个函数或一个数组
  const optionsOrFunc = (resolvedProps as any)?.options;

  let options: any[] = [];
  if (typeof optionsOrFunc === 'function') {
    // 如果是函数，则调用它以获取特定于行的选项
    options = optionsOrFunc(row) || [];
  } else {
    // 否则，直接使用它（它应该是一个数组）
    options = optionsOrFunc || [];
  }

  console.log('getPackageUnitOptions - returning options:', options);
  return options;
};

// 获取包装单位显示名称
const getPackageUnitDisplayName = (packageUnit: string): string => {
  if (!packageUnit) return '';
  
  // 优先使用系统字典获取单位显示名称
  const unitDisplayName = getUnitDisplayName(packageUnit);
  if (unitDisplayName && unitDisplayName !== packageUnit) {
    return unitDisplayName;
  }
  
  // 如果字典中没有找到，直接返回包装单位名称
  return packageUnit;
};

// 处理物料SKU选择确认
const handleSkuConfirm = async (selectedItem: any, rowIndex: number) => {
  console.log('handleSkuConfirm - selectedItem:', selectedItem);
  console.log('handleSkuConfirm - rowIndex:', rowIndex);
  
  if (!formData.value.details?.[0]?.[rowIndex]) return;
  
  const row = formData.value.details[0][rowIndex];
  
  try {
    // 获取物料详细信息，包括包装单位
    const itemDetail = await getMtlItemById(selectedItem.id);
    console.log('handleSkuConfirm - itemDetail:', itemDetail);
    
    // 填充物料基本信息
    row.itemId = selectedItem.id;
    row.sku = selectedItem.sku;
    row.itemName = selectedItem.name;
    row.itemSpecification = selectedItem.specification || '';
    row.unitOfMeasure = selectedItem.baseUnit;
    
    // 自动填充包装单位选项
    if (itemDetail.packageUnits && itemDetail.packageUnits.length > 0) {
      // 更新包装单位选项，优先显示友好的名称
      const packageUnitOptions = itemDetail.packageUnits.map((unit: any) => {
        const displayName = getUnitDisplayName(unit.unitName) || unit.unitName;
        return {
          label: displayName, // 显示友好名称
          value: unit.unitName, // 保存原始值
          conversionFactor: unit.conversionFactor
        };
      });
      
      // 直接将独立的选项列表和相关数据附加到行数据上
      row.packageUnitOptions = packageUnitOptions;
      
      // 默认选择第一个包装单位
      const defaultPackageUnit = itemDetail.packageUnits[0];
      if (defaultPackageUnit) {
        row.packageUnit = defaultPackageUnit.unitName;
        row.conversionFactor = defaultPackageUnit.conversionFactor;
        
        console.log('物料选择完成，包装单位设置:', {
          包装单位: row.packageUnit,
          转换比率: row.conversionFactor,
          当前包装数量: row.packageQty
        });
        
        // 如果有包装数量，自动计算基本数量（允许为0）
        if (row.packageQty >= 0) {
          const calculatedQuantity = parseFloat((row.packageQty * row.conversionFactor).toFixed(4));
          row.expectedQuantity = calculatedQuantity;
          console.log('物料选择时自动计算基本数量:', calculatedQuantity);
        }
        
        // 强制刷新表格配置以更新editable状态
        await nextTick();
        
        const displayName = getUnitDisplayName(defaultPackageUnit.unitName) || defaultPackageUnit.unitName;
        ElMessage.success(`已自动选择包装单位：${displayName}（转换比率：${defaultPackageUnit.conversionFactor}），现在可以填写包装数量了`);
      }
    } else {
      // 如果没有包装单位，清空相关字段
      row.packageUnit = '';
      row.conversionFactor = 1;
      row.packageUnitOptions = []; // 清空行级选项
    }
    
    ElMessage.success('物料信息已自动填充');
  } catch (error) {
    console.error('获取物料详情失败:', error);
    ElMessage.error('获取物料详情失败，请重试');
  }
};

// 处理包装单位变化
const handlePackageUnitChange = async (row: any, newPackageUnit: string) => {
  console.log('handlePackageUnitChange - newPackageUnit:', newPackageUnit, 'row:', row);
  
  if (!newPackageUnit) {
    // 如果清空包装单位，也清空包装数量和转换比率
    row.packageQty = null;
    row.conversionFactor = 1;
    ElMessage.info('已清空包装单位，包装数量已重置');
    return;
  }
  
  // 查找选中包装单位的转换比率
  const packageUnitOptions = getPackageUnitOptions(row);
  
  const selectedOption = packageUnitOptions.find((opt: any) => opt.value === newPackageUnit);
  if (selectedOption) {
    row.conversionFactor = selectedOption.conversionFactor;
    
    // 强制刷新以确保表格状态更新
    await nextTick();
    
    // 如果有包装数量，自动计算基本数量（允许为0）
    if (row.packageQty >= 0) {
      row.expectedQuantity = parseFloat((row.packageQty * row.conversionFactor).toFixed(4));
      if (row.packageQty === 0) {
        ElMessage.success(`包装数量为0，基本数量已自动设置为0`);
      } else {
        ElMessage.success(`已自动计算基本数量: ${row.expectedQuantity}`);
      }
    } else {
      ElMessage.success(`包装单位已选择，现在可以填写包装数量了`);
    }
  }
};

// 处理包装数量变化
const handlePackageQtyChange = (row: any, newPackageQty: number) => {
  console.log('handlePackageQtyChange 被调用 - newPackageQty:', newPackageQty);
  console.log('handlePackageQtyChange - row 当前状态:', {
    packageUnit: row.packageUnit,
    conversionFactor: row.conversionFactor,
    expectedQuantity: row.expectedQuantity
  });
  
  // 延迟计算以确保数据更新完成
  setTimeout(() => {
    calculateAndUpdateExpectedQuantity(row, newPackageQty);
  }, 100);
};

// 计算并更新基本数量
const calculateAndUpdateExpectedQuantity = (row: any, packageQty?: number) => {
  const currentPackageQty = packageQty !== undefined ? packageQty : row.packageQty;
  
  console.log('计算基本数量 - 当前参数:', {
    包装数量: currentPackageQty,
    包装单位: row.packageUnit,
    转换比率: row.conversionFactor
  });
  
  // 如果有包装单位和转换比率，自动计算基本数量（允许包装数量为0）
  if (row.packageUnit && row.conversionFactor && currentPackageQty >= 0) {
    const calculatedQuantity = parseFloat((currentPackageQty * row.conversionFactor).toFixed(4));
    console.log('计算结果:', {
      包装数量: currentPackageQty,
      转换比率: row.conversionFactor,
      计算结果: calculatedQuantity,
      原基本数量: row.expectedQuantity
    });
    
    // 强制更新基本数量
    row.expectedQuantity = calculatedQuantity;
    
    // 通知用户计算完成
    if (currentPackageQty === 0) {
      ElMessage.success(`包装数量为0，基本数量已自动设置为0`);
    } else {
      ElMessage.success(`已自动计算基本数量: ${calculatedQuantity} (${currentPackageQty} × ${row.conversionFactor})`);
    }
  } else {
    console.log('无法计算基本数量，条件不满足:', {
      有包装单位: !!row.packageUnit,
      有转换比率: !!row.conversionFactor,
      包装数量大于等于0: currentPackageQty >= 0
    });
    
    if (!row.packageUnit) {
      ElMessage.warning('请先选择包装单位');
    } else if (!row.conversionFactor) {
      ElMessage.warning('转换比率未设置，请重新选择包装单位');
    } else if (currentPackageQty < 0) {
      ElMessage.warning('包装数量不能为负数');
    }
  }
};

// === 基本数量自动计算 ===
const calculateExpectedQuantity = (row: any) => {
  const packageQty = row.packageQty || 0;
  const conversionFactor = row.conversionFactor || 1;
  return parseFloat((packageQty * conversionFactor).toFixed(4));
};

// 监听包装数量和包装单位变化，自动计算基本数量（保持向后兼容）
const handlePackageChange = (row: any, field: string, value: any) => {
  if (field === 'packageQty' || field === 'packageUnit') {
    console.log('handlePackageChange - row:', row);
    // 如果基本数量还没有手动修改过，则自动计算
    if (!row.manualExpectedQuantity) {
      row.expectedQuantity = calculateExpectedQuantity(row);
    }
  }
};

// 执行实际的计算
const performCalculation = (row: any, packageQty: number) => {
  if (!row.packageUnit || !row.conversionFactor || packageQty < 0) {
    console.log('计算条件不满足:', {
      有包装单位: !!row.packageUnit,
      有转换比率: !!row.conversionFactor,
      包装数量大于等于0: packageQty >= 0
    });
    return;
  }
  
  const expectedQty = parseFloat((packageQty * row.conversionFactor).toFixed(4));
  
  // 检查是否需要更新
  if (Math.abs((row.expectedQuantity || 0) - expectedQty) > 0.0001) {
    row.expectedQuantity = expectedQty;
    console.log('实时计算完成:', {
      包装数量: packageQty,
      转换比率: row.conversionFactor,
      计算结果: expectedQty
    });
    
    if (packageQty === 0) {
      ElMessage.success(`包装数量为0，基本数量已自动设置为0`);
    } else {
      ElMessage.success(`实时计算: ${expectedQty} = ${packageQty} × ${row.conversionFactor}`);
    }
  }
};

// === 序号管理 ===
const openSerialNumberManager = (index: number) => {
  const row = formData.value.details?.[0]?.[index];
  if (!row) return;
  
  ElMessage.info(`序号管理功能开发中... 当前行: ${index + 1}, 物料: ${row.sku}`);
  // TODO: 打开序号管理对话框
  // 这里可以实现：
  // 1. 手工录入序号
  // 2. 扫码录入序号  
  // 3. 批量导入序号
};

// === 明细行删除 ===
const handleDetailDeleteConfirm = async (index: number) => {
  const row = formData.value.details?.[0]?.[index];
  if (!row) return;
  
  try {
    await ElMessageBox.confirm(`确定删除第 ${index + 1} 行明细吗？`, '确认删除', {
      type: 'warning'
    });
    
    // 删除指定行
    formData.value.details[0].splice(index, 1);
    ElMessage.success('删除成功');
  } catch {
    // 用户取消删除
  }
};

// === 明细行处理函数 ===
const handleDetailAdd = async (tableIndex: number): Promise<Record<string, any> | null> => {
  if (tableIndex !== 0) return null; // 只处理第一个明细表

  const tempId = `temp_${Date.now()}`;
  const currentLength = formData.value.details?.[0]?.length ?? 0;
  
  const newRow = {
    tempId: tempId,
    lineNo: currentLength + 1,
    itemId: null,
    sku: '',
    itemName: '',
    itemSpecification: '',
    expectedQuantity: 0, // 盲收时预期量为0
    receivedQuantity: 1, // 默认实收为1
    unitOfMeasure: '',
    packageQty: null,
    packageUnit: '',
    receivedAtLocationId: null, // 默认无库位
    conversionFactor: null,
    batchNo: '',
    productionDate: null,
    expiryDate: null,
    remark: ''
  };
  
  console.log('handleDetailAdd - 创建新行:', newRow);
  return newRow;
};

const handleDetailDelete = async (tableIndex: number, row: any, rowIndex: number): Promise<boolean> => {
  if (tableIndex !== 0) return false; // 只处理第一个明细表
  console.log('handleDetailDelete - row:', row);
  try {
    await ElMessageBox.confirm(`确定删除第 ${rowIndex + 1} 行明细吗?`, '确认删除', { 
      type: 'warning' 
    });
    return true; // 返回 true 表示确认删除
  } catch {
    return false; // 返回 false 表示取消删除
  }
};

// === 状态流程事件处理 ===
const handleStatusChangeFromFlow = async (newStatus: string, oldStatus: string, remark?: string) => {
  try {
    // 调用后端API更新状态
    await updateReceivingRecordStatus(currentRow.value!.id, { 
      status: newStatus as WmsReceivingRecordStatus,
      remark 
    });
    
    // 更新本地数据
    formData.value.status = newStatus as WmsReceivingRecordStatus;
    if (currentRow.value) {
      currentRow.value.status = newStatus as WmsReceivingRecordStatus;
    }
    
    ElMessage.success('状态更新成功');
    loadData(); // 刷新列表数据
  } catch (error) {
    console.error('状态更新失败:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  }
};

const handleBeforeStatusChange = (newStatus: string, oldStatus: string) => {
  console.log('状态即将变更:', { from: oldStatus, to: newStatus });
  // 可以在这里添加状态变更前的验证逻辑
};

const handleAfterStatusChange = (newStatus: string, oldStatus: string) => {
  console.log('状态已变更:', { from: oldStatus, to: newStatus });
  // 可以在这里添加状态变更后的处理逻辑
  // 例如：发送通知、更新相关数据等
};

// ---------- 明细行字段映射 ----------
interface DetailRow {
  id?: number
  tempId: string
  lineNo: number
  itemId: number
  sku: string
  itemName: string
  itemSpecification?: string
  expectedQuantity: number
  receivedQuantity: number
  unitOfMeasure: string
  packageUnit?: string
  packageQty?: number
  receivedAtLocationId?: number
  batchNo?: string
  internalBatchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
  // 前端辅助
  packageUnitOptions?: { label: string; value: string; conversionFactor: number }[]
  conversionFactor?: number
}

const mapDetailVoToRow = (detail: WmsReceivingRecordDetailResp, index: number): DetailRow => {
  const tempId = detail.id ? `id_${detail.id}` : `temp_${Date.now()}_${index}`;
  return {
    id: detail.id,
    tempId,
    lineNo: detail.lineNo ?? index + 1,
    itemId: detail.itemId,
    sku: detail.itemSku,
    itemName: detail.itemName,
    itemSpecification: detail.specification,
    expectedQuantity: detail.expectedQuantity ?? 0,
    receivedQuantity: (detail.receivedQuantity ?? (detail as any).actualQuantity ?? detail.expectedQuantity ?? 0),
    unitOfMeasure: detail.unitOfMeasure,
    packageUnit: detail.packageUnit,
    packageQty: detail.packageQty,
    receivedAtLocationId: undefined, // 待后续补充
    batchNo: detail.batchNo,
    internalBatchNo: detail.internalBatchNo,
    productionDate: detail.productionDate,
    expiryDate: detail.expiryDate,
    remark: detail.remark,
  };
};
</script>




<style lang="scss" scoped>
.add-button-container {
    order: -1;
}

.search-wrapper {
  margin-bottom: 20px;
  :deep(.el-card__body) {
    padding-bottom: 2px;
  }
}

.toolbar-wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.detail-add-button {
    margin-bottom: 10px;
}

.operation-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.status-flow-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}


@media (min-width: 1200px) {
  .status-flow-section {
    margin-bottom: 32px;
    padding: 20px;
  }
}

.status-change-form {
  .change-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    p {
      margin: 8px 0;
      display: flex;
      align-items: center;
      gap: 8px;
      
      strong {
        min-width: 80px;
        color: #303133;
      }
    }
  }
}
</style> 