import type { TabsProps, TabsPaneContext } from 'element-plus';

// 定义单个标签页的配置项
export interface TabItem {
  name: string | number; // 标签页的唯一标识符 (对应 el-tab-pane 的 name)
  label: string; // 标签页显示的标题
  icon?: string | object; // 可选，标签页图标 (Element Plus 图标组件名或对象)
  disabled?: boolean; // 是否禁用该标签页
  lazy?: boolean; // 标签页内容是否懒加载
  closable?: boolean; // 标签是否可关闭 (需要配合 closable 或 editable prop 以及 tab-remove 事件)
  [key: string]: any; // 允许其他自定义属性，可在 slot 中访问
}

// 继承 ElTabs 的 Props，并添加 v-model 和 items
// 排除 'modelValue', 'update:modelValue', 'tabPosition', 'type' 等可能自定义处理的
export interface VNTabsProps extends Partial<Omit<TabsProps, 'modelValue' | 'editable'>> {
  modelValue: string | number; // 当前激活的标签页 name (v-model)
  items: TabItem[]; // 标签页配置数组
  type?: 'card' | 'border-card'; // 可选，标签页样式类型
  tabPosition?: 'top' | 'right' | 'bottom' | 'left'; // 可选，标签页位置
  editable?: boolean; // 标签是否可增加和删除 (需要配合 addable/closable 和相应事件)
}

// 定义组件 Emits
export interface VNTabsEmits {
  (e: 'update:modelValue', name: string | number): void; // v-model 更新
  (e: 'tab-click', pane: TabsPaneContext, event: Event): void; // Use TabsPaneContext
  (e: 'tab-change', name: string | number): void; // activeName 改变时触发
  (e: 'tab-remove', name: string | number): void; // 点击 tab 移除按钮后触发 (需要 editable 或 items[x].closable=true)
  (e: 'edit', paneName: string | number | undefined, action: 'remove' | 'add'): void; // 点击 tab 的新增按钮或删除按钮时触发
} 