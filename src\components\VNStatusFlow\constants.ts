/**
 * VNStatusFlow 常量配置和初始化
 */
import { registerStatusFlowConfig } from './hooks/useStatusFlow'
import { INBOUND_NOTIFICATION_CONFIG } from './configs/inboundNotification'
import { RECEIVING_RECORD_CONFIG } from './configs/receivingRecord'
import { OUTBOUND_NOTIFICATION_CONFIG } from './configs/outboundNotification'

// 预设的业务类型
export const BUSINESS_TYPES = {
  INBOUND_NOTIFICATION: 'INBOUND_NOTIFICATION',
  RECEIVING_RECORD: 'RECEIVING_RECORD',
  PUTAWAY_TASK: 'PUTAWAY_TASK',
  OUTBOUND_NOTIFICATION: 'OUTBOUND_NOTIFICATION',
  PICKING_TASK: 'PICKING_TASK',
  INVENTORY_TASK: 'INVENTORY_TASK'
} as const

// 默认显示配置
export const DEFAULT_DISPLAY_CONFIG = {
  showActions: true,
  showProgress: true,
  allowManualTransition: true,
  showDescription: false,
  showIcon: true,
  nodeSize: 'medium' as const,
  lineStyle: 'solid' as const
}

// 默认节点尺寸配置
export const NODE_SIZES = {
  small: { width: 32, height: 32, fontSize: 14 },
  medium: { width: 40, height: 40, fontSize: 18 },
  large: { width: 48, height: 48, fontSize: 22 }
} as const

// 默认颜色主题
export const COLOR_THEMES = {
  primary: {
    color: '#409eff',
    bgColor: '#ecf5ff'
  },
  success: {
    color: '#67c23a',
    bgColor: '#f0f9ff'
  },
  warning: {
    color: '#e6a23c',
    bgColor: '#fdf6ec'
  },
  danger: {
    color: '#f56c6c',
    bgColor: '#fef0f0'
  },
  info: {
    color: '#909399',
    bgColor: '#f4f4f5'
  }
} as const

// 状态图标映射
export const STATUS_ICONS = {
  // 通用状态图标
  DRAFT: 'Edit',
  PLANNED: 'Calendar',
  PROCESSING: 'Loading',
  COMPLETED: 'Check',
  CANCELLED: 'Close',
  CLOSED: 'Lock',
  
  // 入库相关
  ARRIVED: 'Van', // 替换Truck为Van
  RECEIVING: 'Goods', // 替换Box为Goods
  PARTIALLY_RECEIVED: 'Warning',
  RECEIVED: 'Check',
  
  // 出库相关
  PICKING: 'ShoppingCart',
  PICKED: 'Goods', // 替换Package为Goods
  SHIPPED: 'Van',
  DELIVERED: 'CircleCheck',
  
  // 库存相关
  COUNTING: 'DataAnalysis',
  VERIFIED: 'Select',
  ADJUSTED: 'Edit'
} as const

// 按钮类型映射
export const BUTTON_TYPES = {
  DRAFT: 'default',
  PLANNED: 'primary',
  ARRIVED: 'success',
  RECEIVING: 'warning',
  PARTIALLY_RECEIVED: 'warning',
  RECEIVED: 'success',
  CLOSED: 'info',
  CANCELLED: 'danger',
  PROCESSING: 'warning',
  COMPLETED: 'success'
} as const

// 需要确认的状态转换
export const CONFIRMATION_REQUIRED_STATES = [
  'CANCELLED',
  'CLOSED',
  'COMPLETED'
]

// 需要备注的状态转换
export const REMARK_REQUIRED_STATES = [
  'CANCELLED',
  'PARTIALLY_RECEIVED'
]

// 终态状态（不可再转换）
export const FINAL_STATES = [
  'CANCELLED',
  'CLOSED',
  'COMPLETED',
  'DELIVERED'
]

// 状态优先级（用于排序和进度计算）
export const STATUS_PRIORITY = {
  DRAFT: 0,
  PLANNED: 10,
  ARRIVED: 20,
  RECEIVING: 30,
  PARTIALLY_RECEIVED: 35,
  RECEIVED: 40,
  PROCESSING: 30,
  COMPLETED: 50,
  CLOSED: 60,
  CANCELLED: -1
} as const

// 状态分类
export const STATUS_CATEGORIES = {
  INITIAL: ['DRAFT'],
  IN_PROGRESS: ['PLANNED', 'ARRIVED', 'RECEIVING', 'PARTIALLY_RECEIVED', 'PROCESSING'],
  SUCCESS: ['RECEIVED', 'COMPLETED', 'CLOSED'],
  FAILURE: ['CANCELLED']
} as const

// 配置验证规则
export const CONFIG_VALIDATION_RULES = {
  businessType: {
    required: true,
    message: '业务类型不能为空'
  },
  statusOptions: {
    required: true,
    minLength: 2,
    message: '至少需要定义2个状态选项'
  },
  statusTransitions: {
    required: true,
    minLength: 1,
    message: '至少需要定义1个状态转换规则'
  }
}

// 国际化文本
export const I18N_TEXTS = {
  'zh-CN': {
    statusFlow: '状态流程',
    currentStatus: '当前状态',
    targetStatus: '目标状态',
    statusActions: '状态操作',
    confirmChange: '确认变更',
    cancel: '取消',
    remark: '备注',
    remarkRequired: '请输入状态变更备注',
    changeSuccess: '状态变更成功',
    changeFailed: '状态变更失败',
    noActions: '当前状态下暂无可用操作',
    permissionDenied: '权限不足，无法执行此操作'
  },
  'en-US': {
    statusFlow: 'Status Flow',
    currentStatus: 'Current Status',
    targetStatus: 'Target Status',
    statusActions: 'Status Actions',
    confirmChange: 'Confirm Change',
    cancel: 'Cancel',
    remark: 'Remark',
    remarkRequired: 'Please enter status change remark',
    changeSuccess: 'Status changed successfully',
    changeFailed: 'Status change failed',
    noActions: 'No available actions for current status',
    permissionDenied: 'Insufficient permissions to perform this action'
  }
}

// 性能优化配置
export const PERFORMANCE_CONFIG = {
  // 防抖延迟（毫秒）
  debounceDelay: 300,
  // 动画持续时间（毫秒）
  animationDuration: 300,
  // 最大状态数量（超过时显示警告）
  maxStatusCount: 20,
  // 缓存过期时间（毫秒）
  cacheExpiry: 5 * 60 * 1000 // 5分钟
}

// 组件默认配置
export const DEFAULT_COMPONENT_CONFIG = {
  readonly: false,
  compact: false,
  mode: 'horizontal' as const,
  showTitle: false,
  title: '状态流程'
}

/**
 * 初始化状态流程配置
 * 注册所有预设的业务类型配置
 */
export const initializeStatusFlowConfigs = () => {
  try {
    // 注册入库通知单配置
    registerStatusFlowConfig(INBOUND_NOTIFICATION_CONFIG)

    // 注册收货记录配置
    registerStatusFlowConfig(RECEIVING_RECORD_CONFIG)

    // 注册出库通知单配置
    registerStatusFlowConfig(OUTBOUND_NOTIFICATION_CONFIG)

    // TODO: 注册其他业务类型配置
    // registerStatusFlowConfig(PUTAWAY_TASK_CONFIG)
    // registerStatusFlowConfig(PICKING_TASK_CONFIG)

    console.log('✅ VNStatusFlow: 状态流程配置初始化完成')
  } catch (error) {
    console.error('❌ VNStatusFlow: 状态流程配置初始化失败', error)
  }
}

// 工具函数：获取状态的CSS类名
export const getStatusClassName = (status: string): string => {
  const category = Object.entries(STATUS_CATEGORIES).find(([, statuses]) => 
    statuses.includes(status as never)
  )?.[0]
  
  return `status-${status.toLowerCase()}${category ? ` status-category-${category.toLowerCase()}` : ''}`
}

// 工具函数：检查状态是否为终态
export const isFinalState = (status: string): boolean => {
  return FINAL_STATES.includes(status as any)
}

// 工具函数：获取状态优先级
export const getStatusPriority = (status: string): number => {
  return STATUS_PRIORITY[status as keyof typeof STATUS_PRIORITY] ?? 0
}

// 工具函数：比较状态优先级
export const compareStatusPriority = (statusA: string, statusB: string): number => {
  return getStatusPriority(statusA) - getStatusPriority(statusB)
}

// 工具函数：获取状态分类
export const getStatusCategory = (status: string): string | undefined => {
  return Object.entries(STATUS_CATEGORIES).find(([, statuses]) => 
    statuses.includes(status as never)
  )?.[0]
}

// 导出类型定义
export type BusinessType = keyof typeof BUSINESS_TYPES
export type NodeSize = keyof typeof NODE_SIZES
export type ColorTheme = keyof typeof COLOR_THEMES
export type StatusIcon = keyof typeof STATUS_ICONS
export type ButtonType = keyof typeof BUTTON_TYPES 