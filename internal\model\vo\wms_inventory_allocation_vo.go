package vo

import (
	"time"
)

// WmsInventoryAllocationVO 库存分配视图对象
type WmsInventoryAllocationVO struct {
	ID                  uint      `json:"id"`
	OutboundDetailID    uint      `json:"outboundDetailId"`
	InventoryID         uint      `json:"inventoryId"`
	AllocatedQty        float64   `json:"allocatedQty"`
	PickedQty           float64   `json:"pickedQty"`
	Status              string    `json:"status"`
	StatusName          *string   `json:"statusName"`          // 扩展字段：状态名称
	AllocationTime      time.Time `json:"allocationTime"`
	PickingTaskDetailID *uint     `json:"pickingTaskDetailId"`
	AllocationStrategy  *string   `json:"allocationStrategy"`
	AllocationReason    *string   `json:"allocationReason"`
	Remark              *string   `json:"remark"`
	
	// 进度信息
	AvailableQty        float64   `json:"availableQty"`        // 可拣货数量
	CompletionRate      float64   `json:"completionRate"`      // 完成率
	
	// 扩展字段 - 出库明细信息
	OutboundNotificationNo *string `json:"outboundNotificationNo"` // 出库通知单号
	OutboundLineNo         *int    `json:"outboundLineNo"`         // 出库明细行号
	
	// 扩展字段 - 库存信息
	ItemID              *uint     `json:"itemId"`              // 物料ID
	ItemCode            *string   `json:"itemCode"`            // 物料编码
	ItemName            *string   `json:"itemName"`            // 物料名称
	ItemSpec            *string   `json:"itemSpec"`            // 物料规格
	LocationID          *uint     `json:"locationId"`          // 库位ID
	LocationCode        *string   `json:"locationCode"`        // 库位编码
	LocationName        *string   `json:"locationName"`        // 库位名称
	BatchNo             *string   `json:"batchNo"`             // 批次号
	ProductionDate      *string   `json:"productionDate"`      // 生产日期
	ExpiryDate          *string   `json:"expiryDate"`          // 过期日期
	UnitOfMeasure       *string   `json:"unitOfMeasure"`       // 单位
	
	// 时间字段
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`
	CreatedBy           uint      `json:"createdBy"`
	UpdatedBy           uint      `json:"updatedBy"`
	CreatedByName       *string   `json:"createdByName"`       // 扩展字段：创建人姓名
	UpdatedByName       *string   `json:"updatedByName"`       // 扩展字段：更新人姓名
	
	// 关联数据
	OutboundDetail      *WmsOutboundNotificationDetailVO `json:"outboundDetail,omitempty"`
	Inventory           *WmsInventoryVO                   `json:"inventory,omitempty"`
	PickingTaskDetail   *WmsPickingTaskDetailVO           `json:"pickingTaskDetail,omitempty"`
}

// WmsInventoryAllocationListVO 库存分配列表视图对象
type WmsInventoryAllocationListVO struct {
	ID                     uint      `json:"id"`
	OutboundNotificationNo *string   `json:"outboundNotificationNo"`
	OutboundLineNo         *int      `json:"outboundLineNo"`
	ItemCode               *string   `json:"itemCode"`
	ItemName               *string   `json:"itemName"`
	LocationCode           *string   `json:"locationCode"`
	BatchNo                *string   `json:"batchNo"`
	AllocatedQty           float64   `json:"allocatedQty"`
	PickedQty              float64   `json:"pickedQty"`
	AvailableQty           float64   `json:"availableQty"`
	Status                 string    `json:"status"`
	StatusName             *string   `json:"statusName"`
	AllocationStrategy     *string   `json:"allocationStrategy"`
	AllocationTime         time.Time `json:"allocationTime"`
	CompletionRate         float64   `json:"completionRate"`
	CreatedByName          *string   `json:"createdByName"`
}

// WmsInventoryAllocationStatsVO 库存分配统计视图对象
type WmsInventoryAllocationStatsVO struct {
	// 基础统计
	TotalCount          int     `json:"totalCount"`          // 总分配记录数
	TotalAllocatedQty   float64 `json:"totalAllocatedQty"`   // 总分配数量
	TotalPickedQty      float64 `json:"totalPickedQty"`      // 总拣货数量
	
	// 状态统计
	AllocatedCount      int     `json:"allocatedCount"`      // 已分配数量
	PickedCount         int     `json:"pickedCount"`         // 已拣货数量
	ReleasedCount       int     `json:"releasedCount"`       // 已释放数量
	
	// 策略统计
	FifoCount           int     `json:"fifoCount"`           // FIFO策略数量
	LifoCount           int     `json:"lifoCount"`           // LIFO策略数量
	FefoCount           int     `json:"fefoCount"`           // FEFO策略数量
	NearestCount        int     `json:"nearestCount"`        // 就近策略数量
	
	// 效率统计
	AvgAllocationTime   float64 `json:"avgAllocationTime"`   // 平均分配时间（分钟）
	AvgPickingTime      float64 `json:"avgPickingTime"`      // 平均拣货时间（分钟）
	AllocationAccuracy  float64 `json:"allocationAccuracy"`  // 分配准确率
	
	// 趋势数据
	TrendData           []WmsInventoryAllocationTrendVO `json:"trendData,omitempty"`
}

// WmsInventoryAllocationTrendVO 库存分配趋势数据
type WmsInventoryAllocationTrendVO struct {
	Date            string  `json:"date"`            // 日期
	Count           int     `json:"count"`           // 分配记录数
	AllocatedQty    float64 `json:"allocatedQty"`    // 分配数量
	PickedQty       float64 `json:"pickedQty"`       // 拣货数量
	Accuracy        float64 `json:"accuracy"`        // 准确率
}

// WmsInventoryAvailabilityVO 库存可用性视图对象
type WmsInventoryAvailabilityVO struct {
	ItemID              uint                                    `json:"itemId"`
	ItemCode            *string                                 `json:"itemCode"`
	ItemName            *string                                 `json:"itemName"`
	RequiredQty         float64                                 `json:"requiredQty"`
	AvailableQty        float64                                 `json:"availableQty"`
	AllocatedQty        float64                                 `json:"allocatedQty"`
	ReservedQty         float64                                 `json:"reservedQty"`
	ShortageQty         float64                                 `json:"shortageQty"`
	CanAllocate         bool                                    `json:"canAllocate"`
	AllocationResult    string                                  `json:"allocationResult"` // FULL, PARTIAL, NONE
	
	// 可用库存明细
	AvailableInventories []WmsInventoryAvailabilityDetailVO     `json:"availableInventories,omitempty"`
}

// WmsInventoryAvailabilityDetailVO 库存可用性明细
type WmsInventoryAvailabilityDetailVO struct {
	InventoryID     uint    `json:"inventoryId"`
	LocationCode    *string `json:"locationCode"`
	LocationName    *string `json:"locationName"`
	BatchNo         *string `json:"batchNo"`
	ProductionDate  *string `json:"productionDate"`
	ExpiryDate      *string `json:"expiryDate"`
	AvailableQty    float64 `json:"availableQty"`
	AllocatedQty    float64 `json:"allocatedQty"`
	ReservedQty     float64 `json:"reservedQty"`
	Quality         *string `json:"quality"`         // 质量状态
	Priority        int     `json:"priority"`        // 分配优先级
}

// WmsInventoryReservationVO 库存预占视图对象
type WmsInventoryReservationVO struct {
	ID              uint      `json:"id"`
	InventoryID     uint      `json:"inventoryId"`
	ReservedQty     float64   `json:"reservedQty"`
	ReservationType string    `json:"reservationType"`
	ReferenceID     uint      `json:"referenceId"`
	ReferenceNo     *string   `json:"referenceNo"`     // 关联单据号
	ExpiryTime      *time.Time `json:"expiryTime"`
	Status          string    `json:"status"`
	StatusName      *string   `json:"statusName"`
	Remark          *string   `json:"remark"`
	
	// 扩展字段
	ItemCode        *string   `json:"itemCode"`
	ItemName        *string   `json:"itemName"`
	LocationCode    *string   `json:"locationCode"`
	BatchNo         *string   `json:"batchNo"`
	
	// 时间字段
	CreatedAt       time.Time `json:"createdAt"`
	ReleasedAt      *time.Time `json:"releasedAt"`
	CreatedBy       uint      `json:"createdBy"`
	CreatedByName   *string   `json:"createdByName"`
}

// WmsInventoryAllocationSummaryVO 库存分配汇总视图对象
type WmsInventoryAllocationSummaryVO struct {
	OutboundNotificationID uint                                      `json:"outboundNotificationId"`
	OutboundNotificationNo string                                    `json:"outboundNotificationNo"`
	TotalItems             int                                       `json:"totalItems"`
	AllocatedItems         int                                       `json:"allocatedItems"`
	FullyAllocatedItems    int                                       `json:"fullyAllocatedItems"`
	PartiallyAllocatedItems int                                      `json:"partiallyAllocatedItems"`
	UnallocatedItems       int                                       `json:"unallocatedItems"`
	TotalQty               float64                                   `json:"totalQty"`
	AllocatedQty           float64                                   `json:"allocatedQty"`
	AllocationRate         float64                                   `json:"allocationRate"`
	CanProceedToPicking    bool                                      `json:"canProceedToPicking"`
	
	// 明细分配状态
	Details                []WmsInventoryAllocationDetailSummaryVO  `json:"details,omitempty"`
}

// WmsInventoryAllocationDetailSummaryVO 库存分配明细汇总
type WmsInventoryAllocationDetailSummaryVO struct {
	DetailID        uint    `json:"detailId"`
	LineNo          int     `json:"lineNo"`
	ItemCode        *string `json:"itemCode"`
	ItemName        *string `json:"itemName"`
	RequiredQty     float64 `json:"requiredQty"`
	AllocatedQty    float64 `json:"allocatedQty"`
	AllocationRate  float64 `json:"allocationRate"`
	AllocationCount int     `json:"allocationCount"`    // 分配记录数
	CanAllocate     bool    `json:"canAllocate"`
	ShortageQty     float64 `json:"shortageQty"`
	ShortageReason  *string `json:"shortageReason"`
}
