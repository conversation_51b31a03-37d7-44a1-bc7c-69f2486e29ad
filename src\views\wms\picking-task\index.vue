<template>
  <div class="picking-task-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>拣货任务管理</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>仓库管理</el-breadcrumb-item>
          <el-breadcrumb-item>出库管理</el-breadcrumb-item>
          <el-breadcrumb-item>拣货任务</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          @click="handleWaveManagement"
          v-if="checkPermission('picking:task:wave')"
        >
          <el-icon><Grid /></el-icon>
          波次管理
        </el-button>
        <el-button
          type="success"
          @click="handleCreate"
          v-if="checkPermission('picking:task:create')"
        >
          <el-icon><Plus /></el-icon>
          新建任务
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="5" v-for="(stat, key) in statusStats" :key="key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stat }}</div>
              <div class="stat-label">{{ getStatusLabel(key) }}</div>
            </div>
            <div class="stat-icon" :class="`stat-icon-${key.toLowerCase()}`">
              <el-icon><Box /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任务看板 -->
    <div class="task-board" v-if="showBoard">
      <el-row :gutter="16">
        <el-col :span="6">
          <TaskColumn
            title="待分配"
            :tasks="pendingTasks"
            status="PENDING"
            @task-click="handleTaskClick"
            @assign-task="handleAssignTask"
          />
        </el-col>
        <el-col :span="6">
          <TaskColumn
            title="已分配"
            :tasks="assignedTasks"
            status="ASSIGNED"
            @task-click="handleTaskClick"
            @start-task="handleStartTask"
          />
        </el-col>
        <el-col :span="6">
          <TaskColumn
            title="进行中"
            :tasks="inProgressTasks"
            status="IN_PROGRESS"
            @task-click="handleTaskClick"
            @complete-task="handleCompleteTask"
          />
        </el-col>
        <el-col :span="6">
          <TaskColumn
            title="已完成"
            :tasks="completedTasks"
            status="COMPLETED"
            @task-click="handleTaskClick"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="main-content" v-show="!showBoard">
      <!-- 视图切换 -->
      <div class="view-controls">
        <el-radio-group v-model="viewMode" @change="handleViewModeChange">
          <el-radio-button label="table">表格视图</el-radio-button>
          <el-radio-button label="board">看板视图</el-radio-button>
        </el-radio-group>
      </div>

      <VNTable
        ref="vnTableRef"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        :toolbar-config="toolbarConfig"
        :show-operations="true"
        :operation-width="200"
        operation-fixed="right"
        row-key="id"
        :selection-type="'multiple'"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @selection-change="handleSelectionChange"
        @filter-change="handleFilterChange"
        @refresh="handleRefresh"
      >
        <!-- 自定义列插槽 -->
        <template #column-taskNo="{ row }">
          <el-link
            type="primary"
            @click="handleView(row)"
            :underline="false"
          >
            {{ row.taskNo }}
          </el-link>
        </template>

        <template #column-priority="{ row }">
          <el-tag :type="getPriorityTagType(row.priority)">
            {{ formatPriority(row.priority) }}
          </el-tag>
        </template>

        <template #column-status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ formatStatus(row.status) }}
          </el-tag>
        </template>

        <template #column-progress="{ row }">
          <el-progress
            :percentage="getTaskProgress(row)"
            :color="getProgressColor(row)"
            :show-text="false"
            style="width: 80px"
          />
          <span style="margin-left: 8px; font-size: 12px;">
            {{ getTaskProgress(row) }}%
          </span>
        </template>

        <template #column-assignedUserName="{ row }">
          <el-tag v-if="row.assignedUserName" type="info" size="small">
            {{ row.assignedUserName }}
          </el-tag>
          <span v-else class="text-muted">未分配</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="handleEdit(row)"
            v-if="canEdit(row) && checkPermission('picking:task:edit')"
          >
            编辑
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleMobileEntry(row)"
            v-if="['ASSIGNED', 'IN_PROGRESS'].includes(row.status)"
          >
            <el-icon><Cellphone /></el-icon>
            移动端
          </el-button>
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, row)"
            v-if="getDropdownItems(row).length > 0"
          >
            <el-button size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in getDropdownItems(row)"
                  :key="item.command"
                  :command="item.command"
                  :disabled="item.disabled"
                >
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </VNTable>
    </el-card>

    <!-- 表单对话框 -->
    <PickingTaskForm
      ref="formDialogRef"
      @confirm="handleFormConfirm"
      @cancel="handleFormCancel"
    />

    <!-- 波次管理对话框 -->
    <WaveManager
      ref="waveManagerRef"
      :selected-tasks="selectedTaskIds"
      @confirm="handleWaveConfirm"
      @cancel="handleWaveCancel"
    />

    <!-- 用户选择器 -->
    <UserSelector
      ref="userSelectorRef"
      @confirm="handleUserConfirm"
      @cancel="handleUserCancel"
    />

    <!-- 任务执行对话框 -->
    <TaskExecution
      ref="taskExecutionRef"
      @confirm="handleExecutionConfirm"
      @cancel="handleExecutionCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElCard, ElRow, ElCol, ElButton, ElIcon, ElLink, ElTag, ElProgress, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessage, ElMessageBox, ElBreadcrumb, ElBreadcrumbItem, ElRadioGroup, ElRadioButton } from 'element-plus'
import { Plus, Grid, Box, ArrowDown, Cellphone } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
import PickingTaskForm from './components/PickingTaskForm.vue'
import WaveManager from './components/WaveManager.vue'
import UserSelector from './components/UserSelector.vue'
import TaskExecution from './components/TaskExecution.vue'
import TaskColumn from './components/TaskColumn.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import type { WmsPickingTaskResp, WmsPickingTaskStatus, WmsUserResp } from '@/api/wms/pickingTask'
import { usePickingTaskStore } from '@/store/wms/pickingTask'

// Store
const pickingTaskStore = usePickingTaskStore()

// 路由和Store
const router = useRouter()

// 响应式数据
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const formDialogRef = ref<InstanceType<typeof PickingTaskForm>>()
const waveManagerRef = ref<InstanceType<typeof WaveManager>>()
const userSelectorRef = ref<InstanceType<typeof UserSelector>>()
const taskExecutionRef = ref<InstanceType<typeof TaskExecution>>()

const selectedRows = ref<WmsPickingTaskResp[]>([])
const selectedTaskIds = computed(() => selectedRows.value.map(row => row.id))
const currentTaskId = ref<number>(0)
const viewMode = ref<'table' | 'board'>('table')
const showBoard = computed(() => viewMode.value === 'board')

// 计算属性
const list = computed(() => pickingTaskStore.list)
const total = computed(() => pickingTaskStore.total)
const loading = computed(() => pickingTaskStore.loading)
const statusStats = computed(() => pickingTaskStore.statusStats)
const pendingTasks = computed(() => pickingTaskStore.pendingTasks)
const assignedTasks = computed(() => pickingTaskStore.assignedTasks)
const inProgressTasks = computed(() => pickingTaskStore.inProgressTasks)
const completedTasks = computed(() => pickingTaskStore.completedTasks)

// 分页配置
const pagination = reactive<PaginationConfig>({
  total: total,
  currentPage: computed(() => pickingTaskStore.pagination.pageNum),
  pageSize: computed(() => pickingTaskStore.pagination.pageSize)
})

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'taskNo', label: '任务编号', minWidth: 140, slot: true },
  { prop: 'notificationNo', label: '出库单号', minWidth: 140 },
  { prop: 'pickingStrategy', label: '拣货策略', width: 120 },
  { prop: 'waveNo', label: '波次编号', width: 120 },
  { prop: 'priority', label: '优先级', width: 80, slot: true },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'progress', label: '进度', width: 120, slot: true },
  { prop: 'assignedUserName', label: '分配人员', width: 100, slot: true },
  { prop: 'estimatedDuration', label: '预计时长', width: 100 },
  { prop: 'createdAt', label: '创建时间', width: 150 }
])

// 工具栏配置
const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showFilter: true,
  showExport: checkPermission('picking:task:export'),
  batchOperations: [
    {
      label: '批量分配',
      value: 'batchAssign',
      type: 'primary',
      permission: 'picking:task:assign'
    },
    {
      label: '批量取消',
      value: 'batchCancel',
      type: 'danger',
      permission: 'picking:task:cancel'
    }
  ],
  filterFields: [
    {
      prop: 'taskNo',
      label: '任务编号',
      type: 'input',
      placeholder: '请输入任务编号'
    },
    {
      prop: 'notificationNo',
      label: '出库单号',
      type: 'input',
      placeholder: '请输入出库单号'
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '待分配', value: 'PENDING' },
        { label: '已分配', value: 'ASSIGNED' },
        { label: '进行中', value: 'IN_PROGRESS' },
        { label: '已完成', value: 'COMPLETED' },
        { label: '已取消', value: 'CANCELLED' }
      ]
    },
    {
      prop: 'assignedUserId',
      label: '分配人员',
      type: 'select',
      placeholder: '请选择人员',
      options: [] // 这里应该从API获取用户列表
    },
    {
      prop: 'dateRange',
      label: '创建时间',
      type: 'daterange',
      placeholder: '请选择时间范围'
    }
  ]
}))

// 方法
const loadData = async () => {
  try {
    await pickingTaskStore.fetchList()
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const handlePageChange = (page: number) => {
  pickingTaskStore.searchForm.pageNum = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pickingTaskStore.searchForm.pageSize = size
  pickingTaskStore.searchForm.pageNum = 1
  loadData()
}

const handleSelectionChange = (rows: WmsPickingTaskResp[]) => {
  selectedRows.value = rows
}

const handleFilterChange = (filters: Record<string, any>) => {
  // 处理日期范围
  if (filters.dateRange && filters.dateRange.length === 2) {
    filters.createdAtStart = filters.dateRange[0]
    filters.createdAtEnd = filters.dateRange[1]
    delete filters.dateRange
  }
  
  Object.assign(pickingTaskStore.searchForm, filters)
  pickingTaskStore.searchForm.pageNum = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const handleViewModeChange = (mode: 'table' | 'board') => {
  if (mode === 'board') {
    // 看板模式需要加载所有状态的任务
    loadData()
  }
}

const handleCreate = () => {
  formDialogRef.value?.open('create')
}

const handleView = (row: WmsPickingTaskResp) => {
  formDialogRef.value?.open('view', row.id)
}

const handleEdit = (row: WmsPickingTaskResp) => {
  formDialogRef.value?.open('edit', row.id)
}

const handleTaskClick = (task: WmsPickingTaskResp) => {
  handleView(task)
}

const handleWaveManagement = () => {
  waveManagerRef.value?.open()
}

const handleFormConfirm = () => {
  loadData()
}

const handleFormCancel = () => {
  // 表单取消处理
}

const handleWaveConfirm = () => {
  loadData()
}

const handleWaveCancel = () => {
  // 波次管理取消处理
}

// 权限检查
const checkPermission = (permission: string): boolean => {
  // 这里应该实现实际的权限检查逻辑
  return true // 临时返回true
}

// 工具方法
const canEdit = (row: WmsPickingTaskResp): boolean => {
  return ['PENDING', 'ASSIGNED'].includes(row.status)
}

const getPriorityTagType = (priority: number) => {
  if (priority <= 2) return 'danger'
  if (priority <= 3) return 'warning'
  return 'info'
}

const formatPriority = (priority: number) => {
  const labels = ['', '最高', '高', '中', '低', '最低']
  return labels[priority] || '未知'
}

const getStatusTagType = (status: WmsPickingTaskStatus) => {
  const typeMap = {
    PENDING: 'info',
    ASSIGNED: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: WmsPickingTaskStatus) => {
  const labelMap = {
    PENDING: '待分配',
    ASSIGNED: '已分配',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return labelMap[status] || '未知'
}

const getTaskProgress = (row: WmsPickingTaskResp) => {
  // 优先使用API返回的progress字段，否则根据状态和明细计算
  if (row.progress !== undefined) {
    return row.progress
  }

  // 根据状态和明细计算进度
  if (row.status === 'COMPLETED') return 100
  if (row.status === 'CANCELLED') return 0
  if (row.status === 'PENDING') return 10
  if (row.status === 'ASSIGNED') return 20
  if (row.status === 'IN_PROGRESS') {
    // 根据明细的拣货进度计算
    if (row.details && row.details.length > 0) {
      const totalRequired = row.details.reduce((sum, detail) => sum + detail.requiredQty, 0)
      const totalPicked = row.details.reduce((sum, detail) => sum + (detail.pickedQty || 0), 0)
      return totalRequired > 0 ? Math.round((totalPicked / totalRequired) * 100) : 20
    }
    return 50
  }
  return 0
}

const getProgressColor = (row: WmsPickingTaskResp) => {
  const progress = getTaskProgress(row)
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getStatusLabel = (status: string) => {
  return formatStatus(status as WmsPickingTaskStatus)
}

const getDropdownItems = (row: WmsPickingTaskResp) => {
  const items = []
  
  if (row.status === 'PENDING' && checkPermission('picking:task:assign')) {
    items.push({ command: 'assign', label: '分配人员', disabled: false })
  }
  
  if (row.status === 'ASSIGNED' && checkPermission('picking:task:start')) {
    items.push({ command: 'start', label: '开始任务', disabled: false })
  }
  
  if (row.status === 'IN_PROGRESS' && checkPermission('picking:task:execute')) {
    items.push({ command: 'execute', label: '执行拣货', disabled: false })
    items.push({ command: 'complete', label: '完成任务', disabled: false })
  }
  
  if (['PENDING', 'ASSIGNED', 'IN_PROGRESS'].includes(row.status) && checkPermission('picking:task:cancel')) {
    items.push({ command: 'cancel', label: '取消任务', disabled: false })
  }
  
  return items
}

const handleDropdownCommand = async (command: string, row: WmsPickingTaskResp) => {
  switch (command) {
    case 'assign':
      await handleAssignTask(row)
      break
    case 'start':
      await handleStartTask(row)
      break
    case 'execute':
      await handleExecuteTask(row)
      break
    case 'complete':
      await handleCompleteTask(row)
      break
    case 'cancel':
      await handleCancelTask(row)
      break
  }
}

const handleAssignTask = async (row: WmsPickingTaskResp) => {
  currentTaskId.value = row.id
  userSelectorRef.value?.open()
}

const handleUserConfirm = async (user: WmsUserResp) => {
  try {
    await pickingTaskStore.assignTask(currentTaskId.value, user.id, '手动分配')
    ElMessage.success('分配成功')
    loadData()
  } catch (error) {
    ElMessage.error('分配失败')
    console.error(error)
  }
}

const handleUserCancel = () => {
  currentTaskId.value = 0
}

const handleStartTask = async (row: WmsPickingTaskResp) => {
  try {
    await ElMessageBox.confirm('确定要开始这个拣货任务吗？', '确认开始', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await pickingTaskStore.startTask(row.id)
    ElMessage.success('任务已开始')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('开始任务失败')
      console.error(error)
    }
  }
}

const handleExecuteTask = (row: WmsPickingTaskResp) => {
  currentTaskId.value = row.id
  taskExecutionRef.value?.open(row.id)
}

const handleExecutionConfirm = () => {
  ElMessage.success('拣货执行成功')
  loadData()
}

const handleExecutionCancel = () => {
  currentTaskId.value = 0
}

const handleCompleteTask = async (row: WmsPickingTaskResp) => {
  try {
    await ElMessageBox.confirm('确定要完成这个拣货任务吗？', '确认完成', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await pickingTaskStore.completeTask(row.id, '任务完成')
    ElMessage.success('任务已完成')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('完成任务失败')
      console.error(error)
    }
  }
}

const handleCancelTask = async (row: WmsPickingTaskResp) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消拣货任务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '请输入取消原因'
    })
    
    await pickingTaskStore.cancelTask(row.id, reason)
    ElMessage.success('任务已取消')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
      console.error(error)
    }
  }
}

// 移动端入口
const handleMobileEntry = (row: WmsPickingTaskResp) => {
  // 跳转到移动端拣货页面
  router.push({
    name: 'WmsPickingTaskMobile',
    params: { id: row.id }
  })
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.picking-task-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.task-board {
  margin-bottom: 20px;
}

.main-content {
  background: #fff;
}

.view-controls {
  margin-bottom: 16px;
  text-align: right;
}

.text-muted {
  color: #909399;
}
</style>
