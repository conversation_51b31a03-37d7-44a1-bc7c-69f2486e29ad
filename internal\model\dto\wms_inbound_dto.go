package dto

import (
	"time"

	"backend/internal/model/entity"
	"backend/pkg/response"
)

// WmsInboundNotificationDetailCreateReq 创建入库通知单明细的请求结构
type WmsInboundNotificationDetailCreateReq struct {
	NotificationID    uint    `json:"notificationId,omitempty"`                  // 通知单ID (单独创建时需要)
	LineNo            int     `json:"lineNo" validate:"required,gt=0"`           // 行号
	ItemID            uint    `json:"itemId" validate:"required,gt=0"`           // 物料ID
	ExpectedQuantity  float64 `json:"expectedQuantity" validate:"required,gt=0"` // 预期数量
	UnitOfMeasure     string  `json:"unitOfMeasure" validate:"required"`         // 数量单位
	BatchNo           string  `json:"batchNo,omitempty"`                         // 批次号 (可选)
	PackageQty        float64 `json:"packageQty"`                                // 包装数量
	PackageUnit       string  `json:"packageUnit"`                               // 包装单位
	ProductionDateStr *string `json:"productionDate,omitempty"`                  // 生产日期 (可选)
	ExpiryDateStr     *string `json:"expiryDate,omitempty"`                      // 过期日期 (可选)
	Remark            string  `json:"remark,omitempty"`                          // 备注 (可选)
}

// WmsInboundNotificationCreateReq 创建入库通知单的请求结构
type WmsInboundNotificationCreateReq struct {
	NotificationNo         string                                  `json:"notificationNo"`                          // 通知单号 (业务单号) - 由后端生成
	NotificationType       string                                  `json:"notificationType" validate:"required"`    // 通知类型 (例如: PO, RETURN, TRANSFER)
	ExpectedArrivalDateStr *string                                 `json:"expectedArrivalDate" validate:"required"` // 预期到货时间
	SupplierShipper        *string                                 `json:"supplierShipper,omitempty"`               // 供应商/发货方 (可选)
	ClientID               uint                                    `json:"clientId" validate:"required,gt=0"`       // 客户ID (或货主ID)
	WarehouseID            uint                                    `json:"warehouseId" validate:"required,gt=0"`    // 目标仓库ID
	SourceDocNo            *string                                 `json:"sourceDocNo,omitempty"`                   // 关联客户指令号/源单据号 (可选)
	Remark                 string                                  `json:"remark,omitempty"`                        // 备注 (可选)
	Details                []WmsInboundNotificationDetailCreateReq `json:"details" validate:"required,min=1,dive"`  // 明细列表 (至少包含一条)
}

// WmsInboundNotificationQueryReq 查询入库通知单的请求结构
type WmsInboundNotificationQueryReq struct {
	NotificationNo          *string            `json:"notificationNo,omitempty" form:"notificationNo"`                   // 通知单号 (模糊查询)
	NotificationType        *string            `json:"notificationType,omitempty" form:"notificationType"`               // 通知类型
	Status                  *string            `json:"status,omitempty" form:"status"`                                   // 状态
	Statuses                []string           `json:"statuses,omitempty" form:"statuses"`                               // 状态列表 (IN 查询)
	ClientID                *uint              `json:"clientId,omitempty" form:"clientId"`                               // 客户ID
	WarehouseID             *uint              `json:"warehouseId,omitempty" form:"warehouseId"`                         // 仓库ID
	ExpectedArrivalDateFrom *time.Time         `json:"expectedArrivalDateFrom,omitempty" form:"expectedArrivalDateFrom"` // 预期到货时间范围 - 开始
	ExpectedArrivalDateTo   *time.Time         `json:"expectedArrivalDateTo,omitempty" form:"expectedArrivalDateTo"`     // 预期到货时间范围 - 结束
	CreatedAtFrom           *time.Time         `json:"createdAtFrom,omitempty" form:"createdAtFrom"`                     // 创建时间范围 - 开始
	CreatedAtTo             *time.Time         `json:"createdAtTo,omitempty" form:"createdAtTo"`                         // 创建时间范围 - 结束
	SortField               string             `json:"sortField,omitempty" form:"sortField"`                             // 排序字段
	SortOrder               string             `json:"sortOrder,omitempty" form:"sortOrder"`                             // 排序顺序 (asc/desc)
	IncludeDetails          *bool              `json:"includeDetails,omitempty" form:"includeDetails"`                   // 是否包含明细表数据 (可选，默认false)
	PageQuery               response.PageQuery // 分页参数，不参与JSON序列化
}

// WmsInboundNotificationUpdateStatusReq 更新入库通知单状态的请求结构
type WmsInboundNotificationUpdateStatusReq struct {
	Status string `json:"status" validate:"required"` // 新的状态
	Remark string `json:"remark,omitempty"`           // 备注 (可选)
}

// WmsInboundNotificationBatchImportReq 批量导入入库通知单的请求结构
type WmsInboundNotificationBatchImportReq struct {
	Notifications     []WmsInboundNotificationCreateReq `json:"notifications" validate:"required,min=1,dive"` // 入库通知单列表
	OverwriteExisting bool                              `json:"overwriteExisting"`                            // 是否覆盖已存在的单据
}

// BatchImportResult 批量导入结果
type BatchImportResult struct {
	TotalCount     int      `json:"totalCount"`     // 总数量
	SuccessCount   int      `json:"successCount"`   // 成功数量
	FailureCount   int      `json:"failureCount"`   // 失败数量
	SuccessIDs     []uint   `json:"successIds"`     // 成功导入的ID列表
	FailureReasons []string `json:"failureReasons"` // 失败原因列表
}

// WmsInboundNotificationDetailResp 入库通知单明细的响应结构
type WmsInboundNotificationDetailResp struct {
	entity.WmsInboundNotificationDetail // 嵌入实体，包含所有基础字段
	// 可以添加关联实体的具体信息，例如 ItemName
	ItemSKU  string `json:"itemSku,omitempty"`
	ItemName string `json:"itemName,omitempty"`
}

// WmsInboundNotificationResp 入库通知单的响应结构 (修改：显式字段，NullString改为*string)
type WmsInboundNotificationResp struct {
	// BaseEntity fields
	ID        uint      `json:"id"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
	CreatedBy uint      `json:"createdBy"`
	UpdatedBy uint      `json:"updatedBy"`
	// TenantEntity field
	TenantID uint `json:"tenantId"`
	// WmsInboundNotification fields
	NotificationNo         string  `json:"notificationNo"`
	NotificationType       string  `json:"notificationType"`
	WarehouseID            uint    `json:"warehouseId"`
	ClientID               uint    `json:"clientId"`
	SourceDocNo            *string `json:"sourceDocNo,omitempty"`     // 使用指针 *string
	SupplierShipper        *string `json:"supplierShipper,omitempty"` // 使用指针 *string
	ExpectedArrivalDateStr *string `json:"expectedArrivalDate"`       //
	Status                 string  `json:"status"`
	Remark                 *string `json:"remark,omitempty"` // 使用指针 *string
	AccountBookID          uint    `json:"accountBookId"`
	// Details
	Details []WmsInboundNotificationDetailResp `json:"details,omitempty"` // 使用响应结构的明细, omitempty 用于列表页
	// Added fields
	ClientName    string `json:"clientName,omitempty"`
	WarehouseName string `json:"warehouseName,omitempty"`
	SupplierName  string `json:"supplierName,omitempty"`
}

// WmsInboundNotificationUpdateReq 更新入库通知单的请求结构
type WmsInboundNotificationUpdateReq struct {
	NotificationNo         *string                                  `json:"notificationNo,omitempty"`      // 通知单号 (可选更新)
	NotificationType       *string                                  `json:"notificationType,omitempty"`    // 通知类型
	ExpectedArrivalDateStr *string                                  `json:"expectedArrivalDate,omitempty"` // 预期到货时间
	SupplierShipper        *string                                  `json:"supplierShipper,omitempty"`     // 供应商/发货方
	ClientID               *uint                                    `json:"clientId,omitempty"`            // 客户ID
	WarehouseID            *uint                                    `json:"warehouseId,omitempty"`         // 仓库ID
	SourceDocNo            *string                                  `json:"sourceDocNo,omitempty"`         // 关联客户指令号/源单据号
	Remark                 *string                                  `json:"remark,omitempty"`              // 备注
	Details                *[]WmsInboundNotificationDetailUpdateReq `json:"details,omitempty"`             // 明细表更新（可选，如果提供则进行明细表差异更新）
}

// WmsInboundNotificationDetailUpdateReq 明细表更新请求（用于差异更新）
type WmsInboundNotificationDetailUpdateReq struct {
	ID                *uint    `json:"id,omitempty"`                                         // 明细ID (如果有ID则更新，无ID则新增)
	LineNo            *int     `json:"lineNo,omitempty" validate:"omitempty,gt=0"`           // 行号
	ItemID            *uint    `json:"itemId,omitempty" validate:"omitempty,gt=0"`           // 物料ID
	ExpectedQuantity  *float64 `json:"expectedQuantity,omitempty" validate:"omitempty,gt=0"` // 预期数量
	UnitOfMeasure     *string  `json:"unitOfMeasure,omitempty"`                              // 数量单位
	BatchNo           *string  `json:"batchNo,omitempty"`                                    // 批次号
	PackageQty        *float64 `json:"packageQty,omitempty"`                                 // 包装数量
	PackageUnit       *string  `json:"packageUnit,omitempty"`                                // 包装单位
	ProductionDateStr *string  `json:"productionDate,omitempty"`                             // 生产日期
	ExpiryDateStr     *string  `json:"expiryDate,omitempty"`                                 // 过期日期
	Remark            *string  `json:"remark,omitempty"`                                     // 备注
	_ToDelete         *bool    `json:"_toDelete,omitempty"`                                  // 标记删除（内部使用）
}

// WmsGenerateReceivingReq 根据入库通知单生成收货记录的请求结构
type WmsGenerateReceivingReq struct {
	ReceivedDate time.Time `json:"receivedDate" validate:"required"` // 实际收货日期
	Remarks      string    `json:"remarks,omitempty"`                // 备注
}

// WmsInboundNotificationDetailQueryReq 查询入库通知明细的请求结构
type WmsInboundNotificationDetailQueryReq struct {
	NotificationID       *uint      `json:"notificationId,omitempty" form:"notificationId"`             // 通知单ID
	LineNo               *int       `json:"lineNo,omitempty" form:"lineNo"`                             // 行号
	ItemID               *uint      `json:"itemId,omitempty" form:"itemId"`                             // 物料ID
	BatchNo              *string    `json:"batchNo,omitempty" form:"batchNo"`                           // 批次号
	ExpectedQuantityFrom *float64   `json:"expectedQuantityFrom,omitempty" form:"expectedQuantityFrom"` // 预期数量范围 - 开始
	ExpectedQuantityTo   *float64   `json:"expectedQuantityTo,omitempty" form:"expectedQuantityTo"`     // 预期数量范围 - 结束
	ProductionFrom       *time.Time `json:"productionFrom,omitempty" form:"productionFrom"`             // 生产日期范围 - 开始
	ProductionTo         *time.Time `json:"productionTo,omitempty" form:"productionTo"`                 // 生产日期范围 - 结束
	ExpiryFrom           *time.Time `json:"expiryFrom,omitempty" form:"expiryFrom"`                     // 过期日期范围 - 开始
	ExpiryTo             *time.Time `json:"expiryTo,omitempty" form:"expiryTo"`                         // 过期日期范围 - 结束
	SortField            string     `json:"sortField,omitempty" form:"sortField"`                       // 排序字段
	SortOrder            string     `json:"sortOrder,omitempty" form:"sortOrder"`                       // 排序顺序
	PageQuery            response.PageQuery
}

// WmsInboundNotificationDetailBatchCreateReq 批量创建入库通知明细的请求结构
type WmsInboundNotificationDetailBatchCreateReq struct {
	NotificationID uint                                    `json:"notificationId" validate:"required,gt=0"` // 通知单ID
	Details        []WmsInboundNotificationDetailCreateReq `json:"details" validate:"required,min=1,dive"`  // 明细列表
}

// WmsInboundNotificationDetailBatchUpdateReq 批量更新入库通知明细的请求结构
type WmsInboundNotificationDetailBatchUpdateReq struct {
	Updates []struct {
		ID     uint                                  `json:"id" validate:"required,gt=0"` // 明细ID
		Detail WmsInboundNotificationDetailUpdateReq `json:"detail" validate:"required"`  // 更新数据
	} `json:"updates" validate:"required,min=1,dive"`
}

// InboundNotificationDetailSummary 入库通知明细汇总信息
type InboundNotificationDetailSummary struct {
	NotificationID          uint    `json:"notificationId"`          // 通知单ID
	TotalLines              int     `json:"totalLines"`              // 总行数
	TotalExpectedQuantity   float64 `json:"totalExpectedQuantity"`   // 总预期数量
	TotalItems              int     `json:"totalItems"`              // 总物料数
	UniqueBatches           int     `json:"uniqueBatches"`           // 唯一批次数
	HasExpiryDate           int     `json:"hasExpiryDate"`           // 有过期日期的行数
	NearExpiryLines         int     `json:"nearExpiryLines"`         // 即将过期的行数
	ItemCategories          int     `json:"itemCategories"`          // 物料类别数
	AverageExpectedQuantity float64 `json:"averageExpectedQuantity"` // 平均预期数量
}
