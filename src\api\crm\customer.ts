import request from '@/utils/request';

// --- TypeScript 类型定义 (基于 Go VO 和 DTO) ---

// 基础分页查询参数
interface BaseQueryDTO {
  pageNum?: number;
  pageSize?: number;
  sort?: string;
}

// 基础视图对象
interface BaseVO {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: number;
  updatedBy?: number;
}

// --- CRM 客户相关类型 ---

export interface CrmCustomerCreateDTO {
  customerCode?: string; // 支持自动生成
  customerName: string;
  customerType?: string; // CORPORATE, INDIVIDUAL
  industry?: string;
  businessLicense?: string;
  taxNumber?: string;
  legalRepresentative?: string;
  registeredCapital?: number;
  
  // 联系信息
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  
  // 地址信息
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  postalCode?: string;
  
  // 财务信息
  creditRating?: string;
  creditLimit?: number;
  paymentTerms?: string;
  currencyCode?: string;
  
  // 业务信息
  customerLevel?: string; // VIP, GOLD, SILVER, NORMAL
  customerSource?: string;
  salesRepresentativeId?: number;
  
  // 状态信息
  status?: string; // ACTIVE, INACTIVE, BLACKLIST
  isKeyCustomer?: boolean;
  remark?: string;
  
  // 嵌套创建联系人
  contacts?: CrmCustomerContactCreateDTO[];
}

export interface CrmCustomerUpdateDTO {
  customerName?: string;
  customerType?: string;
  industry?: string;
  businessLicense?: string;
  taxNumber?: string;
  legalRepresentative?: string;
  registeredCapital?: number;
  
  // 联系信息
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  
  // 地址信息
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  postalCode?: string;
  
  // 财务信息
  creditRating?: string;
  creditLimit?: number;
  paymentTerms?: string;
  currencyCode?: string;
  
  // 业务信息
  customerLevel?: string;
  customerSource?: string;
  salesRepresentativeId?: number;
  
  // 状态信息
  status?: string;
  isKeyCustomer?: boolean;
  remark?: string;
}

export interface CrmCustomerQueryDTO extends BaseQueryDTO {
  customerCode?: string;
  customerName?: string;
  customerType?: string;
  customerLevel?: string;
  status?: string;
  isKeyCustomer?: boolean;
  salesRepresentativeId?: number;
  industry?: string;
  province?: string;
  city?: string;
}

export interface CrmCustomerVO extends BaseVO {
  customerCode: string;
  customerName: string;
  customerType: string;
  industry?: string;
  businessLicense?: string;
  taxNumber?: string;
  legalRepresentative?: string;
  registeredCapital?: number;
  
  // 联系信息
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  
  // 地址信息
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  postalCode?: string;
  
  // 财务信息
  creditRating?: string;
  creditLimit?: number;
  paymentTerms?: string;
  currencyCode?: string;
  
  // 业务信息
  customerLevel?: string;
  customerSource?: string;
  salesRepresentativeId?: number;
  salesRepresentativeName?: string; // 关联显示
  
  // 状态信息
  status: string;
  isKeyCustomer?: boolean;
  remark?: string;
  
  // 联系人列表
  contacts?: CrmCustomerContactVO[];
}

export interface CrmCustomerListVO {
  id: number;
  customerCode: string;
  customerName: string;
  customerType: string;
  customerLevel?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  province?: string;
  city?: string;
  salesRepresentativeName?: string;
  status: string;
  isKeyCustomer?: boolean;
  createdAt: string;
}

export interface CrmCustomerSummaryVO {
  totalCount: number;
  activeCount: number;
  inactiveCount: number;
  blacklistCount: number;
  vipCount: number;
  goldCount: number;
  silverCount: number;
  normalCount: number;
  corporateCount: number;
  individualCount: number;
  keyCustomerCount: number;
}

export interface PageCrmCustomerVO {
  list: CrmCustomerListVO[];
  total: number;
  pageNum?: number;
  pageSize?: number;
}

// --- CRM 客户联系人相关类型 ---

export interface CrmCustomerContactCreateDTO {
  customerId?: number; // 在嵌套创建时可能不需要
  contactName: string;
  contactTitle?: string;
  department?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  qq?: string;
  wechat?: string;
  address?: string;
  postalCode?: string;
  birthday?: string;
  gender?: string; // MALE, FEMALE
  contactRole?: string;
  remark?: string;
}

export interface CrmCustomerContactUpdateDTO {
  contactName?: string;
  contactTitle?: string;
  department?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  qq?: string;
  wechat?: string;
  address?: string;
  postalCode?: string;
  birthday?: string;
  gender?: string;
  contactRole?: string;
  remark?: string;
}

export interface CrmCustomerContactVO extends BaseVO {
  customerId: number;
  customerName?: string; // 关联显示
  contactName: string;
  contactTitle?: string;
  department?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  qq?: string;
  wechat?: string;
  address?: string;
  postalCode?: string;
  birthday?: string;
  gender?: string;
  contactRole?: string;
  remark?: string;
}

// 简化的客户选择器类型
export interface CrmCustomerSimpleVO {
  id: number;
  customerCode: string;
  customerName: string;
  customerType: string;
  status: string;
}

// 验证响应类型
export interface ValidationResponse {
  valid: boolean;
  message?: string;
}

// --- API 请求函数 ---

const API_BASE_URL = '/crm/customers';

// --- 客户 API ---

// 分页查询客户
export function getCustomerPage(params: Partial<CrmCustomerQueryDTO>): Promise<PageCrmCustomerVO> {
  return request.get(`${API_BASE_URL}`, { params });
}

// 创建客户
export function createCustomer(data: CrmCustomerCreateDTO): Promise<CrmCustomerVO> {
  return request.post(`${API_BASE_URL}`, data);
}

// 更新客户
export function updateCustomer(id: number, data: CrmCustomerUpdateDTO): Promise<CrmCustomerVO> {
  return request.put(`${API_BASE_URL}/${id}`, data);
}

// 删除客户
export function deleteCustomer(id: number): Promise<void> {
  return request.delete(`${API_BASE_URL}/${id}`);
}

// 获取客户详情
export function getCustomer(id: number): Promise<CrmCustomerVO> {
  return request.get(`${API_BASE_URL}/${id}`);
}

// 根据客户编码获取客户
export function getCustomerByCode(customerCode: string): Promise<CrmCustomerVO> {
  return request.get(`${API_BASE_URL}/code/${customerCode}`);
}

// 获取客户统计摘要
export function getCustomerSummary(): Promise<CrmCustomerSummaryVO> {
  return request.get(`${API_BASE_URL}/summary`);
}

// --- 客户联系人 API ---

// 获取客户联系人列表
export function getCustomerContacts(customerId: number): Promise<CrmCustomerContactVO[]> {
  return request.get(`${API_BASE_URL}/${customerId}/contacts`);
}

// 创建客户联系人
export function createCustomerContact(data: CrmCustomerContactCreateDTO): Promise<CrmCustomerContactVO> {
  return request.post(`${API_BASE_URL}/contacts`, data);
}

// 更新客户联系人
export function updateCustomerContact(contactId: number, data: CrmCustomerContactUpdateDTO): Promise<CrmCustomerContactVO> {
  return request.put(`${API_BASE_URL}/contacts/${contactId}`, data);
}

// 删除客户联系人
export function deleteCustomerContact(contactId: number): Promise<void> {
  return request.delete(`${API_BASE_URL}/contacts/${contactId}`);
}

// 设置主要联系人
export function setPrimaryContact(customerId: number, contactId: number): Promise<void> {
  return request.put(`${API_BASE_URL}/${customerId}/contacts/${contactId}/primary`);
}

// 获取主要联系人
export function getPrimaryContact(customerId: number): Promise<CrmCustomerContactVO> {
  return request.get(`${API_BASE_URL}/${customerId}/contacts/primary`);
}

// --- 业务验证 API ---

// 验证客户编码
export function validateCustomerCode(customerCode: string, excludeId?: number): Promise<ValidationResponse> {
  const params: any = { customerCode };
  if (excludeId) {
    params.excludeId = excludeId;
  }
  return request.get(`${API_BASE_URL}/validate/customer-code`, { params });
}

// 验证营业执照号
export function validateBusinessLicense(businessLicense: string, excludeId?: number): Promise<ValidationResponse> {
  const params: any = { businessLicense };
  if (excludeId) {
    params.excludeId = excludeId;
  }
  return request.get(`${API_BASE_URL}/validate/business-license`, { params });
}

// 验证税务登记号
export function validateTaxNumber(taxNumber: string, excludeId?: number): Promise<ValidationResponse> {
  const params: any = { taxNumber };
  if (excludeId) {
    params.excludeId = excludeId;
  }
  return request.get(`${API_BASE_URL}/validate/tax-number`, { params });
}

// --- 辅助工具函数 ---

// 获取客户简单列表（用于下拉框选择）
export function getCustomerList(): Promise<CrmCustomerSimpleVO[]> {
  return request.get('/crm/customers/simple');
} 