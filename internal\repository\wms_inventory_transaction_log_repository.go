package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsInventoryTransactionLogRepository 库存事务日志数据访问接口
type WmsInventoryTransactionLogRepository interface {
	BaseRepository[entity.WmsInventoryTransactionLog, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInventoryTransactionReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByInventoryID(ctx context.Context, inventoryId uint) ([]*entity.WmsInventoryTransactionLog, error)
	FindByTransactionType(ctx context.Context, transactionType entity.TransactionType) ([]*entity.WmsInventoryTransactionLog, error)
	FindByReferenceDoc(ctx context.Context, refDocType string, refDocID uint) ([]*entity.WmsInventoryTransactionLog, error)
	FindByOperatorID(ctx context.Context, operatorId uint) ([]*entity.WmsInventoryTransactionLog, error)
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryTransactionLog, error)

	// 统计查询
	CountByInventoryID(ctx context.Context, inventoryId uint) (int64, error)
	CountByTransactionType(ctx context.Context, transactionType entity.TransactionType) (int64, error)
	SumQuantityChangeByType(ctx context.Context, transactionType entity.TransactionType, startDate, endDate time.Time) (float64, error)

	// 批量操作
	BatchCreate(ctx context.Context, logs []*entity.WmsInventoryTransactionLog) error

	// 清理操作
	CleanupOldLogs(ctx context.Context, beforeDate time.Time) error
}

// WmsInventoryTransactionLogQueryBuilder 库存事务日志查询构建器接口
type WmsInventoryTransactionLogQueryBuilder interface {
	// 基础条件
	WhereInventoryID(inventoryId uint) WmsInventoryTransactionLogQueryBuilder
	WhereTransactionType(transactionType entity.TransactionType) WmsInventoryTransactionLogQueryBuilder
	WhereReferenceDoc(refDocType string, refDocID uint) WmsInventoryTransactionLogQueryBuilder
	WhereOperatorID(operatorId uint) WmsInventoryTransactionLogQueryBuilder
	WhereDateRange(startDate, endDate time.Time) WmsInventoryTransactionLogQueryBuilder

	// 数量条件
	WhereQuantityChangeGreaterThan(quantity float64) WmsInventoryTransactionLogQueryBuilder
	WhereQuantityChangeLessThan(quantity float64) WmsInventoryTransactionLogQueryBuilder

	// 排序
	OrderByCreatedAt(desc bool) WmsInventoryTransactionLogQueryBuilder
	OrderByQuantityChange(desc bool) WmsInventoryTransactionLogQueryBuilder

	// 关联查询
	WithInventory() WmsInventoryTransactionLogQueryBuilder
	WithOperator() WmsInventoryTransactionLogQueryBuilder

	// 执行查询
	Find() ([]entity.WmsInventoryTransactionLog, error)
	First() (*entity.WmsInventoryTransactionLog, error)
	Count() (int64, error)
	Paginate(pageNum, pageSize int) ([]entity.WmsInventoryTransactionLog, int64, error)
}

// WmsInventoryTransactionLogRepositoryImpl 库存事务日志数据访问实现
type WmsInventoryTransactionLogRepositoryImpl struct {
	BaseRepository[entity.WmsInventoryTransactionLog, uint]
	db *gorm.DB
}

// NewWmsInventoryTransactionLogRepository 创建库存事务日志数据访问实例
func NewWmsInventoryTransactionLogRepository(db *gorm.DB) WmsInventoryTransactionLogRepository {
	baseRepo := NewBaseRepository[entity.WmsInventoryTransactionLog, uint](db)
	return &WmsInventoryTransactionLogRepositoryImpl{
		BaseRepository: baseRepo,
		db:             db,
	}
}

// GetPage 分页查询库存事务日志
func (r *WmsInventoryTransactionLogRepositoryImpl) GetPage(ctx context.Context, query *dto.WmsInventoryTransactionReq) (*response.PageResult, error) {
	// TODO: 实现分页查询逻辑
	return nil, nil
}

// FindByInventoryID 根据库存ID查找事务日志
func (r *WmsInventoryTransactionLogRepositoryImpl) FindByInventoryID(ctx context.Context, inventoryId uint) ([]*entity.WmsInventoryTransactionLog, error) {
	var logs []*entity.WmsInventoryTransactionLog
	err := r.db.WithContext(ctx).Where("inventory_id = ?", inventoryId).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindByTransactionType 根据事务类型查找日志
func (r *WmsInventoryTransactionLogRepositoryImpl) FindByTransactionType(ctx context.Context, transactionType entity.TransactionType) ([]*entity.WmsInventoryTransactionLog, error) {
	var logs []*entity.WmsInventoryTransactionLog
	err := r.db.WithContext(ctx).Where("transaction_type = ?", transactionType).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindByReferenceDoc 根据关联单据查找日志
func (r *WmsInventoryTransactionLogRepositoryImpl) FindByReferenceDoc(ctx context.Context, refDocType string, refDocID uint) ([]*entity.WmsInventoryTransactionLog, error) {
	var logs []*entity.WmsInventoryTransactionLog
	err := r.db.WithContext(ctx).Where("ref_doc_type = ? AND ref_doc_id = ?", refDocType, refDocID).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindByOperatorID 根据操作员ID查找日志
func (r *WmsInventoryTransactionLogRepositoryImpl) FindByOperatorID(ctx context.Context, operatorId uint) ([]*entity.WmsInventoryTransactionLog, error) {
	var logs []*entity.WmsInventoryTransactionLog
	err := r.db.WithContext(ctx).Where("operator_id = ?", operatorId).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindByDateRange 根据日期范围查找日志
func (r *WmsInventoryTransactionLogRepositoryImpl) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryTransactionLog, error) {
	var logs []*entity.WmsInventoryTransactionLog
	err := r.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// CountByInventoryID 统计指定库存的事务数量
func (r *WmsInventoryTransactionLogRepositoryImpl) CountByInventoryID(ctx context.Context, inventoryId uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.WmsInventoryTransactionLog{}).
		Where("inventory_id = ?", inventoryId).
		Count(&count).Error
	return count, err
}

// CountByTransactionType 统计指定类型的事务数量
func (r *WmsInventoryTransactionLogRepositoryImpl) CountByTransactionType(ctx context.Context, transactionType entity.TransactionType) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.WmsInventoryTransactionLog{}).
		Where("transaction_type = ?", transactionType).
		Count(&count).Error
	return count, err
}

// SumQuantityChangeByType 统计指定类型的数量变化总和
func (r *WmsInventoryTransactionLogRepositoryImpl) SumQuantityChangeByType(ctx context.Context, transactionType entity.TransactionType, startDate, endDate time.Time) (float64, error) {
	var sum float64
	err := r.db.WithContext(ctx).Model(&entity.WmsInventoryTransactionLog{}).
		Where("transaction_type = ? AND created_at BETWEEN ? AND ?", transactionType, startDate, endDate).
		Select("COALESCE(SUM(quantity_change), 0)").
		Scan(&sum).Error
	return sum, err
}

// BatchCreate 批量创建事务日志
func (r *WmsInventoryTransactionLogRepositoryImpl) BatchCreate(ctx context.Context, logs []*entity.WmsInventoryTransactionLog) error {
	if len(logs) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(&logs).Error
}

// BatchCreateWithTx 在事务中批量创建事务日志
func (r *WmsInventoryTransactionLogRepositoryImpl) BatchCreateWithTx(tx *gorm.DB, logs []*entity.WmsInventoryTransactionLog) error {
	if len(logs) == 0 {
		return nil
	}
	return tx.Create(&logs).Error
}

// CleanupOldLogs 清理旧日志
func (r *WmsInventoryTransactionLogRepositoryImpl) CleanupOldLogs(ctx context.Context, beforeDate time.Time) error {
	return r.db.WithContext(ctx).Where("created_at < ?", beforeDate).
		Delete(&entity.WmsInventoryTransactionLog{}).Error
}

// NewQueryBuilder 创建查询构建器
func (r *WmsInventoryTransactionLogRepositoryImpl) NewQueryBuilder() WmsInventoryTransactionLogQueryBuilder {
	return &WmsInventoryTransactionLogQueryBuilderImpl{
		db:    r.db,
		query: r.db.Model(&entity.WmsInventoryTransactionLog{}),
	}
}

// WmsInventoryTransactionLogQueryBuilderImpl 库存事务日志查询构建器实现
type WmsInventoryTransactionLogQueryBuilderImpl struct {
	db    *gorm.DB
	query *gorm.DB
}

// WhereInventoryID 按库存ID筛选
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WhereInventoryID(inventoryId uint) WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Where("inventory_id = ?", inventoryId)
	return qb
}

// WhereTransactionType 按事务类型筛选
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WhereTransactionType(transactionType entity.TransactionType) WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Where("transaction_type = ?", transactionType)
	return qb
}

// WhereReferenceDoc 按关联单据筛选
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WhereReferenceDoc(refDocType string, refDocID uint) WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Where("ref_doc_type = ? AND ref_doc_id = ?", refDocType, refDocID)
	return qb
}

// WhereOperatorID 按操作员ID筛选
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WhereOperatorID(operatorId uint) WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Where("operator_id = ?", operatorId)
	return qb
}

// WhereDateRange 按日期范围筛选
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WhereDateRange(startDate, endDate time.Time) WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	return qb
}

// WhereQuantityChangeGreaterThan 按数量变化大于指定值筛选
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WhereQuantityChangeGreaterThan(quantity float64) WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Where("quantity_change > ?", quantity)
	return qb
}

// WhereQuantityChangeLessThan 按数量变化小于指定值筛选
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WhereQuantityChangeLessThan(quantity float64) WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Where("quantity_change < ?", quantity)
	return qb
}

// OrderByCreatedAt 按创建时间排序
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) OrderByCreatedAt(desc bool) WmsInventoryTransactionLogQueryBuilder {
	if desc {
		qb.query = qb.query.Order("created_at DESC")
	} else {
		qb.query = qb.query.Order("created_at ASC")
	}
	return qb
}

// OrderByQuantityChange 按数量变化排序
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) OrderByQuantityChange(desc bool) WmsInventoryTransactionLogQueryBuilder {
	if desc {
		qb.query = qb.query.Order("quantity_change DESC")
	} else {
		qb.query = qb.query.Order("quantity_change ASC")
	}
	return qb
}

// WithInventory 包含库存信息
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WithInventory() WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Preload("Inventory")
	return qb
}

// WithOperator 包含操作员信息
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) WithOperator() WmsInventoryTransactionLogQueryBuilder {
	qb.query = qb.query.Preload("Operator")
	return qb
}

// Find 执行查询并返回结果列表
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) Find() ([]entity.WmsInventoryTransactionLog, error) {
	var logs []entity.WmsInventoryTransactionLog
	err := qb.query.Find(&logs).Error
	return logs, err
}

// First 执行查询并返回第一个结果
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) First() (*entity.WmsInventoryTransactionLog, error) {
	var log entity.WmsInventoryTransactionLog
	err := qb.query.First(&log).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// Count 统计查询结果数量
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) Count() (int64, error) {
	var count int64
	err := qb.query.Count(&count).Error
	return count, err
}

// Paginate 分页查询
func (qb *WmsInventoryTransactionLogQueryBuilderImpl) Paginate(pageNum, pageSize int) ([]entity.WmsInventoryTransactionLog, int64, error) {
	var logs []entity.WmsInventoryTransactionLog
	var total int64

	// 先统计总数
	countQuery := qb.db.Model(&entity.WmsInventoryTransactionLog{})
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (pageNum - 1) * pageSize
	err := qb.query.Offset(offset).Limit(pageSize).Find(&logs).Error
	return logs, total, err
}
