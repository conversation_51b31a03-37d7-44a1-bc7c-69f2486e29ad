package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
	"context"

	"github.com/jinzhu/copier"
)

// ScmSupplierService 供应商服务接口
type ScmSupplierService interface {
	BaseService

	// 供应商管理
	Create(ctx context.Context, req *dto.ScmSupplierCreateReq) (*vo.ScmSupplierVO, error)
	Update(ctx context.Context, id uint, req *dto.ScmSupplierUpdateReq) (*vo.ScmSupplierVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.ScmSupplierVO, error)
	GetBySupplierCode(ctx context.Context, supplierCode string) (*vo.ScmSupplierVO, error)
	GetPage(ctx context.Context, req *dto.ScmSupplierQueryReq) (*response.PageResult, error)
	GetSimpleList(ctx context.Context) ([]*vo.ScmSupplierSimpleVO, error)
	GetSummary(ctx context.Context) (*vo.ScmSupplierSummaryVO, error)

	// 供应商联系人管理
	GetContacts(ctx context.Context, supplierID uint) ([]*vo.ScmSupplierContactVO, error)
	CreateContact(ctx context.Context, req *dto.ScmSupplierContactCreateReq) (*vo.ScmSupplierContactVO, error)
	UpdateContact(ctx context.Context, id uint, req *dto.ScmSupplierContactUpdateReq) (*vo.ScmSupplierContactVO, error)
	DeleteContact(ctx context.Context, id uint) error
	SetPrimaryContact(ctx context.Context, supplierID uint, contactID uint) error
	GetPrimaryContact(ctx context.Context, supplierID uint) (*vo.ScmSupplierContactVO, error)

	// 业务验证
	ValidateSupplierCode(ctx context.Context, supplierCode string, excludeID ...uint) error
	ValidateBusinessLicense(ctx context.Context, businessLicense string, excludeID ...uint) error
	ValidateTaxNumber(ctx context.Context, taxNumber string, excludeID ...uint) error
}

// scmSupplierServiceImpl 供应商服务实现
type scmSupplierServiceImpl struct {
	BaseServiceImpl
}

// NewScmSupplierService 创建供应商服务
func NewScmSupplierService(sm *ServiceManager) ScmSupplierService {
	return &scmSupplierServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建供应商
func (s *scmSupplierServiceImpl) Create(ctx context.Context, req *dto.ScmSupplierCreateReq) (*vo.ScmSupplierVO, error) {
	var result *vo.ScmSupplierVO

	// 使用事务包装整个创建过程
	err := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		repo := txServiceManager.GetRepositoryManager().GetScmSupplierRepository()

		// 最大重试次数（编码生成失败时）
		const maxRetries = 3
		var lastErr error

		for attempt := 0; attempt < maxRetries; attempt++ {
			// 处理供应商编码生成
			supplierCode := req.SupplierCode
			if supplierCode == "" || supplierCode == "AUTO" {
				// 自动生成供应商编码
				codeGenService := txServiceManager.GetCodeGenerationService()
				if codeGenService != nil {
					contextData := map[string]interface{}{
						"supplierName": req.SupplierName,
						"supplierType": req.SupplierType,
					}
					if req.Industry != nil {
						contextData["industry"] = *req.Industry
					}

					generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
						BusinessType: "SUPPLIER",
						ContextData:  contextData,
					})
					if err != nil {
						return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "供应商编码自动生成失败。可能原因：1) 缺少供应商编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
					}
					supplierCode = generatedCode.GeneratedCode
				} else {
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "供应商编码生成服务暂时不可用，请手动输入编码或稍后重试自动生成。")
				}
			}

			// 验证营业执照号唯一性（如果提供）
			if req.BusinessLicense != nil && *req.BusinessLicense != "" {
				if err := s.ValidateBusinessLicense(ctx, *req.BusinessLicense); err != nil {
					return err
				}
			}

			// 验证税务登记号唯一性（如果提供）
			if req.TaxNumber != nil && *req.TaxNumber != "" {
				if err := s.ValidateTaxNumber(ctx, *req.TaxNumber); err != nil {
					return err
				}
			}

			// 从Context获取账套ID
			accountBookID, err := s.GetAccountBookIDFromContext(ctx)
			if err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID").WithCause(err)
			}
			if accountBookID == 0 {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID")
			}

			// 从Context获取用户ID
			userID, err := s.GetUserIDFromContext(ctx)
			if err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
			}
			if userID == 0 {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
			}

			// 创建供应商实体
			supplier := &entity.ScmSupplier{
				AccountBookEntity: entity.AccountBookEntity{
					AccountBookID: uint(accountBookID),
					TenantEntity: entity.TenantEntity{
						BaseEntity: entity.BaseEntity{
							CreatedBy: uint(userID),
						},
					},
				},
				SupplierCode: supplierCode,
				SupplierName: req.SupplierName,
				Status:       "ACTIVE", // 默认状态
			}

			// 复制其他字段
			if err := copier.Copy(supplier, req); err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
			}

			// 重要：重新设置生成的供应商编码，防止被copier.Copy覆盖
			supplier.SupplierCode = supplierCode

			// 处理默认值
			if req.SupplierType != "" {
				supplier.SupplierType = req.SupplierType
			} else {
				supplier.SupplierType = "CORPORATE" // 默认企业供应商
			}
			if req.SupplierLevel != "" {
				supplier.SupplierLevel = req.SupplierLevel
			} else {
				supplier.SupplierLevel = "NORMAL" // 默认普通供应商
			}
			if req.Status != "" {
				supplier.Status = req.Status
			}
			if req.IsKeySupplier != nil {
				supplier.IsKeySupplier = *req.IsKeySupplier
			}
			if req.CurrencyCode != "" {
				supplier.CurrencyCode = req.CurrencyCode
			} else {
				supplier.CurrencyCode = "CNY" // 默认人民币
			}

			// 尝试保存供应商
			err = repo.Create(ctx, supplier)
			if err != nil {
				// 检查是否为重复键错误
				if apperrors.IsDuplicateKeyError(err) {
					s.GetServiceManager().GetLogger().Warn("供应商编码重复，正在重试",
						s.GetServiceManager().GetLogger().WithField("supplierCode", supplierCode),
						s.GetServiceManager().GetLogger().WithField("attempt", attempt+1))

					lastErr = apperrors.NewDuplicateKeyError("供应商编码已存在，正在重新生成")

					// 如果是自动生成的编码且发生重复，继续重试
					if req.SupplierCode == "" || req.SupplierCode == "AUTO" {
						continue
					} else {
						// 手动指定的编码重复，直接返回错误
						return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商编码已存在")
					}
				} else {
					// 其他数据库错误
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建供应商失败").WithCause(err)
				}
			} else {
				// 创建成功，处理联系人
				for _, contactDTO := range req.Contacts {
					contact := &entity.ScmSupplierContact{
						AccountBookEntity: entity.AccountBookEntity{
							AccountBookID: uint(accountBookID),
							TenantEntity: entity.TenantEntity{
								BaseEntity: entity.BaseEntity{
									CreatedBy: uint(userID),
								},
							},
						},
						SupplierID:  supplier.ID,
						ContactName: contactDTO.ContactName,
					}
					if err := copier.Copy(contact, &contactDTO); err == nil {
						// 重要：重新设置账套ID，防止被copier.Copy覆盖
						contact.AccountBookID = uint(accountBookID)
						repo.CreateContact(ctx, contact)
					}
				}

				// 设置返回结果
				result = s.entityToVO(supplier)
				return nil
			}
		}

		// 所有重试都失败了
		if lastErr != nil {
			return lastErr
		}

		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建供应商失败，已达到最大重试次数")
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// Update 更新供应商
func (s *scmSupplierServiceImpl) Update(ctx context.Context, id uint, req *dto.ScmSupplierUpdateReq) (*vo.ScmSupplierVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	// 获取现有实体
	supplier, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商不存在")
	}

	// 验证供应商编码唯一性（如果更新了编码）
	if req.SupplierCode != nil && *req.SupplierCode != supplier.SupplierCode {
		if err := s.ValidateSupplierCode(ctx, *req.SupplierCode, id); err != nil {
			return nil, err
		}
		supplier.SupplierCode = *req.SupplierCode
	}

	// 验证营业执照号唯一性（如果更新了营业执照号）
	if req.BusinessLicense != nil && (supplier.BusinessLicense == nil || *req.BusinessLicense != *supplier.BusinessLicense) {
		if *req.BusinessLicense != "" {
			if err := s.ValidateBusinessLicense(ctx, *req.BusinessLicense, id); err != nil {
				return nil, err
			}
		}
		supplier.BusinessLicense = req.BusinessLicense
	}

	// 验证税务登记号唯一性（如果更新了税务登记号）
	if req.TaxNumber != nil && (supplier.TaxNumber == nil || *req.TaxNumber != *supplier.TaxNumber) {
		if *req.TaxNumber != "" {
			if err := s.ValidateTaxNumber(ctx, *req.TaxNumber, id); err != nil {
				return nil, err
			}
		}
		supplier.TaxNumber = req.TaxNumber
	}

	// 从Context获取用户ID

	// 从Context获取用户ID
	userID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
	}
	if userID == 0 {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
	}

	// 更新其他字段
	if req.SupplierName != nil {
		supplier.SupplierName = *req.SupplierName
	}
	if req.SupplierType != nil {
		supplier.SupplierType = *req.SupplierType
	}
	if req.SupplierLevel != nil {
		supplier.SupplierLevel = *req.SupplierLevel
	}
	if req.SupplierCategory != nil {
		supplier.SupplierCategory = req.SupplierCategory
	}
	if req.Status != nil {
		supplier.Status = *req.Status
	}
	if req.IsKeySupplier != nil {
		supplier.IsKeySupplier = *req.IsKeySupplier
	}

	// 更新其他可选字段
	supplier.Industry = req.Industry
	supplier.LegalRepresentative = req.LegalRepresentative
	supplier.RegisteredCapital = req.RegisteredCapital
	supplier.ContactPerson = req.ContactPerson
	supplier.ContactPhone = req.ContactPhone
	supplier.ContactEmail = req.ContactEmail
	supplier.Website = req.Website
	supplier.Country = req.Country
	supplier.Province = req.Province
	supplier.City = req.City
	supplier.District = req.District
	supplier.Address = req.Address
	supplier.PostalCode = req.PostalCode
	supplier.QualityRating = req.QualityRating
	supplier.ServiceRating = req.ServiceRating
	supplier.DeliveryRating = req.DeliveryRating
	supplier.PaymentTerms = req.PaymentTerms
	if req.CurrencyCode != nil {
		supplier.CurrencyCode = *req.CurrencyCode
	}
	supplier.Remark = req.Remark
	supplier.UpdatedBy = uint(userID)
	supplier.AnnualSupplyCapacity = req.AnnualSupplyCapacity
	supplier.MainProducts = req.MainProducts
	supplier.CertificationInfo = req.CertificationInfo
	supplier.QualificationCertificate = req.QualificationCertificate
	supplier.IsStrategicSupplier = *req.IsStrategicSupplier
	supplier.CreditRating = req.CreditRating
	supplier.CreditLimit = req.CreditLimit
	supplier.ServiceRating = req.ServiceRating
	supplier.SupplierSource = req.SupplierSource
	supplier.ProcurementRepresentativeID = req.ProcurementRepresentativeId

	// 保存更新
	if err := repo.Update(ctx, supplier); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新供应商失败").WithCause(err)
	}

	return s.entityToVO(supplier), nil
}

// Delete 删除供应商
func (s *scmSupplierServiceImpl) Delete(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	if err := repo.Delete(ctx, id); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除供应商失败").WithCause(err)
	}

	return nil
}

// GetByID 获取供应商详情
func (s *scmSupplierServiceImpl) GetByID(ctx context.Context, id uint) (*vo.ScmSupplierVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	supplier, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商不存在")
	}

	// 加载联系人
	contacts, _ := repo.FindContactsBySupplierID(ctx, id)

	supplierVO := s.entityToVO(supplier)
	contactVOs := s.contactsToVO(contacts)
	supplierVO.Contacts = make([]vo.ScmSupplierContactVO, len(contactVOs))
	for i, contactVO := range contactVOs {
		supplierVO.Contacts[i] = *contactVO
	}

	return supplierVO, nil
}

// GetBySupplierCode 根据供应商编码获取供应商
func (s *scmSupplierServiceImpl) GetBySupplierCode(ctx context.Context, supplierCode string) (*vo.ScmSupplierVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	supplier, err := repo.FindBySupplierCode(ctx, supplierCode)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商不存在")
	}

	return s.entityToVO(supplier), nil
}

// GetPage 分页获取供应商列表
func (s *scmSupplierServiceImpl) GetPage(ctx context.Context, req *dto.ScmSupplierQueryReq) (*response.PageResult, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()
	return repo.GetPage(ctx, req)
}

// GetSimpleList 获取活跃供应商简单列表
func (s *scmSupplierServiceImpl) GetSimpleList(ctx context.Context) ([]*vo.ScmSupplierSimpleVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	suppliers, err := repo.FindActiveSimpleList(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询供应商简单列表失败").WithCause(err)
	}

	result := make([]*vo.ScmSupplierSimpleVO, len(suppliers))
	for i, supplier := range suppliers {
		result[i] = &vo.ScmSupplierSimpleVO{
			ID:           supplier.ID,
			SupplierCode: supplier.SupplierCode,
			SupplierName: supplier.SupplierName,
			SupplierType: supplier.SupplierType,
			Status:       supplier.Status,
		}
	}

	return result, nil
}

// GetSummary 获取供应商统计摘要
func (s *scmSupplierServiceImpl) GetSummary(ctx context.Context) (*vo.ScmSupplierSummaryVO, error) {
	// 这里需要实现具体的统计逻辑
	// 暂时返回空的摘要对象
	return &vo.ScmSupplierSummaryVO{}, nil
}

// GetContacts 获取供应商联系人列表
func (s *scmSupplierServiceImpl) GetContacts(ctx context.Context, supplierID uint) ([]*vo.ScmSupplierContactVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	contacts, err := repo.FindContactsBySupplierID(ctx, supplierID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取联系人列表失败").WithCause(err)
	}

	return s.contactsToVO(contacts), nil
}

// CreateContact 创建供应商联系人
func (s *scmSupplierServiceImpl) CreateContact(ctx context.Context, req *dto.ScmSupplierContactCreateReq) (*vo.ScmSupplierContactVO, error) {
	var result *vo.ScmSupplierContactVO

	// 使用事务包装创建过程
	err := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		repo := txServiceManager.GetRepositoryManager().GetScmSupplierRepository()

		// 验证供应商是否存在
		_, err := repo.FindByID(ctx, req.SupplierID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商不存在")
		}

		// 从Context获取账套ID
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID")
		}

		// 从Context获取用户ID
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
		}

		// 创建联系人实体
		contact := &entity.ScmSupplierContact{
			AccountBookEntity: entity.AccountBookEntity{
				AccountBookID: uint(accountBookID),
				TenantEntity: entity.TenantEntity{
					BaseEntity: entity.BaseEntity{
						CreatedBy: uint(userID),
					},
				},
			},
			SupplierID:  req.SupplierID,
			ContactName: req.ContactName,
		}

		if err := copier.Copy(contact, req); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}

		// 重要：重新设置账套ID，防止被copier.Copy覆盖
		contact.AccountBookID = uint(accountBookID)

		// 处理默认值
		if req.ContactRole != nil {
			contact.ContactRole = req.ContactRole
		}

		// 保存联系人
		if err := repo.CreateContact(ctx, contact); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建联系人失败").WithCause(err)
		}

		result = s.contactToVO(contact)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// UpdateContact 更新供应商联系人
func (s *scmSupplierServiceImpl) UpdateContact(ctx context.Context, id uint, req *dto.ScmSupplierContactUpdateReq) (*vo.ScmSupplierContactVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	// 获取现有联系人
	contact, err := repo.FindContactByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "联系人不存在")
	}

	// 从Context获取用户ID
	userID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
	}
	if userID == 0 {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
	}

	// 更新字段
	if req.ContactName != nil {
		contact.ContactName = *req.ContactName
	}
	if req.ContactTitle != nil {
		contact.ContactTitle = req.ContactTitle
	}
	if req.Department != nil {
		contact.Department = req.Department
	}
	if req.Phone != nil {
		contact.Phone = req.Phone
	}
	if req.Mobile != nil {
		contact.Mobile = req.Mobile
	}
	if req.Email != nil {
		contact.Email = req.Email
	}
	if req.QQ != nil {
		contact.QQ = req.QQ
	}
	if req.Wechat != nil {
		contact.Wechat = req.Wechat
	}
	if req.Address != nil {
		contact.Address = req.Address
	}
	if req.PostalCode != nil {
		contact.PostalCode = req.PostalCode
	}
	if req.Birthday != nil {
		contact.Birthday = req.Birthday
	}
	if req.Gender != nil {
		contact.Gender = req.Gender
	}
	if req.ContactRole != nil {
		contact.ContactRole = req.ContactRole
	}
	if req.Remark != nil {
		contact.Remark = req.Remark
	}
	contact.UpdatedBy = uint(userID)
	// 保存更新
	if err := repo.UpdateContact(ctx, contact); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新联系人失败").WithCause(err)
	}

	return s.contactToVO(contact), nil
}

// DeleteContact 删除供应商联系人
func (s *scmSupplierServiceImpl) DeleteContact(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	if err := repo.DeleteContact(ctx, id); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除联系人失败").WithCause(err)
	}

	return nil
}

// SetPrimaryContact 设置主要联系人
func (s *scmSupplierServiceImpl) SetPrimaryContact(ctx context.Context, supplierID uint, contactID uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	if err := repo.SetPrimaryContact(ctx, supplierID, contactID); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "设置主要联系人失败").WithCause(err)
	}

	return nil
}

// GetPrimaryContact 获取主要联系人
func (s *scmSupplierServiceImpl) GetPrimaryContact(ctx context.Context, supplierID uint) (*vo.ScmSupplierContactVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	contact, err := repo.FindPrimaryContactBySupplierID(ctx, supplierID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "主要联系人不存在")
	}

	return s.contactToVO(contact), nil
}

// ValidateSupplierCode 验证供应商编码唯一性
func (s *scmSupplierServiceImpl) ValidateSupplierCode(ctx context.Context, supplierCode string, excludeID ...uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	existing, err := repo.FindBySupplierCode(ctx, supplierCode)
	if err != nil {
		return nil // 查询出错，编码可用
	}

	// 如果没有找到记录，编码可用
	if existing == nil {
		return nil
	}

	// 如果提供了排除ID，检查是否为同一记录
	if len(excludeID) > 0 && existing.ID == excludeID[0] {
		return nil
	}

	return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商编码已存在")
}

// ValidateBusinessLicense 验证营业执照号唯一性
func (s *scmSupplierServiceImpl) ValidateBusinessLicense(ctx context.Context, businessLicense string, excludeID ...uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	existing, err := repo.FindByBusinessLicense(ctx, businessLicense)
	if err != nil {
		return nil // 查询出错，可用
	}

	// 如果没有找到记录，可用
	if existing == nil {
		return nil
	}

	// 如果提供了排除ID，检查是否为同一记录
	if len(excludeID) > 0 && existing.ID == excludeID[0] {
		return nil
	}

	return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "营业执照号已存在")
}

// ValidateTaxNumber 验证税务登记号唯一性
func (s *scmSupplierServiceImpl) ValidateTaxNumber(ctx context.Context, taxNumber string, excludeID ...uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetScmSupplierRepository()

	existing, err := repo.FindByTaxNumber(ctx, taxNumber)
	if err != nil {
		return nil // 查询出错，可用
	}

	// 如果没有找到记录，可用
	if existing == nil {
		return nil
	}

	// 如果提供了排除ID，检查是否为同一记录
	if len(excludeID) > 0 && existing.ID == excludeID[0] {
		return nil
	}

	return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "税务登记号已存在")
}

// entityToVO 将供应商实体转换为VO
func (s *scmSupplierServiceImpl) entityToVO(supplier *entity.ScmSupplier) *vo.ScmSupplierVO {
	if supplier == nil {
		return nil
	}

	supplierVO := &vo.ScmSupplierVO{}
	copier.Copy(supplierVO, supplier)

	return supplierVO
}

// contactToVO 将联系人实体转换为VO
func (s *scmSupplierServiceImpl) contactToVO(contact *entity.ScmSupplierContact) *vo.ScmSupplierContactVO {
	if contact == nil {
		return nil
	}

	contactVO := &vo.ScmSupplierContactVO{}
	copier.Copy(contactVO, contact)

	return contactVO
}

// contactsToVO 将联系人实体列表转换为VO列表
func (s *scmSupplierServiceImpl) contactsToVO(contacts []*entity.ScmSupplierContact) []*vo.ScmSupplierContactVO {
	if len(contacts) == 0 {
		return []*vo.ScmSupplierContactVO{}
	}

	result := make([]*vo.ScmSupplierContactVO, len(contacts))
	for i, contact := range contacts {
		result[i] = s.contactToVO(contact)
	}

	return result
}
