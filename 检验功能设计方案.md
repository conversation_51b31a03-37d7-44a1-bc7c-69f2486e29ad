# 入库通知单检验功能设计方案

## 📋 项目背景

在仓储管理系统中，检验是质量控制的重要环节。本文档分析了当前系统的检验功能现状，并提出了合适的设计方案。

## 🔍 现状分析

### 后端检验功能（已实现）

#### 1. 检验状态定义

```go
type WmsReceivingInspectionStatus string

const (
    ReceivingInspectionStatusNotRequired WmsReceivingInspectionStatus = "NOT_REQUIRED" // 无需检验
    ReceivingInspectionStatusPending     WmsReceivingInspectionStatus = "PENDING"      // 待检验
    ReceivingInspectionStatusInspecting  WmsReceivingInspectionStatus = "INSPECTING"   // 检验中
    ReceivingInspectionStatusPass        WmsReceivingInspectionStatus = "PASS"         // 合格
    ReceivingInspectionStatusFail        WmsReceivingInspectionStatus = "FAIL"         // 不合格
)
```

#### 2. 数据模型

在 `WmsReceivingRecordDetail` 实体中包含：

- `InspectionStatus` - 检验状态
- `InspectorID` - 检验员 ID
- `InspectionNotes` - 检验备注

#### 3. 业务接口

- `UpdateInspectionResult` - 更新检验结果
- `GetPendingInspection` - 获取待检验记录

### 前端检验功能（待完善）

目前前端缺少：

- 收货记录管理页面
- 检验状态显示
- 检验结果维护界面

## 🎯 设计方案

### 业务需求分析

根据业务需求：

- 检验是**可选环节**，不是强制流程
- 检验结果由人工填写（合格/不合格）
- 允许不进行检验（状态为空）

### 推荐方案：检验作为收货阶段的属性

#### 方案选择理由

1. **可选性质**：检验是可选的，不应强制所有入库通知单都经过检验状态
2. **流程简洁**：保持主流程的简洁性，避免不必要的状态跳转
3. **灵活性**：检验结果可以在收货完成后的任何时间填写
4. **业务逻辑**：检验更像是收货过程中的**属性**而不是**状态**

#### 状态流程设计

```mermaid
graph LR
    A[草稿] --> B[已计划]
    B --> C[已到货]
    C --> D[收货中]
    D --> E[收货完成]
    E --> F[已关闭]

    D -.-> G[检验信息<br/>可选填写]
    E -.-> G

    B --> H[已取消]
    C --> H
    D --> H
    E --> H
```

**主流程状态**：

```
草稿 → 已计划 → 已到货 → 收货中 → 收货完成 → 已关闭
                           ↓
                    (收货过程中可选择是否检验)
```

## 🛠️ 实现方案

### 1. 状态流程配置

**保持现有状态流程不变**，不增加检验相关的状态节点：

```typescript
// 入库通知单状态流程（保持不变）
statusOptions: [
  { value: "DRAFT", label: "草稿" },
  { value: "PLANNED", label: "已计划" },
  { value: "ARRIVED", label: "已到货" },
  { value: "RECEIVING", label: "收货中" },
  { value: "PARTIALLY_RECEIVED", label: "部分收货" },
  { value: "RECEIVED", label: "收货完成" },
  { value: "CLOSED", label: "已关闭" },
  { value: "CANCELLED", label: "已取消" },
];
```

### 2. 检验信息管理

#### 2.1 检验状态显示函数

```typescript
// 获取检验状态显示名称
const getInspectionStatusDisplayName = (inspectionStatus: string): string => {
  if (!inspectionStatus) return "-";

  const statusMap: Record<string, string> = {
    NOT_REQUIRED: "无需检验",
    PENDING: "待检验",
    INSPECTING: "检验中",
    PASS: "合格",
    FAIL: "不合格",
  };

  return statusMap[inspectionStatus] || inspectionStatus;
};
```

#### 2.2 检验信息在收货记录中维护

- 检验状态作为收货记录明细的属性
- 提供独立的检验结果录入界面
- 检验操作不影响主流程状态转换

### 3. 用户界面设计

#### 3.1 入库通知单详情页

- 在明细表格中显示检验状态（只读）
- 提供"查看检验详情"链接

#### 3.2 收货记录管理页面（待开发）

- 收货记录列表
- 检验结果录入功能
- 检验状态筛选

#### 3.3 检验工作台（可选）

- 待检验物料列表
- 批量检验操作
- 检验历史记录

## 📊 数据流程

### 检验数据流转

```mermaid
sequenceDiagram
    participant User as 用户
    participant IN as 入库通知单
    participant RR as 收货记录
    participant Inspect as 检验模块

    User->>IN: 创建入库通知单
    IN->>RR: 生成收货记录
    User->>RR: 执行收货操作

    alt 需要检验
        User->>Inspect: 录入检验结果
        Inspect->>RR: 更新检验状态
    else 无需检验
        Note over RR: 检验状态保持空或NOT_REQUIRED
    end

    User->>IN: 查看状态（包含检验信息）
```

## 🎨 界面设计要点

### 1. 检验状态标识

- 使用颜色和图标区分不同检验状态
- 合格：绿色 ✅
- 不合格：红色 ❌
- 待检验：橙色 ⏳
- 无需检验：灰色 ➖

### 2. 操作权限

- 检验员：可以录入和修改检验结果
- 仓库管理员：可以查看所有检验信息
- 普通用户：只能查看检验状态

### 3. 数据验证

- 检验结果必须选择合格或不合格
- 不合格时必须填写检验备注
- 检验员信息自动记录

## 🚀 实施计划

### 阶段一：基础功能（1-2 周）

1. 完善入库通知单中检验状态显示
2. 创建收货记录管理页面基础框架
3. 实现检验结果录入功能

### 阶段二：增强功能（2-3 周）

1. 开发检验工作台
2. 添加检验报表和统计
3. 实现批量检验操作

### 阶段三：优化完善（1 周）

1. 用户体验优化
2. 性能优化
3. 测试和文档完善

## 📈 预期效果

### 业务价值

- ✅ 提供完整的质量控制流程
- ✅ 保持业务流程的灵活性
- ✅ 支持可选检验的业务需求
- ✅ 提升仓储管理的规范性

### 技术优势

- ✅ 不破坏现有状态流程设计
- ✅ 向后兼容，平滑升级
- ✅ 模块化设计，易于维护
- ✅ 扩展性强，支持未来需求变化

## 📝 总结

本方案将检验功能设计为收货阶段的可选属性，而不是独立的状态节点。这种设计既满足了业务的灵活性需求，又保持了系统架构的简洁性。通过独立的检验管理模块，用户可以根据实际需要选择是否进行检验，并灵活录入检验结果。

---

**文档版本**：v1.0  
**创建日期**：2025-06-14  
**最后更新**：2025-06-14
