package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"

	"github.com/kataras/iris/v12"
)

// ScmSupplierController SCM供应商控制器接口
type ScmSupplierController interface {
	// 供应商管理
	Post(ctx iris.Context)       // 创建供应商
	PutBy(ctx iris.Context)      // 更新供应商
	DeleteBy(ctx iris.Context)   // 删除供应商
	GetBy(ctx iris.Context)      // 获取供应商详情
	Get(ctx iris.Context)        // 分页查询供应商
	GetSimple(ctx iris.Context)  // 获取供应商简单列表
	GetSummary(ctx iris.Context) // 获取供应商统计摘要

	// 供应商联系人管理
	GetContacts(ctx iris.Context)       // 获取供应商联系人列表
	PostContact(ctx iris.Context)       // 创建供应商联系人
	PutContact(ctx iris.Context)        // 更新供应商联系人
	DeleteContact(ctx iris.Context)     // 删除供应商联系人
	SetPrimaryContact(ctx iris.Context) // 设置主要联系人
	GetPrimaryContact(ctx iris.Context) // 获取主要联系人

	// 业务功能
	ValidateSupplierCode(ctx iris.Context)    // 验证供应商编码
	ValidateBusinessLicense(ctx iris.Context) // 验证营业执照号
	ValidateTaxNumber(ctx iris.Context)       // 验证税务登记号
	GetBySupplierCode(ctx iris.Context)       // 根据供应商编码获取供应商
}

// scmSupplierControllerImpl SCM供应商控制器实现
type scmSupplierControllerImpl struct {
	BaseControllerImpl
	service service.ScmSupplierService
}

// NewScmSupplierController 创建SCM供应商控制器
func NewScmSupplierController(cm *ControllerManager) ScmSupplierController {
	svc := cm.GetServiceManager().GetScmSupplierService()
	return &scmSupplierControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		service:            svc,
	}
}

// Post 创建供应商
func (c *scmSupplierControllerImpl) Post(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	var req dto.ScmSupplierCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "CreateScmSupplier")
		return
	}

	result, err := c.service.Create(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "CreateScmSupplier")
		return
	}

	c.SuccessWithMessage(ctx, "供应商创建成功", result)
}

// PutBy 更新供应商
func (c *scmSupplierControllerImpl) PutBy(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的供应商ID: "+err.Error()), "UpdateScmSupplier")
		return
	}

	var req dto.ScmSupplierUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "UpdateScmSupplier")
		return
	}

	result, err := c.service.Update(ctx.Request().Context(), id, &req)
	if err != nil {
		c.HandleError(ctx, err, "UpdateScmSupplier")
		return
	}

	c.SuccessWithMessage(ctx, "供应商更新成功", result)
}

// DeleteBy 删除供应商
func (c *scmSupplierControllerImpl) DeleteBy(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的供应商ID: "+err.Error()), "DeleteScmSupplier")
		return
	}

	if err := c.service.Delete(ctx.Request().Context(), id); err != nil {
		c.HandleError(ctx, err, "DeleteScmSupplier")
		return
	}

	c.SuccessWithMessage(ctx, "供应商删除成功", nil)
}

// GetBy 获取供应商详情
func (c *scmSupplierControllerImpl) GetBy(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的供应商ID: "+err.Error()), "GetScmSupplier")
		return
	}

	result, err := c.service.GetByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, "GetScmSupplier")
		return
	}

	c.Success(ctx, result)
}

// Get 分页查询供应商
func (c *scmSupplierControllerImpl) Get(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	var req dto.ScmSupplierQueryReq

	// 使用现有分页机制构建查询参数
	pageQuery := response.BuildPageQuery(ctx)
	req.PageQuery = *pageQuery

	// 读取其他查询参数
	if err := ctx.ReadQuery(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数: "+err.Error()), "GetScmSupplierPage")
		return
	}

	pageResult, err := c.service.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "GetScmSupplierPage")
		return
	}

	c.Success(ctx, pageResult)
}

// GetSimple 获取供应商简单列表
func (c *scmSupplierControllerImpl) GetSimple(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	result, err := c.service.GetSimpleList(ctx.Request().Context())
	if err != nil {
		c.HandleError(ctx, err, "GetScmSupplierSimple")
		return
	}

	c.Success(ctx, result)
}

// GetSummary 获取供应商统计摘要
func (c *scmSupplierControllerImpl) GetSummary(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	result, err := c.service.GetSummary(ctx.Request().Context())
	if err != nil {
		c.HandleError(ctx, err, "GetScmSupplierSummary")
		return
	}

	c.Success(ctx, result)
}

// GetContacts 获取供应商联系人列表
func (c *scmSupplierControllerImpl) GetContacts(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier_contact")

	supplierID, err := ctx.Params().GetUint("supplierId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的供应商ID: "+err.Error()), "GetScmSupplierContacts")
		return
	}

	result, err := c.service.GetContacts(ctx.Request().Context(), supplierID)
	if err != nil {
		c.HandleError(ctx, err, "GetScmSupplierContacts")
		return
	}

	c.Success(ctx, result)
}

// PostContact 创建供应商联系人
func (c *scmSupplierControllerImpl) PostContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier_contact")

	var req dto.ScmSupplierContactCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "CreateScmSupplierContact")
		return
	}

	result, err := c.service.CreateContact(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "CreateScmSupplierContact")
		return
	}

	c.SuccessWithMessage(ctx, "联系人创建成功", result)
}

// PutContact 更新供应商联系人
func (c *scmSupplierControllerImpl) PutContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier_contact")

	id, err := ctx.Params().GetUint("contactId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的联系人ID: "+err.Error()), "UpdateScmSupplierContact")
		return
	}

	var req dto.ScmSupplierContactUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "UpdateScmSupplierContact")
		return
	}

	result, err := c.service.UpdateContact(ctx.Request().Context(), id, &req)
	if err != nil {
		c.HandleError(ctx, err, "UpdateScmSupplierContact")
		return
	}

	c.SuccessWithMessage(ctx, "联系人更新成功", result)
}

// DeleteContact 删除供应商联系人
func (c *scmSupplierControllerImpl) DeleteContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier_contact")

	id, err := ctx.Params().GetUint("contactId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的联系人ID: "+err.Error()), "DeleteScmSupplierContact")
		return
	}

	if err := c.service.DeleteContact(ctx.Request().Context(), id); err != nil {
		c.HandleError(ctx, err, "DeleteScmSupplierContact")
		return
	}

	c.SuccessWithMessage(ctx, "联系人删除成功", nil)
}

// SetPrimaryContact 设置主要联系人
func (c *scmSupplierControllerImpl) SetPrimaryContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier_contact")

	supplierID, err := ctx.Params().GetUint("supplierId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的供应商ID: "+err.Error()), "SetPrimaryScmSupplierContact")
		return
	}

	contactID, err := ctx.Params().GetUint("contactId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的联系人ID: "+err.Error()), "SetPrimaryScmSupplierContact")
		return
	}

	if err := c.service.SetPrimaryContact(ctx.Request().Context(), supplierID, contactID); err != nil {
		c.HandleError(ctx, err, "SetPrimaryScmSupplierContact")
		return
	}

	c.SuccessWithMessage(ctx, "主要联系人设置成功", nil)
}

// GetPrimaryContact 获取主要联系人
func (c *scmSupplierControllerImpl) GetPrimaryContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier_contact")

	supplierID, err := ctx.Params().GetUint("supplierId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的供应商ID: "+err.Error()), "GetPrimaryScmSupplierContact")
		return
	}

	result, err := c.service.GetPrimaryContact(ctx.Request().Context(), supplierID)
	if err != nil {
		c.HandleError(ctx, err, "GetPrimaryScmSupplierContact")
		return
	}

	c.Success(ctx, result)
}

// ValidateSupplierCode 验证供应商编码
func (c *scmSupplierControllerImpl) ValidateSupplierCode(ctx iris.Context) {
	supplierCode := ctx.URLParam("supplierCode")
	if supplierCode == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商编码不能为空"), "ValidateSupplierCode")
		return
	}

	excludeIDStr := ctx.URLParam("excludeId")
	var excludeID uint
	if excludeIDStr != "" {
		id := ctx.URLParamUint64("excludeId")
		excludeID = uint(id)
	}

	var err error
	if excludeID > 0 {
		err = c.service.ValidateSupplierCode(ctx.Request().Context(), supplierCode, excludeID)
	} else {
		err = c.service.ValidateSupplierCode(ctx.Request().Context(), supplierCode)
	}

	if err != nil {
		c.HandleError(ctx, err, "ValidateSupplierCode")
		return
	}

	c.SuccessWithMessage(ctx, "供应商编码可用", map[string]interface{}{"valid": true})
}

// ValidateBusinessLicense 验证营业执照号
func (c *scmSupplierControllerImpl) ValidateBusinessLicense(ctx iris.Context) {
	businessLicense := ctx.URLParam("businessLicense")
	if businessLicense == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "营业执照号不能为空"), "ValidateBusinessLicense")
		return
	}

	excludeIDStr := ctx.URLParam("excludeId")
	var excludeID uint
	if excludeIDStr != "" {
		id := ctx.URLParamUint64("excludeId")
		excludeID = uint(id)
	}

	var err error
	if excludeID > 0 {
		err = c.service.ValidateBusinessLicense(ctx.Request().Context(), businessLicense, excludeID)
	} else {
		err = c.service.ValidateBusinessLicense(ctx.Request().Context(), businessLicense)
	}

	if err != nil {
		c.HandleError(ctx, err, "ValidateBusinessLicense")
		return
	}

	c.SuccessWithMessage(ctx, "营业执照号可用", map[string]interface{}{"valid": true})
}

// ValidateTaxNumber 验证税务登记号
func (c *scmSupplierControllerImpl) ValidateTaxNumber(ctx iris.Context) {
	taxNumber := ctx.URLParam("taxNumber")
	if taxNumber == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "税务登记号不能为空"), "ValidateTaxNumber")
		return
	}

	excludeIDStr := ctx.URLParam("excludeId")
	var excludeID uint
	if excludeIDStr != "" {
		id := ctx.URLParamUint64("excludeId")
		excludeID = uint(id)
	}

	var err error
	if excludeID > 0 {
		err = c.service.ValidateTaxNumber(ctx.Request().Context(), taxNumber, excludeID)
	} else {
		err = c.service.ValidateTaxNumber(ctx.Request().Context(), taxNumber)
	}

	if err != nil {
		c.HandleError(ctx, err, "ValidateTaxNumber")
		return
	}

	c.SuccessWithMessage(ctx, "税务登记号可用", map[string]interface{}{"valid": true})
}

// GetBySupplierCode 根据供应商编码获取供应商
func (c *scmSupplierControllerImpl) GetBySupplierCode(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "scm_supplier")

	supplierCode := ctx.Params().Get("supplierCode")
	if supplierCode == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "供应商编码不能为空"), "GetScmSupplierByCode")
		return
	}

	result, err := c.service.GetBySupplierCode(ctx.Request().Context(), supplierCode)
	if err != nil {
		c.HandleError(ctx, err, "GetScmSupplierByCode")
		return
	}

	c.Success(ctx, result)
}
