import request from '@/utils/request';

// --- TypeScript 类型定义 (基于 Go VO 和 DTO) ---

// 基础分页查询参数
interface BaseQueryDTO {
  pageNum?: number;
  pageSize?: number;
  sort?: string;
}

// 基础视图对象
interface BaseVO {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: number;
  updatedBy?: number;
}

// --- SCM 供应商相关类型 ---

export interface ScmSupplierCreateDTO {
  supplierCode?: string; // 支持自动生成
  supplierName: string;
  supplierType?: string; // CORPORATE, INDIVIDUAL
  industry?: string;
  businessLicense?: string;
  taxNumber?: string;
  legalRepresentative?: string;
  registeredCapital?: number;
  
  // 联系信息
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  
  // 地址信息
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  postalCode?: string;
  
  // 财务信息
  creditRating?: string;
  creditLimit?: number;
  paymentTerms?: string;
  currencyCode?: string;
  
  // 业务信息 - 供应商特有
  supplierLevel?: string; // A, B, C, D
  supplierCategory?: string; // 供应商分类
  supplierSource?: string;
  purchaseRepresentativeId?: number;
  
  // 供应商专业字段
  qualityRating?: string;
  deliveryRating?: string;
  serviceRating?: string;
  annualSupplyCapacity?: number;
  mainProducts?: string;
  certificationInfo?: string;
  qualificationCertificate?: string;
  
  // 状态信息
  status?: string; // ACTIVE, INACTIVE, BLACKLIST
  isKeySupplier?: boolean;
  isStrategicSupplier?: boolean;
  remark?: string;
  
  // 嵌套创建联系人
  contacts?: ScmSupplierContactCreateDTO[];
}

export interface ScmSupplierUpdateDTO {
  supplierCode?: string; // 支持自动生成
  supplierName: string;
  supplierType?: string; // CORPORATE, INDIVIDUAL
  industry?: string;
  businessLicense?: string;
  taxNumber?: string;
  legalRepresentative?: string;
  registeredCapital?: number;
  
  // 联系信息
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  
  // 地址信息
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  postalCode?: string;
  
  // 财务信息
  creditRating?: string;
  creditLimit?: number;
  paymentTerms?: string;
  currencyCode?: string;
  
  // 业务信息 - 供应商特有
  supplierLevel?: string; // A, B, C, D
  supplierCategory?: string; // 供应商分类
  supplierSource?: string;
  purchaseRepresentativeId?: number;
  
  // 供应商专业字段
  qualityRating?: string;
  deliveryRating?: string;
  serviceRating?: string;
  annualSupplyCapacity?: number;
  mainProducts?: string;
  certificationInfo?: string;
  qualificationCertificate?: string;
  
  // 状态信息
  status?: string; // ACTIVE, INACTIVE, BLACKLIST
  isKeySupplier?: boolean;
  isStrategicSupplier?: boolean;
  remark?: string;
}

export interface ScmSupplierQueryDTO extends BaseQueryDTO {
  supplierCode?: string;
  supplierName?: string;
  supplierType?: string;
  supplierLevel?: string;
  supplierCategory?: string;
  status?: string;
  isKeySupplier?: boolean;
  isStrategicSupplier?: boolean;
  purchaseRepresentativeId?: number;
  industry?: string;
  province?: string;
  city?: string;
  qualityRating?: string;
  deliveryRating?: string;
}

export interface ScmSupplierVO extends BaseVO {
  supplierCode: string;
  supplierName: string;
  supplierType: string;
  industry?: string;
  businessLicense?: string;
  taxNumber?: string;
  legalRepresentative?: string;
  registeredCapital?: number;
  
  // 联系信息
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  
  // 地址信息
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;
  postalCode?: string;
  
  // 财务信息
  creditRating?: string;
  creditLimit?: number;
  paymentTerms?: string;
  currencyCode?: string;
  
  // 业务信息
  supplierLevel?: string;
  supplierCategory?: string;
  supplierSource?: string;
  purchaseRepresentativeId?: number;
  purchaseRepresentativeName?: string; // 关联显示
  
  // 供应商专业字段
  qualityRating?: string;
  deliveryRating?: string;
  serviceRating?: string;
  annualSupplyCapacity?: number;
  mainProducts?: string;
  certifications?: string;
  
  // 状态信息
  status: string;
  isKeySupplier?: boolean;
  isStrategicSupplier?: boolean;
  remark?: string;
  
  // 联系人列表
  contacts?: ScmSupplierContactVO[];
}

export interface ScmSupplierListVO {
  id: number;
  supplierCode: string;
  supplierName: string;
  supplierType: string;
  supplierLevel?: string;
  supplierCategory?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  province?: string;
  city?: string;
  purchaseRepresentativeName?: string;
  qualityRating?: string;
  deliveryRating?: string;
  status: string;
  isKeySupplier?: boolean;
  isStrategicSupplier?: boolean;
  createdAt: string;
}

export interface ScmSupplierSummaryVO {
  totalCount: number;
  activeCount: number;
  inactiveCount: number;
  blacklistCount: number;
  aLevelCount: number;
  bLevelCount: number;
  cLevelCount: number;
  dLevelCount: number;
  corporateCount: number;
  individualCount: number;
  keySupplierCount: number;
  strategicSupplierCount: number;
}

export interface PageScmSupplierVO {
  list: ScmSupplierListVO[];
  total: number;
  pageNum?: number;
  pageSize?: number;
}

// --- SCM 供应商联系人相关类型 ---

export interface ScmSupplierContactCreateDTO {
  supplierId?: number; // 在嵌套创建时可能不需要
  contactName: string;
  contactTitle?: string;
  department?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  qq?: string;
  wechat?: string;
  address?: string;
  postalCode?: string;
  birthday?: string;
  gender?: string; // MALE, FEMALE
  contactRole?: string; // 联系人角色
  remark?: string;
}

export interface ScmSupplierContactUpdateDTO {
  contactName?: string;
  contactTitle?: string;
  department?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  qq?: string;
  wechat?: string;
  address?: string;
  postalCode?: string;
  birthday?: string;
  gender?: string;
  contactRole?: string;
  remark?: string;
}

export interface ScmSupplierContactVO extends BaseVO {
  supplierId: number;
  supplierName?: string; // 关联显示
  contactName: string;
  contactTitle?: string;
  department?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  qq?: string;
  wechat?: string;
  address?: string;
  postalCode?: string;
  birthday?: string;
  gender?: string;
  contactRole?: string;
  remark?: string;
}

// 简化的供应商选择器类型
export interface ScmSupplierSimpleVO {
  id: number;
  supplierCode: string;
  supplierName: string;
  supplierType: string;
  status: string;
}

// 验证响应类型
export interface ValidationResponse {
  valid: boolean;
  message?: string;
}

// --- API 请求函数 ---

const API_BASE_URL = '/scm/suppliers';

// --- 供应商 API ---

// 分页查询供应商
export function getSupplierPage(params: Partial<ScmSupplierQueryDTO>): Promise<PageScmSupplierVO> {
  return request.get(`${API_BASE_URL}`, { params });
}

// 创建供应商
export function createSupplier(data: ScmSupplierCreateDTO): Promise<ScmSupplierVO> {
  return request.post(`${API_BASE_URL}`, data);
}

// 更新供应商
export function updateSupplier(id: number, data: ScmSupplierUpdateDTO): Promise<ScmSupplierVO> {
  return request.put(`${API_BASE_URL}/${id}`, data);
}

// 删除供应商
export function deleteSupplier(id: number): Promise<void> {
  return request.delete(`${API_BASE_URL}/${id}`);
}

// 获取供应商详情
export function getSupplier(id: number): Promise<ScmSupplierVO> {
  return request.get(`${API_BASE_URL}/${id}`);
}

// 根据供应商编码获取供应商
export function getSupplierByCode(supplierCode: string): Promise<ScmSupplierVO> {
  return request.get(`${API_BASE_URL}/code/${supplierCode}`);
}

// 获取供应商统计摘要
export function getSupplierSummary(): Promise<ScmSupplierSummaryVO> {
  return request.get(`${API_BASE_URL}/summary`);
}

// --- 供应商联系人 API ---

// 获取供应商联系人列表
export function getSupplierContacts(supplierId: number): Promise<ScmSupplierContactVO[]> {
  return request.get(`${API_BASE_URL}/${supplierId}/contacts`);
}

// 创建供应商联系人
export function createSupplierContact(data: ScmSupplierContactCreateDTO): Promise<ScmSupplierContactVO> {
  return request.post(`${API_BASE_URL}/contacts`, data);
}

// 更新供应商联系人
export function updateSupplierContact(contactId: number, data: ScmSupplierContactUpdateDTO): Promise<ScmSupplierContactVO> {
  return request.put(`${API_BASE_URL}/contacts/${contactId}`, data);
}

// 删除供应商联系人
export function deleteSupplierContact(contactId: number): Promise<void> {
  return request.delete(`${API_BASE_URL}/contacts/${contactId}`);
}

// 设置主要联系人
export function setPrimaryContact(supplierId: number, contactId: number): Promise<void> {
  return request.put(`${API_BASE_URL}/${supplierId}/contacts/${contactId}/primary`);
}

// 获取主要联系人
export function getPrimaryContact(supplierId: number): Promise<ScmSupplierContactVO> {
  return request.get(`${API_BASE_URL}/${supplierId}/contacts/primary`);
}

// --- 业务验证 API ---

// 验证供应商编码
export function validateSupplierCode(supplierCode: string, excludeId?: number): Promise<ValidationResponse> {
  const params: any = { supplierCode };
  if (excludeId) {
    params.excludeId = excludeId;
  }
  return request.get(`${API_BASE_URL}/validate/supplier-code`, { params });
}

// 验证营业执照号
export function validateBusinessLicense(businessLicense: string, excludeId?: number): Promise<ValidationResponse> {
  const params: any = { businessLicense };
  if (excludeId) {
    params.excludeId = excludeId;
  }
  return request.get(`${API_BASE_URL}/validate/business-license`, { params });
}

// 验证税务登记号
export function validateTaxNumber(taxNumber: string, excludeId?: number): Promise<ValidationResponse> {
  const params: any = { taxNumber };
  if (excludeId) {
    params.excludeId = excludeId;
  }
  return request.get(`${API_BASE_URL}/validate/tax-number`, { params });
}

// --- 辅助工具函数 ---

// 获取供应商简单列表（用于下拉框选择）
export function getSupplierList(): Promise<ScmSupplierSimpleVO[]> {
  return request.get('/scm/suppliers/simple');
} 