package middleware

import (
	"context"
	"fmt"
	"strings"

	"backend/internal/repository" // Import repository
	"backend/pkg/errors"          // Import errors for response
	"backend/pkg/logger"
	CustomResponse "backend/pkg/response"
	"backend/pkg/util" // Added import for context utility functions

	"github.com/kataras/iris/v12"
)

// RBACConfig RBAC中间件配置
type RBACConfig struct {
	Enabled             bool     `json:"enabled" yaml:"enabled"`
	SuperAdminRoleCodes []string `json:"superAdminRoleCodes" yaml:"superAdminRoleCodes"` // Use role codes for super admin check
	ExcludedPaths       []string `json:"excludedPaths" yaml:"excludedPaths"`
	PermissionModel     string   `json:"permissionModel" yaml:"permissionModel"` // api or url
	StrictMode          bool     `json:"strictMode" yaml:"strictMode"`
	PermissionDelimiter string   `json:"permissionDelimiter" yaml:"permissionDelimiter"`
}

// 权限模型常量
const (
	PERMISSION_MODEL_URL = "url" // 基于URL的权限模型 (Not fully implemented below, focuses on API)
	PERMISSION_MODEL_API = "api" // 基于API的权限模型
)

// 默认RBAC配置
var defaultRBACConfig = RBACConfig{
	Enabled:             true,
	SuperAdminRoleCodes: []string{"ADMIN", "SUPER_ADMIN"}, // Example super admin codes
	ExcludedPaths:       []string{"/api/v1/auth", "/api/v1/captcha", "/public/", "/swagger", "/health"},
	PermissionModel:     PERMISSION_MODEL_API,
	StrictMode:          true, // Require explicit permission if true
	PermissionDelimiter: ":",
}

// RBACMiddleware 结构体
type RBACMiddleware struct {
	roleRepo     repository.RoleRepository
	roleMenuRepo repository.RoleMenuRepository
	// Add MenuRepo if needed to fetch permissions directly from menu entities
	// menuRepo repository.MenuRepository
	log logger.Logger
	cfg RBACConfig
}

// NewRBACMiddleware 创建 RBAC 中间件实例
func NewRBACMiddleware(
	roleRepo repository.RoleRepository,
	roleMenuRepo repository.RoleMenuRepository,
	// menuRepo repository.MenuRepository, // Pass if needed
	log logger.Logger,
	config ...RBACConfig,
) *RBACMiddleware {
	cfg := defaultRBACConfig
	if len(config) > 0 {
		cfg = config[0] // Use provided config if available
	}

	if roleRepo == nil || roleMenuRepo == nil {
		log.Fatal("RoleRepository and RoleMenuRepository are required for RBACMiddleware")
	}

	return &RBACMiddleware{
		roleRepo:     roleRepo,
		roleMenuRepo: roleMenuRepo,
		// menuRepo: menuRepo,
		log: log,
		cfg: cfg,
	}
}

// CheckPermission 返回一个中间件处理器，用于检查特定权限
func (m *RBACMiddleware) CheckPermission(requiredPermission string) iris.Handler {
	return func(ctx iris.Context) {
		// 0. Check if RBAC is enabled
		if !m.cfg.Enabled {
			ctx.Next()
			return
		}

		// 1. Check excluded paths
		requestPath := ctx.Path()
		for _, excludedPath := range m.cfg.ExcludedPaths {
			if strings.HasPrefix(requestPath, excludedPath) {
				m.log.Debug(ctx.Request().Context(), "RBAC: Path excluded", logger.WithField("path", requestPath)) // Assuming m.log methods take context
				ctx.Next()
				return
			}
		}

		// 2. Get User Info from Standard Go Context
		reqCtx := ctx.Request().Context()

		userID64, errUser := util.GetUserIDFromStdContext(reqCtx)
		if errUser != nil { // GetUserIDFromStdContext should return error if UserID is 0 or not found
			customErr, _ := errUser.(*errors.CustomError) // Already know it is *errors.CustomError
			m.log.Warn(reqCtx, "RBACMiddleware: Failed to get UserID from context", logger.WithError(errUser), logger.WithField("errorCode", customErr.Code))
			CustomResponse.FailWithError(ctx, errUser)
			return
		}
		userID := uint(userID64)

		roleIDs, errRoles := util.GetRoleIDsFromStdContext(reqCtx)
		if errRoles != nil {
			customErr, _ := errRoles.(*errors.CustomError)
			m.log.Warn(reqCtx, "RBACMiddleware: Failed to get RoleIDs from context", logger.WithError(errRoles), logger.WithField("errorCode", customErr.Code))
			CustomResponse.FailWithError(ctx, errRoles)
			return
		}
		// GetRoleIDsFromStdContext will return an error if the key is missing.
		// If the key exists but the value is nil or an empty slice, it will be returned as such (nil error).
		// Downstream logic (e.g., getRoleCodesByIDs) should handle cases like len(roleIDs) == 0.

		// 3. Get User Role Codes (pass roleIDs directly)
		userRoleCodes, err := m.getRoleCodesByIDs(reqCtx, roleIDs) // Pass reqCtx
		if err != nil {
			m.log.Error("RBAC: Error fetching user role codes",
				logger.WithError(err),
				logger.WithField("userID", userID),
				logger.WithField("roleIDs", roleIDs))
			CustomResponse.FailWithError(ctx, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "无法获取用户角色信息"))
			return // Stop execution
		}
		if len(userRoleCodes) == 0 {
			m.log.Warn("RBAC: User has no roles assigned",
				logger.WithField("userID", userID),
				logger.WithField("roleIDs", roleIDs))
			CustomResponse.FailWithError(ctx, errors.NewAuthError(errors.CODE_AUTH_FORBIDDEN, "用户未分配任何角色"))
			return // Stop execution
		}

		// 4. Check for Super Admin bypass
		isSuperAdmin := false
		for _, userRoleCode := range userRoleCodes {
			for _, superAdminCode := range m.cfg.SuperAdminRoleCodes {
				if userRoleCode == superAdminCode {
					isSuperAdmin = true
					break
				}
			}
			if isSuperAdmin {
				break
			}
		}

		if isSuperAdmin {
			m.log.Debug("RBAC: Super admin access granted", logger.WithField("userID", userID))
			ctx.Next()
			return
		}

		// 5. Get User Permissions based on roles
		userPermissions, err := m.getPermissionsByRoleIDs(reqCtx, roleIDs)
		if err != nil {
			m.log.Error("RBAC: Error fetching user permissions",
				logger.WithError(err),
				logger.WithField("userID", userID),
				logger.WithField("roleIDs", roleIDs))
			CustomResponse.FailWithError(ctx, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "无法获取用户权限信息"))
			return // Stop execution
		}

		// 6. Generate Required Permission for the current request (if not passed explicitly)
		// If requiredPermission is empty, generate based on path and method
		var effectiveRequiredPermission string
		if requiredPermission == "" {
			if m.cfg.PermissionModel == PERMISSION_MODEL_API {
				effectiveRequiredPermission = getAPIPermission(ctx.Method(), requestPath, m.cfg.PermissionDelimiter)
				m.log.Debug("RBAC: Generated API permission required", logger.WithField("permission", effectiveRequiredPermission))
			} else {
				m.log.Warn("RBAC: URL permission model not fully supported in this example CheckPermission method when requiredPermission is empty.")
				// Optionally implement URL permission generation or return error
				CustomResponse.FailWithError(ctx, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "不支持的 RBAC 配置"))
				return
			}
			if effectiveRequiredPermission == "" {
				m.log.Warn("RBAC: Could not generate required permission for path", logger.WithField("path", requestPath))
				// If strict mode, deny access if permission cannot be generated
				if m.cfg.StrictMode {
					CustomResponse.FailWithError(ctx, errors.NewAuthError(errors.CODE_AUTH_FORBIDDEN, "无法确定访问此资源所需的权限"))
					return
				} else {
					// If not strict mode, allow access (or handle differently based on policy)
					ctx.Next()
					return
				}
			}
		} else {
			effectiveRequiredPermission = requiredPermission
		}

		// 7. Check if user has the required permission
		hasPerm := false
		for _, userPerm := range userPermissions {
			// Direct match or potentially wildcard match later
			if userPerm == effectiveRequiredPermission {
				hasPerm = true
				break
			}
			// TODO: Implement wildcard matching if needed (e.g., user:*)
		}

		// 8. Handle result
		if hasPerm {
			m.log.Debug("RBAC: Permission granted",
				logger.WithField("userID", userID),
				logger.WithField("required", effectiveRequiredPermission))
			ctx.Next() // Permission granted, continue
		} else {
			m.log.Warn("RBAC: Permission denied",
				logger.WithField("userID", userID),
				logger.WithField("required", effectiveRequiredPermission),
				logger.WithField("userRoles", userRoleCodes),
			// logger.WithField("userPermissions", userPermissions), // Be cautious logging all permissions
			)
			CustomResponse.FailWithError(ctx, errors.ErrAuthForbidden)
			return // Stop execution
		}
	}
}

// AdminRequiredMiddleware 添加新的 AdminRequiredMiddleware 函数
func AdminRequiredMiddleware() iris.Handler {
	return func(ctx iris.Context) {
		reqCtx := ctx.Request().Context()
		isAdmin, errIsAdmin := util.IsAdminFromStdContext(reqCtx)

		if errIsAdmin != nil { // IsAdminFromStdContext returns (false, nil) if key is missing. This error is for type mismatch.
			// Assuming logger.GetLogger() for standalone functions if m.log is not available.
			// And that GetLogger() methods can take context as first arg.
			customErr, _ := errIsAdmin.(*errors.CustomError)
			logger.GetLogger().Warn(reqCtx, "AdminRequiredMiddleware: Error checking admin status from context (likely type mismatch).", logger.WithError(errIsAdmin), logger.WithField("errorCode", customErr.Code))
			CustomResponse.FailWithError(ctx, errIsAdmin) // Pass the *errors.CustomError
			return
		}

		if !isAdmin {
			// Retain original response structure if it's specific, otherwise use FailWithError
			// CustomResponse.FailWithError(ctx, errors.NewAuthError(errors.CODE_AUTH_FORBIDDEN, "需要管理员权限"))
			// Original response:
			ctx.StopWithJSON(iris.StatusForbidden, iris.Map{
				"code":    errors.CODE_AUTH_FORBIDDEN,
				"message": "需要管理员权限",
			})
			return
		}
		// 如果是管理员，则继续处理请求
		ctx.Next()
	}
}

// --- Helper Functions ---

// getRoleCodesByIDs fetches role codes for the given role IDs.
func (m *RBACMiddleware) getRoleCodesByIDs(ctx context.Context, roleIDs []uint) ([]string, error) {
	if len(roleIDs) == 0 {
		return []string{}, nil
	}
	// Use RoleRepository's inherited FindByIds method (correct casing)
	roles, err := m.roleRepo.FindByIds(ctx, roleIDs) // Use FindByIds
	if err != nil {
		return nil, fmt.Errorf("failed to find roles by IDs: %w", err)
	}

	roleCodes := make([]string, 0, len(roles))
	for _, role := range roles {
		roleCodes = append(roleCodes, role.Code)
	}
	m.log.Debug("RBAC Helper: Fetched role codes", logger.WithField("roleIDs", roleIDs), logger.WithField("codes", roleCodes))
	return roleCodes, nil
}

// getPermissionsByRoleIDs fetches unique permission strings for the given role IDs.
func (m *RBACMiddleware) getPermissionsByRoleIDs(ctx context.Context, roleIDs []uint) ([]string, error) {
	if len(roleIDs) == 0 {
		return []string{}, nil
	}
	// Use RoleMenuRepository to find menus associated with these roles
	menus, err := m.roleMenuRepo.FindMenusByRoleIDs(ctx, roleIDs, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to find menus by role IDs: %w", err)
	}

	permissionMap := make(map[string]struct{}) // Use map for deduplication
	for _, menu := range menus {
		if menu.Permission != "" { // Only consider non-empty permission strings
			// Split permissions if multiple are stored in one field (e.g., separated by comma)
			perms := strings.Split(menu.Permission, ",")
			for _, p := range perms {
				trimmedPerm := strings.TrimSpace(p)
				if trimmedPerm != "" {
					permissionMap[trimmedPerm] = struct{}{}
				}
			}
		}
	}

	permissions := make([]string, 0, len(permissionMap))
	for perm := range permissionMap {
		permissions = append(permissions, perm)
	}

	m.log.Debug("RBAC Helper: Fetched permissions", logger.WithField("roleIDs", roleIDs), logger.WithField("permissionsCount", len(permissions)))
	return permissions, nil
}

// --- Utility functions moved from the previous global scope ---

// getAPIPermission generates the permission string based on API conventions.
// Example: GET /api/v1/users/{id} -> users:read
// Example: POST /api/v1/roles -> roles:create
func getAPIPermission(method, path, delimiter string) string {
	// Normalize path: remove /api/vX prefix and leading/trailing slashes
	path = strings.TrimPrefix(path, "/api/v1") // Adjust if versioning changes
	path = strings.TrimPrefix(path, "/api")
	path = strings.Trim(path, "/")
	parts := strings.Split(path, "/")

	if len(parts) == 0 || parts[0] == "" {
		return "" // Cannot determine resource
	}

	resource := parts[0]
	action := methodToAction(method)

	// Simple check: if there are more parts after the resource, assume it's specific, otherwise list/general
	// This might need refinement based on your exact API structure
	// if len(parts) > 1 {
	// More specific action needed? e.g., read:one vs read:list?
	// Maybe handled by specific CheckPermission calls in router
	// }

	return resource + delimiter + action
}

// methodToAction converts HTTP method to a standard action verb.
func methodToAction(method string) string {
	switch strings.ToUpper(method) {
	case "GET", "HEAD":
		return "read"
	case "POST":
		return "create"
	case "PUT", "PATCH":
		return "update"
	case "DELETE":
		return "delete"
	default:
		return strings.ToLower(method) // Fallback for other methods
	}
}
