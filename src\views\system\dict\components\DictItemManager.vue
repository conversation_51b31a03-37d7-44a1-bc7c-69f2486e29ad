<template>
  <div>
    <!-- 字典项管理表格 -->
    <VNTable
      ref="itemTableRef"
      :data="itemTableData"
      :columns="itemTableColumns"
      :loading="itemLoading"
      :pagination="itemPagination"
      :toolbar-config="{ refresh: true, add: hasPermission('sys:dict:item:add') }" 
      :operation-buttons="[]" 
      :show-operations="true"
      show-index
      row-key="id"
      operation-fixed="right"
      :operation-width="150" 
      @refresh="loadDictItems"
      @add="handleAddNewItem"
      @page-change="handleItemPageChange"
      @page-size-change="handleItemPageSizeChange"
      @sort-change="handleItemSortChange" 
      @filter-change="handleItemFilterChange"
    >
      <!-- 字典项表格插槽 (例如状态、是否系统内置) -->
      <template #column-status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>
      <template #column-isSystem="{ row }">
        <el-tag :type="row.isSystem ? 'warning' : 'info'">
          {{ row.isSystem ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 字典项表格操作列插槽 (使用正确的 #operation) -->
      <template #operation="{ row }">
        <el-tooltip content="编辑项" placement="top" v-if="hasPermission('sys:dict:item:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditItem(row)" />
        </el-tooltip>
        <el-tooltip content="删除项" placement="top" v-if="hasPermission('sys:dict:item:delete')">
          <!-- 系统内置项通常不允许删除 -->
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            :disabled="row.isSystem" 
            @click="handleDeleteItem(row)" 
          />
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 字典项新增/编辑弹窗 -->
    <el-dialog
      v-model="itemCrudDialogVisible"
      :title="itemDialogTitle"
      width="60%"
      draggable
      :close-on-click-modal="false"
      append-to-body 
      @close="handleCloseItemDialog"
    >
      <!-- VNForm 用于编辑/新增字典项 -->
      <VNForm
        v-if="itemCrudDialogVisible" 
        ref="itemFormRef"
        :header-fields="itemFormFields"
        v-model="currentEditingItem"
        :loading="itemFormLoading" 
        @submit="submitItemForm"
        @cancel="handleCloseItemDialog"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTooltip, ElTag } from 'element-plus';
// 导入图标
import { Edit, Delete } from '@element-plus/icons-vue';
// 导入组件
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
// 导入类型
// 定义临时的 SortParams 和 FilterParams 类型
type SortParams = { prop: string; order: 'ascending' | 'descending' | null };
type FilterParams = Record<string, any>;
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 导入API和类型
import { 
  getDictItemPage, 
  createDictItem,
  updateDictItem,
  deleteDictItem
} from '@/api/system/systemDict';
import type { 
  DictionaryItemVO, 
  DictItemQueryDTO, 
  DictItemCreateDTO,
  DictItemUpdateDTO
} from '@/api/system/systemDict';
// 导入权限检查函数
import { hasPermission } from '@/hooks/usePermission';
// 导入用户 Store
import { useUserStore } from '@/store/modules/user';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};
// -------------------------------------

// --- Props --- 
interface Props {
  typeId: number;
  typeCode: string;
  typeName: string;
}
const props = defineProps<Props>();

// --- Refs ---
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;

const itemTableRef = ref<VNTableInstance>();
const itemFormRef = ref<VNFormInstance>();

// --- State (Item Management) ---
const itemTableData = ref<DictionaryItemVO[]>([]);
const itemPagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const itemQueryParams = ref<Partial<DictItemQueryDTO>>({}); // 不包含 typeId，会自动添加
const itemLoading = ref(false);
const itemCrudDialogVisible = ref(false); // 控制项编辑/新增弹窗
const currentEditingItem = ref<Partial<DictItemCreateDTO | DictItemUpdateDTO>>({}); // 使用联合类型
const isItemEdit = ref(false); // 是否为编辑项模式
const itemFormLoading = ref(false); // 项表单加载状态
const currentItemSort = ref<SortParams | null>(null);
const currentItemFilters = ref<FilterParams>({});

// --- 获取用户 Store 和 isAdmin 状态 ---
const userStore = useUserStore();
const isAdmin = computed(() => userStore.userInfo?.isAdmin || false);
// -------------------------------------

// --- Computed --- 
const itemDialogTitle = computed(() => (isItemEdit.value ? '编辑字典项' : '新增字典项'));

// --- Table Columns (Item) --- 
const itemTableColumns = ref<TableColumn[]>([
  { prop: 'label', label: '标签', minWidth: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'value', label: '值', minWidth: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'sortOrder', label: '排序', width: 80, sortable: true },
  { prop: 'status', label: '状态', width: 90, slot: true, filterable: true, filterType: 'select', filterOptions: [{label: '启用', value: 1}, {label: '禁用', value: 0}] },
  { prop: 'isSystem', label: '系统内置', width: 100, slot: true },
  { prop: 'remark', label: '备注', minWidth: 150, showOverflowTooltip: true },
]);

// --- Form Fields (Item) --- 
const itemFormFields = computed<HeaderField[]>(() => [
  { field: 'label', label: '标签', rules: [{ required: true, message: '标签不能为空' }], disabled: isItemEdit.value && currentEditingItem.value.isSystem },
  { field: 'value', label: '值', rules: [{ required: true, message: '值不能为空' }], disabled: isItemEdit.value && currentEditingItem.value.isSystem },
  { field: 'sortOrder', label: '排序', type: 'number', props: { precision: 0, min: 0 }, defaultValue: 0 },
  { field: 'status', label: '状态', type: 'select', options: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }], defaultValue: 1 },
  {
    field: 'isSystem',
    label: '系统内置',
    type: 'switch',
    defaultValue: false,
    disabled: computed(() => {
      if (!isAdmin.value && isItemEdit.value) {
        return true;
      }
      if (isAdmin.value && isItemEdit.value && currentEditingItem.value?.isSystem === true) {
        return true;
      }
      return false;
    }).value,
  },
  { field: 'remark', label: '备注', type: 'textarea' },
]);

// --- Lifecycle --- 
onMounted(() => {
  loadDictItems();
});

// 监视 typeId 变化，如果变化则重新加载 (当组件在dialog中复用时可能需要)
watch(() => props.typeId, () => {
  // 重置状态并加载
  itemQueryParams.value = {};
  itemPagination.currentPage = 1;
  itemPagination.pageSize = 10;
  currentItemSort.value = null;
  currentItemFilters.value = {};
  loadDictItems();
});

// --- Methods (Item Management) ---

// 加载字典项数据
const loadDictItems = async (paramsOverwrite = {}) => {
  if (!props.typeId) {
    console.warn('Cannot load items without typeId');
    return;
  }
  itemLoading.value = true;
  try {
    // 准备排序参数
    let sortString: string | undefined = undefined;
    if (currentItemSort.value && currentItemSort.value.prop && currentItemSort.value.order) {
      const direction = currentItemSort.value.order === 'descending' ? 'desc' : 'asc';
      sortString = `${currentItemSort.value.prop},${direction}`;
    }
    
    // 准备筛选参数 (类似类型筛选，但这里没有日期示例)
    const filterParams: Record<string, any> = {};
    for (const key in currentItemFilters.value) {
      const filterValue = currentItemFilters.value[key];
      if (filterValue !== null && filterValue !== undefined && filterValue !== '' && !(Array.isArray(filterValue) && filterValue.length === 0)) {
         if (key === 'status' && Array.isArray(filterValue) && filterValue.length > 0) {
             filterParams[key] = filterValue[0];
         } else {
             filterParams[key] = filterValue;
         }
      }
    }

    const params: Partial<DictItemQueryDTO> & { dictionaryTypeId: number; pageNum: number; pageSize: number; sort?: string } = {
      dictionaryTypeId: props.typeId, // 关键：传入当前管理的类型ID
      pageNum: itemPagination.currentPage,
      pageSize: itemPagination.pageSize,
      sort: sortString,
      ...filterParams,
      ...paramsOverwrite,
    };

    Object.keys(params).forEach(key => {
        const paramKey = key as keyof typeof params; // 断言 key 类型
        if (params[paramKey] === null || params[paramKey] === undefined || params[paramKey] === '') {
            delete params[paramKey];
        }
    });

    console.log('[loadDictItems] Fetching with params:', params);
    const response = await getDictItemPage(params);

    if (response && Array.isArray(response.list)) {
        itemTableData.value = response.list;
        itemPagination.total = response.total || 0;
    } else {
        console.warn('getDictItemPage 返回的数据格式不符合预期:', response);
        itemTableData.value = [];
        itemPagination.total = 0;
    }

  } catch (error) {
    console.error(`Error loading dictionary items for type ${props.typeId}:`, error);
    // ElMessage.error('加载字典项列表失败');
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    itemTableData.value = [];
    itemPagination.total = 0;
  } finally {
    itemLoading.value = false;
  }
};

// 处理项分页变化
const handleItemPageChange = (page: number) => {
  itemPagination.currentPage = page;
  loadDictItems();
};

// 处理项每页数量变化
const handleItemPageSizeChange = (size: number) => {
  itemPagination.pageSize = size;
  itemPagination.currentPage = 1;
  loadDictItems();
};

// 处理项排序变化
const handleItemSortChange = (sort: SortParams) => {
  currentItemSort.value = sort;
  loadDictItems();
};

// 处理项筛选变化
const handleItemFilterChange = (filters: FilterParams) => {
  currentItemFilters.value = filters;
  itemPagination.currentPage = 1;
  loadDictItems();
};

// 处理新增项按钮点击
const handleAddNewItem = () => {
  isItemEdit.value = false;
  currentEditingItem.value = {
    status: 1, 
    sortOrder: 0, 
  };
  itemCrudDialogVisible.value = true;
  // 清除校验
  nextTick(() => {
    itemFormRef.value?.clearValidate();
  });
};

// 处理编辑项按钮点击
const handleEditItem = (row: DictionaryItemVO) => {
  isItemEdit.value = true;
  currentEditingItem.value = JSON.parse(JSON.stringify(row)); 
  itemCrudDialogVisible.value = true;
  // 清除校验
  nextTick(() => {
    itemFormRef.value?.clearValidate();
  });
};

// 处理删除项按钮点击
const handleDeleteItem = async (row: DictionaryItemVO) => {
  if (row.isSystem) {
    ElMessage.warning('系统内置项不允许删除');
    return;
  }
  try {
    await ElMessageBox.confirm(`确定删除字典项 "${row.label}" (${row.value}) 吗?`, '确认删除', { type: 'warning' });
    itemLoading.value = true;
    console.log('Calling deleteDictItem API for ID:', row.id);
    // 调用删除API
    await deleteDictItem(row.id); 
    ElMessage.success('删除成功');
    loadDictItems(); // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting dict item:', error);
      // ElMessage.error('删除失败'); // 假设错误由拦截器处理
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: errorMessage,
          showClose: true,
          duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    itemLoading.value = false;
  }
};

// 关闭项增/改弹窗
const handleCloseItemDialog = () => {
  itemCrudDialogVisible.value = false;
  // itemFormRef.value?.resetForm(); // resetFields 会清除数据，在编辑时可能不希望这样，依赖 destroy-on-close
};

// 提交项表单
const submitItemForm = async () => {
  const formIsValid = await itemFormRef.value?.validateForm();
  if (!formIsValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  itemFormLoading.value = true;
  const payload = { 
    ...currentEditingItem.value, 
    dictionaryTypeId: props.typeId // 确保提交时包含类型ID
  };

  try {
    if (isItemEdit.value) {
      // 编辑逻辑
      const editingData = currentEditingItem.value as Partial<DictionaryItemVO>;
      if (!editingData.id) {
          ElMessage.error('无法获取要编辑的项ID');
          itemFormLoading.value = false;
          return;
      }
      // 准备更新 payload，仅包含可更新字段, 且 isSystem 仅 admin 可提交
      const updatePayload: Partial<DictItemUpdateDTO> = {
          label: editingData.label,
          value: editingData.value,
          sortOrder: editingData.sortOrder,
          status: editingData.status,
          remark: editingData.remark,
          // 只有 admin 提交时才包含 isSystem
          isSystem: isAdmin.value ? editingData.isSystem : undefined, 
      };
      // 移除 undefined 字段，避免发送 null
      Object.keys(updatePayload).forEach(key => {
        if (updatePayload[key as keyof typeof updatePayload] === undefined) {
          delete updatePayload[key as keyof typeof updatePayload];
        }
      });

      console.log('Calling updateDictItem API with:', editingData.id, updatePayload);
      await updateDictItem(editingData.id!, updatePayload);
      ElMessage.success('编辑成功');
    } else {
      // 新增逻辑
      // 确保 isSystem 包含在内
      const createPayload = payload as DictItemCreateDTO; 
      console.log('Calling createDictItem API with:', createPayload);
      await createDictItem(createPayload);
      ElMessage.success('新增成功');
    }
    handleCloseItemDialog(); // 关闭弹窗
    loadDictItems(); // 刷新列表
  } catch (error) {
    console.error('Error saving dictionary item:', error);
    // 错误消息应由API拦截器处理
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    itemFormLoading.value = false;
  }
};

</script>

<style scoped>
/* 可以在这里为字典项管理组件添加特定样式 */
.el-tag + .el-tag {
  margin-left: 5px; /* 给标签之间加点间距 */
}
</style> 