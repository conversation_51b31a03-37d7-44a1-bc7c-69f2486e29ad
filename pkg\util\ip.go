package util

import (
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"regexp"
	"strings"
)

// IP地址相关常量
const (
	LOCAL_IP_V4      = "127.0.0.1"
	LOCAL_IP_V6      = "::1"
	DEFAULT_IP       = "0.0.0.0"
	UNKNOWN_LOCATION = "未知位置"
	IP_LOCATION_API  = "https://ip.useragentinfo.com/jsonp?ip=%s" // IP地理位置API

	// 内网IP地址段
	PRIVATE_IP_CLASS_A = "^10\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$"
	PRIVATE_IP_CLASS_B = "^172\\.(1[6-9]|2\\d|3[0-1])\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$"
	PRIVATE_IP_CLASS_C = "^192\\.168\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$"
)

// IPInfo IP地理位置信息结构体
type IPInfo struct {
	IP       string `json:"ip"`
	Country  string `json:"country"`
	Province string `json:"province"`
	City     string `json:"city"`
	ISP      string `json:"isp"`
}

// GetLocalIP 获取本机IP地址
func GetLocalIP() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", err
	}

	for _, addr := range addrs {
		ipNet, isIpNet := addr.(*net.IPNet)
		// 检查是否是有效的IP地址且不是环回地址
		if isIpNet && !ipNet.IP.IsLoopback() && ipNet.IP.To4() != nil {
			return ipNet.IP.String(), nil
		}
	}

	return "", errors.New("未找到本机IP地址")
}

// GetLocalIPv6 获取本机IPv6地址
func GetLocalIPv6() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", err
	}

	for _, addr := range addrs {
		ipNet, isIpNet := addr.(*net.IPNet)
		// 检查是否是有效的IP地址且不是环回地址
		if isIpNet && !ipNet.IP.IsLoopback() && ipNet.IP.To4() == nil && ipNet.IP.To16() != nil {
			return ipNet.IP.String(), nil
		}
	}

	return "", errors.New("未找到本机IPv6地址")
}

// GetClientIP 从HTTP请求获取客户端IP地址
func GetClientIP(r *http.Request) string {
	// 尝试从X-Forwarded-For头获取IP
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	if ip := strings.TrimSpace(strings.Split(xForwardedFor, ",")[0]); ip != "" {
		return ip
	}

	// 尝试从X-Real-IP头获取IP
	if ip := strings.TrimSpace(r.Header.Get("X-Real-IP")); ip != "" {
		return ip
	}

	// 尝试从请求RemoteAddr获取IP
	if ip, _, err := net.SplitHostPort(strings.TrimSpace(r.RemoteAddr)); err == nil {
		return ip
	}

	return DEFAULT_IP
}

// IsValidIPv4 检查字符串是否为IPv4格式
func IsValidIPv4(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	return ip != nil && ip.To4() != nil
}

// IsValidIPv6 检查字符串是否为IPv6格式
func IsValidIPv6(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	return ip != nil && ip.To4() == nil && ip.To16() != nil
}

// IsPrivateIP 检查IP是否为内网IP
func IsPrivateIP(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	// 检查是否为本地环回地址
	if ip.IsLoopback() {
		return true
	}

	// 检查是否为内网IP
	if ip4 := ip.To4(); ip4 != nil {
		// IPv4内网地址段检查
		classAMatch, _ := regexp.MatchString(PRIVATE_IP_CLASS_A, ipStr)
		classBMatch, _ := regexp.MatchString(PRIVATE_IP_CLASS_B, ipStr)
		classCMatch, _ := regexp.MatchString(PRIVATE_IP_CLASS_C, ipStr)
		return classAMatch || classBMatch || classCMatch
	}

	// IPv6内网地址检查 (fe80::/10)
	return ip.IsLinkLocalUnicast()
}

// IpToLong 将IPv4地址转换为长整数
func IpToLong(ipStr string) uint32 {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return 0
	}
	if ip.To4() == nil {
		return 0 // 仅支持IPv4
	}
	return binary.BigEndian.Uint32(ip.To4())
}

// LongToIp 将长整数转换为IPv4地址
func LongToIp(ipLong uint32) string {
	ipByte := make([]byte, 4)
	binary.BigEndian.PutUint32(ipByte, ipLong)
	ip := net.IP(ipByte)
	return ip.String()
}

// IsIpInRange 检查IP是否在指定范围内
func IsIpInRange(ipStr, startIpStr, endIpStr string) bool {
	ip := IpToLong(ipStr)
	startIp := IpToLong(startIpStr)
	endIp := IpToLong(endIpStr)
	return ip >= startIp && ip <= endIp
}

// GetMaskLength 获取子网掩码长度
func GetMaskLength(maskStr string) int {
	mask := net.ParseIP(maskStr)
	if mask == nil {
		return 0
	}

	if mask.To4() != nil {
		mask = mask.To4()
	}

	bits := 0
	for _, b := range mask {
		bits += bits8(b)
	}

	return bits
}

// bits8 计算字节中1的位数
func bits8(b byte) int {
	var bits = 0
	for ; b != 0; b >>= 1 {
		bits += int(b & 1)
	}
	return bits
}

// GetIPLocation 获取IP地理位置信息
func GetIPLocation(ipStr string) (*IPInfo, error) {
	// 检查是否为本地IP或内网IP
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return nil, errors.New("无效的IP地址")
	}

	if ip.IsLoopback() || IsPrivateIP(ipStr) {
		return &IPInfo{
			IP:       ipStr,
			Country:  "内网IP",
			Province: "",
			City:     "",
			ISP:      "",
		}, nil
	}

	// 调用外部API获取IP地理位置
	url := fmt.Sprintf(IP_LOCATION_API, ipStr)
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析JSONP响应，格式通常是callback({"ip":"x.x.x.x", ...})
	jsonpStr := string(body)
	startIdx := strings.Index(jsonpStr, "(")
	endIdx := strings.LastIndex(jsonpStr, ")")

	if startIdx < 0 || endIdx < 0 || endIdx <= startIdx {
		return nil, errors.New("无效的API响应格式")
	}

	jsonStr := jsonpStr[startIdx+1 : endIdx]

	var info map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &info); err != nil {
		return nil, err
	}

	ipInfo := &IPInfo{
		IP:       ipStr,
		Country:  getStringValue(info, "country", ""),
		Province: getStringValue(info, "province", ""),
		City:     getStringValue(info, "city", ""),
		ISP:      getStringValue(info, "isp", ""),
	}

	return ipInfo, nil
}

// getStringValue 从map中安全获取字符串值
func getStringValue(m map[string]interface{}, key string, defaultValue string) string {
	if value, ok := m[key]; ok {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}

// FormatIPLocation 格式化IP地理位置信息为字符串
func FormatIPLocation(ipInfo *IPInfo) string {
	if ipInfo == nil {
		return UNKNOWN_LOCATION
	}

	location := ipInfo.Country
	if ipInfo.Province != "" && ipInfo.Province != ipInfo.Country {
		location += " " + ipInfo.Province
	}
	if ipInfo.City != "" && ipInfo.City != ipInfo.Province {
		location += " " + ipInfo.City
	}
	if ipInfo.ISP != "" {
		location += " " + ipInfo.ISP
	}

	if location == "" {
		return UNKNOWN_LOCATION
	}

	return location
}

// GetIPLocationString 获取IP地理位置字符串
func GetIPLocationString(ipStr string) string {
	info, err := GetIPLocation(ipStr)
	if err != nil {
		return UNKNOWN_LOCATION
	}
	return FormatIPLocation(info)
}
