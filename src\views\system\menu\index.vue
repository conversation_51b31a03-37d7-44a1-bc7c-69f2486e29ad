<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      operation-fixed="right"
      :operation-width="200"
      :show-pagination="false"
      @refresh="refreshAllMenuData"
      @add="handleAdd()"
    >
      <!-- 图标列 -->
      <template #column-icon="{ row }">
        <el-icon v-if="row.icon"><component :is="row.icon" /></el-icon>
        <span v-else>-</span>
      </template>

      <!-- 菜单类型列 -->
      <template #column-type="{ row }">
        <el-tag :type="getMenuTypeTag(row.type)">{{ formatMenuType(row.type) }}</el-tag>
      </template>

      <!-- 状态列 -->
      <template #column-status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>

       <!-- 是否隐藏列 -->
      <template #column-hidden="{ row }">
        <span>{{ row.hidden ? '是' : '否' }}</span>
      </template>

    </VNTable>

    <!-- 新增/编辑 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="60%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2"
        :label-width="'120px'"
        :loading="loading"
        :show-actions="isViewing ? false : true"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <!-- 父级菜单插槽 -->
        <template #form-item-parentId="{ field, formData: formModel }">
          <el-tree-select
            v-model="formModel[field.field]"
            :data="parentMenuOptions"
            :props="{ label: 'title', children: 'children', value: 'id' }"
            check-strictly
            clearable
            placeholder="选择父级菜单 (不选为顶级)"
            style="width: 100%;"
            :render-after-expand="false"
            :disabled="isViewing"
          />
        </template>

        <!-- 图标选择插槽 (修改) -->
        <template #form-item-icon="{ field, formData: formModel }">
          <el-input v-model="formModel[field.field]" placeholder="选择图标" :disabled="isViewing" readonly>
            <template #prepend><el-icon v-if="formModel[field.field]"><component :is="formModel[field.field]" /></el-icon></template>
            <template #append>
              <el-button :icon="Plus" @click="openIconSelector" :disabled="isViewing" />
            </template>
          </el-input>
        </template>

        <!-- 新增：自定义表单操作按钮 -->
        <template #actions>
            <template v-if="isViewing">
                <el-button
                  v-if="hasPermission('system:menu:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreview"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('system:menu:print')" 
                  type="primary"
                  :icon="Printer"
                  @click="handlePrint"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
                <el-button @click="handleCloseDialog">取消</el-button>
            </template>
        </template>

      </VNForm>
    </el-dialog>

    <!-- 图标选择器弹窗 (新增) -->
    <el-dialog v-model="iconSelectorVisible" title="选择图标" width="70%" top="5vh">
        <el-input
            v-model="iconSearchTerm"
            placeholder="搜索图标 (名称)"
            clearable
            :prefix-icon="Search"
            style="margin-bottom: 15px;"
        />
        <el-scrollbar height="60vh">
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: 10px;">
                <el-button
                    v-for="iconName in filteredIcons"
                    :key="iconName"
                    @click="selectIcon(iconName)"
                    style="height: 80px; padding: 0;"
                >
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; padding: 5px;">
                        <el-icon :size="24"><component :is="allIcons[iconName]" /></el-icon>
                        <span style="font-size: 12px; margin-top: 5px; text-align: center; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 100%;">{{ iconName }}</span>
                    </div>
                </el-button>
            </div>
             <el-empty v-if="filteredIcons.length === 0" description="未找到图标" />
        </el-scrollbar>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, ActionButton } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton, ElIcon, ElTreeSelect, ElInput, ElScrollbar, ElEmpty } from 'element-plus';
import type { TagProps } from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// --- 引入菜单管理 API 函数 ---
import {
  getMenuTree,
  addMenu,
  updateMenu,
  deleteMenu,
  getMenuByID,
} from '@/api/system/systemMenu';
import type { MenuTreeVO, MenuVO, MenuCreateDTO, MenuUpdateDTO } from '@/api/system/systemMenu';

import { hasPermission } from '@/hooks/usePermission';
import { Plus, Search, View, Printer } from '@element-plus/icons-vue';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentMenu = ref<MenuTreeVO | MenuVO | null>(null);
const iconSelectorVisible = ref(false);
const iconSearchTerm = ref('');

// --- Reactive State ---
const loading = ref(false);
const tableData = ref<MenuTreeVO[]>([]);
const formData = ref<Partial<MenuCreateDTO | MenuUpdateDTO>>({});
const parentMenuOptions = ref<MenuTreeVO[]>([]);
const allIcons = ref<Record<string, any>>({});
const filteredIcons = ref<string[]>([]);

// --- 计算属性 ---
const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增菜单';
  if (formMode.value === 'edit') return '编辑菜单';
  if (formMode.value === 'view') return '查看菜单';
  return '菜单管理';
});
const isViewing = computed(() => formMode.value === 'view');

// --- 表格列配置 ---
const tableColumns = ref<TableColumn[]>([
  { prop: 'title', label: '菜单标题', minWidth: 180, align: 'left' },
  { prop: 'icon', label: '图标', width: 80, slot: true },
  { prop: 'sort', label: '排序', width: 80 },
  { prop: 'permission', label: '权限标识', minWidth: 180 },
  { prop: 'path', label: '路由路径', minWidth: 180 },
  { prop: 'component', label: '组件路径', minWidth: 200 },
  { prop: 'type', label: '类型', width: 100, slot: true },
  { prop: 'status', label: '状态', width: 80, slot: true },
  { prop: 'hidden', label: '隐藏', width: 80, slot: true },
]);

// --- 工具栏配置 ---
const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('sys:menu:add'),
  expandAll: hasPermission('sys:menu:expandAll'),
  collapseAll: hasPermission('sys:menu:collapseAll'),
  density: true,
  columnSetting: true,
  fullscreen: true,
}));

// --- 行操作按钮配置 (移除 hidden 中的 row 依赖) ---
const operationButtons = computed<ActionButton[]>(() => [
  {
    label: '查看',
    icon: 'View',
    handler: (row) => handleView(row),
    hidden: !hasPermission('sys:menu:view')
  },
  {
    label: '新增',
    icon: 'Plus',
    type: 'success',
    handler: (row) => handleAdd(row),
    hidden: !hasPermission('sys:menu:add'),
  },
  {
    label: '编辑',
    icon: 'Edit',
    type: 'primary',
    handler: (row) => handleEdit(row),
    hidden: !hasPermission('sys:menu:edit')
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    handler: (row) => handleDelete(row),
    hidden: !hasPermission('sys:menu:delete')
  },
]);

// --- 表单字段配置 (显式指定 computed 返回类型 & Fixes for points 2 & 3) ---
const formFields = computed<HeaderField[]>(() => {
    const disabledInViewMode = isViewing.value;
    return [
        { field: 'parentId', label: '父级菜单', type: 'slot', md: 24, lg: 24, xl: 24, disabled: disabledInViewMode },
        {
            field: 'type', label: '菜单类型', type: 'radio', options: [
                { label: '目录', value: 1 }, { label: '菜单', value: 2 }, { label: '按钮', value: 3 }
            ], rules: [{ required: true, message: '请选择菜单类型' }], disabled: disabledInViewMode
        },
        { field: 'title', label: '菜单标题', rules: [{ required: true, message: '菜单标题为必填项' }], disabled: disabledInViewMode },
        { field: 'name', label: '菜单名称', rules: [{ required: true, message: '菜单名称为必填项' }], disabled: disabledInViewMode },
        { field: 'icon', label: '菜单图标', type: 'slot', disabled: disabledInViewMode },
        { field: 'sort', label: '显示排序', type: 'number', rules: [{ required: true, message: '排序为必填项' }], disabled: disabledInViewMode },
        { field: 'path', label: '路由路径', placeholder: '目录/菜单类型填写', disabled: disabledInViewMode },
        { field: 'component', label: '组件路径', placeholder: '菜单类型填写', disabled: disabledInViewMode },
        { field: 'permission', label: '权限标识', placeholder: '菜单/按钮类型填写', disabled: disabledInViewMode },
        { field: 'redirect', label: '重定向', placeholder: '目录类型填写', disabled: disabledInViewMode },
        { field: 'activeMenu', label: '高亮菜单', placeholder: '子路由需高亮父菜单时填写', disabled: disabledInViewMode },
        { field: 'status', label: '菜单状态', type: 'radio', options: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }], disabled: disabledInViewMode },
        { field: 'hidden', label: '是否隐藏', type: 'radio', options: [{ label: '是', value: true }, { label: '否', value: false }], disabled: disabledInViewMode },
        { field: 'noCache', label: '是否缓存', type: 'radio', options: [{ label: '否', value: false }, { label: '是', value: true }], disabled: disabledInViewMode },
        { field: 'alwaysShow', label: '总是显示', type: 'radio', options: [{ label: '是', value: true }, { label: '否', value: false }], disabled: disabledInViewMode },
        { field: 'breadcrumb', label: '显示面包屑', type: 'radio', options: [{ label: '是', value: true }, { label: '否', value: false }], disabled: disabledInViewMode },
        { field: 'isExternal', label: '是否外链', type: 'radio', options: [{ label: '是', value: true }, { label: '否', value: false }], disabled: disabledInViewMode },
        { field: 'remark', label: '备注', type: 'textarea', rows: 3, md: 24, lg: 24, xl: 24, disabled: disabledInViewMode },
    ];
});

// --- 生命周期 ---
onMounted(() => {
  refreshAllMenuData();
  loadAllIcons();
});

// --- 方法 ---

// Combined function to refresh table data and parent options
const refreshAllMenuData = async () => {
  loading.value = true;
  try {
    const response = await getMenuTree({}) as unknown as { data?: MenuTreeVO[] } | MenuTreeVO[];
    let resData: MenuTreeVO[] = [];
    if (Array.isArray(response)) {
      resData = response;
    } else if (response && Array.isArray(response.data)) {
      resData = response.data;
    }

    if (resData) {
      // Update table data
      tableData.value = resData;
      console.log('[refreshAllMenuData] Menu tree loaded for table:', tableData.value);

      // Update parent options (filter buttons)
      const filterButtons = (nodes: MenuTreeVO[]): MenuTreeVO[] => {
        return nodes.filter(node => node.type !== 3).map(node => ({
          ...node,
          children: node.children ? filterButtons(node.children) : []
        }));
      };
      parentMenuOptions.value = filterButtons(resData);
      console.log('[refreshAllMenuData] Parent menu options loaded (buttons filtered): ', parentMenuOptions.value);
    } else {
      console.warn('[refreshAllMenuData] getMenuTree did not return expected data structure:', response);
      tableData.value = [];
      parentMenuOptions.value = [];
    }
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    console.error('Error loading menu data:', error);
    tableData.value = [];
    parentMenuOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 加载所有图标
const loadAllIcons = () => {
    allIcons.value = ElementPlusIconsVue;
    filteredIcons.value = Object.keys(allIcons.value);
};

// 监听搜索词变化，过滤图标
watch(iconSearchTerm, (newValue) => {
    const searchTermLower = newValue.toLowerCase();
    if (!searchTermLower) {
        filteredIcons.value = Object.keys(allIcons.value);
    } else {
        filteredIcons.value = Object.keys(allIcons.value).filter(iconName =>
            iconName.toLowerCase().includes(searchTermLower)
        );
    }
});

// 打开图标选择器
const openIconSelector = () => {
  iconSearchTerm.value = '';
  filteredIcons.value = Object.keys(allIcons.value);
  iconSelectorVisible.value = true;
};

// 选择图标
const selectIcon = (iconName: string) => {
  formData.value.icon = iconName;
  iconSelectorVisible.value = false;
};

// 打开新增/编辑/查看弹窗 (修改 mode 类型)
const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: MenuTreeVO | MenuVO) => {
  formMode.value = mode;
  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id) {
      console.error('编辑或查看模式下缺少菜单数据或 ID');
      return;
    }
    loading.value = true;
    try {
       const detailData = await getMenuByID(rowData.id) as unknown as MenuVO;
       if (detailData) {
         formData.value = { ...detailData, parentId: detailData.parentId === 0 ? undefined : detailData.parentId };
         currentMenu.value = detailData;
         dialogVisible.value = true;
       } else {
          const errorMessage = getApiErrorMessage({ message: '获取菜单详情失败或数据为空'});
          ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
          });
       }
    } catch(error) {
        console.error('Error fetching menu detail:', error);
        const errorMessage = getApiErrorMessage(error);
        ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
        });
    } finally {
        loading.value = false;
    }

  } else {
    formData.value = {
      type: 1,
      status: 1,
      sort: 0,
      hidden: false,
      noCache: false,
      alwaysShow: false,
      breadcrumb: true,
      isExternal: false,
      parentId: rowData?.id ?? undefined,
    };
    currentMenu.value = null;
    dialogVisible.value = true;
  }

  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentMenu.value = null;
};

// 提交表单
const submitForm = async () => {
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  const dataToSend = { ...formData.value };
  if (!dataToSend.parentId) {
      dataToSend.parentId = 0;
  }

  if (formMode.value === 'add' && dataToSend.parentId) {
      const parentNode = findNodeById(tableData.value, dataToSend.parentId);
      if (parentNode && parentNode.type === 3) {
          ElMessage.error('不能在按钮类型的菜单下添加子菜单');
          return;
      }
  }

  console.log('[submitForm] Sending data:', dataToSend);

  loading.value = true;
  try {
    if (formMode.value === 'add') {
      await addMenu(dataToSend as MenuCreateDTO);
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentMenu.value) {
      await updateMenu(currentMenu.value.id, dataToSend as MenuUpdateDTO);
      ElMessage.success('编辑成功');
    }
    handleCloseDialog();
    refreshAllMenuData();
  } catch (error) {
    console.error(`保存菜单时出错 (${formMode.value}):`, error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
};

// 处理新增按钮点击 (顶级或子级)
const handleAdd = (parentNode?: MenuTreeVO) => {
  if (parentNode && parentNode.type === 3) {
      ElMessage.warning('按钮类型的菜单下不能添加子菜单');
      return;
  }
  handleOpenForm('add', parentNode);
};

// 处理编辑按钮点击
const handleEdit = (row: MenuTreeVO) => {
  handleOpenForm('edit', row);
};

// 处理删除按钮点击
const handleDelete = async (row: MenuTreeVO) => {
  if (row.children && row.children.length > 0) {
    ElMessage.warning('请先删除子菜单');
    return;
  }
  try {
    await ElMessageBox.confirm(`确定删除菜单 "${row.title}" 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    await deleteMenu(row.id);
    ElMessage.success('删除成功');
    refreshAllMenuData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除菜单时出错:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

// 处理查看按钮点击
const handleView = (row: MenuTreeVO) => {
  handleOpenForm('view', row);
};

// --- 辅助函数 ---
const formatMenuType = (type: number): string => {
  if (type === 1) return '目录';
  if (type === 2) return '菜单';
  if (type === 3) return '按钮';
  return '未知';
};

const getMenuTypeTag = (type: number): TagProps['type'] => {
  if (type === 1) return 'info';
  if (type === 2) return 'success';
  if (type === 3) return 'warning';
  return 'info';
};

const findNodeById = (nodes: MenuTreeVO[], id: number): MenuTreeVO | null => {
    for (const node of nodes) {
        if (node.id === id) {
            return node;
        }
        if (node.children) {
            const found = findNodeById(node.children, id);
            if (found) {
                return found;
            }
        }
    }
    return null;
};

// --- 新增：打印处理函数（占位）---
const handlePrintPreview = () => {
  if (!currentMenu.value) return;
  console.log('触发打印预览，菜单:', currentMenu.value);
  ElMessage.info('打印预览功能待实现');
  // TODO: Implement print preview logic
};

const handlePrint = () => {
  if (!currentMenu.value) return;
  console.log('触发打印，菜单:', currentMenu.value);
  ElMessage.info('打印功能待实现');
  // TODO: Implement print logic
};

</script>

<style scoped>
:deep(.el-tree-select) {
  width: 100%;
}
</style>
