package vo

import "time"

// BaseVO 基础视图对象
// 包含实体的基本信息，用于前端展示
type BaseVO struct {
	ID        uint      `json:"id"`        // ID：记录的唯一标识
	CreatedAt time.Time `json:"createdAt"` // 创建时间：记录的创建时间
	UpdatedAt time.Time `json:"updatedAt"` // 更新时间：记录的最后更新时间
	DeletedAt time.Time `json:"deletedAt"` // 删除时间：记录的删除时间
	CreatedBy uint      `json:"createdBy"` // 创建人ID：创建该记录的用户ID
	UpdatedBy uint      `json:"updatedBy"` // 更新人ID：最后更新该记录的用户ID
}

// TenantVO 租户视图对象
// 包含租户相关的基本信息，用于前端展示
type TenantVO struct {
	BaseVO
	TenantID uint `json:"tenantId"` // 租户ID：记录所属的租户ID
}

// PageVO 分页视图对象
// 用于包装分页查询的结果，返回给前端
type PageVO struct {
	List     interface{} `json:"list"`     // 数据列表
	PageNum  int         `json:"pageNum"`  // 当前页码
	PageSize int         `json:"pageSize"` // 每页大小
	Total    int64       `json:"total"`    // 总记录数
	Pages    int         `json:"pages"`    // 总页数
}
