package cache

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/mojocn/base64Captcha"

	"backend/pkg/logger" // Assuming logger is accessible globally or passed
)

// RedisStore 使用 Redis 实现 base64Captcha.Store 接口
type RedisStore struct {
	client     *redis.Client
	expiration time.Duration
	prefix     string
	log        logger.Logger // Add logger
}

// NewRedisStore 创建 Redis 存储实例
func NewRedisStore(client *redis.Client, expiration time.Duration, prefix string, log logger.Logger) base64Captcha.Store {
	// Provide default prefix if empty
	if prefix == "" {
		prefix = "captcha:"
	}
	return &RedisStore{
		client:     client,
		expiration: expiration,
		prefix:     prefix,
		log:        log, // Assign logger
	}
}

// Set 实现 base64Captcha.Store 接口的 Set 方法
func (rs *RedisStore) Set(id string, value string) error {
	ctx := context.Background() // TODO: Consider passing context from caller if possible
	key := rs.prefix + id
	if rs.log != nil {
		rs.log.Debug(ctx, "Setting captcha in redis", logger.With<PERSON>ield("key", key), logger.WithField("expiry", rs.expiration))
	}
	return rs.client.Set(ctx, key, value, rs.expiration).Err()
}

// Get 实现 base64Captcha.Store 接口的 Get 方法
func (rs *RedisStore) Get(id string, clear bool) string {
	ctx := context.Background() // TODO: Consider passing context from caller if possible
	key := rs.prefix + id
	val, err := rs.client.Get(ctx, key).Result()
	if err != nil {
		// Log only if error is not redis.Nil (key not found)
		if err != redis.Nil && rs.log != nil {
			rs.log.Warn(ctx, "Failed to get captcha from redis", logger.WithError(err), logger.WithField("key", key))
		}
		return ""
	}
	if clear {
		if err := rs.client.Del(ctx, key).Err(); err != nil {
			if rs.log != nil {
				rs.log.Error(ctx, "Failed to delete captcha from redis after get", logger.WithError(err), logger.WithField("key", key))
			}
		}
	}
	return val
}

// Verify 实现 base64Captcha.Store 接口的 Verify 方法
func (rs *RedisStore) Verify(id, answer string, clear bool) bool {
	storedAnswer := rs.Get(id, clear) // Get will clear if clear is true
	// Case-insensitive comparison might be better for captcha
	// return storedAnswer != "" && strings.EqualFold(storedAnswer, answer)
	return storedAnswer != "" && storedAnswer == answer
}
