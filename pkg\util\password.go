package util

import (
	"bytes"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"regexp"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"

	"backend/pkg/constant"
	"backend/pkg/errors"
)

const (
	// 密码强度相关常量
	PASSWORD_MIN_LENGTH      = 6        // 密码最小长度
	PASSWORD_MAX_LENGTH      = 20       // 密码最大长度
	PASSWORD_STRONG_MIN_LEN  = 8        // 强密码最小长度
	PASSWORD_BCRYPT_COST     = 12       // bcrypt算法的成本因子
	PASSWORD_SALT_BYTES      = 32       // 盐值字节数
	PASSWORD_HASH_BYTES      = 64       // 哈希字节数
	PASSWORD_DEFAULT         = "123456" // 默认密码
	PASSWORD_ITERATION_COUNT = 10000    // PBKDF2迭代次数
)

// 密码强度级别
const (
	PASSWORD_STRENGTH_WEAK   = 0 // 弱密码
	PASSWORD_STRENGTH_MEDIUM = 1 // 中等强度密码
	PASSWORD_STRENGTH_STRONG = 2 // 强密码
)

// EncryptPassword 加密密码
func EncryptPassword(password string) (string, error) {
	if password == "" {
		return "", errors.NewParamError(constant.PARAM_EMPTY, "密码不能为空")
	}

	// 使用bcrypt加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), PASSWORD_BCRYPT_COST)
	if err != nil {
		return "", errors.NewSystemError(constant.SYS_ERROR, "密码加密失败").WithCause(err)
	}

	return string(hashedPassword), nil
}

// ComparePassword 比较密码
func ComparePassword(hashedPassword, password string) error {
	if hashedPassword == "" || password == "" {
		return errors.NewParamError(constant.PARAM_EMPTY, "密码不能为空")
	}

	// 使用bcrypt比较密码
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))

	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			return errors.NewError(constant.PASSWORD_ERROR, "密码错误")
		}
		return errors.NewSystemError(constant.SYS_ERROR, "密码比较失败").WithCause(err)
	}

	return nil
}

// GenerateSalt 生成盐值
func GenerateSalt() (string, error) {
	salt := make([]byte, PASSWORD_SALT_BYTES)
	_, err := rand.Read(salt)
	if err != nil {
		return "", errors.NewSystemError(constant.SYS_ERROR, "生成盐值失败").WithCause(err)
	}
	return base64.StdEncoding.EncodeToString(salt), nil
}

// CheckPasswordStrength 检查密码强度
func CheckPasswordStrength(password string) int {
	if password == "" {
		return PASSWORD_STRENGTH_WEAK
	}

	// 密码长度检查
	if len(password) < PASSWORD_MIN_LENGTH {
		return PASSWORD_STRENGTH_WEAK
	}

	// 计算密码强度评分
	score := 0

	// 长度加分
	if len(password) >= PASSWORD_STRONG_MIN_LEN {
		score += 1
	}

	// 包含小写字母加分
	if regexp.MustCompile(`[a-z]`).MatchString(password) {
		score += 1
	}

	// 包含大写字母加分
	if regexp.MustCompile(`[A-Z]`).MatchString(password) {
		score += 1
	}

	// 包含数字加分
	if regexp.MustCompile(`[0-9]`).MatchString(password) {
		score += 1
	}

	// 包含特殊字符加分
	if regexp.MustCompile(`[^a-zA-Z0-9]`).MatchString(password) {
		score += 1
	}

	// 根据评分确定密码强度
	if score <= 2 {
		return PASSWORD_STRENGTH_WEAK
	} else if score <= 4 {
		return PASSWORD_STRENGTH_MEDIUM
	} else {
		return PASSWORD_STRENGTH_STRONG
	}
}

// GetPasswordStrengthText 获取密码强度文本描述
func GetPasswordStrengthText(strength int) string {
	switch strength {
	case PASSWORD_STRENGTH_WEAK:
		return "弱"
	case PASSWORD_STRENGTH_MEDIUM:
		return "中"
	case PASSWORD_STRENGTH_STRONG:
		return "强"
	default:
		return "未知"
	}
}

// ValidatePassword 验证密码
func ValidatePassword(password string) error {
	if password == "" {
		return errors.NewParamError(constant.PARAM_EMPTY, "密码不能为空")
	}

	if len(password) < PASSWORD_MIN_LENGTH {
		return errors.NewParamError(
			constant.PARAM_ERROR,
			fmt.Sprintf("密码长度不能小于%d个字符", PASSWORD_MIN_LENGTH),
		)
	}

	if len(password) > PASSWORD_MAX_LENGTH {
		return errors.NewParamError(
			constant.PARAM_ERROR,
			fmt.Sprintf("密码长度不能大于%d个字符", PASSWORD_MAX_LENGTH),
		)
	}

	return nil
}

// GenerateRandomPassword 生成随机密码
func GenerateRandomPassword(length int) (string, error) {
	if length < PASSWORD_MIN_LENGTH {
		length = PASSWORD_MIN_LENGTH
	}

	if length > PASSWORD_MAX_LENGTH {
		length = PASSWORD_MAX_LENGTH
	}

	// 密码字符集
	lowerLetters := "abcdefghijklmnopqrstuvwxyz"
	upperLetters := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	digits := "0123456789"
	symbols := "!@#$%^&*()-_=+[]{}|;:,.<>?/"

	// 合并所有字符集
	charset := lowerLetters + upperLetters + digits + symbols

	// 生成随机密码
	var password bytes.Buffer
	randomBytes := make([]byte, 1)

	// 确保密码至少包含一个小写字母
	_, err := rand.Read(randomBytes)
	if err != nil {
		return "", errors.NewSystemError(constant.SYS_ERROR, "生成随机数失败").WithCause(err)
	}
	password.WriteByte(lowerLetters[int(randomBytes[0])%len(lowerLetters)])

	// 确保密码至少包含一个大写字母
	_, err = rand.Read(randomBytes)
	if err != nil {
		return "", errors.NewSystemError(constant.SYS_ERROR, "生成随机数失败").WithCause(err)
	}
	password.WriteByte(upperLetters[int(randomBytes[0])%len(upperLetters)])

	// 确保密码至少包含一个数字
	_, err = rand.Read(randomBytes)
	if err != nil {
		return "", errors.NewSystemError(constant.SYS_ERROR, "生成随机数失败").WithCause(err)
	}
	password.WriteByte(digits[int(randomBytes[0])%len(digits)])

	// 确保密码至少包含一个特殊字符
	_, err = rand.Read(randomBytes)
	if err != nil {
		return "", errors.NewSystemError(constant.SYS_ERROR, "生成随机数失败").WithCause(err)
	}
	password.WriteByte(symbols[int(randomBytes[0])%len(symbols)])

	// 生成剩余的随机字符
	for i := 4; i < length; i++ {
		_, err := rand.Read(randomBytes)
		if err != nil {
			return "", errors.NewSystemError(constant.SYS_ERROR, "生成随机数失败").WithCause(err)
		}
		index := int(randomBytes[0]) % len(charset)
		password.WriteByte(charset[index])
	}

	// 确保密码包含各种字符
	pwdStr := password.String()
	strength := CheckPasswordStrength(pwdStr)
	if strength < PASSWORD_STRENGTH_MEDIUM {
		// 如果强度不够，重新生成
		return GenerateRandomPassword(length)
	}

	return pwdStr, nil
}

// MaskPassword 掩盖密码（用于日志输出等）
func MaskPassword(password string) string {
	if len(password) <= 3 {
		return "***"
	}
	return password[:1] + strings.Repeat("*", len(password)-2) + password[len(password)-1:]
}

// RandomString 生成指定长度的随机字符串
func RandomString(n int) string {
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, n)
	for i := range result {
		randomBytes := make([]byte, 1)
		if _, err := rand.Read(randomBytes); err != nil {
			// 如果随机数生成失败，使用时间作为备选方案
			result[i] = chars[time.Now().UnixNano()%int64(len(chars))]
			time.Sleep(time.Nanosecond)
		} else {
			result[i] = chars[int(randomBytes[0])%len(chars)]
		}
	}
	return string(result)
}
