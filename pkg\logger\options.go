package logger

// Option 日志配置选项
type Option func(*LogConfig)

// WithLevel 设置日志级别
func WithLevel(level string) Option {
	return func(c *LogConfig) {
		c.Level = level
	}
}

// WithFormat 设置日志格式
func WithFormat(format string) Option {
	return func(c *LogConfig) {
		c.Format = format
	}
}

// WithOutput 设置日志输出方式
func WithOutput(output string) Option {
	return func(c *LogConfig) {
		if output == "file" {
			c.LogInFile = true
			c.LogInConsole = false
		} else if output == "console" {
			c.LogInFile = false
			c.LogInConsole = true
		} else if output == "both" {
			c.LogInFile = true
			c.LogInConsole = true
		}
	}
}

// WithFilePath 设置日志文件路径
func WithFilePath(filePath string) Option {
	return func(c *LogConfig) {
		c.Director = filePath
	}
}

// WithMaxSize 设置单个日志文件最大大小（MB）
func WithMaxSize(maxSize int) Option {
	return func(c *LogConfig) {
		c.MaxSize = maxSize
	}
}

// WithMaxBackups 设置日志文件最大备份数
func WithMaxBackups(maxBackups int) Option {
	return func(c *LogConfig) {
		c.MaxBackups = maxBackups
	}
}

// WithMaxAge 设置日志文件最大保留天数
func WithMaxAge(maxAge int) Option {
	return func(c *LogConfig) {
		c.MaxAge = maxAge
	}
}

// WithCompress 设置是否压缩日志文件
func WithCompress(compress bool) Option {
	return func(c *LogConfig) {
		c.Compress = compress
	}
}

// WithPrefix 设置日志前缀
func WithPrefix(prefix string) Option {
	return func(c *LogConfig) {
		c.Prefix = prefix
	}
}

// WithShowLine 设置是否显示行号
func WithShowLine(showLine bool) Option {
	return func(c *LogConfig) {
		c.ShowLine = showLine
	}
}

// WithEncodeTime 设置时间编码格式
func WithEncodeTime(encodeTime string) Option {
	return func(c *LogConfig) {
		c.EncodeTime = encodeTime
	}
}

// NewLogger 创建新的日志实例
func NewLogger(opts ...Option) Logger {
	// 默认配置
	config := &LogConfig{
		Level:        INFO_LEVEL,
		Format:       JSON_FORMAT,
		Prefix:       "",
		Director:     "logs",
		ShowLine:     true,
		EncodeTime:   "RFC3339",
		LogInFile:    true,
		LogInConsole: true,
		MaxSize:      100,
		MaxBackups:   10,
		MaxAge:       30,
		Compress:     true,
	}

	// 应用选项
	for _, opt := range opts {
		opt(config)
	}

	return NewZapLogger(config)
}
