package vo

import "time"

// OrganizationNodeVO 组织节点视图对象 (用于列表或单个详情)
type OrganizationNodeVO struct {
	ID                uint      `json:"id"`
	AccountBookID     uint      `json:"accountBookId"`
	ParentID          uint      `json:"parentId"`
	NodeType          string    `json:"nodeType"` // department, position
	Name              string    `json:"name"`
	Code              string    `json:"code,omitempty"`
	LeaderUserID      *uint     `json:"leaderUserId,omitempty"`
	Level             int       `json:"level"`
	OrderNum          int       `json:"orderNum"`
	Weight            *int      `json:"weight,omitempty"`
	IsVirtual         bool      `json:"isVirtual"`
	StandardHeadcount *int      `json:"standardHeadcount,omitempty"`
	Status            int       `json:"status"`
	Remarks           string    `json:"remarks,omitempty"`
	CreatedAt         time.Time `json:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt"`
	CreatedBy         uint      `json:"createdBy"`
	UpdatedBy         uint      `json:"updatedBy"`
}

// OrganizationNodeTreeVO 组织节点树视图对象
type OrganizationNodeTreeVO struct {
	OrganizationNodeVO                           // 嵌入基础 VO 信息
	Children           []*OrganizationNodeTreeVO `json:"children,omitempty"` // 子节点列表
}
