package dto

import "backend/pkg/response"

// RoleCreateDTO 角色创建数据传输对象
// 用于管理员创建角色
type RoleCreateOrUpdateDTO struct {
	TenantDTO          // 继承租户信息
	Name        string `json:"name" validate:"required,max=50"`                // 角色名称：角色的显示名称，如"系统管理员"，必填，最大长度50
	Code        string `json:"code" validate:"required,max=50"`                // 角色编码：角色的唯一标识符，如"ADMIN"，用于程序中引用角色，必填，最大长度50
	Status      int    `json:"status" validate:"omitempty,oneof=0 1"`          // 状态：角色状态，0-禁用，1-正常
	DataScope   int    `json:"dataScope" validate:"omitempty,oneof=1 2 3 4 5"` // 数据权限范围：控制角色可访问的数据范围，1-全部，2-自定义，3-本部门，4-本部门及以下，5-仅本人
	Remark      string `json:"remark,omitempty" validate:"omitempty,max=255"`  // 备注：关于角色的附加说明信息，可选，最大长度255
	IsDefault   bool   `json:"isDefault,omitempty"`                            // 是否是默认角色：标识角色是否为新用户默认分配的角色
	IsSystem    bool   `json:"isSystem,omitempty"`                             // 是否是系统角色：标识角色是否为系统内置角色
	Description string `json:"description,omitempty" validate:"max=255"`       // 描述：角色的详细描述信息，可选，最大长度255
	MenuIDs     []uint `json:"menuIds" validate:"required,min=1,dive,gt=0"`    // 菜单ID列表：角色关联的菜单ID集合，必填，至少包含一个菜单ID，每个ID必须大于0
}

// RolePageQueryDTO 角色分页查询数据传输对象
// 用于管理员分页查询角色列表
type RolePageQueryDTO struct {
	PageQuery   response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
	Name        string             `json:"name" query:"name"`                                                // 角色名称：角色的显示名称，可选，用于模糊查询
	Code        string             `json:"code" query:"code"`                                                // 角色编码：角色的唯一标识符，可选，用于模糊查询
	Status      *int               `json:"status" query:"status"`                                            // 状态：角色状态，可选，0-禁用，1-正常
	DataScope   *int               `json:"dataScope" query:"dataScope" validate:"omitempty,oneof=1 2 3 4 5"` // 数据权限范围：控制角色可访问的数据范围，1-全部，2-自定义，3-本部门，4-本部门及以下，5-仅本人
	IsDefault   *bool              `json:"isDefault" query:"isDefault"`                                      // 是否是默认角色：标识角色是否为新用户默认分配的角色，可选
	IsSystem    *bool              `json:"isSystem" query:"isSystem"`                                        // 是否是系统角色：标识角色是否为系统内置角色，可选
	Description string             `json:"description" validate:"max=255"`                                   // 描述：角色的详细描述信息，可选，最大长度255
}

// RoleUpdateMenuDTO 角色菜单更新数据传输对象
// 用于管理员更新角色菜单
type RoleUpdateMenuDTO struct {
	MenuIDs []uint `json:"menuIds" validate:"required,min=1,dive,gt=0"` // 菜单ID列表：角色关联的菜单ID集合，必填，至少包含一个菜单ID，每个ID必须大于0
}

// RoleUpdateStatusDTO 角色状态更新数据传输对象
// 用于管理员更新角色状态
type RoleUpdateStatusDTO struct {
	Status int `json:"status" validate:"required,oneof=0 1"` // 状态：角色状态，必填，0-禁用，1-正常
}

// RoleUpdateIsDefaultDTO 默认角色更新数据传输对象
// 用于管理员更新默认角色标签
type RoleUpdateIsDefaultDTO struct {
	IsDefault bool `json:"isDefault"` // 是否是默认角色：标识角色是否为新用户默认分配的角色
}

// RoleUpdateIsSystemDTO 系统内置角色更新数据传输对象
// 用于管理员更新系统内置角色标签
type RoleUpdateIsSystemDTO struct {
	IsSystem bool `json:"isSystem"` // 是否是系统角色：标识角色是否为系统内置角色
}

// RoleUpdateDataScopeDTO 数据权限范围更新数据传输对象
// 用于管理员更新数据权限范围
type RoleUpdateDataScopeDTO struct {
	DataScope int `json:"dataScope" validate:"oneof=1 2 3 4 5"` // 数据权限范围：控制角色可访问的数据范围，1-全部，2-自定义，3-本部门，4-本部门及以下，5-仅本人
}

// RoleDeleteDTO 角色删除数据传输对象
// 用于管理员删除角色
type RoleDeleteDTO struct {
	IDs []uint `json:"ids" validate:"required,min=1,dive,gt=0"` // 角色ID列表：要删除的角色ID集合，必填，至少包含一个角色ID，每个ID必须大于0
}
