# 应用基础配置
app:
  name: "MiSoft" # 应用名称
  version: "0.1.0" # 应用版本
  mode: "dev" # 运行模式: dev, test, prod
  port: 8080 # HTTP 服务端口
  debug: true # 是否开启 Debug 模式
  timeout: 60 # 请求超时时间 (秒)
  allowed_origins: # CORS 允许的源
    - "http://localhost:5173" # 前端开发服务器地址
    - "http://your.frontend.domain" # 前端生产环境地址
  licensePath: "./license/license.lic" # 还原: 指向固定的授权文件名
  # 日志配置 (对应 config.LogConfig)
  log:
    level: "debug" # 日志级别: debug, info, warn, error, fatal
    format: "console" # 日志格式: console, json
    output: "console" # 日志输出: console, file, both
    filepath: "logs" # 日志文件存放目录 (当 output 包含 file 时有效)
    max_size: 10 # 单个日志文件大小 (MB)
    max_backups: 10 # 保留的日志文件数量
    max_age: 30 # 日志文件保留天数
    compress: false # 是否压缩旧日志文件

# 数据库配置
database:
  type: "postgresql" # 数据库类型: mysql, postgresql
  auto_init: true # 全局设置 (可选, 如果希望所有DB都默认开启)
  # MySQL 配置 (对应 config.MySQLConfig)
  mysql:
    host: "127.0.0.1" # 数据库主机地址
    port: 3306 # 数据库端口
    username: "your_db_user" # 数据库用户名 (请修改)
    password: "your_db_password" # 数据库密码 (请修改)
    database: "your_db_name" # 数据库名称 (请修改)
    charset: "utf8mb4" # 数据库字符集
    parse_time: true # 是否解析时间类型
    loc: "Local" # 时区设置
    max_idle_conns: 10 # 最大空闲连接数
    max_open_conns: 100 # 最大打开连接数
    conn_max_lifetime: 3600 # 连接最大存活时间 (秒)
    log_level: 4 # GORM 日志级别 (1:Silent, 2:Error, 3:Warn, 4:Info)
    slow_threshold: 200 # 慢查询阈值 (毫秒)
    auto_init: true # <-- 添加针对 MySQL 的 auto_init 设置
  # PostgreSQL 配置 (对应 config.PostgreSQLConfig)
  postgresql:
    host: "127.0.0.1"
    port: 5432
    username: "postgres"
    password: "Gj790326,,"
    database: "misoft"
    ssl_mode: "disable" # disable, require, verify-full
    timezone: "Asia/Shanghai" # 时区
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600
    log_level: 4
    slow_threshold: 200
    auto_init: true # <-- 添加针对 PostgreSQL 的 auto_init 设置

# Redis 配置
redis:
  host: "127.0.0.1" # Redis 主机地址
  port: 6379 # Redis 端口
  password: "" # Redis 密码 (如果需要)
  db: 0 # Redis 数据库编号
  pool_size: 10 # 连接池大小
  min_idle_conns: 2 # 最小空闲连接数
  dial_timeout: 5 # 连接超时时间 (秒)
  read_timeout: 3 # 读取超时时间 (秒)
  write_timeout: 3 # 写入超时时间 (秒)

# JWT 配置
jwt:
  secret: "M2Q1ZGNmZjY3ZThhNGU1Yjk5ZGU2NjAyY2E4MGIxZjgzMzk2ZjcwZTc0M2I4NzljMDU3NzQ2YmQ3OTk3ZGFhYw==" # JWT 签名密钥
  expire: 24 # Access Token 过期时间 (小时)
  refresh_expire: 7 # Refresh Token 过期时间 (天, 用于记住我)
  issuer: "misoft" # JWT 签发者 (更新为与应用名一致)

# 验证码配置 (对应 config.CaptchaConfig)
captcha:
  enable: true # 是否启用验证码
  expire: 5 # 过期时间 (分钟)
  length: 4 # 字符长度
  width: 120 # 图片宽度
  height: 40 # 图片高度
  max_skew: 0.3 # 最大倾斜度
  dot_count: 20 # 干扰点数量
  store: "redis" # 存储方式: redis, memory
  prefix: "captcha:" # Redis 存储前缀

# 安全配置
security:
  password_hash_iterations: 10000 # 密码哈希迭代次数 (示例)
  login_retry_limit: 5 # 登录重试次数限制
  login_retry_timeout: 300 # 登录重试锁定时间 (秒)
  # enable_captcha: true      # (已移至 captcha.enable)
  # captcha_expire: 5         # (已移至 captcha.expire)
  token_renewal_strategy: 0 # Token 续期策略 (示例，具体含义需看实现)
  password_hash_algo: "bcrypt" # 密码哈希算法 (示例，需看实现)

# 存储配置
storage:
  type: "local" # 存储类型: local, s3, oss
  maxFileSizeMB: 20 # 全局最大文件大小 (MB)
  allowedTypes: # 全局允许的文件类型列表 (MIME 或 扩展名)
    - "image/jpeg"
    - "image/png"
    - "application/pdf"
    - ".doc"
    - ".docx"
    - ".xls"
    - ".xlsx"
    - ".zip"
    - ".rar"
  local:
    path: "./uploads" # 本地存储路径 (当前项目目录下)
    base_url: "/static/uploads" # 访问 URL 前缀
  s3:
    region: "us-east-1"
    bucket: "your-s3-bucket"
    access_key: "YOUR_S3_ACCESS_KEY"
    secret_key: "YOUR_S3_SECRET_KEY"
    base_url: "https://your-s3-bucket.s3.amazonaws.com"
  oss:
    endpoint: "oss-cn-hangzhou.aliyuncs.com"
    bucket: "your-oss-bucket"
    access_key: "YOUR_OSS_ACCESS_KEY"
    secret_key: "YOUR_OSS_SECRET_KEY"
    base_url: "https://your-oss-bucket.oss-cn-hangzhou.aliyuncs.com"

# 缓存配置 (示例)
cache:
  type: "memory" # 缓存类型: memory, redis
  default_expiration: 3600 # 默认过期时间 (秒)
  cleanup_interval: 600 # 清理间隔 (秒)

# 监控配置 (示例)
monitoring:
  enable_metrics: false # 是否启用 Prometheus 指标
  metrics_path: "/metrics" # 指标暴露路径
  enable_tracing: false # 是否启用分布式追踪
  tracing_exporter: "jaeger" # 追踪导出器: jaeger, zipkin
  jaeger:
    endpoint: "http://localhost:14268/api/traces" # Jaeger collector endpoint
    service_name: "MyApp"
  zipkin:
    endpoint: "http://localhost:9411/api/v2/spans" # Zipkin collector endpoint
    service_name: "MyApp"
