<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="95%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    append-to-body
  >
    <div class="mobile-putaway-dialog" v-if="currentDetail">
      <!-- 物料信息 -->
      <div class="item-info-section">
        <div class="section-title">物料信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">物料编码:</span>
            <span class="value">{{ currentDetail.itemSku }}</span>
          </div>
          <div class="info-item">
            <span class="label">物料名称:</span>
            <span class="value">{{ currentDetail.itemName }}</span>
          </div>
          <div class="info-item">
            <span class="label">建议库位:</span>
            <span class="value">{{ currentDetail.suggestedLocation }}</span>
          </div>
          <div class="info-item" v-if="currentDetail.batchNo">
            <span class="label">批次号:</span>
            <span class="value">{{ currentDetail.batchNo }}</span>
          </div>
        </div>
      </div>
      
      <!-- 数量信息 -->
      <div class="quantity-section">
        <div class="section-title">数量信息</div>
        <div class="quantity-grid">
          <div class="quantity-item">
            <div class="quantity-label">上架数量</div>
            <div class="quantity-value required">{{ currentDetail.putawayQuantity }}</div>
          </div>
          <div class="quantity-item">
            <div class="quantity-label">已上架数量</div>
            <div class="quantity-value completed">{{ currentDetail.completedQuantity || 0 }}</div>
          </div>
          <div class="quantity-item">
            <div class="quantity-label">剩余数量</div>
            <div class="quantity-value remaining">{{ remainingQty }}</div>
          </div>
        </div>
      </div>
      
      <!-- 上架操作 -->
      <div class="putaway-section">
        <div class="section-title">上架操作</div>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <el-form-item label="本次上架数量" prop="putawayQty">
            <div class="quantity-input-group">
              <el-button
                @click="decreaseQty"
                :disabled="formData.putawayQty <= 0"
                circle
              >
                <el-icon><Minus /></el-icon>
              </el-button>
              <el-input-number
                v-model="formData.putawayQty"
                :min="0"
                :max="maxPutawayQty"
                :precision="0"
                controls-position="right"
                class="qty-input"
              />
              <el-button
                @click="increaseQty"
                :disabled="formData.putawayQty >= maxPutawayQty"
                circle
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
            
            <!-- 快捷数量按钮 -->
            <div class="quick-qty-buttons">
              <el-button
                v-for="qty in quickQtyOptions"
                :key="qty"
                size="small"
                @click="setQuickQty(qty)"
                :disabled="qty > maxPutawayQty"
              >
                {{ qty }}
              </el-button>
              <el-button
                size="small"
                @click="setQuickQty(maxPutawayQty)"
                type="primary"
              >
                全部
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item label="实际库位" prop="actualLocation" v-if="mode !== 'adjust'">
            <div class="location-input-group">
              <el-input
                v-model="formData.actualLocation"
                placeholder="请扫描或输入实际库位"
                readonly
              />
              <el-button
                @click="handleScanLocation"
                type="primary"
              >
                <el-icon><Scan /></el-icon>
                扫描
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 异常处理 -->
      <div class="exception-section" v-if="showExceptionOptions">
        <div class="section-title">异常处理</div>
        <div class="exception-buttons">
          <el-button
            @click="handleException('LOCATION_FULL')"
            type="warning"
            size="small"
          >
            库位已满
          </el-button>
          <el-button
            @click="handleException('DAMAGE')"
            type="danger"
            size="small"
          >
            货物损坏
          </el-button>
          <el-button
            @click="handleException('LOCATION_ERROR')"
            type="info"
            size="small"
          >
            库位错误
          </el-button>
          <el-button
            @click="handleException('OTHER')"
            size="small"
          >
            其他异常
          </el-button>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" size="large">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="!canConfirm"
          size="large"
        >
          {{ confirmButtonText }}
        </el-button>
      </div>
    </template>
    
    <!-- 库位扫描对话框 -->
    <el-dialog
      v-model="scanLocationVisible"
      title="扫描库位"
      width="90%"
      append-to-body
    >
      <div class="scan-location-container">
        <el-input
          ref="locationScanInputRef"
          v-model="scannedLocation"
          placeholder="请扫描库位条码"
          @keyup.enter="handleLocationScanConfirm"
          autofocus
        >
          <template #prepend>
            <el-icon><Scan /></el-icon>
          </template>
        </el-input>
        
        <div class="scan-tips">
          <p>请将摄像头对准库位条码进行扫描</p>
          <p>或手动输入库位编码后按回车确认</p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="scanLocationVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleLocationScanConfirm"
          :disabled="!scannedLocation"
        >
          确认
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 异常处理对话框 -->
    <el-dialog
      v-model="exceptionDialogVisible"
      title="异常处理"
      width="90%"
      append-to-body
    >
      <el-form :model="exceptionForm" label-position="top">
        <el-form-item label="异常类型">
          <el-input v-model="exceptionForm.type" readonly />
        </el-form-item>
        <el-form-item label="异常描述">
          <el-input
            v-model="exceptionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请详细描述异常情况"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="exceptionDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleExceptionConfirm"
        >
          提交异常
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, nextTick } from 'vue'
import { ElDialog, ElButton, ElMessage, ElForm, ElFormItem, ElInput, ElInputNumber, ElIcon } from 'element-plus'
import { Plus, Minus, Scan } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { WmsPutawayTaskDetailResp } from '@/api/wms/putawayTask'

// 组件接口定义
interface MobilePutawayDialogEmits {
  confirm: [data: any]
  cancel: []
}

// Emits
const emit = defineEmits<MobilePutawayDialogEmits>()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const scanLocationVisible = ref(false)
const exceptionDialogVisible = ref(false)
const mode = ref<'start' | 'complete' | 'adjust'>('start')
const currentDetail = ref<WmsPutawayTaskDetailResp | null>(null)
const scannedLocation = ref('')

// 表单数据
const formData = reactive({
  putawayQty: 0,
  actualLocation: '',
  remark: ''
})

// 异常表单
const exceptionForm = reactive({
  type: '',
  description: ''
})

// 表单验证规则
const formRules: FormRules = {
  putawayQty: [
    { required: true, message: '请输入上架数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '上架数量必须大于0', trigger: 'blur' }
  ],
  actualLocation: [
    { required: true, message: '请选择实际库位', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    start: '开始上架',
    complete: '完成上架',
    adjust: '调整上架'
  }
  return titles[mode.value]
})

const confirmButtonText = computed(() => {
  const texts = {
    start: '开始上架',
    complete: '完成上架',
    adjust: '确认调整'
  }
  return texts[mode.value]
})

const remainingQty = computed(() => {
  if (!currentDetail.value) return 0
  return currentDetail.value.putawayQuantity - (currentDetail.value.completedQuantity || 0)
})

const maxPutawayQty = computed(() => {
  if (!currentDetail.value) return 0
  if (mode.value === 'adjust') {
    return currentDetail.value.putawayQuantity
  }
  return remainingQty.value
})

const canConfirm = computed(() => {
  return formData.putawayQty > 0 && 
         (mode.value === 'adjust' || formData.actualLocation)
})

const showExceptionOptions = computed(() => {
  return mode.value === 'start' || mode.value === 'complete'
})

// 快捷数量选项
const quickQtyOptions = computed(() => {
  const max = maxPutawayQty.value
  const options = [1, 5, 10, 20, 50]
  return options.filter(qty => qty <= max)
})

// 表单引用
const formRef = ref<FormInstance>()
const locationScanInputRef = ref<InstanceType<typeof ElInput>>()

// 方法
const handleOpen = () => {
  if (currentDetail.value) {
    // 设置默认值
    if (mode.value === 'start' || mode.value === 'complete') {
      formData.putawayQty = remainingQty.value
    } else if (mode.value === 'adjust') {
      formData.putawayQty = currentDetail.value.completedQuantity || 0
    }
    
    formData.actualLocation = currentDetail.value.actualLocation || currentDetail.value.suggestedLocation || ''
    formData.remark = ''
  }
}

const handleClose = () => {
  resetForm()
}

const resetForm = () => {
  formData.putawayQty = 0
  formData.actualLocation = ''
  formData.remark = ''
  formRef.value?.clearValidate()
}

const increaseQty = () => {
  if (formData.putawayQty < maxPutawayQty.value) {
    formData.putawayQty++
  }
}

const decreaseQty = () => {
  if (formData.putawayQty > 0) {
    formData.putawayQty--
  }
}

const setQuickQty = (qty: number) => {
  formData.putawayQty = Math.min(qty, maxPutawayQty.value)
}

const handleScanLocation = () => {
  scannedLocation.value = ''
  scanLocationVisible.value = true
  nextTick(() => {
    locationScanInputRef.value?.focus()
  })
}

const handleLocationScanConfirm = () => {
  if (scannedLocation.value) {
    formData.actualLocation = scannedLocation.value
    scanLocationVisible.value = false
    ElMessage.success('库位扫描成功')
  }
}

const handleException = (type: string) => {
  const typeLabels: Record<string, string> = {
    'LOCATION_FULL': '库位已满',
    'DAMAGE': '货物损坏',
    'LOCATION_ERROR': '库位错误',
    'OTHER': '其他异常'
  }
  
  exceptionForm.type = typeLabels[type]
  exceptionForm.description = ''
  exceptionDialogVisible.value = true
}

const handleExceptionConfirm = () => {
  if (!exceptionForm.description) {
    ElMessage.warning('请输入异常描述')
    return
  }
  
  // 提交异常信息
  ElMessage.success('异常信息已提交')
  exceptionDialogVisible.value = false
  
  // 可以在这里调用异常处理的API
}

const handleConfirm = async () => {
  if (!formRef.value || !currentDetail.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    let resultCompletedQty = 0
    if (mode.value === 'adjust') {
      resultCompletedQty = formData.putawayQty
    } else {
      resultCompletedQty = (currentDetail.value.completedQuantity || 0) + formData.putawayQty
    }
    
    const resultData = {
      detailId: currentDetail.value.id,
      completedQuantity: resultCompletedQty,
      actualLocation: formData.actualLocation,
      remark: formData.remark,
      mode: mode.value
    }
    
    emit('confirm', resultData)
    dialogVisible.value = false
  } catch (error) {
    console.error('上架确认失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (detail: WmsPutawayTaskDetailResp, openMode: 'start' | 'complete' | 'adjust' = 'start') => {
  currentDetail.value = detail
  mode.value = openMode
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.mobile-putaway-dialog {
  padding: 8px 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.item-info-section,
.quantity-section,
.putaway-section,
.exception-section {
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.quantity-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.quantity-item {
  text-align: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.quantity-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.quantity-value {
  font-size: 20px;
  font-weight: 600;
}

.quantity-value.required {
  color: #409eff;
}

.quantity-value.completed {
  color: #67c23a;
}

.quantity-value.remaining {
  color: #e6a23c;
}

.quantity-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.qty-input {
  flex: 1;
}

.quick-qty-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.location-input-group {
  display: flex;
  gap: 8px;
}

.location-input-group .el-input {
  flex: 1;
}

.exception-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

.dialog-footer .el-button {
  flex: 1;
}

.scan-location-container {
  text-align: center;
}

.scan-tips {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
}

.scan-tips p {
  margin: 8px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .quantity-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .quantity-item {
    padding: 8px;
  }
  
  .quantity-value {
    font-size: 18px;
  }
  
  .exception-buttons {
    grid-template-columns: 1fr;
  }
}
</style>
