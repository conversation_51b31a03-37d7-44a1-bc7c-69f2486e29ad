package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
)

// WmsInventoryAlertController 定义库存预警控制器接口
type WmsInventoryAlertController interface {
	// 预警规则管理
	CreateRule(ctx iris.Context)
	UpdateRule(ctx iris.Context)
	DeleteRule(ctx iris.Context)
	GetRule(ctx iris.Context)
	GetRulePage(ctx iris.Context)

	// 预警日志管理
	GetAlertLog(ctx iris.Context)
	GetAlertLogPage(ctx iris.Context)
	AcknowledgeAlert(ctx iris.Context)
	ResolveAlert(ctx iris.Context)

	// 预警检查
	CheckAlerts(ctx iris.Context)
	CheckLowStock(ctx iris.Context)
	CheckExpiring(ctx iris.Context)
	CheckZeroStock(ctx iris.Context)

	// 批量操作
	BatchAcknowledge(ctx iris.Context)
	BatchResolve(ctx iris.Context)
	BatchActivateRules(ctx iris.Context)
	BatchDeactivateRules(ctx iris.Context)

	// 统计分析
	GetAlertStats(ctx iris.Context)
	GetRuleStats(ctx iris.Context)

	// 预警通知
	SendAlertNotification(ctx iris.Context)
	GetAlertNotificationHistory(ctx iris.Context)
}

// wmsInventoryAlertControllerImpl 库存预警控制器实现
type WmsInventoryAlertControllerImpl struct {
	BaseControllerImpl
	inventoryAlertService service.WmsInventoryAlertService
}

// NewWmsInventoryAlertControllerImpl 创建库存预警控制器实例
func NewWmsInventoryAlertControllerImpl(cm *ControllerManager) *WmsInventoryAlertControllerImpl {
	return &WmsInventoryAlertControllerImpl{
		BaseControllerImpl:    *NewBaseController(cm),
		inventoryAlertService: cm.GetServiceManager().GetWmsInventoryAlertService(),
	}
}

// CreateRule 创建预警规则
func (c *WmsInventoryAlertControllerImpl) CreateRule(ctx iris.Context) {
	var req dto.WmsInventoryAlertRuleCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "请求参数格式错误")
		return
	}

	result, err := c.inventoryAlertService.CreateRule(ctx.Request().Context(), &req)
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// UpdateRule 更新预警规则
func (c *WmsInventoryAlertControllerImpl) UpdateRule(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "无效的ID参数")
		return
	}

	var req dto.WmsInventoryAlertRuleUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "请求参数格式错误")
		return
	}

	result, err := c.inventoryAlertService.UpdateRule(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// DeleteRule 删除预警规则
func (c *WmsInventoryAlertControllerImpl) DeleteRule(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "无效的ID参数")
		return
	}

	err = c.inventoryAlertService.DeleteRule(ctx.Request().Context(), uint(id))
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// GetRule 根据ID获取预警规则
func (c *WmsInventoryAlertControllerImpl) GetRule(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "无效的ID参数")
		return
	}

	result, err := c.inventoryAlertService.GetRule(ctx.Request().Context(), uint(id))
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// GetRulePage 分页查询预警规则
func (c *WmsInventoryAlertControllerImpl) GetRulePage(ctx iris.Context) {
	var req dto.WmsInventoryAlertRuleQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryAlertService.GetRulePage(ctx.Request().Context(), &req)
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// GetAlertLog 根据ID获取预警日志
func (c *WmsInventoryAlertControllerImpl) GetAlertLog(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "无效的ID参数")
		return
	}

	result, err := c.inventoryAlertService.GetAlertLog(ctx.Request().Context(), uint(id))
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// GetAlertLogPage 分页查询预警日志
func (c *WmsInventoryAlertControllerImpl) GetAlertLogPage(ctx iris.Context) {
	var req dto.WmsInventoryAlertLogQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryAlertService.GetAlertLogPage(ctx.Request().Context(), &req)
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// AcknowledgeAlert 确认预警
func (c *WmsInventoryAlertControllerImpl) AcknowledgeAlert(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "无效的ID参数")
		return
	}

	acknowledgedByStr := ctx.PostValue("acknowledgedBy")
	acknowledgedBy, err := strconv.ParseUint(acknowledgedByStr, 10, 32)
	if err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "无效的确认人ID参数")
		return
	}

	err = c.inventoryAlertService.AcknowledgeAlert(ctx.Request().Context(), uint(id), uint(acknowledgedBy))
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// ResolveAlert 解决预警
func (c *WmsInventoryAlertControllerImpl) ResolveAlert(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.Fail(ctx, constant.PARAM_ERROR, "无效的ID参数")
		return
	}

	err = c.inventoryAlertService.ResolveAlert(ctx.Request().Context(), uint(id))
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, nil)
}

// CheckAlerts 检查所有预警
func (c *WmsInventoryAlertControllerImpl) CheckAlerts(ctx iris.Context) {
	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			c.Fail(ctx, constant.PARAM_ERROR, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	result, err := c.inventoryAlertService.CheckAlerts(ctx.Request().Context(), warehouseId)
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// CheckLowStock 检查低库存预警
func (c *WmsInventoryAlertControllerImpl) CheckLowStock(ctx iris.Context) {
	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			c.Fail(ctx, constant.PARAM_ERROR, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	result, err := c.inventoryAlertService.CheckLowStock(ctx.Request().Context(), warehouseId)
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}

// CheckExpiring 检查即将过期预警
func (c *WmsInventoryAlertControllerImpl) CheckExpiring(ctx iris.Context) {
	daysStr := ctx.URLParam("days")
	days := 30 // 默认30天
	if daysStr != "" {
		d, err := strconv.Atoi(daysStr)
		if err != nil {
			c.Fail(ctx, constant.PARAM_ERROR, "无效的天数参数")
			return
		}
		days = d
	}

	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			c.Fail(ctx, constant.PARAM_ERROR, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	result, err := c.inventoryAlertService.CheckExpiring(ctx.Request().Context(), days, warehouseId)
	if err != nil {
		c.FailWithError(ctx, err)
		return
	}

	c.Success(ctx, result)
}
