<template>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      destroy-on-close
      @close="handleClose"
    >
      <el-form
        ref="formRef"
        :model="formModel"
        :rules="formRules"
        label-width="120px"
        size="default"
      >
        <el-row :gutter="20">
          <!-- 基础配置 -->
          <el-col :span="24">
            <div class="form-section">
              <h4 class="section-title">基础配置</h4>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="配置层级" prop="configLevel" required>
                    <el-select
                      v-model="formModel.configLevel"
                      placeholder="请选择配置层级"
                      @change="handleConfigLevelChange"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="option in configLevelOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                        :disabled="!hasConfigLevelPermission(option.value)"
                      >
                        <div class="level-option">
                          <span class="level-name">{{ option.label }}</span>
                          <el-tag
                            :type="getConfigLevelTagType(option.value)"
                            size="small"
                            effect="plain"
                          >
                            优先级 {{ option.priority }}
                          </el-tag>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                
                <el-col :span="12">
                  <el-form-item
                    label="配置目标"
                    prop="configTargetId"
                    :required="formModel.configLevel !== 'SYSTEM'"
                  >
                    <el-select
                      v-model="formModel.configTargetId"
                      placeholder="请选择配置目标"
                      filterable
                      remote
                      :remote-method="searchConfigTargets"
                      :loading="targetLoading"
                      style="width: 100%"
                      :disabled="!formModel.configLevel || formModel.configLevel === 'SYSTEM'"
                    >
                      <el-option
                        v-for="target in configTargetOptions"
                        :key="target.id"
                        :label="target.name"
                        :value="target.id"
                      >
                        <div class="target-option">
                          <span class="target-name">{{ target.name }}</span>
                          <span class="target-code" v-if="target.code">
                            ({{ target.code }})
                          </span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="盲收策略" prop="strategy" required>
                    <el-select
                      v-model="formModel.strategy"
                      placeholder="请选择盲收策略"
                      @change="handleStrategyChange"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="option in strategyOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      >
                        <div class="strategy-option">
                          <div class="strategy-header">
                            <span class="strategy-name">{{ option.label }}</span>
                            <el-tag
                              :type="option.color"
                              size="small"
                              effect="plain"
                            >
                              {{ option.value }}
                            </el-tag>
                          </div>
                          <div class="strategy-desc">{{ option.description }}</div>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                
                <el-col :span="12">
                  <el-form-item label="优先级" prop="priority">
                    <el-input-number
                      v-model="formModel.priority"
                      :min="1"
                      :max="10"
                      placeholder="请输入优先级"
                      style="width: 100%"
                    />
                    <div class="field-help">
                      数值越小优先级越高，建议范围1-10
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
  
          <!-- 策略配置 -->
          <el-col :span="24">
            <div class="form-section">
              <h4 class="section-title">策略配置</h4>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="数量限制">
                    <el-input-number
                      v-model="formModel.maxBlindReceivingQty"
                      :min="0"
                      :precision="3"
                      placeholder="最大盲收数量"
                      style="width: 100%"
                    />
                    <div class="field-help">
                      留空表示不限制数量
                    </div>
                  </el-form-item>
                </el-col>
                
                <el-col :span="12" v-if="formModel.strategy === 'SUPPLEMENT'">
                  <el-form-item label="补录时限" prop="supplementTimeLimit">
                    <el-input-number
                      v-model="formModel.supplementTimeLimit"
                      :min="1"
                      :max="720"
                      placeholder="补录时限(小时)"
                      style="width: 100%"
                    />
                    <div class="field-help">
                      超过时限未补录将标记为逾期
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
  
          <!-- 审批配置 -->
          <el-col :span="24">
            <div class="form-section">
              <h4 class="section-title">审批配置</h4>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="审批要求">
                    <el-switch
                      v-model="formModel.requiresApproval"
                      active-text="需要审批"
                      inactive-text="无需审批"
                    />
                  </el-form-item>
                </el-col>
                
                <el-col :span="12" v-if="formModel.requiresApproval">
                  <el-form-item label="审批角色" prop="approvalUserRoles">
                    <el-select
                      v-model="formModel.approvalUserRoles"
                      multiple
                      placeholder="请选择审批角色"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="role in roleOptions"
                        :key="role.code"
                        :label="role.name"
                        :value="role.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
  
          <!-- 其他配置 -->
          <el-col :span="24">
            <div class="form-section">
              <h4 class="section-title">其他配置</h4>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="启用状态">
                    <el-switch
                      v-model="formModel.isActive"
                      active-text="启用"
                      inactive-text="禁用"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="备注">
                    <el-input
                      v-model="formModel.remark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注信息"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
  
        <!-- 配置预览 -->
        <el-row :gutter="20" v-if="formModel.configLevel">
          <el-col :span="24">
            <div class="form-section">
              <h4 class="section-title">配置预览</h4>
              <div class="config-preview">
                <ConfigPreview :config-data="formModel" />
              </div>
            </div>
          </el-col>
        </el-row>
  
        <!-- 冲突检测 -->
        <el-row :gutter="20" v-if="conflictCheckResult">
          <el-col :span="24">
            <div class="form-section">
              <h4 class="section-title">
                冲突检测
                <el-tag
                  :type="conflictCheckResult.hasConflict ? 'danger' : 'success'"
                  size="small"
                  effect="plain"
                >
                  {{ conflictCheckResult.hasConflict ? '存在冲突' : '无冲突' }}
                </el-tag>
              </h4>
              <ConflictCheckResult :result="conflictCheckResult" />
            </div>
          </el-col>
        </el-row>
      </el-form>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="handleCheckConflict" :loading="conflictCheckLoading">
            检测冲突
          </el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import ConfigPreview from './ConfigPreview.vue'
  import ConflictCheckResult from './ConflictCheckResult.vue'
  import { usePermission } from '@/hooks/usePermission'
  import {
    createBlindReceivingConfig,
    updateBlindReceivingConfig,
    checkBlindReceivingConfigConflict,
    getAvailableConfigTargets,
    getRoleListForConfig
  } from '@/api/wms/blindReceivingConfig'
  import type {
    BlindReceivingConfigVO,
    BlindReceivingConfigCreateReq,
    BlindReceivingConfigUpdateReq,
    BlindReceivingConfigFormModel,
    ConfigLevel,
    BlindReceivingStrategy,
    AvailableTargetVO
  } from '@/types/wms/blindReceivingConfig'
  import {
    CONFIG_LEVEL_OPTIONS,
    STRATEGY_OPTIONS,
    DEFAULT_CONFIG
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    modelValue: boolean
    configData?: BlindReceivingConfigVO | null
    isEdit?: boolean
  }
  
  const props = withDefaults(defineProps<Props>(), {
    configData: null,
    isEdit: false
  })
  
  // Emits
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()
  
  // 权限
  const { hasPermission } = usePermission()
  
  // 响应式数据
  const formRef = ref()
  const submitLoading = ref(false)
  const conflictCheckLoading = ref(false)
  const targetLoading = ref(false)
  
  // 表单模型
  const formModel = reactive<BlindReceivingConfigFormModel>({
    configLevel: 'SYSTEM',
    strategy: 'STRICT',
    requiresApproval: false,
    isActive: true,
    priority: DEFAULT_CONFIG.DEFAULT_PRIORITY,
    approvalUserRoles: []
  })
  
  // 选项数据
  const configTargetOptions = ref<AvailableTargetVO[]>([])
  const roleOptions = ref<Array<{ code: string; name: string; description?: string }>>([])
  const conflictCheckResult = ref<any>(null)
  
  // 计算属性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })
  
  const dialogTitle = computed(() => {
    return props.isEdit ? '编辑盲收配置' : '新增盲收配置'
  })
  
  const configLevelOptions = computed(() => CONFIG_LEVEL_OPTIONS)
  const strategyOptions = computed(() => STRATEGY_OPTIONS)
  
  // 表单验证规则
  const formRules = computed(() => ({
    configLevel: [
      { required: true, message: '请选择配置层级', trigger: 'change' }
    ],
    configTargetId: [
      {
        required: formModel.configLevel !== 'SYSTEM',
        message: '请选择配置目标',
        trigger: 'change'
      }
    ],
    strategy: [
      { required: true, message: '请选择盲收策略', trigger: 'change' }
    ],
    supplementTimeLimit: [
      {
        required: formModel.strategy === 'SUPPLEMENT',
        message: '请输入补录时限',
        trigger: 'blur'
      },
      {
        type: 'number',
        min: 1,
        max: 720,
        message: '补录时限必须在1-720小时之间',
        trigger: 'blur'
      }
    ],
    approvalUserRoles: [
      {
        required: formModel.requiresApproval,
        message: '请选择审批角色',
        trigger: 'change'
      }
    ],
    priority: [
      {
        type: 'number',
        min: 1,
        max: 10,
        message: '优先级必须在1-10之间',
        trigger: 'blur'
      }
    ]
  }))
  
  // 工具函数
  const getConfigLevelTagType = (level: ConfigLevel) => {
    const typeMap = {
      'SYSTEM': 'info',
      'WAREHOUSE': 'warning',
      'CLIENT': 'success',
      'USER': 'primary'
    }
    return typeMap[level] || 'info'
  }
  
  const hasConfigLevelPermission = (level: ConfigLevel) => {
    const permissionMap = {
      'SYSTEM': 'wms:blind-config:system',
      'WAREHOUSE': 'wms:blind-config:warehouse',
      'CLIENT': 'wms:blind-config:client',
      'USER': 'wms:blind-config:user'
    }
    return hasPermission(permissionMap[level])
  }
  
  // 事件处理
  const handleConfigLevelChange = (level: ConfigLevel) => {
    formModel.configTargetId = undefined
    configTargetOptions.value = []
    conflictCheckResult.value = null
    
    if (level !== 'SYSTEM') {
      loadConfigTargets(level)
    }
  }
  
  const handleStrategyChange = (strategy: BlindReceivingStrategy) => {
    if (strategy !== 'SUPPLEMENT') {
      formModel.supplementTimeLimit = undefined
    } else {
      formModel.supplementTimeLimit = DEFAULT_CONFIG.SUPPLEMENT_TIME_LIMIT
    }
    conflictCheckResult.value = null
  }
  
  const searchConfigTargets = (query: string) => {
    if (query && formModel.configLevel && formModel.configLevel !== 'SYSTEM') {
      loadConfigTargets(formModel.configLevel, query)
    }
  }
  
  const loadConfigTargets = async (level: ConfigLevel, keyword?: string) => {
    try {
      targetLoading.value = true
      const result = await getAvailableConfigTargets({
        configLevel: level,
        keyword,
        pageSize: 50
      })
      configTargetOptions.value = result.list
    } catch (error) {
      console.error('加载配置目标失败:', error)
      ElMessage.error('加载配置目标失败')
    } finally {
      targetLoading.value = false
    }
  }
  
  const loadRoleOptions = async () => {
    try {
      const result = await getRoleListForConfig()
      roleOptions.value = result
    } catch (error) {
      console.error('加载角色选项失败:', error)
    }
  }
  
  const handleCheckConflict = async () => {
    try {
      await formRef.value?.validate()
      
      conflictCheckLoading.value = true
      const createReq: BlindReceivingConfigCreateReq = {
        configLevel: formModel.configLevel,
        configTargetId: formModel.configTargetId,
        strategy: formModel.strategy,
        supplementTimeLimit: formModel.supplementTimeLimit,
        requiresApproval: formModel.requiresApproval,
        approvalUserRoles: formModel.approvalUserRoles?.join(','),
        maxBlindReceivingQty: formModel.maxBlindReceivingQty,
        isActive: formModel.isActive,
        priority: formModel.priority,
        remark: formModel.remark
      }
      
      const result = await checkBlindReceivingConfigConflict(createReq)
      conflictCheckResult.value = result
      
      if (result.hasConflict) {
        ElMessage.warning('检测到配置冲突，请检查')
      } else {
        ElMessage.success('配置无冲突')
      }
    } catch (error) {
      console.error('冲突检测失败:', error)
      ElMessage.error('冲突检测失败')
    } finally {
      conflictCheckLoading.value = false
    }
  }
  
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate()
      
      // 如果存在冲突，需要用户确认
      if (conflictCheckResult.value?.hasConflict) {
        await ElMessageBox.confirm(
          '检测到配置冲突，确定要继续保存吗？',
          '配置冲突确认',
          {
            type: 'warning'
          }
        )
      }
      
      submitLoading.value = true
      
      const requestData = {
        configLevel: formModel.configLevel,
        configTargetId: formModel.configTargetId,
        strategy: formModel.strategy,
        supplementTimeLimit: formModel.supplementTimeLimit,
        requiresApproval: formModel.requiresApproval,
        approvalUserRoles: formModel.approvalUserRoles?.join(','),
        maxBlindReceivingQty: formModel.maxBlindReceivingQty,
        isActive: formModel.isActive,
        priority: formModel.priority,
        remark: formModel.remark
      }
      
      if (props.isEdit && props.configData) {
        await updateBlindReceivingConfig(props.configData.id, requestData as BlindReceivingConfigUpdateReq)
        ElMessage.success('配置更新成功')
      } else {
        await createBlindReceivingConfig(requestData as BlindReceivingConfigCreateReq)
        ElMessage.success('配置创建成功')
      }
      
      emit('success')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      }
    } finally {
      submitLoading.value = false
    }
  }
  
  const handleClose = () => {
    emit('update:modelValue', false)
  }
  
  const resetForm = () => {
    Object.assign(formModel, {
      configLevel: 'SYSTEM',
      configTargetId: undefined,
      strategy: 'STRICT',
      supplementTimeLimit: undefined,
      requiresApproval: false,
      approvalUserRoles: [],
      maxBlindReceivingQty: undefined,
      isActive: true,
      priority: DEFAULT_CONFIG.DEFAULT_PRIORITY,
      remark: ''
    })
    configTargetOptions.value = []
    conflictCheckResult.value = null
  }
  
  const initFormData = () => {
    if (props.configData) {
      Object.assign(formModel, {
        ...props.configData,
        approvalUserRoles: props.configData.approvalUserRoles?.split(',') || []
      })
      
      if (formModel.configLevel !== 'SYSTEM') {
        loadConfigTargets(formModel.configLevel)
      }
    } else {
      resetForm()
    }
  }
  
  // 监听器
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal) {
        initFormData()
        loadRoleOptions()
      }
    }
  )
  </script>
  
  <style scoped lang="scss">
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      margin: 0 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .level-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .level-name {
      font-weight: 500;
    }
  }
  
  .target-option {
    display: flex;
    align-items: center;
    gap: 5px;
    
    .target-name {
      font-weight: 500;
    }
    
    .target-code {
      color: #909399;
      font-size: 12px;
    }
  }
  
  .strategy-option {
    .strategy-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2px;
      
      .strategy-name {
        font-weight: 500;
      }
    }
    
    .strategy-desc {
      color: #909399;
      font-size: 12px;
    }
  }
  
  .field-help {
    color: #909399;
    font-size: 12px;
    margin-top: 2px;
  }
  
  .config-preview {
    background-color: #f8f9fa;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
  }
  
  :deep(.el-select-dropdown__item) {
    height: auto;
    padding: 8px 20px;
    line-height: 1.4;
  }
  </style>