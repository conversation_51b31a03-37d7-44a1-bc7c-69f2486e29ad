<template>
  <div class="vn-login-container">
    <!-- ADD: Left Panel -->
    <div 
      v-if="showLeftPanel" 
      class="left-panel"
      :style="leftPanelStyle"
    >
        <slot name="left-panel">
            <!-- Default Left Panel Content -->
            <div class="left-panel-content">
                <div class="left-panel-header">
                    <slot name="left-panel-header">
                        <img v-if="logoUrl" :src="logoUrl" :alt="logoAlt" class="logo" />
                        <h1 class="system-title">{{ systemTitle }}</h1>
                    </slot>
                </div>
                <div class="left-panel-body">
                    <slot name="left-panel-body">
                        <!-- Optional: Add more content here via slot -->
                        <p class="slogan">欢迎使用本系统</p>
                    </slot>
                </div>
                <div class="left-panel-footer">
                    <slot name="left-panel-footer">
                        <span v-if="version" class="version">版本: {{ version }}</span>
                    </slot>
                </div>
            </div>
             <!-- Optional: Add overlay for better text contrast -->
             <div class="left-panel-overlay"></div>
        </slot>
    </div>

    <!-- Login Form Card -->
    <el-card class="login-card" shadow="always">
      <template #header>
        <div class="login-header">
          <slot name="header">
            <!-- Default header: Replace with logo or custom content via slot -->
            <h2>系统登陆</h2>
          </slot>
        </div>
      </template>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="formRules"
        label-position="top"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            :prefix-icon="User"
            size="large"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            placeholder="密码"
            :prefix-icon="Lock"
            type="password"
            size="large"
            clearable
            show-password
            @keyup.enter="handleLogin"
          >
          </el-input>
        </el-form-item>

        <el-form-item v-if="showCaptcha" prop="captchaCode" class="captcha-item">
            <el-input
                v-model="loginForm.captchaCode"
                placeholder="验证码"
                :prefix-icon="Key"
                size="large"
                class="captcha-input"
                @keyup.enter="handleLogin"
            />
            <img 
                v-if="captchaImageUrl" 
                :src="captchaImageUrl" 
                alt="验证码"
                class="captcha-image"
                @click="handleRefreshCaptcha"
                title="点击刷新"
            />
            <el-icon v-else class="captcha-placeholder" @click="handleRefreshCaptcha" title="加载验证码">
                <Loading />
            </el-icon>
        </el-form-item>

        <div class="login-options">
           <el-checkbox v-if="showRememberMe" v-model="loginForm.rememberMe" label="记住我" />
            <el-link v-if="showForgotPassword" type="primary" :underline="false" @click="handleForgotPassword">
              忘记密码?
            </el-link>
        </div>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            class="login-button"
            size="large"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登 录' }}
          </el-button>
        </el-form-item>

        <div 
            v-if="showAlternativeLogins && $slots['alternative-logins']" 
            class="alternative-logins-container"
        >
             <el-divider>其他登录方式</el-divider>
             <div class="alternative-logins-content">
                 <slot name="alternative-logins"></slot>
            </div>
        </div>

      </el-form>
      
      <div class="login-footer">
          <slot name="footer">
              <!-- Add footer content like copyright or links via slot -->
          </slot>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, withDefaults, computed, watch } from 'vue';
import { 
    ElCard, ElForm, ElFormItem, ElInput, ElButton, ElCheckbox, ElLink, ElIcon, 
    type FormInstance, type FormRules, ElDivider
} from 'element-plus';
import { User, Lock, Key, Loading } from '@element-plus/icons-vue';
import type { VNLoginProps, VNLoginEmits, LoginFormModel } from './types';
import type { CSSProperties } from 'vue'; // Import CSSProperties

// --- Props and Emits ---
const props = withDefaults(defineProps<VNLoginProps>(), {
    initialUsername: '',
    loading: false,
    rules: undefined,
    showRememberMe: true,
    showForgotPassword: false,
    showCaptcha: false, 
    captchaImageUrl: '',
    showAlternativeLogins: false, 
    // ADDED defaults for left panel
    showLeftPanel: true, 
    leftPanelBackgroundImageUrl: 'https://picsum.photos/seed/loginbg/800/600', // Example default image
    systemTitle: '后台管理系统',
    version: '1.0.0', 
    logoUrl: '', // No default logo
    logoAlt: 'Logo',
});

const emit = defineEmits<VNLoginEmits>();

// --- Refs and State ---
const loginFormRef = ref<FormInstance>();
const loginForm = reactive<LoginFormModel>({
  username: props.initialUsername,
  password: '',
  rememberMe: false,
  captchaCode: '',
});

// --- Validation Rules ---
const defaultRules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
  captchaCode: [
      { 
          required: props.showCaptcha, 
          message: '请输入验证码', 
          trigger: 'blur' 
      }
  ]
});

const formRules = computed(() => {
    const baseRules = props.rules || defaultRules;
    if (props.showCaptcha && !baseRules['captchaCode']) {
        return { ...baseRules, captchaCode: defaultRules['captchaCode'] };
    } else if (baseRules['captchaCode'] && Array.isArray(baseRules['captchaCode'])) {
        const captchaRule = baseRules['captchaCode'][0] || {};
        captchaRule.required = props.showCaptcha;
    }
    return baseRules;
});

// --- Methods ---
const handleLogin = async () => {
  if (!loginFormRef.value || props.loading) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (valid) {
      // Emit login event with form data
      emit('login', { ...loginForm });
    }
  } catch (error) {
    // Validation failed, Form instance might throw
    console.error('Login form validation failed:', error);
  }
};

const handleForgotPassword = () => {
    emit('forgot-password');
};

const handleRefreshCaptcha = () => {
    console.log('Requesting captcha refresh...');
    emit('refresh-captcha');
};

watch(() => props.showCaptcha, (newVal) => {
    if (!newVal) {
        loginForm.captchaCode = '';
        loginFormRef.value?.clearValidate('captchaCode'); 
    }
    const rules = formRules.value; 
    if (rules['captchaCode'] && Array.isArray(rules['captchaCode'])) {
        const captchaRule = rules['captchaCode'][0] || {};
        captchaRule.required = newVal;
    }
});

// ADD: Computed style for left panel background
const leftPanelStyle = computed<CSSProperties>(() => ({
    backgroundImage: props.leftPanelBackgroundImageUrl 
        ? `url(${props.leftPanelBackgroundImageUrl})` 
        : 'none',
}));

</script>

<style scoped>
.vn-login-container {
  display: flex;
  /* justify-content: center; REMOVE - will be handled by flex distribution */
  /* align-items: center; REMOVE - allow items to stretch */
  min-height: 100vh;
  /* ADD Background here */
  background: linear-gradient(to right, #f8f9fa, #e9ecef); /* Example light gray gradient background */
  /* background-color: #f0f2f5; /* Fallback background */
}

/* --- Left Panel Styles --- */
.left-panel {
    width: 45%; /* Adjust width as needed */
    min-height: 100vh;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    box-sizing: border-box;
    color: #fff; /* Default text color for left panel */
}

.left-panel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3); /* Adjust overlay darkness */
    z-index: 1;
}

.left-panel-content {
    position: relative; /* Above overlay */
    z-index: 2;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Push header/footer to edges */
    height: 100%; 
}

.left-panel-header .logo {
    max-width: 150px;
    max-height: 60px;
    margin-bottom: 15px;
}

.left-panel-header .system-title {
    font-size: 2.5em;
    font-weight: bold;
    margin: 0;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.left-panel-body .slogan {
    font-size: 1.2em;
    margin: 20px 0;
}

.left-panel-footer .version {
    font-size: 0.9em;
    opacity: 0.8;
}

/* --- Login Card Styles --- */
.login-card {
  width: 55%; /* Adjust width as needed */
  max-width: none; /* Remove max-width */
  min-height: 100vh; /* Stretch to full height */
  border: none; /* Remove default card border */
  border-radius: 0; /* Remove border radius */
  display: flex;
  flex-direction: column;
  justify-content: center; /* Center form vertically */
  padding: 0 5%; /* Add horizontal padding */
  box-sizing: border-box;
}
/* Adjust card header/body padding if needed via :deep */
.login-card :deep(.el-card__header) {
    border-bottom: none; /* Remove header border */
    padding-top: 40px; /* Add some top padding */
}
.login-card :deep(.el-card__body) {
     padding-bottom: 40px; /* Add some bottom padding */
     width: 100%;
     max-width: 450px; /* Limit form width inside the card */
     margin: 0 auto; /* Center the form content horizontally */
}

/* --- Responsive --- */
@media (max-width: 992px) { /* Example breakpoint for tablet */
    .left-panel {
        width: 35%;
    }
    .login-card {
        width: 65%;
    }
}

@media (max-width: 767px) { /* Example breakpoint for mobile */
    .vn-login-container {
        flex-direction: column;
    }
    .left-panel {
        display: none; /* Hide left panel on mobile */
        /* OR stack on top: */
        /* width: 100%; */
        /* min-height: 200px; */ 
        /* height: 30vh; */
    }
    .login-card {
        width: 100%;
        min-height: auto; /* Reset min-height */
        padding: 20px; /* Adjust padding for mobile */
        justify-content: flex-start; /* Align form to top */
    }
    .login-card :deep(.el-card__body) {
         max-width: none; /* Remove max-width limit */
         padding: 20px 0; /* Adjust body padding */
    }
}

.login-header {
  text-align: center;
}

.login-header h2 {
    margin: 0;
    font-size: 1.5em;
    color: #303133;
}

.el-form-item {
    margin-bottom: 22px;
}

.login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 22px; 
    font-size: 14px;
}

.login-options .el-checkbox {
    margin-right: auto; /* Push forgot password link to right */
    height: auto; /* Override potential fixed height */
}

.login-options .el-link {
    font-size: 14px;
}

.login-button {
  width: 100%;
}

/* Style the password visibility toggle */
.el-input__append .el-icon {
  cursor: pointer;
}

.login-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 12px;
    color: #909399;
}

/* CHANGE: Use Grid layout for the inner content wrapper */
.captcha-item :deep(.el-form-item__content) {
    display: grid; /* USE GRID */
    /* Input takes free space | Image/Placeholder */
    grid-template-columns: 1fr auto; /* REMOVED third column */
    gap: 10px; /* Space between columns */
    align-items: center; /* Vertically align items in the row */
    width: 100%; 
}

/* Remove flex styles from input if they exist */
.captcha-input {
    /* flex-grow: 1; REMOVE if exists */
    min-width: 100px; 
}

/* Other captcha styles remain largely the same */
.captcha-image,
.captcha-placeholder {
    flex-shrink: 0; /* Still useful to prevent shrinking within grid cell */
    vertical-align: middle; 
}

.captcha-image {
    height: 40px; 
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid var(--el-border-color);
}

.captcha-placeholder {
    height: 40px;
    width: 100px; 
    border: 1px dashed var(--el-border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.alternative-logins-container {
    margin-top: 20px;
}
.alternative-logins-content {
    margin-top: 15px;
    display: flex;
    justify-content: center; 
    gap: 20px; 
    align-items: center;
}

:slotted(.alternative-login-icon) {
    font-size: 24px;
    color: #606266;
    cursor: pointer;
    transition: color 0.2s;
}
:slotted(.alternative-login-icon:hover) {
    color: var(--el-color-primary);
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) { 
  .vn-login-container {
    /* On small screens, make the container itself the centering flexbox */
    display: flex; 
    justify-content: center;
    align-items: center;
    /* background-image: none; REMOVE - Keep the background */
    /* padding: 0; REMOVE - padding might be needed */
    padding: 20px; /* Add padding for spacing */
    box-sizing: border-box;
  }

  .left-panel {
    /* Hide the left panel on small screens */
    display: none;
  }

  .login-card {
    /* Ensure the card takes appropriate width */
    width: 90%;      /* Use percentage width */
    max-width: 400px; /* Limit maximum width */
    /* margin: 0 auto;   REMOVE - Centered by parent flex container now */
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Slightly stronger shadow for emphasis */
     border: none; /* Remove border if ElCard adds one */
     /* ADD: Reset min-height if it was set for large screens */
     min-height: auto; 
  }

}

</style> 