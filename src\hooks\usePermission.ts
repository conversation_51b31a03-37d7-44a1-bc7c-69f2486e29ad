import { useUserStore } from '@/store/modules/user';

/**
 * 检查当前用户是否拥有指定权限
 * @param permission 需要检查的权限字符串，例如 'system:user:add'
 * @returns 如果拥有权限则返回 true，否则返回 false
 */
export function hasPermission(permission: string): boolean {
  const userStore = useUserStore();

  // 检查 store 中是否有权限数据
  const permissions = userStore.permissions;
  if (!permissions || permissions.length === 0) {
    // console.warn('权限列表为空或未加载');
    return false; // 如果权限列表为空，默认无权限
  }

  // 检查是否是管理员，管理员拥有所有权限
  // 注意：userStore.userInfo 可能为 null，需要检查
  if (userStore.userInfo?.isAdmin) {
    return true;
  }

  // 检查具体权限是否存在
  // 通常权限标识符包含 '*' 代表所有权限，但这里我们先做精确匹配
  // 如果需要支持通配符，例如 'system:user:*', 需要更复杂的逻辑
  return permissions.includes(permission);
} 