package service

import (
	"context"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsLocationService 定义库位服务接口
type WmsLocationService interface {
	BaseService
	Create(ctx context.Context, req *dto.WmsLocationCreateReq) (*vo.WmsLocationVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsLocationUpdateReq) (*vo.WmsLocationVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsLocationVO, error)
	GetPage(ctx context.Context, req *dto.WmsLocationQueryReq) (*vo.PageResult[vo.WmsLocationVO], error)
	GetWarehouses(ctx context.Context) ([]*vo.WmsWarehouseSimpleVO, error)
	GetLocationTree(ctx context.Context, warehouseID uint) ([]*vo.WmsLocationTreeVO, error)
}

// wmsLocationServiceImpl 库位服务实现
type wmsLocationServiceImpl struct {
	BaseServiceImpl
}

// NewWmsLocationService 创建库位服务
func NewWmsLocationService(sm *ServiceManager) WmsLocationService {
	return &wmsLocationServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建库位
func (s *wmsLocationServiceImpl) Create(ctx context.Context, req *dto.WmsLocationCreateReq) (*vo.WmsLocationVO, error) {
	var location *entity.WmsLocation
	var voResult *vo.WmsLocationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsLocationRepository()

		// 最大重试次数（编码生成失败时）
		const maxRetries = 3
		var lastErr error

		for attempt := 0; attempt < maxRetries; attempt++ {
			// 处理库位编码生成
			locationCode := req.Code
			if locationCode == "" || locationCode == "AUTO" {
				// 自动生成库位编码
				codeGenService := s.GetServiceManager().GetCodeGenerationService()
				if codeGenService != nil {
					contextData := map[string]interface{}{
						"name": req.Name,
						"type": string(req.Type),
					}
					if req.ParentID != nil {
						contextData["parentId"] = *req.ParentID
					}

					generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
						BusinessType: "LOCATION",
						ContextData:  contextData,
					})
					if err != nil {
						return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "库位编码自动生成失败。可能原因：1) 缺少库位编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
					}
					locationCode = generatedCode.GeneratedCode
				} else {
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "库位编码生成服务暂时不可用，请手动输入编码或稍后重试自动生成。")
				}
			}

			location = &entity.WmsLocation{}
			copier.Copy(location, req)

			// 重要：设置生成的编码，防止被copier.Copy覆盖
			location.Code = locationCode

			// 强制从上下文获取账套ID并设置到实体中
			accountBookID, err := s.GetAccountBookIDFromContext(ctx)
			if err != nil {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息，仓库和账套是强绑定的").WithCause(err)
			}
			if accountBookID == 0 {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空，仓库和账套是强绑定的")
			}
			location.AccountBookID = uint(accountBookID)

			if req.ParentID != nil && *req.ParentID > 0 {
				parent, err := repo.FindByID(ctx, *req.ParentID)
				if err != nil {
					if err == gorm.ErrRecordNotFound {
						return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "父级库位不存在")
					}
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询父级库位失败").WithCause(err)
				}
				location.WarehouseID = parent.WarehouseID
			} else {
				if location.Type != entity.LocationTypeWarehouse {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有类型为'WAREHOUSE'的库位才能作为顶级库位")
				}
			}

			// 尝试创建库位
			err = repo.Create(ctx, location)
			if err != nil {
				// 检查是否为重复键错误
				if apperrors.IsDuplicateKeyError(err) {
					s.GetServiceManager().GetLogger().Warn("库位编码重复，正在重试",
						s.GetServiceManager().GetLogger().WithField("locationCode", locationCode),
						s.GetServiceManager().GetLogger().WithField("attempt", attempt+1))

					lastErr = apperrors.NewDuplicateKeyError("库位编码已存在，正在重新生成")

					// 如果是自动生成的编码且发生重复，继续重试
					if req.Code == "" || req.Code == "AUTO" {
						continue
					} else {
						// 手动指定的编码重复，直接返回错误
						return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库位编码已存在")
					}
				} else {
					// 其他数据库错误
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建库位失败").WithCause(err)
				}
			} else {
				// 创建成功，处理特殊逻辑
				if location.ParentID == nil || *location.ParentID == 0 {
					location.WarehouseID = location.ID
					if err := repo.Update(ctx, location); err != nil {
						return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "回填仓库ID失败").WithCause(err)
					}
				}

				// 创建成功，跳出重试循环
				break
			}
		}

		// 检查是否因为重试失败
		if location.ID == 0 {
			if lastErr != nil {
				return lastErr
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建库位失败，已达到最大重试次数")
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, location)
	return voResult, nil
}

// Update 更新库位
func (s *wmsLocationServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsLocationUpdateReq) (*vo.WmsLocationVO, error) {
	var location *entity.WmsLocation
	var voResult *vo.WmsLocationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsLocationRepository()
		var err error

		location, err = repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库位不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库位失败").WithCause(err)
		}

		if req.Code != nil && *req.Code != location.Code {
			exist, err := repo.IsCodeExist(ctx, *req.Code, id)
			if err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "检查库位编码失败").WithCause(err)
			}
			if exist {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库位编码已存在")
			}
		}

		if req.ParentID != nil {
			if *req.ParentID == id {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "不能将自己设置为父级库位")
			}
			if *req.ParentID > 0 {
				// 验证父级库位是否存在（BaseRepository会自动过滤账套）
				_, err := repo.FindByID(ctx, *req.ParentID)
				if err != nil {
					if err == gorm.ErrRecordNotFound {
						return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "父级库位不存在")
					}
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询父级库位失败").WithCause(err)
				}
			}

			pID := *req.ParentID
			for pID > 0 {
				parent, err := repo.FindByID(ctx, pID)
				if err != nil {
					return err
				}
				if parent.ParentID == nil {
					break
				}
				if *parent.ParentID == id {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "不能将库位移动到自己的子孙节点下")
				}
				pID = *parent.ParentID
			}
		}

		copier.Copy(location, req)

		if err := repo.Update(ctx, location); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新库位失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, location)
	return voResult, nil
}

// Delete 删除库位
func (s *wmsLocationServiceImpl) Delete(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsLocationRepository()

	tree, err := repo.GetLocationTree(ctx, id)
	if err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "检查子节点失败").WithCause(err)
	}
	if len(tree) > 1 {
		return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库位下有子节点，无法删除")
	}

	// TODO: 检查是否有库存

	return repo.Delete(ctx, id)
}

// GetByID 获取单个库位
func (s *wmsLocationServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsLocationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsLocationRepository()
	location, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库位不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库位失败").WithCause(err)
	}
	var voResult vo.WmsLocationVO
	copier.Copy(&voResult, location)
	return &voResult, nil
}

// GetPage 获取分页
func (s *wmsLocationServiceImpl) GetPage(ctx context.Context, req *dto.WmsLocationQueryReq) (*vo.PageResult[vo.WmsLocationVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsLocationRepository()
	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取分页数据失败").WithCause(err)
	}

	voList := make([]*vo.WmsLocationVO, 0)
	copier.Copy(&voList, pageResult.List)

	return &vo.PageResult[vo.WmsLocationVO]{
		List:     voList,
		Total:    pageResult.Total,
		PageSize: pageResult.PageSize,
		PageNum:  pageResult.PageNum,
	}, nil
}

// GetWarehouses 获取仓库列表
func (s *wmsLocationServiceImpl) GetWarehouses(ctx context.Context) ([]*vo.WmsWarehouseSimpleVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsLocationRepository()
	warehouses, err := repo.GetWarehouses(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取仓库列表失败").WithCause(err)
	}

	vos := make([]*vo.WmsWarehouseSimpleVO, 0, len(warehouses))
	for _, w := range warehouses {
		name := ""
		if w.Name != nil {
			name = *w.Name
		}
		vos = append(vos, &vo.WmsWarehouseSimpleVO{
			ID:   w.ID,
			Code: w.Code,
			Name: name,
		})
	}
	return vos, nil
}

// GetLocationTree 获取库位树
// warehouseID 为 0 时获取全部库位树，否则获取指定仓库的库位树
func (s *wmsLocationServiceImpl) GetLocationTree(ctx context.Context, warehouseID uint) ([]*vo.WmsLocationTreeVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsLocationRepository()
	locations, err := repo.GetLocationTree(ctx, warehouseID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取库位树数据失败").WithCause(err)
	}
	return buildLocationTree(locations, nil), nil
}

// buildLocationTree 递归构建树
func buildLocationTree(locations []*entity.WmsLocation, parentID *uint) []*vo.WmsLocationTreeVO {
	tree := make([]*vo.WmsLocationTreeVO, 0)
	for _, loc := range locations {
		var currentParentID *uint
		if loc.ParentID != nil {
			currentParentID = loc.ParentID
		}

		if (parentID == nil && currentParentID == nil) || (parentID != nil && currentParentID != nil && *parentID == *currentParentID) {
			node := &vo.WmsLocationTreeVO{}
			copier.Copy(node, loc)

			children := buildLocationTree(locations, &loc.ID)
			node.Children = make([]*vo.WmsLocationTreeVO, 0)
			if len(children) > 0 {
				node.Children = children
			}
			tree = append(tree, node)
		}
	}
	return tree
}
