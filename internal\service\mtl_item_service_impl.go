package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	apperrors "backend/pkg/errors"
	"context"

	"github.com/jinzhu/copier"
)

type MtlItemService interface {
	BaseService

	Create(ctx context.Context, req *dto.MtlItemCreateReq) (*vo.MtlItemVO, error)
	Update(ctx context.Context, id uint, req *dto.MtlItemUpdateReq) (*vo.MtlItemVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.MtlItemVO, error)
	GetPage(ctx context.Context, req *dto.MtlItemQueryReq) (*vo.PageResult[vo.MtlItemVO], error)

	// 包装单位管理
	GetPackageUnits(ctx context.Context, itemId uint) ([]*entity.MtlItemPackageUnit, error)
	AddPackageUnit(ctx context.Context, itemId uint, req *dto.MtlItemPackageUnitCreateReq) (*entity.MtlItemPackageUnit, error)
	UpdatePackageUnit(ctx context.Context, itemId uint, unitId uint, req *dto.MtlItemPackageUnitUpdateReq) (*entity.MtlItemPackageUnit, error)
	DeletePackageUnit(ctx context.Context, itemId uint, unitId uint) error
}

type mtlItemServiceImpl struct {
	BaseServiceImpl
}

func NewMtlItemService(sm *ServiceManager) MtlItemService {
	return &mtlItemServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建物料
func (s *mtlItemServiceImpl) Create(ctx context.Context, req *dto.MtlItemCreateReq) (*vo.MtlItemVO, error) {
	var result *vo.MtlItemVO

	// 使用事务包装整个创建过程
	err := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		repo := txServiceManager.GetRepositoryManager().GetMtlItemRepository()

		// 最大重试次数（编码生成失败时）
		const maxRetries = 3
		var lastErr error

		for attempt := 0; attempt < maxRetries; attempt++ {
			// 处理物料编码生成
			sku := req.Sku
			if sku == "" || sku == "AUTO" {
				// 自动生成物料编码
				s.GetServiceManager().GetLogger().Info("开始自动生成物料编码")
				codeGenService := txServiceManager.GetCodeGenerationService()
				if codeGenService != nil {
					s.GetServiceManager().GetLogger().Info("编码生成服务可用，准备生成编码")

					contextData := map[string]interface{}{
						"name":     req.Name,
						"baseUnit": req.BaseUnit,
					}
					if req.CategoryCode != nil {
						contextData["categoryCode"] = *req.CategoryCode
					}
					if req.GroupCode != nil {
						contextData["groupCode"] = *req.GroupCode
					}

					s.GetServiceManager().GetLogger().Info("调用编码生成服务",
						s.GetServiceManager().GetLogger().WithField("businessType", "MATERIAL"),
						s.GetServiceManager().GetLogger().WithField("contextData", contextData))

					generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
						BusinessType: "MATERIAL",
						ContextData:  contextData,
					})
					if err != nil {
						s.GetServiceManager().GetLogger().Error("生成物料编码失败",
							s.GetServiceManager().GetLogger().WithError(err))
						return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "物料编码自动生成失败。可能原因：1) 缺少物料编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
					}

					s.GetServiceManager().GetLogger().Info("编码生成成功",
						s.GetServiceManager().GetLogger().WithField("generatedCode", generatedCode.GeneratedCode))
					sku = generatedCode.GeneratedCode
				} else {
					s.GetServiceManager().GetLogger().Error("编码生成服务不可用")
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "物料编码生成服务暂时不可用，请手动输入编码或稍后重试自动生成。")
				}
			}

			// 从Context获取账套ID
			accountBookID, err := s.GetAccountBookIDFromContext(ctx)
			if err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID").WithCause(err)
			}

			// 创建实体
			item := &entity.MtlItem{
				AccountBookEntity: entity.AccountBookEntity{
					AccountBookID: uint(accountBookID),
				},
				Sku:      sku,
				Name:     req.Name,
				BaseUnit: req.BaseUnit,
				Status:   "ACTIVE", // 默认状态
			}

			// 复制其他字段
			if err := copier.Copy(item, req); err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
			}

			// 重要：重新设置生成的SKU，防止被copier.Copy覆盖
			item.Sku = sku

			// 处理默认值
			if req.Status != nil {
				item.Status = entity.MtlItemStatus(*req.Status)
			}
			if req.BatchManaged != nil {
				item.BatchManaged = *req.BatchManaged
			}
			if req.SerialManaged != nil {
				item.SerialManaged = *req.SerialManaged
			}

			// 尝试保存物料
			err = repo.Create(ctx, item)
			if err != nil {
				// 检查是否为重复键错误
				if apperrors.IsDuplicateKeyError(err) {
					s.GetServiceManager().GetLogger().Warn("物料编码重复，正在重试",
						s.GetServiceManager().GetLogger().WithField("sku", sku),
						s.GetServiceManager().GetLogger().WithField("attempt", attempt+1))

					lastErr = apperrors.NewDuplicateKeyError("物料编码重复，系统正在重新生成。如果多次失败，请手动输入编码。")

					// 如果是自动生成的编码且发生重复，继续重试
					if req.Sku == "" || req.Sku == "AUTO" {
						continue
					} else {
						// 手动指定的编码重复，直接返回错误
						return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "物料编码已存在")
					}
				} else {
					// 其他数据库错误
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建物料失败").WithCause(err)
				}
			} else {
				// 创建成功，处理包装单位
				for _, unitDTO := range req.PackageUnits {
					unit := &entity.MtlItemPackageUnit{
						ItemID:           item.ID,
						UnitName:         unitDTO.UnitName,
						ConversionFactor: unitDTO.ConversionFactor,
					}
					if err := copier.Copy(unit, &unitDTO); err == nil {
						repo.AddPackageUnit(ctx, unit)
					}
				}

				// 设置返回结果
				result = s.entityToVO(item)
				return nil
			}
		}

		// 所有重试都失败了
		if lastErr != nil {
			return lastErr
		}

		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "物料创建失败，编码生成重试次数已达上限。建议手动输入物料编码或联系系统管理员。")
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// Update 更新物料
func (s *mtlItemServiceImpl) Update(ctx context.Context, id uint, req *dto.MtlItemUpdateReq) (*vo.MtlItemVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()

	// 获取现有实体
	item, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "物料不存在")
	}

	// 更新字段
	if req.Sku != nil {
		item.Sku = *req.Sku
	}
	if req.Name != nil {
		item.Name = *req.Name
	}
	if req.Status != nil {
		item.Status = entity.MtlItemStatus(*req.Status)
	}
	if req.BatchManaged != nil {
		item.BatchManaged = *req.BatchManaged
	}
	if req.SerialManaged != nil {
		item.SerialManaged = *req.SerialManaged
	}

	// 更新其他可选字段
	item.Description = req.Description
	item.Specification = req.Specification
	item.CategoryCode = req.CategoryCode
	item.GroupCode = req.GroupCode
	item.ShelfLifeDays = req.ShelfLifeDays
	item.WeightKg = req.WeightKg
	item.VolumeM3 = req.VolumeM3
	item.LengthM = req.LengthM
	item.WidthM = req.WidthM
	item.HeightM = req.HeightM
	item.DefaultCustomerID = req.DefaultCustomerID
	item.DefaultSupplierID = req.DefaultSupplierID
	item.DefaultLocationID = req.DefaultLocationID
	item.StorageCondition = req.StorageCondition
	item.ImageUrl = req.ImageUrl
	item.Remark = req.Remark

	// 保存更新
	if err := repo.Update(ctx, item); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新物料失败").WithCause(err)
	}

	return s.entityToVO(item), nil
}

// Delete 删除物料
func (s *mtlItemServiceImpl) Delete(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()

	if err := repo.Delete(ctx, id); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除物料失败").WithCause(err)
	}

	return nil
}

// GetByID 获取物料详情
func (s *mtlItemServiceImpl) GetByID(ctx context.Context, id uint) (*vo.MtlItemVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()

	item, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "物料不存在")
	}

	// 加载包装单位
	units, _ := repo.FindPackageUnitsByItemID(ctx, id)

	vo := s.entityToVO(item)
	vo.PackageUnits = s.packageUnitsToVO(units)

	return vo, nil
}

// GetPage 分页查询 (参考WmsLocation实现)
func (s *mtlItemServiceImpl) GetPage(ctx context.Context, req *dto.MtlItemQueryReq) (*vo.PageResult[vo.MtlItemVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()
	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取分页数据失败").WithCause(err)
	}

	voList := make([]*vo.MtlItemVO, 0)
	if pageResult.List != nil {
		for _, item := range pageResult.List.([]*entity.MtlItem) {
			voList = append(voList, s.entityToVO(item))
		}
	}

	return &vo.PageResult[vo.MtlItemVO]{
		List:     voList,
		Total:    pageResult.Total,
		PageSize: pageResult.PageSize,
		PageNum:  pageResult.PageNum,
	}, nil
}

// GetPackageUnits 获取包装单位列表
func (s *mtlItemServiceImpl) GetPackageUnits(ctx context.Context, itemId uint) ([]*entity.MtlItemPackageUnit, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()
	return repo.FindPackageUnitsByItemID(ctx, itemId)
}

// AddPackageUnit 添加包装单位
func (s *mtlItemServiceImpl) AddPackageUnit(ctx context.Context, itemId uint, req *dto.MtlItemPackageUnitCreateReq) (*entity.MtlItemPackageUnit, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()

	unit := &entity.MtlItemPackageUnit{
		ItemID:           itemId,
		UnitName:         req.UnitName,
		ConversionFactor: req.ConversionFactor,
	}

	if err := copier.Copy(unit, req); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
	}

	if err := repo.AddPackageUnit(ctx, unit); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "添加包装单位失败").WithCause(err)
	}

	return unit, nil
}

// UpdatePackageUnit 更新包装单位
func (s *mtlItemServiceImpl) UpdatePackageUnit(ctx context.Context, itemId uint, unitId uint, req *dto.MtlItemPackageUnitUpdateReq) (*entity.MtlItemPackageUnit, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()

	unit, err := repo.FindPackageUnitByID(ctx, unitId)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "包装单位不存在")
	}

	// 更新字段 - 只有非空指针才更新对应字段
	if req.UnitName != nil {
		unit.UnitName = *req.UnitName
	}
	if req.ConversionFactor != nil {
		unit.ConversionFactor = *req.ConversionFactor
	}
	if req.PackageWeightKg != nil {
		unit.PackageWeightKg = req.PackageWeightKg
	}
	if req.PackageVolumeM3 != nil {
		unit.PackageVolumeM3 = req.PackageVolumeM3
	}
	if req.PackageLengthM != nil {
		unit.PackageLengthM = req.PackageLengthM
	}
	if req.PackageWidthM != nil {
		unit.PackageWidthM = req.PackageWidthM
	}
	if req.PackageHeightM != nil {
		unit.PackageHeightM = req.PackageHeightM
	}

	if err := repo.UpdatePackageUnit(ctx, unit); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新包装单位失败").WithCause(err)
	}

	return unit, nil
}

// DeletePackageUnit 删除包装单位
func (s *mtlItemServiceImpl) DeletePackageUnit(ctx context.Context, itemId uint, unitId uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()
	return repo.DeletePackageUnit(ctx, unitId)
}

// entityToVO 实体转VO
func (s *mtlItemServiceImpl) entityToVO(item *entity.MtlItem) *vo.MtlItemVO {
	if item == nil {
		return nil
	}

	vo := &vo.MtlItemVO{}
	copier.Copy(vo, item)
	return vo
}

// packageUnitsToVO 包装单位实体转VO
func (s *mtlItemServiceImpl) packageUnitsToVO(units []*entity.MtlItemPackageUnit) []vo.MtlItemPackageUnitVO {
	if len(units) == 0 {
		return []vo.MtlItemPackageUnitVO{}
	}

	voList := make([]vo.MtlItemPackageUnitVO, len(units))
	for i, unit := range units {
		copier.Copy(&voList[i], unit)
	}
	return voList
}
