package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"backend/pkg/errors"
	"backend/pkg/logger"
)

const (
	// sequenceCacheKeyPrefix 缓存键前缀
	sequenceCacheKeyPrefix = "sequence:"
	// sequenceDateFormat 默认日期格式，用于按天生成序列
	sequenceDateFormat = "20060102"
	// defaultSequenceLength 默认序列号长度（补零后）
	defaultSequenceLength = 6
)

// SequenceService 定义序列号/单据号生成服务接口
type SequenceService interface {
	BaseService // 继承基础服务接口

	// GenerateDailyDocumentNumber 生成按天重置的单据号
	// docTypePrefix: 单据类型前缀, 例如 "REC", "ASN"
	// sequenceLength: 序列号部分的长度 (补零)
	GenerateDailyDocumentNumber(ctx context.Context, docTypePrefix string, sequenceLength int) (string, error)
}

// sequenceServiceImpl 序列号服务实现
type sequenceServiceImpl struct {
	BaseServiceImpl
	cacheService CacheService // 依赖 CacheService
}

// NewSequenceService 创建序列号服务实例
func NewSequenceService(sm *ServiceManager) SequenceService {
	// 使用 ServiceManager 的 GetCacheService 方法
	cacheServiceInstance := sm.GetCacheService()

	// 检查获取到的 CacheService 是否有效
	if cacheServiceInstance == nil {
		// GetCacheService 内部应该已经记录了错误
		sm.GetLogger().Error("NewSequenceService: Failed to get a valid CacheService instance from ServiceManager.")
		// 返回错误实现
		return &errorSequenceServiceImpl{BaseServiceImpl: *NewBaseService(sm)}
	}

	// 创建 sequenceServiceImpl 实例
	s := &sequenceServiceImpl{
		cacheService: cacheServiceInstance, // 直接使用获取到的实例
	}
	s.BaseServiceImpl = *NewBaseService(sm)
	return s
}

// GenerateDailyDocumentNumber 实现生成按天重置的单据号
func (s *sequenceServiceImpl) GenerateDailyDocumentNumber(ctx context.Context, docTypePrefix string, sequenceLength int) (string, error) {
	log := logger.WithContext(ctx).WithField("docTypePrefix", docTypePrefix)

	// 规范化前缀
	prefix := strings.ToUpper(strings.TrimSpace(docTypePrefix))
	if prefix == "" {
		return "", errors.NewParamsError("单据类型前缀不能为空")
	}

	// 确定序列号长度
	length := sequenceLength
	if length <= 0 {
		length = defaultSequenceLength
	}

	// 获取当前日期
	today := time.Now().Format(sequenceDateFormat)

	// 构建缓存 Key，例如: sequence:REC:20231027
	sequenceKey := fmt.Sprintf("%s%s:%s", sequenceCacheKeyPrefix, prefix, today)

	// 调用 CacheService 的原子递增方法
	sequenceNum, err := s.cacheService.Increment(ctx, sequenceKey)
	if err != nil {
		// CacheService.Increment 应该会处理底层的错误 (Redis 或 Memory)
		log.WithError(err).Error("Failed to increment sequence number using CacheService")
		// 根据 CacheService 返回的错误类型进行包装
		// 假设 CacheService 会返回 *errors.CustomError
		if customErr, ok := err.(*errors.CustomError); ok {
			// 如果已经是 CustomError，可以根据需要保留或重新包装
			// 这里选择保留原始错误代码，但添加更具体的上下文消息
			return "", errors.WrapError(customErr, customErr.Code, "生成序列号失败")
		}
		// 如果不是 CustomError，包装为通用系统错误
		return "", errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "生成序列号失败").WithCause(err)
	}

	// 如果是新生成的序列号 (值为1)，可以考虑为缓存键设置过期时间
	// 依赖 CacheService 的 Expire 方法
	if sequenceNum == 1 {
		expiration := 25 * time.Hour // 比一天稍长，防止边界问题
		if expErr := s.cacheService.Expire(ctx, sequenceKey, expiration); expErr != nil {
			// 记录错误，但不影响主流程
			log.WithError(expErr).Warn("Failed to set expiration for new sequence key using CacheService")
		}
	}

	// 格式化单据号，例如: REC20231027000001
	formatString := fmt.Sprintf("%%s%%s%%0%dd", length) // e.g., "%s%s%06d"
	docNumber := fmt.Sprintf(formatString, prefix, today, sequenceNum)

	log.Infof("Generated document number: %s", docNumber)
	return docNumber, nil
}

// --- Error implementation if CacheService is unavailable ---

type errorSequenceServiceImpl struct {
	BaseServiceImpl
}

func (s *errorSequenceServiceImpl) GenerateDailyDocumentNumber(ctx context.Context, docTypePrefix string, sequenceLength int) (string, error) {
	logger.WithContext(ctx).Error("SequenceService is unavailable because CacheService is not available or invalid.")
	return "", errors.NewSystemError(errors.CODE_SYSTEM_UNAVAILABLE, "序列号服务不可用 (依赖的缓存服务无效)")
}
