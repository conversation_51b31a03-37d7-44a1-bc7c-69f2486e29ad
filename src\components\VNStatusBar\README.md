# VNStatusBar 组件

一个通用的底部状态栏组件，用于显示应用程序的状态信息，支持左右中对齐、区域标题、图标、工具提示、自定义渲染和点击事件。

## Props

| Prop Name         | Type                  | Description                             | Default     |
| ----------------- | --------------------- | --------------------------------------- | ----------- |
| `items`           | `StatusBarItem[]`     | 状态项配置数组，必须提供。            | `[]`        |
| `height`          | `string`              | 可选，状态栏高度 (CSS 长度值)         | `'30px'`    |
| `backgroundColor` | `string`              | 可选，背景颜色 (CSS 颜色值)           | `'#f5f5f5'` |
| `textColor`       | `string`              | 可选，文字颜色 (CSS 颜色值)           | `'#606266'` |
| `separator`       | `string \| VNode`      | 可选，状态项之间的全局分隔符         | `'|'`       |
| `leftTitle`       | `string`              | 可选，左侧区域标题                    | `''`        |
| `centerTitle`     | `string`              | 可选，中间区域标题                    | `''`        |
| `rightTitle`      | `string`              | 可选，右侧区域标题                    | `''`        |

### StatusBarItem 接口

每个 `items` 数组中的对象应符合 `StatusBarItem` 接口：

```typescript
import type { VNode } from 'vue';

interface StatusBarItem {
  id: string | number; // 唯一标识符
  label?: string; // 显示的文本标签
  value?: string | number | VNode; // 显示的值，可以是文本、数字或 VNode
  icon?: string | object; // 可选图标 (Element Plus 图标组件名或对象)
  tooltip?: string; // 可选，鼠标悬停提示
  separator?: boolean; // 可选，是否在此项后显示分隔符 (默认 true)
  onClick?: () => void; // 可选，点击事件处理
  component?: object; // 可选，直接渲染一个自定义组件
  componentProps?: Record<string, any>; // 传递给自定义组件的 props
  align?: 'left' | 'center' | 'right'; // 可选，对齐方式 (默认 'left')
}
```

## 使用示例

```vue
<template>
  <div style="height: 200px; border: 1px solid #ccc; position: relative;">
    <!-- 模拟页面内容 -->
    <p style="padding: 10px;">页面内容区域...</p>
    
    <VNStatusBar 
      :items="statusBarItems" 
      left-title="系统信息"
      center-title="活动任务"
      right-title="用户信息"
      style="position: absolute; bottom: 0; left: 0; right: 0;"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue';
import VNStatusBar from '@/components/VNStatusBar/index.vue';
import type { StatusBarItem } from '@/components/VNStatusBar/types';
import { Check, Close, Loading, Bell } from '@element-plus/icons-vue';
import { ElTag } from 'element-plus';

const statusBarItems = ref<StatusBarItem[]>([
  {
    id: 'status',
    label: '状态',
    value: '就绪',
    icon: Check,
    tooltip: '系统当前状态正常'
  },
  {
    id: 'version',
    label: '版本',
    value: '1.0.2',
    onClick: () => alert('当前版本: 1.0.2')
  },
  {
    id: 'loading',
    icon: Loading,
    value: '处理中...',
    align: 'center', // 居中对齐
    tooltip: '后台任务正在运行'
  },
  {
    id: 'user',
    label: '用户',
    value: 'Admin',
    align: 'right', // 右对齐
    icon: 'User'
  },
  {
    id: 'notifications',
    align: 'right',
    icon: Bell,
    value: h(ElTag, { type: 'danger', size: 'small', effect: 'dark' }, () => '3'), // 使用 VNode 自定义渲染值
    tooltip: '有 3 条未读通知',
    onClick: () => console.log('打开通知')
  }
]);
</script>
```

## 注意事项

*   组件依赖 `element-plus` 用于图标和工具提示。
*   通过 `align` 属性可以将状态项分布在左侧、中间或右侧。
*   新增 `leftTitle`, `centerTitle`, `rightTitle` props 可以为对应区域添加标题。
*   `value` 属性可以直接接受 Vue 的 `VNode`，允许高度自定义渲染内容（如示例中的 `ElTag`）。
*   `component` 属性允许直接渲染一个完整的自定义组件作为状态项。
*   如果提供了 `onClick` 处理函数，状态项会变为可点击状态。
*   可以通过设置 `item.separator = false` 来阻止在该项后面显示全局分隔符。 