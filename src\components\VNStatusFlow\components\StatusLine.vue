<template>
  <div 
    class="status-line" 
    :class="[
      `is-${mode}`,
      `is-${style}`,
      {
        'is-completed': completed
      }
    ]"
  >
    <div class="line-track">
      <div class="line-fill" :style="fillStyle"></div>
    </div>
    <div v-if="mode === 'horizontal'" class="line-arrow"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { StatusLineProps } from '../types'

// Props
const props = withDefaults(defineProps<StatusLineProps>(), {
  style: 'solid',
  mode: 'horizontal'
})

// 计算填充样式
const fillStyle = computed(() => {
  const baseStyle: Record<string, string> = {
    width: props.completed ? '100%' : '0%',
    height: '100%'
  }
  
  if (props.completed) {
    baseStyle['background'] = 'linear-gradient(90deg, #409eff 0%, #67c23a 100%)'
  } else {
    baseStyle['background'] = '#e4e7ed'
  }
  
  return baseStyle
})

// 计算线条样式
const lineStyle = computed(() => {
  const styles: Record<string, string> = {}
  
  switch (props.style) {
    case 'dashed':
      styles['borderStyle'] = 'dashed'
      break
    case 'dotted':
      styles['borderStyle'] = 'dotted'
      break
    default:
      styles['borderStyle'] = 'solid'
  }
  
  return styles
})
</script>

<style scoped lang="scss">
.status-line {
  transition: all 0.3s ease;
  
  &.is-horizontal {
    position: absolute;
    top: 50%;
    left: 50px;
    right: -50px;
    height: 2px;
    transform: translateY(-50%);
    z-index: 1;
    
    .line-track {
      width: 100%;
      height: 100%;
      background: #e4e7ed;
      border-radius: 1px;
      overflow: hidden;
      
      .line-fill {
        transition: width 0.6s ease;
        border-radius: 1px;
      }
    }
    
    .line-arrow {
      position: absolute;
      right: -6px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid #e4e7ed;
      border-top: 5px solid transparent;
      border-bottom: 5px solid transparent;
      transition: all 0.3s ease;
    }
    
    &.is-completed {
      .line-arrow {
        border-left-color: #67c23a;
      }
    }
  }
  
  &.is-vertical {
    position: absolute;
    left: 50%;
    top: 50px;
    bottom: -50px;
    width: 2px;
    transform: translateX(-50%);
    z-index: 1;
    
    .line-track {
      width: 100%;
      height: 100%;
      background: #e4e7ed;
      border-radius: 1px;
      overflow: hidden;
      
      .line-fill {
        transition: height 0.6s ease;
        border-radius: 1px;
        width: 100%;
        height: v-bind('completed ? "100%" : "0%"');
      }
    }
  }
  
  // 线条样式变体
  &.is-dashed {
    .line-track {
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: repeating-linear-gradient(
          90deg,
          transparent,
          transparent 4px,
          #e4e7ed 4px,
          #e4e7ed 8px
        );
      }
    }
    
    &.is-completed .line-track::before {
      background-image: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 4px,
        #67c23a 4px,
        #67c23a 8px
      );
    }
  }
  
  &.is-dotted {
    .line-track {
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: repeating-linear-gradient(
          90deg,
          #e4e7ed,
          #e4e7ed 2px,
          transparent 2px,
          transparent 6px
        );
      }
    }
    
    &.is-completed .line-track::before {
      background-image: repeating-linear-gradient(
        90deg,
        #67c23a,
        #67c23a 2px,
        transparent 2px,
        transparent 6px
      );
    }
  }
}

// 连接线动画效果
@keyframes line-flow {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}

.status-line.is-completed .line-track .line-fill {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%
    );
    background-size: 20px 100%;
    animation: line-flow 2s linear infinite;
  }
}

// 垂直模式的连接线动画
.status-line.is-vertical.is-completed .line-track .line-fill::after {
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  background-size: 100% 20px;
  animation: line-flow-vertical 2s linear infinite;
}

@keyframes line-flow-vertical {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 20px;
  }
}

// 悬浮效果
.status-line:hover {
  .line-track {
    transform: scaleY(1.5);
  }
  
  &.is-vertical .line-track {
    transform: scaleX(1.5);
  }
}
</style> 