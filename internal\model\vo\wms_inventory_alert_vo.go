package vo

import (
	"backend/internal/model/entity"
	"fmt"
	"time"
)

// WmsInventoryAlertRuleVO 库存预警规则视图对象
type WmsInventoryAlertRuleVO struct {
	ID                     uint                          `json:"id"`
	RuleName               string                        `json:"ruleName"`
	RuleType               entity.WmsInventoryAlertType  `json:"ruleType"`
	RuleTypeName           string                        `json:"ruleTypeName"`
	ItemID                 *uint                         `json:"itemId"`
	ItemSku                *string                       `json:"itemSku"`
	ItemName               *string                       `json:"itemName"`
	WarehouseID            *uint                         `json:"warehouseId"`
	WarehouseName          *string                       `json:"warehouseName"`
	LocationID             *uint                         `json:"locationId"`
	LocationCode           *string                       `json:"locationCode"`
	LocationName           *string                       `json:"locationName"`
	ThresholdValue         float64                       `json:"thresholdValue"`
	ComparisonOperator     string                        `json:"comparisonOperator"`
	ComparisonOperatorName string                        `json:"comparisonOperatorName"`
	AlertLevel             entity.WmsInventoryAlertLevel `json:"alertLevel"`
	AlertLevelName         string                        `json:"alertLevelName"`
	IsActive               bool                          `json:"isActive"`
	CheckFrequencyMinutes  int                           `json:"checkFrequencyMinutes"`
	Description            *string                       `json:"description"`
	LastCheckAt            *time.Time                    `json:"lastCheckAt"`
	CreatedBy              uint                          `json:"createdBy"`
	CreatedByName          string                        `json:"createdByName"`
	CreatedAt              time.Time                     `json:"createdAt"`
	UpdatedAt              time.Time                     `json:"updatedAt"`

	// 统计信息
	AlertCount  int64      `json:"alertCount"`
	LastAlertAt *time.Time `json:"lastAlertAt"`

	// 业务状态
	CanActivate   bool `json:"canActivate"`
	CanDeactivate bool `json:"canDeactivate"`
	NeedsCheck    bool `json:"needsCheck"`
}

// WmsInventoryAlertLogVO 库存预警日志视图对象
type WmsInventoryAlertLogVO struct {
	ID                 uint                              `json:"id"`
	RuleID             uint                              `json:"ruleId"`
	RuleName           string                            `json:"ruleName"`
	InventoryID        uint                              `json:"inventoryId"`
	AlertType          entity.WmsInventoryAlertType      `json:"alertType"`
	AlertTypeName      string                            `json:"alertTypeName"`
	AlertLevel         entity.WmsInventoryAlertLevel     `json:"alertLevel"`
	AlertLevelName     string                            `json:"alertLevelName"`
	AlertMessage       string                            `json:"alertMessage"`
	CurrentValue       float64                           `json:"currentValue"`
	ThresholdValue     float64                           `json:"thresholdValue"`
	Status             entity.WmsInventoryAlertLogStatus `json:"status"`
	StatusName         string                            `json:"statusName"`
	AcknowledgedBy     *uint                             `json:"acknowledgedBy"`
	AcknowledgedByName *string                           `json:"acknowledgedByName"`
	AcknowledgedAt     *time.Time                        `json:"acknowledgedAt"`
	ResolvedAt         *time.Time                        `json:"resolvedAt"`
	CreatedAt          time.Time                         `json:"createdAt"`
	UpdatedAt          time.Time                         `json:"updatedAt"`

	// 关联信息
	ItemSku       string  `json:"itemSku"`
	ItemName      string  `json:"itemName"`
	LocationCode  string  `json:"locationCode"`
	LocationName  string  `json:"locationName"`
	WarehouseName string  `json:"warehouseName"`
	BatchNo       *string `json:"batchNo"`

	// 业务状态
	CanAcknowledge bool   `json:"canAcknowledge"`
	CanResolve     bool   `json:"canResolve"`
	IsActive       bool   `json:"isActive"`
	IsOverdue      bool   `json:"isOverdue"`
	DurationHours  *int64 `json:"durationHours"` // 预警持续时间(小时)
}

// WmsInventoryAlertRuleDetailVO 库存预警规则详情视图对象
type WmsInventoryAlertRuleDetailVO struct {
	WmsInventoryAlertRuleVO

	// 最近预警日志
	RecentAlerts []WmsInventoryAlertLogVO `json:"recentAlerts"`

	// 统计详情
	Statistics *WmsInventoryAlertRuleStatsVO `json:"statistics"`

	// 操作历史
	OperationLogs []WmsInventoryAlertRuleLogVO `json:"operationLogs"`
}

// WmsInventoryAlertRuleLogVO 库存预警规则操作日志视图对象
type WmsInventoryAlertRuleLogVO struct {
	ID           uint      `json:"id"`
	Operation    string    `json:"operation"`
	OperatorID   uint      `json:"operatorId"`
	OperatorName string    `json:"operatorName"`
	Description  string    `json:"description"`
	CreatedAt    time.Time `json:"createdAt"`
}

// WmsInventoryAlertRuleStatsVO 库存预警规则统计视图对象
type WmsInventoryAlertRuleStatsVO struct {
	// 预警统计
	TotalAlerts        int64 `json:"totalAlerts"`
	ActiveAlerts       int64 `json:"activeAlerts"`
	AcknowledgedAlerts int64 `json:"acknowledgedAlerts"`
	ResolvedAlerts     int64 `json:"resolvedAlerts"`

	// 时间统计
	AvgResponseTime   float64 `json:"avgResponseTime"`   // 平均响应时间(小时)
	AvgResolutionTime float64 `json:"avgResolutionTime"` // 平均解决时间(小时)

	// 趋势数据
	TrendData []WmsInventoryAlertTrendVO `json:"trendData"`
}

// WmsInventoryAlertStatsVO 库存预警统计视图对象
type WmsInventoryAlertStatsVO struct {
	// 规则统计
	TotalRules    int64 `json:"totalRules"`
	ActiveRules   int64 `json:"activeRules"`
	InactiveRules int64 `json:"inactiveRules"`

	// 预警统计
	TotalAlerts        int64 `json:"totalAlerts"`
	ActiveAlerts       int64 `json:"activeAlerts"`
	AcknowledgedAlerts int64 `json:"acknowledgedAlerts"`
	ResolvedAlerts     int64 `json:"resolvedAlerts"`
	CriticalAlerts     int64 `json:"criticalAlerts"`
	WarningAlerts      int64 `json:"warningAlerts"`
	InfoAlerts         int64 `json:"infoAlerts"`

	// 按类型统计
	MinStockAlerts   int64 `json:"minStockAlerts"`
	MaxStockAlerts   int64 `json:"maxStockAlerts"`
	ExpiryAlerts     int64 `json:"expiryAlerts"`
	SlowMovingAlerts int64 `json:"slowMovingAlerts"`

	// 效率统计
	AvgResponseTime   float64 `json:"avgResponseTime"`   // 平均响应时间(小时)
	AvgResolutionTime float64 `json:"avgResolutionTime"` // 平均解决时间(小时)
	ResolutionRate    float64 `json:"resolutionRate"`    // 解决率

	// 时间范围
	DateStart *time.Time `json:"dateStart"`
	DateEnd   *time.Time `json:"dateEnd"`

	// 趋势数据
	TrendData []WmsInventoryAlertTrendVO `json:"trendData"`
}

// WmsInventoryAlertTrendVO 库存预警趋势数据
type WmsInventoryAlertTrendVO struct {
	Date           string  `json:"date"`
	AlertCount     int64   `json:"alertCount"`
	CriticalCount  int64   `json:"criticalCount"`
	WarningCount   int64   `json:"warningCount"`
	InfoCount      int64   `json:"infoCount"`
	ResolvedCount  int64   `json:"resolvedCount"`
	ResolutionRate float64 `json:"resolutionRate"`
}

// WmsInventoryAlertSummaryVO 库存预警汇总视图对象
type WmsInventoryAlertSummaryVO struct {
	// 当前状态
	ActiveAlerts         int64 `json:"activeAlerts"`
	CriticalAlerts       int64 `json:"criticalAlerts"`
	WarningAlerts        int64 `json:"warningAlerts"`
	InfoAlerts           int64 `json:"infoAlerts"`
	UnacknowledgedAlerts int64 `json:"unacknowledgedAlerts"`

	// 今日统计
	TodayNewAlerts      int64 `json:"todayNewAlerts"`
	TodayResolvedAlerts int64 `json:"todayResolvedAlerts"`

	// 按仓库分组
	AlertsByWarehouse []WmsInventoryAlertWarehouseStatsVO `json:"alertsByWarehouse"`

	// 按类型分组
	AlertsByType []WmsInventoryAlertTypeStatsVO `json:"alertsByType"`

	// 最近预警
	RecentAlerts []WmsInventoryAlertLogVO `json:"recentAlerts"`
}

// WmsInventoryAlertWarehouseStatsVO 按仓库统计
type WmsInventoryAlertWarehouseStatsVO struct {
	WarehouseID   uint   `json:"warehouseId"`
	WarehouseName string `json:"warehouseName"`
	AlertCount    int64  `json:"alertCount"`
	CriticalCount int64  `json:"criticalCount"`
	WarningCount  int64  `json:"warningCount"`
	InfoCount     int64  `json:"infoCount"`
}

// WmsInventoryAlertTypeStatsVO 按类型统计
type WmsInventoryAlertTypeStatsVO struct {
	AlertType     entity.WmsInventoryAlertType `json:"alertType"`
	AlertTypeName string                       `json:"alertTypeName"`
	AlertCount    int64                        `json:"alertCount"`
	CriticalCount int64                        `json:"criticalCount"`
	WarningCount  int64                        `json:"warningCount"`
	InfoCount     int64                        `json:"infoCount"`
}

// WmsInventoryAlertNotificationSettingsVO 预警通知设置视图对象
type WmsInventoryAlertNotificationSettingsVO struct {
	UserID          uint      `json:"userId"`
	EmailEnabled    bool      `json:"emailEnabled"`
	SmsEnabled      bool      `json:"smsEnabled"`
	SystemEnabled   bool      `json:"systemEnabled"`
	AlertLevels     []string  `json:"alertLevels"`
	AlertTypes      []string  `json:"alertTypes"`
	QuietHoursStart *string   `json:"quietHoursStart"`
	QuietHoursEnd   *string   `json:"quietHoursEnd"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

// WmsInventoryAlertRulePageVO 库存预警规则分页视图对象
type WmsInventoryAlertRulePageVO struct {
	List  []WmsInventoryAlertRuleVO `json:"list"`
	Total int64                     `json:"total"`
}

// WmsInventoryAlertLogPageVO 库存预警日志分页视图对象
type WmsInventoryAlertLogPageVO struct {
	List  []WmsInventoryAlertLogVO `json:"list"`
	Total int64                    `json:"total"`
}

// NewWmsInventoryAlertRuleVO 创建库存预警规则视图对象
func NewWmsInventoryAlertRuleVO(rule *entity.WmsInventoryAlertRule) *WmsInventoryAlertRuleVO {
	vo := &WmsInventoryAlertRuleVO{
		ID:                     rule.ID,
		RuleName:               rule.RuleName,
		RuleType:               rule.RuleType,
		RuleTypeName:           rule.GetRuleTypeName(),
		ItemID:                 rule.ItemID,
		WarehouseID:            rule.WarehouseID,
		LocationID:             rule.LocationID,
		ThresholdValue:         getFloat64Value(rule.ThresholdValue),
		ComparisonOperator:     "", // 实体中没有此字段
		ComparisonOperatorName: "", // 实体中没有此方法
		AlertLevel:             rule.AlertLevel,
		AlertLevelName:         rule.GetAlertLevelName(),
		IsActive:               rule.IsActive,
		CheckFrequencyMinutes:  rule.CheckFrequencyMinutes,
		Description:            nil, // 实体中没有此字段
		LastCheckAt:            rule.LastCheckAt,
		CreatedBy:              rule.CreatedBy,
		CreatedAt:              rule.CreatedAt,
		UpdatedAt:              rule.UpdatedAt,
		CanActivate:            !rule.IsActive,
		CanDeactivate:          rule.IsActive,
		NeedsCheck:             true, // 简化处理，实体中没有此方法
	}

	// 填充关联信息 - 由于移除了关联关系，这些信息需要在Service层单独查询并填充
	// if rule.Item != nil {
	//     vo.ItemSku = &rule.Item.Sku
	//     vo.ItemName = &rule.Item.Name
	// }

	// if rule.Warehouse != nil {
	//     vo.WarehouseName = rule.Warehouse.Name
	// }

	// if rule.Location != nil {
	//     vo.LocationCode = &rule.Location.Code
	//     vo.LocationName = rule.Location.Name
	// }

	// CreatedByName should be filled by the service layer through a separate query
	// since we don't have an explicit Creator relationship

	return vo
}

// NewWmsInventoryAlertLogVO 创建库存预警日志视图对象
func NewWmsInventoryAlertLogVO(log *entity.WmsInventoryAlertLog) *WmsInventoryAlertLogVO {
	vo := &WmsInventoryAlertLogVO{
		ID:             log.ID,
		RuleID:         log.RuleID,
		InventoryID:    log.InventoryID,
		AlertType:      log.AlertType,
		AlertTypeName:  log.GetAlertTypeName(),
		AlertLevel:     log.AlertLevel,
		AlertLevelName: log.GetAlertLevelName(),
		AlertMessage:   log.AlertMessage,
		CurrentValue:   getFloat64Value(log.CurrentValue),
		ThresholdValue: getFloat64Value(log.ThresholdValue),
		Status:         log.Status,
		StatusName:     log.GetStatusName(),
		AcknowledgedBy: log.AcknowledgedBy,
		AcknowledgedAt: log.AcknowledgedAt,
		ResolvedAt:     log.ResolvedAt,
		CreatedAt:      log.CreatedAt,
		UpdatedAt:      log.UpdatedAt,
		CanAcknowledge: true,  // 简化处理，实体中没有此方法
		CanResolve:     true,  // 简化处理，实体中没有此方法
		IsActive:       true,  // 简化处理，实体中没有此方法
		IsOverdue:      false, // 简化处理，实体中没有此方法
	}

	// 计算持续时间
	if log.Status == entity.AlertLogStatusActive {
		duration := int64(time.Since(log.CreatedAt).Hours())
		vo.DurationHours = &duration
	} else if log.ResolvedAt != nil {
		duration := int64(log.ResolvedAt.Sub(log.CreatedAt).Hours())
		vo.DurationHours = &duration
	}

	// 填充关联信息
	if log.Rule != nil {
		vo.RuleName = log.Rule.RuleName
	}

	if log.Inventory != nil {
		vo.BatchNo = log.Inventory.BatchNo

		// 关联信息需要在Service层单独查询并填充
		// if log.Inventory.Item != nil {
		//     vo.ItemSku = log.Inventory.Item.Sku
		//     vo.ItemName = log.Inventory.Item.Name
		// }

		// if log.Inventory.Location != nil {
		//     vo.LocationCode = log.Inventory.Location.Code
		//     if log.Inventory.Location.Name != nil {
		//         vo.LocationName = *log.Inventory.Location.Name
		//     }
		//     // 仓库信息需要通过 WarehouseID 单独查询
		//     // WmsLocation 实体中没有 Warehouse 关联字段
		// }
	}

	// 确认用户信息需要通过 AcknowledgedBy 字段单独查询
	// WmsInventoryAlertLog 实体中没有 AcknowledgedUser 关联字段

	return vo
}

// GetDurationText 获取持续时间文本
func (vo *WmsInventoryAlertLogVO) GetDurationText() string {
	if vo.DurationHours == nil {
		return "-"
	}

	hours := *vo.DurationHours
	if hours < 24 {
		return fmt.Sprintf("%d小时", hours)
	} else {
		days := hours / 24
		remainingHours := hours % 24
		if remainingHours == 0 {
			return fmt.Sprintf("%d天", days)
		}
		return fmt.Sprintf("%d天%d小时", days, remainingHours)
	}
}

// GetSeverityColor 获取严重程度颜色
func (vo *WmsInventoryAlertLogVO) GetSeverityColor() string {
	switch vo.AlertLevel {
	case entity.AlertLevelCritical:
		return "red"
	case entity.AlertLevelWarning:
		return "orange"
	case entity.AlertLevelInfo:
		return "blue"
	default:
		return "gray"
	}
}

// getFloat64Value 获取float64指针的值，如果为nil则返回0
func getFloat64Value(ptr *float64) float64 {
	if ptr == nil {
		return 0
	}
	return *ptr
}
