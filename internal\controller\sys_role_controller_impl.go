package controller

import (
	// "strconv" // 已移除

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/model/vo"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // Alias pkg/errors to avoid conflict
	"backend/pkg/logger"           // 确保 logger 导入
	"backend/pkg/validator"        // 确保 validator 导入
	// "backend/pkg/util" // Uncomment if needed for user-related context getters
	// "github.com/sirupsen/logrus" // 已移除, 假设 pkg/logger 封装了
)

// RoleController 角色控制器
type RoleControllerImpl struct {
	*BaseControllerImpl
	roleService service.RoleService
}

// NewRoleControllerImpl 创建角色控制器实例
func NewRoleControllerImpl(cm *ControllerManager) *RoleControllerImpl {
	base := NewBaseController(cm)
	roleService := cm.GetServiceManager().GetRoleService()
	if roleService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 RoleService 实例")
	}
	return &RoleControllerImpl{
		BaseControllerImpl: base,
		roleService:        roleService,
	}
}

// CreateRole 创建角色
// @Summary 创建新角色
// @Description 创建一个新的角色信息
// @Tags Roles
// @Accept json
// @Produce json
// @Param role body dto.RoleCreateOrUpdateDTO true "角色创建请求"
// @Success 200 {object} response.Response{data=vo.RoleVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /roles [post]
func (c *RoleControllerImpl) CreateRole(ctx iris.Context) {
	opName := "创建角色"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "role")

	var req dto.RoleCreateOrUpdateDTO // <<< 修改 DTO 类型
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 参数校验 ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}
	// --- 校验结束 ---

	roleVO, err := c.roleService.CreateRole(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, roleVO)
}

// UpdateRole 更新角色
// @Summary 更新角色信息
// @Description 根据角色ID更新角色信息
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path uint true "角色ID"
// @Param role body dto.RoleCreateOrUpdateDTO true "角色更新请求" // <<< 修改 DTO 类型
// @Success 200 {object} response.Response{data=vo.RoleVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "角色未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /roles/{id} [put]
func (c *RoleControllerImpl) UpdateRole(ctx iris.Context) {
	opName := "更新角色"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "role")

	roleIDUint64, err := c.GetPathUintParamWithError(ctx, "id") // <<< 修改路径参数获取
	if err != nil {
		// GetPathUintParamWithError 已经包装了错误，可以直接传递
		c.HandleError(ctx, err, opName)
		return
	}
	roleID := uint(roleIDUint64)
	var req dto.RoleCreateOrUpdateDTO // <<< 修改 DTO 类型
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 参数校验 ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("roleID", roleID), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}
	// --- 校验结束 ---

	// 添加日志：打印从请求体解析得到的 DTO (保持，但考虑统一 logger)
	// c.GetLogger().Debug(ctx.Request().Context(), "Controller - UpdateRole - Parsed request body", logrus.WithField("dto", req))
	// 使用项目 logger:
	c.GetLogger().Debug(ctx.Request().Context(), "Controller - UpdateRole - Parsed request body", logger.WithField("dto", req))

	roleVO, err := c.roleService.UpdateRole(ctx.Request().Context(), roleID, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, roleVO)
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 根据角色ID删除角色
// @Tags Roles
// @Produce json
// @Param id path uint true "角色ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "角色未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /roles/{id} [delete]
func (c *RoleControllerImpl) DeleteRole(ctx iris.Context) {
	opName := "删除角色"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "role")

	roleIDUint64, err := c.GetPathUintParamWithError(ctx, "id") // <<< 修改路径参数获取
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	roleID := uint(roleIDUint64)
	err = c.roleService.DeleteRole(ctx.Request().Context(), roleID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetRoleByID 获取角色详情
// @Summary 获取角色详情
// @Description 根据角色ID获取角色详细信息
// @Tags Roles
// @Produce json
// @Param id path uint true "角色ID"
// @Success 200 {object} response.Response{data=vo.RoleVO} "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "角色未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /roles/{id} [get]
func (c *RoleControllerImpl) GetRoleByID(ctx iris.Context) {
	opName := "获取角色详情"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "role")

	roleIDUint64, err := c.GetPathUintParamWithError(ctx, "id") // <<< 修改路径参数获取
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	roleID := uint(roleIDUint64)
	roleVO, err := c.roleService.GetRoleByID(ctx.Request().Context(), roleID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, roleVO)
}

// PageRoles 获取角色列表 (分页)
// @Summary 获取角色列表 (分页)
// @Description 获取角色列表，支持分页和按名称/编码/状态过滤
// @Tags Roles
// @Produce json
// @Param pageNum query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param sort query string false "排序字段 (例如 'name asc, code desc')"
// @Param name query string false "角色名称过滤 (模糊匹配)"
// @Param code query string false "角色编码过滤 (模糊匹配)"
// @Param status query int false "状态过滤 (0:禁用, 1:启用)" Enums(0, 1)
// @Param dataScope query int false "数据权限范围 (1-全部, 2-自定义, 3-本部门, 4-本部门及以下, 5-仅本人)" Enums(1,2,3,4,5)
// @Param isDefault query bool false "是否默认角色"
// @Param isSystem query bool false "是否系统角色"
// @Success 200 {object} response.Response{data=vo.PageVO} "成功响应 (包含角色列表和分页信息)"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /roles/page [get]
func (c *RoleControllerImpl) PageRoles(ctx iris.Context) {
	opName := "分页查询角色"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "role")

	var queryDTO dto.RolePageQueryDTO // <<< 使用正确的 DTO

	// 使用 ReadQuery 绑定所有查询参数
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析查询参数失败").WithCause(err), opName)
		return
	}

	// --- 参数校验 (如果 RolePageQueryDTO 有 validate 标签) ---
	// validationErrs := validator.Struct(queryDTO) // RolePageQueryDTO 本身和其字段目前没有 'validate' 标签, response.PageQuery 内部可能有
	// if validationErrs != nil {
	// 	c.GetLogger().Warn(ctx.Request().Context(), opName+" - 查询参数验证失败", logger.WithField("errors", validationErrs))
	// 	customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
	// 	c.FailWithError(ctx, customErr)
	// 	return
	// }
	// --- 校验结束 ---

	// 调用服务层，传递整个 DTO
	pageVO, err := c.roleService.PageRoles(ctx.Request().Context(), queryDTO) // <<< 服务层期望 RolePageQueryDTO
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, pageVO)
}

// GetRoleMenus 获取角色关联的菜单
// @Summary 获取角色菜单
// @Description 获取指定角色关联的所有菜单信息列表
// @Tags Roles
// @Produce json
// @Param id path uint true "角色ID"
// @Success 200 {object} response.Response{data=[]vo.MenuVO} "成功响应，返回菜单视图对象列表"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "角色未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /roles/{id}/menus [get]
func (c *RoleControllerImpl) GetRoleMenus(ctx iris.Context) {
	opName := "获取角色菜单"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "role")

	roleIDUint64, err := c.GetPathUintParamWithError(ctx, "id") // <<< 修改路径参数获取
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	roleID := uint(roleIDUint64)
	menuVOs, err := c.roleService.GetRoleMenus(ctx.Request().Context(), roleID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	if menuVOs == nil {
		menuVOs = []*vo.MenuVO{}
	}
	c.Success(ctx, menuVOs)
}

// UpdateRoleMenus 更新角色菜单关联
// @Summary 更新角色菜单
// @Description 更新指定角色关联的菜单 (传入新的菜单ID列表, 空数组或空请求体表示清空关联)
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path uint true "角色ID"
// @Param menuIDs body []uint true "新的菜单ID列表 (uint)"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "角色未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /roles/{id}/menus [put]
func (c *RoleControllerImpl) UpdateRoleMenus(ctx iris.Context) {
	opName := "更新角色菜单"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "role")

	roleIDUint64, err := c.GetPathUintParamWithError(ctx, "id") // <<< 修改路径参数获取
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	roleID := uint(roleIDUint64)
	var menuIDs []uint

	// Check ContentLength first for empty body case (Iris v12 compatible)
	if ctx.Request().ContentLength == 0 {
		// Empty request body means clear all menu associations
		menuIDs = []uint{}
	} else {
		// If body is not empty, attempt to read the JSON array
		if err := ctx.ReadJSON(&menuIDs); err != nil {
			// Handle JSON parsing errors (invalid format, etc.)
			c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析菜单ID列表失败").WithCause(err), opName)
			return
		}
		// Ensure empty slice if JSON parsing results in nil (e.g., from `[]`)
		if menuIDs == nil {
			menuIDs = []uint{}
		}
	}

	err = c.roleService.UpdateRoleMenus(ctx.Request().Context(), roleID, menuIDs)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "更新成功", nil)
}
