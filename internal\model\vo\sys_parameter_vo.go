package vo

// SystemParameterVO 系统参数视图对象
type SystemParameterVO struct {
	BaseVO            // 嵌入基础 VO (ID, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy - 假设 BaseVO 存在)
	ParamKey   string `json:"paramKey"`   // 参数键
	ParamValue string `json:"paramValue"` // 参数值
	Name       string `json:"name"`       // 参数名称
	Remark     string `json:"remark"`     // 备注
	Status     int    `json:"status"`     // 状态 (1:启用, 0:禁用)
	IsSystem   bool   `json:"isSystem"`   // 是否系统内置
	ValueType  string `json:"valueType"`  // 值类型
}

// PageSystemParameterVO 系统参数分页视图对象
type PageSystemParameterVO struct {
	PageVO                      // 嵌入分页信息 (Total, PageNum, PageSize... - 假设 PageVO 存在)
	List   []*SystemParameterVO `json:"list"` // 当前页数据列表
}

// SystemParameterValueVO (Optional - For simple key-value needs)
// 如果有仅需要获取参数值的场景，可以使用此 VO
type SystemParameterValueVO struct {
	Value string `json:"value"` // Only the parameter value
}
