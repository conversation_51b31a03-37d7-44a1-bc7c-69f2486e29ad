package dto

import (
	"backend/pkg/response" // Required for response.PageQuery
	"time"
)

// AuditLogQueryDTO 定义了查询审计日志时使用的筛选条件和分页参数
type AuditLogQueryDTO struct {
	// Filter fields
	UserID       uint64    `form:"userId" json:"userId,omitempty"`
	Username     string    `form:"username" json:"username,omitempty"`
	Action       string    `form:"action" json:"action,omitempty"`
	ResourceType string    `form:"resourceType" json:"resourceType,omitempty"`
	ClientIP     string    `form:"clientIp" json:"clientIp,omitempty"`
	TraceID      string    `form:"traceId" json:"traceId,omitempty"`
	Status       string    `form:"status" json:"status,omitempty"`
	StartDate    time.Time `form:"startDate" json:"startDate,omitempty" time_format:"2006-01-02"`
	EndDate      time.Time `form:"endDate" json:"endDate,omitempty" time_format:"2006-01-02"`

	// Pagination and Sorting parameters.
	// This field is populated by the controller using response.BuildPageQuery(ctx).
	// Its sub-fields (pageNum, pageSize, sort) are read by BuildPageQuery from URL parameters.
	Pagination response.PageQuery `json:"-"` // Excluded from direct JSON binding for the DTO itself.
}
