package repository

import (
	"context"

	"backend/internal/model/entity"

	"gorm.io/gorm"
)

// FinCurrencyRepository 定义了币种仓库的接口
type FinCurrencyRepository interface {
	BaseRepository[entity.FinCurrency, uint]
	FindByCode(ctx context.Context, code string) (*entity.FinCurrency, error)
	FindAllByIDs(ctx context.Context, ids []uint) ([]*entity.FinCurrency, error)
}

// finCurrencyRepositoryImpl 是 FinCurrencyRepository 的实现
type finCurrencyRepositoryImpl struct {
	BaseRepository[entity.FinCurrency, uint]
}

// NewFinCurrencyRepository 创建一个新的币种仓库实例
func NewFinCurrencyRepository(db *gorm.DB) FinCurrencyRepository {
	baseRepo := NewBaseRepository[entity.FinCurrency, uint](db)
	return &finCurrencyRepositoryImpl{
		BaseRepository: baseRepo,
	}
}

// FindByCode 根据币种代码查询
func (r *finCurrencyRepositoryImpl) FindByCode(ctx context.Context, code string) (*entity.FinCurrency, error) {
	return r.FindOneByCondition(ctx, []QueryCondition{NewEqualCondition("code", code)})
}

// FindAllByIDs 根据ID列表查询所有币种
func (r *finCurrencyRepositoryImpl) FindAllByIDs(ctx context.Context, ids []uint) ([]*entity.FinCurrency, error) {
	var currencies []*entity.FinCurrency
	if len(ids) == 0 {
		return currencies, nil
	}
	db := r.GetDB(ctx)
	if err := db.Where("id IN ?", ids).Find(&currencies).Error; err != nil {
		return nil, err
	}
	return currencies, nil
}
