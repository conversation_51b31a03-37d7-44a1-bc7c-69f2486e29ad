<template>
  <div>
    <!-- 字典类型管理表格 -->
    <VNTable
      ref="typeTableRef"
      :data="typeTableData"
      :columns="typeTableColumns" 
      :loading="typeLoading"
      :pagination="typePagination"
      :toolbar-config="typeToolbarConfig"
      :operation-buttons="[]" 
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      operation-fixed="right"
      :operation-width="220" 
      @refresh="loadDictTypes" 
      @add="handleAddNewType"
      @selection-change="handleTypeSelectionChange"
      @batch-delete="handleTypeBatchDelete"
      @import="handleTypeImport"
      @export="handleTypeExport"
      @page-change="handleTypePageChange"
      @page-size-change="handleTypePageSizeChange"
      @sort-change="handleTypeSortChange" 
      @filter-change="handleTypeFilterChange" 
    >
      <!-- 类型表格状态列插槽 -->
      <template #column-status="{ row }">
         <el-tag :type="row.status === 1 ? 'success' : 'danger'">
           {{ row.status === 1 ? '启用' : '禁用' }}
         </el-tag>
      </template>

      <!-- 新增：系统内置列插槽 -->
      <template #column-isSystem="{ row }">
        <el-tag :type="row.isSystem ? 'warning' : 'info'">
          {{ row.isSystem ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 类型表格操作列插槽 (使用正确的 #operation) -->
      <template #operation="{ row }">
        <!-- 恢复原来的按钮代码 -->
        <el-tooltip content="查看类型" placement="top" v-if="hasPermission('sys:dict:type:view')">
          <el-button circle :icon="View" size="small" @click="handleViewType(row)" />
        </el-tooltip>
        <el-tooltip content="编辑类型" placement="top" v-if="hasPermission('sys:dict:type:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditType(row)" />
        </el-tooltip>
        <el-tooltip content="配置项" placement="top" v-if="hasPermission('sys:dict:item:list')">
           <el-button circle :icon="Setting" type="success" size="small" @click="openItemManageDialog(row)" />
        </el-tooltip>
        <el-tooltip content="删除类型" placement="top" v-if="hasPermission('sys:dict:type:delete')">
          <el-button circle :icon="Delete" type="danger" size="small" @click="handleDeleteType(row)" :disabled="row.isSystem" />
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 字典类型新增/编辑弹窗 -->
    <el-dialog
      v-model="typeDialogVisible"
      :title="typeDialogTitle"
      width="50%"
      draggable
      :close-on-click-modal="false"
      @close="handleCloseTypeDialog" 
    >
      <VNForm
        v-if="typeDialogVisible" 
        ref="typeFormRef"
        :header-fields="typeFormFields"
        v-model="currentEditingType"
        :loading="typeFormLoading" 
        :show-actions="false"
        @submit="submitTypeForm"
        @cancel="handleCloseTypeDialog" 
      >
        <!-- 新增：自定义表单操作按钮插槽 -->
        <template #actions>
            <template v-if="isViewing">
                <el-button
                  v-if="hasPermission('sys:dict:type:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreviewType"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('sys:dict:type:print')" 
                  type="primary"
                  :icon="Printer" 
                  @click="handlePrintType"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseTypeDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitTypeForm" :loading="typeFormLoading">提交</el-button>
                <el-button @click="handleCloseTypeDialog">取消</el-button>
            </template>
        </template>
      </VNForm>
    </el-dialog> 

    <!-- 字典项管理模态框 -->
    <el-dialog
      v-model="itemManageDialogVisible"
      :title="'配置项管理 - ' + (currentManagedType?.name || '')"
      width="85%"
      top="5vh"
      draggable
      :close-on-click-modal="false"
      :modal="true"
      destroy-on-close
      @close="currentManagedType = null"
    >
      <!-- 关键：在这里渲染字典项管理组件 -->
      <DictItemManager
        v-if="currentManagedType" 
        :type-id="currentManagedType.id"
        :type-code="currentManagedType.code"
        :type-name="currentManagedType.name"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTooltip, ElTag } from 'element-plus';
// 导入所需图标
import { Edit, Setting, Delete, View, Printer } from '@element-plus/icons-vue'; 
// 假设 VNTable 和 VNForm 组件已全局注册或在此导入
import VNTable from '@/components/VNTable/index.vue'; 
import VNForm from '@/components/VNForm/index.vue'; 
// 假设类型定义文件路径，请根据实际情况修改
// 引入 SortParams 和 FilterParams (暂时使用通用类型替代)
// import type { SortParams, FilterParams } from '@/components/VNTable/types';
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 假设 API 函数和 VO 类型定义文件路径
// 引入 DictTypeQueryDTO
import { getDictTypePage, createDictType, updateDictType, deleteDictType } from '@/api/system/systemDict';
import type { DictionaryTypeVO, DictTypeQueryDTO, DictTypeCreateDTO, DictTypeUpdateDTO } from '@/api/system/systemDict';
// 导入字典项管理组件
import DictItemManager from './components/DictItemManager.vue'; 
// 导入权限检查函数，而不是 hook
import { hasPermission } from '@/hooks/usePermission';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Refs --- 
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;

const typeTableRef = ref<VNTableInstance>();
// 用于类型编辑表单
const typeFormRef = ref<VNFormInstance>();

// --- State --- 

// 字典类型相关状态
const typeTableData = ref<DictionaryTypeVO[]>([]);
const typePagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
// 使用 Partial<DictTypeQueryDTO>
// const typeQueryParams = ref<Partial<DictTypeQueryDTO>>({});
const typeLoading = ref(false);
// 类型增/改弹窗
const typeDialogVisible = ref(false);
// 当前编辑的类型数据
const currentEditingType = ref<Partial<DictTypeCreateDTO | DictTypeUpdateDTO>>({});
// 添加 formMode 和 isViewing
const formMode = ref<'add' | 'edit' | 'view'>('add');
const isViewing = computed(() => formMode.value === 'view');
const selectedTypeRows = ref<DictionaryTypeVO[]>([]); // 添加：存储选中的行

// 添加排序状态
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);
// 添加筛选状态
const currentFilters = ref<Record<string, any>>({});
// 新增：类型表单加载状态
const typeFormLoading = ref(false);

// 字典项管理模态框相关状态
const itemManageDialogVisible = ref(false);
// 当前在模态框中管理的类型
const currentManagedType = ref<DictionaryTypeVO | null>(null);

// --- Computed --- 
// 计算属性，用于动态设置类型编辑弹窗标题
const typeDialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增字典类型';
  if (formMode.value === 'edit') return '编辑字典类型';
  if (formMode.value === 'view') return '查看字典类型';
  return '字典类型'; // Default
});

// --- 移动回来：Helper: Date Formatting ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) { 
        return '-';
    }
    // 使用简单的本地化格式
    return date.toLocaleString();
  } catch (e) {
    console.error('Error formatting date:', dateString, e);
    return '-';
  }
};
// -------------------------------------

// --- 新增：计算属性，用于动态设置工具栏配置 ---
const typeToolbarConfig = computed(() => ({
  refresh: true,
  add: hasPermission('sys:dict:type:add'),
  batchDelete: hasPermission('sys:dict:type:batchdelete'),
  filter: hasPermission('sys:dict:type:search'), // 使用 search 权限控制筛选
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('sys:dict:type:import'),
  export: hasPermission('sys:dict:type:export'),
}));
// ----------------------------------------------

// --- Table Columns (Type) --- 
const typeTableColumns = ref<TableColumn[]>([
  // 添加 filterable
  { prop: 'code', label: '类型编码', minWidth: 150, sortable: true, filterable: true, filterType: 'text' },
  // 添加 filterable
  { prop: 'name', label: '类型名称', minWidth: 150, filterable: true, filterType: 'text' },
  { 
    prop: 'status', 
    label: '状态', 
    width: 90, 
    slot: true, 
    filterable: true, 
    // 筛选类型为下拉选择
    filterType: 'select',
    // 提供筛选项
    filterOptions: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ] 
  }, 
  {
    prop: 'isSystem',
    label: '系统内置',
    width: 100,
    slot: true,
    // 可选：如果需要按此筛选
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
  { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true },
  {
    prop: 'createdAt',
    label: '创建时间',
    // 调整宽度以显示完整时间
    width: 180,
    sortable: true,
    // 允许按创建时间筛选
    filterable: true,
    // 筛选类型为日期
    filterType: 'date',
    // 应用格式化
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

// --- Form Fields (Type) --- 
const typeFormFields = computed<HeaderField[]>(() => [
  {
    field: 'code',
    label: '类型编码',
    // 编辑或查看时禁用编码字段
    disabled: formMode.value === 'edit' || isViewing.value,
    rules: [
      { required: true, message: '类型编码不能为空' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '编码只能包含字母、数字和下划线' },
      { max: 100, message: '编码长度不能超过100' }
    ]
  },
  {
    field: 'name',
    label: '类型名称',
    disabled: isViewing.value, // 查看时禁用
    rules: [
      { required: true, message: '类型名称不能为空' },
      { max: 100, message: '名称长度不能超过100' }
    ]
  },
  {
    field: 'status',
    label: '状态',
    type: 'select',
    options: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }],
    defaultValue: 1,
    disabled: isViewing.value, // 查看时禁用
  },
  {
    field: 'isSystem',
    label: '系统内置',
    type: 'switch', // 使用 switch 组件
    // 规则：查看模式禁用。
    // 编辑模式下，如果当前类型已经是系统内置，则禁用开关（不允许从 true 改为 false）。
    // 新增模式下可用。
    disabled: isViewing.value || (formMode.value === 'edit' && currentEditingType.value.isSystem === true),
    props: { // el-switch 的 props
      activeValue: true,
      inactiveValue: false,
    },
    defaultValue: false, // 新增时默认为非系统内置
  },
  {
    field: 'remark',
    label: '备注',
    type: 'textarea',
    props: { rows: 3 },
    disabled: isViewing.value, // 查看时禁用
    rules: [{ max: 500, message: '备注长度不能超过500' }]
  },
]);

// --- Lifecycle --- 
onMounted(() => {
  // 组件挂载时加载数据
  loadDictTypes();
});

// --- Methods ---

// 加载字典类型数据
const loadDictTypes = async (paramsOverwrite = {}) => {
  typeLoading.value = true;
  try {
    // 准备排序参数
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop && currentSort.value.order) {
      const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
      sortString = `${currentSort.value.prop},${direction}`;
    }

    // 准备筛选参数 (需要根据 filterValues 结构转换)
    const filterParams: Record<string, any> = {};
    for (const key in currentFilters.value) {
      const filterValue = currentFilters.value[key];
      // 只传递非空的筛选值
      if (filterValue !== null && filterValue !== undefined && filterValue !== '' && !(Array.isArray(filterValue) && filterValue.length === 0)) {
         // 对于状态这种下拉选择，后端可能直接接收单个值
         if (key === 'status' && Array.isArray(filterValue) && filterValue.length > 0) {
             // 假设只支持单选状态筛选
             filterParams[key] = filterValue[0]; 
         } else if (key === 'createdAt' && Array.isArray(filterValue) && filterValue.length === 2) {
             // 处理日期范围，假设后端需要 startDate 和 endDate
             filterParams['startDate'] = filterValue[0];
             filterParams['endDate'] = filterValue[1];
         } else {
             filterParams[key] = filterValue;
         }
      }
    }

    const params: Partial<DictTypeQueryDTO> & { pageNum: number; pageSize: number; sort?: string } = {
      pageNum: typePagination.currentPage,
      pageSize: typePagination.pageSize,
      sort: sortString,
      // 合并转换后的筛选参数
      ...filterParams, 
      // 允许覆盖参数
      ...paramsOverwrite, 
    };

    // 清理 params 中的空值或无效值（可选，取决于后端处理方式）
    Object.keys(params).forEach(key => {
        const paramKey = key as keyof typeof params; // 断言 key 为 params 的键
        if (params[paramKey] === null || params[paramKey] === undefined || params[paramKey] === '') {
            delete params[paramKey];
        }
    });


    console.log('[loadDictTypes] Fetching with params:', params);
    // 调用 API
    const response = await getDictTypePage(params);

    // 确保返回结构正确
    if (response && Array.isArray(response.list)) { 
        typeTableData.value = response.list;
        typePagination.total = response.total || 0;
    } else {
        console.warn('getDictTypePage 返回的数据格式不符合预期:', response);
        typeTableData.value = [];
        typePagination.total = 0;
    }

  } catch (error) {
    console.error('Error loading dictionary types:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    // 出错时清空
    typeTableData.value = [];
    typePagination.total = 0;
  } finally {
    typeLoading.value = false;
  }
};

// 处理分页变化
const handleTypePageChange = (page: number) => {
  typePagination.currentPage = page;
  loadDictTypes();
};

// 处理每页数量变化
const handleTypePageSizeChange = (size: number) => {
  typePagination.pageSize = size;
  // 重置到第一页
  typePagination.currentPage = 1; 
  loadDictTypes();
};

// 处理排序变化
// 修复：定义 SortParams 临时类型
type SortParams = { prop: string; order: 'ascending' | 'descending' | null };
const handleTypeSortChange = (sort: SortParams) => {
  console.log('Sort changed:', sort);
  currentSort.value = sort;
  // 重新加载数据以应用排序
  loadDictTypes();
};

// 处理筛选变化
// 修复：定义 FilterParams 临时类型
type FilterParams = Record<string, any>;
const handleTypeFilterChange = (filters: FilterParams) => {
  console.log('Filter changed:', filters);
  // 将 Element Plus 的筛选格式保存到 currentFilters
  currentFilters.value = filters; 
  // 筛选后重置到第一页
  typePagination.currentPage = 1; 
  // 重新加载数据以应用筛选
  loadDictTypes();
};

// 添加：处理行选择变化
const handleTypeSelectionChange = (rows: DictionaryTypeVO[]) => {
  selectedTypeRows.value = rows;
  console.log('Selected type rows:', selectedTypeRows.value);
};

// 处理新增按钮点击
const handleAddNewType = () => {
  formMode.value = 'add'; // 使用 formMode
  currentEditingType.value = { status: 1 };
  typeDialogVisible.value = true;
  nextTick(() => { typeFormRef.value?.clearValidate(); });
};

// 处理编辑按钮点击
const handleEditType = (row: DictionaryTypeVO) => {
  formMode.value = 'edit'; // 使用 formMode
  currentEditingType.value = JSON.parse(JSON.stringify(row));
  typeDialogVisible.value = true;
  nextTick(() => { typeFormRef.value?.clearValidate(); });
};

// 添加：处理查看按钮点击
const handleViewType = (row: DictionaryTypeVO) => {
  formMode.value = 'view'; // 设置为查看模式
  currentEditingType.value = JSON.parse(JSON.stringify(row)); // 加载数据
  typeDialogVisible.value = true; // 打开弹窗
  // 查看模式不需要清除校验
};

// 关闭类型编辑/查看弹窗
const handleCloseTypeDialog = () => {
  typeDialogVisible.value = false;
  // 可以考虑不清空 currentEditingType，依赖 destroy-on-close 或下次打开时覆盖
};

// 提交类型表单
const submitTypeForm = async () => {
  // 查看模式下不提交
  if (isViewing.value) {
    handleCloseTypeDialog();
    return;
  }

  const formIsValid = await typeFormRef.value?.validateForm();
  if (!formIsValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  typeFormLoading.value = true;
  const payload = { ...currentEditingType.value };

  try {
    if (formMode.value === 'edit') { // 使用 formMode
      // 编辑操作
      const editingData = currentEditingType.value as Partial<DictionaryTypeVO>; 
      if (!editingData.id) {
          ElMessage.error('无法获取要编辑的类型ID');
          typeFormLoading.value = false;
          return;
      }
      const updatePayload: Partial<DictTypeUpdateDTO> = {
         name: editingData.name,
         status: editingData.status,
         remark: editingData.remark,
         isSystem: editingData.isSystem,
      };
      console.log('Calling updateDictType API with ID:', editingData.id, 'and payload:', updatePayload);
      await updateDictType(editingData.id, updatePayload);
      ElMessage.success('编辑成功');
    } else { // formMode === 'add'
      // 新增操作
      const createPayload = payload as DictTypeCreateDTO;
      console.log('Calling createDictType API with:', createPayload);
      await createDictType(createPayload);
      ElMessage.success('新增成功');
    }
    handleCloseTypeDialog(); // 关闭弹窗
    loadDictTypes(); // 刷新列表
  } catch (error) {
    console.error('Error saving dictionary type:', error);
    // 错误消息应由API拦截器处理, 但为保持一致性，仍然调用
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    typeFormLoading.value = false;
  }
};

// 打开字典项管理模态框的函数
const openItemManageDialog = (row: DictionaryTypeVO) => {
  console.log('Opening item management for type:', row);
  currentManagedType.value = row; 
  itemManageDialogVisible.value = true; 
};

// 类型表格行操作 - 删除类型
const handleDeleteType = async (row: DictionaryTypeVO) => {
  console.log('Deleting type:', row);

  // 新增：检查是否为系统内置
  if (row.isSystem) {
    ElMessage.warning('系统内置字典类型不允许删除。');
    return;
  }

  try {
      await ElMessageBox.confirm(`确定删除字典类型 "${row.name}" (${row.code}) 吗? 删除后其下的字典项将无法使用!`, '危险操作确认', { 
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning' 
      });
      // 确认删除后调用API
      try {
         typeLoading.value = true;
         await deleteDictType(row.id); // 调用删除API
         ElMessage.success('删除成功');
         loadDictTypes(); // 刷新列表
      } catch (apiError: any) { // 显式指定 apiError 类型
         console.error('Error deleting dict type:', apiError);
         // 不再显示 ElMessage.error，假设拦截器会处理，但为保持一致性，仍然调用
         const errorMessage = getApiErrorMessage(apiError);
         ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
         });
      } finally {
         typeLoading.value = false;
      }
  } catch (error) {
      if (error !== 'cancel') {
          console.error('Error during delete confirmation:', error);
      } else {
           ElMessage.info('已取消删除');
      }
  }
};

// 添加：处理批量删除 (占位符)
const handleTypeBatchDelete = (selectedRows: DictionaryTypeVO[]) => {
  if (!selectedRows || selectedRows.length === 0) {
    ElMessage.warning('请先选择要删除的字典类型');
    return;
  }
  console.log('Batch delete requested for types:', selectedRows.map(r => r.id));
  ElMessageBox.confirm(`确定删除选中的 ${selectedRows.length} 个字典类型吗?`, '确认批量删除', { type: 'warning' })
    .then(async () => {
      ElMessage.info('批量删除功能待实现');
      // loading.value = true;
      // const ids = selectedRows.map(row => row.id);
      // await batchDeleteDictTypes(ids); // 假设有批量删除 API
      // ElMessage.success('批量删除成功');
      // loadDictTypes();
      // typeTableRef.value?.clearSelection();
    })
    .catch(() => {
      ElMessage.info('已取消批量删除');
    })
    // .finally(() => {
    //   loading.value = false;
    // });
};

// 添加：处理导入 (占位符)
const handleTypeImport = () => {
  console.log('Import requested for types');
  ElMessage.info('导入功能待实现');
};

// 添加：处理导出 (占位符)
const handleTypeExport = () => {
  console.log('Export requested for types');
  ElMessage.info('导出功能待实现');
};

// --- 新增：字典类型打印处理函数（占位）---
const handlePrintPreviewType = () => {
  // currentEditingType.value 可能为 Partial<DictTypeCreateDTO | DictTypeUpdateDTO>
  // 或 Partial<DictionaryTypeVO>，取决于 formMode
  const typeToPrint = currentEditingType.value as Partial<DictionaryTypeVO>; 
  if (!typeToPrint || !typeToPrint.id) { // 确保有ID，通常查看的是已有记录
    ElMessage.warning('没有可打印预览的字典类型数据。');
    return;
  }
  console.log('触发打印预览，字典类型:', typeToPrint);
  ElMessage.info('字典类型打印预览功能待实现');
  // TODO: Implement print preview logic for dictionary types
};

const handlePrintType = () => {
  const typeToPrint = currentEditingType.value as Partial<DictionaryTypeVO>; 
  if (!typeToPrint || !typeToPrint.id) {
    ElMessage.warning('没有可打印的字典类型数据。');
    return;
  }
  console.log('触发打印，字典类型:', typeToPrint);
  ElMessage.info('字典类型打印功能待实现');
  // TODO: Implement print logic for dictionary types
};

</script>

<style scoped>
/* Add any specific styles if needed */
</style> 