<template>
  <div class="statusbar-example-page">
    <h2>VNStatusBar 状态栏组件示例</h2>

    <div class="example-container">
      <p>模拟的应用主内容区域...</p>
      <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, voluptatum.</p>
    </div>

    <VNStatusBar 
      :items="statusBarItems" 
      left-title="常规信息"
      center-title="进度与状态"
      right-title="账户与通知"
    />

    <div class="example-container" style="margin-top: 30px;">
        <h3>动态更新示例</h3>
        <el-button @click="updateStatus">更新状态</el-button>
        <el-button @click="toggleLoading">切换加载状态</el-button>
        <pre>{{ JSON.stringify(statusBarItems.map(item => ({ ...item, icon: item.icon ? (typeof item.icon === 'string' ? item.icon : 'Component') : undefined, value: typeof item.value === 'object' ? 'VNode' : item.value, onClick: item.onClick ? 'Function' : undefined })), null, 2) }}</pre>
    </div>

     <div class="example-container" style="margin-top: 30px;">
        <h3>自定义分隔符和样式 (无标题)</h3>
         <VNStatusBar 
            :items="customStyleItems" 
            height="35px" 
            background-color="#304156" 
            text-color="#bfcbd9" 
            :separator="h('span', { style: 'margin: 0 8px; color: #666;' }, '•')"
        />
     </div>

  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue';
import VNStatusBar from './index.vue';
import type { StatusBarItem } from './types';
import { Check, Close, Loading, Bell, InfoFilled, WarningFilled } from '@element-plus/icons-vue';
import { ElTag, ElButton, ElProgress } from 'element-plus';

const isLoading = ref(false);

// Helper function to create a Progress VNode
const createProgress = (percentage: number) => {
  return h(ElProgress, {
    percentage: percentage,
    strokeWidth: 6,
    textInside: true,
    style: { width: '100px', verticalAlign: 'middle' }
  });
};

const statusBarItems = ref<StatusBarItem[]>([
  {
    id: 'status',
    label: '状态',
    value: '就绪',
    icon: Check,
    tooltip: '系统当前状态正常'
  },
  {
    id: 'version',
    label: '版本',
    value: '1.0.2',
    onClick: () => alert('当前版本: 1.0.2')
  },
  {
    id: 'progress',
    align: 'center',
    value: createProgress(30),
    separator: false // 此项后不显示分隔符
  },
  {
    id: 'loading',
    align: 'center', // 居中对齐
    icon: isLoading.value ? Loading : undefined,
    value: isLoading.value ? '处理中...' : '',
    tooltip: isLoading.value ? '后台任务正在运行' : '无活动任务',
    separator: false
  },
  {
    id: 'user',
    label: '用户',
    value: 'Admin',
    align: 'right', // 右对齐
    icon: 'User'
  },
  {
    id: 'notifications',
    align: 'right',
    icon: Bell,
    value: h(ElTag, { type: 'danger', size: 'small', effect: 'dark', round: true }, () => '3'), // 使用 VNode 自定义渲染值
    tooltip: '有 3 条未读通知',
    onClick: () => console.log('打开通知')
  }
]);

const customStyleItems = ref<StatusBarItem[]>([
    {
        id: 'info',
        icon: InfoFilled,
        value: '一些提示信息',
        tooltip: '提示'
    },
    {
        id: 'warning',
        icon: WarningFilled,
        value: '警告内容',
        tooltip: '警告',
        align: 'right'
    }
]);

const updateStatus = () => {
  const statusItem = statusBarItems.value.find(item => item.id === 'status');
  if (statusItem) {
    const now = new Date();
    statusItem.value = `已更新 @ ${now.toLocaleTimeString()}`;
    statusItem.icon = InfoFilled;
    statusItem.tooltip = '状态已更新';
  }
  const progressItem = statusBarItems.value.find(item => item.id === 'progress');
   if (progressItem) {
      const newPercentage = Math.min(100, (progressItem.value as any).props.percentage + 10);
      progressItem.value = createProgress(newPercentage);
   }
};

const toggleLoading = () => {
    isLoading.value = !isLoading.value;
    const loadingItem = statusBarItems.value.find(item => item.id === 'loading');
    if (loadingItem) {
        loadingItem.icon = isLoading.value ? Loading : undefined;
        loadingItem.value = isLoading.value ? '处理中...' : '';
        loadingItem.tooltip = isLoading.value ? '后台任务正在运行' : '无活动任务';
    }
};

</script>

<style scoped>
.statusbar-example-page {
  padding: 20px;
}

.example-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px; /* 与状态栏保持距离 */
  min-height: 150px; /* 模拟内容高度 */
}

h2 {
  margin-bottom: 20px;
}
h3 {
    margin-top: 0;
    margin-bottom: 15px;
}
pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  overflow: auto;
  margin-top: 15px;
  max-height: 200px;
}
</style> 