package vo

// MenuVO 菜单视图对象
// 用于向前端展示菜单信息
type MenuVO struct {
	TenantVO
	ParentID   uint   `json:"parentId"`   // 父菜单ID：上级菜单的ID，0表示顶级菜单
	Name       string `json:"name"`       // 菜单名称：菜单在系统中的名称标识
	Title      string `json:"title"`      // 标题：菜单在界面上显示的名称
	Path       string `json:"path"`       // 路由地址：前端路由的访问路径
	Component  string `json:"component"`  // 组件路径：前端组件的路径
	Redirect   string `json:"redirect"`   // 重定向路径：访问该菜单时重定向的目标路径
	Icon       string `json:"icon"`       // 菜单图标：菜单的图标名称或图标URL
	Type       int    `json:"type"`       // 菜单类型：1-目录（可包含子菜单的菜单项），2-菜单（可点击的菜单项），3-按钮（页面上的操作按钮）
	Permission string `json:"permission"` // 权限标识：用于权限控制的标识符，如"system:user:list"
	Status     int    `json:"status"`     // 状态：0-禁用（不可用），1-正常（可用）
	Sort       int    `json:"sort"`       // 排序：菜单的显示顺序，数值越小越靠前
	Hidden     bool   `json:"hidden"`     // 是否隐藏：true-隐藏（不在菜单中显示），false-显示
	NoCache    bool   `json:"noCache"`    // 是否不缓存：true-不缓存（每次都重新加载），false-缓存（使用缓存提高性能）
	AlwaysShow bool   `json:"alwaysShow"` // 是否总是显示：true-总是显示（即使只有一个子菜单也显示父菜单），false-自动判断
	Breadcrumb bool   `json:"breadcrumb"` // 是否显示面包屑：true-在面包屑中显示，false-在面包屑中隐藏
	ActiveMenu string `json:"activeMenu"` // 高亮菜单：当前路由为子路由时，指定高亮的父级菜单
	Remark     string `json:"remark"`     // 备注：关于菜单的附加说明信息
	IsExternal bool   `json:"isExternal"` // 是否外链：true-是外链（在新窗口打开），false-非外链
}

// MenuTreeVO 菜单树形结构视图对象
// 用于前端展示菜单树形结构
// 包含子菜单的递归结构
type MenuTreeVO struct {
	ID         uint          `json:"id"`         // 菜单ID：菜单的唯一标识
	ParentID   uint          `json:"parentId"`   // 父菜单ID：上级菜单的ID，0表示顶级菜单
	Name       string        `json:"name"`       // 菜单名称：菜单在系统中的名称标识
	Title      string        `json:"title"`      // 标题：菜单在界面上显示的名称
	Path       string        `json:"path"`       // 路由地址：前端路由的访问路径
	Component  string        `json:"component"`  // 组件路径：前端组件的路径
	Redirect   string        `json:"redirect"`   // 重定向路径：访问该菜单时重定向的目标路径
	Icon       string        `json:"icon"`       // 菜单图标：菜单的图标名称或图标URL
	Type       int           `json:"type"`       // 菜单类型：1-目录，2-菜单，3-按钮
	Permission string        `json:"permission"` // 权限标识：用于权限控制的标识符
	Sort       int           `json:"sort"`       // 排序：菜单的显示顺序
	Status     int           `json:"status"`     // 状态：菜单状态，0-禁用，1-正常
	Hidden     bool          `json:"hidden"`     // 是否隐藏：true-隐藏，false-显示
	NoCache    bool          `json:"noCache"`    // 是否不缓存：true-不缓存，false-缓存
	AlwaysShow bool          `json:"alwaysShow"` // 是否总是显示：true-总是显示，false-自动判断
	Breadcrumb bool          `json:"breadcrumb"` // 是否显示面包屑：true-显示，false-隐藏
	ActiveMenu string        `json:"activeMenu"` // 高亮菜单：当前路由为子路由时，指定高亮的父级菜单
	IsExternal bool          `json:"isExternal"` // 是否外链：true-是外链，false-非外链
	Children   []*MenuTreeVO `json:"children"`   // 子菜单：当前菜单的子菜单列表，递归结构
}

// MenuMetaVO 菜单元数据视图对象
// 用于前端路由配置中的meta字段
type MenuMetaVO struct {
	Title      string `json:"title"`      // 标题：菜单在界面上显示的名称
	Icon       string `json:"icon"`       // 图标：菜单的图标名称或图标URL
	NoCache    bool   `json:"noCache"`    // 是否不缓存：true-不缓存，false-缓存
	Hidden     bool   `json:"hidden"`     // 是否隐藏：true-隐藏，false-显示
	AlwaysShow bool   `json:"alwaysShow"` // 是否总是显示：true-总是显示，false-自动判断
	Breadcrumb bool   `json:"breadcrumb"` // 是否显示面包屑：true-显示，false-隐藏
	ActiveMenu string `json:"activeMenu"` // 高亮菜单：当前路由为子路由时，指定高亮的父级菜单
}

// RouterVO 前端路由视图对象
// 用于生成前端路由配置
type RouterVO struct {
	Name       string      `json:"name,omitempty"`       // 路由名称：路由的唯一标识，用于命名路由
	Path       string      `json:"path"`                 // 路由地址：前端路由的访问路径
	Component  string      `json:"component,omitempty"`  // 组件路径：前端组件的路径
	Redirect   string      `json:"redirect,omitempty"`   // 重定向路径：访问该路由时重定向的目标路径
	Hidden     bool        `json:"hidden,omitempty"`     // 是否隐藏：true-在导航中隐藏，false-在导航中显示
	AlwaysShow bool        `json:"alwaysShow,omitempty"` // 是否总是显示：true-总是显示，false-自动判断
	Meta       *MenuMetaVO `json:"meta,omitempty"`       // 元数据：路由的附加配置信息
	Children   []*RouterVO `json:"children,omitempty"`   // 子路由：当前路由的子路由列表
}

// MenuPermissionVO 菜单权限视图对象
// 用于展示菜单的权限标识
type MenuPermissionVO struct {
	ID         uint   `json:"id"`         // 菜单ID：菜单的唯一标识
	Name       string `json:"name"`       // 菜单名称：菜单在系统中的名称标识
	Permission string `json:"permission"` // 权限标识：用于权限控制的标识符
	Type       int    `json:"type"`       // 菜单类型：1-目录，2-菜单，3-按钮
}

// MenuSelectVO 菜单选择视图对象
// 用于菜单选择组件
type MenuSelectVO struct {
	ID       uint            `json:"id"`       // 菜单ID：菜单的唯一标识
	ParentID uint            `json:"parentId"` // 父菜单ID：上级菜单的ID，0表示顶级菜单
	Name     string          `json:"name"`     // 菜单名称：菜单在系统中的名称标识
	Title    string          `json:"title"`    // 标题：菜单在界面上显示的名称
	Type     int             `json:"type"`     // 菜单类型：1-目录，2-菜单，3-按钮
	Children []*MenuSelectVO `json:"children"` // 子菜单：当前菜单的子菜单列表
}
