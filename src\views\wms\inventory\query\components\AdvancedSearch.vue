<template>
  <div class="advanced-search-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>高级搜索</span>
          <el-button type="text" @click="$emit('close')">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      
      <el-form :model="searchForm" label-width="120px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="数量范围">
              <div class="quantity-range">
                <el-input-number
                  v-model="searchForm.quantityMin"
                  placeholder="最小数量"
                  :min="0"
                  :precision="2"
                  style="width: 120px"
                />
                <span class="range-separator">-</span>
                <el-input-number
                  v-model="searchForm.quantityMax"
                  placeholder="最大数量"
                  :min="0"
                  :precision="2"
                  style="width: 120px"
                />
              </div>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="过期日期范围">
              <el-date-picker
                v-model="expiryDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleExpiryDateChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="生产日期范围">
              <el-date-picker
                v-model="productionDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleProductionDateChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="最后更新时间">
              <el-date-picker
                v-model="lastUpdatedRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="handleLastUpdatedChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="物料分类">
              <el-select
                v-model="searchForm.itemCategory"
                placeholder="请选择物料分类"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="category in categoryOptions"
                  :key="category.value"
                  :label="category.label"
                  :value="category.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="物料品牌">
              <el-select
                v-model="searchForm.itemBrand"
                placeholder="请选择物料品牌"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="brand in brandOptions"
                  :key="brand.value"
                  :label="brand.label"
                  :value="brand.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="库位类型">
              <el-select
                v-model="searchForm.locationType"
                placeholder="请选择库位类型"
                clearable
                style="width: 100%"
              >
                <el-option label="存储位" value="STORAGE" />
                <el-option label="拣货位" value="PICKING" />
                <el-option label="暂存位" value="STAGING" />
                <el-option label="收货位" value="RECEIVING" />
                <el-option label="发货位" value="SHIPPING" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="库位区域">
              <el-select
                v-model="searchForm.locationZone"
                placeholder="请选择库位区域"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="zone in zoneOptions"
                  :key="zone.value"
                  :label="zone.label"
                  :value="zone.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="筛选选项">
              <el-checkbox-group v-model="filterOptions">
                <el-checkbox label="includeZero">包含零库存</el-checkbox>
                <el-checkbox label="onlyAvailable">仅显示可用库存</el-checkbox>
                <el-checkbox label="onlyExpiring">仅显示即将过期</el-checkbox>
                <el-checkbox label="onlyExpired">仅显示已过期</el-checkbox>
                <el-checkbox label="onlyLowStock">仅显示低库存</el-checkbox>
                <el-checkbox label="onlyHighStock">仅显示高库存</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div class="search-actions">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button @click="handleSaveTemplate">
          <el-icon><Collection /></el-icon>
          保存模板
        </el-button>
        <el-button @click="handleLoadTemplate">
          <el-icon><FolderOpened /></el-icon>
          加载模板
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElCard, ElForm, ElFormItem, ElRow, ElCol, ElInputNumber, ElDatePicker, ElSelect, ElOption, ElCheckboxGroup, ElCheckbox, ElButton, ElIcon, ElMessage } from 'element-plus'
import { Close, Search, Refresh, Collection, FolderOpened } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'search': []
  'reset': []
  'close': []
}>()

// 响应式数据
const searchForm = reactive({ ...props.modelValue })

// 日期范围
const expiryDateRange = ref<[string, string] | null>(null)
const productionDateRange = ref<[string, string] | null>(null)
const lastUpdatedRange = ref<[string, string] | null>(null)

// 筛选选项
const filterOptions = ref<string[]>([])

// 选项数据
const categoryOptions = ref([
  { label: '原材料', value: 'RAW_MATERIAL' },
  { label: '半成品', value: 'SEMI_FINISHED' },
  { label: '成品', value: 'FINISHED_GOODS' },
  { label: '包装材料', value: 'PACKAGING' },
  { label: '辅助材料', value: 'AUXILIARY' }
])

const brandOptions = ref([
  { label: '品牌A', value: 'BRAND_A' },
  { label: '品牌B', value: 'BRAND_B' },
  { label: '品牌C', value: 'BRAND_C' }
])

const zoneOptions = ref([
  { label: 'A区', value: 'ZONE_A' },
  { label: 'B区', value: 'ZONE_B' },
  { label: 'C区', value: 'ZONE_C' },
  { label: 'D区', value: 'ZONE_D' }
])

// 方法
const handleExpiryDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.expiryDateStart = dates[0]
    searchForm.expiryDateEnd = dates[1]
  } else {
    searchForm.expiryDateStart = undefined
    searchForm.expiryDateEnd = undefined
  }
}

const handleProductionDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.productionDateStart = dates[0]
    searchForm.productionDateEnd = dates[1]
  } else {
    searchForm.productionDateStart = undefined
    searchForm.productionDateEnd = undefined
  }
}

const handleLastUpdatedChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.lastUpdatedStart = dates[0]
    searchForm.lastUpdatedEnd = dates[1]
  } else {
    searchForm.lastUpdatedStart = undefined
    searchForm.lastUpdatedEnd = undefined
  }
}

const handleSearch = () => {
  // 应用筛选选项
  filterOptions.value.forEach(option => {
    searchForm[option] = true
  })
  
  emit('update:modelValue', searchForm)
  emit('search')
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (typeof searchForm[key] === 'boolean') {
      searchForm[key] = false
    } else {
      searchForm[key] = undefined
    }
  })
  
  expiryDateRange.value = null
  productionDateRange.value = null
  lastUpdatedRange.value = null
  filterOptions.value = []
  
  emit('update:modelValue', searchForm)
  emit('reset')
}

const handleSaveTemplate = () => {
  // 保存搜索模板到本地存储
  const template = {
    searchForm: { ...searchForm },
    expiryDateRange: expiryDateRange.value,
    productionDateRange: productionDateRange.value,
    lastUpdatedRange: lastUpdatedRange.value,
    filterOptions: [...filterOptions.value]
  }
  
  localStorage.setItem('inventory_search_template', JSON.stringify(template))
  ElMessage.success('搜索模板已保存')
}

const handleLoadTemplate = () => {
  // 从本地存储加载搜索模板
  const templateStr = localStorage.getItem('inventory_search_template')
  if (templateStr) {
    try {
      const template = JSON.parse(templateStr)
      Object.assign(searchForm, template.searchForm)
      expiryDateRange.value = template.expiryDateRange
      productionDateRange.value = template.productionDateRange
      lastUpdatedRange.value = template.lastUpdatedRange
      filterOptions.value = template.filterOptions || []
      
      ElMessage.success('搜索模板已加载')
    } catch (error) {
      ElMessage.error('加载搜索模板失败')
    }
  } else {
    ElMessage.warning('未找到保存的搜索模板')
  }
}

// 监听搜索表单变化
watch(searchForm, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })
</script>

<style scoped>
.advanced-search-container {
  margin-top: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #909399;
  font-weight: 500;
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}
</style>
