import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStoreHook } from '@/store/modules/user'; // 导入 User Store Hook
import { ElMessage } from 'element-plus'; // 导入 ElMessage

// Import the login page component
import LoginPage from '../views/system/login/Index.vue'
// Import the main layout component
import MainFrame from '../views/system/mainFrame/index.vue'

// 导入组件示例路由和布局
import { exampleRoutes } from '@/components/exampleRoutes'
import ExamplesLayout from '@/views/examples/Layout.vue'

// Define main application routes here
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: LoginPage,
    meta: { title: '系统登陆', requiresAuth: false } // Login page doesn't require auth
  },
  {
    // Main layout route
    path: '/',
    name: 'Home',
    component: MainFrame,
    meta: { title: '主页', requiresAuth: true },
    redirect: '/profile',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/index.vue'),
        meta: { title: '仪表盘', requiresAuth: true }
      },
      {
        path: 'sys',
        name: 'System',
        meta: { title: '系统管理', requiresAuth: true },
        redirect: { name: 'SystemAccountBook' },
        children: [
          {
            path: 'user',
            name: 'SystemUser',
            component: () => import('@/views/system/user/index.vue'),
            meta: { title: '用户管理', requiresAuth: true , permission: 'sys:user:list'}
          },
          {
            path: 'role',
            name: 'SystemRole',
            component: () => import('@/views/system/role/index.vue'),
            meta: { title: '角色管理', requiresAuth: true , permission: 'sys:role:list'}
          },
          {
            path: 'menu',
            name: 'SystemMenu',
            component: () => import('@/views/system/menu/index.vue'),
            meta: { title: '菜单管理', requiresAuth: true , permission: 'sys:menu:list'}
          },
          {
            path: 'accountbook',
            name: 'SystemAccountBook',
            component: () => import('@/views/system/accountBook/index.vue'),
            meta: { title: '账套管理', requiresAuth: true , permission: 'sys:accountbook:list'}
          },
          {
            path: 'dict',
            name: 'SystemDict',
            component: () => import('@/views/system/dict/index.vue'),
            meta: { title: '数据字典', requiresAuth: true , permission: 'sys:dict:list'}
          },
          {
            path: 'parameter',
            name: 'SystemParameter',
            component: () => import('@/views/system/parameter/index.vue'),
            meta: { title: '系统参数', requiresAuth: true , permission: 'sys:parameter:list'}
          },
          // 编码规则 codeRule
          {
            path: 'code-rule',
            name: 'CodeRule',
            component: () => import('@/views/system/codeRule/index.vue'),
            meta: { title: '编码规则', requiresAuth: true , permission: 'sys:code-rule:list'}
          },
          {
            path: 'sql-tool',
            name: 'SqlTool',
            component: () => import('@/views/system/sql-tool/index.vue'),
            meta: { 
              title: 'SQL 工具', 
              requiresAuth: true, 
              requiresAdmin: true
            }
          },
          // --- 添加授权管理路由 ---
          {
            path: 'license',
            name: 'License',
            component: () => import('@/views/system/license/index.vue'),
            meta: { 
              title: '授权管理', 
              requiresAuth: true, 
              permisson: 'sys:license:list' 
            }
          },
          {
            path: 'audit',
            name: 'Audit',
            component: () => import('@/views/system/auditLog/index.vue'),
            meta: { title: '审计日志', requiresAuth: true, permission:'sys:audit:list' }
          },
          // ---------------------
        ]
      },
      // --- HR Module Start ---
      {
        path: 'hr',
        name: 'HR',
        meta: { title: '人事管理', requiresAuth: true, icon: 'OfficeBuilding' }, // Added icon
        redirect: { name: 'Organization' }, // Redirect to the first HR child route
        children: [
          {
            path: 'organization',
            name: 'Organization',
            component: () => import('@/views/hr/organization/index.vue'), // Use @ alias
            meta: { title: '组织结构', requiresAuth: true, permission: 'hr:org:list' } // Added permission check
          },
          // Add other HR module routes here, e.g., employee management
          {
            path: 'employee',
            name: 'Employee',
            component: () => import('@/views/hr/employee/index.vue'),
            meta: { title: '员工管理', requiresAuth: true, permission: 'hr:employee:list' } // Base permission for page access
          }
        ]
      },
      // --- HR Module End ---
      // --- Finance Module Start ---
      {
        path: 'fin',
        name: 'Finance',
        meta: { title: '财务管理', requiresAuth: true, icon: 'Coin' },
        redirect: { name: 'FiscalPeriod' },
        children: [
          {
            path: 'periods',
            name: 'FiscalPeriod',
            component: () => import('@/views/fin/fiscalPeriod/index.vue'),
            meta: { title: '帐期设置', requiresAuth: true, permission: 'fin:periods:list' }
          },
          {
            path: 'currency',
            name: 'FinCurrency',
            component: () => import('@/views/fin/currency/index.vue'),
            meta: { title: '币种管理', requiresAuth: true, permission: 'fin:currency:list' }
          },
          {
            path: 'tax-rate',
            name: 'FinTaxRate',
            component: () => import('@/views/fin/taxRate/index.vue'),
            meta: { title: '税率管理', requiresAuth: true, permission: 'fin:taxrate:list' }
          },
          {
            path: 'exchange-rate',
            name: 'FinExchangeRate',
            component: () => import('@/views/fin/exchangeRate/index.vue'),
            meta: { title: '汇率管理', requiresAuth: true, permission: 'fin:exchangerate:list' }
          }
        ]
      },
      // --- Finance Module End ---
      // --- WMS Module Start ---
      {
        path: 'wms',
        name: 'WMS',
        meta: { title: '仓储管理', requiresAuth: true, icon: 'Box' },
        redirect: { name: 'WmsDashboard' },
        children: [
          // 仪表板
          {
            path: 'dashboard',
            name: 'WmsDashboard',
            component: () => import('@/views/wms/dashboard/index.vue'),
            meta: { title: '仓库概览', requiresAuth: true, permission: 'wms:dashboard:view', icon: 'DataBoard' }
          },

          // 入库管理
          {
            path: 'inbound',
            name: 'WmsInboundManagement',
            meta: { title: '入库管理', requiresAuth: true, permission: 'wms:inbound:view', icon: 'Download' },
            children: [
              {
                path: 'notification',
                name: 'WmsInboundNotification',
                component: () => import('@/views/wms/inbound-notification/index.vue'),
                meta: { title: '入库通知单', requiresAuth: true, permission: 'wms:inbound:notification:view', icon: 'Document' }
              },
              {
                path: 'receiving',
                name: 'WmsReceivingRecord',
                component: () => import('@/views/wms/receiving-record/index.vue'),
                meta: { title: '收货记录', requiresAuth: true, permission: 'wms:inbound:receiving:view', icon: 'Box' }
              },
              {
                path: 'putaway',
                name: 'WmsPutawayTask',
                component: () => import('@/views/wms/putaway-task/index.vue'),
                meta: { title: '上架任务', requiresAuth: true, permission: 'wms:inbound:putaway:view', icon: 'Upload' }
              },
              {
                path: 'putaway-mobile/:id',
                name: 'WmsPutawayTaskMobile',
                component: () => import('@/views/wms/putaway-task/mobile.vue'),
                meta: { title: '移动端上架', requiresAuth: true, permission: 'wms:inbound:putaway:mobile', hideInMenu: true }
              }
            ]
          },

          // 出库管理
          {
            path: 'outbound',
            name: 'WmsOutboundManagement',
            meta: { title: '出库管理', requiresAuth: true, permission: 'wms:outbound:view', icon: 'Upload' },
            children: [
              {
                path: 'notification',
                name: 'WmsOutboundNotification',
                component: () => import('@/views/wms/outbound-notification/index.vue'),
                meta: { title: '出库通知单', requiresAuth: true, permission: 'wms:outbound:notification:view', icon: 'Document' }
              },
              {
                path: 'picking-task',
                name: 'WmsPickingTask',
                component: () => import('@/views/wms/picking-task/index.vue'),
                meta: { title: '拣货任务', requiresAuth: true, permission: 'wms:outbound:picking:view', icon: 'ShoppingCart' }
              },
              {
                path: 'picking-mobile/:id',
                name: 'WmsPickingTaskMobile',
                component: () => import('@/views/wms/picking-task/mobile.vue'),
                meta: { title: '移动端拣货', requiresAuth: true, permission: 'wms:outbound:picking:mobile', hideInMenu: true }
              },
              {
                path: 'shipment',
                name: 'WmsShipment',
                component: () => import('@/views/wms/shipment/index.vue'),
                meta: { title: '发运管理', requiresAuth: true, permission: 'wms:outbound:shipment:view', icon: 'Van' }
              }
            ]
          },

          // 库存管理
          {
            path: 'inventory',
            name: 'WmsInventoryManagement',
            meta: { title: '库存管理', requiresAuth: true, permission: 'wms:inventory:view', icon: 'Goods' },
            children: [
              {
                path: 'overview',
                name: 'WmsInventoryOverview',
                component: () => import('@/views/wms/inventory/overview/index.vue'),
                meta: { title: '库存总览', requiresAuth: true, permission: 'wms:inventory:overview:view', icon: 'DataBoard' }
              },
              {
                path: 'query',
                name: 'WmsInventoryQuery',
                component: () => import('@/views/wms/inventory/query/index.vue'),
                meta: { title: '库存查询', requiresAuth: true, permission: 'wms:inventory:query:view', icon: 'Search' }
              },
              {
                path: 'adjustment',
                name: 'WmsInventoryAdjustment',
                component: () => import('@/views/wms/inventory/adjustment/index.vue'),
                meta: { title: '库存调整', requiresAuth: true, permission: 'wms:inventory:adjustment:view', icon: 'Edit' }
              },
              {
                path: 'counting',
                name: 'WmsInventoryCounting',
                component: () => import('@/views/wms/inventory/counting/index.vue'),
                meta: { title: '盘点管理', requiresAuth: true, permission: 'wms:inventory:counting:view', icon: 'DataAnalysis' }
              }
            ]
          },

          // 基础数据管理
          {
            path: 'master-data',
            name: 'WmsMasterDataManagement',
            meta: { title: '基础数据', requiresAuth: true, permission: 'wms:master:view', icon: 'Setting' },
            children: [
              {
                path: 'warehouse',
                name: 'WmsWarehouseManagement',
                component: () => import('@/views/wms/master-data/warehouse/index.vue'),
                meta: { title: '仓库管理', requiresAuth: true, permission: 'wms:master:warehouse:view', icon: 'OfficeBuilding' }
              },
              {
                path: 'location',
                name: 'WmsLocationManagement',
                component: () => import('@/views/wms/master-data/location/index.vue'),
                meta: { title: '库位管理', requiresAuth: true, permission: 'wms:master:location:view', icon: 'Grid' }
              },
              {
                path: 'item',
                name: 'WmsItemManagement',
                component: () => import('@/views/wms/master-data/item/index.vue'),
                meta: { title: '物料管理', requiresAuth: true, permission: 'wms:master:item:view', icon: 'Box' }
              },
              {
                path: 'client',
                name: 'WmsClientManagement',
                component: () => import('@/views/wms/master-data/client/index.vue'),
                meta: { title: '客户管理', requiresAuth: true, permission: 'wms:master:client:view', icon: 'User' }
              },
              {
                path: 'supplier',
                name: 'WmsSupplierManagement',
                component: () => import('@/views/wms/master-data/supplier/index.vue'),
                meta: { title: '供应商管理', requiresAuth: true, permission: 'wms:master:supplier:view', icon: 'UserFilled' }
              },
              {
                path: 'carrier',
                name: 'WmsCarrierManagement',
                component: () => import('@/views/wms/master-data/carrier/index.vue'),
                meta: { title: '承运商管理', requiresAuth: true, permission: 'wms:master:carrier:view', icon: 'Van' }
              }
            ]
          },

          // 报表分析
          {
            path: 'reports',
            name: 'WmsReportsManagement',
            meta: { title: '报表分析', requiresAuth: true, permission: 'wms:reports:view', icon: 'DataAnalysis' },
            children: [
              {
                path: 'inbound-report',
                name: 'WmsInboundReport',
                component: () => import('@/views/wms/reports/inbound/index.vue'),
                meta: { title: '入库报表', requiresAuth: true, permission: 'wms:reports:inbound:view', icon: 'TrendCharts' }
              },
              {
                path: 'outbound-report',
                name: 'WmsOutboundReport',
                component: () => import('@/views/wms/reports/outbound/index.vue'),
                meta: { title: '出库报表', requiresAuth: true, permission: 'wms:reports:outbound:view', icon: 'TrendCharts' }
              },
              {
                path: 'inventory-report',
                name: 'WmsInventoryReport',
                component: () => import('@/views/wms/reports/inventory/index.vue'),
                meta: { title: '库存报表', requiresAuth: true, permission: 'wms:reports:inventory:view', icon: 'TrendCharts' }
              },
              {
                path: 'performance-report',
                name: 'WmsPerformanceReport',
                component: () => import('@/views/wms/reports/performance/index.vue'),
                meta: { title: '绩效报表', requiresAuth: true, permission: 'wms:reports:performance:view', icon: 'TrendCharts' }
              }
            ]
          },

          // 系统配置
          {
            path: 'config',
            name: 'WmsConfigManagement',
            meta: { title: '系统配置', requiresAuth: true, permission: 'wms:config:view', icon: 'Tools' },
            children: [
              {
                path: 'strategy',
                name: 'WmsStrategyConfig',
                component: () => import('@/views/wms/config/strategy/index.vue'),
                meta: { title: '策略配置', requiresAuth: true, permission: 'wms:config:strategy:view', icon: 'SetUp' }
              },
              {
                path: 'rule',
                name: 'WmsRuleConfig',
                component: () => import('@/views/wms/config/rule/index.vue'),
                meta: { title: '规则配置', requiresAuth: true, permission: 'wms:config:rule:view', icon: 'List' }
              },
              {
                path: 'workflow',
                name: 'WmsWorkflowConfig',
                component: () => import('@/views/wms/config/workflow/index.vue'),
                meta: { title: '流程配置', requiresAuth: true, permission: 'wms:config:workflow:view', icon: 'Connection' }
              },
              {
                path: 'integration',
                name: 'WmsIntegrationConfig',
                component: () => import('@/views/wms/config/integration/index.vue'),
                meta: { title: '集成配置', requiresAuth: true, permission: 'wms:config:integration:view', icon: 'Link' }
              },
              // 保留原有的配置项
              {
                path: 'blind-receiving-config',
                name: 'WmsBlindReceivingConfig',
                component: () => import('@/views/wms/blind-receiving-config/index.vue'),
                meta: { title: '盲收配置', requiresAuth: true, permission: 'wms:blind-receiving-config:list', icon: 'View' }
              },
              {
                path: 'blind-receiving-validation',
                name: 'WmsBlindReceivingValidation',
                component: () => import('@/views/wms/blind-receiving-validation/index.vue'),
                meta: { title: '盲收验证记录', requiresAuth: true, permission: 'wms:blind-receiving-validation:list', icon: 'DocumentChecked' }
              }
            ]
          }
        ]
      },
      // --- WMS Module End ---
      // --- CRM Module Start ---
      {
        path: 'crm',
        name: 'CRM',
        meta: { title: '客户关系管理', requiresAuth: true, icon: 'UserFilled' },
        redirect: { name: 'CrmCustomer' },
        children: [
          {
            path: 'customer',
            name: 'CrmCustomer',
            component: () => import('@/views/crm/customer/index.vue'),
            meta: { title: '客户管理', requiresAuth: true, permission: 'crm:customer:list' }
          }
        ]
      },
      // --- CRM Module End ---
      // --- SCM Module Start ---
      {
        path: 'scm',
        name: 'SCM',
        meta: { title: '供应链管理', requiresAuth: true, icon: 'TruckFilled' },
        redirect: { name: 'ScmSupplier' },
        children: [
          {
            path: 'supplier',
            name: 'ScmSupplier',
            component: () => import('@/views/scm/supplier/index.vue'),
            meta: { title: '供应商管理', requiresAuth: true, permission: 'scm:supplier:list' }
          }
        ]
      },
      // --- SCM Module End ---
      // --- Add Profile Route --- 
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/index.vue'), // Use @ alias
        meta: { title: '个人中心', requiresAuth: true, icon: 'User' } // Added optional icon
      },
      // --- 添加组件示例路由 --- 
      {
        path: 'component-examples', // 基础路径
        name: 'ComponentExamples',
        component: ExamplesLayout, // 使用示例布局组件
        redirect: '/component-examples/vntable-example', // 重定向到第一个示例
        meta: { title: '组件示例', requiresAuth: true, icon: 'Menu' }, // 添加 Meta 信息
        children: exampleRoutes.map(route => ({ // 将 exampleRoutes 作为子路由
          ...route,
          path: route.path.substring(1) // 移除子路由开头的 /，因为父路径已经是 /component-examples
        }))
      },
      // ---------------------
      // Add routes for other children like /profile, /settings here
      // {
      //   path: 'profile',
      //   name: 'Profile',
      //   component: () => import('../views/profile/Index.vue'),
      //   meta: { title: '个人中心', requiresAuth: true }
      // },
      // {
      //   path: 'settings',
      //   name: 'Settings',
      //   component: () => import('../views/settings/Index.vue'),
      //   meta: { title: '设置', requiresAuth: true }
      // },
    ]
  },
  // --- Add other top-level routes if necessary (e.g., 404 page) ---
  // Example 404 route - usually placed last
  // {
  //   path: '/:pathMatch(.*)*',
  //   name: 'NotFound',
  //   component: () => import('../views/NotFound.vue')
  // }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// --- Enable and Refine Navigation Guards ---
router.beforeEach((to, from, next) => {
  const userStore = useUserStoreHook(); // 获取 store 实例
  const isAuthenticated = !!userStore.token; // 从 store 获取认证状态

  const requiresAuth = to.matched.some(record => record.meta['requiresAuth']);
  const requiresAdmin = to.matched.some(record => record.meta['requiresAdmin']); // 检查是否需要管理员权限

  if (requiresAuth && !isAuthenticated) {
    // 需要认证但未登录 -> 跳转登录页
    console.log(`需要认证 (${to.fullPath})，但用户未登录，重定向到 /login`);
    next({ name: 'Login', query: { redirect: to.fullPath } });
  } else if (to.name === 'Login' && isAuthenticated) {
    // 已登录但访问登录页 -> 跳转来源页或首页
    const redirectPath = from.query['redirect'] as string || '/';
    console.log(`用户已登录，尝试访问登录页，重定向到 ${redirectPath}`);
    next({ path: redirectPath });
  } else if (requiresAdmin && (!userStore.userInfo || !userStore.userInfo.isAdmin)) {
    // 需要管理员权限但当前用户不是管理员 -> 阻止导航并提示
    console.warn(`需要管理员权限 (${to.fullPath})，但当前用户不是管理员。阻止导航。`);
    ElMessage.error('您没有权限访问此页面');
    next(false); // 阻止导航
    // 或者可以重定向到无权限页面: next({ name: 'Forbidden' }); 
    // 或者重定向到首页: next({ path: '/' });
  } else {
    // 其他情况 (无需认证，或已认证且权限足够) -> 允许导航
    console.log(`允许导航到 ${to.fullPath}`);
    next();
  }
});

export default router 