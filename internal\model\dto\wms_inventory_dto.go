// internal/model/dto/wms_inventory_dto.go
package dto

import (
	"database/sql"
	"time"

	"backend/internal/model/entity"
	"backend/pkg/response"
)

// WmsInventoryCreateReq 库存创建请求
type WmsInventoryCreateReq struct {
	ItemID         uint                      `json:"itemId" binding:"required" comment:"物料ID"`
	LocationID     uint                      `json:"locationId" binding:"required" comment:"库位ID"`
	BatchNo        *string                   `json:"batchNo" comment:"批次号"`
	Quantity       float64                   `json:"quantity" binding:"required,gte=0" comment:"库存数量"`
	UnitOfMeasure  string                    `json:"unitOfMeasure" binding:"required" comment:"计量单位"`
	Status         entity.WmsInventoryStatus `json:"status" comment:"库存状态"`
	ExpiryDate     *time.Time                `json:"expiryDate" comment:"过期日期"`
	ProductionDate *time.Time                `json:"productionDate" comment:"生产日期"`
	Remark         *string                   `json:"remark" comment:"备注"`
}

// WmsInventoryUpdateReq 库存更新请求
type WmsInventoryUpdateReq struct {
	LocationID     *uint                      `json:"locationId" comment:"库位ID"`
	BatchNo        *string                    `json:"batchNo" comment:"批次号"`
	Quantity       *float64                   `json:"quantity" binding:"omitempty,gte=0" comment:"库存数量"`
	UnitOfMeasure  *string                    `json:"unitOfMeasure" comment:"计量单位"`
	Status         *entity.WmsInventoryStatus `json:"status" comment:"库存状态"`
	ExpiryDate     *time.Time                 `json:"expiryDate" comment:"过期日期"`
	ProductionDate *time.Time                 `json:"productionDate" comment:"生产日期"`
	Remark         *string                    `json:"remark" comment:"备注"`
}

// WmsInventoryTransferReq 定义库存转移请求的 DTO
// 用于封装 WmsInventoryService.TransferInventory 方法的参数
type WmsInventoryTransferReq struct {
	AccountBookID    uint                      `json:"accountBookId" binding:"required"`    // 账套 ID
	ItemID           uint                      `json:"itemId" binding:"required"`           // 物料 ID
	Quantity         float64                   `json:"quantity" binding:"required,gt=0"`    // 转移数量 (必须大于0)
	UnitOfMeasure    string                    `json:"unitOfMeasure" binding:"required"`    // 计量单位
	SourceLocationID uint                      `json:"sourceLocationId" binding:"required"` // 源库位 ID
	TargetLocationID uint                      `json:"targetLocationId" binding:"required"` // 目标库位 ID
	SourceStatus     entity.WmsInventoryStatus `json:"sourceStatus" binding:"required"`     // 源库存状态
	TargetStatus     entity.WmsInventoryStatus `json:"targetStatus" binding:"required"`     // 目标库存状态
	BatchNo          sql.NullString            `json:"batchNo"`                             // 批次号 (可选)
	ProductionDate   sql.NullTime              `json:"productionDate"`                      // 生产日期 (可选)
	ExpiryDate       sql.NullTime              `json:"expiryDate"`                          // 失效日期 (可选)
	ClientID         *uint                     `json:"clientId"`                            // 客户 ID (可选, 使用指针表示可为空)
	TransactionType  string                    `json:"transactionType" binding:"required"`  // 交易类型 (例如: "PUTAWAY_EXEC")
	RefDocType       string                    `json:"refDocType"`                          // 关联单据类型 (可选, 例如: "PUTAWAY_TASK_DETAIL")
	RefDocID         *uint                     `json:"refDocId"`                            // 关联单据 ID (可选)
	RefDocLineID     *uint                     `json:"refDocLineId"`                        // 关联单据行 ID (可选)
	OperatorID       *uint                     `json:"operatorId"`                          // 操作员 ID (可选, 从 Context 获取)
	ReasonCode       string                    `json:"reasonCode"`                          // 原因代码 (可选)
	Timestamp        time.Time                 `json:"-"`                                   // 操作时间戳 (通常由服务层设置，不在请求中传递)
}

// QueryInventoryParams 定义库存查询请求的 DTO (原 WmsInventoryQueryReq)
type QueryInventoryParams struct {
	WarehouseID    *uint      `form:"warehouseId" json:"warehouseId"`                                // 新增：仓库 ID (可选)
	ItemID         *uint      `form:"itemId" json:"itemId"`                                          // 物料 ID (可选)
	ItemSKU        *string    `form:"itemSku" json:"itemSku"`                                        // 物料 SKU (可选, 模糊匹配)
	LocationID     *uint      `form:"locationId" json:"locationId"`                                  // 库位 ID (可选)
	LocationCode   *string    `form:"locationCode" json:"locationCode"`                              // 库位编码 (可选, 模糊匹配)
	BatchNo        *string    `form:"batchNo" json:"batchNo"`                                        // 批次号 (可选, 精确匹配)
	SerialNo       *string    `form:"serialNo" json:"serialNo"`                                      // 序列号 (可选, 精确匹配)
	ClientID       *uint      `form:"clientId" json:"clientId"`                                      // 客户 ID (可选)
	Status         *string    `form:"status" json:"status"`                                          // 库存状态 (可选, entity.WmsInventoryStatus)
	HasQuantity    *bool      `form:"hasQuantity" json:"hasQuantity"`                                // 是否只显示有数量的库存 (可选, true/false)
	ExpiryDateFrom *time.Time `form:"expiryDateFrom" json:"expiryDateFrom" time_format:"2006-01-02"` // 失效日期起 (可选)
	ExpiryDateTo   *time.Time `form:"expiryDateTo" json:"expiryDateTo" time_format:"2006-01-02"`     // 失效日期止 (可选)
	SortField      string     `form:"sortField" json:"sortField"`                                    // 新增：排序字段
	SortOrder      string     `form:"sortOrder" json:"sortOrder"`                                    // 新增：排序顺序 (asc/desc)

	Pagination response.PageQuery `json:"-"` // 分页参数，不参与JSON序列化
}

// WmsInventoryResp 定义库存查询响应的 DTO
type WmsInventoryResp struct {
	ID             uint                      `json:"id"`
	AccountBookID  uint                      `json:"accountBookId"`
	WarehouseID    uint                      `json:"warehouseId"`
	ItemID         uint                      `json:"itemId"`
	ItemSKU        string                    `json:"itemSku,omitempty"`
	ItemName       string                    `json:"itemName,omitempty"`
	LocationID     uint                      `json:"locationId"`
	LocationCode   string                    `json:"locationCode,omitempty"`
	LocationName   string                    `json:"locationName,omitempty"`
	ClientID       uint                      `json:"clientId"` // 新增：客户ID
	BatchNo        sql.NullString            `json:"batchNo"`
	SerialNo       sql.NullString            `json:"serialNo"` // 新增：序列号
	Quantity       float64                   `json:"quantity"`
	AllocatedQty   float64                   `json:"allocatedQty"`
	FrozenQty      float64                   `json:"frozenQty"`
	AvailableQty   float64                   `json:"availableQty"`
	UnitOfMeasure  string                    `json:"unitOfMeasure"`
	Status         entity.WmsInventoryStatus `json:"status"`
	ProductionDate *time.Time                `json:"productionDate,omitempty"`
	ExpiryDate     *time.Time                `json:"expiryDate,omitempty"`
	CreatedAt      time.Time                 `json:"createdAt"`
	UpdatedAt      time.Time                 `json:"updatedAt"`
}

// AllocateInventoryParams 定义库存分配操作的参数 DTO
type AllocateInventoryParams struct {
	AccountBookID    uint                      `json:"accountBookId" binding:"required"` // 账套 ID
	WarehouseID      uint                      `json:"warehouseId" binding:"required"`   // 仓库 ID
	LocationID       *uint                     `json:"locationId"`                       // 源库位 ID (可选，如果指定，则只从此库位分配)
	ItemID           uint                      `json:"itemId" binding:"required"`        // 物料 ID
	ClientID         uint                      `json:"clientId" binding:"required"`      // 客户/货主 ID
	BatchNo          sql.NullString            `json:"batchNo"`                          // 源批次号 (可选，如果指定，则只分配此批次)
	SerialNo         sql.NullString            `json:"serialNo"`                         // 源序列号 (可选，如果指定，则只分配此序列号)
	SourceStatus     entity.WmsInventoryStatus `json:"sourceStatus" binding:"required"`  // 源库存状态 (必须指定从哪个状态分配，通常是 AVAILABLE)
	TargetStatus     entity.WmsInventoryStatus `json:"targetStatus" binding:"required"`  // 目标库存状态 (分配后的状态，通常是 ALLOCATED 或 PENDING_PICK)
	Quantity         float64                   `json:"quantity" binding:"required,gt=0"` // 需要分配的数量
	UnitOfMeasure    string                    `json:"unitOfMeasure" binding:"required"` // 计量单位
	RefDocType       sql.NullString            `json:"refDocType"`                       // 关联单据类型
	RefDocID         sql.NullInt64             `json:"refDocId"`                         // 关联单据头 ID
	RefDocLineID     sql.NullInt64             `json:"refDocLineId"`                     // 关联单据行 ID
	OperatorID       uint                      `json:"operatorId" binding:"required"`    // 操作员 ID (从 Context 获取)
	RequireFullBatch bool                      `json:"requireFullBatch"`                 // 是否要求必须分配完整批次 (例如，整批分配场景)
}

// WmsInventoryChangeStatusReq 定义库存状态变更请求 DTO
type WmsInventoryChangeStatusReq struct {
	WarehouseID   uint    `json:"warehouseId" binding:"required"`   // 仓库 ID
	LocationID    uint    `json:"locationId" binding:"required"`    // 库位 ID
	ItemID        uint    `json:"itemId" binding:"required"`        // 物料 ID
	ClientID      uint    `json:"clientId" binding:"required"`      // 客户/货主 ID
	BatchNo       *string `json:"batchNo"`                          // 批次号 (可选)
	SourceStatus  string  `json:"sourceStatus" binding:"required"`  // 源库存状态
	TargetStatus  string  `json:"targetStatus" binding:"required"`  // 目标库存状态
	Quantity      float64 `json:"quantity" binding:"required,gt=0"` // 需要变更状态的数量
	UnitOfMeasure string  `json:"unitOfMeasure" binding:"required"` // 计量单位
	RefDocType    *string `json:"refDocType,omitempty"`             // 关联单据类型 (可选)
	RefDocID      *uint   `json:"refDocId,omitempty"`               // 关联单据头 ID (可选)
	RefDocLineID  *uint   `json:"refDocLineId,omitempty"`           // 关联单据行 ID (可选)
	Remark        *string `json:"remark,omitempty"`                 // 备注 (可选)
}

// WmsInventoryMoveReq 定义库存移动请求 DTO
type WmsInventoryMoveReq struct {
	SourceLocationID uint    `json:"sourceLocationId" binding:"required"` // 源库位 ID
	TargetLocationID uint    `json:"targetLocationId" binding:"required"` // 目标库位 ID
	ItemID           uint    `json:"itemId" binding:"required"`           // 物料 ID
	ClientID         uint    `json:"clientId" binding:"required"`         // 客户/货主 ID
	BatchNo          *string `json:"batchNo,omitempty"`                   // 批次号 (可选)
	SourceStatus     string  `json:"sourceStatus" binding:"required"`     // 源库存状态
	TargetStatus     string  `json:"targetStatus" binding:"required"`     // 目标库存状态 (可以与源状态相同)
	Quantity         float64 `json:"quantity" binding:"required,gt=0"`    // 需要移动的数量
	UnitOfMeasure    string  `json:"unitOfMeasure" binding:"required"`    // 计量单位
	RefDocType       *string `json:"refDocType,omitempty"`                // 关联单据类型 (可选)
	RefDocID         *uint   `json:"refDocId,omitempty"`                  // 关联单据头 ID (可选)
	RefDocLineID     *uint   `json:"refDocLineId,omitempty"`              // 关联单据行 ID (可选)
	Remark           *string `json:"remark,omitempty"`                    // 备注 (可选)
	// WarehouseID 通常隐含在 LocationID 中，或从上下文获取，暂不包含
}

// WmsInventoryAdjustReq 库存调整请求
type WmsInventoryAdjustReq struct {
	InventoryID       uint                              `json:"inventoryId" binding:"required" comment:"库存ID"`
	AdjustmentType    entity.WmsInventoryAdjustmentType `json:"adjustmentType" binding:"required" comment:"调整类型"`
	QuantityChange    float64                           `json:"quantityChange" binding:"required" comment:"调整数量"`
	StatusAfter       *string                           `json:"statusAfter" comment:"调整后状态"`
	ReasonCode        *string                           `json:"reasonCode" comment:"原因代码"`
	ReasonDescription *string                           `json:"reasonDescription" comment:"原因描述"`
}

// WmsInventoryBatchAdjustReq 批量库存调整请求
type WmsInventoryBatchAdjustReq struct {
	Adjustments []WmsInventoryAdjustReq `json:"adjustments" binding:"required,dive" comment:"调整列表"`
}

// WmsInventoryReserveReq 库存预占请求
type WmsInventoryReserveReq struct {
	InventoryID uint    `json:"inventoryId" binding:"required" comment:"库存ID"`
	ReservedQty float64 `json:"reservedQty" binding:"required,gt=0" comment:"预占数量"`
	ReasonCode  *string `json:"reasonCode" comment:"原因代码"`
	Remark      *string `json:"remark" comment:"备注"`
}

// WmsInventoryReleaseReq 库存释放请求
type WmsInventoryReleaseReq struct {
	InventoryID uint    `json:"inventoryId" binding:"required" comment:"库存ID"`
	ReleasedQty float64 `json:"releasedQty" binding:"required,gt=0" comment:"释放数量"`
	ReasonCode  *string `json:"reasonCode" comment:"原因代码"`
	Remark      *string `json:"remark" comment:"备注"`
}

// WmsInventoryFreezeReq 库存冻结请求
type WmsInventoryFreezeReq struct {
	InventoryID uint    `json:"inventoryId" binding:"required" comment:"库存ID"`
	FrozenQty   float64 `json:"frozenQty" binding:"required,gt=0" comment:"冻结数量"`
	ReasonCode  *string `json:"reasonCode" comment:"原因代码"`
	Remark      *string `json:"remark" comment:"备注"`
}

// WmsInventoryUnfreezeReq 库存解冻请求
type WmsInventoryUnfreezeReq struct {
	InventoryID uint    `json:"inventoryId" binding:"required" comment:"库存ID"`
	UnfrozenQty float64 `json:"unfrozenQty" binding:"required,gt=0" comment:"解冻数量"`
	ReasonCode  *string `json:"reasonCode" comment:"原因代码"`
	Remark      *string `json:"remark" comment:"备注"`
}

// WmsInventoryOperationReq 库存操作验证请求
type WmsInventoryOperationReq struct {
	InventoryID   uint    `json:"inventoryId" binding:"required" comment:"库存ID"`
	OperationType string  `json:"operationType" binding:"required" comment:"操作类型"`
	Quantity      float64 `json:"quantity" binding:"required,gt=0" comment:"操作数量"`
	ReasonCode    *string `json:"reasonCode" comment:"原因代码"`
}

// TODO: 根据需要添加其他库存相关的 DTO，例如库存查询请求/响应等
