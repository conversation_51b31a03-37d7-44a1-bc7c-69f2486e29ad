import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getPutawayTaskPage,
  getPutawayTaskDetail,
  deletePutawayTask,
  assignUserToTask,
  completePutawayTask,
  createPutawayTaskFromReceiving,
  startPutawayTask,
  completePutawayTaskWithRemark,
  pausePutawayTask,
  cancelPutawayTask,
  updatePutawayDetail,
  batchPutaway
} from '@/api/wms/putawayTask'
import type { getUserList } from '@/api/system/systemUser'
import type {
  WmsPutawayTaskSimpleResp,
  WmsPutawayTaskResp,
  WmsPutawayTaskQueryReq,
  WmsPutawayTaskStatus
} from '@/api/wms/putawayTask'
import type {
  PutawayTaskFormData,
  DetailRowData,
  SearchFormData,
  PaginationParams,
  SelectOption
} from '@/types/wms/putawayTask'

export const usePutawayTaskStore = defineStore('wmsPutawayTask', () => {
    // ==================== State ====================
    const list = ref<WmsPutawayTaskSimpleResp[]>([])
    const total = ref(0)
    const loading = ref(false)
    const dialogVisible = ref(false)
    const formMode = ref<'assign' | 'view' | 'execute'>('view')
    const searchData = ref<SearchFormData>({})
    const pagination = ref<PaginationParams>({ pageNum: 1, pageSize: 10 })
    const formData = ref<PutawayTaskFormData>({
        details: []
    })
    const currentId = ref<number | null>(null)

    // ==================== Cache ====================
    const userOptions = ref<SelectOption[]>([])

    // ==================== Getters ====================
    const isReadonly = computed(() => formMode.value === 'view')
    const dialogTitle = computed(() => {
        const titles = {
            assign: '分配上架任务',
            view: '查看上架任务',
            execute: '执行上架任务'
        }
        return titles[formMode.value]
    })

    // ==================== Actions ====================

    /** 获取列表 */
    const fetchList = async () => {
        loading.value = true
        try {
            const params: WmsPutawayTaskQueryReq = {
                ...searchData.value,
                pageNum: pagination.value.pageNum,
                pageSize: pagination.value.pageSize,
                createdAtFrom: searchData.value.createdAtRange?.[0],
                createdAtTo: searchData.value.createdAtRange?.[1],
                completedAtFrom: searchData.value.completedAtRange?.[0],
                completedAtTo: searchData.value.completedAtRange?.[1]
            }
            delete params.createdAtRange
            delete params.completedAtRange

            const res = await getPutawayTaskPage(params)
            list.value = res.list
            total.value = res.total
        } finally {
            loading.value = false
        }
    }

    /** 处理搜索 */
    const handleSearch = () => {
        pagination.value.pageNum = 1
        fetchList()
    }

    /** 重置搜索 */
    const resetSearch = () => {
        searchData.value = {}
        handleSearch()
    }

    /** 打开弹窗 */
    const openDialog = async (mode: 'assign' | 'view' | 'execute', id: number) => {
        formMode.value = mode
        currentId.value = id
        const res = await getPutawayTaskDetail(id)
        formData.value = res
        dialogVisible.value = true
    }

    /** 从收货记录创建上架任务 */
    const handleCreateFromReceiving = async (receivingId: number) => {
        await ElMessageBox.confirm('确定要为该收货记录生成上架任务吗？', '提示', { type: 'info' })
        await createPutawayTaskFromReceiving(receivingId)
        ElMessage.success('上架任务创建成功')
        fetchList() // 创建后刷新列表
    }

    /** 分配任务 */
    const handleAssignTask = async (userId: number) => {
        if (!currentId.value) return
        await assignUserToTask(currentId.value, { assignedToUserId: userId })
        ElMessage.success('任务分配成功')
        dialogVisible.value = false
        fetchList()
    }

    /** 完成任务 */
    const handleCompleteTask = async () => {
        if (!currentId.value) return
        await ElMessageBox.confirm('确定要将此任务标记为完成吗？', '提示', { type: 'warning' })
        await completePutawayTask(currentId.value)
        ElMessage.success('任务已完成')
        dialogVisible.value = false
        fetchList()
    }

    /** 删除任务 */
    const handleDelete = async (id: number) => {
        await ElMessageBox.confirm('确定删除该上架任务吗？', '提示', { type: 'warning' })
        await deletePutawayTask(id)
        ElMessage.success('删除成功')
        fetchList()
    }

    /** 获取用户选项 */
    const fetchUserOptions = async () => {
        // 假设有一个 getUserList 的 API
        // const res = await getUserList({ pageNum: 1, pageSize: 999 })
        // userOptions.value = res.list.map((user) => ({ label: user.nickname, value: user.id }))
    }

    // ==================== 移动端方法 ====================

    // 获取任务详情（移动端用）
    const fetchDetail = async (id: number): Promise<WmsPutawayTaskResp> => {
        try {
            const result = await getPutawayTaskDetail(id)
            return result
        } catch (error) {
            ElMessage.error('获取任务详情失败')
            throw error
        }
    }

    // 开始任务
    const startTask = async (id: number): Promise<void> => {
        try {
            await startPutawayTask(id)
            ElMessage.success('任务已开始')
        } catch (error) {
            ElMessage.error('开始任务失败')
            throw error
        }
    }

    // 完成任务（带备注）
    const completeTask = async (id: number, remark?: string): Promise<void> => {
        try {
            await completePutawayTaskWithRemark(id, remark)
            ElMessage.success('任务已完成')
        } catch (error) {
            ElMessage.error('完成任务失败')
            throw error
        }
    }

    // 暂停任务
    const pauseTask = async (id: number, remark?: string): Promise<void> => {
        try {
            await pausePutawayTask(id, remark)
            ElMessage.success('任务已暂停')
        } catch (error) {
            ElMessage.error('暂停任务失败')
            throw error
        }
    }

    // 取消任务
    const cancelTask = async (id: number, reason: string): Promise<void> => {
        try {
            await cancelPutawayTask(id, reason)
            ElMessage.success('任务已取消')
        } catch (error) {
            ElMessage.error('取消任务失败')
            throw error
        }
    }

    // 更新上架明细
    const updateDetail = async (taskId: number, detailId: number, data: {
        completedQuantity: number
        actualLocation?: string
        remark?: string
    }): Promise<void> => {
        try {
            await updatePutawayDetail(taskId, detailId, data)
            ElMessage.success('上架记录已更新')
        } catch (error) {
            ElMessage.error('更新上架记录失败')
            throw error
        }
    }

    // 批量上架
    const batchPutawayItems = async (taskId: number, putaways: Array<{
        detailId: number
        completedQuantity: number
        actualLocation?: string
        remark?: string
    }>): Promise<void> => {
        try {
            await batchPutaway(taskId, putaways)
            ElMessage.success('批量上架成功')
        } catch (error) {
            ElMessage.error('批量上架失败')
            throw error
        }
    }

    return {
        list,
        total,
        loading,
        dialogVisible,
        formMode,
        isReadonly,
        dialogTitle,
        searchData,
        pagination,
        formData,
        userOptions,
        fetchList,
        handleSearch,
        resetSearch,
        openDialog,
        handleCreateFromReceiving,
        handleAssignTask,
        handleCompleteTask,
        handleDelete,
        fetchUserOptions,
        // 移动端方法
        fetchDetail,
        startTask,
        completeTask,
        pauseTask,
        cancelTask,
        updateDetail,
        batchPutawayItems
    }
})