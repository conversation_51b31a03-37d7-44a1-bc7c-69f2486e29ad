package vo

import "time"

// ==================== 验证记录相关VO ====================

// WmsBlindReceivingValidationVO 盲收验证记录VO
type WmsBlindReceivingValidationVO struct {
	// 基础字段
	ID                uint64     `json:"id"`                 // 验证记录ID
	WarehouseID       uint64     `json:"warehouse_id"`       // 仓库ID
	CustomerID        uint64     `json:"customer_id"`        // 客户ID
	UserID            uint64     `json:"user_id"`            // 用户ID
	ConfigID          uint64     `json:"config_id"`          // 配置ID
	ValidationResult  string     `json:"validation_result"`  // 验证结果
	RequestedQuantity uint64     `json:"requested_quantity"` // 请求数量
	ActualQuantity    uint64     `json:"actual_quantity"`    // 实际数量
	Strategy          string     `json:"strategy"`           // 验证策略
	ValidationData    string     `json:"validation_data"`    // 验证数据
	ProcessingNotes   string     `json:"processing_notes"`   // 处理备注
	ProcessedAt       *time.Time `json:"processed_at"`       // 处理时间
	ProcessedBy       *uint64    `json:"processed_by"`       // 处理人ID
	CreatedAt         time.Time  `json:"created_at"`         // 创建时间
	UpdatedAt         time.Time  `json:"updated_at"`         // 更新时间
	CreatedBy         uint64     `json:"created_by"`         // 创建人ID
	UpdatedBy         uint64     `json:"updated_by"`         // 更新人ID

	// 扩展字段
	ProcessingDuration int64  `json:"processing_duration"` // 处理时长（分钟）
	IsOverdue          bool   `json:"is_overdue"`          // 是否超时
	StatusDisplay      string `json:"status_display"`      // 状态显示名称

	// 关联信息（可选，根据需要填充）
	WarehouseName   string `json:"warehouse_name,omitempty"`    // 仓库名称
	CustomerName    string `json:"customer_name,omitempty"`     // 客户名称
	UserName        string `json:"user_name,omitempty"`         // 用户名称
	ProcessedByName string `json:"processed_by_name,omitempty"` // 处理人名称
}

// WmsBlindReceivingValidationStatsVO 验证统计VO
type WmsBlindReceivingValidationStatsVO struct {
	WarehouseID  uint64    `json:"warehouse_id"`  // 仓库ID
	StartTime    time.Time `json:"start_time"`    // 开始时间
	EndTime      time.Time `json:"end_time"`      // 结束时间
	TotalCount   int64     `json:"total_count"`   // 总验证次数
	SuccessCount int64     `json:"success_count"` // 成功次数
	FailureCount int64     `json:"failure_count"` // 失败次数
	SuccessRate  float64   `json:"success_rate"`  // 成功率（百分比）
}
