package database

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	mysqlDriver "github.com/go-sql-driver/mysql"
	gormMysql "gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"backend/pkg/config"
	appLogger "backend/pkg/logger"
)

// MySQLClient MySQL数据库客户端
type MySQLClient struct {
	config *config.MySQLConfig
	DB     *gorm.DB
	once   sync.Once
}

// NewMySQLClient 创建MySQL客户端
func NewMySQLClient(cfg *config.MySQLConfig) *MySQLClient {
	if cfg == nil {
		appLogger.GetLogger().Fatal("MySQL配置不能为空")
	}
	return &MySQLClient{
		config: cfg,
	}
}

// connectToMySQLAdminDB connects to the MySQL server without specifying a database.
func connectToMySQLAdminDB(cfg *config.MySQLConfig) (*sql.DB, error) {
	log := appLogger.GetLogger()
	// DSN without database name
	adminDSN := fmt.Sprintf("%s:%s@tcp(%s:%d)/?charset=%s&parseTime=%t&loc=%s",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port,
		cfg.Charset, cfg.ParseTime, cfg.Loc)
	db, err := sql.Open("mysql", adminDSN)
	if err != nil {
		log.Error("连接到 MySQL admin DB 失败", appLogger.WithError(err))
		return nil, fmt.Errorf("连接到 MySQL admin DB 失败: %w", err)
	}
	if err = db.Ping(); err != nil {
		db.Close()
		log.Error("ping MySQL admin DB 失败", appLogger.WithError(err))
		return nil, fmt.Errorf("ping MySQL admin DB 失败: %w", err)
	}
	log.Debug("成功连接到 MySQL admin DB")
	return db, nil
}

// createMySQLDatabase creates the target database if it doesn't exist.
func createMySQLDatabase(adminDB *sql.DB, dbName, charset string) error {
	log := appLogger.GetLogger()
	// Use backticks for quoting database name in MySQL
	quotedDBName := fmt.Sprintf("`%s`", strings.ReplaceAll(dbName, "`", "``"))
	// Ensure charset is safe or use a default
	if charset == "" {
		charset = "utf8mb4" // Default charset
	}
	// COLLATE is often derived from charset, e.g., utf8mb4_unicode_ci or utf8mb4_general_ci
	// Using a common default collation.
	collation := charset + "_unicode_ci"

	query := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET %s COLLATE %s", quotedDBName, charset, collation)
	_, err := adminDB.Exec(query)
	if err != nil {
		log.Error("创建 MySQL 数据库失败", appLogger.WithError(err), appLogger.WithField("dbName", dbName))
		return fmt.Errorf("创建 MySQL 数据库 '%s' 失败: %w", dbName, err)
	}
	log.Infof("MySQL 数据库 '%s' 已存在或创建成功", dbName)
	return nil
}

// Connect 连接数据库, 如果配置了 auto_init 则会尝试创建数据库
// 修改返回值为 (*gorm.DB, error) 以匹配 DBClient 接口
func (m *MySQLClient) Connect() (*gorm.DB, error) {
	logApp := appLogger.GetLogger()
	var finalErr error
	m.once.Do(func() {
		targetDSN := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
			m.config.Username, m.config.Password, m.config.Host, m.config.Port,
			m.config.Database, m.config.Charset, m.config.ParseTime, m.config.Loc)

		// Configure GORM logger
		var logLevel gormLogger.LogLevel
		switch m.config.LogLevel {
		case 1:
			logLevel = gormLogger.Silent
		case 2:
			logLevel = gormLogger.Error
		case 3:
			logLevel = gormLogger.Warn
		case 4:
			logLevel = gormLogger.Info
		default:
			logLevel = gormLogger.Warn
		}
		stdLogger := log.New(os.Stdout, "\r\n", log.LstdFlags)
		gormLog := gormLogger.New(
			stdLogger,
			gormLogger.Config{
				SlowThreshold:             time.Duration(m.config.SlowThreshold) * time.Millisecond,
				LogLevel:                  logLevel,
				IgnoreRecordNotFoundError: true,
				Colorful:                  true,
			},
		)

		// Try initial connection
		var err error
		m.DB, err = gorm.Open(gormMysql.Open(targetDSN), &gorm.Config{
			Logger: gormLog,
			NamingStrategy: schema.NamingStrategy{
				SingularTable: true,
			},
		})

		if err != nil {
			// Check if error is "Unknown database" (MySQL error code 1049)
			var mysqlErr *mysqlDriver.MySQLError
			if errors.As(err, &mysqlErr) && mysqlErr.Number == 1049 && m.config.AutoInit {
				logApp.Warnf("MySQL 数据库 '%s' 不存在，将尝试创建 (auto_init=true)...", m.config.Database)

				adminDB, adminErr := connectToMySQLAdminDB(m.config)
				if adminErr != nil {
					finalErr = fmt.Errorf("无法连接到 admin DB 以创建 MySQL 数据库: %w", adminErr)
					return // Exit once.Do
				}
				defer adminDB.Close()

				createErr := createMySQLDatabase(adminDB, m.config.Database, m.config.Charset)
				if createErr != nil {
					finalErr = fmt.Errorf("自动创建 MySQL 数据库 '%s' 失败: %w", m.config.Database, createErr)
					return // Exit once.Do
				}
				logApp.Info("尝试重新连接到新创建的 MySQL 数据库...")
				// Retry connection
				m.DB, err = gorm.Open(gormMysql.Open(targetDSN), &gorm.Config{
					Logger: gormLog,
					NamingStrategy: schema.NamingStrategy{
						SingularTable: true,
					},
				})
				if err != nil {
					finalErr = fmt.Errorf("连接到新创建的 MySQL 数据库 '%s' 失败: %w", m.config.Database, err)
					return // Exit once.Do
				}
			} else {
				// If AutoInit is false or it's a different error
				finalErr = fmt.Errorf("连接 MySQL 数据库失败: %w", err)
				return // Exit once.Do
			}
		}

		// Configure connection pool
		sqlDB, poolErr := m.DB.DB()
		if poolErr != nil {
			finalErr = fmt.Errorf("获取数据库连接池失败: %w", poolErr)
			return // Exit once.Do
		}
		sqlDB.SetMaxIdleConns(m.config.MaxIdleConns)
		sqlDB.SetMaxOpenConns(m.config.MaxOpenConns)
		sqlDB.SetConnMaxLifetime(time.Duration(m.config.ConnMaxLifetime) * time.Second)

		logApp.Info("MySQL 数据库连接和设置成功")
	})

	if finalErr != nil {
		logApp.Errorf("MySQL Connect 过程失败: %v", finalErr)
		return nil, finalErr // Return nil DB on error
	}
	// Return the connected DB instance and nil error on success
	return m.DB, nil
}

// Disconnect 断开数据库连接
func (m *MySQLClient) Disconnect() error {
	if m.DB == nil {
		return nil
	}

	sqlDB, err := m.DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// Ping 测试数据库连接
func (m *MySQLClient) Ping(ctx context.Context) error {
	if m.DB == nil {
		return fmt.Errorf("数据库未连接")
	}

	sqlDB, err := m.DB.DB()
	if err != nil {
		return err
	}

	// Use PingContext
	return sqlDB.PingContext(ctx)
}

// GetDB 获取数据库连接 (确保已连接)
// 修改：移除隐式 Connect() 调用，与 PostgreSQL 版本保持一致
func (m *MySQLClient) GetDB() *gorm.DB {
	if m.DB == nil {
		// If Connect wasn't called or failed before GetDB was called, this is a fatal error.
		appLogger.GetLogger().Fatal("MySQL GetDB called before Connect completed successfully")
	}
	return m.DB
}

// GetSQLDB 获取底层的 *sql.DB 连接池对象
func (m *MySQLClient) GetSQLDB() (*sql.DB, error) {
	if m.DB == nil {
		return nil, fmt.Errorf("MySQL GORM DB instance is not initialized")
	}
	return m.DB.DB()
}
