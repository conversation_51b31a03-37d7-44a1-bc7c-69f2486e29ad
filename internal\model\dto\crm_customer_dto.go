package dto

import (
	"backend/pkg/response"
	"time"

	"github.com/shopspring/decimal"
)

// CrmCustomerCreateReq 创建客户请求
type CrmCustomerCreateReq struct {
	// 客户基本信息
	CustomerCode        string           `json:"customerCode" binding:"omitempty,max=50"`                     // 客户编码，支持自动生成
	CustomerName        string           `json:"customerName" binding:"required,max=255"`                     // 客户名称，必填
	CustomerType        string           `json:"customerType" binding:"omitempty,oneof=CORPORATE INDIVIDUAL"` // 客户类型
	Industry            *string          `json:"industry" binding:"omitempty,max=100"`                        // 所属行业
	BusinessLicense     *string          `json:"businessLicense" binding:"omitempty,max=100"`                 // 营业执照号
	TaxNumber           *string          `json:"taxNumber" binding:"omitempty,max=100"`                       // 税务登记号
	LegalRepresentative *string          `json:"legalRepresentative" binding:"omitempty,max=100"`             // 法定代表人
	RegisteredCapital   *decimal.Decimal `json:"registeredCapital" binding:"omitempty"`                       // 注册资本

	// 联系信息
	ContactPerson *string `json:"contactPerson" binding:"omitempty,max=100"`      // 主要联系人
	ContactPhone  *string `json:"contactPhone" binding:"omitempty,max=50"`        // 联系电话
	ContactEmail  *string `json:"contactEmail" binding:"omitempty,email,max=255"` // 联系邮箱
	Website       *string `json:"website" binding:"omitempty,url,max=255"`        // 公司网站

	// 地址信息
	Country    *string `json:"country" binding:"omitempty,max=100"`   // 国家
	Province   *string `json:"province" binding:"omitempty,max=100"`  // 省份
	City       *string `json:"city" binding:"omitempty,max=100"`      // 城市
	District   *string `json:"district" binding:"omitempty,max=100"`  // 区县
	Address    *string `json:"address" binding:"omitempty"`           // 详细地址
	PostalCode *string `json:"postalCode" binding:"omitempty,max=20"` // 邮政编码

	// 财务信息
	CreditRating *string          `json:"creditRating" binding:"omitempty,max=20"` // 信用等级
	CreditLimit  *decimal.Decimal `json:"creditLimit" binding:"omitempty"`         // 信用额度
	PaymentTerms *string          `json:"paymentTerms" binding:"omitempty,max=50"` // 付款条件
	CurrencyCode string           `json:"currencyCode" binding:"omitempty,max=10"` // 结算币种

	// 业务信息
	CustomerLevel         string  `json:"customerLevel" binding:"omitempty,oneof=VIP GOLD SILVER NORMAL"` // 客户级别
	CustomerSource        *string `json:"customerSource" binding:"omitempty,max=50"`                      // 客户来源
	SalesRepresentativeID *uint   `json:"salesRepresentativeId" binding:"omitempty,gt=0"`                 // 销售代表ID

	// 状态信息
	Status        string  `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE BLACKLIST"` // 状态
	IsKeyCustomer *bool   `json:"isKeyCustomer" binding:"omitempty"`                          // 是否重点客户
	Remark        *string `json:"remark" binding:"omitempty"`                                 // 备注

	// 联系人信息（可选）
	Contacts []CrmCustomerContactDTO `json:"contacts" binding:"omitempty,dive"` // 客户联系人列表
}

// CrmCustomerUpdateReq 更新客户请求
type CrmCustomerUpdateReq struct {
	ID uint `json:"id" binding:"required,gt=0"` // 客户ID，必填

	// 客户基本信息
	CustomerCode        *string          `json:"customerCode" binding:"omitempty,max=50"`                     // 客户编码
	CustomerName        *string          `json:"customerName" binding:"omitempty,max=255"`                    // 客户名称
	CustomerType        *string          `json:"customerType" binding:"omitempty,oneof=CORPORATE INDIVIDUAL"` // 客户类型
	Industry            *string          `json:"industry" binding:"omitempty,max=100"`                        // 所属行业
	BusinessLicense     *string          `json:"businessLicense" binding:"omitempty,max=100"`                 // 营业执照号
	TaxNumber           *string          `json:"taxNumber" binding:"omitempty,max=100"`                       // 税务登记号
	LegalRepresentative *string          `json:"legalRepresentative" binding:"omitempty,max=100"`             // 法定代表人
	RegisteredCapital   *decimal.Decimal `json:"registeredCapital" binding:"omitempty"`                       // 注册资本

	// 联系信息
	ContactPerson *string `json:"contactPerson" binding:"omitempty,max=100"`      // 主要联系人
	ContactPhone  *string `json:"contactPhone" binding:"omitempty,max=50"`        // 联系电话
	ContactEmail  *string `json:"contactEmail" binding:"omitempty,email,max=255"` // 联系邮箱
	Website       *string `json:"website" binding:"omitempty,url,max=255"`        // 公司网站

	// 地址信息
	Country    *string `json:"country" binding:"omitempty,max=100"`   // 国家
	Province   *string `json:"province" binding:"omitempty,max=100"`  // 省份
	City       *string `json:"city" binding:"omitempty,max=100"`      // 城市
	District   *string `json:"district" binding:"omitempty,max=100"`  // 区县
	Address    *string `json:"address" binding:"omitempty"`           // 详细地址
	PostalCode *string `json:"postalCode" binding:"omitempty,max=20"` // 邮政编码

	// 财务信息
	CreditRating *string          `json:"creditRating" binding:"omitempty,max=20"` // 信用等级
	CreditLimit  *decimal.Decimal `json:"creditLimit" binding:"omitempty"`         // 信用额度
	PaymentTerms *string          `json:"paymentTerms" binding:"omitempty,max=50"` // 付款条件
	CurrencyCode *string          `json:"currencyCode" binding:"omitempty,max=10"` // 结算币种

	// 业务信息
	CustomerLevel         *string `json:"customerLevel" binding:"omitempty,oneof=VIP GOLD SILVER NORMAL"` // 客户级别
	CustomerSource        *string `json:"customerSource" binding:"omitempty,max=50"`                      // 客户来源
	SalesRepresentativeID *uint   `json:"salesRepresentativeId" binding:"omitempty,gt=0"`                 // 销售代表ID

	// 状态信息
	Status        *string `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE BLACKLIST"` // 状态
	IsKeyCustomer *bool   `json:"isKeyCustomer" binding:"omitempty"`                          // 是否重点客户
	Remark        *string `json:"remark" binding:"omitempty"`                                 // 备注
}

// CrmCustomerQueryReq 查询客户请求（使用现有分页机制）
type CrmCustomerQueryReq struct {
	response.PageQuery           // 直接嵌入现有分页结构
	CustomerCode          string `form:"customerCode" json:"customerCode" binding:"omitempty,max=50"`                         // 客户编码
	CustomerName          string `form:"customerName" json:"customerName" binding:"omitempty,max=255"`                        // 客户名称
	CustomerType          string `form:"customerType" json:"customerType" binding:"omitempty,oneof=CORPORATE INDIVIDUAL"`     // 客户类型
	Industry              string `form:"industry" json:"industry" binding:"omitempty,max=100"`                                // 所属行业
	CustomerLevel         string `form:"customerLevel" json:"customerLevel" binding:"omitempty,oneof=VIP GOLD SILVER NORMAL"` // 客户级别
	Status                string `form:"status" json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE BLACKLIST"`            // 状态
	SalesRepresentativeID *uint  `form:"salesRepresentativeId" json:"salesRepresentativeId" binding:"omitempty,gt=0"`         // 销售代表ID
	IsKeyCustomer         *bool  `form:"isKeyCustomer" json:"isKeyCustomer"`                                                  // 是否重点客户
	Country               string `form:"country" json:"country" binding:"omitempty,max=100"`                                  // 国家
	Province              string `form:"province" json:"province" binding:"omitempty,max=100"`                                // 省份
	City                  string `form:"city" json:"city" binding:"omitempty,max=100"`                                        // 城市
}

// CrmCustomerContactDTO 客户联系人传输对象
type CrmCustomerContactDTO struct {
	ID           uint       `json:"id,omitempty"`                                 // 联系人ID，更新时需要
	ContactName  string     `json:"contactName" binding:"required,max=100"`       // 联系人姓名，必填
	ContactTitle *string    `json:"contactTitle" binding:"omitempty,max=100"`     // 职务
	Department   *string    `json:"department" binding:"omitempty,max=100"`       // 所属部门
	Phone        *string    `json:"phone" binding:"omitempty,max=50"`             // 电话
	Mobile       *string    `json:"mobile" binding:"omitempty,max=50"`            // 手机
	Email        *string    `json:"email" binding:"omitempty,email,max=255"`      // 邮箱
	QQ           *string    `json:"qq" binding:"omitempty,max=50"`                // QQ号
	Wechat       *string    `json:"wechat" binding:"omitempty,max=100"`           // 微信号
	Address      *string    `json:"address" binding:"omitempty"`                  // 联系地址
	PostalCode   *string    `json:"postalCode" binding:"omitempty,max=20"`        // 邮政编码
	Birthday     *time.Time `json:"birthday" binding:"omitempty"`                 // 生日
	Gender       *string    `json:"gender" binding:"omitempty,oneof=MALE FEMALE"` // 性别
	ContactRole  *string    `json:"contactRole" binding:"omitempty,max=50"`       // 联系人角色
	Remark       *string    `json:"remark" binding:"omitempty"`                   // 备注
}

// CrmCustomerContactCreateReq 创建客户联系人请求
type CrmCustomerContactCreateReq struct {
	CustomerID   uint       `json:"customerId" binding:"required,gt=0"`           // 客户ID，必填
	ContactName  string     `json:"contactName" binding:"required,max=100"`       // 联系人姓名，必填
	ContactTitle *string    `json:"contactTitle" binding:"omitempty,max=100"`     // 职务
	Department   *string    `json:"department" binding:"omitempty,max=100"`       // 所属部门
	Phone        *string    `json:"phone" binding:"omitempty,max=50"`             // 电话
	Mobile       *string    `json:"mobile" binding:"omitempty,max=50"`            // 手机
	Email        *string    `json:"email" binding:"omitempty,email,max=255"`      // 邮箱
	QQ           *string    `json:"qq" binding:"omitempty,max=50"`                // QQ号
	Wechat       *string    `json:"wechat" binding:"omitempty,max=100"`           // 微信号
	Address      *string    `json:"address" binding:"omitempty"`                  // 联系地址
	PostalCode   *string    `json:"postalCode" binding:"omitempty,max=20"`        // 邮政编码
	Birthday     *time.Time `json:"birthday" binding:"omitempty"`                 // 生日
	Gender       *string    `json:"gender" binding:"omitempty,oneof=MALE FEMALE"` // 性别
	ContactRole  *string    `json:"contactRole" binding:"omitempty,max=50"`       // 联系人角色
	Remark       *string    `json:"remark" binding:"omitempty"`                   // 备注
}

// CrmCustomerContactUpdateReq 更新客户联系人请求
type CrmCustomerContactUpdateReq struct {
	ID           uint       `json:"id" binding:"required,gt=0"`                   // 联系人ID，必填
	ContactName  *string    `json:"contactName" binding:"omitempty,max=100"`      // 联系人姓名
	ContactTitle *string    `json:"contactTitle" binding:"omitempty,max=100"`     // 职务
	Department   *string    `json:"department" binding:"omitempty,max=100"`       // 所属部门
	Phone        *string    `json:"phone" binding:"omitempty,max=50"`             // 电话
	Mobile       *string    `json:"mobile" binding:"omitempty,max=50"`            // 手机
	Email        *string    `json:"email" binding:"omitempty,email,max=255"`      // 邮箱
	QQ           *string    `json:"qq" binding:"omitempty,max=50"`                // QQ号
	Wechat       *string    `json:"wechat" binding:"omitempty,max=100"`           // 微信号
	Address      *string    `json:"address" binding:"omitempty"`                  // 联系地址
	PostalCode   *string    `json:"postalCode" binding:"omitempty,max=20"`        // 邮政编码
	Birthday     *time.Time `json:"birthday" binding:"omitempty"`                 // 生日
	Gender       *string    `json:"gender" binding:"omitempty,oneof=MALE FEMALE"` // 性别
	ContactRole  *string    `json:"contactRole" binding:"omitempty,max=50"`       // 联系人角色
	Remark       *string    `json:"remark" binding:"omitempty"`                   // 备注
}
