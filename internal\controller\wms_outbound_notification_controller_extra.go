package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
)

// GetStats 获取统计信息
// GET /api/v1/wms/outbound-notifications/stats
func (ctrl *WmsOutboundNotificationControllerImpl) GetStats(ctx iris.Context) {
	const opName = "GetWmsOutboundNotificationStats"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationStatsReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsOutboundNotificationService.GetStats(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// ImportFromExcel 从Excel导入
// POST /api/v1/wms/outbound-notifications/import-excel
func (ctrl *WmsOutboundNotificationControllerImpl) ImportFromExcel(ctx iris.Context) {
	const opName = "ImportWmsOutboundNotificationsFromExcel"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationImportReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsOutboundNotificationService.ImportFromExcel(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// ExportToExcel 导出到Excel
// GET /api/v1/wms/outbound-notifications/export-excel
func (ctrl *WmsOutboundNotificationControllerImpl) ExportToExcel(ctx iris.Context) {
	const opName = "ExportWmsOutboundNotificationsToExcel"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationExportReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	data, err := ctrl.wmsOutboundNotificationService.ExportToExcel(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", "attachment; filename=outbound_notifications.xlsx")
	ctx.Write(data)
}

// GetByNotificationNo 根据通知单号获取
// GET /api/v1/wms/outbound-notifications/by-notification-no/{notificationNo:string}
func (ctrl *WmsOutboundNotificationControllerImpl) GetByNotificationNo(ctx iris.Context) {
	const opName = "GetWmsOutboundNotificationByNotificationNo"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	notificationNo := ctx.Params().Get("notificationNo")
	if notificationNo == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "通知单号不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsOutboundNotificationService.GetByNotificationNo(ctx.Request().Context(), notificationNo)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// GetByClientOrderNo 根据客户订单号获取
// GET /api/v1/wms/outbound-notifications/by-client-order-no/{clientOrderNo:string}
func (ctrl *WmsOutboundNotificationControllerImpl) GetByClientOrderNo(ctx iris.Context) {
	const opName = "GetWmsOutboundNotificationByClientOrderNo"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	clientOrderNo := ctx.Params().Get("clientOrderNo")
	if clientOrderNo == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "客户订单号不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsOutboundNotificationService.GetByClientOrderNo(ctx.Request().Context(), clientOrderNo)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetPendingAllocation 获取待分配的通知单
// GET /api/v1/wms/outbound-notifications/pending-allocation
func (ctrl *WmsOutboundNotificationControllerImpl) GetPendingAllocation(ctx iris.Context) {
	const opName = "GetPendingAllocationWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	results, err := ctrl.wmsOutboundNotificationService.GetPendingAllocation(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetReadyForPicking 获取可生成拣货任务的通知单
// GET /api/v1/wms/outbound-notifications/ready-for-picking
func (ctrl *WmsOutboundNotificationControllerImpl) GetReadyForPicking(ctx iris.Context) {
	const opName = "GetReadyForPickingWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	results, err := ctrl.wmsOutboundNotificationService.GetReadyForPicking(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetOverdueShipments 获取过期未发货的通知单
// GET /api/v1/wms/outbound-notifications/overdue-shipments
func (ctrl *WmsOutboundNotificationControllerImpl) GetOverdueShipments(ctx iris.Context) {
	const opName = "GetOverdueShipmentsWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	results, err := ctrl.wmsOutboundNotificationService.GetOverdueShipments(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}
