package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsInventoryAlertRuleRepository 库存预警规则数据访问接口
type WmsInventoryAlertRuleRepository interface {
	BaseRepository[entity.WmsInventoryAlertRule, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInventoryAlertRuleQueryReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByRuleName(ctx context.Context, ruleName string) (*entity.WmsInventoryAlertRule, error)
	FindByRuleType(ctx context.Context, ruleType entity.WmsInventoryAlertType) ([]*entity.WmsInventoryAlertRule, error)
	FindByItemID(ctx context.Context, itemId uint) ([]*entity.WmsInventoryAlertRule, error)
	FindByWarehouseID(ctx context.Context, warehouseId uint) ([]*entity.WmsInventoryAlertRule, error)
	FindByLocationID(ctx context.Context, locationId uint) ([]*entity.WmsInventoryAlertRule, error)
	FindByCreator(ctx context.Context, creatorId uint) ([]*entity.WmsInventoryAlertRule, error)
	FindByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) ([]*entity.WmsInventoryAlertRule, error)

	// 复杂查询
	FindActiveRules(ctx context.Context) ([]*entity.WmsInventoryAlertRule, error)
	FindRulesNeedingCheck(ctx context.Context) ([]*entity.WmsInventoryAlertRule, error)
	FindRulesByScope(ctx context.Context, itemId *uint, warehouseId *uint, locationId *uint) ([]*entity.WmsInventoryAlertRule, error)
	FindExpiredRules(ctx context.Context) ([]*entity.WmsInventoryAlertRule, error)

	// 统计查询
	CountByRuleType(ctx context.Context, ruleType entity.WmsInventoryAlertType) (int64, error)
	CountByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) (int64, error)
	CountActiveRules(ctx context.Context) (int64, error)
	CountByCreator(ctx context.Context, creatorId uint) (int64, error)

	// 批量操作
	BatchActivate(ctx context.Context, ids []uint) error
	BatchDeactivate(ctx context.Context, ids []uint) error
	BatchUpdateCheckTime(ctx context.Context, ids []uint) error
	BatchDelete(ctx context.Context, ids []uint) error

	// 事务支持
	BeginTx() *gorm.DB
}

// WmsInventoryAlertRuleQueryBuilder 库存预警规则查询构建器接口
type WmsInventoryAlertRuleQueryBuilder interface {
	// 条件过滤
	WhereRuleName(ruleName string) WmsInventoryAlertRuleQueryBuilder
	WhereRuleType(ruleType entity.WmsInventoryAlertType) WmsInventoryAlertRuleQueryBuilder
	WhereItemID(itemId uint) WmsInventoryAlertRuleQueryBuilder
	WhereWarehouseID(warehouseId uint) WmsInventoryAlertRuleQueryBuilder
	WhereLocationID(locationId uint) WmsInventoryAlertRuleQueryBuilder
	WhereAlertLevel(alertLevel entity.WmsInventoryAlertLevel) WmsInventoryAlertRuleQueryBuilder
	WhereIsActive(isActive bool) WmsInventoryAlertRuleQueryBuilder
	WhereCreatedBy(creatorId uint) WmsInventoryAlertRuleQueryBuilder
	WhereThresholdRange(minValue, maxValue float64) WmsInventoryAlertRuleQueryBuilder
	WhereCreatedDateRange(startDate, endDate time.Time) WmsInventoryAlertRuleQueryBuilder
	WhereLastCheckBefore(checkTime time.Time) WmsInventoryAlertRuleQueryBuilder

	// 关联查询
	WithItem() WmsInventoryAlertRuleQueryBuilder
	WithWarehouse() WmsInventoryAlertRuleQueryBuilder
	WithLocation() WmsInventoryAlertRuleQueryBuilder
	WithCreator() WmsInventoryAlertRuleQueryBuilder

	// 排序
	OrderByCreatedAt(desc bool) WmsInventoryAlertRuleQueryBuilder
	OrderByLastCheckAt(desc bool) WmsInventoryAlertRuleQueryBuilder
	OrderByRuleName(desc bool) WmsInventoryAlertRuleQueryBuilder
	OrderByAlertLevel(desc bool) WmsInventoryAlertRuleQueryBuilder

	// 执行查询
	Find() ([]entity.WmsInventoryAlertRule, error)
	First() (*entity.WmsInventoryAlertRule, error)
	Count() (int64, error)
	Paginate(pageNum, pageSize int) ([]entity.WmsInventoryAlertRule, int64, error)
}

// WmsInventoryAlertRuleRepositoryImpl 库存预警规则数据访问实现
type WmsInventoryAlertRuleRepositoryImpl struct {
	BaseRepositoryImpl[entity.WmsInventoryAlertRule, uint]
}

// NewWmsInventoryAlertRuleRepository 创建库存预警规则数据访问实例
func NewWmsInventoryAlertRuleRepository(db *gorm.DB) WmsInventoryAlertRuleRepository {
	return &WmsInventoryAlertRuleRepositoryImpl{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsInventoryAlertRule, uint]{
			db: db,
		},
	}
}

// GetPage 分页查询
func (r *WmsInventoryAlertRuleRepositoryImpl) GetPage(ctx context.Context, query *dto.WmsInventoryAlertRuleQueryReq) (*response.PageResult, error) {
	// 构建查询条件
	var conditions []QueryCondition

	if query.RuleName != nil && *query.RuleName != "" {
		conditions = append(conditions, NewLikeCondition("rule_name", *query.RuleName))
	}
	if query.RuleType != nil {
		conditions = append(conditions, NewEqualCondition("rule_type", *query.RuleType))
	}
	if query.AlertLevel != nil {
		conditions = append(conditions, NewEqualCondition("alert_level", *query.AlertLevel))
	}
	if query.IsActive != nil {
		conditions = append(conditions, NewEqualCondition("is_active", *query.IsActive))
	}
	if query.ItemID != nil {
		conditions = append(conditions, NewEqualCondition("item_id", *query.ItemID))
	}
	if query.WarehouseID != nil {
		conditions = append(conditions, NewEqualCondition("warehouse_id", *query.WarehouseID))
	}
	if query.LocationID != nil {
		conditions = append(conditions, NewEqualCondition("location_id", *query.LocationID))
	}
	if query.CreatedBy != nil {
		conditions = append(conditions, NewEqualCondition("created_by", *query.CreatedBy))
	}

	// 使用基础仓库的分页查询
	return r.FindByPage(ctx, &query.PageQuery, conditions)
}

// GetByIDWithAssociations 根据ID获取库存预警规则（包含关联数据）
func (r *WmsInventoryAlertRuleRepositoryImpl) GetByIDWithAssociations(ctx context.Context, id uint) (*entity.WmsInventoryAlertRule, error) {
	var rule entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Preload("Item").
		Preload("Warehouse").
		Preload("Location").
		Preload("Creator").
		First(&rule, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &rule, nil
}

// FindByRuleName 根据规则名称查询
func (r *WmsInventoryAlertRuleRepositoryImpl) FindByRuleName(ctx context.Context, ruleName string) (*entity.WmsInventoryAlertRule, error) {
	var rule entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("rule_name = ?", ruleName).First(&rule).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &rule, nil
}

// FindByRuleType 根据规则类型查询
func (r *WmsInventoryAlertRuleRepositoryImpl) FindByRuleType(ctx context.Context, ruleType entity.WmsInventoryAlertType) ([]*entity.WmsInventoryAlertRule, error) {
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("rule_type = ?", ruleType).
		Order("created_at DESC").
		Find(&rules).Error
	return rules, err
}

// FindActiveRules 查找活跃的预警规则
func (r *WmsInventoryAlertRuleRepositoryImpl) FindActiveRules(ctx context.Context) ([]*entity.WmsInventoryAlertRule, error) {
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("is_active = ?", true).
		Preload("Item").
		Preload("Warehouse").
		Preload("Location").
		Order("created_at DESC").
		Find(&rules).Error
	return rules, err
}

// FindRulesNeedingCheck 查找需要检查的预警规则
func (r *WmsInventoryAlertRuleRepositoryImpl) FindRulesNeedingCheck(ctx context.Context) ([]*entity.WmsInventoryAlertRule, error) {
	now := time.Now()
	var rules []*entity.WmsInventoryAlertRule

	// 查找活跃且需要检查的规则（上次检查时间 + 检查频率 <= 当前时间）
	err := r.GetDB(ctx).Where("is_active = ? AND (last_check_at IS NULL OR last_check_at + INTERVAL check_frequency_minutes MINUTE <= ?)",
		true, now).
		Preload("Item").
		Preload("Warehouse").
		Preload("Location").
		Order("last_check_at ASC").
		Find(&rules).Error
	return rules, err
}

// FindRulesByScope 根据范围查找预警规则
func (r *WmsInventoryAlertRuleRepositoryImpl) FindRulesByScope(ctx context.Context, itemId *uint, warehouseId *uint, locationId *uint) ([]*entity.WmsInventoryAlertRule, error) {
	query := r.GetDB(ctx).Where("is_active = ?", true)

	if itemId != nil {
		query = query.Where("item_id = ? OR item_id IS NULL", *itemId)
	}

	if warehouseId != nil {
		query = query.Where("warehouse_id = ? OR warehouse_id IS NULL", *warehouseId)
	}

	if locationId != nil {
		query = query.Where("location_id = ? OR location_id IS NULL", *locationId)
	}

	var rules []*entity.WmsInventoryAlertRule
	err := query.Order("created_at DESC").Find(&rules).Error
	return rules, err
}

// BeginTx 开始事务
func (r *WmsInventoryAlertRuleRepositoryImpl) BeginTx() *gorm.DB {
	return r.db.Begin()
}

// NewQueryBuilder 创建查询构建器
func (r *WmsInventoryAlertRuleRepositoryImpl) NewQueryBuilder() WmsInventoryAlertRuleQueryBuilder {
	return &WmsInventoryAlertRuleQueryBuilderImpl{
		db:    r.db,
		query: r.db.Model(&entity.WmsInventoryAlertRule{}),
	}
}

// WmsInventoryAlertRuleQueryBuilderImpl 库存预警规则查询构建器实现
type WmsInventoryAlertRuleQueryBuilderImpl struct {
	db    *gorm.DB
	query *gorm.DB
}

// WhereRuleName 按规则名称过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereRuleName(name string) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("rule_name LIKE ?", "%"+name+"%")
	return b
}

// WhereAlertType 按预警类型过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereAlertType(alertType entity.WmsInventoryAlertType) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("alert_type = ?", alertType)
	return b
}

// WhereRuleType 按规则类型过滤（别名方法）
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereRuleType(ruleType entity.WmsInventoryAlertType) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("alert_type = ?", ruleType)
	return b
}

// WhereIsActive 按是否激活过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereIsActive(isActive bool) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("is_active = ?", isActive)
	return b
}

// WhereAlertLevel 按预警级别过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereAlertLevel(level entity.WmsInventoryAlertLevel) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("alert_level = ?", level)
	return b
}

// WhereCreatedBy 按创建人过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereCreatedBy(userId uint) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("created_by = ?", userId)
	return b
}

// WhereItemID 按物料ID过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereItemID(itemId uint) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("item_id = ?", itemId)
	return b
}

// WhereWarehouseID 按仓库ID过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereWarehouseID(warehouseId uint) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("warehouse_id = ?", warehouseId)
	return b
}

// WhereLocationID 按库位ID过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereLocationID(locationId uint) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("location_id = ?", locationId)
	return b
}

// WhereThresholdRange 按阈值范围过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereThresholdRange(minValue, maxValue float64) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("threshold_value BETWEEN ? AND ?", minValue, maxValue)
	return b
}

// WhereCreatedDateRange 按创建时间范围过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereCreatedDateRange(startDate, endDate time.Time) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereLastCheckBefore 按最后检查时间过滤
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WhereLastCheckBefore(checkTime time.Time) WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Where("last_check_at < ? OR last_check_at IS NULL", checkTime)
	return b
}

// WithWarehouse 预加载仓库信息
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WithWarehouse() WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Preload("Warehouse")
	return b
}

// WithLocation 预加载库位信息
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WithLocation() WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Preload("Location")
	return b
}

// WithItem 预加载物料信息
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WithItem() WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Preload("Item")
	return b
}

// WithCreator 预加载创建人信息
func (b *WmsInventoryAlertRuleQueryBuilderImpl) WithCreator() WmsInventoryAlertRuleQueryBuilder {
	b.query = b.query.Preload("Creator")
	return b
}

// OrderByCreatedAt 按创建时间排序
func (b *WmsInventoryAlertRuleQueryBuilderImpl) OrderByCreatedAt(desc bool) WmsInventoryAlertRuleQueryBuilder {
	if desc {
		b.query = b.query.Order("created_at DESC")
	} else {
		b.query = b.query.Order("created_at ASC")
	}
	return b
}

// OrderByAlertLevel 按预警级别排序
func (b *WmsInventoryAlertRuleQueryBuilderImpl) OrderByAlertLevel(desc bool) WmsInventoryAlertRuleQueryBuilder {
	if desc {
		b.query = b.query.Order("alert_level DESC")
	} else {
		b.query = b.query.Order("alert_level ASC")
	}
	return b
}

// OrderByLastCheckAt 按最后检查时间排序
func (b *WmsInventoryAlertRuleQueryBuilderImpl) OrderByLastCheckAt(desc bool) WmsInventoryAlertRuleQueryBuilder {
	if desc {
		b.query = b.query.Order("last_check_at DESC")
	} else {
		b.query = b.query.Order("last_check_at ASC")
	}
	return b
}

// OrderByRuleName 按规则名称排序
func (b *WmsInventoryAlertRuleQueryBuilderImpl) OrderByRuleName(desc bool) WmsInventoryAlertRuleQueryBuilder {
	if desc {
		b.query = b.query.Order("rule_name DESC")
	} else {
		b.query = b.query.Order("rule_name ASC")
	}
	return b
}

// Find 执行查询
func (b *WmsInventoryAlertRuleQueryBuilderImpl) Find() ([]entity.WmsInventoryAlertRule, error) {
	var rules []entity.WmsInventoryAlertRule
	err := b.query.Find(&rules).Error
	return rules, err
}

// First 查询第一条记录
func (b *WmsInventoryAlertRuleQueryBuilderImpl) First() (*entity.WmsInventoryAlertRule, error) {
	var rule entity.WmsInventoryAlertRule
	err := b.query.First(&rule).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &rule, nil
}

// Count 统计数量
func (b *WmsInventoryAlertRuleQueryBuilderImpl) Count() (int64, error) {
	var count int64
	err := b.query.Count(&count).Error
	return count, err
}

// Paginate 分页查询
func (b *WmsInventoryAlertRuleQueryBuilderImpl) Paginate(pageNum, pageSize int) ([]entity.WmsInventoryAlertRule, int64, error) {
	var rules []entity.WmsInventoryAlertRule
	var total int64

	// 先统计总数
	countQuery := b.db.Model(&entity.WmsInventoryAlertRule{}).Where(b.query.Statement.SQL.String())
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (pageNum - 1) * pageSize
	err := b.query.Offset(offset).Limit(pageSize).Find(&rules).Error
	return rules, total, err
}
func (r *WmsInventoryAlertRuleRepositoryImpl) FindByItemID(ctx context.Context, itemId uint) ([]*entity.WmsInventoryAlertRule, error) {
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("item_id = ?", itemId).
		Order("created_at DESC").
		Find(&rules).Error
	return rules, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) FindByWarehouseID(ctx context.Context, warehouseId uint) ([]*entity.WmsInventoryAlertRule, error) {
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("warehouse_id = ?", warehouseId).
		Order("created_at DESC").
		Find(&rules).Error
	return rules, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) FindByLocationID(ctx context.Context, locationId uint) ([]*entity.WmsInventoryAlertRule, error) {
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("location_id = ?", locationId).
		Order("created_at DESC").
		Find(&rules).Error
	return rules, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) FindByCreator(ctx context.Context, creatorId uint) ([]*entity.WmsInventoryAlertRule, error) {
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("created_by = ?", creatorId).
		Order("created_at DESC").
		Find(&rules).Error
	return rules, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) FindByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) ([]*entity.WmsInventoryAlertRule, error) {
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("alert_level = ?", alertLevel).
		Order("created_at DESC").
		Find(&rules).Error
	return rules, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) FindExpiredRules(ctx context.Context) ([]*entity.WmsInventoryAlertRule, error) {
	// 这里可以根据业务需求定义过期规则，比如长时间未检查的规则
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	var rules []*entity.WmsInventoryAlertRule
	err := r.GetDB(ctx).Where("last_check_at < ? OR last_check_at IS NULL", thirtyDaysAgo).
		Order("last_check_at ASC").
		Find(&rules).Error
	return rules, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) CountByRuleType(ctx context.Context, ruleType entity.WmsInventoryAlertType) (int64, error) {
	var count int64
	err := r.db.Model(&entity.WmsInventoryAlertRule{}).Where("rule_type = ?", ruleType).Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) CountByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) (int64, error) {
	var count int64
	err := r.db.Model(&entity.WmsInventoryAlertRule{}).Where("alert_level = ?", alertLevel).Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) CountActiveRules(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.Model(&entity.WmsInventoryAlertRule{}).Where("is_active = ?", true).Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) CountByCreator(ctx context.Context, creatorId uint) (int64, error) {
	var count int64
	err := r.db.Model(&entity.WmsInventoryAlertRule{}).Where("created_by = ?", creatorId).Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertRuleRepositoryImpl) BatchActivate(ctx context.Context, ids []uint) error {
	return r.db.Model(&entity.WmsInventoryAlertRule{}).Where("id IN ?", ids).Update("is_active", true).Error
}

func (r *WmsInventoryAlertRuleRepositoryImpl) BatchDeactivate(ctx context.Context, ids []uint) error {
	return r.db.Model(&entity.WmsInventoryAlertRule{}).Where("id IN ?", ids).Update("is_active", false).Error
}

func (r *WmsInventoryAlertRuleRepositoryImpl) BatchUpdateCheckTime(ctx context.Context, ids []uint) error {
	now := time.Now()
	return r.db.Model(&entity.WmsInventoryAlertRule{}).Where("id IN ?", ids).Update("last_check_at", now).Error
}

func (r *WmsInventoryAlertRuleRepositoryImpl) BatchDelete(ctx context.Context, ids []uint) error {
	return r.db.Delete(&entity.WmsInventoryAlertRule{}, ids).Error
}
