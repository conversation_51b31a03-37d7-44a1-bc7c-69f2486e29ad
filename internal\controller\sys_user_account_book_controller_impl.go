package controller

import (
	"fmt"

	"github.com/kataras/iris/v12"

	// "backend/internal/middleware" // We get UserID directly from context now
	"backend/internal/service" // Import pkg/errors for error handling

	// Import VO package
	"backend/internal/model/dto"
	"backend/internal/model/vo"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // Use existing alias for custom errors

	"backend/pkg/logger"    // For c.GetLogger()
	"backend/pkg/validator" // For validator.Struct()
)

// UserAccountBookController 用户账套关联控制器
type UserAccountBookControllerImpl struct {
	*BaseControllerImpl    // Embed concrete implementation pointer
	userAccountBookService service.UserAccountBookService
}

// NewUserAccountBookControllerImpl 创建用户账套关联控制器实例
func NewUserAccountBookControllerImpl(cm *ControllerManager) *UserAccountBookControllerImpl {
	// Get the UserAccountBookService from the service manager
	userAccBookSvc := cm.GetServiceManager().GetUserAccountBookService()
	if userAccBookSvc == nil {
		// Use Fatalf as Panicf is not available
		cm.logger.Fatalf("UserAccountBookService not found in ServiceManager")
	}

	return &UserAccountBookControllerImpl{
		BaseControllerImpl:     NewBaseController(cm),
		userAccountBookService: userAccBookSvc,
	}
}

// GetCurrentUserAccountBooks 获取当前登录用户的账套列表
// @Summary 获取当前用户账套列表
// @Description 获取当前登录用户有权访问的所有账套列表
// @Tags UserSpecific
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.AccountBookVO} // Updated Swagger Response
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /api/v1/user/account-books [get]
// @Security ApiKeyAuth
func (c *UserAccountBookControllerImpl) GetCurrentUserAccountBooks(ctx iris.Context) {
	opName := "获取当前用户账套列表"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	// 从上下文中获取用户ID
	userIDUint64, err := c.GetUserIDFromContext(ctx) // Use BaseController method
	if err != nil {
		c.HandleError(ctx, err, opName) // Handles apperrors.ErrAuthUnauthorized or other errors
		return
	}
	userID := uint(userIDUint64) // Service expects uint

	// 调用服务获取账套列表
	accountBooks, serviceErr := c.userAccountBookService.GetAccountBooksByUserID(ctx.Request().Context(), userID)
	if serviceErr != nil {
		c.HandleError(ctx, serviceErr, opName) // Use c.HandleError
		return
	}

	// 确保返回空数组而不是 nil
	if accountBooks == nil {
		accountBooks = []*vo.AccountBookVO{}
	}

	// 将列表包装在 map[string]interface{}{} 中以符合期望的输出结构
	responseData := map[string]interface{}{"list": accountBooks}
	c.Success(ctx, responseData) // Use c.Success
}

// GetUserAccountBooks 获取指定用户的账套列表
// @Summary 获取指定用户账套列表
// @Description 获取指定用户有权访问的所有账套列表
// @Tags UserManagement, AccountBookManagement // 可以根据需要调整 Tags
// @Produce json
// @Param userId path uint true "用户ID"
// @Success 200 {object} response.Response{data=[]vo.AccountBookSimpleVO} "成功响应，返回账套简要信息列表"
// @Failure 400 {object} response.Response "无效的用户ID参数"
// @Failure 401 {object} response.Response "未授权 (如果需要权限检查)"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/users/{userId}/account-books [get] // 确认路由路径与 router.go 一致
// @Security ApiKeyAuth // 如果需要认证
func (c *UserAccountBookControllerImpl) GetUserAccountBooks(ctx iris.Context) {
	opName := "获取指定用户账套列表"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := c.GetPathUintParamWithError(ctx, "userId") // <<< Use BaseController method
	if err != nil {
		c.HandleError(ctx, err, opName) // Handles parameter error
		return
	}
	userID := uint(userIDUint64)

	// 调用 Service 层方法获取账套列表
	accountBooks, serviceErr := c.userAccountBookService.GetAccountBooksForUser(ctx.Request().Context(), userID)
	if serviceErr != nil {
		c.HandleError(ctx, serviceErr, opName) // Use c.HandleError
		return
	}

	// 确保返回空数组而不是 nil
	if accountBooks == nil {
		accountBooks = []*vo.AccountBookSimpleVO{}
	}
	c.Success(ctx, accountBooks) // Use c.Success
}

// AssignAccountBookToUser 将账套分配给指定用户
// @Summary 分配账套给用户
// @Description 管理员将指定账套分配给指定用户
// @Tags AccountBookManagement, UserManagement
// @Produce json
// @Param userId path uint true "用户ID"
// @Param accountBookId path uint true "账套ID"
// @Success 200 {object} response.Response "分配成功"
// @Failure 400 {object} response.Response "参数错误或业务错误 (如已分配)"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "用户或账套不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/users/{userId}/account-books/{accountBookId} [post]
// @Security ApiKeyAuth
func (c *UserAccountBookControllerImpl) AssignAccountBookToUser(ctx iris.Context) {
	opName := "分配账套给用户"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := c.GetPathUintParamWithError(ctx, "userId")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	userID := uint(userIDUint64)

	accountBookIDUint64, err := c.GetPathUintParamWithError(ctx, "accountBookId")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	accountBookID := uint(accountBookIDUint64)

	req := &dto.AssignAccountBookToUserReq{
		UserID:        userID,
		AccountBookID: accountBookID,
	}

	serviceErr := c.userAccountBookService.AssignAccountBookToUser(ctx.Request().Context(), req)
	if serviceErr != nil {
		c.HandleError(ctx, serviceErr, opName)
		return
	}

	c.SuccessWithMessage(ctx, "分配成功", nil) // Use c.SuccessWithMessage
}

// RemoveAccountBookFromUser 从指定用户移除账套
// @Summary 从用户移除账套
// @Description 管理员从指定用户移除指定账套的访问权限
// @Tags AccountBookManagement, UserManagement
// @Produce json
// @Param userId path uint true "用户ID"
// @Param accountBookId path uint true "账套ID"
// @Success 200 {object} response.Response "移除成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "关联不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/users/{userId}/account-books/{accountBookId} [delete]
// @Security ApiKeyAuth
func (c *UserAccountBookControllerImpl) RemoveAccountBookFromUser(ctx iris.Context) {
	opName := "从用户移除账套"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := c.GetPathUintParamWithError(ctx, "userId")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	userID := uint(userIDUint64)

	accountBookIDUint64, err := c.GetPathUintParamWithError(ctx, "accountBookId")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	accountBookID := uint(accountBookIDUint64)

	req := &dto.RemoveAccountBookFromUserReq{
		UserID:        userID,
		AccountBookID: accountBookID,
	}

	serviceErr := c.userAccountBookService.RemoveAccountBookFromUser(ctx.Request().Context(), req)
	if serviceErr != nil {
		c.HandleError(ctx, serviceErr, opName)
		return
	}

	c.SuccessWithMessage(ctx, "移除成功", nil) // Use c.SuccessWithMessage
}

// UpdateUserAccountBooks 更新指定用户的账套关联 (批量覆盖)
// @Summary 更新用户账套关联
// @Description 管理员批量更新指定用户关联的账套列表 (覆盖旧关联)
// @Tags UserManagement, AccountBookManagement
// @Accept json
// @Produce json
// @Param userId path uint true "用户ID"
// @Param accountBooks body dto.UserAccountBookUpdateDTO true "账套ID列表请求体, e.g., {\"accountBookIds\": [1, 2]}"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "参数错误或请求体解析失败"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "用户不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/v1/sys/users/{userId}/account-books [put]
// @Security ApiKeyAuth
func (c *UserAccountBookControllerImpl) UpdateUserAccountBooks(ctx iris.Context) {
	opName := "更新用户账套关联"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := c.GetPathUintParamWithError(ctx, "userId")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	userID := uint(userIDUint64)

	var req dto.UserAccountBookUpdateDTO
	var accountBookIDs []uint

	if ctx.Request().ContentLength == 0 {
		accountBookIDs = []uint{}
	} else {
		if err := ctx.ReadJSON(&req); err != nil {
			c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("读取或解析账套ID列表失败: %v", err)).WithCause(err), opName)
			return
		}

		validationErrs := validator.Struct(req)
		if validationErrs != nil {
			c.GetLogger().Warn(ctx.Request().Context(), opName+" - 请求体验证失败", logger.WithField("userID", userID), logger.WithField("errors", validationErrs))
			customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
			c.FailWithError(ctx, customErr)
			return
		}
		accountBookIDs = req.AccountBookIDs
	}

	serviceErr := c.userAccountBookService.UpdateUserAccountBooks(ctx.Request().Context(), userID, accountBookIDs)
	if serviceErr != nil {
		c.HandleError(ctx, serviceErr, opName)
		return
	}

	c.SuccessWithMessage(ctx, "用户账套更新成功", nil)
}

// TODO: Add other methods if needed, e.g., for assigning/removing account books
// These administrative actions might belong under different routes:
// - POST /api/v1/sys/users/{userId}/account-books/{accountBookId}  (Assign)
// - DELETE /api/v1/sys/users/{userId}/account-books/{accountBookId} (Remove)
// or
// - POST /api/v1/sys/account-books/{accountBookId}/users/{userId}  (Assign)
// - DELETE /api/v1/sys/account-books/{accountBookId}/users/{userId} (Remove)
