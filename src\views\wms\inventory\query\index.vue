<template>
  <div class="inventory-query-container">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline label-width="80px">
        <el-form-item label="仓库">
          <el-select
            v-model="searchForm.warehouseId"
            placeholder="请选择仓库"
            clearable
            style="width: 200px"
            @change="handleWarehouseChange"
          >
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="库位">
          <el-select
            v-model="searchForm.locationId"
            placeholder="请选择库位"
            clearable
            style="width: 200px"
            :disabled="!searchForm.warehouseId"
          >
            <el-option
              v-for="location in locationOptions"
              :key="location.id"
              :label="`${location.code} - ${location.name}`"
              :value="location.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="物料SKU">
          <el-input
            v-model="searchForm.itemSku"
            placeholder="请输入物料SKU"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="物料名称">
          <el-input
            v-model="searchForm.itemName"
            placeholder="请输入物料名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="批次号">
          <el-input
            v-model="searchForm.batchNo"
            placeholder="请输入批次号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="库存状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="showAdvancedSearch = !showAdvancedSearch">
            <el-icon><Setting /></el-icon>
            高级搜索
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 高级搜索 -->
      <div v-show="showAdvancedSearch" class="advanced-search">
        <el-divider content-position="left">高级搜索</el-divider>
        <el-form :model="searchForm" inline label-width="100px">
          <el-form-item label="数量范围">
            <el-input-number
              v-model="searchForm.quantityMin"
              placeholder="最小数量"
              :min="0"
              style="width: 120px"
            />
            <span style="margin: 0 8px">-</span>
            <el-input-number
              v-model="searchForm.quantityMax"
              placeholder="最大数量"
              :min="0"
              style="width: 120px"
            />
          </el-form-item>
          
          <el-form-item label="过期日期">
            <el-date-picker
              v-model="expiryDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleExpiryDateChange"
            />
          </el-form-item>
          
          <el-form-item label="其他选项">
            <el-checkbox v-model="searchForm.includeZero">包含零库存</el-checkbox>
            <el-checkbox v-model="searchForm.onlyAvailable">仅显示可用库存</el-checkbox>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ inventoryStats.totalItems }}</div>
            <div class="stat-label">总物料数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(inventoryStats.totalQuantity) }}</div>
            <div class="stat-label">总库存量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(inventoryStats.availableQuantity) }}</div>
            <div class="stat-label">可用库存</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ formatNumber(inventoryStats.frozenQuantity) }}</div>
            <div class="stat-label">冻结库存</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-card class="main-content">
      <template #header>
        <div class="card-header">
          <span>库存列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <VNTable
        ref="vnTableRef"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        :show-operations="true"
        :operation-width="200"
        operation-fixed="right"
        row-key="id"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @refresh="handleRefresh"
      >
        <!-- 自定义列插槽 -->
        <template #column-itemSku="{ row }">
          <el-link
            type="primary"
            @click="handleViewDetail(row)"
            :underline="false"
          >
            {{ row.itemSku }}
          </el-link>
        </template>

        <template #column-status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ row.statusName }}
          </el-tag>
        </template>

        <template #column-quantity="{ row }">
          <div class="quantity-info">
            <div class="total-qty">总量: {{ formatNumber(row.quantity) }}</div>
            <div class="quantity-detail">
              <span class="available">可用: {{ formatNumber(row.availableQty) }}</span>
              <span class="frozen">冻结: {{ formatNumber(row.frozenQty) }}</span>
              <span class="allocated">分配: {{ formatNumber(row.allocatedQty) }}</span>
            </div>
          </div>
        </template>

        <template #column-expiryDate="{ row }">
          <div v-if="row.expiryDate">
            <div>{{ row.expiryDate }}</div>
            <div v-if="row.daysToExpiry !== undefined" class="expiry-info">
              <el-tag
                v-if="row.isExpired"
                type="danger"
                size="small"
              >
                已过期
              </el-tag>
              <el-tag
                v-else-if="row.isNearExpiry"
                type="warning"
                size="small"
              >
                {{ row.daysToExpiry }}天后过期
              </el-tag>
              <span v-else class="normal-expiry">
                {{ row.daysToExpiry }}天后过期
              </span>
            </div>
          </div>
          <span v-else class="no-expiry">无过期日期</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleViewDetail(row)"
          >
            查看详情
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleAdjust(row)"
            v-if="checkPermission('inventory:adjust')"
          >
            调整
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleMove(row)"
            v-if="checkPermission('inventory:move')"
          >
            移动
          </el-button>
        </template>
      </VNTable>
    </el-card>

    <!-- 库存详情对话框 -->
    <!-- <InventoryDetailDialog
      ref="inventoryDetailDialogRef"
      @refresh="handleRefresh"
    /> -->

    <!-- 库存调整对话框 -->
    <!-- <InventoryAdjustmentDialog
      ref="inventoryAdjustmentDialogRef"
      @confirm="handleRefresh"
    /> -->

    <!-- 库存移动对话框 -->
    <!-- <InventoryMovementDialog
      ref="inventoryMovementDialogRef"
      @confirm="handleRefresh"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElCard, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElButton, ElIcon, ElRow, ElCol, ElLink, ElTag, ElMessage, ElInputNumber, ElDatePicker, ElDivider, ElCheckbox } from 'element-plus'
import { Search, Refresh, Setting, Download } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
// import InventoryDetailDialog from './components/InventoryDetailDialog.vue'
// import InventoryAdjustmentDialog from './components/InventoryAdjustmentDialog.vue'
// import InventoryMovementDialog from './components/InventoryMovementDialog.vue'
import type { TableColumn } from '@/components/VNTable/types'
import type { WmsInventoryVO } from '@/types/wms/inventory'
import { useInventoryQueryStore } from '@/stores/wms/inventoryQuery'
import { checkPermission } from '@/hooks/usePermission'
import { exportInventory } from '@/api/wms/inventoryQuery'

// Store
const inventoryQueryStore = useInventoryQueryStore()

// 响应式数据
const vnTableRef = ref<InstanceType<typeof VNTable>>()
// const inventoryDetailDialogRef = ref<InstanceType<typeof InventoryDetailDialog>>()
// const inventoryAdjustmentDialogRef = ref<InstanceType<typeof InventoryAdjustmentDialog>>()
// const inventoryMovementDialogRef = ref<InstanceType<typeof InventoryMovementDialog>>()

// 高级搜索显示状态
const showAdvancedSearch = ref(false)

// 过期日期范围
const expiryDateRange = ref<[string, string] | null>(null)

// 计算属性
const { 
  list, 
  total, 
  loading, 
  searchForm, 
  pagination, 
  warehouseOptions, 
  locationOptions, 
  statusOptions, 
  inventoryStats 
} = inventoryQueryStore

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'itemSku', label: '物料SKU', width: 120, slot: true },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'warehouseName', label: '仓库', width: 120 },
  { prop: 'locationCode', label: '库位', width: 100 },
  { prop: 'batchNo', label: '批次号', width: 120 },
  { prop: 'quantity', label: '库存数量', width: 150, slot: true },
  { prop: 'unitOfMeasure', label: '单位', width: 80 },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'expiryDate', label: '过期日期', width: 140, slot: true },
  { prop: 'lastUpdated', label: '最后更新', width: 160 }
])

// 方法
const loadData = async () => {
  await inventoryQueryStore.fetchList()
}

const handleSearch = () => {
  searchForm.pageNum = 1
  loadData()
}

const handleReset = () => {
  inventoryQueryStore.resetSearchForm()
  expiryDateRange.value = null
  showAdvancedSearch.value = false
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  inventoryQueryStore.setPagination(page, searchForm.pageSize || 20)
  loadData()
}

const handlePageSizeChange = (size: number) => {
  inventoryQueryStore.setPagination(1, size)
  loadData()
}

const handleWarehouseChange = (warehouseId: number | undefined) => {
  searchForm.locationId = undefined
  if (warehouseId) {
    inventoryQueryStore.fetchLocationOptions(warehouseId)
  }
}

const handleExpiryDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.expiryDateStart = dates[0]
    searchForm.expiryDateEnd = dates[1]
  } else {
    searchForm.expiryDateStart = undefined
    searchForm.expiryDateEnd = undefined
  }
}

const handleViewDetail = (row: WmsInventoryVO) => {
  // inventoryDetailDialogRef.value?.open(row.id)
  ElMessage.info('库存详情功能开发中...')
}

const handleAdjust = (row: WmsInventoryVO) => {
  // inventoryAdjustmentDialogRef.value?.open(row)
  ElMessage.info('库存调整功能开发中...')
}

const handleMove = (row: WmsInventoryVO) => {
  // inventoryMovementDialogRef.value?.open(row)
  ElMessage.info('库存移动功能开发中...')
}

const handleExport = async () => {
  try {
    const blob = await exportInventory({
      ...searchForm,
      exportFormat: 'excel'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `库存数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 工具方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'AVAILABLE': 'success',
    'FROZEN_QC': 'warning',
    'FROZEN_COUNT': 'warning',
    'FROZEN_CUSTOMER': 'warning',
    'HOLD': 'warning',
    'DAMAGED': 'danger',
    'EXPIRED': 'danger',
    'ALLOCATED': 'info',
    'PENDING_PUTAWAY': 'info',
    'PENDING_PICK': 'info',
    'IN_TRANSIT': 'info',
    'PACKING': 'info'
  }
  return typeMap[status] || 'info'
}

const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 生命周期
onMounted(async () => {
  await inventoryQueryStore.fetchWarehouseOptions()
  await loadData()
})

// 监听仓库变化
watch(() => searchForm.warehouseId, (newVal) => {
  if (newVal) {
    inventoryQueryStore.fetchLocationOptions(newVal)
  } else {
    locationOptions.splice(0)
  }
})
</script>

<style scoped>
.inventory-query-container {
  padding: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.advanced-search {
  margin-top: 16px;
}

.stats-row {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.main-content {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.quantity-info {
  font-size: 12px;
}

.total-qty {
  font-weight: 600;
  margin-bottom: 4px;
}

.quantity-detail {
  display: flex;
  gap: 8px;
  color: #909399;
}

.quantity-detail span {
  font-size: 11px;
}

.available {
  color: #67c23a;
}

.frozen {
  color: #e6a23c;
}

.allocated {
  color: #409eff;
}

.expiry-info {
  margin-top: 4px;
}

.normal-expiry {
  font-size: 11px;
  color: #909399;
}

.no-expiry {
  color: #c0c4cc;
  font-style: italic;
}
</style>
