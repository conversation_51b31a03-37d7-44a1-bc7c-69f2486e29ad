package repository

import (
	"context"
	stdErrors "errors" // Import standard errors
	"time"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors" // Use alias for custom errors
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util" // <<< 确保导入 util 包

	"gorm.io/gorm"
)

// RoleRepository 角色仓库接口
// 负责角色数据的增删改查操作
type RoleRepository interface {
	// 继承基础仓库接口
	BaseRepository[entity.Role, uint]

	// FindByCode 根据角色编码查询角色
	// 参数:
	//   - ctx: 上下文
	//   - code: 角色编码
	// 返回:
	//   - *entity.Role: 角色实体
	//   - error: 错误信息
	FindByCode(ctx context.Context, code string) (*entity.Role, error)

	// FindByName 根据角色名称查询角色
	// 参数:
	//   - ctx: 上下文
	//   - name: 角色名称
	// 返回:
	//   - *entity.Role: 角色实体
	//   - error: 错误信息
	FindByName(ctx context.Context, name string) (*entity.Role, error)

	// UpdateStatus 更新角色状态
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - status: 新状态
	// 返回:
	//   - error: 错误信息
	UpdateStatus(ctx context.Context, roleID uint, status int) error

	// CheckCodeExists 检查角色编码是否存在
	// 参数:
	//   - ctx: 上下文
	//   - code: 角色编码
	//   - excludeID: 排除的角色ID（可选，用于更新时检查）
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	CheckCodeExists(ctx context.Context, code string, excludeID ...uint) (bool, error)

	// CheckNameExists 检查角色名称是否存在
	// 参数:
	//   - ctx: 上下文
	//   - name: 角色名称
	//   - excludeID: 排除的角色ID（可选，用于更新时检查）
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	CheckNameExists(ctx context.Context, name string, excludeID ...uint) (bool, error)

	// FindDefaultRoles 查询默认角色
	// 参数:
	//   - ctx: 上下文
	// 返回:
	//   - []*entity.Role: 角色列表
	//   - error: 错误信息
	FindDefaultRoles(ctx context.Context) ([]*entity.Role, error)

	// FindRolesByDataScope 根据数据权限范围查询角色
	// 参数:
	//   - ctx: 上下文
	//   - dataScope: 数据权限范围
	// 返回:
	//   - []*entity.Role: 角色列表
	//   - error: 错误信息
	FindRolesByDataScope(ctx context.Context, dataScope int) ([]*entity.Role, error)

	// FindRolesByPage 分页查询角色
	// 参数:
	//   - ctx: 上下文
	//   - page: 页码
	//   - size: 每页大小
	//   - name: 角色名称（可选，模糊查询）
	//   - code: 角色编码（可选，模糊查询）
	//   - status: 状态（可选）
	//   - startTime: 开始时间（可选）
	//   - endTime: 结束时间（可选）
	// 返回:
	//   - *response.PageResult: 分页结果
	//   - error: 错误信息
	FindRolesByPage(ctx context.Context, page, size int, name, code string, status *int, startTime, endTime *time.Time) (*response.PageResult, error)
}

// RoleRepositoryImpl 角色仓库实现
type RoleRepositoryImpl struct {
	BaseRepository[entity.Role, uint] // 嵌入基础仓库接口
}

// NewRoleRepository 创建角色仓库
// 参数:
//   - db: 数据库连接
//
// 返回:
//   - RoleRepository: 角色仓库接口
func NewRoleRepository(db *gorm.DB) RoleRepository {
	return &RoleRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.Role, uint](db), // 使用构造函数初始化接口
	}
}

// FindByCode 根据角色编码查询角色
func (r *RoleRepositoryImpl) FindByCode(ctx context.Context, code string) (*entity.Role, error) {
	var role entity.Role
	result := r.GetDB(ctx).Where("code = ?", code).First(&role)
	if result.Error != nil {
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "角色不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据角色编码查询角色失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询角色失败").WithCause(result.Error)
	}
	return &role, nil
}

// FindByName 根据角色名称查询角色
func (r *RoleRepositoryImpl) FindByName(ctx context.Context, name string) (*entity.Role, error) {
	var role entity.Role
	result := r.GetDB(ctx).Where("name = ?", name).First(&role)
	if result.Error != nil {
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "角色不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据角色名称查询角色失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询角色失败").WithCause(result.Error)
	}
	return &role, nil
}

// UpdateStatus 更新角色状态
func (r *RoleRepositoryImpl) UpdateStatus(ctx context.Context, roleID uint, status int) error {
	db := r.GetDB(ctx)
	log := logger.WithContext(ctx)

	uid64, err := util.GetUserIDFromStdContext(ctx)
	if err != nil {
		log.WithError(err).Warn("RoleRepository.UpdateStatus: 无法从上下文中获取UserID，UpdatedBy将不会被设置")
	}

	updates := map[string]interface{}{
		"status": status,
	}
	if uid64 > 0 {
		updates["updated_by"] = uint(uid64)
		log.Infof("RoleRepository.UpdateStatus: Setting updated_by to %d for role ID %d", uint(uid64), roleID)
	} else {
		log.Warnf("RoleRepository.UpdateStatus: updated_by will not be set for role ID %d as UserID could not be retrieved or was invalid (0).", roleID)
	}

	result := db.Model(&entity.Role{}).Where("id = ?", roleID).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("更新角色状态失败")
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新状态失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "角色不存在")
	}
	return nil
}

// CheckCodeExists 检查角色编码是否存在
func (r *RoleRepositoryImpl) CheckCodeExists(ctx context.Context, code string, excludeID ...uint) (bool, error) {
	var count int64
	query := r.GetDB(ctx).Model(&entity.Role{}).Where("code = ?", code)

	// 如果提供了排除ID，则排除该ID的角色
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}

	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查角色编码是否存在失败")
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查角色编码失败").WithCause(result.Error)
	}

	return count > 0, nil
}

// CheckNameExists 检查角色名称是否存在
func (r *RoleRepositoryImpl) CheckNameExists(ctx context.Context, name string, excludeID ...uint) (bool, error) {
	var count int64
	query := r.GetDB(ctx).Model(&entity.Role{}).Where("name = ?", name)

	// 如果提供了排除ID，则排除该ID的角色
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}

	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查角色名称是否存在失败")
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查角色名称失败").WithCause(result.Error)
	}

	return count > 0, nil
}

// FindRolesByPage 分页查询角色
func (r *RoleRepositoryImpl) FindRolesByPage(ctx context.Context, page, size int, name, code string, status *int, startTime, endTime *time.Time) (*response.PageResult, error) {
	// 构建查询条件
	var conditions []Condition

	if name != "" {
		conditions = append(conditions, NewLikeCondition("name", name))
	}

	if code != "" {
		conditions = append(conditions, NewLikeCondition("code", code))
	}

	if status != nil {
		conditions = append(conditions, NewEqualCondition("status", *status))
	}

	if startTime != nil && endTime != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *startTime, *endTime))
	} else if startTime != nil {
		conditions = append(conditions, NewGreaterThanEqualCondition("created_at", *startTime))
	} else if endTime != nil {
		conditions = append(conditions, NewLessThanEqualCondition("created_at", *endTime))
	}

	// 将 []Condition 转换为 []QueryCondition
	queryConditions := make([]QueryCondition, len(conditions))
	for i, c := range conditions {
		queryConditions[i] = c // Condition 实现了 QueryCondition 接口
	}

	// 创建 PageQuery 对象
	pageQuery := &response.PageQuery{
		PageNum:  page,
		PageSize: size,
		Sort:     []response.SortInfo{{Field: "sort", Order: "ASC"}}, // 设置默认排序
	}

	// 调用基础仓库的分页查询方法
	return r.FindByPage(ctx, pageQuery, queryConditions) // <<< 使用转换后的切片
}

// FindDefaultRoles 查询默认角色
func (r *RoleRepositoryImpl) FindDefaultRoles(ctx context.Context) ([]*entity.Role, error) {
	var roles []*entity.Role
	result := r.GetDB(ctx).Where("is_default = ? AND status = ?", true, 1).Find(&roles)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("查询默认角色失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询默认角色失败").WithCause(result.Error)
	}
	return roles, nil
}

// FindRolesByDataScope 根据数据权限范围查询角色
func (r *RoleRepositoryImpl) FindRolesByDataScope(ctx context.Context, dataScope int) ([]*entity.Role, error) {
	var roles []*entity.Role
	result := r.GetDB(ctx).Where("data_scope = ? AND status = ?", dataScope, 1).Find(&roles)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("根据数据权限范围查询角色失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询角色失败").WithCause(result.Error)
	}
	return roles, nil
}
