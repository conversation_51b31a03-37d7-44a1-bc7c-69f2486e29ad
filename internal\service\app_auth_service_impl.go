package service

import (
	"context"

	"backend/internal/model/dto"
	"backend/internal/model/vo"
	"backend/internal/repository"
	"backend/pkg/config" // Assuming config package exists
	"backend/pkg/constant"
	"backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/util" // Assuming password comparison and JWT utils are here
)

// AuthService 认证服务接口
type AuthService interface {
	// 基础服务 (如果需要继承 BaseService)
	// BaseService

	// Login 用户登录 (添加 loginIP 参数)
	// Returns UserLoginVO (without refresh token), refresh token string, and error
	Login(ctx context.Context, loginDTO dto.UserLoginDTO, loginIP string) (*vo.UserLoginVO, string, error)

	// TODO: Add Logout, RefreshToken methods if needed
}

// AuthServiceImpl 认证服务实现
type AuthServiceImpl struct {
	*BaseServiceImpl
	userService    UserService
	captchaService CaptchaService // Directly depend on CaptchaService instance
	userRepo       repository.UserRepository
	tokenService   TokenService
	appCfg         *config.Configuration
}

// NewAuthService 创建认证服务
// Dependencies: UserService, CaptchaService, TokenService, Config
func NewAuthService(serviceManager *ServiceManager, appCfg *config.Configuration, captchaService CaptchaService) AuthService {
	// 调用 NewTokenService，假设它返回 TokenService 实例且不需要 logger 作为直接参数
	tokenSvc := NewTokenService(serviceManager, appCfg)
	// 假设 NewTokenService 内部会处理初始化失败 (e.g., log fatal)
	// 或者在创建时保证非 nil (如果依赖简单且不易失败)

	// No need to create captchaService here, it's injected
	if appCfg.Captcha.Enable && captchaService == nil {
		serviceManager.GetLogger().Fatal(context.Background(), "Captcha is enabled but CaptchaService instance is nil")
	}

	impl := &AuthServiceImpl{
		BaseServiceImpl: NewBaseService(serviceManager),
		userService:     GetUserService(serviceManager),
		captchaService:  captchaService, // Use the injected instance
		userRepo:        serviceManager.GetRepositoryManager().GetUserRepository(),
		tokenService:    tokenSvc,
		appCfg:          appCfg,
	}
	return impl
}

// Login 用户登录
// Returns UserLoginVO (without refresh token), refresh token string, and error
func (s *AuthServiceImpl) Login(ctx context.Context, loginDTO dto.UserLoginDTO, loginIP string) (*vo.UserLoginVO, string, error) {
	// Use logger.WithContext outside transaction if possible, or pass txServiceManager.GetLogger()
	outerLog := logger.WithContext(ctx).WithFields(logger.Fields{
		"username": loginDTO.Username,
		"loginIP":  loginIP,
	})
	outerLog.Debug("开始用户登录流程") // Changed log message

	// <<< Step 1: Captcha Verification (Moved outside transaction) >>>
	if s.appCfg.Captcha.Enable {
		if loginDTO.CaptchaID == "" || loginDTO.Captcha == "" {
			outerLog.Warn("验证码缺失")
			return nil, "", errors.NewParamError(errors.CODE_PARAMS_MISSING, "需要验证码")
		}
		if s.captchaService == nil {
			outerLog.Error("CaptchaService 未初始化")
			return nil, "", errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "验证码服务不可用")
		}
		isValid, err := s.captchaService.VerifyCaptcha(ctx, loginDTO.CaptchaID, loginDTO.Captcha)
		if err != nil {
			outerLog.Warn("验证码验证失败", logger.WithError(err))
			// Try to preserve the original error code if it's a custom error
			originalCode := errors.GetErrorCode(err)
			wrappedErr := errors.WrapError(err, constant.CAPTCHA_INVALID, "验证码错误") // Default wrap
			if originalCode != errors.CODE_SYSTEM_UNKNOWN {                        // If original exists and is not default
				wrappedErr.Code = originalCode // Use original code
			}
			return nil, "", wrappedErr
		}
		if !isValid {
			outerLog.Warn("验证码错误", logger.WithField("isValid", isValid))
			return nil, "", errors.NewParamError(constant.CAPTCHA_INVALID, "验证码无效")
		}
		outerLog.Debug("验证码验证通过")
	} else {
		outerLog.Debug("验证码未启用，跳过验证")
	}

	var loginVO *vo.UserLoginVO
	var refreshToken string // Variable to hold the refresh token
	var transactionErr error

	outerLog.Debug("开始数据库事务") // Log before transaction starts

	// <<< Step 2: Database Transaction >>>
	transactionErr = s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		// 获取事务内的 logger，并添加 context 和基础字段
		txLog := logger.WithContext(ctx).WithFields(logger.Fields{
			"username": loginDTO.Username,
			"loginIP":  loginIP,
			"tx":       true, // 标记为事务内日志
		})

		// <<< Fix 1: Get transactional services/repos FIRST >>>
		txUserService := GetUserService(txServiceManager)
		txUserRepo := txServiceManager.GetRepositoryManager().GetUserRepository()
		// CaptchaService 和 TokenService 通常在事务外操作，或其操作本身是幂等的/非关键事务性的

		// --- Transactional Logic Start ---

		// 1. Find user by username (using transactional service)
		user, err := txUserService.GetUserByUsername(ctx, loginDTO.Username) // <<< Use txUserService >>>
		if err != nil {
			if errors.IsError(err, constant.USER_NOT_FOUND) {
				// If user not found, return specific login failed error
				txLog.Warn("Login failed: User not found")
				return errors.NewBusinessError(constant.LOGIN_FAILED, "用户名或密码错误")
			}
			// For other errors during user lookup, wrap and return
			txLog.Error("Login failed: Error fetching user", logger.WithError(err))
			return errors.WrapError(err, errors.CODE_SYSTEM_INTERNAL, "登录时查询用户信息失败") // Wrapped
		}
		txLog = txLog.WithFields(logger.Fields{"userId": user.ID}) // 更新 txLog
		txLog.Debug("事务中用户已找到")

		// 3. 检查用户状态
		if user.Status != 1 {
			txLog.Warn("用户状态异常", logger.WithField("status", user.Status)) // 使用 txLog
			return errors.NewAuthError(errors.CODE_AUTH_ACCOUNT_LOCKED, "用户已被禁用或锁定")
		}

		// 4. 验证密码 (需要用户实体，使用事务内 Repo)
		// Note: GetUserByUsername returns a VO, still need entity for password check
		userEntity, repoErr := txUserRepo.FindByUsername(ctx, loginDTO.Username)
		if repoErr != nil {
			txLog.Error("事务中获取用户实体失败", logger.WithError(repoErr)) // 使用 txLog
			return errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "获取用户信息失败").WithCause(repoErr)
		}

		passwordErr := util.ComparePassword(userEntity.Password, loginDTO.Password)

		if passwordErr != nil {
			txLog.Warn("密码验证失败", logger.WithError(passwordErr)) // 使用 txLog
			return errors.ErrAuthLoginFailed                    // Use pre-defined AuthLoginFailed error
		}
		txLog.Debug("密码验证通过")

		// 5. 验证码验证 <<< MOVED OUTSIDE TRANSACTION >>>

		// 6. 生成 Token (使用事务内 Service 获取角色，使用外部 TokenService 生成)
		roleVOs, rolesErr := txUserService.GetUserRoles(ctx, user.ID) // <<< Use txUserService >>>
		if rolesErr != nil {
			txLog.Error("事务中获取用户角色失败", logger.WithError(rolesErr)) // 使用 txLog
			roleVOs = []*vo.RoleVO{}                               // Continue without roles or return based on requirements
		}
		roleIDs := make([]uint, len(roleVOs))
		for i, r := range roleVOs {
			roleIDs[i] = r.ID
		}

		// 调用外部 TokenService 生成令牌 (通常不需要事务性)
		tokensVO, tokenErr := s.tokenService.GenerateTokens(ctx, user.ID, user.Username, roleIDs, loginDTO.Remember, user.IsAdmin)
		if tokenErr != nil {
			txLog.Error("调用 TokenService 生成令牌失败", logger.WithError(tokenErr))
			// Wrap the error from token service if possible
			wrappedTokenErr := errors.WrapError(tokenErr, errors.CODE_SYSTEM_INTERNAL, "生成登录令牌失败")
			// Check if tokenErr already has a specific code and use it
			if specificCode := errors.GetErrorCode(tokenErr); specificCode != errors.CODE_SYSTEM_UNKNOWN {
				wrappedTokenErr.Code = specificCode
			}
			return wrappedTokenErr
		}
		txLog.Debug("Token 生成成功")

		// 7. 更新用户登录信息 (使用事务内 Repo - 关键事务步骤)
		if updateErr := txUserRepo.UpdateLoginInfo(ctx, user.ID, loginIP); updateErr != nil {
			txLog.Error("事务中更新用户登录信息失败", logger.WithError(updateErr)) // 使用 txLog
			return errors.WrapError(updateErr, errors.CODE_DATA_UPDATE_FAILED, "更新用户登录信息失败")
		}
		txLog.Debug("事务中更新登录信息成功")

		// 8. 构建登录成功视图对象 (不包含 Refresh Token)
		userProfile := &vo.UserProfileVO{
			ID:                   user.ID,
			Username:             user.Username,
			Nickname:             user.Nickname,
			RealName:             user.RealName,
			Avatar:               user.Avatar,
			IsAdmin:              user.IsAdmin,
			DefaultAccountBookID: user.DefaultAccountBookID,
		}
		loginVO = &vo.UserLoginVO{
			Token:      tokensVO.AccessToken,
			ExpireTime: tokensVO.AccessExpiresAt,
			User:       userProfile,
		}
		// Store the refresh token to return it separately
		refreshToken = tokensVO.RefreshToken
		// --- Transactional Logic End ---

		return nil // 事务成功
	})

	if transactionErr != nil {
		outerLog.Warn("用户登录事务失败", logger.WithError(transactionErr))
		// 细化错误处理 (Keep existing logic)
		if errors.IsParamError(transactionErr) || errors.IsError(transactionErr, constant.CAPTCHA_INVALID) { // Captcha check might be redundant here but harmless
			return nil, "", transactionErr
		} else if errors.IsAuthError(transactionErr) || errors.IsError(transactionErr, constant.LOGIN_FAILED) { // Use constant here too
			return nil, "", errors.ErrAuthLoginFailed // Return specific login failed error
		}
		// For other transaction errors, return the wrapped error
		return nil, "", transactionErr
	}

	outerLog.Info("用户登录成功")
	return loginVO, refreshToken, nil
}

// TODO: Implement util.GetClientIPFromContext (or ensure IP is in context)
