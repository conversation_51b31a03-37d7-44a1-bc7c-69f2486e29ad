package constant

// 响应状态码常量
const (
	SUCCESS   = 0    // 成功
	SYS_ERROR = 1000 // 系统错误

	// 用户相关错误码 (2000-2999)
	USER_NOT_FOUND = 2000 // 用户不存在
	INVALID_PARAM  = 2001 // 无效参数
	UNAUTHORIZED   = 2002 // 未授权
	FORBIDDEN      = 2003 // 禁止访问
	TOKEN_EXPIRED  = 2004 // 令牌过期
	LOGIN_FAILED   = 2005 // 登录失败
	ACCOUNT_LOCKED = 2006 // 账户锁定
	PASSWORD_ERROR = 2007 // 密码错误
	INVALID_TOKEN  = 2008 // 无效令牌
	USER_DISABLED  = 2009 // 用户已禁用
	USER_EXISTS    = 2010 // 用户已存在
	USER_EXPIRED   = 2011 // 用户已过期
	USER_LOCKED    = 2012 // 用户已锁定

	// 数据相关错误码 (3000-3999)
	DATA_NOT_FOUND      = 3000 // 数据不存在
	DATA_ALREADY_EXISTS = 3001 // 数据已存在
	DATA_ERROR          = 3002 // 数据错误
	VALIDATION_ERROR    = 3003 // 验证错误
	DATABASE_ERROR      = 3004 // 数据库错误
	DATA_INTEGRITY      = 3005 // 数据完整性错误
	DATA_CONFLICT       = 3006 // 数据冲突
	DATA_EXPIRED        = 3007 // 数据已过期
	DATA_CORRUPT        = 3008 // 数据已损坏
	DATA_TOO_LARGE      = 3009 // 数据过大

	// 业务相关错误码 (4000-4999)
	BUSINESS_ERROR      = 4000 // 业务错误
	OPERATION_FAILED    = 4001 // 操作失败
	RESOURCE_EXHAUSTED  = 4002 // 资源耗尽
	QUOTA_EXCEEDED      = 4003 // 配额超出
	SERVICE_UNAVAILABLE = 4004 // 服务不可用
	OPERATION_TIMEOUT   = 4005 // 操作超时
	OPERATION_DENIED    = 4006 // 操作被拒绝
	FLOW_CONTROL        = 4007 // 流量控制
	CONCURRENT_LIMIT    = 4008 // 并发限制

	// 参数相关错误码 (5000-5999)
	PARAM_ERROR        = 5000 // 参数错误
	PARAM_EMPTY        = 5001 // 参数为空
	PARAM_TYPE_ERROR   = 5002 // 参数类型错误
	PARAM_FORMAT_ERROR = 5003 // 参数格式错误
	PARAM_VALUE_ERROR  = 5004 // 参数值错误
	PARAM_MISSING      = 5005 // 参数缺失
	PARAM_OVERFLOW     = 5006 // 参数溢出
	PARAM_RANGE_ERROR  = 5007 // 参数范围错误

	// 文件相关错误码 (6000-6999)
	FILE_UPLOAD_ERROR   = 6000 // 文件上传错误
	FILE_TOO_LARGE      = 6001 // 文件过大
	FILE_FORMAT_ERROR   = 6002 // 文件格式错误
	FILE_NOT_FOUND      = 6003 // 文件不存在
	FILE_DOWNLOAD_ERROR = 6004 // 文件下载错误
	FILE_READ_ERROR     = 6005 // 文件读取错误
	FILE_WRITE_ERROR    = 6006 // 文件写入错误
	FILE_DELETE_ERROR   = 6007 // 文件删除错误
	FILE_CORRUPT        = 6008 // 文件损坏
	FILE_TYPE_ERROR     = 6009 // 文件类型错误
	FILE_SIZE_ERROR     = 6010 // 文件大小错误
	FILE_SAVE_ERROR     = 6011 // 文件保存错误
	FILE_REMOVE_ERROR   = 6012 // 文件删除错误
	FILE_MD5_ERROR      = 6013 // 文件MD5计算错误

	// 网络相关错误码 (7000-7999)
	NETWORK_ERROR      = 7000 // 网络错误
	REQUEST_TIMEOUT    = 7001 // 请求超时
	THIRD_PARTY_ERROR  = 7002 // 第三方服务错误
	CONNECTION_ERROR   = 7003 // 连接错误
	DNS_ERROR          = 7004 // DNS解析错误
	GATEWAY_ERROR      = 7005 // 网关错误
	REQUEST_TOO_LARGE  = 7006 // 请求过大
	REQUEST_RATE_LIMIT = 7007 // 请求频率限制
	SSL_ERROR          = 7008 // SSL错误
	PROXY_ERROR        = 7009 // 代理错误

	// 系统相关错误码 (8000-8999)
	SYSTEM_BUSY           = 8000 // 系统繁忙
	SYSTEM_MAINTENANCE    = 8001 // 系统维护中
	SYSTEM_UPGRADE        = 8002 // 系统升级中
	CONFIG_ERROR          = 8003 // 配置错误
	DEPENDENCY_ERROR      = 8004 // 依赖服务错误
	INTERNAL_SERVER_ERROR = 8005 // 服务器内部错误
	NOT_IMPLEMENTED       = 8006 // 功能未实现
	CANCELED              = 8007 // 操作已取消
	RESOURCE_DENIED       = 8008 // 资源拒绝

	// 文件相关错误 (40000-40099)
	ERROR_FILE_UPLOAD = 40001 // 文件上传失败
	ERROR_FILE_READ   = 40002 // 文件读取失败
	ERROR_FILE_WRITE  = 40003 // 文件写入失败
	ERROR_FILE_DELETE = 40004 // 文件删除失败
	ERROR_FILE_EMPTY  = 40005 // 文件内容为空

	// 授权相关错误 (50000-50099)
	ERROR_LICENSE_INVALID   = 50001 // 授权无效
	ERROR_LICENSE_EXPIRED   = 50002 // 授权已过期
	ERROR_LICENSE_MISMATCH  = 50003 // 授权不匹配
	ERROR_LICENSE_CREATE    = 50004 // 创建授权失败
	ERROR_LICENSE_NOT_FOUND = 50005 // 未找到授权

	// 验证码相关错误 (9000-9006)
	CAPTCHA_ERROR_BASE     = 9000                   // 验证码错误基础代码
	CAPTCHA_ERROR          = CAPTCHA_ERROR_BASE + 1 // 验证码错误
	CAPTCHA_EXPIRED        = CAPTCHA_ERROR_BASE + 2 // 验证码已过期
	CAPTCHA_NOT_FOUND      = CAPTCHA_ERROR_BASE + 3 // 验证码不存在
	CAPTCHA_ALREADY_USED   = CAPTCHA_ERROR_BASE + 4 // 验证码已使用
	CAPTCHA_INVALID        = CAPTCHA_ERROR_BASE + 5 // 验证码无效
	CAPTCHA_TOO_MANY_TRIES = CAPTCHA_ERROR_BASE + 6 // 验证码尝试次数过多
)

// 分页相关常量
const (
	DEFAULT_PAGE_SIZE  = 10     // 默认每页记录数
	MAX_PAGE_SIZE      = 100    // 最大每页记录数
	DEFAULT_PAGE_NUM   = 1      // 默认页码
	DEFAULT_SORT_FIELD = "id"   // 默认排序字段
	DEFAULT_SORT_ORDER = "desc" // 默认排序方向
)

// 排序方向常量
const (
	SORT_ASC  = "asc"  // 升序
	SORT_DESC = "desc" // 降序
)

// 文件相关常量
const (
	FILE_SIZE_LIMIT_DEFAULT = 10 * 1024 * 1024                                                 // 默认文件大小限制（10MB）
	FILE_ALLOW_TYPES        = ".jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.pdf,.txt,.zip,.rar" // 允许的文件类型
)

// 令牌相关常量
const (
	TOKEN_PREFIX              = "Bearer "     // 令牌前缀
	TOKEN_EXPIRE_TIME         = 3600 * 24     // 令牌过期时间（秒）
	REFRESH_TOKEN_EXPIRE_TIME = 3600 * 24 * 7 // 刷新令牌过期时间（秒）
)

// 上下文键值常量
const (
	CONTEXT_USER_ID         = "userId"        // 用户ID上下文键
	CONTEXT_USERNAME        = "username"      // 用户名上下文键
	CONTEXT_ROLE_IDS        = "roleIds"       // 角色ID列表上下文键
	CONTEXT_IS_ADMIN        = "isAdmin"       // 是否管理员上下文键 (新增)
	CONTEXT_ACCOUNT_BOOK_ID = "accountBookId" // 账套ID上下文键
	CONTEXT_REQUEST_ID      = "requestId"     // 请求ID上下文键
	CONTEXT_TENANT_ID       = "tenantId"      // 租户ID上下文键

	// 授权状态上下文键
	CONTEXT_LICENSE_STATUS = "licenseStatus" // 授权状态上下文键

	// 根据方案补充的键
	CONTEXT_CLIENT_IP          = "clientIP"         // 新增：客户端IP上下文键
	CONTEXT_TRACE_ID           = "traceID"          // 新增：追踪ID上下文键
	CONTEXT_REQUEST_START_TIME = "requestStartTime" // 新增：请求开始时间上下文键 (可选)
)

// 常量映射到错误信息
var CodeMessages = map[int]string{
	SUCCESS:   "操作成功",
	SYS_ERROR: "系统错误",

	// 用户相关错误信息
	USER_NOT_FOUND: "用户不存在",
	INVALID_PARAM:  "无效参数",
	UNAUTHORIZED:   "未授权",
	FORBIDDEN:      "禁止访问",
	TOKEN_EXPIRED:  "令牌已过期",
	LOGIN_FAILED:   "登录失败",
	ACCOUNT_LOCKED: "账户已锁定",
	PASSWORD_ERROR: "密码错误",
	INVALID_TOKEN:  "无效令牌",
	USER_DISABLED:  "用户已禁用",
	USER_EXISTS:    "用户已存在",
	USER_EXPIRED:   "用户已过期",
	USER_LOCKED:    "用户已锁定",

	// 数据相关错误信息
	DATA_NOT_FOUND:      "数据不存在",
	DATA_ALREADY_EXISTS: "数据已存在",
	DATA_ERROR:          "数据错误",
	VALIDATION_ERROR:    "验证错误",
	DATABASE_ERROR:      "数据库错误",
	DATA_INTEGRITY:      "数据完整性错误",
	DATA_CONFLICT:       "数据冲突",
	DATA_EXPIRED:        "数据已过期",
	DATA_CORRUPT:        "数据已损坏",
	DATA_TOO_LARGE:      "数据过大",

	// 业务相关错误信息
	BUSINESS_ERROR:      "业务错误",
	OPERATION_FAILED:    "操作失败",
	RESOURCE_EXHAUSTED:  "资源耗尽",
	QUOTA_EXCEEDED:      "配额超出",
	SERVICE_UNAVAILABLE: "服务不可用",
	OPERATION_TIMEOUT:   "操作超时",
	OPERATION_DENIED:    "操作被拒绝",
	FLOW_CONTROL:        "流量控制",
	CONCURRENT_LIMIT:    "并发限制",

	// 参数相关错误信息
	PARAM_ERROR:        "参数错误",
	PARAM_EMPTY:        "参数为空",
	PARAM_TYPE_ERROR:   "参数类型错误",
	PARAM_FORMAT_ERROR: "参数格式错误",
	PARAM_VALUE_ERROR:  "参数值错误",
	PARAM_MISSING:      "参数缺失",
	PARAM_OVERFLOW:     "参数溢出",
	PARAM_RANGE_ERROR:  "参数范围错误",

	// 文件相关错误信息
	FILE_UPLOAD_ERROR:   "文件上传错误",
	FILE_TOO_LARGE:      "文件过大",
	FILE_FORMAT_ERROR:   "文件格式错误",
	FILE_NOT_FOUND:      "文件不存在",
	FILE_DOWNLOAD_ERROR: "文件下载错误",
	FILE_READ_ERROR:     "文件读取错误",
	FILE_WRITE_ERROR:    "文件写入错误",
	FILE_DELETE_ERROR:   "文件删除错误",
	FILE_CORRUPT:        "文件损坏",
	FILE_TYPE_ERROR:     "文件类型错误",
	FILE_SIZE_ERROR:     "文件大小错误",
	FILE_SAVE_ERROR:     "文件保存错误",
	FILE_REMOVE_ERROR:   "文件删除错误",
	FILE_MD5_ERROR:      "文件MD5计算错误",

	// 网络相关错误信息
	NETWORK_ERROR:      "网络错误",
	REQUEST_TIMEOUT:    "请求超时",
	THIRD_PARTY_ERROR:  "第三方服务错误",
	CONNECTION_ERROR:   "连接错误",
	DNS_ERROR:          "DNS解析错误",
	GATEWAY_ERROR:      "网关错误",
	REQUEST_TOO_LARGE:  "请求过大",
	REQUEST_RATE_LIMIT: "请求频率限制",
	SSL_ERROR:          "SSL错误",
	PROXY_ERROR:        "代理错误",

	// 系统相关错误信息
	SYSTEM_BUSY:           "系统繁忙",
	SYSTEM_MAINTENANCE:    "系统维护中",
	SYSTEM_UPGRADE:        "系统升级中",
	CONFIG_ERROR:          "配置错误",
	DEPENDENCY_ERROR:      "依赖服务错误",
	INTERNAL_SERVER_ERROR: "服务器内部错误",
	NOT_IMPLEMENTED:       "功能未实现",
	CANCELED:              "操作已取消",
	RESOURCE_DENIED:       "资源拒绝",

	// 文件相关错误
	ERROR_FILE_UPLOAD: "文件上传失败",
	ERROR_FILE_READ:   "文件读取失败",
	ERROR_FILE_WRITE:  "文件写入失败",
	ERROR_FILE_DELETE: "文件删除失败",
	ERROR_FILE_EMPTY:  "文件内容为空",

	// 授权相关错误
	ERROR_LICENSE_INVALID:   "授权无效",
	ERROR_LICENSE_EXPIRED:   "授权已过期",
	ERROR_LICENSE_MISMATCH:  "授权不匹配",
	ERROR_LICENSE_CREATE:    "创建授权失败",
	ERROR_LICENSE_NOT_FOUND: "未找到授权",

	// 验证码相关错误
	CAPTCHA_ERROR:          "验证码错误",
	CAPTCHA_EXPIRED:        "验证码已过期",
	CAPTCHA_NOT_FOUND:      "验证码不存在",
	CAPTCHA_ALREADY_USED:   "验证码已使用",
	CAPTCHA_INVALID:        "验证码无效",
	CAPTCHA_TOO_MANY_TRIES: "验证码尝试次数过多",
}

// GetCodeMessage 根据代码获取错误信息
// 参数:
//   - code: 错误代码
//
// 返回:
//   - string: 错误信息
func GetCodeMessage(code int) string {
	if msg, ok := CodeMessages[code]; ok {
		return msg
	}
	return "未知错误"
}

const (
	PermUserCreate = "user:create"
	PermUserRead   = "user:read"
	PermUserUpdate = "user:update"
	PermUserDelete = "user:delete"
	PermUserList   = "user:list" // 通常列表权限和读权限分开
	// ... 其他 user 权限 ...

	PermRoleCreate = "role:create"
	PermRoleRead   = "role:read"
	// ... 其他 role 权限 ...

	PermMenuCreate = "menu:create"
	PermMenuRead   = "menu:read"
	// ... 其他 menu 权限 ...
)

// Audit Context Keys for enriching audit logs via context values set by handlers/services.
const (
	// AUDIT_ACTION_CTX_KEY is used to override the automatically determined action in the audit log.
	AUDIT_ACTION_CTX_KEY = "audit_action"
	// AUDIT_RESOURCE_TYPE_CTX_KEY is used to override the automatically determined resource type.
	AUDIT_RESOURCE_TYPE_CTX_KEY = "audit_resource_type"
	// AUDIT_RESOURCE_ID_CTX_KEY is used to override the automatically determined resource ID.
	AUDIT_RESOURCE_ID_CTX_KEY = "audit_resource_id"
	// AUDIT_DETAILS_CTX_KEY allows handlers to provide specific details for the audit log entry.
	AUDIT_DETAILS_CTX_KEY = "audit_details"
	// AUDIT_OLD_VALUE_CTX_KEY stores the JSON string of a resource's state before modification.
	AUDIT_OLD_VALUE_CTX_KEY = "audit_old_value"
	// AUDIT_NEW_VALUE_CTX_KEY stores the JSON string of a resource's state after modification.
	AUDIT_NEW_VALUE_CTX_KEY = "audit_new_value"
	// AUDIT_USER_ID_CTX_KEY allows explicit setting of the UserID for the audit log (e.g., during login for the user being logged in).
	AUDIT_USER_ID_CTX_KEY = "audit_user_id"
	// AUDIT_USERNAME_CTX_KEY allows explicit setting of the Username for the audit log.
	AUDIT_USERNAME_CTX_KEY = "audit_username"
	// AUDIT_EVENT_RECORDED_CTX_KEY is a flag to indicate if an audit event has already been recorded by a handler,
	// signaling the AuditMiddleware to skip its own recording for that request.
	AUDIT_EVENT_RECORDED_CTX_KEY = "audit_event_recorded"
)
