package entity

import (
	"fmt"
	"math"
	"time"

	"gorm.io/gorm"
)

// WmsCycleCountTaskStatus 盘点任务状态枚举
type WmsCycleCountTaskStatus string

const (
	CountTaskStatusPending           WmsCycleCountTaskStatus = "PENDING"            // 待盘点
	CountTaskStatusCounting          WmsCycleCountTaskStatus = "COUNTING"           // 盘点中
	CountTaskStatusCompleted         WmsCycleCountTaskStatus = "COMPLETED"          // 已完成
	CountTaskStatusVarianceConfirmed WmsCycleCountTaskStatus = "VARIANCE_CONFIRMED" // 差异已确认
)

// WmsCycleCountTask 对应数据库中的 wms_cycle_count_task 表
// 存储盘点任务信息
type WmsCycleCountTask struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	TaskNo             string                  `gorm:"column:task_no;size:50;not null;uniqueIndex:idx_task_no_account;comment:任务编号" json:"taskNo"`
	PlanID             uint                    `gorm:"column:plan_id;not null;comment:盘点计划ID" json:"planId"`
	LocationID         uint                    `gorm:"column:location_id;not null;comment:库位ID" json:"locationId"`
	ItemID             *uint                   `gorm:"column:item_id;comment:物料ID" json:"itemId"`
	BatchNo            *string                 `gorm:"column:batch_no;size:100;comment:批次号" json:"batchNo"`
	SystemQuantity     float64                 `gorm:"column:system_quantity;type:numeric(12,4);not null;comment:系统数量" json:"systemQuantity"`
	CountedQuantity    *float64                `gorm:"column:counted_quantity;type:numeric(12,4);comment:盘点数量" json:"countedQuantity"`
	VarianceQuantity   *float64                `gorm:"column:variance_quantity;type:numeric(12,4);comment:差异数量" json:"varianceQuantity"`
	VariancePercentage *float64                `gorm:"column:variance_percentage;type:numeric(8,4);comment:差异百分比" json:"variancePercentage"`
	UnitOfMeasure      string                  `gorm:"column:unit_of_measure;size:20;not null;comment:计量单位" json:"unitOfMeasure"`
	Status             WmsCycleCountTaskStatus `gorm:"column:status;type:varchar(20);default:'PENDING';comment:任务状态" json:"status"`
	CounterUserID      *uint                   `gorm:"column:counter_user_id;comment:盘点员ID" json:"counterUserId"`
	CountedAt          *time.Time              `gorm:"column:counted_at;comment:盘点时间" json:"countedAt"`
	VarianceReason     *string                 `gorm:"column:variance_reason;size:200;comment:差异原因" json:"varianceReason"`
	AdjustmentRequired bool                    `gorm:"column:adjustment_required;default:false;comment:是否需要调整" json:"adjustmentRequired"`
	AdjustmentID       *uint                   `gorm:"column:adjustment_id;comment:调整记录ID" json:"adjustmentId"`

	// 关联关系
	Plan        *WmsCycleCountPlan      `gorm:"foreignKey:PlanID;references:ID" json:"plan,omitempty"`
	Location    *WmsLocation            `gorm:"foreignKey:LocationID;references:ID" json:"location,omitempty"`
	Item        *MtlItem                `gorm:"foreignKey:ItemID;references:ID" json:"item,omitempty"`
	CounterUser *User                   `gorm:"foreignKey:CounterUserID;references:ID" json:"counterUser,omitempty"`
	Adjustment  *WmsInventoryAdjustment `gorm:"foreignKey:AdjustmentID;references:ID" json:"adjustment,omitempty"`
}

// TableName 指定数据库表名方法
func (WmsCycleCountTask) TableName() string {
	return "wms_cycle_count_task"
}

// BeforeCreate GORM钩子：创建前自动生成任务编号
func (w *WmsCycleCountTask) BeforeCreate(tx *gorm.DB) error {
	if w.TaskNo == "" {
		// 这里应该调用编码生成服务生成任务编号
		// 暂时使用简单的时间戳格式
		w.TaskNo = fmt.Sprintf("CT%s", time.Now().Format("20060102150405"))
	}
	return nil
}

// BeforeSave GORM钩子：保存前计算差异
func (w *WmsCycleCountTask) BeforeSave(tx *gorm.DB) error {
	if w.CountedQuantity != nil {
		// 计算差异数量
		variance := *w.CountedQuantity - w.SystemQuantity
		w.VarianceQuantity = &variance

		// 计算差异百分比
		if w.SystemQuantity != 0 {
			percentage := (variance / w.SystemQuantity) * 100
			w.VariancePercentage = &percentage
		} else if *w.CountedQuantity != 0 {
			// 系统数量为0但盘点数量不为0，差异百分比为100%
			percentage := 100.0
			w.VariancePercentage = &percentage
		}

		// 判断是否需要调整
		w.AdjustmentRequired = w.HasVariance()
	}
	return nil
}

// CanStart 判断是否可以开始盘点
func (w *WmsCycleCountTask) CanStart() bool {
	return w.Status == CountTaskStatusPending
}

// CanComplete 判断是否可以完成盘点
func (w *WmsCycleCountTask) CanComplete() bool {
	return w.Status == CountTaskStatusCounting && w.CountedQuantity != nil
}

// CanSubmitCount 判断是否可以提交盘点
func (w *WmsCycleCountTask) CanSubmitCount() bool {
	return w.Status == CountTaskStatusCounting
}

// CanConfirmVariance 判断是否可以确认差异
func (w *WmsCycleCountTask) CanConfirmVariance() bool {
	return w.Status == CountTaskStatusCompleted && w.HasVariance()
}

// CanRejectVariance 判断是否可以拒绝差异
func (w *WmsCycleCountTask) CanRejectVariance() bool {
	return w.Status == CountTaskStatusCompleted && w.HasVariance()
}

// Start 开始盘点
func (w *WmsCycleCountTask) Start(counterUserID uint) error {
	if !w.CanStart() {
		return fmt.Errorf("任务状态不允许开始盘点")
	}

	w.Status = CountTaskStatusCounting
	w.CounterUserID = &counterUserID

	return nil
}

// Complete 完成盘点
func (w *WmsCycleCountTask) Complete(countedQuantity float64) error {
	if !w.CanStart() && !w.CanComplete() {
		return fmt.Errorf("任务状态不允许完成盘点")
	}

	now := time.Now()
	w.CountedQuantity = &countedQuantity
	w.CountedAt = &now
	w.Status = CountTaskStatusCompleted

	return nil
}

// ConfirmVariance 确认差异
func (w *WmsCycleCountTask) ConfirmVariance(reason string) error {
	if !w.CanConfirmVariance() {
		return fmt.Errorf("任务状态不允许确认差异")
	}

	w.Status = CountTaskStatusVarianceConfirmed
	w.VarianceReason = &reason

	return nil
}

// HasVariance 判断是否有差异
func (w *WmsCycleCountTask) HasVariance() bool {
	if w.VarianceQuantity == nil {
		return false
	}
	return math.Abs(*w.VarianceQuantity) > 0.0001 // 考虑浮点数精度问题
}

// IsSignificantVariance 判断是否为重大差异
func (w *WmsCycleCountTask) IsSignificantVariance(threshold float64) bool {
	if w.VariancePercentage == nil {
		return false
	}
	return math.Abs(*w.VariancePercentage) > threshold
}

// GetStatusName 获取任务状态中文名称
func (w *WmsCycleCountTask) GetStatusName() string {
	switch w.Status {
	case CountTaskStatusPending:
		return "待盘点"
	case CountTaskStatusCounting:
		return "盘点中"
	case CountTaskStatusCompleted:
		return "已完成"
	case CountTaskStatusVarianceConfirmed:
		return "差异已确认"
	default:
		return "未知状态"
	}
}

// GetVarianceLevel 获取差异等级
func (w *WmsCycleCountTask) GetVarianceLevel() string {
	if !w.HasVariance() {
		return "无差异"
	}

	if w.VariancePercentage == nil {
		return "未知"
	}

	absPercentage := math.Abs(*w.VariancePercentage)

	switch {
	case absPercentage <= 1:
		return "轻微差异"
	case absPercentage <= 5:
		return "一般差异"
	case absPercentage <= 10:
		return "较大差异"
	default:
		return "重大差异"
	}
}

// GetVarianceDescription 获取差异描述
func (w *WmsCycleCountTask) GetVarianceDescription() string {
	if !w.HasVariance() {
		return "无差异"
	}

	if w.VarianceQuantity == nil {
		return "差异未计算"
	}

	if *w.VarianceQuantity > 0 {
		return fmt.Sprintf("盘盈 %.4f %s", *w.VarianceQuantity, w.UnitOfMeasure)
	} else {
		return fmt.Sprintf("盘亏 %.4f %s", math.Abs(*w.VarianceQuantity), w.UnitOfMeasure)
	}
}
