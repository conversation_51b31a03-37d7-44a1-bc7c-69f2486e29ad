<template>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      destroy-on-close
      @close="handleClose"
    >
      <div class="batch-operation-content">
        <!-- 操作类型说明 -->
        <div class="operation-info">
          <el-alert
            :title="operationDescription"
            :type="operationType === 'delete' ? 'error' : 'warning'"
            :closable="false"
            show-icon
          />
        </div>
  
        <!-- 选中项目列表 -->
        <div class="selected-items">
          <h4 class="items-title">
            已选择 {{ selectedItems.length }} 项配置
          </h4>
          
          <div class="items-list">
            <div
              v-for="item in selectedItems"
              :key="item.id"
              class="item-card"
            >
              <div class="item-header">
                <el-tag
                  :type="getConfigLevelTagType(item.configLevel)"
                  size="small"
                  effect="plain"
                >
                  {{ getConfigLevelName(item.configLevel) }}
                </el-tag>
                <el-tag
                  :type="getStrategyTagType(item.strategy)"
                  size="small"
                  effect="plain"
                >
                  {{ getStrategyName(item.strategy) }}
                </el-tag>
              </div>
              
              <div class="item-content">
                <div class="item-target">
                  <span class="label">配置目标:</span>
                  <span class="value">{{ item.configTargetName || '系统默认' }}</span>
                </div>
                
                <div class="item-status">
                  <span class="label">当前状态:</span>
                  <el-tag
                    :type="item.isActive ? 'success' : 'danger'"
                    size="small"
                    effect="plain"
                  >
                    {{ item.isActive ? '启用' : '禁用' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 批量切换状态选项 -->
        <div v-if="operationType === 'toggle'" class="toggle-options">
          <h4 class="options-title">请选择操作</h4>
          <el-radio-group v-model="toggleAction">
            <el-radio label="enable">批量启用</el-radio>
            <el-radio label="disable">批量禁用</el-radio>
          </el-radio-group>
        </div>
  
        <!-- 操作确认 -->
        <div class="operation-confirm">
          <el-checkbox v-model="confirmOperation">
            我确认要执行此批量操作
          </el-checkbox>
        </div>
  
        <!-- 操作结果 -->
        <div v-if="operationResult" class="operation-result">
          <h4 class="result-title">操作结果</h4>
          <el-result
            :icon="operationResult.successCount === selectedItems.length ? 'success' : 'warning'"
            :title="getResultTitle()"
            :sub-title="getResultSubTitle()"
          >
            <template #extra>
              <div class="result-details">
                <div class="result-stats">
                  <div class="stat-item success">
                    <span class="stat-label">成功:</span>
                    <span class="stat-value">{{ operationResult.successCount }}</span>
                  </div>
                  <div class="stat-item error" v-if="operationResult.failureCount > 0">
                    <span class="stat-label">失败:</span>
                    <span class="stat-value">{{ operationResult.failureCount }}</span>
                  </div>
                </div>
                
                <!-- 失败原因 -->
                <div
                  v-if="operationResult.failureReasons.length > 0"
                  class="failure-reasons"
                >
                  <h5>失败原因:</h5>
                  <ul>
                    <li
                      v-for="(reason, index) in operationResult.failureReasons"
                      :key="index"
                      class="failure-reason"
                    >
                      {{ reason }}
                    </li>
                  </ul>
                </div>
              </div>
            </template>
          </el-result>
        </div>
      </div>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">
            {{ operationResult ? '关闭' : '取消' }}
          </el-button>
          <el-button
            v-if="!operationResult"
            type="primary"
            :disabled="!confirmOperation || (operationType === 'toggle' && !toggleAction)"
            :loading="loading"
            @click="handleExecute"
          >
            {{ executeButtonText }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import {
    batchDeleteBlindReceivingConfig,
    batchToggleBlindReceivingConfig
  } from '@/api/wms/blindReceivingConfig'
  import type {
    BlindReceivingConfigVO,
    BatchOperationResult,
    ConfigLevel,
    BlindReceivingStrategy
  } from '@/types/wms/blindReceivingConfig'
  import {
    CONFIG_LEVEL_OPTIONS,
    STRATEGY_OPTIONS
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    modelValue: boolean
    operationType: 'delete' | 'toggle'
    selectedItems: BlindReceivingConfigVO[]
  }
  
  const props = withDefaults(defineProps<Props>(), {
    selectedItems: () => []
  })
  
  // Emits
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()
  
  // 响应式数据
  const loading = ref(false)
  const confirmOperation = ref(false)
  const toggleAction = ref<'enable' | 'disable'>('enable')
  const operationResult = ref<BatchOperationResult | null>(null)
  
  // 计算属性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })
  
  const dialogTitle = computed(() => {
    switch (props.operationType) {
      case 'delete':
        return '批量删除配置'
      case 'toggle':
        return '批量切换状态'
      default:
        return '批量操作'
    }
  })
  
  const operationDescription = computed(() => {
    switch (props.operationType) {
      case 'delete':
        return `您即将删除 ${props.selectedItems.length} 项配置，此操作不可撤销，请谨慎操作！`
      case 'toggle':
        return `您即将批量切换 ${props.selectedItems.length} 项配置的状态，请确认操作类型。`
      default:
        return ''
    }
  })
  
  const executeButtonText = computed(() => {
    switch (props.operationType) {
      case 'delete':
        return '确认删除'
      case 'toggle':
        return toggleAction.value === 'enable' ? '确认启用' : '确认禁用'
      default:
        return '确认执行'
    }
  })
  
  // 工具函数
  const getConfigLevelName = (level: ConfigLevel) => {
    return CONFIG_LEVEL_OPTIONS.find(item => item.value === level)?.label || level
  }
  
  const getConfigLevelTagType = (level: ConfigLevel) => {
    const typeMap = {
      'SYSTEM': 'info',
      'WAREHOUSE': 'warning',
      'CLIENT': 'success',
      'USER': 'primary'
    }
    return typeMap[level] || 'info'
  }
  
  const getStrategyName = (strategy: BlindReceivingStrategy) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: BlindReceivingStrategy) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getResultTitle = () => {
    if (!operationResult.value) return ''
    
    const { successCount, totalCount } = operationResult.value
    if (successCount === totalCount) {
      return '操作成功完成'
    } else if (successCount > 0) {
      return '操作部分成功'
    } else {
      return '操作失败'
    }
  }
  
  const getResultSubTitle = () => {
    if (!operationResult.value) return ''
    
    const { successCount, failureCount, totalCount } = operationResult.value
    return `总计 ${totalCount} 项，成功 ${successCount} 项，失败 ${failureCount} 项`
  }
  
  // 事件处理
  const handleExecute = async () => {
    try {
      loading.value = true
      
      const ids = props.selectedItems.map(item => item.id)
      let result: BatchOperationResult
      
      switch (props.operationType) {
        case 'delete':
          result = await batchDeleteBlindReceivingConfig(ids)
          break
        case 'toggle':
          const isActive = toggleAction.value === 'enable'
          result = await batchToggleBlindReceivingConfig(ids, isActive)
          break
        default:
          throw new Error('不支持的操作类型')
      }
      
      operationResult.value = result
      
      // 显示操作结果消息
      if (result.successCount === result.totalCount) {
        ElMessage.success('批量操作成功完成')
      } else if (result.successCount > 0) {
        ElMessage.warning('批量操作部分成功，请查看详细结果')
      } else {
        ElMessage.error('批量操作失败')
      }
      
      // 如果有成功的操作，触发刷新
      if (result.successCount > 0) {
        emit('success')
      }
      
    } catch (error) {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    } finally {
      loading.value = false
    }
  }
  
  const handleClose = () => {
    emit('update:modelValue', false)
  }
  
  const resetDialog = () => {
    confirmOperation.value = false
    toggleAction.value = 'enable'
    operationResult.value = null
  }
  
  // 监听器
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal) {
        resetDialog()
      }
    }
  )
  </script>
  
  <style scoped lang="scss">
  .batch-operation-content {
    .operation-info {
      margin-bottom: 20px;
    }
    
    .selected-items {
      margin-bottom: 20px;
      
      .items-title {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .items-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        padding: 8px;
        
        .item-card {
          background-color: #f8f9fa;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          padding: 12px;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .item-header {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
          }
          
          .item-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            
            .item-target,
            .item-status {
              display: flex;
              align-items: center;
              gap: 5px;
              
              .label {
                color: #909399;
                font-weight: 500;
              }
              
              .value {
                color: #303133;
              }
            }
          }
        }
      }
    }
    
    .toggle-options {
      margin-bottom: 20px;
      
      .options-title {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .operation-confirm {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      text-align: center;
    }
    
    .operation-result {
      margin-top: 20px;
      
      .result-title {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .result-details {
        .result-stats {
          display: flex;
          justify-content: center;
          gap: 20px;
          margin-bottom: 15px;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            
            .stat-label {
              font-weight: 500;
            }
            
            .stat-value {
              font-size: 18px;
              font-weight: 600;
            }
            
            &.success {
              color: #67c23a;
            }
            
            &.error {
              color: #f56c6c;
            }
          }
        }
        
        .failure-reasons {
          text-align: left;
          
          h5 {
            margin: 0 0 10px 0;
            color: #f56c6c;
            font-size: 14px;
            font-weight: 600;
          }
          
          ul {
            margin: 0;
            padding-left: 20px;
            
            .failure-reason {
              color: #f56c6c;
              font-size: 14px;
              margin-bottom: 5px;
            }
          }
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  
  // 滚动条样式
  .items-list::-webkit-scrollbar {
    width: 6px;
  }
  
  .items-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  .items-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  .items-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  </style>