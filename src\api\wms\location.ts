import request from '@/utils/request'

// --- WMS Location 相关类型定义 (与后端 Go 结构体对齐) ---

// 基础 VO
export interface BaseVO {
  id: number
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
}

// 库位视图对象 (对应 vo.WmsLocationVO)
export interface WmsLocationVO extends BaseVO {
  parentId?: number | null
  path?: string
  code: string
  name: string
  type: string
  status: string
  isPickable: boolean
  isPutaway: boolean
  isInventoryTracked: boolean
  remark?: string
}

// 库位树形视图对象
export interface WmsLocationTreeVO extends WmsLocationVO {
  children: WmsLocationTreeVO[]
  hasChildren?: boolean
}

// 库位创建DTO (对应 dto.WmsLocationCreateReq)
export interface WmsLocationCreateDTO {
  parentId?: number | null
  code: string
  name: string
  type: string
  status?: string
  isPickable?: boolean
  isPutaway?: boolean
  isInventoryTracked?: boolean
  remark?: string
}

// 库位更新DTO (对应 dto.WmsLocationUpdateReq)
export interface WmsLocationUpdateDTO {
  id: number // ID 在 Body 中
  parentId?: number | null
  code?: string
  name?: string
  type?: string
  status?: string
  isPickable?: boolean
  isPutaway?: boolean
  isInventoryTracked?: boolean
  remark?: string
}

// 库位列表查询参数 (对应 dto.WmsLocationQueryReq)
export interface WmsLocationQueryDTO {
  warehouseId?: number | null
  code?: string
  name?: string
  type?: string
  status?: string
  isPickable?: boolean
  isPutaway?: boolean
  // 分页和排序参数
  pageNum?: number
  pageSize?: number
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// --- API 函数定义 ---

const WMS_BASE_URL = '/wms/locations'

/**
 * 获取库位树
 * @param warehouseId 仓库ID (可选，不传时获取全部库位树)
 */
export const getLocationTree = (warehouseId?: number): Promise<WmsLocationTreeVO[]> => {
  return request({
    url: `${WMS_BASE_URL}/tree`,
    method: 'get',
    params: warehouseId ? { warehouseId } : undefined
  })
}

/**
 * 获取库位分页列表
 * @param params 查询参数 (WmsLocationQueryDTO)
 */
export const getLocationPage = (params: WmsLocationQueryDTO): Promise<PageResult<WmsLocationVO>> => {
  return request({
    url: `${WMS_BASE_URL}/page`,
    method: 'get',
    params
  })
}

/**
 * 获取所有仓库列表 (顶层库位)
 */
export const getWarehouseList = (): Promise<any[]> => {
  return request({
    url: `${WMS_BASE_URL}/list`, // 这是获取顶层仓库的接口
    method: 'get'
  })
}

/**
 * 根据 ID 获取库位详情
 * @param id 库位 ID
 */
export const getLocationByID = (id: number): Promise<WmsLocationVO> => {
  return request({
    url: `${WMS_BASE_URL}/${id}`,
    method: 'get'
  })
}

/**
 * 新增库位
 * @param data 库位数据 (WmsLocationCreateDTO)
 */
export const addLocation = (data: WmsLocationCreateDTO): Promise<WmsLocationVO> => {
  return request({
    url: WMS_BASE_URL,
    method: 'post',
    data
  })
}

/**
 * 更新库位
 * @param id 库位 ID
 * @param data 库位数据 (WmsLocationUpdateDTO)
 */
export const updateLocation = (id: number, data: WmsLocationUpdateDTO): Promise<WmsLocationVO> => {
  data.id = id;
  return request({
    url: `${WMS_BASE_URL}/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除库位
 * @param id 库位 ID
 */
export const deleteLocation = (id: number): Promise<null> => {
  return request({
    url: `${WMS_BASE_URL}/${id}`,
    method: 'delete'
  })
}

// 通用分页结果类型
export interface PageResult<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
} 