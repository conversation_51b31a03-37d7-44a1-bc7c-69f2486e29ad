package dto

import (
	"time"

	"backend/pkg/response"
)

// 0. 用户更新数据传输对象
// 用于接收 更新 用户的请求参数
type UserUpdateDTO struct {
	TenantDTO                       // 继承租户信息
	Username             string     `json:"username,omitempty" validate:"omitempty,min=3,max=50"`    // 用户名：用户登录系统的唯一标识，必填，长度3-50
	Nickname             *string    `json:"nickname,omitempty" validate:"max=50"`                    // 昵称：用户在系统中显示的名称，可选，最大长度50
	RealName             *string    `json:"realName,omitempty" validate:"max=50"`                    // 真实姓名：用户的实际姓名，可选，最大长度50
	Gender               *int       `json:"gender,omitempty" validate:"omitempty,oneof=0 1 2"`       // 性别：用户性别，可选，值必须是0, 1, 或 2 (使用 oneof 标签)
	Email                *string    `json:"email,omitempty" validate:"omitempty,max=100"`            // 电子邮箱：用户的邮箱地址，可选，必须是有效的邮箱格式，最大长度100
	Mobile               *string    `json:"mobile,omitempty" validate:"omitempty,max=20"`            // 手机号码：用户的手机号，可选，使用自定义 mobile 验证，最大长度20
	IsAdmin              *bool      `json:"isAdmin,omitempty" validate:"omitempty"`                  // 是否是管理员：标识用户是否具有管理员权限，可选，默认false
	Status               *int       `json:"status,omitempty" validate:"omitempty,oneof=0 1"`         // 状态：用户状态，可选，值必须是0或1 (使用 oneof 标签)
	Remark               *string    `json:"remark,omitempty" validate:"omitempty,max=255"`           // 备注：关于用户的附加说明信息，可选，最大长度255
	ExpireTime           *time.Time `json:"expireTime,omitempty"`                                    // 账号过期时间：用户账号的有效期截止时间，可选，omitempty表示JSON序列化时如果为空则忽略，使用指针类型 *time.Time
	EmployeeID           *uint      `json:"employeeId,omitempty" validate:"omitempty,gt=0"`          // 关联员工ID: 可选，如果提供必须大于0
	DefaultAccountBookID *uint      `json:"defaultAccountBookId,omitempty"`                          // 新增：默认账套ID
	RoleIDs              []uint     `json:"roleIds,omitempty" validate:"omitempty,dive,gt=0"`        // 角色ID列表：用户关联的角色ID集合，可选，用户关联的角色ID
	AccountBookIDs       []uint     `json:"accountBookIds,omitempty" validate:"omitempty,dive,gt=0"` // 账套ID列表：用户关联的账套ID集合，可选，如果提供，内部元素必须大于0
}

// 1. 用户创建数据传输对象
// 用于接收 创建 用户的请求参数
type UserCreateDTO struct {
	TenantDTO                       // 继承租户信息
	Username             string     `json:"username,omitempty" validate:"required,min=3,max=50"`     // 用户名：用户登录系统的唯一标识，必填，长度3-50
	Password             string     `json:"password,omitempty" validate:"required,min=6,max=100"`    // 密码：用户登录密码，必填，长度6-100
	Nickname             *string    `json:"nickname,omitempty" validate:"max=50"`                    // 昵称：用户在系统中显示的名称，可选，最大长度50
	RealName             *string    `json:"realName,omitempty" validate:"max=50"`                    // 真实姓名：用户的实际姓名，可选，最大长度50
	Avatar               *string    `json:"avatar,omitempty" validate:"omitempty,max=255"`           // 头像URL：用户头像的访问地址，可选，必须是有效的URL格式，最大长度255
	Gender               *int       `json:"gender,omitempty" validate:"omitempty,oneof=0 1 2"`       // 性别：用户性别，可选，值必须是0, 1, 或 2 (使用 oneof 标签)
	Email                *string    `json:"email,omitempty" validate:"omitempty,max=100"`            // 电子邮箱：用户的邮箱地址，可选，必须是有效的邮箱格式，最大长度100
	Mobile               *string    `json:"mobile,omitempty" validate:"omitempty,max=20"`            // 手机号码：用户的手机号，可选，使用自定义 mobile 验证，最大长度20
	IsAdmin              *bool      `json:"isAdmin,omitempty" validate:"omitempty"`                  // 是否是管理员：标识用户是否具有管理员权限，可选，默认false
	Status               *int       `json:"status,omitempty" validate:"omitempty,oneof=0 1"`         // 状态：用户状态，可选，值必须是0或1 (使用 oneof 标签)
	Remark               *string    `json:"remark,omitempty" validate:"omitempty,max=255"`           // 备注：关于用户的附加说明信息，可选，最大长度255
	ExpireTime           *time.Time `json:"expireTime,omitempty"`                                    // 账号过期时间：用户账号的有效期截止时间，可选，omitempty表示JSON序列化时如果为空则忽略，使用指针类型 *time.Time
	EmployeeID           *uint      `json:"employeeId,omitempty" validate:"omitempty,gt=0"`          // 关联员工ID: 可选，如果提供必须大于0
	DefaultAccountBookID *uint      `json:"defaultAccountBookId,omitempty"`                          // 新增：默认账套ID
	RoleIDs              []uint     `json:"roleIds,omitempty" validate:"omitempty,dive,gt=0"`        // 角色ID列表：用户关联的角色ID集合，可选，用户关联的角色ID
	AccountBookIDs       []uint     `json:"accountBookIds,omitempty" validate:"omitempty,dive,gt=0"` // 账套ID列表：用户关联的账套ID集合，可选，如果提供，内部元素必须大于0
}

// 2. 用户删除数据传输对象
// 用于接收用户删除请求参数
type UserDeleteDTO struct {
	Ids []uint `json:"ids" validate:"required,min=1,dive,gt=0"` // 用户ID列表：要删除的用户ID集合，必填，至少包含一个用户ID，且每个ID必须大于0
}

// 3. 用户分页查询对象
// 用于查询用户列表的条件参数
// 支持多条件组合查询和分页
type UserPageQueryDTO struct {
	Username  *string            `json:"username,omitempty" query:"username"` // 用户名：按用户名模糊查询，可选
	Nickname  *string            `json:"nickname,omitempty" query:"nickname"` // 昵称：按昵称模糊查询，可选
	RealName  *string            `json:"realName,omitempty" query:"realName"` // 真实姓名：按真实姓名模糊查询，可选
	Gender    *int               `json:"gender,omitempty" query:"gender"`     // 性别：按性别精确查询，可选，使用指针类型以区分零值和未设置
	Email     *string            `json:"email,omitempty" query:"email"`       // 电子邮箱：按邮箱模糊查询，可选
	Mobile    *string            `json:"mobile,omitempty" query:"mobile"`     // 手机号码：按手机号模糊查询，可选
	Status    *int               `json:"status,omitempty" query:"status"`     // 状态：按状态精确查询，可选，使用指针类型以区分零值和未设置
	IsAdmin   *bool              `json:"isAdmin,omitempty" query:"isAdmin"`   // 是否是管理员：按管理员标识精确查询，可选，使用指针类型以区分零值和未设置
	PageQuery response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
}

// 4. 用户密码修改对象
// 用于用户修改自己的密码
// 需要同时提供原密码和新密码
type UserUpdatePasswordDTO struct {
	OldPassword string `json:"oldPassword" validate:"required,min=6,max=32"` // 原密码：用户当前使用的密码，必填，长度6-32个字符
	NewPassword string `json:"newPassword" validate:"required,min=6,max=32"` // 新密码：用户要设置的新密码，必填，长度6-32个字符
}

// 5. 用户状态修改对象
// 用于管理员修改用户状态
type UserUpdateStatusDTO struct {
	Status int `json:"status" validate:"required,oneof=0 1"` // 状态：用户状态，必须是0(禁用)或1(正常)之一
}

// 6. 用户默认账套修改对象
// 用于 管理员/或用户自己 修改用户的默认账套
type UserUpdateDefaultAccountBookDTO struct {
	AccountBookID uint `json:"accountBookId"` // 默认账套ID (允许服务层访问)
}

// 7. 用户员工修改对象
// 用于理员修改用户的员工关联
type UserUpdateEmployeeDTO struct {
	EmployeeID uint `json:"employeeId"` // 员工ID (允许服务层访问)
}

// 8. 用户角色修改对象
// 用于管理员修改用户的角色关联
type UserRoleUpdateDTO struct {
	RoleIDs []uint `json:"roleIds" validate:"required,min=1,dive,gt=0"` // 角色ID列表：用户关联的角色ID集合，必填，至少包含一个角色ID，且每个ID必须大于0
}

// 9. 用户账套修改对象
// 用于管理员修改用户的账套关联
type UserAccountBookUpdateDTO struct {
	AccountBookIDs []uint `json:"accountBookIds" validate:"required,min=1,dive,gt=0"` // 账套ID列表：用户关联的账套ID集合，必填，至少包含一个账套ID，且每个ID必须大于0
}

// 10. 用户登录数据传输对象
// 用于接收用户登录请求参数
type UserLoginDTO struct {
	Username  string `json:"username" validate:"required"`               // 用户名：用户登录系统的唯一标识，必填
	Password  string `json:"password" validate:"required"`               // 密码：用户的登录密码，必填
	CaptchaID string `json:"captchaId" validate:"required_with=Captcha"` // 验证码ID：验证码的唯一标识，当提供验证码时必填
	Captcha   string `json:"captcha" validate:"required_with=CaptchaID"` // 验证码：用户输入的验证码，当提供验证码ID时必填
	Remember  bool   `json:"remember"`                                   // 记住我：是否记住登录状态，true-记住，false-不记住
}

// 11. 用户个人信息更新数据传输对象
// 用于接收用户更新个人信息的请求参数
type UserProfileUpdateDTO struct {
	Nickname             *string `json:"nickname,omitempty" validate:"max=50"`              // 昵称：用户在系统中显示的名称，可选，最大长度50
	RealName             *string `json:"realName,omitempty" validate:"max=50"`              // 真实姓名：用户的实际姓名，可选，最大长度50
	Avatar               *string `json:"avatar,omitempty" validate:"omitempty,max=255"`     // 头像URL：用户头像的访问地址，可选，最大长度255 (移除了 url 校验)
	Gender               *int    `json:"gender,omitempty" validate:"omitempty,oneof=0 1 2"` // 性别：用户性别，可选，值必须是0, 1, 或 2
	Email                *string `json:"email,omitempty" validate:"omitempty,max=100"`      // 电子邮箱：用户的邮箱地址，可选，必须是有效的邮箱格式，最大长度100
	Mobile               *string `json:"mobile,omitempty" validate:"omitempty,max=20"`      // 手机号码：用户的手机号，可选，自定义 mobile 验证，最大长度20
	DefaultAccountBookID *uint   `json:"defaultAccountBookId" validate:"omitempty,gt=0"`    // 新增：默认账套ID (使用指针允许清空, gt=0 确保是有效ID)
}

// 12. 踢出在线用户数据传输对象
// 用于接收踢出在线用户的请求参数
type UserKickOutDTO struct {
	SessionIDs []string `json:"sessionIds" validate:"required,min=1,dive,required"` // 会话ID列表：要踢出的用户会话ID集合，必填，至少包含一个会话ID，且每个元素不能为空
}

// 13. 管理员重置密码数据传输对象
// 用于管理员为指定用户设置新密码
type UserAdminResetPasswordDTO struct {
	NewPassword string `json:"newPassword" validate:"required,min=6,max=100"` // 新密码：必填，长度6-100
}

// 14. 管理员锁定/解锁用户数据传输对象
// 用于管理员锁定/解锁用户
type UserAdminLockDTO struct {
	UserID uint `json:"userId"` // 用户ID：要锁定/解锁的用户ID，必填
	Lock   bool `json:"lock"`   // 锁定：true-锁定，false-解锁
}

// AssignAccountBookToUserReq 定义将账套分配给用户的请求 DTO
// 用于封装将账套分配给用户操作所需的输入数据。
type AssignAccountBookToUserReq struct {
	// UserID 用户 ID
	// binding:"required" - 表示该字段在请求绑定时是必需的。
	UserID uint `json:"userId" validate:"required"`

	// AccountBookID 账套 ID
	// binding:"required" - 表示该字段在请求绑定时是必需的。
	AccountBookID uint `json:"accountBookId" validate:"required"`
}

// RemoveAccountBookFromUserReq 定义从用户移除账套的请求 DTO
// 用于封装从用户移除账套操作所需的输入数据。
type RemoveAccountBookFromUserReq struct {
	// UserID 用户 ID
	// binding:"required" - 表示该字段在请求绑定时是必需的。
	UserID uint `json:"userId" validate:"required"`

	// AccountBookID 账套 ID
	// binding:"required" - 表示该字段在请求绑定时是必需的。
	AccountBookID uint `json:"accountBookId" validate:"required"`
}
