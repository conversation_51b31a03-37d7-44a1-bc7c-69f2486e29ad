<template>
  <el-tabs
    :model-value="modelValue"
    @update:model-value="handleUpdateModelValue"
    :type="type"
    :tab-position="tabPosition"
    :closable="isClosable" 
    :addable="isAddable"
    :editable="isEditable"
    :stretch="stretch"
    :before-leave="beforeLeave"
    @tab-click="handleTabClick"
    @tab-change="handleTabChange"
    @edit="handleEdit"
    @tab-remove="handleTabRemove"
    v-bind="$attrs"
  >
    <el-tab-pane
      v-for="item in items"
      :key="item.name"
      :name="item.name"
      :label="item.label"
      :disabled="item.disabled"
      :lazy="item.lazy"
      :closable="item.closable"
    >
      <!-- Label slot for custom tab header -->
      <template #label v-if="$slots.label">
         <slot name="label" :item="item" :name="item.name"></slot>
      </template>
       <template #label v-else-if="item.icon">
         <span class="custom-tabs-label">
           <el-icon v-if="item.icon" style="vertical-align: middle; margin-right: 4px;">
             <component :is="item.icon" />
           </el-icon>
           <span>{{ item.label }}</span>
         </span>
       </template>

      <!-- Default slot for tab content -->
      <slot :name="item.name" :item="item">
        <!-- Default content if slot not provided -->
         <!-- <p>Content for {{ item.label }} (Name: {{ item.name }})</p> -->
      </slot>

    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import { ElTabs, ElTabPane, ElIcon } from 'element-plus';
import type { VNTabsEmits, TabItem } from './types';
import type { TabsPaneContext, TabsProps, TabPaneName } from 'element-plus';

// Explicitly define props using object syntax and basic types
const props = defineProps({
    modelValue: {
        type: [String, Number],
        required: true
    },
    items: {
        type: Array,
        default: () => [],
    },
    type: {
        type: String,
    },
    tabPosition: {
        type: String,
        default: 'top',
    },
    editable: {
        type: Boolean,
        default: false,
    },
    closable: {
         type: Boolean,
         default: false,
    },
    addable: {
         type: Boolean,
         default: false,
    },
    stretch: {
        type: Boolean,
        default: false,
    },
    beforeLeave: {
        type: Function,
    }
});

const emit = defineEmits<VNTabsEmits>();

// --- Computed properties for clarity ---
// ElTabs' closable/addable are determined by editable if set
const isClosable = computed(() => props.editable || props.closable);
const isAddable = computed(() => props.editable || props.addable);
const isEditable = computed(() => props.editable);

// --- Event Handlers (Forwarding) --- 
const handleUpdateModelValue = (name: string | number) => {
  emit('update:modelValue', name);
};

const handleTabClick = (pane: TabsPaneContext, event: Event) => {
  // Ensure paneName exists before emitting
  if (pane.paneName !== undefined) {
      emit('tab-click', pane as any, event); // Cast to any if needed for type compatibility
  }
};

const handleTabChange = (name: string | number) => {
  emit('tab-change', name);
};

const handleEdit = (paneName: string | number | undefined, action: 'remove' | 'add') => {
  emit('edit', paneName, action);
};

const handleTabRemove = (name: string | number) => {
  emit('tab-remove', name);
};

</script>

<style scoped>
.custom-tabs-label .el-icon {
  vertical-align: middle;
}
.custom-tabs-label span {
  vertical-align: middle;
  margin-left: 4px;
}
/* Add other styles if needed */
</style> 