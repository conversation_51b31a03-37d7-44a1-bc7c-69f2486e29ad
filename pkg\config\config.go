package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"

	"backend/pkg/logger"
)

var (
	// CONFIG 全局配置对象
	CONFIG *Configuration
)

// Configuration 应用配置结构体
type Configuration struct {
	App        AppConfig        `mapstructure:"app"`
	Database   DatabaseConfig   `mapstructure:"database"`
	Redis      RedisConfig      `mapstructure:"redis"`
	JWT        JWTConfig        `mapstructure:"jwt"`
	Security   SecurityConfig   `mapstructure:"security"`
	Storage    StorageConfig    `mapstructure:"storage"`
	Cache      CacheConfig      `mapstructure:"cache"`
	Monitoring MonitoringConfig `mapstructure:"monitoring"`
	Captcha    CaptchaConfig    `mapstructure:"captcha"`
}

// AppConfig 应用基本配置
type AppConfig struct {
	Name           string          `mapstructure:"name"`
	Version        string          `mapstructure:"version"`
	Mode           string          `mapstructure:"mode"`
	Port           int             `mapstructure:"port"`
	Debug          bool            `mapstructure:"debug"`
	Timeout        int             `mapstructure:"timeout"`
	AllowedOrigins []string        `mapstructure:"allowed_origins"`
	Log            LogConfig       `mapstructure:"log"`
	IrisLogLevel   string          `mapstructure:"iris_log_level"`
	LicensePath    string          `mapstructure:"licensePath" json:"licensePath" yaml:"licensePath"`
	RateLimit      RateLimitConfig `mapstructure:"rate_limit"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	Filepath   string `mapstructure:"filepath"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type       string           `mapstructure:"type"`
	AutoInit   bool             `mapstructure:"auto_init"`
	PostgreSQL PostgreSQLConfig `mapstructure:"postgresql"`
	MySQL      MySQLConfig      `mapstructure:"mysql"`
}

// PostgreSQLConfig PostgreSQL数据库配置
type PostgreSQLConfig struct {
	AutoInit        bool   `mapstructure:"auto_init"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	SSLMode         string `mapstructure:"ssl_mode"`
	TimeZone        string `mapstructure:"timezone"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
	LogLevel        int    `mapstructure:"log_level"`
	SlowThreshold   int    `mapstructure:"slow_threshold"`
}

// MySQLConfig MySQL数据库配置
type MySQLConfig struct {
	AutoInit        bool   `mapstructure:"auto_init"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	Charset         string `mapstructure:"charset"`
	ParseTime       bool   `mapstructure:"parse_time"`
	Loc             string `mapstructure:"loc"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
	LogLevel        int    `mapstructure:"log_level"`
	SlowThreshold   int    `mapstructure:"slow_threshold"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Password     string `mapstructure:"password"`
	DB           int    `mapstructure:"db"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
	DialTimeout  int    `mapstructure:"dial_timeout"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret        string `mapstructure:"secret"`
	Expire        int    `mapstructure:"expire"`
	RefreshExpire int    `mapstructure:"refresh_expire"`
	Issuer        string `mapstructure:"issuer"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	PasswordHashIterations int    `mapstructure:"password_hash_iterations"`
	LoginRetryLimit        int    `mapstructure:"login_retry_limit"`
	LoginRetryTimeout      int    `mapstructure:"login_retry_timeout"`
	EnableCaptcha          bool   `mapstructure:"enable_captcha"`
	CaptchaExpire          int    `mapstructure:"captcha_expire"`
	TokenRenewalStrategy   int    `mapstructure:"token_renewal_strategy"`
	PasswordHashAlgo       string `mapstructure:"password_hash_algo"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type  string      `mapstructure:"type"`
	Local LocalConfig `mapstructure:"local"`
	S3    S3Config    `mapstructure:"s3"`
	OSS   OSSConfig   `mapstructure:"oss"`
	// 新增字段以匹配 YAML
	AllowedTypes  []string `mapstructure:"allowedTypes"`
	MaxFileSizeMB int64    `mapstructure:"maxFileSizeMB"`
}

// LocalConfig 本地存储配置
type LocalConfig struct {
	Path    string `mapstructure:"path"`
	BaseURL string `mapstructure:"base_url"`
}

// S3Config AWS S3存储配置
type S3Config struct {
	Endpoint  string `mapstructure:"endpoint"`
	Region    string `mapstructure:"region"`
	Bucket    string `mapstructure:"bucket"`
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
	BaseURL   string `mapstructure:"base_url"`
}

// OSSConfig 阿里云OSS存储配置
type OSSConfig struct {
	Endpoint  string `mapstructure:"endpoint"`
	Bucket    string `mapstructure:"bucket"`
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
	BaseURL   string `mapstructure:"base_url"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Type              string `mapstructure:"type"`
	DefaultExpiration int    `mapstructure:"default_expiration"`
	CleanupInterval   int    `mapstructure:"cleanup_interval"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	EnableMetrics   bool         `mapstructure:"enable_metrics"`
	MetricsPath     string       `mapstructure:"metrics_path"`
	EnableTracing   bool         `mapstructure:"enable_tracing"`
	TracingExporter string       `mapstructure:"tracing_exporter"`
	Jaeger          JaegerConfig `mapstructure:"jaeger"`
	Zipkin          ZipkinConfig `mapstructure:"zipkin"`
}

// JaegerConfig Jaeger配置
type JaegerConfig struct {
	Endpoint    string `mapstructure:"endpoint"`
	ServiceName string `mapstructure:"service_name"`
}

// ZipkinConfig Zipkin配置
type ZipkinConfig struct {
	Endpoint    string `mapstructure:"endpoint"`
	ServiceName string `mapstructure:"service_name"`
}

// CaptchaConfig 验证码配置
type CaptchaConfig struct {
	Enable   bool    `mapstructure:"enable"`    // 是否启用验证码
	Expire   int     `mapstructure:"expire"`    // 过期时间 (单位: 分钟)
	Length   int     `mapstructure:"length"`    // 验证码字符长度
	Width    int     `mapstructure:"width"`     // 图片宽度
	Height   int     `mapstructure:"height"`    // 图片高度
	MaxSkew  float64 `mapstructure:"max_skew"`  // 最大倾斜度 (0.7)
	DotCount int     `mapstructure:"dot_count"` // 干扰点数量
	Store    string  `mapstructure:"store"`     // 存储方式 (redis or memory, 默认 memory)
	Prefix   string  `mapstructure:"prefix"`    // Redis 存储前缀 (默认 captcha:)
}

// RateLimitRule 自定义限流规则
type RateLimitRule struct {
	PathPattern    string   `mapstructure:"pathPattern"`    // 路径模式
	Methods        []string `mapstructure:"methods"`        // 方法
	Limit          int64    `mapstructure:"limit"`          // 限制次数 (cache_service)
	WindowDuration string   `mapstructure:"windowDuration"` // 时间窗口 (cache_service)
	Rate           float64  `mapstructure:"rate"`           // 速率 (每秒请求数) (memory/redis)
	Capacity       int      `mapstructure:"capacity"`       // 容量 (memory/redis)
}

// RateLimitConfig 限流中间件配置
type RateLimitConfig struct {
	Enabled       bool     `mapstructure:"enabled"`
	LimiterType   string   `mapstructure:"limiterType"` // memory, redis, cache_service
	ExcludedPaths []string `mapstructure:"excludedPaths"`
	// CacheService 配置
	GlobalLimit    int64  `mapstructure:"globalLimit"`
	IPLimit        int64  `mapstructure:"ipLimit"`
	UserLimit      int64  `mapstructure:"userLimit"`
	WindowDuration string `mapstructure:"windowDuration"` // e.g., "1m", "60s"
	// Memory/Redis 配置
	Algorithm      string  `mapstructure:"algorithm"`
	GlobalRate     float64 `mapstructure:"globalRate"`
	IPRate         float64 `mapstructure:"ipRate"`
	UserRate       float64 `mapstructure:"userRate"`
	BucketCapacity int     `mapstructure:"bucketCapacity"`
	WindowSize     int     `mapstructure:"windowSize"` // seconds
	// 自定义规则
	CustomRules []RateLimitRule `mapstructure:"customRules"`
	// RedisConfig (如果直接用 Redis 限流时需要，这里复用顶层的 RedisConfig)
	// RedisConfig    RedisConfig     `mapstructure:"redisConfig"` // 可选，如果需要独立的 Redis 配置
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Configuration, error) {
	v := viper.New()

	// 设置默认配置文件路径
	if configPath == "" {
		// 搜索配置文件的可能路径
		possiblePaths := []string{
			"./configs",
			"../configs",
			"../../configs",
			"./config",
			"../config",
			"../../config",
		}

		configFile := "config.yaml"
		for _, path := range possiblePaths {
			fullPath := filepath.Join(path, configFile)
			if _, err := os.Stat(fullPath); err == nil {
				configPath = fullPath
				break
			}
		}

		// 如果仍然找不到，则使用默认路径
		if configPath == "" {
			configPath = "./configs/config.yaml"
		}
	}

	// 设置配置文件类型
	v.SetConfigType(getConfigType(configPath))

	// 读取配置文件
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	configData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("无法读取配置文件: %v", err)
	}

	err = v.ReadConfig(strings.NewReader(string(configData)))
	if err != nil {
		return nil, fmt.Errorf("无法解析配置文件: %v", err)
	}

	// 监听配置文件变化
	v.WatchConfig()
	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Printf("配置文件已更改: %s\n", e.Name)
		// 重新加载配置
		if err := v.ReadInConfig(); err != nil {
			fmt.Printf("重新加载配置文件失败: %v\n", err)
		} else {
			var config Configuration
			if err := v.Unmarshal(&config); err != nil {
				fmt.Printf("重新解析配置文件失败: %v\n", err)
			} else {
				CONFIG = &config
			}
		}
	})

	// 解析配置到结构体
	var config Configuration
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("无法解析配置到结构体: %v", err)
	}

	// 设置全局配置变量
	CONFIG = &config

	return &config, nil
}

// getConfigType 根据文件名确定配置文件类型
func getConfigType(configPath string) string {
	ext := filepath.Ext(configPath)
	if ext == "" {
		return "yaml"
	}
	return ext[1:]
}

// GetPostgreSQLDSN 获取PostgreSQL连接字符串
func (c *Configuration) GetPostgreSQLDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		c.Database.PostgreSQL.Host,
		c.Database.PostgreSQL.Port,
		c.Database.PostgreSQL.Username,
		c.Database.PostgreSQL.Password,
		c.Database.PostgreSQL.Database,
		c.Database.PostgreSQL.SSLMode,
		c.Database.PostgreSQL.TimeZone,
	)
}

// GetMySQLDSN 获取MySQL连接字符串
func (c *Configuration) GetMySQLDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
		c.Database.MySQL.Username,
		c.Database.MySQL.Password,
		c.Database.MySQL.Host,
		c.Database.MySQL.Port,
		c.Database.MySQL.Database,
		c.Database.MySQL.Charset,
		c.Database.MySQL.ParseTime,
		c.Database.MySQL.Loc,
	)
}

// IsDevMode 是否为开发模式
func (c *Configuration) IsDevMode() bool {
	return c.App.Mode == "dev"
}

// IsTestMode 是否为测试模式
func (c *Configuration) IsTestMode() bool {
	return c.App.Mode == "test"
}

// IsProdMode 是否为生产模式
func (c *Configuration) IsProdMode() bool {
	return c.App.Mode == "prod"
}

// GetRedisAddr 获取Redis地址
func (c *Configuration) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Redis.Host, c.Redis.Port)
}

// GetConfig 获取全局配置
// 确保在使用前已调用 LoadConfig
func GetConfig() *Configuration {
	if CONFIG == nil {
		// 可以在这里添加恐慌或默认加载逻辑
		logger.Fatal("配置尚未加载，请先调用 LoadConfig")
		// 或者尝试加载默认路径
		// _, err := LoadConfig("configs/config.yaml")
		// if err != nil {
		// 	 logger.Fatalf("尝试加载默认配置失败: %v", err)
		// }
	}
	return CONFIG
}

// IsProductionMode 判断是否为生产模式
func IsProductionMode() bool {
	return GetConfig().App.Mode == "prod"
}

// IsDevelopmentMode 判断是否为开发模式
func IsDevelopmentMode() bool {
	return GetConfig().App.Mode == "dev"
}

// IsTestMode 判断是否为测试模式
func IsTestMode() bool {
	return GetConfig().App.Mode == "test"
}
