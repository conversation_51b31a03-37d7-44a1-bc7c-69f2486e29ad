package vo

import (
	"backend/internal/model/entity"
	"time"
)

// WmsInventoryAdjustmentVO 库存调整视图对象
type WmsInventoryAdjustmentVO struct {
	ID                 uint                                `json:"id"`
	AdjustmentNo       string                              `json:"adjustmentNo"`
	InventoryID        uint                                `json:"inventoryId"`
	AdjustmentType     entity.WmsInventoryAdjustmentType   `json:"adjustmentType"`
	AdjustmentTypeName string                              `json:"adjustmentTypeName"`
	QuantityBefore     float64                             `json:"quantityBefore"`
	QuantityAfter      float64                             `json:"quantityAfter"`
	QuantityChange     float64                             `json:"quantityChange"`
	StatusBefore       *string                             `json:"statusBefore"`
	StatusAfter        *string                             `json:"statusAfter"`
	ReasonCode         *string                             `json:"reasonCode"`
	ReasonDescription  *string                             `json:"reasonDescription"`
	ApprovalStatus     entity.WmsInventoryAdjustmentStatus `json:"approvalStatus"`
	ApprovalStatusName string                              `json:"approvalStatusName"`
	ApprovedBy         *uint                               `json:"approvedBy"`
	ApprovedByName     *string                             `json:"approvedByName"`
	ApprovedAt         *time.Time                          `json:"approvedAt"`
	OperatorID         uint                                `json:"operatorId"`
	OperatorName       string                              `json:"operatorName"`
	CreatedAt          time.Time                           `json:"createdAt"`
	UpdatedAt          time.Time                           `json:"updatedAt"`

	// 关联信息
	ItemSku       *string `json:"itemSku"`
	ItemName      *string `json:"itemName"`
	LocationCode  *string `json:"locationCode"`
	WarehouseName *string `json:"warehouseName"`
	BatchNo       *string `json:"batchNo"`
	UnitOfMeasure *string `json:"unitOfMeasure"`

	// 业务状态
	CanApprove         bool `json:"canApprove"`
	CanExecute         bool `json:"canExecute"`
	CanCancel          bool `json:"canCancel"`
	IsApprovalRequired bool `json:"isApprovalRequired"`
}

// WmsInventoryAdjustmentDetailVO 库存调整详情视图对象
type WmsInventoryAdjustmentDetailVO struct {
	WmsInventoryAdjustmentVO

	// 库存详细信息
	Inventory *WmsInventoryVO `json:"inventory"`

	// 操作历史
	OperationLogs []WmsInventoryAdjustmentLogVO `json:"operationLogs"`

	// 相关事务
	TransactionLogs []WmsInventoryTransactionLogVO `json:"transactionLogs"`
}

// WmsInventoryAdjustmentLogVO 库存调整操作日志视图对象
type WmsInventoryAdjustmentLogVO struct {
	ID           uint      `json:"id"`
	Operation    string    `json:"operation"`
	OperatorID   uint      `json:"operatorId"`
	OperatorName string    `json:"operatorName"`
	Description  string    `json:"description"`
	CreatedAt    time.Time `json:"createdAt"`
}

// WmsInventoryAdjustmentStatsVO 库存调整统计视图对象
type WmsInventoryAdjustmentStatsVO struct {
	// 基础统计
	TotalCount    int64 `json:"totalCount"`
	PendingCount  int64 `json:"pendingCount"`
	ApprovedCount int64 `json:"approvedCount"`
	ExecutedCount int64 `json:"executedCount"`
	RejectedCount int64 `json:"rejectedCount"`

	// 数量统计
	TotalIncreaseQty   float64 `json:"totalIncreaseQty"`
	TotalDecreaseQty   float64 `json:"totalDecreaseQty"`
	TotalAdjustmentQty float64 `json:"totalAdjustmentQty"`

	// 按类型统计
	IncreaseCount     int64 `json:"increaseCount"`
	DecreaseCount     int64 `json:"decreaseCount"`
	StatusChangeCount int64 `json:"statusChangeCount"`

	// 时间范围
	DateStart *time.Time `json:"dateStart"`
	DateEnd   *time.Time `json:"dateEnd"`

	// 趋势数据
	TrendData []WmsInventoryAdjustmentTrendVO `json:"trendData"`
}

// WmsInventoryAdjustmentTrendVO 库存调整趋势数据
type WmsInventoryAdjustmentTrendVO struct {
	Date         string  `json:"date"`
	Count        int64   `json:"count"`
	IncreaseQty  float64 `json:"increaseQty"`
	DecreaseQty  float64 `json:"decreaseQty"`
	NetChangeQty float64 `json:"netChangeQty"`
}

// WmsInventoryAdjustmentReasonVO 库存调整原因视图对象
type WmsInventoryAdjustmentReasonVO struct {
	Code        string                            `json:"code"`
	Name        string                            `json:"name"`
	Description string                            `json:"description"`
	Type        entity.WmsInventoryAdjustmentType `json:"type"`
	IsActive    bool                              `json:"isActive"`
	SortOrder   int                               `json:"sortOrder"`
}

// WmsInventoryAdjustmentSummaryVO 库存调整汇总视图对象
type WmsInventoryAdjustmentSummaryVO struct {
	ItemID        uint   `json:"itemId"`
	ItemSku       string `json:"itemSku"`
	ItemName      string `json:"itemName"`
	WarehouseID   uint   `json:"warehouseId"`
	WarehouseName string `json:"warehouseName"`

	// 调整统计
	AdjustmentCount  int64   `json:"adjustmentCount"`
	TotalIncreaseQty float64 `json:"totalIncreaseQty"`
	TotalDecreaseQty float64 `json:"totalDecreaseQty"`
	NetAdjustmentQty float64 `json:"netAdjustmentQty"`

	// 最近调整
	LastAdjustmentAt   *time.Time `json:"lastAdjustmentAt"`
	LastAdjustmentType *string    `json:"lastAdjustmentType"`

	// 当前库存
	CurrentQuantity   float64 `json:"currentQuantity"`
	AvailableQuantity float64 `json:"availableQuantity"`
}

// WmsInventoryAdjustmentPageVO 库存调整分页视图对象
type WmsInventoryAdjustmentPageVO struct {
	List  []WmsInventoryAdjustmentVO `json:"list"`
	Total int64                      `json:"total"`
}

// NewWmsInventoryAdjustmentVO 创建库存调整视图对象
func NewWmsInventoryAdjustmentVO(adjustment *entity.WmsInventoryAdjustment) *WmsInventoryAdjustmentVO {
	vo := &WmsInventoryAdjustmentVO{
		ID:                 adjustment.ID,
		AdjustmentNo:       adjustment.AdjustmentNo,
		InventoryID:        adjustment.InventoryID,
		AdjustmentType:     adjustment.AdjustmentType,
		AdjustmentTypeName: adjustment.GetAdjustmentTypeName(),
		QuantityBefore:     adjustment.QuantityBefore,
		QuantityAfter:      adjustment.QuantityAfter,
		QuantityChange:     adjustment.QuantityChange,
		StatusBefore:       adjustment.StatusBefore,
		StatusAfter:        adjustment.StatusAfter,
		ReasonCode:         adjustment.ReasonCode,
		ReasonDescription:  adjustment.ReasonDescription,
		ApprovalStatus:     adjustment.ApprovalStatus,
		ApprovalStatusName: adjustment.GetApprovalStatusName(),
		ApprovedBy:         adjustment.ApprovedBy,
		ApprovedAt:         adjustment.ApprovedAt,
		OperatorID:         adjustment.OperatorID,
		CreatedAt:          adjustment.CreatedAt,
		UpdatedAt:          adjustment.UpdatedAt,
		CanApprove:         adjustment.ApprovalStatus == entity.AdjustmentStatusPending,
		CanExecute:         adjustment.CanExecute(),
		CanCancel:          adjustment.ApprovalStatus == entity.AdjustmentStatusPending,
		IsApprovalRequired: adjustment.IsApprovalRequired(),
	}

	// 填充关联信息 - 由于移除了关联关系，这些信息需要在Service层单独查询并填充
	if adjustment.Inventory != nil {
		// if adjustment.Inventory.Item != nil {
		//     vo.ItemSku = &adjustment.Inventory.Item.Sku
		//     vo.ItemName = &adjustment.Inventory.Item.Name
		//     vo.UnitOfMeasure = &adjustment.Inventory.Item.BaseUnit
		// }
		// if adjustment.Inventory.Location != nil {
		//     vo.LocationCode = &adjustment.Inventory.Location.Code
		//     // 仓库信息需要通过 WarehouseID 单独查询
		//     // 这里暂时不处理，由调用方负责填充
		// }
		vo.BatchNo = adjustment.Inventory.BatchNo
	}

	// 填充操作员信息
	if adjustment.Operator != nil {
		vo.OperatorName = adjustment.Operator.Username
	}

	// 填充审批人信息
	if adjustment.Approver != nil {
		approverName := adjustment.Approver.Username
		vo.ApprovedByName = &approverName
	}

	return vo
}

// NewWmsInventoryAdjustmentDetailVO 创建库存调整详情视图对象
func NewWmsInventoryAdjustmentDetailVO(adjustment *entity.WmsInventoryAdjustment) *WmsInventoryAdjustmentDetailVO {
	vo := &WmsInventoryAdjustmentDetailVO{
		WmsInventoryAdjustmentVO: *NewWmsInventoryAdjustmentVO(adjustment),
	}

	// 填充库存详细信息
	if adjustment.Inventory != nil {
		vo.Inventory = NewWmsInventoryVO(adjustment.Inventory)
	}

	return vo
}

// GetAdjustmentDirection 获取调整方向描述
func (vo *WmsInventoryAdjustmentVO) GetAdjustmentDirection() string {
	if vo.QuantityChange > 0 {
		return "增加"
	} else if vo.QuantityChange < 0 {
		return "减少"
	}
	return "无变化"
}

// GetAdjustmentAmount 获取调整数量的绝对值
func (vo *WmsInventoryAdjustmentVO) GetAdjustmentAmount() float64 {
	if vo.QuantityChange < 0 {
		return -vo.QuantityChange
	}
	return vo.QuantityChange
}
