<template>
    <div class="blind-receiving-config-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <VNBreadcrumb :items="breadcrumbItems" />
        <div class="page-title">
          <h2>盲收策略配置</h2>
          <p class="page-description">管理多层级盲收策略配置，支持系统级、仓库级、客户级、用户级配置</p>
        </div>
      </div>
  
      <!-- 搜索区域 -->
      <el-card class="search-card" shadow="never">
        <VNSearchForm
          :fields="searchFields"
          v-model="searchModel"
          @search="handleSearch"
          @reset="handleReset"
          :loading="loading"
        />
      </el-card>
  
      <!-- 主要内容区域 -->
      <el-card class="main-card" shadow="never">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button
              type="primary"
              :icon="Plus"
              @click="handleCreate"
              v-if="hasPermission('wms:blind-config:create')"
            >
              新增配置
            </el-button>
            <el-button
              :icon="Upload"
              @click="handleImport"
              v-if="hasPermission('wms:blind-config:import')"
            >
              批量导入
            </el-button>
            <el-button
              :icon="Download"
              @click="handleExport"
              :loading="exportLoading"
              v-if="hasPermission('wms:blind-config:export')"
            >
              导出配置
            </el-button>
            <el-button
              :icon="DocumentCopy"
              @click="handleDownloadTemplate"
            >
              下载模板
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-button-group>
              <el-button
                :icon="Delete"
                :disabled="!hasSelection"
                @click="handleBatchDelete"
                v-if="hasPermission('wms:blind-config:delete')"
              >
                批量删除
              </el-button>
              <el-button
                :icon="Switch"
                :disabled="!hasSelection"
                @click="handleBatchToggle"
                v-if="hasPermission('wms:blind-config:update')"
              >
                批量启用/禁用
              </el-button>
            </el-button-group>
            <el-button :icon="Refresh" @click="handleRefresh" circle />
          </div>
        </div>
  
        <!-- 数据表格 -->
        <VNTable
          ref="tableRef"
          :data="tableData"
          :columns="tableColumns"
          :loading="loading"
          :pagination="pagination"
          selection
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <!-- 配置层级列 -->
          <template #configLevel="{ row }">
            <el-tag
              :type="getConfigLevelTagType(row.configLevel)"
              effect="plain"
              size="small"
            >
              {{ getConfigLevelName(row.configLevel) }}
            </el-tag>
          </template>
  
          <!-- 配置目标列 -->
          <template #configTarget="{ row }">
            <div class="config-target">
              <span class="target-name">{{ row.configTargetName || '-' }}</span>
              <span class="target-id" v-if="row.configTargetId">
                (ID: {{ row.configTargetId }})
              </span>
            </div>
          </template>
  
          <!-- 策略列 -->
          <template #strategy="{ row }">
            <el-tag
              :type="getStrategyTagType(row.strategy)"
              effect="plain"
              size="small"
            >
              {{ getStrategyName(row.strategy) }}
            </el-tag>
          </template>
  
          <!-- 审批要求列 -->
          <template #requiresApproval="{ row }">
            <el-tag
              :type="row.requiresApproval ? 'warning' : 'success'"
              effect="plain"
              size="small"
            >
              {{ row.requiresApproval ? '需要审批' : '无需审批' }}
            </el-tag>
          </template>
  
          <!-- 数量限制列 -->
          <template #maxQuantity="{ row }">
            <span v-if="row.maxBlindReceivingQty">
              {{ formatNumber(row.maxBlindReceivingQty) }}
            </span>
            <span v-else class="text-muted">无限制</span>
          </template>
  
          <!-- 补录时限列 -->
          <template #supplementTime="{ row }">
            <span v-if="row.supplementTimeLimit">
              {{ row.supplementTimeLimit }}小时
            </span>
            <span v-else class="text-muted">-</span>
          </template>
  
          <!-- 状态列 -->
          <template #status="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleToggleStatus(row)"
              :disabled="!hasPermission('wms:blind-config:update')"
              :loading="row.statusLoading"
            />
          </template>
  
          <!-- 优先级列 -->
          <template #priority="{ row }">
            <el-tag
              :type="getPriorityTagType(row.priority)"
              effect="plain"
              size="small"
            >
              P{{ row.priority }}
            </el-tag>
          </template>
  
          <!-- 使用统计列 -->
          <template #usageStats="{ row }">
            <div class="usage-stats">
              <span class="usage-count">{{ row.usageCount || 0 }}次</span>
              <span class="last-used text-muted" v-if="row.lastUsedAt">
                最近: {{ formatDate(row.lastUsedAt) }}
              </span>
            </div>
          </template>
  
          <!-- 操作列 -->
          <template #actions="{ row }">
            <el-button-group>
              <el-button
                type="primary"
                :icon="View"
                size="small"
                @click="handleView(row)"
                v-if="hasPermission('wms:blind-config:view')"
              >
                查看
              </el-button>
              <el-button
                type="success"
                :icon="Edit"
                size="small"
                @click="handleEdit(row)"
                v-if="hasPermission('wms:blind-config:update')"
              >
                编辑
              </el-button>
              <el-button
                type="info"
                :icon="CopyDocument"
                size="small"
                @click="handleCopy(row)"
                v-if="hasPermission('wms:blind-config:create')"
              >
                复制
              </el-button>
              <el-button
                type="warning"
                :icon="Setting"
                size="small"
                @click="handleTest(row)"
                v-if="hasPermission('wms:blind-config:test')"
              >
                测试
              </el-button>
              <el-button
                type="danger"
                :icon="Delete"
                size="small"
                @click="handleDelete(row)"
                v-if="hasPermission('wms:blind-config:delete')"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </VNTable>
      </el-card>
  
      <!-- 配置表单对话框 -->
      <ConfigForm
        v-model="showConfigForm"
        :config-data="currentConfigData"
        :is-edit="isEditMode"
        @success="handleFormSuccess"
      />
  
      <!-- 批量操作对话框 -->
      <BatchOperationDialog
        v-model="showBatchDialog"
        :operation-type="batchOperationType"
        :selected-items="selectedItems"
        @success="handleBatchSuccess"
      />
  
      <!-- 配置测试对话框 -->
      <ConfigTestDialog
        v-model="showTestDialog"
        :config-data="currentConfigData"
      />
  
      <!-- 导入对话框 -->
      <ImportDialog
        v-model="showImportDialog"
        :import-url="importUrl"
        :template-url="templateUrl"
        @success="handleImportSuccess"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Plus,
    Upload,
    Download,
    DocumentCopy,
    Delete,
    Switch,
    Refresh,
    View,
    Edit,
    CopyDocument,
    Setting
  } from '@element-plus/icons-vue'
  import VNBreadcrumb from '@/components/VNBreadcrumb/index.vue'
  import VNSearchForm from '@/components/VNSearchForm/index.vue'
  import VNTable from '@/components/VNTable/index.vue'
  import ConfigForm from './components/ConfigForm.vue'
  import BatchOperationDialog from './components/BatchOperationDialog.vue'
  import ConfigTestDialog from './components/ConfigTestDialog.vue'
  import ImportDialog from './components/ImportDialog.vue'
  import { usePermission } from '@/hooks/usePermission'
  import { useDictionary } from '@/hooks/useDictionary'
  import {
    getBlindReceivingConfigPage,
    deleteBlindReceivingConfig,
    batchDeleteBlindReceivingConfig,
    batchToggleBlindReceivingConfig,
    exportBlindReceivingConfigs,
    downloadBlindReceivingConfigTemplate
  } from '@/api/wms/blindReceivingConfig'
  import type {
    BlindReceivingConfigVO,
    BlindReceivingConfigQueryReq,
    BlindReceivingConfigSearchModel,
    ConfigLevel,
    BlindReceivingStrategy
  } from '@/types/wms/blindReceivingConfig'
  import {
    CONFIG_LEVEL_OPTIONS,
    STRATEGY_OPTIONS
  } from '@/types/wms/blindReceivingConfig'
  
  // 权限和字典
  const { hasPermission } = usePermission()
  const { getDictOptions } = useDictionary()
  
  // 页面数据
  const loading = ref(false)
  const exportLoading = ref(false)
  const tableData = ref<BlindReceivingConfigVO[]>([])
  const selectedItems = ref<BlindReceivingConfigVO[]>([])
  
  // 搜索相关
  const searchModel = reactive<BlindReceivingConfigSearchModel>({
    configLevel: undefined,
    strategy: undefined,
    requiresApproval: undefined,
    isActive: undefined,
    keyword: ''
  })
  
  // 分页相关
  const pagination = reactive({
    pageNum: 1,
    pageSize: 20,
    total: 0
  })
  
  // 表单相关
  const showConfigForm = ref(false)
  const currentConfigData = ref<BlindReceivingConfigVO | null>(null)
  const isEditMode = ref(false)
  
  // 批量操作相关
  const showBatchDialog = ref(false)
  const batchOperationType = ref<'delete' | 'toggle'>('delete')
  
  // 其他对话框
  const showTestDialog = ref(false)
  const showImportDialog = ref(false)
  
  // 面包屑
  const breadcrumbItems = [
    { label: '首页', to: '/' },
    { label: 'WMS管理', to: '/wms' },
    { label: '盲收策略配置', to: '/wms/blind-receiving-config' }
  ]
  
  // 搜索字段配置
  const searchFields = computed(() => [
    {
      field: 'configLevel',
      label: '配置层级',
      type: 'select' as const,
      options: CONFIG_LEVEL_OPTIONS.map(item => ({
        label: item.label,
        value: item.value
      })),
      placeholder: '请选择配置层级'
    },
    {
      field: 'strategy',
      label: '盲收策略',
      type: 'select' as const,
      options: STRATEGY_OPTIONS.map(item => ({
        label: item.label,
        value: item.value
      })),
      placeholder: '请选择盲收策略'
    },
    {
      field: 'requiresApproval',
      label: '审批要求',
      type: 'select' as const,
      options: [
        { label: '需要审批', value: true },
        { label: '无需审批', value: false }
      ],
      placeholder: '请选择审批要求'
    },
    {
      field: 'isActive',
      label: '状态',
      type: 'select' as const,
      options: [
        { label: '启用', value: true },
        { label: '禁用', value: false }
      ],
      placeholder: '请选择状态'
    },
    {
      field: 'keyword',
      label: '关键词',
      type: 'input' as const,
      placeholder: '请输入配置目标名称或备注'
    }
  ])
  
  // 表格列配置
  const tableColumns = computed(() => [
    {
      prop: 'configLevel',
      label: '配置层级',
      width: 100,
      sortable: true,
      slot: 'configLevel'
    },
    {
      prop: 'configTarget',
      label: '配置目标',
      width: 180,
      slot: 'configTarget'
    },
    {
      prop: 'strategy',
      label: '盲收策略',
      width: 120,
      slot: 'strategy'
    },
    {
      prop: 'requiresApproval',
      label: '审批要求',
      width: 100,
      slot: 'requiresApproval'
    },
    {
      prop: 'maxQuantity',
      label: '数量限制',
      width: 120,
      slot: 'maxQuantity'
    },
    {
      prop: 'supplementTime',
      label: '补录时限',
      width: 100,
      slot: 'supplementTime'
    },
    {
      prop: 'status',
      label: '状态',
      width: 80,
      slot: 'status'
    },
    {
      prop: 'priority',
      label: '优先级',
      width: 80,
      sortable: true,
      slot: 'priority'
    },
    {
      prop: 'usageStats',
      label: '使用统计',
      width: 120,
      slot: 'usageStats'
    },
    {
      prop: 'updatedAt',
      label: '更新时间',
      width: 150,
      sortable: true,
      formatter: (row: BlindReceivingConfigVO) => formatDate(row.updatedAt)
    },
    {
      prop: 'actions',
      label: '操作',
      width: 350,
      fixed: 'right',
      slot: 'actions'
    }
  ])
  
  // 计算属性
  const hasSelection = computed(() => selectedItems.value.length > 0)
  const importUrl = computed(() => '/api/v1/wms/blind-receiving-configs/import')
  const templateUrl = computed(() => '/api/v1/wms/blind-receiving-configs/template')
  
  // 工具函数
  const getConfigLevelName = (level: ConfigLevel) => {
    return CONFIG_LEVEL_OPTIONS.find(item => item.value === level)?.label || level
  }
  
  const getConfigLevelTagType = (level: ConfigLevel) => {
    const typeMap = {
      'SYSTEM': 'info',
      'WAREHOUSE': 'warning',
      'CLIENT': 'success',
      'USER': 'primary'
    }
    return typeMap[level] || 'info'
  }
  
  const getStrategyName = (strategy: BlindReceivingStrategy) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: BlindReceivingStrategy) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getPriorityTagType = (priority: number) => {
    if (priority <= 2) return 'danger'
    if (priority <= 4) return 'warning'
    return 'success'
  }
  
  const formatNumber = (num: number) => {
    return num.toLocaleString()
  }
  
  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleString()
  }
  
  // 数据加载
  const loadData = async () => {
    try {
      loading.value = true
      const params: BlindReceivingConfigQueryReq = {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        ...searchModel,
        includeTargetInfo: true
      }
      
      const result = await getBlindReceivingConfigPage(params)
      tableData.value = result.list
      pagination.total = result.total
    } catch (error) {
      console.error('加载数据失败:', error)
      ElMessage.error('加载数据失败')
    } finally {
      loading.value = false
    }
  }
  
  // 事件处理
  const handleSearch = () => {
    pagination.pageNum = 1
    loadData()
  }
  
  const handleReset = () => {
    Object.keys(searchModel).forEach(key => {
      searchModel[key as keyof typeof searchModel] = undefined
    })
    handleSearch()
  }
  
  const handleRefresh = () => {
    loadData()
  }
  
  const handleSelectionChange = (selection: BlindReceivingConfigVO[]) => {
    selectedItems.value = selection
  }
  
  const handleSortChange = (sort: { prop: string; order: string }) => {
    // 处理排序
    loadData()
  }
  
  const handlePageChange = (page: number) => {
    pagination.pageNum = page
    loadData()
  }
  
  const handlePageSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    loadData()
  }
  
  const handleCreate = () => {
    currentConfigData.value = null
    isEditMode.value = false
    showConfigForm.value = true
  }
  
  const handleView = (row: BlindReceivingConfigVO) => {
    currentConfigData.value = row
    isEditMode.value = false
    showConfigForm.value = true
  }
  
  const handleEdit = (row: BlindReceivingConfigVO) => {
    currentConfigData.value = row
    isEditMode.value = true
    showConfigForm.value = true
  }
  
  const handleCopy = (row: BlindReceivingConfigVO) => {
    const copyData = { ...row }
    delete copyData.id
    delete copyData.createdAt
    delete copyData.updatedAt
    currentConfigData.value = copyData
    isEditMode.value = false
    showConfigForm.value = true
  }
  
  const handleTest = (row: BlindReceivingConfigVO) => {
    currentConfigData.value = row
    showTestDialog.value = true
  }
  
  const handleDelete = async (row: BlindReceivingConfigVO) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除配置"${getConfigLevelName(row.configLevel)} - ${row.configTargetName || '默认'}"吗？`,
        '确认删除',
        {
          type: 'warning'
        }
      )
      
      await deleteBlindReceivingConfig(row.id)
      ElMessage.success('删除成功')
      loadData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }
  
  const handleToggleStatus = async (row: BlindReceivingConfigVO) => {
    try {
      row.statusLoading = true
      await batchToggleBlindReceivingConfig([row.id], row.isActive)
      ElMessage.success(row.isActive ? '启用成功' : '禁用成功')
    } catch (error) {
      console.error('状态切换失败:', error)
      ElMessage.error('状态切换失败')
      row.isActive = !row.isActive // 回滚状态
    } finally {
      row.statusLoading = false
    }
  }
  
  const handleBatchDelete = () => {
    batchOperationType.value = 'delete'
    showBatchDialog.value = true
  }
  
  const handleBatchToggle = () => {
    batchOperationType.value = 'toggle'
    showBatchDialog.value = true
  }
  
  const handleImport = () => {
    showImportDialog.value = true
  }
  
  const handleExport = async () => {
    try {
      exportLoading.value = true
      const params: BlindReceivingConfigQueryReq = {
        ...searchModel,
        includeTargetInfo: true
      }
      
      const blob = await exportBlindReceivingConfigs(params)
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `盲收配置_${new Date().toISOString().slice(0, 10)}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    } finally {
      exportLoading.value = false
    }
  }
  
  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadBlindReceivingConfigTemplate()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '盲收配置导入模板.xlsx'
      link.click()
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('模板下载成功')
    } catch (error) {
      console.error('模板下载失败:', error)
      ElMessage.error('模板下载失败')
    }
  }
  
  const handleFormSuccess = () => {
    showConfigForm.value = false
    loadData()
  }
  
  const handleBatchSuccess = () => {
    showBatchDialog.value = false
    selectedItems.value = []
    loadData()
  }
  
  const handleImportSuccess = () => {
    showImportDialog.value = false
    loadData()
  }
  
  // 初始化
  onMounted(() => {
    loadData()
  })
  </script>
  
  <style scoped lang="scss">
  .blind-receiving-config-container {
    padding: 20px;
    background-color: #f5f5f5;
  }
  
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      margin-top: 10px;
      
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }
      
      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
  
  .main-card {
    :deep(.el-card__body) {
      padding: 0;
    }
  }
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    
    .toolbar-left {
      display: flex;
      gap: 10px;
    }
    
    .toolbar-right {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
  
  .config-target {
    .target-name {
      font-weight: 500;
    }
    
    .target-id {
      color: #909399;
      font-size: 12px;
      margin-left: 5px;
    }
  }
  
  .usage-stats {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .usage-count {
      font-weight: 500;
      color: #409eff;
    }
    
    .last-used {
      font-size: 12px;
    }
  }
  
  .text-muted {
    color: #909399;
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .blind-receiving-config-container {
      padding: 10px;
    }
    
    .toolbar {
      flex-direction: column;
      gap: 10px;
      align-items: stretch;
      
      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }
    }
  }