import request from '@/utils/request';

// 物料管理API类型定义
export interface MtlItemVO {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  tenantId: number;
  accountBookId: number;
  sku: string;
  name: string;
  description?: string;
  specification?: string;
  categoryCode?: string;
  groupCode?: string;
  baseUnit: string;
  shelfLifeDays?: number;
  batchManaged: boolean;
  serialManaged: boolean;
  storageCondition?: string;
  imageUrl?: string;
  weightKg?: number;
  volumeM3?: number;
  lengthM?: number;
  widthM?: number;
  heightM?: number;
  status: string;
  remark?: string;
  defaultCustomerId?: number;
  defaultSupplierId?: number;
  defaultLocationId?: number;
  packageUnits?: MtlItemPackageUnitVO[];
}

export interface MtlItemPackageUnitVO {
  id: number;
  unitName: string;
  conversionFactor: number;
  packageWeightKg?: number;
  packageVolumeM3?: number;
  packageLengthM?: number;
  packageWidthM?: number;
  packageHeightM?: number;
}

export interface MtlItemCreateDTO {
  sku: string;
  name: string;
  description?: string;
  specification?: string;
  categoryCode?: string;
  groupCode?: string;
  baseUnit: string;
  shelfLifeDays?: number;
  batchManaged?: boolean;
  serialManaged?: boolean;
  storageCondition?: string;
  imageUrl?: string;
  weightKg?: number;
  volumeM3?: number;
  lengthM?: number;
  widthM?: number;
  heightM?: number;
  status?: string;
  remark?: string;
  defaultCustomerId?: number;
  defaultSupplierId?: number;
  defaultLocationId?: number;
  packageUnits?: MtlItemPackageUnitDTO[];
}

export interface MtlItemUpdateDTO {
  id: number;
  sku?: string;
  name?: string;
  description?: string;
  specification?: string;
  categoryCode?: string;
  groupCode?: string;
  shelfLifeDays?: number;
  batchManaged?: boolean;
  serialManaged?: boolean;
  storageCondition?: string;
  imageUrl?: string;
  weightKg?: number;
  volumeM3?: number;
  lengthM?: number;
  widthM?: number;
  heightM?: number;
  status?: string;
  remark?: string;
  defaultCustomerId?: number;
  defaultSupplierId?: number;
  defaultLocationId?: number;
}

export interface MtlItemQueryDTO {
  pageNum: number;
  pageSize: number;
  sort?: string;
  sku?: string;
  name?: string;
  categoryCode?: string;
  groupCode?: string;
  status?: string;
  batchManaged?: boolean;
  serialManaged?: boolean;
  defaultSupplierId?: number;
  defaultLocationId?: number;
}

export interface MtlItemPackageUnitDTO {
  unitName: string;
  conversionFactor: number;
  packageWeightKg?: number;
  packageVolumeM3?: number;
  packageLengthM?: number;
  packageWidthM?: number;
  packageHeightM?: number;
}

export interface MtlItemPackageUnitCreateDTO {
  unitName: string;
  conversionFactor: number;
  packageWeightKg?: number;
  packageVolumeM3?: number;
  packageLengthM?: number;
  packageWidthM?: number;
  packageHeightM?: number;
}

export interface MtlItemPackageUnitUpdateDTO {
  unitName?: string;
  conversionFactor?: number;
  packageWeightKg?: number;
  packageVolumeM3?: number;
  packageLengthM?: number;
  packageWidthM?: number;
  packageHeightM?: number;
}

export interface PageResult<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

// 物料管理API函数
export const getMtlItemPage = (params: MtlItemQueryDTO): Promise<PageResult<MtlItemVO>> => {
  return request({
    url: '/wms/items',
    method: 'get',
    params
  });
};

export const getMtlItemById = (id: number): Promise<MtlItemVO> => {
  return request({
    url: `/wms/items/${id}`,
    method: 'get'
  });
};

export const createMtlItem = (data: MtlItemCreateDTO): Promise<MtlItemVO> => {
  return request({
    url: '/wms/items',
    method: 'post',
    data
  });
};

export const updateMtlItem = (id: number, data: MtlItemUpdateDTO): Promise<MtlItemVO> => {
  return request({
    url: `/wms/items/${id}`,
    method: 'put',
    data
  });
};

export const deleteMtlItem = (id: number): Promise<void> => {
  return request({
    url: `/wms/items/${id}`,
    method: 'delete'
  });
};

// 包装单位管理API函数
export const getMtlItemPackageUnits = (itemId: number): Promise<MtlItemPackageUnitVO[]> => {
  return request({
    url: `/wms/items/${itemId}/package-units`,
    method: 'get'
  });
};

export const createMtlItemPackageUnit = (itemId: number, data: MtlItemPackageUnitCreateDTO): Promise<MtlItemPackageUnitVO> => {
  return request({
    url: `/wms/items/${itemId}/package-units`,
    method: 'post',
    data
  });
};

export const updateMtlItemPackageUnit = (itemId: number, unitId: number, data: MtlItemPackageUnitUpdateDTO): Promise<MtlItemPackageUnitVO> => {
  return request({
    url: `/wms/items/${itemId}/package-units/${unitId}`,
    method: 'put',
    data
  });
};

export const deleteMtlItemPackageUnit = (itemId: number, unitId: number): Promise<void> => {
  return request({
    url: `/wms/items/${itemId}/package-units/${unitId}`,
    method: 'delete'
  });
}; 