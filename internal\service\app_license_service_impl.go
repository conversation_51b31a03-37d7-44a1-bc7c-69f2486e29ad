package service

import (
	"context"
	"fmt"
	"os"

	// "backend/internal/middleware" // Remove middleware import
	"backend/pkg/config"
	apperrors "backend/pkg/errors"
	"backend/pkg/license" // Import our license package
)

// LicenseService 定义授权服务接口
type LicenseService interface {
	BaseService // 嵌入基础服务接口
	UpdateLicenseFromUpload(ctx context.Context, contentBytes []byte) (*license.LicenseInfo, error)
	GetLicenseDetails(ctx context.Context) (*license.LicenseInfo, error) // 新增获取授权详情接口
}

// licenseServiceImpl 授权服务实现
type licenseServiceImpl struct {
	BaseServiceImpl // 嵌入基础服务实现
	appCfg          *config.Configuration
}

// NewLicenseService 创建 LicenseService
func NewLicenseService(sm *ServiceManager) LicenseService {
	return &licenseServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
		appCfg:          sm.appCfg, // 从 ServiceManager 获取配置
	}
}

// UpdateLicenseFromUpload 处理上传的授权文件内容，验证、保存并更新运行时状态
func (s *licenseServiceImpl) UpdateLicenseFromUpload(ctx context.Context, contentBytes []byte) (*license.LicenseInfo, error) {
	logger := s.GetLogger()
	logger.Info(ctx, "开始处理上传的授权文件")

	// 1. 加载嵌入的公钥 (从 pkg/license 获取)
	publicKey, err := license.ParsePublicKeyFromBytes([]byte(license.GetEmbeddedPublicKeyPEM())) // <<< Changed to license.GetEmbeddedPublicKeyPEM()
	if err != nil {
		logger.Error(ctx, "无法解析嵌入的公钥", logger.WithError(err))
		internalErr := apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "无法解析内部公钥").WithCause(err)
		license.UpdateRuntimeLicense(nil, internalErr) // 调用 pkg/license 中的函数
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "服务器内部错误，无法验证授权")
	}

	// 2. 验证上传内容
	verifiedInfo, err := license.VerifyLicenseContent(contentBytes, publicKey)
	if err != nil {
		logger.Warnf("上传的授权文件验证失败: %v", err)
		invalidErr := apperrors.NewBusinessError(apperrors.CODE_LICENSE_INVALID, "授权文件无效或签名错误").WithCause(err)
		license.UpdateRuntimeLicense(nil, invalidErr) // 调用 pkg/license 中的函数
		return nil, invalidErr
	}

	// 3. 检查是否已过期
	if verifiedInfo.IsExpired() {
		expiredMsg := fmt.Sprintf("上传的授权文件已于 %s 过期", verifiedInfo.ExpiryDate.Format("2006-01-02"))
		logger.Warn(ctx, expiredMsg)
		expiredErr := apperrors.NewBusinessError(apperrors.CODE_LICENSE_EXPIRED, expiredMsg)
		license.UpdateRuntimeLicense(nil, expiredErr) // 调用 pkg/license 中的函数
		return nil, expiredErr
	}

	// 4. 获取授权文件路径
	licensePath := s.appCfg.App.LicensePath
	if licensePath == "" {
		logger.Error(ctx, "授权文件路径未在配置中设置 (app.licensePath)")
		configErr := apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "服务器配置错误，无法保存授权文件")
		license.UpdateRuntimeLicense(nil, configErr) // 调用 pkg/license 中的函数
		return nil, configErr
	}

	// 5. 持久化保存 (覆盖旧文件)
	logger.Infof("验证通过，正在将新授权写入文件: %s", licensePath)
	err = os.WriteFile(licensePath, contentBytes, 0644)
	if err != nil {
		logger.Errorf("写入授权文件失败: %v", err)
		writeErr := apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "无法保存新的授权文件").WithCause(err)
		license.UpdateRuntimeLicense(nil, writeErr) // 调用 pkg/license 中的函数
		return nil, writeErr
	}

	// 6. 动态更新运行时状态 (成功)
	logger.Info(ctx, "新授权文件保存成功，正在更新运行时状态")
	license.UpdateRuntimeLicense(verifiedInfo, nil) // 调用 pkg/license 中的函数

	logger.Infof("授权文件更新成功。客户: %s, 到期日: %s", verifiedInfo.CustomerName, verifiedInfo.ExpiryDate.Format("2006-01-02"))
	return verifiedInfo, nil
}

// GetLicenseDetails 获取当前运行时的授权信息和错误状态
func (s *licenseServiceImpl) GetLicenseDetails(ctx context.Context) (*license.LicenseInfo, error) {
	logger := s.GetLogger()
	logger.Debug(ctx, "正在获取运行时授权详情")

	currentInfo, err := license.GetRuntimeLicenseState()

	if err != nil {
		logger.Warnf("获取运行时授权状态时发生错误: %v", err)
		// 检查错误是否已经是 CustomError 类型
		if customErr, ok := err.(*apperrors.CustomError); ok {
			return nil, customErr // 直接返回已是 CustomError 的错误
		}
		// 对于 pkg/license 内部可能产生的标准 error，包装成业务错误，表明获取许可信息失败但可能源于许可本身的问题
		return nil, apperrors.NewBusinessError(apperrors.CODE_LICENSE_INVALID, "获取授权信息失败，可能授权无效或已损坏").WithCause(err)
	}

	if currentInfo == nil {
		logger.Info(ctx, "当前无有效的授权信息 (currentInfo is nil after GetRuntimeLicenseState)")
		// 使用 CODE_BUSINESS_RESOURCE_NOT_FOUND 表示未找到有效的许可信息
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "未找到有效授权信息")
	}

	// 进一步检查从 GetRuntimeLicenseState 获取的 licenseInfo 是否已过期
	// 因为 GetRuntimeLicenseState 本身可能不认为过期是一个立即的 error，
	// 而是将过期的 LicenseInfo 和 nil error 返回。
	// 服务层在这里明确检查并返回业务错误。
	if currentInfo.IsExpired() {
		expiredMsg := fmt.Sprintf("当前授权已于 %s 过期", currentInfo.ExpiryDate.Format("2006-01-02"))
		logger.Warn(ctx, expiredMsg)
		return currentInfo, apperrors.NewBusinessError(apperrors.CODE_LICENSE_EXPIRED, expiredMsg)
	}

	logger.Debugf("成功获取运行时授权详情。客户: %s, 到期日: %s", currentInfo.CustomerName, currentInfo.ExpiryDate.Format("2006-01-02"))
	return currentInfo, nil
}
