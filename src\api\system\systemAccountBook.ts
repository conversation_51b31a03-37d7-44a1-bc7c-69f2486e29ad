import request from '@/utils/request';

// --- TypeScript Type Definitions based on Go DTOs/VOs ---

// AccountBookVO (View Object - for displaying)
export interface AccountBookVO {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: number; // Assuming these might exist based on BaseVO/TenantVO
  updatedBy?: number; // Assuming these might exist
  tenantId?: number;  // Assuming this might exist
  code: string;
  name: string;
  companyName?: string;
  taxId?: string;
  companyAddress?: string;
  companyPhone?: string;
  bankName?: string;
  bankAccount?: string;
  isGroup?: boolean | null; // Use boolean | null to match Go pointer *bool
  isVirtual?: boolean | null;
  status: number;
}

// AccountBookCreateDTO (Data Transfer Object - for creating)
export interface AccountBookCreateDTO {
  code: string;
  name: string;
  companyName?: string;
  taxId?: string;
  companyAddress?: string;
  companyPhone?: string;
  bankName?: string;
  bankAccount?: string;
  isGroup?: boolean | null;
  isVirtual?: boolean | null;
  status: number; // Should be 0 or 1
}

// AccountBookUpdateDTO (Data Transfer Object - for updating)
export interface AccountBookUpdateDTO extends AccountBookCreateDTO {} // Update DTO seems identical to Create DTO in Go code

// AccountBookQueryDTO (Data Transfer Object - for querying list/page)
export interface AccountBookQueryDTO {
  code?: string;
  name?: string;
  companyName?: string;
  status?: number | null; // Use number | null for optional query params
  isGroup?: boolean | null;
  isVirtual?: boolean | null;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

// StatusDTO (For updating status)
export interface StatusDTO {
    status: number; // 0 or 1
}

// Paged response structure (assuming similar to other modules)
export interface AccountBookListResponse {
  list: AccountBookVO[];
  total: number;
  pages?: number; // Optional based on BaseRepositoryImpl
  pageNum?: number; // Optional
  pageSize?: number; // Optional
}


// --- API Service Functions ---

const BASE_URL = '/sys/account-books'; // Base path from Go controller

/**
 * Get paged list of Account Books
 * Corresponds to GET /api/v1/sys/account-books
 * @param params Query parameters including pagination and filters
 */
export function pageAccountBooks(params: AccountBookQueryDTO & { page: number; size: number }) {
  return request.get<AccountBookListResponse>(`${BASE_URL}`, { params });
}

/**
 * Get list of all matching Account Books (no pagination)
 * Corresponds to GET /api/v1/sys/account-books/list
 * @param params Query parameters for filtering
 */
export function listAccountBooks(params?: AccountBookQueryDTO) {
    return request.get<AccountBookVO[]>(`${BASE_URL}/list`, { params });
}


/**
 * Create a new Account Book
 * Corresponds to POST /api/v1/sys/account-books
 * @param data Account Book creation data
 */
export function addAccountBook(data: AccountBookCreateDTO) {
  return request.post<AccountBookVO>(`${BASE_URL}`, data); // Assuming backend returns the created VO
}

/**
 * Update an existing Account Book
 * Corresponds to PUT /api/v1/sys/account-books/{id}
 * @param id Account Book ID
 * @param data Account Book update data
 */
export function updateAccountBook(id: number, data: AccountBookUpdateDTO) {
  return request.put<AccountBookVO>(`${BASE_URL}/${id}`, data); // Assuming backend returns the updated VO
}

/**
 * Delete an Account Book
 * Corresponds to DELETE /api/v1/sys/account-books/{id}
 * @param id Account Book ID
 */
export function deleteAccountBook(id: number) {
  return request.delete<any>(`${BASE_URL}/${id}`); // Response might be empty or just success message
}

/**
 * Get details of a single Account Book
 * Corresponds to GET /api/v1/sys/account-books/{id}
 * @param id Account Book ID
 */
export function getAccountBookByID(id: number) {
  return request.get<AccountBookVO>(`${BASE_URL}/${id}`);
}

/**
 * Update the status of an Account Book
 * Corresponds to PATCH /api/v1/sys/account-books/{id}/status
 * @param id Account Book ID
 * @param status New status (0 or 1)
 */
export function updateAccountBookStatus(id: number, status: number) {
  const data: StatusDTO = { status };
  return request.patch<any>(`${BASE_URL}/${id}/status`, data); // PATCH method
}

/**
 * Import Account Books from Excel
 * TODO: Backend endpoint needs implementation (e.g., POST /api/v1/sys/account-books/import)
 * @param data Array of account book data extracted from Excel
 */
export function importAccountBooks(data: AccountBookCreateDTO[]) {
  console.warn('Backend import endpoint for account books is not implemented yet.');
  // Replace with actual API call when backend is ready
  return request.post<any>(`${BASE_URL}/import`, data); // Example endpoint
}

// Note: Batch delete is not defined in the provided Go controller, so it's omitted here.
// If needed, add a function like:
// export function batchDeleteAccountBooks(ids: number[]) {
//   return request.delete<any>(`${BASE_URL}/batch`, { data: { ids } }); // Example endpoint
// } 