package database

import (
	"errors" // Import errors package
	"fmt"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	// Import entities for migration/seeding
	"backend/internal/model/entity"
	"backend/pkg/config"
	"backend/pkg/logger"
	"backend/pkg/util"
	// For password hashing in seeding
)

// InitializeDb 根据配置初始化数据库连接、执行迁移和填充种子数据
// 返回完全初始化的 DBManager 和错误（如果发生）。
// gorm.DB 实例可以通过 dbManager.GetDB() 获取。
func InitializeDb(cfg *config.DatabaseConfig) (DBManager, error) {
	log := logger.GetLogger()
	var dbManager DBManager
	var gormDB *gorm.DB // Still need this locally for migration/seeding
	var err error

	// 根据数据库类型选择初始化方法
	switch cfg.Type {
	case "mysql":
		log.Info("初始化 MySQL 数据库...")
		dbManager, err = initMySQL(&cfg.MySQL) // Pass MySQL specific config
		if err != nil {
			return nil, fmt.Errorf("初始化 MySQL 失败: %w", err)
		}
	case "postgresql":
		log.Info("初始化 PostgreSQL 数据库...")
		dbManager, err = initPostgreSQL(&cfg.PostgreSQL) // Pass PostgreSQL specific config
		if err != nil {
			return nil, fmt.Errorf("初始化 PostgreSQL 失败: %w", err)
		}
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", cfg.Type)
	}

	log.Info("数据库连接并初始化管理器成功")

	// 获取 GORM DB 实例用于迁移和填充
	gormDB = dbManager.GetDB()
	if gormDB == nil {
		// Connect 应该确保 DB 被设置，但这作为一个额外的安全检查
		return nil, errors.New("DBManager 初始化后无法获取 GORM DB 实例")
	}

	// 如果启用了自动初始化
	if cfg.AutoInit {
		log.Info("检测到 auto_init = true, 开始执行数据库迁移和种子数据填充...")

		// 执行数据库迁移
		log.Info("执行数据库迁移 (AutoMigrate)...")
		err = runMigrations(gormDB)
		if err != nil {
			// 考虑：迁移失败是否应该回滚或清理？目前仅返回错误。
			return dbManager, fmt.Errorf("数据库迁移失败: %w", err) // 返回部分初始化的管理器和错误
		}
		log.Info("数据库迁移完成")

		// 填充种子数据
		log.Info("执行种子数据填充...")
		// TODO: 从配置或环境变量获取默认管理员密码
		defaultAdminPassword := "password123" // 临时使用硬编码密码
		err = seedData(gormDB, defaultAdminPassword)
		if err != nil {
			return dbManager, fmt.Errorf("填充种子数据失败: %w", err) // 返回部分初始化的管理器和错误
		}
		log.Info("种子数据填充完成 (如果需要)")

	} else {
		log.Info("跳过数据库自动迁移和种子数据填充 (auto_init = false)")
	}

	log.Info("数据库初始化流程完成")
	return dbManager, nil
}

// runMigrations 执行数据库自动迁移
func runMigrations(db *gorm.DB) error {
	log := logger.GetLogger()
	log.Info("开始执行数据库迁移...")

	// --- Manually create ENUM types for PostgreSQL before AutoMigrate ---
	if db.Dialector.Name() == "postgres" {
		log.Info("检测到 PostgreSQL，尝试创建 WMS ENUM 类型...")

		// SQL to create types if they don't exist
		createEnumSQL := `
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_location_type') THEN
        CREATE TYPE wms_location_type AS ENUM (
            'WAREHOUSE', 'ZONE', 'AREA', 'AISLE', 'RACK', 'LEVEL', 'BIN', 'SLOT',
            'FLOOR_SLOT', 'DOCK_DOOR', 'STAGING_AREA', 'QC_AREA'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_location_status') THEN
         CREATE TYPE wms_location_status AS ENUM (
            'ACTIVE', 'INACTIVE', 'MAINTENANCE', 'COUNTING'
        );
    END IF;
    -- Add other MTL ENUM types here using the same pattern...
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mtl_item_status') THEN
        CREATE TYPE mtl_item_status AS ENUM ('ACTIVE', 'INACTIVE');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_inbound_notification_status') THEN
        CREATE TYPE wms_inbound_notification_status AS ENUM (
            'DRAFT', 'PLANNED', 'ARRIVED', 'RECEIVING', 'PARTIALLY_RECEIVED',
            'RECEIVED', 'CLOSED', 'CANCELLED'
        );
    ELSE
        -- 如果枚举类型已存在，检查并添加 DRAFT 状态
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'wms_inbound_notification_status') 
            AND enumlabel = 'DRAFT'
        ) THEN
            ALTER TYPE wms_inbound_notification_status ADD VALUE 'DRAFT' BEFORE 'PLANNED';
        END IF;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_receiving_type') THEN
         CREATE TYPE wms_receiving_type AS ENUM (
            'ASN', 'BLIND'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_receiving_record_status') THEN
         CREATE TYPE wms_receiving_record_status AS ENUM (
            'PENDING', 'RECEIVING', 'PENDING_INSPECTION', 'INSPECTING',
            'COMPLETED', 'PARTIALLY_COMPLETED', 'CLOSED', 'CANCELLED'
        );
    END IF;
	IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_receiving_inspection_status') THEN
         CREATE TYPE wms_receiving_inspection_status AS ENUM (
            'NOT_REQUIRED', 'PENDING', 'INSPECTING', 'PASS', 'FAIL'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_putaway_task_status') THEN
        CREATE TYPE wms_putaway_task_status AS ENUM (
            'PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_inventory_status') THEN
         CREATE TYPE wms_inventory_status AS ENUM (
            'AVAILABLE', 'QUALITY_INSPECTION', 'HOLD', 'FROZEN_QC', 'FROZEN_COUNT',
            'FROZEN_CUSTOMER', 'DAMAGED', 'EXPIRED', 'ALLOCATED', 'PENDING_PUTAWAY',
            'PENDING_PICK', 'IN_TRANSIT', 'PACKING'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_inventory_transaction_type') THEN
        CREATE TYPE wms_inventory_transaction_type AS ENUM (
            -- Update values to match wms_inventory_transaction_log_entity.go
            'RECEIPT', 'PUTAWAY', 'PICKING', 'SHIPMENT', 'ADJUSTMENT_GAIN', 'ADJUSTMENT_LOSS',
            'TRANSFER_OUT', 'TRANSFER_IN', 'STOCKTAKE_GAIN', 'STOCKTAKE_LOSS', 'MOVE_OUT',
            'MOVE_IN', 'STATUS_CHANGE', 'RECEIPT_CORRECTION', 'RECEIPT_CANCELLED',
            'FREEZE', 'UNFREEZE', 'ALLOCATE', 'DEALLOCATE'
        );
    END IF;
    -- Add missing WmsCycleCountPlanStatus ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_cycle_count_plan_status') THEN
        CREATE TYPE wms_cycle_count_plan_status AS ENUM (
            'PENDING', 'GENERATING', 'COUNTING', 'REVIEWING',
            'COMPLETED', 'CANCELLED', 'ERROR'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_cycle_count_plan_type') THEN
        CREATE TYPE wms_cycle_count_plan_type AS ENUM (
            'BY_LOCATION', 'BY_ITEM', 'DYNAMIC', 'BLIND'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_cycle_count_task_status') THEN
         CREATE TYPE wms_cycle_count_task_status AS ENUM (
            'PENDING_COUNT', 'COUNTING', 'COUNTED', 'PENDING_RECOUNT', 'RECOUNTING',
            'RECOUNTED', 'COMPLETED', 'CANCELLED'
         );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_inventory_adjustment_type') THEN
         CREATE TYPE wms_inventory_adjustment_type AS ENUM (
             -- Update values to match wms_inventory_adjustment_entity.go
             'CYCLE_COUNT_GAIN', 'CYCLE_COUNT_LOSS', 'MANUAL_GAIN', 'MANUAL_LOSS',
             'DAMAGE', 'SCRAP', 'STATUS_CHANGE', 'INITIAL_LOAD', 'CUSTOMER_RETURN',
             'SUPPLIER_RETURN', 'QUALITY_RELEASE', 'QUALITY_BLOCK', 'OTHER_GAIN', 'OTHER_LOSS'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_adjustment_status') THEN
         CREATE TYPE wms_adjustment_status AS ENUM (
             -- Update values to match wms_inventory_adjustment_entity.go
             'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'PROCESSING', 'COMPLETED', 'CANCELLED'
        );
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_outbound_notification_status') THEN
         CREATE TYPE wms_outbound_notification_status AS ENUM (
             -- Update values to match wms_outbound_notification_entity.go
             'PENDING', 'ALLOCATED', 'PICKING', 'PICKED', 'PACKED', 'SHIPPED',
             'CANCELLED', 'ON_HOLD'
        );
    END IF;
    -- Add missing WmsOutboundOrderStatus ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_outbound_order_status') THEN
        CREATE TYPE wms_outbound_order_status AS ENUM (
            'PENDING', 'ALLOCATING', 'ALLOCATED', 'PARTIAL_ALLOCATED', 'PICKING', 'PICKED',
            'PACKING', 'PACKED', 'SHIPPED', 'PARTIAL_SHIPPED', 'CANCELLED', 'ON_HOLD'
        );
    END IF;
    -- Add missing WmsOutboundOrderType ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_outbound_order_type') THEN
        CREATE TYPE wms_outbound_order_type AS ENUM (
            'SALES_ORDER', 'TRANSFER_ORDER', 'RETURN_TO_VENDOR', 'OTHER'
        );
    END IF;
     IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_picking_task_status') THEN
         CREATE TYPE wms_picking_task_status AS ENUM (
            -- Update values to match wms_picking_task_entity.go
            'PENDING_ASSIGNMENT', 'PENDING_PICKING', 'PICKING', 'COMPLETED', 'ON_HOLD', 'CANCELLED'
        );
    END IF;
    -- Add missing WmsPickingStrategyStatus ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_picking_strategy_status') THEN
        CREATE TYPE wms_picking_strategy_status AS ENUM ('ACTIVE', 'INACTIVE');
    END IF;
    -- Add missing WmsPickingStrategyType ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_picking_strategy_type') THEN
        CREATE TYPE wms_picking_strategy_type AS ENUM ('ORDER', 'ZONE', 'BATCH', 'WAVE');
    END IF;
    -- Add missing WmsPickingRuleConditionField ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_picking_rule_condition_field') THEN
        CREATE TYPE wms_picking_rule_condition_field AS ENUM ('ITEM_CATEGORY', 'ORDER_TYPE', 'ZONE', 'CLIENT', 'ORDER_PRIORITY');
    END IF;
    -- Add missing WmsPickingRuleConditionOperator ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_picking_rule_condition_operator') THEN
        CREATE TYPE wms_picking_rule_condition_operator AS ENUM ('EQ', 'NE', 'GT', 'LT', 'IN', 'NIN');
    END IF;
    -- Add missing WmsPickingRuleActionType ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_picking_rule_action_type') THEN
        CREATE TYPE wms_picking_rule_action_type AS ENUM ('ALLOCATION_LOGIC', 'PICK_FROM_ZONE', 'PICK_FROM_LOCATION_TYPE', 'ASSIGN_TO_USER_GROUP', 'SET_PRIORITY');
    END IF;
    -- Add missing WmsPickingTaskDetailStatus ENUM
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_picking_task_detail_status') THEN
        CREATE TYPE wms_picking_task_detail_status AS ENUM ('PENDING', 'PICKED', 'SKIPPED', 'SHORTAGE', 'CANCELLED');
    END IF;

    -- ENUM type for HR Organization Node Type
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'hr_organization_node_type') THEN
        CREATE TYPE hr_organization_node_type AS ENUM (
            'company', 'department', 'position'
        );
    END IF;

END$$;
		`
		if err := db.Exec(createEnumSQL).Error; err != nil {
			// Return the error directly if ENUM creation fails
			log.Error("执行 WMS ENUM 类型创建 SQL 时出错", zap.Error(err))
			return fmt.Errorf("创建 WMS ENUM 类型失败: %w", err) // Return error
		} else {
			log.Info("WMS ENUM 类型创建 SQL 执行完成 (如果不存在则创建)")
		}
	}
	// --- End of manual ENUM creation ---

	// 自动迁移所有实体
	log.Info("开始执行 AutoMigrate...")
	err := db.AutoMigrate(

		// 系统相关实体
		&entity.User{},
		&entity.Role{},
		&entity.Menu{},
		&entity.AccountBook{},
		&entity.UserAccountBook{},
		&entity.OrganizationNode{}, // Ensure OrganizationNode is here
		&entity.DictionaryType{},
		&entity.DictionaryItem{},
		&entity.SystemParameter{},
		&entity.FileMetadata{},
		&entity.Employee{},
		&entity.AuditLog{},
		&entity.FiscalPeriod{},
		&entity.FinCurrency{},
		&entity.FinExchangeRate{},
		&entity.FinTaxRate{},

		// WMS相关实体
		&entity.WmsLocation{},
		&entity.MtlItem{},
		&entity.MtlItemPackageUnit{},

		// WMS入库流程实体
		&entity.WmsInboundNotification{},
		&entity.WmsInboundNotificationDetail{},
		&entity.WmsReceivingRecord{},
		&entity.WmsReceivingRecordDetail{},
		&entity.WmsPutawayTask{},
		&entity.WmsPutawayTaskDetail{},
		&entity.WmsInventory{},
		&entity.WmsInventoryTransactionLog{},

		// WMS出库流程实体
		&entity.WmsOutboundNotification{},
		&entity.WmsOutboundNotificationDetail{},
		&entity.WmsInventoryAllocation{},
		&entity.WmsPickingTask{},
		&entity.WmsPickingTaskDetail{},
		&entity.WmsShipment{},

		// WMS盲收策略实体
		&entity.WmsBlindReceivingConfig{},
		&entity.WmsBlindReceivingValidation{},

		// WMS库存管理实体
		&entity.WmsInventoryAdjustment{},
		&entity.WmsInventoryMovement{},
		&entity.WmsCycleCountPlan{},
		&entity.WmsCycleCountTask{},
		&entity.WmsInventoryAlertRule{},
		&entity.WmsInventoryAlertLog{},

		// 编码规则系统实体
		&entity.SysCodeRule{},
		&entity.SysCodeFormatComponent{},
		&entity.SysCodeGenerationLog{},

		// CRM相关实体
		&entity.CrmCustomer{},
		&entity.CrmCustomerContact{},

		// SCM相关实体
		&entity.ScmSupplier{},
		&entity.ScmSupplierContact{},
	)
	if err != nil {
		log.Error("AutoMigrate 失败", zap.Error(err))
		return fmt.Errorf("数据库自动迁移失败: %w", err)
	}
	log.Info("AutoMigrate 完成")

	// --- 创建部分唯一索引 (针对 PostgreSQL) ---
	if db.Dialector.Name() == "postgres" {
		log.Info("检测到 PostgreSQL，开始执行 PostgreSQL 特有的初始化...") // Changed log message slightly for clarity

		// --- 创建部分唯一索引 ---
		partialIndexSQL := `
		DO $$
		BEGIN
			-- 检查索引是否已存在，避免重复创建或因约束名冲突导致错误
			-- 注意：更可靠的方法是检查 pg_indexes 或 information_schema.statistics
			-- 但为了简单起见，我们直接尝试创建 IF NOT EXISTS
			-- 或者先删除旧的约束（如果知道名字）
			-- ALTER TABLE wms_locations DROP CONSTRAINT IF EXISTS your_old_unique_constraint_name;

			-- sys_user 过滤唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_sys_user_username_active 
			ON sys_user (username) 
			WHERE deleted_at IS NULL;

			-- sys_role 过滤唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_sys_role_code_active 
			ON sys_role (code) 
			WHERE deleted_at IS NULL;

			-- fin_period 过滤唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_fin_period_code_active 
			ON fin_period (account_book_id,code) 
			WHERE deleted_at IS NULL;


			-- sys_parameter 过滤唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_sys_parameter_param_key_active 
			ON sys_parameter (param_key) 
			WHERE deleted_at IS NULL;

			-- mtl_item 部分唯一索引 (租户+账套+SKU，仅未删除记录)
			CREATE UNIQUE INDEX IF NOT EXISTS idx_mtl_item_active_tenant_account_sku
			ON mtl_item (tenant_id, account_book_id, sku)
			WHERE deleted_at IS NULL;

			-- mtl_item_package_unit 部分唯一索引 (租户+账套+物料+单位名，仅未删除记录)
			CREATE UNIQUE INDEX IF NOT EXISTS idx_mtl_item_package_unit_active_tenant_account_item_unit
			ON mtl_item_package_unit (tenant_id, account_book_id, item_id, unit_name)
			WHERE deleted_at IS NULL;

			-- 例如: sys_account_book (tenant_id, code)
			CREATE UNIQUE INDEX IF NOT EXISTS idx_account_book_active_tenant_code
			ON sys_account_book (tenant_id, code)
			WHERE deleted_at IS NULL;

			-- 例如: hr_employee (tenant_id, employee_code)
			CREATE UNIQUE INDEX IF NOT EXISTS idx_hr_employee_active_tenant_code
			ON hr_employee (tenant_id, employee_code)
			WHERE deleted_at IS NULL;

			-- hr_organization_node 的唯一性约束
			CREATE UNIQUE INDEX IF NOT EXISTS idx_hr_org_node_account_parent_name_active
			ON hr_organization_node (account_book_id, parent_id, name)
			WHERE deleted_at IS NULL;

			CREATE UNIQUE INDEX IF NOT EXISTS idx_hr_org_node_account_code_active
			ON hr_organization_node (account_book_id, code)
			WHERE code IS NOT NULL AND code != '' AND deleted_at IS NULL;

			-- 编码规则系统的唯一性索引
			-- sys_code_rule: 租户+账套+规则编码唯一性
			CREATE UNIQUE INDEX IF NOT EXISTS idx_code_rule_active_tenant_account_code
			ON sys_code_rule (tenant_id, account_book_id, rule_code)
			WHERE deleted_at IS NULL;

			-- sys_code_rule: 租户+账套+业务类型默认规则唯一性
			CREATE UNIQUE INDEX IF NOT EXISTS idx_code_rule_active_business_default
			ON sys_code_rule (tenant_id, account_book_id, business_type)
			WHERE deleted_at IS NULL AND is_default = true;

			-- CRM客户相关唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_crm_customer_active_tenant_account_code
			ON crm_customer (tenant_id, account_book_id, customer_code)
			WHERE deleted_at IS NULL;

			CREATE UNIQUE INDEX IF NOT EXISTS idx_crm_customer_active_business_license
			ON crm_customer (tenant_id, account_book_id, business_license)
			WHERE deleted_at IS NULL AND business_license IS NOT NULL AND business_license != '';

			CREATE UNIQUE INDEX IF NOT EXISTS idx_crm_customer_active_tax_number
			ON crm_customer (tenant_id, account_book_id, tax_number)  
			WHERE deleted_at IS NULL AND tax_number IS NOT NULL AND tax_number != '';

			-- 客户联系人唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_crm_contact_active_customer_primary
			ON crm_customer_contact (customer_id)
			WHERE deleted_at IS NULL AND is_primary = true;

			CREATE UNIQUE INDEX IF NOT EXISTS idx_crm_contact_active_customer_email
			ON crm_customer_contact (customer_id, email)
			WHERE deleted_at IS NULL AND email IS NOT NULL AND email != '';

			-- SCM供应商相关唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_scm_supplier_active_tenant_account_code
			ON scm_supplier (tenant_id, account_book_id, supplier_code)
			WHERE deleted_at IS NULL;

			CREATE UNIQUE INDEX IF NOT EXISTS idx_scm_supplier_active_business_license
			ON scm_supplier (tenant_id, account_book_id, business_license)
			WHERE deleted_at IS NULL AND business_license IS NOT NULL AND business_license != '';

			-- 供应商联系人唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_scm_contact_active_supplier_primary
			ON scm_supplier_contact (supplier_id)
			WHERE deleted_at IS NULL AND is_primary = true;

			-- WMS盲收配置相关唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_wms_blind_config_active_tenant_account_level_target
			ON wms_blind_receiving_config (tenant_id, account_book_id, config_level, config_target_id)
			WHERE deleted_at IS NULL;

			-- WMS盲收验证记录唯一索引
			CREATE UNIQUE INDEX IF NOT EXISTS idx_wms_blind_validation_active_validation_id
			ON wms_blind_receiving_validation (validation_id)
			WHERE deleted_at IS NULL;

			-- WMS出库流程相关唯一索引
			-- 出库通知单号唯一性
			CREATE UNIQUE INDEX IF NOT EXISTS idx_wms_outbound_notification_active_tenant_account_no
			ON wms_outbound_notification (tenant_id, account_book_id, notification_no)
			WHERE deleted_at IS NULL;

			-- 拣货任务号唯一性
			CREATE UNIQUE INDEX IF NOT EXISTS idx_wms_picking_task_active_tenant_account_no
			ON wms_picking_task (tenant_id, account_book_id, task_no)
			WHERE deleted_at IS NULL;

			-- 发运单号唯一性
			CREATE UNIQUE INDEX IF NOT EXISTS idx_wms_shipment_active_tenant_account_no
			ON wms_shipment (tenant_id, account_book_id, shipment_no)
			WHERE deleted_at IS NULL;

			-- 库存分配记录复合索引
			CREATE INDEX IF NOT EXISTS idx_wms_inventory_allocation_outbound_detail
			ON wms_inventory_allocation (outbound_detail_id, status)
			WHERE deleted_at IS NULL;

			CREATE INDEX IF NOT EXISTS idx_wms_inventory_allocation_inventory
			ON wms_inventory_allocation (inventory_id, status)
			WHERE deleted_at IS NULL;

		END $$;
		`
		if err := db.Exec(partialIndexSQL).Error; err != nil {
			// 索引创建失败可能是因为旧约束冲突或语法错误，需要检查日志
			log.Error("创建部分唯一索引时出错", zap.Error(err))
			// 注意：这里选择记录错误但不中断启动，因为 AutoMigrate 可能已成功
			// 或者根据需要返回错误: return fmt.Errorf("创建部分唯一索引失败: %w", err)
		} else {
			log.Info("部分唯一索引创建 SQL 执行完成 (如果不存在则创建)")
		}

		// --- 创建组织结构递归更新函数 ---
		log.Info("创建 PostgreSQL 函数 hr_update_org_node_descendants_level_company_id...")
		createOrgNodeUpdateFunctionSQL := `
CREATE OR REPLACE FUNCTION hr_update_org_node_descendants_level_company_id(
    p_target_node_id BIGINT,
    p_target_node_new_level INT,
    p_target_node_new_company_id BIGINT
)
RETURNS VOID AS $$
BEGIN
    WITH RECURSIVE SubHierarchy AS (
        -- 起始点：p_target_node_id 的直接子节点
        SELECT
            id,
            parent_id,
            p_target_node_new_level + 1 AS calculated_level, -- 直接子节点的新 level
            1 AS relative_depth -- 相对于 p_target_node_id 的深度
        FROM
            hr_organization_node
        WHERE
            parent_id = p_target_node_id
            AND deleted_at IS NULL -- 只处理未删除的节点

        UNION ALL

        -- 递归部分：获取更深层级的子节点
        SELECT
            n.id,
            n.parent_id,
            sh.calculated_level + 1 AS calculated_level, -- 基于父节点的新 level 计算
            sh.relative_depth + 1 AS relative_depth
        FROM
            hr_organization_node n
        JOIN
            SubHierarchy sh ON n.parent_id = sh.id
        WHERE
            n.deleted_at IS NULL -- 只处理未删除的节点
    )
    UPDATE
        hr_organization_node AS target_table
    SET
        level = sh.calculated_level,
        company_id = p_target_node_new_company_id, -- 所有子孙继承 p_target_node_id 的新 CompanyID
        updated_at = CURRENT_TIMESTAMP
    FROM
        SubHierarchy sh
    WHERE
        target_table.id = sh.id
        AND (target_table.level != sh.calculated_level OR target_table.company_id != p_target_node_new_company_id); -- 仅当 level 或 company_id 变化时更新

END;
$$ LANGUAGE plpgsql;
`
		if err := db.Exec(createOrgNodeUpdateFunctionSQL).Error; err != nil {
			log.Error("创建函数 hr_update_org_node_descendants_level_company_id 失败", zap.Error(err))
			return fmt.Errorf("创建组织节点递归更新函数失败: %w", err)
		}
		log.Info("函数 hr_update_org_node_descendants_level_company_id 创建/替换成功")
		log.Info("PostgreSQL 特有初始化完成") // Moved this log to the end of the PG specific block
	}
	// --- 部分唯一索引创建结束 --- // This comment seems misplaced, should be before the PG block ends or removed if PG block has its own end log.

	log.Info("数据库迁移流程完成")
	return nil // 迁移和索引（尝试）创建完成
}

// seedData 填充种子数据 (如管理员账户、默认角色等)
func seedData(db *gorm.DB, defaultAdminPassword string) error {
	log := logger.GetLogger()

	// 辅助函数：创建字符串指针
	ptrString := func(s string) *string {
		return &s
	}

	// --- 检查是否需要填充 ---
	var userCount int64
	// 使用 Unscoped 确保即使有软删除标记也能正确计数
	db.Model(&entity.User{}).Unscoped().Where("username = ?", "admin").Count(&userCount)
	if userCount > 0 {
		log.Info("管理员用户 'admin' 已存在，跳过种子数据填充")
		return nil // 数据已存在，无需填充
	}

	log.Info("开始填充种子数据...")

	// 使用事务确保原子性
	return db.Transaction(func(tx *gorm.DB) error {
		// --- 1. 创建管理员角色 ---
		log.Debug("创建管理员角色...")
		adminRole := entity.Role{
			Name:     "系统管理员",
			Code:     "ADMIN",
			Status:   1,
			IsSystem: true, // 标记为系统角色
			Remark:   "系统内置最高权限管理员",
		}
		if err := tx.Create(&adminRole).Error; err != nil {
			log.Error("创建管理员角色失败", logger.WithError(err))
			return fmt.Errorf("创建管理员角色失败: %w", err)
		}
		log.Info("管理员角色创建成功", logger.WithField("roleId", adminRole.ID))

		// --- 2. 创建管理员用户 ---
		log.Debug("创建管理员用户...")
		hashedPassword, err := util.EncryptPassword(defaultAdminPassword)
		if err != nil {
			log.Error("加密管理员密码失败", logger.WithError(err))
			return fmt.Errorf("加密管理员密码失败: %w", err)
		}

		adminUser := entity.User{
			Username: "admin",
			Password: hashedPassword,
			Nickname: "Admin",
			RealName: "Administrator",
			Status:   1,    // 正常状态
			IsAdmin:  true, // 是管理员
			Remark:   "系统内置管理员账户",
		}
		if err := tx.Create(&adminUser).Error; err != nil {
			log.Error("创建管理员用户失败", logger.WithError(err))
			return fmt.Errorf("创建管理员用户失败: %w", err)
		}
		log.Info("管理员用户创建成功", logger.WithField("userId", adminUser.ID))

		// --- 3. 关联用户和角色 ---
		log.Debug("关联管理员用户和角色...")
		// GORM v2 推荐使用 Association 方法来处理多对多关系
		err = tx.Model(&adminUser).Association("Roles").Append(&adminRole)
		if err != nil {
			log.Error("关联管理员用户和角色失败", logger.WithError(err))
			return fmt.Errorf("关联管理员用户和角色失败: %w", err)
		}
		log.Info("管理员用户和角色关联成功")

		// --- 4. 创建种子菜单数据 ---
		log.Debug("检查并创建种子菜单数据...")
		var menuCount int64
		// 检查一个关键的顶级菜单是否存在，例如系统管理 (使用 Path 检查)
		tx.Model(&entity.Menu{}).Where("path = ?", "/sys").Count(&menuCount)

		if menuCount > 0 {
			log.Info("菜单数据 (Path='/sys') 已存在，跳过菜单种子数据填充")
		} else {
			log.Info("开始填充菜单种子数据...")

			// 定义菜单项 (Type 1: 目录, Type 2: 菜单项)
			// 使用正确的字段名: Sort, Hidden, ParentID (uint)
			systemMenu := entity.Menu{
				// ParentID: 0, // Removed or set to nil implicitly
				Name:       "系统管理",
				Type:       1,
				Path:       "/sys",
				Sort:       10,
				Status:     1,
				Permission: "sys:list",
				Hidden:     false,
				Title:      "系统管理",
				Icon:       "Tools",
			}

			bdMenu := entity.Menu{
				// ParentID: 0, // Removed or set to nil implicitly
				Name:       "基础数据",
				Type:       1,
				Path:       "/bd",
				Sort:       1000,
				Status:     1,
				Permission: "bd:list",
				Hidden:     false,
				Title:      "基础数据",
				Icon:       "List",
			}

			wmsMenu := entity.Menu{
				// ParentID: 0, // Removed or set to nil implicitly
				Name:       "仓库管理",
				Type:       1,
				Path:       "/wms",
				Sort:       100,
				Status:     1,
				Permission: "wms:list",
				Hidden:     false,
				Title:      "仓库管理",
				Icon:       "HomeFilled",
			}

			// 创建顶级菜单
			if err := tx.Create(&systemMenu).Error; err != nil {
				log.Error("创建系统管理菜单失败", logger.WithError(err))
				return fmt.Errorf("创建系统管理菜单失败: %w", err)
			}

			if err := tx.Create(&bdMenu).Error; err != nil {
				log.Error("创建基础数据菜单失败", logger.WithError(err))
				return fmt.Errorf("创建基础数据菜单失败: %w", err)
			}

			if err := tx.Create(&wmsMenu).Error; err != nil {
				log.Error("创建仓库管理菜单失败", logger.WithError(err))
				return fmt.Errorf("创建仓库管理菜单失败: %w", err)
			}

			wmsConfigMenu := entity.Menu{
				ParentID:   &wmsMenu.ID, // Removed or set to nil implicitly
				Name:       "配置中心",
				Type:       1,
				Path:       "/wms/config",
				Sort:       100,
				Status:     1,
				Permission: "wms:config:list",
				Hidden:     false,
				Title:      "配置中心",
				Icon:       "Folder",
			}

			if err := tx.Create(&wmsConfigMenu).Error; err != nil {
				log.Error("创建配置中心菜单失败", logger.WithError(err))
				return fmt.Errorf("创建配置中心菜单失败: %w", err)
			}

			// 定义并创建子菜单
			childMenus0 := []entity.Menu{
				// 基础数据子菜单 (Use pointer for ParentID: &bdMenu.ID)
				{ParentID: &wmsConfigMenu.ID, Name: "库位设置", Type: 2, Icon: "Document", Path: "/wms/location", Sort: 70, Status: 1, Hidden: false, Title: "库位设置", Component: "wms/location/index", Permission: "wms:location:list"},
			}

			if err := tx.Create(&childMenus0).Error; err != nil {
				log.Error("创建配置中心子菜单失败", logger.WithError(err))
				return fmt.Errorf("创建配置中心子菜单失败: %w", err)
			}
			log.Info("基础配置中心种子数据填充成功")

			// 定义并创建子菜单
			childMenus := []entity.Menu{
				// 系统管理子菜单 (Use pointer for ParentID: &systemMenu.ID)
				{ParentID: &systemMenu.ID, Name: "用户管理", Type: 2, Icon: "User", Path: "/sys/user", Sort: 10, Status: 1, Hidden: false, Title: "用户管理", Component: "system/user/index", Permission: "sys:user:list"},
				{ParentID: &systemMenu.ID, Name: "角色管理", Type: 2, Icon: "Open", Path: "/sys/role", Sort: 20, Status: 1, Hidden: false, Title: "角色管理", Component: "system/role/index", Permission: "sys:role:list"},
				{ParentID: &systemMenu.ID, Name: "菜单管理", Type: 2, Icon: "Postcard", Path: "/sys/menu", Sort: 30, Status: 1, Hidden: false, Title: "菜单管理", Component: "system/menu/index", Permission: "sys:menu:list"},
				{ParentID: &systemMenu.ID, Name: "帐套管理", Type: 2, Icon: "ScaleToOriginal", Path: "/sys/accountbook", Sort: 40, Status: 1, Hidden: false, Title: "帐套管理", Component: "system/accountbook/index", Permission: "sys:accountbook:list"},
				{ParentID: &systemMenu.ID, Name: "数据字典", Type: 2, Icon: "Collection", Path: "/sys/dict", Sort: 50, Status: 1, Hidden: false, Title: "数据字典", Component: "system/dic/index", Permission: "sys:dic:list"},
				{ParentID: &systemMenu.ID, Name: "系统参数", Type: 2, Icon: "SetUp", Path: "/sys/parameter", Sort: 60, Status: 1, Hidden: false, Title: "系统参数", Component: "system/parameter/index", Permission: "sys:parameter:list"},
				{ParentID: &systemMenu.ID, Name: "编码规则", Type: 2, Icon: "Watermelon", Path: "/sys/code-rule", Sort: 60, Status: 1, Hidden: false, Title: "编码规则", Component: "system/codeRule/index", Permission: "sys:code-rule:list"},
				{ParentID: &systemMenu.ID, Name: "SQL工具", Type: 2, Icon: "MagicStick", Path: "/sys/sql-tool", Sort: 70, Status: 1, Hidden: false, Title: "SQL工具", Component: "system/sql-tool/index", Permission: "sys:sql-tool:list"},
				{ParentID: &systemMenu.ID, Name: "授权认证", Type: 2, Icon: "ShoppingCart", Path: "/sys/license", Sort: 80, Status: 1, Hidden: false, Title: "授权认证", Component: "system/license/index", Permission: "sys:license:list"},
				{ParentID: &systemMenu.ID, Name: "审计日志", Type: 2, Icon: "Notebook", Path: "/sys/audit", Sort: 90, Status: 1, Hidden: false, Title: "审计日志", Component: "system/audit/index", Permission: "sys:audit:list"},
			}

			if err := tx.Create(&childMenus).Error; err != nil {
				log.Error("创建系统管理子菜单失败", logger.WithError(err))
				return fmt.Errorf("创建系统管理子菜单失败: %w", err)
			}
			log.Info("系统管理菜单种子数据填充成功")

			// --- 4.1 创建用户管理按钮权限 ---
			log.Debug("创建用户管理按钮权限...")
			var userMgmtMenu entity.Menu
			if err := tx.Where("path = ?", "/sys/user").First(&userMgmtMenu).Error; err != nil {
				log.Error("查找用户管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找用户管理菜单失败: %w", err)
			}

			userMgmtButtons := []entity.Menu{
				{ParentID: &userMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:user:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &userMgmtMenu.ID, Name: "新增用户", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:user:add", Sort: 20, Hidden: false, Title: "新增用户"},
				{ParentID: &userMgmtMenu.ID, Name: "编辑用户", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:user:edit", Sort: 30, Hidden: false, Title: "编辑用户"},
				{ParentID: &userMgmtMenu.ID, Name: "删除用户", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:user:delete", Sort: 40, Hidden: false, Title: "删除用户"},
				{ParentID: &userMgmtMenu.ID, Name: "导入用户", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:user:import", Sort: 50, Hidden: false, Title: "导入用户"},
				{ParentID: &userMgmtMenu.ID, Name: "导出用户", Type: 3, Icon: "Download", Status: 1, Permission: "sys:user:export", Sort: 60, Hidden: false, Title: "导出用户"},
				{ParentID: &userMgmtMenu.ID, Name: "筛选用户", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:user:search", Sort: 70, Hidden: false, Title: "筛选用户"}, // 或者可以复用 list? 这里显式添加 search
				{ParentID: &userMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "sys:user:batchdelete", Sort: 80, Hidden: false, Title: "批量删除"},
				{ParentID: &userMgmtMenu.ID, Name: "重置密码", Type: 3, Icon: "Lollipop", Status: 1, Permission: "sys:user:resetpwd", Sort: 90, Hidden: false, Title: "重置密码"},
				{ParentID: &userMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:user:printpreview", Sort: 100, Hidden: false, Title: "打印预览"},
				{ParentID: &userMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:user:print", Sort: 110, Hidden: false, Title: "打印"},
				// ---------------
			}

			if err := tx.Create(&userMgmtButtons).Error; err != nil {
				log.Error("创建用户管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建用户管理按钮权限失败: %w", err)
			}
			log.Info("用户管理按钮权限种子数据填充成功")

			// --- 4.2 创建菜单管理按钮权限 ---
			log.Debug("创建菜单管理按钮权限...")
			var menuMgmtMenu entity.Menu
			if err := tx.Where("path = ?", "/sys/menu").First(&menuMgmtMenu).Error; err != nil {
				log.Error("查找菜单管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找菜单管理菜单失败: %w", err)
			}

			menuMgmtButtons := []entity.Menu{
				// Note: 'list' permission is typically associated with the menu item itself, but adding explicit 'view' button if required by frontend design
				{ParentID: &menuMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:menu:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &menuMgmtMenu.ID, Name: "新增菜单", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:menu:add", Sort: 20, Hidden: false, Title: "新增菜单"},
				{ParentID: &menuMgmtMenu.ID, Name: "编辑菜单", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:menu:edit", Sort: 30, Hidden: false, Title: "编辑菜单"},
				{ParentID: &menuMgmtMenu.ID, Name: "删除菜单", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:menu:delete", Sort: 40, Hidden: false, Title: "删除菜单"},
				{ParentID: &menuMgmtMenu.ID, Name: "导入菜单", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:menu:import", Sort: 50, Hidden: false, Title: "导入菜单"},
				{ParentID: &menuMgmtMenu.ID, Name: "导出菜单", Type: 3, Icon: "Download", Status: 1, Permission: "sys:menu:export", Sort: 60, Hidden: false, Title: "导出菜单"},
				{ParentID: &menuMgmtMenu.ID, Name: "筛选菜单", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:menu:search", Sort: 70, Hidden: false, Title: "筛选菜单"}, // Consider if :list is sufficient
				{ParentID: &menuMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:menu:printpreview", Sort: 80, Hidden: false, Title: "打印预览"},
				{ParentID: &menuMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:menu:print", Sort: 90, Hidden: false, Title: "打印"},
				{ParentID: &menuMgmtMenu.ID, Name: "全部展开", Type: 3, Icon: "ArrowDown", Status: 1, Permission: "sys:menu:expandAll", Sort: 100, Hidden: false, Title: "全部展开"},
				{ParentID: &menuMgmtMenu.ID, Name: "全部折叠", Type: 3, Icon: "ArrowUp", Status: 1, Permission: "sys:menu:collapseAll", Sort: 110, Hidden: false, Title: "全部折叠"},
			}

			if err := tx.Create(&menuMgmtButtons).Error; err != nil {
				log.Error("创建菜单管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建菜单管理按钮权限失败: %w", err)
			}
			log.Info("菜单管理按钮权限种子数据填充成功")

			// --- 4.3 创建角色管理按钮权限 ---
			log.Debug("创建角色管理按钮权限...")
			var roleMgmtMenu entity.Menu
			// 查询 "角色管理" 菜单的 ID
			if err := tx.Where("path = ?", "/sys/role").First(&roleMgmtMenu).Error; err != nil {
				log.Error("查找角色管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找角色管理菜单失败: %w", err)
			}

			roleMgmtButtons := []entity.Menu{
				{ParentID: &roleMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:role:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &roleMgmtMenu.ID, Name: "新增角色", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:role:add", Sort: 20, Hidden: false, Title: "新增角色"},
				{ParentID: &roleMgmtMenu.ID, Name: "编辑角色", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:role:edit", Sort: 30, Hidden: false, Title: "编辑角色"},
				{ParentID: &roleMgmtMenu.ID, Name: "删除角色", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:role:delete", Sort: 40, Hidden: false, Title: "删除角色"},
				{ParentID: &roleMgmtMenu.ID, Name: "导入角色", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:role:import", Sort: 50, Hidden: false, Title: "导入角色"},
				{ParentID: &roleMgmtMenu.ID, Name: "导出角色", Type: 3, Icon: "Download", Status: 1, Permission: "sys:role:export", Sort: 60, Hidden: false, Title: "导出角色"},
				{ParentID: &roleMgmtMenu.ID, Name: "筛选角色", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:role:search", Sort: 70, Hidden: false, Title: "筛选角色"},
				{ParentID: &roleMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "sys:role:batchdelete", Sort: 80, Hidden: false, Title: "批量删除"},
				{ParentID: &roleMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:role:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &roleMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:role:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&roleMgmtButtons).Error; err != nil {
				log.Error("创建角色管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建角色管理按钮权限失败: %w", err)
			}
			log.Info("角色管理按钮权限种子数据填充成功")

			// --- 4.4 创建帐套管理按钮权限 ---
			log.Debug("创建帐套管理按钮权限...")
			var accountBookMgmtMenu entity.Menu
			// 查询 "帐套管理" 菜单的 ID
			if err := tx.Where("path = ?", "/sys/accountbook").First(&accountBookMgmtMenu).Error; err != nil {
				log.Error("查找帐套管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找帐套管理菜单失败: %w", err)
			}

			accountBookMgmtButtons := []entity.Menu{
				{ParentID: &accountBookMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:accountbook:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "新增帐套", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:accountbook:add", Sort: 20, Hidden: false, Title: "新增帐套"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "修改帐套", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:accountbook:edit", Sort: 30, Hidden: false, Title: "修改帐套"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "删除帐套", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:accountbook:delete", Sort: 40, Hidden: false, Title: "删除帐套"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "sys:accountbook:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "筛选帐套", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:accountbook:search", Sort: 60, Hidden: false, Title: "筛选帐套"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "导入帐套", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:accountbook:import", Sort: 70, Hidden: false, Title: "导入帐套"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "导出帐套", Type: 3, Icon: "Download", Status: 1, Permission: "sys:accountbook:export", Sort: 80, Hidden: false, Title: "导出帐套"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:accountbook:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &accountBookMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:accountbook:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&accountBookMgmtButtons).Error; err != nil {
				log.Error("创建帐套管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建帐套管理按钮权限失败: %w", err)
			}
			log.Info("帐套管理按钮权限种子数据填充成功")

			// --- 4.4 创建帐套管理按钮权限 ---
			log.Debug("创建编码规则按钮权限...")
			var codeRuleMgmtMenu entity.Menu
			// 查询 "帐套管理" 菜单的 ID
			if err := tx.Where("path = ?", "/sys/code-rule").First(&codeRuleMgmtMenu).Error; err != nil {
				log.Error("查找编码规则菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找编码规则菜单失败: %w", err)
			}

			codeRuleMgmtButtons := []entity.Menu{
				{ParentID: &codeRuleMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:code-rule:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "新增规则", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:code-rule:add", Sort: 20, Hidden: false, Title: "新增规则"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "修改规则", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:code-rule:edit", Sort: 30, Hidden: false, Title: "修改规则"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "删除规则", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:code-rule:delete", Sort: 40, Hidden: false, Title: "删除规则"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "sys:code-rule:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "筛选规则", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:code-rule:search", Sort: 60, Hidden: false, Title: "筛选规则"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "设为默认", Type: 3, Icon: "Bicycle", Status: 1, Permission: "sys:code-rule:setdefault", Sort: 60, Hidden: false, Title: "设为默认"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "导入规则", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:code-rule:import", Sort: 70, Hidden: false, Title: "导入规则"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "导出规则", Type: 3, Icon: "Download", Status: 1, Permission: "sys:code-rule:export", Sort: 80, Hidden: false, Title: "导出规则"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:code-rule:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &codeRuleMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:code-rule:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&codeRuleMgmtButtons).Error; err != nil {
				log.Error("创建编码规则按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建编码规则按钮权限失败: %w", err)
			}
			log.Info("编码规则按钮权限种子数据填充成功")

			// --- 4.5 创建数据字典按钮权限 ---
			log.Debug("创建数据字典按钮权限...")
			var dictMgmtMenu entity.Menu
			// 查询 "数据字典" 菜单的 ID
			if err := tx.Where("path = ?", "/sys/dict").First(&dictMgmtMenu).Error; err != nil {
				log.Error("查找数据字典菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找数据字典菜单失败: %w", err)
			}

			// 字典类型按钮权限
			dictTypeButtons := []entity.Menu{
				{ParentID: &dictMgmtMenu.ID, Name: "浏览字典类型列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:dict:type:list", Sort: 10, Hidden: false, Title: "浏览字典类型列表"},
				{ParentID: &dictMgmtMenu.ID, Name: "新增字典类型", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:dict:type:add", Sort: 20, Hidden: false, Title: "新增字典类型"},
				{ParentID: &dictMgmtMenu.ID, Name: "修改字典类型", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:dict:type:edit", Sort: 30, Hidden: false, Title: "修改字典类型"},
				{ParentID: &dictMgmtMenu.ID, Name: "删除字典类型", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:dict:type:delete", Sort: 40, Hidden: false, Title: "删除字典类型"},
				{ParentID: &dictMgmtMenu.ID, Name: "批量删除类型", Type: 3, Icon: "Delete", Status: 1, Permission: "sys:dict:type:batchdelete", Sort: 50, Hidden: false, Title: "批量删除类型"},
				{ParentID: &dictMgmtMenu.ID, Name: "筛选字典类型", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:dict:type:search", Sort: 60, Hidden: false, Title: "筛选字典类型"},
				{ParentID: &dictMgmtMenu.ID, Name: "导入字典类型", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:dict:type:import", Sort: 70, Hidden: false, Title: "导入字典类型"},
				{ParentID: &dictMgmtMenu.ID, Name: "导出字典类型", Type: 3, Icon: "Download", Status: 1, Permission: "sys:dict:type:export", Sort: 80, Hidden: false, Title: "导出字典类型"},
				{ParentID: &dictMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:dict:type:printpreview", Sort: 90, Hidden: false, Title: "类型打印预览"},
				{ParentID: &dictMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:dict:type:print", Sort: 100, Hidden: false, Title: "类型打印"},
			}
			if err := tx.Create(&dictTypeButtons).Error; err != nil {
				log.Error("创建数据字典类型按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建数据字典类型按钮权限失败: %w", err)
			}
			log.Info("数据字典类型按钮权限种子数据填充成功")

			// 字典项按钮权限
			dictItemButtons := []entity.Menu{
				{ParentID: &dictMgmtMenu.ID, Name: "浏览字典项列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:dict:item:list", Sort: 10, Hidden: false, Title: "浏览字典项列表"},
				{ParentID: &dictMgmtMenu.ID, Name: "新增字典项", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:dict:item:add", Sort: 20, Hidden: false, Title: "新增字典项"},
				{ParentID: &dictMgmtMenu.ID, Name: "修改字典项", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:dict:item:edit", Sort: 30, Hidden: false, Title: "修改字典项"},
				{ParentID: &dictMgmtMenu.ID, Name: "删除字典项", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:dict:item:delete", Sort: 40, Hidden: false, Title: "删除字典项"},
				{ParentID: &dictMgmtMenu.ID, Name: "批量删除项", Type: 3, Icon: "Delete", Status: 1, Permission: "sys:dict:item:batchdelete", Sort: 50, Hidden: false, Title: "批量删除项"},
				{ParentID: &dictMgmtMenu.ID, Name: "筛选字典项", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:dict:item:search", Sort: 60, Hidden: false, Title: "筛选字典项"},
				{ParentID: &dictMgmtMenu.ID, Name: "导入字典项", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:dict:item:import", Sort: 70, Hidden: false, Title: "导入字典项"},
				{ParentID: &dictMgmtMenu.ID, Name: "导出字典项", Type: 3, Icon: "Download", Status: 1, Permission: "sys:dict:item:export", Sort: 80, Hidden: false, Title: "导出字典项"},
				{ParentID: &dictMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:dict:item:printpreview", Sort: 90, Hidden: false, Title: "项打印预览"},
				{ParentID: &dictMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:dict:item:print", Sort: 100, Hidden: false, Title: "项打印"},
			}
			if err := tx.Create(&dictItemButtons).Error; err != nil {
				log.Error("创建数据字典项按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建数据字典项按钮权限失败: %w", err)
			}
			log.Info("数据字典项按钮权限种子数据填充成功")

			// --- 4.8 创建系统参数按钮权限 ---
			log.Debug("创建系统参数按钮权限...")
			var parameterMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/sys/parameter").First(&parameterMgmtMenu).Error; err != nil {
				log.Error("查找系统参数菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找系统参数菜单失败: %w", err)
			}

			parameterMgmtButtons := []entity.Menu{
				{ParentID: &parameterMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "sys:parameter:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &parameterMgmtMenu.ID, Name: "新增参数", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "sys:parameter:add", Sort: 20, Hidden: false, Title: "新增参数"},
				{ParentID: &parameterMgmtMenu.ID, Name: "修改参数", Type: 3, Icon: "Edit", Status: 1, Permission: "sys:parameter:edit", Sort: 30, Hidden: false, Title: "修改参数"},
				{ParentID: &parameterMgmtMenu.ID, Name: "删除参数", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "sys:parameter:delete", Sort: 40, Hidden: false, Title: "删除参数"},
				{ParentID: &parameterMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "sys:parameter:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &parameterMgmtMenu.ID, Name: "筛选参数", Type: 3, Icon: "Filter", Status: 1, Permission: "sys:parameter:search", Sort: 60, Hidden: false, Title: "筛选参数"},
				{ParentID: &parameterMgmtMenu.ID, Name: "导入参数", Type: 3, Icon: "Upload", Status: 1, Permission: "sys:parameter:import", Sort: 70, Hidden: false, Title: "导入参数"},
				{ParentID: &parameterMgmtMenu.ID, Name: "导出参数", Type: 3, Icon: "Download", Status: 1, Permission: "sys:parameter:export", Sort: 80, Hidden: false, Title: "导出参数"},
				{ParentID: &parameterMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "sys:parameter:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &parameterMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "sys:parameter:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&parameterMgmtButtons).Error; err != nil {
				log.Error("创建系统参数按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建系统参数按钮权限失败: %w", err)
			}
			log.Info("系统参数按钮权限种子数据填充成功")

			// 定义并创建子菜单
			childMenus1 := []entity.Menu{
				// 基础数据子菜单 (Use pointer for ParentID: &bdMenu.ID)
				{ParentID: &bdMenu.ID, Name: "组织结构", Type: 2, Icon: "Cherry", Path: "/hr/organization", Sort: 10, Status: 1, Hidden: false, Title: "组织结构", Component: "hr/organization/index", Permission: "hr:org:list"},
				{ParentID: &bdMenu.ID, Name: "员工信息", Type: 2, Icon: "User", Path: "/hr/employee", Sort: 20, Status: 1, Hidden: false, Title: "员工信息", Component: "hr/employee/index", Permission: "hr:employee:list"},
				{ParentID: &bdMenu.ID, Name: "币种信息", Type: 2, Icon: "Coin", Path: "/fin/currency", Sort: 30, Status: 1, Hidden: false, Title: "币种信息", Component: "fin/currency/index", Permission: "fin:currency:list"},
				{ParentID: &bdMenu.ID, Name: "会计期间", Type: 2, Icon: "Calendar", Path: "/fin/periods", Sort: 40, Status: 1, Hidden: false, Title: "会计期间", Component: "fin/fiscalPeriod/index", Permission: "fin:periods:list"},
				{ParentID: &bdMenu.ID, Name: "税率信息", Type: 2, Icon: "Notification", Path: "/fin/tax-rate", Sort: 50, Status: 1, Hidden: false, Title: "税率信息", Component: "fin/taxRate/index", Permission: "fin:taxrate:list"},
				{ParentID: &bdMenu.ID, Name: "汇率信息", Type: 2, Icon: "Sort", Path: "/fin/exchange-rate", Sort: 60, Status: 1, Hidden: false, Title: "汇率信息", Component: "fin/exchangeRate/index", Permission: "fin:exchangerate:list"},
				{ParentID: &bdMenu.ID, Name: "物品信息", Type: 2, Icon: "Goods", Path: "/wms/item", Sort: 80, Status: 1, Hidden: false, Title: "物品信息", Component: "wms/item/index", Permission: "mtl:item:list"},
				{ParentID: &bdMenu.ID, Name: "客户信息", Type: 2, Icon: "HotWater", Path: "/crm/customer", Sort: 90, Status: 1, Hidden: false, Title: "客户信息", Component: "crm/customer/index", Permission: "crm:customer:list"},
				{ParentID: &bdMenu.ID, Name: "供应商信息", Type: 2, Icon: "KnifeFork", Path: "/scm/supplier", Sort: 100, Status: 1, Hidden: false, Title: "供应商信息", Component: "scm/supplier/index", Permission: "scm:supplier:list"},
			}

			if err := tx.Create(&childMenus1).Error; err != nil {
				log.Error("创建基础数据子菜单失败", logger.WithError(err))
				return fmt.Errorf("创建基础数据子菜单失败: %w", err)
			}
			log.Info("基础数据菜单种子数据填充成功")

			// --- 4.6 创建组织结构按钮权限 ---
			log.Debug("创建库位设置按钮权限...")
			var LocationMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/wms/location").First(&LocationMgmtMenu).Error; err != nil {
				log.Error("查找库位设置菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找库位设置菜单失败: %w", err)
			}

			LocationsButtons := []entity.Menu{
				{ParentID: &LocationMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "wms:location:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &LocationMgmtMenu.ID, Name: "新增节点", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "wms:location:add", Sort: 20, Hidden: false, Title: "新增节点"},
				{ParentID: &LocationMgmtMenu.ID, Name: "修改节点", Type: 3, Icon: "Edit", Status: 1, Permission: "wms:location:edit", Sort: 30, Hidden: false, Title: "修改节点"},
				{ParentID: &LocationMgmtMenu.ID, Name: "删除节点", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "wms:location:delete", Sort: 40, Hidden: false, Title: "删除节点"},
				{ParentID: &LocationMgmtMenu.ID, Name: "筛选节点", Type: 3, Icon: "Filter", Status: 1, Permission: "wms:location:search", Sort: 50, Hidden: false, Title: "筛选节点"},
				{ParentID: &LocationMgmtMenu.ID, Name: "导入节点", Type: 3, Icon: "Upload", Status: 1, Permission: "wms:location:import", Sort: 60, Hidden: false, Title: "导入节点"},
				{ParentID: &LocationMgmtMenu.ID, Name: "导出节点", Type: 3, Icon: "Download", Status: 1, Permission: "wms:location:export", Sort: 70, Hidden: false, Title: "导出节点"},
				{ParentID: &LocationMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "wms:location:printpreview", Sort: 80, Hidden: false, Title: "打印预览"},
				{ParentID: &LocationMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "wms:location:print", Sort: 90, Hidden: false, Title: "打印"},
				{ParentID: &LocationMgmtMenu.ID, Name: "全部展开", Type: 3, Icon: "ArrowDown", Status: 1, Permission: "wms:location:expandAll", Sort: 100, Hidden: false, Title: "全部展开"},
				{ParentID: &LocationMgmtMenu.ID, Name: "全部折叠", Type: 3, Icon: "ArrowUp", Status: 1, Permission: "wms:location:collapseAll", Sort: 110, Hidden: false, Title: "全部折叠"},
			}
			if err := tx.Create(&LocationsButtons).Error; err != nil {
				log.Error("创建库位设置按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建库位设置按钮权限失败: %w", err)
			}
			log.Info("库位设置按钮权限种子数据填充成功")

			// --- 4.6 创建组织结构按钮权限 ---
			log.Debug("创建物品管理按钮权限...")
			var ItemgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/wms/item").First(&ItemgmtMenu).Error; err != nil {
				log.Error("查找物品管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找物品管理菜单失败: %w", err)
			}

			ItemsButtons := []entity.Menu{
				{ParentID: &ItemgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "mtl:item:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &ItemgmtMenu.ID, Name: "新增物品", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "mtl:item:add", Sort: 20, Hidden: false, Title: "新增物品"},
				{ParentID: &ItemgmtMenu.ID, Name: "修改物品", Type: 3, Icon: "Edit", Status: 1, Permission: "mtl:item:edit", Sort: 30, Hidden: false, Title: "修改物品"},
				{ParentID: &ItemgmtMenu.ID, Name: "删除物品", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "mtl:item:delete", Sort: 40, Hidden: false, Title: "删除物品"},
				{ParentID: &ItemgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "mtl:item:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &ItemgmtMenu.ID, Name: "筛选物品", Type: 3, Icon: "Filter", Status: 1, Permission: "mtl:item:search", Sort: 50, Hidden: false, Title: "筛选物品"},
				{ParentID: &ItemgmtMenu.ID, Name: "导入物品", Type: 3, Icon: "Upload", Status: 1, Permission: "mtl:item:import", Sort: 60, Hidden: false, Title: "导入物品"},
				{ParentID: &ItemgmtMenu.ID, Name: "导出物品", Type: 3, Icon: "Download", Status: 1, Permission: "mtl:item:export", Sort: 70, Hidden: false, Title: "导出物品"},
				{ParentID: &ItemgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "mtl:item:printpreview", Sort: 80, Hidden: false, Title: "打印预览"},
				{ParentID: &ItemgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "mtl:item:print", Sort: 90, Hidden: false, Title: "打印"},
			}
			if err := tx.Create(&ItemsButtons).Error; err != nil {
				log.Error("创建物品管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建物品管理按钮权限失败: %w", err)
			}
			log.Info("物品管理按钮权限种子数据填充成功")

			// --- 4.6 创建组织结构按钮权限 ---
			log.Debug("创建客户管理按钮权限...")
			var CustomergmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/crm/customer").First(&CustomergmtMenu).Error; err != nil {
				log.Error("查找客户管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找客户管理菜单失败: %w", err)
			}

			CustomersButtons := []entity.Menu{
				{ParentID: &CustomergmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "crm:customer:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &CustomergmtMenu.ID, Name: "新增客户", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "crm:customer:add", Sort: 20, Hidden: false, Title: "新增客户"},
				{ParentID: &CustomergmtMenu.ID, Name: "修改客户", Type: 3, Icon: "Edit", Status: 1, Permission: "crm:customer:edit", Sort: 30, Hidden: false, Title: "修改客户"},
				{ParentID: &CustomergmtMenu.ID, Name: "删除客户", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "crm:customer:delete", Sort: 40, Hidden: false, Title: "删除客户"},
				{ParentID: &CustomergmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "crm:customer:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &CustomergmtMenu.ID, Name: "筛选客户", Type: 3, Icon: "Filter", Status: 1, Permission: "crm:customer:search", Sort: 50, Hidden: false, Title: "筛选客户"},
				{ParentID: &CustomergmtMenu.ID, Name: "导入客户", Type: 3, Icon: "Upload", Status: 1, Permission: "crm:customer:import", Sort: 60, Hidden: false, Title: "导入客户"},
				{ParentID: &CustomergmtMenu.ID, Name: "导出客户", Type: 3, Icon: "Download", Status: 1, Permission: "crm:customer:export", Sort: 70, Hidden: false, Title: "导出客户"},
				{ParentID: &CustomergmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "crm:customer:printpreview", Sort: 80, Hidden: false, Title: "打印预览"},
				{ParentID: &CustomergmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "crm:customer:print", Sort: 90, Hidden: false, Title: "打印"},
			}
			if err := tx.Create(&CustomersButtons).Error; err != nil {
				log.Error("创建客户管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建客户管理按钮权限失败: %w", err)
			}
			log.Info("客户管理按钮权限种子数据填充成功")

			// --- 4.6 创建组织结构按钮权限 ---
			log.Debug("创建供应商管理按钮权限...")
			var SuppliergmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/scm/supplier").First(&SuppliergmtMenu).Error; err != nil {
				log.Error("查找供应商管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找供应商管理菜单失败: %w", err)
			}

			SuppliersButtons := []entity.Menu{
				{ParentID: &SuppliergmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "scm:supplier:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &SuppliergmtMenu.ID, Name: "新增供应商", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "scm:supplier:add", Sort: 20, Hidden: false, Title: "新增供应商"},
				{ParentID: &SuppliergmtMenu.ID, Name: "修改供应商", Type: 3, Icon: "Edit", Status: 1, Permission: "scm:supplier:edit", Sort: 30, Hidden: false, Title: "修改供应商"},
				{ParentID: &SuppliergmtMenu.ID, Name: "删除供应商", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "scm:supplier:delete", Sort: 40, Hidden: false, Title: "删除供应商"},
				{ParentID: &SuppliergmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "scm:supplier:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &SuppliergmtMenu.ID, Name: "筛选供应商", Type: 3, Icon: "Filter", Status: 1, Permission: "scm:supplier:search", Sort: 50, Hidden: false, Title: "筛选供应商"},
				{ParentID: &SuppliergmtMenu.ID, Name: "导入供应商", Type: 3, Icon: "Upload", Status: 1, Permission: "scm:supplier:import", Sort: 60, Hidden: false, Title: "导入供应商"},
				{ParentID: &SuppliergmtMenu.ID, Name: "导出供应商", Type: 3, Icon: "Download", Status: 1, Permission: "scm:supplier:export", Sort: 70, Hidden: false, Title: "导出供应商"},
				{ParentID: &SuppliergmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "scm:supplier:printpreview", Sort: 80, Hidden: false, Title: "打印预览"},
				{ParentID: &SuppliergmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "scm:supplier:print", Sort: 90, Hidden: false, Title: "打印"},
			}
			if err := tx.Create(&SuppliersButtons).Error; err != nil {
				log.Error("创建供应商管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建供应商管理按钮权限失败: %w", err)
			}
			log.Info("供应商管理按钮权限种子数据填充成功")

			// --- 4.6 创建组织结构按钮权限 ---
			log.Debug("创建组织结构按钮权限...")
			var OrganizationMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/hr/organization").First(&OrganizationMgmtMenu).Error; err != nil {
				log.Error("查找组织结构菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找组织结构菜单失败: %w", err)
			}

			OrganizationNodeButtons := []entity.Menu{
				{ParentID: &OrganizationMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "hr:org:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "新增节点", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "hr:org:add", Sort: 20, Hidden: false, Title: "新增节点"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "修改节点", Type: 3, Icon: "Edit", Status: 1, Permission: "hr:org:edit", Sort: 30, Hidden: false, Title: "修改节点"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "删除节点", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "hr:org:delete", Sort: 40, Hidden: false, Title: "删除节点"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "筛选节点", Type: 3, Icon: "Filter", Status: 1, Permission: "hr:org:search", Sort: 50, Hidden: false, Title: "筛选节点"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "导入节点", Type: 3, Icon: "Upload", Status: 1, Permission: "hr:org:import", Sort: 60, Hidden: false, Title: "导入节点"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "导出节点", Type: 3, Icon: "Download", Status: 1, Permission: "hr:org:export", Sort: 70, Hidden: false, Title: "导出节点"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "hr:org:printpreview", Sort: 80, Hidden: false, Title: "打印预览"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "hr:org:print", Sort: 90, Hidden: false, Title: "打印"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "全部展开", Type: 3, Icon: "ArrowDown", Status: 1, Permission: "hr:org:expandAll", Sort: 100, Hidden: false, Title: "全部展开"},
				{ParentID: &OrganizationMgmtMenu.ID, Name: "全部折叠", Type: 3, Icon: "ArrowUp", Status: 1, Permission: "hr:org:collapseAll", Sort: 110, Hidden: false, Title: "全部折叠"},
			}

			if err := tx.Create(&OrganizationNodeButtons).Error; err != nil {
				log.Error("创建组织结构按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建组织结构按钮权限失败: %w", err)
			}
			log.Info("组织结构按钮权限种子数据填充成功")

			// --- 4.7 创建员工信息按钮权限 ---
			log.Debug("创建员工信息按钮权限...")
			var employeeMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/hr/employee").First(&employeeMgmtMenu).Error; err != nil {
				log.Error("查找员工信息菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找员工信息菜单失败: %w", err)
			}

			employeeMgmtButtons := []entity.Menu{
				{ParentID: &employeeMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "hr:employee:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &employeeMgmtMenu.ID, Name: "新增员工", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "hr:employee:add", Sort: 20, Hidden: false, Title: "新增员工"},
				{ParentID: &employeeMgmtMenu.ID, Name: "修改员工", Type: 3, Icon: "Edit", Status: 1, Permission: "hr:employee:edit", Sort: 30, Hidden: false, Title: "修改员工"},
				{ParentID: &employeeMgmtMenu.ID, Name: "删除员工", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "hr:employee:delete", Sort: 40, Hidden: false, Title: "删除员工"},
				{ParentID: &employeeMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "hr:employee:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &employeeMgmtMenu.ID, Name: "筛选员工", Type: 3, Icon: "Filter", Status: 1, Permission: "hr:employee:search", Sort: 60, Hidden: false, Title: "筛选员工"},
				{ParentID: &employeeMgmtMenu.ID, Name: "导入员工", Type: 3, Icon: "Upload", Status: 1, Permission: "hr:employee:import", Sort: 70, Hidden: false, Title: "导入员工"},
				{ParentID: &employeeMgmtMenu.ID, Name: "导出员工", Type: 3, Icon: "Download", Status: 1, Permission: "hr:employee:export", Sort: 80, Hidden: false, Title: "导出员工"},
				{ParentID: &employeeMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "hr:employee:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &employeeMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "hr:employee:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&employeeMgmtButtons).Error; err != nil {
				log.Error("创建员工信息按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建员工信息按钮权限失败: %w", err)
			}
			log.Info("员工信息按钮权限种子数据填充成功")

			// --- 4.7 创建币种管理按钮权限 ---
			log.Debug("创建币种管理按钮权限...")
			var currencyMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/fin/currency").First(&currencyMgmtMenu).Error; err != nil {
				log.Error("查找币种管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找币种管理菜单失败: %w", err)
			}

			currencyMgmtButtons := []entity.Menu{
				{ParentID: &currencyMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "fin:currency:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &currencyMgmtMenu.ID, Name: "新增币种", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "fin:currency:add", Sort: 20, Hidden: false, Title: "新增币种"},
				{ParentID: &currencyMgmtMenu.ID, Name: "修改币种", Type: 3, Icon: "Edit", Status: 1, Permission: "fin:currency:edit", Sort: 30, Hidden: false, Title: "修改币种"},
				{ParentID: &currencyMgmtMenu.ID, Name: "删除币种", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "fin:currency:delete", Sort: 40, Hidden: false, Title: "删除币种"},
				{ParentID: &currencyMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "fin:currency:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &currencyMgmtMenu.ID, Name: "筛选币种", Type: 3, Icon: "Filter", Status: 1, Permission: "fin:currency:search", Sort: 60, Hidden: false, Title: "筛选币种"},
				{ParentID: &currencyMgmtMenu.ID, Name: "导入币种", Type: 3, Icon: "Upload", Status: 1, Permission: "fin:currency:import", Sort: 70, Hidden: false, Title: "导入币种"},
				{ParentID: &currencyMgmtMenu.ID, Name: "导出币种", Type: 3, Icon: "Download", Status: 1, Permission: "fin:currency:export", Sort: 80, Hidden: false, Title: "导出币种"},
				{ParentID: &currencyMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "fin:currency:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &currencyMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "fin:currency:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&currencyMgmtButtons).Error; err != nil {
				log.Error("创建币种管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建币种管理按钮权限失败: %w", err)
			}
			log.Info("币种管理按钮权限种子数据填充成功")

			// --- 4.7 创建币种管理按钮权限 ---
			log.Debug("创建税率管理按钮权限...")
			var taxRateMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/fin/tax-rate").First(&taxRateMgmtMenu).Error; err != nil {
				log.Error("查找税率管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找税率理菜单失败: %w", err)
			}

			taxRateMgmtButtons := []entity.Menu{
				{ParentID: &taxRateMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "fin:taxrate:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "新增税率", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "fin:taxrate:add", Sort: 20, Hidden: false, Title: "新增税率"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "修改税率", Type: 3, Icon: "Edit", Status: 1, Permission: "fin:taxrate:edit", Sort: 30, Hidden: false, Title: "修改税率"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "删除税率", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "fin:taxrate:delete", Sort: 40, Hidden: false, Title: "删除税率"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "fin:taxrate:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "筛选税率", Type: 3, Icon: "Filter", Status: 1, Permission: "fin:taxrate:search", Sort: 60, Hidden: false, Title: "筛选税率"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "导入税率", Type: 3, Icon: "Upload", Status: 1, Permission: "fin:taxrate:import", Sort: 70, Hidden: false, Title: "导入税率"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "导出税率", Type: 3, Icon: "Download", Status: 1, Permission: "fin:taxrate:export", Sort: 80, Hidden: false, Title: "导出税率"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "fin:taxrate:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &taxRateMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "fin:taxrate:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&taxRateMgmtButtons).Error; err != nil {
				log.Error("创建税率管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建税率管理按钮权限失败: %w", err)
			}
			log.Info("税率管理按钮权限种子数据填充成功")

			// --- 4.7 创建汇率管理按钮权限 ---
			log.Debug("创建汇率管理按钮权限...")
			var exchangeRateMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/fin/exchange-rate").First(&exchangeRateMgmtMenu).Error; err != nil {
				log.Error("查找汇率管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找汇率理菜单失败: %w", err)
			}

			exchangeRateMgmtButtons := []entity.Menu{
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "fin:exchangerate:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "新增税率", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "fin:exchangerate:add", Sort: 20, Hidden: false, Title: "新增税率"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "修改汇率", Type: 3, Icon: "Edit", Status: 1, Permission: "fin:exchangerate:edit", Sort: 30, Hidden: false, Title: "修改汇率"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "删除汇率", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "fin:exchangerate:delete", Sort: 40, Hidden: false, Title: "删除汇率"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "fin:exchangerate:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "筛选汇率", Type: 3, Icon: "Filter", Status: 1, Permission: "fin:exchangerate:search", Sort: 60, Hidden: false, Title: "筛选汇率"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "导入汇率", Type: 3, Icon: "Upload", Status: 1, Permission: "fin:exchangerate:import", Sort: 70, Hidden: false, Title: "导入汇率"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "导出汇率", Type: 3, Icon: "Download", Status: 1, Permission: "fin:exchangerate:export", Sort: 80, Hidden: false, Title: "导出汇率"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "fin:exchangerate:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &exchangeRateMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "fin:exchangerate:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&exchangeRateMgmtButtons).Error; err != nil {
				log.Error("创建汇率管理按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建汇率管理按钮权限失败: %w", err)
			}
			log.Info("汇率管理按钮权限种子数据填充成功")

			// --- 4.7 创建会计期间按钮权限 ---
			log.Debug("创建会计期间按钮权限...")
			var fiscalPeriodMgmtMenu entity.Menu
			// 查询 "系统参数" 菜单的 ID
			if err := tx.Where("path = ?", "/fin/periods").First(&fiscalPeriodMgmtMenu).Error; err != nil {
				log.Error("查找会计期间管理菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找会计期间理菜单失败: %w", err)
			}

			fiscalPeriodMgmtButtons := []entity.Menu{
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "fin:periods:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "生成期间", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "fin:periods:generate", Sort: 20, Hidden: false, Title: "生成期间"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "开启期间", Type: 3, Icon: "Edit", Status: 1, Permission: "fin:periods:open", Sort: 30, Hidden: false, Title: "开启期间"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "关闭期间", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "fin:periods:close", Sort: 40, Hidden: false, Title: "关闭期间"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "筛选期间", Type: 3, Icon: "Filter", Status: 1, Permission: "fin:periods:search", Sort: 60, Hidden: false, Title: "筛选期间"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "导入期间", Type: 3, Icon: "Upload", Status: 1, Permission: "fin:periods:import", Sort: 70, Hidden: false, Title: "导入期间"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "导出期间", Type: 3, Icon: "Download", Status: 1, Permission: "fin:periods:export", Sort: 80, Hidden: false, Title: "导出期间"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "打印预览", Type: 3, Icon: "View", Status: 1, Permission: "fin:periods:printpreview", Sort: 90, Hidden: false, Title: "打印预览"},
				{ParentID: &fiscalPeriodMgmtMenu.ID, Name: "打印", Type: 3, Icon: "Printer", Status: 1, Permission: "fin:periods:print", Sort: 100, Hidden: false, Title: "打印"},
			}

			if err := tx.Create(&fiscalPeriodMgmtButtons).Error; err != nil {
				log.Error("创建会计期间按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建会计期间按钮权限失败: %w", err)
			}
			log.Info("会计期间按钮权限种子数据填充成功")

			wmsInboundMenu := entity.Menu{
				ParentID:   &wmsMenu.ID, // Removed or set to nil implicitly
				Name:       "入库管理",
				Type:       1,
				Path:       "/wms/inbound",
				Sort:       100,
				Status:     1,
				Permission: "wms:inbound:list",
				Hidden:     false,
				Title:      "入库管理",
				Icon:       "Folder",
			}

			if err := tx.Create(&wmsInboundMenu).Error; err != nil {
				log.Error("创建仓库管理菜单失败", logger.WithError(err))
				return fmt.Errorf("创建仓库管理菜单失败: %w", err)
			}

			// 定义并创建子菜单
			childMenus2 := []entity.Menu{
				// 入库管理子菜单 (Use pointer for ParentID: &wmsInboundMenu.ID)
				{ParentID: &wmsInboundMenu.ID, Name: "入库通知单", Type: 2, Icon: "Document", Path: "/wms/inbound", Sort: 10, Status: 1, Hidden: false, Title: "入库通知单", Component: "wms/inbound-notification/index", Permission: "wms:inbound:notification:list"},
				{ParentID: &wmsInboundMenu.ID, Name: "收货记录单", Type: 2, Icon: "Document", Path: "/wms/receiving", Sort: 20, Status: 1, Hidden: false, Title: "收货记录单", Component: "wms/receiving-record/index", Permission: "wms:inbound:receiving:list"},
				{ParentID: &wmsInboundMenu.ID, Name: "上架任务单", Type: 2, Icon: "Document", Path: "/wms/putway", Sort: 30, Status: 1, Hidden: false, Title: "上架任务单", Component: "wms/putaway-task/index", Permission: "wms:inbound:putaway:list"},
			}

			if err := tx.Create(&childMenus2).Error; err != nil {
				log.Error("创建入库管理子菜单失败", logger.WithError(err))
				return fmt.Errorf("创建入库管理子菜单失败: %w", err)
			}
			log.Info("入库管理菜单种子数据填充成功")

			// 创建入库配置目录
			wmsInboundConfigMenu := entity.Menu{
				ParentID:   &wmsInboundMenu.ID,
				Name:       "入库配置",
				Type:       1,
				Path:       "/wms/inbound/config",
				Sort:       40,
				Status:     1,
				Permission: "",
				Hidden:     false,
				Title:      "入库配置",
				Icon:       "Folder",
			}

			if err := tx.Create(&wmsInboundConfigMenu).Error; err != nil {
				log.Error("创建入库配置菜单失败", logger.WithError(err))
				return fmt.Errorf("创建入库配置菜单失败: %w", err)
			}

			// 创建盲收相关子菜单
			blindReceivingMenus := []entity.Menu{
				{
					ParentID:   &wmsInboundConfigMenu.ID,
					Name:       "盲收配置",
					Type:       2,
					Icon:       "Document",
					Path:       "/wms/inbound/config/blind-receiving-config",
					Sort:       10,
					Status:     1,
					Hidden:     false,
					Title:      "盲收配置",
					Component:  "wms/blind-receiving-config/index",
					Permission: "wms:blind-receiving-config:list",
				},
				{
					ParentID:   &wmsInboundConfigMenu.ID,
					Name:       "盲收验证记录",
					Type:       2,
					Icon:       "Document",
					Path:       "/wms/inbound/config/blind-receiving-validation",
					Sort:       20,
					Status:     1,
					Hidden:     false,
					Title:      "盲收验证记录",
					Component:  "wms/blind-receiving-validation/index",
					Permission: "wms:blind-receiving-validation:list",
				},
			}

			if err := tx.Create(&blindReceivingMenus).Error; err != nil {
				log.Error("创建盲收菜单失败", logger.WithError(err))
				return fmt.Errorf("创建盲收菜单失败: %w", err)
			}
			log.Info("盲收菜单种子数据填充成功")

			// --- 4.10 创建盲收配置按钮权限 ---
			log.Debug("创建盲收配置按钮权限...")
			var blindConfigMenu entity.Menu
			if err := tx.Where("path = ?", "/wms/inbound/blind-receiving-config").First(&blindConfigMenu).Error; err != nil {
				log.Error("查找盲收配置菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找盲收配置菜单失败: %w", err)
			}

			blindConfigButtons := []entity.Menu{
				{ParentID: &blindConfigMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "wms:blind-receiving-config:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &blindConfigMenu.ID, Name: "新增配置", Type: 3, Icon: "DocumentAdd", Status: 1, Permission: "wms:blind-receiving-config:add", Sort: 20, Hidden: false, Title: "新增配置"},
				{ParentID: &blindConfigMenu.ID, Name: "修改配置", Type: 3, Icon: "Edit", Status: 1, Permission: "wms:blind-receiving-config:edit", Sort: 30, Hidden: false, Title: "修改配置"},
				{ParentID: &blindConfigMenu.ID, Name: "删除配置", Type: 3, Icon: "DocumentDelete", Status: 1, Permission: "wms:blind-receiving-config:delete", Sort: 40, Hidden: false, Title: "删除配置"},
				{ParentID: &blindConfigMenu.ID, Name: "批量删除", Type: 3, Icon: "Delete", Status: 1, Permission: "wms:blind-receiving-config:batchdelete", Sort: 50, Hidden: false, Title: "批量删除"},
				{ParentID: &blindConfigMenu.ID, Name: "筛选配置", Type: 3, Icon: "Filter", Status: 1, Permission: "wms:blind-receiving-config:search", Sort: 60, Hidden: false, Title: "筛选配置"},
				{ParentID: &blindConfigMenu.ID, Name: "导入配置", Type: 3, Icon: "Upload", Status: 1, Permission: "wms:blind-receiving-config:import", Sort: 70, Hidden: false, Title: "导入配置"},
				{ParentID: &blindConfigMenu.ID, Name: "导出配置", Type: 3, Icon: "Download", Status: 1, Permission: "wms:blind-receiving-config:export", Sort: 80, Hidden: false, Title: "导出配置"},
			}

			if err := tx.Create(&blindConfigButtons).Error; err != nil {
				log.Error("创建盲收配置按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建盲收配置按钮权限失败: %w", err)
			}
			log.Info("盲收配置按钮权限种子数据填充成功")

			// --- 4.11 创建盲收验证记录按钮权限 ---
			log.Debug("创建盲收验证记录按钮权限...")
			var blindValidationMenu entity.Menu
			if err := tx.Where("path = ?", "/wms/inbound/blind-receiving-validation").First(&blindValidationMenu).Error; err != nil {
				log.Error("查找盲收验证记录菜单失败，无法添加按钮权限", logger.WithError(err))
				return fmt.Errorf("查找盲收验证记录菜单失败: %w", err)
			}

			blindValidationButtons := []entity.Menu{
				{ParentID: &blindValidationMenu.ID, Name: "浏览列表", Type: 3, Icon: "Memo", Status: 1, Permission: "wms:blind-receiving-validation:list", Sort: 10, Hidden: false, Title: "浏览列表"},
				{ParentID: &blindValidationMenu.ID, Name: "查看详情", Type: 3, Icon: "View", Status: 1, Permission: "wms:blind-receiving-validation:view", Sort: 20, Hidden: false, Title: "查看详情"},
				{ParentID: &blindValidationMenu.ID, Name: "处理验证", Type: 3, Icon: "Edit", Status: 1, Permission: "wms:blind-receiving-validation:process", Sort: 30, Hidden: false, Title: "处理验证"},
				{ParentID: &blindValidationMenu.ID, Name: "筛选记录", Type: 3, Icon: "Filter", Status: 1, Permission: "wms:blind-receiving-validation:search", Sort: 40, Hidden: false, Title: "筛选记录"},
				{ParentID: &blindValidationMenu.ID, Name: "导出记录", Type: 3, Icon: "Download", Status: 1, Permission: "wms:blind-receiving-validation:export", Sort: 50, Hidden: false, Title: "导出记录"},
			}

			if err := tx.Create(&blindValidationButtons).Error; err != nil {
				log.Error("创建盲收验证记录按钮权限失败", logger.WithError(err))
				return fmt.Errorf("创建盲收验证记录按钮权限失败: %w", err)
			}
			log.Info("盲收验证记录按钮权限种子数据填充成功")

			// --- 4.9 将所有菜单权限分配给管理员角色 ---
			log.Debug("更新管理员角色的菜单权限...")
			var allMenuIDs []uint
			// 查询所有非软删除的菜单ID
			if err := tx.Model(&entity.Menu{}).Pluck("id", &allMenuIDs).Error; err != nil {
				log.Error("查询所有菜单ID失败", logger.WithError(err))
				return fmt.Errorf("查询所有菜单ID失败: %w", err)
			}
			// 使用 Replace 替换管理员角色的所有菜单关联
			if len(allMenuIDs) > 0 {
				menusToAssign := make([]entity.Menu, len(allMenuIDs))
				for i, id := range allMenuIDs {
					menusToAssign[i].ID = id
				}
				if err := tx.Model(&adminRole).Association("Menus").Replace(menusToAssign); err != nil {
					log.Error("将所有菜单关联到管理员角色失败", logger.WithError(err))
					return fmt.Errorf("将所有菜单关联到管理员角色失败: %w", err)
				}
				log.Info("所有菜单已分配给管理员角色")
			}

		} // End of else block for seeding data

		// --- 5: 创建种子字典数据 ---
		log.Debug("检查并创建种子字典数据...")
		var dictTypeCount int64
		// 检查一个关键字典类型是否存在，例如 sys_status
		tx.Model(&entity.DictionaryType{}).Where("code = ?", "status").Count(&dictTypeCount)

		if dictTypeCount > 0 {
			log.Info("字典类型数据 (Code='status') 已存在，跳过字典种子数据填充")
		} else {
			log.Info("开始填充字典种子数据...")

			// --- 5.1 创建字典类型 ---
			statusType := entity.DictionaryType{
				Code:     "status",
				Name:     "状态",
				Status:   1,
				IsSystem: true,
				Remark:   "系统通用的启用/禁用状态",
			}
			genderType := entity.DictionaryType{
				Code:     "gender",
				Name:     "性别",
				Status:   1,
				IsSystem: true,
				Remark:   "用户的性别",
			}

			if err := tx.Create(&statusType).Error; err != nil {
				log.Error("创建 'status' 字典类型失败", logger.WithError(err))
				return fmt.Errorf("创建 'status' 字典类型失败: %w", err)
			}
			if err := tx.Create(&genderType).Error; err != nil {
				log.Error("创建 'gender' 字典类型失败", logger.WithError(err))
				return fmt.Errorf("创建 'gender' 字典类型失败: %w", err)
			}
			log.Info("字典类型种子数据填充成功")

			// --- 5.2 创建字典项 ---
			statusItems := []entity.DictionaryItem{
				{DictionaryTypeID: statusType.ID, Label: "启用", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
				{DictionaryTypeID: statusType.ID, Label: "禁用", Value: "0", SortOrder: 20, Status: 1, IsSystem: true},
			}
			genderItems := []entity.DictionaryItem{
				{DictionaryTypeID: genderType.ID, Label: "男", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
				{DictionaryTypeID: genderType.ID, Label: "女", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
			}

			if err := tx.Create(&statusItems).Error; err != nil {
				log.Error("创建 'status' 字典项失败", logger.WithError(err))
				return fmt.Errorf("创建 'status' 字典项失败: %w", err)
			}
			if err := tx.Create(&genderItems).Error; err != nil {
				log.Error("创建 'gender' 字典项失败", logger.WithError(err))
				return fmt.Errorf("创建 'gender' 字典项失败: %w", err)
			}
			log.Info("字典项种子数据填充成功")

			// --- 5.3 创建 WMS 计量单位字典类型和项 ---
			log.Debug("创建 WMS 计量单位字典类型和项...")
			unitType := entity.DictionaryType{
				Code:     "unit",
				Name:     "计量单位",
				Status:   1,
				IsSystem: true,
				Remark:   "WMS 系统中使用的物品计量单位",
				// TenantID 假设由 TenantEntity 自动处理或在此处显式设置
			}
			if err := tx.Create(&unitType).Error; err != nil {
				log.Error("创建 'unit' 字典类型失败", logger.WithError(err))
				return fmt.Errorf("创建 'unit' 字典类型失败: %w", err)
			}
			log.Info("'unit' 字典类型创建成功")

			unitItems := []entity.DictionaryItem{
				// 基础单位
				{DictionaryTypeID: unitType.ID, Label: "件", Value: "PCS", SortOrder: 10, Status: 1, IsSystem: true, Remark: "基本计量单位，单个物品"},
				{DictionaryTypeID: unitType.ID, Label: "个", Value: "EA", SortOrder: 20, Status: 1, IsSystem: true, Remark: "通用单个计量单位"},
				// 包装单位
				{DictionaryTypeID: unitType.ID, Label: "盒", Value: "BOX", SortOrder: 30, Status: 1, IsSystem: true, Remark: "包装单位：盒"},
				{DictionaryTypeID: unitType.ID, Label: "箱", Value: "CTN", SortOrder: 40, Status: 1, IsSystem: true, Remark: "包装单位：箱"},
				{DictionaryTypeID: unitType.ID, Label: "托", Value: "PLT", SortOrder: 50, Status: 1, IsSystem: true, Remark: "包装/存储单位：托盘"},
				{DictionaryTypeID: unitType.ID, Label: "包", Value: "BAG", SortOrder: 60, Status: 1, IsSystem: true, Remark: "包装单位：包/袋"},
				{DictionaryTypeID: unitType.ID, Label: "瓶", Value: "BTL", SortOrder: 70, Status: 1, IsSystem: true, Remark: "包装单位：瓶"},
				{DictionaryTypeID: unitType.ID, Label: "罐", Value: "CAN", SortOrder: 80, Status: 1, IsSystem: true, Remark: "包装单位：罐"},
				{DictionaryTypeID: unitType.ID, Label: "桶", Value: "DRM", SortOrder: 90, Status: 1, IsSystem: true, Remark: "包装单位：桶"},
				// 重量单位
				{DictionaryTypeID: unitType.ID, Label: "千克", Value: "KG", SortOrder: 100, Status: 1, IsSystem: true, Remark: "重量单位：千克"},
				{DictionaryTypeID: unitType.ID, Label: "克", Value: "G", SortOrder: 110, Status: 1, IsSystem: true, Remark: "重量单位：克"},
				{DictionaryTypeID: unitType.ID, Label: "吨", Value: "T", SortOrder: 120, Status: 1, IsSystem: true, Remark: "重量单位：吨"},
				// 长度单位
				{DictionaryTypeID: unitType.ID, Label: "米", Value: "M", SortOrder: 130, Status: 1, IsSystem: true, Remark: "长度单位：米"},
				{DictionaryTypeID: unitType.ID, Label: "厘米", Value: "CM", SortOrder: 140, Status: 1, IsSystem: true, Remark: "长度单位：厘米"},
				{DictionaryTypeID: unitType.ID, Label: "毫米", Value: "MM", SortOrder: 150, Status: 1, IsSystem: true, Remark: "长度单位：毫米"},
				// 体积/容积单位
				{DictionaryTypeID: unitType.ID, Label: "升", Value: "L", SortOrder: 160, Status: 1, IsSystem: true, Remark: "容积单位：升"},
				{DictionaryTypeID: unitType.ID, Label: "毫升", Value: "ML", SortOrder: 170, Status: 1, IsSystem: true, Remark: "容积单位：毫升"},
				{DictionaryTypeID: unitType.ID, Label: "立方米", Value: "M3", SortOrder: 180, Status: 1, IsSystem: true, Remark: "体积单位：立方米"},
			}
			if err := tx.Create(&unitItems).Error; err != nil {
				log.Error("创建 'unit' 字典项失败", logger.WithError(err))
				return fmt.Errorf("创建 'unit' 字典项失败: %w", err)
			}
			log.Info("'unit' 字典项种子数据填充成功")

		} // end of else block for seeding dictionary data

		// --- 6. 创建员工模块相关的种子字典数据 ---
		log.Debug("检查并创建员工模块相关的种子字典数据...")

		employeeDictTypesAndItems := []struct {
			TypeCode     string
			TypeName     string
			TypeRemark   string
			TypeStatus   int
			TypeIsSystem bool
			Items        []entity.DictionaryItem
		}{
			// 已移除 emp_gender，假设复用现有的全局 gender 字典
			{
				TypeCode:     "emp_status",
				TypeName:     "员工状态",
				TypeRemark:   "员工当前的在职状态",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "试用", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "正式", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "离职", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "退休", Value: "4", SortOrder: 40, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_education",
				TypeName:     "员工学历",
				TypeRemark:   "员工的最高学历水平",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "小学", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "初中", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "高中", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "大专", Value: "4", SortOrder: 40, Status: 1, IsSystem: true},
					{Label: "本科", Value: "5", SortOrder: 50, Status: 1, IsSystem: true},
					{Label: "硕士", Value: "6", SortOrder: 60, Status: 1, IsSystem: true},
					{Label: "博士", Value: "7", SortOrder: 70, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_marital_status",
				TypeName:     "员工婚姻状况",
				TypeRemark:   "员工当前的婚姻状态",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "未婚", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "已婚", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "离异", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "丧偶", Value: "4", SortOrder: 40, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_political_status",
				TypeName:     "员工政治面貌",
				TypeRemark:   "员工的政治面貌情况",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "群众", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "共青团员", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "中共党员", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_id_card_type",
				TypeName:     "员工证件类型",
				TypeRemark:   "员工所持证件的类型",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "居民身份证", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "护照", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "军官证", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_work_type",
				TypeName:     "员工用工形式",
				TypeRemark:   "员工的用工性质",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "全职", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "兼职", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "实习", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_health_status",
				TypeName:     "员工健康状况",
				TypeRemark:   "员工当前的健康情况",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "健康", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "良好", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "一般", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_nation",
				TypeName:     "民族",
				TypeRemark:   "员工所属民族",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "汉族", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "蒙古族", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "回族", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "藏族", Value: "4", SortOrder: 40, Status: 1, IsSystem: true},
					{Label: "维吾尔族", Value: "5", SortOrder: 50, Status: 1, IsSystem: true},
					{Label: "苗族", Value: "6", SortOrder: 60, Status: 1, IsSystem: true},
					{Label: "彝族", Value: "7", SortOrder: 70, Status: 1, IsSystem: true},
					{Label: "壮族", Value: "8", SortOrder: 80, Status: 1, IsSystem: true},
					{Label: "布依族", Value: "9", SortOrder: 90, Status: 1, IsSystem: true},
					{Label: "朝鲜族", Value: "10", SortOrder: 100, Status: 1, IsSystem: true},
					{Label: "满族", Value: "11", SortOrder: 110, Status: 1, IsSystem: true},
					{Label: "侗族", Value: "12", SortOrder: 120, Status: 1, IsSystem: true},
					{Label: "瑶族", Value: "13", SortOrder: 130, Status: 1, IsSystem: true},
					{Label: "白族", Value: "14", SortOrder: 140, Status: 1, IsSystem: true},
					{Label: "土家族", Value: "15", SortOrder: 150, Status: 1, IsSystem: true},
					{Label: "哈尼族", Value: "16", SortOrder: 160, Status: 1, IsSystem: true},
					{Label: "哈萨克族", Value: "17", SortOrder: 170, Status: 1, IsSystem: true},
					{Label: "傣族", Value: "18", SortOrder: 180, Status: 1, IsSystem: true},
					{Label: "黎族", Value: "19", SortOrder: 190, Status: 1, IsSystem: true},
					{Label: "傈僳族", Value: "20", SortOrder: 200, Status: 1, IsSystem: true},
					{Label: "佤族", Value: "21", SortOrder: 210, Status: 1, IsSystem: true},
					{Label: "畲族", Value: "22", SortOrder: 220, Status: 1, IsSystem: true},
					{Label: "高山族", Value: "23", SortOrder: 230, Status: 1, IsSystem: true},
					{Label: "拉祜族", Value: "24", SortOrder: 240, Status: 1, IsSystem: true},
					{Label: "水族", Value: "25", SortOrder: 250, Status: 1, IsSystem: true},
					{Label: "东乡族", Value: "26", SortOrder: 260, Status: 1, IsSystem: true},
					{Label: "纳西族", Value: "27", SortOrder: 270, Status: 1, IsSystem: true},
					{Label: "景颇族", Value: "28", SortOrder: 280, Status: 1, IsSystem: true},
					{Label: "柯尔克孜族", Value: "29", SortOrder: 290, Status: 1, IsSystem: true},
					{Label: "土族", Value: "30", SortOrder: 300, Status: 1, IsSystem: true},
					{Label: "达斡尔族", Value: "31", SortOrder: 310, Status: 1, IsSystem: true},
					{Label: "仫佬族", Value: "32", SortOrder: 320, Status: 1, IsSystem: true},
					{Label: "羌族", Value: "33", SortOrder: 330, Status: 1, IsSystem: true},
					{Label: "布朗族", Value: "34", SortOrder: 340, Status: 1, IsSystem: true},
					{Label: "撒拉族", Value: "35", SortOrder: 350, Status: 1, IsSystem: true},
					{Label: "毛南族", Value: "36", SortOrder: 360, Status: 1, IsSystem: true},
					{Label: "仡佬族", Value: "37", SortOrder: 370, Status: 1, IsSystem: true},
					{Label: "锡伯族", Value: "38", SortOrder: 380, Status: 1, IsSystem: true},
					{Label: "阿昌族", Value: "39", SortOrder: 390, Status: 1, IsSystem: true},
					{Label: "普米族", Value: "40", SortOrder: 400, Status: 1, IsSystem: true},
					{Label: "塔吉克族", Value: "41", SortOrder: 410, Status: 1, IsSystem: true},
					{Label: "怒族", Value: "42", SortOrder: 420, Status: 1, IsSystem: true},
					{Label: "乌孜别克族", Value: "43", SortOrder: 430, Status: 1, IsSystem: true},
					{Label: "俄罗斯族", Value: "44", SortOrder: 440, Status: 1, IsSystem: true},
					{Label: "鄂温克族", Value: "45", SortOrder: 450, Status: 1, IsSystem: true},
					{Label: "德昂族", Value: "46", SortOrder: 460, Status: 1, IsSystem: true},
					{Label: "保安族", Value: "47", SortOrder: 470, Status: 1, IsSystem: true},
					{Label: "裕固族", Value: "48", SortOrder: 480, Status: 1, IsSystem: true},
					{Label: "京族", Value: "49", SortOrder: 490, Status: 1, IsSystem: true},
					{Label: "塔塔尔族", Value: "50", SortOrder: 500, Status: 1, IsSystem: true},
					{Label: "独龙族", Value: "51", SortOrder: 510, Status: 1, IsSystem: true},
					{Label: "鄂伦春族", Value: "52", SortOrder: 520, Status: 1, IsSystem: true},
					{Label: "赫哲族", Value: "53", SortOrder: 530, Status: 1, IsSystem: true},
					{Label: "门巴族", Value: "54", SortOrder: 540, Status: 1, IsSystem: true},
					{Label: "珞巴族", Value: "55", SortOrder: 550, Status: 1, IsSystem: true},
					{Label: "基诺族", Value: "56", SortOrder: 560, Status: 1, IsSystem: true},
					{Label: "其他", Value: "99", SortOrder: 990, Status: 1, IsSystem: true},
					{Label: "外国血统中国籍人士", Value: "100", SortOrder: 1000, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_degree",
				TypeName:     "员工学位",
				TypeRemark:   "员工获得的学位信息",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "无学位", Value: "0", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "学士", Value: "1", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "硕士", Value: "2", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "博士", Value: "3", SortOrder: 40, Status: 1, IsSystem: true},
					{Label: "其他", Value: "99", SortOrder: 990, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_entry_source",
				TypeName:     "员工入职来源",
				TypeRemark:   "员工通过何种渠道或方式加入公司",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "校园招聘", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "社会招聘", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "内部推荐", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "猎头推荐", Value: "4", SortOrder: 40, Status: 1, IsSystem: true},
					{Label: "实习生转正", Value: "5", SortOrder: 50, Status: 1, IsSystem: true},
					{Label: "劳务派遣", Value: "6", SortOrder: 60, Status: 1, IsSystem: true},
					{Label: "公司官网", Value: "7", SortOrder: 70, Status: 1, IsSystem: true},
					{Label: "其他", Value: "99", SortOrder: 990, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_emergency_contact_relationship",
				TypeName:     "紧急联系人关系",
				TypeRemark:   "紧急联系人与员工本人的关系",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "配偶", Value: "1", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "父母", Value: "2", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "子女", Value: "3", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "兄弟姐妹", Value: "4", SortOrder: 40, Status: 1, IsSystem: true},
					{Label: "朋友", Value: "5", SortOrder: 50, Status: 1, IsSystem: true},
					{Label: "同事", Value: "6", SortOrder: 60, Status: 1, IsSystem: true},
					{Label: "其他亲属", Value: "7", SortOrder: 70, Status: 1, IsSystem: true},
					{Label: "其他", Value: "99", SortOrder: 990, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "emp_headhunter_company",
				TypeName:     "猎头公司",
				TypeRemark:   "合作的猎头公司列表",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items:        []entity.DictionaryItem{}, // 初始为空
			},
			{
				TypeCode:     "emp_labor_dispatch_company",
				TypeName:     "劳务公司",
				TypeRemark:   "合作的劳务派遣公司列表",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items:        []entity.DictionaryItem{}, // 初始为空
			},
			{
				TypeCode:     "emp_campus_recruitment_school",
				TypeName:     "招聘院校",
				TypeRemark:   "进行校园招聘的院校列表",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items:        []entity.DictionaryItem{}, // 初始为空
			},
			{
				TypeCode:     "emp_job_grade",
				TypeName:     "员工职级",
				TypeRemark:   "公司内部的职级体系",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items:        []entity.DictionaryItem{}, // 初始为空
			},
			{
				TypeCode:     "emp_job_sub_level",
				TypeName:     "员工职等",
				TypeRemark:   "员工在特定职级内的细分等级，用于更精确的薪资或发展阶梯划分",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items:        []entity.DictionaryItem{}, // 初始为空
			},
			{
				TypeCode:     "emp_professional_title",
				TypeName:     "员工职称",
				TypeRemark:   "员工的专业技术职称",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items:        []entity.DictionaryItem{}, // 初始为空
			},
			{
				TypeCode:     "emp_work_category",
				TypeName:     "员工性质",
				TypeRemark:   "员工的工作性质或序列分类",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items:        []entity.DictionaryItem{},
			},
			{
				TypeCode:     "language",
				TypeName:     "语言种类",
				TypeRemark:   "常用语言列表",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "中文 (普通话)", Value: "zh_mandarin", SortOrder: 1, Status: 1, IsSystem: true},
					{Label: "英语", Value: "en", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "法语", Value: "fr", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "西班牙语", Value: "es", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "德语", Value: "de", SortOrder: 40, Status: 1, IsSystem: true},
					{Label: "日语", Value: "ja", SortOrder: 50, Status: 1, IsSystem: true},
					{Label: "韩语", Value: "ko", SortOrder: 60, Status: 1, IsSystem: true},
					{Label: "俄语", Value: "ru", SortOrder: 70, Status: 1, IsSystem: true},
					{Label: "阿拉伯语", Value: "ar", SortOrder: 80, Status: 1, IsSystem: true},
					{Label: "葡萄牙语", Value: "pt", SortOrder: 90, Status: 1, IsSystem: true},
					{Label: "中文 (粤语)", Value: "zh_cantonese", SortOrder: 100, Status: 1, IsSystem: true},
					{Label: "其他", Value: "other", SortOrder: 999, Status: 1, IsSystem: true},
				},
			},
		}

		for _, dt := range employeeDictTypesAndItems {
			var dictTypeCount int64
			tx.Model(&entity.DictionaryType{}).Where("code = ?", dt.TypeCode).Count(&dictTypeCount)
			if dictTypeCount > 0 {
				log.Info("员工模块字典类型 (Code='" + dt.TypeCode + "') 已存在，跳过创建")
				continue
			}

			log.Info("创建员工模块字典类型: "+dt.TypeName, logger.WithField("code", dt.TypeCode))
			newDictType := entity.DictionaryType{
				Code:     dt.TypeCode,
				Name:     dt.TypeName,
				Status:   dt.TypeStatus, // 默认启用
				IsSystem: dt.TypeIsSystem,
				Remark:   dt.TypeRemark,
			}
			if err := tx.Create(&newDictType).Error; err != nil {
				log.Error("创建员工模块字典类型 '"+dt.TypeCode+"' 失败", logger.WithError(err))
				return fmt.Errorf("创建员工模块字典类型 '%s' 失败: %w", dt.TypeCode, err)
			}

			// 准备字典项，并关联新创建的字典类型ID
			for i := range dt.Items {
				dt.Items[i].DictionaryTypeID = newDictType.ID
			}

			// 只有当存在要创建的字典项时，才执行数据库插入操作
			if len(dt.Items) > 0 {
				if err := tx.Create(&dt.Items).Error; err != nil {
					log.Error("创建员工模块字典类型 '"+dt.TypeCode+"' 的字典项失败", logger.WithError(err))
					return fmt.Errorf("创建员工模块字典类型 '%s' 的字典项失败: %w", dt.TypeCode, err)
				}
			}
			log.Info("员工模块字典类型 '" + dt.TypeCode + "' 及其字典项创建成功")
		}
		log.Info("员工模块相关的种子字典数据填充完成 (如果需要)")

		// --- 6.1 创建CRM/SCM相关字典数据 ---
		log.Debug("检查并创建CRM/SCM相关字典数据...")

		crmScmDictTypes := []struct {
			TypeCode     string
			TypeName     string
			TypeRemark   string
			TypeStatus   int
			TypeIsSystem bool
			Items        []entity.DictionaryItem
		}{
			{
				TypeCode:     "customer_type",
				TypeName:     "客户类型",
				TypeRemark:   "客户的类型分类",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "企业客户", Value: "CORPORATE", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "个人客户", Value: "INDIVIDUAL", SortOrder: 20, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "customer_level",
				TypeName:     "客户级别",
				TypeRemark:   "客户的重要程度级别",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "VIP客户", Value: "VIP", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "金牌客户", Value: "GOLD", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "银牌客户", Value: "SILVER", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "普通客户", Value: "NORMAL", SortOrder: 40, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "customer_status",
				TypeName:     "客户状态",
				TypeRemark:   "客户的当前状态",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "正常", Value: "ACTIVE", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "停用", Value: "INACTIVE", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "黑名单", Value: "BLACKLIST", SortOrder: 30, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "customer_source",
				TypeName:     "客户来源",
				TypeRemark:   "客户的获取渠道",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "电话营销", Value: "PHONE", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "网络推广", Value: "ONLINE", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "朋友介绍", Value: "REFERRAL", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "展会", Value: "EXHIBITION", SortOrder: 40, Status: 1, IsSystem: false},
					{Label: "广告宣传", Value: "ADVERTISING", SortOrder: 50, Status: 1, IsSystem: false},
					{Label: "老客户推荐", Value: "CUSTOMER_REFERRAL", SortOrder: 60, Status: 1, IsSystem: false},
					{Label: "其他", Value: "OTHER", SortOrder: 99, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "credit_rating",
				TypeName:     "信用等级",
				TypeRemark:   "客户/供应商的信用评级",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "AAA级", Value: "AAA", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "AA级", Value: "AA", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "A级", Value: "A", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "BBB级", Value: "BBB", SortOrder: 40, Status: 1, IsSystem: false},
					{Label: "BB级", Value: "BB", SortOrder: 50, Status: 1, IsSystem: false},
					{Label: "B级", Value: "B", SortOrder: 60, Status: 1, IsSystem: false},
					{Label: "CCC级", Value: "CCC", SortOrder: 70, Status: 1, IsSystem: false},
					{Label: "CC级", Value: "CC", SortOrder: 80, Status: 1, IsSystem: false},
					{Label: "C级", Value: "C", SortOrder: 90, Status: 1, IsSystem: false},
					{Label: "D级", Value: "D", SortOrder: 100, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "payment_terms",
				TypeName:     "付款条件",
				TypeRemark:   "客户/供应商的付款方式条件",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "现金付款", Value: "CASH", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "月结30天", Value: "NET30", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "月结60天", Value: "NET60", SortOrder: 30, Status: 1, IsSystem: true},
					{Label: "月结90天", Value: "NET90", SortOrder: 40, Status: 1, IsSystem: true},
					{Label: "预付款", Value: "PREPAID", SortOrder: 50, Status: 1, IsSystem: true},
					{Label: "货到付款", Value: "COD", SortOrder: 60, Status: 1, IsSystem: true},
					{Label: "银行转账", Value: "BANK_TRANSFER", SortOrder: 70, Status: 1, IsSystem: true},
					{Label: "信用证", Value: "LC", SortOrder: 80, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "supplier_type",
				TypeName:     "供应商类型",
				TypeRemark:   "供应商的类型分类",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "企业供应商", Value: "CORPORATE", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "个人供应商", Value: "INDIVIDUAL", SortOrder: 20, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "supplier_level",
				TypeName:     "供应商等级",
				TypeRemark:   "供应商的质量等级",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "A级供应商", Value: "A", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "B级供应商", Value: "B", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "C级供应商", Value: "C", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "D级供应商", Value: "D", SortOrder: 40, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "supplier_status",
				TypeName:     "供应商状态",
				TypeRemark:   "供应商的当前状态",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "正常", Value: "ACTIVE", SortOrder: 10, Status: 1, IsSystem: true},
					{Label: "停用", Value: "INACTIVE", SortOrder: 20, Status: 1, IsSystem: true},
					{Label: "黑名单", Value: "BLACKLIST", SortOrder: 30, Status: 1, IsSystem: true},
				},
			},
			{
				TypeCode:     "supplier_category",
				TypeName:     "供应商分类",
				TypeRemark:   "供应商的业务分类",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "原材料供应商", Value: "RAW_MATERIAL", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "设备供应商", Value: "EQUIPMENT", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "服务供应商", Value: "SERVICE", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "代工供应商", Value: "OEM", SortOrder: 40, Status: 1, IsSystem: false},
					{Label: "物流供应商", Value: "LOGISTICS", SortOrder: 50, Status: 1, IsSystem: false},
					{Label: "办公用品供应商", Value: "OFFICE_SUPPLIES", SortOrder: 60, Status: 1, IsSystem: false},
					{Label: "其他", Value: "OTHER", SortOrder: 99, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "contact_role",
				TypeName:     "联系人角色",
				TypeRemark:   "客户/供应商联系人的业务角色",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "主要联系人", Value: "PRIMARY", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "技术对接人", Value: "TECHNICAL", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "商务对接人", Value: "BUSINESS", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "财务对接人", Value: "FINANCE", SortOrder: 40, Status: 1, IsSystem: false},
					{Label: "质量对接人", Value: "QUALITY", SortOrder: 50, Status: 1, IsSystem: false},
					{Label: "采购对接人", Value: "PROCUREMENT", SortOrder: 60, Status: 1, IsSystem: false},
					{Label: "法务对接人", Value: "LEGAL", SortOrder: 70, Status: 1, IsSystem: false},
					{Label: "决策人", Value: "DECISION_MAKER", SortOrder: 80, Status: 1, IsSystem: false},
					{Label: "其他", Value: "OTHER", SortOrder: 99, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "industry",
				TypeName:     "行业分类",
				TypeRemark:   "客户/供应商所属行业",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "制造业", Value: "MANUFACTURING", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "信息技术", Value: "IT", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "金融服务", Value: "FINANCE", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "零售批发", Value: "RETAIL", SortOrder: 40, Status: 1, IsSystem: false},
					{Label: "建筑房地产", Value: "CONSTRUCTION", SortOrder: 50, Status: 1, IsSystem: false},
					{Label: "物流运输", Value: "LOGISTICS", SortOrder: 60, Status: 1, IsSystem: false},
					{Label: "医疗健康", Value: "HEALTHCARE", SortOrder: 70, Status: 1, IsSystem: false},
					{Label: "教育培训", Value: "EDUCATION", SortOrder: 80, Status: 1, IsSystem: false},
					{Label: "能源化工", Value: "ENERGY", SortOrder: 90, Status: 1, IsSystem: false},
					{Label: "农林牧渔", Value: "AGRICULTURE", SortOrder: 100, Status: 1, IsSystem: false},
					{Label: "其他", Value: "OTHER", SortOrder: 999, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "material_category",
				TypeName:     "物料分类",
				TypeRemark:   "物料的分类管理",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "原材料", Value: "RAW_MATERIAL", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "半成品", Value: "SEMI_FINISHED", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "成品", Value: "FINISHED_GOODS", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "包装材料", Value: "PACKAGING", SortOrder: 40, Status: 1, IsSystem: false},
					{Label: "辅助材料", Value: "AUXILIARY", SortOrder: 50, Status: 1, IsSystem: false},
					{Label: "消耗品", Value: "CONSUMABLES", SortOrder: 60, Status: 1, IsSystem: false},
					{Label: "备件", Value: "SPARE_PARTS", SortOrder: 70, Status: 1, IsSystem: false},
					{Label: "办公用品", Value: "OFFICE_SUPPLIES", SortOrder: 80, Status: 1, IsSystem: false},
					{Label: "工具", Value: "TOOLS", SortOrder: 90, Status: 1, IsSystem: false},
					{Label: "设备", Value: "EQUIPMENT", SortOrder: 100, Status: 1, IsSystem: false},
				},
			},
			{
				TypeCode:     "material_group",
				TypeName:     "物料组",
				TypeRemark:   "物料的组别分类",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "电子元件", Value: "ELECTRONICS", SortOrder: 10, Status: 1, IsSystem: false},
					{Label: "机械零件", Value: "MECHANICAL", SortOrder: 20, Status: 1, IsSystem: false},
					{Label: "化工原料", Value: "CHEMICAL", SortOrder: 30, Status: 1, IsSystem: false},
					{Label: "金属材料", Value: "METAL", SortOrder: 40, Status: 1, IsSystem: false},
					{Label: "塑料制品", Value: "PLASTIC", SortOrder: 50, Status: 1, IsSystem: false},
					{Label: "纺织品", Value: "TEXTILE", SortOrder: 60, Status: 1, IsSystem: false},
					{Label: "食品原料", Value: "FOOD", SortOrder: 70, Status: 1, IsSystem: false},
					{Label: "医药用品", Value: "MEDICAL", SortOrder: 80, Status: 1, IsSystem: false},
					{Label: "建筑材料", Value: "CONSTRUCTION", SortOrder: 90, Status: 1, IsSystem: false},
					{Label: "标准件", Value: "STANDARD", SortOrder: 100, Status: 1, IsSystem: false},
				},
			},
		}

		for _, dt := range crmScmDictTypes {
			var dictTypeCount int64
			tx.Model(&entity.DictionaryType{}).Where("code = ?", dt.TypeCode).Count(&dictTypeCount)
			if dictTypeCount > 0 {
				log.Info("CRM/SCM模块字典类型 (Code='" + dt.TypeCode + "') 已存在，跳过创建")
				continue
			}

			log.Info("创建CRM/SCM模块字典类型: "+dt.TypeName, logger.WithField("code", dt.TypeCode))
			newDictType := entity.DictionaryType{
				Code:     dt.TypeCode,
				Name:     dt.TypeName,
				Status:   dt.TypeStatus,
				IsSystem: dt.TypeIsSystem,
				Remark:   dt.TypeRemark,
			}
			if err := tx.Create(&newDictType).Error; err != nil {
				log.Error("创建CRM/SCM模块字典类型 '"+dt.TypeCode+"' 失败", logger.WithError(err))
				return fmt.Errorf("创建CRM/SCM模块字典类型 '%s' 失败: %w", dt.TypeCode, err)
			}

			// 准备字典项，并关联新创建的字典类型ID
			for i := range dt.Items {
				dt.Items[i].DictionaryTypeID = newDictType.ID
			}

			// 只有当存在要创建的字典项时，才执行数据库插入操作
			if len(dt.Items) > 0 {
				if err := tx.Create(&dt.Items).Error; err != nil {
					log.Error("创建CRM/SCM模块字典类型 '"+dt.TypeCode+"' 的字典项失败", logger.WithError(err))
					return fmt.Errorf("创建CRM/SCM模块字典类型 '%s' 的字典项失败: %w", dt.TypeCode, err)
				}
			}
			log.Info("CRM/SCM模块字典类型 '" + dt.TypeCode + "' 及其字典项创建成功")
		}
		log.Info("CRM/SCM模块相关的种子字典数据填充完成 (如果需要)")

		// --- 6.2 创建WMS库位相关字典数据 ---
		log.Debug("检查并创建WMS库位相关字典数据...")

		wmsLocationDictTypes := []struct {
			TypeCode     string
			TypeName     string
			TypeRemark   string
			TypeStatus   int
			TypeIsSystem bool
			Items        []entity.DictionaryItem
		}{
			{
				TypeCode:     "temperature_zone",
				TypeName:     "温区",
				TypeRemark:   "库位的温度控制分区",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "常温区", Value: "NORMAL", SortOrder: 10, Status: 1, IsSystem: false, Remark: "15-25℃常温存储区域"},
					{Label: "低温区", Value: "LOW_TEMP", SortOrder: 20, Status: 1, IsSystem: false, Remark: "2-8℃低温存储区域"},
					{Label: "冷冻区", Value: "FROZEN", SortOrder: 30, Status: 1, IsSystem: false, Remark: "-18℃以下冷冻存储区域"},
					{Label: "深冷区", Value: "DEEP_FROZEN", SortOrder: 40, Status: 1, IsSystem: false, Remark: "-80℃以下深冷存储区域"},
					{Label: "高温区", Value: "HIGH_TEMP", SortOrder: 50, Status: 1, IsSystem: false, Remark: "35℃以上高温存储区域"},
					{Label: "恒温区", Value: "CONSTANT_TEMP", SortOrder: 60, Status: 1, IsSystem: false, Remark: "恒定温度控制区域"},
				},
			},
			{
				TypeCode:     "danger_level",
				TypeName:     "危险品等级",
				TypeRemark:   "危险品的安全等级分类",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "无危险", Value: "NONE", SortOrder: 10, Status: 1, IsSystem: false, Remark: "普通货品，无特殊危险性"},
					{Label: "一级危险", Value: "LEVEL_1", SortOrder: 20, Status: 1, IsSystem: false, Remark: "爆炸品等高危险品"},
					{Label: "二级危险", Value: "LEVEL_2", SortOrder: 30, Status: 1, IsSystem: false, Remark: "压缩气体和液化气体"},
					{Label: "三级危险", Value: "LEVEL_3", SortOrder: 40, Status: 1, IsSystem: false, Remark: "易燃液体"},
					{Label: "四级危险", Value: "LEVEL_4", SortOrder: 50, Status: 1, IsSystem: false, Remark: "易燃固体、自燃物品和遇湿易燃物品"},
					{Label: "五级危险", Value: "LEVEL_5", SortOrder: 60, Status: 1, IsSystem: false, Remark: "氧化剂和有机过氧化物"},
					{Label: "六级危险", Value: "LEVEL_6", SortOrder: 70, Status: 1, IsSystem: false, Remark: "毒害品和感染性物品"},
					{Label: "七级危险", Value: "LEVEL_7", SortOrder: 80, Status: 1, IsSystem: false, Remark: "放射性物品"},
					{Label: "八级危险", Value: "LEVEL_8", SortOrder: 90, Status: 1, IsSystem: false, Remark: "腐蚀品"},
					{Label: "九级危险", Value: "LEVEL_9", SortOrder: 100, Status: 1, IsSystem: false, Remark: "杂类危险物品"},
				},
			},
			{
				TypeCode:     "safety_level",
				TypeName:     "安全级别",
				TypeRemark:   "库位的安全防护等级",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "普通安全", Value: "NORMAL", SortOrder: 10, Status: 1, IsSystem: false, Remark: "普通安全防护等级"},
					{Label: "高安全", Value: "HIGH", SortOrder: 20, Status: 1, IsSystem: false, Remark: "高等级安全防护，配备监控等设备"},
					{Label: "极高安全", Value: "ULTRA_HIGH", SortOrder: 30, Status: 1, IsSystem: false, Remark: "最高等级安全防护，严格管控"},
					{Label: "防火安全", Value: "FIRE_PROOF", SortOrder: 40, Status: 1, IsSystem: false, Remark: "配备防火设施的安全区域"},
					{Label: "防爆安全", Value: "EXPLOSION_PROOF", SortOrder: 50, Status: 1, IsSystem: false, Remark: "配备防爆设施的安全区域"},
					{Label: "防静电", Value: "ANTI_STATIC", SortOrder: 60, Status: 1, IsSystem: false, Remark: "防静电保护区域"},
				},
			},
			{
				TypeCode:     "load_capacity_level",
				TypeName:     "承重等级",
				TypeRemark:   "库位的承重能力等级",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "轻载", Value: "LIGHT", SortOrder: 10, Status: 1, IsSystem: false, Remark: "≤100kg 轻型货品承重"},
					{Label: "中载", Value: "MEDIUM", SortOrder: 20, Status: 1, IsSystem: false, Remark: "100-500kg 中型货品承重"},
					{Label: "重载", Value: "HEAVY", SortOrder: 30, Status: 1, IsSystem: false, Remark: "500-1000kg 重型货品承重"},
					{Label: "超重载", Value: "SUPER_HEAVY", SortOrder: 40, Status: 1, IsSystem: false, Remark: "1000-5000kg 超重型货品承重"},
					{Label: "特重载", Value: "ULTRA_HEAVY", SortOrder: 50, Status: 1, IsSystem: false, Remark: ">5000kg 特重型货品承重"},
					{Label: "无限制", Value: "UNLIMITED", SortOrder: 60, Status: 1, IsSystem: false, Remark: "承重能力无特殊限制"},
				},
			},
			{
				TypeCode:     "storage_type",
				TypeName:     "存储类型",
				TypeRemark:   "货品的存储方式分类",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "平面存储", Value: "FLAT_STORAGE", SortOrder: 10, Status: 1, IsSystem: false, Remark: "货品平放在地面或货架上"},
					{Label: "堆垛存储", Value: "STACK_STORAGE", SortOrder: 20, Status: 1, IsSystem: false, Remark: "货品堆叠存放"},
					{Label: "托盘存储", Value: "PALLET_STORAGE", SortOrder: 30, Status: 1, IsSystem: false, Remark: "使用托盘进行标准化存储"},
					{Label: "货架存储", Value: "RACK_STORAGE", SortOrder: 40, Status: 1, IsSystem: false, Remark: "使用货架系统存储"},
					{Label: "悬挂存储", Value: "HANGING_STORAGE", SortOrder: 50, Status: 1, IsSystem: false, Remark: "悬挂式存储方式"},
					{Label: "液体罐装", Value: "TANK_STORAGE", SortOrder: 60, Status: 1, IsSystem: false, Remark: "液体货品罐装存储"},
					{Label: "散装存储", Value: "BULK_STORAGE", SortOrder: 70, Status: 1, IsSystem: false, Remark: "散装货品存储"},
					{Label: "密闭存储", Value: "SEALED_STORAGE", SortOrder: 80, Status: 1, IsSystem: false, Remark: "密闭环境存储"},
					{Label: "通风存储", Value: "VENTILATED_STORAGE", SortOrder: 90, Status: 1, IsSystem: false, Remark: "通风环境存储"},
					{Label: "特殊存储", Value: "SPECIAL_STORAGE", SortOrder: 100, Status: 1, IsSystem: false, Remark: "需要特殊条件的存储方式"},
				},
			},
		}

		for _, dt := range wmsLocationDictTypes {
			var dictTypeCount int64
			tx.Model(&entity.DictionaryType{}).Where("code = ?", dt.TypeCode).Count(&dictTypeCount)
			if dictTypeCount > 0 {
				log.Info("WMS库位模块字典类型 (Code='" + dt.TypeCode + "') 已存在，跳过创建")
				continue
			}

			log.Info("创建WMS库位模块字典类型: "+dt.TypeName, logger.WithField("code", dt.TypeCode))
			newDictType := entity.DictionaryType{
				Code:     dt.TypeCode,
				Name:     dt.TypeName,
				Status:   dt.TypeStatus,
				IsSystem: dt.TypeIsSystem,
				Remark:   dt.TypeRemark,
			}
			if err := tx.Create(&newDictType).Error; err != nil {
				log.Error("创建WMS库位模块字典类型 '"+dt.TypeCode+"' 失败", logger.WithError(err))
				return fmt.Errorf("创建WMS库位模块字典类型 '%s' 失败: %w", dt.TypeCode, err)
			}

			// 准备字典项，并关联新创建的字典类型ID
			for i := range dt.Items {
				dt.Items[i].DictionaryTypeID = newDictType.ID
			}

			// 只有当存在要创建的字典项时，才执行数据库插入操作
			if len(dt.Items) > 0 {
				if err := tx.Create(&dt.Items).Error; err != nil {
					log.Error("创建WMS库位模块字典类型 '"+dt.TypeCode+"' 的字典项失败", logger.WithError(err))
					return fmt.Errorf("创建WMS库位模块字典类型 '%s' 的字典项失败: %w", dt.TypeCode, err)
				}
			}
			log.Info("WMS库位模块字典类型 '" + dt.TypeCode + "' 及其字典项创建成功")
		}
		log.Info("WMS库位模块相关的种子字典数据填充完成 (如果需要)")

		// --- 6.3 创建供应商评级相关字典数据 ---
		log.Debug("检查并创建供应商评级相关字典数据...")

		supplierRatingDictTypes := []struct {
			TypeCode     string
			TypeName     string
			TypeRemark   string
			TypeStatus   int
			TypeIsSystem bool
			Items        []entity.DictionaryItem
		}{
			{
				TypeCode:     "delivery_rating",
				TypeName:     "交期评级",
				TypeRemark:   "供应商交期表现评级",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "A级(优秀)", Value: "A", SortOrder: 10, Status: 1, Remark: "交期准确率≥95%，极少延期"},
					{Label: "B级(良好)", Value: "B", SortOrder: 20, Status: 1, Remark: "交期准确率85%-94%，偶有延期"},
					{Label: "C级(一般)", Value: "C", SortOrder: 30, Status: 1, Remark: "交期准确率70%-84%，延期情况较多"},
					{Label: "D级(较差)", Value: "D", SortOrder: 40, Status: 1, Remark: "交期准确率50%-69%，经常延期"},
					{Label: "E级(极差)", Value: "E", SortOrder: 50, Status: 1, Remark: "交期准确率<50%，严重影响生产"},
				},
			},
			{
				TypeCode:     "quality_rating",
				TypeName:     "质量评级",
				TypeRemark:   "供应商产品质量评级",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "A级(优秀)", Value: "A", SortOrder: 10, Status: 1, Remark: "合格率≥99%，质量稳定可靠"},
					{Label: "B级(良好)", Value: "B", SortOrder: 20, Status: 1, Remark: "合格率95%-98%，质量总体良好"},
					{Label: "C级(一般)", Value: "C", SortOrder: 30, Status: 1, Remark: "合格率90%-94%，质量一般"},
					{Label: "D级(较差)", Value: "D", SortOrder: 40, Status: 1, Remark: "合格率80%-89%，质量问题较多"},
					{Label: "E级(极差)", Value: "E", SortOrder: 50, Status: 1, Remark: "合格率<80%，质量问题严重"},
				},
			},
			{
				TypeCode:     "service_rating",
				TypeName:     "服务评级",
				TypeRemark:   "供应商服务质量评级",
				TypeStatus:   1,
				TypeIsSystem: true,
				Items: []entity.DictionaryItem{
					{Label: "A级(优秀)", Value: "A", SortOrder: 10, Status: 1, Remark: "响应迅速，服务主动，解决问题及时"},
					{Label: "B级(良好)", Value: "B", SortOrder: 20, Status: 1, Remark: "响应较快，服务配合度高"},
					{Label: "C级(一般)", Value: "C", SortOrder: 30, Status: 1, Remark: "响应一般，服务态度普通"},
					{Label: "D级(较差)", Value: "D", SortOrder: 40, Status: 1, Remark: "响应缓慢，服务配合度差"},
					{Label: "E级(极差)", Value: "E", SortOrder: 50, Status: 1, Remark: "响应极慢，服务态度恶劣"},
				},
			},
		}

		for _, dt := range supplierRatingDictTypes {
			var dictTypeCount int64
			tx.Model(&entity.DictionaryType{}).Where("code = ?", dt.TypeCode).Count(&dictTypeCount)
			if dictTypeCount > 0 {
				log.Info("供应商评级字典类型 (Code='" + dt.TypeCode + "') 已存在，跳过创建")
				continue
			}

			log.Info("创建供应商评级字典类型: "+dt.TypeName, logger.WithField("code", dt.TypeCode))
			newDictType := entity.DictionaryType{
				Code:     dt.TypeCode,
				Name:     dt.TypeName,
				Status:   dt.TypeStatus,
				IsSystem: dt.TypeIsSystem,
				Remark:   dt.TypeRemark,
			}
			if err := tx.Create(&newDictType).Error; err != nil {
				log.Error("创建供应商评级字典类型 '"+dt.TypeCode+"' 失败", logger.WithError(err))
				return fmt.Errorf("创建供应商评级字典类型 '%s' 失败: %w", dt.TypeCode, err)
			}

			// 准备字典项，并关联新创建的字典类型ID
			for i := range dt.Items {
				dt.Items[i].DictionaryTypeID = newDictType.ID
			}

			// 只有当存在要创建的字典项时，才执行数据库插入操作
			if len(dt.Items) > 0 {
				if err := tx.Create(&dt.Items).Error; err != nil {
					log.Error("创建供应商评级字典类型 '"+dt.TypeCode+"' 的字典项失败", logger.WithError(err))
					return fmt.Errorf("创建供应商评级字典类型 '%s' 的字典项失败: %w", dt.TypeCode, err)
				}
			}
			log.Info("供应商评级字典类型 '" + dt.TypeCode + "' 及其字典项创建成功")
		}
		log.Info("供应商评级相关的种子字典数据填充完成")

		// --- 7. 创建种子币种数据 ---
		log.Debug("检查并创建种子币种数据...")
		var currencyCount int64
		tx.Model(&entity.FinCurrency{}).Where("code = ?", "CNY").Count(&currencyCount)
		if currencyCount > 0 {
			log.Info("币种数据 (Code='CNY') 已存在，跳过币种种子数据填充")
		} else {
			log.Info("开始填充币种种子数据...")
			currencies := []entity.FinCurrency{
				{Code: "CNY", Name: "人民币", Symbol: "¥", Precision: 2, IsEnabled: true},
				{Code: "USD", Name: "美元", Symbol: "$", Precision: 2, IsEnabled: true},
				{Code: "EUR", Name: "欧元", Symbol: "€", Precision: 2, IsEnabled: true},
				{Code: "JPY", Name: "日元", Symbol: "¥", Precision: 0, IsEnabled: true},
				{Code: "GBP", Name: "英镑", Symbol: "£", Precision: 2, IsEnabled: true},
				{Code: "AUD", Name: "澳大利亚元", Symbol: "A$", Precision: 2, IsEnabled: true},
				{Code: "CAD", Name: "加拿大元", Symbol: "C$", Precision: 2, IsEnabled: true},
				{Code: "CHF", Name: "瑞士法郎", Symbol: "CHF", Precision: 2, IsEnabled: true},
				{Code: "HKD", Name: "港元", Symbol: "HK$", Precision: 2, IsEnabled: true},
			}
			if err := tx.Create(&currencies).Error; err != nil {
				log.Error("创建种子币种数据失败", logger.WithError(err))
				return fmt.Errorf("创建种子币种数据失败: %w", err)
			}
			log.Info("种子币种数据填充成功")
		}

		// --- 8. 创建种子税率数据 ---
		log.Debug("检查并创建种子税率数据...")
		var taxRateCount int64
		tx.Model(&entity.FinTaxRate{}).Where("name = ?", "标准税率(13%)").Count(&taxRateCount)
		if taxRateCount > 0 {
			log.Info("税率数据 (Name='标准税率(13%)') 已存在，跳过税率种子数据填充")
		} else {
			log.Info("开始填充税率种子数据...")
			taxRates := []entity.FinTaxRate{
				{Name: "标准税率(13%)", Rate: decimal.NewFromFloat(0.13), Description: "适用于一般商品销售的13%增值税税率", IsEnabled: true},
				{Name: "低税率(9%)", Rate: decimal.NewFromFloat(0.09), Description: "适用于交通运输、建筑、邮政等行业的9%增值税税率", IsEnabled: true},
				{Name: "低税率(6%)", Rate: decimal.NewFromFloat(0.06), Description: "适用于现代服务业的6%增值税税率", IsEnabled: true},
				{Name: "零税率", Rate: decimal.NewFromFloat(0), Description: "适用于出口商品或特定服务的零税率", IsEnabled: true},
				{Name: "简易征收(3%)", Rate: decimal.NewFromFloat(0.03), Description: "适用于小规模纳税人的3%简易征收率", IsEnabled: true},
			}
			if err := tx.Create(&taxRates).Error; err != nil {
				log.Error("创建种子税率数据失败", logger.WithError(err))
				return fmt.Errorf("创建种子税率数据失败: %w", err)
			}
			log.Info("种子税率数据填充成功")
		}

		// --- 9. 创建编码格式组件种子数据 ---
		log.Debug("检查并创建编码格式组件种子数据...")
		var componentCount int64
		tx.Model(&entity.SysCodeFormatComponent{}).Where("component_code = ?", "FIXED").Count(&componentCount)
		if componentCount > 0 {
			log.Info("编码格式组件数据已存在，跳过种子数据填充")
		} else {
			log.Info("开始填充编码格式组件种子数据...")

			// 定义默认组件数据
			components := []entity.SysCodeFormatComponent{
				{
					ComponentCode: "FIXED",
					ComponentName: "固定字符",
					ComponentType: "FIXED",
					FormatPattern: nil,
					Description:   ptrString("固定的字符串"),
					ExampleValue:  ptrString("CRM"),
					Status:        "ACTIVE",
				},
				{
					ComponentCode: "YYYY",
					ComponentName: "四位年份",
					ComponentType: "DATE",
					FormatPattern: ptrString("YYYY"),
					Description:   ptrString("当前年份四位数"),
					ExampleValue:  ptrString("2024"),
					Status:        "ACTIVE",
				},
				{
					ComponentCode: "YY",
					ComponentName: "两位年份",
					ComponentType: "DATE",
					FormatPattern: ptrString("YY"),
					Description:   ptrString("当前年份两位数"),
					ExampleValue:  ptrString("24"),
					Status:        "ACTIVE",
				},
				{
					ComponentCode: "MM",
					ComponentName: "两位月份",
					ComponentType: "DATE",
					FormatPattern: ptrString("MM"),
					Description:   ptrString("当前月份两位数"),
					ExampleValue:  ptrString("03"),
					Status:        "ACTIVE",
				},
				{
					ComponentCode: "DD",
					ComponentName: "两位日期",
					ComponentType: "DATE",
					FormatPattern: ptrString("DD"),
					Description:   ptrString("当前日期两位数"),
					ExampleValue:  ptrString("15"),
					Status:        "ACTIVE",
				},
				{
					ComponentCode: "SEQ",
					ComponentName: "序列号",
					ComponentType: "SEQUENCE",
					FormatPattern: ptrString("SEQ"),
					Description:   ptrString("自增序列号"),
					ExampleValue:  ptrString("0001"),
					Status:        "ACTIVE",
				},
				{
					ComponentCode: "FIELD",
					ComponentName: "字段值",
					ComponentType: "FIELD",
					FormatPattern: ptrString("FIELD"),
					Description:   ptrString("来自业务字段的值"),
					ExampleValue:  ptrString("CORP"),
					Status:        "ACTIVE",
				},
			}

			if err := tx.Create(&components).Error; err != nil {
				log.Error("创建编码格式组件种子数据失败", logger.WithError(err))
				return fmt.Errorf("创建编码格式组件种子数据失败: %w", err)
			}
			log.Info("编码格式组件种子数据填充成功")
		}

		// --- 10. 创建系统参数种子数据 ---
		log.Debug("检查并创建系统参数种子数据...")
		var paramCount int64
		tx.Model(&entity.SystemParameter{}).Where("param_key = ?", "TableColumn.showOverflowTooltip").Count(&paramCount)
		if paramCount > 0 {
			log.Info("系统参数 'TableColumn.showOverflowTooltip' 已存在，跳过创建")
		} else {
			log.Info("创建系统参数 'TableColumn.showOverflowTooltip'...")
			detailRowWrapParam := entity.SystemParameter{
				ParamKey:   "TableColumn.showOverflowTooltip",
				ParamValue: "true",
				Name:       "表格内单元格不自动换行",
				Remark:     "控制明细表格中的单元格内容是否自动换行。true为单行显示，false为多行显示。",
				Status:     1,
				IsSystem:   true,
				ValueType:  "boolean",
			}
			if err := tx.Create(&detailRowWrapParam).Error; err != nil {
				log.Error("创建系统参数 'TableColumn.showOverflowTooltip' 失败", logger.WithError(err))
				return fmt.Errorf("创建系统参数 'TableColumn.showOverflowTooltip' 失败: %w", err)
			}
			log.Info("系统参数 'TableColumn.showOverflowTooltip' 创建成功")
		}

		// --- 11. 创建自动获取汇率系统参数 ---
		tx.Model(&entity.SystemParameter{}).Where("param_key = ?", "ExchangeRate.AutoFetch.Enabled").Count(&paramCount)
		if paramCount > 0 {
			log.Info("系统参数 'ExchangeRate.AutoFetch.Enabled' 已存在，跳过创建")
		} else {
			log.Info("创建系统参数 'ExchangeRate.AutoFetch.Enabled'...")
			autoFetchExchangeRateParam := entity.SystemParameter{
				ParamKey:   "ExchangeRate.AutoFetch.Enabled",
				ParamValue: "false",
				Name:       "自动获取汇率数据",
				Remark:     "控制系统是否在启动时或按计划自动从外部API获取最新的汇率数据。启用此功能可能需要配置API密钥。",
				Status:     1,
				IsSystem:   true,
				ValueType:  "boolean",
			}
			if err := tx.Create(&autoFetchExchangeRateParam).Error; err != nil {
				log.Error("创建系统参数 'ExchangeRate.AutoFetch.Enabled' 失败", logger.WithError(err))
				return fmt.Errorf("创建系统参数 'ExchangeRate.AutoFetch.Enabled' 失败: %w", err)
			}
			log.Info("系统参数 'ExchangeRate.AutoFetch.Enabled' 创建成功")
		}

		// --- 12. 创建汇率API配置参数 ---
		tx.Model(&entity.SystemParameter{}).Where("param_key = ?", "ExchangeRate.Api.Config").Count(&paramCount)
		if paramCount > 0 {
			log.Info("系统参数 'ExchangeRate.Api.Config' 已存在，跳过创建")
		} else {
			log.Info("创建系统参数 'ExchangeRate.Api.Config'...")
			// 使用单行JSON字符串以避免多行字符串问题
			apiConfigJSON := `[{"providerName":"exchangerate-api.com","apiUrl":"https://v6.exchangerate-api.com/v6","apiKey":"","baseCurrency":"USD","timeout":10,"enabled":true}]`
			apiConfigParam := entity.SystemParameter{
				ParamKey:   "ExchangeRate.Api.Config",
				ParamValue: apiConfigJSON,
				Name:       "汇率服务API配置",
				Remark:     "用于自动获取汇率的第三方API配置(JSON数组格式)。字段包括: providerName, apiUrl, apiKey, baseCurrency, timeout, enabled。",
				Status:     1,
				IsSystem:   true,
				ValueType:  "json",
			}
			if err := tx.Create(&apiConfigParam).Error; err != nil {
				log.Error("创建系统参数 'ExchangeRate.Api.Config' 失败", logger.WithError(err))
				return fmt.Errorf("创建系统参数 'ExchangeRate.Api.Config' 失败: %w", err)
			}
			log.Info("系统参数 'ExchangeRate.Api.Config' 创建成功")
		}

		return nil // <<< 事务成功，确保字典数据填充逻辑在此行之前 >>>
	})
}

// initMySQL 初始化 MySQL 连接并设置管理器
func initMySQL(cfg *config.MySQLConfig) (DBManager, error) {
	log := logger.GetLogger()
	log.Info("创建 MySQL 管理器...")
	// 注意：NewMySQLManager 现在返回 DBManager 接口类型
	dbManagerInterface := NewMySQLManager(cfg)

	log.Info("连接 MySQL 数据库 (将处理数据库创建)...")
	// Connect 方法现在应该在 DBManager 接口上调用，并接收两个返回值
	_, err := dbManagerInterface.Connect() // _ 忽略返回的 gorm.DB，因为稍后会通过 GetDB 获取
	if err != nil {
		return nil, fmt.Errorf("连接 MySQL 失败: %w", err)
	}
	log.Info("MySQL 连接成功")

	// 获取 GORM DB 实例
	gormDB := dbManagerInterface.GetDB()
	if gormDB == nil {
		return nil, errors.New("连接后无法获取 MySQL GORM DB 实例")
	}

	// 创建事务管理器
	log.Debug("创建 MySQL 事务管理器...")
	txManager := NewTransactionManager(gormDB)

	// 类型断言以获取具体类型并设置事务管理器
	mysqlManager, ok := dbManagerInterface.(*MySQLManager)
	if !ok {
		// 这理论上不应发生，因为 NewMySQLManager 返回 *MySQLManager
		return nil, errors.New("无法将 DBManager 断言为 *MySQLManager")
	}
	mysqlManager.SetTransactionManager(txManager)
	log.Info("MySQL 管理器和事务管理器设置完成")

	return mysqlManager, nil // 返回设置好的具体管理器（仍然满足 DBManager 接口）
}

// initPostgreSQL 初始化 PostgreSQL 连接并设置管理器
func initPostgreSQL(cfg *config.PostgreSQLConfig) (DBManager, error) {
	log := logger.GetLogger()
	log.Info("创建 PostgreSQL 管理器...")
	// 注意：NewPostgreSQLManager 现在返回 DBManager 接口类型
	dbManagerInterface := NewPostgreSQLManager(cfg)

	log.Info("连接 PostgreSQL 数据库 (将处理数据库创建)...")
	// Connect 方法现在应该在 DBManager 接口上调用，并接收两个返回值
	_, err := dbManagerInterface.Connect() // _ 忽略返回的 gorm.DB
	if err != nil {
		return nil, fmt.Errorf("连接 PostgreSQL 失败: %w", err)
	}
	log.Info("PostgreSQL 连接成功")

	// 获取 GORM DB 实例
	gormDB := dbManagerInterface.GetDB()
	if gormDB == nil {
		return nil, errors.New("连接后无法获取 PostgreSQL GORM DB 实例")
	}

	// 创建事务管理器
	log.Debug("创建 PostgreSQL 事务管理器...")
	txManager := NewTransactionManager(gormDB)

	// 类型断言以获取具体类型并设置事务管理器
	pgManager, ok := dbManagerInterface.(*PostgreSQLManager)
	if !ok {
		// 这理论上不应发生，因为 NewPostgreSQLManager 返回 *PostgreSQLManager
		return nil, errors.New("无法将 DBManager 断言为 *PostgreSQLManager")
	}
	pgManager.SetTransactionManager(txManager)
	log.Info("PostgreSQL 管理器和事务管理器设置完成")

	return pgManager, nil // 返回设置好的具体管理器（仍然满足 DBManager 接口）
}
