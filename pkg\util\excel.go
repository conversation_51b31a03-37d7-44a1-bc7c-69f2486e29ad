package util

import (
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
)

// Excel相关常量
const (
	EXCEL_FORMAT_XLS    = ".xls"      // Excel 97-2003格式
	EXCEL_FORMAT_XLSX   = ".xlsx"     // Excel 2007+格式
	EXCEL_DEFAULT_SHEET = "Sheet1"    // 默认工作表名
	EXCEL_MAX_ROWS      = 1048576     // Excel最大行数
	EXCEL_MAX_COLS      = 16384       // Excel最大列数
	EXCEL_BUFFER_SIZE   = 1024 * 1024 // Excel文件读取缓冲区大小
)

// ExcelWriter Excel写入器
type ExcelWriter struct {
	file      *excelize.File // Excel文件对象
	sheetName string         // 当前工作表名
	headers   []string       // 表头
	styles    map[string]int // 样式映射
	rowIndex  int            // 当前行索引
}

// ExcelReader Excel读取器
type ExcelReader struct {
	file      *excelize.File // Excel文件对象
	sheetName string         // 当前工作表名
	headers   []string       // 表头
	rowCount  int            // 总行数
	colCount  int            // 总列数
}

// NewExcelWriter 创建Excel写入器
func NewExcelWriter() *ExcelWriter {
	file := excelize.NewFile()
	writer := &ExcelWriter{
		file:      file,
		sheetName: EXCEL_DEFAULT_SHEET,
		headers:   []string{},
		styles:    make(map[string]int),
		rowIndex:  1,
	}
	return writer
}

// SetSheetName 设置工作表名
func (w *ExcelWriter) SetSheetName(sheetName string) *ExcelWriter {
	if sheetName != EXCEL_DEFAULT_SHEET {
		// 创建新的工作表
		w.file.NewSheet(sheetName)
		// 删除默认的Sheet1
		w.file.DeleteSheet(EXCEL_DEFAULT_SHEET)
	}
	w.sheetName = sheetName
	return w
}

// SetHeaders 设置表头
func (w *ExcelWriter) SetHeaders(headers []string) *ExcelWriter {
	w.headers = headers

	// 创建表头样式
	headerStyle, err := w.file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "Arial",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#DDEBF7"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})

	if err == nil {
		w.styles["header"] = headerStyle
	}

	// 写入表头
	for i, header := range headers {
		cell := ToExcelColumn(i+1) + "1"
		w.file.SetCellValue(w.sheetName, cell, header)

		if err == nil {
			w.file.SetCellStyle(w.sheetName, cell, cell, headerStyle)
		}
	}

	w.rowIndex = 2 // 从第2行开始写入数据
	return w
}

// WriteRow 写入一行数据
func (w *ExcelWriter) WriteRow(rowData []interface{}) *ExcelWriter {
	for i, data := range rowData {
		colLetter := ToExcelColumn(i + 1)
		cellName := fmt.Sprintf("%s%d", colLetter, w.rowIndex)
		w.file.SetCellValue(w.sheetName, cellName, data)
	}
	w.rowIndex++
	return w
}

// WriteRows 写入多行数据
func (w *ExcelWriter) WriteRows(rowsData [][]interface{}) *ExcelWriter {
	for _, rowData := range rowsData {
		w.WriteRow(rowData)
	}
	return w
}

// WriteMap 写入Map数据
func (w *ExcelWriter) WriteMap(mapData map[string]interface{}) *ExcelWriter {
	// 如果没有表头，无法写入map数据
	if len(w.headers) == 0 {
		return w
	}

	rowData := make([]interface{}, len(w.headers))
	for i, header := range w.headers {
		if value, exists := mapData[header]; exists {
			rowData[i] = value
		} else {
			rowData[i] = ""
		}
	}

	return w.WriteRow(rowData)
}

// WriteMaps 写入多个Map数据
func (w *ExcelWriter) WriteMaps(mapsData []map[string]interface{}) *ExcelWriter {
	for _, mapData := range mapsData {
		w.WriteMap(mapData)
	}
	return w
}

// SetColumnWidth 设置列宽
func (w *ExcelWriter) SetColumnWidth(col string, width float64) *ExcelWriter {
	w.file.SetColWidth(w.sheetName, col, col, width)
	return w
}

// SetColumnWidths 设置多列列宽
func (w *ExcelWriter) SetColumnWidths(startCol, endCol string, width float64) *ExcelWriter {
	w.file.SetColWidth(w.sheetName, startCol, endCol, width)
	return w
}

// SetStyle 设置单元格样式
func (w *ExcelWriter) SetStyle(cellRange string, style *excelize.Style) error {
	styleID, err := w.file.NewStyle(style)
	if err != nil {
		return err
	}

	return w.file.SetCellStyle(w.sheetName, cellRange, cellRange, styleID)
}

// AutoFilter 设置自动筛选
func (w *ExcelWriter) AutoFilter(startCol, endCol string) *ExcelWriter {
	if len(w.headers) > 0 {
		filterRange := fmt.Sprintf("%s1:%s%d", startCol, endCol, w.rowIndex-1)
		w.file.AutoFilter(w.sheetName, filterRange, nil)
	}
	return w
}

// SaveAs 保存Excel文件
func (w *ExcelWriter) SaveAs(filePath string) error {
	// 自动创建目录
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		return err
	}

	// 自动添加文件扩展名
	if !strings.HasSuffix(strings.ToLower(filePath), EXCEL_FORMAT_XLSX) &&
		!strings.HasSuffix(strings.ToLower(filePath), EXCEL_FORMAT_XLS) {
		filePath += EXCEL_FORMAT_XLSX
	}

	return w.file.SaveAs(filePath)
}

// Save 保存Excel文件
func (w *ExcelWriter) Save(filePath string) error {
	return w.SaveAs(filePath)
}

// Close 关闭Excel写入器
func (w *ExcelWriter) Close() error {
	return w.file.Close()
}

// NewExcelReader 创建Excel读取器
func NewExcelReader(filePath string) (*ExcelReader, error) {
	// 检查文件是否存在
	if !CheckFileExists(filePath) {
		return nil, fmt.Errorf("文件不存在: %s", filePath)
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != EXCEL_FORMAT_XLSX && ext != EXCEL_FORMAT_XLS {
		return nil, fmt.Errorf("不支持的文件格式: %s", ext)
	}

	// 打开Excel文件
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, err
	}

	// 获取第一个工作表名
	sheetName := file.GetSheetName(0)

	// 创建读取器
	reader := &ExcelReader{
		file:      file,
		sheetName: sheetName,
		headers:   []string{},
	}

	// 获取行数和列数
	rows, err := file.GetRows(sheetName)
	if err != nil {
		file.Close()
		return nil, err
	}

	reader.rowCount = len(rows)
	if reader.rowCount > 0 {
		reader.colCount = len(rows[0])
	}

	return reader, nil
}

// SetActiveSheet 设置活动工作表
func (r *ExcelReader) SetActiveSheet(sheetName string) error {
	sheetIndex, err := r.file.GetSheetIndex(sheetName)
	if err != nil {
		return err
	}

	r.sheetName = sheetName
	r.file.SetActiveSheet(sheetIndex)

	// 重新获取行数和列数
	rows, err := r.file.GetRows(sheetName)
	if err != nil {
		return err
	}

	r.rowCount = len(rows)
	if r.rowCount > 0 {
		r.colCount = len(rows[0])
	} else {
		r.colCount = 0
	}

	return nil
}

// GetSheetNames 获取所有工作表名
func (r *ExcelReader) GetSheetNames() []string {
	return r.file.GetSheetList()
}

// GetSheetCount 获取工作表数量
func (r *ExcelReader) GetSheetCount() int {
	return r.file.SheetCount
}

// ReadHeaders 读取表头
func (r *ExcelReader) ReadHeaders() ([]string, error) {
	if r.rowCount == 0 {
		return nil, errors.New("空文件，没有表头")
	}

	// 从第一行读取表头
	headers := make([]string, r.colCount)
	for i := 1; i <= r.colCount; i++ {
		cellName := fmt.Sprintf("%s1", ToExcelColumn(i))
		value, err := r.file.GetCellValue(r.sheetName, cellName)
		if err != nil {
			return nil, err
		}
		headers[i-1] = value
	}

	r.headers = headers
	return headers, nil
}

// GetHeaders 获取表头
func (r *ExcelReader) GetHeaders() []string {
	if len(r.headers) == 0 {
		r.ReadHeaders()
	}
	return r.headers
}

// ReadRow 读取指定行
func (r *ExcelReader) ReadRow(rowIndex int) ([]string, error) {
	if rowIndex <= 0 || rowIndex > r.rowCount {
		return nil, fmt.Errorf("行索引超出范围: %d", rowIndex)
	}

	rowData := make([]string, r.colCount)
	for i := 1; i <= r.colCount; i++ {
		cellName := fmt.Sprintf("%s%d", ToExcelColumn(i), rowIndex)
		value, err := r.file.GetCellValue(r.sheetName, cellName)
		if err != nil {
			return nil, err
		}
		rowData[i-1] = value
	}

	return rowData, nil
}

// ReadRowAsMap 读取指定行并转换为Map
func (r *ExcelReader) ReadRowAsMap(rowIndex int) (map[string]string, error) {
	if len(r.headers) == 0 {
		if _, err := r.ReadHeaders(); err != nil {
			return nil, err
		}
	}

	rowData, err := r.ReadRow(rowIndex)
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	for i, header := range r.headers {
		if i < len(rowData) {
			result[header] = rowData[i]
		} else {
			result[header] = ""
		}
	}

	return result, nil
}

// ReadRows 读取多行
func (r *ExcelReader) ReadRows(startRow, endRow int) ([][]string, error) {
	if startRow <= 0 || startRow > r.rowCount {
		return nil, fmt.Errorf("起始行索引超出范围: %d", startRow)
	}

	if endRow > r.rowCount {
		endRow = r.rowCount
	}

	rowsData := make([][]string, 0, endRow-startRow+1)
	for i := startRow; i <= endRow; i++ {
		rowData, err := r.ReadRow(i)
		if err != nil {
			return nil, err
		}
		rowsData = append(rowsData, rowData)
	}

	return rowsData, nil
}

// ReadRowsAsMap 读取多行并转换为Map数组
func (r *ExcelReader) ReadRowsAsMap(startRow, endRow int) ([]map[string]string, error) {
	if len(r.headers) == 0 {
		if _, err := r.ReadHeaders(); err != nil {
			return nil, err
		}
	}

	rowsData, err := r.ReadRows(startRow, endRow)
	if err != nil {
		return nil, err
	}

	result := make([]map[string]string, 0, len(rowsData))
	for _, rowData := range rowsData {
		rowMap := make(map[string]string)
		for i, header := range r.headers {
			if i < len(rowData) {
				rowMap[header] = rowData[i]
			} else {
				rowMap[header] = ""
			}
		}
		result = append(result, rowMap)
	}

	return result, nil
}

// ReadCellValue 读取单元格的值
func (r *ExcelReader) ReadCellValue(cellName string) (string, error) {
	return r.file.GetCellValue(r.sheetName, cellName)
}

// ReadAllRows 读取所有行（除表头外）
func (r *ExcelReader) ReadAllRows() ([][]string, error) {
	if r.rowCount <= 1 {
		return [][]string{}, nil
	}
	return r.ReadRows(2, r.rowCount)
}

// ReadAllRowsAsMap 读取所有行并转换为Map数组（除表头外）
func (r *ExcelReader) ReadAllRowsAsMap() ([]map[string]string, error) {
	if r.rowCount <= 1 {
		return []map[string]string{}, nil
	}
	return r.ReadRowsAsMap(2, r.rowCount)
}

// GetRowCount 获取行数
func (r *ExcelReader) GetRowCount() int {
	return r.rowCount
}

// GetColCount 获取列数
func (r *ExcelReader) GetColCount() int {
	return r.colCount
}

// Close 关闭Excel读取器
func (r *ExcelReader) Close() error {
	return r.file.Close()
}

// ExportToExcel 导出数据到Excel
func ExportToExcel(filePath string, sheetName string, headers []string, data [][]interface{}) error {
	writer := NewExcelWriter()
	writer.SetSheetName(sheetName)
	writer.SetHeaders(headers)
	writer.WriteRows(data)

	// 设置列宽自适应
	for i := range headers {
		col := ToExcelColumn(i + 1)
		writer.SetColumnWidth(col, 15)
	}

	err := writer.SaveAs(filePath)
	if err != nil {
		return err
	}

	return writer.Close()
}

// ExportMapToExcel 导出Map数据到Excel
func ExportMapToExcel(filePath string, sheetName string, headers []string, data []map[string]interface{}) error {
	writer := NewExcelWriter()
	writer.SetSheetName(sheetName)
	writer.SetHeaders(headers)
	writer.WriteMaps(data)

	// 设置列宽自适应
	for i := range headers {
		col := ToExcelColumn(i + 1)
		writer.SetColumnWidth(col, 15)
	}

	err := writer.SaveAs(filePath)
	if err != nil {
		return err
	}

	return writer.Close()
}

// ImportFromExcel 从Excel导入数据
func ImportFromExcel(filePath string) ([]map[string]string, error) {
	reader, err := NewExcelReader(filePath)
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	return reader.ReadAllRowsAsMap()
}

// GetExcelTemplateURL 获取Excel模板URL
func GetExcelTemplateURL(templateName string, baseURL string) string {
	return fmt.Sprintf("%s/templates/%s.xlsx", baseURL, templateName)
}

// ToExcelColumn 数字转Excel列名（1->A, 2->B, ..., 27->AA）
func ToExcelColumn(colNum int) string {
	if colNum <= 0 {
		return ""
	}

	result := ""
	for colNum > 0 {
		remainder := (colNum - 1) % 26
		result = string(rune('A'+remainder)) + result
		colNum = (colNum - remainder - 1) / 26
	}

	return result
}

// FromExcelColumn Excel列名转数字（A->1, B->2, ..., AA->27）
func FromExcelColumn(colName string) int {
	colName = strings.ToUpper(colName)
	result := 0

	for i := 0; i < len(colName); i++ {
		result = result*26 + int(colName[i]-'A'+1)
	}

	return result
}

// ToCellName 坐标转单元格名称（行, 列 -> A1, B2）
func ToCellName(row, col int) string {
	return fmt.Sprintf("%s%d", ToExcelColumn(col), row)
}

// FromCellName 单元格名称转坐标（A1 -> 1, 1）
func FromCellName(cellName string) (row, col int, err error) {
	// 分离列名和行号
	var colName string
	var rowName string

	for i, c := range cellName {
		if c >= '0' && c <= '9' {
			colName = cellName[:i]
			rowName = cellName[i:]
			break
		}
	}

	if colName == "" || rowName == "" {
		return 0, 0, fmt.Errorf("无效的单元格名称: %s", cellName)
	}

	// 解析行号
	row, err = strconv.Atoi(rowName)
	if err != nil {
		return 0, 0, err
	}

	// 解析列名
	col = FromExcelColumn(colName)

	return row, col, nil
}

// ParseExcelTime 解析Excel时间
func ParseExcelTime(excelTime float64) (time.Time, error) {
	// Excel时间是从1900年1月1日开始的天数
	// 但Excel有一个错误，它认为1900年是闰年，实际上不是
	// 所以要处理这个错误：如果天数大于60（1900年3月1日），需要减去1

	// 检查是否为负数或零
	if excelTime < 0 {
		return time.Time{}, fmt.Errorf("无效的Excel时间: %f", excelTime)
	}

	// 起始时间：1899年12月30日
	startDate := time.Date(1899, 12, 30, 0, 0, 0, 0, time.UTC)

	// 天数部分
	days := int(excelTime)

	// 时间部分
	seconds := int((excelTime - float64(days)) * 86400)
	hours := seconds / 3600
	seconds -= hours * 3600
	minutes := seconds / 60
	seconds -= minutes * 60

	// 如果天数大于60（1900年3月1日），减去1（修正Excel的闰年错误）
	if days > 60 {
		days--
	}

	// 计算日期
	date := startDate.AddDate(0, 0, days)

	// 添加时间
	date = date.Add(time.Duration(hours) * time.Hour)
	date = date.Add(time.Duration(minutes) * time.Minute)
	date = date.Add(time.Duration(seconds) * time.Second)

	return date, nil
}

// GetExcelFileName 生成Excel文件名
func GetExcelFileName(prefix string) string {
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("%s_%s.xlsx", prefix, timestamp)
}

// ValidateExcelFile 验证Excel文件
func ValidateExcelFile(reader io.Reader, maxSize int64) error {
	// 检查文件大小
	if maxSize <= 0 {
		maxSize = 10 * 1024 * 1024 // 默认10MB
	}

	// 读取文件头
	excelFile, err := excelize.OpenReader(reader)
	if err != nil {
		return fmt.Errorf("无效的Excel文件格式: %v", err)
	}
	defer excelFile.Close()

	return nil
}

// CreateExcelTemplate 创建Excel模板
func CreateExcelTemplate(filePath string, sheetName string, headers []string) error {
	writer := NewExcelWriter()
	writer.SetSheetName(sheetName)
	writer.SetHeaders(headers)

	// 设置列宽自适应
	for i := range headers {
		col := ToExcelColumn(i + 1)
		writer.SetColumnWidth(col, 15)
	}

	err := writer.SaveAs(filePath)
	if err != nil {
		return err
	}

	return writer.Close()
}

