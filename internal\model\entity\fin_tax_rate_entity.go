package entity

import "github.com/shopspring/decimal"

// FinTaxRate 税率管理
type FinTaxRate struct {
	TenantEntity
	Name        string          `gorm:"column:name;type:varchar(100);uniqueIndex;not null;comment:税率名称" json:"name"`
	Rate        decimal.Decimal `gorm:"column:rate;type:decimal(10,4);not null;comment:税率值" json:"rate"`
	Description string          `gorm:"column:description;type:varchar(255);comment:描述" json:"description"`
	IsEnabled   bool            `gorm:"column:is_enabled;default:true;comment:是否启用" json:"isEnabled"`
}

func (FinTaxRate) TableName() string {
	return "fin_tax_rate"
}
