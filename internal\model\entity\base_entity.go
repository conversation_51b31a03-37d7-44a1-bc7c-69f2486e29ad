package entity

import (
	"time"

	"gorm.io/gorm"
)

// BaseEntity 基础实体结构，所有实体都继承此结构
// 提供了实体对象的基本属性，包括主键、时间戳和操作人信息
// 实现了GORM的软删除功能，支持数据审计
type BaseEntity struct {
	ID        uint           `gorm:"primarykey" json:"id" sortable:"true"`                                         // 主键ID (允许排序)
	CreatedAt time.Time      `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"createdAt" sortable:"true"` // 创建时间 (允许排序)
	UpdatedAt time.Time      `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updatedAt" sortable:"true"` // 更新时间 (允许排序)
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`                                             // 删除时间：实现软删除功能，被删除时记录删除时间而非真正删除数据，JSON序列化时忽略此字段，建立索引提高查询效率
	CreatedBy uint           `gorm:"column:created_by;default:0" json:"createdBy"`                                 // 创建人ID：记录创建该实体的用户ID，用于数据审计，默认为0表示系统创建
	UpdatedBy uint           `gorm:"column:updated_by;default:0" json:"updatedBy"`                                 // 更新人ID：记录最后更新该实体的用户ID，用于数据审计，默认为0表示系统更新
}

// TenantEntity 租户实体结构，用于多租户系统
// 继承自BaseEntity，增加了租户ID字段
// 适用于需要进行租户隔离的业务实体
type TenantEntity struct {
	BaseEntity      // 继承基础实体结构，包含ID、时间戳和操作人等基础字段
	TenantID   uint `gorm:"column:tenant_id;index;default:0" json:"tenantId"` // 租户ID：标识数据所属的租户，用于实现多租户数据隔离，建立索引提高查询效率，默认为0表示系统级数据
}

// AccountBookEntity 账套实体结构，用于多账套系统
// 继承自TenantEntity，增加了账套ID字段
// 适用于需要进行账套隔离的业务实体
type AccountBookEntity struct {
	TenantEntity
	AccountBookID uint `gorm:"column:account_book_id;index;default:0" json:"accountBookId"` // 账套ID：标识数据所属的账套，用于实现多账套数据隔离，建立索引提高查询效率，默认为0表示系统级数据
}

// EmployeeEntity 员工实体结构，用于多员工系统
// 继承自TenantEntity，增加了员工ID字段
// 适用于需要进行员工隔离的业务实体
type EmployeeEntity struct {
	TenantEntity
	EmployeeID uint `gorm:"column:employee_id;index;default:0" json:"employeeId"` // 员工ID：标识数据所属的员工，用于实现多员工数据隔离，建立索引提高查询效率，默认为0表示系统级数据
}

// RoleEntity 角色实体结构，用于多角色系统
// 继承自TenantEntity，增加了角色ID字段
// 适用于需要进行角色隔离的业务实体
type RoleEntity struct {
	TenantEntity
	RoleID uint `gorm:"column:role_id;index;default:0" json:"roleId"` // 角色ID：标识数据所属的角色，用于实现多角色数据隔离，建立索引提高查询效率，默认为0表示系统级数据
}

// UserEntity 用户实体结构，用于多用户系统
// 继承自TenantEntity，增加了用户ID字段
// 适用于需要进行用户隔离的业务实体
type UserEntity struct {
	TenantEntity
	Username string `gorm:"column:username;index;default:''" json:"username"` // 用户名：标识数据所属的用户，用于实现多用户数据隔离，建立索引提高查询效率，默认为空字符串
}
