package vo

import (
	"time"
)

// WmsPutawayTaskDetailVO 上架任务明细的视图对象
type WmsPutawayTaskDetailVO struct {
	ID                      uint       `json:"id"`                      // 主键ID
	AccountBookID           uint       `json:"accountBookId"`           // 账套ID
	PutawayTaskID           uint       `json:"putawayTaskId"`           // 关联上架任务ID
	ReceivingRecordDetailID uint       `json:"receivingRecordDetailId"` // 关联收货明细ID
	LineNo                  int        `json:"lineNo"`                  // 行号
	ItemID                  uint       `json:"itemId"`                  // 物料ID
	PutawayQuantity         float64    `json:"putawayQuantity"`         // 需上架数量
	UnitOfMeasure           string     `json:"unitOfMeasure"`           // 数量单位
	BatchNo                 *string    `json:"batchNo"`                 // 批次号
	ProductionDate          *time.Time `json:"productionDate"`          // 生产日期
	ExpiryDate              *time.Time `json:"expiryDate"`              // 过期日期
	SourceLocationID        uint       `json:"sourceLocationId"`        // 源库位ID
	SuggestedLocationID     *uint      `json:"suggestedLocationId"`     // 建议库位ID
	ActualLocationID        *uint      `json:"actualLocationId"`        // 实际库位ID
	ActualPutawayQuantity   float64    `json:"actualPutawayQuantity"`   // 实际上架数量
	Status                  string     `json:"status"`                  // 状态
	ExceptionReason         *string    `json:"exceptionReason"`         // 异常原因
	CreatedAt               time.Time  `json:"createdAt"`               // 创建时间
	UpdatedAt               time.Time  `json:"updatedAt"`               // 更新时间

	// 扩展字段 (关联查询得到)
	ItemSKU               string `json:"itemSku,omitempty"`               // 物料SKU
	ItemName              string `json:"itemName,omitempty"`              // 物料名称
	SourceLocationCode    string `json:"sourceLocationCode,omitempty"`    // 源库位代码
	SuggestedLocationCode string `json:"suggestedLocationCode,omitempty"` // 建议库位代码
	ActualLocationCode    string `json:"actualLocationCode,omitempty"`    // 实际库位代码
}
