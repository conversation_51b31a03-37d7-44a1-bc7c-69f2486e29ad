<template>
  <div>
    <!-- 系统参数表格 -->
    <VNTable
      ref="parameterTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="parameterToolbarConfig"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      operation-fixed="right"
      :operation-width="220" 
      @refresh="loadParameters"
      @add="handleAddParameter"
      @batch-delete="handleParameterBatchDelete"
      @import="handleParameterImport"
      @export="handleParameterExport"
      @filter-change="handleParameterFilterChange"
      @page-change="handleParameterPageChange"
      @page-size-change="handleParameterPageSizeChange"
      @sort-change="handleParameterSortChange"
      @selection-change="handleParameterSelectionChange"
    >
      <!-- 插槽：状态 -->
      <template #column-status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>
      <!-- 插槽：系统内置 -->
      <template #column-isSystem="{ row }">
        <el-tag :type="row.isSystem ? 'warning' : 'info'">
          {{ row.isSystem ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 插槽：行操作 -->
      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top" v-if="hasPermission('sys:parameter:list')">
          <el-button circle :icon="View" size="small" @click="handleViewParameter(row)" />
        </el-tooltip>
        <el-tooltip content="编辑" placement="top" v-if="hasPermission('sys:parameter:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditParameter(row)" />
        </el-tooltip>
        <el-tooltip content="删除" placement="top" v-if="hasPermission('sys:parameter:delete')">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            :disabled="row.isSystem" 
            @click="handleDeleteParameter(row)" 
          />
        </el-tooltip>
      </template>

    </VNTable>

    <!-- 新增/编辑/查看弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="parameterDialogTitle"
      width="60%"
      draggable
      :close-on-click-modal="false"
      @close="handleCloseParameterDialog"
    >
      <VNForm
        v-if="dialogVisible" 
        ref="parameterFormRef"
        :header-fields="parameterFormFields"
        v-model="formData"
        :loading="formLoading"
        :show-actions="false"
        @submit="submitParameterForm"
        @cancel="handleCloseParameterDialog"
      >
        <!-- 新增：自定义表单操作按钮插槽 -->
        <template #actions>
            <template v-if="isViewing">
                <el-button
                  v-if="hasPermission('sys:parameter:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreview"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('sys:parameter:print')" 
                  type="primary"
                  :icon="Printer"
                  @click="handlePrint"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseParameterDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitParameterForm" :loading="formLoading">提交</el-button>
                <el-button @click="handleCloseParameterDialog">取消</el-button>
            </template>
        </template>
      </VNForm>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus';
// 导入图标 (根据需要添加)
import { View, Edit, Delete, Printer } from '@element-plus/icons-vue';
// 导入 VN 组件和类型
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 导入 API 函数和类型
import {
  getParameterPage,
  createParameter,
  updateParameter,
  deleteParameter,
  //getParameter
} from '@/api/system/systemParameter';
import type {
  SystemParameterVO,
  SystemParameterQueryDTO,
  SystemParameterCreateDTO,
  SystemParameterUpdateDTO
} from '@/api/system/systemParameter';
// 导入权限检查
import { hasPermission } from '@/hooks/usePermission';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`; // 根据用户之前的修改，这里只显示 detail.message
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- 类型别名 (如果需要) ---
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;
type SortParams = { prop: string; order: 'ascending' | 'descending' | null };
type FilterParams = Record<string, any>;

// --- Refs ---
const parameterTableRef = ref<VNTableInstance>();
const parameterFormRef = ref<VNFormInstance>();

// --- Reactive State (遵循 user/index.vue 结构) ---
const loading = ref(false);
const tableData = ref<SystemParameterVO[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const queryParams = ref<Partial<SystemParameterQueryDTO>>({}); // 用于存储非分页的查询参数
const currentSort = ref<SortParams | null>(null);
const currentFilters = ref<FilterParams>({});
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const isViewing = computed(() => formMode.value === 'view');
const formData = ref<Partial<SystemParameterCreateDTO | SystemParameterUpdateDTO>>({});
const formLoading = ref(false);
const selectedRows = ref<SystemParameterVO[]>([]); // 存储选中的行

// --- Helpers (移到前面) ---
const formatDateTime = (dateString: string | null | undefined): string => {
  // console.log('formatDateTime received:', dateString, typeof dateString); // <<< 移除调试日志
  // --- Add explicit type check --- 
  if (typeof dateString !== 'string' || !dateString || dateString.startsWith('0001-01-01')) {
    return '-';
  }
  // --------------------------------
  try {
    const date = new Date(dateString);
    // Check if the date is valid AFTER attempting to parse
    if (isNaN(date.getTime())) {
        console.warn('formatDateTime received invalid date string after initial check:', dateString);
        return '-'; // 无效日期返回占位符
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    // Log the error and the input string for better debugging
    console.error('Error formatting date:', dateString, '\nError:', e);
    return '-'; // 格式化出错返回占位符
  }
};
// ------------------------

// --- Computed Properties (占位符) ---
const parameterDialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增系统参数';
  if (formMode.value === 'edit') return '编辑系统参数';
  if (formMode.value === 'view') return '查看系统参数';
  return '系统参数';
});

const parameterToolbarConfig = computed(() => ({
  refresh: true,
  // 权限占位符
  add: true, // hasPermission('system:parameter:add'),
  batchDelete: true, // hasPermission('system:parameter:batchdelete'),
  filter: true, // hasPermission('system:parameter:search'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: true, // hasPermission('system:parameter:import'),
  export: true, // hasPermission('system:parameter:export'),
}));

const tableColumns = ref<TableColumn[]>([
  { prop: 'paramKey', label: '参数键', minWidth: 240, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'name', label: '参数名称', minWidth: 240, filterable: true, filterType: 'text' },
  { prop: 'paramValue', label: '参数值', minWidth: 200, showOverflowTooltip: true },
  { prop: 'status', label: '状态', width: 90, slot: true, filterable: true, filterType: 'select', filterOptions: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }] },
  { prop: 'isSystem', label: '系统内置', width: 100, slot: true },
  { prop: 'valueType', label: '值类型', width: 100, filterable: true, filterType: 'text' },
  { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true },
  { 
    prop: 'createdAt', 
    label: '创建时间', 
    width: 180, 
    sortable: true, 
    filterable: true, 
    filterType: 'date', 
    formatter: (row) => formatDateTime(row.createdAt) 
  },
]);

const parameterFormFields = computed<HeaderField[]>(() => {
  // --- Assert formData type for accessing isSystem ---
  const isSystemParam = (formData.value as SystemParameterVO)?.isSystem ?? false;

  return [
    {
      field: 'paramKey',
      label: '参数键',
      // Use the asserted variable
      disabled: formMode.value !== 'add' || (isSystemParam && !isViewing.value),
      rules: [
        { required: true, message: '参数键不能为空' },
        { max: 100, message: '参数键长度不能超过100' },
        { pattern: /^[a-zA-Z0-9_.]+$/, message: '键只能包含字母、数字、下划线和点' }
      ]
    },
    {
      field: 'name',
      label: '参数名称',
      // Use the asserted variable
      disabled: isViewing.value || (isSystemParam && !isViewing.value),
      rules: [
        { required: true, message: '参数名称不能为空' },
        { max: 100, message: '名称长度不能超过100' }
      ]
    },
    {
      field: 'paramValue',
      label: '参数值',
      type: 'textarea',
      props: { rows: 3 },
      disabled: isViewing.value,
      rules: [{ required: true, message: '参数值不能为空' }]
    },
    {
      field: 'valueType',
      label: '值类型',
      // Use the asserted variable
      disabled: isViewing.value || (isSystemParam && !isViewing.value),
      placeholder: '例如: string, integer, boolean, json',
      rules: [{ max: 50, message: '值类型长度不能超过50' }]
    },
    {
      field: 'status',
      label: '状态',
      type: 'select',
      options: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }],
      defaultValue: 1,
      // Use the asserted variable
      disabled: isViewing.value || (isSystemParam && !isViewing.value),
    },
    {
      field: 'isSystem',
      label: '系统内置',
      type: 'switch',
      defaultValue: false,
      disabled: formMode.value === 'edit' || isViewing.value
    },
    {
      field: 'remark',
      label: '备注',
      type: 'textarea',
      props: { rows: 3 },
      // Use the asserted variable
      disabled: isViewing.value || (isSystemParam && !isViewing.value),
      rules: [{ max: 500, message: '备注长度不能超过500' }]
    },
  ];
});

// --- Methods (占位符) ---
const loadParameters = async (paramsOverwrite = {}) => {
  loading.value = true;
  try {
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop && currentSort.value.order) {
      const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
      sortString = `${currentSort.value.prop},${direction}`;
    }

    const query = { 
      ...queryParams.value, 
      ...currentFilters.value,
      ...paramsOverwrite
    };

    Object.keys(query).forEach(key => {
      const typedKey = key as keyof typeof query;
      if (query[typedKey] === null || query[typedKey] === undefined || query[typedKey] === '') {
        delete query[typedKey];
      }
    });
    
    const params: Partial<SystemParameterQueryDTO> & { pageNum: number; pageSize: number; sort?: string } = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      sort: sortString,
      ...query,
    };

    console.log('[loadParameters] Fetching with params:', params);
    const response = await getParameterPage(params);

    if (response && Array.isArray(response.list)) {
        tableData.value = response.list;
        pagination.total = response.total || 0;
    } else {
        console.warn('getParameterPage 返回的数据格式不符合预期:', response);
        tableData.value = [];
        pagination.total = 0;
    }

  } catch (error) {
    console.error('Error loading system parameters:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const handleAddParameter = () => {
  formMode.value = 'add';
  formData.value = {
    status: 1, // 默认启用
    isSystem: false, // 默认非系统内置
    paramKey: '', // 确保 key 存在以进行校验
    name: '',
    paramValue: '',
  };
  dialogVisible.value = true;
  nextTick(() => {
    parameterFormRef.value?.clearValidate();
  });
};

const handleEditParameter = (row: SystemParameterVO) => {
  formMode.value = 'edit';
  // 深拷贝以避免直接修改表格数据
  formData.value = JSON.parse(JSON.stringify(row));
  dialogVisible.value = true;
  nextTick(() => {
    parameterFormRef.value?.clearValidate();
  });
};

const handleViewParameter = (row: SystemParameterVO) => {
  formMode.value = 'view';
  formData.value = JSON.parse(JSON.stringify(row));
  dialogVisible.value = true;
  // 查看模式无需清除校验
};

const handleDeleteParameter = async (row: SystemParameterVO) => {
  // 检查是否为系统内置参数
  if (row.isSystem) {
    ElMessage.warning('系统内置参数不允许删除。');
    return;
  }

  try {
    await ElMessageBox.confirm(`确定删除参数 "${row.name}" (${row.paramKey}) 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    console.log('[handleDeleteParameter] Calling deleteParameter for ID:', row.id);
    await deleteParameter(row.id);
    ElMessage.success('删除成功');
    // 如果删除的是当前页最后一条，可能需要调整页码
    if (tableData.value.length === 1 && pagination.currentPage > 1) {
      pagination.currentPage--;
    }
    loadParameters(); // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting system parameter:', error);
      // 错误消息由拦截器处理 - 但我们仍按标准方式调用ElMessage，以防万一
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

const submitParameterForm = async () => {
  if (isViewing.value) {
    handleCloseParameterDialog();
    return;
  }

  const formRef = parameterFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  formLoading.value = true;
  const payload = { ...formData.value };

  try {
    if (formMode.value === 'add') {
      const createPayload: SystemParameterCreateDTO = {
        paramKey: (payload as SystemParameterCreateDTO).paramKey!,
        name: payload.name!,
        paramValue: payload.paramValue!,
        valueType: payload.valueType,
        status: payload.status,
        isSystem: (payload as SystemParameterCreateDTO).isSystem,
        remark: payload.remark,
      };
      console.log('[submitParameterForm] Calling createParameter with:', createPayload);
      await createParameter(createPayload);
      ElMessage.success('新增成功');
    } else { // formMode === 'edit'
      // 准备 Update DTO - 只包含可更新字段
      const updatePayload: Partial<SystemParameterUpdateDTO> = {
        name: payload.name,
        paramValue: payload.paramValue,
        valueType: payload.valueType,
        status: payload.status,
        remark: payload.remark,
        // paramKey 和 isSystem 通常不允许在更新时修改
      };
      // 移除 undefined 字段，避免发送 null (如果需要)
      Object.keys(updatePayload).forEach(key => {
        if (updatePayload[key as keyof typeof updatePayload] === undefined) {
          delete updatePayload[key as keyof typeof updatePayload];
        }
      });

      const currentId = (formData.value as SystemParameterVO).id;
      if (!currentId) {
          throw new Error('无法获取要编辑的参数ID');
      }
      console.log('[submitParameterForm] Calling updateParameter with ID:', currentId, ' Payload:', updatePayload);
      await updateParameter(currentId, updatePayload);
      ElMessage.success('编辑成功');
    }

    handleCloseParameterDialog();
    loadParameters(); // 成功后刷新表格

  } catch (error) {
    console.error('Error saving system parameter:', error);
    // 错误消息通常由请求拦截器处理 - 但我们仍按标准方式调用ElMessage
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    formLoading.value = false;
  }
};

const handleCloseParameterDialog = () => {
  dialogVisible.value = false;
  // 清空表单数据可以在关闭时或下次打开前完成
  // formData.value = {}; 
};

const handleParameterBatchDelete = (rows: SystemParameterVO[]) => {
  console.log('handleParameterBatchDelete called with:', rows);
};

const handleParameterImport = () => {
  console.log('handleParameterImport called');
};

const handleParameterExport = () => {
  console.log('handleParameterExport called');
};

const handleParameterFilterChange = (filters: FilterParams) => {
  console.log('handleParameterFilterChange called with:', filters);
  currentFilters.value = filters;
  pagination.currentPage = 1;
  loadParameters();
};

const handleParameterPageChange = (page: number) => {
  console.log('handleParameterPageChange called with:', page);
  pagination.currentPage = page;
  loadParameters();
};

const handleParameterPageSizeChange = (size: number) => {
  console.log('handleParameterPageSizeChange called with:', size);
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadParameters();
};

const handleParameterSortChange = (sort: SortParams) => {
  console.log('handleParameterSortChange called with:', sort);
  currentSort.value = sort;
  loadParameters();
};

const handleParameterSelectionChange = (rows: SystemParameterVO[]) => {
  console.log('handleParameterSelectionChange called with:', rows);
  selectedRows.value = rows;
};

// --- 新增：打印处理函数（占位）---
const handlePrintPreview = () => {
  // 断言 formData.value 包含 id，或者更安全地检查其是否存在
  const currentParam = formData.value as Partial<SystemParameterVO>; 
  if (!currentParam || !currentParam.id) {
    ElMessage.warning('没有可打印预览的参数数据。');
    return;
  }
  console.log('触发打印预览，系统参数:', currentParam);
  ElMessage.info('打印预览功能待实现');
  // TODO: Implement print preview logic
};

const handlePrint = () => {
  const currentParam = formData.value as Partial<SystemParameterVO>;
  if (!currentParam || !currentParam.id) {
    ElMessage.warning('没有可打印的参数数据。');
    return;
  }
  console.log('触发打印，系统参数:', currentParam);
  ElMessage.info('打印功能待实现');
  // TODO: Implement print logic
};

// --- Lifecycle --- 
onMounted(() => {
  loadParameters();
});

</script>

<style scoped>
/* 添加必要的样式 */
</style> 