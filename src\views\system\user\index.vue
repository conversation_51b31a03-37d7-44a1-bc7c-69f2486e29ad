<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="220"
      @refresh="loadData"
      @add="handleAdd"
      @batch-delete="handleBatchDelete"
      @import="handleImport"
      @export="handleExport"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <template #column-status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '正常' : '禁用' }}
        </el-tag>
      </template>

      <!-- 新增：管理员列插槽 -->
      <template #column-isAdmin="{ row }">
        <el-tag :type="row.isAdmin ? 'warning' : 'info'">
          {{ row.isAdmin ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 修改：使用圆形图标按钮 -->
      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)" v-if="!operationButtons.find(b => b.label === '查看')?.hidden"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEdit(row)" v-if="!operationButtons.find(b => b.label === '编辑')?.hidden && hasPermission('sys:user:edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            @click="handleDelete(row)" 
            v-if="!operationButtons.find(b => b.label === '删除')?.hidden && hasPermission('sys:user:delete')"
            :disabled="row.isAdmin"
          ></el-button>
        </el-tooltip>
        <el-tooltip content="重置密码" placement="top">
          <el-button 
            circle 
            :icon="RefreshRight" 
            type="warning" 
            size="small" 
            @click="handleResetPassword(row)" 
            v-if="!operationButtons.find(b => b.label === '重置密码')?.hidden && hasPermission('sys:user:resetpwd')"
            :disabled="row.isAdmin"
          ></el-button>
        </el-tooltip>
      </template>

    </VNTable>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="60%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="3"
        :label-width="'100px'"
        :loading="loading"
        group-title-type="h4"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <template #form-item-password="{ field, formData: formModel }">
          <!-- 新增模式：显示密码输入框 -->
          <el-input
            v-if="formMode === 'add'"
            v-model="formModel[field.field]"
            type="password"
            placeholder="请输入密码"
            show-password
          />
          <!-- 查看模式：显示重置密码按钮 -->
          <el-button
            v-else-if="formMode === 'view'"
            type="warning"
            plain
            :icon="RefreshRight"
            @click="handleResetPassword(currentRow)"
            :disabled="!hasPermission('sys:user:resetpwd')"
          >
            重置密码
          </el-button>
          <!-- 编辑模式：显示占位符 -->
          <el-input
            v-else-if="formMode === 'edit'"
            value="****** (如需修改请重置)"
            disabled
          />
        </template>

        <!-- 修改：头像字段的插槽 -->
        <template #form-item-avatar="{ field, formData: formModel }">
          <!-- 查看模式：显示头像图片 -->
          <el-image
            v-if="isViewing"
            style="width: 100px; height: 100px; border: 1px dashed #dcdfe6; border-radius: 6px; vertical-align: top;"
            :src="formModel[field.field]"
            :preview-src-list="formModel[field.field] ? [formModel[field.field]] : []"
            :initial-index="0"
            fit="cover"
          >
            <template #error>
              <div class="image-slot">无头像</div>
            </template>
            <template #placeholder>
              <div class="image-slot">加载中...</div>
            </template>
          </el-image>
          <!-- 新增/编辑模式：显示当前头像和提示 -->
          <div v-else>
            <el-image
              style="width: 100px; height: 100px; border: 1px dashed #dcdfe6; border-radius: 6px; vertical-align: top; margin-bottom: 8px;"
              :src="formModel[field.field]"
              fit="cover"
            >
              <template #error>
                <div class="image-slot">无头像</div>
              </template>
              <template #placeholder>
                <div class="image-slot">加载中...</div>
              </template>
            </el-image>
            <div>
              <el-text type="info" size="small">
                如需更新头像，请前往个人中心操作。
              </el-text>
            </div>
          </div>
        </template>

        <template #actions> 
          <template v-if="isViewing">
            <el-button
              v-if="hasPermission('sys:user:printpreview')"
              :icon="View"
              @click="handlePrintPreview"
            >
              打印预览
            </el-button>
            <el-button
              v-if="hasPermission('sys:user:print')"
              type="primary"
              :icon="Printer"
              @click="handlePrint"
            >
              打印
            </el-button>
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else> 
            <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
            <el-button @click="handleCloseDialog">取消</el-button>
          </template>
        </template>

      </VNForm>

    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
// import type { VNTableProps, TableColumn, PaginationConfig, ActionButton /*, ToolbarConfig, SortParams, FilterParams*/ } from '@/components/VNTable/types';
import type { TableColumn, PaginationConfig, ActionButton /*, ToolbarConfig, SortParams, FilterParams*/ } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton, ElInput, ElImage, ElText } from 'element-plus';
// import type { FormInstance } from 'element-plus';
// --- 导入所需图标 ---
import { View, Edit, Delete, RefreshRight, Printer } from '@element-plus/icons-vue';
import {
  getUserList,
  addUser,
  updateUser,
  deleteUser,
  getRoles,
  resetPassword,
  getEmployeeSimpleList,
  batchDeleteUsers,
  getUserDetail,
} from '@/api/system/systemUser';
import type { 
    UserListResponse, 
    RoleSimpleItem, 
    EmployeeSimpleItem, 
    UserDetail,
    UserFormData // Import UserFormData for type checking addUser payload
} from '@/api/system/systemUser'; // Assuming types are here
import { listAccountBooks } from '@/api/system/systemAccountBook';
import type { AccountBookVO } from '@/api/system/systemAccountBook';
import { getDictDataByCode } from '@/api/system/systemDict';
import type { DictDataVO } from '@/api/system/systemDict';

import { hasPermission } from '@/hooks/usePermission';
// import { getToken } from '@/utils/auth';

// --- 自定义校验函数 ---
const validateOptionalEmail = (rule: any, value: any, callback: any) => {
  console.log("validateOptionalEmail rule:", rule);
  if (value === null || value === undefined || String(value).trim() === '') {
    callback(); // 如果值为空或仅空格，则通过校验
  } else {
    // 使用一个常见的邮箱正则表达式进行校验
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    if (emailRegex.test(String(value))) {
      callback();
    } else {
      callback(new Error('请输入有效的邮箱格式'));
    }
  }
};

const validateOptionalMobile = (rule: any, value: any, callback: any) => {
  console.log("validateOptionalMobile rule:", rule);
  if (value === null || value === undefined || String(value).trim() === '') {
    callback(); // 如果值为空或仅空格，则通过校验
  } else {
    // 示例：校验中国大陆11位手机号，以1开头
    // 您可以根据实际需求调整此正则表达式
    const mobileRegex = /^1[3-9]\d{9}$/;
    if (mobileRegex.test(String(value))) {
      callback();
    } else {
      callback(new Error('请输入有效的中国大陆手机号码格式'));
    }
  }
};
// --------------------

// --- 辅助函数：格式化日期时间 ---
const formatDateTime = (dateString: string | null | undefined): string => {
  // Explicitly handle null, undefined, or the zero-date string from backend
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    // Check if the date is valid AFTER attempting to parse
    if (isNaN(date.getTime())) { 
        console.warn('formatDateTime received invalid date string after initial check:', dateString);
        return '-'; // 无效日期返回占位符
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); 
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    // Log the error and the input string for better debugging
    console.error('Error formatting date:', dateString, '\nError:', e);
    return '-'; // 格式化出错返回占位符
  }
};

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  // 仅当它与 apiError.message (如果存在) 不同或 apiError.message 不存在时添加
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      // const listItemPrefix = detail.field ? `字段 ${detail.field}` : `${index + 1}`;
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
// 扩展 formMode 以包含 'view'
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentRow = ref<any>(null); // 用于存储编辑/查看时当前行数据
const isViewing = computed(() => formMode.value === 'view'); // 计算属性判断是否查看模式

// --- Reactive State ---
const loading = ref(false);
const tableData = ref<any[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const formData = ref<Record<string, any>>({});
const allRoles = ref<{ label: string; value: number }[]>([]); // 存储所有角色选项
const allEmployees = ref<{ label: string; value: number }[]>([]); // 新增：存储所有员工选项
const allAccountBooks = ref<{ label: string; value: number }[]>([]); // 存储所有账套选项
const genderOptions = ref<DictDataVO[]>([]); // 新增：存储性别选项

// --- Add state for filters and sort ---
const currentFilters = ref<Record<string, any>>({});
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);

// --- Table Configuration (移除手动添加的 selection 和 index 列) ---
const tableColumns = ref<TableColumn[]>([
  // { type: 'selection', prop: '__selection__', width: 50, fixed: 'left' }, // 由 selection-type prop 控制
  // { type: 'index', prop: '__index__', label: '序号', width: 60, fixed: 'left' }, // 由 show-index prop 控制
  { prop: 'username', label: '用户名', minWidth: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'nickname', label: '昵称', minWidth: 120, filterable: true, filterType: 'text' },
  { prop: 'realName', label: '真实姓名', minWidth: 120, filterable: true, filterType: 'text' },
  {
    prop: 'gender', label: '性别', width: 80,
    formatter: (row) => row.gender === 1 ? '男' : row.gender === 2 ? '女' : '未知',
    filterable: true, filterType: 'select',
    filterOptions: [{ label: '未知', value: 0 }, { label: '男', value: 1 }, { label: '女', value: 2 }]
  },
  { prop: 'email', label: '邮箱', minWidth: 180, filterable: true },
  { prop: 'mobile', label: '手机号', minWidth: 120, filterable: true },
  {
    prop: 'status', label: '状态', width: 90, slot: true,
    filterable: true, filterType: 'select',
    filterOptions: [{ label: '正常', value: 1 }, { label: '禁用', value: 0 }]
  },
  {
    prop: 'loginTime',
    label: '最后登录时间',
    width: 160,
    sortable: true,
    filterable: true,
    filterType: 'date',
    formatter: (row) => formatDateTime(row.loginTime)
  },
  {
    prop: 'isAdmin',
    label: '管理员',
    width: 80,
    slot: true,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    sortable: true,
    filterable: true,
    filterType: 'date',
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

// --- FIX: Use Record<string, any> for toolbarConfig type --- 
const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('sys:user:add'),
  batchDelete: hasPermission('sys:user:batchdelete'),
  filter: hasPermission('sys:user:search'), // 使用 search 权限控制筛选
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('sys:user:import'),
  export: hasPermission('sys:user:export'),
}));
// ---------------------------------------------------------

// --- 权限配置在此更新 ---
const operationButtons = computed<ActionButton[]>(() => [
  { label: '查看', icon: 'View', handler: (row) => handleView(row) },
  {
    label: '编辑',
    icon: 'Edit',
    type: 'primary',
    handler: (row) => handleEdit(row),
    hidden: !hasPermission('sys:user:edit')
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    handler: (row) => handleDelete(row),
    hidden: !hasPermission('sys:user:delete')
  },
  {
    label: '重置密码',
    icon: 'RefreshRight',
    type: 'warning',
    handler: (row) => handleResetPassword(row),
    hidden: !hasPermission('sys:user:resetpwd')
  }
]);

// --- Form Configuration ---
const formFields = computed<HeaderField[]>(() => [
  // 基本信息分组
  { field: 'username', label: '用户名', rules: [{ required: true, message: '用户名为必填项' }], disabled: formMode.value === 'edit' || isViewing.value, group: '基本信息' },
  { field: 'password', label: '密码', type: 'slot', group: '基本信息' },
  { field: 'nickname', label: '昵称', disabled: isViewing.value, group: '基本信息' },
  { field: 'realName', label: '真实姓名', disabled: isViewing.value, group: '基本信息' },
  { field: 'gender', label: '性别', type: 'select', options: genderOptions.value, disabled: isViewing.value, group: '基本信息', placeholder: '请选择性别', props: { clearable: true } },
  { 
    field: 'email', 
    label: '邮箱', 
    rules: [{ validator: validateOptionalEmail, trigger: 'blur' }], // 更新为自定义校验器
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'mobile', 
    label: '手机号码', 
    rules: [{ validator: validateOptionalMobile, trigger: 'blur' }], // 添加自定义校验器
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { field: 'status', label: '状态', type: 'select', options: [{ label: '正常', value: 1 }, { label: '禁用', value: 0 }], defaultValue: 1, disabled: isViewing.value, group: '基本信息', placeholder: '请选择状态' },
  { field: 'expireTime', label: '账号过期时间', type: 'date', props: { type: 'datetime' }, disabled: isViewing.value, group: '基本信息' },
  {
    field: 'employeeId', label: '关联员工', type: 'select',
    options: allEmployees.value,
    placeholder: '请选择关联员工',
    props: { clearable: true, filterable: true },
    disabled: isViewing.value,
    group: '基本信息'
  },
  { field: 'remark', label: '备注', type: 'textarea', rows: 3, span: 24, disabled: isViewing.value, group: '基本信息' },

  // 用户头像分组
  { field: 'avatar', label: '头像', type: 'slot', group: '用户头像' },
  
  // 用户角色分组
  {
    field: 'roleIds', label: '角色', type: 'select', options: allRoles.value,
    rules: [{ required: !isViewing.value, message: '请至少选择一个角色' }],
    props: { 
      multiple: true, 
      clearable: true, 
      collapseTags: false, 
    }, 
    md: 24,
    lg: 24,
    xl: 24,
    disabled: isViewing.value,
    group: '用户角色'
  },
  // <<< 重新添加 accountBookIds 字段定义 >>>
  {
    field: 'accountBookIds', label: '关联账套', type: 'select', options: allAccountBooks.value,
    // rules: [{ required: !isViewing.value, message: '请至少选择一个账套' }], // 根据业务需求决定是否必填
    props: { 
      multiple: true, 
      clearable: true, 
      collapseTags: false, 
    }, 
    md: 24,
    lg: 24,
    xl: 24,
    disabled: isViewing.value,
    group: '关联账套' // 放在新分组
  },
  // <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
]);

// --- Dialog Title ---
const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增用户';
  if (formMode.value === 'edit') return '编辑用户';
  if (formMode.value === 'view') return '查看用户';
  return '用户管理';
});

// --- Lifecycle ---
onMounted(() => {
  loadData();
  loadRoles();
  loadEmployees();
  loadAccountBooks();
  loadGenderOptions();
});

// --- Methods ---
const loadData = async () => {
  loading.value = true;
  try {
    // --- Format sort parameter from local state ---
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop) {
        const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
        sortString = `${currentSort.value.prop},${direction}`;
    }

    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...currentFilters.value, // Use local filters
      sort: sortString // Use formatted local sort
    };

    console.log('[loadData] Fetching user list with params:', params);
    // --- FIX: Use Type Assertion --- 
    // const res = await getUserList(params) as unknown as UserListResponse;
    // console.log('[loadData] Response received (intercepted):', JSON.stringify(res));

    // --- 修改：增强对 getUserList 返回值的处理 --- 
    const response = await getUserList(params);
    console.log('[loadData] Response received from getUserList (should be UserListResponse):', JSON.stringify(response));

    if (response && typeof response === 'object' && response.hasOwnProperty('list') && Array.isArray(response.list) && response.hasOwnProperty('total') && typeof response.total === 'number') {
      // 预期的数据结构 { list: [], total: X }
      const res = response as UserListResponse;

      if (currentRow.value) { // Check if currentRow still holds the edited user info
        const editedUserInList = res.list.find(user => user.id === currentRow.value.id);
        if (editedUserInList) {
          console.log(`[loadData] Mobile for user ${currentRow.value.id} in fetched list:`, editedUserInList.mobile);
        } else {
          console.log(`[loadData] Edited user ${currentRow.value.id} not found on current page (${pagination.currentPage}).`);
        }
      }
      
      tableData.value = res.list;
      pagination.total = res.total; // 确保使用正确的 total
    } else if (Array.isArray(response)) {
      // 兼容模式：如果直接返回了数组 (UserListItem[])，说明拦截器可能改变了数据结构
      console.warn('getUserList 返回的数据格式不符合预期的 UserListResponse (包含 list 和 total)，而是直接返回了一个数组。',
                   '这可能是因为 `utils/request.ts` 中的请求拦截器直接返回了 `res.data.list` 或类似的结构。',
                   '表格将显示数据，但分页总数可能不正确。收到的数据:', response);
      tableData.value = response; // 直接使用数组
      pagination.total = response.length; // 尝试使用当前页的长度作为 total，但这通常不准确
      ElMessage.warning('获取用户列表成功，但分页信息可能不完整。');
    } else {
      // 如果 res 或 res.list 不存在，或结构完全不符
      tableData.value = [];
      pagination.total = 0;
      console.warn('getUserList 返回的数据格式不符合预期 (拦截器处理后):', response);
    }
    // --- 修改结束 ---

  } catch (error) {
    console.error('Error loading user list:', error);
    const errorMessage = getApiErrorMessage(error);
    // ElMessage.error(errorMessage); // 旧的调用方式
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
    // 出错时也清空数据
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const loadRoles = async () => {
  try {
    // --- FIX: Use Type Assertion --- 
    const res = await getRoles() as unknown as { list: RoleSimpleItem[], total: number }; // Assume structure
    
    if (res && Array.isArray(res.list)) { 
      allRoles.value = res.list.map((role: RoleSimpleItem) => ({ 
          label: role.name, 
          value: role.id 
      }));
      console.log('Roles loaded for dropdown:', allRoles.value);
    } else {
      console.warn('getRoles did not return the expected structure { list: [] }:', res);
      allRoles.value = []; // 清空列表以防万一
    }

  } catch (error) {
    // 这里的 catch 理论上不应该再因为处理空列表而被触发
    // 但保留以处理真正的 API 调用错误或其他意外
    console.error('Error loading roles:', error);
    const errorMessage = getApiErrorMessage(error);
    // ElMessage.error(errorMessage); // 旧的调用方式
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
    allRoles.value = []; // 出错时也清空
  }
};

const loadEmployees = async () => {
  try {
    // --- FIX: Use Type Assertion --- 
    const res = await getEmployeeSimpleList() as unknown as { list: EmployeeSimpleItem[], total: number }; // Assume structure
    
    if (res && Array.isArray(res.list)) { 
      allEmployees.value = res.list.map((emp: EmployeeSimpleItem) => ({ 
          label: `${emp.employeeCode} - ${emp.employeeName}`, // 修改label的格式
          value: emp.id 
      }));
      console.log('Employees loaded for dropdown:', allEmployees.value);
    } else {
      console.warn('getEmployeeSimpleList did not return the expected structure { list: [] }:', res);
      allEmployees.value = []; // 清空列表以防万一
    }

  } catch (error) {
    // 这里的 catch 理论上不应该再因为处理空列表而被触发
    // 但保留以处理真正的 API 调用错误或其他意外
    console.error('Error loading employees:', error);
    const errorMessage = getApiErrorMessage(error);
    // ElMessage.error(errorMessage); // 旧的调用方式
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
    allEmployees.value = []; // 出错时也清空
  }
};

const loadAccountBooks = async () => {
  try {
    // 假设 listAccountBooks 返回 AccountBookVO[] 或 { list: AccountBookVO[] }
    // 需要根据实际 API 返回调整
    const res = await listAccountBooks() as unknown as AccountBookVO[] | { list: AccountBookVO[] };
    let books: AccountBookVO[] = [];
    if (Array.isArray(res)) {
      books = res;
    } else if (res && Array.isArray(res.list)) {
      books = res.list;
    } else {
      console.warn('listAccountBooks did not return the expected structure:', res);
    }
    
    allAccountBooks.value = books.map((book: AccountBookVO) => ({
      label: book.name, // 假设用 name 作为显示
      value: book.id
    }));
    console.log('Account books loaded for dropdown:', allAccountBooks.value);

  } catch (error) {
    console.error('Error loading account books:', error);
    const errorMessage = getApiErrorMessage(error);
    // ElMessage.error(errorMessage); // 旧的调用方式
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
    allAccountBooks.value = [];
  }
};

const loadGenderOptions = async () => {
  try {
    const res = await getDictDataByCode('gender');
    genderOptions.value = res || [];
    console.log('Gender options loaded:', genderOptions.value);
  } catch (error) {
    console.error('Error loading gender options:', error);
    genderOptions.value = [];
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  }
};

// 打开弹窗（新增、编辑或查看）
const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: any) => {
  formMode.value = mode;
  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id) {
        console.error('编辑或查看模式下缺少用户数据或ID');
        return;
    }
    currentRow.value = rowData;
    dialogVisible.value = true;
    loading.value = true;
    try {
        // 拦截器已处理，getUserDetail 返回 UserDetail 或 undefined
        // 使用 as unknown as 进行强制类型转换
        const detailData = await getUserDetail(rowData.id) as unknown as UserDetail | undefined;

        // 检查 detailData 是否是一个有效的非 null 对象
        if (detailData && typeof detailData === 'object') {
            // ---- 开始处理有效的 detailData ----
            if ((detailData as any).expireTime && typeof (detailData as any).expireTime === 'string' && (detailData as any).expireTime.startsWith('0001-01-01')) {
                (detailData as any).expireTime = null;
            }
            const roleIds = Array.isArray((detailData as any).roles) ? (detailData as any).roles.map((r: any) => r.id) : [];
            
            let accountBookIds: number[] = [];
            if (Array.isArray((detailData as any).accountBooks)) { 
                try {
                    accountBookIds = ((detailData as any).accountBooks as any[])
                        .map((book: any) => book?.id)
                        .filter((id: any): id is number => typeof id === 'number');
                    console.log(`[handleOpenForm] Extracted account book IDs from user detail:`, accountBookIds);
                } catch (mapError) {
                     console.error(`[handleOpenForm] Error mapping account books from user detail:`, mapError);
                }
            } else {
                console.log(`[handleOpenForm] No accountBooks array found in user detail response or it's not an array.`);
            }
            
            formData.value = { ...detailData, roleIds: roleIds, accountBookIds: accountBookIds }; 
            // ---- 性别字段类型转换 (数字转字符串，用于回显) ----
            if (formData.value['gender'] !== null && formData.value['gender'] !== undefined) {
              formData.value['gender'] = String(formData.value['gender']);
            }
            // ---- 结束处理有效的 detailData ----

        } else {
             // 如果 detailData 无效 (null, undefined, 或非对象)
             console.warn(`[handleOpenForm] Received invalid or empty detailData from API for user ${rowData.id}:`, detailData);
             const errorMessage = getApiErrorMessage({ message: '获取用户详细信息失败或数据为空。' }); // 包装一下以便复用逻辑
             // ElMessage.error(errorMessage); // 旧的调用方式
             ElMessage({
                type: 'error',
                dangerouslyUseHTMLString: true,
                message: errorMessage,
                showClose: true,
                duration: 5 * 1000
             });
        }

    } catch (error) {
        console.error('[handleOpenForm] Error processing user detail:', error);
        const errorMessage = getApiErrorMessage(error);
        // ElMessage.error(errorMessage); // 旧的调用方式
        ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
        });
        const roleIdsFallback = rowData.roles ? rowData.roles.map((r: any) => r.id) : [];
        formData.value = { ...rowData, roleIds: roleIdsFallback, accountBookIds: [] }; // 保证 accountBookIds 存在
        delete formData.value['password']; 
    } finally {
         loading.value = false;
    }

  } else { // 新增模式
    formData.value = {
      status: 1,
      gender: null, // 性别默认为null，如果字典加载后有默认值，也可以设为对应的字符串value
      isAdmin: false,
      roleIds: [],
      username: '',
      accountBookIds: [],
      employeeId: null,
      avatar: null
    };
    currentRow.value = null;
    dialogVisible.value = true;
  }

  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentRow.value = null;
};

// 提交表单
const submitForm = async () => {
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  // Prepare payload 
  const preparePayload = (): Partial<UserFormData> => { // Return type matching API expects
    const payload: Record<string, any> = {};
    const sourceData = formData.value;

    console.log(`[preparePayload] Checking sourceData.mobile:`, sourceData['mobile']); // --- FIX: Use bracket notation --- 

    // Common optional fields 
    const commonFields = [
      'nickname', 'realName', 'avatar', /*'gender',*/ 'email', 'mobile', 
      'status', 'remark', 'expireTime', 'employeeId', 'roleIds', 'tenantId',
      'accountBookIds'
    ];
    commonFields.forEach(key => {
      if (sourceData.hasOwnProperty(key) && sourceData[key] !== null && sourceData[key] !== undefined) {
        payload[key] = sourceData[key]; // Already uses bracket notation implicitly
      }
    });

    // Explicitly handle gender conversion (String from form to Number for backend)
    if (sourceData.hasOwnProperty('gender') && sourceData['gender'] !== null && sourceData['gender'] !== undefined) {
      const genderStr = String(sourceData['gender']);
      if (genderStr.trim() === '') {
        payload['gender'] = undefined; // Or null, depending on DTO
      } else {
        const numGender = Number(genderStr);
        if (isNaN(numGender)) {
          console.warn(`Gender value "${genderStr}" could not be converted to number for submission.`);
          payload['gender'] = undefined; // Or handle as error / send original string if backend expects it
        } else {
          payload['gender'] = numGender;
        }
      }
    } else if (sourceData.hasOwnProperty('gender')) { // if it was explicitly set to null/undefined
        payload['gender'] = undefined; // or null
    }
    
    if (formMode.value === 'add') {
      // --- FIX: Ensure required fields for UserFormData are present --- 
      payload['username'] = sourceData['username']; // Use bracket notation
      payload['password'] = sourceData['password']; // Use bracket notation
      // Add nickname and roleIds if they are required by UserFormData and not already added reliably by the loop
      if (!payload.hasOwnProperty('nickname')) {
          payload['nickname'] = sourceData['nickname']; // Ensure nickname is included
      }
      if (!payload.hasOwnProperty('roleIds')) { 
          payload['roleIds'] = sourceData['roleIds'] || []; // Ensure roleIds are included (as array)
      }
    }

    console.log('[submitForm] Payload prepared:', JSON.stringify(payload));
    // Ensure the final payload conforms to what the API expects (Partial<UserFormData> for update, UserFormData for add)
    // The API function type handles this, but we ensure required fields are present for 'add'
    return payload as Partial<UserFormData>; 
  };

  const dataToSend = preparePayload();

  loading.value = true;
  try {
    if (formMode.value === 'add') {
      // --- FIX: Cast dataToSend if addUser requires full UserFormData --- 
      await addUser(dataToSend as UserFormData); // Cast to satisfy potential stricter requirement
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentRow.value) {
      await updateUser(currentRow.value.id, dataToSend); // Partial is usually fine for update
      ElMessage.success('编辑成功');
    }
    handleCloseDialog();
    loadData(); // Refresh table data
  } catch (error) {
    console.error(`Error saving user:`, error);
    const errorMessage = getApiErrorMessage(error);
    // ElMessage.error(errorMessage); // 旧的调用方式
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
};

// 查看详情
const handleView = (row: any) => {
  handleOpenForm('view', row);
};

// 处理新增按钮点击
const handleAdd = () => {
  handleOpenForm('add');
};

// 处理编辑按钮点击
const handleEdit = (row: any) => {
  handleOpenForm('edit', row);
};

// 处理删除按钮点击
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定删除用户 "${row.nickname}" 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    // --- 调用 API ---
    await deleteUser(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting user:', error);
      const errorMessage = getApiErrorMessage(error);
      // ElMessage.error(errorMessage); // 旧的调用方式
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

// 处理重置密码按钮点击
const handleResetPassword = async (row: any) => {
  try {
    const { value } = await ElMessageBox.prompt(`请输入用户 "${row.nickname}" 的新密码`, '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'password',
      inputValidator: (val) => {
        if (!val || val.length < 6) {
          return '密码长度不能少于6位';
        }
        return true;
      },
      inputErrorMessage: '密码格式不正确'
    });

    loading.value = true;
    // --- 调用 API ---
    await resetPassword(row.id, value);
    ElMessage.success('密码重置成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error resetting password:', error);
      const errorMessage = getApiErrorMessage(error);
      // ElMessage.error(errorMessage); // 旧的调用方式
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消重置密码');
    }
  } finally {
    loading.value = false;
  }
};

// 处理批量删除按钮点击
const handleBatchDelete = async (selectedRows: any[]) => {
  if (!selectedRows || selectedRows.length === 0) {
    ElMessage.warning('请先选择要删除的用户');
    return;
  }
  try {
    await ElMessageBox.confirm(`确定删除选中的 ${selectedRows.length} 个用户吗?`, '确认批量删除', { type: 'warning' });
    loading.value = true;
    const ids = selectedRows.map(row => row.id);
    // --- 调用批量删除 API ---
    await batchDeleteUsers(ids);
    ElMessage.success('批量删除成功');
    loadData();
    vnTableRef.value?.clearSelection();
  } catch (error) {
     if (error !== 'cancel') {
        console.error('Error batch deleting users:', error);
        const errorMessage = getApiErrorMessage(error);
        // ElMessage.error(errorMessage); // 旧的调用方式
        ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
        });
     } else {
        ElMessage.info('已取消批量删除');
     }
  } finally {
     loading.value = false;
  }
};

// 处理导入按钮点击
const handleImport = () => {
  ElMessage.info('导入功能待实现');
};

// 处理导出按钮点击
const handleExport = () => {
  ElMessage.info('导出功能待实现');
};

// 处理筛选
const handleFilterChange = (filters: Record<string, any>) => { // Use Record<string, any> if FilterParams removed
    console.log('Filter changed:', filters);
    currentFilters.value = filters;
    pagination.currentPage = 1; 
    loadData(); 
};

// --- VNTable 事件处理 ---
const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

// 修改 handleSortChange 以获取排序参数并传递
const handleSortChange = (sort: { prop: string; order: 'ascending' | 'descending' | null }) => {
  console.log('Sort changed:', sort);
  currentSort.value = sort;
  loadData(); 
};

const handleSelectionChange = (rows: any[]) => {
  console.log('选中行变更:', rows);
};

// --- 新增：打印处理函数（占位）---
const handlePrintPreview = () => {
  if (!currentRow.value) return;
  console.log('触发打印预览，用户:', currentRow.value);
  ElMessage.info('打印预览功能待实现');
  // TODO: Implement print preview logic, potentially opening a new window/tab
};

const handlePrint = () => {
  if (!currentRow.value) return;
  console.log('触发打印，用户:', currentRow.value);
  ElMessage.info('打印功能待实现');
  // TODO: Implement print logic, potentially directly triggering browser print
};

</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

/* 新增：使分页组件靠右 */
:deep(.el-pagination) {
  margin-top: 15px; /* 可选：添加一些顶部间距 */
  justify-content: flex-end; /* 使用 flexbox 将内容推到右侧 */
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
</style>
