<template>
  <div class="app-nav">
    <el-menu 
      mode="horizontal" 
      :router="true" 
      :default-active="activeRoute"
      class="nav-menu"
    >
      <!-- 移除 TableList 示例菜单项 -->
      <!-- <el-menu-item index="/tablelist-example">TableList 示例</el-menu-item> -->
      <el-menu-item index="/component-examples/vntable-example">VNTable 示例</el-menu-item>
      <el-menu-item index="/component-examples/vnform">VNForm 示例</el-menu-item>
      <el-menu-item index="/component-examples/vnbreadcrumb">VNBreadcrumb 示例</el-menu-item>
      <el-menu-item index="/component-examples/vnsidebar">VNSidebar 示例</el-menu-item>
      <el-menu-item index="/component-examples/vnstatusbar">VNStatusBar 示例</el-menu-item>
      <el-menu-item index="/component-examples/ep-examples">EP 内建组件示例</el-menu-item>
      <el-menu-item index="/component-examples/vnchart">VNChart 图表示例</el-menu-item>
      <el-menu-item index="/component-examples/vndialog">VNDialog 对话框示例</el-menu-item>
      <el-menu-item index="/component-examples/vndrawer">VNDrawer 抽屉示例</el-menu-item>
      <el-menu-item index="/component-examples/vntree">VNTree 树示例</el-menu-item>
      <el-menu-item index="/component-examples/vntabs">VNTabs 标签页示例</el-menu-item>
      <el-menu-item index="/component-examples/vnframe">VNFrame 布局示例</el-menu-item>
      <el-menu-item index="/component-examples/vnlogin">VNLogin 示例</el-menu-item>
      <el-menu-item index="/component-examples/vnprintpreview">打印预览示例</el-menu-item>
    </el-menu>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const activeRoute = ref('');

// 监听路由变化，更新当前激活的菜单项
watch(() => route.path, (newPath) => {
  activeRoute.value = newPath;
}, { immediate: true });

onMounted(() => {
  activeRoute.value = route.path;
});
</script>

<style scoped>
.app-nav {
  margin-bottom: 20px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.nav-menu {
  display: flex;
  justify-content: center;
}
</style> 