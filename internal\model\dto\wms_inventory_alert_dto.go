package dto

import (
	"backend/internal/model/entity"
	"backend/pkg/response"
	"time"
)

// WmsInventoryAlertRuleCreateReq 库存预警规则创建请求
type WmsInventoryAlertRuleCreateReq struct {
	AccountBookID         uint                          `json:"accountBookId" binding:"required" comment:"账套ID"`
	RuleName              string                        `json:"ruleName" binding:"required" comment:"规则名称"`
	RuleType              entity.WmsInventoryAlertType  `json:"ruleType" binding:"required" comment:"预警类型"`
	ItemID                *uint                         `json:"itemId" comment:"物料ID"`
	WarehouseID           *uint                         `json:"warehouseId" comment:"仓库ID"`
	LocationID            *uint                         `json:"locationId" comment:"库位ID"`
	ThresholdValue        float64                       `json:"thresholdValue" binding:"required,gt=0" comment:"阈值"`
	ComparisonOperator    string                        `json:"comparisonOperator" binding:"required" comment:"比较操作符"`
	AlertLevel            entity.WmsInventoryAlertLevel `json:"alertLevel" binding:"required" comment:"预警级别"`
	CheckFrequencyMinutes int                           `json:"checkFrequencyMinutes" binding:"required,gt=0" comment:"检查频率(分钟)"`
	Description           *string                       `json:"description" comment:"描述"`
}

// WmsInventoryAlertRuleUpdateReq 库存预警规则更新请求
type WmsInventoryAlertRuleUpdateReq struct {
	RuleName              *string                        `json:"ruleName" comment:"规则名称"`
	RuleType              *entity.WmsInventoryAlertType  `json:"ruleType" comment:"预警类型"`
	ItemID                *uint                          `json:"itemId" comment:"物料ID"`
	WarehouseID           *uint                          `json:"warehouseId" comment:"仓库ID"`
	LocationID            *uint                          `json:"locationId" comment:"库位ID"`
	ThresholdValue        *float64                       `json:"thresholdValue" comment:"阈值"`
	ComparisonOperator    *string                        `json:"comparisonOperator" comment:"比较操作符"`
	AlertLevel            *entity.WmsInventoryAlertLevel `json:"alertLevel" comment:"预警级别"`
	CheckFrequencyMinutes *int                           `json:"checkFrequencyMinutes" comment:"检查频率(分钟)"`
	IsActive              *bool                          `json:"isActive" comment:"是否启用"`
	Description           *string                        `json:"description" comment:"描述"`
}

// WmsInventoryAlertRuleQueryReq 库存预警规则查询请求
type WmsInventoryAlertRuleQueryReq struct {
	response.PageQuery
	RuleName     *string                        `json:"ruleName" comment:"规则名称"`
	RuleType     *entity.WmsInventoryAlertType  `json:"ruleType" comment:"预警类型"`
	ItemID       *uint                          `json:"itemId" comment:"物料ID"`
	ItemSku      *string                        `json:"itemSku" comment:"物料SKU"`
	WarehouseID  *uint                          `json:"warehouseId" comment:"仓库ID"`
	LocationID   *uint                          `json:"locationId" comment:"库位ID"`
	AlertLevel   *entity.WmsInventoryAlertLevel `json:"alertLevel" comment:"预警级别"`
	IsActive     *bool                          `json:"isActive" comment:"是否启用"`
	CreatedBy    *uint                          `json:"createdBy" comment:"创建人ID"`
	CreatedStart *time.Time                     `json:"createdStart" comment:"创建开始时间"`
	CreatedEnd   *time.Time                     `json:"createdEnd" comment:"创建结束时间"`
}

// WmsInventoryAlertLogQueryReq 库存预警日志查询请求
type WmsInventoryAlertLogQueryReq struct {
	response.PageQuery
	RuleID         *uint                              `json:"ruleId" comment:"规则ID"`
	InventoryID    *uint                              `json:"inventoryId" comment:"库存ID"`
	ItemID         *uint                              `json:"itemId" comment:"物料ID"`
	ItemSku        *string                            `json:"itemSku" comment:"物料SKU"`
	WarehouseID    *uint                              `json:"warehouseId" comment:"仓库ID"`
	LocationID     *uint                              `json:"locationId" comment:"库位ID"`
	AlertType      *entity.WmsInventoryAlertType      `json:"alertType" comment:"预警类型"`
	AlertLevel     *entity.WmsInventoryAlertLevel     `json:"alertLevel" comment:"预警级别"`
	Status         *entity.WmsInventoryAlertLogStatus `json:"status" comment:"预警状态"`
	AcknowledgedBy *uint                              `json:"acknowledgedBy" comment:"确认人ID"`
	CreatedStart   *time.Time                         `json:"createdStart" comment:"创建开始时间"`
	CreatedEnd     *time.Time                         `json:"createdEnd" comment:"创建结束时间"`
}

// WmsInventoryAlertAcknowledgeReq 库存预警确认请求
type WmsInventoryAlertAcknowledgeReq struct {
	AcknowledgedBy uint   `json:"acknowledgedBy" binding:"required" comment:"确认人ID"`
	Remark         string `json:"remark" comment:"确认备注"`
}

// WmsInventoryAlertResolveReq 库存预警解决请求
type WmsInventoryAlertResolveReq struct {
	Remark string `json:"remark" comment:"解决备注"`
}

// WmsInventoryAlertBatchAcknowledgeReq 批量确认预警请求
type WmsInventoryAlertBatchAcknowledgeReq struct {
	IDs            []uint `json:"ids" binding:"required" comment:"预警ID列表"`
	AcknowledgedBy uint   `json:"acknowledgedBy" binding:"required" comment:"确认人ID"`
}

// WmsInventoryAlertBatchResolveReq 批量解决预警请求
type WmsInventoryAlertBatchResolveReq struct {
	IDs []uint `json:"ids" binding:"required" comment:"预警ID列表"`
}

// WmsInventoryAlertBatchActivateReq 批量激活规则请求
type WmsInventoryAlertBatchActivateReq struct {
	IDs []uint `json:"ids" binding:"required" comment:"规则ID列表"`
}

// WmsInventoryAlertBatchDeactivateReq 批量停用规则请求
type WmsInventoryAlertBatchDeactivateReq struct {
	IDs []uint `json:"ids" binding:"required" comment:"规则ID列表"`
}

// WmsInventoryAlertStatsReq 库存预警统计请求
type WmsInventoryAlertStatsReq struct {
	WarehouseID *uint                          `json:"warehouseId" comment:"仓库ID"`
	AlertType   *entity.WmsInventoryAlertType  `json:"alertType" comment:"预警类型"`
	AlertLevel  *entity.WmsInventoryAlertLevel `json:"alertLevel" comment:"预警级别"`
	DateStart   *time.Time                     `json:"dateStart" comment:"开始日期"`
	DateEnd     *time.Time                     `json:"dateEnd" comment:"结束日期"`
	GroupBy     *string                        `json:"groupBy" comment:"分组方式: day/week/month"`
}

// WmsInventoryAlertNotificationSettingsReq 预警通知设置请求
type WmsInventoryAlertNotificationSettingsReq struct {
	EmailEnabled    bool     `json:"emailEnabled" comment:"是否启用邮件通知"`
	SmsEnabled      bool     `json:"smsEnabled" comment:"是否启用短信通知"`
	SystemEnabled   bool     `json:"systemEnabled" comment:"是否启用系统通知"`
	AlertLevels     []string `json:"alertLevels" comment:"通知的预警级别"`
	AlertTypes      []string `json:"alertTypes" comment:"通知的预警类型"`
	QuietHoursStart *string  `json:"quietHoursStart" comment:"免打扰开始时间"`
	QuietHoursEnd   *string  `json:"quietHoursEnd" comment:"免打扰结束时间"`
}

// GetValidAlertLevels 获取有效的预警级别
func (req *WmsInventoryAlertNotificationSettingsReq) GetValidAlertLevels() []string {
	validLevels := []string{"INFO", "WARNING", "CRITICAL"}
	var result []string

	for _, level := range req.AlertLevels {
		for _, valid := range validLevels {
			if level == valid {
				result = append(result, level)
				break
			}
		}
	}

	return result
}

// GetValidAlertTypes 获取有效的预警类型
func (req *WmsInventoryAlertNotificationSettingsReq) GetValidAlertTypes() []string {
	validTypes := []string{"MIN_STOCK", "MAX_STOCK", "EXPIRY", "SLOW_MOVING"}
	var result []string

	for _, alertType := range req.AlertTypes {
		for _, valid := range validTypes {
			if alertType == valid {
				result = append(result, alertType)
				break
			}
		}
	}

	return result
}
