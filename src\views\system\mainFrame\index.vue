<template>
  <VNFrame
    :title="systemTitle"
    :logo-url="logoUrl"
    :sidebar-items="sidebarMenuItems"
    :unique-opened="true"
    :user-info="userInfo"
    :show-breadcrumb="true"
    :breadcrumb-height="'45px'"
    :breadcrumb-items="breadcrumbItems"
    v-model:sidebar-collapsed="isSidebarCollapsed"
    :show-tabs="true"
    :show-footer="true"
    @menu-select="handleMenuSelect"
    @command="handleUserCommand"
    @tab-change="handleTabChange"
    @tab-close="handleTabClose"
  >
    <!-- Main content area for child routes -->
    <router-view v-slot="{ Component }">
      <component :is="Component" :key="route.fullPath" /> <!-- Add key for proper keep-alive with params -->
    </router-view>

    <!-- Optional: Footer Slot -->
    <template #footer>
      <span>© {{ new Date().getFullYear() }} MiSoft. All Rights Reserved.</span>
    </template>

  </VNFrame>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute, type RouteLocationMatched } from 'vue-router';
import VNFrame from '@/components/VNFrame/index.vue'; // Ensure alias '@' points to src
import type { SidebarItem } from '@/components/VNSidebar/types'; // Import correct menu item type
import { ElMessageBox, ElMessage } from 'element-plus'; // Import MessageBox and Message
// Import Pinia store
import { useUserStore } from '@/store/modules/user';
import type { BreadcrumbItem } from '@/components/VNBreadcrumb/types'; // Import BreadcrumbItem type

// Define UserInfo type locally as it's not exported by VNFrame/types
interface UserInfo {
  nickname?: string;
  username?: string;
  avatar?: string;
  dropdownItems?: Array<{ command: string | number | object; text: string; divided?: boolean }>;
}

// Get store instance
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();

// --- Sidebar Collapse State ---
const isSidebarCollapsed = ref(false);

// --- Breadcrumb State --- 
const breadcrumbItems = ref<BreadcrumbItem[]>([]);

// Function to generate breadcrumb items based on the current route's matched records
const generateBreadcrumbs = (matched: RouteLocationMatched[]) => {
  // Filter routes that have a title defined in meta.
  const crumbs = matched
    // 使用方括号访问 meta['title']
    .filter(item => item.meta && item.meta['title'])
    .map(item => ({
      // 使用方括号访问 meta['title']
      label: item.meta['title'] as string,
      to: item.path,
    }));

  // Directly assign the generated crumbs
  breadcrumbItems.value = crumbs;

  console.log('Generated breadcrumbs:', JSON.stringify(breadcrumbItems.value));
};

// Watch for route changes and update breadcrumbs
watch(
  () => route.matched,
  (newMatched) => {
    // Ensure newMatched is valid before processing
    if (Array.isArray(newMatched) && newMatched.length > 0) {
       console.log('Route matched changed, paths:', newMatched.map(r => r.path));
      generateBreadcrumbs(newMatched);
    } else {
        // Handle cases where matched routes might be empty temporarily (e.g., during transitions)
        breadcrumbItems.value = [{ label: 'Dashboard', to: '/dashboard' }]; // Default to dashboard
        console.log('Route matched empty or invalid, defaulting breadcrumbs.');
    }
  },
  { immediate: true } // Run immediately on component mount
);

// --- Configuration Data (Replace mock data with actual data from store/API) ---
const systemTitle = ref('MiSoft 后台管理系统');
// const logoUrl = computed(() => appStore.logo); // Example from store
const logoUrl = ref(''); // Optional: Provide a logo path or load from config/store

// --- Menu Data (from Store) ---
// REMOVED local menuItems ref
const sidebarMenuItems = computed(() => {
  console.log('[mainFrame] Computed sidebarMenuItems accessed. Value:', userStore.accessibleMenus);
  return userStore.accessibleMenus;
});

// --- User Info ---
// userInfo is now a computed property returning the UserInfo object
const userInfo = computed<UserInfo>(() => ({
  nickname: userStore.nickname,
  username: userStore.username,
  avatar: userStore.avatar || '',
  dropdownItems: [
    { command: 'profile', text: '个人中心' },
    { command: 'examples', text: '组件示例' },
    { command: 'logout', text: '退出登录', divided: true },
  ],
}));

// --- Tabs and Keep-Alive ---
// You'll need a mechanism (like Pinia store) to manage active tabs and cached views
/* Commented out as keep-alive is removed for now
const cachedViews = computed<string[]>(() => {
  // Example using a store: return appStore.cachedViews;
  // Return array of route names to keep-alive
  // Make sure the <component :is="Component"> has a unique :key for keep-alive to work correctly with params
  return []; // Replace with your actual caching logic
});
*/

// --- Event Handlers ---
const handleMenuSelect = (index: string, indexPath: string[]) => {
  console.log('Menu selected:', index, indexPath);
  if (index && !index.startsWith('external:')) {
      // Use sidebarMenuItems.value to find the item
      const menuItem = findMenuItem(sidebarMenuItems.value, index); 
      if (menuItem && (!menuItem.children || menuItem.children.length === 0)) {
         router.push(index);
      } else if (menuItem && menuItem.children && menuItem.children.length > 0) {
          console.log('Clicked parent menu:', index);
      }
  } else if (index && index.startsWith('external:')) {
      window.open(index.substring(9), '_blank');
  }
};

// Helper to find menu item (using sidebarMenuItems.value)
function findMenuItem(items: SidebarItem[], index: string): SidebarItem | null {
    for (const item of items) {
        if (item.index === index) return item;
        if (item.children) {
            const found = findMenuItem(item.children, index);
            if (found) return found;
        }
    }
    return null;
}

const handleUserCommand = async (command: string | number | object) => {
  console.log('User command:', command);
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm(
        '您确定要退出当前账户吗？',
        '退出确认',
        {
          confirmButtonText: '确定退出',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );

      // --- User Confirmed: Execute Logout via Store --- 
      console.log('执行退出登录 via store...');
      await userStore.Logout(); // Call store action
      console.log('Store Logout action completed.');

      // Redirect to login page AFTER store action completes
      router.push('/login');
      ElMessage({ type: 'success', message: '已成功退出登录' });

    } catch (error) {
      // --- User Canceled or Error during logout --- 
      if (error === 'cancel') { // Check if error is due to cancellation
        console.log('取消退出登录');
        ElMessage({ type: 'info', message: '已取消退出' });
      } else {
        console.error('Logout process failed:', error);
        // Optionally show an error message to the user
        ElMessage({ type: 'error', message: '退出登录时发生错误' });
      }
    }

  } else if (command === 'profile') {
    router.push('/profile');
  } else if (command === 'examples') {
    router.push('/component-examples');
  } else if (command === 'settings') {
    router.push('/settings');
  }
};

const handleTabChange = (tabIndex: string) => {
  // Navigate router when tab is clicked
   console.log('Tab changed to:', tabIndex);
   // router.push(tabIndex); // Assuming tab index is the route path
   // This might be handled automatically by VNFrame if it integrates with vue-router,
   // or you might need to implement the logic based on how VNFrame manages tabs.
   // Check VNFrame's documentation or implementation for tab navigation.
};

const handleTabClose = (tabIndex: string) => {
  // Logic to close a tab
   console.log('Tab closed:', tabIndex);
   // 1. Remove tab from your tab management state (e.g., Pinia store)
   // 2. Remove view from cachedViews if it was cached
   // 3. Navigate to another tab (e.g., the previous one or the dashboard)
};

console.log('[mainFrame] Before VNFrame render. sidebarMenuItems:', sidebarMenuItems.value);
console.log('[mainFrame] Before VNFrame render. breadcrumbItems:', breadcrumbItems.value);

</script>

<style scoped>
/* Optional: Add specific styles for the layout if needed */

/* Transition styles removed or commented out if not needed */
/*
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: opacity 0.3s ease;
}

.fade-transform-enter-from,
.fade-transform-leave-to {
  opacity: 0;
}
*/

/* Ensure the frame takes full height and width */
:deep(.vn-frame-new) { /* Corrected selector to match VNFrame root class */
  height: 100vh;
  width: 100%; /* Add width to fill parent */
  /* Consider adding display: flex; if needed for internal layout */
}
</style>
