// ==================== 导入类型 ====================
import type {
  WmsAllocationStrategy,
  WmsAllocationStatus,
  WmsInventoryAllocationResp,
  WmsInventoryAllocationSummaryResp,
  WmsInventoryAvailabilityResp,
  WmsInventoryLocationAvailabilityResp,
  WmsAllocationStatusResp,
  WmsAllocationStatusDetailResp
} from '@/api/wms/inventoryAllocation'

// ==================== 基础类型 ====================

// 选择器选项类型
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  color?: string
}

// ==================== 表单数据类型 ====================

// 库存分配行数据（用于表单编辑）
export interface AllocationRowData {
  id?: number
  outboundDetailId: number
  inventoryId: number
  allocatedQty: number
  pickedQty?: number
  allocationStrategy: WmsAllocationStrategy
  status: WmsAllocationStatus
  allocatedAt?: string
  pickedAt?: string
  releasedAt?: string
  remark?: string
  // 扩展字段
  itemCode?: string
  itemName?: string
  batchNo?: string
  locationCode?: string
  availableQty?: number
  productionDate?: string
  expiryDate?: string
  // 表单状态标识
  _isNew?: boolean
  _toDelete?: boolean
  _hasChanged?: boolean
  _editing?: boolean
}

// 库存分配表单数据
export interface InventoryAllocationFormData {
  // 主表数据
  notificationId: number
  strategy: WmsAllocationStrategy
  forceReallocation?: boolean
  remark?: string
  
  // 明细表数据
  allocations: AllocationRowData[]
  
  // 表单状态
  _mode?: 'auto' | 'manual' | 'view'
  _loading?: boolean
  _submitting?: boolean
}

// 手动分配表单数据
export interface ManualAllocationFormData {
  outboundDetailId: number
  itemId: number
  itemCode: string
  itemName: string
  requiredQty: number
  allocatedQty: number
  availableInventories: AvailableInventoryData[]
  selectedAllocations: SelectedAllocationData[]
  remark?: string
}

// 可用库存数据
export interface AvailableInventoryData {
  inventoryId: number
  locationId: number
  locationCode: string
  batchNo?: string
  availableQty: number
  productionDate?: string
  expiryDate?: string
  quality?: string
  cost?: number
  selected?: boolean
  allocatedQty?: number
}

// 选中的分配数据
export interface SelectedAllocationData {
  inventoryId: number
  locationCode: string
  batchNo?: string
  availableQty: number
  allocatedQty: number
  productionDate?: string
  expiryDate?: string
}

// ==================== 组件Props类型 ====================

// 分配策略选择器Props
export interface AllocationStrategySelectorProps {
  modelValue?: WmsAllocationStrategy
  disabled?: boolean
  size?: 'large' | 'default' | 'small'
}

// 库存检查器Props
export interface InventoryCheckerProps {
  itemId: number
  warehouseId?: number
  requiredQty: number
  visible?: boolean
}

// 分配结果展示Props
export interface AllocationResultProps {
  notificationId: number
  readonly?: boolean
}

// 手动分配对话框Props
export interface ManualAllocationDialogProps {
  visible?: boolean
  outboundDetailId?: number
  itemId?: number
  requiredQty?: number
}

// ==================== 常量定义 ====================

// 分配策略选项
export const ALLOCATION_STRATEGY_OPTIONS: SelectOption[] = [
  { value: 'FIFO', label: '先进先出(FIFO)' },
  { value: 'LIFO', label: '后进先出(LIFO)' },
  { value: 'BATCH_PRIORITY', label: '批次优先' },
  { value: 'EXPIRY_DATE_PRIORITY', label: '过期日期优先' },
  { value: 'LOCATION_PRIORITY', label: '库位优先' }
]

// 分配状态选项
export const ALLOCATION_STATUS_OPTIONS: SelectOption[] = [
  { value: 'PENDING', label: '待分配', color: '#909399' },
  { value: 'ALLOCATED', label: '已分配', color: '#67C23A' },
  { value: 'PICKED', label: '已拣货', color: '#409EFF' },
  { value: 'RELEASED', label: '已释放', color: '#F56C6C' }
]

// 库存质量等级选项
export const INVENTORY_QUALITY_OPTIONS: SelectOption[] = [
  { value: 'A', label: 'A级' },
  { value: 'B', label: 'B级' },
  { value: 'C', label: 'C级' },
  { value: 'DAMAGED', label: '损坏' },
  { value: 'EXPIRED', label: '过期' }
]

// ==================== 工具函数类型 ====================

// 分配策略配置类型
export interface AllocationStrategyConfig {
  strategy: WmsAllocationStrategy
  parameters: Record<string, any>
  priority: number
  enabled: boolean
}

// 分配规则类型
export interface AllocationRule {
  id: string
  name: string
  description: string
  condition: string
  action: string
  priority: number
  enabled: boolean
}

// 分配约束类型
export interface AllocationConstraint {
  type: 'MIN_QTY' | 'MAX_QTY' | 'BATCH_SAME' | 'LOCATION_SAME' | 'EXPIRY_DATE' | 'QUALITY'
  value: any
  message: string
}

// 分配优化配置类型
export interface AllocationOptimizationConfig {
  minimizeLocations: boolean
  minimizeBatches: boolean
  preferNearExpiry: boolean
  preferHighQuality: boolean
  balanceWorkload: boolean
  considerCost: boolean
}

// ==================== 业务逻辑类型 ====================

// 分配算法结果
export interface AllocationAlgorithmResult {
  success: boolean
  allocations: AllocationSuggestion[]
  unallocatedQty: number
  warnings: string[]
  errors: string[]
  performance: {
    executionTime: number
    locationsUsed: number
    batchesUsed: number
    efficiency: number
  }
}

// 分配建议
export interface AllocationSuggestion {
  inventoryId: number
  locationCode: string
  batchNo?: string
  suggestedQty: number
  availableQty: number
  score: number
  reasons: string[]
  warnings?: string[]
}

// 分配冲突
export interface AllocationConflict {
  type: 'INSUFFICIENT_INVENTORY' | 'QUALITY_MISMATCH' | 'EXPIRY_ISSUE' | 'LOCATION_BLOCKED'
  itemId: number
  itemCode: string
  requiredQty: number
  availableQty: number
  message: string
  suggestions: string[]
}

// 分配历史记录
export interface AllocationHistory {
  id: number
  notificationId: number
  operation: 'ALLOCATE' | 'RELEASE' | 'ADJUST'
  strategy: WmsAllocationStrategy
  beforeQty: number
  afterQty: number
  operator: string
  operatedAt: string
  remark?: string
}

// ==================== 事件类型 ====================

// 分配事件
export interface AllocationEvents {
  allocate: (data: any) => void
  release: (allocationId: number, reason?: string) => void
  adjust: (allocationId: number, newQty: number, reason?: string) => void
  optimize: (notificationId: number, config?: AllocationOptimizationConfig) => void
}

// 库存检查事件
export interface InventoryCheckEvents {
  check: (itemId: number, warehouseId?: number) => void
  reserve: (itemId: number, qty: number, reason?: string) => void
  release: (reservationId: number, reason?: string) => void
}

// ==================== 统计分析类型 ====================

// 分配统计
export interface AllocationStatistics {
  totalAllocations: number
  totalQty: number
  averageAllocationTime: number
  successRate: number
  strategyDistribution: Record<WmsAllocationStrategy, number>
  locationUtilization: Array<{
    locationCode: string
    utilizationRate: number
    allocationCount: number
  }>
  itemAnalysis: Array<{
    itemCode: string
    itemName: string
    totalRequiredQty: number
    totalAllocatedQty: number
    allocationRate: number
    averageAllocationTime: number
  }>
}

// 库存可用性分析
export interface InventoryAvailabilityAnalysis {
  totalItems: number
  fullyAvailableItems: number
  partiallyAvailableItems: number
  unavailableItems: number
  totalRequiredQty: number
  totalAvailableQty: number
  availabilityRate: number
  shortageItems: Array<{
    itemCode: string
    itemName: string
    requiredQty: number
    availableQty: number
    shortageQty: number
  }>
}

// ==================== 响应类型扩展 ====================

// 扩展的库存分配响应类型
export interface ExtendedInventoryAllocationResp extends WmsInventoryAllocationResp {
  // 计算字段
  allocationProgress?: number
  efficiency?: number
  
  // 状态标识
  canEdit?: boolean
  canDelete?: boolean
  canRelease?: boolean
  canAdjust?: boolean
  
  // 关联数据
  outboundDetail?: any
  inventory?: any
  item?: any
  location?: any
}

// 扩展的库存可用性响应类型
export interface ExtendedInventoryAvailabilityResp extends WmsInventoryAvailabilityResp {
  // 计算字段
  allocationRate?: number
  reservationRate?: number
  utilizationRate?: number
  
  // 分析数据
  recommendations?: AllocationSuggestion[]
  constraints?: AllocationConstraint[]
  conflicts?: AllocationConflict[]
}

// ==================== 配置类型 ====================

// 分配引擎配置
export interface AllocationEngineConfig {
  defaultStrategy: WmsAllocationStrategy
  enableOptimization: boolean
  maxAllocationTime: number
  batchSize: number
  parallelProcessing: boolean
  cacheEnabled: boolean
  auditEnabled: boolean
}

// 分配通知配置
export interface AllocationNotificationConfig {
  enableNotifications: boolean
  notifyOnSuccess: boolean
  notifyOnFailure: boolean
  notifyOnConflict: boolean
  recipients: string[]
  channels: ('EMAIL' | 'SMS' | 'WEBHOOK')[]
}

// ==================== 导出类型 ====================
export type {
  WmsAllocationStrategy,
  WmsAllocationStatus,
  WmsInventoryAllocationResp,
  WmsInventoryAvailabilityResp,
  WmsInventoryLocationAvailabilityResp,
  WmsAllocationStatusResp,
  WmsAllocationStatusDetailResp
}
