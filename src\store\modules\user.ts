import { defineStore } from 'pinia';
import { ref, computed, shallowRef } from 'vue';
import type { PersistenceOptions } from 'pinia-plugin-persistedstate';
import { type LoginCredentials, login as loginApi, type LoginData } from '@/api/auth'; // 使用 API 函数
// import { getUserProfile } from '@/api/user'; // 假设有获取用户信息的 API
import { setToken, getToken, removeToken } from '@/utils/auth'; // 假设有用于操作 Token 的工具函数
// import type { UserLoginDTO } from '@/types/dto/user'; // Login DTO 类型
// import type { UserInfo } from '@/types/api/user'; // 用户信息类型
// Import SidebarItem type
import type { SidebarItem } from '@/components/VNSidebar/types';
import { HomeFilled, Setting, User, House, Key, Menu } from '@element-plus/icons-vue'; // Import necessary icons for mock data
import { getUserMenuTree, getUserPermissions, getUserAccountBooks, type MenuTreeItem, type AccountBookItem } from '@/api/user';
import { hasPermission } from '@/hooks/usePermission'; // 导入权限检查 Hook

// --- 定义后端返回的数据结构类型 ---
// vo.MenuTreeVO 的简化接口
interface MenuTreeVO {
    id: number;
    parentId: number;
    title: string;
    name: string; // 可能是路由 name
    path: string; // 路由路径
    component: string; // 组件路径
    icon: string | null; // 图标 class 或 name
    type: number; // 1=目录, 2=菜单, 3=按钮
    sort: number;
    hidden: boolean;
    permission: string | null; // 权限标识 (按钮类型可能有)
    children?: MenuTreeVO[]; // 子菜单
}

// vo.AccountBookVO 的简化接口
interface AccountBookVO {
    id: number;
    code: string;
    name: string;
    companyName: string;
    status: number;
    isGroup: boolean;
    isVirtual: boolean;
    // ... 其他可能的字段
}

// 定义 UserInfo 接口 (简化版，对应后端 UserSimpleVO)
interface UserInfo {
    id: number;
    username: string;
    nickname: string;
    realName: string;
    avatar: string;
    status: number;
    isAdmin: boolean;
    defaultAccountBookId?: number | string | null; // 添加默认账套ID
    // 可以根据需要添加 roles, permissions 等
}

// 使用 setup store 写法 (推荐)
export const useUserStore = defineStore('user', () => {
  // State refs
  const token = ref<string | null>(getToken()); // 插件会自动处理初始加载
  const userInfo = ref<UserInfo | null>(null);
  const roles = ref<string[]>([]);
  const permissions = ref<string[]>([]);
  const rawMenuTree = ref<MenuTreeItem[]>([]);
  const accountBooks = ref<AccountBookItem[]>([]);
  const currentAccountBookId = ref<number | string | null>(null); // 初始为 null，由插件恢复

  // --- 将 accessibleMenus 改为 computed 属性 ---
  const accessibleMenus = computed(() => {
    console.log('[Computed accessibleMenus] Recalculating based on rawMenuTree...');
    return transformMenuTreeToSidebarItems(rawMenuTree.value);
  });
  // -------------------------------------------

  // Getters (computed)
  const isAuthenticated = computed(() => !!token.value);
  const username = computed(() => userInfo.value?.username || '');
  const nickname = computed(() => userInfo.value?.nickname || '');
  const avatar = computed(() => userInfo.value?.avatar || '');
  const currentAccountBook = computed(() => {
      return accountBooks.value.find((book: AccountBookItem) => book.id === currentAccountBookId.value) || null;
  });

  // Actions

  // !! 重要：转换后端 MenuTreeVO 到前端 SidebarItem 的函数
  // This function might need adjustment if MenuTreeItem structure significantly
  // differs from the expected MenuTreeVO structure it currently accepts.
  // For now, assuming it works or adapting it is a separate step.
  // The input type is changed to MenuTreeItem[] to match the API response.
  function transformMenuTreeToSidebarItems(nodes: MenuTreeItem[]): SidebarItem[] {
    // --- 添加日志 --- 
    console.log('[transform] Transforming nodes (level start):\n', JSON.stringify(nodes?.map(n => ({id:n?.id, title:n?.title, type:n?.type})), null, 2));
    // ---------------
    if (!nodes) return [];

    // 获取当前用户的权限列表 (移到 filter 之前)
    const userPermissions = permissions.value || []; 

    const filteredNodes = nodes.filter(node => {
      if (!node) return false; // 过滤空节点
      const shouldKeep = node.type === 1 || node.type === 2;
      // 2. 检查权限 (修正：使用 hasPermission hook)
      const requiredPermission = node.permission;
      // 使用 hasPermission 函数检查，如果 permission 为空或 null，则视为无需权限 (hook 内部会处理管理员逻辑)
      const hasAccess = !requiredPermission || requiredPermission.trim() === '' || hasPermission(requiredPermission);

      // --- 添加日志 --- 
      console.log(`[transform] Filtering node: ID=${node.id}, Title=${node.title}, Type=${node.type}, RequiredPerm=${requiredPermission || 'N/A'}. HasAccess? ${hasAccess}`);
      // ---------------
      
      return shouldKeep && hasAccess; // 只有类型正确且有权限才保留
    });

    // --- 添加日志 --- 
    console.log('[transform] Filtered nodes:\n', JSON.stringify(filteredNodes.map(n => ({id:n.id, title:n.title})), null, 2));
    // ---------------

    return filteredNodes
      .sort((a, b) => a.sort - b.sort)
      .map(node => {
        // --- 添加日志 --- 
        console.log(`[transform] Mapping node: ID=${node.id}, Title=${node.title}`);
        // ---------------
        try { // 添加 try...catch 以捕获 map 内部错误
          return {
            index: node.path || String(node.id),
            title: node.title,
            icon: node.icon || undefined,
            children: node.children && node.children.length > 0 ? transformMenuTreeToSidebarItems(node.children) : undefined,
          };
        } catch (mapError) {
            console.error(`[transform] Error mapping node ${node.id} (${node.title}):`, mapError);
            return null; // 返回 null 或其他标记表示失败
        }
      })
      // 优化：过滤掉 map 失败的项 (如果 try...catch 返回 null)
      .filter(item => item !== null) as SidebarItem[]; // Add type assertion if filter might return null
  }

  // 获取导航菜单树
  async function FetchAccessibleMenus(): Promise<void> {
    try {
      console.log('[FetchAccessibleMenus] Fetching accessible menus from API...');
      const treeData = await getUserMenuTree() as unknown as MenuTreeItem[]; 
      console.log('[FetchAccessibleMenus] Raw menu tree data from API:\n', JSON.stringify(treeData, null, 2));
      rawMenuTree.value = treeData || []; // 只更新原始数据
      console.log('[FetchAccessibleMenus] Stored rawMenuTree state:\n', JSON.stringify(rawMenuTree.value, null, 2));
      console.log('[FetchAccessibleMenus] Fetch completed. accessibleMenus will be computed.');
    } catch (error) {
      console.error('[FetchAccessibleMenus] Fetch failed:', error);
      // accessibleMenus.value = []; // 不再需要手动清空 computed
      rawMenuTree.value = []; // 清空原始数据
    }
  }

  // 获取用户操作权限
  async function FetchPermissions(): Promise<void> {
    try {
      console.log('Fetching user permissions...');
      const perms = await getUserPermissions() as unknown as string[];
      permissions.value = perms || [];
      console.log('Fetched permissions:', permissions.value);
    } catch (error) {
      console.error('FetchPermissions failed:', error);
      permissions.value = []; // Clear on error
    }
  }

  // 获取可用账套 - FetchAccountBooks 逻辑调整 (移除 localStorage 检查)
  async function FetchAccountBooks(): Promise<void> {
    try {
      console.log('[FetchAccountBooks] Start. Current ID before fetch:', currentAccountBookId.value); // Log before fetch
      const response = await getUserAccountBooks() as unknown as { list: AccountBookItem[] }; // response is {list: [...]}
      // Safely extract the list, defaulting to an empty array if response or response.list is not as expected.
      accountBooks.value = (response && Array.isArray(response.list)) ? response.list : [];
      console.log('[FetchAccountBooks] Fetched and stored books (actual list):', JSON.stringify(accountBooks.value));
      console.log('[FetchAccountBooks] Current ID after fetch (might be restored by plugin):', currentAccountBookId.value); // Log after fetch

    } catch (error) {
      console.error('FetchAccountBooks failed:', error);
      accountBooks.value = [];
      // 移除 SetCurrentAccountBook(null); // 出错时清除
      // 在此仅清空列表，初始化的设置由 Login action 统一处理
    }
  }

  // 设置当前账套 - 移除手动 localStorage 操作
  function SetCurrentAccountBook(accountId: number | string | null): void {
    console.log(`[SetCurrentAccountBook] Called with ID: ${accountId}. Current value before set: ${currentAccountBookId.value}`); // Log entry
    const previousValue = currentAccountBookId.value; // Store previous value for comparison

    // --- 添加检查，避免重复设置相同的值触发不必要的更新 --- 
    if (previousValue === accountId) {
      console.log(`[SetCurrentAccountBook] ID ${accountId} is already set. Skipping update.`);
      return;
    }
    // -----------------------------------------------------

    // Ensure we are working with an array of account books
    const booksArray = Array.isArray(accountBooks.value) 
                       ? accountBooks.value 
                       : ((accountBooks.value as any)?.list && Array.isArray((accountBooks.value as any).list) 
                         ? (accountBooks.value as any).list 
                         : []);

    if (accountId === null) {
        currentAccountBookId.value = null;
        console.log('[SetCurrentAccountBook] ID set to null.');
    } else if (booksArray.some((book: AccountBookItem) => book.id === accountId)) {
      currentAccountBookId.value = accountId;
      console.log(`[SetCurrentAccountBook] ID set to: ${currentAccountBookId.value}.`);
    } else {
      console.warn(`[SetCurrentAccountBook] Attempted to set invalid ID: ${accountId}. Keeping previous value: ${currentAccountBookId.value}`);
      return; // Explicitly return to prevent further logging if invalid
    }

    // 仅在值确实改变时记录日志
    // Note: Since we added the check above, this condition might always be true if code reaches here
    // Keeping it for clarity, but the check above is the main guard against loops.
    if (previousValue !== currentAccountBookId.value) {
       console.log(`[SetCurrentAccountBook] Value changed from ${previousValue} to ${currentAccountBookId.value}. Persistence handled by plugin.`);
    } else {
       // This else block should ideally not be reached due to the check at the beginning
       console.log(`[SetCurrentAccountBook] Value remains ${currentAccountBookId.value}. (This log might indicate an issue if seen)`);
    }
  }

  // 用户登录 - 参数类型现在是导入的 LoginCredentials
  async function Login(credentials: LoginCredentials): Promise<void> {
    try {
      // loginApi 返回的已经是 data 部分 (LoginData 类型)
      const loginData = await loginApi(credentials) as unknown as LoginData;

      // 存储 Token
      setToken(loginData.token);
      token.value = loginData.token;
      userInfo.value = loginData.user;

      // Fetch user-specific data *after* successful login and token set
      // Use Promise.all for concurrent fetching
      console.log('Login successful, fetching user data...');
      await Promise.all([
          FetchAccessibleMenus(),
          FetchPermissions(),
          FetchAccountBooks() // Fetches books and sets default if needed
      ]);
      console.log('User data fetched successfully.');

      // --- 初始化当前账套 ---
      let initialAccountBookId: number | string | null = null;
      const defaultId = userInfo.value?.defaultAccountBookId;
      const accountBooksState = accountBooks.value; // accountBooksState is potentially { list: [...] } or an array

      console.log(`[Login] Initializing current account book. Default ID from user: ${defaultId}`);
      console.log('[Login] Available books (raw state):', JSON.stringify(accountBooksState));

      // Extract the actual list of books
      const actualBookList: AccountBookItem[] = Array.isArray(accountBooksState)
        ? accountBooksState
        : (accountBooksState && Array.isArray((accountBooksState as any).list)) 
          ? (accountBooksState as any).list 
          : [];

      console.log('[Login] Actual book list to be used:', JSON.stringify(actualBookList));

      // 1. 检查用户指定的默认账套是否有效
      if (defaultId !== null && defaultId !== undefined && actualBookList.some(book => book.id === defaultId)) {
        initialAccountBookId = defaultId;
        console.log(`[Login] Using user's default account book: ${initialAccountBookId}`);
      }
      // 2. 如果默认账套无效，且列表不为空，则使用列表第一个
      else if (actualBookList.length > 0) {
        const firstBook = actualBookList[0];
        if (firstBook) {
           initialAccountBookId = firstBook.id;
           console.log(`[Login] User's default is invalid or not set. Using first available book: ${initialAccountBookId}`);
        } else {
           // 理论上不应发生，但作为防御性编程
           initialAccountBookId = null;
           console.log('[Login] Books list is not empty, but first element is unexpectedly undefined.');
        }
      }
      // 3. 如果列表为空，则为 null
      else {
        initialAccountBookId = null;
        console.log('[Login] No available account books, setting current to null.');
      }

      // 设置初始的当前账套
      SetCurrentAccountBook(initialAccountBookId);

      return Promise.resolve(); // 表示登录成功

    } catch (error) {
      // 清除可能残留的 token
      Logout(); // 登录失败时确保清除状态
      console.error('Login Action Failed:', error);
      // 错误信息已经在 request.ts 的拦截器中通过 ElMessage 显示了
      // 这里只需要 reject Promise，让调用方 (Login.vue) 知道失败了
      return Promise.reject(error);
    }
  }

  // 获取用户信息 (示例，通常在登录后或应用加载时调用)
  async function GetUserInfo(): Promise<UserInfo> {
      if (!token.value) {
          return Promise.reject(new Error("Token not found, cannot fetch user info."));
      }
      try {
          // const response = await getUserProfile(); // 调用获取用户信息的 API
          // userInfo.value = response.user;
          // roles.value = response.roles;
          // permissions.value = response.permissions;

          // --- 模拟数据 ---
          // 假设登录接口已经返回了足够的信息，或者暂时不需要 roles/permissions
          if (!userInfo.value) {
              // 如果登录接口没返回user，这里就需要调接口
              console.warn("GetUserInfo: userInfo is null, returning empty object.");
              // throw new Error("Failed to get user info after login.");
              // 返回一个空对象或根据需要处理
              return Promise.resolve({} as UserInfo);
          }
          // --- 模拟数据结束 ---

          return Promise.resolve(userInfo.value);
      } catch (error) {
          console.error("GetUserInfo failed:", error);
          return Promise.reject(error);
      }
  }

  // 用户登出
  function Logout(): Promise<void> {
    return new Promise((resolve) => {
      removeToken();
      token.value = null;
      userInfo.value = null;
      roles.value = [];
      permissions.value = [];
      rawMenuTree.value = []; // 清空原始数据
      // accessibleMenus.value = []; // 不再需要清空 computed
      accountBooks.value = [];
      // Clear current account book and remove from storage
      SetCurrentAccountBook(null);
      console.log('User logged out, all user states cleared.');
      resolve();
    });
  }

  // 清除 Token (主要用于登出或 Token 失效)
  function ResetToken(): Promise<void> {
     return new Promise(resolve => {
         removeToken();
         token.value = null;
         resolve();
     });
  }

  return {
    // State
    token,
    userInfo,
    roles,
    permissions,
    rawMenuTree,      // 仍然需要 rawMenuTree 用于计算
    accountBooks,
    currentAccountBookId,
    // Getters
    isAuthenticated,
    username,
    nickname,
    avatar,
    currentAccountBook,
    accessibleMenus, // 将 computed 属性导出
    // Actions
    FetchAccessibleMenus,
    FetchPermissions,
    FetchAccountBooks,
    SetCurrentAccountBook,
    Logout,
    ResetToken,
    Login,
    GetUserInfo,
  };
}, {
  // --- 配置持久化 --- 
  persist: {
    key: 'pinia-user-state',
    storage: localStorage,
    paths: [
      'token',
      'userInfo',
      'roles',
      'permissions',
      'rawMenuTree',
      'accountBooks',
      'currentAccountBookId'
    ],
  } as PersistenceOptions // <-- 添加类型断言
});

// 在组件外部使用的 Hook (用于 request.ts 等)
// 如果 store 文件本身就在 @/store/modules/user.ts，可能不需要这个 hook，
// 但如果 store 在 @/store/index.ts 中注册，这个 hook 很有用
export function useUserStoreHook() {
  return useUserStore();
}
