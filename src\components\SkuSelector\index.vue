<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择物料SKU"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    destroy-on-close
  >
    <!-- 搜索表单 -->
    <div class="search-form-container">
      <el-form
        ref="formRef"
        :model="searchForm"
        class="search-form"
        label-width="80px"
      >
        <!-- 第一行：查询条件 -->
        <el-row :gutter="16">
          <el-col :span="4">
            <el-form-item label="物料SKU">
              <el-input
                v-model="searchForm.sku"
                placeholder="请输入物料SKU"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="物料名称">
              <el-input
                v-model="searchForm.name"
                placeholder="请输入物料名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="物料组">
              <el-select
                v-model="searchForm.groupCode"
                placeholder="Select"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option value="" label="全部" />
                <el-option
                  v-for="option in itemGroupOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="物料分类">
              <el-select
                v-model="searchForm.categoryCode"
                placeholder="Select"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option value="" label="全部" />
                <el-option
                  v-for="option in itemCategoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.status"
                placeholder="启用"
                clearable
                style="width: 100%"
              >
                <el-option value="" label="全部" />
                <el-option value="ACTIVE" label="启用" />
                <el-option value="INACTIVE" label="禁用" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 第二行：操作按钮 -->
        <el-row>
          <el-col :span="24">
            <div class="action-buttons" style="display: flex; justify-content: space-between; align-items: center;">
              <div>
                <el-checkbox v-model="isMultipleMode" @change="handleMultipleModeChange">
                  多选模式
                </el-checkbox>
              </div>
              <div style="display: flex; gap: 8px;">
                <el-button
                  type="primary"
                  :icon="Search"
                  @click="handleSearch"
                  :loading="loading"
                >
                  搜索
                </el-button>
                <el-button
                  :icon="RefreshRight"
                  @click="handleReset"
                >
                  重置
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <VNTable
        ref="tableRef"
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        :selection-type="isMultipleMode ? 'multiple' : 'single'"
        :selected-rows="selectedRows"
        :show-toolbar="false"
        :max-height="400"
        @selection-change="handleSelectionChange"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @row-dblclick="handleRowDblClick"
      >
        <!-- 物料组插槽 -->
        <template #column-groupCode="{ row }">
          <span>{{ getDictLabel('material_group', row.groupCode || '') || row.groupCode || '-' }}</span>
        </template>

        <!-- 物料分类插槽 -->
        <template #column-categoryCode="{ row }">
          <span>{{ getDictLabel('material_category', row.categoryCode || '') || row.categoryCode || '-' }}</span>
        </template>

        <!-- 默认库位插槽 -->
        <template #column-defaultLocationId="{ row }">
          <span v-if="row.defaultLocationId">{{ getLocationDisplayText(row.defaultLocationId) }}</span>
          <span v-else class="text-muted">-</span>
        </template>

        <!-- 状态插槽 -->
        <template #column-status="{ row }">
          <el-tag
            :type="row.status === 'ACTIVE' ? 'success' : 'danger'"
            size="small"
          >
            {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </VNTable>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="selected-info">
          <span v-if="isMultipleMode && selectedRows.length > 0">
            已选择 {{ selectedRows.length }} 个物料
          </span>
          <span v-else-if="!isMultipleMode && selectedRows.length > 0">
            已选择：{{ selectedRows[0]?.sku }} - {{ selectedRows[0]?.name }}
          </span>
        </div>
        <div class="action-buttons">
          <el-button
            type="primary"
            :disabled="selectedRows.length === 0"
            @click="handleConfirm"
          >
            确定选择
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, RefreshRight } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
import VNPagination from '@/components/VNPagination/index.vue'
import { useDictionary } from '@/hooks/useDictionary'
import { getLocationTree } from '@/api/wms/location'
import type { WmsLocationTreeVO } from '@/api/wms/location'
import { getMtlItemPage } from '@/api/wms/item'
import type { MtlItemVO, MtlItemQueryDTO } from '@/api/wms/item'
import { getSystemParameterByKey } from '@/api/system/systemParameter'

// Props 接口定义
export interface SkuSelectorProps {
  multiple?: boolean
  selectedSkus?: string[]
  filters?: Record<string, any>
}

// Props & Emits
const props = withDefaults(defineProps<SkuSelectorProps>(), {
  multiple: false,
  selectedSkus: () => [],
  filters: () => ({})
})

const emit = defineEmits<{
  confirm: [selectedItems: MtlItemVO[]]
  cancel: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const tableData = ref<MtlItemVO[]>([])
const loading = ref(false)
const selectedRows = ref<MtlItemVO[]>([])
const isMultipleMode = ref(false) // 控制多选模式
const showOverflowTooltip = ref(true) // 新增：用于控制列是否显示Tooltip，默认为true

const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const searchForm = ref({
  sku: '',
  name: '',
  status: 'ACTIVE',
  groupCode: '',
  categoryCode: ''
})

const tableRef = ref()
const formRef = ref()

// 字典选项
const { loadMultipleDictData, getDictLabel } = useDictionary()
const itemGroupOptions = ref<{ label: string; value: string }[]>([])
const itemCategoryOptions = ref<{ label: string; value: string }[]>([])

// 库位数据
const locationTree = ref<WmsLocationTreeVO[]>([])

// 辅助方法
const getLocationDisplayText = (locationId: number | string) => {
  if (!locationId) return '-'
  
  // 递归查找库位信息
  const findLocation = (nodes: WmsLocationTreeVO[], id: number): WmsLocationTreeVO | null => {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const result = findLocation(node.children, id)
        if (result) return result
      }
    }
    return null
  }
  
  const location = findLocation(locationTree.value, Number(locationId))
  if (location) {
    return `${location.code} - ${location.name}`
  }
  
  return `库位-${locationId}`
}

// 计算属性
const tableColumns = computed(() => [
  {
    prop: 'sku',
    label: '物料SKU',
    width: 140,
    sortable: true,
    showOverflowTooltip: showOverflowTooltip.value
  },
  {
    prop: 'name',
    label: '物料名称',
    minWidth: 150,
    showOverflowTooltip: showOverflowTooltip.value
  },
  {
    prop: 'specification',
    label: '规格型号',
    minWidth: 200,
    showOverflowTooltip: showOverflowTooltip.value
  },
  {
    prop: 'groupCode',
    label: '物料组',
    width: 120,
    slot: true,
    showOverflowTooltip: showOverflowTooltip.value
  },
  {
    prop: 'categoryCode',
    label: '物料分类',
    width: 120,
    slot: true,
    showOverflowTooltip: showOverflowTooltip.value
  },
  {
    prop: 'baseUnit',
    label: '基本单位',
    width: 100,
    align: 'center' as const,
    showOverflowTooltip: showOverflowTooltip.value
  },
  {
    prop: 'serialManaged',
    label: '序号管理',
    width: 100,
    align: 'center' as const,
    showOverflowTooltip: showOverflowTooltip.value,
    formatter: (row: any) => row.serialManaged ? '是' : '否'
  },
  {
    prop: 'batchManaged',
    label: '批次管理',
    width: 100,
    align: 'center' as const,
    showOverflowTooltip: showOverflowTooltip.value,
    formatter: (row: any) => row.batchManaged ? '是' : '否'
  },
  {
    prop: 'defaultLocationId',
    label: '默认库位',
    minWidth: 200,
    slot: true,
    showOverflowTooltip: showOverflowTooltip.value
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: true,
    showOverflowTooltip: showOverflowTooltip.value,
    align: 'center' as const
  }
])

// 监听器
watch(dialogVisible, async (newVal) => {
  if (newVal) {
    // 对话框打开时，先加载字典数据，再初始化表格数据
    await loadDictData()
    await initializeData()
  } else {
    emit('cancel')
  }
})

// 生命周期
onMounted(() => {
  loadDictData()
})

// 方法
const loadDictData = async () => {
  try {
    // 并行加载字典数据和库位数据
    const [dictData, locations, tooltipParam] = await Promise.all([
      loadMultipleDictData(['material_group', 'material_category']),
      getLocationTree(),
      getSystemParameterByKey('TableColumn.showOverflowTooltip')
    ])
    
    // 转换为选项格式
    itemGroupOptions.value = dictData['material_group']?.map(item => ({
      label: item.label,
      value: item.value
    })) || []
    
    itemCategoryOptions.value = dictData['material_category']?.map(item => ({
      label: item.label,
      value: item.value
    })) || []
    
    // 缓存库位数据
    locationTree.value = locations || []
    
    // 处理系统参数：如果参数值为'false'，则设置为false，否则（包括null, 'true', 或其他任何值）都默认为true
    showOverflowTooltip.value = tooltipParam !== 'false'

    console.log('字典和系统参数加载完成:', {
      material_group: itemGroupOptions.value,
      material_category: itemCategoryOptions.value,
      locations: locationTree.value.length,
      showOverflowTooltip: showOverflowTooltip.value
    })
  } catch (error) {
    console.error('加载初始化数据失败:', error)
    ElMessage.error('加载初始化数据失败')
    showOverflowTooltip.value = true // 出错时回退到默认值
  }
}

const initializeData = async () => {
  searchForm.value = {
    sku: '',
    name: '',
    status: 'ACTIVE',
    groupCode: '',
    categoryCode: '',
    ...props.filters
  }
  
  pagination.value = {
    currentPage: 1,
    pageSize: 20,
    total: 0
  }
  
  selectedRows.value = []
  await fetchData()
  
  if (props.selectedSkus.length > 0) {
    const preSelectedItems = tableData.value.filter(item => 
      props.selectedSkus.includes(item.sku)
    )
    selectedRows.value = preSelectedItems
  }
}

const fetchData = async () => {
  try {
    loading.value = true
    
    const params: MtlItemQueryDTO = {
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      sku: searchForm.value.sku || undefined,
      name: searchForm.value.name || undefined,
      status: searchForm.value.status || undefined,
      groupCode: searchForm.value.groupCode || undefined,
      categoryCode: searchForm.value.categoryCode || undefined
    }
    
    const response = await getMtlItemPage(params)
    
    tableData.value = response.list || []
    pagination.value.total = response.total || 0
    
  } catch (error) {
    console.error('获取物料数据失败:', error)
    ElMessage.error('获取物料数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = async () => {
  pagination.value.currentPage = 1
  await fetchData()
}

const handleReset = async () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  searchForm.value = {
    sku: '',
    name: '',
    status: 'ACTIVE',
    groupCode: '',
    categoryCode: ''
  }
  pagination.value.currentPage = 1
  await fetchData()
}

const handleSelectionChange = (selection: MtlItemVO[]) => {
  selectedRows.value = selection
}

const handlePageChange = async (page: number) => {
  pagination.value.currentPage = page
  await fetchData()
}

const handleSizeChange = async (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  await fetchData()
}

// 双击行选择
const handleRowDblClick = (row: MtlItemVO) => {
  selectedRows.value = [row]
  handleConfirm()
}

// 多选模式切换
const handleMultipleModeChange = () => {
  selectedRows.value = [] // 清空已选择的行
}

const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择至少一个物料')
    return
  }
  
  emit('confirm', selectedRows.value)
  dialogVisible.value = false
}

const handleCancel = () => {
  dialogVisible.value = false
}

// 暴露方法给父组件
const open = () => {
  dialogVisible.value = true
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
.search-form-container {
  margin-bottom: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  .search-form {
    .el-form-item {
      margin-bottom: 16px;
      
      .el-form-item__label {
        color: #606266;
        font-weight: 500;
        line-height: 32px;
        height: 32px;
      }
      
      .el-form-item__content {
        line-height: 32px;
        min-height: 32px;
        
        .el-input,
        .el-select {
          .el-input__wrapper,
          .el-select__wrapper {
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            transition: all 0.3s ease;
            
            &:hover {
              border-color: #c0c4cc;
            }
            
            &.is-focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }
        }
      }
    }
    
    // 第一行表单项
    .el-row:first-child .el-form-item {
      margin-bottom: 16px;
    }
    
    // 第二行（按钮行）
    .el-row:last-child {
      margin-top: 8px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.table-container {
  margin-bottom: 16px;
  
  :deep(.el-table) {
    width: 100%;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .selected-info {
    color: #606266;
    font-size: 14px;
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

.text-truncate {
  display: inline-block;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-muted {
  color: #909399;
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .search-form-container {
    padding: 12px;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    
    .action-buttons {
      justify-content: center;
    }
  }
}
</style> 