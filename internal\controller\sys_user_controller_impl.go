package controller

// 用户控制器实现 有哪些功能？
// [x] 1. 创建用户
// [x] 2. 更新用户
// [x] 3. 删除用户
// [x] 4. 获取用户详情 (GetUserByID)
// [x] 5. 获取用户完整分页列表(用于列表展示) (PageUsers -> FullPageUsers)
// [x] 6. 修改密码 (UpdatePassword)
// [x] 7. 修改用户状态 (UpdateStatus, 管理性启用/禁用)
// [x] 8. 获取用户个人资料 (GetProfile)
// [x] 9. 更新用户个人资料 (UpdateProfile)
// [x] 10. 获取用户角色 (GetUserRoles)
// [x] 11. 更新用户角色 (UpdateUserRoles)
// [x] 12. 获取用户关联帐套 (GetUserAccountBooks - 占位符)
// [x] 13. 更新用户关联帐套 (UpdateUserAccountBooks)
// [x] 14. 更新默认帐套 (UpdateDefaultAccountBook)
// [x] 15. 管理员重置密码 (AdminResetPassword)
// [x] 16. 更新用户头像 (UploadAvatar -> UpdateAvatarURL)
// [x] 17. 获取用户精简分页列表(用于选择框) (SimplePageUsers)
// [x] 18. 安全锁定用户 (LockUserSecurity)
// [x] 19. 安全解锁用户 (UnlockUserSecurity)
// [ ] 20. 获取在线用户列表(用于列表展示) (OnlinePageUsers - 占位符, 待实现)

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/model/vo" // Assuming UserVO, UserProfileVO, PageVO, RoleVO exist
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // Alias pkg/errors
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/validator"
)

// UserController 用户控制器
type UserControllerImpl struct {
	*BaseControllerImpl
	userService service.UserService
	// Add other services if needed, e.g., for complex permission checks
}

// NewUserController 创建用户控制器实例
func NewUserControllerImpl(cm *ControllerManager) *UserControllerImpl {
	base := NewBaseController(cm)
	userService := cm.GetServiceManager().GetUserService() // Corrected: Call as a method on ServiceManager
	if userService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 UserService 实例") // Consistent with hr_employee_controller_impl.go
	}
	return &UserControllerImpl{
		BaseControllerImpl: base,
		userService:        userService,
	}
}

// CreateUser 创建用户
// @Summary 创建新用户
// @Description 创建一个新的用户信息，包括密码和角色关联
// @Tags Users
// @Accept json
// @Produce json
// @Param user body dto.UserCreateOrUpdateDTO true
// @Success 200 {object} response.Response{data=vo.UserVO} "成功响应，返回用户信息 (不含密码)"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users [post]
func (c *UserControllerImpl) CreateUser(ctx iris.Context) {
	opName := "创建用户"
	var req dto.UserCreateDTO
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 使用验证器验证 DTO ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		// --- 使用新的辅助函数创建 CustomError 并调用 FailWithError ---
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		// -----------------------------------------------------------
		return // Stop execution if validation fails
	}
	// --- 验证通过，继续执行 ---

	// Add validation if needed beyond DTO tags

	userVO, err := c.userService.CreateUser(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, userVO)
}

// UpdateUser 更新用户信息
// @Summary 更新用户信息
// @Description 根据用户ID更新用户信息 (不包括密码)
// @Tags Users
// @Accept json
// @Produce json
// @Param id path uint true "用户ID"
// @Param body body dto.UserCreateOrUpdateDTO true "用户信息更新请求 (不含密码, 包含可选的 accountBookIDs: [1, 3] 用于覆盖关联)"
// @Success 200 {object} response.Response{data=vo.UserVO} "成功响应，返回更新后的用户信息"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id} [put]
func (c *UserControllerImpl) UpdateUser(ctx iris.Context) {
	opName := "更新用户信息"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	// 恢复使用 GetUint64 获取 ID
	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		// 保留之前的错误处理，因为 GetUint64 可能会因为非数字等原因失败
		c.GetLogger().Error(ctx.Request().Context(), opName+" - Error calling ctx.Params().GetUint64(\"id\")", logger.WithError(err))
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	var req dto.UserUpdateDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 使用验证器验证 DTO ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("userId", userID), logger.WithField("errors", validationErrs))
		// --- 使用新的辅助函数创建 CustomError 并调用 FailWithError ---
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		// -----------------------------------------------------------
		return // Stop execution if validation fails
	}
	// --- 验证通过，继续执行 ---

	userVO, err := c.userService.UpdateUser(ctx, userID, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, userVO)
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 根据用户ID删除用户
// @Tags Users
// @Produce json
// @Param id path uint true "用户ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id} [delete]
func (c *UserControllerImpl) DeleteUser(ctx iris.Context) {
	opName := "删除用户"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	// --- 添加管理员权限检查 ---
	if err := c.checkAdminPermission(ctx, opName); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// --- 结束权限检查 ---

	err = c.userService.DeleteUser(ctx.Request().Context(), userID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetUserByID 获取用户详情
// @Summary 获取用户详情
// @Description 根据用户ID获取用户详细信息 (不含密码)
// @Tags Users
// @Produce json
// @Param id path uint true "用户ID"
// @Success 200 {object} response.Response{data=vo.UserVO} "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id} [get]
func (c *UserControllerImpl) GetUserByID(ctx iris.Context) {
	opName := "获取用户详情"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	// 使用 BaseControllerImpl 的辅助方法获取路径参数
	userIDUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		// GetPathUintParamWithError 内部应该已经处理了日志和构建 apperrors.ParamError
		c.HandleError(ctx, err, opName) // 直接传递错误
		return
	}
	userID := uint(userIDUint64)

	userVO, err := c.userService.GetUserByID(ctx.Request().Context(), userID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, userVO)
}

// PageUsers 获取用户列表 (分页)
// @Summary 获取用户列表 (分页)
// @Description 获取用户列表，支持分页和按用户名/昵称/真实姓名/手机/邮箱/性别/状态/管理员过滤
// @Tags Users
// @Produce json
// @Param pageNum query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param sort query string false "排序规则 (例如: 'username:asc,created_at:desc')"
// @Param username query string false "用户名过滤 (模糊匹配)"
// @Param nickname query string false "昵称过滤 (模糊匹配)"
// @Param realName query string false "真实姓名过滤 (模糊匹配)"
// @Param mobile query string false "手机号过滤 (请核对Service层是精确还是模糊匹配)"
// @Param email query string false "邮箱过滤 (请核对Service层是精确还是模糊匹配)"
// @Param gender query int false "性别过滤 (0:未知, 1:男, 2:女)" Enums(0, 1, 2)
// @Param status query int false "状态过滤 (0:禁用, 1:启用)" Enums(0, 1)
// @Param isAdmin query boolean false "是否管理员过滤 (true/false)"
// @Success 200 {object} response.Response{data=response.PageResult} "成功响应 (包含用户视图对象列表和分页信息)"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/page [get]
func (c *UserControllerImpl) PageUsers(ctx iris.Context) {
	opName := "分页查询用户"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	requestCtx := ctx.Request().Context() // Get standard context

	// 1. 准备查询 DTO
	var queryDTO dto.UserPageQueryDTO

	// 2. 自动绑定 URL 查询参数到 DTO 的过滤字段 (基于 query 标签)
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		// 如果绑定失败 (例如类型不匹配)，返回参数错误
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "解析查询参数失败").WithCause(err), opName)
		return
	}

	// 3. 处理分页和排序参数 (仍然使用 BuildPageQuery)
	// 注意: BuildPageQuery 不应再处理已被 ReadQuery 绑定的过滤参数
	pageQuery := response.BuildPageQuery(ctx)
	queryDTO.PageQuery = *pageQuery // 将分页排序信息赋给 DTO

	// 4. 调用服务层方法
	// 服务层现在接收包含所有过滤、分页、排序信息的 DTO
	pageResult, err := c.userService.FullPageUsers(requestCtx, queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	// 5. 返回成功的响应
	c.Success(ctx, pageResult)
}

// UpdatePassword 修改用户密码
// @Summary 修改密码
// @Description 修改指定用户的密码 (需要管理员权限或用户修改自己的密码)
// @Tags Users
// @Accept json
// @Produce json
// @Param id path uint true "用户ID"
// @Param body body dto.UserUpdatePasswordDTO true "旧密码和新密码"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 401 {object} response.Response "旧密码错误 (如果是用户自己修改)"
// @Failure 403 {object} response.Response "无权修改他人密码"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id}/password [put]
func (c *UserControllerImpl) UpdatePassword(ctx iris.Context) {
	opName := "修改密码"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	var req dto.UserUpdatePasswordDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("userId", userID), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	currentUserID, authErr := c.GetUserIDFromContext(ctx)
	if authErr != nil {
		c.HandleError(ctx, authErr, opName)
		return
	}

	isSelf := (uint64(userID) == currentUserID)

	isAdmin, adminCheckErr := c.IsAdminFromContext(ctx) // 使用 BaseControllerImpl 的方法
	if adminCheckErr != nil {
		c.GetLogger().Error(ctx.Request().Context(), opName+" - 检查管理员权限失败", logger.WithError(adminCheckErr), logger.WithField("requesterId", currentUserID))
		c.HandleError(ctx, apperrors.NewAuthError(apperrors.CODE_AUTH_FORBIDDEN, "无法验证操作权限").WithCause(adminCheckErr), opName)
		return
	}

	if !isSelf && !isAdmin {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 权限不足", logger.WithField("requesterId", currentUserID), logger.WithField("targetUserId", userID))
		c.HandleError(ctx, apperrors.NewAuthError(apperrors.CODE_AUTH_FORBIDDEN, "无权修改他人密码"), opName)
		return
	}

	err = c.userService.UpdatePassword(ctx.Request().Context(), userID, req.OldPassword, req.NewPassword, uint(currentUserID))
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "密码修改成功", nil)
}

// checkAdminPermission 是一个辅助函数，用于检查当前用户是否具有管理员权限
// 如果没有管理员权限或检查过程中出错，则返回错误
func (c *UserControllerImpl) checkAdminPermission(ctx iris.Context, opName string) error {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	// isAdmin, adminCheckErr := util.IsAdminFromContext(ctx)
	isAdmin, adminCheckErr := c.IsAdminFromContext(ctx) // 使用 BaseControllerImpl 的方法
	if adminCheckErr != nil {
		c.GetLogger().Error(ctx.Request().Context(), opName+" - 检查管理员权限失败", logger.WithError(adminCheckErr))
		return apperrors.NewAuthError(apperrors.CODE_AUTH_FORBIDDEN, "无法验证操作权限").WithCause(adminCheckErr)
	}
	if !isAdmin {
		currentUserID, idErr := c.GetUserIDFromContext(ctx)
		if idErr != nil {
			// 如果连UserID都获取不到，也视为权限问题或上下文错误
			c.GetLogger().Error(ctx.Request().Context(), opName+" - 检查管理员权限时无法获取当前用户ID", logger.WithError(idErr))
			return apperrors.NewAuthError(apperrors.CODE_AUTH_FORBIDDEN, "无法获取操作者身份").WithCause(idErr)
		}
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 权限不足 (需要管理员)", logger.WithField("requesterId", currentUserID))
		return apperrors.NewAuthError(apperrors.CODE_AUTH_FORBIDDEN, "无权限执行此操作 (需要管理员)")
	}
	return nil
}

// UpdateStatus 修改用户状态
// @Summary 修改用户状态
// @Description 启用或禁用指定用户 (需要管理员权限)
// @Tags Users
// @Accept json
// @Produce json
// @Param id path uint true "用户ID"
// @Param body body dto.UserUpdateStatusDTO true "新的状态 (0:禁用, 1:启用)"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 403 {object} response.Response "无权限操作"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id}/status [put]
func (c *UserControllerImpl) UpdateStatus(ctx iris.Context) {
	opName := "修改用户状态"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	var req dto.UserUpdateStatusDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 使用验证器验证 DTO ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("userId", userID), logger.WithField("errors", validationErrs))
		// --- 使用新的辅助函数创建 CustomError 并调用 FailWithError ---
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		// -----------------------------------------------------------
		return // Stop execution if validation fails
	}
	// --- 验证通过，继续执行 ---

	// --- 使用辅助函数进行权限检查 ---
	if err := c.checkAdminPermission(ctx, opName); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// --- 结束权限检查 ---

	err = c.userService.UpdateStatus(ctx.Request().Context(), userID, req.Status)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "状态更新成功", nil)
}

// GetProfile 获取用户个人资料 (当前登录用户)
// @Summary 获取当前用户个人资料
// @Description 获取当前登录用户的详细个人信息
// @Tags Profile
// @Produce json
// @Success 200 {object} response.Response{data=vo.UserProfileVO} "成功响应"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /profile [get]
func (c *UserControllerImpl) GetProfile(ctx iris.Context) {
	opName := "获取个人资料"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userID, err := c.GetUserIDFromContext(ctx)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	profileVO, err := c.userService.GetUserProfile(ctx.Request().Context(), uint(userID))
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, profileVO)
}

// UpdateProfile 更新用户个人资料 (当前登录用户)
// @Summary 更新当前用户个人资料
// @Description 更新当前登录用户的个人信息 (昵称、邮箱、手机号等)
// @Tags Profile
// @Accept json
// @Produce json
// @Param body body dto.UserProfileUpdateDTO true "用户个人信息更新请求"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /profile [put]
func (c *UserControllerImpl) UpdateProfile(ctx iris.Context) {
	opName := "更新个人资料"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	// 从 util 获取 UserID (假设 util.GetUserIDFromContext 存在且返回 uint64, error)
	userIDUint64, err := c.GetUserIDFromContext(ctx)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	userID := uint(userIDUint64)

	var req dto.UserProfileUpdateDTO // DTO 用于读取请求
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("userId", userID), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	err = c.userService.UpdateProfile(ctx.Request().Context(), userID, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "个人资料更新成功", nil)
}

// GetUserRoles 获取指定用户的角色列表
// @Summary 获取用户角色
// @Description 获取指定用户ID关联的所有角色信息
// @Tags Roles
// @Produce json
// @Param id path uint true "用户ID"
// @Success 200 {object} response.Response{data=[]vo.RoleVO} "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id}/roles [get]
func (c *UserControllerImpl) GetUserRoles(ctx iris.Context) {
	opName := "获取用户角色"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	roles, err := c.userService.GetUserRoles(ctx.Request().Context(), userID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	if roles == nil {
		roles = []*vo.RoleVO{}
	}
	c.Success(ctx, roles)
}

// UpdateUserRoles 更新指定用户的角色关联
// @Summary 更新用户角色
// @Description 更新指定用户ID的角色关联 (传入新的角色ID列表, 空数组或空请求体表示清空关联)
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path uint true "用户ID"
// @Param body body dto.UserRoleUpdateDTO true "新的角色ID列表请求 (包含 roleIds 字段)"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id}/roles [put]
func (c *UserControllerImpl) UpdateUserRoles(ctx iris.Context) {
	opName := "更新用户角色"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName) // <<< 使用 apperrors 别名
		return
	}
	userID := uint(userIDUint64)

	var req dto.UserRoleUpdateDTO // <<< 修正 DTO 类型声明
	var roleIDs []uint

	if ctx.Request().ContentLength == 0 {
		roleIDs = []uint{}
	} else {
		if err := ctx.ReadJSON(&req); err != nil {
			c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析角色ID列表失败, 请确保格式为 {\"roleIds\": [...]} ").WithCause(err), opName) // <<< 使用 apperrors 别名
			return
		}
		if req.RoleIDs == nil {
			roleIDs = []uint{}
		} else {
			roleIDs = req.RoleIDs
		}
	}

	// --- 使用验证器验证 DTO (即使请求体为空，req 也可能非 nil，但其 RoleIDs 为 nil，会被 omitempty 跳过) ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("userId", userID), logger.WithField("errors", validationErrs))
		// --- 使用新的辅助函数创建 CustomError 并调用 FailWithError ---
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		// -----------------------------------------------------------
		return // Stop execution if validation fails
	}
	// --- 验证通过，继续执行 ---

	err = c.userService.UpdateUserRoles(ctx.Request().Context(), userID, roleIDs)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "用户角色更新成功", nil)
}

// AdminResetPassword 管理员重置用户密码
// @Summary 管理员重置密码
// @Description 管理员为指定用户设置新密码 (不需要旧密码)
// @Tags Users
// @Accept json
// @Produce json
// @Param id path uint true "用户ID"
// @Param body body dto.UserAdminResetPasswordDTO true "新密码请求"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 403 {object} response.Response "无权限操作 (例如尝试重置 admin)"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id}/admin-reset-password [put]
func (c *UserControllerImpl) AdminResetPassword(ctx iris.Context) {
	opName := "管理员重置密码"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	var req dto.UserAdminResetPasswordDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 使用验证器验证 DTO ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("userId", userID), logger.WithField("errors", validationErrs))
		// --- 使用新的辅助函数创建 CustomError 并调用 FailWithError ---
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		// -----------------------------------------------------------
		return // Stop execution if validation fails
	}
	// --- 验证通过，继续执行 ---

	// --- 使用辅助函数进行权限检查 ---
	if err := c.checkAdminPermission(ctx, opName); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// --- 结束权限检查 ---

	err = c.userService.AdminResetPassword(ctx.Request().Context(), userID, req.NewPassword)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "密码重置成功", nil)
}

// UploadAvatar 处理用户头像上传 (新增)
// @Summary 上传用户头像
// @Description 为当前登录用户上传头像文件
// @Tags Profile
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "头像文件"
// @Success 200 {object} response.Response{data=object} "成功响应，data可能为UserProfileVO或包含avatarUrl的map"
// @Failure 400 {object} response.Response "请求参数错误 (如缺少文件、文件过大或类型不允许)"
// @Failure 401 {object} response.Response "用户未认证"
// @Failure 500 {object} response.Response "服务器内部错误 (文件保存或数据库更新失败)"
// @Router /profile/avatar [post]
func (c *UserControllerImpl) UploadAvatar(ctx iris.Context) {
	opName := "上传头像"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	// 0. 获取当前用户ID
	uploaderID, authErr := c.GetUserIDFromContext(ctx)
	if authErr != nil {
		c.HandleError(ctx, authErr, opName)
		return
	}
	userID := uint(uploaderID) // 头像业务中，上传者ID和业务ID都是用户ID
	businessID := userID
	businessType := "user_avatar" // 定义业务类型

	// 1. 从 ServiceManager 获取 FileService
	fileService := c.GetServiceManager().GetFileService()
	if fileService == nil {
		c.HandleError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "文件服务不可用"), opName)
		return
	}

	// 2. 解析表单数据 (限制大小，这里可以参考配置)
	// 假设头像最大 5MB
	maxSize := int64(5 << 20)
	// fmt.Printf("--- [DEBUG] %s: 准备解析表单, maxSize=%d ---\n", opName, maxSize) // 移除调试打印
	c.GetLogger().Debug(ctx.Request().Context(), opName+": 准备解析表单", logger.WithField("maxSize", maxSize)) // 替换为 Debug 日志

	err := ctx.Request().ParseMultipartForm(maxSize)
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_FORMAT_ERROR, "无法解析表单数据或文件过大").WithCause(err), opName)
		return
	}

	// 3. 获取文件
	file, header, err := ctx.FormFile("file") // 表单字段名 "file"
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "缺少文件字段 'file'").WithCause(err), opName)
		return
	}
	defer file.Close()

	// 4. 调用 FileService 上传文件
	metadata, serviceErr := fileService.UploadFile(ctx.Request().Context(), userID, file, header, businessType, &businessID)
	if serviceErr != nil {
		c.HandleError(ctx, serviceErr, opName)
		return
	}

	// 5. 调用 FileService 获取文件访问 URL (修改)
	avatarURL, urlErr := fileService.GetFileURL(ctx.Request().Context(), metadata)
	if urlErr != nil {
		// 如果 GetFileURL 出错，记录日志但可能仍继续，因为文件已上传
		c.GetLogger().WithError(urlErr).Warn(ctx, opName+" - 获取文件URL失败", logger.WithField("metadataID", metadata.ID))
		// 即使获取 URL 失败，也尝试更新数据库（如果 URL 生成逻辑出错，但文件本身存在）
		// 降级使用下载 URL 或 object key?
		// 决定：如果获取 URL 失败，可能是配置问题，暂不更新数据库，返回错误
		c.HandleError(ctx, apperrors.WrapError(urlErr, apperrors.CODE_SYSTEM_INTERNAL, "生成文件URL失败"), opName)
		return
	}

	// 6. 调用 UserService 更新用户的 Avatar 字段 (使用获取到的 avatarURL)
	err = c.userService.UpdateAvatarURL(ctx.Request().Context(), userID, avatarURL)
	if err != nil {
		c.HandleError(ctx, err, opName+" - 更新头像URL失败")
		// 注意：此时文件已上传，但数据库更新失败，可以考虑是否删除已上传文件或记录日志
		return
	}

	// 7. 获取更新后的用户信息并返回
	updatedProfile, err := c.userService.GetUserProfile(ctx.Request().Context(), userID)
	if err != nil {
		c.GetLogger().WithError(err).Warn(ctx, opName+" - 获取更新后的用户信息失败", logger.WithField("userID", userID))
		// 即使获取失败，头像更新本身是成功的，返回成功信息和新的URL
		c.Success(ctx, map[string]string{"avatarUrl": avatarURL})
		return
	}

	c.Success(ctx, updatedProfile)
}

// LockUserSecurity 安全锁定用户 (新增)
// @Summary 安全锁定用户
// @Description 因安全原因锁定指定的用户账号 (需要管理员权限)，将设置用户的 LockedTime
// @Tags Users
// @Produce json
// @Param id path uint true "用户ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 403 {object} response.Response "无权限操作"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id}/security-lock [put]
func (c *UserControllerImpl) LockUserSecurity(ctx iris.Context) {
	opName := "安全锁定用户"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	// --- 使用辅助函数进行权限检查 ---
	if err := c.checkAdminPermission(ctx, opName); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// --- 结束权限检查 ---

	// 调用服务层方法处理安全锁定逻辑
	err = c.userService.LockUserSecurity(ctx.Request().Context(), userID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "用户安全锁定成功", nil)
}

// UnlockUserSecurity 安全解锁用户 (新增)
// @Summary 安全解锁用户
// @Description 解除指定用户的安全锁定状态 (需要管理员权限)，将清除用户的 LockedTime
// @Tags Users
// @Produce json
// @Param id path uint true "用户ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 403 {object} response.Response "无权限操作"
// @Failure 404 {object} response.Response "用户未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /users/{id}/security-unlock [put]
func (c *UserControllerImpl) UnlockUserSecurity(ctx iris.Context) {
	opName := "安全解锁用户"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "user")

	userIDUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的用户ID参数").WithCause(err), opName)
		return
	}
	userID := uint(userIDUint64)

	// --- 使用辅助函数进行权限检查 ---
	if err := c.checkAdminPermission(ctx, opName); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// --- 结束权限检查 ---

	// 调用服务层方法处理安全解锁逻辑
	err = c.userService.UnlockUserSecurity(ctx.Request().Context(), userID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "用户安全解锁成功", nil)
}
