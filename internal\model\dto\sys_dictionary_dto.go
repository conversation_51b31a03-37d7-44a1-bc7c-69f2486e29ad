package dto

import (
	"backend/pkg/response"
)

// --- Dictionary Type DTOs ---

// DictTypeCreateDTO 字典类型创建请求 DTO
type DictTypeCreateOrUpdateDTO struct {
	TenantDTO
	Code     string `json:"code" validate:"required,alphanum,max=100"` // 类型编码 (必填, 字母数字, 最大100)
	Name     string `json:"name" validate:"required,max=100"`          // 类型名称 (必填, 最大100)
	Status   *int   `json:"status" validate:"omitempty,oneof=0 1"`     // 状态 (可选, 0或1, 默认应由Service层处理为1)
	IsSystem *bool  `json:"isSystem" validate:"omitempty"`             // 新增：是否系统内置 (可选, 默认应由Service层处理为false)
	Remark   string `json:"remark" validate:"max=500"`                 // 备注 (可选, 最大500)
}

// DictTypePageQueryDTO 字典类型查询 DTO (用于分页)
type DictTypePageQueryDTO struct {
	PageQuery response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
	Code      string             `json:"code" form:"code"`                                    // 按编码查询 (可选, 模糊匹配)
	Name      string             `json:"name" form:"name"`                                    // 按名称查询 (可选, 模糊匹配)
	Status    *int               `json:"status" form:"status" validate:"omitempty,oneof=0 1"` // 按状态查询 (可选, 0或1)
}

// DictTypeDeleteDTO 字典类型删除请求 DTO
type DictTypeDeleteDTO struct {
	Ids []uint `json:"ids" validate:"required,min=1,dive,gt=0"` // 字典类型ID列表 (必填, 至少1个, 每个ID需大于0)
}

// --- Dictionary Item DTOs ---

// DictItemCreateDTO 字典项创建请求 DTO
type DictItemCreateOrUpdateDTO struct {
	BaseDTO
	DictionaryTypeID uint   `json:"dictionaryTypeId" validate:"required,gt=0"` // 关联的字典类型ID (必填, 需大于0)
	Label            string `json:"label" validate:"required,max=100"`         // 标签 (必填, 最大100)
	Value            string `json:"value" validate:"required,max=100"`         // 值 (必填, 最大100)
	SortOrder        *int   `json:"sortOrder" validate:"omitempty,gte=0"`      // 排序值 (可选, 大于等于0, 默认应由Service层处理为0)
	Status           *int   `json:"status" validate:"omitempty,oneof=0 1"`     // 状态 (可选, 0或1, 默认应由Service层处理为1)
	IsSystem         *bool  `json:"isSystem"`                                  // 是否系统内置 (可选, 默认应为false)
	Remark           string `json:"remark" validate:"max=500"`                 // 备注 (可选, 最大500)
}

// DictItemPageQueryDTO 字典项查询 DTO (用于分页)
type DictItemPageQueryDTO struct {
	PageQuery        response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
	DictionaryTypeID *uint              `json:"dictionaryTypeId" form:"dictionaryTypeId" validate:"omitempty,gt=0"` // 按类型ID查询 (可选)
	Label            string             `json:"label" form:"label"`                                                 // 按标签查询 (可选, 模糊匹配)
	Value            string             `json:"value" form:"value"`                                                 // 按值查询 (可选, 精确或模糊匹配)
	Status           *int               `json:"status" form:"status" validate:"omitempty,oneof=0 1"`                // 按状态查询 (可选, 0或1)
}

// DictItemDeleteDTO 字典项删除请求 DTO
type DictItemDeleteDTO struct {
	Ids []uint `json:"ids" validate:"required,min=1,dive,gt=0"` // 字典项ID列表 (必填, 至少1个, 每个ID需大于0)
}

// DictItemsByTypeCodeQueryDTO 按类型编码查询字典项列表 DTO (用于公共接口)
type DictItemsByTypeCodeQueryDTO struct {
	TypeCode string `json:"typeCode" form:"typeCode" validate:"required"` // 字典类型编码 (必填)
}
