package entity

// SystemParameter 系统参数实体
type SystemParameter struct {
	TenantEntity        // 继承租户实体基类，包含ID、创建时间、更新时间等基础字段
	ParamKey     string `gorm:"column:param_key;type:varchar(100);not null;comment:参数键" json:"paramKey"`     // 参数键 (程序代码中使用)
	ParamValue   string `gorm:"column:param_value;type:text;not null;comment:参数值" json:"paramValue"`         // 参数值 (实际存储的值)
	Name         string `gorm:"column:name;type:varchar(100);not null;comment:参数名称" json:"name"`             // 参数名称 (UI 显示)
	Remark       string `gorm:"column:remark;type:varchar(500);comment:备注" json:"remark"`                    // 参数描述
	Status       int    `gorm:"column:status;type:smallint;default:1;comment:状态 (1:启用, 0:禁用)" json:"status"` // 状态 (1:启用, 0:禁用) - 使用 smallint
	IsSystem     bool   `gorm:"column:is_system;default:false;comment:是否系统内置" json:"isSystem"`               // 是否系统内置 (系统内置通常不允许删除/修改Key)
	ValueType    string `gorm:"column:value_type;type:varchar(50);comment:值类型" json:"valueType"`             // 值类型 (如: string, integer, boolean, json) - 可选但推荐
}

// TableName 指定表名
func (SystemParameter) TableName() string {
	return "sys_parameter" // 表名
}
