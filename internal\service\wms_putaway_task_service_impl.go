package service

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsPutawayTaskService 定义上架任务服务接口
type WmsPutawayTaskService interface {
	BaseService
	Create(ctx context.Context, req *dto.WmsPutawayTaskCreateReq) (*vo.WmsPutawayTaskVO, error)
	CreateFromReceiving(ctx context.Context, receivingID uint) (*vo.WmsPutawayTaskVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsPutawayTaskUpdateReq) (*vo.WmsPutawayTaskVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsPutawayTaskVO, error)
	GetPage(ctx context.Context, req *dto.WmsPutawayTaskQueryReq) (*vo.PageResult[vo.WmsPutawayTaskVO], error)
	AssignToUser(ctx context.Context, id uint, userID uint) error
	BatchAssignToUser(ctx context.Context, taskIDs []uint, userID uint) error
	CompleteTask(ctx context.Context, id uint) error
	GetPendingTasks(ctx context.Context, warehouseID uint64) ([]*vo.WmsPutawayTaskVO, error)
	GetTasksByUser(ctx context.Context, userID uint64) ([]*vo.WmsPutawayTaskVO, error)
}

// wmsPutawayTaskServiceImpl 上架任务服务实现
type wmsPutawayTaskServiceImpl struct {
	BaseServiceImpl
}

// NewWmsPutawayTaskService 创建上架任务服务
func NewWmsPutawayTaskService(sm *ServiceManager) WmsPutawayTaskService {
	return &wmsPutawayTaskServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建上架任务
func (s *wmsPutawayTaskServiceImpl) Create(ctx context.Context, req *dto.WmsPutawayTaskCreateReq) (*vo.WmsPutawayTaskVO, error) {
	var task *entity.WmsPutawayTask
	var voResult *vo.WmsPutawayTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskRepository()

		// 生成任务单号
		taskNo, err := s.generateTaskNo(ctx)
		if err != nil {
			return err
		}

		task = &entity.WmsPutawayTask{}
		copier.Copy(task, req)
		task.TaskNo = taskNo

		// 强制从上下文获取账套ID
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		task.AccountBookID = uint(accountBookID)

		// 验证收货记录是否存在
		if req.ReceivingRecordID > 0 {
			receivingRepo := txRepoMgr.GetWmsReceivingRecordRepository()
			_, err := receivingRepo.FindByID(ctx, req.ReceivingRecordID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货记录不存在")
				}
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证收货记录失败").WithCause(err)
			}
		}

		// 创建上架任务
		if err := repo.Create(ctx, task); err != nil {
			if apperrors.IsDuplicateKeyError(err) {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "任务单号已存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建上架任务失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, task)
	return voResult, nil
}

// CreateFromReceiving 从收货记录创建上架任务
func (s *wmsPutawayTaskServiceImpl) CreateFromReceiving(ctx context.Context, receivingID uint) (*vo.WmsPutawayTaskVO, error) {
	var task *entity.WmsPutawayTask
	var voResult *vo.WmsPutawayTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		receivingRepo := txRepoMgr.GetWmsReceivingRecordRepository()
		putawayRepo := txRepoMgr.GetWmsPutawayTaskRepository()

		// 查询收货记录
		receiving, err := receivingRepo.FindByID(ctx, receivingID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货记录不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询收货记录失败").WithCause(err)
		}

		// 检查收货记录状态
		if receiving.Status != "COMPLETED" && receiving.Status != "PARTIALLY_COMPLETED" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有完成或部分完成的收货记录才能创建上架任务")
		}

		// 检查是否已经创建过上架任务
		existingTasks, err := putawayRepo.FindByReceivingRecordID(ctx, receivingID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "检查现有上架任务失败").WithCause(err)
		}
		if len(existingTasks) > 0 {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "该收货记录已经创建过上架任务")
		}

		// 生成任务单号
		taskNo, err := s.generateTaskNo(ctx)
		if err != nil {
			return err
		}

		// 创建上架任务
		task = &entity.WmsPutawayTask{
			TaskNo:            taskNo,
			ReceivingRecordID: receivingID,
			Priority:          1, // 默认优先级
			Status:            "PENDING",
		}
		task.AccountBookID = receiving.AccountBookID

		if err := putawayRepo.Create(ctx, task); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建上架任务失败").WithCause(err)
		}

		// 自动从收货明细创建上架任务明细
		detailService := s.GetServiceManager().GetWmsPutawayTaskDetailService()
		if err := detailService.CreateFromReceivingDetails(ctx, task.ID, receivingID); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建上架任务明细失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, task)
	return voResult, nil
}

// Update 更新上架任务
func (s *wmsPutawayTaskServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsPutawayTaskUpdateReq) (*vo.WmsPutawayTaskVO, error) {
	var task *entity.WmsPutawayTask
	var voResult *vo.WmsPutawayTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskRepository()
		var err error

		task, err = repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务失败").WithCause(err)
		}

		// 检查状态是否允许修改
		if task.Status == "COMPLETED" || task.Status == "CANCELLED" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已完成或已取消的任务不能修改")
		}

		// 更新字段
		copier.Copy(task, req)

		if err := repo.Update(ctx, task); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新上架任务失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, task)
	return voResult, nil
}

// Delete 删除上架任务
func (s *wmsPutawayTaskServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskRepository()

		task, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if task.Status != "PENDING" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待处理状态的任务才能删除")
		}

		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除上架任务失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取上架任务
func (s *wmsPutawayTaskServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsPutawayTaskVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskRepository()

	task, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务失败").WithCause(err)
	}

	var voResult *vo.WmsPutawayTaskVO
	copier.Copy(&voResult, task)
	return voResult, nil
}

// GetPage 分页查询上架任务
func (s *wmsPutawayTaskServiceImpl) GetPage(ctx context.Context, req *dto.WmsPutawayTaskQueryReq) (*vo.PageResult[vo.WmsPutawayTaskVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务失败").WithCause(err)
	}

	var voResult *vo.PageResult[vo.WmsPutawayTaskVO]
	copier.Copy(&voResult, pageResult)
	return voResult, nil
}

// AssignToUser 分配任务给用户
func (s *wmsPutawayTaskServiceImpl) AssignToUser(ctx context.Context, id uint, userID uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskRepository()

		task, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务失败").WithCause(err)
		}

		// 检查任务状态
		if task.Status != "PENDING" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待处理状态的任务才能分配")
		}

		if err := repo.AssignToUser(ctx, id, userID); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "分配任务失败").WithCause(err)
		}

		return nil
	})
}

// BatchAssignToUser 批量分配任务给用户
func (s *wmsPutawayTaskServiceImpl) BatchAssignToUser(ctx context.Context, taskIDs []uint, userID uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskRepository()

		if err := repo.BatchAssignToUser(ctx, taskIDs, userID); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "批量分配任务失败").WithCause(err)
		}

		return nil
	})
}

// CompleteTask 完成任务
func (s *wmsPutawayTaskServiceImpl) CompleteTask(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskRepository()

		task, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务失败").WithCause(err)
		}

		// 检查任务状态
		if task.Status != "ASSIGNED" && task.Status != "IN_PROGRESS" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已分配或进行中的任务才能完成")
		}

		if err := repo.CompleteTask(ctx, id, time.Now()); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "完成任务失败").WithCause(err)
		}

		return nil
	})
}

// GetPendingTasks 获取待处理任务
func (s *wmsPutawayTaskServiceImpl) GetPendingTasks(ctx context.Context, warehouseID uint64) ([]*vo.WmsPutawayTaskVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskRepository()

	tasks, err := repo.GetPendingTasks(ctx, uint(warehouseID))
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询待处理任务失败").WithCause(err)
	}

	var voResults []*vo.WmsPutawayTaskVO
	copier.Copy(&voResults, tasks)
	return voResults, nil
}

// GetTasksByUser 获取用户的任务
func (s *wmsPutawayTaskServiceImpl) GetTasksByUser(ctx context.Context, userID uint64) ([]*vo.WmsPutawayTaskVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskRepository()

	tasks, err := repo.GetTasksByUser(ctx, uint(userID))
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询用户任务失败").WithCause(err)
	}

	var voResults []*vo.WmsPutawayTaskVO
	copier.Copy(&voResults, tasks)
	return voResults, nil
}

// generateTaskNo 生成任务单号
func (s *wmsPutawayTaskServiceImpl) generateTaskNo(ctx context.Context) (string, error) {
	// 最大重试次数（编码生成失败时）
	const maxRetries = 3
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		s.GetServiceManager().GetLogger().Info("开始生成上架任务编号",
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
			s.GetServiceManager().GetLogger().WithField("maxRetries", maxRetries))

		codeGenService := s.GetServiceManager().GetCodeGenerationService()
		if codeGenService == nil {
			s.GetServiceManager().GetLogger().Error("编码生成服务不可用")
			return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "上架任务编号生成服务暂时不可用，请稍后重试或联系系统管理员。")
		}

		s.GetServiceManager().GetLogger().Info("编码生成服务可用，准备生成上架任务编号")

		s.GetServiceManager().GetLogger().Info("调用编码生成服务",
			s.GetServiceManager().GetLogger().WithField("businessType", "PUTAWAY_TASK"))

		generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
			BusinessType: "PUTAWAY_TASK",
		})
		if err != nil {
			s.GetServiceManager().GetLogger().Error("生成上架任务编号失败",
				s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
				s.GetServiceManager().GetLogger().WithError(err))

			// 检查是否为配置问题（不需要重试）
			if apperrors.IsParamError(err) {
				return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "上架任务编号自动生成失败。可能原因：1) 缺少上架任务编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
			}

			lastErr = err
			continue // 重试
		}

		s.GetServiceManager().GetLogger().Info("上架任务编号生成成功",
			s.GetServiceManager().GetLogger().WithField("generatedCode", generatedCode.GeneratedCode),
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1))

		return generatedCode.GeneratedCode, nil
	}

	// 所有重试都失败了
	if lastErr != nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "上架任务编号生成失败，系统重试次数已达上限。请稍后重试或联系系统管理员。").WithCause(lastErr)
	}

	return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "上架任务编号生成失败，编码生成重试次数已达上限。请联系系统管理员。")
}
