package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	"backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util"
)

// MenuService 菜单服务接口
type MenuService interface {
	// 基础服务
	BaseService

	// 基本CRUD操作
	CreateMenu(ctx context.Context, menuDTO dto.MenuCreateOrUpdateDTO) (*vo.MenuVO, error)
	UpdateMenu(ctx context.Context, id uint, menuDTO dto.MenuCreateOrUpdateDTO) (*vo.MenuVO, error)
	DeleteMenu(ctx context.Context, id uint) error
	GetMenuByID(ctx context.Context, id uint) (*vo.MenuVO, error)
	ListMenus(ctx context.Context, queryDTO dto.MenuPageQueryDTO) ([]*vo.MenuVO, error)
	PageMenus(ctx context.Context, queryDTO dto.MenuPageQueryDTO) (*vo.PageVO, error)

	// 特殊功能
	GetMenuTree(ctx context.Context, queryDTO dto.MenuTreeDTO) ([]*vo.MenuTreeVO, error)
	GetUserMenus(ctx context.Context, userID uint) ([]*vo.MenuVO, error)
	GetUserMenuTree(ctx context.Context, userID uint) ([]*vo.MenuTreeVO, error)
	GetUserPermissions(ctx context.Context, userID uint) ([]string, error)
	UpdateStatus(ctx context.Context, id uint, status int) error
}

// MenuServiceImpl 菜单服务实现
type MenuServiceImpl struct {
	*BaseServiceImpl
}

// NewMenuService 创建菜单服务
func NewMenuService(serviceManager *ServiceManager) MenuService {
	return &MenuServiceImpl{
		BaseServiceImpl: NewBaseService(serviceManager),
	}
}

// GetMenuService 从服务管理器获取菜单服务
func GetMenuService(serviceManager *ServiceManager) MenuService {
	return GetService[MenuService](
		serviceManager,
		func(sm *ServiceManager) MenuService {
			return NewMenuService(sm)
		},
	)
}

// getMenuRepository 获取菜单仓库
func (s *MenuServiceImpl) getMenuRepository() repository.MenuRepository {
	return s.GetServiceManager().GetRepositoryManager().GetMenuRepository()
}

// getRoleMenuRepository 获取角色菜单仓库
func (s *MenuServiceImpl) getRoleMenuRepository() repository.RoleMenuRepository {
	return s.GetServiceManager().GetRepositoryManager().GetRoleMenuRepository()
}

// getUserRoleRepository 获取用户角色仓库
func (s *MenuServiceImpl) getUserRoleRepository() repository.UserRoleRepository {
	return s.GetServiceManager().GetRepositoryManager().GetUserRoleRepository()
}

// CreateMenu 创建菜单
func (s *MenuServiceImpl) CreateMenu(ctx context.Context, menuDTO dto.MenuCreateOrUpdateDTO) (*vo.MenuVO, error) {
	s.GetLogger().Debug(ctx, "创建菜单",
		logger.WithField("name", menuDTO.Name),
		logger.WithField("path", menuDTO.Path))

	// 验证菜单参数
	if err := s.validateMenuParams(ctx, menuDTO, 0); err != nil {
		return nil, err
	}

	// 创建菜单实体
	menu := &entity.Menu{
		Name:       menuDTO.Name,
		Path:       menuDTO.Path,
		Component:  menuDTO.Component,
		Redirect:   menuDTO.Redirect,
		Icon:       menuDTO.Icon,
		Title:      menuDTO.Title,
		Hidden:     menuDTO.Hidden,
		NoCache:    menuDTO.NoCache,
		Sort:       menuDTO.Sort,
		Type:       menuDTO.Type,
		Permission: menuDTO.Permission,
		Status:     menuDTO.Status,
		Remark:     menuDTO.Remark,
	}

	// Handle ParentID separately due to type change (*uint)
	if menuDTO.ParentID != 0 {
		parentID := menuDTO.ParentID // Create a local variable to take its address
		menu.ParentID = &parentID
	} else {
		menu.ParentID = nil // Top-level menu
	}

	// Set CreatedBy and UpdatedBy
	uid64, err := util.GetUserIDFromStdContext(ctx) // 使用 util.GetUserIDFromStdContext
	if err != nil {
		s.GetLogger().WithError(err).Warn(ctx, "CreateMenu: 无法从上下文中获取UserID")
	}
	menu.CreatedBy = uint(uid64)
	menu.UpdatedBy = uint(uid64)

	// 保存菜单
	menuRepo := s.getMenuRepository()
	if err := menuRepo.Create(ctx, menu); err != nil {
		s.GetLogger().Error(ctx, "创建菜单失败",
			logger.WithField("name", menuDTO.Name),
			logger.WithField("path", menuDTO.Path),
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_CREATE_FAILED, "创建菜单数据失败")
	}

	// 转换为视图对象
	return s.convertToMenuVO(menu), nil
}

// UpdateMenu 更新菜单
func (s *MenuServiceImpl) UpdateMenu(ctx context.Context, id uint, menuDTO dto.MenuCreateOrUpdateDTO) (*vo.MenuVO, error) {
	s.GetLogger().Debug(ctx, "更新菜单",
		logger.WithField("menuId", id),
		logger.WithField("name", menuDTO.Name),
		logger.WithField("path", menuDTO.Path))

	// 验证菜单参数
	if err := s.validateMenuParams(ctx, menuDTO, id); err != nil {
		return nil, err
	}

	// 查询菜单
	menuRepo := s.getMenuRepository()
	menu, err := menuRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询菜单失败",
			logger.WithField("menuId", id),
			logger.WithError(err))
		return nil, err
	}

	// 更新菜单信息
	menu.Name = menuDTO.Name
	menu.Path = menuDTO.Path
	menu.Component = menuDTO.Component
	menu.Redirect = menuDTO.Redirect
	menu.Icon = menuDTO.Icon
	menu.Title = menuDTO.Title
	menu.Hidden = menuDTO.Hidden
	menu.NoCache = menuDTO.NoCache
	menu.Breadcrumb = menuDTO.Breadcrumb
	menu.AlwaysShow = menuDTO.AlwaysShow
	menu.ActiveMenu = menuDTO.ActiveMenu
	menu.Sort = menuDTO.Sort
	menu.Type = menuDTO.Type
	menu.Permission = menuDTO.Permission
	menu.Status = menuDTO.Status
	menu.Remark = menuDTO.Remark

	// Handle ParentID separately
	if menuDTO.ParentID != 0 {
		parentID := menuDTO.ParentID
		menu.ParentID = &parentID
	} else {
		menu.ParentID = nil
	}

	// Set UpdatedBy
	uid64Update, errUpdate := util.GetUserIDFromStdContext(ctx) // 使用 util.GetUserIDFromStdContext
	if errUpdate != nil {
		s.GetLogger().WithError(errUpdate).Warn(ctx, "UpdateMenu: 无法从上下文中获取UserID")
	}
	menu.UpdatedBy = uint(uid64Update)

	// 保存菜单
	if err := menuRepo.Update(ctx, menu); err != nil {
		s.GetLogger().Error(ctx, "更新菜单失败",
			logger.WithField("menuId", id),
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_UPDATE_FAILED, "更新菜单数据失败")
	}

	// 转换为视图对象
	return s.convertToMenuVO(menu), nil
}

// DeleteMenu 删除菜单
func (s *MenuServiceImpl) DeleteMenu(ctx context.Context, id uint) error {
	s.GetLogger().Debug(ctx, "删除菜单",
		logger.WithField("menuId", id))

	// <<< 先在事务外检查菜单是否存在 >>>
	menuRepo := s.getMenuRepository() // Non-tx repo for initial check
	_, err := menuRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询菜单失败 (删除前检查)", logger.WithField("menuId", id), logger.WithError(err))
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			return nil // Not found, consider delete successful (idempotent)
		}
		return err // Return wrapped error from FindByID if configured
	}

	// <<< 使用事务包裹检查和删除操作 >>>
	err = s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		txMenuRepo := txSm.GetRepositoryManager().GetMenuRepository()
		txRoleMenuRepo := txSm.GetRepositoryManager().GetRoleMenuRepository()
		txLog := logger.WithContext(ctx).WithFields(logger.Fields{"tx": true, "menuId": id})

		// 1. 检查是否有子菜单 (事务内检查防止竞态条件)
		children, findErr := txMenuRepo.FindByParentID(ctx, id, nil)
		if findErr != nil {
			txLog.Error("事务中查询子菜单失败", logger.WithError(findErr))
			return errors.WrapError(findErr, errors.CODE_DATA_QUERY_FAILED, "查询子菜单失败")
		}
		if len(children) > 0 {
			txLog.Warn("存在子菜单，不允许删除")
			return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "存在子菜单，不允许删除")
		}

		// 2. 删除菜单与角色的关联 (事务内处理)
		if roleMenuErr := txRoleMenuRepo.DeleteMenuRoles(ctx, id); roleMenuErr != nil {
			txLog.Error("事务中删除菜单角色关联失败", logger.WithError(roleMenuErr))
			return errors.WrapError(roleMenuErr, errors.CODE_DATA_DELETE_FAILED, "删除菜单角色关联失败")
		}

		// 3. 删除菜单
		if deleteErr := txMenuRepo.Delete(ctx, id); deleteErr != nil {
			txLog.Error("事务中删除菜单失败", logger.WithError(deleteErr))
			return errors.WrapError(deleteErr, errors.CODE_DATA_DELETE_FAILED, "删除菜单失败")
		}

		return nil // 事务成功
	})

	if err != nil {
		// 事务执行失败，错误已在内部记录和包装
		return err
	}

	s.GetLogger().Info(ctx, "菜单删除成功", logger.WithField("menuId", id))
	return nil
}

// GetMenuByID 根据ID获取菜单
func (s *MenuServiceImpl) GetMenuByID(ctx context.Context, id uint) (*vo.MenuVO, error) {
	s.GetLogger().Debug(ctx, "根据ID获取菜单",
		logger.WithField("menuId", id))

	// 查询菜单
	menuRepo := s.getMenuRepository()
	menu, err := menuRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询菜单失败",
			logger.WithField("menuId", id),
			logger.WithError(err))
		return nil, err
	}

	// 转换为视图对象
	return s.convertToMenuVO(menu), nil
}

// ListMenus 获取菜单列表
func (s *MenuServiceImpl) ListMenus(ctx context.Context, queryDTO dto.MenuPageQueryDTO) ([]*vo.MenuVO, error) {
	s.GetLogger().Debug(ctx, "获取菜单列表")

	// 构建查询条件
	conditions := s.buildMenuQueryConditions(queryDTO)
	// 将 []repository.Condition 转换为 []repository.QueryCondition
	queryConditions := make([]repository.QueryCondition, len(conditions))
	for i, c := range conditions {
		queryConditions[i] = c
	}

	// 查询菜单列表
	menuRepo := s.getMenuRepository()
	sortInfos := []response.SortInfo{{Field: "sort", Order: "asc"}}         // 修正字段名
	menus, err := menuRepo.FindByCondition(ctx, queryConditions, sortInfos) // 使用转换后的类型
	if err != nil {
		s.GetLogger().Error(ctx, "查询菜单列表失败",
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询菜单列表数据失败")
	}

	// 转换为视图对象列表
	return s.convertToMenuVOList(menus), nil
}

// PageMenus 分页获取菜单列表
func (s *MenuServiceImpl) PageMenus(ctx context.Context, queryDTO dto.MenuPageQueryDTO) (*vo.PageVO, error) {
	s.GetLogger().Debug(ctx, "分页获取菜单列表",
		logger.WithField("pageNum", queryDTO.PageQuery.PageNum),
		logger.WithField("pageSize", queryDTO.PageQuery.PageSize))

	// 构建查询条件
	conditions := s.buildMenuQueryConditions(queryDTO)
	// 将 []repository.Condition 转换为 []repository.QueryCondition
	queryConditions := make([]repository.QueryCondition, len(conditions))
	for i, c := range conditions {
		queryConditions[i] = c
	}

	// 分页查询菜单列表
	menuRepo := s.getMenuRepository()
	pageQuery := &response.PageQuery{
		PageNum:  queryDTO.PageQuery.PageNum,
		PageSize: queryDTO.PageQuery.PageSize,
		Sort:     queryDTO.PageQuery.Sort,
	}
	pageResult, err := menuRepo.FindByPage(ctx, pageQuery, queryConditions)
	if err != nil {
		s.GetLogger().Error(ctx, "分页查询菜单列表失败",
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "分页查询菜单列表数据失败")
	}

	// 转换分页中的菜单列表为视图对象列表
	menuVOs := s.convertToMenuVOList(pageResult.List.([]*entity.Menu))

	// 构建分页视图对象
	pageVO := &vo.PageVO{
		List:     menuVOs,
		Total:    pageResult.Total,
		Pages:    pageResult.Pages,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
	}

	return pageVO, nil
}

// GetMenuTree 获取菜单树
func (s *MenuServiceImpl) GetMenuTree(ctx context.Context, queryDTO dto.MenuTreeDTO) ([]*vo.MenuTreeVO, error) {
	s.GetLogger().Debug(ctx, "获取菜单树")

	// 构建查询条件
	menuQueryDTO := dto.MenuPageQueryDTO{
		Status: queryDTO.Status,
		Type:   queryDTO.Type,
	}

	// 获取菜单列表
	menuVOs, err := s.ListMenus(ctx, menuQueryDTO)
	if err != nil {
		return nil, err
	}

	// 构建菜单树
	return s.buildMenuTree(menuVOs, 0), nil
}

// GetUserMenus 获取用户拥有的菜单列表 (扁平结构)
func (s *MenuServiceImpl) GetUserMenus(ctx context.Context, userID uint) ([]*vo.MenuVO, error) {
	s.GetLogger().Debug(ctx, "获取用户菜单列表", logger.WithField("userId", userID))

	var menus []*entity.Menu
	var err error

	// --- Check if the user is an admin ---
	userRepo := s.GetServiceManager().GetRepositoryManager().GetUserRepository()
	user, userErr := userRepo.FindByID(ctx, userID)
	if userErr != nil {
		s.GetLogger().Error(ctx, "查询用户信息失败",
			logger.WithField("userId", userID),
			logger.WithError(userErr))
		return nil, fmt.Errorf("查询用户信息失败: %w", userErr)
	}

	if user.IsAdmin {
		// --- Admin User: Get all active and visible menus ---
		s.GetLogger().Info(ctx, "用户是管理员，获取所有有效菜单", logger.WithField("userId", userID))
		menuRepo := s.getMenuRepository()
		conditions := []repository.Condition{
			{Field: "status", Operator: repository.OP_EQ, Value: 1},
			{Field: "hidden", Operator: repository.OP_EQ, Value: false},
		}
		// 将 []repository.Condition 转换为 []repository.QueryCondition
		queryConditions := make([]repository.QueryCondition, len(conditions))
		for i, c := range conditions {
			queryConditions[i] = c
		}
		sortInfos := []response.SortInfo{{Field: "sort", Order: "asc"}}        // 修正字段名
		menus, err = menuRepo.FindByCondition(ctx, queryConditions, sortInfos) // 使用转换后的类型
		if err != nil {
			s.GetLogger().Error(ctx, "管理员获取所有菜单失败", logger.WithError(err))
			return nil, fmt.Errorf("管理员获取所有菜单失败: %w", err)
		}
	} else {
		// --- Non-Admin User: Original role-based logic ---
		s.GetLogger().Debug(ctx, "非管理员用户，执行基于角色的菜单查询", logger.WithField("userId", userID))
		// 1. 获取用户角色实体列表
		userRoleRepo := s.getUserRoleRepository()
		roles, roleErr := userRoleRepo.GetUserRoles(ctx, userID, nil)
		if roleErr != nil {
			s.GetLogger().Error(ctx, "查询用户角色失败",
				logger.WithField("userId", userID),
				logger.WithError(roleErr))
			return nil, fmt.Errorf("查询用户角色失败: %w", roleErr)
		}

		if len(roles) == 0 {
			s.GetLogger().Info(ctx, "用户没有任何角色，返回空菜单", logger.WithField("userId", userID))
			return []*vo.MenuVO{}, nil
		}

		// 提取角色 ID 列表
		roleIDs := make([]uint, 0, len(roles))
		for _, role := range roles {
			roleIDs = append(roleIDs, role.ID)
		}

		// 2. 直接根据角色ID列表查询菜单实体 (添加 nil for sortInfos)
		menuRepo := s.getMenuRepository()
		menus, err = menuRepo.FindMenusByRoleIDs(ctx, roleIDs, nil)
		if err != nil {
			s.GetLogger().Error(ctx, "根据角色ID查询菜单实体失败",
				logger.WithField("roleIds", roleIDs),
				logger.WithError(err))
			return nil, fmt.Errorf("根据角色ID查询菜单实体失败: %w", err)
		}
	}

	// 4. 转换为视图对象列表 (Common step for both admin and non-admin)
	menuVOs := s.convertToMenuVOList(menus)

	return menuVOs, nil
}

// GetUserMenuTree 获取用户菜单树
func (s *MenuServiceImpl) GetUserMenuTree(ctx context.Context, userID uint) ([]*vo.MenuTreeVO, error) {
	s.GetLogger().Debug(ctx, "获取用户菜单树",
		logger.WithField("userId", userID))

	// 获取用户菜单
	menuVOs, err := s.GetUserMenus(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 构建菜单树
	return s.buildMenuTree(menuVOs, 0), nil
}

// GetUserPermissions 获取用户权限标识列表
func (s *MenuServiceImpl) GetUserPermissions(ctx context.Context, userID uint) ([]string, error) {
	s.GetLogger().Debug(ctx, "获取用户权限标识列表", logger.WithField("userId", userID))

	var permissions []string
	var err error

	// --- Check if the user is an admin ---
	userRepo := s.GetServiceManager().GetRepositoryManager().GetUserRepository()
	user, userErr := userRepo.FindByID(ctx, userID)
	if userErr != nil {
		s.GetLogger().Error(ctx, "查询用户信息失败",
			logger.WithField("userId", userID),
			logger.WithError(userErr))
		return nil, fmt.Errorf("查询用户信息失败: %w", userErr)
	}

	if user.IsAdmin {
		// --- Admin User: Get all non-empty permissions from active menus ---
		s.GetLogger().Info(ctx, "用户是管理员，获取所有有效权限标识", logger.WithField("userId", userID))
		menuRepo := s.getMenuRepository()
		conditions := []repository.Condition{
			{Field: "status", Operator: repository.OP_EQ, Value: 1},
			{Field: "permission", Operator: repository.OP_NE, Value: ""},
		}
		// 将 []repository.Condition 转换为 []repository.QueryCondition
		queryConditions := make([]repository.QueryCondition, len(conditions))
		for i, c := range conditions {
			queryConditions[i] = c
		}
		sortInfos := []response.SortInfo{{Field: "id", Order: "asc"}}               // 修正字段名
		menus, menuErr := menuRepo.FindByCondition(ctx, queryConditions, sortInfos) // 使用转换后的类型
		if menuErr != nil {
			s.GetLogger().Error(ctx, "管理员获取所有菜单以提取权限失败", logger.WithError(menuErr))
			return nil, fmt.Errorf("管理员获取所有菜单以提取权限失败: %w", menuErr)
		}
		permissions = make([]string, 0, len(menus))
		permissionSet := make(map[string]struct{})
		for _, menu := range menus {
			if menu.Permission != "" {
				if _, exists := permissionSet[menu.Permission]; !exists {
					permissions = append(permissions, menu.Permission)
					permissionSet[menu.Permission] = struct{}{}
				}
			}
		}
	} else {
		// --- Non-Admin User: Original role-based logic ---
		s.GetLogger().Debug(ctx, "非管理员用户，执行基于角色的权限查询", logger.WithField("userId", userID))
		// 1. 获取用户角色实体列表
		userRoleRepo := s.getUserRoleRepository()
		roles, roleErr := userRoleRepo.GetUserRoles(ctx, userID, nil)
		if roleErr != nil {
			s.GetLogger().Error(ctx, "查询用户角色失败",
				logger.WithField("userId", userID),
				logger.WithError(roleErr))
			return nil, fmt.Errorf("查询用户角色失败: %w", roleErr)
		}

		if len(roles) == 0 {
			s.GetLogger().Info(ctx, "用户没有任何角色，返回空权限列表", logger.WithField("userId", userID))
			return []string{}, nil
		}

		// 提取角色 ID 列表
		roleIDs := make([]uint, 0, len(roles))
		for _, role := range roles {
			roleIDs = append(roleIDs, role.ID)
		}

		// 2. 获取这些角色拥有的所有非空权限标识 (添加 nil for sortInfos)
		menuRepo := s.getMenuRepository()
		permissions, err = menuRepo.FindPermissionsByRoleIDs(ctx, roleIDs, nil)
		if err != nil {
			s.GetLogger().Error(ctx, "查询角色权限标识失败",
				logger.WithField("roleIds", roleIDs),
				logger.WithError(err))
			return nil, fmt.Errorf("查询角色权限标识失败: %w", err)
		}
	}

	return permissions, nil
}

// UpdateStatus 更新菜单状态
func (s *MenuServiceImpl) UpdateStatus(ctx context.Context, id uint, status int) error {
	s.GetLogger().Debug(ctx, "更新菜单状态",
		logger.WithField("menuId", id),
		logger.WithField("status", status))

	// 查询菜单
	menuRepo := s.getMenuRepository()
	_, err := menuRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询菜单失败",
			logger.WithField("menuId", id),
			logger.WithError(err))
		return err
	}

	// 更新状态
	if err := menuRepo.UpdateStatus(ctx, id, status); err != nil {
		s.GetLogger().Error(ctx, "更新菜单状态失败",
			logger.WithField("menuId", id),
			logger.WithError(err))
		return errors.WrapError(err, errors.CODE_DATA_UPDATE_FAILED, "更新菜单状态数据失败")
	}

	return nil
}

// validateMenuParams 验证菜单参数
func (s *MenuServiceImpl) validateMenuParams(ctx context.Context, menuDTO interface{}, id uint) error {
	var parentID uint
	var ok bool
	var dtoInstance dto.MenuCreateOrUpdateDTO // 使用合并后的类型

	switch v := menuDTO.(type) {
	case dto.MenuCreateOrUpdateDTO: // 修正类型
		dtoInstance = v
		ok = true
	default:
		return errors.NewParamError(errors.CODE_PARAMS_INVALID, "菜单参数类型错误")
	}

	if !ok {
		// This should ideally not be reached due to the default case, but added for safety
		return errors.NewParamError(errors.CODE_PARAMS_INVALID, "无法识别的菜单参数类型")
	}

	parentID = dtoInstance.ParentID // 从正确的实例获取 ParentID

	// 验证父菜单
	if parentID > 0 {
		menuRepo := s.getMenuRepository()
		_, err := menuRepo.FindByID(ctx, parentID)
		if err != nil {
			if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
				return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "父菜单不存在")
			}
			s.GetLogger().Error(ctx, "查询父菜单失败",
				logger.WithField("parentId", parentID),
				logger.WithError(err))
			return errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询父菜单数据失败")
		}

		// 不能选择自己作为父菜单
		if id > 0 && id == parentID {
			return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "不能选择自己作为父菜单")
		}

		// 不能选择子菜单作为父菜单（防止循环依赖）
		if id > 0 {
			children, err := s.getChildrenIDs(ctx, id)
			if err != nil {
				return err
			}
			for _, childID := range children {
				if childID == parentID {
					return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "不能选择子菜单作为父菜单")
				}
			}
		}
	}

	return nil
}

// getChildrenIDs 获取所有子菜单ID
func (s *MenuServiceImpl) getChildrenIDs(ctx context.Context, menuID uint) ([]uint, error) {
	menuRepo := s.getMenuRepository()
	// 添加 nil for sortInfos
	children, err := menuRepo.FindByParentID(ctx, menuID, nil)
	if err != nil {
		s.GetLogger().Error(ctx, "查询子菜单失败",
			logger.WithField("menuId", menuID),
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "递归查询子菜单失败")
	}

	ids := make([]uint, 0, len(children))
	for _, child := range children {
		ids = append(ids, child.ID)
		// 递归获取子菜单的子菜单
		childIDs, err := s.getChildrenIDs(ctx, child.ID)
		if err != nil {
			return nil, errors.WrapError(err, errors.CODE_SYSTEM_INTERNAL, fmt.Sprintf("递归获取子菜单 %d 的子菜单失败", child.ID))
		}
		ids = append(ids, childIDs...)
	}

	return ids, nil
}

// buildMenuQueryConditions 构建菜单查询条件
func (s *MenuServiceImpl) buildMenuQueryConditions(queryDTO dto.MenuPageQueryDTO) []repository.Condition {
	conditions := make([]repository.Condition, 0)

	// 菜单名称（模糊查询）
	if queryDTO.Name != nil && *queryDTO.Name != "" {
		conditions = append(conditions, repository.NewLikeCondition("name", *queryDTO.Name))
	}

	// 菜单标题（模糊查询）
	if queryDTO.Title != nil && *queryDTO.Title != "" {
		conditions = append(conditions, repository.NewLikeCondition("title", *queryDTO.Title))
	}

	// 菜单类型
	if queryDTO.Type != nil {
		conditions = append(conditions, repository.NewEqualCondition("type", *queryDTO.Type))
	}

	// 状态
	if queryDTO.Status != nil {
		conditions = append(conditions, repository.NewEqualCondition("status", *queryDTO.Status))
	}

	// 权限标识（模糊查询）
	if queryDTO.Permission != nil && *queryDTO.Permission != "" {
		conditions = append(conditions, repository.NewLikeCondition("permission", *queryDTO.Permission))
	}

	return conditions
}

// buildMenuTree 构建菜单树
func (s *MenuServiceImpl) buildMenuTree(menuVOs []*vo.MenuVO, parentID uint) []*vo.MenuTreeVO {
	// 按父ID分组
	menuMap := make(map[uint][]*vo.MenuVO)
	for _, menu := range menuVOs {
		menuMap[menu.ParentID] = append(menuMap[menu.ParentID], menu)
	}

	// 构建树
	return s.buildMenuTreeNodes(menuMap, parentID)
}

// buildMenuTreeNodes 构建菜单树节点
func (s *MenuServiceImpl) buildMenuTreeNodes(menuMap map[uint][]*vo.MenuVO, parentID uint) []*vo.MenuTreeVO {
	treeNodes := make([]*vo.MenuTreeVO, 0)
	children := menuMap[parentID]
	if len(children) == 0 {
		return treeNodes
	}

	for _, child := range children {
		treeNode := &vo.MenuTreeVO{
			ID:         child.ID,
			ParentID:   child.ParentID,
			Name:       child.Name,
			Path:       child.Path,
			Component:  child.Component,
			Redirect:   child.Redirect,
			Icon:       child.Icon,
			Title:      child.Title,
			Hidden:     child.Hidden,
			Sort:       child.Sort,
			Type:       child.Type,
			Permission: child.Permission,
			Status:     child.Status,
			Children:   s.buildMenuTreeNodes(menuMap, child.ID),
		}
		treeNodes = append(treeNodes, treeNode)
	}

	return treeNodes
}

// convertToMenuVO 将菜单实体转换为视图对象
func (s *MenuServiceImpl) convertToMenuVO(menu *entity.Menu) *vo.MenuVO {
	if menu == nil {
		return nil
	}

	// 创建基础的TenantVO
	baseVO := vo.BaseVO{
		ID:        menu.ID,
		CreatedAt: menu.CreatedAt,
		UpdatedAt: menu.UpdatedAt,
		CreatedBy: menu.CreatedBy,
		UpdatedBy: menu.UpdatedBy,
	}

	tenantVO := vo.TenantVO{
		BaseVO:   baseVO,
		TenantID: menu.TenantID, // Assuming Menu entity has TenantID field
	}

	// Correctly handle *uint ParentID when converting to uint
	var parentIDValue uint
	if menu.ParentID != nil {
		parentIDValue = *menu.ParentID
	} // else parentIDValue remains 0 (default for uint)

	return &vo.MenuVO{
		TenantVO:   tenantVO,
		ParentID:   parentIDValue,
		Name:       menu.Name,
		Path:       menu.Path,
		Component:  menu.Component,
		Redirect:   menu.Redirect,
		Icon:       menu.Icon,
		Title:      menu.Title,
		Hidden:     menu.Hidden,
		NoCache:    menu.NoCache,
		Breadcrumb: menu.Breadcrumb,
		AlwaysShow: menu.AlwaysShow,
		ActiveMenu: menu.ActiveMenu,
		Sort:       menu.Sort,
		Type:       menu.Type,
		Permission: menu.Permission,
		Status:     menu.Status,
		Remark:     menu.Remark,
	}
}

// convertToMenuVOList 将菜单实体列表转换为视图对象列表
func (s *MenuServiceImpl) convertToMenuVOList(menus []*entity.Menu) []*vo.MenuVO {
	menuVOs := make([]*vo.MenuVO, 0, len(menus))
	for _, menu := range menus {
		menuVOs = append(menuVOs, s.convertToMenuVO(menu))
	}
	return menuVOs
}
