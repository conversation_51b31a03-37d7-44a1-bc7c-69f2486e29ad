package security

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"backend/pkg/errors"
)

// CSRF相关常量
const (
	// 默认配置
	DEFAULT_CSRF_HEADER       = "X-CSRF-Token"    // 默认CSRF头
	DEFAULT_CSRF_PARAM        = "_csrf"           // 默认CSRF参数名
	DEFAULT_CSRF_COOKIE       = "csrf_token"      // 默认CSRF Cookie名
	DEFAULT_CSRF_TIMEOUT      = 24 * time.Hour    // 默认超时时间
	DEFAULT_CSRF_TOKEN_LENGTH = 32                // 默认令牌长度
	DEFAULT_CSRF_SESSION_KEY  = "csrf:session:%s" // 默认会话键模板

	// 错误码
	CSRF_ERROR_INVALID_TOKEN     = "INVALID_CSRF_TOKEN"      // 无效的CSRF令牌
	CSRF_ERROR_TOKEN_EXPIRED     = "CSRF_TOKEN_EXPIRED"      // CSRF令牌已过期
	CSRF_ERROR_TOKEN_USED        = "CSRF_TOKEN_ALREADY_USED" // CSRF令牌已使用
	CSRF_ERROR_TOKEN_NOT_FOUND   = "CSRF_TOKEN_NOT_FOUND"    // CSRF令牌未找到
	CSRF_ERROR_STORAGE_ERROR     = "CSRF_STORAGE_ERROR"      // 存储错误
	CSRF_ERROR_SESSION_NOT_FOUND = "SESSION_NOT_FOUND"       // 会话未找到
)

// CSRF错误代码
const (
	CODE_CSRF_ERROR_BASE        = errors.CODE_AUTH_ERROR + 200 // CSRF错误基础代码
	CODE_CSRF_TOKEN_INVALID     = CODE_CSRF_ERROR_BASE + 1            // 无效的CSRF令牌
	CODE_CSRF_TOKEN_EXPIRED     = CODE_CSRF_ERROR_BASE + 2            // CSRF令牌已过期
	CODE_CSRF_TOKEN_USED        = CODE_CSRF_ERROR_BASE + 3            // CSRF令牌已使用
	CODE_CSRF_TOKEN_NOT_FOUND   = CODE_CSRF_ERROR_BASE + 4            // CSRF令牌未找到
	CODE_CSRF_STORAGE_ERROR     = CODE_CSRF_ERROR_BASE + 5            // 存储错误
	CODE_CSRF_SESSION_NOT_FOUND = CODE_CSRF_ERROR_BASE + 6            // 会话未找到
)

// CSRFConfig CSRF配置
type CSRFConfig struct {
	Enabled        bool             `json:"enabled" yaml:"enabled"`               // 是否启用CSRF防护
	HeaderName     string           `json:"headerName" yaml:"headerName"`         // CSRF请求头名称
	ParamName      string           `json:"paramName" yaml:"paramName"`           // CSRF参数名称
	CookieName     string           `json:"cookieName" yaml:"cookieName"`         // CSRF Cookie名称
	CookiePath     string           `json:"cookiePath" yaml:"cookiePath"`         // Cookie路径
	CookieDomain   string           `json:"cookieDomain" yaml:"cookieDomain"`     // Cookie域
	CookieSecure   bool             `json:"cookieSecure" yaml:"cookieSecure"`     // Cookie是否安全
	CookieHTTPOnly bool             `json:"cookieHttpOnly" yaml:"cookieHttpOnly"` // Cookie是否仅HTTP
	CookieSameSite string           `json:"cookieSameSite" yaml:"cookieSameSite"` // Cookie SameSite
	SessionKey     string           `json:"sessionKey" yaml:"sessionKey"`         // 会话键模板
	TokenLength    int              `json:"tokenLength" yaml:"tokenLength"`       // 令牌长度
	Timeout        time.Duration    `json:"timeout" yaml:"timeout"`               // 超时时间
	SafeMethods    []string         `json:"safeMethods" yaml:"safeMethods"`       // 安全方法（无需验证）
	ExcludedPaths  []string         `json:"excludedPaths" yaml:"excludedPaths"`   // 排除的路径
	TrustXHeaders  bool             `json:"trustXHeaders" yaml:"trustXHeaders"`   // 是否信任X-头
	AllowedOrigins []string         `json:"allowedOrigins" yaml:"allowedOrigins"` // 允许的来源
	FailureStatus  int              `json:"failureStatus" yaml:"failureStatus"`   // 失败状态码
	ErrorHandler   CSRFErrorHandler `json:"-" yaml:"-"`                           // 错误处理器
	TokenStore     CSRFTokenStore   `json:"-" yaml:"-"`                           // 令牌存储
}

// CSRFToken CSRF令牌
type CSRFToken struct {
	Value     string    `json:"value"`     // 令牌值
	Created   time.Time `json:"created"`   // 创建时间
	Expires   time.Time `json:"expires"`   // 过期时间
	SessionID string    `json:"sessionId"` // 会话ID
	Used      bool      `json:"used"`      // 是否已使用
}

// CSRFTokenStore CSRF令牌存储接口
type CSRFTokenStore interface {
	// SaveToken 保存令牌
	SaveToken(ctx context.Context, token CSRFToken) error

	// GetToken 获取令牌
	GetToken(ctx context.Context, tokenValue, sessionID string) (CSRFToken, error)

	// InvalidateToken 失效令牌
	InvalidateToken(ctx context.Context, tokenValue, sessionID string) error

	// CleanupExpiredTokens 清理过期令牌
	CleanupExpiredTokens(ctx context.Context) error
}

// CSRFErrorHandler CSRF错误处理接口
type CSRFErrorHandler interface {
	// HandleError 处理CSRF错误
	HandleError(w http.ResponseWriter, r *http.Request, err error)
}

// DefaultCSRFErrorHandler 默认CSRF错误处理器
type DefaultCSRFErrorHandler struct {
	FailureStatus int
}

// HandleError 处理CSRF错误
// 参数:
//   - w: 响应写入器
//   - r: HTTP请求
//   - err: 错误
func (h *DefaultCSRFErrorHandler) HandleError(w http.ResponseWriter, r *http.Request, err error) {
	status := h.FailureStatus
	if status == 0 {
		status = http.StatusForbidden
	}

	// 检查是否为自定义错误
	var csrfErr *errors.CustomError
	if ce, ok := err.(*errors.CustomError); ok {
		csrfErr = ce
	} else {
		// 如果不是自定义错误，创建一个
		csrfErr = errors.NewAuthError(CODE_CSRF_TOKEN_INVALID, err.Error()).
			WithHttpStatus(status)
	}

	// 返回JSON错误响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(csrfErr.GetHttpStatus())
	w.Write([]byte(csrfErr.ToJSON()))
}

// MemoryCSRFTokenStore 内存CSRF令牌存储
type MemoryCSRFTokenStore struct {
	tokens map[string]CSRFToken
	mu     sync.RWMutex
}

// NewMemoryCSRFTokenStore 创建内存CSRF令牌存储
// 返回:
//   - *MemoryCSRFTokenStore: 内存令牌存储实例
func NewMemoryCSRFTokenStore() *MemoryCSRFTokenStore {
	return &MemoryCSRFTokenStore{
		tokens: make(map[string]CSRFToken),
	}
}

// SaveToken 保存令牌
// 参数:
//   - ctx: 上下文
//   - token: CSRF令牌
//
// 返回:
//   - error: 错误信息
func (s *MemoryCSRFTokenStore) SaveToken(ctx context.Context, token CSRFToken) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	key := s.TokenKey(token.Value, token.SessionID)
	s.tokens[key] = token
	return nil
}

// GetToken 获取令牌
// 参数:
//   - ctx: 上下文
//   - tokenValue: 令牌值
//   - sessionID: 会话ID
//
// 返回:
//   - CSRFToken: CSRF令牌
//   - error: 错误信息
func (s *MemoryCSRFTokenStore) GetToken(ctx context.Context, tokenValue, sessionID string) (CSRFToken, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	key := s.TokenKey(tokenValue, sessionID)
	token, ok := s.tokens[key]
	if !ok {
		return CSRFToken{}, errors.NewAuthError(CODE_CSRF_TOKEN_NOT_FOUND, "CSRF令牌未找到").
			WithMetadata("error_code", CSRF_ERROR_TOKEN_NOT_FOUND)
	}
	if token.Expires.Before(time.Now()) {
		return CSRFToken{}, errors.NewAuthError(CODE_CSRF_TOKEN_EXPIRED, "CSRF令牌已过期").
			WithMetadata("error_code", CSRF_ERROR_TOKEN_EXPIRED)
	}
	if token.Used {
		return CSRFToken{}, errors.NewAuthError(CODE_CSRF_TOKEN_USED, "CSRF令牌已使用").
			WithMetadata("error_code", CSRF_ERROR_TOKEN_USED)
	}
	return token, nil
}

// InvalidateToken 失效令牌
// 参数:
//   - ctx: 上下文
//   - tokenValue: 令牌值
//   - sessionID: 会话ID
//
// 返回:
//   - error: 错误信息
func (s *MemoryCSRFTokenStore) InvalidateToken(ctx context.Context, tokenValue, sessionID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	key := s.TokenKey(tokenValue, sessionID)
	token, ok := s.tokens[key]
	if !ok {
		return errors.NewAuthError(CODE_CSRF_TOKEN_NOT_FOUND, "CSRF令牌未找到").
			WithMetadata("error_code", CSRF_ERROR_TOKEN_NOT_FOUND)
	}
	token.Used = true
	s.tokens[key] = token
	return nil
}

// CleanupExpiredTokens 清理过期令牌
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - error: 错误信息
func (s *MemoryCSRFTokenStore) CleanupExpiredTokens(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	now := time.Now()
	for key, token := range s.tokens {
		if token.Expires.Before(now) {
			delete(s.tokens, key)
		}
	}
	return nil
}

// TokenKey 生成令牌键
// 参数:
//   - tokenValue: 令牌值
//   - sessionID: 会话ID
//
// 返回:
//   - string: 令牌键
func (s *MemoryCSRFTokenStore) TokenKey(tokenValue, sessionID string) string {
	return fmt.Sprintf("%s:%s", sessionID, tokenValue)
}

// CSRF CSRF防护
type CSRF struct {
	config     CSRFConfig
	tokenStore CSRFTokenStore
}

// CSRFOption CSRF选项函数
type CSRFOption func(*CSRFConfig)

var (
	defaultCSRF *CSRF
	csrfOnce    sync.Once
)

// NewCSRF 创建CSRF防护
// 参数:
//   - options: CSRF选项
//
// 返回:
//   - *CSRF: CSRF实例
func NewCSRF(options ...CSRFOption) *CSRF {
	config := CSRFConfig{
		Enabled:        true,
		HeaderName:     DEFAULT_CSRF_HEADER,
		ParamName:      DEFAULT_CSRF_PARAM,
		CookieName:     DEFAULT_CSRF_COOKIE,
		CookiePath:     "/",
		CookieHTTPOnly: true,
		SessionKey:     DEFAULT_CSRF_SESSION_KEY,
		TokenLength:    DEFAULT_CSRF_TOKEN_LENGTH,
		Timeout:        DEFAULT_CSRF_TIMEOUT,
		SafeMethods:    []string{"GET", "HEAD", "OPTIONS", "TRACE"},
		FailureStatus:  http.StatusForbidden,
		ErrorHandler:   &DefaultCSRFErrorHandler{FailureStatus: http.StatusForbidden},
		TokenStore:     NewMemoryCSRFTokenStore(),
	}

	// 应用选项
	for _, option := range options {
		option(&config)
	}

	return &CSRF{
		config:     config,
		tokenStore: config.TokenStore,
	}
}

// Init 初始化CSRF防护
// 参数:
//   - options: CSRF选项
func Init(options ...CSRFOption) {
	csrfOnce.Do(func() {
		defaultCSRF = NewCSRF(options...)
	})
}

// GetCSRF 获取CSRF防护实例
// 返回:
//   - *CSRF: CSRF实例
func GetCSRF() *CSRF {
	if defaultCSRF == nil {
		Init()
	}
	return defaultCSRF
}

// WithHeaderName 设置头名称
// 参数:
//   - headerName: 头名称
//
// 返回:
//   - CSRFOption: CSRF选项
func WithHeaderName(headerName string) CSRFOption {
	return func(c *CSRFConfig) {
		c.HeaderName = headerName
	}
}

// WithParamName 设置参数名称
// 参数:
//   - paramName: 参数名称
//
// 返回:
//   - CSRFOption: CSRF选项
func WithParamName(paramName string) CSRFOption {
	return func(c *CSRFConfig) {
		c.ParamName = paramName
	}
}

// WithCookieName 设置Cookie名称
// 参数:
//   - cookieName: Cookie名称
//
// 返回:
//   - CSRFOption: CSRF选项
func WithCookieName(cookieName string) CSRFOption {
	return func(c *CSRFConfig) {
		c.CookieName = cookieName
	}
}

// WithTimeout 设置超时时间
// 参数:
//   - timeout: 超时时间
//
// 返回:
//   - CSRFOption: CSRF选项
func WithTimeout(timeout time.Duration) CSRFOption {
	return func(c *CSRFConfig) {
		c.Timeout = timeout
	}
}

// WithTokenStore 设置令牌存储
// 参数:
//   - tokenStore: 令牌存储
//
// 返回:
//   - CSRFOption: CSRF选项
func WithTokenStore(tokenStore CSRFTokenStore) CSRFOption {
	return func(c *CSRFConfig) {
		c.TokenStore = tokenStore
	}
}

// WithErrorHandler 设置错误处理器
// 参数:
//   - errorHandler: 错误处理器
//
// 返回:
//   - CSRFOption: CSRF选项
func WithErrorHandler(errorHandler CSRFErrorHandler) CSRFOption {
	return func(c *CSRFConfig) {
		c.ErrorHandler = errorHandler
	}
}

// WithExcludedPaths 设置排除的路径
// 参数:
//   - paths: 路径列表
//
// 返回:
//   - CSRFOption: CSRF选项
func WithExcludedPaths(paths ...string) CSRFOption {
	return func(c *CSRFConfig) {
		c.ExcludedPaths = paths
	}
}

// WithAllowedOrigins 设置允许的来源
// 参数:
//   - origins: 来源列表
//
// 返回:
//   - CSRFOption: CSRF选项
func WithAllowedOrigins(origins ...string) CSRFOption {
	return func(c *CSRFConfig) {
		c.AllowedOrigins = origins
	}
}

// WithSafeMethods 设置安全方法
// 参数:
//   - methods: 方法列表
//
// 返回:
//   - CSRFOption: CSRF选项
func WithSafeMethods(methods ...string) CSRFOption {
	return func(c *CSRFConfig) {
		c.SafeMethods = methods
	}
}

// GenerateToken 生成CSRF令牌
// 参数:
//   - ctx: 上下文
//   - sessionID: 会话ID
//
// 返回:
//   - string: 令牌值
//   - error: 错误信息
func (c *CSRF) GenerateToken(ctx context.Context, sessionID string) (string, error) {
	// 生成随机令牌
	tokenBytes := make([]byte, c.config.TokenLength)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL,
			fmt.Sprintf("生成随机令牌失败: %v", err)).
			WithCause(err)
	}
	tokenValue := base64.URLEncoding.EncodeToString(tokenBytes)

	// 创建令牌
	token := CSRFToken{
		Value:     tokenValue,
		Created:   time.Now(),
		Expires:   time.Now().Add(c.config.Timeout),
		SessionID: sessionID,
		Used:      false,
	}

	// 保存令牌
	if err := c.tokenStore.SaveToken(ctx, token); err != nil {
		return "", errors.NewSystemError(CODE_CSRF_STORAGE_ERROR,
			fmt.Sprintf("保存CSRF令牌失败: %v", err)).
			WithMetadata("error_code", CSRF_ERROR_STORAGE_ERROR).
			WithCause(err)
	}

	return tokenValue, nil
}

// ValidateToken 验证CSRF令牌
// 参数:
//   - ctx: 上下文
//   - tokenValue: 令牌值
//   - sessionID: 会话ID
//
// 返回:
//   - error: 错误信息
func (c *CSRF) ValidateToken(ctx context.Context, tokenValue, sessionID string) error {
	if tokenValue == "" {
		return errors.NewAuthError(CODE_CSRF_TOKEN_NOT_FOUND, "CSRF令牌未提供").
			WithMetadata("error_code", CSRF_ERROR_TOKEN_NOT_FOUND).
			WithHttpStatus(http.StatusForbidden)
	}

	// 获取令牌
	token, err := c.tokenStore.GetToken(ctx, tokenValue, sessionID)
	if err != nil {
		// 如果是自定义错误，直接返回
		if _, ok := err.(*errors.CustomError); ok {
			return err
		}
		// 否则包装错误
		return errors.NewAuthError(CODE_CSRF_TOKEN_INVALID, fmt.Sprintf("验证CSRF令牌失败: %v", err)).
			WithMetadata("error_code", CSRF_ERROR_INVALID_TOKEN).
			WithCause(err).
			WithHttpStatus(http.StatusForbidden)
	}

	// 检查过期
	if token.Expires.Before(time.Now()) {
		return errors.NewAuthError(CODE_CSRF_TOKEN_EXPIRED, "CSRF令牌已过期").
			WithMetadata("error_code", CSRF_ERROR_TOKEN_EXPIRED).
			WithHttpStatus(http.StatusForbidden)
	}

	// 检查是否已使用
	if token.Used {
		return errors.NewAuthError(CODE_CSRF_TOKEN_USED, "CSRF令牌已使用").
			WithMetadata("error_code", CSRF_ERROR_TOKEN_USED).
			WithHttpStatus(http.StatusForbidden)
	}

	// 标记为已使用
	if err := c.tokenStore.InvalidateToken(ctx, tokenValue, sessionID); err != nil {
		return errors.NewSystemError(CODE_CSRF_STORAGE_ERROR, fmt.Sprintf("标记CSRF令牌为已使用失败: %v", err)).
			WithMetadata("error_code", CSRF_ERROR_STORAGE_ERROR).
			WithCause(err)
	}

	return nil
}

// Middleware CSRF中间件
// 参数:
//   - next: 下一个处理器
//
// 返回:
//   - http.Handler: HTTP处理器
func (c *CSRF) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 如果未启用，直接通过
		if !c.config.Enabled {
			next.ServeHTTP(w, r)
			return
		}

		// 检查是否是安全方法
		if c.IsSafeMethod(r.Method) {
			next.ServeHTTP(w, r)
			return
		}

		// 检查是否是排除的路径
		if c.IsExcludedPath(r.URL.Path) {
			next.ServeHTTP(w, r)
			return
		}

		// 获取会话ID
		sessionID := c.GetSessionID(r)
		if sessionID == "" {
			c.config.ErrorHandler.HandleError(w, r,
				errors.NewAuthError(CODE_CSRF_SESSION_NOT_FOUND, "会话未找到").
					WithMetadata("error_code", CSRF_ERROR_SESSION_NOT_FOUND).
					WithHttpStatus(http.StatusUnauthorized))
			return
		}

		// 获取令牌
		tokenValue := c.GetTokenFromRequest(r)
		if tokenValue == "" {
			c.config.ErrorHandler.HandleError(w, r,
				errors.NewAuthError(CODE_CSRF_TOKEN_NOT_FOUND, "CSRF令牌未找到").
					WithMetadata("error_code", CSRF_ERROR_TOKEN_NOT_FOUND).
					WithHttpStatus(http.StatusForbidden))
			return
		}

		// 验证令牌
		if err := c.ValidateToken(r.Context(), tokenValue, sessionID); err != nil {
			c.config.ErrorHandler.HandleError(w, r, err)
			return
		}

		// 生成新令牌
		newToken, err := c.GenerateToken(r.Context(), sessionID)
		if err != nil {
			c.config.ErrorHandler.HandleError(w, r,
				errors.NewSystemError(CODE_CSRF_STORAGE_ERROR, "生成CSRF令牌失败").
					WithMetadata("error_code", CSRF_ERROR_STORAGE_ERROR).
					WithCause(err))
			return
		}

		// 设置令牌Cookie
		c.SetTokenCookie(w, newToken)

		// 继续处理请求
		next.ServeHTTP(w, r)
	})
}

// GetTokenFromRequest 从请求中获取令牌
// 参数:
//   - r: HTTP请求
//
// 返回:
//   - string: 令牌值
func (c *CSRF) GetTokenFromRequest(r *http.Request) string {
	// 从头中获取
	token := r.Header.Get(c.config.HeaderName)
	if token != "" {
		return token
	}

	// 从表单中获取
	token = r.FormValue(c.config.ParamName)
	if token != "" {
		return token
	}

	// 从Cookie中获取
	cookie, err := r.Cookie(c.config.CookieName)
	if err == nil && cookie.Value != "" {
		return cookie.Value
	}

	return ""
}

// SetTokenCookie 设置令牌Cookie
// 参数:
//   - w: 响应写入器
//   - token: 令牌值
func (c *CSRF) SetTokenCookie(w http.ResponseWriter, token string) {
	cookie := &http.Cookie{
		Name:     c.config.CookieName,
		Value:    token,
		Path:     c.config.CookiePath,
		Domain:   c.config.CookieDomain,
		Secure:   c.config.CookieSecure,
		HttpOnly: c.config.CookieHTTPOnly,
		Expires:  time.Now().Add(c.config.Timeout),
	}

	if c.config.CookieSameSite != "" {
		switch strings.ToLower(c.config.CookieSameSite) {
		case "strict":
			cookie.SameSite = http.SameSiteStrictMode
		case "lax":
			cookie.SameSite = http.SameSiteLaxMode
		case "none":
			cookie.SameSite = http.SameSiteNoneMode
		}
	}

	http.SetCookie(w, cookie)
}

// IsSafeMethod 检查是否是安全方法
// 参数:
//   - method: HTTP方法
//
// 返回:
//   - bool: 是否是安全方法
func (c *CSRF) IsSafeMethod(method string) bool {
	method = strings.ToUpper(method)
	for _, safeMethod := range c.config.SafeMethods {
		if method == strings.ToUpper(safeMethod) {
			return true
		}
	}
	return false
}

// IsExcludedPath 检查是否是排除的路径
// 参数:
//   - path: 路径
//
// 返回:
//   - bool: 是否是排除的路径
func (c *CSRF) IsExcludedPath(path string) bool {
	for _, excludedPath := range c.config.ExcludedPaths {
		if strings.HasPrefix(path, excludedPath) {
			return true
		}
	}
	return false
}

// GetSessionID 获取会话ID
// 参数:
//   - r: HTTP请求
//
// 返回:
//   - string: 会话ID
func (c *CSRF) GetSessionID(r *http.Request) string {
	// 实际项目中，这里应该从会话中获取会话ID
	// 这里简化为从Cookie中获取
	cookie, err := r.Cookie("session_id")
	if err != nil {
		return ""
	}
	return cookie.Value
}

// 包级函数，方便直接使用

// GenerateToken 生成CSRF令牌
// 参数:
//   - ctx: 上下文
//   - sessionID: 会话ID
//
// 返回:
//   - string: 令牌值
//   - error: 错误信息
func GenerateToken(ctx context.Context, sessionID string) (string, error) {
	return GetCSRF().GenerateToken(ctx, sessionID)
}

// ValidateToken 验证CSRF令牌
// 参数:
//   - ctx: 上下文
//   - tokenValue: 令牌值
//   - sessionID: 会话ID
//
// 返回:
//   - error: 错误信息
func ValidateToken(ctx context.Context, tokenValue, sessionID string) error {
	return GetCSRF().ValidateToken(ctx, tokenValue, sessionID)
}

// Middleware 获取CSRF中间件
// 返回:
//   - func(http.Handler) http.Handler: 中间件函数
func Middleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return GetCSRF().Middleware(next)
	}
}
