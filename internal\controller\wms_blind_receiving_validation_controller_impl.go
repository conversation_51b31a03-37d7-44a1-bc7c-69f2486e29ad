package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
)

// WmsBlindReceivingValidationController 盲收验证记录控制器接口
type WmsBlindReceivingValidationController interface {
	BaseController

	// 基础CRUD操作
	Create(ctx iris.Context)
	GetByID(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)

	// 查询操作
	GetByConfigID(ctx iris.Context)
	GetByWarehouseAndCustomer(ctx iris.Context)
	GetPendingValidations(ctx iris.Context)
	GetValidationHistory(ctx iris.Context)

	// 业务操作
	ProcessValidation(ctx iris.Context)
	ValidateBlindReceiving(ctx iris.Context)

	// 统计操作
	GetValidationStats(ctx iris.Context)
}

// wmsBlindReceivingValidationControllerImpl 盲收验证记录控制器实现
type wmsBlindReceivingValidationControllerImpl struct {
	BaseControllerImpl
	validationService service.WmsBlindReceivingValidationService
}

// NewWmsBlindReceivingValidationController 创建盲收验证记录控制器实例
func NewWmsBlindReceivingValidationController(cm *ControllerManager) WmsBlindReceivingValidationController {
	return &wmsBlindReceivingValidationControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		validationService:  cm.GetServiceManager().GetWmsBlindReceivingValidationService(),
	}
}

// ValidateBlindReceiving 执行盲收验证
func (ctrl *wmsBlindReceivingValidationControllerImpl) ValidateBlindReceiving(ctx iris.Context) {
	const opName = "ValidateBlindReceiving"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_validation")

	var req dto.BlindReceivingValidationReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.validationService.ValidateBlindReceiving(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// Create 创建验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsBlindReceivingValidation"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_validation")

	var req dto.WmsBlindReceivingValidationCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.validationService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetByID 根据ID获取验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsBlindReceivingValidationByID"

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	result, err := ctrl.validationService.GetByID(ctx.Request().Context(), id)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// Update 更新验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsBlindReceivingValidation"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_validation")

	var req dto.WmsBlindReceivingValidationUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.validationService.Update(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// Delete 删除验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsBlindReceivingValidation"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_validation")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.validationService.Delete(ctx.Request().Context(), id)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, nil)
}

// GetByConfigID 根据配置ID获取验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) GetByConfigID(ctx iris.Context) {
	const opName = "GetWmsBlindReceivingValidationsByConfigID"

	configID, err := ctrl.GetPathUintParamWithError(ctx, "configId")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	limitStr := ctx.URLParamDefault("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	result, err := ctrl.validationService.GetByConfigID(ctx.Request().Context(), uint(configID), limit)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetByWarehouseAndCustomer 根据仓库和客户获取验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) GetByWarehouseAndCustomer(ctx iris.Context) {
	const opName = "GetWmsBlindReceivingValidationsByWarehouseAndCustomer"

	warehouseIDStr := ctx.URLParam("warehouseId")
	customerIDStr := ctx.URLParam("customerId")

	if warehouseIDStr == "" || customerIDStr == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "仓库ID和客户ID不能为空")
		ctrl.FailWithError(ctx, paramErr)
		return
	}

	warehouseID, err := strconv.ParseUint(warehouseIDStr, 10, 64)
	if err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的仓库ID参数").WithCause(err)
		ctrl.FailWithError(ctx, paramErr)
		return
	}

	customerID, err := strconv.ParseUint(customerIDStr, 10, 64)
	if err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的客户ID参数").WithCause(err)
		ctrl.FailWithError(ctx, paramErr)
		return
	}

	limitStr := ctx.URLParamDefault("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	result, err := ctrl.validationService.GetByWarehouseAndCustomer(ctx.Request().Context(), uint(warehouseID), uint(customerID), limit)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetPendingValidations 获取待处理的验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) GetPendingValidations(ctx iris.Context) {
	const opName = "GetPendingWmsBlindReceivingValidations"

	warehouseIDStr := ctx.URLParam("warehouseId")
	if warehouseIDStr == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "仓库ID不能为空")
		ctrl.FailWithError(ctx, paramErr)
		return
	}

	warehouseID, err := strconv.ParseUint(warehouseIDStr, 10, 64)
	if err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的仓库ID参数").WithCause(err)
		ctrl.FailWithError(ctx, paramErr)
		return
	}

	result, err := ctrl.validationService.GetPendingValidations(ctx.Request().Context(), uint(warehouseID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetValidationHistory 获取验证历史记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) GetValidationHistory(ctx iris.Context) {
	const opName = "GetWmsBlindReceivingValidationHistory"

	var req dto.WmsBlindReceivingValidationHistoryReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.validationService.GetValidationHistory(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// ProcessValidation 处理验证记录
func (ctrl *wmsBlindReceivingValidationControllerImpl) ProcessValidation(ctx iris.Context) {
	const opName = "ProcessWmsBlindReceivingValidation"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_validation")

	var req dto.WmsBlindReceivingValidationProcessReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.validationService.ProcessValidation(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.SuccessWithMessage(ctx, "验证记录处理成功", nil)
}

// GetValidationStats 获取验证统计信息
func (ctrl *wmsBlindReceivingValidationControllerImpl) GetValidationStats(ctx iris.Context) {
	const opName = "GetWmsBlindReceivingValidationStats"

	var req dto.WmsBlindReceivingValidationStatsReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.validationService.GetValidationStats(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}
