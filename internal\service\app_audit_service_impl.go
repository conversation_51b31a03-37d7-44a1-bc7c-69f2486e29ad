package service

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/datatypes"

	// middleware "backend/internal/middleware" // Or model if AuditEvent moved (Plan has this, but AuditEvent is not yet defined in middleware package)
	"backend/internal/model/dto"
	"backend/internal/model/entity" // For converting from entity
	"backend/internal/model/vo"     // For AuditLogVO
	"backend/internal/repository"
	"backend/pkg/logger"
	"backend/pkg/response" // For response.PageResult
	// "backend/pkg/util" // For GetTraceIDFromStdContextDefault if needed in RecordEvent (not directly used in this snippet from plan)
	// "backend/pkg/errors" // For apperrors if needed for wrapping
)

// AuditEventPlaceholder 是一个临时的占位符，直到 middleware.AuditEvent 被定义。
// 计划书中的 AuditLogService.RecordEvent 依赖 middleware.AuditEvent。
// 我们将在后续步骤中定义 middleware.AuditEvent。
// TODO: Replace with actual middleware.AuditEvent once defined in Step 5 of the plan.
type AuditEventPlaceholder struct {
	UserID       uint64 // Corresponds to entity.AuditLog.UserID
	Username     string
	Action       string
	ResourceType string
	ResourceID   string
	Time         time.Time // Corresponds to entity.AuditLog.Timestamp -  确保此字段存在且类型正确
	IP           string    // Corresponds to entity.AuditLog.ClientIP
	UserAgent    string
	RequestURI   string
	Method       string
	StatusCode   int
	Duration     int64 // Corresponds to entity.AuditLog.DurationMs
	TraceID      string
	Status       string
	OldValue     string // Plan suggests AuditEvent might have string, then service converts to datatypes.JSON
	NewValue     string
	Details      interface{} // Plan suggests interface{}, service marshals to JSON string or uses fmt.Sprintf
}

// AuditLogService 定义了审计日志服务的接口
type AuditLogService interface {
	RecordEvent(eventData *AuditEventPlaceholder) error // 使用临时占位符 AuditEventPlaceholder
	// ListAuditLogs now returns a PageResult containing AuditLogVOs
	ListAuditLogs(query dto.AuditLogQueryDTO) (*response.PageResult, error)
}

// auditLogServiceImpl 是 AuditLogService 的实现
type auditLogServiceImpl struct {
	repo   repository.AuditLogRepository
	logger logger.Logger
}

// NewAuditLogService 是 AuditLogService 的构造函数
// 注意：计划书中提到 "Renamed"，但通常构造函数返回接口类型，命名为 New<InterfaceName>
func NewAuditLogService(repo repository.AuditLogRepository, log logger.Logger) AuditLogService {
	return &auditLogServiceImpl{repo: repo, logger: log}
}

// convertToAuditLogVO 将 entity.AuditLog 转换为 vo.AuditLogVO
func (s *auditLogServiceImpl) convertToAuditLogVO(logEntity *entity.AuditLog) *vo.AuditLogVO {
	if logEntity == nil {
		return nil
	}
	return &vo.AuditLogVO{
		ID:           logEntity.ID,
		UserID:       logEntity.UserID,
		Username:     logEntity.Username,
		Action:       logEntity.Action,
		ResourceType: logEntity.ResourceType,
		ResourceID:   logEntity.ResourceID,
		Timestamp:    logEntity.Timestamp,
		ClientIP:     logEntity.ClientIP,
		UserAgent:    logEntity.UserAgent,
		RequestURI:   logEntity.RequestURI,
		Method:       logEntity.Method,
		StatusCode:   logEntity.StatusCode,
		DurationMs:   logEntity.DurationMs,
		TraceID:      logEntity.TraceID,
		Status:       logEntity.Status,
		OldValue:     logEntity.OldValue, // Assuming datatypes.JSON is fine for VO
		NewValue:     logEntity.NewValue,
		Details:      logEntity.Details,
		CreatedAt:    logEntity.CreatedAt,
	}
}

// convertToAuditLogVOList 将 []*entity.AuditLog 转换为 []*vo.AuditLogVO
func (s *auditLogServiceImpl) convertToAuditLogVOList(entityList []*entity.AuditLog) []*vo.AuditLogVO {
	voList := make([]*vo.AuditLogVO, 0, len(entityList))
	for _, entity := range entityList {
		voList = append(voList, s.convertToAuditLogVO(entity))
	}
	return voList
}

// RecordEvent 记录一个审计事件
func (s *auditLogServiceImpl) RecordEvent(eventData *AuditEventPlaceholder) error { // 使用临时占位符
	logEntry := entity.AuditLog{ // DB model
		UserID:       eventData.UserID,
		Username:     eventData.Username,
		Action:       eventData.Action,
		ResourceType: eventData.ResourceType,
		ResourceID:   eventData.ResourceID,
		Timestamp:    eventData.Time, // 使用 eventData.Time
		ClientIP:     eventData.IP,
		UserAgent:    eventData.UserAgent,
		RequestURI:   eventData.RequestURI,
		Method:       eventData.Method,
		StatusCode:   eventData.StatusCode,
		DurationMs:   eventData.Duration,
		TraceID:      eventData.TraceID,
		Status:       eventData.Status,
	}

	if eventData.OldValue != "" {
		logEntry.OldValue = datatypes.JSON(eventData.OldValue)
	}
	if eventData.NewValue != "" {
		logEntry.NewValue = datatypes.JSON(eventData.NewValue)
	}

	if eventData.Details != nil {
		detailsBytes, err := json.Marshal(eventData.Details)
		if err == nil {
			logEntry.Details = string(detailsBytes)
		} else {
			s.logger.Warnf("Failed to marshal audit event details: %v. Falling back to fmt.Sprintf", err, logger.WithField("traceId", eventData.TraceID))
			logEntry.Details = fmt.Sprintf("%+v", eventData.Details)
		}
	}

	err := s.repo.Create(&logEntry)
	if err != nil {
		s.logger.Errorf("Failed to record audit event to DB: %v", err, logger.WithField("traceId", eventData.TraceID))
		return err
	}
	s.logger.Debugf("Audit event recorded: UserID=%d, Action=%s, Resource=%s/%s",
		logEntry.UserID, logEntry.Action, logEntry.ResourceType, logEntry.ResourceID,
		logger.WithField("traceId", eventData.TraceID))
	return nil
}

// ListAuditLogs 列出审计日志，支持分页和筛选
func (s *auditLogServiceImpl) ListAuditLogs(query dto.AuditLogQueryDTO) (*response.PageResult, error) {
	pageResultEntity, err := s.repo.Find(query)
	if err != nil {
		s.logger.Error("Error fetching audit logs from repository", logger.WithError(err))
		return nil, err
	}

	entityList, ok := pageResultEntity.List.([]*entity.AuditLog)
	if !ok {
		if pageResultEntity.List == nil && pageResultEntity.Total == 0 {
			entityList = []*entity.AuditLog{}
		} else {
			s.logger.Error("Repository Find returned PageResult.List with unexpected type", logger.WithField("type", fmt.Sprintf("%T", pageResultEntity.List)))
			return nil, fmt.Errorf("unexpected list type from repository: %T", pageResultEntity.List)
		}
	}

	voList := s.convertToAuditLogVOList(entityList)

	return &response.PageResult{
		List:     voList,
		Total:    pageResultEntity.Total,
		PageNum:  pageResultEntity.PageNum,
		PageSize: pageResultEntity.PageSize,
		Pages:    pageResultEntity.Pages,
		Sort:     pageResultEntity.Sort,
	}, nil
}
