package entity

import "time"

// SysCodeRule 编码规则实体
type SysCodeRule struct {
	AccountBookEntity
	RuleCode        string     `gorm:"column:rule_code;type:varchar(50);not null;index;comment:规则编码" json:"ruleCode"`
	RuleName        string     `gorm:"column:rule_name;type:varchar(100);not null;comment:规则名称" json:"ruleName"`
	BusinessType    string     `gorm:"column:business_type;type:varchar(50);not null;index;comment:业务类型" json:"businessType"`
	CodeFormat      string     `gorm:"column:code_format;type:varchar(200);not null;comment:编码格式模板" json:"codeFormat"`
	Separator       *string    `gorm:"column:separator;type:varchar(10);comment:分隔符" json:"separator"`
	ResetFrequency  string     `gorm:"column:reset_frequency;type:varchar(20);not null;default:'NEVER';comment:重置频率" json:"resetFrequency"`
	SequenceLength  *int       `gorm:"column:sequence_length;default:4;comment:序号长度" json:"sequenceLength"`
	SequenceStart   *int       `gorm:"column:sequence_start;default:1;comment:序号起始值" json:"sequenceStart"`
	CurrentSequence int64      `gorm:"column:current_sequence;default:0;comment:当前序号" json:"currentSequence"`
	LastResetTime   *time.Time `gorm:"column:last_reset_time;comment:上次重置时间" json:"lastResetTime"`
	Status          string     `gorm:"column:status;type:varchar(20);not null;default:'ACTIVE';index;comment:状态" json:"status"`
	IsDefault       bool       `gorm:"column:is_default;default:false;comment:是否默认规则" json:"isDefault"`
	Remark          *string    `gorm:"column:remark;type:text;comment:备注" json:"remark"`
}

// TableName 指定数据库表名
func (SysCodeRule) TableName() string {
	return "sys_code_rule"
}

// SysCodeFormatComponent 编码格式组件实体
type SysCodeFormatComponent struct {
	ID            uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	ComponentCode string    `gorm:"column:component_code;type:varchar(50);not null;uniqueIndex;comment:组件编码" json:"componentCode"`
	ComponentName string    `gorm:"column:component_name;type:varchar(100);not null;comment:组件名称" json:"componentName"`
	ComponentType string    `gorm:"column:component_type;type:varchar(30);not null;comment:组件类型" json:"componentType"`
	FormatPattern *string   `gorm:"column:format_pattern;type:varchar(100);comment:格式模式" json:"formatPattern"`
	Description   *string   `gorm:"column:description;type:text;comment:描述说明" json:"description"`
	ExampleValue  *string   `gorm:"column:example_value;type:varchar(100);comment:示例值" json:"exampleValue"`
	Status        string    `gorm:"column:status;type:varchar(20);not null;default:'ACTIVE';comment:状态" json:"status"`
	CreatedAt     time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt     time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updatedAt"`
}

// TableName 指定数据库表名
func (SysCodeFormatComponent) TableName() string {
	return "sys_code_format_component"
}

// SysCodeGenerationLog 编码生成历史实体
type SysCodeGenerationLog struct {
	ID                uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID          uint      `gorm:"column:tenant_id;not null;index;comment:租户ID" json:"tenantId"`
	AccountBookID     uint      `gorm:"column:account_book_id;not null;index;comment:账套ID" json:"accountBookId"`
	RuleID            uint      `gorm:"column:rule_id;not null;index;comment:规则ID" json:"ruleId"`
	BusinessType      string    `gorm:"column:business_type;type:varchar(50);not null;index;comment:业务类型" json:"businessType"`
	BusinessID        *uint     `gorm:"column:business_id;index;comment:业务记录ID" json:"businessId"`
	GeneratedCode     string    `gorm:"column:generated_code;type:varchar(100);not null;index;comment:生成的编码" json:"generatedCode"`
	SequenceNumber    int64     `gorm:"column:sequence_number;not null;comment:使用的序号" json:"sequenceNumber"`
	GenerationContext *string   `gorm:"column:generation_context;type:text;comment:生成时的上下文数据" json:"generationContext"`
	CreatedBy         uint      `gorm:"column:created_by;not null;comment:创建人" json:"createdBy"`
	CreatedAt         time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"createdAt"`
}

// TableName 指定数据库表名
func (SysCodeGenerationLog) TableName() string {
	return "sys_code_generation_log"
}

// 编码规则状态常量
const (
	CodeRuleStatusActive   = "ACTIVE"
	CodeRuleStatusInactive = "INACTIVE"
)

// 重置频率常量
const (
	ResetFrequencyNever   = "NEVER"
	ResetFrequencyDaily   = "DAILY"
	ResetFrequencyMonthly = "MONTHLY"
	ResetFrequencyYearly  = "YEARLY"
)

// 业务类型常量
const (
	BusinessTypeCustomer = "CUSTOMER"
	BusinessTypeSupplier = "SUPPLIER"
	BusinessTypeMaterial = "MATERIAL"
	BusinessTypeLocation = "LOCATION"

	// WMS入库流程业务类型
	BusinessTypeInboundNotification = "INBOUND_NOTIFICATION"
	BusinessTypeReceivingRecord     = "RECEIVING_RECORD"
	BusinessTypePutawayTask         = "PUTAWAY_TASK"
	BusinessTypeBatchNumber         = "BATCH_NUMBER"

	// WMS出库流程业务类型
	BusinessTypeOutboundNotification = "OUTBOUND_NOTIFICATION" // 出库通知单
	BusinessTypePickingTask          = "PICKING_TASK"          // 拣货任务
	BusinessTypeShipment             = "SHIPMENT"              // 发运单
)

// 组件类型常量
const (
	ComponentTypeFixed    = "FIXED"
	ComponentTypeDate     = "DATE"
	ComponentTypeSequence = "SEQUENCE"
	ComponentTypeField    = "FIELD"
)
