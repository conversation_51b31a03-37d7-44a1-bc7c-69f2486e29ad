package entity

// MtlItemPackageUnit 对应数据库中的 mtl_item_package_units 表
// 存储物料的不同包装单位及其换算关系
type MtlItemPackageUnit struct {
	// 基础实体字段
	AccountBookEntity
	ItemID           uint    `gorm:"column:item_id;not null;index;comment:关联物料ID" json:"itemId"`                               // 关联物料ID - 移除GORM唯一索引，使用数据库部分唯一索引
	UnitName         string  `gorm:"column:unit_name;type:varchar(50);not null;index;comment:包装单位名称" json:"unitName"`          // 包装单位名称 - 移除GORM唯一索引，使用数据库部分唯一索引
	ConversionFactor float64 `gorm:"column:conversion_factor;type:numeric(12,6);not null;comment:换算率" json:"conversionFactor"` // 换算率: 1 此包装单位 = X 基本单位

	// 可选的包装单位物理属性
	PackageWeightKg *float64 `gorm:"column:package_weight_kg;type:numeric(10,3);comment:包装重量(kg)" json:"packageWeightKg,omitempty"`
	PackageVolumeM3 *float64 `gorm:"column:package_volume_m3;type:numeric(10,6);comment:包装体积(m³)" json:"packageVolumeM3,omitempty"`
	PackageLengthM  *float64 `gorm:"column:package_length_m;type:numeric(10,3);comment:包装长度(m)" json:"packageLengthM,omitempty"`
	PackageWidthM   *float64 `gorm:"column:package_width_m;type:numeric(10,3);comment:包装宽度(m)" json:"packageWidthM,omitempty"`
	PackageHeightM  *float64 `gorm:"column:package_height_m;type:numeric(10,3);comment:包装高度(m)" json:"packageHeightM,omitempty"`
}

// TableName 指定数据库表名方法
func (MtlItemPackageUnit) TableName() string {
	return "mtl_item_package_unit"
}
