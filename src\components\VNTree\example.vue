<template>
  <div class="tree-examples-page">
    <h2>VNTree 树形控件组件示例</h2>

    <el-row :gutter="20">
      <!-- Basic Usage & Filtering -->
      <el-col :span="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>基础用法与过滤</span>
            </div>
          </template>
          <el-input
            v-model="filterText"
            placeholder="输入关键字进行过滤"
            clearable
            style="margin-bottom: 15px;"
          />
          <VNTree
            ref="basicTreeRef"
            :data="basicData"
            :filter-text="filterText"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>

      <!-- Checkbox & Get Checked Nodes -->
      <el-col :span="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>复选框与获取选中</span>
            </div>
          </template>
          <VNTree
            ref="checkboxTreeRef"
            :data="basicData"
            show-checkbox
            node-key="id" 
            :default-expanded-keys="[2, 3]"
            :default-checked-keys="[5]"
            @check="handleCheck"
          />
          <div style="margin-top: 15px;">
             <el-button size="small" @click="getCheckedNodes">获取选中节点</el-button>
             <el-button size="small" @click="getCheckedKeys">获取选中 Key</el-button>
             <el-button size="small" @click="setCheckedKeys">设置选中 Key (1, 5)</el-button>
             <el-button size="small" @click="resetChecked">清空选中</el-button>
          </div>
           <pre class="log-pre">选中Keys: {{ checkedKeysLog }}</pre>
           <pre class="log-pre">选中Nodes: {{ checkedNodesLog }}</pre>
        </el-card>
      </el-col>

      <!-- Lazy Loading -->
      <el-col :span="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>懒加载</span>
            </div>
          </template>
          <VNTree
            ref="lazyTreeRef"
            node-key="id"
            :tree-props="{ isLeaf: 'leaf' }"
            :lazyLoad="loadLazyNode" 
            show-checkbox
          />
        </el-card>
      </el-col>

    </el-row>
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- Custom Content Slot -->
        <el-col :span="8">
           <el-card class="box-card">
            <template #header>
                <div class="card-header">
                <span>自定义节点内容</span>
                </div>
            </template>
            <VNTree :data="basicData" default-expand-all>
                <template #default="{ node, data }">
                     <span class="custom-tree-node-example">
                        <el-icon v-if="node.isLeaf"><Document /></el-icon>
                        <el-icon v-else><FolderOpened /></el-icon>
                        <span>{{ node.label }}</span>
                        <el-tag size="small" type="info" v-if="data.tag">{{ data.tag }}</el-tag>
                    </span>
                </template>
            </VNTree>
           </el-card>
        </el-col>
    </el-row>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNTree from './index.vue';
import type { TreeNode } from './types';
import { ElCard, ElInput, ElButton, ElMessage, ElRow, ElCol, ElIcon, ElTag } from 'element-plus';
import { Document, FolderOpened } from '@element-plus/icons-vue';
import type { Node } from 'element-plus/es/components/tree/src/tree.type';

const filterText = ref('');
const basicTreeRef = ref<InstanceType<typeof VNTree> | null>(null);
const checkboxTreeRef = ref<InstanceType<typeof VNTree> | null>(null);
const lazyTreeRef = ref<InstanceType<typeof VNTree> | null>(null);

const checkedKeysLog = ref<string>('[]');
const checkedNodesLog = ref<string>('[]');

// --- Basic Data ---
const basicData = ref<TreeNode[]>([
  {
    id: 1,
    label: 'Level one 1',
    tag: 'Root',
    children: [
      {
        id: 4,
        label: 'Level two 1-1',
        tag: 'Branch',
        children: [
          { id: 9, label: 'Level three 1-1-1', tag: 'Leaf' },
          { id: 10, label: 'Level three 1-1-2', tag: 'Leaf' },
        ],
      },
    ],
  },
  {
    id: 2,
    label: 'Level one 2',
    tag: 'Root',
    children: [
      { id: 5, label: 'Level two 2-1', tag: 'Leaf' },
      { id: 6, label: 'Level two 2-2', tag: 'Leaf' },
    ],
  },
  {
    id: 3,
    label: 'Level one 3',
    tag: 'Root',
    children: [
      { id: 7, label: 'Level two 3-1', tag: 'Leaf' },
      { id: 8, label: 'Level two 3-2', disabled: true, tag: 'Disabled' },
    ],
  },
]);

// --- Event Handlers ---
const handleNodeClick = (data: TreeNode, node: any) => {
  console.log('Node clicked:', data, node);
  ElMessage.info(`Clicked: ${data.label}`);
};

const handleCheck = (data: TreeNode, checkedInfo: any) => {
  console.log('Check event:', data, checkedInfo);
  checkedKeysLog.value = JSON.stringify(checkedInfo.checkedKeys);
};

// --- Checkbox Tree Methods ---
const getCheckedNodes = () => {
  const nodes = checkboxTreeRef.value?.getTreeInstance()?.getCheckedNodes(false, false); // (leafOnly, includeHalfChecked)
  checkedNodesLog.value = JSON.stringify(nodes?.map(n => ({ id: n.id, label: n.label })) || [], null, 2);
  ElMessage.success('已获取选中节点 (见日志和下方显示)');
  console.log('Checked Nodes:', nodes);
};

const getCheckedKeys = () => {
  const keys = checkboxTreeRef.value?.getTreeInstance()?.getCheckedKeys(false); // (leafOnly)
  checkedKeysLog.value = JSON.stringify(keys || []);
  ElMessage.success('已获取选中 Keys (见日志和下方显示)');
   console.log('Checked Keys:', keys);
};

const setCheckedKeys = () => {
  checkboxTreeRef.value?.getTreeInstance()?.setCheckedKeys([1, 5], false); // (keys, leafOnly)
  getCheckedKeys(); // Update log
  ElMessage.info('已设置选中 Keys: [1, 5]');
};

const resetChecked = () => {
  checkboxTreeRef.value?.getTreeInstance()?.setCheckedKeys([], false);
  getCheckedKeys(); // Update log
  getCheckedNodes(); // Update log
  ElMessage.warning('已清空所有选中');
};

// --- Lazy Loading Logic ---
let lazyNodeCount = 0;
const loadLazyNode = (node: Node, resolve: (data: TreeNode[]) => void) => {
  if (node.level === 0) {
    // Load root nodes
     setTimeout(() => {
        resolve([
          { id: 'lazy-1', label: 'Lazy Node 1' },
          { id: 'lazy-2', label: 'Lazy Node 2', isLeaf: true }, // Mark as leaf
        ]);
     }, 500);
     return;
  }
  if (node.level > 2) return resolve([]); // Limit depth for example

  setTimeout(() => {
    const children: TreeNode[] = [];
    for (let i = 0; i < 3; i++) {
        lazyNodeCount++;
        children.push({
            id: `lazy-${node.data.id}-${lazyNodeCount}`,
            label: `Child ${node.data.label}-${lazyNodeCount}`,
            leaf: node.level >= 2 // Mark nodes at level 2 as leaf
        });
    }
    resolve(children);
  }, 500);
};

</script>

<style scoped>
.tree-examples-page {
  padding: 20px;
}

.box-card {
  min-height: 300px; /* Ensure cards have some height */
  display: flex; /* Use flexbox for better layout */
  flex-direction: column;
}

.box-card :deep(.el-card__body) {
    flex-grow: 1; /* Allow body to grow */
    display: flex;
    flex-direction: column;
}

.vn-tree-wrapper {
    flex-grow: 1; /* Allow tree to fill space */
    overflow: auto; /* Add scroll if tree is too tall */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.log-pre {
    margin-top: 10px;
    padding: 8px;
    background-color: #f5f7fa;
    border: 1px solid #e0e4e9;
    border-radius: 4px;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 100px;
    overflow-y: auto;
}

.custom-tree-node-example {
  flex-grow: 1; 
  display: flex;
  align-items: center;
  font-size: 14px;
}

.custom-tree-node-example .el-icon {
    margin-right: 5px;
    color: #888;
}

.custom-tree-node-example span {
    margin-right: 8px;
}

.custom-tree-node-example .el-tag {
    margin-left: auto;
}
</style> 