<template>
  <el-dialog
    v-model="dialogVisible"
    title="确认签收"
    width="60%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="delivery-dialog-container" v-if="shipmentData">
      <!-- 发运单信息 -->
      <el-card class="shipment-info-card" shadow="never">
        <template #header>
          <span>发运单信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">发运单号:</span>
              <span class="value">{{ shipmentData.shipmentNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">运单号:</span>
              <span class="value">{{ shipmentData.trackingNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">承运商:</span>
              <span class="value">{{ shipmentData.carrierName }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">收货人:</span>
              <span class="value">{{ shipmentData.consigneeName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">收货电话:</span>
              <span class="value">{{ shipmentData.consigneePhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">发运时间:</span>
              <span class="value">{{ shipmentData.actualShipDate }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 签收信息 -->
      <el-card class="delivery-info-card" shadow="never">
        <template #header>
          <span>签收信息</span>
        </template>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="实际签收人" prop="actualConsigneeName">
                <el-input
                  v-model="formData.actualConsigneeName"
                  placeholder="请输入实际签收人姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签收时间" prop="actualDeliveryDate">
                <el-date-picker
                  v-model="formData.actualDeliveryDate"
                  type="datetime"
                  placeholder="请选择签收时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="签收人电话" prop="actualConsigneePhone">
                <el-input
                  v-model="formData.actualConsigneePhone"
                  placeholder="请输入签收人电话"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签收方式" prop="deliveryMethod">
                <el-select
                  v-model="formData.deliveryMethod"
                  placeholder="请选择签收方式"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in deliveryMethodOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="货物状态" prop="goodsCondition">
                <el-select
                  v-model="formData.goodsCondition"
                  placeholder="请选择货物状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in goodsConditionOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="满意度评分" prop="satisfactionRating">
                <el-rate
                  v-model="formData.satisfactionRating"
                  :max="5"
                  show-score
                  text-color="#ff9900"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="签收备注" prop="deliveryRemark">
            <el-input
              v-model="formData.deliveryRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入签收备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 异常情况 -->
      <el-card class="exception-card" shadow="never" v-if="formData.goodsCondition === 'DAMAGED'">
        <template #header>
          <span>异常情况说明</span>
        </template>
        
        <el-form
          :model="exceptionData"
          label-width="120px"
        >
          <el-form-item label="异常类型">
            <el-checkbox-group v-model="exceptionData.exceptionTypes">
              <el-checkbox label="PACKAGE_DAMAGED">包装破损</el-checkbox>
              <el-checkbox label="GOODS_DAMAGED">货物损坏</el-checkbox>
              <el-checkbox label="QUANTITY_SHORT">数量短缺</el-checkbox>
              <el-checkbox label="QUALITY_ISSUE">质量问题</el-checkbox>
              <el-checkbox label="OTHER">其他</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="异常描述">
            <el-input
              v-model="exceptionData.exceptionDescription"
              type="textarea"
              :rows="3"
              placeholder="请详细描述异常情况"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 签收照片 -->
      <el-card class="photo-upload-card" shadow="never">
        <template #header>
          <span>签收照片</span>
        </template>
        
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          list-type="picture-card"
          accept="image/*"
          multiple
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        
        <div class="upload-tip">
          <el-text type="info" size="small">
            建议上传货物签收现场照片，包括包装状态、货物状态等
          </el-text>
        </div>
      </el-card>
      
      <!-- 电子签名 -->
      <el-card class="signature-card" shadow="never">
        <template #header>
          <span>电子签名</span>
        </template>
        
        <div class="signature-container">
          <canvas
            ref="signatureCanvas"
            width="400"
            height="200"
            @mousedown="startDrawing"
            @mousemove="draw"
            @mouseup="stopDrawing"
            @touchstart="startDrawing"
            @touchmove="draw"
            @touchend="stopDrawing"
          ></canvas>
          <div class="signature-actions">
            <el-button size="small" @click="clearSignature">清除签名</el-button>
            <el-button type="primary" size="small" @click="saveSignature">保存签名</el-button>
          </div>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="!canConfirm"
        >
          确认签收
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, nextTick } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElDatePicker, ElSelect, ElOption, ElRate, ElCheckboxGroup, ElCheckbox, ElUpload, ElIcon, ElText } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import { useShipmentStore } from '@/store/wms/shipment'

// 组件接口定义
interface DeliveryConfirmDialogEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<DeliveryConfirmDialogEmits>()

// Store
const shipmentStore = useShipmentStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const shipmentData = ref<any>(null)
const fileList = ref<any[]>([])
const isDrawing = ref(false)
const signatureDataUrl = ref('')

// 表单数据
const formData = reactive({
  actualConsigneeName: '',
  actualConsigneePhone: '',
  actualDeliveryDate: new Date(),
  deliveryMethod: '',
  goodsCondition: 'GOOD',
  satisfactionRating: 5,
  deliveryRemark: ''
})

// 异常数据
const exceptionData = reactive({
  exceptionTypes: [] as string[],
  exceptionDescription: ''
})

// 表单验证规则
const formRules: FormRules = {
  actualConsigneeName: [
    { required: true, message: '请输入实际签收人姓名', trigger: 'blur' }
  ],
  actualConsigneePhone: [
    { required: true, message: '请输入签收人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  actualDeliveryDate: [
    { required: true, message: '请选择签收时间', trigger: 'change' }
  ],
  deliveryMethod: [
    { required: true, message: '请选择签收方式', trigger: 'change' }
  ],
  goodsCondition: [
    { required: true, message: '请选择货物状态', trigger: 'change' }
  ]
}

// 选项数据
const deliveryMethodOptions = [
  { label: '本人签收', value: 'SELF' },
  { label: '代收', value: 'PROXY' },
  { label: '放置指定地点', value: 'DESIGNATED_PLACE' },
  { label: '快递柜', value: 'LOCKER' }
]

const goodsConditionOptions = [
  { label: '完好', value: 'GOOD' },
  { label: '轻微损坏', value: 'MINOR_DAMAGE' },
  { label: '严重损坏', value: 'DAMAGED' },
  { label: '丢失', value: 'LOST' }
]

// 计算属性
const canConfirm = computed(() => {
  return formData.actualConsigneeName && 
         formData.actualConsigneePhone && 
         formData.actualDeliveryDate && 
         formData.deliveryMethod && 
         formData.goodsCondition
})

// 上传配置
const uploadAction = ref('/api/upload/images')
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})

// 表单引用
const formRef = ref<FormInstance>()
const uploadRef = ref()
const signatureCanvas = ref<HTMLCanvasElement>()

// 方法
const loadShipmentData = async (shipmentId: number) => {
  try {
    const data = await shipmentStore.fetchDetail(shipmentId)
    shipmentData.value = data
    
    // 预填充签收人信息
    formData.actualConsigneeName = data.consigneeName
    formData.actualConsigneePhone = data.consigneePhone
  } catch (error) {
    ElMessage.error('加载发运单数据失败')
    console.error(error)
  }
}

const handleOpen = () => {
  nextTick(() => {
    initSignatureCanvas()
  })
}

const handleClose = () => {
  resetData()
}

const resetData = () => {
  shipmentData.value = null
  fileList.value = []
  signatureDataUrl.value = ''
  Object.assign(formData, {
    actualConsigneeName: '',
    actualConsigneePhone: '',
    actualDeliveryDate: new Date(),
    deliveryMethod: '',
    goodsCondition: 'GOOD',
    satisfactionRating: 5,
    deliveryRemark: ''
  })
  Object.assign(exceptionData, {
    exceptionTypes: [],
    exceptionDescription: ''
  })
  formRef.value?.clearValidate()
}

// 签名相关方法
const initSignatureCanvas = () => {
  if (!signatureCanvas.value) return
  
  const canvas = signatureCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  ctx.strokeStyle = '#000000'
  ctx.lineWidth = 2
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  
  // 设置背景色
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
}

const startDrawing = (event: MouseEvent | TouchEvent) => {
  isDrawing.value = true
  const canvas = signatureCanvas.value
  if (!canvas) return
  
  const rect = canvas.getBoundingClientRect()
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  let x, y
  if (event instanceof MouseEvent) {
    x = event.clientX - rect.left
    y = event.clientY - rect.top
  } else {
    event.preventDefault()
    x = event.touches[0].clientX - rect.left
    y = event.touches[0].clientY - rect.top
  }
  
  ctx.beginPath()
  ctx.moveTo(x, y)
}

const draw = (event: MouseEvent | TouchEvent) => {
  if (!isDrawing.value) return
  
  const canvas = signatureCanvas.value
  if (!canvas) return
  
  const rect = canvas.getBoundingClientRect()
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  let x, y
  if (event instanceof MouseEvent) {
    x = event.clientX - rect.left
    y = event.clientY - rect.top
  } else {
    event.preventDefault()
    x = event.touches[0].clientX - rect.left
    y = event.touches[0].clientY - rect.top
  }
  
  ctx.lineTo(x, y)
  ctx.stroke()
}

const stopDrawing = () => {
  isDrawing.value = false
}

const clearSignature = () => {
  const canvas = signatureCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  signatureDataUrl.value = ''
}

const saveSignature = () => {
  const canvas = signatureCanvas.value
  if (!canvas) return
  
  signatureDataUrl.value = canvas.toDataURL('image/png')
  ElMessage.success('签名已保存')
}

// 上传相关方法
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleUploadSuccess: UploadProps['onSuccess'] = (response, file) => {
  ElMessage.success('图片上传成功')
}

const handleUploadError: UploadProps['onError'] = (error) => {
  ElMessage.error('图片上传失败')
  console.error(error)
}

const handleConfirm = async () => {
  if (!formRef.value || !shipmentData.value) return
  
  try {
    await formRef.value.validate()
    
    if (!signatureDataUrl.value) {
      ElMessage.warning('请先完成电子签名')
      return
    }
    
    submitting.value = true
    
    const deliveryData = {
      shipmentId: shipmentData.value.id,
      actualConsigneeName: formData.actualConsigneeName,
      actualConsigneePhone: formData.actualConsigneePhone,
      actualDeliveryDate: formData.actualDeliveryDate,
      deliveryMethod: formData.deliveryMethod,
      goodsCondition: formData.goodsCondition,
      satisfactionRating: formData.satisfactionRating,
      deliveryRemark: formData.deliveryRemark,
      exceptionTypes: exceptionData.exceptionTypes,
      exceptionDescription: exceptionData.exceptionDescription,
      photos: fileList.value.map(file => file.response?.url || file.url),
      signatureImage: signatureDataUrl.value
    }
    
    await shipmentStore.confirmDelivery(shipmentData.value.id, deliveryData)
    
    ElMessage.success('签收确认成功')
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('签收确认失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (shipmentId: number) => {
  dialogVisible.value = true
  loadShipmentData(shipmentId)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.delivery-dialog-container {
  padding: 16px 0;
}

.shipment-info-card,
.delivery-info-card,
.exception-card,
.photo-upload-card,
.signature-card {
  margin-bottom: 16px;
}

.shipment-info-card :deep(.el-card__header),
.delivery-info-card :deep(.el-card__header),
.exception-card :deep(.el-card__header),
.photo-upload-card :deep(.el-card__header),
.signature-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.upload-tip {
  margin-top: 8px;
  text-align: center;
}

.signature-container {
  text-align: center;
}

.signature-container canvas {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: crosshair;
  background-color: #ffffff;
}

.signature-actions {
  margin-top: 12px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
