<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择入库通知单"
    width="75%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="{}"
      :show-operations="true"
      :operation-width="100"
      operation-fixed="right"
      row-key="id"
      :selection-type="'single'"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @selection-change="handleSelectionChange"
      highlight-current-row
    >
      <template #column-status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ formatStatus(row.status) }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-button type="primary" size="small" @click="handleSelect(row)">选择</el-button>
      </template>
    </VNTable>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedRow">确认选择</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElDialog, ElButton, ElMessage } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types'
import { getInboundNotificationPage, type WmsInboundNotificationSimpleResp, type WmsInboundNotificationResp } from '@/api/wms/inboundNotification'

const emit = defineEmits(['confirm', 'cancel'])

const dialogVisible = ref(false)
const loading = ref(false)
const tableData = ref<WmsInboundNotificationResp[]>([])
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const selectedRow = ref<WmsInboundNotificationResp | null>(null)

const vnTableRef = ref<InstanceType<typeof VNTable>>()

const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'notificationNo', label: '通知单号', minWidth: 180 },
  {
    prop: 'status',
    label: '状态',
    width: 120,
    slot: true,
  },
  { prop: 'clientName', label: '委托客户', minWidth: 150 },
  { prop: 'warehouseName', label: '预入库仓库', minWidth: 150 },
  { prop: 'expectedArrivalDate', label: '预期到货日期', width: 160 },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    formatter: (row: any, column: any, cellValue: string) => {
      if (!cellValue) return "";
      return cellValue.substring(0, 19).replace("T", " ");
    },
  },
])

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      status: 'PLANNED' // 核心查询条件
    }
    const res = await getInboundNotificationPage(params)
    tableData.value = res?.list || []
    pagination.total = res?.total || 0
  } catch (error) {
    ElMessage.error('加载入库通知单失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleSelectionChange = (rows: WmsInboundNotificationResp[]) => {
  selectedRow.value = rows[0] || null
}

const handleSelect = (row: WmsInboundNotificationResp) => {
  selectedRow.value = row
  handleConfirm()
}

const handleConfirm = () => {
  if (selectedRow.value) {
    emit('confirm', selectedRow.value)
    handleClose()
  } else {
    ElMessage.warning('请选择一个入库通知单')
  }
}

const handleOpen = () => {
  loadData()
}

const handleClose = () => {
  dialogVisible.value = false
  selectedRow.value = null
  emit('cancel')
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, "success" | "info" | "warning" | "danger" | ""> = {
    DRAFT: "info",
    PLANNED: "",
    ARRIVED: "",
    RECEIVING: "warning",
    PARTIALLY_RECEIVED: "warning",
    RECEIVED: "success",
    CLOSED: "info",
    CANCELLED: "danger",
  };
  return statusMap[status] || "info";
};

const formatStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    DRAFT: "草稿",
    PLANNED: "已计划",
    ARRIVED: "已到货",
    RECEIVING: "收货中",
    PARTIALLY_RECEIVED: "部分收货",
    RECEIVED: "收货完成",
    CLOSED: "已关闭",
    CANCELLED: "已取消",
  };
  return statusMap[status] || status;
};

// 暴露给父组件的方法
defineExpose({
  open: () => {
    dialogVisible.value = true
  },
  close: handleClose
})
</script> 