package database

import (
	"context"
	"fmt"

	"backend/pkg/constant"
	apperrors "backend/pkg/errors"

	"gorm.io/gorm"
)

// ContextAccountBookIDKey 定义用于在 Context 中传递账套 ID 的键类型
// 使用自定义类型避免潜在的键冲突
type contextKey string

const ContextAccountBookIDKey contextKey = "CURRENT_ACCOUNT_BOOK_ID"

// AccountBookScope 返回一个 GORM Scope，用于自动添加 account_book_id 查询条件
func AccountBookScope(accountBookID uint) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 确保 accountBookID 有效
		if accountBookID == 0 {
			// 如果 accountBookID 无效，可以选择恐慌、记录错误或返回一个必定失败的查询
			// 这里返回一个必定失败的查询，避免意外查询到所有数据
			db.AddError(fmt.Errorf("无效的 accountBookID (0) 用于 AccountBookScope"))
			return db.Where("1 = 0") // 无效条件
		}

		// 检查 GORM 模型是否包含 account_book_id 字段
		// 这可以防止 Scope 被错误地应用到没有该字段的模型上 (如 sys_users)
		if db.Statement.Schema != nil {
			field := db.Statement.Schema.LookUpField("account_book_id")
			if field == nil {
				// 如果模型没有 account_book_id 字段，可以选择跳过 Scope 或报错
				// 这里选择跳过，并可能记录一个警告 (根据需要)
				// log.Warnf("模型 %s 没有 account_book_id 字段，跳过 AccountBookScope", db.Statement.Schema.Name)
				return db
			}
		} else {
			// 如果Schema未初始化（如Create操作），直接应用WHERE条件
			// 对于Create操作，这个条件不会生效，但也不会报错
			// 对于查询操作，如果表确实有account_book_id字段，条件会生效
			// 如果表没有这个字段，SQL执行时会报错，这是合理的
		}

		// 添加 WHERE 条件
		return db.Where("account_book_id = ?", accountBookID)
	}
}

// GetAccountBookIDFromContext 从 Context 获取当前请求的账套 ID
// 返回值：accountBookID, ok (bool, true 表示成功获取)
func GetAccountBookIDFromContext(ctx context.Context) (uint, bool) {
	val := ctx.Value(constant.CONTEXT_ACCOUNT_BOOK_ID)
	if id, ok := val.(uint); ok && id > 0 {
		return id, true
	}
	return 0, false // 未找到或无效
}

// ApplyAccountBookScope 从 Context 获取账套 ID 并应用 Scope
// 这是一个便捷函数，可以在 Repository 中使用
// 如果 Context 中没有有效的账套 ID，会返回错误
func ApplyAccountBookScope(ctx context.Context, db *gorm.DB) (*gorm.DB, error) {
	accountBookID, ok := GetAccountBookIDFromContext(ctx)
	if !ok {
		// 如果 Context 中没有账套 ID，这通常表示请求处理流程有问题（例如 Middleware 未运行或失败）
		// 或者该操作本身就不需要账套隔离
		// 这里返回错误，强制要求调用者处理这种情况
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "无法从 Context 中获取有效的账套 ID")
	}
	// 应用 Scope
	return db.Scopes(AccountBookScope(accountBookID)), nil
}
