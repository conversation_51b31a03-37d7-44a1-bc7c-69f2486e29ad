<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      operation-fixed="right"
      :operation-width="200" 
      :show-pagination="false" 
      @refresh="refreshLocationTree"
      @add="handleAdd()"
    >
      <!-- 库位名称列，带图标 -->
      <template #column-name="{ row }">
        <el-icon style="margin-right: 5px; vertical-align: middle;">
          <House v-if="row.type === 'WAREHOUSE'" />
          <Grid v-else-if="row.type === 'ZONE'" />
          <OfficeBuilding v-else-if="row.type === 'AREA'" />
          <Switch v-else-if="row.type === 'AISLE'" />
          <Files v-else-if="row.type === 'RACK'" />
          <Minus v-else-if="row.type === 'LEVEL'" />
          <Box v-else-if="row.type === 'BIN'" />
          <Coin v-else-if="row.type === 'SLOT'" />
          <Platform v-else-if="row.type === 'FLOOR_SLOT'" />
          <Van v-else-if="row.type === 'DOCK_DOOR'" />
          <DataBoard v-else-if="row.type === 'STAGING_AREA'" />
          <Monitor v-else-if="row.type === 'QC_AREA'" />
        </el-icon>
        <span style="vertical-align: middle;">{{ row.name }}</span>
      </template>

      <!-- 库位类型列 -->
      <template #column-type="{ row }">
        <el-tag :type="getLocationTypeTag(row.type)">{{ formatLocationType(row.type) }}</el-tag>
      </template>

      <!-- 状态列 -->
      <template #column-status="{ row }">
        <el-tag :type="getLocationStatusTag(row.status)">{{ formatLocationStatus(row.status) }}</el-tag>
      </template>

      <!-- 可拣货列 -->
      <template #column-isPickable="{ row }">
        <el-tag :type="row.isPickable ? 'success' : 'info'" size="small">
          {{ row.isPickable ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 可上架列 -->
      <template #column-isPutaway="{ row }">
        <el-tag :type="row.isPutaway ? 'success' : 'info'" size="small">
          {{ row.isPutaway ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 计库存列 -->
      <template #column-isInventoryTracked="{ row }">
        <el-tag :type="row.isInventoryTracked ? 'success' : 'info'" size="small">
          {{ row.isInventoryTracked ? '是' : '否' }}
        </el-tag>
      </template>

    </VNTable>

    <!-- 新增/编辑 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="80%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="4"
        :label-width="'120px'"
        :loading="loading"
        :show-actions="isViewing ? false : true"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <!-- 父级库位插槽 -->
        <template #form-item-parentId="{ field, formData: formModel }">
          <el-tree-select
            v-model="formModel[field.field]"
            :data="parentLocationOptions"
            :props="{ label: 'name', children: 'children', value: 'id' }" 
            check-strictly
            clearable
            placeholder="选择父级库位 (不选为顶级)"
            style="width: 100%;"
            :render-after-expand="false"
            :disabled="field.disabled"
            @change="(value) => handleParentChange(value)"
          />
        </template>

        <!-- 库位类型插槽 -->
        <template #form-item-type="{ field, formData: formModel }">
          <el-select 
            v-model="formModel[field.field]" 
            placeholder="请选择库位类型" 
            style="width: 100%;"
            :disabled="isViewing || (formMode === 'edit' && !!currentData?.id)" 
            @change="(value) => handleLocationTypeChange(value, formModel)"
          >
            <el-option label="仓库" value="WAREHOUSE"></el-option>
            <el-option label="库区" value="ZONE"></el-option>
            <el-option label="作业区" value="AREA"></el-option>
            <el-option label="巷道" value="AISLE"></el-option>
            <el-option label="货架" value="RACK"></el-option>
            <el-option label="货架层" value="LEVEL"></el-option>
            <el-option label="货位/储位" value="BIN"></el-option>
            <el-option label="存储槽" value="SLOT"></el-option>
            <el-option label="地板槽" value="FLOOR_SLOT"></el-option>
            <el-option label="月台门" value="DOCK_DOOR"></el-option>
            <el-option label="暂存区" value="STAGING_AREA"></el-option>
            <el-option label="质检区" value="QC_AREA"></el-option>
          </el-select>
        </template>

        <!-- 状态插槽 -->
        <template #form-item-status="{ field, formData: formModel }">
          <el-radio-group v-model="formModel[field.field]" :disabled="isViewing">
            <el-radio label="ACTIVE">启用</el-radio>
            <el-radio label="INACTIVE">禁用</el-radio>
            <el-radio label="MAINTENANCE">维护中</el-radio>
            <el-radio label="COUNTING">盘点中</el-radio>
          </el-radio-group>
        </template>
        
        <!-- 表单操作按钮 -->
        <template #actions>
          <template v-if="isViewing">
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
            <el-button @click="handleCloseDialog">取消</el-button>
          </template>
        </template>

      </VNForm>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, ActionButton } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton, ElTreeSelect, ElSelect, ElOption, ElIcon, ElRadioGroup, ElRadio, ElInput } from 'element-plus';
import type { TagProps } from 'element-plus';
import { House, Grid, OfficeBuilding, Switch, Files, Minus, Box, Coin, Platform, Van, DataBoard, Monitor, Refresh } from '@element-plus/icons-vue';

// --- 引入库位管理 API 函数 ---
import {
  getLocationTree,
  addLocation,
  updateLocation,
  deleteLocation,
  getLocationByID,
} from '@/api/wms/location';
import type { WmsLocationTreeVO, WmsLocationVO, WmsLocationCreateDTO, WmsLocationUpdateDTO } from '@/api/wms/location';

import { hasPermission } from '@/hooks/usePermission';
// 导入字典Hook
import { useDictionary } from '@/hooks/useDictionary';

// --- 辅助函数：格式化日期时间 (复用user的，保持与user一致) ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) { 
        return '-';
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); 
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    return '-';
  }
};

// --- 辅助函数：获取并格式化API错误消息 (复用user的，保持与user一致) ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }
  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  return '操作出错了，请稍后重试';
};

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentData = ref<WmsLocationTreeVO | WmsLocationVO | null>(null);

// --- Reactive State ---
const loading = ref(false);
const tableData = ref<WmsLocationTreeVO[]>([]);
const formData = ref<Partial<WmsLocationCreateDTO | WmsLocationUpdateDTO>>({});
const parentLocationOptions = ref<WmsLocationTreeVO[]>([]);


// 字典数据
const { loadWmsDictData, getDictItems, BUSINESS_DICT_CODES } = useDictionary();

// --- 计算属性 ---
const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增库位';
  if (formMode.value === 'edit') return '编辑库位';
  if (formMode.value === 'view') return '查看库位';
  return '库位管理';
});
const isViewing = computed(() => formMode.value === 'view');

// --- 表格列配置 ---
const tableColumns = ref<TableColumn[]>([
  { prop: 'name', label: '库位名称', minWidth: 200, align: 'left', slot: true },
  { prop: 'code', label: '库位编码', width: 140 },
  { prop: 'type', label: '类型', width: 120, slot: true },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'isPickable', label: '可拣货', width: 80, slot: true },
  { prop: 'isPutaway', label: '可上架', width: 80, slot: true },
  { prop: 'isInventoryTracked', label: '计库存', width: 80, slot: true },
  { prop: 'remark', label: '备注', minWidth: 150 },
  { prop: 'updatedAt', label: '更新时间', width: 160, formatter: (row: WmsLocationVO) => formatDateTime(row.updatedAt) },
  { prop: 'createdAt', label: '创建时间', width: 160, formatter: (row: WmsLocationVO) => formatDateTime(row.createdAt) },
]);

// --- 工具栏配置 ---
const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('wms:location:add'),
  expandAll: true,
  collapseAll: true,
  density: true,
  columnSetting: true,
  fullscreen: true,
}));

// --- 行操作按钮配置 ---
const operationButtons = computed<ActionButton[]>(() => [
  {
    label: '查看',
    icon: 'View',
    handler: (row) => handleView(row),
    hidden: !hasPermission('wms:location:list')
  },
  {
    label: '新增下级',
    icon: 'Plus',
    type: 'success',
    handler: (row) => handleAdd(row),
    hidden: !hasPermission('wms:location:add')
  },
  {
    label: '编辑',
    icon: 'Edit',
    type: 'primary',
    handler: (row) => handleEdit(row),
    hidden: !hasPermission('wms:location:edit')
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    handler: (row) => handleDelete(row),
    hidden: !hasPermission('wms:location:delete')
  },
]);

// --- 表单字段配置 (分组显示) ---
const formFields = computed<HeaderField[]>(() => {
    const disabledInViewMode = isViewing.value;
    const isEditMode = formMode.value === 'edit';
    const currentSelectedType = formData.value.type;
    console.log('currentSelectedType:', currentSelectedType);
    const fields: HeaderField[] = [
        // 基础信息分组
        {
            field: 'parentId',
            label: '父级库位',
            type: 'slot',
            group: '基础信息',
            md: 24,
            lg: 24,
            xl: 24,
            disabled: disabledInViewMode || isEditMode,
        },
        {
            field: 'type',
            label: '库位类型',
            type: 'slot',
            group: '基础信息',
            rules: [{ required: true, message: '请选择库位类型' }],
            disabled: disabledInViewMode || (isEditMode && !!currentData.value?.id),
        },
        {
            field: 'code',
            label: '库位编码',
            group: '基础信息',
            placeholder: '留空可自动生成或手动输入',
            rules: [
              { pattern: /^[a-zA-Z0-9_-]*$/, message: '编码只能包含字母、数字、下划线和中划线' },
              { max: 50, message: '编码长度不能超过50' }
            ],
            disabled: disabledInViewMode || isEditMode,
        },
        {
            field: 'name',
            label: '库位名称',
            group: '基础信息',
            placeholder: '请输入库位名称',
            rules: [{ required: true, message: '库位名称为必填项' }],
            disabled: disabledInViewMode,
        },
        {
            field: 'status',
            label: '状态',
            type: 'select',
            group: '基础信息',
            options: [
                { label: '启用', value: 'ACTIVE' },
                { label: '禁用', value: 'INACTIVE' },
                { label: '维护中', value: 'MAINTENANCE' },
                { label: '盘点中', value: 'COUNTING' },
            ],
            disabled: disabledInViewMode,
        },

        // 功能设置分组
        {
            field: 'isPickable',
            label: '可拣货',
            type: 'switch',
            group: '基础信息',
            disabled: disabledInViewMode,
        },
        {
            field: 'isPutaway',
            label: '可上架',
            type: 'switch',
            group: '基础信息',
            disabled: disabledInViewMode,
        },
        {
            field: 'isInventoryTracked',
            label: '计库存',
            type: 'switch',
            group: '基础信息',
            disabled: disabledInViewMode,
        },
        {
            field: 'address',
            label: '库位地址',
            group: '基础信息',
            disabled: disabledInViewMode,
        },
        {
            field: 'contact',
            label: '联系人',
            group: '基础信息',
            disabled: disabledInViewMode,
        },
        {
            field: 'phone',
            label: '联系电话',
            group: '基础信息',
            disabled: disabledInViewMode,
        },


        // 特殊属性分组
        {
            field: 'temperatureZone',
            label: '温区',
            type: 'select',
            group: '特殊属性',
            placeholder: '请选择温区',
            options: getDictItems(BUSINESS_DICT_CODES.TEMPERATURE_ZONE).map(item => ({
                label: item.label,
                value: item.value
            })),
            disabled: disabledInViewMode,
        },
        {
            field: 'hazardLevel',
            label: '危险品等级',
            type: 'select',
            group: '特殊属性',
            placeholder: '请选择危险品等级',
            options: getDictItems(BUSINESS_DICT_CODES.DANGER_LEVEL).map(item => ({
                label: item.label,
                value: item.value
            })),
            disabled: disabledInViewMode,
        },
        {
            field: 'securityLevel',
            label: '安全级别',
            type: 'select',
            group: '特殊属性',
            placeholder: '请选择安全级别',
            options: getDictItems(BUSINESS_DICT_CODES.SAFETY_LEVEL).map(item => ({
                label: item.label,
                value: item.value
            })),
            disabled: disabledInViewMode,
        },
        {
            field: 'weightClass',
            label: '承重等级',
            type: 'select',
            group: '特殊属性',
            placeholder: '请选择承重等级',
            options: getDictItems(BUSINESS_DICT_CODES.LOAD_CAPACITY_LEVEL).map(item => ({
                label: item.label,
                value: item.value
            })),
            disabled: disabledInViewMode,
        },
        {
            field: 'storageType',
            label: '存储类型',
            type: 'select',
            group: '特殊属性',
            placeholder: '请选择存储类型',
            options: getDictItems(BUSINESS_DICT_CODES.STORAGE_TYPE).map(item => ({
                label: item.label,
                value: item.value
            })),
            disabled: disabledInViewMode,
        },
        {
            field: 'requiresEquipment',
            label: '所需设备',
            group: '特殊属性',
            placeholder: '请选择所需设备',
            disabled: disabledInViewMode,
        },

        // 容量设置分组
        {
            field: 'maxWeightKg',
            label: '最大承重(kg)',
            type: 'number',
            group: '容量设置',
            props: { precision: 2 },
            disabled: disabledInViewMode,
        },
        {
            field: 'maxVolumeM3',
            label: '最大体积(m³)',
            type: 'number',
            group: '容量设置',
            props: { precision: 4 },
            disabled: disabledInViewMode,
        },
        {
            field: 'maxPallets',
            label: '最大托盘数',
            type: 'number',
            group: '容量设置',
            disabled: disabledInViewMode,
        },
        {
            field: 'maxLengthM',
            label: '最大长度(m)',
            type: 'number',
            group: '容量设置',
            props: { precision: 2 },
            disabled: disabledInViewMode,
        },
        {
            field: 'maxWidthM',
            label: '最大宽度(m)',
            type: 'number',
            group: '容量设置',
            props: { precision: 2 },
            disabled: disabledInViewMode,
        },
        {
            field: 'maxHeightM',
            label: '最大高度(m)',
            type: 'number',
            group: '容量设置',
            props: { precision: 2 },
            disabled: disabledInViewMode,
        },
        {
            field: 'maxItemUnits',
            label: '最大存储单元数',
            type: 'number',
            group: '容量设置',
            disabled: disabledInViewMode,
        },

        // 备注
        {
            field: 'remark',
            label: '备注',
            type: 'textarea',
            group: '其他信息',
            props: { rows: 3 },
            span: 2,
            disabled: disabledInViewMode
        },
    ];

    return fields;
});

// --- 生命周期 ---
onMounted(async () => {
  // 加载字典数据
  await loadWmsDictData();
  // 加载库位树数据
  refreshLocationTree();
});

// --- 方法 ---

// 刷新表格数据和父级选项
const refreshLocationTree = async () => {
  loading.value = true;
  try {
    const response = await getLocationTree() as unknown as { data?: WmsLocationTreeVO[] } | WmsLocationTreeVO[];
    let resData: WmsLocationTreeVO[] = [];
    if (Array.isArray(response)) {
      resData = response;
    } else if (response && Array.isArray(response.data)) {
      resData = response.data;
    }

    if (resData) {
      tableData.value = resData;
      parentLocationOptions.value = filterNodesForParentOptions(resData, currentData.value?.id);
    } else {
      tableData.value = [];
      parentLocationOptions.value = [];
    }
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    tableData.value = [];
    parentLocationOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 递归过滤父级选项
const filterNodesForParentOptions = (nodes: WmsLocationTreeVO[], currentNodeIdToExclude?: number): WmsLocationTreeVO[] => {
  const result: WmsLocationTreeVO[] = [];
  for (const node of nodes) {
    if (node.id === currentNodeIdToExclude) continue;

    const children = node.children ? filterNodesForParentOptions(node.children, currentNodeIdToExclude) : [];
    const newNode = { ...node, children };
    result.push(newNode);
  }
  return result;
};

// 打开新增/编辑/查看弹窗
const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: WmsLocationTreeVO) => { 
  formMode.value = mode;

  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id) {
      ElMessage.error('编辑或查看模式下缺少库位数据或 ID');
      return;
    }
    loading.value = true;
    try {
       const detailData = await getLocationByID(rowData.id) as unknown as WmsLocationVO;
       if (detailData) {
         formData.value = { 
            ...detailData, 
            parentId: detailData.parentId === 0 ? undefined : detailData.parentId,
         };
         currentData.value = detailData; 
         parentLocationOptions.value = filterNodesForParentOptions(tableData.value, detailData.id);
         dialogVisible.value = true;
       } else {
          const errorMessage = getApiErrorMessage({ message: '获取库位详情失败或数据为空'});
          ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
       }
    } catch(error) {
        const errorMessage = getApiErrorMessage(error);
        ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    } finally {
        loading.value = false;
    }
  } else { // 新增模式
    formData.value = {
      status: 'ACTIVE', 
      parentId: rowData?.id ?? undefined,
      code: '',
      name: '',
      type: '',
      isPickable: false,
      isPutaway: false,
      isInventoryTracked: true,
    };
    currentData.value = null;
    parentLocationOptions.value = filterNodesForParentOptions(tableData.value); 
    dialogVisible.value = true;
  }

  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentData.value = null;
};

// 提交表单
const submitForm = async () => {
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  const dataToSend: Partial<WmsLocationCreateDTO | WmsLocationUpdateDTO> = { ...formData.value };
  if (!dataToSend.parentId) {
      dataToSend.parentId = null; 
  }

  loading.value = true;
  try {
    if (formMode.value === 'add') {
      const result = await addLocation(dataToSend as WmsLocationCreateDTO);
      console.log('addLocation result', result);
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentData.value?.id) {
      await updateLocation(currentData.value.id, dataToSend as WmsLocationUpdateDTO);
      ElMessage.success('编辑成功');
    }
    handleCloseDialog();
    refreshLocationTree(); 
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  } finally {
    loading.value = false;
  }
};

// 处理新增按钮点击
const handleAdd = (parentLocation?: WmsLocationTreeVO) => {
  handleOpenForm('add', parentLocation);
};

// 处理编辑按钮点击
const handleEdit = (row: WmsLocationTreeVO) => {
  handleOpenForm('edit', row);
};

// 处理删除按钮点击
const handleDelete = async (row: WmsLocationTreeVO) => {
  if (row.children && row.children.length > 0) {
    ElMessage.warning('请先删除子库位');
    return;
  }
  try {
    await ElMessageBox.confirm(`确定删除库位 "${row.name}" 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    await deleteLocation(row.id);
    ElMessage.success('删除成功');
    refreshLocationTree();
  } catch (error) {
    if (error !== 'cancel') {
      const errorMessage = getApiErrorMessage(error);
      ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

// 处理查看按钮点击
const handleView = (row: WmsLocationTreeVO) => {
  handleOpenForm('view', row);
};

// --- 辅助函数 ---
const formatLocationType = (type: string | undefined): string => {
  const typeMap: Record<string, string> = {
    'WAREHOUSE': '仓库',
    'ZONE': '库区',
    'AREA': '作业区',
    'AISLE': '巷道',
    'RACK': '货架',
    'LEVEL': '货架层',
    'BIN': '货位/储位',
    'SLOT': '存储槽',
    'FLOOR_SLOT': '地板槽',
    'DOCK_DOOR': '月台门',
    'STAGING_AREA': '暂存区',
    'QC_AREA': '质检区',
  };
  return typeMap[type || ''] || '未知';
};

const getLocationTypeTag = (type: string | undefined): TagProps['type'] => {
  const tagMap: Record<string, TagProps['type']> = {
    'WAREHOUSE': 'primary',
    'ZONE': 'success',
    'AREA': 'warning',
    'AISLE': 'info',
    'RACK': 'info',
    'LEVEL': 'success',
    'BIN': 'warning',
    'SLOT': 'info',
    'FLOOR_SLOT': 'info',
    'DOCK_DOOR': 'primary',
    'STAGING_AREA': 'success',
    'QC_AREA': 'danger',
  };
  return tagMap[type || ''] || 'info';
};

const formatLocationStatus = (status: string | undefined): string => {
  const statusMap: Record<string, string> = {
    'ACTIVE': '启用',
    'INACTIVE': '禁用',
    'MAINTENANCE': '维护中',
    'COUNTING': '盘点中',
  };
  return statusMap[status || ''] || '未知';
};

const getLocationStatusTag = (status: string | undefined): TagProps['type'] => {
  const tagMap: Record<string, TagProps['type']> = {
    'ACTIVE': 'success',
    'INACTIVE': 'danger',
    'MAINTENANCE': 'warning',
    'COUNTING': 'info',
  };
  return tagMap[status || ''] || 'info';
};

// 处理父级选择变化
const handleParentChange = (parentId?: number) => {
    // 可以在这里添加根据父级类型调整子级类型的逻辑
    console.log('Parent changed to:', parentId);
};

// 处理库位类型变化
const handleLocationTypeChange = (newType: string, formModel: Partial<WmsLocationCreateDTO | WmsLocationUpdateDTO>) => {
    console.log('Location type changed to:', newType);
    console.log('formModel:', formModel);
    // 可以在这里添加根据类型调整表单字段的逻辑
};

</script>

<style scoped>
:deep(.el-tree-select) {
  width: 100%;
}
</style> 