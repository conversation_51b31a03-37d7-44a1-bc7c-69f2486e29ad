package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
	"context"

	"github.com/jinzhu/copier"
)

// CrmCustomerService 客户服务接口
type CrmCustomerService interface {
	BaseService

	// 客户管理
	Create(ctx context.Context, req *dto.CrmCustomerCreateReq) (*vo.CrmCustomerVO, error)
	Update(ctx context.Context, id uint, req *dto.CrmCustomerUpdateReq) (*vo.CrmCustomerVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.CrmCustomerVO, error)
	GetByCustomerCode(ctx context.Context, customerCode string) (*vo.CrmCustomerVO, error)
	GetPage(ctx context.Context, req *dto.CrmCustomerQueryReq) (*response.PageResult, error)
	GetSimpleList(ctx context.Context) ([]*vo.CrmCustomerSimpleVO, error)
	GetSummary(ctx context.Context) (*vo.CrmCustomerSummaryVO, error)

	// 客户联系人管理
	GetContacts(ctx context.Context, customerID uint) ([]*vo.CrmCustomerContactVO, error)
	CreateContact(ctx context.Context, req *dto.CrmCustomerContactCreateReq) (*vo.CrmCustomerContactVO, error)
	UpdateContact(ctx context.Context, id uint, req *dto.CrmCustomerContactUpdateReq) (*vo.CrmCustomerContactVO, error)
	DeleteContact(ctx context.Context, id uint) error
	SetPrimaryContact(ctx context.Context, customerID uint, contactID uint) error
	GetPrimaryContact(ctx context.Context, customerID uint) (*vo.CrmCustomerContactVO, error)

	// 业务验证
	ValidateCustomerCode(ctx context.Context, customerCode string, excludeID ...uint) error
	ValidateBusinessLicense(ctx context.Context, businessLicense string, excludeID ...uint) error
	ValidateTaxNumber(ctx context.Context, taxNumber string, excludeID ...uint) error
}

// crmCustomerServiceImpl 客户服务实现
type crmCustomerServiceImpl struct {
	BaseServiceImpl
}

// NewCrmCustomerService 创建客户服务
func NewCrmCustomerService(sm *ServiceManager) CrmCustomerService {
	return &crmCustomerServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建客户
func (s *crmCustomerServiceImpl) Create(ctx context.Context, req *dto.CrmCustomerCreateReq) (*vo.CrmCustomerVO, error) {
	var result *vo.CrmCustomerVO

	// 使用事务包装整个创建过程
	err := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		repo := txServiceManager.GetRepositoryManager().GetCrmCustomerRepository()

		// 最大重试次数（编码生成失败时）
		const maxRetries = 3
		var lastErr error

		for attempt := 0; attempt < maxRetries; attempt++ {
			// 处理客户编码生成
			customerCode := req.CustomerCode
			if customerCode == "" || customerCode == "AUTO" {
				// 自动生成客户编码
				codeGenService := txServiceManager.GetCodeGenerationService()
				if codeGenService != nil {
					contextData := map[string]interface{}{
						"customerName": req.CustomerName,
						"customerType": req.CustomerType,
					}
					if req.Industry != nil {
						contextData["industry"] = *req.Industry
					}

					generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
						BusinessType: "CUSTOMER",
						ContextData:  contextData,
					})
					if err != nil {
						return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "客户编码自动生成失败。可能原因：1) 缺少客户编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
					}
					customerCode = generatedCode.GeneratedCode
				} else {
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "客户编码生成服务暂时不可用，请手动输入编码或稍后重试自动生成。")
				}
			}

			// 验证营业执照号唯一性（如果提供）
			if req.BusinessLicense != nil && *req.BusinessLicense != "" {
				if err := s.ValidateBusinessLicense(ctx, *req.BusinessLicense); err != nil {
					return err
				}
			}

			// 验证税务登记号唯一性（如果提供）
			if req.TaxNumber != nil && *req.TaxNumber != "" {
				if err := s.ValidateTaxNumber(ctx, *req.TaxNumber); err != nil {
					return err
				}
			}

			// 从Context获取账套ID
			accountBookID, err := s.GetAccountBookIDFromContext(ctx)
			if err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID").WithCause(err)
			}
			if accountBookID == 0 {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID")
			}
			// 从Context获取用户ID
			userID, err := s.GetUserIDFromContext(ctx)
			if err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
			}
			if userID == 0 {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
			}

			// 创建客户实体
			customer := &entity.CrmCustomer{
				AccountBookEntity: entity.AccountBookEntity{
					AccountBookID: uint(accountBookID),
					TenantEntity: entity.TenantEntity{
						BaseEntity: entity.BaseEntity{
							CreatedBy: uint(userID),
						},
					},
				},
				CustomerCode: customerCode,
				CustomerName: req.CustomerName,
				Status:       "ACTIVE", // 默认状态
			}

			// 复制其他字段
			if err := copier.Copy(customer, req); err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
			}

			// 重要：重新设置生成的客户编码，防止被copier.Copy覆盖
			customer.CustomerCode = customerCode

			// 处理默认值
			if req.CustomerType != "" {
				customer.CustomerType = req.CustomerType
			} else {
				customer.CustomerType = "CORPORATE" // 默认企业客户
			}
			if req.CustomerLevel != "" {
				customer.CustomerLevel = req.CustomerLevel
			} else {
				customer.CustomerLevel = "NORMAL" // 默认普通客户
			}
			if req.Status != "" {
				customer.Status = req.Status
			}
			if req.IsKeyCustomer != nil {
				customer.IsKeyCustomer = *req.IsKeyCustomer
			}
			if req.CurrencyCode != "" {
				customer.CurrencyCode = req.CurrencyCode
			} else {
				customer.CurrencyCode = "CNY" // 默认人民币
			}

			// 尝试保存客户
			err = repo.Create(ctx, customer)
			if err != nil {
				// 检查是否为重复键错误
				if apperrors.IsDuplicateKeyError(err) {
					s.GetServiceManager().GetLogger().Warn("客户编码重复，正在重试",
						s.GetServiceManager().GetLogger().WithField("customerCode", customerCode),
						s.GetServiceManager().GetLogger().WithField("attempt", attempt+1))

					lastErr = apperrors.NewDuplicateKeyError("客户编码重复，系统正在重新生成。如果多次失败，请手动输入编码。")

					// 如果是自动生成的编码且发生重复，继续重试
					if req.CustomerCode == "" || req.CustomerCode == "AUTO" {
						continue
					} else {
						// 手动指定的编码重复，直接返回错误
						return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户编码已存在")
					}
				} else {
					// 其他数据库错误
					return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建客户失败").WithCause(err)
				}
			} else {
				// 创建成功，处理联系人
				for _, contactDTO := range req.Contacts {
					contact := &entity.CrmCustomerContact{
						AccountBookEntity: entity.AccountBookEntity{
							AccountBookID: uint(accountBookID),
							TenantEntity: entity.TenantEntity{
								BaseEntity: entity.BaseEntity{
									CreatedBy: uint(userID),
								},
							},
						},
						CustomerID:  customer.ID,
						ContactName: contactDTO.ContactName,
					}
					if err := copier.Copy(contact, &contactDTO); err == nil {
						// 重要：重新设置账套ID，防止被copier.Copy覆盖
						contact.AccountBookID = uint(accountBookID)
						contact.CreatedBy = uint(userID)
						repo.CreateContact(ctx, contact)
					}
				}

				// 设置返回结果
				result = s.entityToVO(customer)
				return nil
			}
		}

		// 所有重试都失败了
		if lastErr != nil {
			return lastErr
		}

		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "客户创建失败，编码生成重试次数已达上限。建议手动输入客户编码或联系系统管理员。")
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// Update 更新客户
func (s *crmCustomerServiceImpl) Update(ctx context.Context, id uint, req *dto.CrmCustomerUpdateReq) (*vo.CrmCustomerVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	// 获取现有实体
	customer, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户不存在")
	}

	// 验证客户编码唯一性（如果更新了编码）
	if req.CustomerCode != nil && *req.CustomerCode != customer.CustomerCode {
		if err := s.ValidateCustomerCode(ctx, *req.CustomerCode, id); err != nil {
			return nil, err
		}
		customer.CustomerCode = *req.CustomerCode
	}

	// 验证营业执照号唯一性（如果更新了营业执照号）
	if req.BusinessLicense != nil && (customer.BusinessLicense == nil || *req.BusinessLicense != *customer.BusinessLicense) {
		if *req.BusinessLicense != "" {
			if err := s.ValidateBusinessLicense(ctx, *req.BusinessLicense, id); err != nil {
				return nil, err
			}
		}
		customer.BusinessLicense = req.BusinessLicense
	}

	// 验证税务登记号唯一性（如果更新了税务登记号）
	if req.TaxNumber != nil && (customer.TaxNumber == nil || *req.TaxNumber != *customer.TaxNumber) {
		if *req.TaxNumber != "" {
			if err := s.ValidateTaxNumber(ctx, *req.TaxNumber, id); err != nil {
				return nil, err
			}
		}
		customer.TaxNumber = req.TaxNumber
	}

	// 从Context获取用户ID
	userID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
	}
	if userID == 0 {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
	}

	// 更新其他字段
	if req.CustomerName != nil {
		customer.CustomerName = *req.CustomerName
	}
	if req.CustomerType != nil {
		customer.CustomerType = *req.CustomerType
	}
	if req.CustomerLevel != nil {
		customer.CustomerLevel = *req.CustomerLevel
	}
	if req.Status != nil {
		customer.Status = *req.Status
	}
	if req.IsKeyCustomer != nil {
		customer.IsKeyCustomer = *req.IsKeyCustomer
	}

	// 更新其他可选字段
	customer.Industry = req.Industry
	customer.LegalRepresentative = req.LegalRepresentative
	customer.RegisteredCapital = req.RegisteredCapital
	customer.ContactPerson = req.ContactPerson
	customer.ContactPhone = req.ContactPhone
	customer.ContactEmail = req.ContactEmail
	customer.Website = req.Website
	customer.Country = req.Country
	customer.Province = req.Province
	customer.City = req.City
	customer.District = req.District
	customer.Address = req.Address
	customer.PostalCode = req.PostalCode
	customer.CreditRating = req.CreditRating
	customer.CreditLimit = req.CreditLimit
	customer.PaymentTerms = req.PaymentTerms
	if req.CurrencyCode != nil {
		customer.CurrencyCode = *req.CurrencyCode
	}
	customer.CustomerSource = req.CustomerSource
	customer.SalesRepresentativeID = req.SalesRepresentativeID
	customer.Remark = req.Remark
	customer.UpdatedBy = uint(userID)
	// 保存更新
	if err := repo.Update(ctx, customer); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新客户失败").WithCause(err)
	}

	return s.entityToVO(customer), nil
}

// Delete 删除客户
func (s *crmCustomerServiceImpl) Delete(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	if err := repo.Delete(ctx, id); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除客户失败").WithCause(err)
	}

	return nil
}

// GetByID 获取客户详情
func (s *crmCustomerServiceImpl) GetByID(ctx context.Context, id uint) (*vo.CrmCustomerVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	customer, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户不存在")
	}

	// 加载联系人
	contacts, _ := repo.FindContactsByCustomerID(ctx, id)

	customerVO := s.entityToVO(customer)
	contactVOs := s.contactsToVO(contacts)
	customerVO.Contacts = make([]vo.CrmCustomerContactVO, len(contactVOs))
	for i, contactVO := range contactVOs {
		customerVO.Contacts[i] = *contactVO
	}

	return customerVO, nil
}

// GetByCustomerCode 根据客户编码获取客户
func (s *crmCustomerServiceImpl) GetByCustomerCode(ctx context.Context, customerCode string) (*vo.CrmCustomerVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	customer, err := repo.FindByCustomerCode(ctx, customerCode)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户不存在")
	}

	return s.entityToVO(customer), nil
}

// GetPage 分页获取客户列表
func (s *crmCustomerServiceImpl) GetPage(ctx context.Context, req *dto.CrmCustomerQueryReq) (*response.PageResult, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()
	return repo.GetPage(ctx, req)
}

// GetSummary 获取客户统计摘要
func (s *crmCustomerServiceImpl) GetSummary(ctx context.Context) (*vo.CrmCustomerSummaryVO, error) {
	// 这里需要实现具体的统计逻辑
	// 暂时返回空的摘要对象
	return &vo.CrmCustomerSummaryVO{}, nil
}

// GetContacts 获取客户联系人列表
func (s *crmCustomerServiceImpl) GetContacts(ctx context.Context, customerID uint) ([]*vo.CrmCustomerContactVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	contacts, err := repo.FindContactsByCustomerID(ctx, customerID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取联系人列表失败").WithCause(err)
	}

	return s.contactsToVO(contacts), nil
}

// CreateContact 创建客户联系人
func (s *crmCustomerServiceImpl) CreateContact(ctx context.Context, req *dto.CrmCustomerContactCreateReq) (*vo.CrmCustomerContactVO, error) {
	var result *vo.CrmCustomerContactVO

	// 使用事务包装创建过程
	err := s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		repo := txServiceManager.GetRepositoryManager().GetCrmCustomerRepository()

		// 验证客户是否存在
		_, err := repo.FindByID(ctx, req.CustomerID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户不存在")
		}

		// 从Context获取账套ID
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID")
		}
		// 从Context获取用户ID
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
		}

		// 创建联系人实体
		contact := &entity.CrmCustomerContact{
			AccountBookEntity: entity.AccountBookEntity{
				AccountBookID: uint(accountBookID),
				TenantEntity: entity.TenantEntity{
					BaseEntity: entity.BaseEntity{
						CreatedBy: uint(userID),
					},
				},
			},
			CustomerID:  req.CustomerID,
			ContactName: req.ContactName,
		}

		if err := copier.Copy(contact, req); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}

		// 重要：重新设置账套ID，防止被copier.Copy覆盖
		contact.AccountBookID = uint(accountBookID)
		contact.CreatedBy = uint(userID)

		// 处理默认值
		if req.ContactRole != nil {
			contact.ContactRole = req.ContactRole
		}

		// 保存联系人
		if err := repo.CreateContact(ctx, contact); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建联系人失败").WithCause(err)
		}

		result = s.contactToVO(contact)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// UpdateContact 更新客户联系人
func (s *crmCustomerServiceImpl) UpdateContact(ctx context.Context, id uint, req *dto.CrmCustomerContactUpdateReq) (*vo.CrmCustomerContactVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	// 获取现有联系人
	contact, err := repo.FindContactByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "联系人不存在")
	}

	// 从Context获取用户ID
	userID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID").WithCause(err)
	}
	if userID == 0 {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前用户ID")
	}

	// 更新字段
	if req.ContactName != nil {
		contact.ContactName = *req.ContactName
	}
	if req.ContactTitle != nil {
		contact.ContactTitle = req.ContactTitle
	}
	if req.Department != nil {
		contact.Department = req.Department
	}
	if req.Phone != nil {
		contact.Phone = req.Phone
	}
	if req.Mobile != nil {
		contact.Mobile = req.Mobile
	}
	if req.Email != nil {
		contact.Email = req.Email
	}
	if req.QQ != nil {
		contact.QQ = req.QQ
	}
	if req.Wechat != nil {
		contact.Wechat = req.Wechat
	}
	if req.Address != nil {
		contact.Address = req.Address
	}
	if req.PostalCode != nil {
		contact.PostalCode = req.PostalCode
	}
	if req.Birthday != nil {
		contact.Birthday = req.Birthday
	}
	if req.Gender != nil {
		contact.Gender = req.Gender
	}
	if req.ContactRole != nil {
		contact.ContactRole = req.ContactRole
	}
	if req.Remark != nil {
		contact.Remark = req.Remark
	}
	contact.UpdatedBy = uint(userID)
	// 保存更新
	if err := repo.UpdateContact(ctx, contact); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新联系人失败").WithCause(err)
	}

	return s.contactToVO(contact), nil
}

// DeleteContact 删除客户联系人
func (s *crmCustomerServiceImpl) DeleteContact(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	if err := repo.DeleteContact(ctx, id); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除联系人失败").WithCause(err)
	}

	return nil
}

// SetPrimaryContact 设置主要联系人
func (s *crmCustomerServiceImpl) SetPrimaryContact(ctx context.Context, customerID uint, contactID uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	if err := repo.SetPrimaryContact(ctx, customerID, contactID); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "设置主要联系人失败").WithCause(err)
	}

	return nil
}

// GetPrimaryContact 获取主要联系人
func (s *crmCustomerServiceImpl) GetPrimaryContact(ctx context.Context, customerID uint) (*vo.CrmCustomerContactVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	contact, err := repo.FindPrimaryContactByCustomerID(ctx, customerID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "主要联系人不存在")
	}

	return s.contactToVO(contact), nil
}

// ValidateCustomerCode 验证客户编码唯一性
func (s *crmCustomerServiceImpl) ValidateCustomerCode(ctx context.Context, customerCode string, excludeID ...uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	existing, err := repo.FindByCustomerCode(ctx, customerCode)
	if err != nil {
		return nil // 查询出错，编码可用
	}

	// 如果没有找到记录，编码可用
	if existing == nil {
		return nil
	}

	// 如果提供了排除ID，检查是否为同一记录
	if len(excludeID) > 0 && existing.ID == excludeID[0] {
		return nil
	}

	return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户编码已存在")
}

// ValidateBusinessLicense 验证营业执照号唯一性
func (s *crmCustomerServiceImpl) ValidateBusinessLicense(ctx context.Context, businessLicense string, excludeID ...uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	existing, err := repo.FindByBusinessLicense(ctx, businessLicense)
	if err != nil {
		return nil // 查询出错，可用
	}

	// 如果没有找到记录，可用
	if existing == nil {
		return nil
	}

	// 如果提供了排除ID，检查是否为同一记录
	if len(excludeID) > 0 && existing.ID == excludeID[0] {
		return nil
	}

	return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "营业执照号已存在")
}

// ValidateTaxNumber 验证税务登记号唯一性
func (s *crmCustomerServiceImpl) ValidateTaxNumber(ctx context.Context, taxNumber string, excludeID ...uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	existing, err := repo.FindByTaxNumber(ctx, taxNumber)
	if err != nil {
		return nil // 查询出错，可用
	}

	// 如果没有找到记录，可用
	if existing == nil {
		return nil
	}

	// 如果提供了排除ID，检查是否为同一记录
	if len(excludeID) > 0 && existing.ID == excludeID[0] {
		return nil
	}

	return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "税务登记号已存在")
}

// entityToVO 将客户实体转换为VO
func (s *crmCustomerServiceImpl) entityToVO(customer *entity.CrmCustomer) *vo.CrmCustomerVO {
	if customer == nil {
		return nil
	}

	customerVO := &vo.CrmCustomerVO{}
	copier.Copy(customerVO, customer)

	return customerVO
}

// contactToVO 将联系人实体转换为VO
func (s *crmCustomerServiceImpl) contactToVO(contact *entity.CrmCustomerContact) *vo.CrmCustomerContactVO {
	if contact == nil {
		return nil
	}

	contactVO := &vo.CrmCustomerContactVO{}
	copier.Copy(contactVO, contact)

	return contactVO
}

// contactsToVO 将联系人实体列表转换为VO列表
func (s *crmCustomerServiceImpl) contactsToVO(contacts []*entity.CrmCustomerContact) []*vo.CrmCustomerContactVO {
	if len(contacts) == 0 {
		return []*vo.CrmCustomerContactVO{}
	}

	result := make([]*vo.CrmCustomerContactVO, len(contacts))
	for i, contact := range contacts {
		result[i] = s.contactToVO(contact)
	}

	return result
}

// GetSimpleList 获取客户简单列表
func (s *crmCustomerServiceImpl) GetSimpleList(ctx context.Context) ([]*vo.CrmCustomerSimpleVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetCrmCustomerRepository()

	// 获取当前账套下的活跃客户
	customers, err := repo.FindActiveSimpleList(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取客户简单列表失败").WithCause(err)
	}

	// 转换为VO
	result := make([]*vo.CrmCustomerSimpleVO, len(customers))
	for i, customer := range customers {
		result[i] = &vo.CrmCustomerSimpleVO{
			ID:           customer.ID,
			CustomerCode: customer.CustomerCode,
			CustomerName: customer.CustomerName,
			CustomerType: customer.CustomerType,
			Status:       customer.Status,
		}
	}

	return result, nil
}
