<template>
  <el-dialog
    v-model="dialogVisible"
    title="库存分配"
    width="70%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="allocation-dialog-container" v-if="allocationData">
      <!-- 分配信息 -->
      <el-card class="allocation-info-card" shadow="never">
        <template #header>
          <span>分配信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">出库通知单号:</span>
              <span class="value">{{ allocationData.notificationNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">客户名称:</span>
              <span class="value">{{ allocationData.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">当前状态:</span>
              <el-tag :type="getStatusTagType(allocationData.allocationStatus)">
                {{ formatStatus(allocationData.allocationStatus) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 分配策略 -->
      <el-card class="strategy-card" shadow="never">
        <template #header>
          <span>分配策略</span>
        </template>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-form-item label="分配策略" prop="strategy">
            <el-select
              v-model="formData.strategy"
              placeholder="请选择分配策略"
              style="width: 100%"
            >
              <el-option
                v-for="option in strategyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="分配备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入分配备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 分配预览 -->
      <el-card class="preview-card" shadow="never" v-if="allocationPreview.length > 0">
        <template #header>
          <div class="section-header">
            <span>分配预览</span>
            <el-button
              type="primary"
              size="small"
              @click="handlePreview"
              :loading="previewing"
            >
              重新预览
            </el-button>
          </div>
        </template>
        
        <VNTable
          ref="previewTableRef"
          :data="allocationPreview"
          :columns="previewColumns"
          :loading="false"
          row-key="id"
          show-index
          max-height="300"
        />
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          @click="handlePreview"
          :loading="previewing"
          :disabled="!formData.strategy"
        >
          预览分配
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="!canConfirm"
        >
          确认分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElTag, ElForm, ElFormItem, ElSelect, ElOption, ElInput } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn } from '@/components/VNTable/types'
import { useInventoryAllocationStore } from '@/store/wms/inventoryAllocation'

// 组件接口定义
interface AllocationDialogEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<AllocationDialogEmits>()

// Store
const inventoryAllocationStore = useInventoryAllocationStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const previewing = ref(false)
const allocationData = ref<any>(null)
const allocationPreview = ref<any[]>([])

// 表单数据
const formData = reactive({
  strategy: '',
  remark: ''
})

// 表单验证规则
const formRules: FormRules = {
  strategy: [
    { required: true, message: '请选择分配策略', trigger: 'change' }
  ]
}

// 策略选项
const strategyOptions = computed(() => inventoryAllocationStore.strategyOptions)

// 预览表格列配置
const previewColumns = computed<TableColumn[]>(() => [
  { prop: 'itemCode', label: '物料编码', width: 120 },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'requiredQty', label: '需求数量', width: 100 },
  { prop: 'allocatedQty', label: '分配数量', width: 100 },
  { prop: 'locationCode', label: '库位', width: 120 },
  { prop: 'batchNo', label: '批次号', width: 120 },
  { prop: 'availableQty', label: '可用数量', width: 100 }
])

// 计算属性
const canConfirm = computed(() => {
  return formData.strategy && allocationPreview.value.length > 0
})

// 表单引用
const formRef = ref<FormInstance>()
const previewTableRef = ref<InstanceType<typeof VNTable>>()

// 方法
const loadAllocationData = async (allocationId: number) => {
  try {
    const data = await inventoryAllocationStore.fetchDetail(allocationId)
    allocationData.value = data
  } catch (error) {
    ElMessage.error('加载分配数据失败')
    console.error(error)
  }
}

const handleOpen = () => {
  // 在open方法中会传入allocationId
}

const handleClose = () => {
  resetData()
}

const resetData = () => {
  allocationData.value = null
  allocationPreview.value = []
  Object.assign(formData, {
    strategy: '',
    remark: ''
  })
  formRef.value?.clearValidate()
}

const handlePreview = async () => {
  if (!formData.strategy || !allocationData.value) {
    ElMessage.warning('请先选择分配策略')
    return
  }
  
  previewing.value = true
  try {
    const suggestion = await inventoryAllocationStore.getAllocationSuggestion(
      allocationData.value.notificationId,
      formData.strategy
    )
    allocationPreview.value = suggestion.allocations || []
    ElMessage.success('分配预览生成成功')
  } catch (error) {
    ElMessage.error('生成分配预览失败')
    console.error(error)
  } finally {
    previewing.value = false
  }
}

const handleConfirm = async () => {
  if (!formRef.value || !allocationData.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    await inventoryAllocationStore.executeAllocation(allocationData.value.id, formData.strategy)
    
    ElMessage.success('分配成功')
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('分配失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING': 'info',
    'PARTIAL': 'warning',
    'ALLOCATED': 'success',
    'FAILED': 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'PENDING': '待分配',
    'PARTIAL': '部分分配',
    'ALLOCATED': '已分配',
    'FAILED': '分配失败'
  }
  return labelMap[status] || '未知'
}

// 暴露方法
const open = (allocationId: number) => {
  dialogVisible.value = true
  loadAllocationData(allocationId)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.allocation-dialog-container {
  padding: 16px 0;
}

.allocation-info-card,
.strategy-card,
.preview-card {
  margin-bottom: 16px;
}

.allocation-info-card :deep(.el-card__header),
.strategy-card :deep(.el-card__header),
.preview-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 100px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}
</style>
