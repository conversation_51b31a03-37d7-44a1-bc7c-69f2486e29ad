package main

import (
	"errors"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid" // Add import for uuid

	// 导入我们刚刚创建的 license 包，需要确保 go.mod 中路径正确
	// 模块名为 "backend"
	"backend/pkg/license" // Corrected import path based on go.mod
)

func main() {
	// --- 定义命令行参数 ---
	genKeys := flag.Bool("genkeys", false, "仅生成新的密钥对 (private_key.pem, public_key.pem) 并退出")
	privateKeyPath := flag.String("key", "private_key.pem", "私钥文件路径 (如果不存在会自动生成)")
	publicKeyPath := flag.String("pubkey", "public_key.pem", "公钥文件路径 (如果私钥不存在会自动生成)")
	keyBits := flag.Int("bits", 2048, "生成密钥对时的位数 (至少 2048)")
	outputFilePath := flag.String("out", "license/license.lic", "输出授权文件路径") // 修改: 改回指定完整文件路径, 默认值包含目录和固定文件名
	customerName := flag.String("customer", "", "客户名称 (生成授权时必填)")
	expiryDateStr := flag.String("expiry", "", "授权到期日期 (格式: YYYY-MM-DD)")
	featuresStr := flag.String("features", "", "授权的功能列表 (逗号分隔, 例如: feature1,feature2)")
	licenseType := flag.String("type", "Subscription", "许可证类型 (例如: Trial, Subscription, Perpetual)")
	maxUsers := flag.Int("users", 0, "最大用户数 (0表示无限制)")
	maxInstances := flag.Int("instances", 0, "最大实例数 (0表示无限制)")
	// ... 可以添加更多参数对应 LicenseInfo 中的字段 ...

	flag.Parse()

	// --- 模式一：仅生成密钥对 ---
	if *genKeys {
		fmt.Println("正在生成新的密钥对...")
		if _, err := os.Stat(*privateKeyPath); err == nil {
			log.Fatalf("错误: 私钥文件 '%s' 已存在。请先删除或重命名现有文件，或指定不同的路径 (-key)。", *privateKeyPath)
		}
		if _, err := os.Stat(*publicKeyPath); err == nil {
			log.Fatalf("错误: 公钥文件 '%s' 已存在。请先删除或重命名现有文件，或指定不同的路径 (-pubkey)。", *publicKeyPath)
		}
		err := license.GenerateRSAKeyPairPEM(*privateKeyPath, *publicKeyPath, *keyBits)
		if err != nil {
			log.Fatalf("错误: 生成密钥对失败: %v", err)
		}
		fmt.Printf("成功生成私钥: %s\n", *privateKeyPath)
		fmt.Printf("成功生成公钥: %s\n", *publicKeyPath)
		return
	}

	// --- 模式二：生成授权文件 (原有逻辑) ---

	// --- 参数校验 (生成授权文件时需要) ---
	if *customerName == "" {
		log.Fatal("错误: 生成授权文件需要提供客户名称 (-customer)")
	}
	if *privateKeyPath == "" {
		log.Fatal("错误: 必须提供私钥文件路径 (-key)")
	}
	if *outputFilePath == "" { // 使用 outputFilePath 进行校验
		log.Fatal("错误: 必须提供输出文件路径 (-out)")
	}

	// --- 检查并生成密钥对 (如果需要，仅在生成授权文件模式下) ---
	if _, err := os.Stat(*privateKeyPath); errors.Is(err, os.ErrNotExist) {
		fmt.Printf("私钥文件 '%s' 不存在，正在生成新的密钥对...\n", *privateKeyPath)
		if *publicKeyPath == "" {
			log.Fatalf("错误: 自动生成密钥对时需要指定公钥路径 (-pubkey)")
		}
		err := license.GenerateRSAKeyPairPEM(*privateKeyPath, *publicKeyPath, *keyBits)
		if err != nil {
			log.Fatalf("错误: 生成密钥对失败: %v", err)
		}
		fmt.Printf("已生成私钥: %s\n", *privateKeyPath)
		fmt.Printf("已生成公钥: %s\n", *publicKeyPath)
	} else if err != nil {
		log.Fatalf("错误: 检查私钥文件 '%s' 时出错: %v", *privateKeyPath, err)
	}

	// --- 解析日期 ---
	var expiryDate time.Time
	var err error
	if *expiryDateStr != "" {
		expiryDate, err = time.Parse("2006-01-02", *expiryDateStr)
		if err != nil {
			log.Fatalf("错误: 解析到期日期失败 (%s): %v", *expiryDateStr, err)
		}
	} else if *licenseType != "Perpetual" {
		log.Fatal("错误: 对于非永久许可证，必须提供到期日期 (-expiry YYYY-MM-DD)")
	}

	// --- 解析功能列表 ---
	var features []string
	if *featuresStr != "" {
		features = strings.Split(*featuresStr, ",")
		for i := range features {
			features[i] = strings.TrimSpace(features[i])
		}
	}

	// --- 构造 LicenseInfo ---
	licenseInfo := &license.LicenseInfo{
		LicenseID:    generateUniqueID(),
		Issuer:       "AuthSoft Inc.",
		IssueDate:    time.Now(),
		CustomerName: *customerName,
		LicenseType:  *licenseType,
		ExpiryDate:   expiryDate,
		Features:     features,
		MaxUsers:     *maxUsers,
		MaxInstances: *maxInstances,
	}

	// --- 调用库函数生成文件 ---
	fmt.Printf("正在生成授权文件 %s...\n", *outputFilePath) // 使用 outputFilePath

	// 确保输出目录存在
	outputDir := filepath.Dir(*outputFilePath) // 从完整路径获取目录
	if _, err := os.Stat(outputDir); errors.Is(err, os.ErrNotExist) {
		fmt.Printf("输出目录 '%s' 不存在，正在创建...\n", outputDir)
		err = os.MkdirAll(outputDir, os.ModePerm) // Create directory if it doesn't exist
		if err != nil {
			log.Fatalf("错误: 创建输出目录 '%s' 失败: %v", outputDir, err)
		}
	} else if err != nil {
		log.Fatalf("错误: 检查输出目录 '%s' 时出错: %v", outputDir, err)
	}

	err = license.GenerateLicenseFile(licenseInfo, *privateKeyPath, *outputFilePath) // 使用 outputFilePath
	if err != nil {
		log.Fatalf("错误: 生成授权文件失败: %v", err)
	}

	fmt.Printf("授权文件 %s 生成成功。\n", *outputFilePath) // 使用 outputFilePath
	fmt.Println("--- 授权信息 ---")
	fmt.Printf("  客户: %s\n", licenseInfo.CustomerName)
	fmt.Printf("  类型: %s\n", licenseInfo.LicenseType)
	if !licenseInfo.ExpiryDate.IsZero() {
		fmt.Printf("  到期日期: %s\n", licenseInfo.ExpiryDate.Format("2006-01-02"))
	}
	fmt.Printf("  功能: %s\n", strings.Join(licenseInfo.Features, ", "))
	if licenseInfo.MaxUsers > 0 {
		fmt.Printf("  最大用户数: %d\n", licenseInfo.MaxUsers)
	}
	if licenseInfo.MaxInstances > 0 {
		fmt.Printf("  最大实例数: %d\n", licenseInfo.MaxInstances)
	}
	fmt.Println("-----------------")
}

// generateUniqueID - 使用 google/uuid 生成唯一的许可证 ID
func generateUniqueID() string {
	u, err := uuid.NewRandom()
	if err != nil {
		log.Printf("警告: 生成 UUID 失败: %v, 使用时间戳作为后备 ID", err)
		return fmt.Sprintf("LID-fallback-%d", time.Now().UnixNano())
	}
	return u.String()
}

// 注意:
// 1. `generateUniqueID` 已更新为使用 UUID。
// 2. 导入路径 "backend/pkg/license" 已根据 go.mod 调整。
// 3. 添加了 -genkeys 标志用于仅生成密钥对。
// 4. 生成授权文件时会自动创建输出目录 (如果不存在)。
// 5. 授权文件名已改回固定名称 (默认为 license/license.lic), 由 -out 标志指定。
