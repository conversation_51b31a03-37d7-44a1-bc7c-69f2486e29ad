package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsOutboundNotificationService 定义出库通知单服务接口
type WmsOutboundNotificationService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsOutboundNotificationCreateReq) (*vo.WmsOutboundNotificationVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsOutboundNotificationUpdateReq) (*vo.WmsOutboundNotificationVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsOutboundNotificationVO, error)
	GetPage(ctx context.Context, req *dto.WmsOutboundNotificationQueryReq) (*vo.PageResult[vo.WmsOutboundNotificationListVO], error)

	// 批量操作
	BatchCreate(ctx context.Context, req *dto.WmsOutboundNotificationBatchCreateReq) (*dto.BatchImportResult, error)
	BatchApprove(ctx context.Context, req *dto.WmsOutboundNotificationBatchApproveReq) error
	BatchCancel(ctx context.Context, req *dto.WmsOutboundNotificationBatchCancelReq) error

	// 状态管理
	Approve(ctx context.Context, req *dto.WmsOutboundNotificationApproveReq) error
	Cancel(ctx context.Context, req *dto.WmsOutboundNotificationCancelReq) error

	// 库存分配
	AllocateInventory(ctx context.Context, req *dto.WmsOutboundNotificationAllocateReq) (*vo.WmsOutboundNotificationAllocationStatusVO, error)
	BatchAllocateInventory(ctx context.Context, req *dto.WmsOutboundNotificationBatchAllocateReq) ([]*vo.WmsOutboundNotificationAllocationStatusVO, error)
	GetAllocationStatus(ctx context.Context, id uint) (*vo.WmsOutboundNotificationAllocationStatusVO, error)

	// 拣货任务生成
	GeneratePickingTask(ctx context.Context, id uint, strategy string) (*vo.WmsPickingTaskVO, error)
	BatchGeneratePickingTask(ctx context.Context, ids []uint, strategy string) ([]*vo.WmsPickingTaskVO, error)

	// 统计分析
	GetStats(ctx context.Context, req *dto.WmsOutboundNotificationStatsReq) (*vo.WmsOutboundNotificationStatsVO, error)

	// 导入导出
	ImportFromExcel(ctx context.Context, req *dto.WmsOutboundNotificationImportReq) (*dto.BatchImportResult, error)
	ExportToExcel(ctx context.Context, req *dto.WmsOutboundNotificationExportReq) ([]byte, error)

	// 业务查询
	GetByNotificationNo(ctx context.Context, notificationNo string) (*vo.WmsOutboundNotificationVO, error)
	GetByClientOrderNo(ctx context.Context, clientOrderNo string) ([]*vo.WmsOutboundNotificationVO, error)
	GetPendingAllocation(ctx context.Context) ([]*vo.WmsOutboundNotificationListVO, error)
	GetReadyForPicking(ctx context.Context) ([]*vo.WmsOutboundNotificationListVO, error)
	GetOverdueShipments(ctx context.Context) ([]*vo.WmsOutboundNotificationListVO, error)
}

// wmsOutboundNotificationServiceImpl 出库通知单服务实现
type wmsOutboundNotificationServiceImpl struct {
	BaseServiceImpl
}

// NewWmsOutboundNotificationService 创建出库通知单服务
func NewWmsOutboundNotificationService(sm *ServiceManager) WmsOutboundNotificationService {
	return &wmsOutboundNotificationServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建出库通知单
func (s *wmsOutboundNotificationServiceImpl) Create(ctx context.Context, req *dto.WmsOutboundNotificationCreateReq) (*vo.WmsOutboundNotificationVO, error) {
	var notification *entity.WmsOutboundNotification
	var voResult *vo.WmsOutboundNotificationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsOutboundNotificationRepository()

		// 生成出库通知单号
		notificationNo, err := s.generateNotificationNo(ctx, req)
		if err != nil {
			return err
		}

		notification = &entity.WmsOutboundNotification{}
		copier.Copy(notification, req)
		notification.NotificationNo = notificationNo
		notification.Status = string(entity.OutboundStatusDraft) // 新增时默认为草稿状态

		// 强制从上下文获取账套ID - 账套强绑定
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空")
		}
		notification.AccountBookID = uint(accountBookID)

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}
		notification.CreatedBy = uint(userID)

		// 处理明细表数据
		if len(req.Details) > 0 {
			details := make([]entity.WmsOutboundNotificationDetail, len(req.Details))
			for i, detailReq := range req.Details {
				if err := s.validateNotificationDetail(ctx, txRepoMgr, &detailReq); err != nil {
					return err
				}

				detail := &entity.WmsOutboundNotificationDetail{}
				copier.Copy(detail, &detailReq)
				detail.NotificationID = notification.ID
				detail.AccountBookID = notification.AccountBookID // 明细表继承主表的账套ID
				detail.CreatedBy = notification.CreatedBy

				details[i] = *detail
			}
			notification.Details = details
		}

		// 创建通知单（包含明细表）
		if err := repo.Create(ctx, notification); err != nil {
			if apperrors.IsDuplicateKeyError(err) {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "通知单号已存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建出库通知单失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, notification)
	return voResult, nil
}

// Update 更新出库通知单（支持主表+明细表差异更新）
func (s *wmsOutboundNotificationServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsOutboundNotificationUpdateReq) (*vo.WmsOutboundNotificationVO, error) {
	var voResult *vo.WmsOutboundNotificationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsOutboundNotificationRepository()

		// 1. 获取当前记录（预加载明细表）
		current, err := repo.FindByID(ctx, id)
		if err != nil {
			return fmt.Errorf("查询记录失败: %w", err)
		}

		// 检查状态是否允许修改
		if current.Status != string(entity.OutboundStatusDraft) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有草稿状态的通知单才能修改")
		}

		// 预加载明细表
		if err := repo.GetDB(ctx).Preload("Details").First(current, id).Error; err != nil {
			return fmt.Errorf("预加载明细表失败: %w", err)
		}

		// 2. 更新主表字段
		updates := make(map[string]interface{})
		if req.ClientID != 0 {
			updates["client_id"] = req.ClientID
		}
		if req.ClientOrderNo != nil {
			updates["client_order_no"] = *req.ClientOrderNo
		}
		if req.WarehouseID != 0 {
			updates["warehouse_id"] = req.WarehouseID
		}
		if req.RequiredShipDate != nil {
			updates["required_ship_date"] = *req.RequiredShipDate
		}
		if req.Priority != 0 {
			updates["priority"] = req.Priority
		}
		if req.ConsigneeName != "" {
			updates["consignee_name"] = req.ConsigneeName
		}
		if req.ConsigneePhone != nil {
			updates["consignee_phone"] = *req.ConsigneePhone
		}
		if req.ConsigneeAddress != nil {
			updates["consignee_address"] = *req.ConsigneeAddress
		}
		if req.CarrierID != nil {
			updates["carrier_id"] = *req.CarrierID
		}
		if req.ShippingMethod != nil {
			updates["shipping_method"] = *req.ShippingMethod
		}
		if req.Remark != nil {
			updates["remark"] = *req.Remark
		}

		// 更新主表
		if len(updates) > 0 {
			// 强制从上下文获取用户ID - 用户强绑定
			userID, err := s.GetUserIDFromContext(ctx)
			if err != nil {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
			}
			if userID == 0 {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
			}
			updates["updated_by"] = uint(userID)

			if err := repo.GetDB(ctx).Model(current).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新主表失败: %w", err)
			}
		}

		// 3. 处理明细表差异更新
		if len(req.Details) > 0 {
			if err := s.updateNotificationDetails(ctx, repo.GetDB(ctx), current, req.Details); err != nil {
				return fmt.Errorf("更新明细表失败: %w", err)
			}
		}

		// 4. 重新查询完整数据返回
		var result entity.WmsOutboundNotification
		if err := repo.GetDB(ctx).Preload("Details").First(&result, id).Error; err != nil {
			return fmt.Errorf("查询更新后数据失败: %w", err)
		}

		copier.Copy(&voResult, &result)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return voResult, nil
}

// Delete 删除出库通知单
func (s *wmsOutboundNotificationServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsOutboundNotificationRepository()

		notification, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if notification.Status != string(entity.OutboundStatusDraft) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有草稿状态的通知单才能删除")
		}

		// 删除主表（GORM会自动处理明细表的级联删除）
		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除出库通知单失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取出库通知单
func (s *wmsOutboundNotificationServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsOutboundNotificationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	// 先获取基本信息
	notification, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
	}

	// 使用BaseRepository的DB连接直接预加载明细和物料信息
	db := repo.GetDB(ctx)
	if err := db.Preload("Details").Where("id = ?", id).First(notification).Error; err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "加载通知单明细失败").WithCause(err)
	}

	voResult := &vo.WmsOutboundNotificationVO{}
	if err := copier.Copy(voResult, notification); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "转换数据失败").WithCause(err)
	}

	// 填充扩展信息
	if err := s.fillExtendedInfo(ctx, voResult); err != nil {
		return nil, err
	}

	return voResult, nil
}

// GetPage 获取出库通知单分页数据
func (s *wmsOutboundNotificationServiceImpl) GetPage(ctx context.Context, req *dto.WmsOutboundNotificationQueryReq) (*vo.PageResult[vo.WmsOutboundNotificationListVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	// 调用Repository的分页查询
	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单分页数据失败").WithCause(err)
	}

	// 转换为VO
	voResult := &vo.PageResult[vo.WmsOutboundNotificationListVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsOutboundNotificationListVO, len(pageResult.List.([]*entity.WmsOutboundNotification))),
	}

	notifications := pageResult.List.([]*entity.WmsOutboundNotification)
	for i, notification := range notifications {
		listVO := &vo.WmsOutboundNotificationListVO{}
		copier.Copy(listVO, notification)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		voResult.List[i] = listVO
	}

	return voResult, nil
}

// BatchApprove 批量审核出库通知单
func (s *wmsOutboundNotificationServiceImpl) BatchApprove(ctx context.Context, req *dto.WmsOutboundNotificationBatchApproveReq) error {
	for _, id := range req.IDs {
		approveReq := &dto.WmsOutboundNotificationApproveReq{
			ID:     id,
			Remark: req.Remark,
		}

		if err := s.Approve(ctx, approveReq); err != nil {
			// 记录错误但继续处理其他通知单
			continue
		}
	}

	return nil
}

// BatchCancel 批量取消出库通知单
func (s *wmsOutboundNotificationServiceImpl) BatchCancel(ctx context.Context, req *dto.WmsOutboundNotificationBatchCancelReq) error {
	for _, id := range req.IDs {
		cancelReq := &dto.WmsOutboundNotificationCancelReq{
			ID:     id,
			Reason: req.Reason,
		}

		if err := s.Cancel(ctx, cancelReq); err != nil {
			// 记录错误但继续处理其他通知单
			continue
		}
	}

	return nil
}

// Approve 审核出库通知单
func (s *wmsOutboundNotificationServiceImpl) Approve(ctx context.Context, req *dto.WmsOutboundNotificationApproveReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsOutboundNotificationRepository()

		// 检查通知单是否存在
		notification, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
		}

		// 检查状态是否允许审核
		if notification.Status != string(entity.OutboundStatusDraft) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有草稿状态的通知单才能审核")
		}

		// 验证明细数据完整性
		if err := s.validateNotificationForApproval(ctx, txRepoMgr, notification); err != nil {
			return err
		}

		// 更新状态为已审核
		remark := ""
		if req.Remark != nil {
			remark = *req.Remark
		}

		if err := repo.UpdateStatus(ctx, req.ID, string(entity.OutboundStatusApproved), remark); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新通知单状态失败").WithCause(err)
		}

		return nil
	})
}

// Cancel 取消出库通知单
func (s *wmsOutboundNotificationServiceImpl) Cancel(ctx context.Context, req *dto.WmsOutboundNotificationCancelReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsOutboundNotificationRepository()

		// 检查通知单是否存在
		notification, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
		}

		// 检查状态是否允许取消
		if notification.Status == string(entity.OutboundStatusCancelled) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "通知单已经是取消状态")
		}
		if notification.Status == string(entity.OutboundStatusShipped) ||
			notification.Status == string(entity.OutboundStatusDelivered) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已发运或已送达的通知单不能取消")
		}

		// 如果已分配库存，需要释放库存分配
		if notification.Status == string(entity.OutboundStatusAllocated) ||
			notification.Status == string(entity.OutboundStatusPicking) ||
			notification.Status == string(entity.OutboundStatusPicked) {
			if err := s.releaseInventoryAllocations(ctx, txRepoMgr, req.ID); err != nil {
				return fmt.Errorf("释放库存分配失败: %w", err)
			}
		}

		// 更新状态为已取消
		reason := ""
		if req.Reason != nil {
			reason = *req.Reason
		}

		if err := repo.UpdateStatus(ctx, req.ID, string(entity.OutboundStatusCancelled), reason); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新通知单状态失败").WithCause(err)
		}

		return nil
	})
}
