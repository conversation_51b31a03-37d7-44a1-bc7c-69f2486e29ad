package service

import (
	"context"
	"fmt"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
)

// GetStats 获取统计信息
func (s *wmsOutboundNotificationServiceImpl) GetStats(ctx context.Context, req *dto.WmsOutboundNotificationStatsReq) (*vo.WmsOutboundNotificationStatsVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	// 获取基础统计数据
	var startDate, endDate *time.Time
	if req.DateStart != nil {
		startDate = req.DateStart
	}
	if req.DateEnd != nil {
		endDate = req.DateEnd
	}

	statsData, err := repo.GetStatsByDateRange(ctx, *startDate, *endDate)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取统计数据失败").WithCause(err)
	}

	result := &vo.WmsOutboundNotificationStatsVO{}
	copier.Copy(result, statsData)

	// TODO: 根据GroupBy参数生成趋势数据
	// TODO: 计算效率统计

	return result, nil
}

// ImportFromExcel 从Excel导入
func (s *wmsOutboundNotificationServiceImpl) ImportFromExcel(ctx context.Context, req *dto.WmsOutboundNotificationImportReq) (*dto.BatchImportResult, error) {
	// TODO: 解析Excel文件
	// TODO: 验证数据格式
	// TODO: 批量创建通知单

	result := &dto.BatchImportResult{
		TotalCount:     0,
		SuccessCount:   0,
		FailureCount:   0,
		SuccessIDs:     []uint{},
		FailureReasons: []string{},
	}

	// 临时实现，返回未实现错误
	return result, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "Excel导入功能暂未实现")
}

// ExportToExcel 导出到Excel
func (s *wmsOutboundNotificationServiceImpl) ExportToExcel(ctx context.Context, req *dto.WmsOutboundNotificationExportReq) ([]byte, error) {
	// TODO: 查询数据
	// TODO: 生成Excel文件
	// TODO: 返回文件字节数组

	// 临时实现，返回未实现错误
	return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "Excel导出功能暂未实现")
}

// performInventoryAllocation 执行库存分配
func (s *wmsOutboundNotificationServiceImpl) performInventoryAllocation(
	ctx context.Context,
	txRepoMgr *repository.RepositoryManager,
	allocationService WmsInventoryAllocationService,
	details []*entity.WmsOutboundNotificationDetail,
	req *dto.WmsOutboundNotificationAllocateReq,
) (*vo.WmsOutboundNotificationAllocationStatusVO, error) {

	result := &vo.WmsOutboundNotificationAllocationStatusVO{
		NotificationID: req.ID,
		TotalItems:     len(details),
		Details:        make([]vo.WmsOutboundNotificationDetailAllocationVO, len(details)),
	}

	allocatedItems := 0
	strategy := "FIFO" // 默认策略
	if req.AllocationStrategy != nil {
		strategy = *req.AllocationStrategy
	}

	for i, detail := range details {
		// 为每个明细执行自动分配
		autoReq := &dto.WmsInventoryAllocationAutoReq{
			OutboundDetailID:   detail.ID,
			AllocationStrategy: strategy,
			ForceAllocate:      req.ForceAllocate,
		}

		allocations, err := allocationService.AutoAllocate(ctx, autoReq)
		if err != nil {
			return nil, fmt.Errorf("明细ID %d 分配失败: %w", detail.ID, err)
		}

		// 计算分配结果
		allocatedQty := float64(0)
		for _, allocation := range allocations {
			allocatedQty += allocation.AllocatedQty
		}

		if allocatedQty > 0 {
			allocatedItems++
		}

		detailVO := vo.WmsOutboundNotificationDetailAllocationVO{
			DetailID:       detail.ID,
			LineNo:         detail.LineNo,
			RequiredQty:    detail.RequiredQty,
			AllocatedQty:   allocatedQty,
			AllocationRate: allocatedQty / detail.RequiredQty * 100,
			CanAllocate:    detail.RequiredQty > allocatedQty,
			ShortageQty:    detail.RequiredQty - allocatedQty,
		}

		if detailVO.ShortageQty > 0 {
			detailVO.ShortageReason = &[]string{"库存不足"}[0]
		}

		result.Details[i] = detailVO
	}

	result.AllocatedItems = allocatedItems
	if result.TotalItems > 0 {
		result.AllocationRate = float64(allocatedItems) / float64(result.TotalItems) * 100
	}

	// 判断分配结果
	if allocatedItems == 0 {
		result.AllocationResult = "NONE"
		result.CanAllocate = true
	} else if allocatedItems == result.TotalItems {
		result.AllocationResult = "FULL"
		result.CanAllocate = false
	} else {
		result.AllocationResult = "PARTIAL"
		result.CanAllocate = true
	}

	return result, nil
}
