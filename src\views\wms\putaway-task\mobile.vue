<template>
  <div class="mobile-putaway-container">
    <!-- 移动端头部 -->
    <div class="mobile-header">
      <div class="header-left">
        <el-button
          type="primary"
          size="small"
          @click="handleBack"
          circle
        >
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
      </div>
      <div class="header-center">
        <h3>上架任务</h3>
      </div>
      <div class="header-right">
        <el-button
          type="success"
          size="small"
          @click="handleScanMode"
          circle
        >
          <el-icon><Scan /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 任务信息卡片 -->
    <div class="task-info-card" v-if="currentTask">
      <div class="task-header">
        <div class="task-no">{{ currentTask.taskNo }}</div>
        <el-tag :type="getStatusTagType(currentTask.status)">
          {{ formatStatus(currentTask.status) }}
        </el-tag>
      </div>
      <div class="task-details">
        <div class="detail-item">
          <span class="label">收货记录号:</span>
          <span class="value">{{ currentTask.receivingRecordNo }}</span>
        </div>
        <div class="detail-item">
          <span class="label">上架策略:</span>
          <span class="value">{{ currentTask.putawayStrategy }}</span>
        </div>
        <div class="detail-item">
          <span class="label">分配人员:</span>
          <span class="value">{{ currentTask.assignedUserName }}</span>
        </div>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-info">
          <span>完成进度: {{ completedItems }}/{{ totalItems }}</span>
          <span>{{ progressPercentage }}%</span>
        </div>
        <el-progress
          :percentage="progressPercentage"
          :color="getProgressColor()"
          :stroke-width="8"
        />
      </div>
    </div>

    <!-- 上架明细列表 -->
    <div class="putaway-list">
      <div class="list-header">
        <span>上架明细 ({{ putawayDetails.length }})</span>
        <el-button
          type="primary"
          size="small"
          @click="handleCompleteAll"
          :disabled="!canCompleteAll"
        >
          完成任务
        </el-button>
      </div>
      
      <div class="detail-items">
        <div
          v-for="detail in putawayDetails"
          :key="detail.id"
          class="detail-item-card"
          :class="getDetailCardClass(detail)"
          @click="handleDetailClick(detail)"
        >
          <div class="item-header">
            <div class="item-code">{{ detail.itemSku }}</div>
            <el-tag :type="getDetailStatusType(detail.status)" size="small">
              {{ formatDetailStatus(detail.status) }}
            </el-tag>
          </div>
          
          <div class="item-content">
            <div class="item-name">{{ detail.itemName }}</div>
            <div class="item-info">
              <div class="info-row">
                <span class="label">建议库位:</span>
                <span class="value">{{ detail.suggestedLocation }}</span>
              </div>
              <div class="info-row">
                <span class="label">上架数量:</span>
                <span class="value">{{ detail.putawayQuantity }}</span>
              </div>
              <div class="info-row">
                <span class="label">已上架数量:</span>
                <span class="value">{{ detail.completedQuantity || 0 }}</span>
              </div>
              <div class="info-row" v-if="detail.batchNo">
                <span class="label">批次号:</span>
                <span class="value">{{ detail.batchNo }}</span>
              </div>
            </div>
          </div>
          
          <div class="item-actions">
            <el-button
              v-if="detail.status === 'PENDING'"
              type="primary"
              size="small"
              @click.stop="handleStartPutaway(detail)"
            >
              开始上架
            </el-button>
            <el-button
              v-if="detail.status === 'IN_PROGRESS'"
              type="success"
              size="small"
              @click.stop="handleCompletePutaway(detail)"
            >
              完成上架
            </el-button>
            <el-button
              v-if="detail.status === 'COMPLETED'"
              type="info"
              size="small"
              @click.stop="handleAdjustPutaway(detail)"
            >
              调整
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <el-button
        type="warning"
        @click="handlePauseTask"
        :disabled="currentTask?.status !== 'IN_PROGRESS'"
      >
        暂停任务
      </el-button>
      <el-button
        type="danger"
        @click="handleCancelTask"
        :disabled="!canCancelTask"
      >
        取消任务
      </el-button>
    </div>

    <!-- 上架对话框 -->
    <MobilePutawayDialog
      ref="mobilePutawayDialogRef"
      @confirm="handlePutawayConfirm"
      @cancel="handlePutawayCancel"
    />

    <!-- 扫码对话框 -->
    <MobileScanDialog
      ref="mobileScanDialogRef"
      @confirm="handleScanConfirm"
      @cancel="handleScanCancel"
    />

    <!-- 任务完成对话框 -->
    <el-dialog
      v-model="completeDialogVisible"
      title="完成任务"
      width="90%"
      :close-on-click-modal="false"
    >
      <div class="complete-summary">
        <div class="summary-item">
          <span class="label">任务编号:</span>
          <span class="value">{{ currentTask?.taskNo }}</span>
        </div>
        <div class="summary-item">
          <span class="label">总物料数:</span>
          <span class="value">{{ totalItems }}</span>
        </div>
        <div class="summary-item">
          <span class="label">已完成数:</span>
          <span class="value">{{ completedItems }}</span>
        </div>
        <div class="summary-item">
          <span class="label">完成率:</span>
          <span class="value">{{ progressPercentage }}%</span>
        </div>
      </div>
      
      <el-form :model="completeForm" label-width="80px">
        <el-form-item label="完成备注">
          <el-input
            v-model="completeForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入完成备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="completeDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirmComplete"
          :loading="completing"
        >
          确认完成
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElButton, ElIcon, ElTag, ElProgress, ElDialog, ElForm, ElFormItem, ElInput, ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Scan } from '@element-plus/icons-vue'
import MobilePutawayDialog from './components/MobilePutawayDialog.vue'
import MobileScanDialog from './components/MobileScanDialog.vue'
import type { WmsPutawayTaskResp, WmsPutawayTaskDetailResp } from '@/api/wms/putawayTask'
import { usePutawayTaskStore } from '@/store/wms/putawayTask'

// 路由和Store
const route = useRoute()
const router = useRouter()
const putawayTaskStore = usePutawayTaskStore()

// 响应式数据
const currentTask = ref<WmsPutawayTaskResp | null>(null)
const putawayDetails = ref<WmsPutawayTaskDetailResp[]>([])
const completeDialogVisible = ref(false)
const completing = ref(false)

// 表单数据
const completeForm = reactive({
  remark: ''
})

// 组件引用
const mobilePutawayDialogRef = ref<InstanceType<typeof MobilePutawayDialog>>()
const mobileScanDialogRef = ref<InstanceType<typeof MobileScanDialog>>()

// 计算属性
const totalItems = computed(() => {
  return putawayDetails.value.reduce((sum, detail) => sum + detail.putawayQuantity, 0)
})

const completedItems = computed(() => {
  return putawayDetails.value.reduce((sum, detail) => sum + (detail.completedQuantity || 0), 0)
})

const progressPercentage = computed(() => {
  return totalItems.value > 0 ? Math.round((completedItems.value / totalItems.value) * 100) : 0
})

const canCompleteAll = computed(() => {
  return currentTask.value?.status === 'IN_PROGRESS' && progressPercentage.value === 100
})

const canCancelTask = computed(() => {
  return currentTask.value && ['ASSIGNED', 'IN_PROGRESS'].includes(currentTask.value.status)
})

// 方法
const loadTaskData = async () => {
  const taskId = Number(route.params.id)
  if (!taskId) {
    ElMessage.error('任务ID无效')
    handleBack()
    return
  }

  try {
    const task = await putawayTaskStore.fetchDetail(taskId)
    currentTask.value = task
    putawayDetails.value = task.details?.map(detail => ({
      ...detail,
      status: detail.completedQuantity >= detail.putawayQuantity ? 'COMPLETED' : 
              detail.completedQuantity > 0 ? 'IN_PROGRESS' : 'PENDING'
    })) || []
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    console.error(error)
    handleBack()
  }
}

const handleBack = () => {
  router.back()
}

const handleScanMode = () => {
  mobileScanDialogRef.value?.open()
}

const handleDetailClick = (detail: WmsPutawayTaskDetailResp) => {
  if (detail.status === 'PENDING') {
    handleStartPutaway(detail)
  } else if (detail.status === 'IN_PROGRESS') {
    handleCompletePutaway(detail)
  }
}

const handleStartPutaway = (detail: WmsPutawayTaskDetailResp) => {
  mobilePutawayDialogRef.value?.open(detail, 'start')
}

const handleCompletePutaway = (detail: WmsPutawayTaskDetailResp) => {
  mobilePutawayDialogRef.value?.open(detail, 'complete')
}

const handleAdjustPutaway = (detail: WmsPutawayTaskDetailResp) => {
  mobilePutawayDialogRef.value?.open(detail, 'adjust')
}

const handlePutawayConfirm = (data: any) => {
  // 更新明细状态
  const detail = putawayDetails.value.find(d => d.id === data.detailId)
  if (detail) {
    detail.completedQuantity = data.completedQuantity
    detail.actualLocation = data.actualLocation
    detail.status = detail.completedQuantity >= detail.putawayQuantity ? 'COMPLETED' : 
                   detail.completedQuantity > 0 ? 'IN_PROGRESS' : 'PENDING'
  }
  ElMessage.success('上架操作成功')
}

const handlePutawayCancel = () => {
  // 上架取消处理
}

const handleScanConfirm = (data: any) => {
  // 扫码上架处理
  ElMessage.success('扫码上架成功')
  loadTaskData() // 刷新数据
}

const handleScanCancel = () => {
  // 扫码取消处理
}

const handleCompleteAll = () => {
  completeDialogVisible.value = true
}

const handleConfirmComplete = async () => {
  if (!currentTask.value) return

  completing.value = true
  try {
    await putawayTaskStore.completeTask(currentTask.value.id, completeForm.remark)
    ElMessage.success('任务完成成功')
    completeDialogVisible.value = false
    handleBack()
  } catch (error) {
    ElMessage.error('完成任务失败')
    console.error(error)
  } finally {
    completing.value = false
  }
}

const handlePauseTask = async () => {
  if (!currentTask.value) return

  try {
    await ElMessageBox.confirm('确定要暂停这个上架任务吗？', '确认暂停', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用暂停任务的API
    ElMessage.success('任务已暂停')
    loadTaskData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('暂停任务失败')
    }
  }
}

const handleCancelTask = async () => {
  if (!currentTask.value) return

  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消上架任务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '请输入取消原因'
    })

    await putawayTaskStore.cancelTask(currentTask.value.id, reason)
    ElMessage.success('任务已取消')
    handleBack()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

// 工具方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'ASSIGNED': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'ASSIGNED': '已分配',
    'IN_PROGRESS': '进行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return labelMap[status] || '未知'
}

const getDetailStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING': 'info',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success'
  }
  return typeMap[status] || 'info'
}

const formatDetailStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'PENDING': '待上架',
    'IN_PROGRESS': '上架中',
    'COMPLETED': '已上架'
  }
  return labelMap[status] || '未知'
}

const getDetailCardClass = (detail: WmsPutawayTaskDetailResp) => {
  return {
    'detail-pending': detail.status === 'PENDING',
    'detail-in-progress': detail.status === 'IN_PROGRESS',
    'detail-completed': detail.status === 'COMPLETED'
  }
}

const getProgressColor = () => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 生命周期
onMounted(() => {
  loadTaskData()
  
  // 阻止页面滚动
  document.body.style.overflow = 'hidden'
})

onUnmounted(() => {
  // 恢复页面滚动
  document.body.style.overflow = 'auto'
})
</script>

<style scoped>
.mobile-putaway-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  overflow: hidden;
}

.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-center h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

.task-info-card {
  margin: 12px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-no {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.task-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item .label {
  color: #909399;
}

.detail-item .value {
  color: #303133;
  font-weight: 500;
}

.progress-section {
  margin-top: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.putaway-list {
  flex: 1;
  margin: 0 12px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #303133;
}

.detail-items {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.detail-item-card {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-item-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.detail-item-card:last-child {
  margin-bottom: 0;
}

.detail-pending {
  border-left: 4px solid #909399;
}

.detail-in-progress {
  border-left: 4px solid #e6a23c;
}

.detail-completed {
  border-left: 4px solid #67c23a;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-code {
  font-weight: 600;
  color: #303133;
}

.item-content {
  margin-bottom: 12px;
}

.item-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.item-info .info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.item-info .label {
  color: #909399;
}

.item-info .value {
  color: #303133;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.bottom-actions {
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
}

.bottom-actions .el-button {
  flex: 1;
}

.complete-summary {
  margin-bottom: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.summary-item .label {
  color: #606266;
}

.summary-item .value {
  color: #303133;
  font-weight: 600;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .mobile-header {
    padding: 8px 12px;
  }
  
  .task-info-card {
    margin: 8px;
    padding: 12px;
  }
  
  .putaway-list {
    margin: 0 8px;
  }
  
  .detail-item-card {
    padding: 8px;
  }
  
  .bottom-actions {
    padding: 8px 12px;
  }
}
</style>
