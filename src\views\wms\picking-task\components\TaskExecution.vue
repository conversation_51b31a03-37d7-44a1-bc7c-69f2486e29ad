<template>
  <el-dialog
    v-model="dialogVisible"
    title="拣货任务执行"
    width="90%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="task-execution-container">
      <!-- 任务信息 -->
      <el-card class="task-info-card" shadow="never">
        <template #header>
          <span>任务信息</span>
        </template>
        
        <el-row :gutter="16" v-if="taskData">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">任务编号:</span>
              <span class="value">{{ taskData.taskNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">出库单号:</span>
              <span class="value">{{ taskData.notificationNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">拣货策略:</span>
              <span class="value">{{ taskData.pickingStrategy }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">分配人员:</span>
              <span class="value">{{ taskData.assignedUserName }}</span>
            </div>
          </el-col>
        </el-row>
        
        <!-- 进度统计 -->
        <div class="progress-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总物料数" :value="totalItems" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已拣货数" :value="pickedItems" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="剩余数量" :value="remainingItems" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="完成率" :value="completionRate" suffix="%" />
            </el-col>
          </el-row>
        </div>
      </el-card>
      
      <!-- 拣货明细 -->
      <el-card class="picking-details-card" shadow="never">
        <template #header>
          <div class="section-header">
            <span>拣货明细</span>
            <div class="header-actions">
              <el-button
                type="primary"
                size="small"
                @click="handleBatchPick"
                :disabled="selectedDetails.length === 0"
              >
                批量拣货
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleScanMode"
              >
                扫码模式
              </el-button>
            </div>
          </div>
        </template>
        
        <VNTable
          ref="detailTableRef"
          :data="pickingDetails"
          :columns="detailColumns"
          :loading="loading"
          :show-operations="true"
          :operation-width="150"
          operation-fixed="right"
          row-key="id"
          :selection-type="'multiple'"
          @selection-change="handleDetailSelectionChange"
          show-index
        >
          <template #column-status="{ row }">
            <el-tag :type="getDetailStatusType(row.status)">
              {{ formatDetailStatus(row.status) }}
            </el-tag>
          </template>
          
          <template #column-pickedQty="{ row }">
            <el-input-number
              v-model="row.pickedQty"
              :min="0"
              :max="row.requiredQty"
              :precision="0"
              size="small"
              style="width: 100px"
              @change="handlePickedQtyChange(row)"
            />
          </template>
          
          <template #column-progress="{ row }">
            <el-progress
              :percentage="getDetailProgress(row)"
              :color="getDetailProgressColor(row)"
              :show-text="false"
              style="width: 80px"
            />
            <span style="margin-left: 8px; font-size: 12px;">
              {{ getDetailProgress(row) }}%
            </span>
          </template>
          
          <template #operation="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handlePickItem(row)"
              :disabled="row.status === 'PICKED'"
            >
              拣货
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleAdjustItem(row)"
              v-if="row.status === 'PICKED'"
            >
              调整
            </el-button>
          </template>
        </VNTable>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">关闭</el-button>
        <el-button
          type="success"
          @click="handleCompleteAll"
          :disabled="completionRate < 100"
        >
          完成任务
        </el-button>
      </div>
    </template>
    
    <!-- 拣货对话框 -->
    <PickingDialog
      ref="pickingDialogRef"
      @confirm="handlePickingConfirm"
      @cancel="handlePickingCancel"
    />
    
    <!-- 扫码对话框 -->
    <ScanDialog
      ref="scanDialogRef"
      @confirm="handleScanConfirm"
      @cancel="handleScanCancel"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElStatistic, ElTag, ElInputNumber, ElProgress, ElMessageBox } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import PickingDialog from './PickingDialog.vue'
import ScanDialog from './ScanDialog.vue'
import type { TableColumn } from '@/components/VNTable/types'
import type { WmsPickingTaskResp, WmsPickingTaskDetailResp } from '@/api/wms/pickingTask'
import { usePickingTaskStore } from '@/store/wms/pickingTask'

// 组件接口定义
interface TaskExecutionEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<TaskExecutionEmits>()

// Store
const pickingTaskStore = usePickingTaskStore()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const taskData = ref<WmsPickingTaskResp | null>(null)
const pickingDetails = ref<WmsPickingTaskDetailResp[]>([])
const selectedDetails = ref<WmsPickingTaskDetailResp[]>([])

// 计算属性
const totalItems = computed(() => {
  return pickingDetails.value.reduce((sum, detail) => sum + detail.requiredQty, 0)
})

const pickedItems = computed(() => {
  return pickingDetails.value.reduce((sum, detail) => sum + (detail.pickedQty || 0), 0)
})

const remainingItems = computed(() => {
  return totalItems.value - pickedItems.value
})

const completionRate = computed(() => {
  return totalItems.value > 0 ? Math.round((pickedItems.value / totalItems.value) * 100) : 0
})

// 表格列配置
const detailColumns = computed<TableColumn[]>(() => [
  { prop: 'itemCode', label: '物料编码', minWidth: 120 },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'sourceLocationCode', label: '源库位', width: 120 },
  { prop: 'batchNo', label: '批次号', width: 120 },
  { prop: 'requiredQty', label: '需求数量', width: 100 },
  { prop: 'pickedQty', label: '已拣数量', width: 120, slot: true },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'progress', label: '进度', width: 120, slot: true }
])

// 表格引用
const detailTableRef = ref<InstanceType<typeof VNTable>>()
const pickingDialogRef = ref<InstanceType<typeof PickingDialog>>()
const scanDialogRef = ref<InstanceType<typeof ScanDialog>>()

// 方法
const loadTaskData = async (taskId: number) => {
  loading.value = true
  try {
    const task = await pickingTaskStore.fetchDetail(taskId)
    taskData.value = task
    pickingDetails.value = task.details?.map(detail => ({
      ...detail,
      pickedQty: detail.pickedQty || 0,
      status: detail.pickedQty >= detail.requiredQty ? 'PICKED' : 'PENDING'
    })) || []
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  // 在open方法中会传入taskId
}

const handleClose = () => {
  taskData.value = null
  pickingDetails.value = []
  selectedDetails.value = []
}

const handleDetailSelectionChange = (rows: WmsPickingTaskDetailResp[]) => {
  selectedDetails.value = rows
}

const handlePickedQtyChange = (row: WmsPickingTaskDetailResp) => {
  // 更新状态
  row.status = row.pickedQty >= row.requiredQty ? 'PICKED' : 'PICKING'
}

const handlePickItem = (row: WmsPickingTaskDetailResp) => {
  pickingDialogRef.value?.open(row)
}

const handlePickingConfirm = (data: any) => {
  // 更新拣货数量
  const detail = pickingDetails.value.find(d => d.id === data.detailId)
  if (detail) {
    detail.pickedQty = data.pickedQty
    detail.status = detail.pickedQty >= detail.requiredQty ? 'PICKED' : 'PICKING'
  }
  ElMessage.success('拣货成功')
}

const handlePickingCancel = () => {
  // 拣货取消处理
}

const handleAdjustItem = (row: WmsPickingTaskDetailResp) => {
  // 调整拣货数量
  pickingDialogRef.value?.open(row, 'adjust')
}

const handleBatchPick = () => {
  if (selectedDetails.value.length === 0) {
    ElMessage.warning('请选择要拣货的明细')
    return
  }
  
  // 批量拣货逻辑
  ElMessage.info('批量拣货功能开发中...')
}

const handleScanMode = () => {
  scanDialogRef.value?.open()
}

const handleScanConfirm = (data: any) => {
  // 扫码拣货逻辑
  ElMessage.success('扫码拣货成功')
}

const handleScanCancel = () => {
  // 扫码取消处理
}

const handleCompleteAll = async () => {
  try {
    await ElMessageBox.confirm('确定要完成整个拣货任务吗？', '确认完成', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    if (!taskData.value) return
    
    // 提交拣货结果
    await pickingTaskStore.completeTask(taskData.value.id, '拣货完成')
    
    ElMessage.success('任务完成')
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('完成任务失败')
      console.error(error)
    }
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const getDetailStatusType = (status: string) => {
  switch (status) {
    case 'PICKED':
      return 'success'
    case 'PICKING':
      return 'warning'
    case 'PENDING':
      return 'info'
    default:
      return 'info'
  }
}

const formatDetailStatus = (status: string) => {
  switch (status) {
    case 'PICKED':
      return '已拣货'
    case 'PICKING':
      return '拣货中'
    case 'PENDING':
      return '待拣货'
    default:
      return '未知'
  }
}

const getDetailProgress = (row: WmsPickingTaskDetailResp) => {
  return row.requiredQty > 0 ? Math.round(((row.pickedQty || 0) / row.requiredQty) * 100) : 0
}

const getDetailProgressColor = (row: WmsPickingTaskDetailResp) => {
  const progress = getDetailProgress(row)
  if (progress < 50) return '#f56c6c'
  if (progress < 100) return '#e6a23c'
  return '#67c23a'
}

// 暴露方法
const open = (taskId: number) => {
  dialogVisible.value = true
  loadTaskData(taskId)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.task-execution-container {
  padding: 16px 0;
}

.task-info-card,
.picking-details-card {
  margin-bottom: 16px;
}

.task-info-card :deep(.el-card__header),
.picking-details-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.progress-stats {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
