package service

import (
	"context"
	"fmt"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// updateDetailAllocatedQty 更新出库明细的分配数量
func (s *wmsInventoryAllocationServiceImpl) updateDetailAllocatedQty(ctx context.Context, txRepoMgr *repository.RepositoryManager, outboundDetailID uint) error {
	allocationRepo := txRepoMgr.GetWmsInventoryAllocationRepository()
	detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()

	// 计算该明细的总分配数量
	allocations, err := allocationRepo.FindByOutboundDetailID(ctx, outboundDetailID)
	if err != nil {
		return fmt.Errorf("查询分配记录失败: %w", err)
	}

	totalAllocatedQty := float64(0)
	for _, allocation := range allocations {
		if allocation.Status == string(entity.AllocationStatusAllocated) ||
			allocation.Status == string(entity.AllocationStatusPicked) {
			totalAllocatedQty += allocation.AllocatedQty
		}
	}

	// 更新明细的分配数量
	if err := detailRepo.UpdateAllocatedQty(ctx, outboundDetailID, totalAllocatedQty); err != nil {
		return fmt.Errorf("更新明细分配数量失败: %w", err)
	}

	return nil
}

// updateDetailPickedQty 更新出库明细的拣货数量
func (s *wmsInventoryAllocationServiceImpl) updateDetailPickedQty(ctx context.Context, txRepoMgr *repository.RepositoryManager, outboundDetailID uint) error {
	allocationRepo := txRepoMgr.GetWmsInventoryAllocationRepository()
	detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()

	// 计算该明细的总拣货数量
	allocations, err := allocationRepo.FindByOutboundDetailID(ctx, outboundDetailID)
	if err != nil {
		return fmt.Errorf("查询分配记录失败: %w", err)
	}

	totalPickedQty := float64(0)
	for _, allocation := range allocations {
		if allocation.Status == string(entity.AllocationStatusPicked) {
			totalPickedQty += allocation.PickedQty
		}
	}

	// 更新明细的拣货数量
	if err := detailRepo.UpdatePickedQty(ctx, outboundDetailID, totalPickedQty); err != nil {
		return fmt.Errorf("更新明细拣货数量失败: %w", err)
	}

	return nil
}

// findAvailableInventories 根据策略查找可用库存
func (s *wmsInventoryAllocationServiceImpl) findAvailableInventories(
	ctx context.Context,
	txRepoMgr *repository.RepositoryManager,
	detail *entity.WmsOutboundNotificationDetail,
	strategy string,
	requiredQty float64,
) ([]*InventoryInfo, error) {
	// TODO: 实现库存查找逻辑
	// 1. 根据物料ID查找库存
	// 2. 过滤可用库存（状态正常、数量充足等）
	// 3. 根据策略排序（FIFO、LIFO、FEFO、NEAREST等）
	// 4. 返回排序后的库存列表

	// 临时实现，返回空列表
	return []*InventoryInfo{}, nil
}

// InventoryInfo 库存信息结构
type InventoryInfo struct {
	InventoryID    uint
	LocationID     uint
	AvailableQty   float64
	BatchNo        *string
	ProductionDate *string
	ExpiryDate     *string
	Quality        string
	Priority       int
}

// performAllocation 执行分配
func (s *wmsInventoryAllocationServiceImpl) performAllocation(
	ctx context.Context,
	txRepoMgr *repository.RepositoryManager,
	detail *entity.WmsOutboundNotificationDetail,
	inventories []*InventoryInfo,
	requiredQty float64,
	req *dto.WmsInventoryAllocationAutoReq,
) ([]*entity.WmsInventoryAllocation, error) {

	var allocations []*entity.WmsInventoryAllocation
	remainingQty := requiredQty
	maxAllocations := 10 // 默认最大分配记录数

	if req.MaxAllocations != nil && *req.MaxAllocations > 0 {
		maxAllocations = *req.MaxAllocations
	}

	// 强制从上下文获取账套ID和用户ID
	accountBookID, err := s.GetAccountBookIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
	}

	userID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
	}

	for i, inventory := range inventories {
		if remainingQty <= 0 || i >= maxAllocations {
			break
		}

		// 计算本次分配数量
		allocateQty := remainingQty
		if allocateQty > inventory.AvailableQty {
			allocateQty = inventory.AvailableQty
		}

		// 创建分配记录
		allocation := &entity.WmsInventoryAllocation{
			OutboundDetailID:   detail.ID,
			InventoryID:        inventory.InventoryID,
			AllocatedQty:       allocateQty,
			PickedQty:          0,
			Status:             string(entity.AllocationStatusAllocated),
			AllocationTime:     time.Now(),
			AllocationStrategy: &req.AllocationStrategy,
			AllocationReason:   &[]string{"自动分配"}[0],
		}
		allocation.AccountBookID = uint(accountBookID)
		allocation.CreatedBy = uint(userID)

		allocations = append(allocations, allocation)
		remainingQty -= allocateQty
	}

	// 批量创建分配记录
	if len(allocations) > 0 {
		allocationRepo := txRepoMgr.GetWmsInventoryAllocationRepository()
		if err := allocationRepo.BatchCreate(ctx, allocations); err != nil {
			return nil, fmt.Errorf("批量创建分配记录失败: %w", err)
		}
	}

	return allocations, nil
}

// fillExtendedInfo 填充扩展信息
func (s *wmsInventoryAllocationServiceImpl) fillExtendedInfo(ctx context.Context, vo *vo.WmsInventoryAllocationVO) {
	// TODO: 填充物料信息、库位信息等扩展信息
}

// CheckAvailability 检查库存可用性
func (s *wmsInventoryAllocationServiceImpl) CheckAvailability(ctx context.Context, req *dto.WmsInventoryAvailabilityCheckReq) (*vo.WmsInventoryAvailabilityVO, error) {
	// TODO: 实现库存可用性检查
	result := &vo.WmsInventoryAvailabilityVO{
		ItemID:           req.ItemID,
		RequiredQty:      req.RequiredQty,
		AvailableQty:     0,
		AllocatedQty:     0,
		ReservedQty:      0,
		ShortageQty:      req.RequiredQty,
		CanAllocate:      false,
		AllocationResult: "NONE",
	}

	return result, nil
}

// BatchCheckAvailability 批量检查库存可用性
func (s *wmsInventoryAllocationServiceImpl) BatchCheckAvailability(ctx context.Context, req *dto.WmsInventoryAvailabilityBatchCheckReq) ([]*vo.WmsInventoryAvailabilityVO, error) {
	var results []*vo.WmsInventoryAvailabilityVO

	for _, item := range req.Items {
		result, err := s.CheckAvailability(ctx, &item)
		if err != nil {
			// 记录错误但继续处理其他物料
			result = &vo.WmsInventoryAvailabilityVO{
				ItemID:           item.ItemID,
				RequiredQty:      item.RequiredQty,
				AvailableQty:     0,
				CanAllocate:      false,
				AllocationResult: "ERROR",
			}
		}
		results = append(results, result)
	}

	return results, nil
}

// GetPage 获取库存分配分页数据
func (s *wmsInventoryAllocationServiceImpl) GetPage(ctx context.Context, req *dto.WmsInventoryAllocationQueryReq) (*vo.PageResult[vo.WmsInventoryAllocationListVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryAllocationRepository()

	// 调用Repository的分页查询
	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存分配分页数据失败").WithCause(err)
	}

	// 转换为VO
	voResult := &vo.PageResult[vo.WmsInventoryAllocationListVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsInventoryAllocationListVO, len(pageResult.List.([]*entity.WmsInventoryAllocation))),
	}

	allocations := pageResult.List.([]*entity.WmsInventoryAllocation)
	for i, allocation := range allocations {
		listVO := &vo.WmsInventoryAllocationListVO{}
		copier.Copy(listVO, allocation)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		voResult.List[i] = listVO
	}

	return voResult, nil
}

// fillListExtendedInfo 填充列表扩展信息
func (s *wmsInventoryAllocationServiceImpl) fillListExtendedInfo(ctx context.Context, vo *vo.WmsInventoryAllocationListVO) {
	// TODO: 填充物料信息、库位信息等扩展信息
}

// GetByID 根据ID获取库存分配
func (s *wmsInventoryAllocationServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsInventoryAllocationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryAllocationRepository()

	allocation, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库存分配记录不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存分配记录失败").WithCause(err)
	}

	voResult := &vo.WmsInventoryAllocationVO{}
	copier.Copy(voResult, allocation)

	// 填充扩展信息
	s.fillExtendedInfo(ctx, voResult)

	return voResult, nil
}

// Update 更新库存分配
func (s *wmsInventoryAllocationServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsInventoryAllocationUpdateReq) (*vo.WmsInventoryAllocationVO, error) {
	var voResult *vo.WmsInventoryAllocationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryAllocationRepository()

		// 获取当前记录
		current, err := repo.FindByID(ctx, id)
		if err != nil {
			return fmt.Errorf("查询记录失败: %w", err)
		}

		// 检查状态是否允许修改
		if current.Status != string(entity.AllocationStatusAllocated) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已分配状态的记录才能修改")
		}

		// 更新字段
		updates := make(map[string]interface{})
		if req.AllocatedQty > 0 {
			updates["allocated_qty"] = req.AllocatedQty
		}
		if req.AllocationStrategy != nil {
			updates["allocation_strategy"] = *req.AllocationStrategy
		}
		if req.AllocationReason != nil {
			updates["allocation_reason"] = *req.AllocationReason
		}
		if req.Remark != nil {
			updates["remark"] = *req.Remark
		}

		if len(updates) > 0 {
			// 强制从上下文获取用户ID
			userID, err := s.GetUserIDFromContext(ctx)
			if err != nil {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
			}
			updates["updated_by"] = uint(userID)

			if err := repo.GetDB(ctx).Model(current).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新分配记录失败: %w", err)
			}
		}

		// 如果更新了分配数量，需要更新明细的分配数量
		if req.AllocatedQty > 0 {
			if err := s.updateDetailAllocatedQty(ctx, txRepoMgr, current.OutboundDetailID); err != nil {
				return fmt.Errorf("更新明细分配数量失败: %w", err)
			}
		}

		// 重新查询数据返回
		var result entity.WmsInventoryAllocation
		if err := repo.GetDB(ctx).First(&result, id).Error; err != nil {
			return fmt.Errorf("查询更新后数据失败: %w", err)
		}

		copier.Copy(&voResult, &result)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return voResult, nil
}

// Delete 删除库存分配
func (s *wmsInventoryAllocationServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryAllocationRepository()

		allocation, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "库存分配记录不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存分配记录失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if allocation.Status == string(entity.AllocationStatusPicked) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已拣货的分配记录不能删除")
		}

		// 删除分配记录
		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除库存分配记录失败").WithCause(err)
		}

		// 更新明细的分配数量
		if err := s.updateDetailAllocatedQty(ctx, txRepoMgr, allocation.OutboundDetailID); err != nil {
			return fmt.Errorf("更新明细分配数量失败: %w", err)
		}

		return nil
	})
}

// GetByInventoryID 根据库存ID获取分配记录
func (s *wmsInventoryAllocationServiceImpl) GetByInventoryID(ctx context.Context, inventoryID uint) ([]*vo.WmsInventoryAllocationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryAllocationRepository()

	allocations, err := repo.FindByInventoryID(ctx, inventoryID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存分配记录失败").WithCause(err)
	}

	var results []*vo.WmsInventoryAllocationVO
	for _, allocation := range allocations {
		vo := &vo.WmsInventoryAllocationVO{}
		copier.Copy(vo, allocation)

		// 填充扩展信息
		s.fillExtendedInfo(ctx, vo)

		results = append(results, vo)
	}

	return results, nil
}

// GetAllocationSummary 获取分配汇总
func (s *wmsInventoryAllocationServiceImpl) GetAllocationSummary(ctx context.Context, outboundDetailID uint) (*vo.WmsInventoryAllocationSummaryVO, error) {
	// TODO: 实现分配汇总逻辑
	// 需要先定义正确的VO结构体

	result := &vo.WmsInventoryAllocationSummaryVO{}
	return result, nil
}
