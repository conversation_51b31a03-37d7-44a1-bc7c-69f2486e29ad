package entity

import (
	"time"

	"gorm.io/gorm"
)

// 出库通知单状态枚举
type WmsOutboundNotificationStatus string

const (
	OutboundStatusDraft      WmsOutboundNotificationStatus = "DRAFT"      // 草稿
	OutboundStatusApproved   WmsOutboundNotificationStatus = "APPROVED"   // 已审核
	OutboundStatusAllocated  WmsOutboundNotificationStatus = "ALLOCATED"  // 已分配
	OutboundStatusPicking    WmsOutboundNotificationStatus = "PICKING"    // 拣货中
	OutboundStatusPicked     WmsOutboundNotificationStatus = "PICKED"     // 已拣货
	OutboundStatusPacked     WmsOutboundNotificationStatus = "PACKED"     // 已打包
	OutboundStatusShipped    WmsOutboundNotificationStatus = "SHIPPED"    // 已发运
	OutboundStatusDelivered  WmsOutboundNotificationStatus = "DELIVERED"  // 已送达
	OutboundStatusCancelled  WmsOutboundNotificationStatus = "CANCELLED"  // 已取消
)

// WmsOutboundNotification 对应数据库中的 wms_outbound_notification 表
// 存储出库通知单头信息
type WmsOutboundNotification struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	NotificationNo   string     `gorm:"column:notification_no;size:50;not null;uniqueIndex:idx_outbound_no_account;comment:出库通知单号" json:"notificationNo"`
	ClientID         uint       `gorm:"column:client_id;not null;comment:委托客户ID" json:"clientId"`
	ClientOrderNo    *string    `gorm:"column:client_order_no;size:100;comment:客户订单号" json:"clientOrderNo"`
	WarehouseID      uint       `gorm:"column:warehouse_id;not null;comment:出库仓库ID" json:"warehouseId"`
	RequiredShipDate *time.Time `gorm:"column:required_ship_date;type:date;comment:要求发货日期" json:"-"`
	Priority         int        `gorm:"column:priority;not null;default:5;comment:优先级(1-10)" json:"priority"`
	Status           string     `gorm:"column:status;type:varchar(20);not null;default:'DRAFT';comment:状态" json:"status"`

	// 收货人信息
	ConsigneeName    string  `gorm:"column:consignee_name;size:100;not null;comment:收货人姓名" json:"consigneeName"`
	ConsigneePhone   *string `gorm:"column:consignee_phone;size:50;comment:收货人电话" json:"consigneePhone"`
	ConsigneeAddress *string `gorm:"column:consignee_address;size:500;comment:收货人地址" json:"consigneeAddress"`

	// 运输信息
	CarrierID      *uint   `gorm:"column:carrier_id;comment:承运商ID" json:"carrierId"`
	ShippingMethod *string `gorm:"column:shipping_method;size:50;comment:运输方式" json:"shippingMethod"`

	Remark *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`

	// 扩展显示字段
	RequiredShipDateStr *string `gorm:"-" json:"requiredShipDate"` // 要求发货日期字符串格式

	// 关联字段 (反向关联)
	Details      []WmsOutboundNotificationDetail `gorm:"foreignKey:NotificationID;references:ID" json:"details,omitempty"`      // 关联的明细行列表
	PickingTasks []WmsPickingTask                `gorm:"foreignKey:NotificationID;references:ID" json:"pickingTasks,omitempty"` // 关联的拣货任务列表
}

// TableName 指定数据库表名方法
func (WmsOutboundNotification) TableName() string {
	return "wms_outbound_notification"
}

// BeforeCreate GORM钩子：创建前处理
func (w *WmsOutboundNotification) BeforeCreate(tx *gorm.DB) error {
	// 处理日期字段的字符串转换
	if w.RequiredShipDateStr != nil && *w.RequiredShipDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.RequiredShipDateStr); err == nil {
			w.RequiredShipDate = &parsedDate
		}
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前处理
func (w *WmsOutboundNotification) BeforeUpdate(tx *gorm.DB) error {
	// 处理日期字段的字符串转换
	if w.RequiredShipDateStr != nil && *w.RequiredShipDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.RequiredShipDateStr); err == nil {
			w.RequiredShipDate = &parsedDate
		}
	}
	return nil
}

// AfterFind GORM钩子：查询后处理
func (w *WmsOutboundNotification) AfterFind(tx *gorm.DB) error {
	// 将日期字段转换为字符串格式用于前端显示
	if w.RequiredShipDate != nil {
		dateStr := w.RequiredShipDate.Format("2006-01-02")
		w.RequiredShipDateStr = &dateStr
	}
	return nil
}
