import type { DrawerProps } from 'element-plus';

// 继承 ElDrawer 的部分 Props，并添加 v-model 控制
// 排除 'modelValue', 'update:modelValue', 'close'
export interface VNDrawerProps extends Partial<Omit<DrawerProps, 'modelValue' | 'close'>> {
  modelValue: boolean; // 控制抽屉显示与隐藏 (v-model)
  showConfirmButton?: boolean; // 是否显示默认的确认按钮 (如果使用 footer slot 则无效)
  showCancelButton?: boolean; // 是否显示默认的取消按钮 (如果使用 footer slot 则无效)
  confirmButtonText?: string; // 确认按钮文字
  cancelButtonText?: string; // 取消按钮文字
  confirmButtonLoading?: boolean; // 确认按钮加载状态
  // 可以根据需要添加更多自定义 Props, 例如 footerAlign
  footerAlign?: 'left' | 'center' | 'right'; // 默认底部按钮对齐方式
}

// 定义组件 Emits
export interface VNDrawerEmits {
  (e: 'update:modelValue', value: boolean): void; // v-model 更新
  (e: 'open'): void; // Drawer 打开的回调
  (e: 'opened'): void; // Drawer 打开动画结束时的回调
  (e: 'close'): void; // Drawer 关闭的回调 (点击关闭按钮或遮罩层时触发)
  (e: 'closed'): void; // Drawer 关闭动画结束时的回调
  (e: 'confirm'): void; // 点击默认确认按钮时触发
  (e: 'cancel'): void; // 点击默认取消按钮时触发
} 