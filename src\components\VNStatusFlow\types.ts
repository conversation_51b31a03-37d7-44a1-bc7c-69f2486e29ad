/**
 * 全局状态流程管理组件类型定义
 */

// 状态选项配置
export interface StatusOption {
  // 状态值
  value: string
  // 状态标签
  label: string
  // 状态颜色
  color: string
  // 状态背景色
  bgColor: string
  // 状态图标
  icon?: string
  // 状态描述
  description?: string
}

// 状态转换规则
export interface StatusTransition {
  // 源状态
  from: string
  // 目标状态列表
  to: string[]
  // 转换条件
  condition?: (data: any) => boolean
  // 转换权限
  permission?: string
  // 是否需要备注
  requireRemark?: boolean
}

// 显示配置
export interface DisplayConfig {
  // 是否显示操作按钮
  showActions: boolean
  // 是否显示进度
  showProgress: boolean
  // 是否允许手动转换
  allowManualTransition: boolean
  // 是否显示状态描述
  showDescription?: boolean
  // 是否显示状态图标
  showIcon?: boolean
  // 节点大小
  nodeSize?: 'small' | 'medium' | 'large'
  // 连接线样式
  lineStyle?: 'solid' | 'dashed' | 'dotted'
}

// 权限配置
export interface PermissionConfig {
  [transitionKey: string]: string
}

// 状态流程配置
export interface StatusFlowConfig {
  // 业务类型标识
  businessType: string
  // 状态选项列表
  statusOptions: StatusOption[]
  // 状态转换规则
  statusTransitions: StatusTransition[]
  // 显示配置
  displayConfig: DisplayConfig
  // 操作权限配置
  permissionConfig: PermissionConfig
  // 状态转换中文标签
  transitionLabels?: Record<string, string>
  // 按钮类型映射（按状态值）
  buttonTypes?: Record<string, 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger'>
  // 按钮图标映射（按状态值，对应 Element Plus 图标名称，如 'Promotion'）
  buttonIcons?: Record<string, string>
  // API配置
  apiConfig?: {
    // 状态变更API
    changeStatusApi?: string
    // 权限检查API
    checkPermissionApi?: string
  }
}

// 组件Props
export interface VNStatusFlowProps {
  // 业务类型
  businessType: string
  // 当前状态
  currentStatus: string
  // 业务数据ID
  businessId?: string | number
  // 业务数据
  businessData?: Record<string, any>
  // 只读模式
  readonly?: boolean
  // 紧凑模式
  compact?: boolean
  // 显示模式
  mode?: 'horizontal' | 'vertical'
  // 自定义配置
  config?: Partial<StatusFlowConfig>
  // 是否显示标题
  showTitle?: boolean
  // 自定义标题
  title?: string
  /** 是否允许折叠显示节点 */
  collapsible?: boolean
  /** 初始是否折叠（收起） */
  defaultCollapsed?: boolean
}

// 组件Events
export interface VNStatusFlowEmits {
  // 状态变更事件
  statusChange: [newStatus: string, oldStatus: string, remark?: string]
  // 状态变更前事件
  beforeStatusChange: [newStatus: string, oldStatus: string]
  // 状态变更后事件
  afterStatusChange: [newStatus: string, oldStatus: string]
  // 权限检查失败事件
  permissionDenied: [transition: string, permission: string]
}

// 状态变更请求
export interface StatusChangeRequest {
  // 业务ID
  businessId: string | number
  // 新状态
  newStatus: string
  // 旧状态
  oldStatus: string
  // 备注
  remark?: string
  // 额外数据
  extraData?: Record<string, any>
}

// 状态变更响应
export interface StatusChangeResponse {
  // 是否成功
  success: boolean
  // 错误消息
  message?: string
  // 新状态
  newStatus?: string
  // 响应数据
  data?: any
}

// 状态节点Props
export interface StatusNodeProps {
  // 状态配置
  status: StatusOption
  // 是否激活
  active: boolean
  // 是否完成
  completed: boolean
  // 是否禁用
  disabled: boolean
  // 节点大小
  size?: 'small' | 'medium' | 'large'
  // 显示模式
  mode?: 'horizontal' | 'vertical'
}

// 状态连接线Props
export interface StatusLineProps {
  // 是否完成
  completed: boolean
  // 线条样式
  style?: 'solid' | 'dashed' | 'dotted'
  // 显示模式
  mode?: 'horizontal' | 'vertical'
}

// 状态操作Props
export interface StatusActionsProps {
  // 可用转换
  availableTransitions: StatusTransition[]
  // 当前状态
  currentStatus: string
  // 只读模式
  readonly: boolean
  // 权限检查函数
  checkPermission?: (permission: string) => boolean
  // 获取中文标签函数
  getTransitionLabel: (from: string, to: string) => string
  // 获取按钮类型函数
  getButtonType: (status: string) => 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger'
  // 获取按钮图标名称函数
  getButtonIconName: (status: string) => string | undefined
}

// Hook返回类型
export interface UseStatusFlowReturn {
  // 当前配置
  config: Ref<StatusFlowConfig>
  // 当前状态
  currentStatus: Ref<string>
  // 状态列表
  statusList: ComputedRef<StatusOption[]>
  // 可用转换
  availableTransitions: ComputedRef<StatusTransition[]>
  // 是否可以转换到指定状态
  canTransitionTo: (status: string) => boolean
  // 判断状态是否已完成
  isStatusCompleted: (status: string) => boolean
  // 获取状态配置
  getStatusConfig: (status: string) => StatusOption | undefined
  // 执行状态转换
  changeStatus: (newStatus: string, remark?: string, businessData?: Record<string, any>) => Promise<StatusChangeResponse>
  // 重置状态
  resetStatus: (status: string) => void
  // 更新配置
  updateConfig: (newConfig: Partial<StatusFlowConfig>) => void
  // 获取状态转换的权限
  getTransitionPermission: (from: string, to: string) => string | undefined
  // 获取状态转换标签
  getTransitionLabel: (from: string, to: string) => string
  // 获取按钮类型
  getButtonType: (status: string) => 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger'
  // 获取按钮图标名称
  getButtonIconName: (status: string) => string | undefined
  // 验证状态转换
  validateTransition: (from: string, to: string, businessData?: Record<string, any>) => { valid: boolean; message?: string }
}

// Vue3相关类型导入
import type { Ref, ComputedRef } from 'vue' 