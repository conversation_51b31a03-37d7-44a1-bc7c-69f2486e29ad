# WMS 上架任务 PC 端前端功能实现执行计划 - ✅ [已完成]

本文档旨在规划 WMS **上架任务** 模块 PC 端前端功能的完整实现。此功能旨在为管理人员提供一个监控、分配和（模拟）执行上架任务的界面。

我们将继续沿用**入库通知单/收货记录**模块的架构和代码风格，以确保项目整体的一致性。

## 1. 核心目标 - ✅ [已完成]

- ~~**任务可视化**: 以列表形式清晰展示所有上架任务，包括其状态、优先级、关联单据和负责人。~~
- ~~**任务分配**: 实现将"待处理"状态的任务分配给指定仓库操作员的功能。~~
- ~~**模拟执行**: 提供一个界面，让 PC 端用户可以查看任务详情、建议库位，并能将任务标记为"完成"，以模拟移动端的实际操作。~~
- ~~**流程串联**: 在"收货记录"界面提供入口，一键生成上架任务。~~

## 2. 后端 API 分析 - ✅ [已完成]

根据 `wms_putaway_task_controller_impl.go` 和相关 DTO/VO 文件，我们明确以下关键 API：

- `GET /api/v1/wms/putaway-tasks`: 分页查询上架任务列表。
- `GET /api/v1/wms/putaway-tasks/{id}`: 获取单个任务的详细信息，包含明细。
- `POST /api/v1/wms/putaway-tasks/from-receiving/{receivingId}`: **从收货记录创建上架任务**。这是任务创建的主要入口。
- `PUT /api/v1/wms/putaway-tasks/{id}/assign`: **将任务分配给用户**。这是核心交互之一。
- `PUT /api/v1/wms/putaway-tasks/{id}/complete`: **将整个任务标记为完成**。这是 PC 端模拟执行的核心。
- `DELETE /api/v1/wms/putaway-tasks/{id}`: 删除任务（仅限特定状态）。

## 3. 实现步骤 - ✅ [已完成]

### 第一阶段：基础架构搭建 - ✅ [已完成]

此阶段将创建所有必需的文件，作为后续功能开发的骨架。

1.  **API 层 (`/src/api/wms/putawayTask.ts`)**: - ✅ [已完成]

    - ~~创建新文件 `putawayTask.ts`。~~
    - ~~根据后端 DTO 和 VO 定义 `WmsPutawayTaskQueryReq`, `WmsPutawayTaskResp` 等所有请求和响应的 TypeScript 类型。~~
    - ~~封装 `getPutawayTaskPage`, `getPutawayTaskDetail`, `createPutawayTaskFromReceiving`, `assignUserToTask`, `completePutawayTask`, `deletePutawayTask` 等 API 请求函数。~~

2.  **类型定义层 (`/src/types/wms/putawayTask.ts`)**: - ✅ [已完成]

    - ~~创建新文件 `putawayTask.ts`。~~
    - ~~定义与 UI 相关的类型，如 `PutawayTaskFormData`, `DetailRowData`, `SearchFormData` 等，用于组件和 Store。~~

3.  **状态管理层 (`/src/store/wms/putawayTask.ts`)**: - ✅ [已完成]

    - ~~创建新文件 `putawayTask.ts`。~~
    - ~~沿用现有 Store 模式，管理上架任务列表的数据、加载状态、搜索条件、分页信息以及详情弹窗的表单数据。~~

4.  **视图层 (`/src/views/wms/putaway-task/index.vue`)**: - ✅ [已完成]
    - ~~创建 `putaway-task` 目录及 `index.vue` 主视图文件。~~

### 第二阶段：核心功能实现 - ✅ [已完成]

此阶段将基于已创建的骨架文件，完成界面的开发。

1.  **列表页面 (`index.vue`)** - ✅ [已完成]

    - **搜索区**: ~~实现基于 `WmsPutawayTaskQueryReq` 的搜索功能，包含字段：任务单号、收货单号、状态、分配人等。~~
    - **表格区**:
      - ~~展示上架任务列表，数据源为 `WmsPutawayTaskSimpleVO`。~~
      - ~~关键列：任务单号、关联收货单号、状态、优先级、分配给、创建时间、完成时间。~~
      - **行操作按钮**:
        - ~~`查看`: 打开只读的详情弹窗。~~
        - ~~`分配`: （当任务状态为 `PENDING` 时可用）打开一个小型对话框，用于选择用户并分配任务。~~
        - ~~`执行`: （当任务状态为 `ASSIGNED` 或 `IN_PROGRESS` 时可用）打开详情弹窗，并提供"完成任务"按钮。~~
        - ~~`删除`: （当任务状态为 `PENDING` 时可用）删除任务。~~
    - **分页组件**: ~~集成标准分页。~~

2.  **详情/执行弹窗 (`index.vue` 内的 `el-dialog`)** - ✅ [已完成]
    - **模式**: ~~支持 `view` (查看) 和 `execute` (执行) 两种模式。~~
    - **主信息区**: ~~展示任务的头信息，如任务号、状态、优先级。~~
    - **明细表格**:
      - ~~这是一个**只读**的表格，用于展示上架任务的明细。~~
      - ~~关键列：行号、物料 SKU、物料名称、需上架数量、源库位、**建议库位**。~~
      - **注意**: ~~PC 端不实现行级别的库位/数量修改，这部分是移动端 PDA 的功能。PC 端只做展示和整体状态变更。~~
    - **底部按钮**:
      - ~~`关闭` 按钮。~~
      - ~~在 `execute` 模式下，额外显示一个 `确认完成` 按钮，点击后调用 `completePutawayTask` API，将整个任务标记为完成。~~

### 第三阶段：流程集成与路由 - ✅ [已完成]

1.  **流程打通**: - ✅ [已完成]

    - ~~修改 `frontend/src/views/wms/receiving-record/index.vue` 文件。~~
    - ~~在收货记录列表的行操作中，增加一个 `生成上架任务` 的按钮。~~
    - ~~该按钮仅在收货记录状态为 `COMPLETED` 时显示。~~
    - ~~点击按钮后，调用 `createPutawayTaskFromReceiving` API，并给出成功提示。~~

2.  **路由与菜单**: - ✅ [已完成]
    - ~~修改 `frontend/src/router/index.ts` 文件。~~
    - ~~在 `WMS` 模块的子路由中，添加上架任务页面的路由配置，路径为 `/wms/putaway-task`。~~

### 第四阶段：联调与优化 - ✅ [已完成]

1.  ~~与后端进行接口联调，确保数据交互无误。~~
2.  ~~测试所有按钮的可用性、状态流转的正确性。~~
3.  ~~优化界面交互，确保用户体验流畅。~~

---

~~计划制定完毕，我将立即开始执行。~~
**所有计划任务均已完成。**
