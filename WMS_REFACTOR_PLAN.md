# WMS 模块重构方案 (V2)

## 一、 总体目标与核心原则

1.  **解决所有编译错误和 Linter 问题：** 确保代码库的健康度和可维护性。
2.  **对齐业务需求：** 使模块功能完全符合 `WMS.md` 文档中定义的业务逻辑和数据模型。
3.  **统一 API 设计：** 废弃当前 V1/V2 的混乱实现，整合为一套命名清晰、基于指针 DTO/VO 的 API 接口。所有 DTO/VO 命名将不包含 `V2` 后缀。
4.  **简化数据模型：** 在 Entity 层 (`wms_location_entity.go`) 中，将所有 `sql.NullString`, `sql.NullFloat64` 等可空类型，统一修改为指针类型 (`*string`, `*float64`)，与 DTO 层保持一致，彻底消除繁琐且易错的类型转换。

## 二、 详细重构步骤（从底层到上层）

### 第 1 步：数据模型层重构 (Entity, DTO, VO)

#### 1.1. 重构 Entity (`wms_location_entity.go`) - [x] 已完成

- **目标：** 使用指针代替 `sql.Null*`，并对齐项目标准实体结构。
- **操作：**
  - **[修正]** 确认了 `AccountBookEntity` 为项目标准，予以保留。
  - **[完成]** 将 `Name`, `Remark`, `Address` 等所有 `sql.NullString` 字段修改为 `*string` 类型。
  - **[完成]** 将 `MaxWeightKg`, `MaxVolumeM3` 等所有 `sql.NullFloat64` 字段修改为 `*float64` 类型。
  - **[完成]** 将 `MaxPallets`, `MaxItemUnits` 等所有 `sql.NullInt64` 字段修改为 `*int64` 类型。

#### 1.2. 重构 DTO (`wms_location_dto.go`) - [x] 已完成

- **目标：** 定义清晰的、用于数据传入（请求体）的结构体。
- **操作：**
  - **[完成]** 废弃了所有现存的 V1/V2 DTO 结构。
  - **[完成]** 重新定义了一套统一的、基于指针的 DTO (`Create`, `Update`, `Query`)。
  - **[完成]** 确保了 `Type` 字段的 `oneof` 校验规则与 Entity 定义完全一致。

#### 1.3. 定义 VO (`wms_location_vo.go`) - [x] 已完成

- **目标：** 定义清晰的、用于数据传出（响应体）的结构体，将内部实体与外部表示解耦。
- **操作：**
  - **[完成]** 创建了新文件 `internal/model/vo/wms_location_vo.go`。
  - **[完成]** 定义了 `WmsLocationVO`（详细视图）。
  - **[完成]** 定义了 `WmsLocationTreeVO`（层级树视图）。
  - **[完成]** 定义了 `WmsWarehouseSimpleVO`（简化列表视图）。

### 第 2 步：仓库层重构 (Repository) - [x] 已完成

#### 2.1. 重构 Repository (`wms_location_repository_impl.go`) - [x] 已完成

- **目标：** 使 Repository 层与新的数据模型和业务需求对齐。
- **操作：**
  - **[完成]** 废弃了所有旧的、不再需要的方法。
  - **[完成]** 基于 `BaseRepository` 的标准 `FindByPage` 重写了 `GetPage`。
  - **[完成]** 基于 `FindByCondition` 重写了 `GetWarehouses` 和 `GetLocationTree`。
  - **[完成]** 基于 `Exists` 重写了 `IsCodeExist`。

### 第 3 步：服务层重构 (Service) - [x] 已完成

#### 3.1. 重构 Service (`wms_location_service_impl.go`) - [x] 已完成

- **目标：** 实现 `WMS.md` 中定义的正确业务逻辑，解决所有编译错误和逻辑缺陷。
- **操作：**
  - **[完成]** 在 `wms_location_service_impl.go` 中定义了清晰的服务接口 `WmsLocationService`。
  - **[完成]** 重构了 `wmsLocationServiceImpl`，使其嵌入 `BaseServiceImpl` 并通过 `ServiceManager` 获取依赖。
  - **[完成]** 在事务中实现了 `Create` 和 `Update` 方法，并加入了完整的业务校验逻辑。
  - **[完成]** 实现了 `Delete`, `GetByID`, `GetPage`, `GetWarehouses`, `GetLocationTree` 等方法，并处理了 Entity 到 VO 的转换。

### 第 4 步：接口层重构 (Controller) - [x] 已完成

#### 4.1. 重构 Controller (`wms_location_controller_impl.go`) - [x] 已完成

- **目标：** 使 Controller 层成为连接路由和新 Service 层的干净、标准的桥梁。
- **操作：**
  - **[完成]** 定义了符合 Iris 框架的 `WmsLocationController` 接口和 `wmsLocationControllerImpl` 实现。
  - **[完成]** 实现了 `Post`, `PutBy`, `DeleteBy`, `GetBy` 等 RESTful 方法。
  - **[完成]** 使用 `ctx.ReadJSON`, `ctx.ReadQuery` 等 Iris 标准方法处理请求和响应。

### 第 5 步：框架集成 (管理器部分) - [x] 已完成

#### 5.1. 更新 Service Manager (`service_manager.go`) - [x] 已完成

- **目标：** 使 `WmsLocationService` 可被框架发现。
- **操作：** **[完成]** 添加了 `GetWmsLocationService` 方法。

#### 5.2. 更新 Repository Manager (`repository_manager.go`) - [x] 已完成

- **目标：** 使 `WmsLocationRepository` 可被框架发现。
- **操作：** **[完成]** 添加了 `GetWmsLocationRepository` 方法。

#### 5.3. 更新 Controller Manager (`controller_manager.go`) - [x] 已完成

- **目标：** 使 `WmsLocationController` 可被框架发现。
- **操作：** **[完成]** 添加了 `GetWmsLocationController` 方法。

### 第 6 步：前端实现计划

- **目标：** 基于项目中成熟的 `
