package entity

import (
	"time"

	"gorm.io/gorm"
)

// WmsReceivingRecordStatus 定义收货单状态常量
type WmsReceivingRecordStatus string

const (
	ReceivingRecordStatusPending            WmsReceivingRecordStatus = "PENDING"             // 待处理/刚创建
	ReceivingRecordStatusReceiving          WmsReceivingRecordStatus = "RECEIVING"           // 收货中 (正在清点记录)
	ReceivingRecordStatusPendingInspection  WmsReceivingRecordStatus = "PENDING_INSPECTION"  // 待检验 (如果需要检验)
	ReceivingRecordStatusInspecting         WmsReceivingRecordStatus = "INSPECTING"          // 检验中 (如果需要检验)
	ReceivingRecordStatusCompleted          WmsReceivingRecordStatus = "COMPLETED"           // 收货完成 (合格/免检，等待上架)
	ReceivingRecordStatusPartiallyCompleted WmsReceivingRecordStatus = "PARTIALLY_COMPLETED" // 部分完成 (部分收货或部分合格)
	ReceivingRecordStatusClosed             WmsReceivingRecordStatus = "CLOSED"              // 已关闭 (货物已完全处理，如上架或退货)
	ReceivingRecordStatusCancelled          WmsReceivingRecordStatus = "CANCELLED"           // 已取消
)

// WmsReceivingType 定义收货类型常量
type WmsReceivingType string

const (
	ReceivingTypeASN   WmsReceivingType = "ASN"   // 按通知收货
	ReceivingTypeBlind WmsReceivingType = "BLIND" // 盲收
)

// WmsReceivingRecord 对应数据库中的 wms_receiving_records 表
// 存储收货单主信息
type WmsReceivingRecord struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	ReceivingNo           string                     `gorm:"column:receiving_no;type:varchar(50);not null;uniqueIndex;comment:收货单号" json:"receivingNo"`                // 收货单号 (业务唯一键)
	NotificationID        *uint                      `gorm:"column:notification_id;index;comment:关联ASN ID" json:"notificationId"`                                      // 关联的 wms_inbound_notifications ID (可空, 允许盲收)
	ReceivingType         WmsReceivingType           `gorm:"column:receiving_type;type:varchar(20);not null;default:'ASN';comment:收货类型" json:"receivingType"`          // 收货类型
	WarehouseID           uint                       `gorm:"column:warehouse_id;not null;comment:仓库ID" json:"warehouseId"`                                             // 仓库ID
	ClientID              uint                       `gorm:"column:client_id;not null;comment:委托客户ID" json:"clientId"`                                                 // 委托客户ID
	SupplierShipper       *string                    `gorm:"column:supplier_shipper;type:varchar(100);comment:供应商/发货方" json:"supplierShipper"`                         // 供应商/发货方 (从 ASN 或手动输入)
	ActualArrivalDate     *time.Time                 `gorm:"column:actual_arrival_date;comment:实际到达时间" json:"-"`                                                       // 实际到达/开始收货时间
	Status                string                     `gorm:"column:status;type:wms_receiving_record_status;not null;default:'PENDING';index;comment:状态" json:"status"` // 收货单状态 (使用数据库定义的 ENUM)
	SupplementDeadline    *time.Time                 `gorm:"column:supplement_deadline;comment:补货截止时间" json:"-"`                                                       // 补货截止时间
	Remark                *string                    `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                                         // 备注
	SourceDocNo           *string                    `gorm:"column:source_doc_no;type:varchar(100);comment:来源单据号" json:"sourceDocNo"`                                  // 来源单据号
	ActualArrivalDateStr  *string                    `gorm:"-" json:"actualArrivalDate"`
	SupplementDeadlineStr *string                    `gorm:"-" json:"supplementDeadline"`                                                       // 补货截止时间
	Details        		  []WmsReceivingRecordDetail `gorm:"foreignKey:receiving_record_id;references:ID" json:"details,omitempty"` // 关联的收货明细 (修正了 foreignKey)

}

// TableName 指定数据库表名方法
func (WmsReceivingRecord) TableName() string {
	return "wms_receiving_record"
}

// BeforeSave 在保存前处理时间字段
func (w *WmsReceivingRecord) BeforeSave(tx *gorm.DB) (err error) {
	if w.ActualArrivalDateStr != nil && *w.ActualArrivalDateStr != "" {
		actualArrivalDate, err := time.Parse("2006-01-02", *w.ActualArrivalDateStr)
		if err != nil {
			return err
		}
		w.ActualArrivalDate = &actualArrivalDate
	} else {
		w.ActualArrivalDate = nil
	}
	if w.SupplementDeadlineStr != nil && *w.SupplementDeadlineStr != "" {
		supplementDeadline, err := time.Parse("2006-01-02", *w.SupplementDeadlineStr)
		if err != nil {
			return err
		}
		w.SupplementDeadline = &supplementDeadline
	} else {
		w.SupplementDeadline = nil
	}
	return
}

// AfterFind 在查询后处理时间字段
func (w *WmsReceivingRecord) AfterFind(tx *gorm.DB) (err error) {
	if w.ActualArrivalDate != nil {
		dateStr := w.ActualArrivalDate.Format("2006-01-02")
		w.ActualArrivalDateStr = &dateStr
	}
	if w.SupplementDeadline != nil {
		dateStr := w.SupplementDeadline.Format("2006-01-02")
		w.SupplementDeadlineStr = &dateStr
	}
	return
}
