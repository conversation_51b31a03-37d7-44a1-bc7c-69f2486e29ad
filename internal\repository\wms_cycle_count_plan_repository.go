package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsCycleCountPlanRepository 盘点计划数据访问接口
type WmsCycleCountPlanRepository interface {
	BaseRepository[entity.WmsCycleCountPlan, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsCycleCountPlanQueryReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByPlanNo(ctx context.Context, planNo string) (*entity.WmsCycleCountPlan, error)
	FindByWarehouseID(ctx context.Context, warehouseId uint) ([]*entity.WmsCycleCountPlan, error)
	FindByStatus(ctx context.Context, status entity.WmsCycleCountPlanStatus) ([]*entity.WmsCycleCountPlan, error)
	FindByCountType(ctx context.Context, countType entity.WmsCycleCountType) ([]*entity.WmsCycleCountPlan, error)
	FindByResponsibleUser(ctx context.Context, userId uint) ([]*entity.WmsCycleCountPlan, error)
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsCycleCountPlan, error)
	FindByCreator(ctx context.Context, creatorId uint) ([]*entity.WmsCycleCountPlan, error)

	// 复杂查询
	FindPendingPlans(ctx context.Context, warehouseId *uint) ([]*entity.WmsCycleCountPlan, error)
	FindActivePlans(ctx context.Context, warehouseId *uint) ([]*entity.WmsCycleCountPlan, error)
	FindPlansNeedingApproval(ctx context.Context) ([]*entity.WmsCycleCountPlan, error)
	FindOverduePlans(ctx context.Context) ([]*entity.WmsCycleCountPlan, error)

	// 统计查询
	CountByStatus(ctx context.Context, status entity.WmsCycleCountPlanStatus) (int64, error)
	CountByCountType(ctx context.Context, countType entity.WmsCycleCountType) (int64, error)
	CountByWarehouse(ctx context.Context, warehouseId uint) (int64, error)
	CountByResponsibleUser(ctx context.Context, userId uint, startDate, endDate time.Time) (int64, error)

	// 批量操作
	BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsCycleCountPlanStatus) error
	BatchApprove(ctx context.Context, ids []uint, approvedBy uint) error
	BatchStart(ctx context.Context, ids []uint) error

	// 事务支持
	BeginTx() *gorm.DB
}

// WmsCycleCountPlanQueryBuilder 盘点计划查询构建器接口
type WmsCycleCountPlanQueryBuilder interface {
	// 条件过滤
	WherePlanNo(planNo string) WmsCycleCountPlanQueryBuilder
	WherePlanName(planName string) WmsCycleCountPlanQueryBuilder
	WhereCountType(countType entity.WmsCycleCountType) WmsCycleCountPlanQueryBuilder
	WhereWarehouseID(warehouseId uint) WmsCycleCountPlanQueryBuilder
	WhereStatus(status entity.WmsCycleCountPlanStatus) WmsCycleCountPlanQueryBuilder
	WhereResponsibleUser(userId uint) WmsCycleCountPlanQueryBuilder
	WhereCreatedBy(creatorId uint) WmsCycleCountPlanQueryBuilder
	WherePlannedDateRange(startDate, endDate time.Time) WmsCycleCountPlanQueryBuilder
	WhereCreatedDateRange(startDate, endDate time.Time) WmsCycleCountPlanQueryBuilder
	WhereCountStrategy(strategy entity.WmsCycleCountStrategy) WmsCycleCountPlanQueryBuilder

	// 关联查询
	WithWarehouse() WmsCycleCountPlanQueryBuilder
	WithResponsibleUser() WmsCycleCountPlanQueryBuilder
	WithCreator() WmsCycleCountPlanQueryBuilder
	WithApprover() WmsCycleCountPlanQueryBuilder
	WithTasks() WmsCycleCountPlanQueryBuilder

	// 排序
	OrderByCreatedAt(desc bool) WmsCycleCountPlanQueryBuilder
	OrderByPlannedDate(desc bool) WmsCycleCountPlanQueryBuilder
	OrderByApprovedAt(desc bool) WmsCycleCountPlanQueryBuilder
	OrderByPlanNo(desc bool) WmsCycleCountPlanQueryBuilder

	// 执行查询
	Find() ([]entity.WmsCycleCountPlan, error)
	First() (*entity.WmsCycleCountPlan, error)
	Count() (int64, error)
	Paginate(pageNum, pageSize int) ([]entity.WmsCycleCountPlan, int64, error)
}

// WmsCycleCountPlanRepositoryImpl 盘点计划数据访问实现
type WmsCycleCountPlanRepositoryImpl struct {
	BaseRepository[entity.WmsCycleCountPlan, uint] // 嵌入基础仓库接口
}

// NewWmsCycleCountPlanRepository 创建盘点计划数据访问实例
func NewWmsCycleCountPlanRepository(db *gorm.DB) WmsCycleCountPlanRepository {
	return &WmsCycleCountPlanRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.WmsCycleCountPlan, uint](db), // 使用构造函数初始化接口
	}
}

// GetPage 分页查询
func (r *WmsCycleCountPlanRepositoryImpl) GetPage(ctx context.Context, query *dto.WmsCycleCountPlanQueryReq) (*response.PageResult, error) {
	// 构建查询条件
	var conditions []QueryCondition

	if query.PlanNo != nil && *query.PlanNo != "" {
		conditions = append(conditions, NewEqualCondition("plan_no", *query.PlanNo))
	}
	if query.PlanName != nil && *query.PlanName != "" {
		conditions = append(conditions, NewLikeCondition("plan_name", *query.PlanName))
	}
	if query.WarehouseID != nil {
		conditions = append(conditions, NewEqualCondition("warehouse_id", *query.WarehouseID))
	}
	if query.Status != nil {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.CountType != nil {
		conditions = append(conditions, NewEqualCondition("count_type", *query.CountType))
	}
	if query.CountStrategy != nil {
		conditions = append(conditions, NewEqualCondition("count_strategy", *query.CountStrategy))
	}
	if query.ResponsibleUserID != nil {
		conditions = append(conditions, NewEqualCondition("responsible_user_id", *query.ResponsibleUserID))
	}
	if query.CreatedBy != nil {
		conditions = append(conditions, NewEqualCondition("created_by", *query.CreatedBy))
	}
	if query.PlannedStart != nil && query.PlannedEnd != nil {
		conditions = append(conditions, NewBetweenCondition("planned_date", *query.PlannedStart, *query.PlannedEnd))
	}
	if query.CreatedStart != nil && query.CreatedEnd != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *query.CreatedStart, *query.CreatedEnd))
	}

	// 使用基础仓库的分页查询
	return r.BaseRepository.FindByPage(ctx, &query.PageQuery, conditions)
}

// CreateWithTx 在事务中创建盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) CreateWithTx(tx *gorm.DB, plan *entity.WmsCycleCountPlan) error {
	return tx.Create(plan).Error
}

// UpdateWithTx 在事务中更新盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) UpdateWithTx(tx *gorm.DB, plan *entity.WmsCycleCountPlan) error {
	return tx.Save(plan).Error
}

// GetByIDWithAssociations 根据ID获取盘点计划（包含关联数据）
func (r *WmsCycleCountPlanRepositoryImpl) GetByIDWithAssociations(ctx context.Context, id uint) (*entity.WmsCycleCountPlan, error) {
	var plan entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Preload("Warehouse").
		Preload("ResponsibleUser").
		Preload("Creator").
		Preload("Approver").
		Preload("Tasks").
		First(&plan, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &plan, nil
}

// FindByPlanNo 根据计划编号查询
func (r *WmsCycleCountPlanRepositoryImpl) FindByPlanNo(ctx context.Context, planNo string) (*entity.WmsCycleCountPlan, error) {
	var plan entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("plan_no = ?", planNo).First(&plan).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &plan, nil
}

// FindByWarehouseID 根据仓库ID查询盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) FindByWarehouseID(ctx context.Context, warehouseId uint) ([]*entity.WmsCycleCountPlan, error) {
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("warehouse_id = ?", warehouseId).
		Order("created_at DESC").
		Find(&plans).Error
	return plans, err
}

// FindByStatus 根据状态查询盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) FindByStatus(ctx context.Context, status entity.WmsCycleCountPlanStatus) ([]*entity.WmsCycleCountPlan, error) {
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("status = ?", status).
		Order("created_at DESC").
		Find(&plans).Error
	return plans, err
}

// FindPendingPlans 查找待执行的盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) FindPendingPlans(ctx context.Context, warehouseId *uint) ([]*entity.WmsCycleCountPlan, error) {
	query := r.GetDB(ctx).Where("status = ?", entity.CountPlanStatusApproved)

	if warehouseId != nil {
		query = query.Where("warehouse_id = ?", *warehouseId)
	}

	var plans []*entity.WmsCycleCountPlan
	err := query.Preload("Warehouse").
		Preload("ResponsibleUser").
		Order("planned_date ASC").
		Find(&plans).Error
	return plans, err
}

// FindActivePlans 查找活跃的盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) FindActivePlans(ctx context.Context, warehouseId *uint) ([]*entity.WmsCycleCountPlan, error) {
	query := r.GetDB(ctx).Where("status = ?", entity.CountPlanStatusInProgress)

	if warehouseId != nil {
		query = query.Where("warehouse_id = ?", *warehouseId)
	}

	var plans []*entity.WmsCycleCountPlan
	err := query.Preload("Warehouse").
		Preload("ResponsibleUser").
		Order("planned_date ASC").
		Find(&plans).Error
	return plans, err
}

// FindPlansNeedingApproval 查找需要审批的盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) FindPlansNeedingApproval(ctx context.Context) ([]*entity.WmsCycleCountPlan, error) {
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("status = ?", entity.CountPlanStatusDraft).
		Preload("Creator").
		Order("created_at ASC").
		Find(&plans).Error
	return plans, err
}

// FindOverduePlans 查找逾期的盘点计划
func (r *WmsCycleCountPlanRepositoryImpl) FindOverduePlans(ctx context.Context) ([]*entity.WmsCycleCountPlan, error) {
	today := time.Now().Truncate(24 * time.Hour)
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("status IN ? AND planned_date < ?",
		[]entity.WmsCycleCountPlanStatus{entity.CountPlanStatusApproved, entity.CountPlanStatusInProgress},
		today).
		Preload("Warehouse").
		Preload("ResponsibleUser").
		Order("planned_date ASC").
		Find(&plans).Error
	return plans, err
}

// BeginTx 开始事务
func (r *WmsCycleCountPlanRepositoryImpl) BeginTx() *gorm.DB {
	return r.GetDB(context.Background()).Begin()
}

// NewQueryBuilder 创建查询构建器
func (r *WmsCycleCountPlanRepositoryImpl) NewQueryBuilder() WmsCycleCountPlanQueryBuilder {
	db := r.GetDB(context.Background())
	return &WmsCycleCountPlanQueryBuilderImpl{
		db:    db,
		query: db.Model(&entity.WmsCycleCountPlan{}),
	}
}

// 实现其他缺失的方法
func (r *WmsCycleCountPlanRepositoryImpl) FindByCountType(ctx context.Context, countType entity.WmsCycleCountType) ([]*entity.WmsCycleCountPlan, error) {
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("count_type = ?", countType).
		Order("created_at DESC").
		Find(&plans).Error
	return plans, err
}

func (r *WmsCycleCountPlanRepositoryImpl) FindByResponsibleUser(ctx context.Context, userId uint) ([]*entity.WmsCycleCountPlan, error) {
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("responsible_user_id = ?", userId).
		Order("created_at DESC").
		Find(&plans).Error
	return plans, err
}

func (r *WmsCycleCountPlanRepositoryImpl) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsCycleCountPlan, error) {
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("planned_date BETWEEN ? AND ?", startDate, endDate).
		Order("planned_date ASC").
		Find(&plans).Error
	return plans, err
}

func (r *WmsCycleCountPlanRepositoryImpl) FindByCreator(ctx context.Context, creatorId uint) ([]*entity.WmsCycleCountPlan, error) {
	var plans []*entity.WmsCycleCountPlan
	err := r.GetDB(ctx).Where("created_by = ?", creatorId).
		Order("created_at DESC").
		Find(&plans).Error
	return plans, err
}

func (r *WmsCycleCountPlanRepositoryImpl) CountByStatus(ctx context.Context, status entity.WmsCycleCountPlanStatus) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountPlan{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

func (r *WmsCycleCountPlanRepositoryImpl) CountByCountType(ctx context.Context, countType entity.WmsCycleCountType) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountPlan{}).Where("count_type = ?", countType).Count(&count).Error
	return count, err
}

func (r *WmsCycleCountPlanRepositoryImpl) CountByWarehouse(ctx context.Context, warehouseId uint) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountPlan{}).Where("warehouse_id = ?", warehouseId).Count(&count).Error
	return count, err
}

func (r *WmsCycleCountPlanRepositoryImpl) CountByResponsibleUser(ctx context.Context, userId uint, startDate, endDate time.Time) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountPlan{}).
		Where("responsible_user_id = ? AND created_at BETWEEN ? AND ?", userId, startDate, endDate).
		Count(&count).Error
	return count, err
}

func (r *WmsCycleCountPlanRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsCycleCountPlanStatus) error {
	return r.GetDB(ctx).Model(&entity.WmsCycleCountPlan{}).Where("id IN ?", ids).Update("status", status).Error
}

func (r *WmsCycleCountPlanRepositoryImpl) BatchApprove(ctx context.Context, ids []uint, approvedBy uint) error {
	now := time.Now()
	return r.GetDB(ctx).Model(&entity.WmsCycleCountPlan{}).Where("id IN ?", ids).Updates(map[string]interface{}{
		"status":      entity.CountPlanStatusApproved,
		"approved_by": approvedBy,
		"approved_at": now,
	}).Error
}

func (r *WmsCycleCountPlanRepositoryImpl) BatchStart(ctx context.Context, ids []uint) error {
	return r.GetDB(ctx).Model(&entity.WmsCycleCountPlan{}).Where("id IN ?", ids).Update("status", entity.CountPlanStatusInProgress).Error
}
