package repository

import (
	"context"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsPutawayTaskDetailRepository 定义上架任务明细仓库接口
type WmsPutawayTaskDetailRepository interface {
	BaseRepository[entity.WmsPutawayTaskDetail, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsPutawayTaskDetailQueryReq) (*response.PageResult, error)

	// 根据任务ID获取明细列表
	GetByTaskID(ctx context.Context, taskID uint) ([]*entity.WmsPutawayTaskDetail, error)

	// 根据任务ID获取明细列表（带扩展信息）
	GetByTaskIDWithExtInfo(ctx context.Context, taskID uint) ([]*dto.WmsPutawayTaskDetailResp, error)

	// 根据收货记录明细ID查询
	GetByReceivingRecordDetailID(ctx context.Context, receivingRecordDetailID uint) ([]*entity.WmsPutawayTaskDetail, error)

	// 根据物料ID查询
	GetByItemID(ctx context.Context, itemID uint) ([]*entity.WmsPutawayTaskDetail, error)

	// 根据源库位ID查询
	GetBySourceLocationID(ctx context.Context, sourceLocationID uint) ([]*entity.WmsPutawayTaskDetail, error)

	// 根据实际库位ID查询
	GetByActualLocationID(ctx context.Context, actualLocationID uint) ([]*entity.WmsPutawayTaskDetail, error)

	// 根据状态查询
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsPutawayTaskDetail, error)

	// 根据批次号查询
	GetByBatchNo(ctx context.Context, batchNo string) ([]*entity.WmsPutawayTaskDetail, error)

	// 批量创建
	BatchCreate(ctx context.Context, details []entity.WmsPutawayTaskDetail) error

	// 批量更新状态
	BatchUpdateStatus(ctx context.Context, ids []uint, status string) error

	// 批量删除
	BatchDelete(ctx context.Context, ids []uint) error

	// 获取明细汇总信息
	GetDetailsSummary(ctx context.Context, taskID uint) (*dto.PutawayTaskDetailSummary, error)

	// 更新执行信息
	UpdateExecuteInfo(ctx context.Context, id uint, actualLocationID uint, actualQuantity float64, exceptionReason *string) error

	// 检查是否有未完成的明细
	HasPendingDetails(ctx context.Context, taskID uint) (bool, error)

	// 获取任务的完成进度
	GetTaskProgress(ctx context.Context, taskID uint) (completed int, total int, err error)

	// 根据多个条件复合查询
	FindByMultipleConditions(ctx context.Context, conditions map[string]interface{}) ([]*entity.WmsPutawayTaskDetail, error)

	// 统计查询
	GetStatistics(ctx context.Context, conditions map[string]interface{}) (map[string]interface{}, error)
}

// wmsPutawayTaskDetailRepository 上架任务明细仓库实现
type wmsPutawayTaskDetailRepository struct {
	BaseRepositoryImpl[entity.WmsPutawayTaskDetail, uint]
}

// NewWmsPutawayTaskDetailRepository 创建上架任务明细仓库
func NewWmsPutawayTaskDetailRepository(db *gorm.DB) WmsPutawayTaskDetailRepository {
	return &wmsPutawayTaskDetailRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsPutawayTaskDetail, uint]{
			db: db,
		},
	}
}

// GetPage 获取上架任务明细分页数据
func (r *wmsPutawayTaskDetailRepository) GetPage(ctx context.Context, query *dto.WmsPutawayTaskDetailQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.PutawayTaskID != nil {
		conditions = append(conditions, NewEqualCondition("putaway_task_id", *query.PutawayTaskID))
	}
	if query.ReceivingRecordDetailID != nil {
		conditions = append(conditions, NewEqualCondition("receiving_record_detail_id", *query.ReceivingRecordDetailID))
	}
	if query.ItemID != nil {
		conditions = append(conditions, NewEqualCondition("item_id", *query.ItemID))
	}
	if query.BatchNo != nil && *query.BatchNo != "" {
		conditions = append(conditions, NewLikeCondition("batch_no", *query.BatchNo))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if len(query.Statuses) > 0 {
		conditions = append(conditions, NewInCondition("status", query.Statuses))
	}
	if query.SourceLocationID != nil {
		conditions = append(conditions, NewEqualCondition("source_location_id", *query.SourceLocationID))
	}
	if query.SuggestedLocationID != nil {
		conditions = append(conditions, NewEqualCondition("suggested_location_id", *query.SuggestedLocationID))
	}
	if query.ActualLocationID != nil {
		conditions = append(conditions, NewEqualCondition("actual_location_id", *query.ActualLocationID))
	}
	if query.QuantityMin != nil {
		conditions = append(conditions, NewGreaterThanEqualCondition("putaway_quantity", *query.QuantityMin))
	}
	if query.QuantityMax != nil {
		conditions = append(conditions, NewLessThanEqualCondition("putaway_quantity", *query.QuantityMax))
	}
	if query.ProductionDateFrom != nil && query.ProductionDateTo != nil {
		conditions = append(conditions, NewBetweenCondition("production_date", *query.ProductionDateFrom, *query.ProductionDateTo))
	}
	if query.ExpiryDateFrom != nil && query.ExpiryDateTo != nil {
		conditions = append(conditions, NewBetweenCondition("expiry_date", *query.ExpiryDateFrom, *query.ExpiryDateTo))
	}

	return r.FindByPage(ctx, &query.Pagination, conditions)
}

// GetByTaskID 根据任务ID获取明细列表
func (r *wmsPutawayTaskDetailRepository) GetByTaskID(ctx context.Context, taskID uint) ([]*entity.WmsPutawayTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("putaway_task_id", taskID),
	}
	sortInfos := []response.SortInfo{
		{Field: "line_no", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByTaskIDWithExtInfo 根据任务ID获取明细列表（带扩展信息）
func (r *wmsPutawayTaskDetailRepository) GetByTaskIDWithExtInfo(ctx context.Context, taskID uint) ([]*dto.WmsPutawayTaskDetailResp, error) {
	var results []*dto.WmsPutawayTaskDetailResp
	db := r.GetDB(ctx)

	// 使用JOIN查询获取扩展信息
	result := db.Table("wms_putaway_task_detail ptd").
		Select(`ptd.*, 
				i.sku as item_sku, 
				i.name as item_name,
				sl.code as source_location_code,
				sgl.code as suggested_location_code,
				al.code as actual_location_code`).
		Joins("LEFT JOIN mtl_item i ON i.id = ptd.item_id").
		Joins("LEFT JOIN wms_location sl ON sl.id = ptd.source_location_id").
		Joins("LEFT JOIN wms_location sgl ON sgl.id = ptd.suggested_location_id").
		Joins("LEFT JOIN wms_location al ON al.id = ptd.actual_location_id").
		Where("ptd.putaway_task_id = ?", taskID).
		Order("ptd.line_no ASC, ptd.created_at ASC").
		Find(&results)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据任务ID查询明细失败: taskID=%d", taskID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询上架任务明细失败").WithCause(result.Error)
	}

	return results, nil
}

// GetByReceivingRecordDetailID 根据收货记录明细ID查询
func (r *wmsPutawayTaskDetailRepository) GetByReceivingRecordDetailID(ctx context.Context, receivingRecordDetailID uint) ([]*entity.WmsPutawayTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("receiving_record_detail_id", receivingRecordDetailID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByItemID 根据物料ID查询
func (r *wmsPutawayTaskDetailRepository) GetByItemID(ctx context.Context, itemID uint) ([]*entity.WmsPutawayTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("item_id", itemID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetBySourceLocationID 根据源库位ID查询
func (r *wmsPutawayTaskDetailRepository) GetBySourceLocationID(ctx context.Context, sourceLocationID uint) ([]*entity.WmsPutawayTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("source_location_id", sourceLocationID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByActualLocationID 根据实际库位ID查询
func (r *wmsPutawayTaskDetailRepository) GetByActualLocationID(ctx context.Context, actualLocationID uint) ([]*entity.WmsPutawayTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("actual_location_id", actualLocationID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByStatus 根据状态查询
func (r *wmsPutawayTaskDetailRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsPutawayTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByBatchNo 根据批次号查询
func (r *wmsPutawayTaskDetailRepository) GetByBatchNo(ctx context.Context, batchNo string) ([]*entity.WmsPutawayTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("batch_no", batchNo),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// BatchCreate 批量创建
func (r *wmsPutawayTaskDetailRepository) BatchCreate(ctx context.Context, details []entity.WmsPutawayTaskDetail) error {
	if len(details) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(details, 100) // 分批创建，每批100条

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建上架任务明细失败: count=%d", len(details))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建上架任务明细失败").WithCause(result.Error)
	}

	return nil
}

// BatchUpdateStatus 批量更新状态
func (r *wmsPutawayTaskDetailRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPutawayTaskDetail{}).
		Where("id IN ?", ids).
		Update("status", status)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量更新明细状态失败: ids=%v, status=%s", ids, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新明细状态失败").WithCause(result.Error)
	}

	return nil
}

// BatchDelete 批量删除
func (r *wmsPutawayTaskDetailRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.Where("id IN ?", ids).Delete(&entity.WmsPutawayTaskDetail{})

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量删除明细失败: ids=%v", ids)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "批量删除明细失败").WithCause(result.Error)
	}

	return nil
}

// GetDetailsSummary 获取明细汇总信息
func (r *wmsPutawayTaskDetailRepository) GetDetailsSummary(ctx context.Context, taskID uint) (*dto.PutawayTaskDetailSummary, error) {
	var summary dto.PutawayTaskDetailSummary
	db := r.GetDB(ctx)

	// 使用单一查询获取汇总信息
	result := db.Table("wms_putaway_task_detail").
		Select(`
			COUNT(*) as total_detail_count,
			COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_detail_count,
			COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_detail_count,
			COUNT(CASE WHEN status = 'ON_HOLD' OR exception_reason IS NOT NULL THEN 1 END) as exception_detail_count,
			COALESCE(SUM(putaway_quantity), 0) as total_putaway_quantity,
			COALESCE(SUM(actual_putaway_quantity), 0) as total_actual_quantity
		`).
		Where("putaway_task_id = ?", taskID).
		First(&summary)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("获取明细汇总信息失败: taskID=%d", taskID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取明细汇总信息失败").WithCause(result.Error)
	}

	// 计算派生字段
	summary.QuantityDifference = summary.TotalActualQuantity - summary.TotalPutawayQuantity
	if summary.TotalDetailCount > 0 {
		summary.CompletionPercentage = float64(summary.CompletedDetailCount) / float64(summary.TotalDetailCount) * 100
	}

	return &summary, nil
}

// UpdateExecuteInfo 更新执行信息
func (r *wmsPutawayTaskDetailRepository) UpdateExecuteInfo(ctx context.Context, id uint, actualLocationID uint, actualQuantity float64, exceptionReason *string) error {
	db := r.GetDB(ctx)

	updates := map[string]interface{}{
		"actual_location_id":      actualLocationID,
		"actual_putaway_quantity": actualQuantity,
		"status":                  entity.PutawayTaskStatusCompleted,
	}

	if exceptionReason != nil {
		updates["exception_reason"] = *exceptionReason
		if *exceptionReason != "" {
			updates["status"] = entity.PutawayTaskStatusOnHold
		}
	}

	result := db.Model(&entity.WmsPutawayTaskDetail{}).
		Where("id = ?", id).
		Updates(updates)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新执行信息失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新执行信息失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "明细记录不存在")
	}

	return nil
}

// HasPendingDetails 检查是否有未完成的明细
func (r *wmsPutawayTaskDetailRepository) HasPendingDetails(ctx context.Context, taskID uint) (bool, error) {
	var count int64
	db := r.GetDB(ctx)

	result := db.Model(&entity.WmsPutawayTaskDetail{}).
		Where("putaway_task_id = ?", taskID).
		Where("status IN ?", []string{"PENDING", "IN_PROGRESS", "ASSIGNED"}).
		Count(&count)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("检查未完成明细失败: taskID=%d", taskID)
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查未完成明细失败").WithCause(result.Error)
	}

	return count > 0, nil
}

// GetTaskProgress 获取任务的完成进度
func (r *wmsPutawayTaskDetailRepository) GetTaskProgress(ctx context.Context, taskID uint) (completed int, total int, err error) {
	db := r.GetDB(ctx)

	var result struct {
		Total     int `json:"total"`
		Completed int `json:"completed"`
	}

	queryResult := db.Table("wms_putaway_task_detail").
		Select(`
			COUNT(*) as total,
			COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed
		`).
		Where("putaway_task_id = ?", taskID).
		First(&result)

	if queryResult.Error != nil {
		logger.WithContext(ctx).WithError(queryResult.Error).Errorf("获取任务进度失败: taskID=%d", taskID)
		return 0, 0, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取任务进度失败").WithCause(queryResult.Error)
	}

	return result.Completed, result.Total, nil
}

// FindByMultipleConditions 根据多个条件复合查询
func (r *wmsPutawayTaskDetailRepository) FindByMultipleConditions(ctx context.Context, conditions map[string]interface{}) ([]*entity.WmsPutawayTaskDetail, error) {
	var details []*entity.WmsPutawayTaskDetail
	db := r.GetDB(ctx)

	query := db.Model(&entity.WmsPutawayTaskDetail{})

	// 动态构建查询条件
	for key, value := range conditions {
		switch key {
		case "putaway_task_id":
			query = query.Where("putaway_task_id = ?", value)
		case "item_id":
			query = query.Where("item_id = ?", value)
		case "status":
			query = query.Where("status = ?", value)
		case "statuses":
			if statuses, ok := value.([]string); ok {
				query = query.Where("status IN ?", statuses)
			}
		case "source_location_id":
			query = query.Where("source_location_id = ?", value)
		case "actual_location_id":
			query = query.Where("actual_location_id = ?", value)
		case "batch_no":
			query = query.Where("batch_no = ?", value)
		case "quantity_min":
			query = query.Where("putaway_quantity >= ?", value)
		case "quantity_max":
			query = query.Where("putaway_quantity <= ?", value)
		}
	}

	result := query.Order("line_no ASC, created_at ASC").Find(&details)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("复合条件查询失败: conditions=%v", conditions)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "复合条件查询失败").WithCause(result.Error)
	}

	return details, nil
}

// GetStatistics 统计查询
func (r *wmsPutawayTaskDetailRepository) GetStatistics(ctx context.Context, conditions map[string]interface{}) (map[string]interface{}, error) {
	db := r.GetDB(ctx)
	query := db.Table("wms_putaway_task_detail")

	// 动态构建查询条件
	for key, value := range conditions {
		switch key {
		case "putaway_task_id":
			query = query.Where("putaway_task_id = ?", value)
		case "item_id":
			query = query.Where("item_id = ?", value)
		case "status":
			query = query.Where("status = ?", value)
		case "date_from":
			query = query.Where("created_at >= ?", value)
		case "date_to":
			query = query.Where("created_at <= ?", value)
		}
	}

	var stats struct {
		TotalCount         int     `json:"total_count"`
		PendingCount       int     `json:"pending_count"`
		CompletedCount     int     `json:"completed_count"`
		ExceptionCount     int     `json:"exception_count"`
		TotalQuantity      float64 `json:"total_quantity"`
		CompletedQuantity  float64 `json:"completed_quantity"`
		AverageQuantity    float64 `json:"average_quantity"`
		CompletionRate     float64 `json:"completion_rate"`
	}

	result := query.Select(`
		COUNT(*) as total_count,
		COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_count,
		COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_count,
		COUNT(CASE WHEN status = 'ON_HOLD' THEN 1 END) as exception_count,
		COALESCE(SUM(putaway_quantity), 0) as total_quantity,
		COALESCE(SUM(actual_putaway_quantity), 0) as completed_quantity,
		COALESCE(AVG(putaway_quantity), 0) as average_quantity
	`).First(&stats)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("统计查询失败: conditions=%v", conditions)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "统计查询失败").WithCause(result.Error)
	}

	// 计算完成率
	if stats.TotalCount > 0 {
		stats.CompletionRate = float64(stats.CompletedCount) / float64(stats.TotalCount) * 100
	}

	// 转换为map返回
	statisticsMap := map[string]interface{}{
		"total_count":        stats.TotalCount,
		"pending_count":      stats.PendingCount,
		"completed_count":    stats.CompletedCount,
		"exception_count":    stats.ExceptionCount,
		"total_quantity":     stats.TotalQuantity,
		"completed_quantity": stats.CompletedQuantity,
		"average_quantity":   stats.AverageQuantity,
		"completion_rate":    stats.CompletionRate,
	}

	return statisticsMap, nil
} 