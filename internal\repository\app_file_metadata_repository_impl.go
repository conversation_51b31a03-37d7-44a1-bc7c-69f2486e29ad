package repository

import (
	"backend/internal/model/entity"
	"backend/pkg/response"
	"context"

	"gorm.io/gorm"
)

// FileMetadataRepository 文件元数据仓库接口
type FileMetadataRepository interface {
	// 继承基础仓库接口，指定实体类型为 FileMetadata, 主键类型为 uint
	BaseRepository[entity.FileMetadata, uint]

	// FindByBusiness 根据业务标识查找文件元数据列表 (新增)
	// 参数:
	//   - ctx: 上下文
	//   - businessType: 业务类型
	//   - businessID: 业务ID
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.FileMetadata: 元数据列表
	//   - error: 错误信息
	FindByBusiness(ctx context.Context, businessType string, businessID uint, sortInfos []response.SortInfo) ([]*entity.FileMetadata, error)

	// 未来可以添加特定查询方法，例如：
	// FindByObjectKey(ctx context.Context, objectKey string) (*entity.FileMetadata, error)
}

// fileMetadataRepositoryImpl 文件元数据仓库实现
type fileMetadataRepositoryImpl struct {
	// 嵌入基础仓库接口
	BaseRepository[entity.FileMetadata, uint]
}

// NewFileMetadataRepository 创建文件元数据仓库
func NewFileMetadataRepository(db *gorm.DB) FileMetadataRepository {
	return &fileMetadataRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.FileMetadata, uint](db),
	}
}

// FindByBusiness 根据业务标识查找文件元数据列表 (新增)
func (r *fileMetadataRepositoryImpl) FindByBusiness(ctx context.Context, businessType string, businessID uint, sortInfos []response.SortInfo) ([]*entity.FileMetadata, error) {
	conditions := []QueryCondition{
		NewCondition("business_type", OP_EQ, businessType),
		NewCondition("business_id", OP_EQ, businessID),
	}
	// 调用符合规范的 FindByCondition 并传递 sortInfos
	return r.FindByCondition(ctx, conditions, sortInfos)
}

// --- 特定查询方法的实现 (示例) ---

// // FindByObjectKey 根据对象键查询
// func (r *fileMetadataRepositoryImpl) FindByObjectKey(ctx context.Context, objectKey string) (*entity.FileMetadata, error) {
// 	var metadata entity.FileMetadata
// 	result := r.GetDB(ctx).Where("object_key = ?", objectKey).First(&metadata)
// 	if result.Error != nil {
// 		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
// 			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "文件元数据不存在")
// 		}
// 		logger.WithContext(ctx).WithError(result.Error).Error("根据对象键查询文件元数据失败")
// 		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询文件元数据失败").WithCause(result.Error)
// 	}
// 	return &metadata, nil
// }

// // FindByBusiness 根据业务标识查询
// func (r *fileMetadataRepositoryImpl) FindByBusiness(ctx context.Context, businessType string, businessID uint) ([]*entity.FileMetadata, error) {
// 	var metadatas []*entity.FileMetadata
// 	result := r.GetDB(ctx).Where("business_type = ? AND business_id = ?", businessType, businessID).Find(&metadatas)
// 	if result.Error != nil {
// 		logger.WithContext(ctx).WithError(result.Error).Error("根据业务标识查询文件元数据失败")
// 		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询文件元数据列表失败").WithCause(result.Error)
// 	}
// 	return metadatas, nil
// }
