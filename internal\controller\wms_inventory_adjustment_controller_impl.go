package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
)

// WmsInventoryAdjustmentController 定义库存调整控制器接口
type WmsInventoryAdjustmentController interface {
	// 基础CRUD操作
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 审批流程
	Approve(ctx iris.Context)
	Reject(ctx iris.Context)

	// 执行调整
	Execute(ctx iris.Context)
	ExecuteSingle(ctx iris.Context)

	// 批量操作
	BatchCreate(ctx iris.Context)
	BatchApprove(ctx iris.Context)
	BatchExecute(ctx iris.Context)

	// 统计分析
	GetStats(ctx iris.Context)
	GetSummary(ctx iris.Context)

	// 导入导出
	ExportToExcel(ctx iris.Context)
	ImportFromExcel(ctx iris.Context)

	// 辅助功能
	GetReasons(ctx iris.Context)
	Validate(ctx iris.Context)
}

// wmsInventoryAdjustmentControllerImpl 库存调整控制器实现
type WmsInventoryAdjustmentControllerImpl struct {
	BaseControllerImpl
	inventoryAdjustmentService service.WmsInventoryAdjustmentService
}

// NewWmsInventoryAdjustmentControllerImpl 创建库存调整控制器实例
func NewWmsInventoryAdjustmentControllerImpl(cm *ControllerManager) *WmsInventoryAdjustmentControllerImpl {
	return &WmsInventoryAdjustmentControllerImpl{
		BaseControllerImpl:         *NewBaseController(cm),
		inventoryAdjustmentService: cm.GetServiceManager().GetWmsInventoryAdjustmentService(),
	}
}

// Create 创建库存调整记录
func (c *WmsInventoryAdjustmentControllerImpl) Create(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.inventoryAdjustmentService.Create(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// Update 更新库存调整记录
func (c *WmsInventoryAdjustmentControllerImpl) Update(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5003, "无效的ID参数")
		return
	}

	var req dto.WmsInventoryAdjustmentUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.inventoryAdjustmentService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// Delete 删除库存调整记录
func (c *WmsInventoryAdjustmentControllerImpl) Delete(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5003, "无效的ID参数")
		return
	}

	err = c.inventoryAdjustmentService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, nil)
}

// GetByID 根据ID获取库存调整记录
func (c *WmsInventoryAdjustmentControllerImpl) GetByID(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5003, "无效的ID参数")
		return
	}

	result, err := c.inventoryAdjustmentService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetPage 分页查询库存调整记录
func (c *WmsInventoryAdjustmentControllerImpl) GetPage(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryAdjustmentService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// Approve 审批库存调整
func (c *WmsInventoryAdjustmentControllerImpl) Approve(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5003, "无效的ID参数")
		return
	}

	var req dto.WmsInventoryAdjustmentApprovalReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	err = c.inventoryAdjustmentService.Approve(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, nil)
}

// Reject 拒绝库存调整
func (c *WmsInventoryAdjustmentControllerImpl) Reject(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5003, "无效的ID参数")
		return
	}

	approvedByStr := ctx.PostValue("approvedBy")
	approvedBy, err := strconv.ParseUint(approvedByStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5003, "无效的审批人ID参数")
		return
	}

	reason := ctx.PostValue("reason")
	if reason == "" {
		response.Fail(ctx, 5003, "拒绝原因不能为空")
		return
	}

	err = c.inventoryAdjustmentService.Reject(ctx.Request().Context(), uint(id), uint(approvedBy), reason)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, nil)
}

// Execute 执行库存调整
func (c *WmsInventoryAdjustmentControllerImpl) Execute(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentExecuteReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	if err := c.inventoryAdjustmentService.Execute(ctx.Request().Context(), &req); err != nil {
		response.Fail(ctx, 5001, "执行库存调整失败")
		return
	}

	response.Success(ctx, nil)
}

// ExecuteSingle 执行单个库存调整
func (c *WmsInventoryAdjustmentControllerImpl) ExecuteSingle(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5003, "无效的ID参数")
		return
	}

	err = c.inventoryAdjustmentService.ExecuteSingle(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, nil)
}

// BatchCreate 批量创建库存调整
func (c *WmsInventoryAdjustmentControllerImpl) BatchCreate(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentBatchCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.inventoryAdjustmentService.BatchCreate(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// BatchApprove 批量审批库存调整
func (c *WmsInventoryAdjustmentControllerImpl) BatchApprove(ctx iris.Context) {
	var req struct {
		IDs        []uint `json:"ids"`
		ApproverID uint   `json:"approverId"`
	}
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	err := c.inventoryAdjustmentService.BatchApprove(ctx.Request().Context(), req.IDs, req.ApproverID)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, nil)
}

// BatchExecute 批量执行库存调整
func (c *WmsInventoryAdjustmentControllerImpl) BatchExecute(ctx iris.Context) {
	var req struct {
		IDs []uint `json:"ids"`
	}
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	err := c.inventoryAdjustmentService.BatchExecute(ctx.Request().Context(), req.IDs)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, nil)
}

// GetStats 获取库存调整统计
func (c *WmsInventoryAdjustmentControllerImpl) GetStats(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentStatsReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5003, "请求参数格式错误")
		return
	}

	result, err := c.inventoryAdjustmentService.GetStats(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}
