package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/model/vo"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/validator"
	// 确保导入 response 包
)

// EmployeeControllerImpl 员工控制器实现
type EmployeeControllerImpl struct {
	*BaseControllerImpl
	employeeService service.EmployeeService
}

// NewEmployeeControllerImpl 创建员工控制器实例
func NewEmployeeControllerImpl(cm *ControllerManager) *EmployeeControllerImpl {
	base := NewBaseController(cm)
	employeeService := cm.GetServiceManager().GetEmployeeService() // Call as a method on ServiceManager
	if employeeService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 EmployeeService 实例") // 使用 cm.GetContext()
	}
	return &EmployeeControllerImpl{
		BaseControllerImpl: base,
		employeeService:    employeeService,
	}
}

// CreateEmployee 创建员工
// @Summary 创建新员工
// @Description 创建一个新的员工信息
// @Tags Employees
// @Accept json
// @Produce json
// @Param employee body dto.EmployeeCreateOrUpdateDTO true "员工创建或更新请求"
// @Success 200 {object} response.Response{data=vo.EmployeeVO} "成功响应，返回员工信息"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees [post]
func (c *EmployeeControllerImpl) CreateEmployee(ctx iris.Context) {
	opName := "创建员工"
	var req dto.EmployeeCreateOrUpdateDTO
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	employeeVO, err := c.employeeService.CreateEmployee(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, employeeVO)
}

// UpdateEmployee 更新员工信息
// @Summary 更新员工信息
// @Description 根据员工ID更新员工信息
// @Tags Employees
// @Accept json
// @Produce json
// @Param id path uint true "员工ID"
// @Param employee body dto.EmployeeCreateOrUpdateDTO true "员工信息更新请求"
// @Success 200 {object} response.Response{data=vo.EmployeeVO} "成功响应，返回更新后的员工信息"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "员工未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees/{id} [put]
func (c *EmployeeControllerImpl) UpdateEmployee(ctx iris.Context) {
	opName := "更新员工信息"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	employeeID, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	var req dto.EmployeeCreateOrUpdateDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("employeeId", employeeID), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	employeeVO, err := c.employeeService.UpdateEmployee(ctx.Request().Context(), uint(employeeID), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, employeeVO)
}

// DeleteEmployees 删除员工
// @Summary 删除一个或多个员工
// @Description 根据员工ID列表删除员工
// @Tags Employees
// @Accept json
// @Produce json
// @Param ids body dto.BatchDeleteDTO true "员工ID列表请求" // 假设 dto.BatchDeleteDTO 包含一个 []uint 类型的 IDs 字段
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees [delete] // 通常批量删除用 POST 或 DELETE，这里用 DELETE，路径可能需要调整为 /employees/batch-delete
func (c *EmployeeControllerImpl) DeleteEmployees(ctx iris.Context) {
	opName := "删除员工"
	var req dto.EmployeeDeleteDTO // 假设有 BatchDeleteDTO { IDs []uint }
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	if len(req.Ids) == 0 {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "至少需要一个员工ID才能删除"), opName)
		return
	}

	err := c.employeeService.DeleteEmployees(ctx.Request().Context(), req.Ids)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// DeleteEmployeeByID 删除单个员工
// @Summary 删除单个员工
// @Description 根据员工ID删除单个员工信息
// @Tags Employees
// @Produce json
// @Param id path uint true "员工ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "员工未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees/{id} [delete] // Swagger 注解与 router.go 中的定义保持一致
func (c *EmployeeControllerImpl) DeleteEmployeeByID(ctx iris.Context) {
	opName := "删除单个员工ByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	employeeID, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	// 调用 service 的批量删除，传入单个ID的切片
	if err := c.employeeService.DeleteEmployees(ctx.Request().Context(), []uint{uint(employeeID)}); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetEmployeeByID 获取员工详情
// @Summary 获取员工详情
// @Description 根据员工ID获取员工详细信息
// @Tags Employees
// @Produce json
// @Param id path uint true "员工ID"
// @Success 200 {object} response.Response{data=vo.EmployeeVO} "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "员工未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees/{id} [get]
func (c *EmployeeControllerImpl) GetEmployeeByID(ctx iris.Context) {
	opName := "获取员工详情ByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	employeeID, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	employeeVO, err := c.employeeService.GetEmployeeByID(ctx.Request().Context(), uint(employeeID))
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, employeeVO)
}

// GetEmployeeByCode 获取员工详情（通过员工代码）
// @Summary 获取员工详情（通过员工代码）
// @Description 根据员工代码获取员工详细信息
// @Tags Employees
// @Produce json
// @Param code path string true "员工代码"
// @Success 200 {object} response.Response{data=vo.EmployeeVO} "成功响应"
// @Failure 400 {object} response.Response "员工代码无效"
// @Failure 404 {object} response.Response "员工未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees/code/{code} [get]
func (c *EmployeeControllerImpl) GetEmployeeByCode(ctx iris.Context) {
	opName := "获取员工详情ByCode"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	employeeCode := ctx.Params().Get("code")
	if employeeCode == "" {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "员工代码参数缺失"), opName)
		return
	}

	employeeVO, err := c.employeeService.GetEmployeeByCode(ctx.Request().Context(), employeeCode)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, employeeVO)
}

// GetEmployeePage 获取员工分页列表
// @Summary 获取员工分页列表
// @Description 获取员工列表，支持分页和过滤
// @Tags Employees
// @Produce json
// @Param pageNum query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param sort query string false "排序规则 (例如: 'employee_code:asc,created_at:desc')"
// @Param employeeCode query string false "员工代码过滤 (模糊匹配)"
// @Param name query string false "员工姓名过滤 (模糊匹配)"
// @Param mobile query string false "手机号过滤 (模糊匹配)"
// @Param status query int false "状态过滤 (具体值参考业务定义, e.g., 0:禁用, 1:启用)"
// @Param departmentId query uint false "部门ID过滤"
// @Success 200 {object} response.Response{data=response.PageResult} "成功响应 (包含员工视图对象列表和分页信息)"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees/page [get]
func (c *EmployeeControllerImpl) GetEmployeePage(ctx iris.Context) {
	opName := "获取员工分页列表"
	var queryDTO dto.EmployeePageQueryDTO
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")


	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "解析查询参数失败").WithCause(err), opName)
		return
	}

	pageResult, err := c.employeeService.GetEmployeePage(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, pageResult)
}

// GetEmployeeSimpleList 获取员工简单列表 (用于下拉框等)
// @Summary 获取员工简单列表
// @Description 获取员工的简单信息列表 (ID, Code, Name)，通常用于前端选择器。
// @Tags Employees
// @Produce json
// @Param code query string false "员工代码 (模糊搜索)"
// @Param name query string false "员工姓名 (模糊搜索)"
// @Param status query int false "员工状态 (例如: 1 表示在职)"
// @Success 200 {object} response.Response{data=map[string][]vo.EmployeeSimpleVO} "成功响应，返回 {list: []}"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees/simple-list [get]
func (c *EmployeeControllerImpl) GetEmployeeSimpleList(ctx iris.Context) {
	opName := "获取员工简单列表"
	var queryDTO dto.EmployeeSimpleListQueryDTO
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	// 从查询参数绑定 DTO
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "解析查询参数失败").WithCause(err), opName)
		return
	}

	// 调用 Service 层
	simpleList, err := c.employeeService.GetEmployeeSimpleList(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName) // Service 层应该返回包装好的错误
		return
	}

	// 确保返回空数组而不是 nil
	if simpleList == nil {
		simpleList = []*vo.EmployeeSimpleVO{}
	}

	// 将结果包装在 { list: ... } 结构中
	responsePayload := iris.Map{
		"list": simpleList,
	}
	c.Success(ctx, responsePayload) // 返回包装后的数据
}

// UpdateEmployeeStatus 更新员工状态
// @Summary 更新员工状态
// @Description 根据员工ID更新员工的状态
// @Tags Employees
// @Accept json
// @Produce json
// @Param id path uint true "员工ID"
// @Param status_request body dto.EmployeeStatusUpdateDTO true "状态更新请求 (例如: {"status": 1})"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "员工未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /employees/{id}/status [put]
func (c *EmployeeControllerImpl) UpdateEmployeeStatus(ctx iris.Context) {
	opName := "更新员工状态"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	employeeID, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	var req dto.EmployeeStatusUpdateDTO // Corrected DTO name
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// 可选：添加对 status 值的验证，例如是否在允许的范围内
	// if req.Status < 0 || req.Status > 1 { // 假设状态只有 0 和 1
	// 	 c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_VALUE_ERROR, "无效的状态值"), opName)
	// 	 return
	// }

	err = c.employeeService.UpdateEmployeeStatus(ctx.Request().Context(), uint(employeeID), req.Status)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "状态更新成功", nil)
}

// UploadEmployeeAvatar 处理员工头像上传 (新增)
// @Summary 上传员工头像
// @Description 为员工上传头像文件，成功后返回头像URL
// @Tags Employees
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "头像文件"
// @Success 200 {object} response.Response{data=map[string]string} "成功响应，data 包含 avatarUrl"
// @Failure 400 {object} response.Response "请求参数错误 (如缺少文件、文件过大或类型不允许)"
// @Failure 500 {object} response.Response "服务器内部错误 (文件保存失败)"
// @Router /employees/avatar/upload [post]
func (c *EmployeeControllerImpl) UploadEmployeeAvatar(ctx iris.Context) {
	opName := "上传员工头像"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "employee")

	// 1. 从 ServiceManager 获取 FileService
	fileService := c.GetServiceManager().GetFileService()
	if fileService == nil {
		c.HandleError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "文件服务不可用"), opName)
		return
	}

	// 2. 解析表单数据
	// 设置一个合理的默认最大请求大小，例如10MB。 FileService 内部会再次根据其配置校验精确的文件大小。
	const defaultMaxRequestSizeMB int64 = 10
	maxSize := defaultMaxRequestSizeMB << 20 // 转换为 bytes

	c.GetLogger().Debug(ctx.Request().Context(), opName+": 准备解析表单", logger.WithField("maxSize", maxSize))

	err := ctx.Request().ParseMultipartForm(maxSize)
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_FORMAT_ERROR, "无法解析表单数据或文件过大").WithCause(err), opName)
		return
	}

	// 3. 获取文件
	file, header, err := ctx.FormFile("file") // "file" 是前端上传组件中 `name` 属性的值
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "缺少文件字段 'file'").WithCause(err), opName)
		return
	}
	defer file.Close()

	// 4. 调用 FileService 上传文件
	var uploaderID uint = 0          // 默认为0，或尝试从上下文中获取当前用户ID
	if c.BaseControllerImpl != nil { // 确保 BaseControllerImpl 已初始化
		userID, authErr := c.GetUserIDFromContext(ctx) // GetUserIDFromContext 来自 BaseControllerImpl
		if authErr == nil {
			uploaderID = uint(userID)
		}
	}

	businessType := "employee_avatar"
	metadata, serviceErr := fileService.UploadFile(ctx.Request().Context(), uploaderID, file, header, businessType, nil)
	if serviceErr != nil {
		c.HandleError(ctx, serviceErr, opName)
		return
	}

	// 5. 调用 FileService 获取文件访问 URL
	avatarURL, urlErr := fileService.GetFileURL(ctx.Request().Context(), metadata)
	if urlErr != nil {
		c.HandleError(ctx, apperrors.WrapError(urlErr, apperrors.CODE_SYSTEM_INTERNAL, "生成文件URL失败"), opName)
		return
	}

	// 6. 返回成功响应，包含头像URL
	c.Success(ctx, iris.Map{"avatarUrl": avatarURL})
}
