<template>
  <div class="inventory-allocation-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>库存分配管理</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>仓库管理</el-breadcrumb-item>
          <el-breadcrumb-item>出库管理</el-breadcrumb-item>
          <el-breadcrumb-item>库存分配</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          @click="handleBatchAllocate"
          v-if="checkPermission('allocation:batch')"
        >
          <el-icon><Magic /></el-icon>
          批量分配
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ allocationStats.pending }}</div>
              <div class="stat-label">待分配</div>
            </div>
            <div class="stat-icon stat-icon-pending">
              <el-icon><Clock /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ allocationStats.allocated }}</div>
              <div class="stat-label">已分配</div>
            </div>
            <div class="stat-icon stat-icon-allocated">
              <el-icon><CircleCheck /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ allocationStats.partial }}</div>
              <div class="stat-label">部分分配</div>
            </div>
            <div class="stat-icon stat-icon-partial">
              <el-icon><Warning /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ allocationStats.failed }}</div>
              <div class="stat-label">分配失败</div>
            </div>
            <div class="stat-icon stat-icon-failed">
              <el-icon><CircleClose /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="main-content">
      <VNTable
        ref="vnTableRef"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        :toolbar-config="toolbarConfig"
        :show-operations="true"
        :operation-width="200"
        operation-fixed="right"
        row-key="id"
        :selection-type="'multiple'"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @selection-change="handleSelectionChange"
        @filter-change="handleFilterChange"
        @refresh="handleRefresh"
      >
        <!-- 自定义列插槽 -->
        <template #column-notificationNo="{ row }">
          <el-link
            type="primary"
            @click="handleView(row)"
            :underline="false"
          >
            {{ row.notificationNo }}
          </el-link>
        </template>

        <template #column-allocationStatus="{ row }">
          <el-tag :type="getAllocationStatusTagType(row.allocationStatus)">
            {{ formatAllocationStatus(row.allocationStatus) }}
          </el-tag>
        </template>

        <template #column-allocationProgress="{ row }">
          <div class="progress-container">
            <el-progress
              :percentage="row.allocationProgress"
              :color="getProgressColor(row.allocationProgress)"
              :stroke-width="6"
              :show-text="false"
            />
            <span class="progress-text">{{ row.allocationProgress }}%</span>
          </div>
        </template>

        <template #column-strategy="{ row }">
          <el-tag size="small" type="info">
            {{ formatStrategy(row.strategy) }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="handleAllocate(row)"
            v-if="canAllocate(row) && checkPermission('allocation:execute')"
          >
            分配
          </el-button>
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, row)"
            v-if="getDropdownItems(row).length > 0"
          >
            <el-button size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in getDropdownItems(row)"
                  :key="item.command"
                  :command="item.command"
                  :disabled="item.disabled"
                >
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </VNTable>
    </el-card>

    <!-- 分配对话框 -->
    <AllocationDialog
      ref="allocationDialogRef"
      @confirm="handleAllocationConfirm"
      @cancel="handleAllocationCancel"
    />

    <!-- 批量分配对话框 -->
    <BatchAllocationDialog
      ref="batchAllocationDialogRef"
      @confirm="handleBatchAllocationConfirm"
      @cancel="handleBatchAllocationCancel"
    />

    <!-- 分配详情对话框 -->
    <AllocationDetailDialog
      ref="allocationDetailDialogRef"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElCard, ElRow, ElCol, ElButton, ElIcon, ElLink, ElTag, ElProgress, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessage, ElBreadcrumb, ElBreadcrumbItem } from 'element-plus'
import { Magic, Clock, CircleCheck, Warning, CircleClose, ArrowDown } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
import AllocationDialog from './components/AllocationDialog.vue'
import BatchAllocationDialog from './components/BatchAllocationDialog.vue'
import AllocationDetailDialog from './components/AllocationDetailDialog.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import type { WmsInventoryAllocationSummaryResp } from '@/types/wms/inventoryAllocation'
import { useInventoryAllocationStore } from '@/store/wms/inventoryAllocation'

// Store
const inventoryAllocationStore = useInventoryAllocationStore()

// 响应式数据
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const allocationDialogRef = ref<InstanceType<typeof AllocationDialog>>()
const batchAllocationDialogRef = ref<InstanceType<typeof BatchAllocationDialog>>()
const allocationDetailDialogRef = ref<InstanceType<typeof AllocationDetailDialog>>()

const selectedRows = ref<WmsInventoryAllocationSummaryResp[]>([])

// 统计数据
const allocationStats = reactive({
  pending: 0,
  allocated: 0,
  partial: 0,
  failed: 0
})

// 计算属性
const list = computed(() => inventoryAllocationStore.list)
const total = computed(() => inventoryAllocationStore.total)
const loading = computed(() => inventoryAllocationStore.loading)

// 分页配置
const pagination = reactive<PaginationConfig>({
  total: total,
  currentPage: computed(() => inventoryAllocationStore.pagination.pageNum),
  pageSize: computed(() => inventoryAllocationStore.pagination.pageSize)
})

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'notificationNo', label: '出库通知单号', minWidth: 140, slot: true },
  { prop: 'customerName', label: '客户名称', width: 120 },
  { prop: 'allocationStatus', label: '分配状态', width: 100, slot: true },
  { prop: 'allocationProgress', label: '分配进度', width: 120, slot: true },
  { prop: 'strategy', label: '分配策略', width: 100, slot: true },
  { prop: 'totalItems', label: '总物料数', width: 100 },
  { prop: 'allocatedItems', label: '已分配数', width: 100 },
  { prop: 'totalQty', label: '总数量', width: 100 },
  { prop: 'allocatedQty', label: '已分配量', width: 100 },
  { prop: 'createdAt', label: '创建时间', width: 150 },
  { prop: 'allocatedAt', label: '分配时间', width: 150 }
])

// 工具栏配置
const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showFilter: true,
  showExport: checkPermission('allocation:export'),
  batchOperations: [
    {
      label: '批量分配',
      value: 'batchAllocate',
      type: 'primary',
      permission: 'allocation:batch'
    },
    {
      label: '批量释放',
      value: 'batchRelease',
      type: 'warning',
      permission: 'allocation:release'
    }
  ],
  filterFields: [
    {
      prop: 'notificationNo',
      label: '出库通知单号',
      type: 'input',
      placeholder: '请输入出库通知单号'
    },
    {
      prop: 'customerName',
      label: '客户名称',
      type: 'input',
      placeholder: '请输入客户名称'
    },
    {
      prop: 'allocationStatus',
      label: '分配状态',
      type: 'select',
      placeholder: '请选择分配状态',
      options: [
        { label: '待分配', value: 'PENDING' },
        { label: '部分分配', value: 'PARTIAL' },
        { label: '已分配', value: 'ALLOCATED' },
        { label: '分配失败', value: 'FAILED' }
      ]
    },
    {
      prop: 'strategy',
      label: '分配策略',
      type: 'select',
      placeholder: '请选择分配策略',
      options: [
        { label: 'FIFO', value: 'FIFO' },
        { label: 'LIFO', value: 'LIFO' },
        { label: '批次优先', value: 'BATCH_FIRST' },
        { label: '就近原则', value: 'NEAREST' }
      ]
    },
    {
      prop: 'dateRange',
      label: '创建时间',
      type: 'daterange',
      placeholder: '请选择时间范围'
    }
  ]
}))

// 方法
const loadData = async () => {
  try {
    await inventoryAllocationStore.fetchList()
    updateStats()
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const updateStats = () => {
  const stats = {
    pending: 0,
    allocated: 0,
    partial: 0,
    failed: 0
  }
  
  list.value.forEach(item => {
    switch (item.allocationStatus) {
      case 'PENDING':
        stats.pending++
        break
      case 'ALLOCATED':
        stats.allocated++
        break
      case 'PARTIAL':
        stats.partial++
        break
      case 'FAILED':
        stats.failed++
        break
    }
  })
  
  Object.assign(allocationStats, stats)
}

const handlePageChange = (page: number) => {
  inventoryAllocationStore.searchForm.pageNum = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  inventoryAllocationStore.searchForm.pageSize = size
  inventoryAllocationStore.searchForm.pageNum = 1
  loadData()
}

const handleSelectionChange = (rows: WmsInventoryAllocationSummaryResp[]) => {
  selectedRows.value = rows
}

const handleFilterChange = (filters: Record<string, any>) => {
  // 处理日期范围
  if (filters.dateRange && filters.dateRange.length === 2) {
    filters.createdAtStart = filters.dateRange[0]
    filters.createdAtEnd = filters.dateRange[1]
    delete filters.dateRange
  }
  
  Object.assign(inventoryAllocationStore.searchForm, filters)
  inventoryAllocationStore.searchForm.pageNum = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const handleView = (row: WmsInventoryAllocationSummaryResp) => {
  allocationDetailDialogRef.value?.open(row.id)
}

const handleAllocate = (row: WmsInventoryAllocationSummaryResp) => {
  allocationDialogRef.value?.open(row.id)
}

const handleBatchAllocate = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要分配的记录')
    return
  }
  
  const ids = selectedRows.value.map(row => row.id)
  batchAllocationDialogRef.value?.open(ids)
}

const handleAllocationConfirm = () => {
  ElMessage.success('分配成功')
  loadData()
}

const handleAllocationCancel = () => {
  // 分配取消处理
}

const handleBatchAllocationConfirm = () => {
  ElMessage.success('批量分配成功')
  loadData()
}

const handleBatchAllocationCancel = () => {
  // 批量分配取消处理
}

// 权限检查
const checkPermission = (permission: string): boolean => {
  // 这里应该实现实际的权限检查逻辑
  return true // 临时返回true
}

// 工具方法
const canAllocate = (row: WmsInventoryAllocationSummaryResp): boolean => {
  return ['PENDING', 'PARTIAL', 'FAILED'].includes(row.allocationStatus)
}

const getAllocationStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING': 'info',
    'PARTIAL': 'warning',
    'ALLOCATED': 'success',
    'FAILED': 'danger'
  }
  return typeMap[status] || 'info'
}

const formatAllocationStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'PENDING': '待分配',
    'PARTIAL': '部分分配',
    'ALLOCATED': '已分配',
    'FAILED': '分配失败'
  }
  return labelMap[status] || '未知'
}

const formatStrategy = (strategy: string) => {
  const labelMap: Record<string, string> = {
    'FIFO': 'FIFO',
    'LIFO': 'LIFO',
    'BATCH_FIRST': '批次优先',
    'NEAREST': '就近原则'
  }
  return labelMap[strategy] || strategy
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const getDropdownItems = (row: WmsInventoryAllocationSummaryResp) => {
  const items = []

  if (row.allocationStatus === 'ALLOCATED' && checkPermission('allocation:release')) {
    items.push({ command: 'release', label: '释放分配', disabled: false })
  }

  if (row.allocationStatus === 'FAILED' && checkPermission('allocation:retry')) {
    items.push({ command: 'retry', label: '重新分配', disabled: false })
  }

  if (checkPermission('allocation:detail')) {
    items.push({ command: 'detail', label: '分配详情', disabled: false })
  }

  return items
}

const handleDropdownCommand = async (command: string, row: WmsInventoryAllocationSummaryResp) => {
  switch (command) {
    case 'release':
      await handleRelease(row)
      break
    case 'retry':
      await handleRetry(row)
      break
    case 'detail':
      handleView(row)
      break
  }
}

const handleRelease = async (row: WmsInventoryAllocationSummaryResp) => {
  try {
    await inventoryAllocationStore.releaseAllocation(row.id)
    ElMessage.success('释放成功')
    loadData()
  } catch (error) {
    ElMessage.error('释放失败')
    console.error(error)
  }
}

const handleRetry = async (row: WmsInventoryAllocationSummaryResp) => {
  try {
    await inventoryAllocationStore.retryAllocation(row.id)
    ElMessage.success('重新分配成功')
    loadData()
  } catch (error) {
    ElMessage.error('重新分配失败')
    console.error(error)
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.inventory-allocation-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.stat-icon-pending {
  color: #909399;
}

.stat-icon-allocated {
  color: #67c23a;
}

.stat-icon-partial {
  color: #e6a23c;
}

.stat-icon-failed {
  color: #f56c6c;
}

.main-content {
  background: #fff;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-container .el-progress {
  flex: 1;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 35px;
}
</style>
