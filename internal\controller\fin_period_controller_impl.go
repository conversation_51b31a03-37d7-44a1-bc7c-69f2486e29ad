package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"

	"github.com/kataras/iris/v12"
)

// FiscalPeriodController 定义了会计期间控制器的接口。
type FiscalPeriodController interface {
	GenerateForYear(ctx iris.Context)
	ChangeStatus(ctx iris.Context)
	List(ctx iris.Context)
}

// FiscalPeriodControllerImpl 是 FiscalPeriodController 的具体实现。
// 它嵌入了 BaseControllerImpl 以继承通用的控制器逻辑。
type FiscalPeriodControllerImpl struct {
	*BaseControllerImpl
	service service.FiscalPeriodService
}

// NewFiscalPeriodControllerImpl 创建一个新的会计期间控制器实例。
// 它遵循标准的工厂模式，只接收一个 ControllerManager 作为参数，
// 并从管理器中获取所需的服务，实现了依赖关系的解耦。
func NewFiscalPeriodControllerImpl(cm *ControllerManager) *FiscalPeriodControllerImpl {
	return &FiscalPeriodControllerImpl{
		BaseControllerImpl: NewBaseController(cm),
		service:            cm.GetServiceManager().GetFiscalPeriodService(),
	}
}

// GenerateForYear 处理生成年度会计期间的请求。
func (c *FiscalPeriodControllerImpl) GenerateForYear(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fiscal_period")

	var reqDTO dto.FiscalPeriodGenerateDTO
	// 使用 iris 标准的 ReadJSON 方法来解析和验证请求体。
	if err := ctx.ReadJSON(&reqDTO); err != nil {
		// 当请求参数无效时，创建一个标准的参数错误并使用 HandleError 进行统一处理。
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "请求体解析失败或参数无效").WithCause(err)
		c.HandleError(ctx, paramErr, "GenerateForYear")
		return
	}

	// 调用服务层方法，并传递上下文。服务层会自行从上下文中提取用户信息。
	if err := c.service.GenerateForYear(ctx.Request().Context(), reqDTO); err != nil {
		c.HandleError(ctx, err, "GenerateForYear")
		return
	}
	// 使用 BaseController 提供的标准方法返回成功的响应。
	c.SuccessWithMessage(ctx, "财务年度生成成功", nil)
}

// ChangeStatus 处理更改会计期间状态的请求。
func (c *FiscalPeriodControllerImpl) ChangeStatus(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fiscal_period")

	// 使用 BaseController 提供的辅助方法来安全地获取和转换路径参数。
	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "ChangeStatus")
		return
	}

	var reqDTO dto.UpdateFiscalPeriodStatusDTO
	if err := ctx.ReadJSON(&reqDTO); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "请求体解析失败或参数无效").WithCause(err)
		c.HandleError(ctx, paramErr, "ChangeStatus")
		return
	}

	if err := c.service.ChangeStatus(ctx.Request().Context(), id, reqDTO); err != nil {
		c.HandleError(ctx, err, "ChangeStatus")
		return
	}
	c.SuccessWithMessage(ctx, "状态更新成功", nil)
}

// List 处理根据条件查询会计期间列表的请求。
func (c *FiscalPeriodControllerImpl) List(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fiscal_period")

	var queryDTO dto.FiscalPeriodQueryDTO
	// 使用 iris 标准的 ReadQuery 方法来解析 URL 查询参数。
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		c.HandleError(ctx, paramErr, "List")
		return
	}

	// 使用 response 包中的辅助函数来构建分页查询对象。
	pageQueryInstance := response.BuildPageQuery(ctx)
	queryDTO.Pagination = *pageQueryInstance

	result, err := c.service.List(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, "List")
		return
	}
	c.Success(ctx, result)
}
