# 审计日志模块说明文档

## 简介

审计日志模块提供了全面的操作审计功能，用于记录系统中的关键操作，支持多种存储方式、灵活的日志格式和丰富的查询功能。该模块主要用于满足安全合规要求、问题排查和用户行为分析。

## 目录结构

```go
audit/
├── audit.go   # 审计日志核心实现
├── types.go   # 审计日志类型定义
└── README.md  # 说明文档
```

## 核心概念

### 审计事件类型

```go
// 审计事件类型常量
const (
    EVENT_TYPE_LOGIN        = "LOGIN"        // 登录事件
    EVENT_TYPE_LOGOUT       = "LOGOUT"       // 登出事件
    EVENT_TYPE_CREATE       = "CREATE"       // 创建操作
    EVENT_TYPE_UPDATE       = "UPDATE"       // 更新操作
    EVENT_TYPE_DELETE       = "DELETE"       // 删除操作
    EVENT_TYPE_QUERY        = "QUERY"        // 查询操作
    EVENT_TYPE_IMPORT       = "IMPORT"       // 导入操作
    EVENT_TYPE_EXPORT       = "EXPORT"       // 导出操作
    EVENT_TYPE_AUTHORIZE    = "AUTHORIZE"    // 授权操作
    EVENT_TYPE_CONFIGURATION = "CONFIGURATION" // 配置操作
    EVENT_TYPE_SYSTEM       = "SYSTEM"       // 系统操作
    EVENT_TYPE_OTHER        = "OTHER"        // 其他操作
)
```

### 审计事件状态

```go
// 审计事件状态常量
const (
    EVENT_STATUS_SUCCESS = "SUCCESS" // 成功
    EVENT_STATUS_FAILURE = "FAILURE" // 失败
    EVENT_STATUS_WARNING = "WARNING" // 警告
    EVENT_STATUS_INFO    = "INFO"    // 信息
)
```

### 审计日志结构

```go
// AuditEvent 审计事件结构
type AuditEvent struct {
    ID            string                 `json:"id"`            // 事件ID
    Timestamp     time.Time              `json:"timestamp"`     // 事件时间
    Type          string                 `json:"type"`          // 事件类型
    Status        string                 `json:"status"`        // 事件状态
    UserID        string                 `json:"user_id"`       // 用户ID
    Username      string                 `json:"username"`      // 用户名
    TenantID      string                 `json:"tenant_id"`     // 租户ID
    TenantName    string                 `json:"tenant_name"`   // 租户名称
    ClientIP      string                 `json:"client_ip"`     // 客户端IP
    UserAgent     string                 `json:"user_agent"`    // 用户代理
    ResourceType  string                 `json:"resource_type"` // 资源类型
    ResourceID    string                 `json:"resource_id"`   // 资源ID
    ResourceName  string                 `json:"resource_name"` // 资源名称
    Operation     string                 `json:"operation"`     // 操作
    RequestURL    string                 `json:"request_url"`   // 请求URL
    RequestMethod string                 `json:"request_method"`// 请求方法
    RequestParams map[string]interface{} `json:"request_params"`// 请求参数
    OldValue      interface{}            `json:"old_value"`     // 旧值
    NewValue      interface{}            `json:"new_value"`     // 新值
    Duration      int64                  `json:"duration"`      // 持续时间(毫秒)
    ErrorCode     string                 `json:"error_code"`    // 错误代码
    ErrorMessage  string                 `json:"error_message"` // 错误消息
    Module        string                 `json:"module"`        // 模块
    Function      string                 `json:"function"`      // 功能
    Details       map[string]interface{} `json:"details"`       // 详细信息
    TraceID       string                 `json:"trace_id"`      // 跟踪ID
    SpanID        string                 `json:"span_id"`       // 跨度ID
}
```

## 核心接口

`AuditLogger` 接口定义了审计日志的基本操作：

```go
// AuditLogger 审计日志接口
type AuditLogger interface {
    // 记录审计事件
    Log(event *AuditEvent) error
    
    // 批量记录审计事件
    LogBatch(events []*AuditEvent) error
    
    // 查询审计事件
    Query(query *AuditQuery) ([]*AuditEvent, error)
    
    // 分页查询审计事件
    QueryPage(query *AuditQuery, page, size int) (*AuditPageResult, error)
    
    // 统计审计事件
    Count(query *AuditQuery) (int64, error)
    
    // 清理审计事件
    Clean(before time.Time) (int64, error)
    
    // 导出审计事件
    Export(query *AuditQuery, format string, writer io.Writer) error
    
    // 关闭审计日志
    Close() error
}
```

## 审计日志实现

`DefaultAuditLogger` 是 `AuditLogger` 接口的默认实现，具有以下特点：

1. 多存储支持：支持数据库、文件、Elasticsearch等多种存储方式
2. 异步处理：使用缓冲通道异步处理审计日志，不阻塞业务流程
3. 批量写入：定期批量写入存储，提高性能
4. 失败重试：支持失败重试机制，确保日志不丢失
5. 灵活过滤：支持按类型、状态、用户等多维度过滤
6. 数据脱敏：支持敏感信息自动脱敏
7. 高性能：优化的写入和查询性能
8. 可扩展：支持自定义存储和格式化

## 使用示例

### 创建审计日志实例

```go
// 创建默认配置的审计日志实例
logger := audit.NewDefaultAuditLogger(db)

// 创建自定义配置的审计日志实例
config := &audit.AuditConfig{
    BufferSize:    1000,
    FlushInterval: time.Second * 5,
    AsyncWrite:    true,
    RetryTimes:    3,
    RetryInterval: time.Second,
    Sensitive:     []string{"password", "token", "secret"},
}
logger := audit.NewAuditLogger(db, config)
```

### 记录审计事件

```go
// 创建审计事件
event := &audit.AuditEvent{
    Type:         audit.EVENT_TYPE_CREATE,
    Status:       audit.EVENT_STATUS_SUCCESS,
    UserID:       "1001",
    Username:     "admin",
    TenantID:     "tenant_001",
    ClientIP:     "*************",
    ResourceType: "user",
    ResourceID:   "2001",
    ResourceName: "张三",
    Operation:    "创建用户",
    RequestURL:   "/api/users",
    RequestMethod: "POST",
    RequestParams: map[string]interface{}{
        "username": "zhangsan",
        "email":    "<EMAIL>",
    },
    NewValue: map[string]interface{}{
        "id":       "2001",
        "username": "zhangsan",
        "email":    "<EMAIL>",
    },
    Module:   "用户管理",
    Function: "用户创建",
    TraceID:  "trace_12345",
}

// 记录审计事件
err := logger.Log(event)
```

### 使用审计构建器

```go
// 使用构建器创建审计事件
event := audit.NewAuditEventBuilder().
    WithType(audit.EVENT_TYPE_UPDATE).
    WithStatus(audit.EVENT_STATUS_SUCCESS).
    WithUser("1001", "admin").
    WithTenant("tenant_001", "总公司").
    WithResource("user", "2001", "张三").
    WithOperation("更新用户").
    WithRequest("/api/users/2001", "PUT", map[string]interface{}{"email": "<EMAIL>"}).
    WithChange(oldUser, newUser).
    WithModule("用户管理", "用户更新").
    WithTraceID("trace_12345").
    Build()

// 记录审计事件
err := logger.Log(event)
```

### 从HTTP请求创建审计事件

```go
// 从HTTP请求创建审计事件
event, err := audit.NewAuditEventFromRequest(c, audit.EVENT_TYPE_QUERY, audit.EVENT_STATUS_SUCCESS)
if err != nil {
    // 处理错误
}

// 设置其他字段
event.ResourceType = "user"
event.Operation = "查询用户列表"
event.Module = "用户管理"
event.Function = "用户查询"

// 记录审计事件
err = logger.Log(event)
```

### 查询审计事件

```go
// 创建查询条件
query := &audit.AuditQuery{
    StartTime:    time.Now().Add(-24 * time.Hour),
    EndTime:      time.Now(),
    Types:        []string{audit.EVENT_TYPE_LOGIN, audit.EVENT_TYPE_LOGOUT},
    Status:       []string{audit.EVENT_STATUS_SUCCESS},
    UserID:       "1001",
    ResourceType: "user",
    SortField:    "timestamp",
    SortOrder:    "desc",
}

// 分页查询
result, err := logger.QueryPage(query, 1, 10)
if err != nil {
    // 处理错误
}

// 使用查询结果
fmt.Printf("总记录数: %d\n", result.Total)
for _, event := range result.Data {
    fmt.Printf("事件ID: %s, 时间: %s, 用户: %s, 操作: %s\n",
        event.ID, event.Timestamp, event.Username, event.Operation)
}
```

### 导出审计事件

```go
// 创建查询条件
query := &audit.AuditQuery{
    StartTime: time.Now().Add(-7 * 24 * time.Hour),
    EndTime:   time.Now(),
    Module:    "用户管理",
}

// 创建文件
file, err := os.Create("audit_log.csv")
if err != nil {
    // 处理错误
}
defer file.Close()

// 导出为CSV
err = logger.Export(query, "csv", file)
if err != nil {
    // 处理错误
}
```

### 清理过期审计事件

```go
// 清理30天前的审计事件
count, err := logger.Clean(time.Now().Add(-30 * 24 * time.Hour))
if err != nil {
    // 处理错误
}
fmt.Printf("已清理 %d 条审计记录\n", count)
```

## 中间件集成

### Iris中间件

```go
// 创建审计中间件
auditMiddleware := audit.NewIrisAuditMiddleware(logger, &audit.AuditMiddlewareConfig{
    SkipPaths:     []string{"/api/health", "/api/metrics"},
    IncludeBody:   true,
    BodySizeLimit: 1024 * 10, // 10KB
    SensitiveParams: []string{"password", "token", "secret"},
})

// 注册中间件
app.Use(auditMiddleware)
```

## 最佳实践

1. **选择性审计**：只记录关键操作，避免记录过多无用信息
2. **异步处理**：使用异步模式记录审计日志，避免影响业务性能
3. **数据脱敏**：对敏感信息进行脱敏处理，保护用户隐私
4. **定期清理**：设置合理的保留策略，定期清理过期审计日志
5. **批量查询**：使用分页查询大量审计记录，避免内存溢出
6. **索引优化**：为常用查询字段创建索引，提高查询性能
7. **监控告警**：对异常操作设置监控和告警机制
8. **定期备份**：定期备份重要的审计日志数据

## 注意事项

1. 审计日志可能包含敏感信息，确保存储安全和访问控制
2. 大量审计日志会占用存储空间，需要合理规划存储容量
3. 频繁的审计日志写入可能影响系统性能，建议使用异步模式
4. 审计日志查询可能较为耗时，建议使用索引和分页查询
5. 审计日志应该是只追加的，不应该允许修改或删除（除了过期清理）
6. 在分布式系统中，确保审计日志的时间戳一致性
7. 审计日志应该包含足够的上下文信息，便于问题排查和分析
