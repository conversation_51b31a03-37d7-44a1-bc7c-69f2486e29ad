package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsPickingTaskRepository 定义拣货任务仓库接口
type WmsPickingTaskRepository interface {
	BaseRepository[entity.WmsPickingTask, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsPickingTaskQueryReq) (*response.PageResult, error)

	// 根据任务号查找
	FindByTaskNo(ctx context.Context, taskNo string) (*entity.WmsPickingTask, error)

	// 检查任务号是否存在
	IsTaskNoExist(ctx context.Context, taskNo string, excludeID uint) (bool, error)

	// 根据出库通知单ID查询任务
	FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsPickingTask, error)

	// 根据拣货策略查询任务
	FindByPickingStrategy(ctx context.Context, strategy string) ([]*entity.WmsPickingTask, error)

	// 根据波次号查询任务
	FindByWaveNo(ctx context.Context, waveNo string) ([]*entity.WmsPickingTask, error)

	// 根据分配用户ID查询任务
	FindByAssignedUserID(ctx context.Context, userID uint) ([]*entity.WmsPickingTask, error)

	// 获取指定状态的任务
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsPickingTask, error)

	// 批量创建任务
	BatchCreate(ctx context.Context, tasks []*entity.WmsPickingTask) error

	// 更新状态
	UpdateStatus(ctx context.Context, id uint, status string, remark string) error

	// 批量更新状态
	BatchUpdateStatus(ctx context.Context, ids []uint, status string, remark string) error

	// 分配任务给用户
	AssignTask(ctx context.Context, id uint, userID uint) error

	// 批量分配任务
	BatchAssignTask(ctx context.Context, ids []uint, userID uint) error

	// 开始任务
	StartTask(ctx context.Context, id uint) error

	// 完成任务
	CompleteTask(ctx context.Context, id uint, remark string) error

	// 取消任务
	CancelTask(ctx context.Context, id uint, reason string) error

	// 获取待分配的任务
	GetPendingAssignment(ctx context.Context) ([]*entity.WmsPickingTask, error)

	// 获取进行中的任务
	GetInProgressTasks(ctx context.Context, userID *uint) ([]*entity.WmsPickingTask, error)

	// 根据优先级查询任务
	GetByPriority(ctx context.Context, priority int) ([]*entity.WmsPickingTask, error)

	// 根据日期范围查询任务
	GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsPickingTask, error)

	// 获取任务统计信息
	GetTaskStats(ctx context.Context, userID *uint, startDate, endDate *time.Time) (map[string]interface{}, error)

	// 获取过期任务
	GetOverdueTasks(ctx context.Context) ([]*entity.WmsPickingTask, error)

	// 更新任务进度
	UpdateTaskProgress(ctx context.Context, id uint) error
}

// wmsPickingTaskRepository 拣货任务仓库实现
type wmsPickingTaskRepository struct {
	BaseRepositoryImpl[entity.WmsPickingTask, uint]
}

// NewWmsPickingTaskRepository 创建拣货任务仓库
func NewWmsPickingTaskRepository(db *gorm.DB) WmsPickingTaskRepository {
	return &wmsPickingTaskRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsPickingTask, uint]{
			db: db,
		},
	}
}

// GetPage 获取拣货任务分页数据
func (r *wmsPickingTaskRepository) GetPage(ctx context.Context, query *dto.WmsPickingTaskQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.TaskNo != nil && *query.TaskNo != "" {
		conditions = append(conditions, NewLikeCondition("task_no", *query.TaskNo))
	}
	if query.NotificationID != nil {
		conditions = append(conditions, NewEqualCondition("notification_id", *query.NotificationID))
	}
	if query.PickingStrategy != nil && *query.PickingStrategy != "" {
		conditions = append(conditions, NewEqualCondition("picking_strategy", *query.PickingStrategy))
	}
	if query.WaveNo != nil && *query.WaveNo != "" {
		conditions = append(conditions, NewEqualCondition("wave_no", *query.WaveNo))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.Priority != nil {
		conditions = append(conditions, NewEqualCondition("priority", *query.Priority))
	}
	if query.AssignedUserID != nil {
		conditions = append(conditions, NewEqualCondition("assigned_user_id", *query.AssignedUserID))
	}
	if query.CreatedAtStart != nil && query.CreatedAtEnd != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *query.CreatedAtStart, *query.CreatedAtEnd))
	}

	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// FindByTaskNo 根据任务号查找
func (r *wmsPickingTaskRepository) FindByTaskNo(ctx context.Context, taskNo string) (*entity.WmsPickingTask, error) {
	var task entity.WmsPickingTask
	db := r.GetDB(ctx)

	// 预加载关联的明细
	result := db.Preload("Details").Where("task_no = ?", taskNo).First(&task)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "拣货任务不存在").WithCause(result.Error)
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据任务号查询失败: taskNo=%s", taskNo)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询拣货任务失败").WithCause(result.Error)
	}

	return &task, nil
}

// IsTaskNoExist 检查任务号是否存在
func (r *wmsPickingTaskRepository) IsTaskNoExist(ctx context.Context, taskNo string, excludeID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("task_no", taskNo),
	}
	if excludeID > 0 {
		conditions = append(conditions, NewNotEqualCondition("id", excludeID))
	}
	return r.Exists(ctx, conditions)
}

// FindByNotificationID 根据出库通知单ID查询任务
func (r *wmsPickingTaskRepository) FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("notification_id", notificationID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByPickingStrategy 根据拣货策略查询任务
func (r *wmsPickingTaskRepository) FindByPickingStrategy(ctx context.Context, strategy string) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("picking_strategy", strategy),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByWaveNo 根据波次号查询任务
func (r *wmsPickingTaskRepository) FindByWaveNo(ctx context.Context, waveNo string) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("wave_no", waveNo),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByAssignedUserID 根据分配用户ID查询任务
func (r *wmsPickingTaskRepository) FindByAssignedUserID(ctx context.Context, userID uint) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("assigned_user_id", userID),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "assigned_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByStatus 获取指定状态的任务
func (r *wmsPickingTaskRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// BatchCreate 批量创建任务
func (r *wmsPickingTaskRepository) BatchCreate(ctx context.Context, tasks []*entity.WmsPickingTask) error {
	if len(tasks) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(tasks, 100) // 每批100条
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建拣货任务失败: count=%d", len(tasks))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建拣货任务失败").WithCause(result.Error)
	}

	return nil
}

// UpdateStatus 更新任务状态
func (r *wmsPickingTaskRepository) UpdateStatus(ctx context.Context, id uint, status string, remark string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{"status": status}
	if remark != "" {
		updates["remark"] = remark
	}

	result := db.Model(&entity.WmsPickingTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新任务状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新任务状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在")
	}

	return nil
}

// BatchUpdateStatus 批量更新状态
func (r *wmsPickingTaskRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string, remark string) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	updates := map[string]interface{}{"status": status}
	if remark != "" {
		updates["remark"] = remark
	}

	result := db.Model(&entity.WmsPickingTask{}).Where("id IN ?", ids).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量更新任务状态失败: ids=%v, status=%s", ids, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新任务状态失败").WithCause(result.Error)
	}

	return nil
}

// AssignTask 分配任务给用户
func (r *wmsPickingTaskRepository) AssignTask(ctx context.Context, id uint, userID uint) error {
	db := r.GetDB(ctx)
	now := time.Now()
	updates := map[string]interface{}{
		"assigned_user_id": userID,
		"assigned_time":    &now,
		"status":           string(entity.PickingTaskStatusAssigned),
	}

	result := db.Model(&entity.WmsPickingTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("分配任务失败: id=%d, userID=%d", id, userID)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "分配任务失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在")
	}

	return nil
}

// BatchAssignTask 批量分配任务
func (r *wmsPickingTaskRepository) BatchAssignTask(ctx context.Context, ids []uint, userID uint) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	now := time.Now()
	updates := map[string]interface{}{
		"assigned_user_id": userID,
		"assigned_time":    &now,
		"status":           string(entity.PickingTaskStatusAssigned),
	}

	result := db.Model(&entity.WmsPickingTask{}).Where("id IN ?", ids).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量分配任务失败: ids=%v, userID=%d", ids, userID)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量分配任务失败").WithCause(result.Error)
	}

	return nil
}

// StartTask 开始任务
func (r *wmsPickingTaskRepository) StartTask(ctx context.Context, id uint) error {
	db := r.GetDB(ctx)
	now := time.Now()
	updates := map[string]interface{}{
		"start_time": &now,
		"status":     string(entity.PickingTaskStatusInProgress),
	}

	result := db.Model(&entity.WmsPickingTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("开始任务失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "开始任务失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在")
	}

	return nil
}

// CompleteTask 完成任务
func (r *wmsPickingTaskRepository) CompleteTask(ctx context.Context, id uint, remark string) error {
	db := r.GetDB(ctx)
	now := time.Now()
	updates := map[string]interface{}{
		"complete_time": &now,
		"status":        string(entity.PickingTaskStatusCompleted),
	}
	if remark != "" {
		updates["remark"] = remark
	}

	result := db.Model(&entity.WmsPickingTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("完成任务失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "完成任务失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在")
	}

	return nil
}

// CancelTask 取消任务
func (r *wmsPickingTaskRepository) CancelTask(ctx context.Context, id uint, reason string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{
		"status": string(entity.PickingTaskStatusCancelled),
		"remark": reason,
	}

	result := db.Model(&entity.WmsPickingTask{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("取消任务失败: id=%d, reason=%s", id, reason)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "取消任务失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在")
	}

	return nil
}

// GetPendingAssignment 获取待分配的任务
func (r *wmsPickingTaskRepository) GetPendingAssignment(ctx context.Context) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", string(entity.PickingTaskStatusPending)),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetInProgressTasks 获取进行中的任务
func (r *wmsPickingTaskRepository) GetInProgressTasks(ctx context.Context, userID *uint) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewInCondition("status", []interface{}{
			string(entity.PickingTaskStatusAssigned),
			string(entity.PickingTaskStatusInProgress),
		}),
	}
	if userID != nil {
		conditions = append(conditions, NewEqualCondition("assigned_user_id", *userID))
	}

	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "assigned_time", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByPriority 根据优先级查询任务
func (r *wmsPickingTaskRepository) GetByPriority(ctx context.Context, priority int) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("priority", priority),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByDateRange 根据日期范围查询任务
func (r *wmsPickingTaskRepository) GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsPickingTask, error) {
	conditions := []QueryCondition{
		NewBetweenCondition("created_at", startDate, endDate),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetTaskStats 获取任务统计信息
func (r *wmsPickingTaskRepository) GetTaskStats(ctx context.Context, userID *uint, startDate, endDate *time.Time) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var stats struct {
		TotalCount      int64   `json:"total_count"`
		PendingCount    int64   `json:"pending_count"`
		AssignedCount   int64   `json:"assigned_count"`
		InProgressCount int64   `json:"in_progress_count"`
		CompletedCount  int64   `json:"completed_count"`
		CancelledCount  int64   `json:"cancelled_count"`
		AvgTime         float64 `json:"avg_time"`
	}

	query := db.Model(&entity.WmsPickingTask{})
	if userID != nil {
		query = query.Where("assigned_user_id = ?", *userID)
	}
	if startDate != nil && endDate != nil {
		query = query.Where("created_at BETWEEN ? AND ?", *startDate, *endDate)
	}

	// 总数统计
	if err := query.Count(&stats.TotalCount).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取拣货任务总数统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取统计数据失败").WithCause(err)
	}

	// 各状态统计
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	statusQuery := db.Model(&entity.WmsPickingTask{}).Select("status, COUNT(*) as count")
	if userID != nil {
		statusQuery = statusQuery.Where("assigned_user_id = ?", *userID)
	}
	if startDate != nil && endDate != nil {
		statusQuery = statusQuery.Where("created_at BETWEEN ? AND ?", *startDate, *endDate)
	}

	if err := statusQuery.Group("status").Scan(&statusCounts).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取拣货任务状态统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取状态统计失败").WithCause(err)
	}

	// 填充状态统计
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(entity.PickingTaskStatusPending):
			stats.PendingCount = sc.Count
		case string(entity.PickingTaskStatusAssigned):
			stats.AssignedCount = sc.Count
		case string(entity.PickingTaskStatusInProgress):
			stats.InProgressCount = sc.Count
		case string(entity.PickingTaskStatusCompleted):
			stats.CompletedCount = sc.Count
		case string(entity.PickingTaskStatusCancelled):
			stats.CancelledCount = sc.Count
		}
	}

	// 平均完成时间统计（分钟）
	avgQuery := db.Model(&entity.WmsPickingTask{}).
		Select("AVG(EXTRACT(EPOCH FROM (complete_time - start_time))/60) as avg_time").
		Where("status = ? AND start_time IS NOT NULL AND complete_time IS NOT NULL", string(entity.PickingTaskStatusCompleted))

	if userID != nil {
		avgQuery = avgQuery.Where("assigned_user_id = ?", *userID)
	}
	if startDate != nil && endDate != nil {
		avgQuery = avgQuery.Where("created_at BETWEEN ? AND ?", *startDate, *endDate)
	}

	if err := avgQuery.Scan(&stats.AvgTime).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取平均完成时间统计失败")
		// 不返回错误，只记录日志
		stats.AvgTime = 0
	}

	result := map[string]interface{}{
		"total_count":       stats.TotalCount,
		"pending_count":     stats.PendingCount,
		"assigned_count":    stats.AssignedCount,
		"in_progress_count": stats.InProgressCount,
		"completed_count":   stats.CompletedCount,
		"cancelled_count":   stats.CancelledCount,
		"avg_time":          stats.AvgTime,
	}

	return result, nil
}

// GetOverdueTasks 获取过期任务
func (r *wmsPickingTaskRepository) GetOverdueTasks(ctx context.Context) ([]*entity.WmsPickingTask, error) {
	now := time.Now()
	// 假设任务超过24小时未完成视为过期
	overdueTime := now.Add(-24 * time.Hour)

	conditions := []QueryCondition{
		NewInCondition("status", []interface{}{
			string(entity.PickingTaskStatusAssigned),
			string(entity.PickingTaskStatusInProgress),
		}),
		NewLessThanCondition("created_at", overdueTime),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
		{Field: "priority", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// UpdateTaskProgress 更新任务进度
func (r *wmsPickingTaskRepository) UpdateTaskProgress(ctx context.Context, id uint) error {
	// 这个方法可以根据任务明细的完成情况来更新任务的整体进度
	// 这里简化实现，实际可能需要计算明细的完成率
	db := r.GetDB(ctx)

	// 获取任务的明细完成情况
	var progress struct {
		TotalDetails     int64 `json:"total_details"`
		CompletedDetails int64 `json:"completed_details"`
	}

	if err := db.Model(&entity.WmsPickingTaskDetail{}).
		Where("task_id = ?", id).
		Select("COUNT(*) as total_details, SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as completed_details", string(entity.PickingDetailStatusCompleted)).
		Scan(&progress).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取任务进度失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取任务进度失败").WithCause(err)
	}

	// 如果所有明细都完成了，更新任务状态为完成
	if progress.TotalDetails > 0 && progress.CompletedDetails == progress.TotalDetails {
		now := time.Now()
		updates := map[string]interface{}{
			"complete_time": &now,
			"status":        string(entity.PickingTaskStatusCompleted),
		}

		if err := db.Model(&entity.WmsPickingTask{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("更新任务完成状态失败: id=%d", id)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新任务完成状态失败").WithCause(err)
		}
	}

	return nil
}
