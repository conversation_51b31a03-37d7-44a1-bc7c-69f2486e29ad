package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
)

// WmsInventoryController 定义基础库存控制器接口
type WmsInventoryController interface {
	// 基础CRUD操作
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 库存操作
	Reserve(ctx iris.Context)
	Release(ctx iris.Context)
	Freeze(ctx iris.Context)
	Unfreeze(ctx iris.Context)

	// 库存调整
	Adjust(ctx iris.Context)
	BatchAdjust(ctx iris.Context)

	// 库存查询
	GetByLocation(ctx iris.Context)
	GetByItem(ctx iris.Context)
	GetByWarehouse(ctx iris.Context)
	GetAvailable(ctx iris.Context)

	// 库存统计
	GetSummary(ctx iris.Context)
	GetStats(ctx iris.Context)

	// 库存验证
	CheckAvailability(ctx iris.Context)
	ValidateOperation(ctx iris.Context)
}

// wmsInventoryControllerImpl 基础库存控制器实现
type WmsInventoryControllerImpl struct {
	BaseControllerImpl
	inventoryService service.WmsInventoryService
}

// NewWmsInventoryControllerImpl 创建基础库存控制器实例
func NewWmsInventoryControllerImpl(cm *ControllerManager) *WmsInventoryControllerImpl {
	return &WmsInventoryControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		inventoryService:   cm.GetServiceManager().GetWmsInventoryService(),
	}
}

// Create 创建库存记录
func (c *WmsInventoryControllerImpl) Create(ctx iris.Context) {
	var req dto.WmsInventoryCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 400, "请求参数格式错误")
		return
	}

	result, err := c.inventoryService.Create(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, result)
}

// Update 更新库存记录
func (c *WmsInventoryControllerImpl) Update(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 400, "无效的ID参数")
		return
	}

	var req dto.WmsInventoryUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 400, "请求参数格式错误")
		return
	}

	result, err := c.inventoryService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, result)
}

// Delete 删除库存记录
func (c *WmsInventoryControllerImpl) Delete(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 400, "无效的ID参数")
		return
	}

	err = c.inventoryService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetByID 根据ID获取库存记录
func (c *WmsInventoryControllerImpl) GetByID(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 400, "无效的ID参数")
		return
	}

	result, err := c.inventoryService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, result)
}

// GetPage 分页查询库存记录
func (c *WmsInventoryControllerImpl) GetPage(ctx iris.Context) {
	var req dto.WmsInventoryQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 400, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, result)
}

// Reserve 预占库存
func (c *WmsInventoryControllerImpl) Reserve(ctx iris.Context) {
	var req dto.WmsInventoryReserveReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 400, "请求参数格式错误")
		return
	}

	err := c.inventoryService.Reserve(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Release 释放库存预占
func (c *WmsInventoryControllerImpl) Release(ctx iris.Context) {
	var req dto.WmsInventoryReleaseReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 400, "请求参数格式错误")
		return
	}

	err := c.inventoryService.Release(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Freeze 冻结库存
func (c *WmsInventoryControllerImpl) Freeze(ctx iris.Context) {
	var req dto.WmsInventoryFreezeReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 400, "请求参数格式错误")
		return
	}

	err := c.inventoryService.Freeze(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Unfreeze 解冻库存
func (c *WmsInventoryControllerImpl) Unfreeze(ctx iris.Context) {
	var req dto.WmsInventoryUnfreezeReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 400, "请求参数格式错误")
		return
	}

	err := c.inventoryService.Unfreeze(ctx.Request().Context(), &req)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetByLocation 根据库位获取库存
func (c *WmsInventoryControllerImpl) GetByLocation(ctx iris.Context) {
	locationIdStr := ctx.Params().Get("locationId")
	locationId, err := strconv.ParseUint(locationIdStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 400, "无效的库位ID参数")
		return
	}

	result, err := c.inventoryService.GetByLocation(ctx.Request().Context(), uint(locationId))
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, result)
}

// GetByItem 根据物料获取库存
func (c *WmsInventoryControllerImpl) GetByItem(ctx iris.Context) {
	itemIdStr := ctx.Params().Get("itemId")
	itemId, err := strconv.ParseUint(itemIdStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 400, "无效的物料ID参数")
		return
	}

	result, err := c.inventoryService.GetByItem(ctx.Request().Context(), uint(itemId))
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, result)
}

// CheckAvailability 检查库存可用性
func (c *WmsInventoryControllerImpl) CheckAvailability(ctx iris.Context) {
	itemIdStr := ctx.URLParam("itemId")
	itemId, err := strconv.ParseUint(itemIdStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 400, "无效的物料ID参数")
		return
	}

	requiredQtyStr := ctx.URLParam("requiredQty")
	requiredQty, err := strconv.ParseFloat(requiredQtyStr, 64)
	if err != nil {
		response.Fail(ctx, 400, "无效的需求数量参数")
		return
	}

	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			response.Fail(ctx, 400, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	isAvailable, err := c.inventoryService.CheckAvailability(ctx.Request().Context(), uint(itemId), requiredQty, warehouseId)
	if err != nil {
		response.Fail(ctx, 500, err.Error())
		return
	}

	response.Success(ctx, map[string]interface{}{
		"itemId":      itemId,
		"requiredQty": requiredQty,
		"warehouseId": warehouseId,
		"isAvailable": isAvailable,
	})
}
