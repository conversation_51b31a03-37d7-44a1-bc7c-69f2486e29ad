<template>
  <div>
    <!-- 物料管理表格 -->
    <VNTable
      ref="itemTableRef"
      :data="itemTableData"
      :columns="itemTableColumns" 
      :loading="itemLoading"
      :pagination="itemPagination"
      :toolbar-config="itemToolbarConfig"
      :operation-buttons="[]" 
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      operation-fixed="right"
      :operation-width="250" 
      @refresh="loadItems" 
      @add="handleAddNewItem"
      @selection-change="handleItemSelectionChange"
      @batch-delete="handleItemBatchDelete"
      @import="handleItemImport"
      @export="handleItemExport"
      @page-change="handleItemPageChange"
      @page-size-change="handleItemPageSizeChange"
      @sort-change="handleItemSortChange" 
      @filter-change="handleItemFilterChange" 
    >
      <!-- 状态列插槽 -->
      <template #column-status="{ row }">
         <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
           {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
         </el-tag>
      </template>

      <!-- 批次管理列插槽 -->
      <template #column-batchManaged="{ row }">
        <el-tag :type="row.batchManaged ? 'success' : 'info'">
          {{ row.batchManaged ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 序列号管理列插槽 -->
      <template #column-serialManaged="{ row }">
        <el-tag :type="row.serialManaged ? 'success' : 'info'">
          {{ row.serialManaged ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 物料分类列插槽 -->
      <template #column-categoryCode="{ row }">
        <span>{{ getDictLabel('material_category', row.categoryCode || '') || '-' }}</span>
      </template>

      <!-- 物料组列插槽 -->
      <template #column-groupCode="{ row }">
        <span>{{ getDictLabel('material_group', row.groupCode || '') || '-' }}</span>
      </template>

      <!-- 基本单位列插槽 -->
      <template #column-baseUnit="{ row }">
        <span>{{ getDictLabel('unit', row.baseUnit) || row.baseUnit }}</span>
      </template>

      <!-- 操作列插槽 -->
      <template #operation="{ row }">
        <el-tooltip content="查看物料" placement="top" v-if="hasPermission('mtl:item:list')">
          <el-button circle :icon="View" size="small" @click="handleViewItem(row)" />
        </el-tooltip>
        <el-tooltip content="编辑物料" placement="top" v-if="hasPermission('mtl:item:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditItem(row)" />
        </el-tooltip>
        <el-tooltip content="包装单位" placement="top" v-if="hasPermission('mtl:item:list')">
           <el-button circle :icon="Setting" type="success" size="small" @click="openPackageUnitManageDialog(row)" />
        </el-tooltip>
        <el-tooltip content="删除物料" placement="top" v-if="hasPermission('mtl:item:delete')">
          <el-button circle :icon="Delete" type="danger" size="small" @click="handleDeleteItem(row)" />
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 物料新增/编辑/查看弹窗 -->
    <el-dialog
      v-model="itemDialogVisible"
      :title="itemDialogTitle"
      width="80%"
      draggable
      align-center
      top="5vh"
      :close-on-click-modal="false"
      @close="handleCloseItemDialog" 
    >
      <VNForm
        v-if="itemDialogVisible" 
        ref="itemFormRef"
        :header-fields="itemFormFields"
        v-model="currentEditingItem"
        :default-columns="4"
        :label-width="'120px'"
        group-title-type="h4"
        :loading="itemFormLoading" 
        :show-actions="false"
        @submit="submitItemForm"
        @cancel="handleCloseItemDialog" 
      >
        <!-- 默认仓库位置插槽 -->
        <template #form-item-defaultLocationId="{ field, formData: formModel }">
          <el-tree-select
            v-if="!isViewing"
            v-model="formModel[field.field]"
            :data="locationTree"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            check-strictly
            :render-after-expand="false"
            placeholder="请选择默认仓库位置"
            clearable
            style="width: 100%;"
          />
          <div v-else class="custom-disabled-text-wrapper">
            <el-text class="custom-disabled-text">
              {{ getLocationDisplayName(formModel[field.field]) || '-' }}
            </el-text>
          </div>
        </template>

        <!-- 自定义表单操作按钮插槽 -->
        <template #actions>
            <template v-if="isViewing">
                <el-button
                  v-if="hasPermission('wms:item:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreview"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('wms:item:print')" 
                  type="primary"
                  :icon="Printer" 
                  @click="handlePrint"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseItemDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitItemForm" :loading="itemFormLoading">提交</el-button>
                <el-button @click="handleCloseItemDialog">取消</el-button>
            </template>
        </template>
      </VNForm>
    </el-dialog> 

    <!-- 包装单位管理模态框 -->
    <el-dialog
      v-model="packageUnitManageDialogVisible"
      :title="'包装单位管理 - ' + (currentManagedItem?.name || '')"
      width="85%"
      top="5vh"
      draggable
      :close-on-click-modal="false"
      :modal="true"
      destroy-on-close
      @close="currentManagedItem = null"
    >
      <!-- 关键：在这里渲染包装单位管理组件 -->
      <PackageUnitManager
        v-if="currentManagedItem" 
        :item-id="currentManagedItem.id"
        :item-sku="currentManagedItem.sku"
        :item-name="currentManagedItem.name"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTooltip, ElTag, ElInput, ElTreeSelect, ElText } from 'element-plus';
// 导入所需图标
import { Edit, Setting, Delete, View, Printer, Refresh } from '@element-plus/icons-vue'; 
// 导入组件
import VNTable from '@/components/VNTable/index.vue'; 
import VNForm from '@/components/VNForm/index.vue'; 
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 导入API函数和类型
import { getMtlItemPage, createMtlItem, updateMtlItem, deleteMtlItem, getMtlItemById } from '@/api/wms/item';
import type { MtlItemVO, MtlItemQueryDTO, MtlItemCreateDTO, MtlItemUpdateDTO } from '@/api/wms/item';
// 导入客户、供应商、库位API
import { getCustomerList } from '@/api/crm/customer';
import type { CrmCustomerSimpleVO } from '@/api/crm/customer';
import { getSupplierList } from '@/api/scm/supplier';
import type { ScmSupplierSimpleVO } from '@/api/scm/supplier';
import { getLocationTree } from '@/api/wms/location';
import type { WmsLocationTreeVO } from '@/api/wms/location';
// 导入包装单位管理组件
import PackageUnitManager from './components/PackageUnitManager.vue'; 
// 导入权限检查函数
import { hasPermission } from '@/hooks/usePermission';
// 导入字典hooks
import { useDictionary } from '@/hooks/useDictionary';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Refs --- 
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;

const itemTableRef = ref<VNTableInstance>();
const itemFormRef = ref<VNFormInstance>();

// --- State --- 
const itemTableData = ref<MtlItemVO[]>([]);
const itemPagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const itemLoading = ref(false);
// 物料弹窗
const itemDialogVisible = ref(false);
const currentEditingItem = ref<Partial<MtlItemCreateDTO & MtlItemUpdateDTO & { id?: number }>>({});
const formMode = ref<'add' | 'edit' | 'view'>('add');
const isViewing = computed(() => formMode.value === 'view');
const selectedItemRows = ref<MtlItemVO[]>([]);

// 添加排序状态
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);
// 添加筛选状态
const currentFilters = ref<Record<string, any>>({});
// 物料表单加载状态
const itemFormLoading = ref(false);

// 包装单位管理模态框相关状态
const packageUnitManageDialogVisible = ref(false);
const currentManagedItem = ref<MtlItemVO | null>(null);

// 使用字典管理
const { loadMultipleDictData, getDictLabel } = useDictionary();

// 字典数据
const dictData = ref({
  unit: [] as Array<{ label: string; value: string }>,
  materialCategory: [] as Array<{ label: string; value: string }>,
  materialGroup: [] as Array<{ label: string; value: string }>
});

// 客户、供应商、库位数据
const customerList = ref<CrmCustomerSimpleVO[]>([]);
const supplierList = ref<ScmSupplierSimpleVO[]>([]);
const locationTree = ref<WmsLocationTreeVO[]>([]);

// 下拉框选项
const customerOptions = computed(() => 
  customerList.value.map(item => ({ label: `${item.customerCode} - ${item.customerName}`, value: item.id }))
);
const supplierOptions = computed(() => 
  supplierList.value.map(item => ({ label: `${item.supplierCode} - ${item.supplierName}`, value: item.id }))
);

// --- Computed --- 
const itemDialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增物料';
  if (formMode.value === 'edit') return '编辑物料';
  if (formMode.value === 'view') return '查看物料';
  return '物料管理';
});

// --- Helper: Date Formatting ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) { 
        return '-';
    }
    return date.toLocaleString();
  } catch (e) {
    return '-';
  }
};

// --- Table Configuration ---
const itemTableColumns = ref<TableColumn[]>([
  { prop: 'sku', label: '物料编码', minWidth: 120, sortable: true, filterable: true, filterType: 'text', fixed: 'left' },
  { prop: 'name', label: '物料名称', minWidth: 220, filterable: true, filterType: 'text' },
  { prop: 'specification', label: '规格型号', minWidth: 320, showOverflowTooltip: true },
  { prop: 'categoryCode', label: '物料分类', width: 120, slot: true },
  { prop: 'groupCode', label: '物料组', width: 120, slot: true },
  { prop: 'baseUnit', label: '基本单位', width: 80, slot: true },
  { prop: 'status', label: '状态', width: 80, slot: true, filterable: true, filterType: 'select', filterOptions: [{ label: '启用', value: 'ACTIVE' }, { label: '禁用', value: 'INACTIVE' }] },
  { prop: 'batchManaged', label: '批次管理', width: 90, slot: true, filterable: true, filterType: 'select', filterOptions: [{ label: '是', value: true }, { label: '否', value: false }] },
  { prop: 'serialManaged', label: '序列管理', width: 90, slot: true, filterable: true, filterType: 'select', filterOptions: [{ label: '是', value: true }, { label: '否', value: false }] },
  { prop: 'weightKg', label: '重量(kg)', width: 100, formatter: (row) => row.weightKg?.toFixed(3) || '-' },
  { prop: 'shelfLifeDays', label: '保质期(天)', width: 100, formatter: (row) => row.shelfLifeDays || '-' },
  { prop: 'description', label: '描述', minWidth: 150, showOverflowTooltip: true },
  { 
    prop: 'createdAt', 
    label: '创建时间', 
    width: 160, 
    sortable: true, 
    filterable: true, 
    filterType: 'date', 
    formatter: (row) => formatDateTime(row.createdAt) 
  },
]);

const itemToolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('mtl:item:add'),
  batchDelete: hasPermission('mtl:item:delete'),
  filter: hasPermission('mtl:item:list'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('mtl:item:import'),
  export: hasPermission('mtl:item:export'),
}));

// --- Form Configuration ---
const itemFormFields = computed<HeaderField[]>(() => [
  // 基本信息分组
  { 
    field: 'sku', 
    label: '物料编码', 
    placeholder: '留空可自动生成或手动输入',
    rules: [
      { max: 100, message: '物料编码长度不能超过100' }
    ], 
    disabled: formMode.value === 'edit' || isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'name', 
    label: '物料名称', 
    rules: [
      { required: true, message: '物料名称为必填项' },
      { max: 255, message: '物料名称长度不能超过255' }
    ], 
    placeholder: '请输入物料名称',
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'specification', 
    label: '规格型号', 
    placeholder: '请输入规格型号',
    disabled: isViewing.value, 
    group: '基本信息',
    md: 12,
    lg: 12,
    xl: 12
  },
  { 
    field: 'categoryCode', 
    label: '物料分类', 
    type: 'select',
    options: dictData.value.materialCategory,
    placeholder: '请选择物料分类',
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'groupCode', 
    label: '物料组', 
    type: 'select',
    options: dictData.value.materialGroup,
    placeholder: '请选择物料组',
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'baseUnit', 
    label: '基本单位', 
    type: 'select',
    options: dictData.value.unit,
    placeholder: '请选择基本单位',
    rules: [
      { required: true, message: '基本单位为必填项' }
    ], 
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'status', 
    label: '状态', 
    type: 'select', 
    options: [
      { label: '启用', value: 'ACTIVE' }, 
      { label: '禁用', value: 'INACTIVE' }
    ], 
    defaultValue: 'ACTIVE', 
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'description', 
    label: '描述', 
    type: 'textarea', 
    rows: 2, 
    span: 12,
    disabled: isViewing.value, 
    group: '基本信息' 
  },

  // 管理设置分组
  { 
    field: 'batchManaged', 
    label: '批次管理', 
    type: 'switch', 
    defaultValue: false, 
    disabled: isViewing.value, 
    group: '管理设置' 
  },
  { 
    field: 'serialManaged', 
    label: '序列号管理', 
    type: 'switch', 
    defaultValue: false, 
    disabled: isViewing.value, 
    group: '管理设置' 
  },
  { 
    field: 'shelfLifeDays', 
    label: '保质期(天)', 
    type: 'number', 
    props: { min: 0, precision: 0 }, 
    disabled: isViewing.value, 
    group: '管理设置' 
  },
  { 
    field: 'storageCondition', 
    label: '存储条件', 
    disabled: isViewing.value, 
    group: '管理设置' 
  },

  // 物理属性分组
  { 
    field: 'weightKg', 
    label: '重量(kg)', 
    type: 'number', 
    props: { min: 0, precision: 3, step: 0.001 }, 
    disabled: isViewing.value, 
    group: '物理属性' 
  },
  { 
    field: 'volumeM3', 
    label: '体积(m³)', 
    type: 'number', 
    props: { min: 0, precision: 6, step: 0.000001 }, 
    disabled: isViewing.value, 
    group: '物理属性' 
  },
  { 
    field: 'lengthM', 
    label: '长度(m)', 
    type: 'number', 
    props: { min: 0, precision: 3, step: 0.001 }, 
    disabled: isViewing.value, 
    group: '物理属性' 
  },
  { 
    field: 'widthM', 
    label: '宽度(m)', 
    type: 'number', 
    props: { min: 0, precision: 3, step: 0.001 }, 
    disabled: isViewing.value, 
    group: '物理属性' 
  },
  { 
    field: 'heightM', 
    label: '高度(m)', 
    type: 'number', 
    props: { min: 0, precision: 3, step: 0.001 }, 
    disabled: isViewing.value, 
    group: '物理属性' 
  },

  // 默认关联分组
  { 
    field: 'defaultCustomerId', 
    label: '默认客户', 
    type: 'select',
    options: customerOptions.value,
    clearable: true,
    disabled: isViewing.value, 
    group: '默认关联',
    placeholder: '请选择默认客户'
  },
  { 
    field: 'defaultSupplierId', 
    label: '默认供应商', 
    type: 'select',
    options: supplierOptions.value,
    clearable: true,
    disabled: isViewing.value, 
    group: '默认关联',
    placeholder: '请选择默认供应商'
  },
  { 
    field: 'defaultLocationId', 
    label: '默认仓库位置', 
    type: 'slot',
    disabled: isViewing.value, 
    group: '默认关联'
  },

  // 其他信息分组
  { 
    field: 'remark', 
    label: '备注', 
    type: 'textarea', 
    rows: 3, 
    span: 24, 
    disabled: isViewing.value, 
    group: '其他信息' 
  },
]);

// --- Lifecycle ---
onMounted(() => {
  loadItems();
  loadDictionaryData();
  loadSelectionData();
});

// --- Methods ---
// 获取库位显示名称
const getLocationDisplayName = (locationId: number | null | undefined): string => {
  if (!locationId) return '';
  
  // 递归查找库位名称
  const findLocationName = (nodes: WmsLocationTreeVO[], id: number): string => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.name;
      }
      if (node.children && node.children.length > 0) {
        const result = findLocationName(node.children, id);
        if (result) return result;
      }
    }
    return '';
  };
  
  return findLocationName(locationTree.value, locationId);
};

// 加载选择数据
const loadSelectionData = async () => {
  try {
    // 并行加载客户、供应商、库位数据
    const [customers, suppliers, locations] = await Promise.all([
      getCustomerList(),
      getSupplierList(),
      getLocationTree()
    ]);
    
    customerList.value = customers;
    supplierList.value = suppliers;
    locationTree.value = locations;
  } catch (error) {
    console.error('加载选择数据失败:', error);
    ElMessage.error('加载选择数据失败');
  }
};

const loadItems = async (paramsOverwrite = {}) => {
  itemLoading.value = true;
  try {
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop && currentSort.value.order) {
      const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
      sortString = `${currentSort.value.prop},${direction}`;
    }

    const query = { 
      ...currentFilters.value,
      ...paramsOverwrite
    };

    Object.keys(query).forEach(key => {
      const typedKey = key as keyof typeof query;
      if (query[typedKey] === null || query[typedKey] === undefined || query[typedKey] === '') {
        delete query[typedKey];
      }
    });
    
    const params: MtlItemQueryDTO = {
      pageNum: itemPagination.currentPage,
      pageSize: itemPagination.pageSize,
      sort: sortString,
      ...query,
    };

    console.log('[loadItems] Fetching with params:', params);
    const response = await getMtlItemPage(params);

    if (response && Array.isArray(response.list)) {
        itemTableData.value = response.list;
        itemPagination.total = response.total || 0;
    } else {
        console.warn('getMtlItemPage 返回的数据格式不符合预期:', response);
        itemTableData.value = [];
        itemPagination.total = 0;
    }

  } catch (error) {
    console.error('Error loading items:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    itemTableData.value = [];
    itemPagination.total = 0;
  } finally {
    itemLoading.value = false;
  }
};

const handleAddNewItem = () => {
  formMode.value = 'add';
  currentEditingItem.value = {
    status: 'ACTIVE',
    batchManaged: false,
    serialManaged: false,
    sku: '',
    name: '',
    baseUnit: '',
  };
  itemDialogVisible.value = true;
  nextTick(() => {
    itemFormRef.value?.clearValidate();
  });
};

const handleEditItem = async (row: MtlItemVO) => {
  formMode.value = 'edit';
  try {
    itemFormLoading.value = true;
    const detailData = await getMtlItemById(row.id);
    if (detailData) {
      currentEditingItem.value = JSON.parse(JSON.stringify(detailData));
    } else {
      currentEditingItem.value = JSON.parse(JSON.stringify(row));
    }
    itemDialogVisible.value = true;
    nextTick(() => {
      itemFormRef.value?.clearValidate();
    });
  } catch (error) {
    console.error('Error loading item detail:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    itemFormLoading.value = false;
  }
};

const handleViewItem = async (row: MtlItemVO) => {
  formMode.value = 'view';
  try {
    itemFormLoading.value = true;
    const detailData = await getMtlItemById(row.id);
    if (detailData) {
      currentEditingItem.value = JSON.parse(JSON.stringify(detailData));
    } else {
      currentEditingItem.value = JSON.parse(JSON.stringify(row));
    }
    itemDialogVisible.value = true;
  } catch (error) {
    console.error('Error loading item detail:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    itemFormLoading.value = false;
  }
};

const handleDeleteItem = async (row: MtlItemVO) => {
  try {
    await ElMessageBox.confirm(`确定删除物料 "${row.name}" (${row.sku}) 吗?`, '确认删除', { type: 'warning' });
    itemLoading.value = true;
    console.log('[handleDeleteItem] Calling deleteMtlItem for ID:', row.id);
    await deleteMtlItem(row.id);
    ElMessage.success('删除成功');
    if (itemTableData.value.length === 1 && itemPagination.currentPage > 1) {
      itemPagination.currentPage--;
    }
    loadItems();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting item:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    itemLoading.value = false;
  }
};

const submitItemForm = async () => {
  if (isViewing.value) {
    handleCloseItemDialog();
    return;
  }

  const formRef = itemFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  itemFormLoading.value = true;
  const payload = { ...currentEditingItem.value } as any;

  try {
    if (formMode.value === 'add') {
      const createPayload: MtlItemCreateDTO = {
        sku: payload.sku!,
        name: payload.name!,
        description: payload.description,
        specification: payload.specification,
        categoryCode: payload.categoryCode,
        groupCode: payload.groupCode,
        baseUnit: payload.baseUnit!,
        shelfLifeDays: payload.shelfLifeDays,
        batchManaged: payload.batchManaged,
        serialManaged: payload.serialManaged,
        storageCondition: payload.storageCondition,
        imageUrl: payload.imageUrl,
        weightKg: payload.weightKg,
        volumeM3: payload.volumeM3,
        lengthM: payload.lengthM,
        widthM: payload.widthM,
        heightM: payload.heightM,
        status: payload.status,
        remark: payload.remark,
        defaultCustomerId: payload.defaultCustomerId,
        defaultSupplierId: payload.defaultSupplierId,
        defaultLocationId: payload.defaultLocationId,
      };
      console.log('[submitItemForm] Calling createMtlItem with:', createPayload);
      await createMtlItem(createPayload);
      ElMessage.success('新增成功');
    } else {
      const updatePayload: MtlItemUpdateDTO = {
        id: (payload as any).id,
        sku: payload.sku,
        name: payload.name,
        description: payload.description,
        specification: payload.specification,
        categoryCode: payload.categoryCode,
        groupCode: payload.groupCode,
        shelfLifeDays: payload.shelfLifeDays,
        batchManaged: payload.batchManaged,
        serialManaged: payload.serialManaged,
        storageCondition: payload.storageCondition,
        imageUrl: payload.imageUrl,
        weightKg: payload.weightKg,
        volumeM3: payload.volumeM3,
        lengthM: payload.lengthM,
        widthM: payload.widthM,
        heightM: payload.heightM,
        status: payload.status,
        remark: payload.remark,
        defaultCustomerId: payload.defaultCustomerId,
        defaultSupplierId: payload.defaultSupplierId,
        defaultLocationId: payload.defaultLocationId,
      };
      console.log('[submitItemForm] Calling updateMtlItem with:', updatePayload);
      await updateMtlItem(updatePayload.id, updatePayload);
      ElMessage.success('编辑成功');
    }

    handleCloseItemDialog();
    loadItems();

  } catch (error) {
    console.error('Error submitting item form:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    itemFormLoading.value = false;
  }
};

const handleCloseItemDialog = () => {
  itemDialogVisible.value = false;
  currentEditingItem.value = {};
  formMode.value = 'add';
  itemFormLoading.value = false;
};

// 打开包装单位管理对话框
const openPackageUnitManageDialog = (row: MtlItemVO) => {
  currentManagedItem.value = row;
  packageUnitManageDialogVisible.value = true;
};

// 表格事件处理方法
const handleItemSelectionChange = (selection: MtlItemVO[]) => {
  selectedItemRows.value = selection;
};

const handleItemBatchDelete = async () => {
  if (selectedItemRows.value.length === 0) {
    ElMessage.warning('请选择要删除的物料');
    return;
  }
  
  try {
    await ElMessageBox.confirm(`确定删除选中的 ${selectedItemRows.value.length} 个物料吗？`, '确认批量删除', { type: 'warning' });
    itemLoading.value = true;
    
    // 逐个删除选中的物料
    for (const item of selectedItemRows.value) {
      await deleteMtlItem(item.id);
    }
    
    ElMessage.success('批量删除成功');
    loadItems();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error in batch delete:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消批量删除');
    }
  } finally {
    itemLoading.value = false;
  }
};

const handleItemImport = () => {
  ElMessage.info('导入功能待开发');
};

const handleItemExport = () => {
  ElMessage.info('导出功能待开发');
};

const handleItemPageChange = (page: number) => {
  itemPagination.currentPage = page;
  loadItems();
};

const handleItemPageSizeChange = (size: number) => {
  itemPagination.pageSize = size;
  itemPagination.currentPage = 1;
  loadItems();
};

const handleItemSortChange = (sortParams: { prop: string; order: 'ascending' | 'descending' | null }) => {
  currentSort.value = sortParams;
  loadItems();
};

const handleItemFilterChange = (filterParams: Record<string, any>) => {
  currentFilters.value = filterParams;
  itemPagination.currentPage = 1;
  loadItems();
};

// 打印相关方法
const handlePrintPreview = () => {
  ElMessage.info('打印预览功能待开发');
};

const handlePrint = () => {
  ElMessage.info('打印功能待开发');
};

// 加载字典数据
const loadDictionaryData = async () => {
  try {
    const dictResults = await loadMultipleDictData(['unit', 'material_category', 'material_group']);
    dictData.value.unit = dictResults['unit'] || [];
    dictData.value.materialCategory = dictResults['material_category'] || [];
    dictData.value.materialGroup = dictResults['material_group'] || [];
  } catch (error) {
    console.error('加载字典数据失败:', error);
  }
};
</script>

<style scoped lang="scss">
/* Styles for the disabled text field wrapper */
.custom-disabled-text-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: var(--el-component-size, 32px); /* Match Element Plus input height */
  padding: 1px 11px; /* Simulate el-input padding */
  background-color: var(--el-disabled-bg-color, #f5f7fa); /* Simulate disabled bg color */
  border: 1px solid var(--el-disabled-border-color, #e4e7ed); /* Simulate disabled border color */
  border-radius: var(--el-border-radius-base, 4px); /* Simulate border radius */
  box-shadow: 0 0 0 0 transparent; /* Ensure no other shadows */
  cursor: not-allowed; /* Simulate disabled cursor */
  overflow: hidden; /* Prevent text from overflowing */
}

.custom-disabled-text-wrapper .custom-disabled-text {
  color: var(--el-text-color-placeholder, #a8abb2); /* Use placeholder or disabled text color */
  font-size: inherit;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%; /* Ensure text uses available space for ellipsis to work */
}
</style>
