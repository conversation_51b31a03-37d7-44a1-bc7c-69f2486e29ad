package repository

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"
	"context"
	"fmt"

	"gorm.io/gorm"
)

// SysCodeRuleRepository 编码规则仓库接口
type SysCodeRuleRepository interface {
	BaseRepository[entity.SysCodeRule, uint]

	// 基础CRUD扩展
	FindByRuleCode(ctx context.Context, ruleCode string) (*entity.SysCodeRule, error)
	FindByBusinessType(ctx context.Context, businessType string) ([]*entity.SysCodeRule, error)
	FindDefaultByBusinessType(ctx context.Context, businessType string) (*entity.SysCodeRule, error)
	GetPage(ctx context.Context, req *dto.SysCodeRuleQueryReq) (*response.PageResult, error)

	// 序号管理
	GetNextSequence(ctx context.Context, ruleID uint) (int64, error)
	ResetSequence(ctx context.Context, ruleID uint) error
	UpdateCurrentSequence(ctx context.Context, ruleID uint, newSequence int64) error

	// 默认规则管理
	SetAsDefault(ctx context.Context, ruleID uint, businessType string) error
	ClearDefaultByBusinessType(ctx context.Context, businessType string) error
}

// sysCodeRuleRepositoryImpl 编码规则仓库实现
type sysCodeRuleRepositoryImpl struct {
	BaseRepositoryImpl[entity.SysCodeRule, uint]
}

// NewSysCodeRuleRepository 创建编码规则仓库
func NewSysCodeRuleRepository(db *gorm.DB) SysCodeRuleRepository {
	return &sysCodeRuleRepositoryImpl{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.SysCodeRule, uint]{db: db},
	}
}

// FindByRuleCode 根据规则编码查找
func (r *sysCodeRuleRepositoryImpl) FindByRuleCode(ctx context.Context, ruleCode string) (*entity.SysCodeRule, error) {
	var rule entity.SysCodeRule
	err := r.GetDB(ctx).Where("rule_code = ?", ruleCode).First(&rule).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

// FindByBusinessType 根据业务类型查找规则列表
func (r *sysCodeRuleRepositoryImpl) FindByBusinessType(ctx context.Context, businessType string) ([]*entity.SysCodeRule, error) {
	var rules []*entity.SysCodeRule
	err := r.GetDB(ctx).Where("business_type = ?", businessType).Find(&rules).Error
	return rules, err
}

// FindDefaultByBusinessType 查找业务类型的默认规则
func (r *sysCodeRuleRepositoryImpl) FindDefaultByBusinessType(ctx context.Context, businessType string) (*entity.SysCodeRule, error) {
	var rule entity.SysCodeRule
	err := r.GetDB(ctx).Where("business_type = ? AND is_default = true", businessType).First(&rule).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

// GetPage 分页查询
func (r *sysCodeRuleRepositoryImpl) GetPage(ctx context.Context, req *dto.SysCodeRuleQueryReq) (*response.PageResult, error) {
	var conditions []QueryCondition

	// 构建查询条件
	if req.RuleCode != nil && *req.RuleCode != "" {
		conditions = append(conditions, NewLikeCondition("rule_code", *req.RuleCode))
	}
	if req.RuleName != nil && *req.RuleName != "" {
		conditions = append(conditions, NewLikeCondition("rule_name", *req.RuleName))
	}
	if req.BusinessType != nil && *req.BusinessType != "" {
		conditions = append(conditions, NewEqualCondition("business_type", *req.BusinessType))
	}
	if req.Status != nil && *req.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *req.Status))
	}
	if req.IsDefault != nil {
		conditions = append(conditions, NewEqualCondition("is_default", *req.IsDefault))
	}

	// 执行分页查询
	return r.FindByPage(ctx, &req.PageQuery, conditions)
}

// GetNextSequence 获取下一个序号（原子操作）
func (r *sysCodeRuleRepositoryImpl) GetNextSequence(ctx context.Context, ruleID uint) (int64, error) {
	var rule entity.SysCodeRule

	// 使用行锁确保并发安全
	err := r.GetDB(ctx).Set("gorm:query_option", "FOR UPDATE").
		Where("id = ?", ruleID).First(&rule).Error
	if err != nil {
		return 0, err
	}

	// 增加序号
	newSequence := rule.CurrentSequence + 1

	// 更新序号
	err = r.GetDB(ctx).Model(&rule).Update("current_sequence", newSequence).Error
	if err != nil {
		return 0, err
	}

	return newSequence, nil
}

// ResetSequence 重置序号
func (r *sysCodeRuleRepositoryImpl) ResetSequence(ctx context.Context, ruleID uint) error {
	updates := map[string]interface{}{
		"current_sequence": 0,
		"last_reset_time":  "NOW()",
	}
	return r.GetDB(ctx).Model(&entity.SysCodeRule{}).Where("id = ?", ruleID).Updates(updates).Error
}

// UpdateCurrentSequence 更新当前序号
func (r *sysCodeRuleRepositoryImpl) UpdateCurrentSequence(ctx context.Context, ruleID uint, newSequence int64) error {
	return r.GetDB(ctx).Model(&entity.SysCodeRule{}).
		Where("id = ?", ruleID).
		Update("current_sequence", newSequence).Error
}

// SetAsDefault 设置为默认规则
func (r *sysCodeRuleRepositoryImpl) SetAsDefault(ctx context.Context, ruleID uint, businessType string) error {
	// 使用事务确保原子性
	return r.GetDB(ctx).Transaction(func(tx *gorm.DB) error {
		// 先清除该业务类型的其他默认规则
		if err := tx.Model(&entity.SysCodeRule{}).
			Where("business_type = ? AND id != ?", businessType, ruleID).
			Update("is_default", false).Error; err != nil {
			return err
		}

		// 设置当前规则为默认
		return tx.Model(&entity.SysCodeRule{}).
			Where("id = ?", ruleID).
			Update("is_default", true).Error
	})
}

// ClearDefaultByBusinessType 清除业务类型的默认规则
func (r *sysCodeRuleRepositoryImpl) ClearDefaultByBusinessType(ctx context.Context, businessType string) error {
	return r.GetDB(ctx).Model(&entity.SysCodeRule{}).
		Where("business_type = ?", businessType).
		Update("is_default", false).Error
}

// SysCodeFormatComponentRepository 编码格式组件仓库接口
type SysCodeFormatComponentRepository interface {
	Create(ctx context.Context, component *entity.SysCodeFormatComponent) error
	Update(ctx context.Context, component *entity.SysCodeFormatComponent) error
	Delete(ctx context.Context, id uint) error
	FindByID(ctx context.Context, id uint) (*entity.SysCodeFormatComponent, error)
	FindAll(ctx context.Context) ([]*entity.SysCodeFormatComponent, error)
	FindByType(ctx context.Context, componentType string) ([]*entity.SysCodeFormatComponent, error)
	GetPage(ctx context.Context, req *dto.SysCodeFormatComponentQueryReq) (*response.PageResult, error)
}

// sysCodeFormatComponentRepositoryImpl 编码格式组件仓库实现
type sysCodeFormatComponentRepositoryImpl struct {
	db *gorm.DB
}

// NewSysCodeFormatComponentRepository 创建编码格式组件仓库
func NewSysCodeFormatComponentRepository(db *gorm.DB) SysCodeFormatComponentRepository {
	return &sysCodeFormatComponentRepositoryImpl{db: db}
}

// Create 创建组件
func (r *sysCodeFormatComponentRepositoryImpl) Create(ctx context.Context, component *entity.SysCodeFormatComponent) error {
	return r.db.WithContext(ctx).Create(component).Error
}

// Update 更新组件
func (r *sysCodeFormatComponentRepositoryImpl) Update(ctx context.Context, component *entity.SysCodeFormatComponent) error {
	return r.db.WithContext(ctx).Save(component).Error
}

// Delete 删除组件
func (r *sysCodeFormatComponentRepositoryImpl) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&entity.SysCodeFormatComponent{}, id).Error
}

// FindByID 根据ID查找
func (r *sysCodeFormatComponentRepositoryImpl) FindByID(ctx context.Context, id uint) (*entity.SysCodeFormatComponent, error) {
	var component entity.SysCodeFormatComponent
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&component).Error
	if err != nil {
		return nil, err
	}
	return &component, nil
}

// FindAll 查找所有组件
func (r *sysCodeFormatComponentRepositoryImpl) FindAll(ctx context.Context) ([]*entity.SysCodeFormatComponent, error) {
	var components []*entity.SysCodeFormatComponent
	err := r.db.WithContext(ctx).Where("status = ?", "ACTIVE").Order("component_type, component_code").Find(&components).Error
	return components, err
}

// FindByType 根据组件类型查找
func (r *sysCodeFormatComponentRepositoryImpl) FindByType(ctx context.Context, componentType string) ([]*entity.SysCodeFormatComponent, error) {
	var components []*entity.SysCodeFormatComponent
	err := r.db.WithContext(ctx).Where("component_type = ? AND status = ?", componentType, "ACTIVE").Find(&components).Error
	return components, err
}

// GetPage 分页查询
func (r *sysCodeFormatComponentRepositoryImpl) GetPage(ctx context.Context, req *dto.SysCodeFormatComponentQueryReq) (*response.PageResult, error) {
	db := r.db.WithContext(ctx)

	// 构建查询条件
	if req.ComponentCode != nil && *req.ComponentCode != "" {
		db = db.Where("component_code LIKE ?", "%"+*req.ComponentCode+"%")
	}
	if req.ComponentName != nil && *req.ComponentName != "" {
		db = db.Where("component_name LIKE ?", "%"+*req.ComponentName+"%")
	}
	if req.ComponentType != nil && *req.ComponentType != "" {
		db = db.Where("component_type = ?", *req.ComponentType)
	}
	if req.Status != nil && *req.Status != "" {
		db = db.Where("status = ?", *req.Status)
	}

	// 分页查询
	var total int64
	var components []*entity.SysCodeFormatComponent

	// 计算总数
	if err := db.Model(&entity.SysCodeFormatComponent{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计总数失败: %w", err)
	}

	// 分页查询数据
	offset := (req.PageNum - 1) * req.PageSize
	if err := db.Order("component_type, component_code").Offset(offset).Limit(req.PageSize).Find(&components).Error; err != nil {
		return nil, fmt.Errorf("分页查询失败: %w", err)
	}

	return &response.PageResult{
		List:     components,
		Total:    total,
		PageNum:  req.PageNum,
		PageSize: req.PageSize,
	}, nil
}

// SysCodeGenerationLogRepository 编码生成历史仓库接口
type SysCodeGenerationLogRepository interface {
	Create(ctx context.Context, log *entity.SysCodeGenerationLog) error
	FindByID(ctx context.Context, id uint) (*entity.SysCodeGenerationLog, error)
	FindByCode(ctx context.Context, code string) (*entity.SysCodeGenerationLog, error)
	FindByRuleID(ctx context.Context, ruleID uint) ([]*entity.SysCodeGenerationLog, error)
	GetPage(ctx context.Context, req *dto.SysCodeGenerationLogQueryReq) (*response.PageResult, error)
}

// sysCodeGenerationLogRepositoryImpl 编码生成历史仓库实现
type sysCodeGenerationLogRepositoryImpl struct {
	db *gorm.DB
}

// NewSysCodeGenerationLogRepository 创建编码生成历史仓库
func NewSysCodeGenerationLogRepository(db *gorm.DB) SysCodeGenerationLogRepository {
	return &sysCodeGenerationLogRepositoryImpl{db: db}
}

// Create 创建生成历史记录
func (r *sysCodeGenerationLogRepositoryImpl) Create(ctx context.Context, log *entity.SysCodeGenerationLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// FindByID 根据ID查找
func (r *sysCodeGenerationLogRepositoryImpl) FindByID(ctx context.Context, id uint) (*entity.SysCodeGenerationLog, error) {
	var log entity.SysCodeGenerationLog
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// FindByCode 根据编码查找
func (r *sysCodeGenerationLogRepositoryImpl) FindByCode(ctx context.Context, code string) (*entity.SysCodeGenerationLog, error) {
	var log entity.SysCodeGenerationLog
	err := r.db.WithContext(ctx).Where("generated_code = ?", code).First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// FindByRuleID 根据规则ID查找历史记录
func (r *sysCodeGenerationLogRepositoryImpl) FindByRuleID(ctx context.Context, ruleID uint) ([]*entity.SysCodeGenerationLog, error) {
	var logs []*entity.SysCodeGenerationLog
	err := r.db.WithContext(ctx).Where("rule_id = ?", ruleID).Order("created_at DESC").Find(&logs).Error
	return logs, err
}

// GetPage 分页查询
func (r *sysCodeGenerationLogRepositoryImpl) GetPage(ctx context.Context, req *dto.SysCodeGenerationLogQueryReq) (*response.PageResult, error) {
	db := r.db.WithContext(ctx)

	// 构建查询条件
	if req.RuleID != nil {
		db = db.Where("rule_id = ?", *req.RuleID)
	}
	if req.BusinessType != nil && *req.BusinessType != "" {
		db = db.Where("business_type = ?", *req.BusinessType)
	}
	if req.BusinessID != nil {
		db = db.Where("business_id = ?", *req.BusinessID)
	}
	if req.GeneratedCode != nil && *req.GeneratedCode != "" {
		db = db.Where("generated_code LIKE ?", "%"+*req.GeneratedCode+"%")
	}
	if req.CreatedBy != nil {
		db = db.Where("created_by = ?", *req.CreatedBy)
	}
	if req.StartTime != nil {
		db = db.Where("created_at >= ?", *req.StartTime)
	}
	if req.EndTime != nil {
		db = db.Where("created_at <= ?", *req.EndTime)
	}

	// 分页查询
	var total int64
	var logs []*entity.SysCodeGenerationLog

	// 计算总数
	if err := db.Model(&entity.SysCodeGenerationLog{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("统计总数失败: %w", err)
	}

	// 分页查询数据
	offset := (req.PageNum - 1) * req.PageSize
	if err := db.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("分页查询失败: %w", err)
	}

	return &response.PageResult{
		List:     logs,
		Total:    total,
		PageNum:  req.PageNum,
		PageSize: req.PageSize,
	}, nil
}
