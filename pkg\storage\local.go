package storage

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"mime"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// LocalStorage 本地存储实现
type LocalStorage struct {
	config StorageConfig
}

// NewLocalStorage 创建本地存储
func NewLocalStorage(config StorageConfig) (Storage, error) {
	// 确保根目录存在
	if config.RootDir == "" {
		return nil, NewStorageError("根目录不能为空", "ROOT_DIR_EMPTY")
	}

	// 创建根目录
	if err := os.MkdirAll(config.RootDir, 0755); err != nil {
		return nil, NewStorageError(fmt.Sprintf("创建根目录失败: %v", err), "CREATE_ROOT_DIR_FAILED")
	}

	// 检查根目录权限
	if _, err := os.Stat(config.RootDir); err != nil {
		return nil, NewStorageError(fmt.Sprintf("访问根目录失败: %v", err), "ACCESS_ROOT_DIR_FAILED")
	}

	// 设置基础URL
	if config.BaseURL == "" {
		config.BaseURL = "/"
	}

	return &LocalStorage{
		config: config,
	}, nil
}

// Upload 上传文件
func (s *LocalStorage) Upload(ctx context.Context, objectKey string, reader io.Reader, options ...UploadOptions) (FileInfo, error) {
	var opt UploadOptions
	if len(options) > 0 {
		opt = options[0]
	}

	// 清理对象键
	objectKey = cleanObjectKey(objectKey)
	if objectKey == "" {
		return FileInfo{}, ErrInvalidObjectKey
	}

	// 构建文件路径
	filePath := filepath.Join(s.config.RootDir, objectKey)
	dirPath := filepath.Dir(filePath)

	// 创建目录
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return FileInfo{}, NewStorageError(fmt.Sprintf("创建目录失败: %v", err), "CREATE_DIR_FAILED")
	}

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return FileInfo{}, NewStorageError(fmt.Sprintf("创建文件失败: %v", err), "CREATE_FILE_FAILED")
	}
	defer file.Close()

	// 计算MD5哈希值
	hash := md5.New()
	multiWriter := io.MultiWriter(file, hash)

	// 缓冲区大小
	bufferSize := DEFAULT_BUFFER_SIZE
	if opt.BufferSize > 0 {
		bufferSize = int(opt.BufferSize)
	}

	// 拷贝数据
	written, err := io.CopyBuffer(multiWriter, reader, make([]byte, bufferSize))
	if err != nil {
		return FileInfo{}, NewStorageError(fmt.Sprintf("写入文件失败: %v", err), "WRITE_FILE_FAILED")
	}

	// 计算哈希值
	hashValue := hex.EncodeToString(hash.Sum(nil))

	// 获取文件信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return FileInfo{}, NewStorageError(fmt.Sprintf("获取文件信息失败: %v", err), "GET_FILE_INFO_FAILED")
	}

	// 设置文件权限
	if err := os.Chmod(filePath, 0644); err != nil {
		return FileInfo{}, NewStorageError(fmt.Sprintf("设置文件权限失败: %v", err), "SET_FILE_PERMISSION_FAILED")
	}

	// 构建返回结果
	result := FileInfo{
		Name:        filepath.Base(objectKey),
		Path:        objectKey,
		URL:         s.buildURL(objectKey),
		Size:        written,
		ContentType: getContentType(objectKey, opt.ContentType),
		Extension:   filepath.Ext(objectKey),
		Hash:        hashValue,
		CreateTime:  fileInfo.ModTime(),
		ModifyTime:  fileInfo.ModTime(),
		IsDir:       false,
		StorageType: STORAGE_TYPE_LOCAL,
		Metadata:    opt.Metadata,
	}

	// 设置过期时间
	if opt.Expires > 0 {
		result.ExpiresTime = time.Now().Add(opt.Expires)
	}

	return result, nil
}

// UploadFile 上传本地文件
func (s *LocalStorage) UploadFile(ctx context.Context, objectKey, filePath string, options ...UploadOptions) (FileInfo, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return FileInfo{}, NewStorageError(fmt.Sprintf("打开文件失败: %v", err), "OPEN_FILE_FAILED")
	}
	defer file.Close()

	// 获取文件大小
	fileInfo, err := file.Stat()
	if err != nil {
		return FileInfo{}, NewStorageError(fmt.Sprintf("获取文件信息失败: %v", err), "GET_FILE_INFO_FAILED")
	}

	// 检查文件大小
	if s.config.MaxFileSize > 0 && fileInfo.Size() > s.config.MaxFileSize {
		return FileInfo{}, ErrFileTooLarge
	}

	// 设置内容类型
	var opt UploadOptions
	if len(options) > 0 {
		opt = options[0]
	}
	if opt.ContentType == "" {
		opt.ContentType = getContentType(filePath, "")
	}

	// 上传文件
	return s.Upload(ctx, objectKey, file, opt)
}

// Download 下载文件
func (s *LocalStorage) Download(ctx context.Context, objectKey string, writer io.Writer, options ...DownloadOptions) error {
	var opt DownloadOptions
	if len(options) > 0 {
		opt = options[0]
	}

	// 清理对象键
	objectKey = cleanObjectKey(objectKey)
	if objectKey == "" {
		return ErrInvalidObjectKey
	}

	// 构建文件路径
	filePath := filepath.Join(s.config.RootDir, objectKey)

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return ErrObjectNotFound
		}
		return NewStorageError(fmt.Sprintf("访问文件失败: %v", err), "ACCESS_FILE_FAILED")
	}

	// 如果是目录，返回错误
	if fileInfo.IsDir() {
		return NewStorageError("不能下载目录", "CANNOT_DOWNLOAD_DIRECTORY")
	}

	// 检查修改时间
	if !opt.IfModified.IsZero() && !fileInfo.ModTime().After(opt.IfModified) {
		return nil // 文件未修改
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return NewStorageError(fmt.Sprintf("打开文件失败: %v", err), "OPEN_FILE_FAILED")
	}
	defer file.Close()

	// 处理范围请求
	if opt.Range != "" {
		// 解析范围
		ranges, err := parseRange(opt.Range, fileInfo.Size())
		if err != nil {
			return NewStorageError(fmt.Sprintf("解析范围失败: %v", err), "PARSE_RANGE_FAILED")
		}

		// 暂时只支持单个范围
		if len(ranges) > 0 {
			start, length := ranges[0].start, ranges[0].length
			if _, err := file.Seek(start, io.SeekStart); err != nil {
				return NewStorageError(fmt.Sprintf("定位文件失败: %v", err), "SEEK_FILE_FAILED")
			}

			// 使用有限的读取器
			reader := io.LimitReader(file, length)
			if _, err := io.Copy(writer, reader); err != nil {
				return NewStorageError(fmt.Sprintf("读取文件失败: %v", err), "READ_FILE_FAILED")
			}
			return nil
		}
	}

	// 缓冲区大小
	bufferSize := DEFAULT_BUFFER_SIZE
	if opt.BufferSize > 0 {
		bufferSize = int(opt.BufferSize)
	}

	// 拷贝数据
	if _, err := io.CopyBuffer(writer, file, make([]byte, bufferSize)); err != nil {
		return NewStorageError(fmt.Sprintf("读取文件失败: %v", err), "READ_FILE_FAILED")
	}

	return nil
}

// DownloadFile 下载文件到本地
func (s *LocalStorage) DownloadFile(ctx context.Context, objectKey, filePath string, options ...DownloadOptions) error {
	// 创建目录
	dirPath := filepath.Dir(filePath)
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return NewStorageError(fmt.Sprintf("创建目录失败: %v", err), "CREATE_DIR_FAILED")
	}

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return NewStorageError(fmt.Sprintf("创建文件失败: %v", err), "CREATE_FILE_FAILED")
	}
	defer file.Close()

	// 下载文件
	return s.Download(ctx, objectKey, file, options...)
}

// Stat 获取文件信息
func (s *LocalStorage) Stat(ctx context.Context, objectKey string) (FileInfo, error) {
	// 清理对象键
	objectKey = cleanObjectKey(objectKey)
	if objectKey == "" {
		return FileInfo{}, ErrInvalidObjectKey
	}

	// 构建文件路径
	filePath := filepath.Join(s.config.RootDir, objectKey)

	// 获取文件信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return FileInfo{}, ErrObjectNotFound
		}
		return FileInfo{}, NewStorageError(fmt.Sprintf("获取文件信息失败: %v", err), "GET_FILE_INFO_FAILED")
	}

	// 计算哈希值（仅对文件）
	var hashValue string
	if !fileInfo.IsDir() {
		// 打开文件
		file, err := os.Open(filePath)
		if err != nil {
			return FileInfo{}, NewStorageError(fmt.Sprintf("打开文件失败: %v", err), "OPEN_FILE_FAILED")
		}
		defer file.Close()

		// 计算MD5哈希值
		hash := md5.New()
		if _, err := io.Copy(hash, file); err != nil {
			return FileInfo{}, NewStorageError(fmt.Sprintf("计算哈希值失败: %v", err), "CALCULATE_HASH_FAILED")
		}
		hashValue = hex.EncodeToString(hash.Sum(nil))
	}

	// 构建返回结果
	result := FileInfo{
		Name:        fileInfo.Name(),
		Path:        objectKey,
		URL:         s.buildURL(objectKey),
		Size:        fileInfo.Size(),
		ContentType: getContentType(objectKey, ""),
		Extension:   filepath.Ext(objectKey),
		Hash:        hashValue,
		CreateTime:  fileInfo.ModTime(), // 本地文件系统不记录创建时间
		ModifyTime:  fileInfo.ModTime(),
		IsDir:       fileInfo.IsDir(),
		StorageType: STORAGE_TYPE_LOCAL,
	}

	return result, nil
}

// List 列出文件
func (s *LocalStorage) List(ctx context.Context, prefix string, options ...ListOptions) ([]FileInfo, error) {
	var opt ListOptions
	if len(options) > 0 {
		opt = options[0]
	}

	// 清理前缀
	prefix = cleanObjectKey(prefix)
	dirPath := filepath.Join(s.config.RootDir, prefix)

	// 检查目录是否存在
	dirInfo, err := os.Stat(dirPath)
	if err != nil {
		if os.IsNotExist(err) {
			return []FileInfo{}, nil // 目录不存在，返回空列表
		}
		return nil, NewStorageError(fmt.Sprintf("访问目录失败: %v", err), "ACCESS_DIR_FAILED")
	}

	// 如果不是目录，返回错误
	if !dirInfo.IsDir() {
		return nil, NewStorageError("不是目录", "NOT_A_DIRECTORY")
	}

	var files []FileInfo
	maxKeys := 1000 // 默认最大数量
	if opt.MaxKeys > 0 {
		maxKeys = opt.MaxKeys
	}

	// 递归获取文件
	err = filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 如果是根目录，跳过
		if path == dirPath {
			return nil
		}

		// 转换为对象键
		relPath, err := filepath.Rel(s.config.RootDir, path)
		if err != nil {
			return err
		}
		objectKey := filepath.ToSlash(relPath)

		// 如果不是递归且不是直接子项，则跳过
		if !opt.Recursive {
			if strings.Count(strings.TrimPrefix(objectKey, prefix), "/") > 0 {
				// 如果是目录，添加为前缀
				if info.IsDir() {
					// 查找是否已经添加了该前缀
					prefixPath := objectKey + "/"
					found := false
					for _, f := range files {
						if f.Path == prefixPath {
							found = true
							break
						}
					}
					if !found {
						files = append(files, FileInfo{
							Name:        info.Name(),
							Path:        prefixPath,
							IsDir:       true,
							StorageType: STORAGE_TYPE_LOCAL,
						})
					}
				}
				if info.IsDir() {
					return filepath.SkipDir // 跳过该目录
				}
				return nil
			}
		}

		// 如果达到最大数量，停止遍历
		if len(files) >= maxKeys {
			return io.EOF
		}

		// 计算哈希值（仅对文件）
		var hashValue string
		if !info.IsDir() {
			// 打开文件
			file, err := os.Open(path)
			if err != nil {
				return err
			}
			defer file.Close()

			// 计算MD5哈希值
			hash := md5.New()
			if _, err := io.Copy(hash, file); err != nil {
				return err
			}
			hashValue = hex.EncodeToString(hash.Sum(nil))
		}

		// 添加文件信息
		files = append(files, FileInfo{
			Name:        info.Name(),
			Path:        objectKey,
			URL:         s.buildURL(objectKey),
			Size:        info.Size(),
			ContentType: getContentType(objectKey, ""),
			Extension:   filepath.Ext(objectKey),
			Hash:        hashValue,
			CreateTime:  info.ModTime(),
			ModifyTime:  info.ModTime(),
			IsDir:       info.IsDir(),
			StorageType: STORAGE_TYPE_LOCAL,
		})

		return nil
	})

	// 如果是因为达到最大数量而停止的，不返回错误
	if err == io.EOF {
		err = nil
	}

	if err != nil {
		return nil, NewStorageError(fmt.Sprintf("列出文件失败: %v", err), "LIST_FILES_FAILED")
	}

	return files, nil
}

// Delete 删除文件
func (s *LocalStorage) Delete(ctx context.Context, objectKey string) error {
	// 清理对象键
	objectKey = cleanObjectKey(objectKey)
	if objectKey == "" {
		return ErrInvalidObjectKey
	}

	// 构建文件路径
	filePath := filepath.Join(s.config.RootDir, objectKey)

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return ErrObjectNotFound
		}
		return NewStorageError(fmt.Sprintf("访问文件失败: %v", err), "ACCESS_FILE_FAILED")
	}

	// 如果是目录且不为空，返回错误
	if fileInfo.IsDir() {
		// 检查目录是否为空
		entries, err := os.ReadDir(filePath)
		if err != nil {
			return NewStorageError(fmt.Sprintf("读取目录失败: %v", err), "READ_DIR_FAILED")
		}
		if len(entries) > 0 {
			return NewStorageError("目录不为空", "DIRECTORY_NOT_EMPTY")
		}
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		return NewStorageError(fmt.Sprintf("删除文件失败: %v", err), "DELETE_FILE_FAILED")
	}

	return nil
}

// DeleteBatch 批量删除文件
func (s *LocalStorage) DeleteBatch(ctx context.Context, objectKeys []string) error {
	for _, objectKey := range objectKeys {
		if err := s.Delete(ctx, objectKey); err != nil {
			return err
		}
	}
	return nil
}

// Copy 复制文件
func (s *LocalStorage) Copy(ctx context.Context, sourceKey, destKey string) error {
	// 清理对象键
	sourceKey = cleanObjectKey(sourceKey)
	destKey = cleanObjectKey(destKey)
	if sourceKey == "" || destKey == "" {
		return ErrInvalidObjectKey
	}

	// 构建文件路径
	sourcePath := filepath.Join(s.config.RootDir, sourceKey)
	destPath := filepath.Join(s.config.RootDir, destKey)

	// 检查源文件是否存在
	sourceInfo, err := os.Stat(sourcePath)
	if err != nil {
		if os.IsNotExist(err) {
			return ErrObjectNotFound
		}
		return NewStorageError(fmt.Sprintf("访问源文件失败: %v", err), "ACCESS_SOURCE_FILE_FAILED")
	}

	// 如果源是目录，返回错误
	if sourceInfo.IsDir() {
		return NewStorageError("不支持复制目录", "CANNOT_COPY_DIRECTORY")
	}

	// 创建目标目录
	destDir := filepath.Dir(destPath)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return NewStorageError(fmt.Sprintf("创建目标目录失败: %v", err), "CREATE_DEST_DIR_FAILED")
	}

	// 打开源文件
	sourceFile, err := os.Open(sourcePath)
	if err != nil {
		return NewStorageError(fmt.Sprintf("打开源文件失败: %v", err), "OPEN_SOURCE_FILE_FAILED")
	}
	defer sourceFile.Close()

	// 创建目标文件
	destFile, err := os.Create(destPath)
	if err != nil {
		return NewStorageError(fmt.Sprintf("创建目标文件失败: %v", err), "CREATE_DEST_FILE_FAILED")
	}
	defer destFile.Close()

	// 拷贝数据
	if _, err := io.Copy(destFile, sourceFile); err != nil {
		return NewStorageError(fmt.Sprintf("复制文件失败: %v", err), "COPY_FILE_FAILED")
	}

	// 设置文件权限
	if err := os.Chmod(destPath, 0644); err != nil {
		return NewStorageError(fmt.Sprintf("设置文件权限失败: %v", err), "SET_FILE_PERMISSION_FAILED")
	}

	return nil
}

// Move 移动文件
func (s *LocalStorage) Move(ctx context.Context, sourceKey, destKey string) error {
	// 复制文件
	if err := s.Copy(ctx, sourceKey, destKey); err != nil {
		return err
	}

	// 删除源文件
	return s.Delete(ctx, sourceKey)
}

// Rename 重命名文件
func (s *LocalStorage) Rename(ctx context.Context, objectKey, newName string) error {
	// 清理对象键
	objectKey = cleanObjectKey(objectKey)
	if objectKey == "" {
		return ErrInvalidObjectKey
	}

	// 处理新名称
	if newName == "" {
		return NewStorageError("新名称不能为空", "NEW_NAME_EMPTY")
	}

	// 不允许包含路径分隔符
	if strings.Contains(newName, "/") {
		return NewStorageError("新名称不能包含路径分隔符", "INVALID_NEW_NAME")
	}

	// 构建新的对象键
	dir := filepath.Dir(objectKey)
	if dir == "." {
		dir = ""
	} else {
		dir += "/"
	}
	newObjectKey := dir + newName

	// 移动文件
	return s.Move(ctx, objectKey, newObjectKey)
}

// GetURL 获取文件URL
func (s *LocalStorage) GetURL(ctx context.Context, objectKey string, expires ...time.Duration) (string, error) {
	// 清理对象键
	objectKey = cleanObjectKey(objectKey)
	if objectKey == "" {
		return "", ErrInvalidObjectKey
	}

	// 检查文件是否存在
	filePath := filepath.Join(s.config.RootDir, objectKey)
	if _, err := os.Stat(filePath); err != nil {
		if os.IsNotExist(err) {
			return "", ErrObjectNotFound
		}
		return "", NewStorageError(fmt.Sprintf("访问文件失败: %v", err), "ACCESS_FILE_FAILED")
	}

	// 返回URL
	return s.buildURL(objectKey), nil
}

// IsExist 检查文件是否存在
func (s *LocalStorage) IsExist(ctx context.Context, objectKey string) (bool, error) {
	// 清理对象键
	objectKey = cleanObjectKey(objectKey)
	if objectKey == "" {
		return false, ErrInvalidObjectKey
	}

	// 检查文件是否存在
	filePath := filepath.Join(s.config.RootDir, objectKey)
	_, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, NewStorageError(fmt.Sprintf("访问文件失败: %v", err), "ACCESS_FILE_FAILED")
	}

	return true, nil
}

// CreateDir 创建目录
func (s *LocalStorage) CreateDir(ctx context.Context, dirKey string) error {
	// 清理目录键
	dirKey = cleanObjectKey(dirKey)
	if dirKey == "" {
		return ErrInvalidObjectKey
	}

	// 确保以/结尾
	if !strings.HasSuffix(dirKey, "/") {
		dirKey += "/"
	}

	// 构建目录路径
	dirPath := filepath.Join(s.config.RootDir, dirKey)

	// 创建目录
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return NewStorageError(fmt.Sprintf("创建目录失败: %v", err), "CREATE_DIR_FAILED")
	}

	return nil
}

// GetConfig 获取存储配置
func (s *LocalStorage) GetConfig() StorageConfig {
	return s.config
}

// Close 关闭存储
func (s *LocalStorage) Close() error {
	// 本地存储不需要关闭
	return nil
}

// 辅助函数

// cleanObjectKey 清理对象键
func cleanObjectKey(objectKey string) string {
	// 去除前导/
	objectKey = strings.TrimPrefix(objectKey, "/")
	// 规范化路径分隔符
	objectKey = filepath.ToSlash(objectKey)
	return objectKey
}

// getContentType 获取内容类型
func getContentType(filename, defaultType string) string {
	// 如果提供了默认类型，直接返回
	if defaultType != "" {
		return defaultType
	}

	// 根据扩展名判断
	ext := strings.ToLower(filepath.Ext(filename))
	mimeType := mime.TypeByExtension(ext)
	if mimeType != "" {
		return mimeType
	}

	// 未知类型
	return "application/octet-stream"
}

// 构建URL
func (s *LocalStorage) buildURL(objectKey string) string {
	// 确保以/开头
	if !strings.HasPrefix(s.config.BaseURL, "/") {
		s.config.BaseURL = "/" + s.config.BaseURL
	}
	// 确保以/结尾
	if !strings.HasSuffix(s.config.BaseURL, "/") {
		s.config.BaseURL += "/"
	}
	// 去除前导/
	objectKey = strings.TrimPrefix(objectKey, "/")
	return s.config.BaseURL + objectKey
}

// 定义HTTP范围类型
type httpRange struct {
	start  int64
	length int64
}

// parseRange 解析HTTP范围请求头
func parseRange(rangeHeader string, size int64) ([]httpRange, error) {
	if rangeHeader == "" {
		return nil, nil
	}

	const prefix = "bytes="
	if !strings.HasPrefix(rangeHeader, prefix) {
		return nil, fmt.Errorf("无效的范围请求头: %s", rangeHeader)
	}

	ranges := []httpRange{}
	for _, r := range strings.Split(rangeHeader[len(prefix):], ",") {
		r = strings.TrimSpace(r)
		if r == "" {
			continue
		}

		i := strings.Index(r, "-")
		if i < 0 {
			return nil, fmt.Errorf("无效的范围: %s", r)
		}

		start, end := strings.TrimSpace(r[:i]), strings.TrimSpace(r[i+1:])
		var startByte, endByte int64
		var err error

		if start == "" {
			// 负范围，如 -100
			if end == "" {
				return nil, fmt.Errorf("无效的范围: %s", r)
			}
			endByte, err = strconv.ParseInt(end, 10, 64)
			if err != nil {
				return nil, fmt.Errorf("无效的范围: %s", r)
			}
			if endByte <= 0 {
				return nil, fmt.Errorf("无效的范围: %s", r)
			}
			startByte = size - endByte
			if startByte < 0 {
				startByte = 0
			}
			endByte = size - 1
		} else {
			startByte, err = strconv.ParseInt(start, 10, 64)
			if err != nil {
				return nil, fmt.Errorf("无效的范围: %s", r)
			}
			if end == "" {
				// 如 500-
				endByte = size - 1
			} else {
				endByte, err = strconv.ParseInt(end, 10, 64)
				if err != nil {
					return nil, fmt.Errorf("无效的范围: %s", r)
				}
			}
		}

		if startByte < 0 || endByte < 0 || startByte > endByte || endByte >= size {
			return nil, fmt.Errorf("无效的范围: %s", r)
		}

		ranges = append(ranges, httpRange{
			start:  startByte,
			length: endByte - startByte + 1,
		})
	}

	return ranges, nil
}

