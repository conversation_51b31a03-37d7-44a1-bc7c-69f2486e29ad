<template>
  <div class="status-actions">
    <div class="action-title">
      <el-icon><Operation /></el-icon>
      <span>状态操作</span>
    </div>
    
    <div class="action-buttons">
      <template v-for="transition in filteredTransitions" :key="`${currentStatus}-${transition.from}`">
        <el-button
          v-for="targetStatus in transition.to"
          :key="`${currentStatus}-${targetStatus}`"
          :type="getButtonType(targetStatus)"
          :icon="getButtonIcon(targetStatus)"
          :disabled="!canExecuteTransition(transition)"
          :loading="loadingTransitions.has(targetStatus)"
          @click="handleTransition(targetStatus)"
        >
          {{ getTransitionLabel(currentStatus, targetStatus) }}
        </el-button>
      </template>
      
      <!-- 如果没有可用操作，显示提示 -->
      <div v-if="filteredTransitions.length === 0" class="no-actions">
        <el-text type="info" size="small">
          <el-icon><InfoFilled /></el-icon>
          当前状态下暂无可用操作
        </el-text>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Operation, 
  InfoFilled,
  Promotion,
  CircleCheck,
  Warning,
  Check,
  Lock,
  CircleClose,
  Loading,
  Edit
} from '@element-plus/icons-vue'
import type { StatusActionsProps, StatusTransition } from '../types'

// Props
const props = defineProps<StatusActionsProps>()

// Emits
const emit = defineEmits<{
  statusChange: [newStatus: string]
}>()

// 加载状态
const loadingTransitions = ref<Set<string>>(new Set())

// 过滤可用的状态转换
const filteredTransitions = computed(() => {
  return props.availableTransitions.filter(transition => {
    // 检查权限
    if (transition.permission && props.checkPermission) {
      return props.checkPermission(transition.permission)
    }
    return true
  })
})

// 获取按钮类型
const getButtonType = props.getButtonType

// 动态解析图标组件
const iconComponentMap: Record<string, any> = {
  Promotion,
  CircleCheck,
  Warning,
  Check,
  Lock,
  CircleClose,
  Loading,
  Edit,
  Operation
}

const getButtonIcon = (status: string) => {
  const iconName = props.getButtonIconName(status) || 'Operation'
  return iconComponentMap[iconName] || Operation
}

// 获取状态转换标签
const getTransitionLabel = props.getTransitionLabel

// 检查是否可以执行转换
const canExecuteTransition = (transition: StatusTransition): boolean => {
  if (props.readonly) return false
  
  // 检查权限
  if (transition.permission && props.checkPermission) {
    return props.checkPermission(transition.permission)
  }
  
  // 检查转换条件
  if (transition.condition) {
    // 这里可以传入业务数据进行条件检查
    return transition.condition({})
  }
  
  return true
}

// 处理状态转换
const handleTransition = async (newStatus: string) => {
  if (props.readonly) return
  
  try {
    // 设置加载状态
    loadingTransitions.value.add(newStatus)
    
    // 发出状态变更事件
    emit('statusChange', newStatus)
    
  } finally {
    // 移除加载状态
    setTimeout(() => {
      loadingTransitions.value.delete(newStatus)
    }, 1000)
  }
}

// 获取操作按钮的提示文本
// const getTooltipContent = (targetStatus: string): string => {
//   const labels: Record<string, string> = {
//     'PLANNED': '将通知单状态设置为已计划，表示已确认入库计划',
//     'ARRIVED': '确认货物已到达仓库，可以开始收货作业',
//     'RECEIVING': '开始执行收货作业',
//     'PARTIALLY_RECEIVED': '标记为部分收货，表示收货未完成',
//     'RECEIVED': '标记收货作业已完成',
//     'CLOSED': '关闭通知单，不可再进行操作',
//     'CANCELLED': '取消通知单，终止整个流程'
//   }
//   
//   return labels[targetStatus] || ''
// }

// 检查是否有危险操作
// const isDangerousAction = (status: string): boolean => {
//   return ['CANCELLED'].includes(status)
// }

// 检查是否需要确认的操作
// const needsConfirmation = (status: string): boolean => {
//   return ['CLOSED', 'CANCELLED', 'RECEIVED'].includes(status)
// }
</script>

<style scoped lang="scss">
.status-actions {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  .action-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    
    .el-icon {
      color: #409eff;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
    
    .el-button {
      transition: all 0.3s ease;
      
      &:hover:not(.is-disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      // 危险操作样式
      &.el-button--danger {
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(45deg, #f56c6c, #ff8a8a);
          border-radius: inherit;
          z-index: -1;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        &:hover::before {
          opacity: 0.1;
        }
      }
      
      // 成功操作样式
      &.el-button--success {
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(45deg, #67c23a, #85ce61);
          border-radius: inherit;
          z-index: -1;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        &:hover::before {
          opacity: 0.1;
        }
      }
    }
    
    .no-actions {
      padding: 8px 16px;
      background: #f0f2f5;
      border-radius: 4px;
      color: #8c8c8c;
      
      .el-text {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .status-actions {
    .action-buttons {
      justify-content: center;
      
      .el-button {
        min-width: 120px;
      }
    }
  }
}

// 加载状态动画
@keyframes button-loading {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.el-button.is-loading {
  background: linear-gradient(90deg, #409eff, #66b3ff, #409eff);
  background-size: 200% 100%;
  animation: button-loading 1.5s ease-in-out infinite;
}

// 按钮分组样式
.action-buttons {
  .el-button {
    &:first-child {
      margin-left: 0;
    }
    
    // 按钮类型分组样式
    &.el-button--primary {
      order: 1;
    }
    
    &.el-button--success {
      order: 2;
    }
    
    &.el-button--warning {
      order: 3;
    }
    
    &.el-button--info {
      order: 4;
    }
    
    &.el-button--danger {
      order: 5;
      margin-left: auto; // 危险操作放到最右边
    }
  }
}

// 悬浮提示样式增强
:deep(.el-tooltip__popper) {
  max-width: 300px;
  
  .el-tooltip__content {
    padding: 8px 12px;
    font-size: 13px;
    line-height: 1.5;
  }
}
</style> 