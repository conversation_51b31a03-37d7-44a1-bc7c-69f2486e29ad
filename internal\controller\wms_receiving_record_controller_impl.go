package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
)

// WmsReceivingRecordController 定义收货记录控制器接口
type WmsReceivingRecordController interface {
	Create(ctx iris.Context)
	CreateFromNotification(ctx iris.Context)
	CreateBlindReceiving(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)
	GetBlindReceivingRecords(ctx iris.Context)
	UpdateStatus(ctx iris.Context)
}

// wmsReceivingRecordControllerImpl 收货记录控制器实现
type WmsReceivingRecordControllerImpl struct {
	BaseControllerImpl
	wmsReceivingRecordService service.WmsReceivingRecordService
}

// NewWmsReceivingRecordControllerImpl 创建收货记录控制器
func NewWmsReceivingRecordControllerImpl(cm *ControllerManager) *WmsReceivingRecordControllerImpl {
	return &WmsReceivingRecordControllerImpl{
		BaseControllerImpl:        *NewBaseController(cm),
		wmsReceivingRecordService: cm.GetServiceManager().GetWmsReceivingRecordService(),
	}
}

// Create 创建收货记录
// POST /api/v1/wms/receiving-records
func (ctrl *WmsReceivingRecordControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsReceivingRecord"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	var req dto.WmsReceivingRecordCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsReceivingRecordService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// CreateFromNotification 从通知单创建收货记录
// POST /api/v1/wms/receiving-records/from-notification/{notificationId:uint}
func (ctrl *WmsReceivingRecordControllerImpl) CreateFromNotification(ctx iris.Context) {
	const opName = "CreateReceivingRecordFromNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	notificationID, err := ctrl.GetPathUintParamWithError(ctx, "notificationId")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsReceivingRecordService.CreateFromNotification(ctx.Request().Context(), uint(notificationID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// CreateBlindReceiving 创建盲收记录
// POST /api/v1/wms/receiving-records/blind-receiving
func (ctrl *WmsReceivingRecordControllerImpl) CreateBlindReceiving(ctx iris.Context) {
	const opName = "CreateBlindReceivingRecord"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	var req dto.WmsBlindReceivingCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsReceivingRecordService.CreateBlindReceiving(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新收货记录
// PUT /api/v1/wms/receiving-records/{id:uint}
func (ctrl *WmsReceivingRecordControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsReceivingRecord"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsReceivingRecordUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsReceivingRecordService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除收货记录
// DELETE /api/v1/wms/receiving-records/{id:uint}
func (ctrl *WmsReceivingRecordControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsReceivingRecord"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsReceivingRecordService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个收货记录
// GET /api/v1/wms/receiving-records/{id:uint}
func (ctrl *WmsReceivingRecordControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsReceivingRecordByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsReceivingRecordService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取收货记录分页
// GET /api/v1/wms/receiving-records
func (ctrl *WmsReceivingRecordControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsReceivingRecordsPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	var req dto.WmsReceivingRecordQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	pageResult, err := ctrl.wmsReceivingRecordService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// GetBlindReceivingRecords 获取盲收记录列表
// GET /api/v1/wms/receiving-records/blind-receiving
func (ctrl *WmsReceivingRecordControllerImpl) GetBlindReceivingRecords(ctx iris.Context) {
	const opName = "GetBlindReceivingRecords"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	warehouseID := ctx.URLParamUint64("warehouseId")

	records, err := ctrl.wmsReceivingRecordService.GetBlindReceivingRecords(ctx.Request().Context(), warehouseID)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, records)
}

// UpdateStatus 更新收货记录状态
// PUT /api/v1/wms/receiving-records/{id:uint}/status
func (ctrl *WmsReceivingRecordControllerImpl) UpdateStatus(ctx iris.Context) {
	const opName = "UpdateWmsReceivingRecordStatus"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_receiving_record")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsReceivingRecordUpdateStatusReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsReceivingRecordService.UpdateStatus(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}
