<template>
  <div>
    <h1>仪表盘</h1>
    <p>这里是仪表盘页面内容。</p>
    <!-- Add dashboard widgets or content here -->
  </div>
</template>

<script setup lang="ts">
// Dashboard specific logic can go here
import { onMounted } from 'vue';

onMounted(() => {
  console.log('Dashboard component mounted');
});
</script>

<script lang="ts">
// Use a separate <script> block for options API to define the component name
export default {
  name: 'Dashboard' // This name is used by <keep-alive>
}
</script>

<style scoped>
h1 {
  margin-bottom: 1rem;
}
/* Dashboard specific styles */
</style>
