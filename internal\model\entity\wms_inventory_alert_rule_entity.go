package entity

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// WmsInventoryAlertType 库存预警类型枚举
type WmsInventoryAlertType string

const (
	AlertTypeLowStock   WmsInventoryAlertType = "LOW_STOCK"   // 低库存预警
	AlertTypeHighStock  WmsInventoryAlertType = "HIGH_STOCK"  // 高库存预警
	AlertTypeExpiry     WmsInventoryAlertType = "EXPIRY"      // 过期预警
	AlertTypeSlowMoving WmsInventoryAlertType = "SLOW_MOVING" // 呆滞库存预警
)

// WmsInventoryAlertLevel 库存预警级别枚举
type WmsInventoryAlertLevel string

const (
	AlertLevelInfo     WmsInventoryAlertLevel = "INFO"     // 信息
	AlertLevelWarning  WmsInventoryAlertLevel = "WARNING"  // 警告
	AlertLevelCritical WmsInventoryAlertLevel = "CRITICAL" // 严重
)

// WmsInventoryAlertRule 对应数据库中的 wms_inventory_alert_rule 表
// 存储库存预警规则信息
type WmsInventoryAlertRule struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	RuleName              string                 `gorm:"column:rule_name;size:200;not null;comment:规则名称" json:"ruleName"`
	RuleType              WmsInventoryAlertType  `gorm:"column:rule_type;type:varchar(20);not null;comment:预警类型" json:"ruleType"`
	ItemID                *uint                  `gorm:"column:item_id;comment:物料ID" json:"itemId"`
	WarehouseID           *uint                  `gorm:"column:warehouse_id;comment:仓库ID" json:"warehouseId"`
	LocationID            *uint                  `gorm:"column:location_id;comment:库位ID" json:"locationId"`
	ThresholdValue        *float64               `gorm:"column:threshold_value;type:numeric(12,4);comment:阈值" json:"thresholdValue"`
	ThresholdUnit         *string                `gorm:"column:threshold_unit;size:20;comment:阈值单位" json:"thresholdUnit"`
	AlertLevel            WmsInventoryAlertLevel `gorm:"column:alert_level;type:varchar(20);default:'WARNING';comment:预警级别" json:"alertLevel"`
	IsActive              bool                   `gorm:"column:is_active;default:true;comment:是否启用" json:"isActive"`
	NotificationEmails    *string                `gorm:"column:notification_emails;type:text;comment:通知邮箱列表" json:"notificationEmails"`
	NotificationUsers     *string                `gorm:"column:notification_users;type:text;comment:通知用户列表" json:"notificationUsers"`
	CheckFrequencyMinutes int                    `gorm:"column:check_frequency_minutes;default:60;comment:检查频率(分钟)" json:"checkFrequencyMinutes"`
	LastCheckAt           *time.Time             `gorm:"column:last_check_at;comment:最后检查时间" json:"lastCheckAt"`

	// 关联关系 - 移除显式外键定义以避免GORM创建错误的约束
	// 这些关联关系可以在Service层通过手动查询来实现
	// Item      *MtlItem     `gorm:"foreignKey:ItemID;references:ID" json:"item,omitempty"`
	// Warehouse *WmsLocation `gorm:"foreignKey:WarehouseID;references:ID" json:"warehouse,omitempty"`
	// Location  *WmsLocation `gorm:"foreignKey:LocationID;references:ID" json:"location,omitempty"`
}

// TableName 指定数据库表名方法
func (WmsInventoryAlertRule) TableName() string {
	return "wms_inventory_alert_rule"
}

// GetNotificationEmailList 获取通知邮箱列表
func (w *WmsInventoryAlertRule) GetNotificationEmailList() []string {
	if w.NotificationEmails == nil || *w.NotificationEmails == "" {
		return []string{}
	}

	emails := strings.Split(*w.NotificationEmails, ",")
	result := make([]string, 0, len(emails))

	for _, email := range emails {
		email = strings.TrimSpace(email)
		if email != "" {
			result = append(result, email)
		}
	}

	return result
}

// SetNotificationEmailList 设置通知邮箱列表
func (w *WmsInventoryAlertRule) SetNotificationEmailList(emails []string) {
	if len(emails) == 0 {
		w.NotificationEmails = nil
		return
	}

	emailStr := strings.Join(emails, ",")
	w.NotificationEmails = &emailStr
}

// GetNotificationUserList 获取通知用户ID列表
func (w *WmsInventoryAlertRule) GetNotificationUserList() []uint {
	if w.NotificationUsers == nil || *w.NotificationUsers == "" {
		return []uint{}
	}

	var userIDs []uint
	err := json.Unmarshal([]byte(*w.NotificationUsers), &userIDs)
	if err != nil {
		return []uint{}
	}

	return userIDs
}

// SetNotificationUserList 设置通知用户ID列表
func (w *WmsInventoryAlertRule) SetNotificationUserList(userIDs []uint) {
	if len(userIDs) == 0 {
		w.NotificationUsers = nil
		return
	}

	userIDsJSON, err := json.Marshal(userIDs)
	if err != nil {
		w.NotificationUsers = nil
		return
	}

	userIDsStr := string(userIDsJSON)
	w.NotificationUsers = &userIDsStr
}

// IsTimeToCheck 判断是否到了检查时间
func (w *WmsInventoryAlertRule) IsTimeToCheck() bool {
	if !w.IsActive {
		return false
	}

	if w.LastCheckAt == nil {
		return true
	}

	nextCheckTime := w.LastCheckAt.Add(time.Duration(w.CheckFrequencyMinutes) * time.Minute)
	return time.Now().After(nextCheckTime)
}

// UpdateLastCheckTime 更新最后检查时间
func (w *WmsInventoryAlertRule) UpdateLastCheckTime() {
	now := time.Now()
	w.LastCheckAt = &now
}

// GetRuleTypeName 获取预警类型中文名称
func (w *WmsInventoryAlertRule) GetRuleTypeName() string {
	switch w.RuleType {
	case AlertTypeLowStock:
		return "低库存预警"
	case AlertTypeHighStock:
		return "高库存预警"
	case AlertTypeExpiry:
		return "过期预警"
	case AlertTypeSlowMoving:
		return "呆滞库存预警"
	default:
		return "未知类型"
	}
}

// GetAlertLevelName 获取预警级别中文名称
func (w *WmsInventoryAlertRule) GetAlertLevelName() string {
	switch w.AlertLevel {
	case AlertLevelInfo:
		return "信息"
	case AlertLevelWarning:
		return "警告"
	case AlertLevelCritical:
		return "严重"
	default:
		return "未知级别"
	}
}

// GetScopeName 获取适用范围描述
// 注意：由于移除了关联关系，此方法只能显示ID信息
// 如需显示名称，应在Service层查询相关信息后设置
func (w *WmsInventoryAlertRule) GetScopeName() string {
	var parts []string

	if w.WarehouseID != nil {
		parts = append(parts, fmt.Sprintf("仓库ID:%d", *w.WarehouseID))
	}

	if w.LocationID != nil {
		parts = append(parts, fmt.Sprintf("库位ID:%d", *w.LocationID))
	}

	if w.ItemID != nil {
		parts = append(parts, fmt.Sprintf("物料ID:%d", *w.ItemID))
	}

	if len(parts) == 0 {
		return "全局"
	}

	return strings.Join(parts, ", ")
}

// GetThresholdDescription 获取阈值描述
func (w *WmsInventoryAlertRule) GetThresholdDescription() string {
	if w.ThresholdValue == nil {
		return "未设置"
	}

	unit := ""
	if w.ThresholdUnit != nil {
		unit = *w.ThresholdUnit
	}

	switch w.RuleType {
	case AlertTypeLowStock:
		return fmt.Sprintf("低于 %.2f %s", *w.ThresholdValue, unit)
	case AlertTypeHighStock:
		return fmt.Sprintf("高于 %.2f %s", *w.ThresholdValue, unit)
	case AlertTypeExpiry:
		return fmt.Sprintf("%.0f 天内过期", *w.ThresholdValue)
	case AlertTypeSlowMoving:
		return fmt.Sprintf("%.0f 天未移动", *w.ThresholdValue)
	default:
		return fmt.Sprintf("%.2f %s", *w.ThresholdValue, unit)
	}
}

// Validate 验证规则配置
func (w *WmsInventoryAlertRule) Validate() error {
	if w.RuleName == "" {
		return fmt.Errorf("规则名称不能为空")
	}

	if w.CheckFrequencyMinutes <= 0 {
		return fmt.Errorf("检查频率必须大于0")
	}

	// 根据不同的预警类型验证阈值
	switch w.RuleType {
	case AlertTypeLowStock, AlertTypeHighStock:
		if w.ThresholdValue == nil || *w.ThresholdValue < 0 {
			return fmt.Errorf("库存预警阈值必须大于等于0")
		}
	case AlertTypeExpiry, AlertTypeSlowMoving:
		if w.ThresholdValue == nil || *w.ThresholdValue <= 0 {
			return fmt.Errorf("时间阈值必须大于0")
		}
	}

	return nil
}
