package license

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"os"
)

// --- 密钥加载 ---

// loadPrivate<PERSON><PERSON> loads an RSA private key from a PEM file.
func loadPrivateKey(path string) (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("无法读取私钥文件 %s: %w", path, err)
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return nil, errors.New("无法解码 PEM 块")
	}

	key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		// Try parsing as PKCS1 as a fallback
		key, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("无法解析 PKCS1/PKCS8 私钥: %w", err)
		}
	}

	privateKey, ok := key.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("密钥不是有效的 RSA 私钥")
	}

	return privateKey, nil
}

// loadPublicKey loads an RSA public key from a PEM file.
func loadPublicKey(path string) (*rsa.PublicKey, error) {
	keyData, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("无法读取公钥文件 %s: %w", path, err)
	}
	return ParsePublicKeyFromBytes(keyData)
}

// ParsePublicKeyFromBytes parses an RSA public key from PEM-encoded bytes.
func ParsePublicKeyFromBytes(pubKeyBytes []byte) (*rsa.PublicKey, error) {
	block, _ := pem.Decode(pubKeyBytes)
	if block == nil {
		return nil, errors.New("无法解码 PEM 块")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		// Try parsing as PKCS1 as a fallback (often used with `openssl rsa -pubout`)
		pub, err = x509.ParsePKCS1PublicKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("无法解析 PKIX/PKCS1 公钥: %w", err)
		}
	}

	publicKey, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("密钥不是有效的 RSA 公钥")
	}

	return publicKey, nil
}

// --- 签名与验证 (底层) ---

// sign generates a SHA256 hash of the data and signs it with the private key using PSS padding.
func sign(privateKey *rsa.PrivateKey, data []byte) ([]byte, error) {
	hashed := sha256.Sum256(data)
	signature, err := rsa.SignPSS(rand.Reader, privateKey, crypto.SHA256, hashed[:], nil)
	if err != nil {
		return nil, fmt.Errorf("签名数据时出错: %w", err)
	}
	return signature, nil
}

// verify checks the PSS signature against the data hash using the public key.
func verify(publicKey *rsa.PublicKey, data []byte, signature []byte) error {
	hashed := sha256.Sum256(data)
	err := rsa.VerifyPSS(publicKey, crypto.SHA256, hashed[:], signature, nil)
	if err != nil {
		return fmt.Errorf("签名验证失败: %w", err)
	}
	return nil
}

// --- 密钥生成 ---

// GenerateRSAKeyPairPEM generates a new RSA private and public key pair and saves them
// to the specified file paths in PEM format.
func GenerateRSAKeyPairPEM(privateKeyPath, publicKeyPath string, bits int) error {
	// Define file permissions inside the function
	const defaultPrivateKeyPerm = os.FileMode(0600) // Permissions for private key file
	const defaultPublicKeyPerm = os.FileMode(0644)  // Permissions for public key file

	if bits < 2048 {
		return errors.New("密钥位数不足，建议至少使用 2048 位")
	}

	// 1. Generate RSA private key
	privateKey, err := rsa.GenerateKey(rand.Reader, bits)
	if err != nil {
		return fmt.Errorf("生成 RSA 私钥失败: %w", err)
	}

	// 2. Encode private key to PKCS8 PEM format
	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(privateKey)
	if err != nil {
		return fmt.Errorf("无法编码私钥 (PKCS8): %w", err)
	}
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	// 3. Write private key to file
	err = os.WriteFile(privateKeyPath, privateKeyPEM, defaultPrivateKeyPerm)
	if err != nil {
		return fmt.Errorf("无法写入私钥文件 %s: %w", privateKeyPath, err)
	}

	// 4. Get public key from private key
	publicKey := &privateKey.PublicKey

	// 5. Encode public key to PKIX PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return fmt.Errorf("无法编码公钥 (PKIX): %w", err)
	}
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	// 6. Write public key to file
	err = os.WriteFile(publicKeyPath, publicKeyPEM, defaultPublicKeyPerm)
	if err != nil {
		// Attempt to clean up the private key file if public key writing fails
		_ = os.Remove(privateKeyPath)
		return fmt.Errorf("无法写入公钥文件 %s: %w", publicKeyPath, err)
	}

	return nil
}
