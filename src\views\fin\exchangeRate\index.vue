<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="180"
      @refresh="loadData"
      @add="handleAdd"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
    >
      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEdit(row)" v-if="hasPermission('fin:exchangerate:edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            @click="handleDelete(row)" 
            v-if="hasPermission('fin:exchangerate:delete')"
          ></el-button>
        </el-tooltip>
      </template>
    </VNTable>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      :close-on-click-modal="false"
      draggable
      align-center
      @close="handleCloseDialog"
    >
      <VNForm
        ref="vnFormRef"
        v-model="formData"
        :header-fields="formFields"
        :default-columns="2"
        :label-width="'100px'"
        :loading="loading"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <template #actions>
          <template v-if="isViewing">
            <el-button @click="handleCloseDialog">
              关闭
            </el-button>
          </template>
          <template v-else>
            <el-button type="primary" :loading="loading" @click="submitForm">
              提交
            </el-button>
            <el-button @click="handleCloseDialog">
              取消
            </el-button>
          </template>
        </template>
      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import VNTable from '@/components/VNTable/index.vue'
import VNForm from '@/components/VNForm/index.vue'
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types'
import type { HeaderField } from '@/components/VNForm/types'
import { ElDialog, ElMessage, ElMessageBox, ElButton } from 'element-plus'
import { View, Edit, Delete } from '@element-plus/icons-vue'
import {
  getExchangeRatePage,
  addExchangeRate,
  updateExchangeRate,
  deleteExchangeRate,
  getExchangeRateDetail,
} from '@/api/fin/exchangeRate'
import type {
  ExchangeRateListItem,
  ExchangeRateFormData,
  ExchangeRateQueryParams,
} from '@/api/fin/exchangeRate'
import { getEnabledCurrencyList } from '@/api/fin/currency'
import type { CurrencyListItem } from '@/api/fin/currency'
import { hasPermission } from '@/hooks/usePermission'

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const vnFormRef = ref<InstanceType<typeof VNForm>>()
const dialogVisible = ref(false)
const formMode = ref<'add' | 'edit' | 'view'>('add')
const currentRow = ref<ExchangeRateListItem | null>(null)
const isViewing = computed(() => formMode.value === 'view')

// --- Reactive State ---
const loading = ref(false)
const tableData = ref<ExchangeRateListItem[]>([])
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
})
const currentFilters = ref<Record<string, any>>({})
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);
const formData = ref<Partial<ExchangeRateFormData>>({})
const currencyOptions = ref<{label: string, value: number}[]>([])

// --- Computed Properties (like user/index.vue) ---
const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'fromCurrency.name', label: '源币种', minWidth: 150 },
  { prop: 'toCurrency.name', label: '目标币种', minWidth: 150 },
  { prop: 'rate', label: '汇率', width: 180, sortable: true },
  { prop: 'rateDate', label: '汇率日期', width: 120, sortable: true, formatter: row => row.rateDate ? row.rateDate.substring(0, 10) : '' },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    sortable: true,
    formatter: row => formatDateTime(row.createdAt),
  },
])

const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('fin:exchangerate:add'),
  filter: true,
  columnSetting: true,
  density: true,
  fullscreen: true,
}))

const formFields = computed<HeaderField[]>(() => [
  { field: 'fromCurrencyId', label: '源币种', type: 'select', options: currencyOptions.value, props: { filterable: true, clearable: true }, rules: [{ required: true, message: '请选择源币种' }], disabled: isViewing.value },
  { field: 'toCurrencyId', label: '目标币种', type: 'select', options: currencyOptions.value, props: { filterable: true, clearable: true }, rules: [{ required: true, message: '请选择目标币种' }], disabled: isViewing.value },
  { field: 'rate', label: '汇率', type: 'number', props: { precision: 8, placeholder: '请输入汇率' }, rules: [{ required: true, message: '汇率为必填项' }], disabled: isViewing.value },
  { field: 'rateDate', label: '汇率日期', type: 'date', props: { valueFormat: 'YYYY-MM-DD' }, rules: [{ required: true, message: '汇率日期为必填项' }], disabled: isViewing.value },
])

const dialogTitle = computed(() => {
  if (formMode.value === 'add')
    return '新增汇率'
  if (formMode.value === 'edit')
    return '编辑汇率'
  if (formMode.value === 'view')
    return '查看汇率'
  return '汇率管理'
})

// --- Lifecycle Hooks ---
onMounted(() => {
  loadData()
  loadCurrencies()
})

// --- Methods ---
async function loadCurrencies() {
  try {
    const res = await getEnabledCurrencyList()
    currencyOptions.value = res.map((c: CurrencyListItem) => ({
      label: `${c.name} (${c.code})`,
      value: c.id,
    }))
  }
  catch (error) {
    currencyOptions.value = []
    const errorMessage = getApiErrorMessage(error)
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: `加载币种列表失败: ${errorMessage}`, showClose: true, duration: 5000 })
  }
}

async function loadData() {
  loading.value = true
  try {
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop) {
        const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
        sortString = `${currentSort.value.prop},${direction}`;
    }

    const params: ExchangeRateQueryParams = {
      ...currentFilters.value,
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      sort: sortString
    }

    const response = await getExchangeRatePage(params)
    tableData.value = response.list
    pagination.total = response.total
  }
  catch (error) {
    const errorMessage = getApiErrorMessage(error)
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 })
    tableData.value = []
    pagination.total = 0
  }
  finally {
    loading.value = false
  }
}

async function handleOpenForm(mode: 'add' | 'edit' | 'view', rowData?: ExchangeRateListItem) {
  formMode.value = mode
  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id)
      return
    currentRow.value = rowData
    dialogVisible.value = true
    loading.value = true
    try {
      const detailData = await getExchangeRateDetail(rowData.id)
      formData.value = {
        ...detailData,
        fromCurrencyId: detailData.fromCurrencyId,
        toCurrencyId: detailData.toCurrencyId,
      }
    }
    catch (error) {
      const errorMessage = getApiErrorMessage(error)
      ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: `获取详情失败: ${errorMessage}`, showClose: true, duration: 5000 })
      formData.value = { ...rowData }
    }
    finally {
      loading.value = false
    }
  }
  else {
    formData.value = {
      fromCurrencyId: null,
      toCurrencyId: null,
      rate: undefined,
      rateDate: new Date().toISOString().substring(0, 10),
    }
    currentRow.value = null
    dialogVisible.value = true
  }

  await nextTick(() => {
    vnFormRef.value?.clearValidate()
  })
}

function handleCloseDialog() {
  dialogVisible.value = false
  formData.value = {}
  currentRow.value = null
}

async function submitForm() {
  if (isViewing.value) {
    handleCloseDialog()
    return
  }
  const formRef = vnFormRef.value
  if (!formRef)
    return

  const isValid = await formRef.validateForm()
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项')
    return
  }

  if (formData.value.fromCurrencyId === formData.value.toCurrencyId) {
    ElMessage.warning('源币种和目标币种不能相同')
    return
  }

  const dataToSend = { ...formData.value }

  loading.value = true
  try {
    if (formMode.value === 'add') {
      await addExchangeRate(dataToSend as ExchangeRateFormData)
      ElMessage.success('新增成功')
    }
    else if (formMode.value === 'edit' && currentRow.value) {
      await updateExchangeRate(currentRow.value.id, dataToSend)
      ElMessage.success('编辑成功')
    }
    handleCloseDialog()
    loadData()
  }
  catch (error) {
    const errorMessage = getApiErrorMessage(error)
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 })
  }
  finally {
    loading.value = false
  }
}

function handleView(row: ExchangeRateListItem) {
  handleOpenForm('view', row)
}

function handleAdd() {
  handleOpenForm('add')
}

function handleEdit(row: ExchangeRateListItem) {
  handleOpenForm('edit', row)
}

async function handleDelete(row: ExchangeRateListItem) {
  try {
    await ElMessageBox.confirm('确定删除这条汇率记录吗?', '确认删除', { type: 'warning' })
    loading.value = true
    await deleteExchangeRate(row.id)
    ElMessage.success('删除成功')
    loadData()
  }
  catch (error) {
    if (error !== 'cancel') {
      const errorMessage = getApiErrorMessage(error)
      ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 })
    }
    else {
      ElMessage.info('已取消删除')
    }
  }
  finally {
    loading.value = false
  }
}

function handleFilterChange(filters: Record<string, any>) {
  currentFilters.value = filters
  pagination.currentPage = 1
  loadData()
}

function handlePageChange(page: number) {
  pagination.currentPage = page
  loadData()
}

function handlePageSizeChange(size: number) {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

function handleSortChange(sort: { prop: string; order: 'ascending' | 'descending' | null }) {
  currentSort.value = sort
  loadData()
}

// --- 辅助函数：格式化日期时间 ---
const formatDateTime = (dateStr: string | null | undefined): string => {
  if (!dateStr) return '';
  if (dateStr.startsWith('0001-01-01')) {
    return '-';
  }
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      console.warn('formatDateTime received invalid date string after initial check:', dateStr);
      return '-';
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error('Error formatting date:', dateStr, '\nError:', e);
    return '-';
  }
};

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};
</script> 