<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择出库通知单"
    width="80%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :show-operations="true"
      :operation-width="100"
      operation-fixed="right"
      row-key="id"
      :selection-type="'single'"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @selection-change="handleSelectionChange"
      @filter-change="handleFilterChange"
      highlight-current-row
    >
      <template #column-status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ formatStatus(row.status) }}
        </el-tag>
      </template>
      
      <template #column-priority="{ row }">
        <el-tag :type="getPriorityTagType(row.priority)">
          {{ formatPriority(row.priority) }}
        </el-tag>
      </template>
      
      <template #operation="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="handleSelect(row)"
          :disabled="!canSelect(row)"
        >
          选择
        </el-button>
      </template>
    </VNTable>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedRow"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElTag } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import type { WmsOutboundNotificationResp, WmsOutboundNotificationStatus } from '@/api/wms/outboundNotification'
import { getOutboundNotificationPage } from '@/api/wms/outboundNotification'

// 组件接口定义
interface OutboundNotificationSelectorProps {
  filters?: Record<string, any>
}

interface OutboundNotificationSelectorEmits {
  confirm: [notification: WmsOutboundNotificationResp]
  cancel: []
}

// Props 和 Emits
const props = withDefaults(defineProps<OutboundNotificationSelectorProps>(), {
  filters: () => ({})
})

const emit = defineEmits<OutboundNotificationSelectorEmits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const tableData = ref<WmsOutboundNotificationResp[]>([])
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const selectedRow = ref<WmsOutboundNotificationResp | null>(null)

// 表格配置
const vnTableRef = ref<InstanceType<typeof VNTable>>()

const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'notificationNo', label: '通知单号', minWidth: 140 },
  { prop: 'clientName', label: '客户名称', minWidth: 150 },
  { prop: 'clientOrderNo', label: '客户订单号', width: 120 },
  { prop: 'warehouseName', label: '仓库', width: 100 },
  { prop: 'priority', label: '优先级', width: 80, slot: true },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'requiredShipDate', label: '要求发货日期', width: 120 },
  { prop: 'consigneeName', label: '收货人', width: 100 },
  { prop: 'createdAt', label: '创建时间', width: 150 }
])

const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showFilter: true,
  filterFields: [
    {
      prop: 'notificationNo',
      label: '通知单号',
      type: 'input',
      placeholder: '请输入通知单号'
    },
    {
      prop: 'clientName',
      label: '客户名称',
      type: 'input',
      placeholder: '请输入客户名称'
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '已审核', value: 'APPROVED' },
        { label: '已分配', value: 'ALLOCATED' }
      ]
    },
    {
      prop: 'priority',
      label: '优先级',
      type: 'select',
      placeholder: '请选择优先级',
      options: [
        { label: '最高', value: 1 },
        { label: '高', value: 2 },
        { label: '中', value: 3 },
        { label: '低', value: 4 },
        { label: '最低', value: 5 }
      ]
    }
  ]
}))

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      // 只显示可以创建拣货任务的状态
      status: ['APPROVED', 'ALLOCATED'].join(','),
      ...props.filters
    }
    const res = await getOutboundNotificationPage(params)
    tableData.value = res?.list || []
    pagination.total = res?.total || 0
  } catch (error) {
    ElMessage.error('加载出库通知单列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  loadData()
}

const handleClose = () => {
  selectedRow.value = null
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleSelectionChange = (rows: WmsOutboundNotificationResp[]) => {
  selectedRow.value = rows[0] || null
}

const handleFilterChange = (filters: Record<string, any>) => {
  pagination.currentPage = 1
  Object.assign(props.filters, filters)
  loadData()
}

const handleSelect = (row: WmsOutboundNotificationResp) => {
  selectedRow.value = row
  handleConfirm()
}

const handleConfirm = () => {
  if (selectedRow.value) {
    emit('confirm', selectedRow.value)
    dialogVisible.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const canSelect = (row: WmsOutboundNotificationResp): boolean => {
  // 只有已审核或已分配状态的通知单可以创建拣货任务
  return ['APPROVED', 'ALLOCATED'].includes(row.status)
}

const getStatusTagType = (status: WmsOutboundNotificationStatus) => {
  const typeMap = {
    DRAFT: 'info',
    PLANNED: 'primary',
    APPROVED: 'success',
    ALLOCATING: 'warning',
    ALLOCATED: 'success',
    PICKING: 'warning',
    PICKED: 'success',
    SHIPPED: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: WmsOutboundNotificationStatus) => {
  const labelMap = {
    DRAFT: '草稿',
    PLANNED: '已计划',
    APPROVED: '已审核',
    ALLOCATING: '库存分配中',
    ALLOCATED: '已分配',
    PICKING: '拣货中',
    PICKED: '已拣货',
    SHIPPED: '已发运',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return labelMap[status] || '未知'
}

const getPriorityTagType = (priority: number) => {
  if (priority <= 2) return 'danger'
  if (priority <= 3) return 'warning'
  return 'info'
}

const formatPriority = (priority: number) => {
  const labels = ['', '最高', '高', '中', '低', '最低']
  return labels[priority] || '未知'
}

// 暴露方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
