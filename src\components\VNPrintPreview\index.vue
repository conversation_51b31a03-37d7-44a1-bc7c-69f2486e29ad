<template>
  <div v-if="modelValue" class="print-preview-overlay">
    <!-- 关闭按钮 -->
    <el-button
      class="close-button"
      type="danger"
      circle
      :icon="CloseBold"
      title="关闭预览"
      @click="handleClose"
    />

    <!-- 渲染核心 Frame 组件 -->
    <VNPrintPreviewFrame
      ref="frameRef"
      class="preview-frame-content"
      :default-paper-size="props.defaultPaperSize"
      :default-orientation="props.defaultOrientation"
      :default-margins="props.defaultMargins"
      :preview-background-color="props.previewBackgroundColor"
      :show-margin-lines="props.showMarginLines"
      :print-styles="props.printStyles"
    >
      <!-- 将外部传入的 content 插槽传递给 Frame -->
      <template #content>
        <slot name="content"></slot>
      </template>
    </VNPrintPreviewFrame>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, withDefaults } from 'vue';
import { ElButton, ElMessage } from 'element-plus';
import { CloseBold } from '@element-plus/icons-vue';
import VNPrintPreviewFrame from './VNPrintPreviewFrame.vue'; // 引入 Frame 组件
import type { PaperSize, PrintOrientation, PrintMargins } from './types'; // 引入 Props 类型 (可能需要更新)

// --- Props for the Overlay Wrapper (add printStyles) ---
interface OverlayProps {
  modelValue: boolean; // Required for v-model
  // Props to pass down to the Frame component
  defaultPaperSize?: PaperSize;
  defaultOrientation?: PrintOrientation;
  defaultMargins?: PrintMargins;
  previewBackgroundColor?: string;
  showMarginLines?: boolean;
  printStyles?: string; // <-- Add printStyles prop
}

const props = withDefaults(defineProps<OverlayProps>(), {
  modelValue: false, 
  defaultPaperSize: 'A4',
  defaultOrientation: 'portrait',
  defaultMargins: () => ({ top: 20, right: 15, bottom: 20, left: 15 }),
  previewBackgroundColor: '#f0f2f5',
  showMarginLines: true,
  printStyles: '', // <-- Add default
});

const emit = defineEmits(['update:modelValue', 'closed']); // Emits for v-model and close event

// --- Refs ---
const frameRef = ref<InstanceType<typeof VNPrintPreviewFrame> & { isPrinting: boolean } | null>(null);
const isPrintingLocal = ref(false);

// --- Methods ---
const handleClose = () => {
  if (frameRef.value?.isPrinting) { 
      ElMessage.info('正在处理打印，请稍后关闭');
      return; 
  }
  emit('update:modelValue', false);
  emit('closed');
};

const triggerFramePrint = async () => {
  if (!frameRef.value) return;
  isPrintingLocal.value = true;
  try {
    await frameRef.value.print();
    console.log('Frame print process finished (according to wrapper).');
  } catch (e) {
    console.error("Error triggering print from wrapper:", e);
  } finally {
    isPrintingLocal.value = false; 
  }
};

// 注意：打印按钮现在位于 Frame 组件内部，由 Frame 自身处理点击事件

</script>

<style scoped>
.print-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.1); /* 半透明背景，或用 Frame 的背景色 */
  z-index: 2000; /* 确保在顶层 */
  display: flex; /* 使用 flex 布局 */
  justify-content: center; /* 水平居中 Frame (可选) */
  align-items: center; /* 垂直居中 Frame (可选) */
  /* 如果希望 Frame 直接填满，移除 justify/align，并设置 Frame 宽高 100% */
}

.close-button {
  position: fixed; /* 固定在视口右上角 */
  top: 20px;
  right: 20px;
  z-index: 2002; /* 在 Frame 之上 */
}

.preview-frame-content {
   /* 让 Frame 占据 Overlay 的主要空间 */
   width: 100%; /* 填满 Overlay */
   height: 100%; /* 填满 Overlay */
   /* 可以添加边框或阴影与背景区分 */
   /* box-shadow: 0 0 15px rgba(0,0,0,0.2); */
   /* background-color: white; /* 确保 Frame 有背景 */
}
</style>

<style>
/* 这里的 @media print 样式需要调整，因为它现在在 VNPrintPreviewFrame.vue 中 */
/* 需要确保能隐藏 .print-preview-overlay 和 .close-button */
/* 但 VNPrintPreviewFrame.vue 内部的 @media print 会处理大部分逻辑 */
@media print {
  .print-preview-overlay {
     display: none !important;
     visibility: hidden !important;
  }
   /* VNPrintPreviewFrame.vue 内部的 @media print 会隐藏 control-panel */
}
</style> 