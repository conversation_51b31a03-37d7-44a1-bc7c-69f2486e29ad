import request from '@/utils/request'

// ==================== 类型定义 ====================

// 拣货任务状态枚举
export type WmsPickingTaskStatus = 
  | 'PENDING'         // 待分配
  | 'ASSIGNED'        // 已分配
  | 'IN_PROGRESS'     // 进行中
  | 'COMPLETED'       // 已完成
  | 'CANCELLED'       // 已取消

// 拣货策略枚举
export type WmsPickingStrategy = 
  | 'BY_ORDER'        // 按订单拣货
  | 'BY_ITEM'         // 按物料拣货
  | 'BY_LOCATION'     // 按库位拣货
  | 'BATCH_PICKING'   // 批量拣货

// 拣货任务响应类型
export interface WmsPickingTaskResp {
  id: number
  taskNo: string
  notificationId: number
  notificationNo?: string
  pickingStrategy: WmsPickingStrategy
  waveNo?: string
  assignedUserId?: number
  assignedUserName?: string
  status: WmsPickingTaskStatus
  priority: number
  estimatedDuration?: number
  actualDuration?: number
  startTime?: string
  endTime?: string
  remark?: string
  details?: WmsPickingTaskDetailResp[]
  createdAt: string
  updatedAt: string
}

// 拣货任务明细响应类型
export interface WmsPickingTaskDetailResp {
  id: number
  lineNo: number
  outboundDetailId: number
  itemId: number
  itemCode?: string
  itemName?: string
  itemSpec?: string
  requiredQty: number
  pickedQty?: number
  sourceLocationId?: number
  sourceLocationCode?: string
  targetLocationId?: number
  targetLocationCode?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  status: string
  remark?: string
}

// 拣货任务创建请求类型
export interface WmsPickingTaskCreateReq {
  notificationId: number
  pickingStrategy: WmsPickingStrategy
  waveNo?: string
  assignedUserId?: number
  priority?: number
  estimatedDuration?: number
  remark?: string
  details: WmsPickingTaskDetailCreateReq[]
}

// 拣货任务明细创建请求类型
export interface WmsPickingTaskDetailCreateReq {
  lineNo: number
  outboundDetailId: number
  itemId: number
  requiredQty: number
  sourceLocationId?: number
  targetLocationId?: number
  batchNo?: string
  remark?: string
}

// 拣货任务更新请求类型
export interface WmsPickingTaskUpdateReq {
  pickingStrategy?: WmsPickingStrategy
  waveNo?: string
  assignedUserId?: number
  priority?: number
  estimatedDuration?: number
  remark?: string
  details?: WmsPickingTaskDetailCreateReq[]
}

// 拣货任务查询请求类型
export interface WmsPickingTaskQueryReq {
  pageNum?: number
  pageSize?: number
  taskNo?: string
  notificationId?: number
  notificationNo?: string
  pickingStrategy?: WmsPickingStrategy
  waveNo?: string
  assignedUserId?: number
  status?: WmsPickingTaskStatus
  priority?: number
  createdAtStart?: string
  createdAtEnd?: string
}

// 拣货任务分配请求类型
export interface WmsPickingTaskAssignReq {
  userId: number
  remark?: string
}

// 拣货任务执行请求类型
export interface WmsPickingTaskExecuteReq {
  details: WmsPickingTaskExecuteDetailReq[]
  remark?: string
}

// 拣货任务执行明细请求类型
export interface WmsPickingTaskExecuteDetailReq {
  detailId: number
  pickedQty: number
  actualLocationId?: number
  actualBatchNo?: string
  remark?: string
}

// 波次响应类型
export interface WmsWaveResp {
  id: number
  waveNo: string
  status: string
  taskCount: number
  totalItems: number
  assignedUserId?: number
  assignedUserName?: string
  estimatedDuration?: number
  createdAt: string
  tasks?: WmsPickingTaskResp[]
}

// 波次创建请求类型
export interface WmsWaveCreateReq {
  waveNo?: string
  taskIds: number[]
  assignedUserId?: number
  estimatedDuration?: number
  remark?: string
}

// 波次查询请求类型
export interface WmsWaveQueryReq {
  pageNum?: number
  pageSize?: number
  waveNo?: string
  status?: string
  assignedUserId?: number
  createdAtStart?: string
  createdAtEnd?: string
}

// 用户响应类型
export interface WmsUserResp {
  id: number
  username: string
  nickname: string
  email?: string
  phone?: string
  department?: string
  role?: string
  status: string
}

// 用户查询请求类型
export interface WmsUserQueryReq {
  pageNum?: number
  pageSize?: number
  username?: string
  nickname?: string
  department?: string
  role?: string
  status?: string
}

// 移动端拣货任务响应类型
export interface WmsPickingTaskMobileResp {
  id: number
  taskNo: string
  priority: number
  estimatedDuration?: number
  itemCount: number
  locationCount: number
  status: WmsPickingTaskStatus
  details: WmsPickingTaskMobileDetailResp[]
}

// 移动端拣货明细响应类型
export interface WmsPickingTaskMobileDetailResp {
  id: number
  lineNo: number
  itemCode: string
  itemName: string
  requiredQty: number
  pickedQty: number
  sourceLocationCode: string
  batchNo?: string
  status: string
}

// 移动端拣货提交请求类型
export interface WmsPickingMobileSubmitReq {
  taskId: number
  userId: number
  details: WmsPickingMobileDetailSubmitReq[]
  remark?: string
}

// 移动端拣货明细提交请求类型
export interface WmsPickingMobileDetailSubmitReq {
  detailId: number
  pickedQty: number
  actualLocationCode?: string
  actualBatchNo?: string
  remark?: string
}

// 分页响应类型
export interface WmsPickingTaskPageResp {
  list: WmsPickingTaskResp[]
  total: number
}

export interface WmsWavePageResp {
  list: WmsWaveResp[]
  total: number
}

export interface WmsUserPageResp {
  list: WmsUserResp[]
  total: number
}

// ==================== API接口 ====================

// 拣货任务基础CRUD接口
export const getPickingTaskPage = (params: WmsPickingTaskQueryReq): Promise<WmsPickingTaskPageResp> => {
  return request<WmsPickingTaskPageResp>({
    url: '/wms/picking-tasks/page',
    method: 'get',
    params
  }) as unknown as Promise<WmsPickingTaskPageResp>
}

export const getPickingTaskById = (id: number): Promise<WmsPickingTaskResp> => {
  return request<WmsPickingTaskResp>({
    url: `/wms/picking-tasks/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsPickingTaskResp>
}

export const createPickingTask = (data: WmsPickingTaskCreateReq): Promise<WmsPickingTaskResp> => {
  return request<WmsPickingTaskResp>({
    url: '/wms/picking-tasks',
    method: 'post',
    data
  }) as unknown as Promise<WmsPickingTaskResp>
}

export const updatePickingTask = (id: number, data: WmsPickingTaskUpdateReq): Promise<WmsPickingTaskResp> => {
  return request<WmsPickingTaskResp>({
    url: `/wms/picking-tasks/${id}`,
    method: 'put',
    data
  }) as unknown as Promise<WmsPickingTaskResp>
}

export const deletePickingTask = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/picking-tasks/${id}`,
    method: 'delete'
  }) as unknown as Promise<void>
}

// 拣货任务分配接口
export const assignPickingTask = (id: number, data: WmsPickingTaskAssignReq): Promise<void> => {
  return request<void>({
    url: `/wms/picking-tasks/${id}/assign`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const batchAssignPickingTasks = (data: { taskIds: number[]; userId: number; remark?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/picking-tasks/batch-assign',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 拣货任务执行接口
export const startPickingTask = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/picking-tasks/${id}/start`,
    method: 'post'
  }) as unknown as Promise<void>
}

export const executePickingTask = (id: number, data: WmsPickingTaskExecuteReq): Promise<void> => {
  return request<void>({
    url: `/wms/picking-tasks/${id}/execute`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const completePickingTask = (id: number, data: { remark?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/picking-tasks/${id}/complete`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const cancelPickingTask = (id: number, data: { reason?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/picking-tasks/${id}/cancel`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 波次管理接口
export const getWaveList = (params: WmsWaveQueryReq): Promise<WmsWavePageResp> => {
  return request<WmsWavePageResp>({
    url: '/wms/waves/page',
    method: 'get',
    params
  }) as unknown as Promise<WmsWavePageResp>
}

export const getWaveById = (id: number): Promise<WmsWaveResp> => {
  return request<WmsWaveResp>({
    url: `/wms/waves/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsWaveResp>
}

export const createWave = (data: WmsWaveCreateReq): Promise<WmsWaveResp> => {
  return request<WmsWaveResp>({
    url: '/wms/waves',
    method: 'post',
    data
  }) as unknown as Promise<WmsWaveResp>
}

export const addTasksToWave = (waveId: number, taskIds: number[]): Promise<void> => {
  return request<void>({
    url: `/wms/waves/${waveId}/add-tasks`,
    method: 'post',
    data: { taskIds }
  }) as unknown as Promise<void>
}

export const removeTasksFromWave = (waveId: number, taskIds: number[]): Promise<void> => {
  return request<void>({
    url: `/wms/waves/${waveId}/remove-tasks`,
    method: 'post',
    data: { taskIds }
  }) as unknown as Promise<void>
}

// 用户管理接口
export const getUserPage = (params: WmsUserQueryReq): Promise<WmsUserPageResp> => {
  return request<WmsUserPageResp>({
    url: '/wms/users/page',
    method: 'get',
    params
  }) as unknown as Promise<WmsUserPageResp>
}

export const getPickingUsers = (): Promise<WmsUserResp[]> => {
  return request<WmsUserResp[]>({
    url: '/wms/users/picking-users',
    method: 'get'
  }) as unknown as Promise<WmsUserResp[]>
}

// 移动端接口
export const getMobilePickingTasks = (userId: number): Promise<WmsPickingTaskMobileResp[]> => {
  return request<WmsPickingTaskMobileResp[]>({
    url: `/wms/picking-tasks/mobile/user/${userId}`,
    method: 'get'
  }) as unknown as Promise<WmsPickingTaskMobileResp[]>
}

export const getMobilePickingTaskById = (id: number): Promise<WmsPickingTaskMobileResp> => {
  return request<WmsPickingTaskMobileResp>({
    url: `/wms/picking-tasks/mobile/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsPickingTaskMobileResp>
}

export const submitMobilePicking = (data: WmsPickingMobileSubmitReq): Promise<void> => {
  return request<void>({
    url: '/wms/picking-tasks/mobile/submit',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 批量操作接口
export const batchDeletePickingTasks = (ids: number[]): Promise<void> => {
  return request<void>({
    url: '/wms/picking-tasks/batch-delete',
    method: 'delete',
    data: { ids }
  }) as unknown as Promise<void>
}

export const batchCancelPickingTasks = (data: { ids: number[]; reason?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/picking-tasks/batch-cancel',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 导出接口
export const exportPickingTasks = (params: WmsPickingTaskQueryReq): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/picking-tasks/export',
    method: 'get',
    params,
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}
