package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsInventoryAlertLogRepository 库存预警日志数据访问接口
type WmsInventoryAlertLogRepository interface {
	BaseRepository[entity.WmsInventoryAlertLog, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInventoryAlertLogQueryReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByRuleID(ctx context.Context, ruleId uint) ([]*entity.WmsInventoryAlertLog, error)
	FindByInventoryID(ctx context.Context, inventoryId uint) ([]*entity.WmsInventoryAlertLog, error)
	FindByAlertType(ctx context.Context, alertType entity.WmsInventoryAlertType) ([]*entity.WmsInventoryAlertLog, error)
	FindByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) ([]*entity.WmsInventoryAlertLog, error)
	FindByStatus(ctx context.Context, status entity.WmsInventoryAlertLogStatus) ([]*entity.WmsInventoryAlertLog, error)
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryAlertLog, error)
	FindByAcknowledgedUser(ctx context.Context, userId uint) ([]*entity.WmsInventoryAlertLog, error)

	// 复杂查询
	FindActiveAlerts(ctx context.Context) ([]*entity.WmsInventoryAlertLog, error)
	FindUnacknowledgedAlerts(ctx context.Context) ([]*entity.WmsInventoryAlertLog, error)
	FindCriticalAlerts(ctx context.Context) ([]*entity.WmsInventoryAlertLog, error)
	FindRecentAlerts(ctx context.Context, hours int) ([]*entity.WmsInventoryAlertLog, error)
	FindAlertsByInventory(ctx context.Context, inventoryId uint, limit int) ([]*entity.WmsInventoryAlertLog, error)
	FindDuplicateAlerts(ctx context.Context, ruleId uint, inventoryId uint, hours int) ([]*entity.WmsInventoryAlertLog, error)

	// 统计查询
	CountByStatus(ctx context.Context, status entity.WmsInventoryAlertLogStatus) (int64, error)
	CountByAlertType(ctx context.Context, alertType entity.WmsInventoryAlertType) (int64, error)
	CountByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) (int64, error)
	CountByDateRange(ctx context.Context, startDate, endDate time.Time) (int64, error)
	GetAlertStatistics(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error)

	// 批量操作
	BatchAcknowledge(ctx context.Context, ids []uint, acknowledgedBy uint) error
	BatchResolve(ctx context.Context, ids []uint) error
	BatchUpdateStatus(ids []uint, status entity.WmsInventoryAlertLogStatus) error
	BatchCreateAlerts(logs []entity.WmsInventoryAlertLog) error

	// 清理操作
	CleanupOldLogs(beforeDate time.Time) error
	CleanupResolvedLogs(beforeDate time.Time) error

	// 事务支持
	BeginTx() *gorm.DB
}

// WmsInventoryAlertLogQueryBuilder 库存预警日志查询构建器接口
type WmsInventoryAlertLogQueryBuilder interface {
	// 条件过滤
	WhereRuleID(ruleId uint) WmsInventoryAlertLogQueryBuilder
	WhereInventoryID(inventoryId uint) WmsInventoryAlertLogQueryBuilder
	WhereAlertType(alertType entity.WmsInventoryAlertType) WmsInventoryAlertLogQueryBuilder
	WhereAlertLevel(alertLevel entity.WmsInventoryAlertLevel) WmsInventoryAlertLogQueryBuilder
	WhereStatus(status entity.WmsInventoryAlertLogStatus) WmsInventoryAlertLogQueryBuilder
	WhereAcknowledgedBy(userId uint) WmsInventoryAlertLogQueryBuilder
	WhereCreatedDateRange(startDate, endDate time.Time) WmsInventoryAlertLogQueryBuilder
	WhereAcknowledgedDateRange(startDate, endDate time.Time) WmsInventoryAlertLogQueryBuilder
	WhereResolvedDateRange(startDate, endDate time.Time) WmsInventoryAlertLogQueryBuilder
	WhereCurrentValueRange(minValue, maxValue float64) WmsInventoryAlertLogQueryBuilder
	WhereThresholdValueRange(minValue, maxValue float64) WmsInventoryAlertLogQueryBuilder

	// 关联查询
	WithRule() WmsInventoryAlertLogQueryBuilder
	WithInventory() WmsInventoryAlertLogQueryBuilder
	WithAcknowledgedUser() WmsInventoryAlertLogQueryBuilder

	// 排序
	OrderByCreatedAt(desc bool) WmsInventoryAlertLogQueryBuilder
	OrderByAcknowledgedAt(desc bool) WmsInventoryAlertLogQueryBuilder
	OrderByResolvedAt(desc bool) WmsInventoryAlertLogQueryBuilder
	OrderByAlertLevel(desc bool) WmsInventoryAlertLogQueryBuilder

	// 执行查询
	Find() ([]entity.WmsInventoryAlertLog, error)
	First() (*entity.WmsInventoryAlertLog, error)
	Count() (int64, error)
	Paginate(pageNum, pageSize int) ([]entity.WmsInventoryAlertLog, int64, error)
}

// WmsInventoryAlertLogRepositoryImpl 库存预警日志数据访问实现
type WmsInventoryAlertLogRepositoryImpl struct {
	BaseRepository[entity.WmsInventoryAlertLog, uint]
}

// NewWmsInventoryAlertLogRepository 创建库存预警日志数据访问实例
func NewWmsInventoryAlertLogRepository(db *gorm.DB) WmsInventoryAlertLogRepository {
	return &WmsInventoryAlertLogRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.WmsInventoryAlertLog, uint](db),
	}
}

// GetPage 分页查询
func (r *WmsInventoryAlertLogRepositoryImpl) GetPage(ctx context.Context, query *dto.WmsInventoryAlertLogQueryReq) (*response.PageResult, error) {
	// 构建查询条件
	var conditions []QueryCondition

	if query.RuleID != nil {
		conditions = append(conditions, NewEqualCondition("rule_id", *query.RuleID))
	}
	if query.InventoryID != nil {
		conditions = append(conditions, NewEqualCondition("inventory_id", *query.InventoryID))
	}
	if query.AlertType != nil {
		conditions = append(conditions, NewEqualCondition("alert_type", *query.AlertType))
	}
	if query.AlertLevel != nil {
		conditions = append(conditions, NewEqualCondition("alert_level", *query.AlertLevel))
	}
	if query.Status != nil {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.AcknowledgedBy != nil {
		conditions = append(conditions, NewEqualCondition("acknowledged_by", *query.AcknowledgedBy))
	}
	if query.CreatedStart != nil && query.CreatedEnd != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *query.CreatedStart, *query.CreatedEnd))
	}

	// 使用基础仓库的分页查询
	return r.FindByPage(ctx, &query.PageQuery, conditions)
}

// GetByIDWithAssociations 根据ID获取库存预警日志（包含关联数据）
func (r *WmsInventoryAlertLogRepositoryImpl) GetByIDWithAssociations(ctx context.Context, id uint) (*entity.WmsInventoryAlertLog, error) {
	var log entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Preload("Rule").
		Preload("Inventory").
		Preload("AcknowledgedUser").
		First(&log, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// FindByRuleID 根据规则ID查询预警日志
func (r *WmsInventoryAlertLogRepositoryImpl) FindByRuleID(ctx context.Context, ruleId uint) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("rule_id = ?", ruleId).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindByInventoryID 根据库存ID查询预警日志
func (r *WmsInventoryAlertLogRepositoryImpl) FindByInventoryID(ctx context.Context, inventoryId uint) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("inventory_id = ?", inventoryId).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindActiveAlerts 查找活跃的预警
func (r *WmsInventoryAlertLogRepositoryImpl) FindActiveAlerts(ctx context.Context) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("status = ?", entity.AlertLogStatusActive).
		Preload("Rule").
		Preload("Inventory").
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindUnacknowledgedAlerts 查找未确认的预警
func (r *WmsInventoryAlertLogRepositoryImpl) FindUnacknowledgedAlerts(ctx context.Context) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("status = ? AND acknowledged_at IS NULL", entity.AlertLogStatusActive).
		Preload("Rule").
		Preload("Inventory").
		Order("created_at ASC").
		Find(&logs).Error
	return logs, err
}

// FindCriticalAlerts 查找严重级别的预警
func (r *WmsInventoryAlertLogRepositoryImpl) FindCriticalAlerts(ctx context.Context) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("alert_level = ? AND status = ?", entity.AlertLevelCritical, entity.AlertLogStatusActive).
		Preload("Rule").
		Preload("Inventory").
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// FindRecentAlerts 查找最近几小时的预警
func (r *WmsInventoryAlertLogRepositoryImpl) FindRecentAlerts(ctx context.Context, hours int) ([]*entity.WmsInventoryAlertLog, error) {
	since := time.Now().Add(-time.Duration(hours) * time.Hour)
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("created_at >= ?", since).
		Preload("Rule").
		Preload("Inventory").
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

// BeginTx 开始事务
func (r *WmsInventoryAlertLogRepositoryImpl) BeginTx() *gorm.DB {
	return r.GetDB(context.Background()).Begin()
}

// NewQueryBuilder 创建查询构建器
func (r *WmsInventoryAlertLogRepositoryImpl) NewQueryBuilder(ctx context.Context) WmsInventoryAlertLogQueryBuilder {
	db := r.GetDB(ctx)
	return &WmsInventoryAlertLogQueryBuilderImpl{
		db:    db,
		query: db.Model(&entity.WmsInventoryAlertLog{}),
	}
}

// WmsInventoryAlertLogQueryBuilderImpl 库存预警日志查询构建器实现
type WmsInventoryAlertLogQueryBuilderImpl struct {
	db    *gorm.DB
	query *gorm.DB
}

// WhereRuleID 按规则ID过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereRuleID(ruleId uint) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("rule_id = ?", ruleId)
	return b
}

// WhereInventoryID 按库存ID过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereInventoryID(inventoryId uint) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("inventory_id = ?", inventoryId)
	return b
}

// WhereAlertLevel 按预警级别过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereAlertLevel(level entity.WmsInventoryAlertLevel) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("alert_level = ?", level)
	return b
}

// WhereStatus 按状态过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereStatus(status entity.WmsInventoryAlertLogStatus) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("status = ?", status)
	return b
}

// WhereTriggeredDateRange 按触发时间范围过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereTriggeredDateRange(startDate, endDate time.Time) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("triggered_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereAlertType 按预警类型过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereAlertType(alertType entity.WmsInventoryAlertType) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("alert_type = ?", alertType)
	return b
}

// WhereAcknowledgedBy 按确认人过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereAcknowledgedBy(userId uint) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("acknowledged_by = ?", userId)
	return b
}

// WhereCreatedDateRange 按创建时间范围过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereCreatedDateRange(startDate, endDate time.Time) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereAcknowledgedDateRange 按确认时间范围过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereAcknowledgedDateRange(startDate, endDate time.Time) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("acknowledged_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereResolvedDateRange 按解决时间范围过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereResolvedDateRange(startDate, endDate time.Time) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("resolved_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereCurrentValueRange 按当前值范围过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereCurrentValueRange(minValue, maxValue float64) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("current_value BETWEEN ? AND ?", minValue, maxValue)
	return b
}

// WhereThresholdValueRange 按阈值范围过滤
func (b *WmsInventoryAlertLogQueryBuilderImpl) WhereThresholdValueRange(minValue, maxValue float64) WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Where("threshold_value BETWEEN ? AND ?", minValue, maxValue)
	return b
}

// WithRule 预加载规则信息
func (b *WmsInventoryAlertLogQueryBuilderImpl) WithRule() WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Preload("Rule")
	return b
}

// WithInventory 预加载库存信息
func (b *WmsInventoryAlertLogQueryBuilderImpl) WithInventory() WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Preload("Inventory")
	return b
}

// WithAcknowledgedUser 预加载确认用户信息
func (b *WmsInventoryAlertLogQueryBuilderImpl) WithAcknowledgedUser() WmsInventoryAlertLogQueryBuilder {
	b.query = b.query.Preload("AcknowledgedUser")
	return b
}

// OrderByTriggeredAt 按触发时间排序
func (b *WmsInventoryAlertLogQueryBuilderImpl) OrderByTriggeredAt(desc bool) WmsInventoryAlertLogQueryBuilder {
	if desc {
		b.query = b.query.Order("triggered_at DESC")
	} else {
		b.query = b.query.Order("triggered_at ASC")
	}
	return b
}

// OrderByAcknowledgedAt 按确认时间排序
func (b *WmsInventoryAlertLogQueryBuilderImpl) OrderByAcknowledgedAt(desc bool) WmsInventoryAlertLogQueryBuilder {
	if desc {
		b.query = b.query.Order("acknowledged_at DESC")
	} else {
		b.query = b.query.Order("acknowledged_at ASC")
	}
	return b
}

// OrderByAlertLevel 按预警级别排序
func (b *WmsInventoryAlertLogQueryBuilderImpl) OrderByAlertLevel(desc bool) WmsInventoryAlertLogQueryBuilder {
	if desc {
		b.query = b.query.Order("alert_level DESC")
	} else {
		b.query = b.query.Order("alert_level ASC")
	}
	return b
}

// OrderByCreatedAt 按创建时间排序
func (b *WmsInventoryAlertLogQueryBuilderImpl) OrderByCreatedAt(desc bool) WmsInventoryAlertLogQueryBuilder {
	if desc {
		b.query = b.query.Order("created_at DESC")
	} else {
		b.query = b.query.Order("created_at ASC")
	}
	return b
}

// OrderByResolvedAt 按解决时间排序
func (b *WmsInventoryAlertLogQueryBuilderImpl) OrderByResolvedAt(desc bool) WmsInventoryAlertLogQueryBuilder {
	if desc {
		b.query = b.query.Order("resolved_at DESC")
	} else {
		b.query = b.query.Order("resolved_at ASC")
	}
	return b
}

// Find 执行查询
func (b *WmsInventoryAlertLogQueryBuilderImpl) Find() ([]entity.WmsInventoryAlertLog, error) {
	var logs []entity.WmsInventoryAlertLog
	err := b.query.Find(&logs).Error
	return logs, err
}

// First 查询第一条记录
func (b *WmsInventoryAlertLogQueryBuilderImpl) First() (*entity.WmsInventoryAlertLog, error) {
	var log entity.WmsInventoryAlertLog
	err := b.query.First(&log).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// Count 统计数量
func (b *WmsInventoryAlertLogQueryBuilderImpl) Count() (int64, error) {
	var count int64
	err := b.query.Count(&count).Error
	return count, err
}

// Paginate 分页查询
func (b *WmsInventoryAlertLogQueryBuilderImpl) Paginate(pageNum, pageSize int) ([]entity.WmsInventoryAlertLog, int64, error) {
	var logs []entity.WmsInventoryAlertLog
	var total int64

	// 先统计总数
	countQuery := b.db.Model(&entity.WmsInventoryAlertLog{}).Where(b.query.Statement.SQL.String())
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (pageNum - 1) * pageSize
	err := b.query.Offset(offset).Limit(pageSize).Find(&logs).Error
	return logs, total, err
}
func (r *WmsInventoryAlertLogRepositoryImpl) FindByAlertType(ctx context.Context, alertType entity.WmsInventoryAlertType) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("alert_type = ?", alertType).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) FindByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("alert_level = ?", alertLevel).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) FindByStatus(ctx context.Context, status entity.WmsInventoryAlertLogStatus) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("status = ?", status).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) FindByAcknowledgedUser(ctx context.Context, userId uint) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("acknowledged_by = ?", userId).
		Order("acknowledged_at DESC").
		Find(&logs).Error
	return logs, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) FindAlertsByInventory(ctx context.Context, inventoryId uint, limit int) ([]*entity.WmsInventoryAlertLog, error) {
	var logs []*entity.WmsInventoryAlertLog
	query := r.GetDB(ctx).Where("inventory_id = ?", inventoryId).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&logs).Error
	return logs, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) FindDuplicateAlerts(ctx context.Context, ruleId uint, inventoryId uint, hours int) ([]*entity.WmsInventoryAlertLog, error) {
	since := time.Now().Add(-time.Duration(hours) * time.Hour)
	var logs []*entity.WmsInventoryAlertLog
	err := r.GetDB(ctx).Where("rule_id = ? AND inventory_id = ? AND created_at >= ?", ruleId, inventoryId, since).
		Order("created_at DESC").
		Find(&logs).Error
	return logs, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) CountByStatus(ctx context.Context, status entity.WmsInventoryAlertLogStatus) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsInventoryAlertLog{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) CountByAlertType(ctx context.Context, alertType entity.WmsInventoryAlertType) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsInventoryAlertLog{}).Where("alert_type = ?", alertType).Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) CountByAlertLevel(ctx context.Context, alertLevel entity.WmsInventoryAlertLevel) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsInventoryAlertLog{}).Where("alert_level = ?", alertLevel).Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) CountByDateRange(ctx context.Context, startDate, endDate time.Time) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsInventoryAlertLog{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Count(&count).Error
	return count, err
}

func (r *WmsInventoryAlertLogRepositoryImpl) GetAlertStatistics(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	var result struct {
		TotalAlerts        int64 `json:"totalAlerts"`
		ActiveAlerts       int64 `json:"activeAlerts"`
		AcknowledgedAlerts int64 `json:"acknowledgedAlerts"`
		ResolvedAlerts     int64 `json:"resolvedAlerts"`
		CriticalAlerts     int64 `json:"criticalAlerts"`
		WarningAlerts      int64 `json:"warningAlerts"`
		InfoAlerts         int64 `json:"infoAlerts"`
	}

	db := r.GetDB(ctx)
	baseQuery := db.Model(&entity.WmsInventoryAlertLog{}).Where("created_at BETWEEN ? AND ?", startDate, endDate)

	// 获取总预警数
	baseQuery.Count(&result.TotalAlerts)

	// 获取各状态预警数
	db.Model(&entity.WmsInventoryAlertLog{}).Where("created_at BETWEEN ? AND ? AND status = ?", startDate, endDate, entity.AlertLogStatusActive).Count(&result.ActiveAlerts)
	db.Model(&entity.WmsInventoryAlertLog{}).Where("created_at BETWEEN ? AND ? AND status = ?", startDate, endDate, entity.AlertLogStatusAcknowledged).Count(&result.AcknowledgedAlerts)
	db.Model(&entity.WmsInventoryAlertLog{}).Where("created_at BETWEEN ? AND ? AND status = ?", startDate, endDate, entity.AlertLogStatusResolved).Count(&result.ResolvedAlerts)

	// 获取各级别预警数
	db.Model(&entity.WmsInventoryAlertLog{}).Where("created_at BETWEEN ? AND ? AND alert_level = ?", startDate, endDate, entity.AlertLevelCritical).Count(&result.CriticalAlerts)
	db.Model(&entity.WmsInventoryAlertLog{}).Where("created_at BETWEEN ? AND ? AND alert_level = ?", startDate, endDate, entity.AlertLevelWarning).Count(&result.WarningAlerts)
	db.Model(&entity.WmsInventoryAlertLog{}).Where("created_at BETWEEN ? AND ? AND alert_level = ?", startDate, endDate, entity.AlertLevelInfo).Count(&result.InfoAlerts)

	return map[string]interface{}{
		"totalAlerts":        result.TotalAlerts,
		"activeAlerts":       result.ActiveAlerts,
		"acknowledgedAlerts": result.AcknowledgedAlerts,
		"resolvedAlerts":     result.ResolvedAlerts,
		"criticalAlerts":     result.CriticalAlerts,
		"warningAlerts":      result.WarningAlerts,
		"infoAlerts":         result.InfoAlerts,
	}, nil
}

func (r *WmsInventoryAlertLogRepositoryImpl) BatchAcknowledge(ctx context.Context, ids []uint, acknowledgedBy uint) error {
	now := time.Now()
	return r.GetDB(ctx).Model(&entity.WmsInventoryAlertLog{}).Where("id IN ?", ids).Updates(map[string]interface{}{
		"status":          entity.AlertLogStatusAcknowledged,
		"acknowledged_by": acknowledgedBy,
		"acknowledged_at": now,
	}).Error
}

func (r *WmsInventoryAlertLogRepositoryImpl) BatchResolve(ctx context.Context, ids []uint) error {
	now := time.Now()
	return r.GetDB(ctx).Model(&entity.WmsInventoryAlertLog{}).Where("id IN ?", ids).Updates(map[string]interface{}{
		"status":      entity.AlertLogStatusResolved,
		"resolved_at": now,
	}).Error
}

func (r *WmsInventoryAlertLogRepositoryImpl) BatchUpdateStatus(ids []uint, status entity.WmsInventoryAlertLogStatus) error {
	return r.GetDB(context.Background()).Model(&entity.WmsInventoryAlertLog{}).Where("id IN ?", ids).Update("status", status).Error
}

func (r *WmsInventoryAlertLogRepositoryImpl) BatchCreateAlerts(logs []entity.WmsInventoryAlertLog) error {
	return r.GetDB(context.Background()).CreateInBatches(logs, 100).Error
}

func (r *WmsInventoryAlertLogRepositoryImpl) CleanupOldLogs(beforeDate time.Time) error {
	return r.GetDB(context.Background()).Where("created_at < ?", beforeDate).Delete(&entity.WmsInventoryAlertLog{}).Error
}

func (r *WmsInventoryAlertLogRepositoryImpl) CleanupResolvedLogs(beforeDate time.Time) error {
	return r.GetDB(context.Background()).Where("status = ? AND resolved_at < ?", entity.AlertLogStatusResolved, beforeDate).
		Delete(&entity.WmsInventoryAlertLog{}).Error
}
