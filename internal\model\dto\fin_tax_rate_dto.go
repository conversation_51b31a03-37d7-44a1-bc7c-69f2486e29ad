package dto

import "encoding/json"

// FinTaxRatePageReq 分页查询请求参数
type FinTaxRatePageReq struct {
	Name      string `json:"name" form:"name"`           // 税率名称
	IsEnabled *bool  `json:"isEnabled" form:"isEnabled"` // 是否启用
	Page      int    `json:"page" form:"page"`           // 页码
	PageSize  int    `json:"pageSize" form:"pageSize"`   // 每页数量
}

// FinTaxRateCreateReq 创建请求参数
type FinTaxRateCreateReq struct {
	TenantDTO
	Name        string      `json:"name" binding:"required,max=100"`
	Rate        json.Number `json:"rate" binding:"required"`
	Description string      `json:"description" binding:"max=255"`
	IsEnabled   *bool       `json:"isEnabled" binding:"omitempty"`
}

// FinTaxRateUpdateReq 更新请求参数
type FinTaxRateUpdateReq struct {
	ID uint `json:"id" binding:"required"`
	TenantDTO
	Name        string      `json:"name" binding:"required,max=100"`
	Rate        json.Number `json:"rate" binding:"required"`
	Description string      `json:"description" binding:"max=255"`
	IsEnabled   *bool       `json:"isEnabled" binding:"omitempty"`
}
