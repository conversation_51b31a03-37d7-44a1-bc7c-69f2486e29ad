package util

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"backend/pkg/constant"
	"backend/pkg/errors"
)

// 文件相关常量
const (
	FILE_TEMP_DIR           = "temp"                                                           // 临时文件目录
	FILE_UPLOAD_DIR         = "upload"                                                         // 上传文件目录
	FILE_DEFAULT_PERM       = 0755                                                             // 默认文件权限
	FILE_BUFFER_SIZE        = 1024 * 1024                                                      // 文件缓冲区大小
	FILE_SIZE_LIMIT_DEFAULT = 10 * 1024 * 1024                                                 // 默认文件大小限制（10MB）
	FILE_ALLOW_TYPES        = ".jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.pdf,.txt,.zip,.rar" // 允许的文件类型
)

// MD5HashFile 计算文件的MD5哈希值
func MD5HashFile(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// CheckFileExists 检查文件是否存在
func CheckFileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}

// GetFileSize 获取文件大小
func GetFileSize(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, errors.NewDataError(constant.FILE_NOT_FOUND, fmt.Sprintf("文件不存在或无法访问: %s", filePath)).WithCause(err)
	}
	return info.Size(), nil
}

// GetFileName 获取文件名
func GetFileName(filePath string) string {
	return filepath.Base(filePath)
}

// GetFileExt 获取文件扩展名
func GetFileExt(filePath string) string {
	return strings.ToLower(path.Ext(filePath))
}

// GetFileNameWithoutExt 获取不带扩展名的文件名
func GetFileNameWithoutExt(filePath string) string {
	fileName := filepath.Base(filePath)
	ext := filepath.Ext(fileName)
	return fileName[:len(fileName)-len(ext)]
}

// CreateDir 创建目录
func CreateDir(dirPath string) error {
	if CheckFileExists(dirPath) {
		return nil
	}

	err := os.MkdirAll(dirPath, FILE_DEFAULT_PERM)
	if err != nil {
		return errors.NewSystemError(constant.SYS_ERROR, fmt.Sprintf("创建目录失败: %s", dirPath)).WithCause(err)
	}

	return nil
}

// RemoveFile 删除文件
func RemoveFile(filePath string) error {
	if !CheckFileExists(filePath) {
		return nil
	}

	err := os.Remove(filePath)
	if err != nil {
		return errors.NewError(constant.FILE_REMOVE_ERROR, "删除文件失败").WithCause(err)
	}

	return nil
}

// RemoveDir 删除目录及其内容
func RemoveDir(dirPath string) error {
	if !CheckFileExists(dirPath) {
		return nil
	}

	err := os.RemoveAll(dirPath)
	if err != nil {
		return errors.NewSystemError(constant.SYS_ERROR, fmt.Sprintf("删除目录失败: %s", dirPath)).WithCause(err)
	}

	return nil
}

// ReadFile 读取文件内容
func ReadFile(filePath string) ([]byte, error) {
	if !CheckFileExists(filePath) {
		return nil, errors.NewError(constant.FILE_NOT_FOUND, "文件不存在").WithDetail("filePath", filePath, "文件不存在")
	}

	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, errors.NewSystemError(constant.FILE_READ_ERROR, fmt.Sprintf("读取文件失败: %s", filePath)).WithCause(err)
	}

	return data, nil
}

// WriteFile 写入文件内容
func WriteFile(filePath string, content []byte) error {
	dir := filepath.Dir(filePath)
	if err := CreateDir(dir); err != nil {
		return err
	}

	err := os.WriteFile(filePath, content, FILE_DEFAULT_PERM)
	if err != nil {
		return errors.NewError(constant.FILE_SAVE_ERROR, "保存文件失败").WithCause(err)
	}

	return nil
}

// AppendToFile 追加内容到文件
func AppendToFile(filePath string, content []byte) error {
	file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, FILE_DEFAULT_PERM)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.Write(content)
	return err
}

// CopyFile 复制文件
func CopyFile(srcPath, dstPath string) error {
	// 打开源文件
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	// 创建目标目录
	dstDir := filepath.Dir(dstPath)
	if err := CreateDir(dstDir); err != nil {
		return err
	}

	// 创建目标文件
	dstFile, err := os.Create(dstPath)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	// 复制文件内容
	buffer := make([]byte, FILE_BUFFER_SIZE)
	for {
		n, err := srcFile.Read(buffer)
		if err != nil && err != io.EOF {
			return err
		}
		if n == 0 {
			break
		}

		if _, err := dstFile.Write(buffer[:n]); err != nil {
			return err
		}
	}

	return nil
}

// MoveFile 移动文件
func MoveFile(srcPath, dstPath string) error {
	if err := CopyFile(srcPath, dstPath); err != nil {
		return err
	}
	return RemoveFile(srcPath)
}

// RenameFile 重命名文件
func RenameFile(oldPath, newPath string) error {
	return os.Rename(oldPath, newPath)
}

// GetFileMD5 获取文件MD5值
func GetFileMD5(filePath string) (string, error) {
	md5Hash, err := MD5HashFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return "", errors.NewError(constant.FILE_NOT_FOUND, "文件不存在").WithCause(err)
		}
		return "", errors.NewError(constant.FILE_MD5_ERROR, "计算文件MD5失败").WithCause(err)
	}
	return md5Hash, nil
}

// CreateTempFile 创建临时文件
func CreateTempFile(prefix, suffix string) (*os.File, error) {
	tempDir := os.TempDir()
	if err := CreateDir(tempDir); err != nil {
		return nil, err
	}

	return os.CreateTemp(tempDir, prefix+"*"+suffix)
}

// GetWorkDir 获取当前工作目录
func GetWorkDir() (string, error) {
	return os.Getwd()
}

// IsDir 判断路径是否为目录
func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

// IsFile 判断路径是否为文件
func IsFile(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return !s.IsDir()
}

// GetFileModTime 获取文件修改时间
func GetFileModTime(path string) (time.Time, error) {
	s, err := os.Stat(path)
	if err != nil {
		return time.Time{}, err
	}
	return s.ModTime(), nil
}

// ListDir 列出目录中的文件和子目录
func ListDir(dirPath string) ([]string, error) {
	files, err := os.ReadDir(dirPath)
	if err != nil {
		return nil, err
	}

	var result []string
	for _, file := range files {
		result = append(result, file.Name())
	}

	return result, nil
}

// ListDirWithFilter 列出目录中符合过滤条件的文件
func ListDirWithFilter(dirPath string, filter func(string) bool) ([]string, error) {
	files, err := os.ReadDir(dirPath)
	if err != nil {
		return nil, err
	}

	var result []string
	for _, file := range files {
		name := file.Name()
		if filter(name) {
			result = append(result, name)
		}
	}

	return result, nil
}

// SaveUploadedFile 保存上传的文件
func SaveUploadedFile(file *multipart.FileHeader, dstPath string) error {
	// 打开源文件
	src, err := file.Open()
	if err != nil {
		return errors.NewError(constant.FILE_UPLOAD_ERROR, "打开上传文件失败").WithCause(err)
	}
	defer src.Close()

	// 创建目标目录
	dstDir := filepath.Dir(dstPath)
	if err := CreateDir(dstDir); err != nil {
		return err
	}

	// 创建目标文件
	dst, err := os.Create(dstPath)
	if err != nil {
		return errors.NewError(constant.FILE_SAVE_ERROR, "创建文件失败").WithCause(err)
	}
	defer dst.Close()

	// 复制文件内容
	_, err = io.Copy(dst, src)
	if err != nil {
		return errors.NewError(constant.FILE_SAVE_ERROR, "保存文件失败").WithCause(err)
	}

	return nil
}

// CheckFileType 检查文件类型是否允许
func CheckFileType(fileName string, allowTypes string) bool {
	fileExt := strings.ToLower(path.Ext(fileName))
	return strings.Contains(strings.ToLower(allowTypes), fileExt)
}

// CheckFileSize 检查文件大小是否超出限制
func CheckFileSize(fileSize int64, maxSize int64) bool {
	return fileSize <= maxSize
}

// GetSafeFileName 获取安全的文件名
func GetSafeFileName(fileName string) string {
	// 替换不安全字符
	safeFileName := fileName
	unsafe := []string{"\\", "/", ":", "*", "?", "\"", "<", ">", "|"}
	for _, char := range unsafe {
		safeFileName = strings.ReplaceAll(safeFileName, char, "_")
	}
	return safeFileName
}

// GenerateUniqueFileName 生成唯一文件名
func GenerateUniqueFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	name := originalName[:len(originalName)-len(ext)]
	return fmt.Sprintf("%s_%d%s", name, time.Now().UnixNano(), ext)
}

// ValidateUploadFile 验证上传文件
func ValidateUploadFile(file *multipart.FileHeader) error {
	// 检查文件大小
	if !CheckFileSize(file.Size, constant.FILE_SIZE_LIMIT_DEFAULT) {
		return errors.NewError(constant.FILE_SIZE_ERROR, "文件大小超出限制")
	}

	// 检查文件类型
	if !CheckFileType(file.Filename, constant.FILE_ALLOW_TYPES) {
		return errors.NewError(constant.FILE_TYPE_ERROR, "文件类型不允许")
	}

	return nil
}

// GenerateUploadPath 生成上传文件路径
func GenerateUploadPath(fileName string) string {
	// 基于日期生成子目录路径
	year := strconv.Itoa(time.Now().Year())
	month := fmt.Sprintf("%02d", time.Now().Month())
	day := fmt.Sprintf("%02d", time.Now().Day())

	// 使用安全的文件名
	safeFileName := GetSafeFileName(fileName)

	// 生成唯一文件名
	uniqueFileName := GenerateUniqueFileName(safeFileName)

	// 构建完整路径
	return filepath.Join(FILE_UPLOAD_DIR, year, month, day, uniqueFileName)
}

// GetRelativePath 获取相对路径
func GetRelativePath(basePath, fullPath string) (string, error) {
	return filepath.Rel(basePath, fullPath)
}

// GetAbsolutePath 获取绝对路径
func GetAbsolutePath(relativePath string) (string, error) {
	return filepath.Abs(relativePath)
}

// ReadAllDirFiles 读取目录中所有文件
func ReadAllDirFiles(dirPath string) ([]string, error) {
	var files []string

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			files = append(files, path)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return files, nil
}
