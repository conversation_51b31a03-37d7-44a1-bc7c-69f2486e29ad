package license

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
)

// VerifyLicense verifies the signed license data using the public key.
// It decodes the data and signature, verifies the signature, and unmarshals the data into LicenseInfo.
func VerifyLicense(signedData *SignedLicenseData, publicKey *rsa.PublicKey) (*LicenseInfo, error) {
	if signedData == nil {
		return nil, fmt.Errorf("signed license data cannot be nil")
	}
	if publicKey == nil {
		return nil, fmt.Errorf("public key cannot be nil")
	}

	// 1. Decode Base64 data and signature
	jsonData, err := base64.StdEncoding.DecodeString(signedData.Data)
	if err != nil {
		return nil, fmt.Errorf("无法解码 Base64 数据: %w", err)
	}
	signatureBytes, err := base64.StdEncoding.DecodeString(signedData.Signature)
	if err != nil {
		return nil, fmt.Errorf("无法解码 Base64 签名: %w", err)
	}

	// 2. Verify the signature using the public key
	err = verify(publicKey, jsonData, signatureBytes) // Calls the shared verify function
	if err != nil {
		// Signature is invalid
		return nil, fmt.Errorf("授权签名无效: %w", err)
	}

	// 3. Unmarshal the JSON data into LicenseInfo struct
	var info LicenseInfo
	err = json.Unmarshal(jsonData, &info)
	if err != nil {
		return nil, fmt.Errorf("无法解析授权信息 JSON: %w", err)
	}

	// Signature is valid and data is parsed
	return &info, nil
}

// VerifyLicenseContent verifies license content directly from bytes (e.g., read from a file).
// It expects the content to be JSON representing SignedLicenseData.
func VerifyLicenseContent(content []byte, publicKey *rsa.PublicKey) (*LicenseInfo, error) {
	if len(content) == 0 {
		return nil, fmt.Errorf("license content cannot be empty")
	}

	// 1. Unmarshal the content into SignedLicenseData
	var signedData SignedLicenseData
	err := json.Unmarshal(content, &signedData)
	if err != nil {
		return nil, fmt.Errorf("无法解析授权文件内容结构: %w", err)
	}

	// 2. Verify the contained data and signature
	return VerifyLicense(&signedData, publicKey)
}

// LoadAndVerifyLicenseFile loads a license file, parses the public key (if needed), and verifies it.
// publicKeyInput can be either a path to the public key PEM file (string)
// or the PEM-encoded public key bytes ([]byte).
func LoadAndVerifyLicenseFile(licenseFilePath string, publicKeyInput interface{}) (*LicenseInfo, error) {
	var publicKey *rsa.PublicKey
	var err error

	// 1. Parse or load the public key
	switch key := publicKeyInput.(type) {
	case string: // Assume it's a path
		publicKey, err = loadPublicKey(key) // Calls shared load function
		if err != nil {
			return nil, fmt.Errorf("无法从文件加载公钥 %s: %w", key, err)
		}
	case []byte: // Assume it's the PEM content
		publicKey, err = ParsePublicKeyFromBytes(key) // Calls shared parse function
		if err != nil {
			return nil, fmt.Errorf("无法解析公钥字节: %w", err)
		}
	default:
		return nil, fmt.Errorf("无效的公钥输入类型: %T", publicKeyInput)
	}

	// 2. Read the license file content
	content, err := os.ReadFile(licenseFilePath)
	if err != nil {
		return nil, fmt.Errorf("无法读取授权文件 %s: %w", licenseFilePath, err)
	}

	// 3. Verify the content using the loaded public key
	return VerifyLicenseContent(content, publicKey)
}
