package repository

import (
	"backend/internal/model/entity"
	"context"

	"gorm.io/gorm"
)

// FiscalPeriodRepository 定义了会计期间仓库的接口。
// 它通过继承通用的 BaseRepository[entity.FiscalPeriod, uint64] 复用了标准的 CRUD 和分页查询功能，
// 同时可以根据业务需要添加额外特定的数据操作方法。
type FiscalPeriodRepository interface {
	BaseRepository[entity.FiscalPeriod, uint64] // 继承基础仓库接口，获得 CRUD, FindByPage 等标准方法

	// CreateBatch 批量创建会计期间。这是一个特定于业务的、非标准的基础仓库方法。
	CreateBatch(ctx context.Context, periods []*entity.FiscalPeriod) error

	// UpdateStatus 更新指定ID的会计期间的状态。这是一个部分更新操作，也是一个特定的业务方法。
	UpdateStatus(ctx context.Context, id uint64, status string, updaterID uint64) error

	// CheckYearExists 检查指定年份的会计期间是否存在。
	// 这是一个具体的查询辅助方法，它利用了基础仓库提供的 Exists 方法。
	CheckYearExists(ctx context.Context, year int) (bool, error)
}

// fiscalPeriodRepositoryImpl 是 FiscalPeriodRepository 的具体实现。
type fiscalPeriodRepositoryImpl struct {
	BaseRepository[entity.FiscalPeriod, uint64]          // 嵌入基础仓库实现，从而继承其所有方法
	db                                          *gorm.DB // 保留 gorm.DB 实例用于执行自定义的数据库操作
}

// NewFiscalPeriodRepository 创建一个新的会计期间仓库实例。
// 它初始化了嵌入的 BaseRepository，使其能够处理 FiscalPeriod 实体。
func NewFiscalPeriodRepository(db *gorm.DB) FiscalPeriodRepository {
	return &fiscalPeriodRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.FiscalPeriod, uint64](db), // 初始化嵌入的基础仓库
		db:             db,
	}
}

// CreateBatch 批量创建会计期间。
// 实现利用了 gorm 的批量创建功能，并通过 WithContext 传递上下文。
func (r *fiscalPeriodRepositoryImpl) CreateBatch(ctx context.Context, periods []*entity.FiscalPeriod) error {
	return r.db.WithContext(ctx).Create(&periods).Error
}

// UpdateStatus 更新指定ID的会计期间的状态。
// 实现使用了 gorm 的 Updates 方法进行部分更新，并通过 WithContext 传递上下文。
func (r *fiscalPeriodRepositoryImpl) UpdateStatus(ctx context.Context, id uint64, status string, updaterID uint64) error {
	return r.db.WithContext(ctx).Model(&entity.FiscalPeriod{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":     status,
		"updated_by": updaterID,
	}).Error
}

// CheckYearExists 检查指定年份的会计期间是否存在。
// 这个方法的实现展示了如何复用 BaseRepository 的 Exists 方法来构建具体的查询。
// 服务层将不再需要手动编写 Exists 的逻辑。
func (r *fiscalPeriodRepositoryImpl) CheckYearExists(ctx context.Context, year int) (bool, error) {
	// 直接调用继承来的 Exists 方法，并传入构造好的查询条件
	return r.Exists(ctx, []QueryCondition{
		NewEqualCondition("fiscal_year", year),
	})
}

// 注意:
// 原有的 Find 方法已被移除。相关的查询逻辑应迁移到服务(Service)层。
// 在服务层中，您应该：
// 1. 解析前端传递的查询参数 (如 dto.FiscalPeriodQueryDTO)。
// 2. 根据参数动态构建一个 `[]repository.QueryCondition` 切片。
//    例如:
//    var conditions []repository.QueryCondition
//    if query.FiscalYear != nil {
//        conditions = append(conditions, repository.NewEqualCondition("fiscal_year", *query.FiscalYear))
//    }
//    if query.Status != "" {
//        conditions = append(conditions, repository.NewEqualCondition("status", query.Status))
//    }
// 3. 调用本仓库从 BaseRepository 继承的 `FindByPage(ctx, pageQuery, conditions)` 方法来执行分页查询。
//
// 原有的 FindByID 方法也已被移除，请直接使用从 BaseRepository 继承的同名方法。
