package service

import (
	"context"
	"time"

	"github.com/mojocn/base64Captcha"

	"backend/internal/model/vo"
	"backend/pkg/config"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
)

// CaptchaService 验证码服务接口
type CaptchaService interface {
	// GenerateCaptcha 生成验证码
	GenerateCaptcha(ctx context.Context) (*vo.CaptchaVO, error)

	// VerifyCaptcha 验证验证码
	VerifyCaptcha(ctx context.Context, id, answer string) (bool, error)
}

// CaptchaServiceImpl 验证码服务实现
type CaptchaServiceImpl struct {
	logger       logger.Logger
	captcha      *base64Captcha.Captcha
	cfg          *config.CaptchaConfig
	cacheService CacheService
}

// NewCaptchaService 创建验证码服务
func NewCaptchaService(log logger.Logger, appCfg *config.Configuration, cacheSvc CacheService) (CaptchaService, error) {
	if cacheSvc == nil {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "CacheService is required for CaptchaService")
	}

	captchaStore := cacheSvc.GetCaptchaStore()

	if captchaStore == nil {
		log.Error(context.Background(), "CacheService returned nil CaptchaStore, falling back to temporary memory store")
		captchaStore = base64Captcha.NewMemoryStore(100, 5*time.Minute)
	}

	driver := base64Captcha.NewDriverDigit(
		appCfg.Captcha.Height,   // 高度
		appCfg.Captcha.Width,    // 宽度
		appCfg.Captcha.Length,   // 长度
		appCfg.Captcha.MaxSkew,  // 最大倾斜度
		appCfg.Captcha.DotCount, // 点数
	)

	impl := &CaptchaServiceImpl{
		logger:       log,
		captcha:      base64Captcha.NewCaptcha(driver, captchaStore),
		cfg:          &appCfg.Captcha,
		cacheService: cacheSvc,
	}
	return impl, nil
}

// GenerateCaptcha 生成验证码
func (s *CaptchaServiceImpl) GenerateCaptcha(ctx context.Context) (*vo.CaptchaVO, error) {
	s.logger.Debug(ctx, "生成验证码")
	if s.captcha == nil {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "验证码服务未初始化")
	}

	id, b64s, answer, err := s.captcha.Generate()
	if err != nil {
		s.logger.Error(ctx, "生成验证码失败", logger.WithError(err))
		return nil, apperrors.NewSystemError(constant.CAPTCHA_ERROR, "生成验证码失败").WithCause(err)
	}

	if config.CONFIG != nil && config.CONFIG.App.Mode == "dev" {
		s.logger.Debug(ctx, "Generated captcha answer (dev mode only)", logger.WithField("answer", answer))
	}

	captchaVO := &vo.CaptchaVO{
		CaptchaID: id,
		ImageData: b64s,
		Enabled:   s.cfg.Enable,
	}

	s.logger.Info(ctx, "验证码生成成功", logger.WithField("captchaId", id))
	return captchaVO, nil
}

// VerifyCaptcha 验证验证码
func (s *CaptchaServiceImpl) VerifyCaptcha(ctx context.Context, id, answer string) (bool, error) {
	s.logger.Debug(ctx, "验证验证码", logger.WithField("captchaId", id))

	if s.captcha == nil {
		return false, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "验证码服务未初始化")
	}

	if !s.captcha.Verify(id, answer, true) {
		s.logger.Warn(ctx, "验证码验证失败", logger.WithField("captchaId", id))
		return false, apperrors.NewParamError(constant.CAPTCHA_INVALID, "验证码无效")
	}

	s.logger.Info(ctx, "验证码验证成功", logger.WithField("captchaId", id))
	return true, nil
}
