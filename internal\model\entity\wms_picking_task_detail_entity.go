package entity

import (
	"time"

	"gorm.io/gorm"
)

// 拣货任务明细状态枚举
type WmsPickingTaskDetailStatus string

const (
	PickingDetailStatusPending    WmsPickingTaskDetailStatus = "PENDING"     // 待拣货
	PickingDetailStatusInProgress WmsPickingTaskDetailStatus = "IN_PROGRESS" // 拣货中
	PickingDetailStatusPicked     WmsPickingTaskDetailStatus = "PICKED"      // 已拣货
	PickingDetailStatusCompleted  WmsPickingTaskDetailStatus = "COMPLETED"   // 已完成
	PickingDetailStatusSkipped    WmsPickingTaskDetailStatus = "SKIPPED"     // 已跳过
	PickingDetailStatusShortage   WmsPickingTaskDetailStatus = "SHORTAGE"    // 缺货
	PickingDetailStatusCancelled  WmsPickingTaskDetailStatus = "CANCELLED"   // 已取消
)

// WmsPickingTaskDetail 对应数据库中的 wms_picking_task_detail 表
// 存储拣货任务的明细行信息
type WmsPickingTaskDetail struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	TaskID       uint `gorm:"column:task_id;not null;comment:关联拣货任务ID" json:"taskId"`             // 关联的拣货任务ID
	LineNo       int  `gorm:"column:line_no;not null;comment:行号" json:"lineNo"`                   // 明细行号
	AllocationID uint `gorm:"column:allocation_id;not null;comment:关联库存分配ID" json:"allocationId"` // 关联的库存分配ID
	ItemID       uint `gorm:"column:item_id;not null;comment:物料ID" json:"itemId"`                 // 物料ID
	LocationID   uint `gorm:"column:location_id;not null;comment:拣货库位ID" json:"locationId"`       // 拣货库位ID

	RequiredQty   float64 `gorm:"column:required_qty;type:numeric(12,4);not null;comment:要求拣货数量" json:"requiredQty"`
	PickedQty     float64 `gorm:"column:picked_qty;type:numeric(12,4);default:0;comment:实际拣货数量" json:"pickedQty"`
	UnitOfMeasure string  `gorm:"column:unit_of_measure;size:20;not null;comment:单位" json:"unitOfMeasure"`

	// 批次信息
	BatchNo        *string    `gorm:"column:batch_no;size:100;comment:批次号" json:"batchNo"`
	ProductionDate *time.Time `gorm:"column:production_date;type:date;comment:生产日期" json:"-"`
	ExpiryDate     *time.Time `gorm:"column:expiry_date;type:date;comment:过期日期" json:"-"`

	// 执行信息
	PickingSequence int        `gorm:"column:picking_sequence;comment:拣货顺序" json:"pickingSequence"`
	Status          string     `gorm:"column:status;size:20;default:'PENDING';comment:明细状态" json:"status"`
	PickedTime      *time.Time `gorm:"column:picked_time;comment:拣货时间" json:"pickedTime"`
	PickedUserID    *uint      `gorm:"column:picked_user_id;comment:拣货员ID" json:"pickedUserId"`

	// 异常信息
	ShortageQty    float64 `gorm:"column:shortage_qty;type:numeric(12,4);default:0;comment:缺货数量" json:"shortageQty"`
	ShortageReason *string `gorm:"column:shortage_reason;size:200;comment:缺货原因" json:"shortageReason"`

	Remark *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`

	// 扩展显示字段
	ProductionDateStr *string `gorm:"-" json:"productionDate"` // 生产日期字符串格式
	ExpiryDateStr     *string `gorm:"-" json:"expiryDate"`     // 过期日期字符串格式

	// 关联字段 - 移除显式外键定义以避免GORM创建错误的约束
	// 这些关联关系可以在Service层通过手动查询来实现
	// PickingTask WmsPickingTask         `gorm:"foreignKey:TaskID;references:ID" json:"pickingTask,omitempty"`      // 关联的拣货任务
	// Allocation  WmsInventoryAllocation `gorm:"foreignKey:AllocationID;references:ID" json:"allocation,omitempty"` // 关联的库存分配
	// Item        MtlItem                `gorm:"foreignKey:ItemID;references:ID" json:"item,omitempty"`             // 关联的物料
	// Location    WmsLocation            `gorm:"foreignKey:LocationID;references:ID" json:"location,omitempty"`     // 关联的库位
}

// TableName 指定数据库表名方法
func (WmsPickingTaskDetail) TableName() string {
	return "wms_picking_task_detail"
}

// AfterFind GORM钩子：查询后处理
func (w *WmsPickingTaskDetail) AfterFind(tx *gorm.DB) error {
	// 将生产日期字段转换为字符串格式用于前端显示
	if w.ProductionDate != nil {
		dateStr := w.ProductionDate.Format("2006-01-02")
		w.ProductionDateStr = &dateStr
	}
	// 将过期日期字段转换为字符串格式用于前端显示
	if w.ExpiryDate != nil {
		dateStr := w.ExpiryDate.Format("2006-01-02")
		w.ExpiryDateStr = &dateStr
	}
	return nil
}

// GetRemainingQty 获取剩余待拣数量
func (w *WmsPickingTaskDetail) GetRemainingQty() float64 {
	return w.RequiredQty - w.PickedQty
}

// IsCompleted 判断是否已完成拣货
func (w *WmsPickingTaskDetail) IsCompleted() bool {
	return w.Status == string(PickingDetailStatusPicked) && w.PickedQty >= w.RequiredQty
}

// CanPick 判断是否可以拣货
func (w *WmsPickingTaskDetail) CanPick() bool {
	return w.Status == string(PickingDetailStatusPending) && w.GetRemainingQty() > 0
}

// HasShortage 判断是否有缺货
func (w *WmsPickingTaskDetail) HasShortage() bool {
	return w.ShortageQty > 0 || w.Status == string(PickingDetailStatusShortage)
}
