import type { WmsReceivingRecordResp, WmsReceivingRecordSimpleResp, WmsReceivingRecordStatus } from "@/api/wms/receivingRecord"
import type { WmsItemRespV2 } from "@/api/wms/inboundNotification"

// ==================== 表单 & 表格相关类型 ====================

// 明细行数据
export interface DetailRowData {
  id?: number
  lineNo: number
  itemId?: number
  itemSku?: string
  itemName?: string
  itemSpecification?: string
  expectedQuantity: number
  receivedQuantity: number // 实收数量
  unitOfMeasure: string
  receivedAtLocationId?: number
  packageQty?: number
  packageUnit?: string
  batchNo?: string
  internalBatchNo?: string
  productionDate?: string
  expiryDate?: string
  discrepancyReason?: string
  remark?: string
  // 表单状态标识
  _isNew?: boolean
  _toDelete?: boolean
  _hasChanged?: boolean
}

// 收货记录表单数据
export interface ReceivingRecordFormData {
  id?: number
  receivingNo?: string
  notificationId?: number
  notificationNo?: string
  receivingType: string
  actualArrivalDate: string
  clientId: number | null
  warehouseId: number | null
  supplierShipper?: string
  sourceDocNo?: string
  remark?: string
  status?: WmsReceivingRecordStatus
  supplementDeadline?: string
  
  // 明细表数据 (对齐入库通知单，使用二维数组以适应VNForm的缺陷)
  details: DetailRowData[][]
  
  // 表单状态
  _mode?: 'create' | 'edit' | 'view'
  _loading?: boolean
}

// 搜索表单数据
export interface SearchFormData {
  receivingNo?: string
  notificationNo?: string
  status?: WmsReceivingRecordStatus | ''
  clientId?: number | null
  warehouseId?: number | null
  receivedAtRange?: [string, string]
  createdAtRange?: [string, string]
}

// ==================== 组件 Props ====================

export interface SkuSelectorProps {
  visible: boolean
  multiple?: boolean
  selectedSkus?: string[]
  filters?: Record<string, any>
  onConfirm?: (selectedItems: WmsItemRespV2[]) => void
  onCancel?: () => void
}

export interface StatusFlowProps {
  currentStatus: WmsReceivingRecordStatus
  recordId: number
  readonly?: boolean
  onStatusChange?: (newStatus: WmsReceivingRecordStatus, remark?: string) => void
}

export interface DetailTableProps {
  data: DetailRowData[]
  readonly?: boolean
  loading?: boolean
  onAdd?: () => void
  onDelete?: (index: number) => void
  onEdit?: (index: number, field: string, value: any) => void
  onSkuSelect?: (index: number) => void
}

// ==================== 配置类型 ====================

// 表格列定义
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  sortable?: boolean
  filterable?: boolean
  slot?: boolean
  showOverflowTooltip?: boolean
  fixed?: 'left' | 'right'
}

// 操作按钮定义
export interface OperationButton {
  label: string
  icon: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  handler: (row: WmsReceivingRecordSimpleResp) => void
  visible?: (row: WmsReceivingRecordSimpleResp) => boolean
  disabled?: (row: WmsReceivingRecordSimpleResp) => boolean
}

// 状态流转定义
export interface StatusTransition {
  from: WmsReceivingRecordStatus
  to: WmsReceivingRecordStatus[]
  label: string
  color: string
  requireRemark?: boolean
}

// 状态显示配置
export interface StatusConfig {
  value: WmsReceivingRecordStatus
  label: string
  color: string
  bgColor: string
  icon?: string
}

// ==================== 通用 & 辅助类型 ====================

// 下拉选项
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  children?: SelectOption[]
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  validator?: (rule: any, value: any, callback: any) => void
}

export interface FormValidationRules {
  [key: string]: ValidationRule[]
}

// 分页参数
export interface PaginationParams {
  pageNum: number
  pageSize: number
  total: number
  currentPage: number
}

// 排序参数
export interface SortParams {
  prop: string
  order: 'ascending' | 'descending'
}

// 筛选参数
export interface FilterParams {
  [key: string]: any
}

// ==================== 盲收创建请求 ====================

export interface BlindReceivingCreateReq {
  actualArrivalDate?: string // YYYY-MM-DD，可为空表示立即收货
  supplementDeadline?: string // YYYY-MM-DD，可选
  clientId: number
  warehouseId: number
  supplierShipper?: string
  sourceDocNo?: string
  remark?: string
  details?: DetailRowData[] // 明细可为空，支持后续补录
} 