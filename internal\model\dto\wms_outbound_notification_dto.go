package dto

import (
	"backend/pkg/response"
	"time"
)

// WmsOutboundNotificationCreateReq 创建出库通知单请求
type WmsOutboundNotificationCreateReq struct {
	ClientID         uint                                     `json:"clientId" validate:"required"`
	ClientOrderNo    *string                                  `json:"clientOrderNo"`
	WarehouseID      uint                                     `json:"warehouseId" validate:"required"`
	RequiredShipDate *string                                  `json:"requiredShipDate"`
	Priority         int                                      `json:"priority" validate:"min=1,max=10"`
	ConsigneeName    string                                   `json:"consigneeName" validate:"required,max=100"`
	ConsigneePhone   *string                                  `json:"consigneePhone" validate:"omitempty,max=50"`
	ConsigneeAddress *string                                  `json:"consigneeAddress" validate:"omitempty,max=500"`
	CarrierID        *uint                                    `json:"carrierId"`
	ShippingMethod   *string                                  `json:"shippingMethod" validate:"omitempty,max=50"`
	Remark           *string                                  `json:"remark"`
	Details          []WmsOutboundNotificationDetailCreateReq `json:"details" validate:"required,min=1,dive"`
}

// WmsOutboundNotificationDetailCreateReq 创建出库通知单明细请求
type WmsOutboundNotificationDetailCreateReq struct {
	LineNo                 int     `json:"lineNo" validate:"required,min=1"`
	ItemID                 uint    `json:"itemId" validate:"required"`
	RequiredQty            float64 `json:"requiredQty" validate:"required,gt=0"`
	UnitOfMeasure          string  `json:"unitOfMeasure" validate:"required,max=20"`
	RequiredBatchNo        *string `json:"requiredBatchNo" validate:"omitempty,max=100"`
	RequiredProductionDate *string `json:"requiredProductionDate"`
	RequiredExpiryDate     *string `json:"requiredExpiryDate"`
	Remark                 *string `json:"remark"`
}

// WmsOutboundNotificationUpdateReq 更新出库通知单请求
type WmsOutboundNotificationUpdateReq struct {
	ID               uint                                     `json:"id" validate:"required"`
	ClientID         uint                                     `json:"clientId" validate:"required"`
	ClientOrderNo    *string                                  `json:"clientOrderNo"`
	WarehouseID      uint                                     `json:"warehouseId" validate:"required"`
	RequiredShipDate *string                                  `json:"requiredShipDate"`
	Priority         int                                      `json:"priority" validate:"min=1,max=10"`
	ConsigneeName    string                                   `json:"consigneeName" validate:"required,max=100"`
	ConsigneePhone   *string                                  `json:"consigneePhone" validate:"omitempty,max=50"`
	ConsigneeAddress *string                                  `json:"consigneeAddress" validate:"omitempty,max=500"`
	CarrierID        *uint                                    `json:"carrierId"`
	ShippingMethod   *string                                  `json:"shippingMethod" validate:"omitempty,max=50"`
	Remark           *string                                  `json:"remark"`
	Details          []WmsOutboundNotificationDetailUpdateReq `json:"details" validate:"required,min=1,dive"`
}

// WmsOutboundNotificationDetailUpdateReq 更新出库通知单明细请求
type WmsOutboundNotificationDetailUpdateReq struct {
	ID                     *uint   `json:"id"` // 新增明细时为nil，更新明细时必填
	LineNo                 int     `json:"lineNo" validate:"required,min=1"`
	ItemID                 uint    `json:"itemId" validate:"required"`
	RequiredQty            float64 `json:"requiredQty" validate:"required,gt=0"`
	UnitOfMeasure          string  `json:"unitOfMeasure" validate:"required,max=20"`
	RequiredBatchNo        *string `json:"requiredBatchNo" validate:"omitempty,max=100"`
	RequiredProductionDate *string `json:"requiredProductionDate"`
	RequiredExpiryDate     *string `json:"requiredExpiryDate"`
	Remark                 *string `json:"remark"`
}

// WmsOutboundNotificationQueryReq 查询出库通知单请求
type WmsOutboundNotificationQueryReq struct {
	PageQuery        response.PageQuery `json:"-"` // 分页查询参数，由Controller填充
	NotificationNo   *string            `json:"notificationNo"`
	ClientID         *uint              `json:"clientId"`
	ClientOrderNo    *string            `json:"clientOrderNo"`
	WarehouseID      *uint              `json:"warehouseId"`
	Status           *string            `json:"status"`
	Priority         *int               `json:"priority"`
	ConsigneeName    *string            `json:"consigneeName"`
	CarrierID        *uint              `json:"carrierId"`
	RequiredShipDate *time.Time         `json:"requiredShipDate"`
	CreatedAtStart   *time.Time         `json:"createdAtStart"`
	CreatedAtEnd     *time.Time         `json:"createdAtEnd"`
}

// WmsOutboundNotificationApproveReq 审核出库通知单请求
type WmsOutboundNotificationApproveReq struct {
	ID     uint    `json:"id" validate:"required"`
	Remark *string `json:"remark"`
}

// WmsOutboundNotificationCancelReq 取消出库通知单请求
type WmsOutboundNotificationCancelReq struct {
	ID     uint    `json:"id" validate:"required"`
	Reason *string `json:"reason"`
}

// WmsOutboundNotificationBatchCreateReq 批量创建出库通知单请求
type WmsOutboundNotificationBatchCreateReq struct {
	Notifications []WmsOutboundNotificationCreateReq `json:"notifications" validate:"required,min=1,max=100,dive"`
}

// WmsOutboundNotificationBatchApproveReq 批量审核出库通知单请求
type WmsOutboundNotificationBatchApproveReq struct {
	IDs    []uint  `json:"ids" validate:"required,min=1,max=100"`
	Remark *string `json:"remark"`
}

// WmsOutboundNotificationBatchCancelReq 批量取消出库通知单请求
type WmsOutboundNotificationBatchCancelReq struct {
	IDs    []uint  `json:"ids" validate:"required,min=1,max=100"`
	Reason *string `json:"reason"`
}

// WmsOutboundNotificationAllocateReq 分配库存请求
type WmsOutboundNotificationAllocateReq struct {
	ID                 uint    `json:"id" validate:"required"`
	AllocationStrategy *string `json:"allocationStrategy"` // FIFO, LIFO, FEFO等
	ForceAllocate      bool    `json:"forceAllocate"`      // 是否强制分配（即使库存不足）
}

// WmsOutboundNotificationBatchAllocateReq 批量分配库存请求
type WmsOutboundNotificationBatchAllocateReq struct {
	IDs                []uint  `json:"ids" validate:"required,min=1,max=100"`
	AllocationStrategy *string `json:"allocationStrategy"`
	ForceAllocate      bool    `json:"forceAllocate"`
}

// WmsOutboundNotificationImportReq Excel导入请求
type WmsOutboundNotificationImportReq struct {
	FileData []byte `json:"fileData" validate:"required"`
	FileName string `json:"fileName" validate:"required"`
}

// WmsOutboundNotificationExportReq 导出请求
type WmsOutboundNotificationExportReq struct {
	WmsOutboundNotificationQueryReq
	ExportFormat string   `json:"exportFormat" validate:"required,oneof=excel pdf"`
	ExportFields []string `json:"exportFields"`
}

// WmsOutboundNotificationStatsReq 统计分析请求
type WmsOutboundNotificationStatsReq struct {
	DateStart   *time.Time `json:"dateStart"`
	DateEnd     *time.Time `json:"dateEnd"`
	GroupBy     string     `json:"groupBy" validate:"oneof=day week month status client warehouse"`
	ClientID    *uint      `json:"clientId"`
	WarehouseID *uint      `json:"warehouseId"`
}
