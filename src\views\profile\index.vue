<template>
  <div class="profile-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>个人信息</span>
          <!-- Add Change Password Button -->
          <el-button type="warning" :icon="Edit" @click="openChangePasswordDialog">修改密码</el-button>
        </div>
      </template>
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2" 
        :label-width="'100px'"
        :loading="loading"
        :show-submit-buttons="true"
        :show-cancel-button="false"
        group-title-type="h4"
        submit-button-text="保存更改"
        @submit="submitForm"
        @cancel="resetForm"
      >
        <!-- Avatar Slot -->
        <template #form-item-avatar="{ field, formData: formModel }">
          <el-upload
            class="avatar-uploader"
            :action="uploadActionUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <el-image v-if="formModel[field.field]" :src="formModel[field.field]" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </template>

        <!-- Roles Slot -->
        <template #form-item-roles="{ field, formData: formModel }">
           <el-tag 
             v-for="role in formModel[field.field]" 
             :key="role.id" 
             type="info" 
             style="margin-right: 5px; margin-bottom: 5px;"
           >
             {{ role.name }}
           </el-tag>
        </template>
        
        <!-- AccountBooks Slot -->
        <template #form-item-accountBooks="{ field, formData: formModel }">
           <el-tag 
             v-for="book in formModel[field.field]" 
             :key="book.id" 
             type="success" 
             style="margin-right: 5px; margin-bottom: 5px;"
            >
             {{ book.name }}
           </el-tag>
        </template>

        <!-- Default Account Book Slot (for selection) -->
        <template #form-item-defaultAccountBook>
          <el-select
            v-model="formData.defaultAccountBookId"
            placeholder="选择默认账套"
            style="width: 100%;"
            clearable
            filterable
            :disabled="loading"
          >
            <el-option
              v-for="book in availableAccountBookOptions"
              :key="book.value"
              :label="book.label"
              :value="book.value"
            />
          </el-select>
          <el-text type="info" size="small" style="margin-top: 4px;">
             设置后将在下次登录时默认选中此账套。该设置将在点击下方"保存更改"按钮后生效。
          </el-text>
        </template>

      </VNForm>
    </el-card>

    <!-- Change Password Dialog -->
    <el-dialog
      v-model="changePasswordDialogVisible"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
      @close="resetPasswordForm"
      draggable
      align-center
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordFormData"
        :rules="passwordFormRules"
        label-width="80px"
        @submit.prevent="submitChangePassword"
      >
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input v-model="passwordFormData.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordFormData.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordFormData.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="changePasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitChangePassword" :loading="passwordFormLoading">确认修改</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import VNForm from '@/components/VNForm/index.vue';
import type { HeaderField } from '@/components/VNForm/types';
import { ElCard, ElMessage, ElTag, ElUpload, ElImage, ElIcon, ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElText } from 'element-plus';
import type { UploadProps, UploadFile, UploadFiles, FormInstance, FormRules } from 'element-plus';
import { Plus, Edit } from '@element-plus/icons-vue';
import { getUserProfile, updateUserProfile, changePassword } from '@/api/user';
import type { UserProfileVO, UserProfileUpdateDTO, ChangePasswordDTO, AccountBookSimpleVO, AccountBookItem } from '@/api/user';
import { useUserStore } from '@/store/modules/user';
import { cloneDeep } from 'lodash-es';
import { getToken } from '@/utils/auth';

const vnFormRef = ref<InstanceType<typeof VNForm>>();
const passwordFormRef = ref<FormInstance>(); // Ref for password form
const userStore = useUserStore();
const loading = ref(false);
const formData = ref<Partial<UserProfileVO & 
  { 
    defaultAccountBookId?: number | string | undefined; 
    associatedAccountBooksDisplay?: string; // For displaying associated account books
    rolesDisplay?: string; // For displaying roles
  }
>>({ defaultAccountBookId: undefined });

const initialFormData = ref<Partial<UserProfileVO & 
  { 
    defaultAccountBookId?: number | string | undefined; 
    associatedAccountBooksDisplay?: string; 
    rolesDisplay?: string; 
  }
>>({ defaultAccountBookId: undefined }); // Store initial data for reset

// --- State for Change Password --- 
const changePasswordDialogVisible = ref(false);
const passwordFormLoading = ref(false);
const passwordFormData = reactive<ChangePasswordDTO & { confirmPassword?: string }>({ // Add confirmPassword
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// --- Password Form Rules ---
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  console.log("validateConfirmPassword rule:", rule);
  if (value === '') {
    callback(new Error('请再次输入新密码'));
  } else if (value !== passwordFormData.newPassword) {
    callback(new Error('两次输入的新密码不一致!'));
  } else {
    callback();
  }
};

const passwordFormRules = reactive<FormRules>({
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
});
// ---------------------------

// Define Form Fields
const formFields = computed<HeaderField[]>(() => [
  { field: 'avatar', label: '头像', type: 'slot', group: '基本信息', md: 24, lg: 24, xl: 24 },
  { field: 'username', label: '用户名', disabled: true, group: '基本信息' },
  { field: 'nickname', label: '昵称', group: '基本信息', rules: [{ required: true, message: '昵称不能为空' }] },
  { field: 'realName', label: '真实姓名', group: '基本信息' },
  { 
    field: 'gender', 
    label: '性别', 
    type: 'select', 
    options: [{ label: '未知', value: 0 }, { label: '男', value: 1 }, { label: '女', value: 2 }], 
    group: '基本信息' 
  },
  { field: 'email', label: '邮箱', group: '基本信息', rules: [{ type: 'email', message: '请输入正确的邮箱地址' }] },
  { field: 'mobile', label: '手机号码', group: '基本信息' },

  { field: 'empCode', label: '员工工号', disabled: true, group: '关联信息' },
  { field: 'empName', label: '员工姓名', disabled: true, group: '关联信息' },
  { 
    field: 'defaultAccountBook', // Field name used for slot targeting
    label: '默认账套', 
    type: 'slot', // Use slot for custom select binding
    group: '关联信息', 
  },
  { field: 'roles', label: '所属角色', type: 'slot', disabled: true, group: '关联信息', md: 24, lg: 24, xl: 24 },
  { field: 'accountBooks', label: '关联账套', type: 'slot', disabled: true, group: '关联信息', md: 24, lg: 24, xl: 24 },

  { field: 'loginIp', label: '最后登录IP', disabled: true, group: '登录信息' },
  { field: 'loginTime', label: '最后登录时间', disabled: true, group: '登录信息',
    formatter: (val: any) => {
      console.log('VNForm formatter for loginTime CALLED with:', val); // 新增测试日志
      return formatDateTime(val);
    }
  },
]);

// --- Computed property for select options (修正 label) ---
const availableAccountBookOptions = computed(() => {
  // formData.value.accountBooks is now an array after onMounted
  const booksFromFormData = formData.value?.accountBooks || [];
  
  // Process userStore.accountBooks, as it might come from /user/account-books which returns {list: [...]}
  // and is processed by request.ts interceptor to be {list: [...]}
  let booksFromStoreProcessed: AccountBookItem[] = [];
  if (userStore.accountBooks) {
    if (Array.isArray(userStore.accountBooks)) {
      booksFromStoreProcessed = userStore.accountBooks;
    } else if (typeof userStore.accountBooks === 'object' && Array.isArray((userStore.accountBooks as any).list)) {
      booksFromStoreProcessed = (userStore.accountBooks as any).list; // Extract .list if it's an object
    }
  }

  const finalBookList = booksFromFormData.length > 0 ? booksFromFormData : booksFromStoreProcessed;
  
  return (finalBookList || []).map(book => ({ 
    label: `${book.name}`,
    value: book.id
  }));
});
// -----------------------------------------

// Fetch profile data on mount
onMounted(async () => {
  loading.value = true;
  try {
    const userProfileData = await getUserProfile(); // userProfileData IS UserProfileVO
    console.log('API 返回的原始 userProfileData:', JSON.stringify(userProfileData, null, 2)); 
    
    if (userProfileData && typeof userProfileData === 'object') { // 检查 userProfileData 是否是有效的对象
      // profileData 就是 userProfileData
      const actualAccountBooks: AccountBookSimpleVO[] = userProfileData.accountBooks || [];

      const processedData = { 
        ...userProfileData,
        accountBooks: actualAccountBooks,
        defaultAccountBookId: userProfileData.defaultAccountBookId === null || userProfileData.defaultAccountBookId === '' ? undefined : userProfileData.defaultAccountBookId
      };
      formData.value = processedData;
      initialFormData.value = cloneDeep(processedData);

    } else {
      // 如果 userProfileData 不是一个有效的对象 (例如 null, undefined, 或非对象类型)
      const errorMessage = getApiErrorMessage({ message: '获取用户信息失败或返回数据格式不正确' });
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
      console.error('Failed to fetch user profile data or data is invalid/unexpected format:', userProfileData);
    }

    console.log('LoginTime before passing to VNForm:', userProfileData.loginTime); // 移入判断块

    // 加载用户关联的账套 (这里的账套列表是 AccountBookItem 类型)
    // 这些是从 /api/v1/user/account-books 获取的，其 id 和 name 与 AccountBookSimpleVO 一致
    // await userStore.FetchAccountBooks(); // 确保账套已加载
    // console.log('Account books from store after fetch:', JSON.parse(JSON.stringify(userStore.accountBooks)));

    // 确保 FetchAccountBooks 完成并且 accountBooks 已更新
    if (userStore.accountBooks.length === 0) {
      await userStore.FetchAccountBooks();
    }
    console.log('Account books loaded:', userStore.accountBooks);
    console.log('Current account book ID:', userStore.currentAccountBookId);


    // 初始化表单中的关联账套显示 (基于 userProfileData.accountBooks，类型是 AccountBookSimpleVO[])
    // 注意：profileData.accountBooks 和 userStore.accountBooks 的类型可能不完全一致
    // profileData.accountBooks 是 AccountBookSimpleVO[], userStore.accountBooks 是 AccountBookItem[]
    // AccountBookSimpleVO 比 AccountBookItem 多一个 code 字段。 在这里，我们只需要 id 和 name，所以是兼容的。
    if (userProfileData && userProfileData.accountBooks) {
      // selectedAccountBooks.value = userProfileData.value.accountBooks.map(ab => ab.id);
      // formConfigInstance.value.updateField({ name: 'associatedAccountBooksDisplay', props: { modelValue: userProfileData.value.accountBooks.map(ab => ab.name).join(', ') } });

      // const associatedBookNames = userProfileData.value.accountBooks.map(ab => ab.name).join(', ') || '无';
      // formData.value.associatedAccountBooksDisplay = associatedBookNames;

      // 正确处理 userProfileData.value.accountBooks
      const booksFromProfile = userProfileData.accountBooks || [];
      const associatedBookNames = booksFromProfile.map(ab => ab.name).join(', ') || '无';
      formData.value.associatedAccountBooksDisplay = associatedBookNames;


      // 设置默认账套下拉框的选项
      // 选项应该是用户有权访问的账套，即 userStore.accountBooks
      const accountBookOptions = userStore.accountBooks.map(ab => ({ label: ab.name, value: ab.id }));
      // console.log('Account book options for select:', accountBookOptions);

      const defaultAccountBookField = formFields.value.find(field => field.field === 'defaultAccountBook');
      if (defaultAccountBookField && defaultAccountBookField.props) {
        (defaultAccountBookField.props as any).options = accountBookOptions;
      }

      // 设置默认账套的当前值
      if (userProfileData.defaultAccountBookId) {
        formData.value.defaultAccountBookId = userProfileData.defaultAccountBookId;
      }
    }
    // 初始化所属角色显示
    if (userProfileData && userProfileData.roles) {
      formData.value.rolesDisplay = userProfileData.roles.map(role => role.name).join(', ') || '无';
    }

  } catch (error: any) {
    console.error('Error fetching user profile request:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
});

// Submit form handler
const submitForm = async () => {
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  loading.value = true;
  try {
    // 1. Prepare payload including defaultAccountBookId
    const payload: UserProfileUpdateDTO = {
      nickname: formData.value.nickname,
      realName: formData.value.realName,
      avatar: formData.value.avatar,
      gender: formData.value.gender,
      email: formData.value.email,
      mobile: formData.value.mobile,
      defaultAccountBookId: formData.value.defaultAccountBookId, // Include defaultAccountBookId
    };

    // --- 添加日志 ---
    // console.log('Payload before sending:', JSON.stringify(payload, null, 2));
    // console.log('formData.defaultAccountBookId value:', formData.value.defaultAccountBookId);
    // --------------

    // 2. Call updateUserProfile API once with the combined payload
    await updateUserProfile(payload);
    // console.log('User profile and default account book updated successfully via single API call.');
    ElMessage.success('个人信息及设置保存成功');
    
    // 3. Update local state and store UI state *after* successful save
    // Update store user info for immediate UI feedback (nickname, avatar, defaultAccountBookId)
    if (userStore.userInfo) {
        if (payload.nickname) userStore.userInfo.nickname = payload.nickname;
        if (payload.avatar) userStore.userInfo.avatar = payload.avatar;
        // Update defaultAccountBookId in store as well
        userStore.userInfo.defaultAccountBookId = payload.defaultAccountBookId;
    }
    
    // Update initial data to current saved state
    initialFormData.value = cloneDeep(formData.value); 

  } catch (error: any) {
    // console.error('Failed to update profile:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
};

// Reset form to initial state
const resetForm = () => {
  formData.value = cloneDeep(initialFormData.value);
  vnFormRef.value?.clearValidate(); // Clear validation state
};

// Avatar upload handlers (最终修正签名)
const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response: any, 
  uploadFile: UploadFile, 
  uploadFiles: UploadFiles // 确保包含第三个参数
) => {
  console.log('Profile Avatar Success Raw Response:', response);
  console.log('Profile Avatar Success Upload File:', uploadFile);
  console.log('Profile Avatar Success Upload Files:', uploadFiles);

  if (response && response.code === 0 && response.data && typeof response.data.avatar === 'string' && response.data.avatar.length > 0) {
    const newAvatarUrl = response.data.avatar;
    formData.value.avatar = newAvatarUrl;
    vnFormRef.value?.clearValidate(['avatar']);
    ElMessage.success('头像上传成功');
    if (userStore.userInfo) {
      userStore.userInfo.avatar = newAvatarUrl;
    }
  } else {
    const message = response?.message || '头像上传失败，响应格式不正确或缺少头像URL。';
    const errorMessage = getApiErrorMessage({ message });
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  }
};

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (!rawFile.type.startsWith('image/')) {
    const errorMessage = getApiErrorMessage({ message: '请上传图片文件!' });
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    return false;
  }
  if (rawFile.size / 1024 / 1024 > 2) { // 2MB limit
    const errorMessage = getApiErrorMessage({ message: '图片大小不能超过 2MB!' });
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    return false;
  }
  return true;
};

// Optional: Date Time formatter (if loginTime needs formatting)
const formatDateTime = (dateString: string | null | undefined): string => {
  console.log('formatDateTime called with:', dateString); // 恢复此日志
  if (!dateString || dateString.startsWith('0001-01-01')) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，所以+1；padStart补零
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error('Error formatting date:', e); // 增加错误日志
    return '-';
  }
};

// --- Methods for Change Password --- 
const openChangePasswordDialog = () => {
  resetPasswordForm();
  changePasswordDialogVisible.value = true;
};

const resetPasswordForm = () => {
  passwordFormData.oldPassword = '';
  passwordFormData.newPassword = '';
  passwordFormData.confirmPassword = '';
  passwordFormRef.value?.clearValidate();
};

const submitChangePassword = async () => {
  if (!passwordFormRef.value) return;
  // 获取用户 ID
  const userId = userStore.userInfo?.id;
  if (!userId) {
      const errorMessage = getApiErrorMessage({ message: '无法获取用户信息，请稍后重试' });
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
      return;
  }

  const isValid = await passwordFormRef.value.validate();
  if (!isValid) return;

  passwordFormLoading.value = true;
  try {
    const payload: ChangePasswordDTO = {
      oldPassword: passwordFormData.oldPassword,
      newPassword: passwordFormData.newPassword
    };
    // 传递 userId
    await changePassword(userId, payload);
    ElMessage.success('密码修改成功，请重新登录');
    changePasswordDialogVisible.value = false;
    // Logout after successful password change
    await userStore.Logout();
    window.location.reload(); // Force reload or redirect to login

  } catch (error) {
    // console.error('Failed to change password:', error);
    // Error message might be handled by interceptor
  } finally {
    passwordFormLoading.value = false;
  }
};

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  // 仅当它与 apiError.message (如果存在) 不同或 apiError.message 不存在时添加
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};
// --------------------------------

// --- 动态构建上传 URL ---
const baseApiUrl = import.meta.env['VITE_APP_BASE_API'] || '/api/v1';
const uploadActionUrl = computed(() => `${baseApiUrl}/user/profile/avatar`);

// --- 上传请求头 ---
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${getToken()}`
}));

</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  display: block;
  border-radius: 50%; /* Make it circular */
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
}
</style> 