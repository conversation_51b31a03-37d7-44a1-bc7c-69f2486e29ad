<template>
  <div class="task-column">
    <div class="column-header">
      <h4 class="column-title">{{ title }}</h4>
      <el-badge :value="tasks.length" class="task-count" />
    </div>
    
    <div class="column-content" ref="columnContentRef">
      <div
        v-for="task in tasks"
        :key="task.id"
        class="task-card"
        :class="getTaskCardClass(task)"
        @click="handleTaskClick(task)"
        draggable
        @dragstart="handleDragStart(task, $event)"
        @dragover.prevent
        @drop="handleDrop($event)"
      >
        <!-- 任务头部 -->
        <div class="task-header">
          <div class="task-no">{{ task.taskNo }}</div>
          <el-tag :type="getPriorityTagType(task.priority)" size="small">
            {{ formatPriority(task.priority) }}
          </el-tag>
        </div>
        
        <!-- 任务内容 -->
        <div class="task-content">
          <div class="task-info">
            <div class="info-item">
              <span class="label">出库单号:</span>
              <span class="value">{{ task.notificationNo }}</span>
            </div>
            <div class="info-item" v-if="task.waveNo">
              <span class="label">波次:</span>
              <span class="value">{{ task.waveNo }}</span>
            </div>
            <div class="info-item" v-if="task.assignedUserName">
              <span class="label">分配人员:</span>
              <span class="value">{{ task.assignedUserName }}</span>
            </div>
            <div class="info-item" v-if="task.estimatedDuration">
              <span class="label">预计时长:</span>
              <span class="value">{{ task.estimatedDuration }}分钟</span>
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="task-progress" v-if="showProgress(task)">
            <el-progress
              :percentage="getTaskProgress(task)"
              :color="getProgressColor(task)"
              :show-text="false"
              :stroke-width="4"
            />
            <span class="progress-text">{{ getTaskProgress(task) }}%</span>
          </div>
        </div>
        
        <!-- 任务操作 -->
        <div class="task-actions">
          <el-button
            v-for="action in getTaskActions(task)"
            :key="action.key"
            :type="action.type"
            size="small"
            @click.stop="handleActionClick(action, task)"
            :disabled="action.disabled"
          >
            {{ action.label }}
          </el-button>
        </div>
        
        <!-- 任务时间 -->
        <div class="task-time">
          <span class="time-label">创建时间:</span>
          <span class="time-value">{{ formatTime(task.createdAt) }}</span>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="tasks.length === 0" class="empty-state">
        <el-empty description="暂无任务" :image-size="80" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits } from 'vue'
import { ElBadge, ElTag, ElProgress, ElButton, ElEmpty } from 'element-plus'
import type { WmsPickingTaskResp, WmsPickingTaskStatus } from '@/api/wms/pickingTask'

// 组件接口定义
interface TaskColumnProps {
  title: string
  tasks: WmsPickingTaskResp[]
  status: WmsPickingTaskStatus
}

interface TaskColumnEmits {
  'task-click': [task: WmsPickingTaskResp]
  'assign-task': [task: WmsPickingTaskResp]
  'start-task': [task: WmsPickingTaskResp]
  'complete-task': [task: WmsPickingTaskResp]
  'cancel-task': [task: WmsPickingTaskResp]
  'task-drop': [task: WmsPickingTaskResp, targetStatus: WmsPickingTaskStatus]
}

// Props 和 Emits
const props = defineProps<TaskColumnProps>()
const emit = defineEmits<TaskColumnEmits>()

// 响应式数据
const columnContentRef = ref<HTMLElement>()
const draggedTask = ref<WmsPickingTaskResp | null>(null)

// 任务操作配置
interface TaskAction {
  key: string
  label: string
  type: 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger'
  disabled: boolean
}

// 方法
const handleTaskClick = (task: WmsPickingTaskResp) => {
  emit('task-click', task)
}

const handleActionClick = (action: TaskAction, task: WmsPickingTaskResp) => {
  switch (action.key) {
    case 'assign':
      emit('assign-task', task)
      break
    case 'start':
      emit('start-task', task)
      break
    case 'complete':
      emit('complete-task', task)
      break
    case 'cancel':
      emit('cancel-task', task)
      break
  }
}

const handleDragStart = (task: WmsPickingTaskResp, event: DragEvent) => {
  draggedTask.value = task
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', task.id.toString())
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (draggedTask.value && draggedTask.value.status !== props.status) {
    emit('task-drop', draggedTask.value, props.status)
  }
  draggedTask.value = null
}

// 工具方法
const getTaskCardClass = (task: WmsPickingTaskResp) => {
  return {
    'task-card-urgent': task.priority <= 2,
    'task-card-overdue': isOverdue(task),
    'task-card-selected': false // 可以根据需要添加选中状态
  }
}

const getPriorityTagType = (priority: number) => {
  if (priority <= 2) return 'danger'
  if (priority <= 3) return 'warning'
  return 'info'
}

const formatPriority = (priority: number) => {
  const labels = ['', '最高', '高', '中', '低', '最低']
  return labels[priority] || '未知'
}

const showProgress = (task: WmsPickingTaskResp) => {
  return task.status === 'IN_PROGRESS'
}

const getTaskProgress = (task: WmsPickingTaskResp) => {
  if (task.status === 'COMPLETED') return 100
  if (task.status === 'CANCELLED') return 0
  if (task.status === 'PENDING') return 10
  if (task.status === 'ASSIGNED') return 20
  if (task.status === 'IN_PROGRESS') {
    // 根据明细的拣货进度计算
    if (task.details && task.details.length > 0) {
      const totalRequired = task.details.reduce((sum, detail) => sum + detail.requiredQty, 0)
      const totalPicked = task.details.reduce((sum, detail) => sum + (detail.pickedQty || 0), 0)
      return totalRequired > 0 ? Math.round((totalPicked / totalRequired) * 100) : 20
    }
    return 50
  }
  return 0
}

const getProgressColor = (task: WmsPickingTaskResp) => {
  const progress = getTaskProgress(task)
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getTaskActions = (task: WmsPickingTaskResp): TaskAction[] => {
  const actions: TaskAction[] = []
  
  switch (task.status) {
    case 'PENDING':
      actions.push({
        key: 'assign',
        label: '分配',
        type: 'primary',
        disabled: false
      })
      break
    case 'ASSIGNED':
      actions.push({
        key: 'start',
        label: '开始',
        type: 'success',
        disabled: false
      })
      break
    case 'IN_PROGRESS':
      actions.push({
        key: 'complete',
        label: '完成',
        type: 'success',
        disabled: false
      })
      break
  }
  
  // 所有状态都可以取消（除了已完成）
  if (task.status !== 'COMPLETED') {
    actions.push({
      key: 'cancel',
      label: '取消',
      type: 'danger',
      disabled: false
    })
  }
  
  return actions
}

const formatTime = (time: string) => {
  if (!time) return '-'
  const date = new Date(time)
  return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

const isOverdue = (task: WmsPickingTaskResp) => {
  // 判断任务是否超期
  if (!task.expectedEndTime) return false
  return new Date(task.expectedEndTime) < new Date()
}
</script>

<style scoped>
.task-column {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.column-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.task-count {
  margin-left: 8px;
}

.column-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  max-height: 600px;
}

.task-card {
  background: #fff;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  transform: translateY(-1px);
}

.task-card:last-child {
  margin-bottom: 0;
}

.task-card-urgent {
  border-left: 4px solid #f56c6c;
}

.task-card-overdue {
  background: #fef0f0;
  border-color: #f56c6c;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-no {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.task-content {
  margin-bottom: 12px;
}

.task-info {
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #909399;
  flex-shrink: 0;
}

.value {
  color: #606266;
  text-align: right;
  word-break: break-all;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

.task-actions {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.task-actions .el-button {
  flex: 1;
  min-width: 0;
}

.task-time {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #c0c4cc;
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

.time-label {
  flex-shrink: 0;
}

.time-value {
  text-align: right;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

/* 拖拽样式 */
.task-card[draggable="true"]:hover {
  cursor: grab;
}

.task-card[draggable="true"]:active {
  cursor: grabbing;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-card {
    padding: 8px;
  }
  
  .task-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .info-item {
    font-size: 11px;
  }
}
</style>
