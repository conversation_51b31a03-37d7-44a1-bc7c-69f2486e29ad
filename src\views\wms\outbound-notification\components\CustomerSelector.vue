<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择客户"
    width="75%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :show-operations="true"
      :operation-width="100"
      operation-fixed="right"
      row-key="id"
      :selection-type="'single'"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @selection-change="handleSelectionChange"
      @filter-change="handleFilterChange"
      highlight-current-row
    >
      <template #column-status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ formatStatus(row.status) }}
        </el-tag>
      </template>
      
      <template #operation="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="handleSelect(row)"
        >
          选择
        </el-button>
      </template>
    </VNTable>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedRow"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElTag } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import { getClientPage, type WmsClientResp } from '@/api/wms/outboundNotification'

// 组件接口定义
interface CustomerSelectorProps {
  multiple?: boolean
  selectedCustomers?: number[]
  filters?: Record<string, any>
}

interface CustomerSelectorEmits {
  confirm: [customer: WmsClientResp]
  cancel: []
}

// Props 和 Emits
const props = withDefaults(defineProps<CustomerSelectorProps>(), {
  multiple: false,
  selectedCustomers: () => [],
  filters: () => ({})
})

const emit = defineEmits<CustomerSelectorEmits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const tableData = ref<WmsClientResp[]>([])
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const selectedRow = ref<WmsClientResp | null>(null)

// 表格配置
const vnTableRef = ref<InstanceType<typeof VNTable>>()

const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'clientCode', label: '客户编码', minWidth: 120 },
  { prop: 'clientName', label: '客户名称', minWidth: 180 },
  { 
    prop: 'status', 
    label: '状态', 
    width: 80, 
    slot: true 
  },
  { prop: 'contactPerson', label: '联系人', width: 100 },
  { prop: 'contactPhone', label: '联系电话', width: 120 },
  { prop: 'address', label: '地址', minWidth: 200 }
])

const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showFilter: true,
  filterFields: [
    {
      prop: 'clientCode',
      label: '客户编码',
      type: 'input',
      placeholder: '请输入客户编码'
    },
    {
      prop: 'clientName',
      label: '客户名称',
      type: 'input',
      placeholder: '请输入客户名称'
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      options: [
        { label: '启用', value: 'ACTIVE' },
        { label: '禁用', value: 'INACTIVE' }
      ]
    }
  ]
}))

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      status: 'ACTIVE', // 只显示启用的客户
      ...props.filters
    }
    const res = await getClientPage(params)
    tableData.value = res?.list || []
    pagination.total = res?.total || 0
  } catch (error) {
    ElMessage.error('加载客户列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  loadData()
}

const handleClose = () => {
  selectedRow.value = null
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleSelectionChange = (rows: WmsClientResp[]) => {
  selectedRow.value = rows[0] || null
}

const handleFilterChange = (filters: Record<string, any>) => {
  pagination.currentPage = 1
  Object.assign(props.filters, filters)
  loadData()
}

const handleSelect = (row: WmsClientResp) => {
  selectedRow.value = row
  handleConfirm()
}

const handleConfirm = () => {
  if (selectedRow.value) {
    emit('confirm', selectedRow.value)
    dialogVisible.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return 'success'
    case 'INACTIVE':
      return 'danger'
    default:
      return 'info'
  }
}

const formatStatus = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return '启用'
    case 'INACTIVE':
      return '禁用'
    default:
      return '未知'
  }
}

// 暴露方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
