/**
 * 库存分配状态管理 Store
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  WmsInventoryAllocationResp,
  WmsInventoryAllocationSummaryResp,
  WmsInventoryAllocationSearchForm,
  WmsInventoryAllocationStrategy
} from '@/types/wms/inventoryAllocation'
import * as inventoryAllocationApi from '@/api/wms/inventoryAllocation'

export const useInventoryAllocationStore = defineStore('wms-inventory-allocation', () => {
  // 状态数据
  const list = ref<WmsInventoryAllocationSummaryResp[]>([])
  const total = ref(0)
  const loading = ref(false)
  const currentRecord = ref<WmsInventoryAllocationResp | null>(null)

  // 搜索表单
  const searchForm = ref<WmsInventoryAllocationSearchForm>({
    pageNum: 1,
    pageSize: 10,
    notificationNo: '',
    customerName: '',
    allocationStatus: undefined,
    strategy: undefined,
    createdAtStart: '',
    createdAtEnd: ''
  })

  // 分页信息
  const pagination = computed(() => ({
    pageNum: searchForm.value.pageNum,
    pageSize: searchForm.value.pageSize,
    total: total.value
  }))

  // 分配策略选项
  const strategyOptions = ref<{ label: string; value: WmsInventoryAllocationStrategy }[]>([
    { label: 'FIFO (先进先出)', value: 'FIFO' },
    { label: 'LIFO (后进先出)', value: 'LIFO' },
    { label: '批次优先', value: 'BATCH_FIRST' },
    { label: '就近原则', value: 'NEAREST' },
    { label: '成本优先', value: 'COST_FIRST' },
    { label: '质量优先', value: 'QUALITY_FIRST' }
  ])

  // Actions
  const fetchList = async (params?: Partial<WmsInventoryAllocationSearchForm>) => {
    loading.value = true
    try {
      if (params) {
        Object.assign(searchForm.value, params)
      }
      
      const response = await inventoryAllocationApi.getAllocationPage(searchForm.value)
      list.value = response.list
      total.value = response.total
    } catch (error) {
      console.error('获取库存分配列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchDetail = async (id: number): Promise<WmsInventoryAllocationResp> => {
    try {
      const response = await inventoryAllocationApi.getAllocationDetail(id)
      currentRecord.value = response
      return response
    } catch (error) {
      console.error('获取库存分配详情失败:', error)
      throw error
    }
  }

  // 执行分配
  const executeAllocation = async (id: number, strategy?: WmsInventoryAllocationStrategy): Promise<void> => {
    try {
      await inventoryAllocationApi.executeAllocation(id, { strategy })
      await fetchList() // 刷新列表
    } catch (error) {
      console.error('执行库存分配失败:', error)
      throw error
    }
  }

  // 批量分配
  const batchAllocate = async (ids: number[], strategy: WmsInventoryAllocationStrategy): Promise<void> => {
    try {
      await inventoryAllocationApi.batchAllocate(ids, { strategy })
      await fetchList() // 刷新列表
    } catch (error) {
      console.error('批量分配失败:', error)
      throw error
    }
  }

  // 释放分配
  const releaseAllocation = async (id: number): Promise<void> => {
    try {
      await inventoryAllocationApi.releaseAllocation(id)
      await fetchList() // 刷新列表
    } catch (error) {
      console.error('释放分配失败:', error)
      throw error
    }
  }

  // 批量释放
  const batchRelease = async (ids: number[]): Promise<void> => {
    try {
      await inventoryAllocationApi.batchRelease(ids)
      await fetchList() // 刷新列表
    } catch (error) {
      console.error('批量释放失败:', error)
      throw error
    }
  }

  // 重新分配
  const retryAllocation = async (id: number, strategy?: WmsInventoryAllocationStrategy): Promise<void> => {
    try {
      await inventoryAllocationApi.retryAllocation(id, { strategy })
      await fetchList() // 刷新列表
    } catch (error) {
      console.error('重新分配失败:', error)
      throw error
    }
  }

  // 获取可用库存
  const getAvailableInventory = async (itemId: number, warehouseId?: number): Promise<any[]> => {
    try {
      return await inventoryAllocationApi.getAvailableInventory(itemId, warehouseId)
    } catch (error) {
      console.error('获取可用库存失败:', error)
      throw error
    }
  }

  // 检查库存可用性
  const checkInventoryAvailability = async (items: any[]): Promise<any> => {
    try {
      return await inventoryAllocationApi.checkInventoryAvailability(items)
    } catch (error) {
      console.error('检查库存可用性失败:', error)
      throw error
    }
  }

  // 预占库存
  const reserveInventory = async (allocationId: number, details: any[]): Promise<void> => {
    try {
      await inventoryAllocationApi.reserveInventory(allocationId, details)
    } catch (error) {
      console.error('预占库存失败:', error)
      throw error
    }
  }

  // 释放预占
  const releaseReservation = async (allocationId: number): Promise<void> => {
    try {
      await inventoryAllocationApi.releaseReservation(allocationId)
    } catch (error) {
      console.error('释放预占失败:', error)
      throw error
    }
  }

  // 获取分配建议
  const getAllocationSuggestion = async (
    notificationId: number, 
    strategy: WmsInventoryAllocationStrategy
  ): Promise<any> => {
    try {
      return await inventoryAllocationApi.getAllocationSuggestion(notificationId, strategy)
    } catch (error) {
      console.error('获取分配建议失败:', error)
      throw error
    }
  }

  // 手动分配
  const manualAllocate = async (allocationId: number, details: any[]): Promise<void> => {
    try {
      await inventoryAllocationApi.manualAllocate(allocationId, details)
      await fetchList() // 刷新列表
    } catch (error) {
      console.error('手动分配失败:', error)
      throw error
    }
  }

  // 获取分配历史
  const getAllocationHistory = async (allocationId: number): Promise<any[]> => {
    try {
      return await inventoryAllocationApi.getAllocationHistory(allocationId)
    } catch (error) {
      console.error('获取分配历史失败:', error)
      throw error
    }
  }

  // 导出分配结果
  const exportAllocationResult = async (ids: number[]): Promise<void> => {
    try {
      await inventoryAllocationApi.exportAllocationResult(ids)
    } catch (error) {
      console.error('导出分配结果失败:', error)
      throw error
    }
  }

  // 获取分配统计
  const getAllocationStats = async (params?: any): Promise<any> => {
    try {
      return await inventoryAllocationApi.getAllocationStats(params)
    } catch (error) {
      console.error('获取分配统计失败:', error)
      throw error
    }
  }

  // 优化分配
  const optimizeAllocation = async (allocationId: number, options?: any): Promise<any> => {
    try {
      return await inventoryAllocationApi.optimizeAllocation(allocationId, options)
    } catch (error) {
      console.error('优化分配失败:', error)
      throw error
    }
  }

  // 工具方法
  const resetSearchForm = () => {
    searchForm.value = {
      pageNum: 1,
      pageSize: 10,
      notificationNo: '',
      customerName: '',
      allocationStatus: undefined,
      strategy: undefined,
      createdAtStart: '',
      createdAtEnd: ''
    }
  }

  const clearCurrentRecord = () => {
    currentRecord.value = null
  }

  // 获取状态标签类型
  const getStatusTagType = (status: string) => {
    const typeMap: Record<string, string> = {
      'PENDING': 'info',
      'PARTIAL': 'warning',
      'ALLOCATED': 'success',
      'FAILED': 'danger'
    }
    return typeMap[status] || 'info'
  }

  // 格式化状态
  const formatStatus = (status: string) => {
    const labelMap: Record<string, string> = {
      'PENDING': '待分配',
      'PARTIAL': '部分分配',
      'ALLOCATED': '已分配',
      'FAILED': '分配失败'
    }
    return labelMap[status] || '未知'
  }

  // 格式化策略
  const formatStrategy = (strategy: string) => {
    const option = strategyOptions.value.find(opt => opt.value === strategy)
    return option ? option.label : strategy
  }

  // 计算分配进度
  const calculateProgress = (allocated: number, total: number): number => {
    return total > 0 ? Math.round((allocated / total) * 100) : 0
  }

  // 返回状态和方法
  return {
    // 状态
    list,
    total,
    loading,
    currentRecord,
    searchForm,
    pagination,
    strategyOptions,

    // 基础操作
    fetchList,
    fetchDetail,

    // 分配操作
    executeAllocation,
    batchAllocate,
    releaseAllocation,
    batchRelease,
    retryAllocation,
    manualAllocate,

    // 库存操作
    getAvailableInventory,
    checkInventoryAvailability,
    reserveInventory,
    releaseReservation,

    // 分析功能
    getAllocationSuggestion,
    getAllocationHistory,
    getAllocationStats,
    optimizeAllocation,

    // 导出功能
    exportAllocationResult,

    // 工具方法
    resetSearchForm,
    clearCurrentRecord,
    getStatusTagType,
    formatStatus,
    formatStrategy,
    calculateProgress
  }
})
