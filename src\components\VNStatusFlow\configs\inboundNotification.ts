/**
 * 入库通知单状态流程配置
 */
import type { StatusFlowConfig } from '../types'

// 入库通知单状态流程配置
export const INBOUND_NOTIFICATION_CONFIG: StatusFlowConfig = {
  businessType: 'INBOUND_NOTIFICATION',
  
  // 状态选项配置
  statusOptions: [
    {
      value: 'DRAFT',
      label: '草稿',
      color: '#909399',
      bgColor: '#f4f4f5',
      icon: 'Edit',
      description: '通知单草稿状态，可以编辑和修改'
    },
    {
      value: 'PLANNED',
      label: '已计划',
      color: '#409eff',
      bgColor: '#ecf5ff',
      icon: 'Calendar',
      description: '已提交计划，等待到货确认'
    },
    {
      value: 'ARRIVED',
      label: '已到货',
      color: '#67c23a',
      bgColor: '#f0f9ff',
      icon: 'Van',
      description: '货物已到达，可以开始收货'
    },
    {
      value: 'RECEIVING',
      label: '收货中',
      color: '#e6a23c',
      bgColor: '#fdf6ec',
      icon: 'Goods',
      description: '正在进行收货作业'
    },
    {
      value: 'PARTIALLY_RECEIVED',
      label: '部分收货',
      color: '#e6a23c',
      bgColor: '#fdf6ec',
      icon: 'Warning',
      description: '已收货但未完成全部数量'
    },
    {
      value: 'RECEIVED',
      label: '收货完成',
      color: '#67c23a',
      bgColor: '#f0f9ff',
      icon: 'Check',
      description: '收货作业已完成'
    },
    {
      value: 'CLOSED',
      label: '已关闭',
      color: '#909399',
      bgColor: '#f4f4f5',
      icon: 'Lock',
      description: '通知单已关闭，不可再操作'
    },
    {
      value: 'CANCELLED',
      label: '已取消',
      color: '#f56c6c',
      bgColor: '#fef0f0',
      icon: 'Close',
      description: '通知单已取消'
    }
  ],

  // 状态转换规则
  statusTransitions: [
    {
      from: 'DRAFT',
      to: ['PLANNED', 'CANCELLED'],
      permission: 'wms:inbound:plan'
    },
    {
      from: 'PLANNED',
      to: ['ARRIVED', 'CANCELLED'],
      permission: 'wms:inbound:confirm-arrival'
    },
    {
      from: 'ARRIVED',
      to: ['RECEIVING', 'CANCELLED'],
      permission: 'wms:inbound:start-receiving'
    },
    {
      from: 'RECEIVING',
      to: ['PARTIALLY_RECEIVED', 'RECEIVED', 'CANCELLED'],
      permission: 'wms:inbound:complete-receiving'
    },
    {
      from: 'PARTIALLY_RECEIVED',
      to: ['RECEIVING', 'RECEIVED', 'CANCELLED'],
      permission: 'wms:inbound:continue-receiving'
    },
    {
      from: 'RECEIVED',
      to: ['CLOSED'],
      permission: 'wms:inbound:close'
    },
    {
      from: 'CANCELLED',
      to: [],
      permission: ''
    },
    {
      from: 'CLOSED',
      to: [],
      permission: ''
    }
  ],

  // 显示配置
  displayConfig: {
    showActions: true,
    showProgress: true,
    allowManualTransition: true,
    showDescription: true,
    showIcon: true,
    nodeSize: 'medium',
    lineStyle: 'solid'
  },

  // 权限配置
  permissionConfig: {
    'DRAFT->PLANNED': 'wms:inbound:plan',
    'DRAFT->CANCELLED': 'wms:inbound:cancel',
    'PLANNED->ARRIVED': 'wms:inbound:confirm-arrival',
    'PLANNED->CANCELLED': 'wms:inbound:cancel',
    'ARRIVED->RECEIVING': 'wms:inbound:start-receiving',
    'ARRIVED->CANCELLED': 'wms:inbound:cancel',
    'RECEIVING->PARTIALLY_RECEIVED': 'wms:inbound:partial-receive',
    'RECEIVING->RECEIVED': 'wms:inbound:complete-receiving',
    'RECEIVING->CANCELLED': 'wms:inbound:cancel',
    'PARTIALLY_RECEIVED->RECEIVING': 'wms:inbound:continue-receiving',
    'PARTIALLY_RECEIVED->RECEIVED': 'wms:inbound:complete-receiving',
    'PARTIALLY_RECEIVED->CANCELLED': 'wms:inbound:cancel',
    'RECEIVED->CLOSED': 'wms:inbound:close'
  },

  // API配置
  apiConfig: {
    changeStatusApi: '/api/v1/wms/inbound-notifications/{id}/status',
    checkPermissionApi: '/api/v1/auth/check-permission'
  }
}

// 状态转换标签映射
export const STATUS_TRANSITION_LABELS = {
  'DRAFT->PLANNED': '提交计划',
  'DRAFT->CANCELLED': '取消通知单',
  'PLANNED->ARRIVED': '确认到货',
  'PLANNED->CANCELLED': '取消通知单',
  'ARRIVED->RECEIVING': '开始收货',
  'ARRIVED->CANCELLED': '取消通知单',
  'RECEIVING->PARTIALLY_RECEIVED': '部分收货',
  'RECEIVING->RECEIVED': '收货完成',
  'RECEIVING->CANCELLED': '取消收货',
  'PARTIALLY_RECEIVED->RECEIVING': '继续收货',
  'PARTIALLY_RECEIVED->RECEIVED': '收货完成',
  'PARTIALLY_RECEIVED->CANCELLED': '取消收货',
  'RECEIVED->CLOSED': '关闭通知单'
}

// 状态操作按钮类型映射
export const STATUS_BUTTON_TYPES = {
  'DRAFT->PLANNED': 'primary',
  'DRAFT->CANCELLED': 'danger',
  'PLANNED->ARRIVED': 'success',
  'PLANNED->CANCELLED': 'danger',
  'ARRIVED->RECEIVING': 'warning',
  'ARRIVED->CANCELLED': 'danger',
  'RECEIVING->PARTIALLY_RECEIVED': 'warning',
  'RECEIVING->RECEIVED': 'success',
  'RECEIVING->CANCELLED': 'danger',
  'PARTIALLY_RECEIVED->RECEIVING': 'warning',
  'PARTIALLY_RECEIVED->RECEIVED': 'success',
  'PARTIALLY_RECEIVED->CANCELLED': 'danger',
  'RECEIVED->CLOSED': 'info'
}

// 需要备注的状态转换
export const REQUIRE_REMARK_TRANSITIONS = [
  'CANCELLED',
  'PARTIALLY_RECEIVED'
]

// 业务规则验证
export const validateStatusTransition = (
  from: string, 
  to: string, 
  businessData?: Record<string, any>
): { valid: boolean; message?: string } => {
  // 基础规则验证
  const transition = INBOUND_NOTIFICATION_CONFIG.statusTransitions.find(t => t.from === from)
  if (!transition || !transition.to.includes(to)) {
    return { valid: false, message: '不允许的状态转换' }
  }

  // 业务规则验证
  if (businessData) {
    // 检查明细数据
    if (to === 'PLANNED' && (!businessData['details'] || businessData['details'].length === 0)) {
      return { valid: false, message: '提交计划前必须添加明细数据' }
    }

    // 检查收货数量
    if (to === 'RECEIVED') {
      const hasUncompletedItems = businessData['details']?.some((item: any) => 
        (item.expectedQuantity || 0) > (item.receivedQuantity || 0)
      )
      if (hasUncompletedItems) {
        return { valid: false, message: '存在未完成收货的明细，请先完成收货或选择部分收货状态' }
      }
    }
  }

  return { valid: true }
}

// 添加中文标签映射到配置
;(INBOUND_NOTIFICATION_CONFIG as any).transitionLabels = STATUS_TRANSITION_LABELS;

// 按状态值映射按钮类型（Element Plus type）
export const STATUS_BUTTON_TYPES_BY_STATUS = {
  DRAFT: 'default',
  PLANNED: 'primary',
  ARRIVED: 'success',
  RECEIVING: 'warning',
  PARTIALLY_RECEIVED: 'warning',
  RECEIVED: 'success',
  CLOSED: 'info',
  CANCELLED: 'danger'
} as const

// 按状态值映射按钮图标（Element Plus Icon 名称）
export const STATUS_BUTTON_ICONS = {
  DRAFT: 'Edit',
  PLANNED: 'Promotion',
  ARRIVED: 'CircleCheck',
  RECEIVING: 'Loading',
  PARTIALLY_RECEIVED: 'Warning',
  RECEIVED: 'Check',
  CLOSED: 'Lock',
  CANCELLED: 'CircleClose'
} as const

// 附加到配置
;(INBOUND_NOTIFICATION_CONFIG as any).buttonTypes = STATUS_BUTTON_TYPES_BY_STATUS;
;(INBOUND_NOTIFICATION_CONFIG as any).buttonIcons = STATUS_BUTTON_ICONS; 