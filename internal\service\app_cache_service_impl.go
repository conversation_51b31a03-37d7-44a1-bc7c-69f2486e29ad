package service

import (
	"context"
	"fmt"
	"sync" // 添加 sync 包用于 Mutex
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/mojocn/base64Captcha"
	"github.com/patrickmn/go-cache" // 内存缓存库

	baseCache "backend/pkg/cache" // 别名，避免与 go-cache 冲突
	"backend/pkg/config"
	"backend/pkg/errors"
	"backend/pkg/logger"
)

// CacheService 通用缓存服务接口
type CacheService interface {
	// Set 存储键值对，带过期时间
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	// Get 获取键对应的值 (返回 interface{} 和 bool)
	Get(ctx context.Context, key string) (interface{}, bool, error)
	// Delete 删除键
	Delete(ctx context.Context, key string) error
	// Exists 检查键是否存在
	Exists(ctx context.Context, key string) (bool, error)

	// Increment 原子增加键的值 (返回增加后的值)
	Increment(ctx context.Context, key string) (int64, error)

	// Expire 设置键的过期时间
	Expire(ctx context.Context, key string, expiration time.Duration) error

	// GetCaptchaStore 获取用于验证码的 base64Captcha.Store
	GetCaptchaStore() base64Captcha.Store
}

// CacheServiceImpl 缓存服务实现
type CacheServiceImpl struct {
	*BaseServiceImpl
	redisClient  *redis.Client
	memoryCache  *cache.Cache
	memoryLock   sync.Mutex // 为内存缓存的原子操作添加锁
	appCfg       *config.Configuration
	useRedis     bool                // 标记是否优先使用 Redis
	captchaStore base64Captcha.Store // 缓存创建好的验证码存储
}

// NewCacheService 创建缓存服务实例
func NewCacheService(serviceManager *ServiceManager, redisClient *redis.Client, appCfg *config.Configuration) CacheService {
	log := serviceManager.GetLogger()
	useRedisPref := appCfg.Cache.Type == "redis" // 检查通用缓存配置
	redisAvailable := redisClient != nil
	useRedis := useRedisPref && redisAvailable

	// 初始化内存缓存 (总是初始化，作为后备或独立使用)
	memoryCache := cache.New(time.Duration(appCfg.Cache.DefaultExpiration)*time.Second,
		time.Duration(appCfg.Cache.CleanupInterval)*time.Second)
	log.Info("内存缓存已初始化")

	if useRedisPref && !redisAvailable {
		log.Warn("配置使用 Redis 缓存，但 Redis 连接不可用，将仅使用内存缓存")
	} else if useRedis {
		log.Info("优先使用 Redis 缓存")
	} else {
		log.Info("仅使用内存缓存")
	}

	// --- 初始化用于验证码的 Store ---
	var captchaStore base64Captcha.Store
	// 使用 captcha 配置决定验证码存储，但仍受 redis 是否可用限制
	captchaUseRedis := appCfg.Captcha.Store == "redis"
	if captchaUseRedis && redisAvailable {
		log.Info("验证码存储: Redis")
		captchaStore = baseCache.NewRedisStore(redisClient, time.Duration(appCfg.Captcha.Expire)*time.Minute, appCfg.Captcha.Prefix, log)
	} else {
		if captchaUseRedis && !redisAvailable {
			log.Warn("验证码配置使用 Redis 存储，但 Redis 连接不可用，回退到内存存储")
		}
		log.Info("验证码存储: 内存")
		captchaStore = base64Captcha.NewMemoryStore(base64Captcha.GCLimitNumber, time.Duration(appCfg.Captcha.Expire)*time.Minute)
	}
	// --- 结束验证码 Store 初始化 ---

	impl := &CacheServiceImpl{
		BaseServiceImpl: NewBaseService(serviceManager),
		redisClient:     redisClient,
		memoryCache:     memoryCache,
		memoryLock:      sync.Mutex{}, // 初始化 Mutex
		appCfg:          appCfg,
		useRedis:        useRedis,     // 标记通用缓存是否用 Redis
		captchaStore:    captchaStore, // 存储已创建的验证码 store
	}
	return impl
}

// Set 实现设置缓存
func (s *CacheServiceImpl) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	log := logger.WithContext(ctx)
	if s.useRedis {
		err := s.redisClient.Set(ctx, key, value, expiration).Err()
		if err != nil {
			log.Error("Redis Set 失败", logger.WithError(err), logger.WithField("key", key))
			return errors.NewDataError(errors.CODE_DATA_UPDATE_FAILED, "Redis Set 失败").WithCause(err)
		}
		log.Debug("Redis Set 成功", logger.WithField("key", key))
		return nil
	} else {
		s.memoryCache.Set(key, value, expiration)
		log.Debug("Memory Cache Set 成功", logger.WithField("key", key))
		return nil
	}
}

// Get 实现获取缓存 (返回 interface{}, bool, error)
func (s *CacheServiceImpl) Get(ctx context.Context, key string) (interface{}, bool, error) {
	log := logger.WithContext(ctx)
	if s.useRedis {
		valStr, err := s.redisClient.Get(ctx, key).Result()
		if err == redis.Nil {
			log.Debug("Redis Get: Key 不存在", logger.WithField("key", key))
			return nil, false, nil // Not found, return nil value, false, no error
		} else if err != nil {
			log.Error("Redis Get 失败", logger.WithError(err), logger.WithField("key", key))
			return nil, false, errors.NewDataError(errors.CODE_DATA_QUERY_FAILED, "Redis Get 失败").WithCause(err)
		}
		log.Debug("Redis Get 成功", logger.WithField("key", key))
		return valStr, true, nil // Found, return string value, true, no error
	} else {
		if x, found := s.memoryCache.Get(key); found {
			log.Debug("Memory Cache Get 成功", logger.WithField("key", key))
			return x, true, nil // Found, return original value, true, no error
		}
		log.Debug("Memory Cache Get: Key 不存在", logger.WithField("key", key))
		return nil, false, nil // Not found, return nil value, false, no error
	}
}

// Delete 实现删除缓存
func (s *CacheServiceImpl) Delete(ctx context.Context, key string) error {
	log := logger.WithContext(ctx)
	if s.useRedis {
		err := s.redisClient.Del(ctx, key).Err()
		if err != nil {
			log.Error("Redis Del 失败", logger.WithError(err), logger.WithField("key", key))
			return errors.NewDataError(errors.CODE_DATA_DELETE_FAILED, "Redis Del 失败").WithCause(err)
		}
		log.Debug("Redis Del 成功", logger.WithField("key", key))
		return nil
	} else {
		s.memoryCache.Delete(key)
		log.Debug("Memory Cache Del 成功", logger.WithField("key", key))
		return nil
	}
}

// Exists 实现检查键是否存在
func (s *CacheServiceImpl) Exists(ctx context.Context, key string) (bool, error) {
	log := logger.WithContext(ctx)
	if s.useRedis {
		val, err := s.redisClient.Exists(ctx, key).Result()
		if err != nil {
			log.Error("Redis Exists 失败", logger.WithError(err), logger.WithField("key", key))
			return false, errors.NewDataError(errors.CODE_DATA_QUERY_FAILED, "Redis Exists 失败").WithCause(err)
		}
		exists := val > 0
		log.Debug("Redis Exists 检查完成", logger.WithField("key", key), logger.WithField("exists", exists))
		return exists, nil
	} else {
		_, found := s.memoryCache.Get(key)
		log.Debug("Memory Cache Exists 检查完成", logger.WithField("key", key), logger.WithField("exists", found))
		return found, nil
	}
}

// Increment 实现原子增加
func (s *CacheServiceImpl) Increment(ctx context.Context, key string) (int64, error) {
	log := logger.WithContext(ctx)
	if s.useRedis {
		val, err := s.redisClient.Incr(ctx, key).Result()
		if err != nil {
			log.Error("Redis Incr 失败", logger.WithError(err), logger.WithField("key", key))
			return 0, errors.NewDataError(errors.CODE_DATA_UPDATE_FAILED, "Redis Incr 失败").WithCause(err)
		}
		log.Debug("Redis Incr 成功", logger.WithField("key", key), logger.WithField("newValue", val))
		return val, nil
	} else {
		// 使用 Mutex 保护内存缓存的增量操作
		s.memoryLock.Lock()
		defer s.memoryLock.Unlock()

		currentVal, found := s.memoryCache.Get(key)
		var newVal int64 = 1 // 如果 key 不存在，从 1 开始
		if found {
			if intVal, ok := currentVal.(int64); ok {
				newVal = intVal + 1
			} else if intVal, ok := currentVal.(int); ok { // 兼容 int 类型
				newVal = int64(intVal + 1)
			} else {
				// 如果存储的不是数字类型，无法增加
				log.Error("Memory Cache Incr: 值不是整数类型", logger.WithField("key", key), logger.WithField("type", fmt.Sprintf("%T", currentVal)))
				return 0, errors.NewDataError(errors.CODE_DATA_VALIDATION_FAILED, "缓存值不是整数类型")
			}
		}
		// 使用 cache.DefaultExpiration 或配置的默认过期时间？这里使用默认
		s.memoryCache.Set(key, newVal, cache.DefaultExpiration)
		log.Debug("Memory Cache Incr 成功", logger.WithField("key", key), logger.WithField("newValue", newVal))
		return newVal, nil
	}
}

// Expire 实现设置过期时间
func (s *CacheServiceImpl) Expire(ctx context.Context, key string, expiration time.Duration) error {
	log := logger.WithContext(ctx)
	if s.useRedis {
		ok, err := s.redisClient.Expire(ctx, key, expiration).Result()
		if err != nil {
			log.Error("Redis Expire 失败", logger.WithError(err), logger.WithField("key", key))
			return errors.NewDataError(errors.CODE_DATA_UPDATE_FAILED, "Redis Expire 失败").WithCause(err)
		}
		if !ok {
			// Key 不存在，根据业务需要可能不算错误
			log.Warn("Redis Expire: Key 不存在", logger.WithField("key", key))
			// return errors.NewDataError(errors.CODE_DATA_NOT_FOUND, "Key 不存在无法设置过期时间")
		}
		log.Debug("Redis Expire 设置成功", logger.WithField("key", key), logger.WithField("duration", expiration))
		return nil
	} else {
		// patrickmn/go-cache 在 Set 时设置过期，单独的 Expire 不直接支持
		// 我们可以尝试 Get -> Set 来模拟，但这并非原子操作且可能覆盖值类型
		// 或者简单地认为内存缓存不支持单独设置过期，或此操作无效
		log.Warn("Memory Cache 不支持独立的 Expire 操作", logger.WithField("key", key))
		// 返回 nil 表示操作完成（虽然可能无效果）或返回错误
		// return errors.NewSystemError(errors.CODE_SYSTEM_NOT_IMPLEMENTED, "Memory Cache 不支持 Expire")
		return nil // 暂定为无操作
	}
}

// GetCaptchaStore 返回用于验证码的存储实例
func (s *CacheServiceImpl) GetCaptchaStore() base64Captcha.Store {
	// 直接返回在 New 时根据配置和 Redis 可用性创建好的 store
	if s.captchaStore == nil {
		// 理论上不应该发生，因为 NewCacheService 总是会创建一个
		s.GetLogger().Error("Captcha store 未初始化")
		// 返回一个临时的内存 store 以避免 panic
		return base64Captcha.NewMemoryStore(100, 5*time.Minute)
	}
	return s.captchaStore
}
