# `internal/repository` 技术文档

## 1. 概述

`repository` 包是数据访问层（Data Access Layer, DAL），作为应用与数据库之间的主要抽象屏障。它遵循**仓库模式 (Repository Pattern)**，封装了所有与数据持久化和检索相关的逻辑，特别是针对 **GORM ORM** 的操作。此包旨在将数据访问的具体实现细节（如 SQL 查询、GORM API 调用）与上层业务逻辑（服务层）分离。

**核心设计目标：**

- **抽象化数据访问**：对服务层隐藏 GORM 的直接使用，提供更语义化的数据操作接口。
- **可测试性**：通过接口和具体实现的分离，使得服务层在进行单元测试时，可以方便地模拟（mock）仓库接口，而无需真实的数据库连接。
- **代码复用与一致性**：通过泛型基础仓库 `BaseRepository` 封装通用的 CRUD (Create, Read, Update, Delete) 操作，确保数据访问方式的一致性。
- **统一管理**：利用 `RepositoryManager` 集中管理所有仓库实例的生命周期和数据库事务。
- **查询灵活性**：通过 `QueryCondition` 接口及其实现，支持动态构建复杂的数据库查询条件。

需要注意的是，仓库层本身不直接解析或依赖于 HTTP 请求上下文中的信息（例如当前登录用户 ID、租户 ID 等）。这些信息通常由服务层从请求上下文中提取，并通过方法参数的形式传递给仓库层相应的方法，以便进行数据过滤、权限校验或审计记录等操作。

## 2. 核心概念

### 2.1. `BaseRepository` 和 `BaseRepositoryImpl` (`base_repository.go`)

`BaseRepository[T any, ID any]` 是一个泛型接口，定义了针对任意实体类型 `T`（其主键类型为 `ID`）的通用数据操作方法。`BaseRepositoryImpl[T any, ID any]` 是其使用 GORM 的具体实现。

**主要功能：**

- **CRUD 操作**:
  - `Create(ctx context.Context, entity *T) error`: 创建单个实体。
  - `Delete(ctx context.Context, id ID) error`: 根据主键 ID 删除实体。
  - `Update(ctx context.Context, entity *T) error`: 更新单个实体（通常基于主键）。
  - `FindByID(ctx context.Context, id ID) (*T, error)`: 根据主键 ID 查询单个实体。
  - `FindByIds(ctx context.Context, ids []ID) ([]*T, error)`: 根据主键 ID 列表查询多个实体。
- **条件查询**:
  - `FindByPage(ctx context.Context, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error)`: 执行分页查询，支持动态条件和排序规则。
  - `FindByCondition(ctx context.Context, conditions []QueryCondition, sortInfos []response.SortInfo) ([]*T, error)`: 根据条件列表查询实体列表，支持排序。
  - `FindOneByCondition(ctx context.Context, conditions []QueryCondition) (*T, error)`: 根据条件查询单个实体，若找到多个则通常返回第一个或报错。
  - `Exists(ctx context.Context, conditions []QueryCondition) (bool, error)`: 根据条件判断实体是否存在。
- **数据库连接获取**:
  - `GetDB(ctx context.Context) *gorm.DB`: 获取当前操作上下文关联的 `*gorm.DB` 实例。**重要**：此方法返回的 `*gorm.DB` 实例是创建 `BaseRepositoryImpl` 时注入的 `db`（通过 `r.db.WithContext(ctx)` 与当前上下文关联）。如果此仓库实例是在 `RepositoryManager.Transaction` 的回调函数中通过事务性的 `RepositoryManager` 获取的，那么这个 `db` 本身就是事务性的。仓库实现不再尝试从 `ctx.Value` 中解析事务性 DB。

**辅助功能：**

- `getSortableColumns(entityType reflect.Type) map[string]string`: 通过反射机制解析实体结构体中标注了 `sortable:"true"` 的字段，并缓存 API 字段名到数据库列名的映射。这用于在 `FindByPage` 等方法中安全地应用动态排序。
- `applyConditions(query *gorm.DB, conditions []QueryCondition) *gorm.DB`: 将 `QueryCondition` 列表应用到 GORM 查询构建器上。
- `applySortInfos(db *gorm.DB, sortInfos []response.SortInfo, allowedColumns map[string]string) *gorm.DB`: 将排序信息 (`response.SortInfo`) 应用到 GORM 查询构建器，同时会校验排序字段是否在 `allowedColumns` (通常来自 `getSortableColumns`) 中，防止 SQL 注入。

### 2.2. `RepositoryManager` (`repository_manager.go`)

`RepositoryManager` 扮演着仓库实例的工厂和事务协调者的角色。它负责集中管理项目中所有仓库实例的创建、缓存及数据库事务的边界。

**主要功能：**

- **仓库实例管理**:
  - `NewRepositoryManager(db *gorm.DB, logger logger.Logger) *RepositoryManager`: 创建一个新的仓库管理器实例，需要注入一个全局的 `*gorm.DB` 连接和日志记录器。
  - `GetRepository[R any](rm *RepositoryManager, factory func(db *gorm.DB, logger logger.Logger) R) R`: 泛型方法，用于获取指定类型 `R` 的仓库实例。
    - 采用懒加载（Lazy Loading）机制：首次请求特定类型的仓库时，会调用传入的 `factory` 函数创建实例。
    - 使用 `sync.Map` 对创建的仓库实例进行缓存，确保在单个 `RepositoryManager` 实例的生命周期内，特定类型的仓库是单例的。仓库的反射类型名称作为缓存键。
    - 工厂函数 `factory` 会接收当前 `RepositoryManager` 持有的 `*gorm.DB` 实例和 `logger.Logger` 实例作为参数。
  - 为每个具体的仓库（如 `UserRepository`, `RoleRepository` 等）提供了便捷的 Getter 方法（例如 `GetUserRepository()`, `GetRoleRepository()`）。这些方法内部调用泛型的 `GetRepository`，并传递各自的构造函数作为 `factory`。
- **事务管理**:
  - `Transaction(ctx context.Context, fc func(txRepoMgr *RepositoryManager) error, opts ...*sql.TxOptions) error`: 执行数据库事务。
    - 此方法封装了 GORM 的 `Begin()` 和 `Commit()`/`Rollback()` 逻辑。
    - 在事务开始时，会创建一个新的、**事务性**的 `RepositoryManager` 实例 (`txRepoMgr`)。这个新的管理器会使用从 `db.Begin(opts...)` 获取的事务性 `*gorm.DB` 连接 (`txDB`)，并拥有独立的、空的仓库实例缓存。
    - 用户提供的回调函数 `fc` 会接收这个 `txRepoMgr`。在 `fc` 函数内部通过 `txRepoMgr` 获取的任何仓库实例，其所有操作都将自动使用这个 `txDB`，从而确保它们在同一个数据库事务中执行。
    - 如果 `fc` 函数返回 `nil` 错误，事务将自动提交 (`txDB.Commit()`)。
    - 如果 `fc` 函数返回非 `nil` 错误，事务将自动回滚 (`txDB.Rollback()`)。
    - 支持通过 `opts...*sql.TxOptions` 传递标准库的事务选项（如隔离级别）。
- **数据库连接获取**:
  - `DB() *gorm.DB`: 返回当前 `RepositoryManager` 实例所持有的 `*gorm.DB` 连接。这可能是初始化时传入的全局连接，或者是在事务回调中创建的事务性连接。
- **上下文处理**:
  - `WithContext(ctx context.Context) *RepositoryManager`: 创建一个新的 `RepositoryManager` 实例，该实例共享原始 `RepositoryManager` 的 `*gorm.DB` 和 `logger`，但关联了新的 `context.Context`。仓库实例的缓存 (`repos`) **不会**被复制，新的管理器将拥有自己独立的缓存。这主要用于确保在不同的请求处理上下文中，可以按需创建和使用不同的（或者说新缓存的）仓库实例，尽管它们可能共享相同的底层 DB 连接（非事务情况下）。

### 2.3. `QueryCondition` 接口及实现 (`condition.go`)

为了支持灵活的动态查询构建，定义了 `QueryCondition` 接口和一系列相关的结构体与辅助函数。

- **`QueryCondition` 接口**:
  - `Apply(db *gorm.DB) *gorm.DB`: 核心方法。任何实现了此接口的类型都可以被用来向 GORM 的查询构建器 `*gorm.DB` 中添加具体的查询条件。
- **`ConditionOperator` (枚举类型 `string`)**:
  - 定义了常用的 SQL 操作符，如 `OP_EQ` (`=`), `OP_NE` (`<>`), `OP_GT` (`>`), `OP_LIKE` (`LIKE`), `OP_IN` (`IN`), `OP_BETWEEN` (`BETWEEN`), `OP_IS_NULL` (`IS NULL`) 等。
- **`Condition` 结构体**:
  - 字段: `Field` (数据库列名), `Operator` (上述 `ConditionOperator`), `Value` (条件对应的值)。
  - 实现了 `QueryCondition` 接口。其 `Apply` 方法会根据 `Operator` 将条件转换为 GORM 的 `Where` 或其他条件子句。例如，`OP_LIKE` 会自动在 `Value` 两侧添加 `%`。
  - 提供了一系列便捷的构造函数，如 `NewEqualCondition`, `NewLikeCondition`, `NewInCondition`, `NewBetweenCondition`, `NewDateBetweenCondition` (处理日期区间的开始和结束时间)。
- **`ExprCondition` 结构体**:
  - 字段: `Expr` (原始 SQL 表达式字符串，如 `age > ? AND name LIKE ?`) 和 `Args` (表达式对应的参数列表)。
  - 实现了 `QueryCondition` 接口。其 `Apply` 方法直接使用 `db.Where(Expr, Args...)`。
  - 构造函数: `NewExprCondition(expr string, args ...interface{}) ExprCondition`。

## 3. 具体仓库实现剖析

所有具体的仓库实现（如 `UserRepositoryImpl`, `MenuRepositoryImpl` 等）均遵循以下模式：

1.  **定义接口**：例如 `type UserRepository interface { BaseRepository[entity.User, uint]; SpecificMethod(...) }`。
    - 接口通常嵌入通用的 `BaseRepository[EntityType, PrimaryKeyType]` 接口。
    - 声明该特定实体仓库独有的数据操作方法。
2.  **定义实现结构体**：例如 `type UserRepositoryImpl struct { BaseRepository[entity.User, uint] }`。
    - 对于需要通用 CRUD 功能的仓库，结构体嵌入 `BaseRepository[EntityType, PrimaryKeyType]` **接口**。
    - 对于主要处理关联关系、操作相对特殊的仓库（如 `RoleMenuRepository`），则直接持有 `*gorm.DB` 实例。
    - 实现接口中定义的特定方法。
3.  **提供构造函数**：例如 `func NewUserRepository(db *gorm.DB) UserRepository { return &UserRepositoryImpl{ BaseRepository: NewBaseRepository[entity.User, uint](db) } }`。
    - 构造函数接收 `*gorm.DB` (通常由 `RepositoryManager` 在创建实例时注入)。
    - 对于嵌入了 `BaseRepository` 接口的结构体，推荐使用 `NewBaseRepository[EntityType, PrimaryKeyType](db)` 来初始化该嵌入字段，以保持封装和一致性。
    - 返回接口类型的实例。

以下是各个具体仓库的职责和关键方法摘要：

### 3.1. `AccountBookRepository` (`sys_account_book_repository_impl.go`)

- **管理实体**: `entity.AccountBook` (账套)
- **核心功能**: 除继承 `BaseRepository` 的通用功能外：
  - `FindByCode(ctx context.Context, code string) (*entity.AccountBook, error)`: 根据账套编码查询。
  - `FindByName(ctx context.Context, name string) (*entity.AccountBook, error)`: 根据账套名称查询。

### 3.2. `MenuRepository` (`sys_menu_repository_impl.go`)

- **管理实体**: `entity.Menu` (菜单)
- **核心功能**:
  - `FindByParentID(...)`, `FindByName(...)`, `FindByPermission(...)`: 按特定字段查询。
  - `UpdateStatus(...)`: 更新菜单状态。
  - `CheckNameExists(...)`, `CheckPermissionExists(...)`: 检查唯一性约束。
  - `FindMenuTree(...)`: 查询并构建菜单树结构，支持按状态、类型过滤。
  - `FindMenusByRoleID(...)`, `FindMenusByRoleIDs(...)`, `FindPermissionsByRoleIDs(...)`: 根据角色 ID 查询关联的菜单或权限。
  - `HasChildren(...)`: 检查菜单是否有子菜单。
  - `FindMenusByPage(...)`: 自定义的分页查询，支持多条件过滤。

### 3.3. `RoleMenuRepository` (`sys_role_menu_repository_impl.go`)

- **管理实体**: `entity.RoleMenu` (角色菜单关联表)
- **核心功能**: 维护角色与菜单的多对多关系。
  - `AddMenuToRole(...)`, `RemoveMenuFromRole(...)`: 添加/移除单个角色菜单关联。
  - `GetRoleMenus(...)`, `GetMenuRoles(...)`: 获取一方关联的另一方实体列表。
  - `HasMenu(...)`: 检查角色是否拥有特定菜单。
  - `SetRoleMenus(...)`: 覆盖式设置角色的所有菜单关联。
  - `DeleteRoleMenus(...)`, `DeleteMenuRoles(...)`: 删除指定角色或菜单的所有关联。
  - `FindMenuIDsByRoleIDs(...)`: 批量查询角色关联的菜单 ID。

### 3.4. `RoleRepository` (`sys_role_repository_impl.go`)

- **管理实体**: `entity.Role` (角色)
- **核心功能**:
  - `FindByCode(...)`, `FindByName(...)`: 按特定字段查询。
  - `UpdateStatus(...)`: 更新角色状态。
  - `CheckCodeExists(...)`, `CheckNameExists(...)`: 检查唯一性约束。
  - `FindDefaultRoles(...)`, `FindRolesByDataScope(...)`: 按特定业务逻辑查询角色。
  - `FindRolesByPage(...)`: 自定义分页查询，支持多条件过滤。

### 3.5. `UserAccountBookRepository` (`sys_user_account_book_repository_impl.go`)

- **管理实体**: `entity.UserAccountBook` (用户账套关联表)
- **核心功能**: 维护用户与账套的多对多关系。
  - `Exists(...)`: 检查用户与账套的关联是否存在。
  - `FindByUser(...)`: 获取用户的所有账套 ID。
  - `SetUserAccountBooks(...)`: 覆盖式设置用户的账套关联。
  - `Create(...)`, `DeleteByUserIDAndAccountBookID(...)`: 创建/删除单个关联。
  - `FindUserIDsByAccountBookID(...)`: 分页查询拥有指定账套的用户 ID。
  - `FindUserAccountBooks(...)`, `FindAccountBookUsers(...)`: 获取一方关联的另一方实体列表。

### 3.6. `UserRepository` (`sys_user_repository_impl.go`)

- **管理实体**: `entity.User` (用户)
- **核心功能**:
  - **重写部分 `BaseRepository` 方法以支持预加载 (Preload)**: `FindByID`, `FindByIds`, `FindByPage` 在查询用户时会通过 GORM 的 `Preload("Roles").Preload("AccountBooks")` 预加载用户的角色和账套信息。
  - `FindByUsername(...)`, `FindByEmail(...)`, `FindByMobile(...)`: 按唯一标识查询用户 (均预加载关联)。
  - `UpdatePassword(...)`, `UpdateStatus(...)`, `UpdateLoginInfo(...)`, `UpdateProfile(...)`: 更新用户特定信息。
  - `CheckUsernameExists(...)`, `CheckEmailExists(...)`, `CheckMobileExists(...)`: 检查唯一性约束。
  - `LockSecurity(...)`, `UnlockSecurity(...)`: 执行用户安全锁定/解锁相关数据库操作。

### 3.7. `UserRoleRepository` (`sys_user_role_repository_impl.go`)

- **管理实体**: `entity.UserRole` (用户角色关联表)
- **核心功能**: 维护用户与角色的多对多关系。
  - `AddRoleToUser(...)`, `RemoveRoleFromUser(...)`: 添加/移除单个用户角色关联。
  - `GetUserRoles(...)`, `GetRoleUsers(...)`: 获取一方关联的另一方实体列表。
  - `HasRole(...)`: 检查用户是否拥有特定角色。
  - `SetUserRoles(...)`, `SetRoleUsers(...)`: 覆盖式设置关联。
  - `CountUsersByRole(...)`, `CountRolesByUser(...)`, `CountUsersByRoleIDs(...)`: 统计关联数量。
  - `FindUsersNotInRole(...)`, `FindUsersInRole(...)`: 根据角色分页查询用户（属于/不属于）。
  - `DeleteUserRolesByUserID(...)`, `DeleteRoleUsersByRoleID(...)`: 删除指定用户或角色的所有关联。

### 3.8. `FileMetadataRepository` (`app_file_metadata_repository_impl.go`)

- **管理实体**: `entity.FileMetadata` (文件元数据)
- **核心功能**:
  - `FindByBusiness(ctx context.Context, businessType string, businessID uint, sortInfos []response.SortInfo) ([]*entity.FileMetadata, error)`: 根据业务类型 (`business_type`) 和业务 ID (`business_id`) 查询关联的文件元数据列表。

## 4. 事务管理详解

项目的事务管理严格依赖 `RepositoryManager` 的 `Transaction` 方法。

```go
// 示例：在 Service 层调用
err := s.repoManager.Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
    // 1. 从 txRepoMgr 获取事务性仓库实例
    //    这些实例在创建时会接收一个事务性的 *gorm.DB (txRepoMgr.DB())。
    //    仓库内部的 GetDB/getDB 方法会直接使用这个已是事务性的 DB 实例。
    userRepo := txRepoMgr.GetUserRepository() // UserRepository for this transaction
    orderRepo := txRepoMgr.GetOrderRepository() // OrderRepository for this transaction

    // 2. 执行一系列数据库操作
    newUser := &entity.User{Name: "Tx User"}
    if err := userRepo.Create(ctx, newUser); err != nil {
        // 返回错误将导致事务回滚
        s.logger.Error(ctx, "Failed to create user in transaction", logger.WithError(err))
        return err
    }

    newOrder := &entity.Order{UserID: newUser.ID, Amount: 100}
    if err := orderRepo.Create(ctx, newOrder); err != nil {
        // 返回错误将导致事务回滚
        s.logger.Error(ctx, "Failed to create order in transaction", logger.WithError(err))
        return err
    }

    // 3. 所有操作成功，返回 nil 以提交事务
    return nil
})

if err != nil {
    // 处理事务提交/回滚后可能发生的错误，或 fc 函数返回的原始错误
    s.logger.Error(ctx, "Transaction failed", logger.WithError(err))
    // return err or handle accordingly
}
```

**关键点**：

- **事务隔离**：`txRepoMgr` 使用的数据库连接是事务性的，与外部的全局连接隔离。
- **独立缓存**：`txRepoMgr` 拥有自己独立的仓库实例缓存。这意味着即使在事务外已经获取了某个仓库（如 `repoManager.GetUserRepository()`），在事务内通过 `txRepoMgr.GetUserRepository()` 获取的仍将是一个新的、绑定到事务的实例。
- **自动提交/回滚**：开发者只需关注回调函数 `fc` 的逻辑和错误返回，`Transaction` 方法会自动处理 `COMMIT` 或 `ROLLBACK`。
- **上下文传递**：务必将正确的 `context.Context` (通常是请求上下文) 传递给 `Transaction` 方法以及后续的仓库方法调用，以便于日志追踪、超时控制和可能的上下文值传递。

## 5. 错误处理和日志记录

- **错误封装**：仓库方法在遇到数据库错误或记录未找到等情况时，会返回 `error`。推荐使用 `pkg/errors` (`apperrors`) 包来创建或包装错误，附加错误码（如 `apperrors.CODE_DATA_NOT_FOUND`, `apperrors.CODE_DB_ERROR`）和上下文信息。GORM 的 `gorm.ErrRecordNotFound` 通常会被转换为更具体的业务层错误。
- **日志集成**：所有仓库实现都接收并使用 `logger.Logger` 实例（通过 `BaseRepositoryImpl` 或构造函数注入）。在关键操作（尤其是写操作失败、唯一性约束冲突或非预期错误）时，会记录详细的错误日志，包含操作名称、参数（酌情）、错误详情和堆栈信息。

## 6. 如何添加新的 Repository

当需要为新的数据实体添加数据访问逻辑时，请遵循以下步骤：

1.  **定义实体**：在 `internal/entity` 包中定义好 GORM 实体结构体，例如 `Product`。

2.  **创建 Repository 接口**：
    在 `internal/repository` 目录下创建 `product_repository.go` (或类似文件名)。

    ```go
    package repository

    import (
        "context"
        "backend/internal/entity"
        "backend/pkg/response" // For PageQuery, SortInfo etc.
    )

    type ProductRepository interface {
        BaseRepository[entity.Product, uint] // 嵌入基础仓库接口 (假设主键是 uint)

        // 在此定义 Product 特有的数据访问方法
        FindByNameAndCategory(ctx context.Context, name string, categoryID uint) (*entity.Product, error)
        FindByPriceRange(ctx context.Context, minPrice, maxPrice float64, pageQuery *response.PageQuery) (*response.PageResult, error)
    }
    ```

3.  **实现 Repository 结构体**：
    在同一个文件中，定义实现结构体并实现接口方法。

    ```go
    package repository

    // ... (imports from above) ...
    import (
        "gorm.io/gorm"
        "backend/pkg/logger"
    )

    type productRepositoryImpl struct {
        BaseRepositoryImpl[entity.Product, uint]
    }

    // NewProductRepository 是构造函数
    func NewProductRepository(db *gorm.DB, logger logger.Logger) ProductRepository {
        return &productRepositoryImpl{
            BaseRepositoryImpl: BaseRepositoryImpl[entity.Product, uint]{DB: db, Logger: logger},
        }
    }

    // 实现特定方法
    func (r *productRepositoryImpl) FindByNameAndCategory(ctx context.Context, name string, categoryID uint) (*entity.Product, error) {
        var product entity.Product
        // GetDB(ctx) 确保使用正确的 DB 实例 (可能是事务性的)
        err := r.GetDB(ctx).Where("name = ? AND category_id = ?", name, categoryID).First(&product).Error
        if err != nil {
            if errors.Is(err, gorm.ErrRecordNotFound) {
                return nil, apperrors.NewNotFoundError("product not found with given name and category") // 使用 apperrors
            }
            r.Logger.Error(ctx, "Error finding product by name and category", logger.WithError(err), logger.WithField("name", name), logger.WithField("categoryID", categoryID))
            return nil, apperrors.NewDbError("failed to find product").WithCause(err) // 使用 apperrors
        }
        return &product, nil
    }

    func (r *productRepositoryImpl) FindByPriceRange(ctx context.Context, minPrice, maxPrice float64, pageQuery *response.PageQuery) (*response.PageResult, error) {
        conditions := []QueryCondition{
            NewCondition("price", OP_GTE, minPrice), // 使用 NewCondition 辅助函数
            NewCondition("price", OP_LTE, maxPrice),
        }
        // 利用 BaseRepositoryImpl 的 FindByPage
        return r.FindByPage(ctx, pageQuery, conditions)
    }
    ```

4.  **在 `RepositoryManager` 中注册**：
    打开 `internal/repository/repository_manager.go` 文件。

    - 添加新的 Getter 方法：
      ```go
      // GetProductRepository 获取 ProductRepository
      func (rm *RepositoryManager) GetProductRepository() ProductRepository {
          // 使用泛型 GetRepository 和 NewProductRepository 构造函数
          return GetRepository(rm, NewProductRepository)
      }
      ```

5.  **使用**：
    在服务层，通过 `RepositoryManager` 实例获取并使用新的仓库：
    ```go
    // In some service method:
    // productService := s.repoManager.GetProductRepository()
    // product, err := productService.FindByNameAndCategory(ctx, "Laptop", 1)
    ```

遵循这些步骤可以确保新的仓库与现有架构保持一致，并能正确利用事务管理和依赖注入机制。

## 7. 最佳实践与注意事项

- **保持接口的纯粹性**：Repository 接口应只包含数据访问相关的方法。避免将业务逻辑泄漏到仓库层。
- **正确使用 `GetDB(ctx)`**：在实现自定义查询方法时，务必通过 `r.GetDB(ctx)` 获取 `*gorm.DB` 实例，而不是直接使用注入的 `r.DB` 字段，以确保事务的正确传播。
- **错误处理**：清晰地处理 GORM 返回的错误，特别是 `gorm.ErrRecordNotFound`，将其转换为对上层更友好的错误类型或 `nil`（如果业务允许）。
- **预加载（Preloading）**：对于经常需要一起查询的关联数据，考虑在查询方法中使用 GORM 的 `Preload` 功能以避免 N+1 问题。`UserRepository` 是一个很好的例子。
- **条件构造**：优先使用 `condition.go` 中提供的 `Condition` 结构体和辅助函数来构建查询条件，而不是直接拼接 SQL 字符串，以增强安全性和可读性。
- **日志记录**：在复杂查询或关键写操作的错误路径中添加适当的日志，方便问题排查。
- **避免在 `BaseRepositoryImpl` 中直接修改 `r.DB`**：`BaseRepositoryImpl` 中的 `DB` 字段应被视为在特定 `RepositoryManager` 生命周期内（或事务内）的固定 DB 连接。所有操作应通过 `GetDB(ctx)` 后返回的 `*gorm.DB` 实例进行链式调用。
