package entity

import "time"

// Menu 菜单实体
// 用于存储系统菜单和权限数据，支持多级菜单结构
// 继承自TenantEntity，支持多租户功能
type Menu struct {
	TenantEntity        // 继承租户实体基类，包含ID、创建时间、更新时间等基础字段
	Name         string `gorm:"column:name;size:50;not null" json:"name" sortable:"true"`    // 菜单名称：菜单在系统中的名称标识，不能为空 (允许排序)
	ParentID     *uint  `gorm:"column:parent_id;index" json:"parentId"`                      // 父级菜单ID：上级菜单的ID，NULL表示顶级菜单，建立索引提高查询效率 (Type changed to pointer)
	Path         string `gorm:"column:path;size:200" json:"path"`                            // 路由路径：前端路由的访问路径，如"/system/user"
	Component    string `gorm:"column:component;size:255" json:"component"`                  // 组件路径：前端组件的路径，如"system/user/index"
	Redirect     string `gorm:"column:redirect;size:255" json:"redirect"`                    // 重定向路径：访问该菜单时重定向的目标路径
	Icon         string `gorm:"column:icon;size:100" json:"icon"`                            // 图标：菜单的图标名称或图标URL
	Title        string `gorm:"column:title;size:50" json:"title"`                           // 标题：菜单在界面上显示的名称，可与name不同
	Type         int    `gorm:"column:type;default:1" json:"type" sortable:"true"`           // 菜单类型：1-目录（可包含子菜单的菜单项），2-菜单（可点击的菜单项），3-按钮（页面上的操作按钮） (允许排序)
	Permission   string `gorm:"column:permission;size:1000" json:"permission,omitempty"`     // 权限标识：用于权限控制的标识符，如"system:user:list"
	Status       int    `gorm:"column:status;default:1;index" json:"status" sortable:"true"` // 状态：0-禁用（不可用），1-正常（可用），建立索引提高查询效率 (允许排序)
	Sort         int    `gorm:"column:sort;default:0" json:"sort" sortable:"true"`           // 排序：菜单的显示顺序，数值越小越靠前 (允许排序)
	Hidden       bool   `gorm:"column:hidden;default:false" json:"hidden"`                   // 是否隐藏：true-隐藏（不在菜单中显示），false-显示
	NoCache      bool   `gorm:"column:no_cache;default:false" json:"noCache"`                // 是否缓存：true-不缓存（每次都重新加载），false-缓存（使用缓存提高性能）
	Meta         string `gorm:"column:meta;size:2048" json:"meta"`                           // 元数据：存储菜单的额外配置信息，通常为JSON格式字符串
	AlwaysShow   bool   `gorm:"column:always_show;default:false" json:"alwaysShow"`          // 是否总是显示：true-总是显示（即使只有一个子菜单也显示父菜单），false-自动判断
	Breadcrumb   bool   `gorm:"column:breadcrumb;default:true" json:"breadcrumb"`            // 是否显示面包屑：true-在面包屑中显示，false-在面包屑中隐藏
	ActiveMenu   string `gorm:"column:active_menu;size:255" json:"activeMenu"`               // 高亮菜单：当前路由为子路由时，指定高亮的父级菜单
	Remark       string `gorm:"column:remark;size:500" json:"remark"`                        // 备注：关于菜单的附加说明信息

	// 关联字段
	Children []Menu `gorm:"foreignKey:ParentID" json:"children,omitempty"` // 子菜单：当前菜单的子菜单列表，一对多关系
	// foreignKey:ParentID - 指定子菜单表中的外键为ParentID
	// json:"children,omitempty" - JSON序列化时，如果children为空则忽略此字段

	Parent *Menu `gorm:"foreignKey:ParentID" json:"parent,omitempty"` // 父菜单：当前菜单的父级菜单，多对一关系
	// foreignKey:ParentID - 指定当前表中的外键为ParentID
	// json:"parent,omitempty" - JSON序列化时，如果parent为空则忽略此字段

	Roles []Role `gorm:"many2many:sys_role_menu;foreignKey:ID;joinForeignKey:MenuID;References:ID;joinReferences:RoleID" json:"roles,omitempty"` // 菜单角色：拥有该菜单权限的角色列表，多对多关系
	// many2many:role_menu - 指定中间表名为sys_role_menu
	// foreignKey:ID - 指定Menu表的外键为ID
	// joinForeignKey:MenuID - 指定中间表中关联Menu的外键为MenuID
	// References:ID - 指定Role表的引用键为ID
	// joinReferences:RoleID - 指定中间表中关联Role的引用键为RoleID
	// json:"roles,omitempty" - JSON序列化时，如果roles为空则忽略此字段

	AssignedAt *time.Time `gorm:"-" json:"assignedAt,omitempty"` // 角色分配时间 (仅在特定查询中填充)
}

// TableName 指定菜单表名
// 当前被注释掉，使用GORM默认的表名规则
// 取消注释后可自定义表名为"sys_menu"
func (Menu) TableName() string {
	return "sys_menu"
}
