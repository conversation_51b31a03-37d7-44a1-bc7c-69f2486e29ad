import request from '@/utils/request';

/**
 * 审计日志视图对象
 */
export interface AuditLogVO {
    id: number;
    userId?: number;
    username?: string;
    action: string;
    resourceType?: string;
    resourceId?: string;
    timestamp: string; // Using string to accommodate formatted date-time
    clientIp?: string;
    userAgent?: string;
    requestUri?: string;
    method?: string;
    statusCode?: number;
    durationMs?: number;
    traceId?: string;
    status?: string;
    oldValue?: string; // JSON string
    newValue?: string; // JSON string
    details?: string;
    createdAt: string; // Using string to accommodate formatted date-time
}

/**
 * 审计日志列表响应
 */
export interface AuditLogListResponse {
    list: AuditLogVO[];
    total: number;
}


/**
 * 审计日志查询参数
 */
export interface AuditLogQuery {
    pageNum?: number;
    pageSize?: number;
    sort?: string;
    userId?: number;
    username?: string;
    action?: string;
    resourceType?: string;
    clientIp?: string;
    traceId?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
}


/**
 * 获取审计日志列表
 * @param params 查询参数
 * @returns AuditLogListResponse
 */
export const getAuditLogList = (params: AuditLogQuery): Promise<AuditLogListResponse> => {
    return request({
        url: '/admin/audit',
        method: 'get',
        params
    }) as unknown as Promise<AuditLogListResponse>;
}; 