// Below is the code of d:\lasoft\backend\pkg\database\migration.go
// 数据库迁移，用于管理数据库的迁移。
package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"gorm.io/gorm"
)

// MigrationRecord 迁移记录
type MigrationRecord struct {
	ID        uint      `gorm:"primaryKey;autoIncrement"`
	Version   string    `gorm:"size:191;uniqueIndex"`
	Name      string    `gorm:"size:255"`
	AppliedAt time.Time `gorm:"autoCreateTime"`
}

// TableName 指定表名
func (MigrationRecord) TableName() string {
	return "sys_migration"
}

// MigrationManager 迁移管理器
type MigrationManager struct {
	db            *gorm.DB
	migrationsDir string
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB, migrationsDir string) *MigrationManager {
	return &MigrationManager{
		db:            db,
		migrationsDir: migrationsDir,
	}
}

// Initialize 初始化迁移表
func (mm *MigrationManager) Initialize() error {
	// 创建迁移记录表
	err := mm.db.AutoMigrate(&MigrationRecord{})
	if err != nil {
		return fmt.Errorf("创建迁移记录表失败: %w", err)
	}
	return nil
}

// GetAppliedMigrations 获取已应用的迁移
func (mm *MigrationManager) GetAppliedMigrations() ([]MigrationRecord, error) {
	var records []MigrationRecord
	err := mm.db.Order("id").Find(&records).Error
	if err != nil {
		return nil, fmt.Errorf("获取已应用迁移失败: %w", err)
	}
	return records, nil
}

// GetPendingMigrations 获取待应用的迁移
func (mm *MigrationManager) GetPendingMigrations() ([]string, error) {
	// 获取迁移文件
	files, err := mm.GetMigrationFiles()
	if err != nil {
		return nil, err
	}

	// 获取已应用的迁移
	records, err := mm.GetAppliedMigrations()
	if err != nil {
		return nil, err
	}

	// 找出待应用的迁移
	applied := make(map[string]bool)
	for _, record := range records {
		applied[record.Version] = true
	}

	var pending []string
	for _, file := range files {
		if strings.HasSuffix(file, ".up.sql") {
			version := strings.Split(filepath.Base(file), "_")[0]
			if !applied[version] {
				pending = append(pending, file)
			}
		}
	}

	// 按文件名排序
	sort.Strings(pending)
	return pending, nil
}

// ApplyMigration 应用单个迁移
func (mm *MigrationManager) ApplyMigration(file string) error {
	// 解析版本和名称
	filename := filepath.Base(file)
	parts := strings.Split(filename, "_")
	version := parts[0]
	name := strings.TrimSuffix(strings.Join(parts[1:], "_"), ".up.sql")

	// 读取迁移文件内容
	content, err := os.ReadFile(filepath.Join(mm.migrationsDir, file))
	if err != nil {
		return fmt.Errorf("读取迁移文件失败: %w", err)
	}

	// 开启事务
	tx := mm.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %w", tx.Error)
	}

	// 执行迁移
	err = tx.Exec(string(content)).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("执行迁移失败: %w", err)
	}

	// 记录迁移
	record := MigrationRecord{
		Version:   version,
		Name:      name,
		AppliedAt: time.Now(),
	}
	err = tx.Create(&record).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("记录迁移失败: %w", err)
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	log.Printf("成功应用迁移: %s", file)
	return nil
}

// RevertMigration 回滚单个迁移
func (mm *MigrationManager) RevertMigration(version string) error {
	// 查找对应的迁移记录
	var record MigrationRecord
	err := mm.db.Where("version = ?", version).First(&record).Error
	if err != nil {
		return fmt.Errorf("找不到迁移记录: %w", err)
	}

	// 构建回滚文件名
	downFile := fmt.Sprintf("%s_%s.down.sql", version, record.Name)
	downPath := filepath.Join(mm.migrationsDir, downFile)

	// 检查回滚文件是否存在
	if _, err := os.Stat(downPath); os.IsNotExist(err) {
		return fmt.Errorf("回滚文件不存在: %s", downFile)
	}

	// 读取回滚文件内容
	content, err := os.ReadFile(downPath)
	if err != nil {
		return fmt.Errorf("读取回滚文件失败: %w", err)
	}

	// 开启事务
	tx := mm.db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %w", tx.Error)
	}

	// 执行回滚
	err = tx.Exec(string(content)).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("执行回滚失败: %w", err)
	}

	// 删除迁移记录
	err = tx.Delete(&record).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("删除迁移记录失败: %w", err)
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	log.Printf("成功回滚迁移: %s", downFile)
	return nil
}

// MigrateUp 应用所有待应用的迁移
func (mm *MigrationManager) MigrateUp() error {
	pending, err := mm.GetPendingMigrations()
	if err != nil {
		return err
	}

	if len(pending) == 0 {
		log.Println("没有待应用的迁移")
		return nil
	}

	for _, file := range pending {
		err := mm.ApplyMigration(file)
		if err != nil {
			return err
		}
	}

	log.Printf("成功应用 %d 个迁移", len(pending))
	return nil
}

// MigrateDown 回滚最后一个迁移
func (mm *MigrationManager) MigrateDown() error {
	records, err := mm.GetAppliedMigrations()
	if err != nil {
		return err
	}

	if len(records) == 0 {
		log.Println("没有可回滚的迁移")
		return nil
	}

	// 获取最后一个迁移
	lastMigration := records[len(records)-1]
	return mm.RevertMigration(lastMigration.Version)
}

// GetMigrationFiles 获取迁移文件列表
func (mm *MigrationManager) GetMigrationFiles() ([]string, error) {
	// 检查迁移目录是否存在
	if _, err := os.Stat(mm.migrationsDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("迁移目录不存在: %s", mm.migrationsDir)
	}

	// 读取目录下的所有文件
	entries, err := os.ReadDir(mm.migrationsDir)
	if err != nil {
		return nil, fmt.Errorf("读取迁移目录失败: %w", err)
	}

	// 筛选出SQL迁移文件
	var files []string
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".sql") {
			files = append(files, entry.Name())
		}
	}

	return files, nil
}

/* // Removed/Commented out due to duplicate logic in Connect methods and init.go
// CreateDatabaseIfNotExists 检查数据库是否存在，如果不存在则创建
func CreateDatabaseIfNotExists(dbType, host string, port int, username, password, dbName string) error {
	var dsn string
	var createStmt string

	// 根据数据库类型构建连接字符串和创建数据库语句
	switch dbType {
	case "postgresql":
		// 连接到默认的postgres数据库
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=postgres sslmode=disable",
			host, port, username, password)
		createStmt = fmt.Sprintf("CREATE DATABASE %s;", dbName)
	case "mysql":
		// MySQL连接字符串不指定数据库
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/",
			username, password, host, port)
		createStmt = fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;", dbName)
	default:
		return fmt.Errorf("不支持的数据库类型: %s", dbType)
	}

	// 连接到数据库服务器
	db, err := sql.Open(dbType, dsn)
	if err != nil {
		return fmt.Errorf("连接数据库服务器失败: %w", err)
	}
	defer func(db *sql.DB) {
		err := db.Close()
		if err != nil {

		}
	}(db)

	// 检查连接
	if err = db.Ping(); err != nil {
		return fmt.Errorf("无法连接到数据库服务器: %w", err)
	}

	if dbType == "postgresql" {
		// 检查PostgreSQL数据库是否存在
		var exists bool
		checkStmt := fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname='%s');", dbName)
		err = db.QueryRow(checkStmt).Scan(&exists)
		if err != nil {
			return fmt.Errorf("检查数据库是否存在失败: %w", err)
		}

		if !exists {
			log.Printf("数据库 %s 不存在，正在创建...", dbName)
			_, err = db.Exec(createStmt)
			if err != nil {
				return fmt.Errorf("创建数据库失败: %w", err)
			}
			log.Printf("成功创建数据库: %s", dbName)
		} else {
			log.Printf("数据库 %s 已存在", dbName)
		}
	} else if dbType == "mysql" {
		// 对于MySQL，直接尝试创建数据库，如果存在会自动跳过
		_, err = db.Exec(createStmt)
		if err != nil {
			return fmt.Errorf("创建数据库失败: %w", err)
		}
		log.Printf("确保数据库 %s 存在", dbName)
	}

	return nil
}
*/

/* // Removed/Commented out as initialization logic is now in init.go
// MigrateDatabase 执行数据库迁移
// 参数：
//   - db: 数据库连接
//   - autoMigrate: 是否自动迁移表结构
//   - seedData: 是否添加种子数据
//
// 返回：
//   - error: 迁移过程中的错误
func MigrateDatabase(db *gorm.DB, autoMigrate bool, seedData bool) error {
	getLogger := logger.GetLogger()
	ctx := context.Background()

	getLogger.Info(ctx, "开始数据库迁移...")

	// 获取全局配置
	cfg := config.CONFIG
	if cfg == nil {
		return fmt.Errorf("全局配置未初始化")
	}

	// 获取数据库连接信息以检查数据库是否存在
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 检测当前数据库是否能正常访问
	if err := sqlDB.Ping(); err != nil {
		getLogger.Info(ctx, "无法连接到数据库，尝试创建数据库...")

		// 获取驱动器类型
		dbType := cfg.Database.Type

		switch dbType {
		case "postgresql":
			// 从配置文件中获取PostgreSQL连接信息
			pgConfig := cfg.Database.PostgreSQL

			// 创建数据库
			if err := CreateDatabaseIfNotExists("postgresql", pgConfig.Host, pgConfig.Port,
				pgConfig.Username, pgConfig.Password, pgConfig.Database); err != nil {
				return fmt.Errorf("创建PostgreSQL数据库失败: %w", err)
			}

			// 重新连接到新创建的数据库
			dsn := cfg.GetPostgreSQLDSN() // Assuming GetPostgreSQLDSN exists in config
			newDB, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
			if err != nil {
				return fmt.Errorf("连接到新创建的PostgreSQL数据库失败: %w", err)
			}
			db = newDB

		case "mysql":
			// 从配置文件中获取MySQL连接信息
			mysqlConfig := cfg.Database.MySQL

			// 创建数据库
			if err := CreateDatabaseIfNotExists("mysql", mysqlConfig.Host, mysqlConfig.Port,
				mysqlConfig.Username, mysqlConfig.Password, mysqlConfig.Database); err != nil {
				return fmt.Errorf("创建MySQL数据库失败: %w", err)
			}

			// 重新连接到新创建的数据库
			dsn := cfg.GetMySQLDSN() // Assuming GetMySQLDSN exists in config
			newDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
			if err != nil {
				return fmt.Errorf("连接到新创建的MySQL数据库失败: %w", err)
			}
			db = newDB

		default:
			return fmt.Errorf("不支持的数据库类型: %s", dbType)
		}
	}

	// 自动迁移数据库表结构
	if autoMigrate {
		if err := autoMigrateSchema(db); err != nil {
			return err
		}
	}

	// 添加种子数据
	if seedData {
		if err := seedInitialData(db); err != nil {
			return err
		}
	}

	getLogger.Info(ctx, "数据库迁移完成")
	return nil
}
*/

// autoMigrateSchema and seedInitialData are moved or handled by init.go
/*
func autoMigrateSchema(db *gorm.DB) error { ... }
func seedInitialData(db *gorm.DB) error { ... }
*/
