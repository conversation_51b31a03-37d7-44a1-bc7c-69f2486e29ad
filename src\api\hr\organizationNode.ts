import request from '@/utils/request' // 假设您有这样一个封装好的请求工具

// --- OrganizationNode 相关类型定义 (与后端 Go 结构体对齐) ---

// TODO: 仔细核对并根据后端 hr_organization_node_entity.go 和 hr_organization_node_dto.go 调整以下类型

export interface OrganizationNodeMetaVO { // 原 MenuMetaVO，可能不需要或需要大幅修改
  title: string;
  // icon?: string; // 组织节点通常没有图标
  noCache?: boolean; // 根据实际需要决定是否保留
  hidden?: boolean;
  alwaysShow?: boolean;
  breadcrumb?: boolean;
  activeMenu?: string;
}

// 基础 VO (对应 Go 中的 TenantEntity 内嵌的 BaseEntity)
export interface BaseVO {
  id: number
  createdAt?: string // 后端为 time.Time, 前端通常接收为 string
  updatedAt?: string // 后端为 time.Time, 前端通常接收为 string
  createdBy?: number // 后端为 *uint, 前端可为 number | null
  updatedBy?: number // 后端为 *uint, 前端可为 number | null
}

// 租户 VO (对应 Go 中的 TenantEntity)
export interface TenantVO extends BaseVO {
  tenantId?: number // 后端为 uint, 前端可为 number
}

// 组织节点视图对象 (对应 entity.OrganizationNode)
export interface OrganizationNodeVO extends TenantVO {
  accountBookId: number    // Go: uint
  parentId: number         // Go: uint (0 表示顶层)
  nodeType: string         // Go: string (NodeTypeCompany, NodeTypeDepartment, NodeTypePosition)
  name: string             // Go: string
  code?: string            // Go: string, omitempty
  leaderUserId?: number | null // Go: *uint
  level: number            // Go: int
  orderNum: number         // Go: int
  weight?: number | null       // Go: *int
  isVirtual: boolean       // Go: bool
  standardHeadcount?: number | null // Go: *int
  status: number           // Go: int (1=启用, 0=禁用)
  remarks?: string         // Go: string, omitempty
  companyId: number        // Go: uint
  // parent?: OrganizationNodeVO // 避免循环引用，TreeVO 中处理 children
  // children?: OrganizationNodeTreeVO[] // 在 TreeVO 中定义
}

// 组织节点树形视图对象
export interface OrganizationNodeTreeVO extends OrganizationNodeVO {
  children?: OrganizationNodeTreeVO[]
  hasChildren?: boolean // 前端 UI 可能需要
}

// 组织节点创建或更新DTO (对应 dto.OrganizationNodeCreateOrUpdateDTO)
export interface OrganizationNodeCreateOrUpdateDTO {
  parentId?: number | null     // Go: *uint (前端用 null 表示未设置，0 表示顶级根)
  nodeType: string         // Go: string, validate:required
  name: string             // Go: string, validate:required
  code?: string            // Go: string, omitempty
  leaderUserId?: number | null // Go: *uint
  orderNum?: number | null     // Go: *int (前端表单可能是 number，提交时处理)
  weight?: number | null       // Go: *int
  isVirtual?: boolean | null   // Go: *bool (注意 Go DTO 中是 *bool)
  standardHeadcount?: number | null // Go: *int
  status?: number | null       // Go: *int (0 或 1)
  remarks?: string         // Go: string, omitempty
  // companyId 在创建时通常不直接由前端指定，特别是顶级公司；更新时 companyId 不应被修改
  // accountBookId 和 tenantId 通常由后端或请求拦截器处理
}

// 特化的创建DTO (如果需要区分)
export type OrganizationNodeCreateDTO = OrganizationNodeCreateOrUpdateDTO;

// 特化的更新DTO (如果需要区分, 通常会要求 id)
export interface OrganizationNodeUpdateDTO extends OrganizationNodeCreateOrUpdateDTO {
  // id: number; // ID 通常在URL中传递，不在body中，但前端组装对象时可能包含
}

// 组织节点列表查询参数 (对应 dto.OrganizationNodePageQueryDTO 的查询字段部分)
export interface OrganizationNodeQueryDTO {
    name?: string
    code?: string
    nodeType?: string
    status?: number | null // Go: *int
    parentId?: number | null // Go: *uint
    companyId?: number   // 扩展查询条件
    // 分页和排序参数应由调用 API 的函数额外处理或合并
    // pageNum?: number
    // pageSize?: number
    // sortField?: string
    // sortOrder?: string 
}

// 组织节点树查询参数
export interface OrganizationNodeTreeQueryDTO {
    status?: number | null // Go: *int
    nodeType?: string
    companyId?: number
}

// 更新节点状态 DTO (对应 dto.UpdateNodeStatusDTO)
export interface UpdateNodeStatusDTO {
    status: number // 0 或 1
}

// --- API 函数定义 ---

const ORG_BASE_URL = '/hr/organization' // 修正基础路径

/**
 * 获取组织结构树
 * @param params 查询参数 (status, nodeType, companyId)
 */
export const getOrganizationNodeTree = (params?: OrganizationNodeTreeQueryDTO): Promise<OrganizationNodeTreeVO[]> => {
  return request({
    url: `${ORG_BASE_URL}/tree`, // 直接使用 /tree
    method: 'get',
    params
  })
}

/**
 * 获取组织节点列表 (扁平结构，支持分页和排序)
 * @param params 查询参数 (OrganizationNodeQueryDTO & PageParams & SortParams)
 */
export interface PageParams { pageNum?: number; pageSize?: number; }
export interface SortParams { sortField?: string; sortOrder?: string; } // asc | desc
export const getOrganizationNodeList = (params?: OrganizationNodeQueryDTO & PageParams & SortParams): Promise<any> => { // 后端若按 PageQuery 返回，则 Promise 类型应为 PageResult<OrganizationNodeVO>
    return request({
        url: `${ORG_BASE_URL}/nodes`, // 改为 /nodes, 移除 /list
        method: 'get',
        params
    })
}

/**
 * 根据 ID 获取组织节点详情
 * @param id 节点 ID
 */
export const getOrganizationNodeByID = (id: number): Promise<OrganizationNodeVO> => {
  return request({
    url: `${ORG_BASE_URL}/nodes/${id}`,
    method: 'get'
  })
}

/**
 * 新增组织节点
 * @param data 节点数据 (OrganizationNodeCreateDTO)
 */
export const addOrganizationNode = (data: OrganizationNodeCreateDTO): Promise<OrganizationNodeVO> => { // 返回创建的节点信息
  return request({
    url: `${ORG_BASE_URL}/nodes`,
    method: 'post',
    data
  })
}

/**
 * 更新组织节点
 * @param id 节点 ID
 * @param data 节点数据 (OrganizationNodeUpdateDTO)
 */
export const updateOrganizationNode = (id: number, data: OrganizationNodeUpdateDTO): Promise<OrganizationNodeVO> => { // 返回更新后的节点信息
  return request({
    url: `${ORG_BASE_URL}/nodes/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除组织节点
 * @param id 节点 ID
 */
export const deleteOrganizationNode = (id: number): Promise<null> => {
  return request({
    url: `${ORG_BASE_URL}/nodes/${id}`,
    method: 'delete'
  })
}

/**
 * 更新组织节点状态
 * @param id 节点 ID
 * @param statusInfo 状态数据 ({ status: number })
 */
export const updateOrganizationNodeStatus = (id: number, statusInfo: UpdateNodeStatusDTO): Promise<null> => {
    return request({
        url: `${ORG_BASE_URL}/nodes/${id}/status`,
        method: 'patch', // PUT -> PATCH
        data: statusInfo
    });
};


// --- 以下原 menu 特有的辅助 API，组织结构模块不需要 ---
// /**
//  * 获取当前用户菜单树 (用于侧边栏) - 假设 API 路径
//  */
// export const getUserMenuTree = (): Promise<MenuTreeVO[]> => {
//     return request({
//         url: '/user/menus/tree', // 参考后端 Controller 定义
//         method: 'get'
//     })
// }

// /**
//  * 获取当前用户权限标识 - 假设 API 路径
//  */
// export const getUserPermissions = (): Promise<string[]> => {
//     return request({
//         url: '/user/permissions', // 参考后端 Controller 定义
//         method: 'get'
//     })
// }

// 如果需要分页接口 (hr_organization_node 后端似乎直接用了 /list 配合 PageQuery)
/**
 * 分页获取组织节点列表 (如果后端支持特定分页接口)
 * @param params 查询参数及分页参数 (pageNum, pageSize, name, title, status, nodeType, companyId)
 */
// export const getOrganizationNodePage = (params: OrganizationNodeQueryDTO & { pageNum: number; pageSize: number }): Promise<ResponseData<PageResult<OrganizationNodeVO>>> => {
//   return request({
//     url: `${BASE_URL}/page`, // 假设后端有 /page 接口
//     method: 'get',
//     params
//   })
// } 