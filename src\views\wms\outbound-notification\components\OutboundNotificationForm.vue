<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="isViewMode"
    >
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="通知单号" prop="notificationNo">
              <el-input
                v-model="formData.notificationNo"
                placeholder="系统自动生成"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户" prop="clientId">
              <div class="input-with-button">
                <el-input
                  v-model="formData.clientName"
                  placeholder="请选择客户"
                  readonly
                />
                <el-button
                  @click="handleSelectCustomer"
                  :disabled="isViewMode"
                >
                  选择
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户订单号" prop="clientOrderNo">
              <el-input
                v-model="formData.clientOrderNo"
                placeholder="请输入客户订单号"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="仓库" prop="warehouseId">
              <el-select
                v-model="formData.warehouseId"
                placeholder="请选择仓库"
                style="width: 100%"
                @change="handleWarehouseChange"
              >
                <el-option
                  v-for="warehouse in warehouses"
                  :key="warehouse.id"
                  :label="warehouse.warehouseName"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="要求发货日期" prop="requiredShipDate">
              <el-date-picker
                v-model="formData.requiredShipDate"
                type="date"
                placeholder="请选择发货日期"
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-date-picker>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级" prop="priority">
              <el-select
                v-model="formData.priority"
                placeholder="请选择优先级"
                style="width: 100%"
              >
                <el-option
                  v-for="option in priorityOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 收货信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>收货信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="收货人" prop="consigneeName">
              <el-input
                v-model="formData.consigneeName"
                placeholder="请输入收货人姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收货电话" prop="consigneePhone">
              <el-input
                v-model="formData.consigneePhone"
                placeholder="请输入收货电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="承运商" prop="carrierId">
              <el-select
                v-model="formData.carrierId"
                placeholder="请选择承运商"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="carrier in carriers"
                  :key="carrier.id"
                  :label="carrier.carrierName"
                  :value="carrier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="16">
            <el-form-item label="收货地址" prop="consigneeAddress">
              <el-input
                v-model="formData.consigneeAddress"
                placeholder="请输入收货地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运输方式" prop="shippingMethod">
              <el-select
                v-model="formData.shippingMethod"
                placeholder="请选择运输方式"
                style="width: 100%"
              >
                <el-option
                  v-for="option in shippingMethodOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 明细信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span>明细信息</span>
            <el-button
              type="primary"
              size="small"
              @click="handleAddDetail"
              :disabled="isViewMode"
            >
              添加明细
            </el-button>
          </div>
        </template>
        
        <VNTable
          ref="detailTableRef"
          :data="formData.details"
          :columns="detailColumns"
          :loading="false"
          :show-operations="true"
          :operation-width="120"
          operation-fixed="right"
          row-key="lineNo"
          show-index
          max-height="300"
        >
          <template #column-itemCode="{ row, index }">
            <div class="input-with-button">
              <el-input
                v-model="row.itemCode"
                placeholder="请选择物料"
                readonly
              />
              <el-button
                size="small"
                @click="handleSelectItem(row, index)"
                :disabled="isViewMode"
              >
                选择
              </el-button>
            </div>
          </template>
          
          <template #column-requiredQty="{ row }">
            <el-input-number
              v-model="row.requiredQty"
              :min="1"
              :precision="0"
              style="width: 100%"
              :disabled="isViewMode"
            />
          </template>
          
          <template #column-unitOfMeasure="{ row }">
            <el-select
              v-model="row.unitOfMeasure"
              placeholder="单位"
              style="width: 100%"
              :disabled="isViewMode"
            >
              <el-option
                v-for="option in unitOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
          
          <template #operation="{ row, index }">
            <el-button
              type="danger"
              size="small"
              @click="handleRemoveDetail(index)"
              :disabled="isViewMode"
            >
              删除
            </el-button>
          </template>
        </VNTable>
      </el-card>
      
      <!-- 备注信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>备注信息</span>
        </template>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-card>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ isViewMode ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!isViewMode"
          @click="handleSave"
          :loading="submitting"
        >
          保存
        </el-button>
        <el-button
          v-if="!isViewMode"
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          提交
        </el-button>
      </div>
    </template>
    
    <!-- 客户选择器 -->
    <CustomerSelector
      ref="customerSelectorRef"
      @confirm="handleCustomerConfirm"
      @cancel="handleCustomerCancel"
    />
    
    <!-- SKU选择器 -->
    <SkuSelector
      ref="skuSelectorRef"
      @confirm="handleSkuConfirm"
      @cancel="handleSkuCancel"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, onMounted } from 'vue'
import { ElDialog, ElButton, ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElInputNumber, ElCard, ElRow, ElCol, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import CustomerSelector from './CustomerSelector.vue'
import SkuSelector from '@/components/SkuSelector/index.vue'
import type { TableColumn } from '@/components/VNTable/types'
import type { OutboundNotificationFormData, DetailRowData } from '@/types/wms/outboundNotification'
import type { WmsClientResp, WmsWarehouseResp, WmsCarrierResp, WmsItemResp } from '@/api/wms/outboundNotification'
import { getWarehouseList, getCarrierPage } from '@/api/wms/outboundNotification'
import { useOutboundNotificationStore } from '@/store/wms/outboundNotification'

// 组件接口定义
interface OutboundNotificationFormEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<OutboundNotificationFormEmits>()

// Store
const outboundNotificationStore = useOutboundNotificationStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const mode = ref<'create' | 'edit' | 'view'>('create')
const recordId = ref<number | null>(null)
const currentDetailIndex = ref<number>(-1)

// 基础数据
const warehouses = ref<WmsWarehouseResp[]>([])
const carriers = ref<WmsCarrierResp[]>([])

// 表单数据
const formData = reactive<OutboundNotificationFormData>({
  clientId: null,
  warehouseId: null,
  priority: 3,
  details: [],
  _mode: 'create'
})

// 表单验证规则
const formRules: FormRules = {
  clientId: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  warehouseId: [
    { required: true, message: '请选择仓库', trigger: 'change' }
  ],
  requiredShipDate: [
    { required: true, message: '请选择要求发货日期', trigger: 'change' }
  ],
  consigneeName: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' }
  ],
  consigneePhone: [
    { required: true, message: '请输入收货电话', trigger: 'blur' }
  ],
  consigneeAddress: [
    { required: true, message: '请输入收货地址', trigger: 'blur' }
  ]
}

// 选项数据
const priorityOptions = [
  { label: '最高', value: 1 },
  { label: '高', value: 2 },
  { label: '中', value: 3 },
  { label: '低', value: 4 },
  { label: '最低', value: 5 }
]

const shippingMethodOptions = [
  { label: '快递', value: 'EXPRESS' },
  { label: '物流', value: 'LOGISTICS' },
  { label: '自提', value: 'SELF_PICKUP' },
  { label: '直送', value: 'DIRECT_DELIVERY' }
]

const unitOptions = [
  { label: '件', value: 'PCS' },
  { label: '箱', value: 'BOX' },
  { label: '千克', value: 'KG' },
  { label: '克', value: 'G' },
  { label: '升', value: 'L' },
  { label: '毫升', value: 'ML' }
]

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    create: '新建出库通知单',
    edit: '编辑出库通知单',
    view: '查看出库通知单'
  }
  return titles[mode.value]
})

const isViewMode = computed(() => mode.value === 'view')

// 明细表格列配置
const detailColumns = computed<TableColumn[]>(() => [
  { prop: 'itemCode', label: '物料编码', minWidth: 150, slot: true },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'itemSpec', label: '规格', width: 120 },
  { prop: 'requiredQty', label: '需求数量', width: 120, slot: true },
  { prop: 'unitOfMeasure', label: '单位', width: 100, slot: true },
  { prop: 'requiredBatchNo', label: '指定批次', width: 120 },
  { prop: 'remark', label: '备注', width: 120 }
])

// 表单引用
const formRef = ref<FormInstance>()
const detailTableRef = ref<InstanceType<typeof VNTable>>()
const customerSelectorRef = ref<InstanceType<typeof CustomerSelector>>()
const skuSelectorRef = ref<InstanceType<typeof SkuSelector>>()

// 方法
const loadBaseData = async () => {
  try {
    // 加载仓库列表
    const warehouseResult = await getWarehouseList()
    warehouses.value = warehouseResult
    
    // 加载承运商列表
    const carrierResult = await getCarrierPage({ pageNum: 1, pageSize: 100 })
    carriers.value = carrierResult.list
  } catch (error) {
    ElMessage.error('加载基础数据失败')
    console.error(error)
  }
}

const loadFormData = async (id: number) => {
  try {
    const record = await outboundNotificationStore.fetchDetail(id)
    
    // 填充表单数据
    Object.assign(formData, {
      id: record.id,
      notificationNo: record.notificationNo,
      clientId: record.clientId,
      clientName: record.clientName,
      clientOrderNo: record.clientOrderNo,
      warehouseId: record.warehouseId,
      requiredShipDate: record.requiredShipDate,
      priority: record.priority,
      consigneeName: record.consigneeName,
      consigneePhone: record.consigneePhone,
      consigneeAddress: record.consigneeAddress,
      carrierId: record.carrierId,
      shippingMethod: record.shippingMethod,
      remark: record.remark,
      details: record.details?.map((detail, index) => ({
        ...detail,
        lineNo: index + 1,
        _isNew: false
      })) || []
    })
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const handleOpen = () => {
  loadBaseData()
  
  if (mode.value !== 'create' && recordId.value) {
    loadFormData(recordId.value)
  }
}

const handleClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    clientId: null,
    clientName: '',
    clientOrderNo: '',
    warehouseId: null,
    requiredShipDate: '',
    priority: 3,
    consigneeName: '',
    consigneePhone: '',
    consigneeAddress: '',
    carrierId: null,
    shippingMethod: '',
    remark: '',
    details: []
  })
  formRef.value?.clearValidate()
}

const handleSelectCustomer = () => {
  customerSelectorRef.value?.open()
}

const handleCustomerConfirm = (customer: WmsClientResp) => {
  formData.clientId = customer.id
  formData.clientName = customer.clientName
}

const handleCustomerCancel = () => {
  // 客户选择取消处理
}

const handleWarehouseChange = () => {
  // 仓库变更处理
}

const handleAddDetail = () => {
  const newDetail: DetailRowData = {
    lineNo: formData.details.length + 1,
    itemId: undefined,
    itemCode: '',
    itemName: '',
    itemSpec: '',
    requiredQty: 1,
    unitOfMeasure: 'PCS',
    requiredBatchNo: '',
    remark: '',
    _isNew: true
  }
  formData.details.push(newDetail)
}

const handleRemoveDetail = (index: number) => {
  formData.details.splice(index, 1)
  // 重新编号
  formData.details.forEach((detail, idx) => {
    detail.lineNo = idx + 1
  })
}

const handleSelectItem = (row: DetailRowData, index: number) => {
  currentDetailIndex.value = index
  skuSelectorRef.value?.open()
}

const handleSkuConfirm = (item: WmsItemResp) => {
  if (currentDetailIndex.value >= 0) {
    const detail = formData.details[currentDetailIndex.value]
    detail.itemId = item.id
    detail.itemCode = item.itemCode
    detail.itemName = item.itemName
    detail.itemSpec = item.itemSpec
    detail.unitOfMeasure = item.unitOfMeasure
  }
}

const handleSkuCancel = () => {
  currentDetailIndex.value = -1
}

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
}

const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    
    // 验证明细
    if (formData.details.length === 0) {
      ElMessage.error('请添加至少一条明细')
      return false
    }
    
    for (const detail of formData.details) {
      if (!detail.itemId) {
        ElMessage.error('请选择物料')
        return false
      }
      if (!detail.requiredQty || detail.requiredQty <= 0) {
        ElMessage.error('请输入正确的需求数量')
        return false
      }
    }
    
    return true
  } catch (error) {
    return false
  }
}

const handleSave = async () => {
  if (!(await validateForm())) return
  
  submitting.value = true
  try {
    const submitData = {
      ...formData,
      details: formData.details.map(detail => ({
        lineNo: detail.lineNo,
        itemId: detail.itemId!,
        requiredQty: detail.requiredQty,
        unitOfMeasure: detail.unitOfMeasure,
        requiredBatchNo: detail.requiredBatchNo,
        remark: detail.remark
      }))
    }
    
    if (mode.value === 'create') {
      await outboundNotificationStore.create(submitData)
      ElMessage.success('保存成功')
    } else {
      await outboundNotificationStore.update(recordId.value!, submitData)
      ElMessage.success('更新成功')
    }
    
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleSubmit = async () => {
  await handleSave()
  // 提交后可以自动审核或其他操作
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (openMode: 'create' | 'edit' | 'view', id?: number) => {
  mode.value = openMode
  recordId.value = id || null
  formData._mode = openMode
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.form-section {
  margin-bottom: 16px;
}

.form-section :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-with-button {
  display: flex;
  gap: 8px;
}

.input-with-button .el-input {
  flex: 1;
}

.dialog-footer {
  text-align: right;
}
</style>
