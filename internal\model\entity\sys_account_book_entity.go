package entity

// AccountBook 账套实体
// 代表一个独立的核算或业务主体（公司、分部等）
type AccountBook struct {
	TenantEntity          // 嵌入租户实体 (包含 BaseEntity 和 TenantID)
	Code           string `gorm:"column:code;size:50;not null;comment:账套编码" json:"code" sortable:"true"`        // 账套编码 (允许排序)
	Name           string `gorm:"column:name;size:100;not null;comment:账套名称" json:"name" sortable:"true"`       // 账套名称 (允许排序)
	CompanyName    string `gorm:"column:company_name;size:100;comment:公司名称" json:"companyName,omitempty"`       // 公司名称 (开票用)
	TaxID          string `gorm:"column:tax_id;size:50;comment:税号" json:"taxId,omitempty"`                      // 税号 (开票用)
	CompanyAddress string `gorm:"column:company_address;size:255;comment:公司地址" json:"companyAddress,omitempty"` // 公司地址 (开票用)
	CompanyPhone   string `gorm:"column:company_phone;size:30;comment:公司电话" json:"companyPhone,omitempty"`      // 公司电话 (开票用)
	BankName       string `gorm:"column:bank_name;size:100;comment:开户行名称" json:"bankName,omitempty"`            // 开户行名称 (开票用)
	BankAccount    string `gorm:"column:bank_account;size:50;comment:银行账号" json:"bankAccount,omitempty"`        // 银行账号 (开票用)
	IsGroup        bool   `gorm:"column:is_group;default:false;comment:是否集团账套" json:"isGroup"`                  // 是否集团账套 (用于区分合并报表或特殊流程)
	IsVirtual      bool   `gorm:"column:is_virtual;default:false;comment:是否虚拟账套" json:"isVirtual"`              // 是否虚拟账套 (用于模拟、测试或特殊业务场景)
	Status         int    `gorm:"column:status;default:1;comment:状态" json:"status" sortable:"true"`             // 状态 (允许排序)
	// 反向关联字段 (可选，但推荐)
	Users []User `gorm:"many2many:sys_user_account_book;foreignKey:ID;joinForeignKey:AccountBookID;References:ID;joinReferences:UserID" json:"users,omitempty"` // 关联的用户列表
}

// TableName 指定表名
func (AccountBook) TableName() string {
	return "sys_account_book"
}
