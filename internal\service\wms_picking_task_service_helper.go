package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// generateTaskNo 生成拣货任务号
func (s *wmsPickingTaskServiceImpl) generateTaskNo(ctx context.Context, req *dto.WmsPickingTaskCreateReq) (string, error) {
	// 使用编码生成服务
	codeGenService := s.GetServiceManager().GetCodeGenerationService()
	if codeGenService == nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "编码生成服务不可用")
	}

	// 构建编码生成请求
	generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
		BusinessType: "PICKING_TASK",
		ContextData: map[string]interface{}{
			"notificationId":  req.NotificationID,
			"pickingStrategy": req.PickingStrategy,
		},
	})
	if err != nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "拣货任务号生成失败").WithCause(err)
	}

	return generatedCode.GeneratedCode, nil
}

// generateTaskDetails 生成拣货任务明细
func (s *wmsPickingTaskServiceImpl) generateTaskDetails(
	ctx context.Context,
	txRepoMgr *repository.RepositoryManager,
	task *entity.WmsPickingTask,
	notification *entity.WmsOutboundNotification,
) error {
	// 获取出库通知单的库存分配记录
	allocationRepo := txRepoMgr.GetWmsInventoryAllocationRepository()
	detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()

	// 获取出库通知单明细
	details, err := detailRepo.FindByNotificationID(ctx, notification.ID)
	if err != nil {
		return fmt.Errorf("查询出库明细失败: %w", err)
	}

	var taskDetails []*entity.WmsPickingTaskDetail
	lineNo := 1

	for _, detail := range details {
		// 获取该明细的库存分配记录
		allocations, err := allocationRepo.FindByOutboundDetailID(ctx, detail.ID)
		if err != nil {
			return fmt.Errorf("查询库存分配失败: %w", err)
		}

		// 为每个分配记录创建拣货明细
		for _, allocation := range allocations {
			if allocation.Status != string(entity.AllocationStatusAllocated) {
				continue // 只处理已分配的记录
			}

			taskDetail := &entity.WmsPickingTaskDetail{
				TaskID:          task.ID,
				LineNo:          lineNo,
				AllocationID:    allocation.ID,
				ItemID:          detail.ItemID,
				LocationID:      allocation.InventoryID, // TODO: 从库存记录获取库位ID
				RequiredQty:     allocation.AllocatedQty,
				PickedQty:       0,
				UnitOfMeasure:   detail.UnitOfMeasure,
				PickingSequence: lineNo, // 默认按行号排序
				Status:          string(entity.PickingDetailStatusPending),
			}
			taskDetail.AccountBookID = task.AccountBookID
			taskDetail.CreatedBy = task.CreatedBy

			// TODO: 从库存记录获取批次信息
			// TODO: 根据拣货策略优化拣货序号

			taskDetails = append(taskDetails, taskDetail)
			lineNo++
		}
	}

	if len(taskDetails) == 0 {
		return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "没有可拣货的库存分配记录")
	}

	// 批量创建拣货明细
	taskDetailRepo := txRepoMgr.GetWmsPickingTaskDetailRepository()
	if err := taskDetailRepo.BatchCreate(ctx, taskDetails); err != nil {
		return fmt.Errorf("创建拣货明细失败: %w", err)
	}

	return nil
}

// fillExtendedInfo 填充扩展信息
func (s *wmsPickingTaskServiceImpl) fillExtendedInfo(ctx context.Context, vo *vo.WmsPickingTaskVO) error {
	// TODO: 填充出库通知单号、分配拣货员姓名等扩展信息
	// TODO: 计算统计信息（总物料种类数、总数量、完成率等）
	return nil
}

// fillListExtendedInfo 填充列表扩展信息
func (s *wmsPickingTaskServiceImpl) fillListExtendedInfo(ctx context.Context, vo *vo.WmsPickingTaskListVO) {
	// TODO: 填充出库通知单号、分配拣货员姓名等扩展信息
	// TODO: 计算统计信息（总物料种类数、总数量、完成率等）
}

// AssignTask 分配拣货任务
func (s *wmsPickingTaskServiceImpl) AssignTask(ctx context.Context, req *dto.WmsPickingTaskAssignReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		// 检查任务是否存在
		task, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务失败").WithCause(err)
		}

		// 检查状态是否允许分配
		if task.Status != string(entity.PickingTaskStatusPending) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待分配状态的任务才能分配")
		}

		// TODO: 验证用户是否存在且有拣货权限

		// 分配任务
		if err := repo.AssignTask(ctx, req.ID, req.AssignedUserID); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "分配拣货任务失败").WithCause(err)
		}

		return nil
	})
}

// BatchAssignTask 批量分配拣货任务
func (s *wmsPickingTaskServiceImpl) BatchAssignTask(ctx context.Context, req *dto.WmsPickingTaskBatchAssignReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		// TODO: 验证用户是否存在且有拣货权限

		// 批量分配任务
		if err := repo.BatchAssignTask(ctx, req.IDs, req.AssignedUserID); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "批量分配拣货任务失败").WithCause(err)
		}

		return nil
	})
}

// StartTask 开始拣货任务
func (s *wmsPickingTaskServiceImpl) StartTask(ctx context.Context, req *dto.WmsPickingTaskStartReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		// 检查任务是否存在
		task, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务失败").WithCause(err)
		}

		// 检查状态是否允许开始
		if task.Status != string(entity.PickingTaskStatusAssigned) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已分配状态的任务才能开始")
		}

		// TODO: 验证当前用户是否为分配的拣货员

		// 开始任务
		if err := repo.StartTask(ctx, req.ID); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "开始拣货任务失败").WithCause(err)
		}

		return nil
	})
}

// CompleteTask 完成拣货任务
func (s *wmsPickingTaskServiceImpl) CompleteTask(ctx context.Context, req *dto.WmsPickingTaskCompleteReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		// 检查任务是否存在
		task, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务失败").WithCause(err)
		}

		// 检查状态是否允许完成
		if task.Status != string(entity.PickingTaskStatusInProgress) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有进行中状态的任务才能完成")
		}

		// 检查是否所有明细都已完成
		detailRepo := txRepoMgr.GetWmsPickingTaskDetailRepository()
		uncompletedDetails, err := detailRepo.GetUncompletedDetails(ctx, req.ID)
		if err != nil {
			return fmt.Errorf("检查明细完成状态失败: %w", err)
		}

		if len(uncompletedDetails) > 0 {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "还有未完成的拣货明细")
		}

		// 完成任务
		remark := ""
		if req.Remark != nil {
			remark = *req.Remark
		}

		if err := repo.CompleteTask(ctx, req.ID, remark); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "完成拣货任务失败").WithCause(err)
		}

		// 更新出库通知单状态为已拣货
		notificationRepo := txRepoMgr.GetWmsOutboundNotificationRepository()
		if err := notificationRepo.UpdateStatus(ctx, task.NotificationID, string(entity.OutboundStatusPicked), "拣货任务已完成"); err != nil {
			return fmt.Errorf("更新出库通知单状态失败: %w", err)
		}

		return nil
	})
}

// CancelTask 取消拣货任务
func (s *wmsPickingTaskServiceImpl) CancelTask(ctx context.Context, req *dto.WmsPickingTaskCancelReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPickingTaskRepository()

		// 检查任务是否存在
		task, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "拣货任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询拣货任务失败").WithCause(err)
		}

		// 检查状态是否允许取消
		if task.Status == string(entity.PickingTaskStatusCompleted) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已完成的任务不能取消")
		}
		if task.Status == string(entity.PickingTaskStatusCancelled) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "任务已经是取消状态")
		}

		// 取消任务
		reason := ""
		if req.Reason != nil {
			reason = *req.Reason
		}

		if err := repo.CancelTask(ctx, req.ID, reason); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "取消拣货任务失败").WithCause(err)
		}

		// 如果任务已开始，需要释放相关的库存分配
		if task.Status == string(entity.PickingTaskStatusInProgress) {
			if err := s.releaseTaskAllocations(ctx, txRepoMgr, req.ID); err != nil {
				return fmt.Errorf("释放库存分配失败: %w", err)
			}
		}

		return nil
	})
}

// releaseTaskAllocations 释放任务的库存分配
func (s *wmsPickingTaskServiceImpl) releaseTaskAllocations(ctx context.Context, txRepoMgr *repository.RepositoryManager, taskID uint) error {
	// 获取任务明细
	detailRepo := txRepoMgr.GetWmsPickingTaskDetailRepository()
	details, err := detailRepo.FindByTaskID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("查询任务明细失败: %w", err)
	}

	// 释放每个明细对应的库存分配
	allocationRepo := txRepoMgr.GetWmsInventoryAllocationRepository()
	for _, detail := range details {
		if err := allocationRepo.ReleaseAllocation(ctx, detail.AllocationID, "拣货任务取消"); err != nil {
			return fmt.Errorf("释放库存分配失败: %w", err)
		}
	}

	return nil
}

// GetByTaskNo 根据任务号获取
func (s *wmsPickingTaskServiceImpl) GetByTaskNo(ctx context.Context, taskNo string) (*vo.WmsPickingTaskVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPickingTaskRepository()

	task, err := repo.FindByTaskNo(ctx, taskNo)
	if err != nil {
		return nil, err
	}

	voResult := &vo.WmsPickingTaskVO{}
	copier.Copy(voResult, task)

	// 填充扩展信息
	if err := s.fillExtendedInfo(ctx, voResult); err != nil {
		return nil, err
	}

	return voResult, nil
}

// GetByNotificationID 根据出库通知单ID获取
func (s *wmsPickingTaskServiceImpl) GetByNotificationID(ctx context.Context, notificationID uint) ([]*vo.WmsPickingTaskVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPickingTaskRepository()

	tasks, err := repo.FindByNotificationID(ctx, notificationID)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsPickingTaskVO
	for _, task := range tasks {
		voResult := &vo.WmsPickingTaskVO{}
		copier.Copy(voResult, task)

		// 填充扩展信息
		if err := s.fillExtendedInfo(ctx, voResult); err != nil {
			return nil, err
		}

		results = append(results, voResult)
	}

	return results, nil
}

// GetPendingAssignment 获取待分配的任务
func (s *wmsPickingTaskServiceImpl) GetPendingAssignment(ctx context.Context) ([]*vo.WmsPickingTaskListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPickingTaskRepository()

	tasks, err := repo.GetPendingAssignment(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsPickingTaskListVO
	for _, task := range tasks {
		listVO := &vo.WmsPickingTaskListVO{}
		copier.Copy(listVO, task)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// GetInProgressTasks 获取进行中的任务
func (s *wmsPickingTaskServiceImpl) GetInProgressTasks(ctx context.Context, userID *uint) ([]*vo.WmsPickingTaskListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPickingTaskRepository()

	tasks, err := repo.GetInProgressTasks(ctx, userID)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsPickingTaskListVO
	for _, task := range tasks {
		listVO := &vo.WmsPickingTaskListVO{}
		copier.Copy(listVO, task)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// GetOverdueTasks 获取过期任务
func (s *wmsPickingTaskServiceImpl) GetOverdueTasks(ctx context.Context) ([]*vo.WmsPickingTaskListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPickingTaskRepository()

	tasks, err := repo.GetOverdueTasks(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsPickingTaskListVO
	for _, task := range tasks {
		listVO := &vo.WmsPickingTaskListVO{}
		copier.Copy(listVO, task)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}
