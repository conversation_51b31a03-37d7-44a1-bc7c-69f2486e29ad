package middleware

import (
	"strings"
)

// SliceContains 检查切片是否包含某个字符串
// 参数:
//   - slice: 字符串切片
//   - item: 要检查的字符串
//
// 返回:
//   - bool: 是否包含
func SliceContains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// MatchPath 检查路径是否匹配模式
// 参数:
//   - path: 请求路径
//   - pattern: 匹配模式
//
// 返回:
//   - bool: 是否匹配
func MatchPath(path, pattern string) bool {
	// 精确匹配
	if path == pattern {
		return true
	}

	// 通配符匹配
	if pattern == "*" {
		return true
	}

	// 前缀匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}

	// 后缀匹配
	if strings.HasPrefix(pattern, "*") {
		suffix := strings.TrimPrefix(pattern, "*")
		return strings.HasSuffix(path, suffix)
	}

	// 其他情况，简单的前缀匹配
	return strings.HasPrefix(path, pattern)
}
