package dto

import "backend/pkg/response"

// FiscalPeriodGenerateDTO 定义了生成会计期间所需的数据。
// 它允许财务年的开始不与日历年对齐。
type FiscalPeriodGenerateDTO struct {
	// FiscalYear 是要生成的财务年度的标签, 例如 2024。
	FiscalYear int `json:"fiscalYear" validate:"required,min=2000"`

	// AccountBookID 是会计期间所属的账簿ID。
	AccountBookID uint `json:"accountBookId" validate:"required"`

	// StartYear 是该财务年度在日历上实际开始的年份。
	StartYear int `json:"startYear" validate:"required,min=2000"`

	// StartMonth 是该财务年度在日历上实际开始的月份。
	StartMonth int `json:"startMonth" validate:"required,min=1,max=12"`

	// StartDay 是该财务年度在日历上实际开始的日。
	StartDay int `json:"startDay" validate:"required,min=1,max=31"`
}

// UpdateFiscalPeriodStatusDTO is used to update the status of a fiscal period.
type UpdateFiscalPeriodStatusDTO struct {
	Status string `json:"status" validate:"required,oneof=OPEN CLOSED"`
}

// FiscalPeriodQueryDTO defines the query parameters for listing fiscal periods.
type FiscalPeriodQueryDTO struct {
	FiscalYear    *int   `form:"fiscalYear" json:"fiscalYear,omitempty"`
	AccountBookID uint   `form:"accountBookId" json:"accountBookId,omitempty"`
	Status        string `form:"status" json:"status,omitempty"`

	Pagination response.PageQuery `json:"-"`
}
