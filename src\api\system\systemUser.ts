import request from '@/utils/request'; // 假设你的请求工具路径

// --- 类型定义 (根据后端接口调整) ---

// 用户列表项 (对应后端 UserListVO)
export interface UserListItem {
  id: number;
  username: string;
  nickname: string;
  realName: string;
  gender: number;
  email?: string;
  mobile?: string;
  status: number;
  isAdmin: boolean;
  loginTime?: string; // 或 Date
  createTime: string; // 或 Date
  roles?: { id: number; name: string }[]; // 明确 roles 结构
  employeeId?: number;
  employeeName?: string; // 新增：后端关联查询的员工姓名
  deptName?: string;     // 新增：后端关联查询的部门名称
  // ... 其他 UserVO 中的字段
}

// 用户列表响应
export interface UserListResponse {
  list: UserListItem[];
  total: number;
}

// 用户查询参数
export interface UserQueryParams {
  pageNum: number;
  pageSize: number;
  username?: string;
  nickname?: string;
  status?: number;
  deptId?: number; // 新增：按部门过滤
  sort?: string; // 格式: "field,direction" e.g., "createTime,desc"
}

// 用户创建/更新数据 (UserFormData - DTO)
export interface UserFormData {
  username?: string; // 新增时必需，编辑时通常不允许修改
  password?: string; // 新增时必需，编辑时通常不传
  nickname: string;
  realName?: string;
  avatar?: string; // 上传后通常存 URL
  gender?: number;
  email?: string;
  mobile?: string;
  status?: number;
  isAdmin?: boolean;
  expireTime?: string | null; // 或 Date
  employeeId?: number | null;
  roleIds: number[]; // 关联的角色 ID 数组
  remark?: string;
  deptId?: number; // 如果需要关联部门
  accountBookIds: number[]; // 新增：账套 ID 数组
}

// 单个用户详情 (可能比 UserFormData 多一些关联数据)
export interface UserDetail extends Omit<UserFormData, 'password' | 'accountBookIds'> { // 详情通常不含密码和账套ID列表（单独获取）
    id: number; // 详情肯定有ID
    roles?: { id: number; name: string }[]; // 包含完整的角色对象
    deptName?: string; // 部门名称
    employeeName?: string; // 员工姓名
    createTime?: string;
    // ... 其他需要回显的字段
}

// 角色简要信息
export interface RoleSimpleItem {
  id: number;
  name: string;
}

// 员工简要信息
export interface EmployeeSimpleItem {
  id: number;
  employeeCode: string;
  employeeName: string; // 假设后端返回 name 字段代表员工名
}

// 上传文件响应
export interface UploadResponse {
    url: string; // 文件访问 URL
    // 可能还有其他字段，如 filename, size 等
}

// 后端可能直接返回 ID 数组，或包含在对象中
export type UserAccountBooksResponse = number[] | { accountBookIds: number[] };

// --- API 函数定义 (根据最终确认调整路径) ---

/**
 * 获取用户列表 (分页)
 * @param params 查询参数
 * This function is expected to return a Promise resolving to UserListResponse { list: UserListItem[], total: number }.
 * If it resolves to only UserListItem[] (an array), the pagination 'total' will be missing,
 * potentially due to how `request.get` in `utils/request.ts` handles the response from the backend.
 */
export function getUserList(params: UserQueryParams): Promise<UserListResponse> {
    // 路径: GET /api/v1/sys/users/page

    // 旧代码 1 (直接返回，当函数签名要求 Promise<UserListResponse> 时，如果 request.get 类型为 Promise<AxiosResponse<T>> 则报错):
    // const promise = request.get<UserListResponse>('/sys/users/page', { params });
    // return promise;

    // 旧代码 2 (尝试 .then(res => res.data)，如果 request.get 拦截器已解包则 res.data 为 undefined):
    // const promise = request.get<UserListResponse>('/sys/users/page', { params }).then(axiosResponse => {
    //   return axiosResponse.data;
    // });
    // return promise;

    // 新代码: 假设 request.get 的拦截器已返回业务数据 T (UserListResponse),
    // 但 request.get 的类型签名可能仍是 Promise<AxiosResponse<T>>。
    // 使用类型断言修正类型，以匹配函数签名 Promise<UserListResponse> 并确保运行时正确。
    const promise = request.get<UserListResponse>('/sys/users/page', { params }) as unknown as Promise<UserListResponse>;

    promise.then(data => {
      // 这里的 data 应该直接是 UserListResponse 结构
      console.log('[systemUser.ts getUserList] Data received (UserListResponse):', JSON.stringify(data));
    }).catch(error => {
      console.error('[systemUser.ts getUserList] Error from request.get or type assertion:', error);
    });

    return promise;
}

/**
 * 新增用户
 * @param data 用户数据
 */
export function addUser(data: UserFormData) {
  // 路径: POST /api/v1/sys/users
  return request.post<any>('/sys/users', data);
}

/**
 * 更新用户信息
 * @param id 用户 ID
 * @param data 用户数据
 */
export function updateUser(id: number, data: Partial<UserFormData>) {
  // 路径: PUT /api/v1/sys/users/{id}
  return request.put<any>(`/sys/users/${id}`, data);
}

/**
 * 删除用户
 * @param id 用户 ID
 */
export function deleteUser(id: number) {
  // 路径: DELETE /api/v1/sys/users/{id}
  return request.delete<any>(`/sys/users/${id}`);
}

/**
 * 获取角色简要列表 (用于表单选择)
 * TODO: 后端当前提供的是分页接口 GET /api/v1/sys/roles/page。
 *       为获取所有简要角色，建议后端新增 /sys/roles/simple 接口，
 *       或让 /sys/roles/page 支持如 simple=true 或 pageSize=-1 的参数。
 *       当前调用分页接口可能只返回部分角色。
 */
export function getRoles() {
  // 调用接口，不传递分页参数，期望后端默认返回所有简要角色
  return request.get<{ list: RoleSimpleItem[] }>('/sys/roles/page');
}

/**
 * 重置用户密码 (管理员操作)
 * @param id 用户 ID
 * @param newPassword 新密码
 */
export function resetPassword(id: number, newPassword: string) {
  // 路径更新: PUT /api/v1/sys/users/{id}/admin-reset-password
  // 请求体更新: { newPassword: ... }
  return request.put<any>(`/sys/users/${id}/admin-reset-password`, { newPassword: newPassword });
}

/**
 * 获取员工简要列表 (用于表单选择)
 * TODO: 后端当前提供的是分页接口 GET /api/v1/hr/employees/simple-list。
 *       为获取所有简要员工，建议后端新增 /hr/employees/simple 接口，
 *       或让 /hr/employees 支持如 simple=true 或 pageSize=-1 的参数。
 *       当前调用分页接口可能只返回部分员工。
 */
export function getEmployeeSimpleList() {
  // 调用接口，不传递分页参数，期望后端默认返回所有简要员工
  return request.get<{ list: EmployeeSimpleItem[] }>('/hr/employees/simple-list');
}

/**
 * 批量删除用户
 * TODO: 后端确认是否支持及实现。推荐端点: DELETE /api/v1/sys/users 请求体 { ids: [...] }
 * @param ids 用户 ID 数组
 */
export function batchDeleteUsers(ids: number[]) {
  // 暂无后端实现
  console.log('[systemUser.ts batchDeleteUsers] ids:', ids);
  console.warn('后端批量删除用户接口尚未实现');
  return Promise.reject('批量删除接口未实现'); // 返回一个 rejected Promise
  // return request.delete<any>('/sys/users', { data: { ids } }); // 后端实现后取消注释
}

/**
 * 获取单个用户详情
 * @param id 用户 ID
 */
export function getUserDetail(id: number): Promise<UserDetail | undefined> {
  // 路径: GET /api/v1/sys/users/{id}

  // 旧代码 1 (直接返回):
  // return request.get<UserDetail>(`/sys/users/${id}`);

  // 旧代码 2 (尝试 .then(res => res.data)):
  // return request.get<UserDetail>(`/sys/users/${id}`).then(axiosResponse => {
  //   return axiosResponse.data;
  // });

  // 新代码 (使用类型断言):
  return request.get<UserDetail>(`/sys/users/${id}`) as unknown as Promise<UserDetail | undefined>;
}

/**
 * 获取指定用户的账套 ID 列表
 * @param userId 用户 ID
 */
export function getUserAccountBooks(userId: number) {
    // 路径: GET /api/v1/sys/users/{userId}/account-books
    // 拦截器已处理 .data，直接返回业务数据类型
    return request.get<UserAccountBooksResponse>(`/sys/users/${userId}/account-books`); 
}

// 注意：头像上传的 action URL 应为 /api/v1/user/upload/avatar (由 VNForm 配置)
// 后端需要实现 POST /api/v1/user/upload/avatar 接口

// --- 可以根据需要添加其他 API ---

// 例如：获取单个用户详情 (用于编辑表单回显，如果列表数据不全)
// export function getUserDetail(id: number) {
//   return request.get<UserFormData>(`/system/users/${id}`);
// } 