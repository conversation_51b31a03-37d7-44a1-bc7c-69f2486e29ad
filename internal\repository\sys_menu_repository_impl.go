package repository

import (
	"context"
	stdErrors "errors" // 引入标准库 errors
	"reflect"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors" // 引入并使用别名
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util" // 确保导入 util 包

	"gorm.io/gorm"
)

// MenuRepository 菜单仓库接口
// 负责菜单数据的增删改查操作
type MenuRepository interface {
	// 继承基础仓库接口
	BaseRepository[entity.Menu, uint]

	// FindByParentID 根据父ID查询菜单
	// 参数:
	//   - ctx: 上下文
	//   - parentID: 父菜单ID
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.Menu: 菜单列表
	//   - error: 错误信息
	FindByParentID(ctx context.Context, parentID uint, sortInfos []response.SortInfo) ([]*entity.Menu, error)

	// FindByName 根据菜单名称查询菜单
	// 参数:
	//   - ctx: 上下文
	//   - name: 菜单名称
	// 返回:
	//   - *entity.Menu: 菜单实体
	//   - error: 错误信息
	FindByName(ctx context.Context, name string) (*entity.Menu, error)

	// FindByPermission 根据权限标识查询菜单
	// 参数:
	//   - ctx: 上下文
	//   - permission: 权限标识
	// 返回:
	//   - *entity.Menu: 菜单实体
	//   - error: 错误信息
	FindByPermission(ctx context.Context, permission string) (*entity.Menu, error)

	// UpdateStatus 更新菜单状态
	// 参数:
	//   - ctx: 上下文
	//   - menuID: 菜单ID
	//   - status: 新状态
	// 返回:
	//   - error: 错误信息
	UpdateStatus(ctx context.Context, menuID uint, status int) error

	// CheckNameExists 检查菜单名称是否存在
	// 参数:
	//   - ctx: 上下文
	//   - name: 菜单名称
	//   - parentID: 父菜单ID
	//   - excludeID: 排除的菜单ID（可选，用于更新时检查）
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	CheckNameExists(ctx context.Context, name string, parentID uint, excludeID ...uint) (bool, error)

	// CheckPermissionExists 检查权限标识是否存在
	// 参数:
	//   - ctx: 上下文
	//   - permission: 权限标识
	//   - excludeID: 排除的菜单ID（可选，用于更新时检查）
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	CheckPermissionExists(ctx context.Context, permission string, excludeID ...uint) (bool, error)

	// FindMenuTree 查询菜单树
	// 参数:
	//   - ctx: 上下文
	//   - status: 状态（可选）
	//   - menuType: 菜单类型（可选）
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.Menu: 菜单树
	//   - error: 错误信息
	FindMenuTree(ctx context.Context, status, menuType *int, sortInfos []response.SortInfo) ([]*entity.Menu, error)

	// FindMenusByRoleID 根据角色ID查询菜单
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.Menu: 菜单列表
	//   - error: 错误信息
	FindMenusByRoleID(ctx context.Context, roleID uint, sortInfos []response.SortInfo) ([]*entity.Menu, error)

	// FindMenusByRoleIDs 根据角色ID列表查询菜单
	// 参数:
	//   - ctx: 上下文
	//   - roleIDs: 角色ID列表
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.Menu: 菜单列表
	//   - error: 错误信息
	FindMenusByRoleIDs(ctx context.Context, roleIDs []uint, sortInfos []response.SortInfo) ([]*entity.Menu, error)

	// FindPermissionsByRoleIDs 根据角色ID列表查询权限标识
	// 参数:
	//   - ctx: 上下文
	//   - roleIDs: 角色ID列表
	//   - sortInfos: 排序信息
	// 返回:
	//   - []string: 权限标识列表
	//   - error: 错误信息
	FindPermissionsByRoleIDs(ctx context.Context, roleIDs []uint, sortInfos []response.SortInfo) ([]string, error)

	// HasChildren 检查菜单是否有子菜单
	// 参数:
	//   - ctx: 上下文
	//   - menuID: 菜单ID
	// 返回:
	//   - bool: 是否有子菜单
	//   - error: 错误信息
	HasChildren(ctx context.Context, menuID uint) (bool, error)

	// FindMenusByPage 自定义分页查询菜单
	// 参数:
	//   - ctx: 上下文
	//   - pageQuery: 分页及排序信息
	//   - name: 菜单名称（可选，模糊查询）
	//   - status: 状态（可选）
	//   - menuType: 菜单类型（可选）
	// 返回:
	//   - *response.PageResult: 分页结果
	//   - error: 错误信息
	FindMenusByPage(ctx context.Context, pageQuery *response.PageQuery, name string, status, menuType *int) (*response.PageResult, error)

	// FindAllMenus 获取所有菜单 (用于构建完整树等场景)
	// 参数:
	//   - ctx: 上下文
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.Menu: 菜单列表
	//   - error: 错误信息
	FindAllMenus(ctx context.Context, sortInfos []response.SortInfo) ([]*entity.Menu, error)
}

// MenuRepositoryImpl 菜单仓库实现
type MenuRepositoryImpl struct {
	BaseRepository[entity.Menu, uint] // 嵌入基础仓库接口
}

// NewMenuRepository 创建菜单仓库
// 参数:
//   - db: 数据库连接
//
// 返回:
//   - MenuRepository: 菜单仓库接口
func NewMenuRepository(db *gorm.DB) MenuRepository {
	return &MenuRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.Menu, uint](db), // 使用构造函数初始化接口
	}
}

// FindByParentID 根据父ID查询菜单
func (r *MenuRepositoryImpl) FindByParentID(ctx context.Context, parentID uint, sortInfos []response.SortInfo) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	db := r.GetDB(ctx)
	if parentID == 0 {
		db = db.Where("parent_id IS NULL")
	} else {
		db = db.Where("parent_id = ?", parentID)
	}

	// 获取 entity.Menu 的允许排序列并应用排序
	// MenuRepositoryImpl 明确操作 entity.Menu，因此可以直接获取其类型
	entityType := reflect.TypeOf(entity.Menu{}) // 或者 reflect.TypeOf(new(entity.Menu)).Elem()
	allowedSortColumns := getSortableColumns(entityType)
	db = applySortInfos(db, sortInfos, allowedSortColumns)

	result := db.Find(&menus)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据父ID查询子菜单失败 (ParentID: %d)", parentID)
		// 使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询子菜单失败").WithCause(result.Error)
	}
	return menus, nil
}

// FindByName 根据菜单名称查询菜单
func (r *MenuRepositoryImpl) FindByName(ctx context.Context, name string) (*entity.Menu, error) {
	var menu entity.Menu
	result := r.GetDB(ctx).Where("name = ?", name).First(&menu)
	if result.Error != nil {
		// 使用 stdErrors.Is 和 apperrors
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "菜单不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据菜单名称查询菜单失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询菜单失败").WithCause(result.Error)
	}
	return &menu, nil
}

// FindByPermission 根据权限标识查询菜单
func (r *MenuRepositoryImpl) FindByPermission(ctx context.Context, permission string) (*entity.Menu, error) {
	if permission == "" {
		// 使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_PARAMS_INVALID, "权限标识不能为空")
	}

	var menu entity.Menu
	result := r.GetDB(ctx).Where("permission = ?", permission).First(&menu)
	if result.Error != nil {
		// 使用 stdErrors.Is 和 apperrors
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "菜单不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据权限标识查询菜单失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询菜单失败").WithCause(result.Error)
	}
	return &menu, nil
}

// UpdateStatus 更新菜单状态
func (r *MenuRepositoryImpl) UpdateStatus(ctx context.Context, menuID uint, status int) error {
	db := r.GetDB(ctx)
	log := logger.WithContext(ctx)

	uid64, err := util.GetUserIDFromStdContext(ctx)
	if err != nil {
		log.WithError(err).Warn("MenuRepository.UpdateStatus: 无法从上下文中获取UserID，UpdatedBy将不会被设置")
	}

	updates := map[string]interface{}{
		"status": status,
	}
	if uid64 > 0 {
		updates["updated_by"] = uint(uid64)
		log.Infof("MenuRepository.UpdateStatus: Setting updated_by to %d for menu ID %d", uint(uid64), menuID)
	} else {
		log.Warnf("MenuRepository.UpdateStatus: updated_by will not be set for menu ID %d as UserID could not be retrieved or was invalid (0).", menuID)
	}

	result := db.Model(&entity.Menu{}).Where("id = ?", menuID).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("更新菜单状态失败")
		// 使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新状态失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		// 使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "菜单不存在")
	}
	return nil
}

// CheckNameExists 检查菜单名称是否存在
func (r *MenuRepositoryImpl) CheckNameExists(ctx context.Context, name string, parentID uint, excludeID ...uint) (bool, error) {
	var count int64
	query := r.GetDB(ctx).Model(&entity.Menu{}).Where("name = ? AND parent_id = ?", name, parentID)
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查菜单名称是否存在失败")
		// 使用 apperrors
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查菜单名称失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// CheckPermissionExists 检查权限标识是否存在
func (r *MenuRepositoryImpl) CheckPermissionExists(ctx context.Context, permission string, excludeID ...uint) (bool, error) {
	if permission == "" {
		return false, nil
	}
	var count int64
	query := r.GetDB(ctx).Model(&entity.Menu{}).Where("permission = ?", permission)
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查权限标识是否存在失败")
		// 使用 apperrors
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查权限标识失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// FindMenuTree 查询菜单树 (依赖 FindByCondition)
func (r *MenuRepositoryImpl) FindMenuTree(ctx context.Context, status, menuType *int, sortInfos []response.SortInfo) ([]*entity.Menu, error) {
	var conditions []QueryCondition
	if status != nil {
		conditions = append(conditions, NewEqualCondition("status", *status))
	}
	if menuType != nil {
		conditions = append(conditions, NewEqualCondition("type", *menuType))
	}
	// FindByCondition 已按标准修改
	menus, err := r.FindByCondition(ctx, conditions, sortInfos)
	if err != nil {
		return nil, err
	}
	return buildMenuTree(menus, 0), nil
}

// buildMenuTree 构建菜单树
// 参数:
//   - menus: 菜单列表
//   - parentID: 父菜单ID
//
// 返回:
//   - []*entity.Menu: 菜单树
func buildMenuTree(menus []*entity.Menu, parentID uint) []*entity.Menu {
	var tree []*entity.Menu

	for _, menu := range menus {
		// Correct comparison logic for *uint and uint
		match := false
		if parentID == 0 {
			// Looking for top-level nodes (ParentID is nil)
			match = menu.ParentID == nil
		} else {
			// Looking for children of a specific parent
			// ParentID must not be nil AND its value must match
			match = menu.ParentID != nil && *menu.ParentID == parentID
		}

		if match {
			children := buildMenuTree(menus, menu.ID)
			if len(children) > 0 {
				// Keep the original conversion logic for menu.Children
				menuChildren := make([]entity.Menu, len(children))
				for i, child := range children {
					menuChildren[i] = *child // Assuming menu.Children is still []entity.Menu
				}
				menu.Children = menuChildren
			}
			tree = append(tree, menu)
		}
	}

	return tree
}

// FindMenusByRoleID 根据角色ID查询菜单
func (r *MenuRepositoryImpl) FindMenusByRoleID(ctx context.Context, roleID uint, sortInfos []response.SortInfo) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	db := r.GetDB(ctx).
		Table("sys_menu as m").Unscoped().Where("m.deleted_at IS NULL").
		Select("m.*").
		Joins("JOIN sys_role_menu rm ON rm.menu_id = m.id").
		Where("rm.role_id = ?", roleID)

	// !! 注意: applySortInfos 需要 allowedColumns map
	// !! 暂时移除排序，或考虑如何传递实体信息
	// db = applySortInfos(db, sortInfos, nil) // 传递 nil 或空 map

	result := db.Find(&menus)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("根据角色ID查询菜单失败")
		// 使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询菜单失败").WithCause(result.Error)
	}
	return menus, nil
}

// FindMenusByRoleIDs 根据角色ID列表查询菜单
func (r *MenuRepositoryImpl) FindMenusByRoleIDs(ctx context.Context, roleIDs []uint, sortInfos []response.SortInfo) ([]*entity.Menu, error) {
	if len(roleIDs) == 0 {
		return []*entity.Menu{}, nil
	}
	var menus []*entity.Menu
	db := r.GetDB(ctx).Table("sys_menu as m").Unscoped().Where("m.deleted_at IS NULL").Distinct("m.id").Select("m.*")
	isSuperAdmin := false
	for _, roleID := range roleIDs {
		if roleID == 1 {
			isSuperAdmin = true
			break
		}
	}
	if isSuperAdmin {
		db = db.Where("m.type IN (?) AND m.status = ? AND m.hidden = ?", []int{1, 2}, 1, false)
	} else {
		db = db.Joins("INNER JOIN sys_role_menu rm ON m.id = rm.menu_id").
			Where("rm.role_id IN (?) AND m.type IN (?) AND m.status = ? AND m.hidden = ?", roleIDs, []int{1, 2}, 1, false)
	}

	// !! 注意: applySortInfos 需要 allowedColumns map
	// !! 暂时移除排序，或考虑如何传递实体信息
	// db = applySortInfos(db, sortInfos, nil) // 传递 nil 或空 map

	result := db.Find(&menus)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("根据角色ID查询菜单实体失败")
		// 使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询菜单失败").WithCause(result.Error)
	}
	return menus, nil
}

// FindPermissionsByRoleIDs 根据角色ID列表查询权限标识
func (r *MenuRepositoryImpl) FindPermissionsByRoleIDs(ctx context.Context, roleIDs []uint, sortInfos []response.SortInfo) ([]string, error) {
	if len(roleIDs) == 0 {
		return []string{}, nil
	}
	var permissions []string
	// 使用 Unscoped() 阻止 GORM 自动添加 "sys_menu".deleted_at IS NULL
	// 然后手动添加 "m.deleted_at IS NULL"
	db := r.GetDB(ctx).Table("sys_menu as m").Unscoped().Where("m.deleted_at IS NULL").Distinct().Select("m.permission")

	isSuperAdmin := false
	for _, roleID := range roleIDs {
		if roleID == 1 {
			isSuperAdmin = true
			break
		}
	}
	if isSuperAdmin {
		// m.deleted_at IS NULL 已经在前面添加
		db = db.Where("m.status = ? AND m.permission IS NOT NULL AND m.permission != ''", 1)
	} else {
		db = db.Joins("INNER JOIN sys_role_menu rm ON m.id = rm.menu_id").
			// m.deleted_at IS NULL 已经在前面添加
			Where("rm.role_id IN (?) AND m.status = ? AND m.permission IS NOT NULL AND m.permission != ''", roleIDs, 1)
	}

	// !! 注意: applySortInfos 需要 allowedColumns map
	// !! 排序作用于 m.permission，可能需要特殊处理或验证
	// db = applySortInfos(db, sortInfos, map[string]string{"permission": "m.permission"}) // 假设只允许按 permission 排序

	result := db.Pluck("m.permission", &permissions)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("根据角色ID查询权限标识失败")
		// 使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询权限标识失败").WithCause(result.Error)
	}
	return permissions, nil
}

// HasChildren 检查菜单是否有子菜单
func (r *MenuRepositoryImpl) HasChildren(ctx context.Context, menuID uint) (bool, error) {
	var count int64
	result := r.GetDB(ctx).Model(&entity.Menu{}).Where("parent_id = ?", menuID).Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查菜单是否有子菜单失败")
		// 使用 apperrors
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查子菜单失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// FindMenusByPage 自定义分页查询菜单 (依赖 FindByPage)
func (r *MenuRepositoryImpl) FindMenusByPage(ctx context.Context, pageQuery *response.PageQuery, name string, status, menuType *int) (*response.PageResult, error) {
	var conditions []QueryCondition
	if name != "" {
		conditions = append(conditions, NewLikeCondition("name", name))
	}
	if status != nil {
		conditions = append(conditions, NewEqualCondition("status", *status))
	}
	if menuType != nil {
		conditions = append(conditions, NewEqualCondition("type", *menuType))
	}
	// FindByPage 已按标准修改
	result, err := r.FindByPage(ctx, pageQuery, conditions)
	if err != nil {
		// FindByPage 内部已记录日志和包装错误，这里只需传递
		return nil, err
	}
	return result, nil
}

// FindAllMenus 获取所有菜单 (用于构建完整树等场景)
func (r *MenuRepositoryImpl) FindAllMenus(ctx context.Context, sortInfos []response.SortInfo) ([]*entity.Menu, error) {
	// 此处调用 FindByCondition，它已按标准修改
	return r.FindByCondition(ctx, nil, sortInfos)
}
