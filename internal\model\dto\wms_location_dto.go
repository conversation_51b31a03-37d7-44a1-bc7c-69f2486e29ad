package dto

import (
	"backend/pkg/response"
)

// WmsLocationCreateReq 创建库位请求
type WmsLocationCreateReq struct {
	Code               string   `json:"code" binding:"required,alphanum,max=50"`                                                                               // 库位代码 (必须)
	Name               *string  `json:"name" binding:"omitempty,max=100"`                                                                                      // 库位名称 (可选)
	Type               string   `json:"type" binding:"required,oneof=WAREHOUSE ZONE AREA AISLE RACK LEVEL BIN SLOT FLOOR_SLOT DOCK_DOOR STAGING_AREA QC_AREA"` // 库位类型 (必须)
	ParentID           *uint    `json:"parentId"`                                                                                                              // 父级库位ID (可选, 0 或 null 表示根)
	Status             *string  `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE MAINTENANCE COUNTING"`                                                 // 状态 (可选, 默认 ACTIVE)
	Remark             *string  `json:"remark" binding:"omitempty,max=1024"`                                                                                   // 备注 (可选)
	IsPickable         *bool    `json:"isPickable"`                                                                                                            // 是否可拣货 (可选, 默认 false)
	IsPutaway          *bool    `json:"isPutaway"`                                                                                                             // 是否可上架 (可选, 默认 false)
	IsInventoryTracked *bool    `json:"isInventoryTracked"`                                                                                                    // 是否计算库存 (可选, 默认 true)
	Address            *string  `json:"address" binding:"omitempty,max=255"`
	Contact            *string  `json:"contact" binding:"omitempty,max=50"`
	Phone              *string  `json:"phone" binding:"omitempty,max=30"`
	TemperatureZone    *string  `json:"temperatureZone" binding:"omitempty,max=50"`
	HazardLevel        *string  `json:"hazardLevel" binding:"omitempty,max=50"`
	SecurityLevel      *string  `json:"securityLevel" binding:"omitempty,max=50"`
	WeightClass        *string  `json:"weightClass" binding:"omitempty,max=50"`
	StorageType        *string  `json:"storageType" binding:"omitempty,max=50"`
	RequiresEquipment  *string  `json:"requiresEquipment" binding:"omitempty,max=50"`
	MaxWeightKg        *float64 `json:"maxWeightKg" binding:"omitempty,min=0"`
	MaxVolumeM3        *float64 `json:"maxVolumeM3" binding:"omitempty,min=0"`
	MaxPallets         *int64   `json:"maxPallets" binding:"omitempty,min=0"`
	MaxLengthM         *float64 `json:"maxLengthM" binding:"omitempty,min=0"`
	MaxWidthM          *float64 `json:"maxWidthM" binding:"omitempty,min=0"`
	MaxHeightM         *float64 `json:"maxHeightM" binding:"omitempty,min=0"`
	MaxItemUnits       *int64   `json:"maxItemUnits" binding:"omitempty,min=0"`
}

// WmsLocationUpdateReq 更新库位请求 (所有字段都是可选的)
type WmsLocationUpdateReq struct {
	ID                 uint     `json:"id" binding:"required"` // ID 在更新时是必须的
	Code               *string  `json:"code" binding:"omitempty,alphanum,max=50"`
	Name               *string  `json:"name" binding:"omitempty,max=100"`
	Type               *string  `json:"type" binding:"omitempty,oneof=WAREHOUSE ZONE AREA AISLE RACK LEVEL BIN SLOT FLOOR_SLOT DOCK_DOOR STAGING_AREA QC_AREA"`
	ParentID           *uint    `json:"parentId"` // 0 或 null 表示设置为根
	Status             *string  `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE MAINTENANCE COUNTING"`
	Remark             *string  `json:"remark" binding:"omitempty,max=1024"`
	IsPickable         *bool    `json:"isPickable"`
	IsPutaway          *bool    `json:"isPutaway"`
	IsInventoryTracked *bool    `json:"isInventoryTracked"` // 是否计算库存 (可选, 默认 true)
	Address            *string  `json:"address" binding:"omitempty,max=255"`
	Contact            *string  `json:"contact" binding:"omitempty,max=50"`
	Phone              *string  `json:"phone" binding:"omitempty,max=30"`
	TemperatureZone    *string  `json:"temperatureZone" binding:"omitempty,max=50"`
	HazardLevel        *string  `json:"hazardLevel" binding:"omitempty,max=50"`
	SecurityLevel      *string  `json:"securityLevel" binding:"omitempty,max=50"`
	WeightClass        *string  `json:"weightClass" binding:"omitempty,max=50"`
	StorageType        *string  `json:"storageType" binding:"omitempty,max=50"`
	RequiresEquipment  *string  `json:"requiresEquipment" binding:"omitempty,max=50"`
	MaxWeightKg        *float64 `json:"maxWeightKg" binding:"omitempty,min=0"`
	MaxVolumeM3        *float64 `json:"maxVolumeM3" binding:"omitempty,min=0"`
	MaxPallets         *int64   `json:"maxPallets" binding:"omitempty,min=0"`
	MaxLengthM         *float64 `json:"maxLengthM" binding:"omitempty,min=0"`
	MaxWidthM          *float64 `json:"maxWidthM" binding:"omitempty,min=0"`
	MaxHeightM         *float64 `json:"maxHeightM" binding:"omitempty,min=0"`
	MaxItemUnits       *int64   `json:"maxItemUnits" binding:"omitempty,min=0"`
}

// WmsLocationQueryReq 库位分页查询参数
type WmsLocationQueryReq struct {
	response.PageQuery
	WarehouseID        *uint  `json:"warehouseId" form:"warehouseId"`
	Code               string `json:"code" form:"code"`
	Name               string `json:"name" form:"name"`
	Type               string `json:"type" form:"type"`
	Status             string `json:"status" form:"status"`
	IsPickable         *bool  `json:"isPickable" form:"isPickable"`
	IsPutaway          *bool  `json:"isPutaway" form:"isPutaway"`
	IsInventoryTracked *bool  `json:"isInventoryTracked" form:"isInventoryTracked"`
}

// WmsWarehouseListReq 定义仓库列表查询的简化结构
type WmsWarehouseListReq struct {
	// 目前不需要额外参数，但保留结构以便未来扩展
}
