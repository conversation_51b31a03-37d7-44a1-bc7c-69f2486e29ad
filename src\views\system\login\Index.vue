<template>
  <div class="login-page-wrapper">
    <VNLogin
      :loading="loading"
      :show-captcha="showCaptcha"
      :captcha-image-url="captchaImageUrl"
      :show-alternative-logins="false"
      @login="handleLogin"
      @refresh-captcha="handleCaptchaRefresh"
      @forgot-password="handleForgotPassword"
      @mobile-login-click="handleMobileLogin" 
      @dingtalk-login-click="handleDingTalkLogin"
      system-title="MiSoft 后台管理系统"
      version="1.0.0"
      left-panel-background-image-url="/src/assets/login-bg.jpg" 
    >
      <!-- Pass through alternative login options -->
      <template #alternative-logins>
         <el-tooltip content="微信登录" placement="bottom">
             <el-icon class="alternative-login-icon" size="24"><ChatDotRound /></el-icon>
         </el-tooltip>
         <el-tooltip content="支付宝登录" placement="bottom">
             <el-icon class="alternative-login-icon" size="24"><Platform /></el-icon> 
         </el-tooltip>
         <el-tooltip content="Github登录" placement="bottom">
             <el-icon class="alternative-login-icon" size="24"><Promotion /></el-icon> 
         </el-tooltip>
         <el-tooltip content="飞书登录" placement="bottom">
             <el-icon class="alternative-login-icon" size="24"><Connection /></el-icon> 
         </el-tooltip>
         <el-tooltip content="手机登录" placement="bottom">
             <el-icon class="alternative-login-icon" size="24"><Iphone /></el-icon> 
         </el-tooltip>
         <el-tooltip content="钉钉登录" placement="bottom">
             <el-icon class="alternative-login-icon" size="24"><Comment /></el-icon> 
         </el-tooltip>
      </template>

       <!-- Example: Add custom footer if needed -->
       <!-- 
       <template #footer>
          <span>© {{ new Date().getFullYear() }} Your Company</span>
       </template> 
       -->
    </VNLogin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElTooltip, ElIcon } from 'element-plus';
// Import the UI component
import VNLogin from '@/components/VNLogin/index.vue'; 
import type { LoginFormModel } from '@/components/VNLogin/types';
// Import icons for the slot example
import { ChatDotRound, Platform, Promotion, Connection, Iphone, Comment } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user'; // Import the user store
import { getCaptcha } from '@/api/auth'; // Import getCaptcha API
import type { CaptchaData } from '@/api/auth'; // Import the type if not already

// --- Router --- 
const router = useRouter();
const route = useRoute();
// --- Pinia Store --- 
const userStore = useUserStore(); // Get the store instance

// --- State --- 
const loading = ref(false);
const showCaptcha = ref(true); // Set initial value to true
const captchaImageUrl = ref(''); // Renamed from captchaUrl for clarity
const captchaLoading = ref(false); // Added in previous step
let captchaId = '';

// --- Methods --- 

// Handle login form submission
const handleLogin = async (loginData: LoginFormModel) => {
  if (showCaptcha.value && !captchaId) {
    ElMessage.error('请先获取验证码');
    return;
  }
  // Assuming VNLogin provides the entered captcha code in loginData.captchaCode
  if (showCaptcha.value && !loginData.captchaCode) {
     ElMessage.error('请输入验证码');
     return;
  }

  // Construct the correct payload matching LoginCredentials type
  const credentials = {
    username: loginData.username,
    password: loginData.password,
    captchaId: captchaId,
    captcha: loginData.captchaCode,
    rememberMe: loginData.rememberMe
  };

  loading.value = true;
  console.log('Attempting login via store with:', credentials);

  try {
    await userStore.Login(credentials);
    ElMessage.success('登录成功！正在跳转...');
    const redirect = route.query['redirect'] as string || '/';
    router.push(redirect);

  } catch (error: any) {
    console.error('Store login action failed:', error);
    ElMessage.error(error.message || '登录失败，请检查您的凭据或联系管理员。');
    handleCaptchaRefresh();
  } finally {
    loading.value = false;
  }
};

// Handle captcha refresh request
const handleCaptchaRefresh = async () => {
  if (captchaLoading.value) return;
  console.log('Refreshing captcha...');
  captchaLoading.value = true;
  captchaImageUrl.value = '';

  try {
    const captchaData = await getCaptcha() as unknown as CaptchaData;

    if (captchaData && captchaData.captchaId) {
      captchaId = captchaData.captchaId;
      captchaImageUrl.value = captchaData.imageData;
      console.log('Captcha refreshed, ID:', captchaId);
    } else {
      console.error('Invalid captcha data structure received:', captchaData);
      throw new Error('获取验证码数据结构无效');
    }
  } catch (error: any) {
    console.error('Failed to refresh captcha:', error);
    ElMessage.error(error.message || '无法加载验证码，请稍后重试。');
    captchaId = '';
    captchaImageUrl.value = '';
  } finally {
    captchaLoading.value = false;
  }
};

// Handle forgot password click
const handleForgotPassword = () => {
  console.log('Forgot password clicked');
  ElMessage.info('忘记密码功能暂未实现。');
  // Navigate to password reset page or show modal
};

// --- Alternative Login Handlers (Placeholders) ---
const handleMobileLogin = () => {
    console.log('Mobile login clicked');
    ElMessage.info('手机登录功能暂未实现。');
    // Implement mobile login flow
};

const handleDingTalkLogin = () => {
    console.log('DingTalk login clicked');
    ElMessage.info('钉钉登录功能暂未实现。');
    // Implement DingTalk login flow (OAuth)
};

// --- Lifecycle --- 
// Uncomment and use onMounted
onMounted(() => {
  handleCaptchaRefresh(); // Load captcha when component mounts
});

</script>

<style scoped>
.login-page-wrapper {
  display: flex;
  align-items: center; /* Vertically center VNLogin */
  justify-content: center; /* Horizontally center VNLogin */
  min-height: 100vh; /* Ensure wrapper takes full viewport height */
  width: 100%; /* Ensure wrapper takes full width (needed for centering) */
  /* Optional: Add background for the entire page behind the centered component */
  /* background: linear-gradient(to right, #ece9e6, #ffffff); */ 
}

/* Optional: Limit the width of the VNLogin component itself */
.login-page-wrapper > :deep(.vn-login-container) {
   max-width: 1100px; /* Example max-width for the login component */
   width: 90%; /* Use percentage for responsiveness */
   /* Add height if VNLogin doesn't define its own, e.g., height: 600px; */
   /* Add box-shadow for better visual separation */
   box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
   border-radius: 8px; /* Optional: rounded corners */
   overflow: hidden; /* Ensure content stays within rounded corners */
}

/* Style for icons passed into the slot */
.alternative-login-icon {
    font-size: 24px;
    color: #606266;
    cursor: pointer;
    transition: color 0.2s;
}
.alternative-login-icon:hover {
    color: var(--el-color-primary);
}
</style> 