<template>
  <div class="vnform-example-container">
    <h2>VNForm 示例</h2>

    <!-- Add buttons for interaction -->
    <div style="margin-bottom: 20px; display: flex; flex-wrap: wrap; gap: 10px;">
      <el-button @click="triggerValidation">手动触发表单校验</el-button>
      <el-button @click="resetCurrentForm">重置当前表单</el-button>
      <el-button @click="clearUsernameValidation">清除用户名校验</el-button>
      <el-button @click="scrollToEmail">滚动到邮箱</el-button>
      <el-button @click="toggleLoading">切换加载状态</el-button>
      <el-button @click="toggleDisabled">切换禁用状态 (外部)</el-button>
    </div>

    <!-- NEW: Simplified Controls for Status -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>模拟父组件状态 (仅作用于第一个表单)</span>
        </div>
      </template>
      <div style="display: flex; flex-wrap: wrap; gap: 20px; align-items: center;">
        <el-form-item label="表单状态" style="margin-bottom: 0;">
          <el-select v-model="simulatedFormState" placeholder="选择状态" style="width: 150px;">
            <el-option label="编辑中" value="editing" />
            <el-option label="锁定" value="locked" />
            <el-option label="结案" value="closed" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批状态" style="margin-bottom: 0;">
          <el-select v-model="simulatedApprovalState" placeholder="选择审批状态" style="width: 150px;">
             <el-option label="无" value="" />
             <el-option label="审批中" value="pending" />
             <el-option label="审批完结" value="approved" />
             <el-option label="拒绝" value="rejected" />
          </el-select>
        </el-form-item>
      </div>
    </el-card>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="表头模式 (全功能)" name="headerOnly">
        <VNForm
          ref="vnFormRef1"
          title="用户信息 (全功能展示)"
          :header-fields="form1.fields"
          v-model="form1.data"
          :loading="formLoading"
          :disabled="isForm1Disabled"
          :default-columns="3"
          :status-tags="statusTagsForForm1"
          @submit="handleSubmit"
          @cancel="handleCancel"
          @change="handleChange"
        >
          <!-- Custom Slot for form-item-customSlot -->
          <template #form-item-customRating="{ field, formData }">
            <div style="border: 1px dashed blue; padding: 10px;">
              <p style="color: #303133; margin-bottom: 5px;">这是 <strong>{{ field.label }}</strong> 的自定义插槽内容。</p>
              <p style="color: #606266; margin-top: 0;">当前用户名: {{ formData['username'] }}</p>
              <el-rate v-model="formData[field.field]" />
            </div>
          </template>

          <!-- Custom Actions Slot -->
          <template #actions="{ submit, reset }">
             <el-button type="success" @click="submit">自定义保存</el-button>
             <el-button type="warning" @click="reset">自定义重置</el-button>
             <el-button>其他操作</el-button>
          </template>
        </VNForm>
      </el-tab-pane>

      <el-tab-pane label="表头 + 明细模式" name="headerAndDetails">
         <VNForm
           ref="vnFormRef2"
           title="采购订单 (表头+明细)"
           :header-fields="form2.fields"
           v-model="form2.data"
           :detail-tables="form2.detailsConfig"
           :on-row-add="handleDetailAdd"
           :on-row-delete="handleDetailDelete"
           @submit="handleSubmit"
           @cancel="handleCancel"
           @change="handleChange"
           @edit="handleExampleEdit"
         />
      </el-tab-pane>
    </el-tabs>

    <div v-if="submittedData" class="submitted-data">
      <h3>提交的数据:</h3>
      <pre>{{ JSON.stringify(submittedData, null, 2) }}</pre>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import VNForm from './index.vue';
import type { HeaderField, DetailTableConfig, StatusTagConfig } from './types';
import { ElMessage, ElMessageBox, ElRate, ElCard, ElFormItem, ElSelect, ElOption } from 'element-plus';
import { cloneDeep } from 'lodash-es';

const activeTab = ref('headerOnly');
const submittedData = ref<any>(null);
const vnFormRef1 = ref<InstanceType<typeof VNForm> | null>(null);
const vnFormRef2 = ref<InstanceType<typeof VNForm> | null>(null);
const formLoading = ref(false);
const formDisabled = ref(false);

// --- NEW: Simulate Parent Component State --- 
const simulatedFormState = ref<'editing' | 'locked' | 'closed'>('editing');
const simulatedApprovalState = ref<'' | 'pending' | 'approved' | 'rejected'>('');

// Compute the statusTags array based on simulated state
const statusTagsForForm1 = computed(() => {
  const tags: StatusTagConfig[] = [];

  // Form State Tag Logic (Example)
  if (simulatedFormState.value === 'locked') {
    tags.push({ visible: true, content: '锁定', type: 'danger' });
  } else if (simulatedFormState.value === 'closed') {
    tags.push({ visible: true, content: '结案', type: 'danger' });
  }

  // Approval State Tag Logic (Example)
  if (simulatedApprovalState.value === 'pending') {
    tags.push({ visible: true, content: '审批中', type: 'warning' });
  } else if (simulatedApprovalState.value === 'approved') {
    tags.push({ visible: true, content: '审批完结', type: 'success' });
  } else if (simulatedApprovalState.value === 'rejected') {
    tags.push({ visible: true, content: '拒绝', type: 'danger' });
  }

  return tags;
});

// Compute the disabled state based on external prop and form state
const isForm1Disabled = computed(() => {
  return formDisabled.value || simulatedFormState.value === 'locked' || simulatedFormState.value === 'closed';
});
// --- End of NEW --- 

// 表单1: 仅表头 (展示更多功能)
const form1 = ref({
  fields: [
    // Group 1: 基本信息
    { field: 'username', label: '用户名', placeholder: '请输入用户名', group: '基本信息', rules: [{ required: true, message: '用户名为必填项' }] },
    { field: 'email', label: '邮箱', type: 'text', placeholder: '请输入邮箱', group: '基本信息', rules: [{ type: 'email', message: '请输入正确的邮箱地址' }] },
    { field: 'age', label: '年龄', type: 'number', group: '基本信息', rules: [{ type: 'number', min: 18, message: '年龄必须大于等于18' }] },
    { 
      field: 'gender', 
      label: '性别', 
      type: 'select', 
      group: '基本信息', 
      options: [
        { label: '男', value: 'male' }, 
        { label: '女', value: 'female' }, 
        { label: '保密', value: 'secret', disabled: true }
      ],
      placeholder: '请选择性别'
    },
    { field: 'birthDate', label: '出生日期', type: 'date', group: '基本信息', valueFormat: 'YYYY-MM-DD' },
    { field: 'isActive', label: '激活状态', type: 'checkbox', group: '基本信息' },

    // Group 2: 偏好设置
    { 
      field: 'city', 
      label: '城市', 
      type: 'select', 
      group: '偏好设置', 
      options: [
        { label: '北京', value: 'beijing' }, { label: '上海', value: 'shanghai' },
      ], 
      placeholder: '请选择城市', 
      clearable: true
    },
    { 
      field: 'interests', 
      label: '兴趣爱好', 
      type: 'select', 
      group: '偏好设置', 
      options: [
        { label: '运动', value: 'sports' }, 
        { label: '音乐', value: 'music' }, 
        { label: '旅游', value: 'travel' } 
      ],
      props: {
        multiple: true, 
        clearable: true, 
        collapseTags: true, 
        collapseTagsTooltip: true
      },
      placeholder: '选择兴趣爱好 (可多选)'
    },
    { 
      field: 'receiveEmails', 
      label: '接收邮件', 
      type: 'checkbox', 
      group: '偏好设置',
      props: { trueLabel: 'yes', falseLabel: 'no' }
    },
    // Dynamic visibility example
    { 
      field: 'emailFrequency', 
      label: '邮件频率', 
      type: 'select', 
      group: '偏好设置', 
      visible: (form) => form['receiveEmails'] === 'yes', // Only visible if receiveEmails is 'yes'
      options: [{ label: '每天', value: 'daily' }, { label: '每周', value: 'weekly' }]
    },

    // Group 3: 备注与其他
    { field: 'remarks', label: '备注', type: 'textarea', group: '备注与其他', placeholder: '可选备注信息', rows: 3 },
    // Custom slot example
    { field: 'customRating', label: '自定义评分', type: 'slot', group: '备注与其他' },
    // Upload example (basic)
    { field: 'attachment', label: '附件上传', type: 'upload', group: '备注与其他', action: 'https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15' /* Replace with your actual upload URL */ }

  ] as HeaderField[],
  data: ref({ // Use ref for inner data to make it reactive for visibility checks etc.
    username: '张三',
    email: '<EMAIL>',
    age: 30,
    gender: 'male',
    birthDate: '1994-05-15',
    isActive: true,
    city: 'beijing',
    interests: ['music', 'travel'],
    receiveEmails: 'yes', 
    emailFrequency: 'weekly',
    remarks: '这是一个初始备注。',
    customRating: 4, // Initial value for the custom slot
    attachment: [] // Initial value for upload
  })
});

// 表单2: 表头 + 明细
const form2 = ref({
  title: '采购订单',
  fields: [
    { field: 'orderNo', label: '订单号', disabled: true },
    { field: 'supplier', label: '供应商', placeholder: '输入供应商名称', rules: [{ required: true, message: '供应商不能为空' }] },
    { field: 'orderDate', label: '订单日期', type: 'date' },
  ] as HeaderField[],
  data: ref<{ header: Record<string, any>; details: any[][] }>({ 
    header: {
       orderNo: `PO-${Date.now()}`,
       supplier: '示例供应商',
       orderDate: new Date().toISOString().slice(0, 10)
    },
    details: [] // Initialize as empty array
  }),
  detailsConfig: [
    {
      title: '订单明细',
      tableProps: {
        rowKey: 'itemCode',
        editable: true,
        columns: [
          { prop: 'itemCode', label: '物料编码', width: 180, editable: true, rules: [{ required: true, message: '物料编码不能为空' }] },
          { prop: 'itemName', label: '物料名称', editable: true, rules: [{ required: true, message: '物料名称不能为空' }] },
          { 
            prop: 'quantity', label: '数量', width: 120, editable: true, 
            editComponent: 'input-number', 
            editComponentProps: { min: 0, precision: 0 },
            rules: [{ type: 'number', min: 1, message: '数量必须大于0' }] 
          },
          { 
            prop: 'unitPrice', label: '单价', width: 150, editable: true,
            editComponent: 'input-number', 
            editComponentProps: { min: 0, precision: 2, step: 0.01 },
            rules: [{ type: 'number', message: '单价必须是数字' }] 
          },
        ],
        data: [
          { itemCode: 'ITEM001', itemName: '螺丝刀', quantity: 10, unitPrice: 5.5 },
          { itemCode: 'ITEM002', itemName: '扳手', quantity: 5, unitPrice: 12.0 },
          { itemCode: 'ITEM003', itemName: '', quantity: 0, unitPrice: 'abc' }, // Invalid data for testing validation
        ],
        pagination: false,
      }
    },
    {
      title: '附加费用明细',
      tableProps: {
        rowKey: 'feeType',
        editable: false,
        columns: [
          { prop: 'feeType', label: '费用类型' },
          { prop: 'amount', label: '金额', width: 150 },
          { prop: 'description', label: '描述' },
        ],
        data: [
          { feeType: '运费', amount: 50.0, description: '快递运输' },
          { feeType: '保险费', amount: 20.0, description: '货物保险' },
        ],
        pagination: false,
      }
    }
  ] as DetailTableConfig[]
});

form2.value.data.details = form2.value.detailsConfig.map(dc => cloneDeep(dc.tableProps.data || []));

const handleSubmit = (formData: any) => {
  console.log('接收到表单提交事件:', formData);
  submittedData.value = formData;
  ElMessage.success('表单提交成功 (模拟)!');
};

const handleCancel = () => {
  console.log('表单取消/重置事件触发');
  submittedData.value = null; // Clear submitted data on cancel
};

const handleChange = (field: string, value: any, form: Record<string, any>) => {
  console.log(`字段 '${field}' 改变为:`, value, '当前表单数据:', form);
};

const handleDetailAdd = async (tableIndex: number): Promise<Record<string, any> | null> => {
  console.log(`请求添加新行到明细表 ${tableIndex}`);
  // 检查表单是否被状态锁定 (现在检查 isForm1Disabled)
  if (activeTab.value === 'headerOnly' && isForm1Disabled.value) {
      ElMessage.warning('表单当前状态不允许添加行');
      return null;
  }
  // For form2, we might need a separate disabled check if needed
  // else if (activeTab.value === 'headerAndDetails' && isForm2Disabled.value) { ... }

  if (tableIndex === 0) { // 订单明细 (假设只在 form2 中)
    const newItemCode = `NEWITEM${Date.now().toString().slice(-4)}`;
    try {
        const { value } = await ElMessageBox.prompt('请输入新物料编码', '添加物料', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputValue: newItemCode,
            inputPattern: /.+/,
            inputErrorMessage: '物料编码不能为空'
        });
        ElMessage.success('返回新行数据进行添加');
        return { itemCode: value, itemName: '新物料', quantity: 1, unitPrice: 0 };
    } catch {
        ElMessage.info('取消添加');
        return null;
    }
  } else if (tableIndex === 1) { // 附加费用
    ElMessage.info('此表示例中不允许直接添加行。');
    return null;
  } 
  return null; // Default case
};

const handleDetailDelete = async (tableIndex: number, row: any): Promise<boolean> => {
  console.log(`请求删除明细表 ${tableIndex} 的行:`, row);
  // 检查表单是否被状态锁定 (现在检查 isForm1Disabled)
  if (activeTab.value === 'headerOnly' && isForm1Disabled.value) {
       ElMessage.warning('表单当前状态不允许删除行');
       return false;
  }
  // Similar check for form2 if needed

  try {
    await ElMessageBox.confirm(`确定删除这行 ${JSON.stringify(row)} 吗?`, '确认删除', { type: 'warning' });
    ElMessage.success('确认删除');
    return true; // Indicate successful deletion confirmation
  } catch {
    ElMessage.info('取消删除');
    return false; // Indicate cancellation
  }
};

const handleExampleEdit = (tableIndex: number, row: any) => {
  ElMessage.info(`触发了编辑事件：表格 ${tableIndex}, 行数据: ${JSON.stringify(row)}`);
  // 实际应用中，可以在这里打开一个编辑对话框，并将 row 数据传递给它
  // 例如: openEditDialog(row);
};

const getCurrentFormRef = (): InstanceType<typeof VNForm> | null => {
  return activeTab.value === 'headerOnly' ? vnFormRef1.value : vnFormRef2.value;
}

const triggerValidation = async () => {
  const formRef = getCurrentFormRef();
  if (formRef) {
    console.log(`开始校验表单: ${activeTab.value}`);
    const isValid = await formRef.validateForm();
    if (isValid) {
      ElMessage.success('表单校验通过!');
      console.log('表单校验通过!');
    } else {
      ElMessage.error('表单校验失败! 请检查表单和控制台输出。');
      console.log('表单校验失败!');
    }
  } else {
    console.error('无法获取表单实例进行校验。');
  }
};

const resetCurrentForm = () => {
  const formRef = getCurrentFormRef();
  if (formRef) {
    formRef.resetForm();
    submittedData.value = null; // Also clear display
    ElMessage.info('当前表单已重置。');
    // 如果需要也重置状态选择器:
    // currentFormStatus.value = null;
    // currentApprovalStatus.value = null;
  } else {
    console.error('无法获取表单实例进行重置。');
  }
};

const clearUsernameValidation = () => {
   const formRef = getCurrentFormRef();
   if (formRef && activeTab.value === 'headerOnly') { // Only form1 has username
     formRef.clearValidate('username');
     ElMessage.info('已清除用户名的校验状态。');
   } else {
     ElMessage.warning('当前标签页没有用户名或无法获取表单实例。');
   }
};

const scrollToEmail = () => {
   const formRef = getCurrentFormRef();
   if (formRef && activeTab.value === 'headerOnly') { // Only form1 has email
     formRef.scrollToField('email');
     ElMessage.info('尝试滚动到邮箱字段。');
   } else {
     ElMessage.warning('当前标签页没有邮箱或无法获取表单实例。');
   }
};

const toggleLoading = () => {
  formLoading.value = !formLoading.value;
};

const toggleDisabled = () => {
  formDisabled.value = !formDisabled.value;
  ElMessage.info(`外部禁用状态切换为: ${formDisabled.value}`);
};

</script>

<style scoped>
.vnform-example-container {
  padding: 20px;
  max-width: 950px; /* Increased max-width */
  margin: 20px auto; /* 居中显示 */
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.submitted-data {
  margin-top: 30px;
  padding: 15px;
  background-color: #eef;
  border: 1px solid #dde;
  border-radius: 4px;
}

h3 {
  margin-bottom: 10px;
}

pre {
  white-space: pre-wrap; /* 允许长内容换行 */
  word-wrap: break-word;
  max-height: 300px; /* Limit height */
  overflow-y: auto; /* Add scroll if needed */
  background-color: #fff; /* Add background for readability */
  padding: 10px;
  border: 1px solid #ccc;
}
</style> 