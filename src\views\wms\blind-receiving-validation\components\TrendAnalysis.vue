<template>
    <div class="trend-analysis">
      <!-- 时间范围选择 -->
      <div class="analysis-header">
        <div class="time-range-selector">
          <span class="selector-label">时间范围：</span>
          <el-date-picker
            v-model="selectedDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
          <el-button
            :icon="Refresh"
            @click="refreshData"
            :loading="loading"
          >
            刷新
          </el-button>
        </div>
        
        <div class="quick-selectors">
          <el-button
            v-for="range in quickRanges"
            :key="range.key"
            :type="activeRange === range.key ? 'primary' : 'default'"
            size="small"
            @click="selectQuickRange(range.key)"
          >
            {{ range.label }}
          </el-button>
        </div>
      </div>
  
      <!-- 图表区域 -->
      <div class="charts-container">
        <el-row :gutter="20">
          <!-- 验证次数趋势 -->
          <el-col :span="24">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <span class="card-title">验证次数趋势</span>
                  <div class="chart-controls">
                    <el-radio-group v-model="validationChartType" size="small">
                      <el-radio-button label="daily">按天</el-radio-button>
                      <el-radio-button label="weekly">按周</el-radio-button>
                      <el-radio-button label="monthly">按月</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
              </template>
              
              <VNChart
                type="line"
                :data="validationTrendData"
                :options="validationTrendOptions"
                height="300px"
              />
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 策略分布 -->
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="card-title">验证策略分布</span>
              </template>
              
              <VNChart
                type="pie"
                :data="strategyDistributionData"
                :options="strategyDistributionOptions"
                height="250px"
              />
            </el-card>
          </el-col>
          
          <!-- 结果分布 -->
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="card-title">验证结果分布</span>
              </template>
              
              <VNChart
                type="pie"
                :data="resultDistributionData"
                :options="resultDistributionOptions"
                height="250px"
              />
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 响应时间趋势 -->
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="card-title">响应时间趋势</span>
              </template>
              
              <VNChart
                type="line"
                :data="responseTimeTrendData"
                :options="responseTimeTrendOptions"
                height="250px"
              />
            </el-card>
          </el-col>
          
          <!-- 用户活跃度 -->
          <el-col :span="12">
            <el-card class="chart-card" shadow="never">
              <template #header>
                <span class="card-title">用户验证活跃度</span>
              </template>
              
              <VNChart
                type="bar"
                :data="userActivityData"
                :options="userActivityOptions"
                height="250px"
              />
            </el-card>
          </el-col>
        </el-row>
      </div>
  
      <!-- 详细统计表格 -->
      <div class="statistics-table" style="margin-top: 20px;">
        <el-card shadow="never">
          <template #header>
            <span class="card-title">详细统计数据</span>
          </template>
          
          <el-table :data="statisticsTableData" stripe>
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="totalValidations" label="总验证次数" align="center" />
            <el-table-column prop="successCount" label="成功次数" align="center" />
            <el-table-column prop="failureCount" label="失败次数" align="center" />
            <el-table-column prop="successRate" label="成功率" align="center">
              <template #default="{ row }">
                <el-tag :type="row.successRate >= 90 ? 'success' : row.successRate >= 70 ? 'warning' : 'danger'">
                  {{ row.successRate }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="avgResponseTime" label="平均响应时间(ms)" align="center" />
            <el-table-column prop="strictCount" label="严格模式" align="center" />
            <el-table-column prop="supplementCount" label="补录模式" align="center" />
            <el-table-column prop="fullCount" label="完全模式" align="center" />
          </el-table>
        </el-card>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue'
  import { Refresh } from '@element-plus/icons-vue'
  import VNChart from '@/components/VNChart/index.vue'
  import {
    getBlindReceivingValidationTrend,
    getBlindReceivingValidationStats
  } from '@/api/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    dateRange?: [string, string]
  }
  
  const props = withDefaults(defineProps<Props>(), {
    dateRange: () => ['', '']
  })
  
  // 响应式数据
  const loading = ref(false)
  const selectedDateRange = ref<[string, string]>(props.dateRange)
  const activeRange = ref('30days')
  const validationChartType = ref('daily')
  
  // 快速时间范围选择
  const quickRanges = [
    { key: '7days', label: '近7天' },
    { key: '30days', label: '近30天' },
    { key: '90days', label: '近90天' },
    { key: 'thisMonth', label: '本月' },
    { key: 'lastMonth', label: '上月' },
    { key: 'thisQuarter', label: '本季度' }
  ]
  
  // 图表数据
  const validationTrendData = ref([])
  const strategyDistributionData = ref([
    { name: '严格模式', value: 0 },
    { name: '补录模式', value: 0 },
    { name: '完全模式', value: 0 }
  ])
  const resultDistributionData = ref([
    { name: '允许', value: 0 },
    { name: '禁止', value: 0 }
  ])
  const responseTimeTrendData = ref([])
  const userActivityData = ref([])
  const statisticsTableData = ref([])
  
  // 图表配置
  const validationTrendOptions = computed(() => ({
    title: {
      text: '验证次数趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['总验证次数', '成功次数', '失败次数'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: validationTrendData.value.map((item: any) => item.date)
    },
    yAxis: {
      type: 'value',
      name: '次数'
    },
    series: [
      {
        name: '总验证次数',
        type: 'line',
        data: validationTrendData.value.map((item: any) => item.total),
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '成功次数',
        type: 'line',
        data: validationTrendData.value.map((item: any) => item.success),
        smooth: true,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '失败次数',
        type: 'line',
        data: validationTrendData.value.map((item: any) => item.failure),
        smooth: true,
        itemStyle: { color: '#f56c6c' }
      }
    ]
  }))
  
  const strategyDistributionOptions = computed(() => ({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '策略分布',
        type: 'pie',
        radius: '50%',
        data: strategyDistributionData.value,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }))
  
  const resultDistributionOptions = computed(() => ({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '验证结果',
        type: 'pie',
        radius: '50%',
        data: resultDistributionData.value,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }))
  
  const responseTimeTrendOptions = computed(() => ({
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: responseTimeTrendData.value.map((item: any) => item.date)
    },
    yAxis: {
      type: 'value',
      name: '响应时间(ms)'
    },
    series: [
      {
        name: '平均响应时间',
        type: 'line',
        data: responseTimeTrendData.value.map((item: any) => item.avgTime),
        smooth: true,
        itemStyle: { color: '#e6a23c' }
      }
    ]
  }))
  
  const userActivityOptions = computed(() => ({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: userActivityData.value.map((item: any) => item.userName)
    },
    yAxis: {
      type: 'value',
      name: '验证次数'
    },
    series: [
      {
        name: '验证次数',
        type: 'bar',
        data: userActivityData.value.map((item: any) => item.count),
        itemStyle: { color: '#909399' }
      }
    ]
  }))
  
  // 工具函数
  const generateDateRange = (rangeKey: string): [string, string] => {
    const now = new Date()
    let start: Date
    let end = new Date(now)
    
    switch (rangeKey) {
      case '7days':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30days':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90days':
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'thisMonth':
        start = new Date(now.getFullYear(), now.getMonth(), 1)
        break
      case 'lastMonth':
        start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        end = new Date(now.getFullYear(), now.getMonth(), 0)
        break
      case 'thisQuarter':
        const quarter = Math.floor(now.getMonth() / 3)
        start = new Date(now.getFullYear(), quarter * 3, 1)
        break
      default:
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }
    
    return [
      start.toISOString().split('T')[0],
      end.toISOString().split('T')[0]
    ]
  }
  
  // 数据加载
  const loadTrendData = async () => {
    try {
      loading.value = true
      
      const params = {
        dateFrom: selectedDateRange.value[0],
        dateTo: selectedDateRange.value[1],
        groupBy: validationChartType.value
      }
      
      const result = await getBlindReceivingValidationTrend(params)
      
      validationTrendData.value = result.trendData
      strategyDistributionData.value = result.strategyDistribution
      resultDistributionData.value = result.resultDistribution
      responseTimeTrendData.value = result.responseTimeTrend
      userActivityData.value = result.userActivity.slice(0, 10) // 取前10个活跃用户
      statisticsTableData.value = result.dailyStatistics
      
    } catch (error) {
      console.error('加载趋势数据失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 事件处理
  const handleDateRangeChange = () => {
    activeRange.value = 'custom'
    loadTrendData()
  }
  
  const selectQuickRange = (rangeKey: string) => {
    activeRange.value = rangeKey
    selectedDateRange.value = generateDateRange(rangeKey)
    loadTrendData()
  }
  
  const refreshData = () => {
    loadTrendData()
  }
  
  // 监听图表类型变化
  watch(validationChartType, () => {
    loadTrendData()
  })
  
  // 监听外部日期范围变化
  watch(
    () => props.dateRange,
    (newRange) => {
      if (newRange[0] && newRange[1]) {
        selectedDateRange.value = newRange
        loadTrendData()
      }
    }
  )
  
  // 初始化
  onMounted(() => {
    if (!selectedDateRange.value[0] || !selectedDateRange.value[1]) {
      selectedDateRange.value = generateDateRange('30days')
    }
    loadTrendData()
  })
  </script>
  
  <style scoped lang="scss">
  .trend-analysis {
    .analysis-header {
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
      
      .time-range-selector {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        .selector-label {
          color: #606266;
          font-weight: 500;
          white-space: nowrap;
        }
      }
      
      .quick-selectors {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
    
    .charts-container {
      .chart-card {
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .card-title {
            font-weight: 600;
            color: #303133;
          }
          
          .chart-controls {
            display: flex;
            gap: 8px;
          }
        }
      }
    }
    
    .statistics-table {
      .card-title {
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  // 响应式设计
  @media (max-width: 1200px) {
    .trend-analysis {
      .charts-container {
        .el-row .el-col {
          margin-bottom: 20px;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .trend-analysis {
      .analysis-header {
        .time-range-selector {
          flex-direction: column;
          align-items: stretch;
          
          .selector-label {
            margin-bottom: 8px;
          }
        }
        
        .quick-selectors {
          justify-content: center;
        }
      }
      
      .charts-container {
        .chart-card .card-header {
          flex-direction: column;
          gap: 12px;
          align-items: stretch;
        }
      }
    }
  }
  </style>