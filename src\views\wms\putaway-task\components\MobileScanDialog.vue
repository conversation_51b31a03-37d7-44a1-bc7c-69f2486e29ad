<template>
  <el-dialog
    v-model="dialogVisible"
    title="扫码上架"
    width="95%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    append-to-body
  >
    <div class="mobile-scan-dialog">
      <!-- 扫码区域 -->
      <div class="scan-section">
        <div class="scan-input-group">
          <el-input
            ref="scanInputRef"
            v-model="scannedCode"
            placeholder="请扫描物料条码或手动输入"
            @keyup.enter="handleScanConfirm"
            autofocus
            clearable
          >
            <template #prepend>
              <el-icon><Scan /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleScanConfirm" type="primary">
                确认
              </el-button>
            </template>
          </el-input>
        </div>
        
        <div class="scan-tips">
          <p><el-icon><InfoFilled /></el-icon> 请扫描物料条码或库位条码</p>
          <p><el-icon><InfoFilled /></el-icon> 也可以手动输入编码后按回车确认</p>
        </div>
      </div>
      
      <!-- 扫描历史 -->
      <div class="scan-history" v-if="scanHistory.length > 0">
        <div class="section-title">
          <span>扫描记录</span>
          <el-button size="small" @click="clearHistory">清空</el-button>
        </div>
        
        <div class="history-list">
          <div
            v-for="(record, index) in scanHistory"
            :key="index"
            class="history-item"
            :class="getHistoryItemClass(record)"
          >
            <div class="history-content">
              <div class="history-code">{{ record.code }}</div>
              <div class="history-info">
                <span class="history-type">{{ record.type }}</span>
                <span class="history-time">{{ formatTime(record.timestamp) }}</span>
              </div>
              <div class="history-result" v-if="record.result">
                {{ record.result }}
              </div>
            </div>
            <div class="history-status">
              <el-icon v-if="record.success" color="#67c23a"><CircleCheck /></el-icon>
              <el-icon v-else color="#f56c6c"><CircleClose /></el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 匹配结果 -->
      <div class="match-result" v-if="matchedItem">
        <div class="section-title">匹配结果</div>
        
        <div class="matched-item-card">
          <div class="item-header">
            <div class="item-code">{{ matchedItem.itemSku }}</div>
            <el-tag type="success">匹配成功</el-tag>
          </div>
          
          <div class="item-content">
            <div class="item-name">{{ matchedItem.itemName }}</div>
            <div class="item-details">
              <div class="detail-row">
                <span class="label">建议库位:</span>
                <span class="value">{{ matchedItem.suggestedLocation }}</span>
              </div>
              <div class="detail-row">
                <span class="label">上架数量:</span>
                <span class="value">{{ matchedItem.putawayQuantity }}</span>
              </div>
              <div class="detail-row">
                <span class="label">已上架数量:</span>
                <span class="value">{{ matchedItem.completedQuantity || 0 }}</span>
              </div>
              <div class="detail-row" v-if="matchedItem.batchNo">
                <span class="label">批次号:</span>
                <span class="value">{{ matchedItem.batchNo }}</span>
              </div>
            </div>
          </div>
          
          <!-- 快速上架 -->
          <div class="quick-putaway">
            <div class="quick-title">快速上架</div>
            <div class="quick-input-group">
              <el-input-number
                v-model="quickPutawayQty"
                :min="1"
                :max="remainingQty"
                :precision="0"
                controls-position="right"
              />
              <el-button
                type="primary"
                @click="handleQuickPutaway"
                :disabled="quickPutawayQty <= 0 || quickPutawayQty > remainingQty"
              >
                上架
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 扫码统计 -->
      <div class="scan-stats">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ scanHistory.length }}</div>
            <div class="stat-label">总扫描</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ successfulScans }}</div>
            <div class="stat-label">成功</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ failedScans }}</div>
            <div class="stat-label">失败</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ completedItems }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" size="large">关闭</el-button>
        <el-button
          type="primary"
          @click="handleBatchConfirm"
          :disabled="completedItems === 0"
          size="large"
        >
          批量确认 ({{ completedItems }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, nextTick } from 'vue'
import { ElDialog, ElButton, ElMessage, ElInput, ElInputNumber, ElIcon, ElTag } from 'element-plus'
import { Scan, InfoFilled, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import type { WmsPutawayTaskDetailResp } from '@/api/wms/putawayTask'

// 组件接口定义
interface MobileScanDialogEmits {
  confirm: [data: any]
  cancel: []
}

// 扫描记录接口
interface ScanRecord {
  code: string
  type: 'ITEM' | 'LOCATION' | 'UNKNOWN'
  timestamp: number
  success: boolean
  result?: string
  itemId?: number
}

// Emits
const emit = defineEmits<MobileScanDialogEmits>()

// 响应式数据
const dialogVisible = ref(false)
const scannedCode = ref('')
const scanHistory = ref<ScanRecord[]>([])
const matchedItem = ref<WmsPutawayTaskDetailResp | null>(null)
const quickPutawayQty = ref(1)
const putawayDetails = ref<WmsPutawayTaskDetailResp[]>([])
const completedPutaways = ref<any[]>([])

// 计算属性
const successfulScans = computed(() => {
  return scanHistory.value.filter(record => record.success).length
})

const failedScans = computed(() => {
  return scanHistory.value.filter(record => !record.success).length
})

const completedItems = computed(() => {
  return completedPutaways.value.length
})

const remainingQty = computed(() => {
  if (!matchedItem.value) return 0
  return matchedItem.value.putawayQuantity - (matchedItem.value.completedQuantity || 0)
})

// 表单引用
const scanInputRef = ref<InstanceType<typeof ElInput>>()

// 方法
const handleOpen = () => {
  nextTick(() => {
    scanInputRef.value?.focus()
  })
}

const handleClose = () => {
  resetData()
}

const resetData = () => {
  scannedCode.value = ''
  scanHistory.value = []
  matchedItem.value = null
  quickPutawayQty.value = 1
  completedPutaways.value = []
}

const handleScanConfirm = () => {
  if (!scannedCode.value.trim()) {
    ElMessage.warning('请输入扫描内容')
    return
  }
  
  processScanCode(scannedCode.value.trim())
  scannedCode.value = ''
  
  // 重新聚焦输入框
  nextTick(() => {
    scanInputRef.value?.focus()
  })
}

const processScanCode = (code: string) => {
  // 判断扫描类型
  const scanType = determineScanType(code)
  
  const record: ScanRecord = {
    code,
    type: scanType,
    timestamp: Date.now(),
    success: false
  }
  
  if (scanType === 'ITEM') {
    // 物料条码扫描
    const item = findItemByCode(code)
    if (item) {
      record.success = true
      record.result = `匹配到物料: ${item.itemName}`
      record.itemId = item.id
      matchedItem.value = item
      quickPutawayQty.value = Math.min(remainingQty.value, 1)
      ElMessage.success('物料匹配成功')
    } else {
      record.result = '未找到匹配的物料'
      ElMessage.error('未找到匹配的物料')
    }
  } else if (scanType === 'LOCATION') {
    // 库位条码扫描
    record.success = true
    record.result = `库位: ${code}`
    ElMessage.success('库位扫描成功')
  } else {
    // 未知类型
    record.result = '未识别的条码类型'
    ElMessage.warning('未识别的条码类型')
  }
  
  scanHistory.value.unshift(record)
}

const determineScanType = (code: string): 'ITEM' | 'LOCATION' | 'UNKNOWN' => {
  // 根据条码格式判断类型
  if (code.startsWith('ITM')) {
    return 'ITEM'
  } else if (code.startsWith('LOC')) {
    return 'LOCATION'
  } else {
    // 尝试匹配物料
    const item = findItemByCode(code)
    if (item) {
      return 'ITEM'
    }
    return 'UNKNOWN'
  }
}

const findItemByCode = (code: string): WmsPutawayTaskDetailResp | null => {
  // 在上架明细中查找匹配的物料
  return putawayDetails.value.find(detail => 
    detail.itemSku === code || 
    detail.barcode === code ||
    detail.itemName.includes(code)
  ) || null
}

const handleQuickPutaway = () => {
  if (!matchedItem.value || quickPutawayQty.value <= 0) return
  
  const putawayData = {
    detailId: matchedItem.value.id,
    itemSku: matchedItem.value.itemSku,
    itemName: matchedItem.value.itemName,
    completedQuantity: quickPutawayQty.value,
    actualLocation: matchedItem.value.suggestedLocation,
    timestamp: Date.now()
  }
  
  completedPutaways.value.push(putawayData)
  
  // 更新匹配物料的已上架数量
  matchedItem.value.completedQuantity = (matchedItem.value.completedQuantity || 0) + quickPutawayQty.value
  
  ElMessage.success(`成功上架 ${quickPutawayQty.value} 件`)
  
  // 重置
  matchedItem.value = null
  quickPutawayQty.value = 1
}

const clearHistory = () => {
  scanHistory.value = []
  ElMessage.success('扫描记录已清空')
}

const getHistoryItemClass = (record: ScanRecord) => {
  return {
    'history-success': record.success,
    'history-failed': !record.success
  }
}

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const handleBatchConfirm = () => {
  if (completedPutaways.value.length === 0) {
    ElMessage.warning('没有完成的上架记录')
    return
  }
  
  emit('confirm', {
    type: 'batch',
    putaways: completedPutaways.value
  })
  
  dialogVisible.value = false
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (details: WmsPutawayTaskDetailResp[] = []) => {
  putawayDetails.value = details
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.mobile-scan-dialog {
  padding: 8px 0;
}

.scan-section {
  margin-bottom: 20px;
}

.scan-input-group {
  margin-bottom: 12px;
}

.scan-tips {
  color: #909399;
  font-size: 14px;
}

.scan-tips p {
  display: flex;
  align-items: center;
  margin: 4px 0;
}

.scan-tips .el-icon {
  margin-right: 4px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.scan-history {
  margin-bottom: 20px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.history-item.history-success {
  background-color: #f0f9ff;
  border-color: #67c23a;
}

.history-item.history-failed {
  background-color: #fef0f0;
  border-color: #f56c6c;
}

.history-content {
  flex: 1;
}

.history-code {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.history-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.history-result {
  font-size: 12px;
  color: #606266;
}

.history-status {
  margin-left: 8px;
}

.match-result {
  margin-bottom: 20px;
}

.matched-item-card {
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #67c23a;
  border-radius: 6px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-code {
  font-weight: 600;
  color: #303133;
}

.item-content {
  margin-bottom: 12px;
}

.item-name {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.item-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.detail-row .label {
  color: #909399;
}

.detail-row .value {
  color: #303133;
  font-weight: 500;
}

.quick-putaway {
  border-top: 1px solid #e4e7ed;
  padding-top: 12px;
}

.quick-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.quick-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.quick-input-group .el-input-number {
  flex: 1;
}

.scan-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

.dialog-footer .el-button {
  flex: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }
  
  .stat-item {
    padding: 6px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .history-list {
    max-height: 150px;
  }
}
</style>
