<template>
  <el-dialog
    v-model="dialogVisible"
    title="库存分配"
    width="90%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="allocation-container">
      <!-- 分配策略选择 -->
      <div class="strategy-section">
        <el-form :model="allocationForm" label-width="120px" inline>
          <el-form-item label="分配策略">
            <el-select
              v-model="allocationForm.strategy"
              placeholder="请选择分配策略"
              style="width: 200px"
            >
              <el-option
                v-for="option in strategyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :loading="allocating"
              @click="handleAutoAllocate"
            >
              自动分配
            </el-button>
            <el-button @click="handleManualAllocate">
              手动分配
            </el-button>
            <el-button @click="handleClearAllocation">
              清除分配
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 分配明细表格 -->
      <div class="allocation-details">
        <VNTable
          ref="vnTableRef"
          :data="allocationData"
          :columns="allocationColumns"
          :loading="loading"
          :show-operations="true"
          :operation-width="120"
          operation-fixed="right"
          row-key="id"
          show-index
        >
          <template #column-availableQty="{ row }">
            <span :class="{ 'text-danger': row.availableQty < row.requiredQty }">
              {{ row.availableQty }}
            </span>
          </template>
          
          <template #column-allocationStatus="{ row }">
            <el-tag :type="getAllocationStatusType(row.allocationStatus)">
              {{ formatAllocationStatus(row.allocationStatus) }}
            </el-tag>
          </template>
          
          <template #operation="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleManualAllocateItem(row)"
            >
              手动分配
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleReleaseAllocation(row)"
            >
              释放
            </el-button>
          </template>
        </VNTable>
      </div>
      
      <!-- 分配结果汇总 -->
      <div class="allocation-summary">
        <el-descriptions title="分配汇总" :column="4" border>
          <el-descriptions-item label="总需求数量">
            {{ allocationSummary.totalRequired }}
          </el-descriptions-item>
          <el-descriptions-item label="已分配数量">
            {{ allocationSummary.totalAllocated }}
          </el-descriptions-item>
          <el-descriptions-item label="未分配数量">
            {{ allocationSummary.totalUnallocated }}
          </el-descriptions-item>
          <el-descriptions-item label="分配完成率">
            {{ allocationSummary.completionRate }}%
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="!canConfirm"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
  
  <!-- 手动分配对话框 -->
  <ManualAllocationDialog
    ref="manualAllocationDialogRef"
    @confirm="handleManualAllocationConfirm"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElTag, ElForm, ElFormItem, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import ManualAllocationDialog from './ManualAllocationDialog.vue'
import type { TableColumn } from '@/components/VNTable/types'
import type { WmsAllocationStrategy } from '@/api/wms/inventoryAllocation'
import * as inventoryAllocationApi from '@/api/wms/inventoryAllocation'

// 组件接口定义
interface InventoryAllocationProps {
  notificationId: number
  details: any[]
}

interface InventoryAllocationEmits {
  confirm: [allocations: any[]]
  cancel: []
}

// Props 和 Emits
const props = defineProps<InventoryAllocationProps>()
const emit = defineEmits<InventoryAllocationEmits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const allocating = ref(false)
const allocationData = ref<any[]>([])

const allocationForm = reactive({
  strategy: 'FIFO' as WmsAllocationStrategy
})

// 分配策略选项
const strategyOptions = [
  { label: '先进先出(FIFO)', value: 'FIFO' },
  { label: '后进先出(LIFO)', value: 'LIFO' },
  { label: '批次优先', value: 'BATCH_PRIORITY' },
  { label: '过期日期优先', value: 'EXPIRY_DATE_PRIORITY' },
  { label: '库位优先', value: 'LOCATION_PRIORITY' }
]

// 表格列配置
const allocationColumns = computed<TableColumn[]>(() => [
  { prop: 'itemCode', label: '物料编码', minWidth: 120 },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'requiredQty', label: '需求数量', width: 100 },
  { prop: 'availableQty', label: '可用数量', width: 100, slot: true },
  { prop: 'allocatedQty', label: '已分配数量', width: 120 },
  { prop: 'unallocatedQty', label: '未分配数量', width: 120 },
  { prop: 'allocationStatus', label: '分配状态', width: 100, slot: true }
])

// 分配汇总
const allocationSummary = computed(() => {
  const totalRequired = allocationData.value.reduce((sum, item) => sum + item.requiredQty, 0)
  const totalAllocated = allocationData.value.reduce((sum, item) => sum + (item.allocatedQty || 0), 0)
  const totalUnallocated = totalRequired - totalAllocated
  const completionRate = totalRequired > 0 ? Math.round((totalAllocated / totalRequired) * 100) : 0
  
  return {
    totalRequired,
    totalAllocated,
    totalUnallocated,
    completionRate
  }
})

// 是否可以确认
const canConfirm = computed(() => {
  return allocationSummary.value.completionRate === 100
})

// 方法
const loadAllocationData = async () => {
  loading.value = true
  try {
    // 加载分配数据
    const result = await inventoryAllocationApi.getAllocationStatus(props.notificationId)
    allocationData.value = result.details || []
  } catch (error) {
    ElMessage.error('加载分配数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  loadAllocationData()
}

const handleClose = () => {
  // 清理数据
}

const handleAutoAllocate = async () => {
  allocating.value = true
  try {
    await inventoryAllocationApi.autoAllocate({
      notificationId: props.notificationId,
      strategy: allocationForm.strategy
    })
    ElMessage.success('自动分配完成')
    await loadAllocationData()
  } catch (error) {
    ElMessage.error('自动分配失败')
    console.error(error)
  } finally {
    allocating.value = false
  }
}

const handleManualAllocate = () => {
  // 打开手动分配对话框
  manualAllocationDialogRef.value?.open(allocationData.value)
}

const handleManualAllocateItem = (row: any) => {
  // 打开单个物料的手动分配对话框
  manualAllocationDialogRef.value?.open([row])
}

const handleClearAllocation = async () => {
  try {
    await inventoryAllocationApi.batchReleaseAllocation({
      notificationId: props.notificationId
    })
    ElMessage.success('清除分配完成')
    await loadAllocationData()
  } catch (error) {
    ElMessage.error('清除分配失败')
    console.error(error)
  }
}

const handleReleaseAllocation = async (row: any) => {
  try {
    await inventoryAllocationApi.releaseAllocation(row.id, {
      reason: '手动释放'
    })
    ElMessage.success('释放分配完成')
    await loadAllocationData()
  } catch (error) {
    ElMessage.error('释放分配失败')
    console.error(error)
  }
}

const handleManualAllocationConfirm = async () => {
  // 处理手动分配确认
  await loadAllocationData()
}

const handleConfirm = () => {
  emit('confirm', allocationData.value)
  dialogVisible.value = false
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

const getAllocationStatusType = (status: string) => {
  switch (status) {
    case 'ALLOCATED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'RELEASED':
      return 'danger'
    default:
      return 'info'
  }
}

const formatAllocationStatus = (status: string) => {
  switch (status) {
    case 'ALLOCATED':
      return '已分配'
    case 'PENDING':
      return '待分配'
    case 'RELEASED':
      return '已释放'
    default:
      return '未知'
  }
}

// 组件引用
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const manualAllocationDialogRef = ref<InstanceType<typeof ManualAllocationDialog>>()

// 暴露方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.allocation-container {
  padding: 16px 0;
}

.strategy-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.allocation-details {
  margin-bottom: 16px;
}

.allocation-summary {
  margin-top: 16px;
}

.text-danger {
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
