<template>
  <el-dialog
    v-model="dialogVisible"
    title="波次管理"
    width="90%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="wave-manager-container">
      <!-- 操作工具栏 -->
      <div class="toolbar">
        <el-button
          type="primary"
          @click="handleCreateWave"
          :disabled="selectedTasks.length === 0"
        >
          创建波次
        </el-button>
        <el-button
          @click="handleOptimizeWave"
          :disabled="selectedTasks.length === 0"
        >
          智能优化
        </el-button>
        <el-button @click="handleRefresh">
          刷新
        </el-button>
      </div>
      
      <!-- 左侧：待分配任务 -->
      <div class="content-area">
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="pending-tasks">
              <h4>待分配任务</h4>
              <VNTable
                ref="pendingTasksTableRef"
                :data="pendingTasks"
                :columns="taskColumns"
                :loading="pendingTasksLoading"
                :show-operations="true"
                :operation-width="80"
                operation-fixed="right"
                row-key="id"
                :selection-type="'multiple'"
                @selection-change="handleTaskSelectionChange"
                max-height="400"
              >
                <template #column-priority="{ row }">
                  <el-tag :type="getPriorityTagType(row.priority)">
                    {{ formatPriority(row.priority) }}
                  </el-tag>
                </template>
                
                <template #column-estimatedDuration="{ row }">
                  {{ row.estimatedDuration || '-' }}分钟
                </template>
                
                <template #operation="{ row }">
                  <el-button
                    type="text"
                    size="small"
                    @click="handleAddToWave(row)"
                  >
                    添加
                  </el-button>
                </template>
              </VNTable>
            </div>
          </el-col>
          
          <!-- 右侧：波次列表 -->
          <el-col :span="12">
            <div class="wave-list">
              <h4>波次列表</h4>
              <VNTable
                ref="waveTableRef"
                :data="waves"
                :columns="waveColumns"
                :loading="wavesLoading"
                :show-operations="true"
                :operation-width="120"
                operation-fixed="right"
                row-key="id"
                max-height="400"
                @row-click="handleWaveRowClick"
              >
                <template #column-status="{ row }">
                  <el-tag :type="getWaveStatusTagType(row.status)">
                    {{ formatWaveStatus(row.status) }}
                  </el-tag>
                </template>
                
                <template #column-progress="{ row }">
                  <el-progress
                    :percentage="getWaveProgress(row)"
                    :color="getWaveProgressColor(row)"
                    :show-text="false"
                    style="width: 80px"
                  />
                </template>
                
                <template #operation="{ row }">
                  <el-button
                    type="text"
                    size="small"
                    @click="handleEditWave(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="handleDeleteWave(row)"
                  >
                    删除
                  </el-button>
                </template>
              </VNTable>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 波次详情 -->
      <div class="wave-details" v-if="selectedWave">
        <h4>波次详情：{{ selectedWave.waveNo }}</h4>
        <VNTable
          :data="selectedWave.tasks || []"
          :columns="waveTaskColumns"
          :loading="false"
          :show-operations="true"
          :operation-width="80"
          operation-fixed="right"
          row-key="id"
          max-height="200"
        >
          <template #operation="{ row }">
            <el-button
              type="text"
              size="small"
              @click="handleRemoveFromWave(row)"
            >
              移除
            </el-button>
          </template>
        </VNTable>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">关闭</el-button>
      </div>
    </template>
  </el-dialog>
  
  <!-- 创建波次对话框 -->
  <CreateWaveDialog
    ref="createWaveDialogRef"
    :selected-tasks="selectedTasks"
    @confirm="handleCreateWaveConfirm"
  />
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, defineExpose, onMounted } from 'vue'
import { ElDialog, ElButton, ElMessage, ElTag, ElProgress, ElRow, ElCol, ElMessageBox } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import CreateWaveDialog from './CreateWaveDialog.vue'
import type { TableColumn } from '@/components/VNTable/types'
import type { WmsPickingTaskResp, WmsWaveResp } from '@/api/wms/pickingTask'
import { usePickingTaskStore } from '@/store/wms/pickingTask'

// 组件接口定义
interface WaveManagerProps {
  visible?: boolean
  selectedTasks?: number[]
}

interface WaveManagerEmits {
  confirm: []
  cancel: []
}

// Props 和 Emits
const props = withDefaults(defineProps<WaveManagerProps>(), {
  visible: false,
  selectedTasks: () => []
})

const emit = defineEmits<WaveManagerEmits>()

// Store
const pickingTaskStore = usePickingTaskStore()

// 响应式数据
const dialogVisible = ref(false)
const pendingTasksLoading = ref(false)
const wavesLoading = ref(false)
const selectedTasks = ref<number[]>([])
const selectedWave = ref<WmsWaveResp | null>(null)

// 计算属性
const pendingTasks = computed(() => pickingTaskStore.pendingTasks)
const waves = computed(() => pickingTaskStore.waves)

// 表格列配置
const taskColumns = computed<TableColumn[]>(() => [
  { prop: 'taskNo', label: '任务编号', width: 120 },
  { prop: 'notificationNo', label: '出库单号', width: 120 },
  { prop: 'priority', label: '优先级', width: 80, slot: true },
  { prop: 'estimatedDuration', label: '预计时长', width: 100, slot: true }
])

const waveColumns = computed<TableColumn[]>(() => [
  { prop: 'waveNo', label: '波次编号', width: 120 },
  { prop: 'taskCount', label: '任务数', width: 80 },
  { prop: 'totalItems', label: '物料数', width: 80 },
  { prop: 'status', label: '状态', width: 80, slot: true },
  { prop: 'progress', label: '进度', width: 100, slot: true },
  { prop: 'assignedUserName', label: '分配人员', width: 100 }
])

const waveTaskColumns = computed<TableColumn[]>(() => [
  { prop: 'taskNo', label: '任务编号', width: 120 },
  { prop: 'notificationNo', label: '出库单号', width: 120 },
  { prop: 'priority', label: '优先级', width: 80 },
  { prop: 'status', label: '状态', width: 80 }
])

// 方法
const loadPendingTasks = async () => {
  pendingTasksLoading.value = true
  try {
    await pickingTaskStore.fetchList({ status: 'PENDING' })
  } catch (error) {
    ElMessage.error('加载待分配任务失败')
    console.error(error)
  } finally {
    pendingTasksLoading.value = false
  }
}

const loadWaves = async () => {
  wavesLoading.value = true
  try {
    await pickingTaskStore.fetchWaves()
  } catch (error) {
    ElMessage.error('加载波次列表失败')
    console.error(error)
  } finally {
    wavesLoading.value = false
  }
}

const handleOpen = () => {
  loadPendingTasks()
  loadWaves()
  selectedTasks.value = [...props.selectedTasks]
}

const handleClose = () => {
  selectedTasks.value = []
  selectedWave.value = null
}

const handleTaskSelectionChange = (rows: WmsPickingTaskResp[]) => {
  selectedTasks.value = rows.map(row => row.id)
}

const handleWaveRowClick = (row: WmsWaveResp) => {
  selectedWave.value = row
}

const handleCreateWave = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要创建波次的任务')
    return
  }
  createWaveDialogRef.value?.open()
}

const handleCreateWaveConfirm = async (waveData: any) => {
  try {
    await pickingTaskStore.createWave({
      ...waveData,
      taskIds: selectedTasks.value
    })
    ElMessage.success('创建波次成功')
    await loadPendingTasks()
    await loadWaves()
    selectedTasks.value = []
  } catch (error) {
    ElMessage.error('创建波次失败')
    console.error(error)
  }
}

const handleOptimizeWave = () => {
  // 智能优化波次逻辑
  ElMessage.info('智能优化功能开发中...')
}

const handleAddToWave = (task: WmsPickingTaskResp) => {
  if (!selectedWave.value) {
    ElMessage.warning('请先选择一个波次')
    return
  }
  
  // 添加任务到波次
  handleAddTasksToWave(selectedWave.value.id, [task.id])
}

const handleAddTasksToWave = async (waveId: number, taskIds: number[]) => {
  try {
    await pickingTaskStore.addTasksToWave(waveId, taskIds)
    ElMessage.success('添加任务到波次成功')
    await loadPendingTasks()
    await loadWaves()
  } catch (error) {
    ElMessage.error('添加任务到波次失败')
    console.error(error)
  }
}

const handleRemoveFromWave = async (task: WmsPickingTaskResp) => {
  if (!selectedWave.value) return
  
  try {
    // 这里需要实现从波次移除任务的API
    ElMessage.success('从波次移除任务成功')
    await loadPendingTasks()
    await loadWaves()
  } catch (error) {
    ElMessage.error('从波次移除任务失败')
    console.error(error)
  }
}

const handleEditWave = (wave: WmsWaveResp) => {
  // 编辑波次逻辑
  ElMessage.info('编辑波次功能开发中...')
}

const handleDeleteWave = async (wave: WmsWaveResp) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除波次 ${wave.waveNo} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里需要实现删除波次的API
    ElMessage.success('删除波次成功')
    await loadWaves()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除波次失败')
      console.error(error)
    }
  }
}

const handleRefresh = () => {
  loadPendingTasks()
  loadWaves()
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const getPriorityTagType = (priority: number) => {
  if (priority <= 2) return 'danger'
  if (priority <= 3) return 'warning'
  return 'info'
}

const formatPriority = (priority: number) => {
  const labels = ['', '最高', '高', '中', '低', '最低']
  return labels[priority] || '未知'
}

const getWaveStatusTagType = (status: string) => {
  switch (status) {
    case 'CREATED':
      return 'info'
    case 'ASSIGNED':
      return 'warning'
    case 'IN_PROGRESS':
      return 'primary'
    case 'COMPLETED':
      return 'success'
    case 'CANCELLED':
      return 'danger'
    default:
      return 'info'
  }
}

const formatWaveStatus = (status: string) => {
  switch (status) {
    case 'CREATED':
      return '已创建'
    case 'ASSIGNED':
      return '已分配'
    case 'IN_PROGRESS':
      return '进行中'
    case 'COMPLETED':
      return '已完成'
    case 'CANCELLED':
      return '已取消'
    default:
      return '未知'
  }
}

const getWaveProgress = (wave: WmsWaveResp) => {
  if (!wave.tasks || wave.tasks.length === 0) return 0
  const completedTasks = wave.tasks.filter(task => task.status === 'COMPLETED').length
  return Math.round((completedTasks / wave.tasks.length) * 100)
}

const getWaveProgressColor = (wave: WmsWaveResp) => {
  const progress = getWaveProgress(wave)
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

// 组件引用
const pendingTasksTableRef = ref<InstanceType<typeof VNTable>>()
const waveTableRef = ref<InstanceType<typeof VNTable>>()
const createWaveDialogRef = ref<InstanceType<typeof CreateWaveDialog>>()

// 暴露方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.wave-manager-container {
  padding: 16px 0;
}

.toolbar {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.content-area {
  margin-bottom: 16px;
}

.pending-tasks,
.wave-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
}

.pending-tasks h4,
.wave-list h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.wave-details {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.wave-details h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}
</style>
