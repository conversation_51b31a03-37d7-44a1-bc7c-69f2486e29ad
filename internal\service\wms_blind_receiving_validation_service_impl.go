package service

import (
	"context"
	"fmt"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	apperrors "backend/pkg/errors"
	"backend/pkg/util"
)

// WmsBlindReceivingValidationService 盲收验证记录服务接口
type WmsBlindReceivingValidationService interface {
	BaseService // 继承基础服务接口

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsBlindReceivingValidationCreateReq) (*vo.WmsBlindReceivingValidationVO, error)
	GetByID(ctx context.Context, id uint64) (*vo.WmsBlindReceivingValidationVO, error)
	Update(ctx context.Context, req *dto.WmsBlindReceivingValidationUpdateReq) (*vo.WmsBlindReceivingValidationVO, error)
	Delete(ctx context.Context, id uint64) error

	// 查询操作
	GetByConfigID(ctx context.Context, configID uint, limit int) ([]*vo.WmsBlindReceivingValidationVO, error)
	GetByWarehouseAndCustomer(ctx context.Context, warehouseID, customerID uint, limit int) ([]*vo.WmsBlindReceivingValidationVO, error)
	GetPendingValidations(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error)
	GetValidationHistory(ctx context.Context, req *dto.WmsBlindReceivingValidationHistoryReq) ([]*vo.WmsBlindReceivingValidationVO, error)

	// 业务操作
	ProcessValidation(ctx context.Context, req *dto.WmsBlindReceivingValidationProcessReq) error
	ValidateBlindReceiving(ctx context.Context, req *dto.BlindReceivingValidationReq) (*dto.BlindReceivingValidationResp, error)

	// 统计操作
	GetValidationStats(ctx context.Context, req *dto.WmsBlindReceivingValidationStatsReq) (*vo.WmsBlindReceivingValidationStatsVO, error)
}

// WmsBlindReceivingValidationServiceImpl 盲收验证记录服务实现
type WmsBlindReceivingValidationServiceImpl struct {
	BaseServiceImpl // 嵌入基础服务实现
}

// NewWmsBlindReceivingValidationService 创建盲收验证记录服务实例
func NewWmsBlindReceivingValidationService(sm *ServiceManager) WmsBlindReceivingValidationService {
	return &WmsBlindReceivingValidationServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// ValidateBlindReceiving 执行盲收验证
func (s *WmsBlindReceivingValidationServiceImpl) ValidateBlindReceiving(ctx context.Context, req *dto.BlindReceivingValidationReq) (*dto.BlindReceivingValidationResp, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()

	// 参数验证
	if req.WarehouseID == 0 {
		return nil, apperrors.NewValidationError("warehouseID", req.WarehouseID, "仓库ID不能为空")
	}
	if req.ClientID == nil || *req.ClientID == 0 {
		return nil, apperrors.NewValidationError("clientID", req.ClientID, "客户ID不能为空")
	}
	if req.Quantity <= 0 {
		return nil, apperrors.NewValidationError("quantity", req.Quantity, "请求数量必须大于0")
	}

	// 获取有效的盲收配置
	config, err := configRepo.GetEffectiveConfig(ctx, req.WarehouseID, req.ClientID, req.UserID)
	if err != nil {
		return nil, err
	}

	if config == nil {
		return &dto.BlindReceivingValidationResp{
			IsAllowed:         false,
			RequiresApproval:  false,
			ValidationMessage: "未找到匹配的盲收配置",
		}, nil
	}

	// 强制从上下文获取用户ID - 用户强绑定
	userID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
	}
	if userID == 0 {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
	}
	userIDUint := uint(userID)

	// 创建验证记录
	validation := &entity.WmsBlindReceivingValidation{
		ValidationID:      fmt.Sprintf("VLD_%d_%d", time.Now().Unix(), req.WarehouseID),
		WarehouseID:       req.WarehouseID,
		ClientID:          req.ClientID,
		UserID:            &userIDUint,
		ConfigID:          config.ID,
		Strategy:          config.Strategy,
		RequestQuantity:   req.Quantity,
		IsAllowed:         config.IsActive,
		ValidationMessage: fmt.Sprintf("自动验证 - 配置ID: %d", config.ID),
		ItemID:            &req.ItemID,
	}

	// 设置基础实体字段
	validation.CreatedBy = userIDUint
	validation.UpdatedBy = userIDUint

	if err := validationRepo.Create(ctx, validation); err != nil {
		return nil, err
	}

	// 根据策略返回验证结果
	resp := &dto.BlindReceivingValidationResp{
		IsAllowed:         config.IsActive,
		RequiresApproval:  config.RequiresApproval,
		Strategy:          config.Strategy,
		ConfigLevel:       config.ConfigLevel,
		ApprovalUserRoles: config.GetApprovalUserRolesList(),
	}

	// 检查数量限制
	if config.MaxBlindReceivingQty != nil && req.Quantity > *config.MaxBlindReceivingQty {
		resp.IsAllowed = false
		resp.ValidationMessage = fmt.Sprintf("盲收数量 %.2f 超过限制 %.2f", req.Quantity, *config.MaxBlindReceivingQty)
	} else {
		resp.ValidationMessage = fmt.Sprintf("根据%s策略，允许盲收", config.Strategy)
	}

	return resp, nil
}

// Create 创建验证记录
func (s *WmsBlindReceivingValidationServiceImpl) Create(ctx context.Context, req *dto.WmsBlindReceivingValidationCreateReq) (*vo.WmsBlindReceivingValidationVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	// 参数验证
	if req.WarehouseID == 0 {
		return nil, apperrors.NewValidationError("warehouseID", req.WarehouseID, "仓库ID不能为空")
	}
	if req.CustomerID == 0 {
		return nil, apperrors.NewValidationError("customerID", req.CustomerID, "客户ID不能为空")
	}
	if req.RequestedQuantity <= 0 {
		return nil, apperrors.NewValidationError("requestedQuantity", req.RequestedQuantity, "请求数量必须大于0")
	}

	// 强制从上下文获取用户ID - 用户强绑定
	userID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
	}
	if userID == 0 {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
	}
	userIDUint := uint(userID)
	clientID := uint(req.CustomerID)

	// 创建验证记录实体
	validation := &entity.WmsBlindReceivingValidation{
		ValidationID:      fmt.Sprintf("VLD_%d_%d", time.Now().Unix(), req.WarehouseID),
		WarehouseID:       uint(req.WarehouseID),
		ClientID:          &clientID,
		UserID:            &userIDUint,
		ConfigID:          uint(req.ConfigID),
		Strategy:          req.Strategy,
		RequestQuantity:   float64(req.RequestedQuantity),
		IsAllowed:         true,
		ValidationMessage: req.ValidationData,
	}

	// 设置基础实体字段
	validation.CreatedBy = userIDUint
	validation.UpdatedBy = userIDUint

	// 保存到数据库
	if err := validationRepo.Create(ctx, validation); err != nil {
		return nil, err
	}

	// 转换为VO并返回
	return s.entityToVO(ctx, validation), nil
}

// GetByID 根据ID获取验证记录
func (s *WmsBlindReceivingValidationServiceImpl) GetByID(ctx context.Context, id uint64) (*vo.WmsBlindReceivingValidationVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if id == 0 {
		return nil, apperrors.NewValidationError("id", id, "ID不能为空")
	}

	validation, err := validationRepo.GetByID(ctx, uint(id))
	if err != nil {
		return nil, err
	}

	return s.entityToVO(ctx, validation), nil
}

// Update 更新验证记录
func (s *WmsBlindReceivingValidationServiceImpl) Update(ctx context.Context, req *dto.WmsBlindReceivingValidationUpdateReq) (*vo.WmsBlindReceivingValidationVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if req.ID == 0 {
		return nil, apperrors.NewValidationError("id", req.ID, "ID不能为空")
	}

	// 获取现有记录
	validation, err := validationRepo.GetByID(ctx, uint(req.ID))
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	userID, err := util.GetUserIDFromStdContext(ctx)
	if err != nil {
		return nil, apperrors.NewAuthError(apperrors.CODE_AUTH_UNAUTHORIZED, "用户未登录")
	}
	userIDUint := uint(userID)

	// 更新字段 - 注意：根据实际Entity字段调整
	if req.ValidationResult != "" {
		if req.ValidationResult == "SUCCESS" {
			validation.ApprovalStatus = &[]string{"APPROVED"}[0]
		} else if req.ValidationResult == "FAILURE" {
			validation.ApprovalStatus = &[]string{"REJECTED"}[0]
		}
	}

	if req.ProcessingNotes != "" {
		validation.Remark = &req.ProcessingNotes
	}
	if req.ValidationData != "" {
		validation.ValidationMessage = req.ValidationData
	}

	validation.UpdatedBy = userIDUint
	validation.UpdatedAt = time.Now()

	// 如果是处理完成，设置处理时间
	if req.ValidationResult == "SUCCESS" || req.ValidationResult == "FAILURE" {
		now := time.Now()
		validation.ProcessedAt = &now
		validation.ProcessedBy = &userIDUint
	}

	// 保存更新
	if err := validationRepo.Update(ctx, validation); err != nil {
		return nil, err
	}

	return s.entityToVO(ctx, validation), nil
}

// Delete 删除验证记录
func (s *WmsBlindReceivingValidationServiceImpl) Delete(ctx context.Context, id uint64) error {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if id == 0 {
		return apperrors.NewValidationError("id", id, "ID不能为空")
	}

	return validationRepo.Delete(ctx, uint(id))
}

// GetByConfigID 根据配置ID获取验证记录
func (s *WmsBlindReceivingValidationServiceImpl) GetByConfigID(ctx context.Context, configID uint, limit int) ([]*vo.WmsBlindReceivingValidationVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if configID == 0 {
		return nil, apperrors.NewValidationError("configID", configID, "配置ID不能为空")
	}

	validations, err := validationRepo.GetByConfigID(ctx, configID, limit)
	if err != nil {
		return nil, err
	}

	return s.entitiesToVOs(ctx, validations), nil
}

// GetByWarehouseAndCustomer 根据仓库和客户获取验证记录
func (s *WmsBlindReceivingValidationServiceImpl) GetByWarehouseAndCustomer(ctx context.Context, warehouseID, customerID uint, limit int) ([]*vo.WmsBlindReceivingValidationVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if warehouseID == 0 {
		return nil, apperrors.NewValidationError("warehouseID", warehouseID, "仓库ID不能为空")
	}
	if customerID == 0 {
		return nil, apperrors.NewValidationError("customerID", customerID, "客户ID不能为空")
	}

	validations, err := validationRepo.GetByWarehouseAndCustomer(ctx, warehouseID, customerID, limit)
	if err != nil {
		return nil, err
	}

	return s.entitiesToVOs(ctx, validations), nil
}

// GetPendingValidations 获取待处理的验证记录
func (s *WmsBlindReceivingValidationServiceImpl) GetPendingValidations(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if warehouseID == 0 {
		return nil, apperrors.NewValidationError("warehouseID", warehouseID, "仓库ID不能为空")
	}

	validations, err := validationRepo.GetPendingValidations(ctx, warehouseID)
	if err != nil {
		return nil, err
	}

	return s.entitiesToVOs(ctx, validations), nil
}

// GetValidationHistory 获取验证历史记录
func (s *WmsBlindReceivingValidationServiceImpl) GetValidationHistory(ctx context.Context, req *dto.WmsBlindReceivingValidationHistoryReq) ([]*vo.WmsBlindReceivingValidationVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if req.WarehouseID == 0 {
		return nil, apperrors.NewValidationError("warehouseID", req.WarehouseID, "仓库ID不能为空")
	}
	if req.Days <= 0 {
		req.Days = 30 // 默认30天
	}

	var customerIDPtr *uint
	if req.CustomerID != 0 {
		customerID := uint(req.CustomerID)
		customerIDPtr = &customerID
	}

	validations, err := validationRepo.GetValidationHistory(ctx, uint(req.WarehouseID), customerIDPtr, req.Days)
	if err != nil {
		return nil, err
	}

	return s.entitiesToVOs(ctx, validations), nil
}

// ProcessValidation 处理验证记录
func (s *WmsBlindReceivingValidationServiceImpl) ProcessValidation(ctx context.Context, req *dto.WmsBlindReceivingValidationProcessReq) error {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if req.ID == 0 {
		return apperrors.NewValidationError("id", req.ID, "验证记录ID不能为空")
	}
	if req.ValidationResult == "" {
		return apperrors.NewValidationError("validationResult", req.ValidationResult, "验证结果不能为空")
	}

	// 获取验证记录
	validation, err := validationRepo.GetByID(ctx, uint(req.ID))
	if err != nil {
		return err
	}

	// 检查状态
	if validation.ApprovalStatus != nil && *validation.ApprovalStatus != "PENDING" {
		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "该验证记录已处理，无法重复处理")
	}

	// 获取用户信息
	userID, err := util.GetUserIDFromStdContext(ctx)
	if err != nil {
		return apperrors.NewAuthError(apperrors.CODE_AUTH_UNAUTHORIZED, "用户未登录")
	}
	userIDUint := uint(userID)

	// 更新验证记录
	if req.ValidationResult == "SUCCESS" {
		validation.ApprovalStatus = &[]string{"APPROVED"}[0]
	} else if req.ValidationResult == "FAILURE" {
		validation.ApprovalStatus = &[]string{"REJECTED"}[0]
	}

	validation.Remark = &req.ProcessingNotes
	validation.UpdatedBy = userIDUint
	validation.UpdatedAt = time.Now()

	now := time.Now()
	validation.ProcessedAt = &now
	validation.ProcessedBy = &userIDUint

	return validationRepo.Update(ctx, validation)
}

// GetValidationStats 获取验证统计信息
func (s *WmsBlindReceivingValidationServiceImpl) GetValidationStats(ctx context.Context, req *dto.WmsBlindReceivingValidationStatsReq) (*vo.WmsBlindReceivingValidationStatsVO, error) {
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()
	if req.WarehouseID == 0 {
		return nil, apperrors.NewValidationError("warehouseID", req.WarehouseID, "仓库ID不能为空")
	}

	// 设置默认时间范围
	if req.StartTime.IsZero() {
		req.StartTime = time.Now().AddDate(0, 0, -30) // 默认30天前
	}
	if req.EndTime.IsZero() {
		req.EndTime = time.Now()
	}

	// 获取统计数据
	stats, err := validationRepo.GetValidationStats(ctx, uint(req.WarehouseID), req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}

	return &vo.WmsBlindReceivingValidationStatsVO{
		WarehouseID:  req.WarehouseID,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
		TotalCount:   stats["total_count"].(int64),
		SuccessCount: stats["success_count"].(int64),
		FailureCount: stats["failure_count"].(int64),
		SuccessRate:  stats["success_rate"].(float64),
	}, nil
}

// entityToVO 将实体转换为VO
func (s *WmsBlindReceivingValidationServiceImpl) entityToVO(ctx context.Context, validation *entity.WmsBlindReceivingValidation) *vo.WmsBlindReceivingValidationVO {
	if validation == nil {
		return nil
	}

	vo := &vo.WmsBlindReceivingValidationVO{
		ID:                uint64(validation.ID),
		WarehouseID:       uint64(validation.WarehouseID),
		CustomerID:        0,
		UserID:            0,
		ConfigID:          uint64(validation.ConfigID),
		ValidationResult:  "PENDING",
		RequestedQuantity: uint64(validation.RequestQuantity),
		ActualQuantity:    0,
		Strategy:          validation.Strategy,
		ValidationData:    validation.ValidationMessage,
		ProcessingNotes:   "",
		ProcessedAt:       validation.ProcessedAt,
		ProcessedBy:       nil,
		CreatedAt:         validation.CreatedAt,
		UpdatedAt:         validation.UpdatedAt,
		CreatedBy:         uint64(validation.CreatedBy),
		UpdatedBy:         uint64(validation.UpdatedBy),
	}

	if validation.ClientID != nil {
		vo.CustomerID = uint64(*validation.ClientID)
	}
	if validation.UserID != nil {
		vo.UserID = uint64(*validation.UserID)
	}
	if validation.ProcessedBy != nil {
		processedBy := uint64(*validation.ProcessedBy)
		vo.ProcessedBy = &processedBy
	}
	if validation.Remark != nil {
		vo.ProcessingNotes = *validation.Remark
	}

	if validation.ApprovalStatus != nil {
		switch *validation.ApprovalStatus {
		case "APPROVED":
			vo.ValidationResult = "SUCCESS"
		case "REJECTED":
			vo.ValidationResult = "FAILURE"
		default:
			vo.ValidationResult = "PENDING"
		}
	} else {
		vo.ValidationResult = "PENDING"
	}

	s.fillValidationInfo(ctx, vo)

	return vo
}

// entitiesToVOs 将实体列表转换为VO列表
func (s *WmsBlindReceivingValidationServiceImpl) entitiesToVOs(ctx context.Context, validations []*entity.WmsBlindReceivingValidation) []*vo.WmsBlindReceivingValidationVO {
	if len(validations) == 0 {
		return []*vo.WmsBlindReceivingValidationVO{}
	}

	vos := make([]*vo.WmsBlindReceivingValidationVO, len(validations))
	for i, validation := range validations {
		vos[i] = s.entityToVO(ctx, validation)
	}

	return vos
}

// fillValidationInfo 填充验证记录的扩展信息
func (s *WmsBlindReceivingValidationServiceImpl) fillValidationInfo(ctx context.Context, vo *vo.WmsBlindReceivingValidationVO) {
	if vo == nil {
		return
	}

	if vo.ProcessedAt != nil {
		duration := vo.ProcessedAt.Sub(vo.CreatedAt)
		vo.ProcessingDuration = int64(duration.Minutes())
	}

	if vo.ValidationResult == "PENDING" {
		if time.Since(vo.CreatedAt).Hours() > 24 {
			vo.IsOverdue = true
		}
	}

	switch vo.ValidationResult {
	case "PENDING":
		vo.StatusDisplay = "待处理"
	case "SUCCESS":
		vo.StatusDisplay = "验证成功"
	case "FAILURE":
		vo.StatusDisplay = "验证失败"
	default:
		vo.StatusDisplay = vo.ValidationResult
	}
}
