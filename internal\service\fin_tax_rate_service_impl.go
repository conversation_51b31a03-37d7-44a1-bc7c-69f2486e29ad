package service

import (
	"context"
	"errors"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"

	"github.com/shopspring/decimal"
)

// FinTaxRateService 定义了税率服务的接口
type FinTaxRateService interface {
	Create(ctx context.Context, req *dto.FinTaxRateCreateReq) (*vo.FinTaxRateItemRes, error)
	Update(ctx context.Context, req *dto.FinTaxRateUpdateReq) (*vo.FinTaxRateItemRes, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.FinTaxRateItemRes, error)
	GetPage(ctx context.Context, req *dto.FinTaxRatePageReq) (*response.PageResult, error)
	GetList(ctx context.Context) ([]*vo.FinTaxRateItemRes, error)
}

// finTaxRateServiceImpl 是 FinTaxRateService 的实现
type finTaxRateServiceImpl struct {
	*BaseServiceImpl
	repo repository.FinTaxRateRepository
}

// NewFinTaxRateService 创建一个新的税率服务实例
func NewFinTaxRateService(sm *ServiceManager, repo repository.FinTaxRateRepository) FinTaxRateService {
	return &finTaxRateServiceImpl{
		BaseServiceImpl: NewBaseService(sm),
		repo:            repo,
	}
}

// Create 创建税率
func (s *finTaxRateServiceImpl) Create(ctx context.Context, req *dto.FinTaxRateCreateReq) (*vo.FinTaxRateItemRes, error) {
	// 检查Name是否已存在
	existing, err := s.repo.FindByName(ctx, req.Name)
	if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询税率失败").WithCause(err)
	}
	if existing != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("税率名称 '%s' 已存在", req.Name))
	}

	rate, err := decimal.NewFromString(req.Rate.String())
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "税率格式无效").WithCause(err)
	}

	entity := &entity.FinTaxRate{
		Name:        req.Name,
		Rate:        rate,
		Description: req.Description,
	}

	if req.IsEnabled != nil {
		entity.IsEnabled = *req.IsEnabled
	} else {
		entity.IsEnabled = true // 默认为 true
	}

	// 设置创建人
	userID, _ := s.GetUserIDFromContext(ctx)
	entity.CreatedBy = uint(userID)

	if err := s.repo.Create(ctx, entity); err != nil {
		return nil, err
	}

	return vo.FromTaxRateEntity(entity), nil
}

// Update 更新税率
func (s *finTaxRateServiceImpl) Update(ctx context.Context, req *dto.FinTaxRateUpdateReq) (*vo.FinTaxRateItemRes, error) {
	// 检查Name是否被其他税率占用
	existing, err := s.repo.FindByName(ctx, req.Name)
	if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询税率失败").WithCause(err)
	}
	if existing != nil && existing.ID != req.ID {
		return nil, apperrors.NewParamError(apperrors.CODE_DATA_ALREADY_EXISTS, fmt.Sprintf("税率名称 '%s' 已被其他税率占用", req.Name))
	}

	// 查找原始实体
	originalEntity, err := s.repo.FindByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	rate, err := decimal.NewFromString(req.Rate.String())
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "税率格式无效").WithCause(err)
	}

	entity := &entity.FinTaxRate{
		Name:        req.Name,
		Rate:        rate,
		Description: req.Description,
	}
	entity.ID = req.ID

	if req.IsEnabled != nil {
		entity.IsEnabled = *req.IsEnabled
	} else {
		// 如果不传递，则保留原始值
		entity.IsEnabled = originalEntity.IsEnabled
	}

	// 保留原始创建信息
	entity.CreatedAt = originalEntity.CreatedAt
	entity.CreatedBy = originalEntity.CreatedBy
	// 设置更新人
	userID, _ := s.GetUserIDFromContext(ctx)
	entity.UpdatedBy = uint(userID)

	if err := s.repo.Update(ctx, entity); err != nil {
		return nil, err
	}

	return vo.FromTaxRateEntity(entity), nil
}

// Delete 删除税率
func (s *finTaxRateServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetByID 根据ID获取税率
func (s *finTaxRateServiceImpl) GetByID(ctx context.Context, id uint) (*vo.FinTaxRateItemRes, error) {
	entity, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return vo.FromTaxRateEntity(entity), nil
}

// GetPage 分页获取税率
func (s *finTaxRateServiceImpl) GetPage(ctx context.Context, req *dto.FinTaxRatePageReq) (*response.PageResult, error) {
	var conditions []repository.QueryCondition
	if req.Name != "" {
		conditions = append(conditions, repository.NewLikeCondition("name", req.Name))
	}
	if req.IsEnabled != nil {
		conditions = append(conditions, repository.NewEqualCondition("is_enabled", *req.IsEnabled))
	}

	pageQuery := &response.PageQuery{
		PageNum:  req.Page,
		PageSize: req.PageSize,
	}

	pageResult, err := s.repo.FindByPage(ctx, pageQuery, conditions)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "分页查询税率失败").WithCause(err)
	}

	// 转换VO
	if pageResult.List != nil {
		entities, ok := pageResult.List.([]*entity.FinTaxRate)
		if ok {
			pageResult.List = vo.FromTaxRateEntities(entities)
		}
	}

	return pageResult, nil
}

// GetList 获取所有启用的税率列表
func (s *finTaxRateServiceImpl) GetList(ctx context.Context) ([]*vo.FinTaxRateItemRes, error) {
	conditions := []repository.QueryCondition{
		repository.NewEqualCondition("is_enabled", true),
	}
	sortInfos := []response.SortInfo{{Field: "name", Order: "asc"}}

	entities, err := s.repo.FindByCondition(ctx, conditions, sortInfos)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询启用税率列表失败").WithCause(err)
	}

	return vo.FromTaxRateEntities(entities), nil
}
