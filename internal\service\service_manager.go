package service

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"backend/internal/repository"
	"backend/pkg/config"
	"backend/pkg/logger"
	"backend/pkg/storage"

	"github.com/go-redis/redis/v8"
)

// ServiceManager 服务管理器
// 用于集中管理服务实例和事务处理
type ServiceManager struct {
	// 仓库管理器
	repoManager *repository.RepositoryManager

	// 服务缓存
	services sync.Map

	// 日志记录器
	logger logger.Logger

	// 上下文
	ctx context.Context

	// --- 缓存的依赖实例 ---
	redisClient *redis.Client // 缓存的 Redis 客户端实例
	redisOnce   sync.Once     // 用于确保 Redis 客户端只初始化一次

	// --- 其他字段 ---
	// userRoleRepo repository.UserRoleRepository

	// AuthService 依赖项
	appCfg *config.Configuration

	// systemParameterService SystemParameterService // <<< 移除此字段

	sqlDB *sql.DB // <<< 新增字段：存储原始 SQL DB 连接

	// 新增 storage 实例
	storage storage.Storage

	// 新增 license 服务
	// licenseService LicenseService

	// --- AuditLogService 字段暂时移除，因类型未定义 ---
	// auditLogService AuditLogService // backend/internal/service/audit_log_service.go 中需要定义此类型
	// --- 结束 AuditLogService 字段 ---
}

// NewServiceManager 创建服务管理器
// 参数:
//   - repoManager: 仓库管理器
//   - appCfg: 应用配置
//   - sqlDB: 原始 *sql.DB 连接 (新增)
//
// 返回:
//   - *ServiceManager: 服务管理器实例
func NewServiceManager(repoManager *repository.RepositoryManager, appCfg *config.Configuration, sqlDB *sql.DB, storage storage.Storage) *ServiceManager {
	if sqlDB == nil {
		logger.GetLogger().Warn("NewServiceManager: *sql.DB is nil, SQLToolService will not be available")
	}
	sm := &ServiceManager{
		repoManager: repoManager,
		services:    sync.Map{},
		logger:      logger.GetLogger(),
		appCfg:      appCfg,
		sqlDB:       sqlDB, // <<< 存储 sqlDB
		storage:     storage,
		// systemParameterService: nil, // <<< 移除初始化
	}

	// --- AuditLogService 初始化暂时移除，因类型未定义 ---
	// sm.logger.Warn("AuditLogService 初始化被跳过，因为它依赖于尚未定义的类型/方法 (AuditLogService, NewAuditLogService, GetAuditLogRepository)。请在相应文件中定义它们。")
	// --- 结束 AuditLogService 初始化 ---

	return sm
}

// WithContext 设置上下文
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - *ServiceManager: 服务管理器实例
func (m *ServiceManager) WithContext(ctx context.Context) *ServiceManager {
	if ctx == nil {
		ctx = context.Background()
	}

	// 创建新的 ServiceManager 实例，并复制依赖项
	// 注意: repoManager 实例保持不变，context 会在调用 repoManager 方法时传递
	newSM := &ServiceManager{
		repoManager: m.repoManager, // <<< repoManager 保持不变
		services:    sync.Map{},
		logger:      m.logger,
		ctx:         ctx,
		appCfg:      m.appCfg,
		sqlDB:       m.sqlDB,
		storage:     m.storage,
		// systemParameterService: m.systemParameterService, // <<< 移除复制
		// --- AuditLogService 复制暂时移除 ---
		// auditLogService: m.auditLogService,
		// --- 结束 AuditLogService 复制 ---
	}
	return newSM
}

// GetRepository 获取仓库
// 参数:
//   - factory: 仓库工厂函数
//
// 返回:
//   - R: 仓库实例
func GetRepository[R any](m *ServiceManager, factory func(*repository.RepositoryManager) R) R {
	return factory(m.repoManager)
}

// GetService 获取泛型服务
// 参数:
//   - factory: 服务工厂函数，接收 *ServiceManager 作为参数
//
// 返回:
//   - S: 服务实例
func GetService[S any](m *ServiceManager, factory func(*ServiceManager) S) S {
	// 使用类型名称作为缓存键
	typeName := fmt.Sprintf("%T", (*S)(nil))

	// 从缓存中获取
	if service, ok := m.services.Load(typeName); ok {
		return service.(S)
	}

	// 创建新实例并存储到缓存
	service, _ := m.services.LoadOrStore(typeName, factory(m))

	return service.(S)
}

// GetSQLDB 获取原始 SQL DB 连接
func (m *ServiceManager) GetSQLDB() *sql.DB {
	return m.sqlDB
}

// WithTransaction 在事务中执行函数
// 参数:
//   - fn: 在事务中执行的函数
//
// 返回:
//   - error: 错误信息
func (m *ServiceManager) WithTransaction(fn func(txServiceManager *ServiceManager) error) error {
	// 直接调用 repoManager 的 Transaction 方法，并传递上下文
	return m.repoManager.Transaction(m.ctx, func(txRepoManager *repository.RepositoryManager) error {
		// 创建事务版本的ServiceManager，并复制依赖项
		txServiceManager := &ServiceManager{
			repoManager: txRepoManager,
			services:    sync.Map{},
			logger:      m.logger,
			ctx:         m.ctx,
			appCfg:      m.appCfg,
			sqlDB:       m.sqlDB,
			storage:     m.storage,
			// systemParameterService: m.systemParameterService, // <<< 移除复制
			// --- AuditLogService 传递暂时移除 ---
			// auditLogService: m.auditLogService,
			// --- 结束 AuditLogService 传递 ---
		}

		// 执行事务函数
		err := fn(txServiceManager)
		if err != nil {
			// repoManager.Transaction 会处理回滚，这里只记录日志并返回错误
			m.logger.WithError(err).Error("服务事务执行失败")
			return err
		}

		return nil
	})
}

// WithTransactionByLevel 在指定隔离级别的事务中执行函数
// 参数:
//   - level: 事务隔离级别 (例如 "READ UNCOMMITTED", "READ COMMITTED", "REPEATABLE READ", "SERIALIZABLE")
//   - fn: 在事务中执行的函数
//
// 返回:
//   - error: 错误信息
func (m *ServiceManager) WithTransactionByLevel(level string, fn func(txServiceManager *ServiceManager) error) error {
	// 解析隔离级别字符串
	var isoLevel sql.IsolationLevel
	switch strings.ToUpper(level) {
	case "READ UNCOMMITTED":
		isoLevel = sql.LevelReadUncommitted
	case "READ COMMITTED":
		isoLevel = sql.LevelReadCommitted
	case "REPEATABLE READ":
		isoLevel = sql.LevelRepeatableRead
	case "SERIALIZABLE":
		isoLevel = sql.LevelSerializable
	default:
		m.logger.Warnf("无效的事务隔离级别 '%s'，将使用默认级别", level)
		isoLevel = sql.LevelDefault // 使用数据库默认
	}

	txOptions := &sql.TxOptions{Isolation: isoLevel}

	// 调用 repoManager 的 Transaction 方法，传递上下文和事务选项
	return m.repoManager.Transaction(m.ctx, func(txRepoManager *repository.RepositoryManager) error {
		// 创建事务版本的ServiceManager
		txServiceManager := &ServiceManager{
			repoManager: txRepoManager,
			services:    sync.Map{},
			logger:      m.logger,
			ctx:         m.ctx,
			appCfg:      m.appCfg,
			sqlDB:       m.sqlDB,
			storage:     m.storage,
			// systemParameterService: m.systemParameterService, // <<< 移除复制
			// --- AuditLogService 传递暂时移除 ---
			// auditLogService: m.auditLogService,
			// --- 结束 AuditLogService 传递 ---
		}

		// 执行事务函数
		err := fn(txServiceManager)
		if err != nil {
			m.logger.WithError(err).Error("服务事务执行失败")
			return err
		}

		return nil
	}, txOptions) // <<< 传递事务选项
}

// SetLogger 设置日志记录器
// 参数:
//   - logger: 日志记录器
//
// 返回:
//   - *ServiceManager: 服务管理器实例
func (m *ServiceManager) SetLogger(logger logger.Logger) *ServiceManager {
	m.logger = logger
	return m
}

// GetLogger 获取日志记录器
// 返回:
//   - logger.Logger: 日志记录器
func (m *ServiceManager) GetLogger() logger.Logger {
	if m.logger == nil {
		return logger.GetLogger()
	}
	return m.logger
}

// GetContext 获取上下文
// 返回:
//   - context.Context: 上下文
func (m *ServiceManager) GetContext() context.Context {
	return m.ctx
}

// GetRepositoryManager 获取仓库管理器
// 返回:
//   - *repository.RepositoryManager: 仓库管理器
func (m *ServiceManager) GetRepositoryManager() *repository.RepositoryManager {
	return m.repoManager
}

// GetAuthService 获取 AuthService
func (m *ServiceManager) GetAuthService() AuthService {
	return GetService(m, func(sm *ServiceManager) AuthService {
		// 获取依赖 CaptchaService
		captchaSvc := sm.GetCaptchaService() // GetCaptchaService 内部会处理创建和错误
		// 严格检查依赖是否获取成功
		if captchaSvc == nil {
			// 如果 CaptchaService 获取失败，AuthService 无法正常工作，直接 panic
			sm.logger.Error("创建 AuthService 失败: 无法获取依赖 CaptchaService")
			panic("AuthService critical dependency failed: CaptchaService")
		}
		// 依赖获取成功，创建 AuthService
		return NewAuthService(sm, sm.appCfg, captchaSvc)
	})
}

// GetTokenService 获取 Token 服务
func (m *ServiceManager) GetTokenService() TokenService {
	return GetService(m, func(sm *ServiceManager) TokenService {
		return NewTokenService(sm, sm.appCfg)
	})
}

// GetUserService 获取用户服务 (懒加载)
func (m *ServiceManager) GetUserService() UserService {
	return GetService(
		m,
		func(sm *ServiceManager) UserService {
			// 调用 UserService 的构造函数
			// 注意：NewUserService 依赖于多个其他服务
			// GetService 框架会自动处理这些依赖（如果它们也在 ServiceManager 中注册并可获取）
			return NewUserService(sm)
		})
}

// GetEmployeeService 获取员工服务
func (m *ServiceManager) GetEmployeeService() EmployeeService {
	return GetService(
		m,
		func(sm *ServiceManager) EmployeeService {
			return NewEmployeeService(sm) // 使用正确的构造函数名
		})
}

// FinCurrencyService 获取币种服务
func (m *ServiceManager) FinCurrencyService() FinCurrencyService {
	return GetService(m, func(sm *ServiceManager) FinCurrencyService {
		return NewFinCurrencyService(sm, sm.repoManager.GetFinCurrencyRepository())
	})
}

// FinTaxRateService 获取税率服务
func (m *ServiceManager) FinTaxRateService() FinTaxRateService {
	return GetService(m, func(sm *ServiceManager) FinTaxRateService {
		return NewFinTaxRateService(sm, sm.repoManager.GetFinTaxRateRepository())
	})
}

// FinExchangeRateService 获取汇率服务
func (m *ServiceManager) FinExchangeRateService() FinExchangeRateService {
	return GetService(m, func(sm *ServiceManager) FinExchangeRateService {
		return NewFinExchangeRateService(sm)
	})
}

// GetRoleService 获取角色服务 (新增)
func (m *ServiceManager) GetRoleService() RoleService {
	return GetService(
		m,
		func(sm *ServiceManager) RoleService {
			// 假设 NewRoleService 存在且接收 *ServiceManager 作为参数
			return NewRoleService(sm)
		},
	)
}

// GetMenuService 获取菜单服务 (新增)
func (m *ServiceManager) GetMenuService() MenuService {
	return GetService(
		m,
		func(sm *ServiceManager) MenuService {
			// 假设 NewMenuService 存在且接收 *ServiceManager 作为参数
			return NewMenuService(sm)
		},
	)
}

// GetCaptchaService 获取 CaptchaService (延迟加载并缓存)
func (m *ServiceManager) GetCaptchaService() CaptchaService {
	return GetService(m, func(sm *ServiceManager) CaptchaService {
		logger := sm.GetLogger()
		cfg := sm.GetConfig()
		cacheSvc := sm.GetCacheService() // CacheService 内部处理其自身的依赖，包括可能的 Redis

		// 即使 CacheService 内部可能不依赖 Redis，但 NewCaptchaService 的签名需要它。
		// 如果 CacheService 为 nil，可能意味着关键的缓存基础设施未准备好。
		if cacheSvc == nil {
			logger.Error("创建 CaptchaService 失败: 无法获取依赖 CacheService")
			// 根据 NewCaptchaService 能否处理 nil CacheService 或业务需求决定行为
			// panic("CaptchaService critical dependency failed: CacheService")
			return nil // 或者返回一个特定的、表示服务不可用的 CaptchaService 实现
		}

		// 假设 NewCaptchaService 返回 (CaptchaService, error)
		captchaInstance, err := NewCaptchaService(logger, cfg, cacheSvc)
		if err != nil {
			logger.WithError(err).Error("创建 CaptchaService 实例失败")
			// panic(fmt.Sprintf("Failed to create CaptchaService: %v", err))
			return nil // 或者返回一个特定的、表示服务不可用的 CaptchaService 实现
		}
		return captchaInstance
	})
}

// GetCacheService 获取 CacheService (示例)
func (m *ServiceManager) GetCacheService() CacheService {
	return GetService(m, func(sm *ServiceManager) CacheService {
		// 获取依赖：Redis 客户端 (可能是 nil)
		redisClient := sm.GetRedisClient() // <<< 调用 GetRedisClient 方法
		// 注意：无论 redisClient 是否为 nil，都应该调用 NewCacheService，
		// 因为 NewCacheService 内部会根据配置和 redisClient 是否为 nil 来决定具体行为。
		if redisClient == nil {
			// 即使 Redis 客户端为 nil，也可能配置了使用内存缓存，
			// 记录一个警告可能更合适，而不是直接失败。
			sm.logger.Warn("GetRedisClient 返回了 nil，CacheService 将无法使用 Redis 功能")
			// 继续执行，因为 CacheService 可能配置为仅使用内存
		}
		// 调用 NewCacheService，传入可能为 nil 的 redisClient
		cs := NewCacheService(sm, redisClient, sm.appCfg)
		if cs == nil { // 检查 NewCacheService 是否因为其他原因失败
			sm.logger.Error("NewCacheService 返回了 nil")
			return nil
		}
		return cs
	})
}

// GetRedisClient 获取 Redis 客户端实例 (线程安全，带缓存和初始化逻辑)
// 如果 Redis 未配置或连接失败，则返回 nil。
func (m *ServiceManager) GetRedisClient() *redis.Client {
	m.redisOnce.Do(func() {
		cfg := m.appCfg // 从 ServiceManager 获取应用配置
		if cfg == nil { // 首先检查 cfg 是否为 nil
			m.logger.Warn("Application configuration (appCfg) is nil. Redis client will not be initialized.")
			m.redisClient = nil
			return
		}

		// 检查 Redis Host 和 Port 是否配置 (作为启用的判断依据)
		// 这是基于 pkg/config/config.go 中的 RedisConfig 结构
		if cfg.Redis.Host == "" || cfg.Redis.Port == 0 {
			m.logger.Info("Redis is not configured (Host or Port is missing/zero). Redis client will not be initialized.")
			m.redisClient = nil
			return
		}

		redisAddr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)

		clientOptions := &redis.Options{
			Addr:         redisAddr,
			Password:     cfg.Redis.Password,
			DB:           cfg.Redis.DB,
			PoolSize:     cfg.Redis.PoolSize,
			MinIdleConns: cfg.Redis.MinIdleConns,
		}
		// 根据配置设置超时
		if cfg.Redis.DialTimeout > 0 {
			clientOptions.DialTimeout = time.Duration(cfg.Redis.DialTimeout) * time.Second
		}
		if cfg.Redis.ReadTimeout > 0 {
			clientOptions.ReadTimeout = time.Duration(cfg.Redis.ReadTimeout) * time.Second
		}
		if cfg.Redis.WriteTimeout > 0 {
			clientOptions.WriteTimeout = time.Duration(cfg.Redis.WriteTimeout) * time.Second
		}

		client := redis.NewClient(clientOptions)

		// Ping Redis 服务器以验证连接
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := client.Ping(ctx).Err(); err != nil {
			m.logger.WithError(err).Error("无法连接到 Redis", logger.WithField("address", redisAddr))
			m.redisClient = nil // 连接失败，设置为 nil
			return
		}
		m.logger.Info("成功连接到 Redis", logger.WithField("address", redisAddr))
		m.redisClient = client
	})
	return m.redisClient
}

// SetCaptchaService 设置并注册 CaptchaService (通常内部使用)
// 注意：这个方法现在与 GetCaptchaService 的延迟加载逻辑可能冲突
// 考虑是否真的需要外部设置，或者仅依赖 GetCaptchaService 的内部创建
func (m *ServiceManager) SetCaptchaService(captchaSvc CaptchaService) {
	if captchaSvc != nil {
		// 如果希望覆盖缓存，可以使用 Store
		typeName := fmt.Sprintf("%T", (*CaptchaService)(nil))
		m.services.Store(typeName, captchaSvc)
	}
}

// GetFileService retrieves the FileService instance.
func (m *ServiceManager) GetFileService() FileService {
	return GetService(m, func(sm *ServiceManager) FileService {
		// 确保传入的 storage 实例不为 nil
		if sm.storage == nil {
			sm.logger.Error("创建 FileService 失败: Storage 实例为 nil")
			panic("FileService critical dependency failed: Storage is nil")
		}
		// 依赖获取成功，创建 FileService
		// 获取其他依赖
		cfg := sm.GetConfig()
		repoMgr := sm.GetRepositoryManager()
		metadataRepo := repoMgr.GetFileMetadataRepository() // 从 repoManager 获取
		log := sm.GetLogger()

		// 检查 FileMetadataRepository 是否获取成功
		if metadataRepo == nil {
			sm.logger.Error("创建 FileService 失败: 无法获取 FileMetadataRepository")
			panic("FileService critical dependency failed: FileMetadataRepository")
		}

		return NewFileService(cfg, sm.storage, metadataRepo, repoMgr, log)
	})
}

// GetLicenseService 获取 LicenseService (延迟加载并缓存)
func (m *ServiceManager) GetLicenseService() LicenseService {
	// 1. 检查 sync.Map 缓存 (移除了对 m.licenseService 的检查)
	typeName := fmt.Sprintf("%T", (*LicenseService)(nil))
	if svc, ok := m.services.Load(typeName); ok {
		// 缓存命中，直接返回
		return svc.(LicenseService)
	}

	// 2. 缓存未命中，创建新实例
	m.logger.Info("创建 LicenseService 实例")
	newInstance := NewLicenseService(m) // 假设 NewLicenseService 只需要 ServiceManager

	// 3. 存入 sync.Map 缓存
	actualSvc, _ := m.services.LoadOrStore(typeName, newInstance)

	// 4. 返回实际存入缓存的实例
	return actualSvc.(LicenseService)
}

// GetSequenceService 获取序列号服务
func (m *ServiceManager) GetSequenceService() SequenceService {
	return GetService(m, func(sm *ServiceManager) SequenceService {
		// NewSequenceService 位于 sequence_service.go
		// 它依赖 CacheService，我们需要先获取 CacheService
		// 注意：这里假设 CacheService 总是可用的，或者 NewSequenceService 内部会处理 CacheService 为 nil 的情况
		return NewSequenceService(sm) // 直接传递 ServiceManager，NewSequenceService 内部获取 CacheService
	})
}

// GetConfig 获取应用配置 (新增)
// 返回:
//   - *config.Configuration: 应用配置实例
func (m *ServiceManager) GetConfig() *config.Configuration {
	return m.appCfg
}

// GetAccountBookService 获取账套服务 (懒加载)
func (m *ServiceManager) GetAccountBookService() AccountBookService {
	return GetService(
		m,
		func(sm *ServiceManager) AccountBookService {
			// 调用 AccountBookService 的构造函数
			return NewAccountBookService(sm)
		},
	)
}

// GetUserAccountBookService 获取用户账套服务 (懒加载)
func (m *ServiceManager) GetUserAccountBookService() UserAccountBookService {
	return GetService(
		m,
		func(sm *ServiceManager) UserAccountBookService {
			// 调用 UserAccountBookService 的构造函数
			return NewUserAccountBookService(sm)
		},
	)
}

// GetSystemParameterService 获取系统参数服务
func (m *ServiceManager) GetSystemParameterService() SystemParameterService {
	return GetService(
		m,
		func(sm *ServiceManager) SystemParameterService {
			return NewSystemParameterService(sm) // 使用正确的构造函数名
		},
	)
}

// GetDictionaryService 获取字典服务
func (m *ServiceManager) GetDictionaryService() DictionaryService {
	return GetService(
		m,
		func(sm *ServiceManager) DictionaryService {
			return NewDictionaryService(sm) // 使用正确的构造函数名
		},
	)
}

// GetOrganizationNodeService 获取组织节点服务
func (m *ServiceManager) GetOrganizationNodeService() OrganizationNodeService {
	return GetService(
		m,
		func(sm *ServiceManager) OrganizationNodeService {
			return NewOrganizationNodeService(sm) // 使用正确的构造函数名
		},
	)
}

// GetFiscalPeriodService 获取会计期间服务 (懒加载)
func (m *ServiceManager) GetFiscalPeriodService() FiscalPeriodService {
	return GetService(
		m,
		func(sm *ServiceManager) FiscalPeriodService {
			return NewFiscalPeriodService(sm)
		},
	)
}

// GetWmsLocationService 获取 WMS 位置服务
func (m *ServiceManager) GetWmsLocationService() WmsLocationService {
	return GetService(
		m,
		func(sm *ServiceManager) WmsLocationService {
			return NewWmsLocationService(sm)
		},
	)
}

// GetMtlItemService 获取物料服务
func (m *ServiceManager) GetMtlItemService() MtlItemService {
	return GetService(
		m,
		func(sm *ServiceManager) MtlItemService {
			return NewMtlItemService(sm)
		},
	)
}

// GetCodeGenerationService 获取编码生成服务
func (m *ServiceManager) GetCodeGenerationService() CodeGenerationService {
	return GetService(
		m,
		func(sm *ServiceManager) CodeGenerationService {
			return NewCodeGenerationService(sm)
		},
	)
}

// GetSysCodeRuleService 获取编码规则管理服务
func (m *ServiceManager) GetSysCodeRuleService() SysCodeRuleService {
	return GetService(
		m,
		func(sm *ServiceManager) SysCodeRuleService {
			return NewSysCodeRuleService(sm)
		},
	)
}

// GetCrmCustomerService 获取CRM客户服务
func (m *ServiceManager) GetCrmCustomerService() CrmCustomerService {
	return GetService(
		m,
		func(sm *ServiceManager) CrmCustomerService {
			return NewCrmCustomerService(sm)
		},
	)
}

// GetScmSupplierService 获取SCM供应商服务
func (m *ServiceManager) GetScmSupplierService() ScmSupplierService {
	return GetService(
		m,
		func(sm *ServiceManager) ScmSupplierService {
			return NewScmSupplierService(sm)
		},
	)
}

// GetWmsInboundNotificationService 获取入库通知单服务
func (m *ServiceManager) GetWmsInboundNotificationService() WmsInboundNotificationService {
	return GetService(
		m,
		func(sm *ServiceManager) WmsInboundNotificationService {
			return NewWmsInboundNotificationService(sm)
		},
	)
}

// GetWmsReceivingRecordService 获取收货记录服务
func (m *ServiceManager) GetWmsReceivingRecordService() WmsReceivingRecordService {
	return GetService(
		m,
		func(sm *ServiceManager) WmsReceivingRecordService {
			return NewWmsReceivingRecordService(sm)
		},
	)
}

// GetWmsPutawayTaskService 获取上架任务服务
func (m *ServiceManager) GetWmsPutawayTaskService() WmsPutawayTaskService {
	return GetService(
		m,
		func(sm *ServiceManager) WmsPutawayTaskService {
			return NewWmsPutawayTaskService(sm)
		},
	)
}

// GetWmsPutawayTaskDetailService 获取上架任务明细服务
func (m *ServiceManager) GetWmsPutawayTaskDetailService() WmsPutawayTaskDetailService {
	return GetService(
		m,
		func(sm *ServiceManager) WmsPutawayTaskDetailService {
			return NewWmsPutawayTaskDetailService(sm)
		},
	)
}

// GetWmsBlindReceivingConfigService 获取盲收配置服务
func (m *ServiceManager) GetWmsBlindReceivingConfigService() WmsBlindReceivingConfigService {
	return GetService(m, func(sm *ServiceManager) WmsBlindReceivingConfigService {
		return NewWmsBlindReceivingConfigService(sm)
	})
}

// GetWmsBlindReceivingValidationService 获取盲收验证记录服务
func (m *ServiceManager) GetWmsBlindReceivingValidationService() WmsBlindReceivingValidationService {
	return GetService(m, func(sm *ServiceManager) WmsBlindReceivingValidationService {
		return NewWmsBlindReceivingValidationService(sm)
	})
}

// ==================== 出库流程模块 Service 获取方法 ====================

// GetWmsOutboundNotificationService 获取出库通知单服务
func (m *ServiceManager) GetWmsOutboundNotificationService() WmsOutboundNotificationService {
	return GetService(m, func(sm *ServiceManager) WmsOutboundNotificationService {
		return NewWmsOutboundNotificationService(sm)
	})
}

// GetWmsPickingTaskService 获取拣货任务服务
func (m *ServiceManager) GetWmsPickingTaskService() WmsPickingTaskService {
	return GetService(m, func(sm *ServiceManager) WmsPickingTaskService {
		return NewWmsPickingTaskService(sm)
	})
}

// GetWmsInventoryAllocationService 获取库存分配服务
func (m *ServiceManager) GetWmsInventoryAllocationService() WmsInventoryAllocationService {
	return GetService(m, func(sm *ServiceManager) WmsInventoryAllocationService {
		return NewWmsInventoryAllocationService(sm)
	})
}

// GetWmsShipmentService 获取发运单服务
func (m *ServiceManager) GetWmsShipmentService() WmsShipmentService {
	return GetService(m, func(sm *ServiceManager) WmsShipmentService {
		return NewWmsShipmentService(sm)
	})
}

// ===== 库存管理模块服务 =====

// GetWmsInventoryQueryService 获取库存查询服务
func (m *ServiceManager) GetWmsInventoryQueryService() WmsInventoryQueryService {
	return GetService(m, func(sm *ServiceManager) WmsInventoryQueryService {
		return NewWmsInventoryQueryService(sm)
	})
}

// GetWmsInventoryAdjustmentService 获取库存调整服务
func (m *ServiceManager) GetWmsInventoryAdjustmentService() WmsInventoryAdjustmentService {
	return GetService(m, func(sm *ServiceManager) WmsInventoryAdjustmentService {
		return NewWmsInventoryAdjustmentService(sm)
	})
}

// GetWmsInventoryMovementService 获取库存移动服务
func (m *ServiceManager) GetWmsInventoryMovementService() WmsInventoryMovementService {
	return GetService(m, func(sm *ServiceManager) WmsInventoryMovementService {
		return NewWmsInventoryMovementService(sm)
	})
}

// GetWmsCycleCountService 获取盘点管理服务
func (m *ServiceManager) GetWmsCycleCountService() WmsCycleCountService {
	return GetService(m, func(sm *ServiceManager) WmsCycleCountService {
		return NewWmsCycleCountService(sm)
	})
}

// GetWmsInventoryAlertService 获取库存预警服务
func (m *ServiceManager) GetWmsInventoryAlertService() WmsInventoryAlertService {
	return GetService(m, func(sm *ServiceManager) WmsInventoryAlertService {
		return NewWmsInventoryAlertService(sm)
	})
}

// GetNotificationService 获取通知服务
func (m *ServiceManager) GetNotificationService() NotificationService {
	return GetService(m, func(sm *ServiceManager) NotificationService {
		return NewNotificationService(sm)
	})
}

// GetWmsInventoryService 获取基础库存管理服务
func (m *ServiceManager) GetWmsInventoryService() WmsInventoryService {
	return GetService(m, func(sm *ServiceManager) WmsInventoryService {
		return NewWmsInventoryService(sm)
	})
}

// GetWmsInventoryTransactionLogService 获取库存事务日志服务
func (m *ServiceManager) GetWmsInventoryTransactionLogService() WmsInventoryTransactionLogService {
	return GetService(m, func(sm *ServiceManager) WmsInventoryTransactionLogService {
		return NewWmsInventoryTransactionLogService(sm)
	})
}
