package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsReceivingRecordRepository 定义收货记录仓库接口
type WmsReceivingRecordRepository interface {
	BaseRepository[entity.WmsReceivingRecord, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsReceivingRecordQueryReq) (*response.PageResult, error)

	// 根据收货单号查找
	FindByReceivingNo(ctx context.Context, receivingNo string) (*entity.WmsReceivingRecord, error)

	// 检查收货单号是否存在
	IsReceivingNoExist(ctx context.Context, receivingNo string, excludeID uint) (bool, error)

	// 根据通知单ID查询收货记录
	FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsReceivingRecord, error)

	// 获取盲收记录（NotificationID为空的记录）
	GetBlindReceivingRecords(ctx context.Context, warehouseID uint) ([]*entity.WmsReceivingRecord, error)

	// 获取待检验的收货记录
	GetPendingInspection(ctx context.Context, warehouseID uint) ([]*entity.WmsReceivingRecord, error)

	// 获取指定状态的收货记录
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsReceivingRecord, error)

	// 根据客户ID查询
	GetByClientID(ctx context.Context, clientID uint) ([]*entity.WmsReceivingRecord, error)

	// 根据日期范围查询
	GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsReceivingRecord, error)

	// 更新状态
	UpdateStatus(ctx context.Context, id uint, status string, remark string) error

	// 获取待上架的收货记录
	GetPendingPutaway(ctx context.Context, warehouseID uint) ([]*entity.WmsReceivingRecord, error)
}

// wmsReceivingRecordRepository 收货记录仓库实现
type wmsReceivingRecordRepository struct {
	BaseRepositoryImpl[entity.WmsReceivingRecord, uint]
}

// NewWmsReceivingRecordRepository 创建收货记录仓库
func NewWmsReceivingRecordRepository(db *gorm.DB) WmsReceivingRecordRepository {
	return &wmsReceivingRecordRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsReceivingRecord, uint]{
			db: db,
		},
	}
}

// GetPage 获取收货记录分页数据
func (r *wmsReceivingRecordRepository) GetPage(ctx context.Context, query *dto.WmsReceivingRecordQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.ReceivingNo != nil && *query.ReceivingNo != "" {
		conditions = append(conditions, NewLikeCondition("receiving_no", *query.ReceivingNo))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.ClientID != nil {
		conditions = append(conditions, NewEqualCondition("client_id", *query.ClientID))
	}
	if query.WarehouseID != nil {
		conditions = append(conditions, NewEqualCondition("warehouse_id", *query.WarehouseID))
	}
	if query.ReceivedAtFrom != nil && query.ReceivedAtTo != nil {
		conditions = append(conditions, NewBetweenCondition("actual_arrival_date", *query.ReceivedAtFrom, *query.ReceivedAtTo))
	}

	return r.FindByPage(ctx, &query.Pagination, conditions)
}

// FindByReceivingNo 根据收货单号查找
func (r *wmsReceivingRecordRepository) FindByReceivingNo(ctx context.Context, receivingNo string) (*entity.WmsReceivingRecord, error) {
	var record entity.WmsReceivingRecord
	db := r.GetDB(ctx)

	// 预加载关联的明细
	result := db.Preload("Details").Where("receiving_no = ?", receivingNo).First(&record)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "收货记录不存在").WithCause(result.Error)
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据收货单号查询失败: receivingNo=%s", receivingNo)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询收货记录失败").WithCause(result.Error)
	}

	return &record, nil
}

// IsReceivingNoExist 检查收货单号是否存在
func (r *wmsReceivingRecordRepository) IsReceivingNoExist(ctx context.Context, receivingNo string, excludeID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("receiving_no", receivingNo),
	}
	if excludeID > 0 {
		conditions = append(conditions, NewNotEqualCondition("id", excludeID))
	}
	return r.Exists(ctx, conditions)
}

// FindByNotificationID 根据通知单ID查询收货记录
func (r *wmsReceivingRecordRepository) FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsReceivingRecord, error) {
	conditions := []QueryCondition{
		NewEqualCondition("notification_id", notificationID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetBlindReceivingRecords 获取盲收记录（NotificationID为空的记录）
func (r *wmsReceivingRecordRepository) GetBlindReceivingRecords(ctx context.Context, warehouseID uint) ([]*entity.WmsReceivingRecord, error) {
	var records []*entity.WmsReceivingRecord
	db := r.GetDB(ctx)

	// 查询盲收记录：notification_id 为 NULL
	result := db.Where("notification_id IS NULL").
		Where("warehouse_id = ?", warehouseID).
		Where("status NOT IN ?", []string{"CLOSED", "CANCELLED"}).
		Order("created_at desc").
		Find(&records)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询盲收记录失败: warehouseID=%d", warehouseID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询盲收记录失败").WithCause(result.Error)
	}

	return records, nil
}

// GetPendingInspection 获取待检验的收货记录
func (r *wmsReceivingRecordRepository) GetPendingInspection(ctx context.Context, warehouseID uint) ([]*entity.WmsReceivingRecord, error) {
	var records []*entity.WmsReceivingRecord
	db := r.GetDB(ctx)

	// 查询待检验的记录
	result := db.Where("warehouse_id = ?", warehouseID).
		Where("status IN ?", []string{"PENDING_INSPECTION", "INSPECTING"}).
		Order("actual_arrival_date asc").
		Find(&records)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询待检验记录失败: warehouseID=%d", warehouseID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询待检验记录失败").WithCause(result.Error)
	}

	return records, nil
}

// GetByStatus 获取指定状态的收货记录
func (r *wmsReceivingRecordRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsReceivingRecord, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "actual_arrival_date", Order: "asc"},
		{Field: "receiving_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByClientID 根据客户ID查询
func (r *wmsReceivingRecordRepository) GetByClientID(ctx context.Context, clientID uint) ([]*entity.WmsReceivingRecord, error) {
	conditions := []QueryCondition{
		NewEqualCondition("client_id", clientID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByDateRange 根据日期范围查询
func (r *wmsReceivingRecordRepository) GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsReceivingRecord, error) {
	conditions := []QueryCondition{
		NewBetweenCondition("actual_arrival_date", startDate, endDate),
	}
	sortInfos := []response.SortInfo{
		{Field: "actual_arrival_date", Order: "asc"},
		{Field: "receiving_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// UpdateStatus 更新收货记录状态
func (r *wmsReceivingRecordRepository) UpdateStatus(ctx context.Context, id uint, status string, remark string) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsReceivingRecord{}).Where("id = ?", id).Updates(map[string]interface{}{"status": status, "remark": remark})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新收货记录状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新收货记录状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "收货记录不存在")
	}

	return nil
}

// GetPendingPutaway 获取待上架的收货记录
func (r *wmsReceivingRecordRepository) GetPendingPutaway(ctx context.Context, warehouseID uint) ([]*entity.WmsReceivingRecord, error) {
	var records []*entity.WmsReceivingRecord
	db := r.GetDB(ctx)

	// 查询完成收货且等待上架的记录
	result := db.Where("warehouse_id = ?", warehouseID).
		Where("status IN ?", []string{"COMPLETED", "PARTIALLY_COMPLETED"}).
		Order("actual_arrival_date asc").
		Find(&records)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询待上架记录失败: warehouseID=%d", warehouseID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询待上架记录失败").WithCause(result.Error)
	}

	return records, nil
}
