# WMS 库存管理模块完整执行计划

## 📋 项目概述

**目标**：基于现有WMS项目架构，设计并实现一套专业完整的库存管理模块，包括库存查询、库存调整、库存移动、盘点管理、库存预警等核心功能。

**技术架构**：Go + Iris + GORM + Vue 3 + TypeScript + Element Plus + Pinia
**设计原则**：基于现有入库/出库流程模块的成功实践，确保架构一致性和代码复用性

## 🎯 功能模块规划

### 核心功能模块

#### 1. **库存查询管理** 📊
- 实时库存查询（多维度筛选）
- 库存明细查看（批次、序列号、状态）
- 库存汇总统计（按物料、库位、客户）
- 库存历史追踪（变动记录）
- 库存可用性检查

#### 2. **库存调整管理** ⚖️
- 库存数量调整（增加/减少）
- 库存状态调整（可用/冻结/损坏等）
- 批量库存调整
- 调整原因管理
- 调整审批流程

#### 3. **库存移动管理** 🔄
- 库位间移动
- 状态变更移动
- 批量移动操作
- 移动任务管理
- 移动历史记录

#### 4. **盘点管理** 📝
- 盘点计划制定
- 盘点任务执行
- 盘点差异处理
- 循环盘点管理
- 盘点报告生成

#### 5. **库存预警管理** ⚠️
- 库存上下限预警
- 过期预警管理
- 呆滞库存预警
- 安全库存监控
- 预警规则配置

#### 6. **库存分析报表** 📈
- 库存周转分析
- ABC分类分析
- 库存成本分析
- 库存趋势分析
- 自定义报表

## 🏗️ 技术架构设计

### 后端架构 (Go + Iris + GORM)

#### 数据库设计
基于现有的库存相关表结构：
- `wms_inventory` - 库存主表（已存在）
- `wms_inventory_transaction_log` - 库存事务日志（已存在）
- `wms_inventory_adjustment` - 库存调整记录（新增）
- `wms_inventory_movement` - 库存移动记录（新增）
- `wms_cycle_count_plan` - 盘点计划（新增）
- `wms_cycle_count_task` - 盘点任务（新增）
- `wms_inventory_alert_rule` - 预警规则（新增）
- `wms_inventory_alert_log` - 预警日志（新增）

#### 服务层设计
```
internal/
├── model/
│   ├── entity/          # 实体定义
│   ├── dto/             # 数据传输对象
│   └── vo/              # 视图对象
├── repository/          # 数据访问层
├── service/             # 业务逻辑层
└── controller/          # 控制器层
```

### 前端架构 (Vue 3 + TypeScript)

#### 页面结构
```
frontend/src/views/wms/inventory/
├── query/               # 库存查询
│   ├── index.vue        # 库存查询主页面
│   └── components/      # 查询相关组件
├── adjustment/          # 库存调整
│   ├── index.vue        # 调整管理主页面
│   └── components/      # 调整相关组件
├── movement/            # 库存移动
│   ├── index.vue        # 移动管理主页面
│   └── components/      # 移动相关组件
├── cycle-count/         # 盘点管理
│   ├── index.vue        # 盘点主页面
│   ├── plan.vue         # 盘点计划
│   ├── task.vue         # 盘点任务
│   └── components/      # 盘点相关组件
├── alert/               # 库存预警
│   ├── index.vue        # 预警管理主页面
│   └── components/      # 预警相关组件
└── report/              # 库存报表
    ├── index.vue        # 报表主页面
    └── components/      # 报表相关组件
```

## 📅 实施计划

### 第一阶段：基础架构搭建 (2周)

#### 1.1 后端基础架构 (1周)
**目标**：完成数据库设计和基础实体定义

**任务清单**：
- [ ] 设计新增数据库表结构
- [ ] 创建实体模型 (Entity)
- [ ] 定义DTO和VO对象
- [ ] 实现Repository层
- [ ] 配置数据库迁移

**交付物**：
- 完整的数据库表结构
- 实体模型定义
- 基础的CRUD Repository

#### 1.2 前端基础架构 (1周)
**目标**：搭建前端项目结构和基础组件

**任务清单**：
- [ ] 创建页面目录结构
- [ ] 定义TypeScript类型
- [ ] 创建API接口层
- [ ] 实现Pinia状态管理
- [ ] 配置路由结构

**交付物**：
- 完整的前端项目结构
- TypeScript类型定义
- 基础的API和Store

### 第二阶段：核心功能实现 (4周)

#### 2.1 库存查询模块 (1周)
**目标**：实现库存查询和展示功能

**后端任务**：
- [ ] 实现库存查询Service
- [ ] 创建库存查询Controller
- [ ] 实现多维度筛选逻辑
- [ ] 添加库存统计功能

**前端任务**：
- [ ] 创建库存查询主页面
- [ ] 实现高级搜索组件
- [ ] 创建库存详情组件
- [ ] 实现库存统计图表

**验收标准**：
- 支持按物料、库位、批次等多维度查询
- 实时显示库存数量和状态
- 提供库存汇总统计
- 支持导出功能

#### 2.2 库存调整模块 (1周)
**目标**：实现库存调整功能

**后端任务**：
- [ ] 实现库存调整Service
- [ ] 创建调整记录管理
- [ ] 实现调整审批流程
- [ ] 添加调整原因管理

**前端任务**：
- [ ] 创建库存调整主页面
- [ ] 实现调整表单组件
- [ ] 创建批量调整功能
- [ ] 实现调整审批界面

**验收标准**：
- 支持单个和批量库存调整
- 记录调整原因和操作人
- 支持调整审批流程
- 自动更新库存事务日志

#### 2.3 库存移动模块 (1周)
**目标**：实现库存移动功能

**后端任务**：
- [ ] 实现库存移动Service
- [ ] 创建移动任务管理
- [ ] 实现移动验证逻辑
- [ ] 添加移动历史记录

**前端任务**：
- [ ] 创建库存移动主页面
- [ ] 实现移动任务组件
- [ ] 创建库位选择器
- [ ] 实现移动历史查看

**验收标准**：
- 支持库位间库存移动
- 验证移动的合法性
- 记录移动历史
- 支持移动任务管理

#### 2.4 盘点管理模块 (1周)
**目标**：实现盘点计划和执行功能

**后端任务**：
- [ ] 实现盘点计划Service
- [ ] 创建盘点任务管理
- [ ] 实现差异处理逻辑
- [ ] 添加盘点报告生成

**前端任务**：
- [ ] 创建盘点计划页面
- [ ] 实现盘点任务执行
- [ ] 创建差异处理界面
- [ ] 实现盘点报告展示

**验收标准**：
- 支持盘点计划制定
- 实现盘点任务执行
- 自动计算盘点差异
- 生成盘点报告

### 第三阶段：高级功能实现 (2周)

#### 3.1 库存预警模块 (1周)
**目标**：实现库存预警和监控功能

**后端任务**：
- [ ] 实现预警规则引擎
- [ ] 创建预警任务调度
- [ ] 实现预警通知机制
- [ ] 添加预警历史记录

**前端任务**：
- [ ] 创建预警规则配置
- [ ] 实现预警监控面板
- [ ] 创建预警处理界面
- [ ] 实现预警统计图表

**验收标准**：
- 支持多种预警规则配置
- 实时监控库存状态
- 自动发送预警通知
- 提供预警处理跟踪

#### 3.2 库存报表模块 (1周)
**目标**：实现库存分析和报表功能

**后端任务**：
- [ ] 实现报表数据Service
- [ ] 创建统计分析逻辑
- [ ] 实现报表导出功能
- [ ] 添加自定义报表支持

**前端任务**：
- [ ] 创建报表展示页面
- [ ] 实现图表组件集成
- [ ] 创建报表配置界面
- [ ] 实现报表导出功能

**验收标准**：
- 提供多种库存分析报表
- 支持图表可视化展示
- 支持报表导出
- 支持自定义报表配置

### 第四阶段：集成测试和优化 (1周)

#### 4.1 系统集成测试
**目标**：确保各模块间的协调工作

**任务清单**：
- [ ] 端到端功能测试
- [ ] 性能压力测试
- [ ] 数据一致性测试
- [ ] 用户体验测试

#### 4.2 系统优化
**目标**：优化系统性能和用户体验

**任务清单**：
- [ ] 数据库查询优化
- [ ] 前端性能优化
- [ ] 缓存策略优化
- [ ] 用户界面优化

## 🔧 技术实现要点

### 数据一致性保证
- 使用数据库事务确保库存操作的原子性
- 实现乐观锁防止并发冲突
- 建立完整的库存事务日志

### 性能优化策略
- 实现库存数据缓存机制
- 使用数据库索引优化查询
- 实现分页和懒加载
- 采用异步处理提升响应速度

### 安全性考虑
- 实现操作权限控制
- 添加数据访问审计
- 实现敏感操作二次确认
- 建立数据备份机制

## 📊 预期成果

### 功能完整性
- 覆盖库存管理全生命周期
- 支持多种业务场景
- 提供丰富的查询和分析功能
- 具备完善的预警机制

### 技术先进性
- 采用现代化技术栈
- 具备良好的扩展性
- 支持高并发访问
- 提供优秀的用户体验

### 业务价值
- 提升库存管理效率
- 降低库存管理成本
- 提高库存数据准确性
- 支持业务决策分析

## 🚀 项目里程碑

| 阶段 | 时间 | 主要交付物 | 完成标准 |
|------|------|------------|----------|
| 第一阶段 | 2周 | 基础架构 | 数据库和基础框架完成 |
| 第二阶段 | 4周 | 核心功能 | 主要业务功能可用 |
| 第三阶段 | 2周 | 高级功能 | 预警和报表功能完成 |
| 第四阶段 | 1周 | 系统优化 | 系统稳定可上线 |

**总计**：9周完成整个库存管理模块的开发

## 📋 详细实施指南

### 数据库表结构设计

#### 新增表结构

**1. 库存调整记录表 (wms_inventory_adjustment)**
```sql
CREATE TABLE wms_inventory_adjustment (
    id BIGSERIAL PRIMARY KEY,
    account_book_id BIGINT NOT NULL,
    adjustment_no VARCHAR(50) NOT NULL,
    inventory_id BIGINT NOT NULL,
    adjustment_type VARCHAR(20) NOT NULL, -- 'INCREASE', 'DECREASE', 'STATUS_CHANGE'
    quantity_before NUMERIC(12,4) NOT NULL,
    quantity_after NUMERIC(12,4) NOT NULL,
    quantity_change NUMERIC(12,4) NOT NULL,
    status_before VARCHAR(20),
    status_after VARCHAR(20),
    reason_code VARCHAR(50),
    reason_description TEXT,
    approval_status VARCHAR(20) DEFAULT 'PENDING',
    approved_by BIGINT,
    approved_at TIMESTAMP,
    operator_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    UNIQUE(account_book_id, adjustment_no)
);
```

**2. 库存移动记录表 (wms_inventory_movement)**
```sql
CREATE TABLE wms_inventory_movement (
    id BIGSERIAL PRIMARY KEY,
    account_book_id BIGINT NOT NULL,
    movement_no VARCHAR(50) NOT NULL,
    item_id BIGINT NOT NULL,
    from_location_id BIGINT NOT NULL,
    to_location_id BIGINT NOT NULL,
    batch_no VARCHAR(100),
    quantity NUMERIC(12,4) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    movement_type VARCHAR(20) NOT NULL, -- 'MANUAL', 'SYSTEM', 'PUTAWAY', 'PICKING'
    movement_reason VARCHAR(100),
    status VARCHAR(20) DEFAULT 'PENDING', -- 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'
    operator_id BIGINT NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    UNIQUE(account_book_id, movement_no)
);
```

**3. 盘点计划表 (wms_cycle_count_plan)**
```sql
CREATE TABLE wms_cycle_count_plan (
    id BIGSERIAL PRIMARY KEY,
    account_book_id BIGINT NOT NULL,
    plan_no VARCHAR(50) NOT NULL,
    plan_name VARCHAR(200) NOT NULL,
    count_type VARCHAR(20) NOT NULL, -- 'FULL', 'CYCLE', 'SPOT', 'ABC'
    warehouse_id BIGINT,
    planned_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'DRAFT', -- 'DRAFT', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'
    count_strategy VARCHAR(50), -- 'BY_LOCATION', 'BY_ITEM', 'BY_ABC', 'RANDOM'
    include_zero_stock BOOLEAN DEFAULT FALSE,
    freeze_inventory BOOLEAN DEFAULT TRUE,
    responsible_user_id BIGINT,
    description TEXT,
    created_by BIGINT NOT NULL,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    UNIQUE(account_book_id, plan_no)
);
```

**4. 盘点任务表 (wms_cycle_count_task)**
```sql
CREATE TABLE wms_cycle_count_task (
    id BIGSERIAL PRIMARY KEY,
    account_book_id BIGINT NOT NULL,
    task_no VARCHAR(50) NOT NULL,
    plan_id BIGINT NOT NULL,
    location_id BIGINT NOT NULL,
    item_id BIGINT,
    batch_no VARCHAR(100),
    system_quantity NUMERIC(12,4) NOT NULL,
    counted_quantity NUMERIC(12,4),
    variance_quantity NUMERIC(12,4),
    variance_percentage NUMERIC(8,4),
    unit_of_measure VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING', -- 'PENDING', 'COUNTING', 'COMPLETED', 'VARIANCE_CONFIRMED'
    counter_user_id BIGINT,
    counted_at TIMESTAMP,
    variance_reason VARCHAR(200),
    adjustment_required BOOLEAN DEFAULT FALSE,
    adjustment_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_book_id, task_no)
);
```

**5. 库存预警规则表 (wms_inventory_alert_rule)**
```sql
CREATE TABLE wms_inventory_alert_rule (
    id BIGSERIAL PRIMARY KEY,
    account_book_id BIGINT NOT NULL,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(20) NOT NULL, -- 'LOW_STOCK', 'HIGH_STOCK', 'EXPIRY', 'SLOW_MOVING'
    item_id BIGINT,
    warehouse_id BIGINT,
    location_id BIGINT,
    threshold_value NUMERIC(12,4),
    threshold_unit VARCHAR(20),
    alert_level VARCHAR(20) DEFAULT 'WARNING', -- 'INFO', 'WARNING', 'CRITICAL'
    is_active BOOLEAN DEFAULT TRUE,
    notification_emails TEXT,
    notification_users TEXT,
    check_frequency_minutes INTEGER DEFAULT 60,
    last_check_at TIMESTAMP,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);
```

**6. 库存预警日志表 (wms_inventory_alert_log)**
```sql
CREATE TABLE wms_inventory_alert_log (
    id BIGSERIAL PRIMARY KEY,
    account_book_id BIGINT NOT NULL,
    rule_id BIGINT NOT NULL,
    inventory_id BIGINT NOT NULL,
    alert_type VARCHAR(20) NOT NULL,
    alert_level VARCHAR(20) NOT NULL,
    alert_message TEXT NOT NULL,
    current_value NUMERIC(12,4),
    threshold_value NUMERIC(12,4),
    status VARCHAR(20) DEFAULT 'ACTIVE', -- 'ACTIVE', 'ACKNOWLEDGED', 'RESOLVED'
    acknowledged_by BIGINT,
    acknowledged_at TIMESTAMP,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 后端实现架构

#### Entity层实现示例

**库存调整实体 (wms_inventory_adjustment_entity.go)**
```go
package entity

import (
    "time"
)

type WmsInventoryAdjustmentType string

const (
    AdjustmentTypeIncrease     WmsInventoryAdjustmentType = "INCREASE"
    AdjustmentTypeDecrease     WmsInventoryAdjustmentType = "DECREASE"
    AdjustmentTypeStatusChange WmsInventoryAdjustmentType = "STATUS_CHANGE"
)

type WmsInventoryAdjustmentStatus string

const (
    AdjustmentStatusPending  WmsInventoryAdjustmentStatus = "PENDING"
    AdjustmentStatusApproved WmsInventoryAdjustmentStatus = "APPROVED"
    AdjustmentStatusRejected WmsInventoryAdjustmentStatus = "REJECTED"
    AdjustmentStatusExecuted WmsInventoryAdjustmentStatus = "EXECUTED"
)

type WmsInventoryAdjustment struct {
    AccountBookEntity

    AdjustmentNo       string                         `gorm:"column:adjustment_no;size:50;not null;uniqueIndex:idx_adjustment_no_account;comment:调整单号" json:"adjustmentNo"`
    InventoryID        uint                           `gorm:"column:inventory_id;not null;comment:库存ID" json:"inventoryId"`
    AdjustmentType     WmsInventoryAdjustmentType     `gorm:"column:adjustment_type;type:varchar(20);not null;comment:调整类型" json:"adjustmentType"`
    QuantityBefore     float64                        `gorm:"column:quantity_before;type:numeric(12,4);not null;comment:调整前数量" json:"quantityBefore"`
    QuantityAfter      float64                        `gorm:"column:quantity_after;type:numeric(12,4);not null;comment:调整后数量" json:"quantityAfter"`
    QuantityChange     float64                        `gorm:"column:quantity_change;type:numeric(12,4);not null;comment:调整数量" json:"quantityChange"`
    StatusBefore       *string                        `gorm:"column:status_before;type:varchar(20);comment:调整前状态" json:"statusBefore"`
    StatusAfter        *string                        `gorm:"column:status_after;type:varchar(20);comment:调整后状态" json:"statusAfter"`
    ReasonCode         *string                        `gorm:"column:reason_code;type:varchar(50);comment:原因代码" json:"reasonCode"`
    ReasonDescription  *string                        `gorm:"column:reason_description;type:text;comment:原因描述" json:"reasonDescription"`
    ApprovalStatus     WmsInventoryAdjustmentStatus   `gorm:"column:approval_status;type:varchar(20);default:'PENDING';comment:审批状态" json:"approvalStatus"`
    ApprovedBy         *uint                          `gorm:"column:approved_by;comment:审批人ID" json:"approvedBy"`
    ApprovedAt         *time.Time                     `gorm:"column:approved_at;comment:审批时间" json:"approvedAt"`
    OperatorID         uint                           `gorm:"column:operator_id;not null;comment:操作员ID" json:"operatorId"`
}

func (WmsInventoryAdjustment) TableName() string {
    return "wms_inventory_adjustment"
}
```

#### Service层实现示例

**库存查询服务接口 (wms_inventory_query_service.go)**
```go
package service

import (
    "context"
    "backend/internal/model/dto"
    "backend/internal/model/vo"
)

type WmsInventoryQueryService interface {
    // 库存查询
    GetInventoryPage(ctx context.Context, req *dto.WmsInventoryQueryReq) (*vo.WmsInventoryPageResp, error)
    GetInventoryDetail(ctx context.Context, id uint) (*vo.WmsInventoryDetailVO, error)
    GetInventoryByLocation(ctx context.Context, locationId uint) ([]*vo.WmsInventoryVO, error)
    GetInventoryByItem(ctx context.Context, itemId uint) ([]*vo.WmsInventoryVO, error)

    // 库存统计
    GetInventorySummary(ctx context.Context, req *dto.WmsInventorySummaryReq) (*vo.WmsInventorySummaryVO, error)
    GetInventoryTurnover(ctx context.Context, req *dto.WmsInventoryTurnoverReq) (*vo.WmsInventoryTurnoverVO, error)

    // 库存可用性
    CheckInventoryAvailability(ctx context.Context, req *dto.WmsInventoryAvailabilityReq) (*vo.WmsInventoryAvailabilityVO, error)
    GetAvailableInventory(ctx context.Context, itemId uint, warehouseId uint, requiredQty float64) ([]*vo.WmsInventoryVO, error)
}
```

### 前端实现架构

#### API层实现示例

**库存查询API (inventoryQuery.ts)**
```typescript
// 库存查询相关API
export interface WmsInventoryQueryReq {
  pageNum?: number
  pageSize?: number
  warehouseId?: number
  locationId?: number
  itemId?: number
  itemSku?: string
  batchNo?: string
  status?: string
  quantityMin?: number
  quantityMax?: number
  expiryDateStart?: string
  expiryDateEnd?: string
}

export interface WmsInventoryVO {
  id: number
  warehouseId: number
  warehouseName?: string
  locationId: number
  locationCode?: string
  itemId: number
  itemSku?: string
  itemName?: string
  batchNo?: string
  quantity: number
  allocatedQty: number
  frozenQty: number
  availableQty: number
  unitOfMeasure: string
  status: string
  productionDate?: string
  expiryDate?: string
  lastUpdated: string
}

export interface WmsInventoryPageResp {
  list: WmsInventoryVO[]
  total: number
}

// API函数
export const getInventoryPage = (params: WmsInventoryQueryReq): Promise<WmsInventoryPageResp> => {
  return request({
    url: '/wms/inventory/query',
    method: 'get',
    params
  })
}

export const getInventoryDetail = (id: number): Promise<WmsInventoryVO> => {
  return request({
    url: `/wms/inventory/query/${id}`,
    method: 'get'
  })
}

export const getInventorySummary = (params: any): Promise<any> => {
  return request({
    url: '/wms/inventory/summary',
    method: 'get',
    params
  })
}
```

#### Store实现示例

**库存查询Store (inventoryQuery.ts)**
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as inventoryQueryApi from '@/api/wms/inventoryQuery'
import type { WmsInventoryQueryReq, WmsInventoryVO } from '@/api/wms/inventoryQuery'

export const useInventoryQueryStore = defineStore('wms-inventory-query', () => {
  // 状态数据
  const list = ref<WmsInventoryVO[]>([])
  const total = ref(0)
  const loading = ref(false)
  const currentRecord = ref<WmsInventoryVO | null>(null)

  // 搜索表单
  const searchForm = ref<WmsInventoryQueryReq>({
    pageNum: 1,
    pageSize: 10,
    warehouseId: undefined,
    locationId: undefined,
    itemSku: '',
    status: undefined
  })

  // 分页信息
  const pagination = computed(() => ({
    pageNum: searchForm.value.pageNum || 1,
    pageSize: searchForm.value.pageSize || 10,
    total: total.value
  }))

  // 获取库存列表
  const fetchList = async () => {
    loading.value = true
    try {
      const response = await inventoryQueryApi.getInventoryPage(searchForm.value)
      list.value = response.list || []
      total.value = response.total || 0
    } catch (error) {
      console.error('获取库存列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取库存详情
  const fetchDetail = async (id: number) => {
    try {
      const response = await inventoryQueryApi.getInventoryDetail(id)
      currentRecord.value = response
      return response
    } catch (error) {
      console.error('获取库存详情失败:', error)
      throw error
    }
  }

  // 重置搜索表单
  const resetSearchForm = () => {
    searchForm.value = {
      pageNum: 1,
      pageSize: 10,
      warehouseId: undefined,
      locationId: undefined,
      itemSku: '',
      status: undefined
    }
  }

  return {
    // 状态
    list,
    total,
    loading,
    currentRecord,
    searchForm,
    pagination,

    // 方法
    fetchList,
    fetchDetail,
    resetSearchForm
  }
})
```

#### 页面实现示例

**库存查询主页面 (inventory/query/index.vue)**
```vue
<template>
  <div class="inventory-query-container">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="仓库">
          <el-select v-model="searchForm.warehouseId" placeholder="请选择仓库" clearable>
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物料SKU">
          <el-input v-model="searchForm.itemSku" placeholder="请输入物料SKU" clearable />
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="可用" value="AVAILABLE" />
            <el-option label="冻结" value="FROZEN" />
            <el-option label="损坏" value="DAMAGED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ inventoryStats.totalItems }}</div>
            <div class="stat-label">总物料数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ inventoryStats.totalQuantity }}</div>
            <div class="stat-label">总库存量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ inventoryStats.availableQuantity }}</div>
            <div class="stat-label">可用库存</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value">{{ inventoryStats.frozenQuantity }}</div>
            <div class="stat-label">冻结库存</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-card class="main-content">
      <VNTable
        ref="vnTableRef"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        :toolbar-config="toolbarConfig"
        :show-operations="true"
        :operation-width="200"
        operation-fixed="right"
        row-key="id"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @refresh="handleRefresh"
      >
        <!-- 自定义列插槽 -->
        <template #column-itemSku="{ row }">
          <el-link
            type="primary"
            @click="handleViewDetail(row)"
            :underline="false"
          >
            {{ row.itemSku }}
          </el-link>
        </template>

        <template #column-status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ formatStatus(row.status) }}
          </el-tag>
        </template>

        <template #column-quantity="{ row }">
          <div class="quantity-info">
            <div>总量: {{ row.quantity }}</div>
            <div class="quantity-detail">
              <span>可用: {{ row.availableQty }}</span>
              <span>冻结: {{ row.frozenQty }}</span>
              <span>分配: {{ row.allocatedQty }}</span>
            </div>
          </div>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleViewDetail(row)"
          >
            查看详情
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleAdjust(row)"
            v-if="checkPermission('inventory:adjust')"
          >
            调整
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleMove(row)"
            v-if="checkPermission('inventory:move')"
          >
            移动
          </el-button>
        </template>
      </VNTable>
    </el-card>

    <!-- 库存详情对话框 -->
    <InventoryDetailDialog
      ref="inventoryDetailDialogRef"
      @refresh="handleRefresh"
    />

    <!-- 库存调整对话框 -->
    <InventoryAdjustmentDialog
      ref="inventoryAdjustmentDialogRef"
      @confirm="handleRefresh"
    />

    <!-- 库存移动对话框 -->
    <InventoryMovementDialog
      ref="inventoryMovementDialogRef"
      @confirm="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElCard, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElButton, ElIcon, ElRow, ElCol, ElLink, ElTag, ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
import InventoryDetailDialog from './components/InventoryDetailDialog.vue'
import InventoryAdjustmentDialog from './components/InventoryAdjustmentDialog.vue'
import InventoryMovementDialog from './components/InventoryMovementDialog.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import type { WmsInventoryVO } from '@/api/wms/inventoryQuery'
import { useInventoryQueryStore } from '@/store/wms/inventoryQuery'
import { checkPermission } from '@/hooks/usePermission'

// Store
const inventoryQueryStore = useInventoryQueryStore()

// 响应式数据
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const inventoryDetailDialogRef = ref<InstanceType<typeof InventoryDetailDialog>>()
const inventoryAdjustmentDialogRef = ref<InstanceType<typeof InventoryAdjustmentDialog>>()
const inventoryMovementDialogRef = ref<InstanceType<typeof InventoryMovementDialog>>()

// 仓库选项
const warehouseOptions = ref<any[]>([])

// 统计数据
const inventoryStats = reactive({
  totalItems: 0,
  totalQuantity: 0,
  availableQuantity: 0,
  frozenQuantity: 0
})

// 计算属性
const { list, total, loading, searchForm, pagination } = inventoryQueryStore

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'itemSku', label: '物料SKU', width: 120, slot: true },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'locationCode', label: '库位', width: 100 },
  { prop: 'batchNo', label: '批次号', width: 120 },
  { prop: 'quantity', label: '库存数量', width: 150, slot: true },
  { prop: 'unitOfMeasure', label: '单位', width: 80 },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'expiryDate', label: '过期日期', width: 120 },
  { prop: 'lastUpdated', label: '最后更新', width: 160 }
])

// 工具栏配置
const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showExport: true,
  showImport: false,
  showAdd: false,
  showBatchDelete: false
}))

// 方法
const loadData = async () => {
  try {
    await inventoryQueryStore.fetchList()
    updateStats()
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const updateStats = () => {
  const stats = {
    totalItems: list.value.length,
    totalQuantity: 0,
    availableQuantity: 0,
    frozenQuantity: 0
  }

  list.value.forEach(item => {
    stats.totalQuantity += item.quantity
    stats.availableQuantity += item.availableQty
    stats.frozenQuantity += item.frozenQty
  })

  Object.assign(inventoryStats, stats)
}

const handleSearch = () => {
  searchForm.value.pageNum = 1
  loadData()
}

const handleReset = () => {
  inventoryQueryStore.resetSearchForm()
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  searchForm.value.pageNum = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  searchForm.value.pageSize = size
  searchForm.value.pageNum = 1
  loadData()
}

const handleViewDetail = (row: WmsInventoryVO) => {
  inventoryDetailDialogRef.value?.open(row.id)
}

const handleAdjust = (row: WmsInventoryVO) => {
  inventoryAdjustmentDialogRef.value?.open(row)
}

const handleMove = (row: WmsInventoryVO) => {
  inventoryMovementDialogRef.value?.open(row)
}

// 工具方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'AVAILABLE': 'success',
    'FROZEN': 'warning',
    'DAMAGED': 'danger',
    'EXPIRED': 'info'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'AVAILABLE': '可用',
    'FROZEN': '冻结',
    'DAMAGED': '损坏',
    'EXPIRED': '过期'
  }
  return labelMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.inventory-query-container {
  padding: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.stats-row {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.main-content {
  margin-bottom: 16px;
}

.quantity-info {
  font-size: 12px;
}

.quantity-detail {
  display: flex;
  gap: 8px;
  margin-top: 4px;
  color: #909399;
}

.quantity-detail span {
  font-size: 11px;
}
</style>
```

### 项目交付标准

#### 代码质量标准
- **代码覆盖率**: 单元测试覆盖率 ≥ 80%
- **代码规范**: 严格遵循项目编码规范
- **性能要求**: 页面加载时间 ≤ 2秒
- **兼容性**: 支持主流浏览器

#### 功能完整性标准
- **核心功能**: 所有规划功能100%实现
- **异常处理**: 完善的错误处理机制
- **用户体验**: 友好的交互界面
- **数据安全**: 完整的权限控制

#### 文档交付标准
- **技术文档**: API文档、数据库设计文档
- **用户手册**: 操作指南、功能说明
- **部署文档**: 安装部署指南
- **测试报告**: 功能测试、性能测试报告

这个执行计划基于现有WMS项目的成功实践，确保了架构的一致性和技术的先进性，将为企业提供一套专业、完整、高效的库存管理解决方案。
