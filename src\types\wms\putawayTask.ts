import type {
  WmsPutawayTaskResp,
  WmsPutawayTaskDetailResp,
  WmsPutawayTaskStatus
} from "@/api/wms/putawayTask"

// ==================== 表单 & 表格相关类型 ====================

// 明细行数据 (用于详情弹窗中的表格)
export interface DetailRowData {
    id: number
    lineNo: number
    itemId: number
    itemSku?: string
    itemName?: string
    putawayQuantity: number
    unitOfMeasure: string
    sourceLocationCode?: string
    suggestedLocationCode?: string
    status: WmsPutawayTaskStatus
    remark?: string
}

// 主表单数据 (用于详情弹窗)
export interface PutawayTaskFormData {
    id?: number
    taskNo?: string
    receivingRecordNo?: string
    status?: WmsPutawayTaskStatus
    priority?: number
    assignedToUserName?: string
    remark?: string
    details: DetailRowData[]
}

// 搜索表单数据
export interface SearchFormData {
    taskNo?: string
    receivingRecordNo?: string
    status?: WmsPutawayTaskStatus
    assignedToUserId?: number
}


// ==================== 其他 UI 相关类型 ====================

// 分页参数
export interface PaginationParams {
    pageNum: number
    pageSize: number
    total: number
}

// 通用选择项
export interface SelectOption {
    value: any
    label: string
}

// ==================== 移动端相关类型 ====================

// 移动端上架表单数据
export interface MobilePutawayFormData {
    putawayQty: number
    actualLocation: string
    remark: string
}

// 扫描记录
export interface ScanRecord {
    code: string
    type: 'ITEM' | 'LOCATION' | 'UNKNOWN'
    timestamp: number
    success: boolean
    result?: string
    itemId?: number
}

// 快速上架数据
export interface QuickPutawayData {
    detailId: number
    itemSku: string
    itemName: string
    completedQuantity: number
    actualLocation?: string
    timestamp: number
}

// 移动端任务完成表单
export interface MobileTaskCompleteForm {
    remark: string
}

// 异常处理表单
export interface ExceptionForm {
    type: string
    description: string
}

// 移动端上架对话框模式
export type MobilePutawayMode = 'start' | 'complete' | 'adjust'

// 移动端上架确认数据
export interface MobilePutawayConfirmData {
    detailId: number
    completedQuantity: number
    actualLocation: string
    remark: string
    mode: MobilePutawayMode
}