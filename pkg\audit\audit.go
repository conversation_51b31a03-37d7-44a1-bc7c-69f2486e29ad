package audit

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"
)

// 全局审计实例
var (
	instance     *Audit
	once         sync.Once
	defaultAudit *Audit
)

// LoggerInterface 定义日志记录接口，用于审计系统的日志输出
type LoggerInterface interface {
	Debug(args ...interface{})
	Info(args ...interface{})
	Warn(args ...interface{})
	Error(args ...interface{})
	WithField(key string, value interface{}) LoggerInterface
	WithFields(fields map[string]interface{}) LoggerInterface
}

// Audit 审计核心结构体，提供审计记录、过滤和处理功能
type Audit struct {
	config   AuditConfig
	handlers []AuditHandler
	logger   LoggerInterface
	storage  AuditStorage
}

// NewAudit 创建新的审计实例
// 参数:
//   - config: 审计配置
//
// 返回:
//   - *Audit: 审计实例
func NewAudit(config AuditConfig) *Audit {
	return &Audit{
		config:   config,
		handlers: make([]AuditHandler, 0),
		logger:   nil, // 实际使用时需要设置
	}
}

// Init 初始化审计系统，创建全局单例实例
// 参数:
//   - config: 审计配置
func Init(config AuditConfig) {
	once.Do(func() {
		instance = NewAudit(config)
		defaultAudit = instance
	})
}

// GetAudit 获取审计实例，如果实例不存在则初始化一个默认配置的实例
// 返回:
//   - *Audit: 审计实例
func GetAudit() *Audit {
	if instance == nil {
		defaultConfig := AuditConfig{
			Enabled:        true,
			AsyncEnabled:   true,
			LogEnabled:     true,
			StorageEnabled: true,
			DefaultLevel:   LEVEL_INFO,
			RetentionDays:  AUDIT_LOG_RETENTION_DAYS,
			Filter: AuditFilter{
				MinLevel: LEVEL_INFO,
			},
		}
		Init(defaultConfig)
	}
	return instance
}

// SetLogger 设置审计日志组件
// 参数:
//   - logger: 日志接口实现
func (a *Audit) SetLogger(logger LoggerInterface) {
	a.logger = logger
}

// RegisterHandler 注册审计处理器
// 参数:
//   - handler: 审计处理器
func (a *Audit) RegisterHandler(handler AuditHandler) {
	a.handlers = append(a.handlers, handler)
}

// SetStorage 设置审计存储
// 参数:
//   - storage: 审计存储接口实现
func (a *Audit) SetStorage(storage AuditStorage) {
	a.storage = storage
}

// Record 记录审计事件
// 参数:
//   - ctx: 上下文
//   - action: 操作类型
//   - entityType: 实体类型
//   - entityID: 实体ID
//   - oldValue: 操作前的值
//   - newValue: 操作后的值
//   - options: 审计选项
//
// 返回:
//   - AuditResult: 审计结果
func (a *Audit) Record(ctx context.Context, action, entityType string, entityID interface{}, oldValue, newValue interface{}, options ...AuditOptions) AuditResult {
	if !a.config.Enabled {
		return AuditResult{Success: false, Message: "审计功能未启用"}
	}

	// 合并选项
	var opt AuditOptions
	if len(options) > 0 {
		opt = options[0]
	}

	// 创建审计事件
	event := CreateAuditEvent(ctx, action, entityType, entityID, oldValue, newValue, a.config.DefaultLevel, opt)

	// 如果需要过滤，则先进行过滤
	if !a.ShouldAudit(event) {
		return AuditResult{Success: false, Message: "审计事件被过滤"}
	}

	// 异步处理
	if a.config.AsyncEnabled || opt.Async {
		go a.processEvent(event)
	} else {
		a.processEvent(event)
	}

	return AuditResult{
		Success: true,
		Message: "审计事件已记录",
		EventID: event.TraceID,
	}
}

// RecordOperation 记录操作事件
// 参数:
//   - ctx: 上下文
//   - action: 操作类型
//   - entityType: 实体类型
//   - entityID: 实体ID
//   - detail: 详细信息
//   - options: 审计选项
//
// 返回:
//   - AuditResult: 审计结果
func (a *Audit) RecordOperation(ctx context.Context, action, entityType string, entityID interface{}, detail string, options ...AuditOptions) AuditResult {
	if !a.config.Enabled {
		return AuditResult{Success: false, Message: "审计功能未启用"}
	}

	// 合并选项
	var opt AuditOptions
	if len(options) > 0 {
		opt = options[0]
	}

	// 创建审计事件
	event := CreateAuditEvent(ctx, action, entityType, entityID, nil, nil, a.config.DefaultLevel, opt)
	event.Detail = detail

	// 如果需要过滤，则先进行过滤
	if !a.ShouldAudit(event) {
		return AuditResult{Success: false, Message: "审计事件被过滤"}
	}

	// 异步处理
	if a.config.AsyncEnabled || opt.Async {
		go a.processEvent(event)
	} else {
		a.processEvent(event)
	}

	return AuditResult{
		Success: true,
		Message: "审计事件已记录",
		EventID: event.TraceID,
	}
}

// RecordChange 记录变更事件
// 参数:
//   - ctx: 上下文
//   - action: 操作类型
//   - entityType: 实体类型
//   - entityID: 实体ID
//   - oldValue: 变更前的值
//   - newValue: 变更后的值
//   - options: 审计选项
//
// 返回:
//   - AuditResult: 审计结果
func (a *Audit) RecordChange(ctx context.Context, action, entityType string, entityID interface{}, oldValue, newValue interface{}, options ...AuditOptions) AuditResult {
	// 这里利用Record方法，但明确此次为变更事件
	if oldValue == nil && newValue == nil {
		return AuditResult{Success: false, Message: "变更事件必须提供旧值或新值"}
	}

	var opt AuditOptions
	if len(options) > 0 {
		opt = options[0]
	}

	// 计算变更字段
	changes := DiffValues(oldValue, newValue, opt.IgnoreFields, opt.IgnoreZeroValue)

	// 如果没有变更，则不记录
	if len(changes) == 0 {
		return AuditResult{Success: false, Message: "无变更字段"}
	}

	// 创建审计事件
	event := CreateAuditEvent(ctx, action, entityType, entityID, oldValue, newValue, a.config.DefaultLevel, opt)
	event.Changes = changes

	// 如果需要过滤，则先进行过滤
	if !a.ShouldAudit(event) {
		return AuditResult{Success: false, Message: "审计事件被过滤"}
	}

	// 异步处理
	if a.config.AsyncEnabled || opt.Async {
		go a.processEvent(event)
	} else {
		a.processEvent(event)
	}

	return AuditResult{
		Success: true,
		Message: "变更事件已记录",
		EventID: event.TraceID,
	}
}

// processEvent 处理审计事件
// 参数:
//   - event: 审计事件
func (a *Audit) processEvent(event AuditEvent) {
	// 记录日志
	if a.config.LogEnabled && a.logger != nil {
		LogEvent(event, a.logger)
	}

	// 保存到存储
	if a.config.StorageEnabled && a.storage != nil {
		if err := a.storage.Save(event); err != nil && a.logger != nil {
			a.logger.Error("保存审计事件失败", err)
		}
	}

	// 处理器处理
	for _, handler := range a.handlers {
		if err := handler.Handle(event); err != nil && a.logger != nil {
			a.logger.Error("处理审计事件失败", err)
		}
	}
}

// shouldAudit 判断是否应该审计
// 参数:
//   - event: 审计事件
//
// 返回:
//   - bool: 是否应该审计
func (a *Audit) ShouldAudit(event AuditEvent) bool {
	filter := a.config.Filter

	// 级别过滤
	if filter.MinLevel != "" && GetLevelWeight(event.Level) < GetLevelWeight(filter.MinLevel) {
		return false
	}

	// 模块过滤
	if len(filter.IncludeModules) > 0 && !Contains(filter.IncludeModules, event.Module) {
		return false
	}
	if len(filter.ExcludeModules) > 0 && Contains(filter.ExcludeModules, event.Module) {
		return false
	}

	// 操作过滤
	if len(filter.IncludeActions) > 0 && !Contains(filter.IncludeActions, event.Action) {
		return false
	}
	if len(filter.ExcludeActions) > 0 && Contains(filter.ExcludeActions, event.Action) {
		return false
	}

	// 实体过滤
	if len(filter.IncludeEntities) > 0 && !Contains(filter.IncludeEntities, event.EntityType) {
		return false
	}
	if len(filter.ExcludeEntities) > 0 && Contains(filter.ExcludeEntities, event.EntityType) {
		return false
	}

	// 用户过滤
	if len(filter.IncludeUsers) > 0 && !ContainsUint(filter.IncludeUsers, event.UserID) {
		return false
	}
	if len(filter.ExcludeUsers) > 0 && ContainsUint(filter.ExcludeUsers, event.UserID) {
		return false
	}

	return true
}

// GetLevelWeight 获取日志级别权重
// 参数:
//   - level: 级别
//
// 返回:
//   - int: 权重
func GetLevelWeight(level string) int {
	switch strings.ToUpper(level) {
	case LEVEL_TRACE:
		return 0
	case LEVEL_DEBUG:
		return 1
	case LEVEL_INFO:
		return 2
	case LEVEL_WARN:
		return 3
	case LEVEL_ERROR:
		return 4
	case LEVEL_FATAL:
		return 5
	default:
		return 2 // 默认INFO级别
	}
}

// LogEvent 记录审计事件日志
// 参数:
//   - event: 审计事件
//   - log: 日志接口
func LogEvent(event AuditEvent, log LoggerInterface) {
	fields := map[string]interface{}{
		"traceId":    event.TraceID,
		"userId":     event.UserID,
		"username":   event.Username,
		"action":     event.Action,
		"entityType": event.EntityType,
		"entityId":   event.EntityID,
		"status":     event.Status,
		"module":     event.Module,
		"ip":         event.IP,
		"level":      event.Level,
		"time":       event.Time.Format(time.RFC3339),
		"duration":   event.Duration,
		"detail":     event.Detail,
	}

	var message string
	if event.Message != "" {
		message = event.Message
	} else {
		message = fmt.Sprintf("用户[%s]在[%s]模块执行了[%s]操作，对象类型[%s]，对象ID[%v]，状态[%s]",
			event.Username, event.Module, event.Action, event.EntityType, event.EntityID, event.Status)
	}

	// 如果log为nil或不完整实现接口，则直接跳过
	if log == nil {
		return
	}

	// 根据级别记录不同级别的日志
	logger := log.WithFields(fields)
	if logger == nil {
		return
	}

	switch strings.ToUpper(event.Level) {
	case LEVEL_TRACE, LEVEL_DEBUG:
		logger.Debug(message)
	case LEVEL_INFO:
		logger.Info(message)
	case LEVEL_WARN:
		logger.Warn(message)
	case LEVEL_ERROR, LEVEL_FATAL:
		logger.Error(message)
	default:
		logger.Info(message)
	}
}

// CreateAuditEvent 从上下文创建审计事件
// 参数:
//   - ctx: 上下文
//   - action: 操作类型
//   - entityType: 实体类型
//   - entityID: 实体ID
//   - oldValue: 操作前的值
//   - newValue: 操作后的值
//   - level: 日志级别
//   - options: 审计选项
//
// 返回:
//   - AuditEvent: 审计事件
func CreateAuditEvent(ctx context.Context, action, entityType string, entityID, oldValue, newValue interface{}, level string, options AuditOptions) AuditEvent {
	// 创建基本事件
	event := AuditEvent{
		TraceInfo: TraceInfo{
			TraceID:   GetTraceIDFromContext(ctx),
			UserID:    GetUserIDFromContext(ctx),
			Username:  GetUsernameFromContext(ctx),
			IP:        GetIPFromContext(ctx),
			UserAgent: GetUserAgentFromContext(ctx),
			Time:      time.Now(),
			Module:    GetModuleFromContext(ctx),
			Function:  GetFunctionFromContext(ctx),
		},
		Action:     action,
		EntityType: entityType,
		EntityID:   entityID,
		Status:     RESULT_SUCCESS,
		Level:      level,
		OldValue:   oldValue,
		NewValue:   newValue,
		Duration:   0,
		Changes:    make([]FieldChange, 0),
	}

	// 如果有旧值和新值，计算变化
	if oldValue != nil || newValue != nil {
		event.Changes = DiffValues(oldValue, newValue, options.IgnoreFields, options.IgnoreZeroValue)
	}

	// 额外数据
	if options.ExtraData != nil {
		event.RelatedData = options.ExtraData
	} else {
		event.RelatedData = make(map[string]interface{})
	}

	return event
}

// DiffValues 比较两个值的差异
// 参数:
//   - oldValue: 旧值
//   - newValue: 新值
//   - ignoreFields: 忽略的字段
//   - ignoreZeroValue: 是否忽略零值
//
// 返回:
//   - []FieldChange: 字段变更列表
func DiffValues(oldValue, newValue interface{}, ignoreFields []string, ignoreZeroValue bool) []FieldChange {
	changes := make([]FieldChange, 0)

	// 如果都是nil，没有变化
	if oldValue == nil && newValue == nil {
		return changes
	}

	// 如果有一个是nil，则是完整的变化
	if oldValue == nil || newValue == nil {
		return []FieldChange{{
			Field:     "value",
			FieldDesc: "整体值",
			OldValue:  oldValue,
			NewValue:  newValue,
		}}
	}

	// 获取反射值
	oldVal := reflect.ValueOf(oldValue)
	newVal := reflect.ValueOf(newValue)

	// 非结构体直接比较
	if oldVal.Kind() != reflect.Struct || newVal.Kind() != reflect.Struct {
		if oldVal.Interface() != newVal.Interface() {
			return []FieldChange{{
				Field:     "value",
				FieldDesc: "值",
				OldValue:  oldValue,
				NewValue:  newValue,
			}}
		}
		return changes
	}

	// 结构体字段比较
	oldType := oldVal.Type()
	for i := 0; i < oldVal.NumField(); i++ {
		field := oldType.Field(i)
		fieldName := field.Name

		// 检查是否是忽略字段
		if Contains(ignoreFields, fieldName) {
			continue
		}

		// 检查是否有json标签
		jsonTag := strings.Split(field.Tag.Get("json"), ",")[0]
		if jsonTag != "" {
			fieldName = jsonTag
		}

		// 跳过忽略的json字段
		if jsonTag == "-" {
			continue
		}

		// 判断是否是敏感字段
		isSensitive := Contains(SensitiveFields, fieldName)

		// 获取新值中对应的字段
		_, found := newVal.Type().FieldByName(field.Name)
		if !found {
			continue
		}

		oldFieldValue := oldVal.Field(i).Interface()
		newFieldValue := newVal.FieldByName(field.Name).Interface()

		// 如果设置了忽略零值，且新值为零值，则跳过
		if ignoreZeroValue && IsZeroValue(newFieldValue) {
			continue
		}

		// 如果值不同，记录变化
		if !reflect.DeepEqual(oldFieldValue, newFieldValue) {
			// 敏感字段不记录具体值
			if isSensitive {
				oldFieldValue = "******"
				newFieldValue = "******"
			}

			// 添加变更记录
			fieldDesc := field.Tag.Get("description")
			if fieldDesc == "" {
				fieldDesc = fieldName
			}

			changes = append(changes, FieldChange{
				Field:       fieldName,
				FieldDesc:   fieldDesc,
				OldValue:    oldFieldValue,
				NewValue:    newFieldValue,
				IsSensitive: isSensitive,
			})
		}
	}

	return changes
}

// IsZeroValue 判断值是否为零值
// 参数:
//   - v: 要判断的值
//
// 返回:
//   - bool: 是否为零值
func IsZeroValue(v interface{}) bool {
	return reflect.DeepEqual(v, reflect.Zero(reflect.TypeOf(v)).Interface())
}

// 从上下文获取信息

// GetTraceIDFromContext 从上下文获取追踪ID
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - string: 追踪ID
func GetTraceIDFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if traceID, ok := ctx.Value(TRACE_ID).(string); ok {
		return traceID
	}
	return ""
}

// GetUserIDFromContext 从上下文获取用户ID
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - uint: 用户ID
func GetUserIDFromContext(ctx context.Context) uint {
	if ctx == nil {
		return 0
	}
	if userID, ok := ctx.Value(TRACE_USER_ID).(uint); ok {
		return userID
	}
	return 0
}

// GetUsernameFromContext 从上下文获取用户名
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - string: 用户名
func GetUsernameFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if username, ok := ctx.Value(TRACE_USERNAME).(string); ok {
		return username
	}
	return ""
}

// GetIPFromContext 从上下文获取IP地址
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - string: IP地址
func GetIPFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if ip, ok := ctx.Value(TRACE_IP).(string); ok {
		return ip
	}
	return ""
}

// GetUserAgentFromContext 从上下文获取用户代理
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - string: 用户代理
func GetUserAgentFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if userAgent, ok := ctx.Value("userAgent").(string); ok {
		return userAgent
	}
	return ""
}

// GetModuleFromContext 从上下文获取模块
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - string: 模块
func GetModuleFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if module, ok := ctx.Value(TRACE_MODULE).(string); ok {
		return module
	}
	return ""
}

// GetFunctionFromContext 从上下文获取功能
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - string: 功能
func GetFunctionFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if function, ok := ctx.Value(TRACE_FUNCTION).(string); ok {
		return function
	}
	return ""
}

// 包级方法，便于直接调用

// Record 记录审计事件
// 参数:
//   - ctx: 上下文
//   - action: 操作类型
//   - entityType: 实体类型
//   - entityID: 实体ID
//   - oldValue: 操作前的值
//   - newValue: 操作后的值
//   - options: 审计选项
//
// 返回:
//   - AuditResult: 审计结果
func Record(ctx context.Context, action, entityType string, entityID interface{}, oldValue, newValue interface{}, options ...AuditOptions) AuditResult {
	return GetAudit().Record(ctx, action, entityType, entityID, oldValue, newValue, options...)
}

// RecordOperation 记录操作事件
// 参数:
//   - ctx: 上下文
//   - action: 操作类型
//   - entityType: 实体类型
//   - entityID: 实体ID
//   - detail: 详细信息
//   - options: 审计选项
//
// 返回:
//   - AuditResult: 审计结果
func RecordOperation(ctx context.Context, action, entityType string, entityID interface{}, detail string, options ...AuditOptions) AuditResult {
	return GetAudit().RecordOperation(ctx, action, entityType, entityID, detail, options...)
}

// RecordChange 记录变更事件
// 参数:
//   - ctx: 上下文
//   - action: 操作类型
//   - entityType: 实体类型
//   - entityID: 实体ID
//   - oldValue: 变更前的值
//   - newValue: 变更后的值
//   - options: 审计选项
//
// 返回:
//   - AuditResult: 审计结果
func RecordChange(ctx context.Context, action, entityType string, entityID interface{}, oldValue, newValue interface{}, options ...AuditOptions) AuditResult {
	return GetAudit().RecordChange(ctx, action, entityType, entityID, oldValue, newValue, options...)
}

// RegisterHandler 注册审计处理器
// 参数:
//   - handler: 审计处理器
func RegisterHandler(handler AuditHandler) {
	GetAudit().RegisterHandler(handler)
}

// SetStorage 设置审计存储
// 参数:
//   - storage: 审计存储接口实现
func SetStorage(storage AuditStorage) {
	GetAudit().SetStorage(storage)
}

// 工具函数

// Contains 检查字符串切片是否包含指定值
// 参数:
//   - slice: 字符串切片
//   - value: 要检查的值
//
// 返回:
//   - bool: 是否包含
func Contains(slice []string, value string) bool {
	for _, item := range slice {
		if item == value {
			return true
		}
	}
	return false
}

// ContainsUint 检查uint切片是否包含指定值
// 参数:
//   - slice: uint切片
//   - value: 要检查的值
//
// 返回:
//   - bool: 是否包含
func ContainsUint(slice []uint, value uint) bool {
	for _, item := range slice {
		if item == value {
			return true
		}
	}
	return false
}

// ToJSON 将对象转换为JSON字符串
// 参数:
//   - v: 要转换的对象
//
// 返回:
//   - string: JSON字符串
func ToJSON(v interface{}) string {
	if v == nil {
		return ""
	}

	jsonData, err := json.Marshal(v)
	if err != nil {
		return fmt.Sprintf("%v", v)
	}
	return string(jsonData)
}

// MaskSensitiveFields 掩盖敏感字段
// 参数:
//   - data: 数据映射
//
// 返回:
//   - map[string]interface{}: 掩盖敏感字段后的数据映射
func MaskSensitiveFields(data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range data {
		if Contains(SensitiveFields, k) {
			result[k] = "******"
		} else {
			result[k] = v
		}
	}
	return result
}
