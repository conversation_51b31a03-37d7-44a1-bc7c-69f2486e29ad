<template>
  <div class="sidebar-example-layout">
    <VNSidebar 
      :items="menuItems" 
      :collapse="isCollapsed" 
      :default-openeds="['/products']" 
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      class="sidebar-container"
    />
    <div class="main-content">
      <div class="controls">
        <el-button @click="toggleCollapse">
          {{ isCollapsed ? '展开侧边栏' : '折叠侧边栏' }}
        </el-button>
        <el-alert title="这是一个侧边栏示例页面。点击侧边栏菜单项会（如果配置了路由）尝试导航。实际内容区域将由 vue-router 的 <router-view> 渲染。" type="info" :closable="false" show-icon style="margin-top: 15px;"></el-alert>
      </div>
      <div class="content-area">
        <!-- In a real application, a <router-view> would go here -->
        <h3>示例内容区域</h3>
        <p>当前路由: {{ currentRoutePath }}</p>
        <p>侧边栏当前状态: {{ isCollapsed ? '折叠' : '展开' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import VNSidebar from './index.vue';
import type { SidebarItem } from './types';
import { ElButton, ElAlert } from 'element-plus';
import { 
  HomeFilled, 
  Setting, 
  Goods, 
  List, 
  CirclePlusFilled 
} from '@element-plus/icons-vue'; // 引入更多图标

const isCollapsed = ref(false);
const route = useRoute();

const currentRoutePath = computed(() => route.path);

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const menuItems = ref<SidebarItem[]>([
  {
    label: '仪表盘',
    icon: HomeFilled,
    index: '/vntable-example', // 使用现有路由
    to: { name: 'VNTableExample' }
  },
  {
    label: '表单页',
    icon: List,
    index: '/vnform', // 使用现有路由
    to: { name: 'VNFormExample' }
  },
  {
    label: '面包屑页',
    icon: 'ArrowRight', // 也可以使用图标名称字符串
    index: '/vnbreadcrumb',
    to: { name: 'VNBreadcrumbExample' }
  },
  {
    label: '系统管理',
    icon: Setting,
    index: '/system', // 父菜单 index
    children: [
      {
        label: '用户管理',
        icon: 'User',
        index: '/system/users', // 子菜单 index
        to: { path: '/system/users' } // 假设的路由
      },
      {
        label: '角色管理',
        icon: CirclePlusFilled,
        index: '/system/roles',
        to: { path: '/system/roles' } // 假设的路由
      }
    ]
  }
]);
</script>

<style scoped>
.sidebar-example-layout {
  display: flex;
  height: calc(100vh - 84px); /* 减去 AppNav 的高度和可能的间距 */
  background-color: #f0f2f5; /* 主内容区域背景色 */
}

.sidebar-container {
  height: 100%;
  background-color: #304156 !important; /* Ensure background color override */
}

.main-content {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto; /* 如果内容过多，允许滚动 */
}

.controls {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.content-area {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style> 