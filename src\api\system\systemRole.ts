import request from '@/utils/request'; // 假设您的请求工具路径

// --- 角色相关类型定义 (基于 Go DTO) ---

// 角色列表项 (对应后端 RoleListVO 或 Role 结构体的部分字段)
export interface RoleListItem {
  id: number;
  name: string;       // 角色名称
  code: string;       // 角色编码
  status: number;     // 状态 (例如 1:启用, 0:禁用)
  sort?: number;      // 排序 (可选，如果列表返回)
  dataScope?: number; // 数据权限范围 (可选，如果列表返回)
  remark?: string;    // 备注 (可选)
  isDefault?: boolean;// 是否默认角色 (可选)
  isSystem?: boolean; // 是否系统角色 (可选)
  createTime: string; // 创建时间 (或 Date 类型)
  // 根据后端实际返回的列表 VO 添加或移除字段
}

// 角色列表响应结构
export interface RoleListResponse {
  list: RoleListItem[]; // 角色列表
  total: number;        // 总数
}

// 角色查询参数 (对应 RoleQueryDTO)
export interface RoleQueryParams {
  pageNum?: number;    // 页码 (来自 QueryDTO)
  pageSize?: number;   // 每页数量 (来自 QueryDTO)
  sort?: string;      // 排序参数，格式: "字段名,方向" (来自 QueryDTO)
  name?: string;      // 按角色名称筛选 (模糊)
  code?: string;      // 按角色编码筛选 (模糊)
  status?: number;    // 按状态筛选 (0 或 1)
  dataScope?: number; // 按数据权限范围筛选
  isDefault?: boolean;// 按是否默认角色筛选
  // 如果需要日期范围查询 (来自 DateRangeDTO)
  beginTime?: string; // 开始时间
  endTime?: string;   // 结束时间
}

// 用于创建/更新角色的数据结构 (合并 RoleCreateDTO 和 RoleUpdateDTO)
export interface RoleFormData {
  name: string;       // 角色名称 (必需, max=50)
  code: string;       // 角色编码 (必需, max=50)
  status?: number;    // 状态 (0 或 1, 默认为 1)
  sort?: number;      // 排序 (>=0, 可选)
  dataScope?: number; // 数据权限范围 (1-5, 可选)
  remark?: string;    // 备注 (max=255, 可选)
  isDefault?: boolean;// 是否默认角色 (可选)
  isSystem?: boolean; // 是否系统角色 (可选, 通常不可编辑)
  description?: string;// 描述 (max=255, 可选)
  menuIds: number[];  // 菜单ID列表 (必需, min=1, dive, gt=0) - 注意前端用 number[]
}

// 角色详情结构 (假设后端返回类似 RoleUpdateDTO 的结构 + ID 和时间戳)
export interface RoleDetail extends Omit<RoleFormData, 'menuIds'> { // 可能不直接返回 menuIds，需要单独接口或包含在内
  id: number;         // 角色 ID
  menuIds?: number[]; // 确认后端 getRoleDetail 是否返回此字段
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
  // 可能还有其他关联数据
}

// 菜单树节点结构 (请根据您的菜单 API 返回结果调整)
export interface MenuTreeItem {
    id: number;           // 菜单 ID (uint -> number)
    title: string;        // 显示的菜单名称 (修正：使用 title)
    icon?: string;        // 新增：菜单图标名称 (可选)
    children?: MenuTreeItem[]; // 子菜单
    // 可能还有 parentId 等字段，取决于后端结构
}


// --- API 请求函数 --- (路径需根据实际后端路由调整)

/**
 * 获取角色列表 (分页)
 * @param params 查询参数 (对应 RoleQueryDTO)
 */
export function getRoleList(params: RoleQueryParams) {
  // 假设后端路径: GET /api/v1/sys/roles/page
  return request.get<RoleListResponse>('/sys/roles/page', { params });
}

/**
 * 新增角色
 * @param data 角色数据 (对应 RoleCreateDTO)
 */
export function addRole(data: RoleFormData) {
  // 假设后端路径: POST /api/v1/sys/roles
  return request.post<any>('/sys/roles', data);
}

/**
 * 更新角色信息
 * @param id 角色 ID
 * @param data 角色数据 (对应 RoleUpdateDTO)
 */
export function updateRole(id: number, data: Partial<RoleFormData>) {
  // 假设后端路径: PUT /api/v1/sys/roles/{id}
  return request.put<any>(`/sys/roles/${id}`, data);
}

/**
 * 删除角色
 * @param id 角色 ID
 */
export function deleteRole(id: number) {
  // 假设后端路径: DELETE /api/v1/sys/roles/{id}
  return request.delete<any>(`/sys/roles/${id}`);
}

/**
 * 获取角色详情
 * @param id 角色 ID
 */
export function getRoleDetail(id: number) {
    // 假设后端路径: GET /api/v1/sys/roles/{id}
    // 拦截器已处理 .data，直接返回业务数据类型
    return request.get<Omit<RoleDetail, 'menuIds'>>(`/sys/roles/${id}`); 
}

/**
 * 获取指定角色的菜单 ID 列表
 * @param id 角色 ID
 */
export function getRoleMenus(id: number) {
    // 路径: GET /api/v1/sys/roles/{id}/menus
    // 拦截器已处理 .data，直接返回业务数据类型
    return request.get<{ menuIds: number[] } | number[]>(`/sys/roles/${id}/menus`); 
}

/**
 * 获取菜单树 (用于角色权限分配)
 * 请根据您的实际 API 调整路径和响应类型
 */
export function getMenuTree() {
    // 假设后端路径: GET /api/v1/sys/menus/tree (需要确认)
    return request.get<MenuTreeItem[]>('/sys/menus/tree'); 
}

/**
 * 获取角色简要列表 (用于下拉选择)
 */
export function getRoleSimpleList() {
    // 假设后端路径: GET /api/v1/sys/roles/simple (需要确认或调整)
    return request.get<{ list: { id: number; name: string }[] }>('/sys/roles/simple');
}

/**
 * 批量删除角色 (可选)
 * @param ids 角色 ID 数组
 */
export function batchDeleteRoles(ids: number[]) {
    // 假设后端路径: DELETE /api/v1/sys/roles/{id} (路由定义似乎只支持单个删除)
    // 可能需要后端添加批量删除路由 DELETE /api/v1/sys/roles 或调整
    console.warn('后端批量删除路由可能未按预期实现，当前假设为 /sys/roles');
    return request.delete<any>('/sys/roles', { data: { ids } }); 
}

// --- 其他特定操作的 API (根据需要添加) ---

/**
 * 更新角色状态 (对应 RoleStatusDTO)
 * @param ids 角色 ID 数组
 * @param status 新状态 (0 或 1)
 */
export function updateRoleStatus(ids: number[], status: number) {
    // 假设后端路径: PUT /api/v1/sys/roles/status
    return request.put<any>('/sys/roles/status', { ids, status });
}

/**
 * 更新角色数据权限范围 (对应 RoleDataScopeDTO)
 * @param id 角色 ID
 * @param dataScope 数据范围 (1-5)
 * @param deptIds 部门 ID 列表 (仅当 dataScope=2 时需要)
 */
export function updateRoleDataScope(id: number, dataScope: number, deptIds?: number[]) {
    // 假设后端路径: PUT /api/v1/sys/roles/{id}/data-scope
    const data: { dataScope: number; deptIds?: number[] } = { dataScope };
    if (dataScope === 2) {
        data.deptIds = deptIds || []; // 自定义时必须提供 deptIds
    }
    return request.put<any>(`/sys/roles/${id}/data-scope`, data);
}

/**
 * 更新角色关联的菜单 (对应 RoleMenuDTO)
 * @param id 角色 ID
 * @param menuIds 菜单 ID 列表
 */
export function updateRoleMenus(id: number, menuIds: number[]) {
    // 路径: PUT /api/v1/sys/roles/{id}/menus
    return request.put<any>(`/sys/roles/${id}/menus`, { menuIds });
}

// --- 导出/导入/角色用户查询等 API 可根据需要添加 ---
