package controller

import (
	"time"

	"github.com/kataras/iris/v12"

	// 假设 IsAdminMiddleware 在这里
	"backend/internal/model/dto"
	"backend/internal/model/vo"
	"backend/internal/service"
	"backend/pkg/config"
	apperrors "backend/pkg/errors"
	"backend/pkg/constant"
	// 假设 GetUserIDFromContext, IsAdminFromContext 在这里
	// 导入 response 包
)

// SQLToolController 提供执行 SQL 的接口
type SQLToolControllerImpl struct {
	*BaseControllerImpl
	sqlToolService service.SQLToolService
	appCfg         *config.Configuration // If needed for config checks
}

// NewSQLToolController 创建 SQL 工具控制器实例
func NewSQLToolController(cm *ControllerManager, appCfg *config.Configuration) *SQLToolControllerImpl {
	base := NewBaseController(cm)
	// 获取 SQLToolService, 依赖注入需要确保 ServiceManager 能提供它
	sqlToolSvc := service.GetSQLToolService(cm.GetServiceManager())
	if sqlToolSvc == nil {
		base.GetLogger().Fatal("无法获取 SQLToolService 实例")
	}
	return &SQLToolControllerImpl{
		BaseControllerImpl: base,
		sqlToolService:     sqlToolSvc,
		appCfg:             appCfg,
	}
}

// ExecuteSQL 执行 SQL 语句
// @Summary 执行原始 SQL 语句 (仅限管理员)
// @Description 接收 SQL 语句，在数据库中执行，并返回结果。极度危险，仅限管理员使用！
// @Tags Admin Tools
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param body body dto.SQLExecuteDTO true "要执行的 SQL"
// @Success 200 {object} response.Response{data=vo.SQLResultVO} "成功响应，包含执行结果或错误信息"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "禁止访问 (非管理员)"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /admin/sql-tool [post]
func (c *SQLToolControllerImpl) ExecuteSQL(ctx iris.Context) {
	opName := "执行SQL工具"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "sql_tool")

	// --- 移除冗余的权限检查 ---
	// 路由层已有 middleware.AdminRequiredMiddleware() 保证
	/*
		isAdmin, err := util.IsAdminFromContext(ctx)
		if err != nil {
			c.HandleError(ctx, err, opName)
			return
		}
		if !isAdmin {
			c.HandleError(ctx, apperrors.NewAuthError(apperrors.CODE_AUTH_FORBIDDEN, "需要管理员权限"), opName)
			return
		}
	*/
	// --- 结束移除 ---

	// 获取用户ID用于审计
	userID, err := c.GetUserIDFromContext(ctx)
	if err != nil {
		c.HandleError(ctx, err, opName) // Should not happen if authenticated, but check anyway
		return
	}

	var req dto.SQLExecuteDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	if req.SQL == "" {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "SQL 语句不能为空"), opName)
		return
	}

	// 调用 Service 执行
	// 注意：Service 层返回的 error 可能是 nil，但 vo 中可能包含数据库执行的错误信息
	startTime := time.Now()
	resultVO, execErr := c.sqlToolService.Execute(ctx.Request().Context(), uint(userID), req.SQL)

	// 填充基础信息，即使 service 返回 error
	if resultVO == nil { // Service 内部 panic 或意外返回 nil？
		resultVO = &vo.SQLResultVO{
			ResultType:  "Error",
			Error:       "执行时发生未知内部错误",
			ExecutedSQL: req.SQL,
			DurationMS:  time.Since(startTime).Milliseconds(),
		}
	} else {
		resultVO.DurationMS = time.Since(startTime).Milliseconds() // 确保 Duration 总是设置
		if execErr != nil && resultVO.Error == "" {
			// 如果 service 返回 error 但 VO 中没有设置 error message，则设置它
			resultVO.Error = execErr.Error()
			resultVO.ResultType = "Error"
		} else if execErr == nil && resultVO.Error != "" {
			// 如果 service 返回 nil error 但 VO 中有 error message (例如扫描错误)，确保类型是 Error
			resultVO.ResultType = "Error"
		}
	}

	// --- 调整：根据执行结果决定返回成功或失败 ---
	if execErr != nil {
		// 如果 service 返回了错误，则使用 HandleError 返回（通常会包含非 0 code）
		// 确保 execErr 是 *apperrors.CustomError 类型，或者包装它
		appErr, ok := execErr.(*apperrors.CustomError)
		if !ok {
			// 如果不是 CustomError，包装成 SystemError
			appErr = apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "执行 SQL 时发生未知错误").WithCause(execErr)
		}
		c.HandleError(ctx, appErr, opName)
	} else {
		// 如果 service 没有返回错误，则使用 Success 返回 VO
		// 注意：即使 VO 中可能包含由数据库操作本身报告的错误信息 (如语法错误)，这里仍返回业务成功 code
		// 这符合"请求已成功处理，具体执行结果见data"的模式
		c.Success(ctx, resultVO)
	}
}
