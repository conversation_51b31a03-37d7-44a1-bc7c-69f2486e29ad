import request from '@/utils/request'

// 编码规则相关接口

// 编码规则基础类型
export interface CodeRule {
  id?: number
  ruleCode: string
  ruleName: string
  businessType: string
  codeFormat: string
  separator?: string
  resetFrequency: string
  sequenceLength: number
  sequenceStart: number
  currentSequence?: number
  lastResetTime?: string
  status: string
  isDefault: boolean
  remark?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
}

// 创建编码规则请求
export interface CreateCodeRuleRequest {
  ruleCode: string
  ruleName: string
  businessType: string
  codeFormat: string
  separator?: string
  resetFrequency: string
  sequenceLength: number
  sequenceStart: number
  status: string
  isDefault: boolean
  remark?: string
}

// 更新编码规则请求
export interface UpdateCodeRuleRequest {
  ruleName: string
  codeFormat: string
  separator?: string
  resetFrequency: string
  sequenceLength: number
  sequenceStart: number
  status: string
  isDefault: boolean
  remark?: string
}

// 查询编码规则请求
export interface QueryCodeRuleRequest {
  pageNum?: number
  pageSize?: number
  ruleCode?: string
  ruleName?: string
  businessType?: string
  status?: string
  isDefault?: boolean
  sort?: string
}

// 编码生成请求
export interface CodeGenerationRequest {
  businessType: string
  ruleId?: number
  contextData?: Record<string, any>
}

// 编码生成响应
export interface CodeGenerationResponse {
  generatedCode: string
  ruleId: number
  sequenceNumber: number
  businessType: string
}

// 编码预览请求
export interface CodePreviewRequest {
  codeFormat: string
  contextData?: Record<string, any>
}

// 编码预览响应
export interface CodePreviewResponse {
  previewCode: string
  formatComponents: Array<{
    component: string
    value: string
    description: string
  }>
}

// 重置序号请求
export interface ResetSequenceRequest {
  ruleId: number
  resetValue?: number
}

// 业务类型选项
export interface BusinessTypeOption {
  value: string
  label: string
  description: string
}

// 重置频率选项
export interface ResetFrequencyOption {
  value: string
  label: string
  description: string
}

// 分页响应
export interface PageResult<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// API 调用函数

/**
 * 创建编码规则
 */
export function createCodeRule(data: CreateCodeRuleRequest) {
  return request({
    url: '/sys/code-rules',
    method: 'post',
    data
  })
}

/**
 * 更新编码规则
 */
export function updateCodeRule(id: number, data: UpdateCodeRuleRequest) {
  return request({
    url: `/sys/code-rules/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除编码规则
 */
export function deleteCodeRule(id: number) {
  return request({
    url: `/sys/code-rules/${id}`,
    method: 'delete'
  })
}

/**
 * 获取编码规则详情
 */
export function getCodeRule(id: number) {
  return request({
    url: `/sys/code-rules/${id}`,
    method: 'get'
  })
}

/**
 * 分页查询编码规则
 */
export function getCodeRulePage(params: QueryCodeRuleRequest): Promise<PageResult<CodeRule>> {
  return request({
    url: '/sys/code-rules',
    method: 'get',
    params
  })
}

/**
 * 设置为默认规则
 */
export function setAsDefault(id: number) {
  return request({
    url: `/sys/code-rules/${id}/set-default`,
    method: 'post'
  })
}

/**
 * 生成编码
 */
export function generateCode(data: CodeGenerationRequest): Promise<CodeGenerationResponse> {
  return request({
    url: '/sys/code-generation/generate',
    method: 'post',
    data
  })
}

/**
 * 预览编码
 */
export function previewCode(data: CodePreviewRequest): Promise<CodePreviewResponse> {
  return request({
    url: '/sys/code-generation/preview',
    method: 'post',
    data
  })
}

/**
 * 重置序号
 */
export function resetSequence(data: ResetSequenceRequest) {
  return request({
    url: '/sys/code-generation/reset-sequence',
    method: 'post',
    data
  })
}

/**
 * 获取业务类型选项
 */
export function getBusinessTypeOptions(): Promise<BusinessTypeOption[]> {
  return request({
    url: '/sys/code-rules/business-type-options',
    method: 'get'
  })
}

/**
 * 获取重置频率选项
 */
export function getResetFrequencyOptions(): Promise<ResetFrequencyOption[]> {
  return request({
    url: '/sys/code-rules/reset-frequency-options',
    method: 'get'
  })
} 