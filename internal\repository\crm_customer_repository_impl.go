package repository

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
	"context"

	"gorm.io/gorm"
)

// CrmCustomerRepository 客户仓库接口
type CrmCustomerRepository interface {
	BaseRepository[entity.CrmCustomer, uint]

	// 客户专用方法
	FindByCustomerCode(ctx context.Context, customerCode string) (*entity.CrmCustomer, error)
	FindByBusinessLicense(ctx context.Context, businessLicense string) (*entity.CrmCustomer, error)
	FindByTaxNumber(ctx context.Context, taxNumber string) (*entity.CrmCustomer, error)
	GetPage(ctx context.Context, req *dto.CrmCustomerQueryReq) (*response.PageResult, error)
	FindActiveSimpleList(ctx context.Context) ([]*entity.CrmCustomer, error)
	GetCustomerSummary(ctx context.Context) (*entity.CrmCustomer, error) // 返回统计数据用的结构体，这里暂时用entity

	// 客户联系人相关方法
	FindContactsByCustomerID(ctx context.Context, customerID uint) ([]*entity.CrmCustomerContact, error)
	FindContactByID(ctx context.Context, contactID uint) (*entity.CrmCustomerContact, error)
	FindPrimaryContactByCustomerID(ctx context.Context, customerID uint) (*entity.CrmCustomerContact, error)
	CreateContact(ctx context.Context, contact *entity.CrmCustomerContact) error
	UpdateContact(ctx context.Context, contact *entity.CrmCustomerContact) error
	DeleteContact(ctx context.Context, contactID uint) error
	SetPrimaryContact(ctx context.Context, customerID uint, contactID uint) error
}

// crmCustomerRepositoryImpl 客户仓库实现
type crmCustomerRepositoryImpl struct {
	BaseRepositoryImpl[entity.CrmCustomer, uint]
}

// NewCrmCustomerRepository 创建客户仓库
func NewCrmCustomerRepository(db *gorm.DB) CrmCustomerRepository {
	return &crmCustomerRepositoryImpl{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.CrmCustomer, uint]{db: db},
	}
}

// FindByCustomerCode 根据客户编码查找客户 (自动处理账套scope)
func (r *crmCustomerRepositoryImpl) FindByCustomerCode(ctx context.Context, customerCode string) (*entity.CrmCustomer, error) {
	conditions := []QueryCondition{
		NewEqualCondition("customer_code", customerCode),
	}
	return r.FindOneByCondition(ctx, conditions)
}

// FindByBusinessLicense 根据营业执照号查找客户 (自动处理账套scope)
func (r *crmCustomerRepositoryImpl) FindByBusinessLicense(ctx context.Context, businessLicense string) (*entity.CrmCustomer, error) {
	conditions := []QueryCondition{
		NewEqualCondition("business_license", businessLicense),
	}
	return r.FindOneByCondition(ctx, conditions)
}

// FindByTaxNumber 根据税务登记号查找客户 (自动处理账套scope)
func (r *crmCustomerRepositoryImpl) FindByTaxNumber(ctx context.Context, taxNumber string) (*entity.CrmCustomer, error) {
	conditions := []QueryCondition{
		NewEqualCondition("tax_number", taxNumber),
	}
	return r.FindOneByCondition(ctx, conditions)
}

// GetPage 使用现有分页机制实现分页查询 (参考MtlItem实现)
func (r *crmCustomerRepositoryImpl) GetPage(ctx context.Context, req *dto.CrmCustomerQueryReq) (*response.PageResult, error) {
	var conditions []QueryCondition

	if req.CustomerCode != "" {
		conditions = append(conditions, NewLikeCondition("customer_code", req.CustomerCode))
	}
	if req.CustomerName != "" {
		conditions = append(conditions, NewLikeCondition("customer_name", req.CustomerName))
	}
	if req.CustomerType != "" {
		conditions = append(conditions, NewEqualCondition("customer_type", req.CustomerType))
	}
	if req.Industry != "" {
		conditions = append(conditions, NewLikeCondition("industry", req.Industry))
	}
	if req.CustomerLevel != "" {
		conditions = append(conditions, NewEqualCondition("customer_level", req.CustomerLevel))
	}
	if req.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", req.Status))
	}
	if req.SalesRepresentativeID != nil {
		conditions = append(conditions, NewEqualCondition("sales_representative_id", *req.SalesRepresentativeID))
	}
	if req.IsKeyCustomer != nil {
		conditions = append(conditions, NewEqualCondition("is_key_customer", *req.IsKeyCustomer))
	}
	if req.Country != "" {
		conditions = append(conditions, NewLikeCondition("country", req.Country))
	}
	if req.Province != "" {
		conditions = append(conditions, NewLikeCondition("province", req.Province))
	}
	if req.City != "" {
		conditions = append(conditions, NewLikeCondition("city", req.City))
	}

	// 使用BaseRepository的FindByPage方法 (自动处理账套scope)
	return r.FindByPage(ctx, &req.PageQuery, conditions)
}

// FindActiveSimpleList 查询活跃客户简单列表
func (r *crmCustomerRepositoryImpl) FindActiveSimpleList(ctx context.Context) ([]*entity.CrmCustomer, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", "ACTIVE"),
	}

	sorts := []response.SortInfo{
		{Field: "customer_code", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sorts)
}

// GetCustomerSummary 获取客户统计摘要 (用于仪表板)
func (r *crmCustomerRepositoryImpl) GetCustomerSummary(ctx context.Context) (*entity.CrmCustomer, error) {
	// 这里应该返回统计数据，但由于VO的统计结构比较复杂
	// 暂时返回一个空的客户实体，实际使用时需要在Service层进行具体的统计查询
	// 或者定义专门的统计结构体
	return &entity.CrmCustomer{}, nil
}

// FindContactsByCustomerID 查询指定客户的所有联系人
func (r *crmCustomerRepositoryImpl) FindContactsByCustomerID(ctx context.Context, customerID uint) ([]*entity.CrmCustomerContact, error) {
	var contacts []*entity.CrmCustomerContact
	err := r.GetDB(ctx).Where("customer_id = ?", customerID).Order("id ASC").Find(&contacts).Error
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询客户联系人失败").WithCause(err)
	}
	return contacts, nil
}

// FindContactByID 根据ID查询联系人
func (r *crmCustomerRepositoryImpl) FindContactByID(ctx context.Context, contactID uint) (*entity.CrmCustomerContact, error) {
	var contact entity.CrmCustomerContact
	err := r.GetDB(ctx).First(&contact, contactID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "联系人不存在")
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询联系人失败").WithCause(err)
	}
	return &contact, nil
}

// FindPrimaryContactByCustomerID 查询指定客户的主要联系人
func (r *crmCustomerRepositoryImpl) FindPrimaryContactByCustomerID(ctx context.Context, customerID uint) (*entity.CrmCustomerContact, error) {
	var contact entity.CrmCustomerContact
	err := r.GetDB(ctx).Where("customer_id = ?", customerID).First(&contact).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "主要联系人不存在")
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询主要联系人失败").WithCause(err)
	}
	return &contact, nil
}

// CreateContact 创建联系人
func (r *crmCustomerRepositoryImpl) CreateContact(ctx context.Context, contact *entity.CrmCustomerContact) error {
	err := r.GetDB(ctx).Create(contact).Error
	if err != nil {
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "创建联系人失败").WithCause(err)
	}
	return nil
}

// UpdateContact 更新联系人
func (r *crmCustomerRepositoryImpl) UpdateContact(ctx context.Context, contact *entity.CrmCustomerContact) error {
	err := r.GetDB(ctx).Save(contact).Error
	if err != nil {
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新联系人失败").WithCause(err)
	}
	return nil
}

// DeleteContact 删除联系人 (软删除)
func (r *crmCustomerRepositoryImpl) DeleteContact(ctx context.Context, contactID uint) error {
	err := r.GetDB(ctx).Delete(&entity.CrmCustomerContact{}, contactID).Error
	if err != nil {
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除联系人失败").WithCause(err)
	}
	return nil
}

// SetPrimaryContact 设置主要联系人 (事务处理)
func (r *crmCustomerRepositoryImpl) SetPrimaryContact(ctx context.Context, customerID uint, contactID uint) error {
	return r.GetDB(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 将该客户的所有联系人的主要标识设为false
		err := tx.Model(&entity.CrmCustomerContact{}).
			Where("customer_id = ?", customerID).
			Update("is_primary", false).Error
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "清除主要联系人标识失败").WithCause(err)
		}

		// 2. 将指定联系人设为主要联系人
		err = tx.Model(&entity.CrmCustomerContact{}).
			Where("id = ? AND customer_id = ?", contactID, customerID).
			Update("is_primary", true).Error
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "设置主要联系人失败").WithCause(err)
		}

		return nil
	})
}
