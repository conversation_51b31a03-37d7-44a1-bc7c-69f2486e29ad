package entity

// FinCurrency 币种管理
type FinCurrency struct {
	TenantEntity
	Code      string `gorm:"column:code;type:varchar(3);uniqueIndex;not null;comment:币种代码" json:"code"`
	Name      string `gorm:"column:name;type:varchar(50);not null;comment:币种名称" json:"name"`
	Symbol    string `gorm:"column:symbol;type:varchar(10);comment:币种符号" json:"symbol"`
	Precision int    `gorm:"column:precision;type:int;default:2;comment:精度 (小数位数)" json:"precision"`
	IsEnabled bool   `gorm:"column:is_enabled;default:true;comment:是否启用" json:"isEnabled"`
}

func (FinCurrency) TableName() string {
	return "fin_currency"
}
