<template>
  <div class="outbound-notification-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>出库通知单管理</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>仓库管理</el-breadcrumb-item>
          <el-breadcrumb-item>出库管理</el-breadcrumb-item>
          <el-breadcrumb-item>出库通知单</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          @click="handleCreate"
          v-if="checkPermission('outbound:notification:create')"
        >
          <el-icon><Plus /></el-icon>
          新建通知单
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6" v-for="(stat, key) in statusStats" :key="key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stat }}</div>
              <div class="stat-label">{{ getStatusLabel(key) }}</div>
            </div>
            <div class="stat-icon" :class="`stat-icon-${key.toLowerCase()}`">
              <el-icon><Document /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="main-content">
      <VNTable
        ref="vnTableRef"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        :toolbar-config="toolbarConfig"
        :show-operations="true"
        :operation-width="200"
        operation-fixed="right"
        row-key="id"
        :selection-type="'multiple'"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @selection-change="handleSelectionChange"
        @filter-change="handleFilterChange"
        @refresh="handleRefresh"
      >
        <!-- 自定义列插槽 -->
        <template #column-notificationNo="{ row }">
          <el-link
            type="primary"
            @click="handleView(row)"
            :underline="false"
          >
            {{ row.notificationNo }}
          </el-link>
        </template>

        <template #column-priority="{ row }">
          <el-tag :type="getPriorityTagType(row.priority)">
            {{ formatPriority(row.priority) }}
          </el-tag>
        </template>

        <template #column-status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ formatStatus(row.status) }}
          </el-tag>
        </template>

        <template #column-progress="{ row }">
          <el-progress
            :percentage="getProgress(row)"
            :color="getProgressColor(row)"
            :show-text="false"
            style="width: 80px"
          />
          <span style="margin-left: 8px; font-size: 12px;">
            {{ getProgress(row) }}%
          </span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="handleEdit(row)"
            v-if="canEdit(row) && checkPermission('outbound:notification:edit')"
          >
            编辑
          </el-button>
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, row)"
            v-if="getDropdownItems(row).length > 0"
          >
            <el-button size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in getDropdownItems(row)"
                  :key="item.command"
                  :command="item.command"
                  :disabled="item.disabled"
                >
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </VNTable>
    </el-card>

    <!-- 表单对话框 -->
    <OutboundNotificationForm
      ref="formDialogRef"
      @confirm="handleFormConfirm"
      @cancel="handleFormCancel"
    />

    <!-- 库存分配对话框 -->
    <InventoryAllocation
      ref="inventoryAllocationRef"
      :notification-id="currentNotificationId"
      :details="currentDetails"
      @confirm="handleAllocationConfirm"
      @cancel="handleAllocationCancel"
    />

    <!-- 状态流转组件 -->
    <VNStatusFlow
      ref="statusFlowRef"
      business-type="OUTBOUND_NOTIFICATION"
      :business-id="currentNotificationId"
      @status-change="handleStatusChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElCard, ElRow, ElCol, ElButton, ElIcon, ElLink, ElTag, ElProgress, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessage, ElMessageBox, ElBreadcrumb, ElBreadcrumbItem } from 'element-plus'
import { Plus, Document, ArrowDown } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
import VNStatusFlow from '@/components/VNStatusFlow/index.vue'
import OutboundNotificationForm from './components/OutboundNotificationForm.vue'
import InventoryAllocation from './components/InventoryAllocation.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import type { WmsOutboundNotificationResp, WmsOutboundNotificationStatus } from '@/api/wms/outboundNotification'
import { useOutboundNotificationStore } from '@/store/wms/outboundNotification'

// Store
const outboundNotificationStore = useOutboundNotificationStore()

// 响应式数据
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const formDialogRef = ref<InstanceType<typeof OutboundNotificationForm>>()
const inventoryAllocationRef = ref<InstanceType<typeof InventoryAllocation>>()
const statusFlowRef = ref<InstanceType<typeof VNStatusFlow>>()

const selectedRows = ref<WmsOutboundNotificationResp[]>([])
const currentNotificationId = ref<number>(0)
const currentDetails = ref<any[]>([])

// 计算属性
const list = computed(() => outboundNotificationStore.list)
const total = computed(() => outboundNotificationStore.total)
const loading = computed(() => outboundNotificationStore.loading)
const statusStats = computed(() => outboundNotificationStore.statusStats)

// 分页配置
const pagination = reactive<PaginationConfig>({
  total: total,
  currentPage: computed(() => outboundNotificationStore.pagination.pageNum),
  pageSize: computed(() => outboundNotificationStore.pagination.pageSize)
})

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'notificationNo', label: '通知单号', minWidth: 140, slot: true },
  { prop: 'clientName', label: '客户名称', minWidth: 150 },
  { prop: 'clientOrderNo', label: '客户订单号', width: 120 },
  { prop: 'warehouseName', label: '仓库', width: 100 },
  { prop: 'priority', label: '优先级', width: 80, slot: true },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'progress', label: '进度', width: 120, slot: true },
  { prop: 'requiredShipDate', label: '要求发货日期', width: 120 },
  { prop: 'consigneeName', label: '收货人', width: 100 },
  { prop: 'createdAt', label: '创建时间', width: 150 }
])

// 工具栏配置
const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showFilter: true,
  showExport: checkPermission('outbound:notification:export'),
  showImport: checkPermission('outbound:notification:import'),
  batchOperations: [
    {
      label: '批量审核',
      value: 'batchApprove',
      type: 'primary',
      permission: 'outbound:notification:approve'
    },
    {
      label: '批量分配',
      value: 'batchAllocate',
      type: 'success',
      permission: 'outbound:notification:allocate'
    },
    {
      label: '批量删除',
      value: 'batchDelete',
      type: 'danger',
      permission: 'outbound:notification:delete'
    }
  ],
  filterFields: [
    {
      prop: 'notificationNo',
      label: '通知单号',
      type: 'input',
      placeholder: '请输入通知单号'
    },
    {
      prop: 'clientId',
      label: '客户',
      type: 'select',
      placeholder: '请选择客户',
      options: [] // 这里应该从API获取客户列表
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '草稿', value: 'DRAFT' },
        { label: '已计划', value: 'PLANNED' },
        { label: '已审核', value: 'APPROVED' },
        { label: '库存分配中', value: 'ALLOCATING' },
        { label: '已分配', value: 'ALLOCATED' },
        { label: '拣货中', value: 'PICKING' },
        { label: '已拣货', value: 'PICKED' },
        { label: '已发运', value: 'SHIPPED' },
        { label: '已完成', value: 'COMPLETED' },
        { label: '已取消', value: 'CANCELLED' }
      ]
    },
    {
      prop: 'priority',
      label: '优先级',
      type: 'select',
      placeholder: '请选择优先级',
      options: [
        { label: '最高', value: 1 },
        { label: '高', value: 2 },
        { label: '中', value: 3 },
        { label: '低', value: 4 },
        { label: '最低', value: 5 }
      ]
    },
    {
      prop: 'dateRange',
      label: '创建时间',
      type: 'daterange',
      placeholder: '请选择时间范围'
    }
  ]
}))

// 方法
const loadData = async () => {
  try {
    await outboundNotificationStore.fetchList()
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const handlePageChange = (page: number) => {
  outboundNotificationStore.searchForm.pageNum = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  outboundNotificationStore.searchForm.pageSize = size
  outboundNotificationStore.searchForm.pageNum = 1
  loadData()
}

const handleSelectionChange = (rows: WmsOutboundNotificationResp[]) => {
  selectedRows.value = rows
}

const handleFilterChange = (filters: Record<string, any>) => {
  // 处理日期范围
  if (filters.dateRange && filters.dateRange.length === 2) {
    filters.createdAtStart = filters.dateRange[0]
    filters.createdAtEnd = filters.dateRange[1]
    delete filters.dateRange
  }
  
  Object.assign(outboundNotificationStore.searchForm, filters)
  outboundNotificationStore.searchForm.pageNum = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const handleCreate = () => {
  formDialogRef.value?.open('create')
}

const handleView = (row: WmsOutboundNotificationResp) => {
  formDialogRef.value?.open('view', row.id)
}

const handleEdit = (row: WmsOutboundNotificationResp) => {
  formDialogRef.value?.open('edit', row.id)
}

const handleFormConfirm = () => {
  loadData()
}

const handleFormCancel = () => {
  // 表单取消处理
}

// 权限检查
const checkPermission = (permission: string): boolean => {
  // 这里应该实现实际的权限检查逻辑
  return true // 临时返回true
}

// 工具方法
const canEdit = (row: WmsOutboundNotificationResp): boolean => {
  return ['DRAFT', 'PLANNED'].includes(row.status)
}

const getPriorityTagType = (priority: number) => {
  if (priority <= 2) return 'danger'
  if (priority <= 3) return 'warning'
  return 'info'
}

const formatPriority = (priority: number) => {
  const labels = ['', '最高', '高', '中', '低', '最低']
  return labels[priority] || '未知'
}

const getStatusTagType = (status: WmsOutboundNotificationStatus) => {
  const typeMap = {
    DRAFT: 'info',
    PLANNED: 'primary',
    APPROVED: 'success',
    ALLOCATING: 'warning',
    ALLOCATED: 'success',
    PICKING: 'warning',
    PICKED: 'success',
    SHIPPED: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: WmsOutboundNotificationStatus) => {
  const labelMap = {
    DRAFT: '草稿',
    PLANNED: '已计划',
    APPROVED: '已审核',
    ALLOCATING: '库存分配中',
    ALLOCATED: '已分配',
    PICKING: '拣货中',
    PICKED: '已拣货',
    SHIPPED: '已发运',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return labelMap[status] || '未知'
}

const getProgress = (row: WmsOutboundNotificationResp) => {
  // 优先使用API返回的progress字段，否则根据状态计算
  if (row.progress !== undefined) {
    return row.progress
  }

  // 根据状态计算进度
  const progressMap = {
    DRAFT: 10,
    PLANNED: 20,
    APPROVED: 30,
    ALLOCATING: 40,
    ALLOCATED: 50,
    PICKING: 70,
    PICKED: 80,
    SHIPPED: 90,
    COMPLETED: 100,
    CANCELLED: 0
  }
  return progressMap[row.status] || 0
}

const getProgressColor = (row: WmsOutboundNotificationResp) => {
  const progress = getProgress(row)
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const getStatusLabel = (status: string) => {
  return formatStatus(status as WmsOutboundNotificationStatus)
}

const getDropdownItems = (row: WmsOutboundNotificationResp) => {
  const items = []
  
  if (row.status === 'PLANNED' && checkPermission('outbound:notification:approve')) {
    items.push({ command: 'approve', label: '审核通过', disabled: false })
  }
  
  if (row.status === 'APPROVED' && checkPermission('outbound:notification:allocate')) {
    items.push({ command: 'allocate', label: '库存分配', disabled: false })
  }
  
  if (row.status === 'ALLOCATED' && checkPermission('outbound:notification:picking')) {
    items.push({ command: 'generatePicking', label: '生成拣货任务', disabled: false })
  }
  
  if (['DRAFT', 'PLANNED'].includes(row.status) && checkPermission('outbound:notification:delete')) {
    items.push({ command: 'delete', label: '删除', disabled: false })
  }
  
  if (!['COMPLETED', 'CANCELLED'].includes(row.status) && checkPermission('outbound:notification:cancel')) {
    items.push({ command: 'cancel', label: '取消', disabled: false })
  }
  
  items.push({ command: 'statusFlow', label: '状态流转', disabled: false })
  
  return items
}

const handleDropdownCommand = async (command: string, row: WmsOutboundNotificationResp) => {
  switch (command) {
    case 'approve':
      await handleApprove(row)
      break
    case 'allocate':
      await handleAllocate(row)
      break
    case 'generatePicking':
      await handleGeneratePicking(row)
      break
    case 'delete':
      await handleDelete(row)
      break
    case 'cancel':
      await handleCancel(row)
      break
    case 'statusFlow':
      handleStatusFlow(row)
      break
  }
}

const handleApprove = async (row: WmsOutboundNotificationResp) => {
  try {
    await ElMessageBox.confirm('确定要审核通过这个出库通知单吗？', '确认审核', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await outboundNotificationStore.approve(row.id, '审核通过')
    ElMessage.success('审核成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审核失败')
      console.error(error)
    }
  }
}

const handleAllocate = (row: WmsOutboundNotificationResp) => {
  currentNotificationId.value = row.id
  currentDetails.value = row.details || []
  inventoryAllocationRef.value?.open()
}

const handleAllocationConfirm = () => {
  ElMessage.success('库存分配成功')
  loadData()
}

const handleAllocationCancel = () => {
  // 分配取消处理
}

const handleGeneratePicking = async (row: WmsOutboundNotificationResp) => {
  try {
    await ElMessageBox.confirm('确定要生成拣货任务吗？', '确认生成', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const result = await outboundNotificationStore.generatePickingTask(row.id)
    ElMessage.success(`拣货任务生成成功，任务编号：${result.taskNo}`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('生成拣货任务失败')
      console.error(error)
    }
  }
}

const handleDelete = async (row: WmsOutboundNotificationResp) => {
  try {
    await ElMessageBox.confirm('确定要删除这个出库通知单吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await outboundNotificationStore.remove(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const handleCancel = async (row: WmsOutboundNotificationResp) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消出库通知单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '请输入取消原因'
    })
    
    await outboundNotificationStore.cancel(row.id, reason)
    ElMessage.success('取消成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消失败')
      console.error(error)
    }
  }
}

const handleStatusFlow = (row: WmsOutboundNotificationResp) => {
  currentNotificationId.value = row.id
  statusFlowRef.value?.open()
}

const handleStatusChange = () => {
  loadData()
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.outbound-notification-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.main-content {
  background: #fff;
}
</style>
