-- 初始化物料编码规则
-- 注意：这里使用的是示例数据，实际使用时需要根据具体的租户ID和账套ID进行调整

-- 插入物料默认编码规则
INSERT INTO sys_code_rule (
    tenant_id, 
    account_book_id, 
    created_by, 
    updated_by,
    rule_code, 
    rule_name, 
    business_type, 
    code_format, 
    separator, 
    reset_frequency, 
    sequence_length, 
    sequence_start, 
    current_sequence, 
    status, 
    is_default, 
    remark
) VALUES (
    1, -- tenant_id，需要根据实际情况调整
    1, -- account_book_id，需要根据实际情况调整
    1, -- created_by，需要根据实际情况调整
    1, -- updated_by，需要根据实际情况调整
    'MTL_DEFAULT', 
    '物料默认编码规则', 
    'MATERIAL', 
    'M{YYYY}{MM}{SEQ:4}', 
    '', 
    'MONTHLY', 
    4, 
    1, 
    0, 
    'ACTIVE', 
    true, 
    '物料编码格式：M + 年月 + 4位序号，如M202412001'
) ON CONFLICT (tenant_id, account_book_id, rule_code) 
WHERE deleted_at IS NULL 
DO UPDATE SET
    rule_name = EXCLUDED.rule_name,
    code_format = EXCLUDED.code_format,
    separator = EXCLUDED.separator,
    reset_frequency = EXCLUDED.reset_frequency,
    sequence_length = EXCLUDED.sequence_length,
    sequence_start = EXCLUDED.sequence_start,
    status = EXCLUDED.status,
    is_default = EXCLUDED.is_default,
    remark = EXCLUDED.remark,
    updated_at = CURRENT_TIMESTAMP;

-- 查询验证
SELECT 
    id,
    rule_code,
    rule_name,
    business_type,
    code_format,
    is_default,
    status
FROM sys_code_rule 
WHERE business_type = 'MATERIAL' 
AND deleted_at IS NULL; 