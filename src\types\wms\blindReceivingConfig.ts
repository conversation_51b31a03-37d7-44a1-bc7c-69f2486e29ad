 // ==================== 枚举类型定义 ====================

// 配置层级枚举
export type ConfigLevel = 
| 'SYSTEM'      // 系统级
| 'WAREHOUSE'   // 仓库级
| 'CLIENT'      // 客户级
| 'USER'        // 用户级

// 盲收策略枚举
export type BlindReceivingStrategy = 
| 'STRICT'      // 严格模式：不允许盲收
| 'SUPPLEMENT'  // 补录模式：允许盲收需补录
| 'FULL'        // 完全模式：允许盲收无需补录

// 审批状态枚举
export type ApprovalStatus = 
| 'PENDING'     // 待审批
| 'APPROVED'    // 已批准
| 'REJECTED'    // 已拒绝
| 'CANCELLED'   // 已取消

// 补录状态枚举
export type SupplementStatus = 
| 'PENDING'     // 待补录
| 'COMPLETED'   // 已完成
| 'OVERDUE'     // 已逾期

// ==================== 基础实体类型 ====================

// 盲收配置实体
export interface BlindReceivingConfigEntity {
id: number
createdAt: string
updatedAt: string
createdBy: number
updatedBy: number
tenantId: number
accountBookId: number
configLevel: ConfigLevel
configTargetId?: number
strategy: BlindReceivingStrategy
supplementTimeLimit?: number
requiresApproval: boolean
approvalUserRoles?: string
maxBlindReceivingQty?: number
isActive: boolean
priority: number
remark?: string
}

// 盲收验证记录实体
export interface BlindReceivingValidationEntity {
id: number
createdAt: string
updatedAt: string
tenantId: number
accountBookId: number
warehouseId: number
clientId?: number
userId?: number
configId: number
strategy: BlindReceivingStrategy
requestQuantity: number
isAllowed: boolean
validationMessage: string
approvalStatus?: ApprovalStatus
supplementStatus?: SupplementStatus
supplementDeadline?: string
actualQuantity?: number
remark?: string
}

// 盲收统计实体
export interface BlindReceivingStatsEntity {
id: number
createdAt: string
tenantId: number
accountBookId: number
warehouseId?: number
statsDate: string
totalBlindReceivings: number
totalBlindQuantity: number
approvedCount: number
rejectedCount: number
pendingApprovalCount: number
supplementedCount: number
pendingSupplementCount: number
overdueSupplementCount: number
averageProcessingTime: number
strategyDistribution: Record<BlindReceivingStrategy, number>
}

// ==================== DTO 类型定义 ====================

// 盲收配置创建请求DTO
export interface BlindReceivingConfigCreateReq {
configLevel: ConfigLevel
configTargetId?: number
strategy: BlindReceivingStrategy
supplementTimeLimit?: number
requiresApproval?: boolean
approvalUserRoles?: string
maxBlindReceivingQty?: number
isActive?: boolean
priority?: number
remark?: string
}

// 盲收配置更新请求DTO
export interface BlindReceivingConfigUpdateReq {
configLevel?: ConfigLevel
configTargetId?: number
strategy?: BlindReceivingStrategy
supplementTimeLimit?: number
requiresApproval?: boolean
approvalUserRoles?: string
maxBlindReceivingQty?: number
isActive?: boolean
priority?: number
remark?: string
}

// 盲收配置查询请求DTO
export interface BlindReceivingConfigQueryReq {
pageNum?: number
pageSize?: number
configLevel?: ConfigLevel
configTargetId?: number
strategy?: BlindReceivingStrategy
strategies?: BlindReceivingStrategy[]
requiresApproval?: boolean
isActive?: boolean
priorityMin?: number
priorityMax?: number
createdAtFrom?: string
createdAtTo?: string
sort?: string
includeTargetInfo?: boolean
}

// 盲收验证请求DTO
export interface BlindReceivingValidationReq {
warehouseId: number
clientId?: number
userId?: number
itemId?: number
quantity: number
sourceType?: string
sourceId?: number
remark?: string
}

// 审批处理请求DTO
export interface BlindReceivingApprovalReq {
validationId: number
approvalStatus: ApprovalStatus
approvalRemark?: string
}

// 补录处理请求DTO
export interface BlindReceivingSupplementReq {
validationId: number
actualQuantity: number
supplementRemark?: string
inboundNotificationId?: number
}

// 批量创建请求DTO
export interface BlindReceivingConfigBatchCreateReq {
configs: BlindReceivingConfigCreateReq[]
overwriteExisting?: boolean
}

// 批量更新请求DTO
export interface BlindReceivingConfigBatchUpdateReq {
updates: Array<{
  id: number
  data: BlindReceivingConfigUpdateReq
}>
}

// 统计查询请求DTO
export interface BlindReceivingStatsQueryReq {
warehouseId?: number
clientId?: number
dateFrom?: string
dateTo?: string
strategy?: BlindReceivingStrategy
groupBy?: 'day' | 'week' | 'month'
}

// ==================== VO 类型定义 ====================

// 盲收配置响应VO
export interface BlindReceivingConfigVO {
id: number
createdAt: string
updatedAt: string
createdBy: number
updatedBy: number
tenantId: number
accountBookId: number
configLevel: ConfigLevel
configTargetId?: number
strategy: BlindReceivingStrategy
supplementTimeLimit?: number
requiresApproval: boolean
approvalUserRoles?: string
maxBlindReceivingQty?: number
isActive: boolean
priority: number
remark?: string

// 扩展字段
configLevelName?: string
configTargetName?: string
strategyName?: string
createdByName?: string
updatedByName?: string
approvalUserRoleNames?: string[]
accountBookName?: string

// 统计字段
usageCount?: number
lastUsedAt?: string
effectiveRanking?: number
}

// 盲收验证响应VO
export interface BlindReceivingValidationVO {
id?: number
isAllowed: boolean
strategy: BlindReceivingStrategy
requiresApproval: boolean
maxQuantityLimit?: number
supplementDeadline?: string
validationMessage: string
configLevel: ConfigLevel
configId: number
approvalUserRoles?: string[]

// 流程信息
approvalStatus?: ApprovalStatus
supplementStatus?: SupplementStatus
processedAt?: string
processedBy?: number
processedByName?: string

// 扩展信息
warehouseName?: string
clientName?: string
userName?: string
itemName?: string
itemSku?: string
}

// 可用目标响应VO
export interface AvailableTargetVO {
id: number
name: string
code?: string
type: string
description?: string
isActive: boolean
}

// 盲收统计响应VO
export interface BlindReceivingStatsVO {
// 总体统计
totalBlindReceivings: number
totalBlindQuantity: number

// 审批统计
approvedCount: number
rejectedCount: number
pendingApprovalCount: number
approvalRate: number

// 补录统计
supplementedCount: number
pendingSupplementCount: number
overdueSupplementCount: number
supplementRate: number

// 性能统计
averageProcessingTime: number
averageSupplementTime: number

// 策略分布
strategyDistribution: Record<BlindReceivingStrategy, number>

// 趋势数据
dailyStats?: Array<{
  date: string
  count: number
  quantity: number
  strategy: BlindReceivingStrategy
  approvalRate: number
  supplementRate: number
}>

// 排行数据
topWarehouses?: Array<{
  warehouseId: number
  warehouseName: string
  count: number
  quantity: number
}>

topClients?: Array<{
  clientId: number
  clientName: string
  count: number
  quantity: number
}>

topUsers?: Array<{
  userId: number
  userName: string
  count: number
  quantity: number
}>
}

// ==================== 分页和响应类型 ====================

// 分页结果类型
export interface PageResult<T> {
list: T[]
total: number
pageNum: number
pageSize: number
pages: number
}

// 批量操作结果
export interface BatchOperationResult {
totalCount: number
successCount: number
failureCount: number
successIds: number[]
failureReasons: string[]
warnings?: string[]
}

// API响应基础类型
export interface ApiResponse<T = any> {
code: number
message: string
data: T
timestamp?: string
}

// ==================== 表单和界面类型 ====================

// 配置表单模型
export interface BlindReceivingConfigFormModel {
id?: number
configLevel: ConfigLevel
configTargetId?: number
strategy: BlindReceivingStrategy
supplementTimeLimit?: number
requiresApproval: boolean
approvalUserRoles?: string[]
maxBlindReceivingQty?: number
isActive: boolean
priority: number
remark?: string
}

// 搜索表单模型
export interface BlindReceivingConfigSearchModel {
configLevel?: ConfigLevel
configTargetId?: number
strategy?: BlindReceivingStrategy
requiresApproval?: boolean
isActive?: boolean
priorityRange?: [number, number]
dateRange?: [string, string]
keyword?: string
}

// 验证表单模型
export interface BlindReceivingValidationFormModel {
warehouseId: number
clientId?: number
userId?: number
itemId?: number
quantity: number
sourceType?: string
sourceId?: number
remark?: string
}

// 表格列配置
export interface BlindReceivingConfigTableColumn {
prop: string
label: string
width?: number
sortable?: boolean
filterable?: boolean
formatter?: (row: BlindReceivingConfigVO) => string
}

// ==================== 常量定义 ====================

// 配置层级选项
export const CONFIG_LEVEL_OPTIONS = [
{ label: '系统级', value: 'SYSTEM' as ConfigLevel, priority: 4 },
{ label: '仓库级', value: 'WAREHOUSE' as ConfigLevel, priority: 3 },
{ label: '客户级', value: 'CLIENT' as ConfigLevel, priority: 2 },
{ label: '用户级', value: 'USER' as ConfigLevel, priority: 1 }
]

// 策略选项
export const STRATEGY_OPTIONS = [
{ 
  label: '严格模式', 
  value: 'STRICT' as BlindReceivingStrategy, 
  description: '不允许盲收',
  color: 'danger'
},
{ 
  label: '补录模式', 
  value: 'SUPPLEMENT' as BlindReceivingStrategy, 
  description: '允许盲收需补录',
  color: 'warning'
},
{ 
  label: '完全模式', 
  value: 'FULL' as BlindReceivingStrategy, 
  description: '允许盲收无需补录',
  color: 'success'
}
]

// 审批状态选项
export const APPROVAL_STATUS_OPTIONS = [
{ label: '待审批', value: 'PENDING' as ApprovalStatus, color: 'warning' },
{ label: '已批准', value: 'APPROVED' as ApprovalStatus, color: 'success' },
{ label: '已拒绝', value: 'REJECTED' as ApprovalStatus, color: 'danger' },
{ label: '已取消', value: 'CANCELLED' as ApprovalStatus, color: 'info' }
]

// 补录状态选项
export const SUPPLEMENT_STATUS_OPTIONS = [
{ label: '待补录', value: 'PENDING' as SupplementStatus, color: 'warning' },
{ label: '已完成', value: 'COMPLETED' as SupplementStatus, color: 'success' },
{ label: '已逾期', value: 'OVERDUE' as SupplementStatus, color: 'danger' }
]

// 默认配置
export const DEFAULT_CONFIG = {
SUPPLEMENT_TIME_LIMIT: 72, // 默认72小时
MAX_BLIND_RECEIVING_QTY: 1000, // 默认最大数量
DEFAULT_PRIORITY: 5, // 默认优先级
PAGE_SIZE: 20 // 默认分页大小
}