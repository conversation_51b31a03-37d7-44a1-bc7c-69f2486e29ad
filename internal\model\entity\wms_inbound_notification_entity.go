package entity

import (
	"time"

	"gorm.io/gorm"
)

// WmsInboundNotificationStatus 定义入库通知单状态常量
type WmsInboundNotificationStatus string

const (
	InboundNotificationStatusDraft             WmsInboundNotificationStatus = "DRAFT"              // 草稿
	InboundNotificationStatusPlanned           WmsInboundNotificationStatus = "PLANNED"            // 已计划/待收货
	InboundNotificationStatusArrived           WmsInboundNotificationStatus = "ARRIVED"            // 已到货 (物理到达，可选状态)
	InboundNotificationStatusReceiving         WmsInboundNotificationStatus = "RECEIVING"          // 收货中
	InboundNotificationStatusPartiallyReceived WmsInboundNotificationStatus = "PARTIALLY_RECEIVED" // 部分收货
	InboundNotificationStatusReceived          WmsInboundNotificationStatus = "RECEIVED"           // 收货完成 (所有预期货物已对照记录)
	InboundNotificationStatusClosed            WmsInboundNotificationStatus = "CLOSED"             // 已关闭 (关联的货物已完成上架)
	InboundNotificationStatusCancelled         WmsInboundNotificationStatus = "CANCELLED"          // 已取消
)

// WmsInboundNotification 对应数据库中的 wms_inbound_notifications 表
// 存储入库通知单头信息 (ASN)
type WmsInboundNotification struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	NotificationNo      string     `gorm:"column:notification_no;type:varchar(50);not null;comment:通知单号" json:"notificationNo"`                     // 通知单号 (业务唯一键)
	NotificationType    string     `gorm:"column:notification_type;type:varchar(30);comment:通知类型" json:"notificationType"`                          // 通知类型 (例如: PO, RETURN, TRANSFER)
	WarehouseID         uint       `gorm:"column:warehouse_id;comment:目标仓库ID" json:"warehouseId"`                                                   // 目标仓库ID
	ClientID            uint       `gorm:"column:client_id;not null;comment:委托客户ID" json:"clientId"`                                                // 委托客户ID
	SourceDocNo         *string    `gorm:"column:source_doc_no;type:varchar(100);comment:关联客户指令号/源单据号" json:"sourceDocNo"`                          // 关联客户指令号/源单据号
	SupplierShipper     *string    `gorm:"column:supplier_shipper;type:varchar(100);comment:供应商/发货方" json:"supplierShipper"`                        // 供应商/发货方 名称或代码
	ExpectedArrivalDate *time.Time `gorm:"column:expected_arrival_date;type:date;comment:预期到货日期" json:"-"`                                          // 预期到货日期 (使用 *time.Time 处理可空日期)，只做为数据库字段，不作为显示字段，需要勾子转换显示为ExpectedArrivalDateStr
	Status              string     `gorm:"column:status;type:wms_inbound_notification_status;not null;default:'DRAFT';comment:通知单状态" json:"status"` // 通知单状态 (使用数据库定义的 ENUM)
	Remark              *string    `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                                        // 备注

	// 扩展显示字段
	ExpectedArrivalDateStr *string `gorm:"-" json:"expectedArrivalDate"` // 预期到货日期 因为只要日期，不要时间，所以以此字段为扩展字段

	// 关联字段 (反向关联)
	Details []WmsInboundNotificationDetail `gorm:"foreignKey:NotificationID;references:ID" json:"details,omitempty"` // 关联的明细行列表
}

// TableName 指定数据库表名方法
func (WmsInboundNotification) TableName() string {
	return "wms_inbound_notification"
}

func (w *WmsInboundNotification) BeforeSave(tx *gorm.DB) (err error) {
	if w.ExpectedArrivalDateStr != nil && *w.ExpectedArrivalDateStr != "" {
		expectedArrivalDate, err := time.Parse("2006-01-02", *w.ExpectedArrivalDateStr)
		if err != nil {
			return err
		}
		w.ExpectedArrivalDate = &expectedArrivalDate
	} else {
		w.ExpectedArrivalDate = nil
	}
	return
}

func (w *WmsInboundNotification) AfterFind(tx *gorm.DB) (err error) {
	if w.ExpectedArrivalDate != nil {
		dateStr := w.ExpectedArrivalDate.Format("2006-01-02")
		w.ExpectedArrivalDateStr = &dateStr
	}
	return
}
