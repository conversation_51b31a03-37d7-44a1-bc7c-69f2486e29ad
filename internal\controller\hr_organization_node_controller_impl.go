package controller

import (
	// Import strconv for potential future use if needed

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // Use alias
	// 确保 response 包已导入
	// "backend/pkg/constant" // Keep if needed, remove if BaseController handles codes
	// "backend/pkg/logger" // Keep if needed, remove if BaseController handles logging
)

// OrganizationNodeControllerImpl 组织节点控制器 (重命名)
type OrganizationNodeControllerImpl struct { // <<< 重命名
	*BaseControllerImpl
	organizationNodeService service.OrganizationNodeService
}

// NewOrganizationNodeControllerImpl 创建组织节点控制器 (重命名)
func NewOrganizationNodeControllerImpl(cm *ControllerManager) *OrganizationNodeControllerImpl { // <<< 重命名并修改返回类型
	base := NewBaseController(cm)
	nodeService := cm.GetServiceManager().GetOrganizationNodeService()
	if nodeService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 OrganizationNodeService 实例")
	}
	return &OrganizationNodeControllerImpl{ // <<< 修改返回类型
		BaseControllerImpl:      base,
		organizationNodeService: nodeService,
	}
}

// CreateNode 创建组织节点
func (c *OrganizationNodeControllerImpl) CreateNode(ctx iris.Context) { // <<< 修改接收者类型
	opName := "创建组织节点"
	var req dto.OrganizationNodeCreateOrUpdateDTO
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "organization_node")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	result, err := c.organizationNodeService.CreateNode(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, result)
}

// UpdateNode 更新组织节点
func (c *OrganizationNodeControllerImpl) UpdateNode(ctx iris.Context) { // <<< 修改接收者类型
	opName := "更新组织节点"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "organization_node")

	idUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的节点ID参数").WithCause(err), opName)
		return
	}
	id := uint(idUint64)

	var req dto.OrganizationNodeCreateOrUpdateDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	result, err := c.organizationNodeService.UpdateNode(ctx.Request().Context(), id, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, result)
}

// DeleteNode 删除组织节点
func (c *OrganizationNodeControllerImpl) DeleteNode(ctx iris.Context) { // <<< 修改接收者类型
	opName := "删除组织节点"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "organization_node")

	idUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的节点ID参数").WithCause(err), opName)
		return
	}
	id := uint(idUint64)

	err = c.organizationNodeService.DeleteNode(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetNodeByID 获取指定ID节点
func (c *OrganizationNodeControllerImpl) GetNodeByID(ctx iris.Context) { // <<< 修改接收者类型
	opName := "获取组织节点详情"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "organization_node")

	idUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的节点ID参数").WithCause(err), opName)
		return
	}
	id := uint(idUint64)

	result, err := c.organizationNodeService.GetNodeByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, result)
}

// GetOrganizationTree 获取组织树
func (c *OrganizationNodeControllerImpl) GetOrganizationTree(ctx iris.Context) { // <<< 修改接收者类型
	opName := "获取组织树"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "organization_node")

	// 从查询参数中获取可选的 companyID
	companyIDQuery := ctx.URLParamIntDefault("companyID", 0)
	var companyIDPtr *uint
	if companyIDQuery > 0 {
		val := uint(companyIDQuery)
		companyIDPtr = &val
	}

	result, err := c.organizationNodeService.GetOrganizationTree(ctx.Request().Context(), companyIDPtr)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, result)
}

// GetNodeList 获取组织节点列表
func (c *OrganizationNodeControllerImpl) GetNodeList(ctx iris.Context) { // <<< 修改接收者类型
	opName := "获取组织节点列表"
	var queryDTO dto.OrganizationNodePageQueryDTO
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "organization_node")

	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取查询参数失败").WithCause(err), opName)
		return
	}

	// 当前 dto.OrganizationNodeQueryDTO 没有内嵌 PageQuery 字段。
	// 为了符合分页规范，dto.OrganizationNodeQueryDTO 应该内嵌一个 response.PageQuery 类型的字段 (例如名为 PageQuery)。
	// 然后取消下面代码的注释，并确保 queryDTO 结构已更新。
	/*
	   // 假设 dto.OrganizationNodeQueryDTO 最终会内嵌 PageQuery response.PageQuery
	   pageQueryFromBuild := response.BuildPageQuery(ctx)
	   if queryDTO.PageQuery == nil {
	       queryDTO.PageQuery = &response.PageQuery{} // 初始化，如果它是指针类型
	   }
	   queryDTO.PageQuery.PageNum = pageQueryFromBuild.PageNum
	   queryDTO.PageQuery.PageSize = pageQueryFromBuild.PageSize
	   queryDTO.PageQuery.Sort = pageQueryFromBuild.Sort
	*/

	// 目前，我们直接将 queryDTO 传递给服务层。
	// 服务层需要能够从当前的 queryDTO 结构中处理分页和排序参数。
	result, err := c.organizationNodeService.GetNodeList(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, result)
}

// UpdateNodeStatus 更新组织节点状态
func (c *OrganizationNodeControllerImpl) UpdateNodeStatus(ctx iris.Context) { // <<< 修改接收者类型
	opName := "更新组织节点状态"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "organization_node")

	idUint64, err := ctx.Params().GetUint64("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的节点ID参数").WithCause(err), opName)
		return
	}
	id := uint(idUint64)

	var req dto.UpdateNodeStatusDTO // Ensure this DTO exists
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	err = c.organizationNodeService.UpdateNodeStatus(ctx.Request().Context(), id, req.Status)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "状态更新成功", nil)
}
