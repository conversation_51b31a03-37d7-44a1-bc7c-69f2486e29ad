<template>
  <div class="drawer-examples-page">
    <h2>VNDrawer 抽屉组件示例</h2>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础用法 (从右侧滑出)</span>
        </div>
      </template>
      <el-button @click="visible1 = true">打开基础抽屉</el-button>
      <VNDrawer
        v-model="visible1"
        title="基础抽屉标题"
        :show-confirm-button="true"
        confirm-button-text="保存"
        @confirm="handleConfirm(1)"
        @close="logEvent('Drawer 1 closed')"
      >
        <p>这是抽屉的主要内容区域。</p>
        <p>可以放置表单、详情或其他信息。</p>
      </VNDrawer>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>不同方向与尺寸 (从左侧滑出)</span>
        </div>
      </template>
      <el-button @click="visible2 = true">打开左侧抽屉 (50%)</el-button>
      <VNDrawer
        v-model="visible2"
        title="设置面板"
        direction="ltr"
        size="50%"
        :show-cancel-button="false"
        :show-confirm-button="true"
        footer-align="center"
        confirm-button-text="应用设置"
        @confirm="handleConfirm(2)"
      >
        <el-form label-position="top">
          <el-form-item label="主题颜色">
             <el-color-picker v-model="themeColor" />
          </el-form-item>
           <el-form-item label="启用暗黑模式">
             <el-switch v-model="darkMode" />
          </el-form-item>
        </el-form>
      </VNDrawer>
    </el-card>

     <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>自定义头部和底部 (从底部滑出)</span>
        </div>
      </template>
      <el-button @click="visible3 = true">打开自定义抽屉</el-button>
      <VNDrawer
        v-model="visible3"
        direction="btt"
        size="300px"
        :with-header="false" 
      >
        <template #header>
          <div style="padding: 10px 20px; border-bottom: 1px solid #eee; font-weight: bold;">
             自定义标题区域 <el-icon><Setting /></el-icon>
          </div>
        </template>

        <div>
          <p>这里是主要内容...</p>
          <p>...</p>
        </div>

        <template #footer>
           <div style="display: flex; justify-content: space-between; padding: 10px 20px;">
                <span>共 3 个项目</span>
                <div>
                    <el-button size="small" @click="visible3 = false">关闭</el-button>
                    <el-button size="small" type="success">导出</el-button>
                </div>
            </div>
        </template>
      </VNDrawer>
    </el-card>

      <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>嵌套抽屉 (不推荐，但可实现)</span>
        </div>
      </template>
       <el-button @click="outerDrawerVisible = true">打开外层抽屉</el-button>
        <VNDrawer v-model="outerDrawerVisible" title="外层抽屉" size="60%">
          <p>外层抽屉内容</p>
          <el-button @click="innerDrawerVisible = true">打开内层抽屉</el-button>
          
          <VNDrawer 
            v-model="innerDrawerVisible" 
            title="内层抽屉" 
            size="40%" 
            append-to-body 
            :show-confirm-button="true"
            @confirm="() => { innerDrawerVisible = false; ElMessage.info('内层确认'); }"
          >
              <span>这是内层抽屉。使用了 `append-to-body`。</span>
          </VNDrawer>
      </VNDrawer>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNDrawer from './index.vue';
import { ElButton, ElCard, ElMessage, ElForm, ElFormItem, ElColorPicker, ElSwitch, ElIcon } from 'element-plus';
import { Setting } from '@element-plus/icons-vue'; // 引入图标

const visible1 = ref(false);
const visible2 = ref(false);
const visible3 = ref(false);
const outerDrawerVisible = ref(false);
const innerDrawerVisible = ref(false);

const themeColor = ref('#409EFF');
const darkMode = ref(false);

const logEvent = (msg: string) => {
  console.log(msg);
};

const handleConfirm = (drawerNum: number) => {
  ElMessage.success(`抽屉 ${drawerNum} 点击了确认`);
  if (drawerNum === 1) visible1.value = false;
  if (drawerNum === 2) visible2.value = false;
  // Drawer 3 uses custom footer, no confirm event emitted by default
};

</script>

<style scoped>
.drawer-examples-page {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

p {
  margin: 15px 0;
}
</style>