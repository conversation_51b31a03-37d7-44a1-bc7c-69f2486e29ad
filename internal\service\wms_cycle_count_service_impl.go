package service

import (
	"context"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
)

// WmsCycleCountService 盘点服务接口
type WmsCycleCountService interface {
	BaseService

	// 盘点计划管理
	CreatePlan(ctx context.Context, req *dto.WmsCycleCountPlanCreateReq) (*vo.WmsCycleCountPlanVO, error)
	UpdatePlan(ctx context.Context, id uint, req *dto.WmsCycleCountPlanUpdateReq) (*vo.WmsCycleCountPlanVO, error)
	DeletePlan(ctx context.Context, id uint) error
	GetPlan(ctx context.Context, id uint) (*vo.WmsCycleCountPlanVO, error)
	GetPlanPage(ctx context.Context, req *dto.WmsCycleCountPlanQueryReq) (*vo.PageResult[vo.WmsCycleCountPlanVO], error)

	// 盘点任务管理
	CreateTask(ctx context.Context, req *dto.WmsCycleCountTaskCreateReq) (*vo.WmsCycleCountTaskVO, error)
	UpdateTask(ctx context.Context, id uint, req *dto.WmsCycleCountTaskUpdateReq) (*vo.WmsCycleCountTaskVO, error)
	DeleteTask(ctx context.Context, id uint) error
	GetTask(ctx context.Context, id uint) (*vo.WmsCycleCountTaskVO, error)
	GetTaskPage(ctx context.Context, req *dto.WmsCycleCountTaskQueryReq) (*vo.PageResult[vo.WmsCycleCountTaskVO], error)

	// 盘点执行
	StartPlan(ctx context.Context, planId uint) error
	CompletePlan(ctx context.Context, planId uint) error
	StartTask(ctx context.Context, taskId uint, counterUserId uint) error
	CompleteTask(ctx context.Context, taskId uint, req *dto.WmsCycleCountTaskCompleteReq) error

	// 盘点差异处理
	ConfirmVariance(ctx context.Context, taskId uint, req *dto.WmsCycleCountVarianceReq) error
	GenerateAdjustment(ctx context.Context, taskId uint) (*vo.WmsInventoryAdjustmentVO, error)

	// 批量操作
	BatchCreateTasks(ctx context.Context, planId uint, req *dto.WmsCycleCountTaskBatchCreateReq) ([]*vo.WmsCycleCountTaskVO, error)
	BatchAssignCounter(ctx context.Context, taskIds []uint, counterUserId uint) error

	// 统计分析
	GetPlanStats(ctx context.Context, planId uint) (*vo.WmsCycleCountPlanStatsVO, error)
	GetCounterStats(ctx context.Context, counterUserId uint, startDate, endDate string) (*vo.WmsCycleCountCounterStatsVO, error)
}

// wmsCycleCountServiceImpl 盘点服务实现
type wmsCycleCountServiceImpl struct {
	BaseServiceImpl
}

// NewWmsCycleCountService 创建盘点服务实例
func NewWmsCycleCountService(sm *ServiceManager) WmsCycleCountService {
	return &wmsCycleCountServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// CreatePlan 创建盘点计划
func (s *wmsCycleCountServiceImpl) CreatePlan(ctx context.Context, req *dto.WmsCycleCountPlanCreateReq) (*vo.WmsCycleCountPlanVO, error) {
	var result *vo.WmsCycleCountPlanVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountPlanRepository()
		codeService := s.GetServiceManager().GetCodeGenerationService()

		// 生成盘点计划编号
		codeRes, err := codeService.GenerateCode(ctx, &dto.CodeGenerationReq{
			BusinessType: "WMS_CYCLE_COUNT_PLAN",
		})
		if err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "生成盘点计划编号失败").WithCause(err)
		}

		// 创建盘点计划
		plan := &entity.WmsCycleCountPlan{
			PlanNo:            codeRes.GeneratedCode,
			PlanName:          req.PlanName,
			WarehouseID:       &req.WarehouseID,
			CountType:         req.CountType,
			CountStrategy:     &req.CountStrategy,
			Status:            entity.CountPlanStatusDraft,
			PlannedDate:       req.PlannedDate,
			ResponsibleUserID: &req.ResponsibleUserID,
			Description:       req.Description,
		}

		if err := repo.Create(ctx, plan); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "创建盘点计划失败").WithCause(err)
		}

		// 转换为VO
		result = &vo.WmsCycleCountPlanVO{}
		if err := copier.Copy(result, plan); err != nil {
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// UpdatePlan 更新盘点计划
func (s *wmsCycleCountServiceImpl) UpdatePlan(ctx context.Context, id uint, req *dto.WmsCycleCountPlanUpdateReq) (*vo.WmsCycleCountPlanVO, error) {
	var result *vo.WmsCycleCountPlanVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountPlanRepository()

		// 获取现有记录
		plan, err := repo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点计划失败").WithCause(err)
		}
		if plan == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点计划不存在")
		}

		// 检查状态是否允许修改
		if plan.Status != entity.CountPlanStatusDraft {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有草稿状态的计划才能修改")
		}

		// 更新字段
		if req.PlanName != nil {
			plan.PlanName = *req.PlanName
		}
		if req.PlannedDate != nil {
			plan.PlannedDate = *req.PlannedDate
		}
		if req.ResponsibleUserID != nil {
			plan.ResponsibleUserID = req.ResponsibleUserID
		}
		if req.Description != nil {
			plan.Description = req.Description
		}

		if err := repo.Update(ctx, plan); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新盘点计划失败").WithCause(err)
		}

		// 转换为VO
		result = &vo.WmsCycleCountPlanVO{}
		if err := copier.Copy(result, plan); err != nil {
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// DeletePlan 删除盘点计划
func (s *wmsCycleCountServiceImpl) DeletePlan(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountPlanRepository()

		// 检查计划是否存在
		plan, err := repo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点计划失败").WithCause(err)
		}
		if plan == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点计划不存在")
		}

		// 检查状态是否允许删除
		if plan.Status != entity.CountPlanStatusDraft {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有草稿状态的计划才能删除")
		}

		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除盘点计划失败").WithCause(err)
		}

		return nil
	})
}

// GetPlan 根据ID获取盘点计划
func (s *wmsCycleCountServiceImpl) GetPlan(ctx context.Context, id uint) (*vo.WmsCycleCountPlanVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsCycleCountPlanRepository()

	plan, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点计划失败").WithCause(err)
	}
	if plan == nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点计划不存在")
	}

	result := &vo.WmsCycleCountPlanVO{}
	if err := copier.Copy(result, plan); err != nil {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
	}

	return result, nil
}

// GetPlanPage 分页查询盘点计划
func (s *wmsCycleCountServiceImpl) GetPlanPage(ctx context.Context, req *dto.WmsCycleCountPlanQueryReq) (*vo.PageResult[vo.WmsCycleCountPlanVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsCycleCountPlanRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询盘点计划失败").WithCause(err)
	}

	// 转换为VO
	result := &vo.PageResult[vo.WmsCycleCountPlanVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsCycleCountPlanVO, 0),
	}

	// 处理 pageResult.List，它是 interface{} 类型
	if pageResult.List != nil {
		if records, ok := pageResult.List.([]*entity.WmsCycleCountPlan); ok {
			for _, record := range records {
				planVO := &vo.WmsCycleCountPlanVO{}
				if err := copier.Copy(planVO, record); err != nil {
					return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
				}
				result.List = append(result.List, planVO)
			}
		}
	}

	return result, nil
}

// StartPlan 启动盘点计划
func (s *wmsCycleCountServiceImpl) StartPlan(ctx context.Context, planId uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountPlanRepository()

		// 获取计划
		plan, err := repo.FindByID(ctx, planId)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点计划失败").WithCause(err)
		}
		if plan == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点计划不存在")
		}

		// 检查状态
		if plan.Status != entity.CountPlanStatusApproved {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有已审批的计划才能启动")
		}

		// 更新状态
		plan.Status = entity.CountPlanStatusInProgress

		if err := repo.Update(ctx, plan); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新盘点计划状态失败").WithCause(err)
		}

		return nil
	})
}

// CompletePlan 完成盘点计划
func (s *wmsCycleCountServiceImpl) CompletePlan(ctx context.Context, planId uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountPlanRepository()

		// 获取计划
		plan, err := repo.FindByID(ctx, planId)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点计划失败").WithCause(err)
		}
		if plan == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点计划不存在")
		}

		// 检查状态
		if plan.Status != entity.CountPlanStatusInProgress {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有进行中的计划才能完成")
		}

		// 更新状态
		plan.Status = entity.CountPlanStatusCompleted

		if err := repo.Update(ctx, plan); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新盘点计划状态失败").WithCause(err)
		}

		return nil
	})
}

// CreateTask 创建盘点任务
func (s *wmsCycleCountServiceImpl) CreateTask(ctx context.Context, req *dto.WmsCycleCountTaskCreateReq) (*vo.WmsCycleCountTaskVO, error) {
	var result *vo.WmsCycleCountTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()
		codeService := s.GetServiceManager().GetCodeGenerationService()

		// 生成盘点任务编号
		codeRes, err := codeService.GenerateCode(ctx, &dto.CodeGenerationReq{
			BusinessType: "WMS_CYCLE_COUNT_TASK",
		})
		if err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "生成盘点任务编号失败").WithCause(err)
		}

		// 创建盘点任务
		task := &entity.WmsCycleCountTask{
			TaskNo:        codeRes.GeneratedCode,
			PlanID:        req.PlanID,
			LocationID:    req.LocationID,
			ItemID:        req.SkuID,
			Status:        entity.CountTaskStatusPending,
			CounterUserID: req.CounterUserID,
		}

		if err := repo.Create(ctx, task); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "创建盘点任务失败").WithCause(err)
		}

		// 转换为VO
		result = &vo.WmsCycleCountTaskVO{}
		if err := copier.Copy(result, task); err != nil {
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// UpdateTask 更新盘点任务
func (s *wmsCycleCountServiceImpl) UpdateTask(ctx context.Context, id uint, req *dto.WmsCycleCountTaskUpdateReq) (*vo.WmsCycleCountTaskVO, error) {
	var result *vo.WmsCycleCountTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()

		// 获取现有记录
		task, err := repo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
		}
		if task == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
		}

		// 检查状态是否允许修改
		if task.Status != entity.CountTaskStatusPending {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有待盘点状态的任务才能修改")
		}

		// 更新字段
		if req.CounterUserID != nil {
			task.CounterUserID = req.CounterUserID
		}

		if err := repo.Update(ctx, task); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新盘点任务失败").WithCause(err)
		}

		// 转换为VO
		result = &vo.WmsCycleCountTaskVO{}
		if err := copier.Copy(result, task); err != nil {
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// DeleteTask 删除盘点任务
func (s *wmsCycleCountServiceImpl) DeleteTask(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()

		// 检查任务是否存在
		task, err := repo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
		}
		if task == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
		}

		// 检查状态是否允许删除
		if task.Status != entity.CountTaskStatusPending {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有待盘点状态的任务才能删除")
		}

		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除盘点任务失败").WithCause(err)
		}

		return nil
	})
}

// GetTask 根据ID获取盘点任务
func (s *wmsCycleCountServiceImpl) GetTask(ctx context.Context, id uint) (*vo.WmsCycleCountTaskVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsCycleCountTaskRepository()

	task, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
	}
	if task == nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
	}

	result := &vo.WmsCycleCountTaskVO{}
	if err := copier.Copy(result, task); err != nil {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
	}

	return result, nil
}

// GetTaskPage 分页查询盘点任务
func (s *wmsCycleCountServiceImpl) GetTaskPage(ctx context.Context, req *dto.WmsCycleCountTaskQueryReq) (*vo.PageResult[vo.WmsCycleCountTaskVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsCycleCountTaskRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询盘点任务失败").WithCause(err)
	}

	// 转换为VO
	result := &vo.PageResult[vo.WmsCycleCountTaskVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsCycleCountTaskVO, 0),
	}

	// 处理 pageResult.List，它是 interface{} 类型
	if pageResult.List != nil {
		if records, ok := pageResult.List.([]*entity.WmsCycleCountTask); ok {
			for _, record := range records {
				taskVO := &vo.WmsCycleCountTaskVO{}
				if err := copier.Copy(taskVO, record); err != nil {
					return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
				}
				result.List = append(result.List, taskVO)
			}
		}
	}

	return result, nil
}

// StartTask 开始盘点任务
func (s *wmsCycleCountServiceImpl) StartTask(ctx context.Context, taskId uint, counterUserId uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()

		// 获取任务
		task, err := repo.FindByID(ctx, taskId)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
		}
		if task == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
		}

		// 检查状态
		if task.Status != entity.CountTaskStatusPending {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有待盘点状态的任务才能开始")
		}

		// 更新状态和盘点人
		task.Status = entity.CountTaskStatusCounting
		task.CounterUserID = &counterUserId

		if err := repo.Update(ctx, task); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新盘点任务状态失败").WithCause(err)
		}

		return nil
	})
}

// CompleteTask 完成盘点任务
func (s *wmsCycleCountServiceImpl) CompleteTask(ctx context.Context, taskId uint, req *dto.WmsCycleCountTaskCompleteReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()

		// 获取任务
		task, err := repo.FindByID(ctx, taskId)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
		}
		if task == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
		}

		// 检查状态
		if task.Status != entity.CountTaskStatusCounting {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有盘点中状态的任务才能完成")
		}

		// 更新任务信息
		task.Status = entity.CountTaskStatusCompleted
		task.CountedQuantity = &req.ActualQuantity

		// 计算差异 (这会在 BeforeSave 钩子中自动计算)
		if err := repo.Update(ctx, task); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新盘点任务失败").WithCause(err)
		}

		return nil
	})
}

// ConfirmVariance 确认盘点差异
func (s *wmsCycleCountServiceImpl) ConfirmVariance(ctx context.Context, taskId uint, req *dto.WmsCycleCountVarianceReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()

		// 获取任务
		task, err := repo.FindByID(ctx, taskId)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
		}
		if task == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
		}

		// 检查状态
		if task.Status != entity.CountTaskStatusCompleted {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有已完成状态的任务才能确认差异")
		}

		// 更新差异确认信息
		task.Status = entity.CountTaskStatusVarianceConfirmed
		task.VarianceReason = req.VarianceReason

		if err := repo.Update(ctx, task); err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "确认盘点差异失败").WithCause(err)
		}

		return nil
	})
}

// GenerateAdjustment 生成库存调整单
func (s *wmsCycleCountServiceImpl) GenerateAdjustment(ctx context.Context, taskId uint) (*vo.WmsInventoryAdjustmentVO, error) {
	var result *vo.WmsInventoryAdjustmentVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		taskRepo := txRepoMgr.GetWmsCycleCountTaskRepository()

		// 获取任务
		task, err := taskRepo.FindByID(ctx, taskId)
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
		}
		if task == nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
		}

		// 检查状态和差异
		if task.Status != entity.CountTaskStatusVarianceConfirmed {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有差异已确认的任务才能生成调整单")
		}

		if task.VarianceQuantity == nil || *task.VarianceQuantity == 0 {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "无差异的任务不需要生成调整单")
		}

		// TODO: 这里应该调用库存调整服务来创建调整单
		// 暂时返回一个模拟的结果
		result = &vo.WmsInventoryAdjustmentVO{
			// 填充调整单信息
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// BatchCreateTasks 批量创建盘点任务
func (s *wmsCycleCountServiceImpl) BatchCreateTasks(ctx context.Context, planId uint, req *dto.WmsCycleCountTaskBatchCreateReq) ([]*vo.WmsCycleCountTaskVO, error) {
	var results []*vo.WmsCycleCountTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()
		codeService := s.GetServiceManager().GetCodeGenerationService()

		for _, taskReq := range req.Tasks {
			// 生成盘点任务编号
			codeRes, err := codeService.GenerateCode(ctx, &dto.CodeGenerationReq{
				BusinessType: "WMS_CYCLE_COUNT_TASK",
			})
			if err != nil {
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "生成盘点任务编号失败").WithCause(err)
			}

			// 创建盘点任务
			task := &entity.WmsCycleCountTask{
				TaskNo:        codeRes.GeneratedCode,
				PlanID:        planId,
				LocationID:    taskReq.LocationID,
				ItemID:        taskReq.SkuID,
				Status:        entity.CountTaskStatusPending,
				CounterUserID: taskReq.CounterUserID,
			}

			if err := repo.Create(ctx, task); err != nil {
				return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "创建盘点任务失败").WithCause(err)
			}

			// 转换为VO
			taskVO := &vo.WmsCycleCountTaskVO{}
			if err := copier.Copy(taskVO, task); err != nil {
				return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
			}
			results = append(results, taskVO)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return results, nil
}

// BatchAssignCounter 批量分配盘点员
func (s *wmsCycleCountServiceImpl) BatchAssignCounter(ctx context.Context, taskIds []uint, counterUserId uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsCycleCountTaskRepository()

		for _, taskId := range taskIds {
			// 获取任务
			task, err := repo.FindByID(ctx, taskId)
			if err != nil {
				return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取盘点任务失败").WithCause(err)
			}
			if task == nil {
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_RESOURCE_NOT_FOUND, "盘点任务不存在")
			}

			// 检查状态
			if task.Status != entity.CountTaskStatusPending {
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "只有待盘点状态的任务才能分配盘点员")
			}

			// 分配盘点员
			task.CounterUserID = &counterUserId

			if err := repo.Update(ctx, task); err != nil {
				return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "分配盘点员失败").WithCause(err)
			}
		}

		return nil
	})
}

// GetPlanStats 获取盘点计划统计信息
func (s *wmsCycleCountServiceImpl) GetPlanStats(ctx context.Context, planId uint) (*vo.WmsCycleCountPlanStatsVO, error) {
	// TODO: 实现盘点计划统计逻辑
	// 这里应该查询计划下的任务统计信息
	result := &vo.WmsCycleCountPlanStatsVO{
		TotalTasks:         0,
		CompletedTasks:     0,
		TasksWithVariance:  0,
		CompletionRate:     0.0,
		VarianceRate:       0.0,
		TotalSystemQty:     0.0,
		TotalCountedQty:    0.0,
		TotalVarianceQty:   0.0,
		AvgVariancePercent: 0.0,
		TasksByStatus:      make(map[string]int64),
		TasksByLocation:    make([]vo.WmsCycleCountLocationStatsVO, 0),
		TasksByItem:        make([]vo.WmsCycleCountItemStatsVO, 0),
	}

	return result, nil
}

// GetCounterStats 获取盘点员统计信息
func (s *wmsCycleCountServiceImpl) GetCounterStats(ctx context.Context, counterUserId uint, startDate, endDate string) (*vo.WmsCycleCountCounterStatsVO, error) {
	// TODO: 实现盘点员统计逻辑
	// 这里应该查询盘点员在指定时间范围内的统计信息
	result := &vo.WmsCycleCountCounterStatsVO{
		CounterUserID:     counterUserId,
		TotalTasks:        0,
		CompletedTasks:    0,
		TasksWithVariance: 0,
		AverageAccuracy:   0.0,
		CompletionRate:    0.0,
		VarianceRate:      0.0,
		AvgCompletionTime: 0.0,
		StartDate:         startDate,
		EndDate:           endDate,
		DailyStats:        make([]vo.WmsCycleCountCounterDailyStatsVO, 0),
	}

	return result, nil
}
