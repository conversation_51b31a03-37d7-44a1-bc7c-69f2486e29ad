# WMS 盘点模块框架合规性修复执行计划

## 概述
本执行计划旨在修复WMS盘点模块与项目框架标准的不一致问题，确保与入库模块等其他模块保持一致的架构模式。

## 问题总结

### 严重问题 (Critical)
1. **Repository层**: 未继承BaseRepository接口，缺少框架标准的条件查询支持
2. **Service层**: 未继承BaseService接口，缺少服务管理器集成
3. **Controller层**: 未继承BaseController接口，缺少统一错误处理和审计日志
4. **Manager集成**: 未在RepositoryManager、ServiceManager、ControllerManager中注册

### 一般问题 (Major)
1. **Entity层**: 基础实体继承不一致
2. **DTO层**: 缺少统一的分页查询结构
3. **VO层**: 辅助函数位置不当
4. **事务处理**: 未使用框架统一的事务管理

## 修复执行计划

### 阶段1: Repository层框架合规性修复 (优先级: 高)

#### 任务1.1: 修复WmsCycleCountPlanRepository
- [ ] 继承BaseRepository[entity.WmsCycleCountPlan, uint]接口
- [ ] 移除自定义查询构建器，使用框架QueryCondition系统
- [ ] 实现标准的分页查询方法
- [ ] 添加到RepositoryManager中

#### 任务1.2: 修复WmsCycleCountTaskRepository  
- [ ] 继承BaseRepository[entity.WmsCycleCountTask, uint]接口
- [ ] 使用框架标准的条件查询系统
- [ ] 实现标准的分页查询方法
- [ ] 添加到RepositoryManager中

#### 任务1.3: 移除自定义QueryBuilder
- [ ] 删除WmsCycleCountPlanQueryBuilder相关代码
- [ ] 使用框架的QueryCondition替代

### 阶段2: Service层框架合规性修复 (优先级: 高)

#### 任务2.1: 修复WmsCycleCountService
- [ ] 继承BaseService接口
- [ ] 使用BaseServiceImpl作为基类
- [ ] 集成ServiceManager
- [ ] 使用框架的上下文处理方法
- [ ] 使用框架的事务管理

#### 任务2.2: 服务管理器集成
- [ ] 在ServiceManager中添加GetWmsCycleCountService()方法
- [ ] 确保服务正确初始化和依赖注入

### 阶段3: Controller层框架合规性修复 (优先级: 高)

#### 任务3.1: 修复WmsCycleCountController
- [ ] 继承BaseControllerImpl
- [ ] 使用ControllerManager初始化
- [ ] 使用框架统一的错误处理
- [ ] 添加审计日志支持
- [ ] 使用框架的参数解析方法

#### 任务3.2: 控制器管理器集成
- [ ] 在ControllerManager中添加GetWmsCycleCountController()方法
- [ ] 在main.go中正确初始化控制器

### 阶段4: Entity/DTO/VO层优化 (优先级: 中)

#### 任务4.1: Entity层优化
- [ ] 统一基础实体继承模式
- [ ] 确保与其他模块一致的字段命名和类型

#### 任务4.2: DTO层优化
- [ ] 统一分页查询DTO结构
- [ ] 添加缺失的验证标签
- [ ] 确保与框架标准一致

#### 任务4.3: VO层优化
- [ ] 移除VO文件中的辅助函数到工具类
- [ ] 统一分页结果类型定义
- [ ] 优化VO转换逻辑

### 阶段5: 集成测试和验证 (优先级: 中)

#### 任务5.1: 单元测试
- [ ] 为修复后的Repository编写单元测试
- [ ] 为修复后的Service编写单元测试
- [ ] 为修复后的Controller编写单元测试

#### 任务5.2: 集成测试
- [ ] 测试完整的CRUD操作流程
- [ ] 测试分页查询功能
- [ ] 测试事务处理
- [ ] 测试错误处理和审计日志

#### 任务5.3: 性能测试
- [ ] 对比修复前后的性能差异
- [ ] 确保框架合规性不影响性能

## 详细实施步骤

### 步骤1: Repository层修复

#### 1.1 修改WmsCycleCountPlanRepository接口
```go
type WmsCycleCountPlanRepository interface {
    BaseRepository[entity.WmsCycleCountPlan, uint]
    
    // 业务特定方法
    FindByPlanNo(ctx context.Context, planNo string) (*entity.WmsCycleCountPlan, error)
    FindByStatus(ctx context.Context, status entity.WmsCycleCountPlanStatus) ([]*entity.WmsCycleCountPlan, error)
    // ... 其他业务方法
}
```

#### 1.2 修改Repository实现
```go
type wmsCycleCountPlanRepository struct {
    BaseRepositoryImpl[entity.WmsCycleCountPlan, uint]
}

func NewWmsCycleCountPlanRepository(db *gorm.DB) WmsCycleCountPlanRepository {
    return &wmsCycleCountPlanRepository{
        BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsCycleCountPlan, uint]{
            db: db,
        },
    }
}
```

#### 1.3 使用QueryCondition系统
```go
func (r *wmsCycleCountPlanRepository) GetPage(ctx context.Context, query *dto.WmsCycleCountPlanQueryReq) (*response.PageResult, error) {
    conditions := []QueryCondition{}
    
    if query.PlanNo != nil && *query.PlanNo != "" {
        conditions = append(conditions, NewLikeCondition("plan_no", *query.PlanNo))
    }
    if query.Status != nil {
        conditions = append(conditions, NewEqualCondition("status", *query.Status))
    }
    // ... 其他条件
    
    return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}
```

### 步骤2: Service层修复

#### 2.1 修改Service接口和实现
```go
type WmsCycleCountService interface {
    BaseService
    // 业务方法...
}

type wmsCycleCountServiceImpl struct {
    BaseServiceImpl
}

func NewWmsCycleCountService(sm *ServiceManager) WmsCycleCountService {
    return &wmsCycleCountServiceImpl{
        BaseServiceImpl: *NewBaseService(sm),
    }
}
```

#### 2.2 使用框架事务管理
```go
func (s *wmsCycleCountServiceImpl) CreatePlan(ctx context.Context, req *dto.WmsCycleCountPlanCreateReq) (*vo.WmsCycleCountPlanVO, error) {
    var result *vo.WmsCycleCountPlanVO
    
    err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
        repo := txRepoMgr.GetWmsCycleCountPlanRepository()
        
        // 获取账套ID和用户ID
        accountBookID, err := s.GetAccountBookIDFromContext(ctx)
        if err != nil {
            return err
        }
        
        userID, err := s.GetUserIDFromContext(ctx)
        if err != nil {
            return err
        }
        
        // 业务逻辑...
        return nil
    })
    
    return result, err
}
```

### 步骤3: Controller层修复

#### 3.1 修改Controller结构
```go
type WmsCycleCountController interface {
    CreatePlan(ctx iris.Context)
    UpdatePlan(ctx iris.Context)
    // ... 其他方法
}

type wmsCycleCountControllerImpl struct {
    BaseControllerImpl
    wmsCycleCountService service.WmsCycleCountService
}

func NewWmsCycleCountControllerImpl(cm *ControllerManager) *wmsCycleCountControllerImpl {
    return &wmsCycleCountControllerImpl{
        BaseControllerImpl:   *NewBaseController(cm),
        wmsCycleCountService: cm.GetServiceManager().GetWmsCycleCountService(),
    }
}
```

#### 3.2 使用框架错误处理
```go
func (ctrl *wmsCycleCountControllerImpl) CreatePlan(ctx iris.Context) {
    const opName = "CreateWmsCycleCountPlan"
    ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_cycle_count_plan")
    
    var req dto.WmsCycleCountPlanCreateReq
    if err := ctx.ReadJSON(&req); err != nil {
        paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
        ctrl.HandleError(ctx, paramErr, opName)
        return
    }
    
    vo, err := ctrl.wmsCycleCountService.CreatePlan(ctx.Request().Context(), &req)
    if err != nil {
        ctrl.HandleError(ctx, err, opName)
        return
    }
    ctrl.Success(ctx, vo)
}
```

## 验收标准

### 功能验收
- [ ] 所有CRUD操作正常工作
- [ ] 分页查询功能正常
- [ ] 业务流程(创建计划->审批->执行->完成)正常
- [ ] 错误处理和验证正常

### 框架合规性验收
- [ ] Repository层正确继承BaseRepository
- [ ] Service层正确继承BaseService
- [ ] Controller层正确继承BaseController
- [ ] 正确集成到各个Manager中
- [ ] 使用框架统一的事务管理
- [ ] 使用框架统一的错误处理
- [ ] 审计日志正常记录

### 性能验收
- [ ] 查询性能不低于修复前
- [ ] 内存使用合理
- [ ] 并发处理能力正常

## 风险评估

### 高风险
- **数据兼容性**: 修改Repository可能影响现有数据查询
- **业务逻辑**: Service层重构可能影响业务流程

### 中风险  
- **API兼容性**: Controller层修改可能影响前端调用
- **性能影响**: 框架层增加可能影响性能

### 低风险
- **DTO/VO修改**: 主要是结构优化，影响较小

## 回滚计划

1. **代码回滚**: 保留.bak文件作为回滚版本
2. **数据库回滚**: 如有数据库变更，准备回滚脚本
3. **配置回滚**: 恢复原有的路由和依赖注入配置

## 时间估算

- **阶段1 (Repository)**: 2-3天
- **阶段2 (Service)**: 2-3天  
- **阶段3 (Controller)**: 1-2天
- **阶段4 (优化)**: 1-2天
- **阶段5 (测试)**: 2-3天

**总计**: 8-13天

## 后续优化建议

1. **代码生成**: 考虑使用代码生成工具自动生成符合框架标准的代码
2. **文档更新**: 更新开发文档，明确框架使用标准
3. **代码审查**: 建立代码审查机制，确保新代码符合框架标准
4. **单元测试**: 完善单元测试覆盖率
5. **性能监控**: 建立性能监控机制，及时发现性能问题
