// internal/repository/wms_location_repository.go
package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors" // 使用您项目中的错误包路径
	"backend/pkg/logger"           // 使用您项目中的日志包路径
	"backend/pkg/response"
)

// WmsLocationRepository 定义仓库库位接口
type WmsLocationRepository interface {
	BaseRepository[entity.WmsLocation, uint]
	GetPage(ctx context.Context, query *dto.WmsLocationQueryReq) (*response.PageResult, error)
	GetWarehouses(ctx context.Context) ([]*entity.WmsLocation, error)
	GetLocationTree(ctx context.Context, warehouseID uint) ([]*entity.WmsLocation, error)
	IsCodeExist(ctx context.Context, code string, excludeID uint) (bool, error)
}

// wmsLocationRepository 仓库库位接口实现
type wmsLocationRepository struct {
	BaseRepositoryImpl[entity.WmsLocation, uint]
}

// NewWmsLocationRepository 创建仓库库位接口
func NewWmsLocationRepository(db *gorm.DB) WmsLocationRepository {
	return &wmsLocationRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsLocation, uint]{
			db: db,
		},
	}
}

// GetPage 获取库位分页数据
func (r *wmsLocationRepository) GetPage(ctx context.Context, query *dto.WmsLocationQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}
	if query.WarehouseID != nil {
		conditions = append(conditions, NewEqualCondition("warehouse_id", *query.WarehouseID))
	}
	if query.Code != "" {
		conditions = append(conditions, NewLikeCondition("code", query.Code))
	}
	if query.Name != "" {
		conditions = append(conditions, NewLikeCondition("name", query.Name))
	}
	if query.Type != "" {
		conditions = append(conditions, NewEqualCondition("type", query.Type))
	}
	if query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", query.Status))
	}
	if query.IsPickable != nil {
		conditions = append(conditions, NewEqualCondition("is_pickable", *query.IsPickable))
	}
	if query.IsPutaway != nil {
		conditions = append(conditions, NewEqualCondition("is_putaway", *query.IsPutaway))
	}

	return r.FindByPage(ctx, &query.PageQuery, conditions)
}

// GetWarehouses 获取所有仓库列表 (顶层库位)
func (r *wmsLocationRepository) GetWarehouses(ctx context.Context) ([]*entity.WmsLocation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("type", entity.LocationTypeWarehouse),
	}
	// 按 code 排序
	sortInfos := []response.SortInfo{
		{Field: "code", Order: "asc"},
	}
	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetLocationTree 获取库位树数据 (用于构建树)
// warehouseID 为 0 时获取全部库位，否则获取指定仓库的库位
func (r *wmsLocationRepository) GetLocationTree(ctx context.Context, warehouseID uint) ([]*entity.WmsLocation, error) {
	var conditions []QueryCondition

	// 如果 warehouseID 不为 0，则添加仓库条件
	if warehouseID != 0 {
		conditions = append(conditions, NewEqualCondition("warehouse_id", warehouseID))
	}

	return r.FindByCondition(ctx, conditions, nil) // 无特殊排序
}

// IsCodeExist 检查库位编码是否存在
func (r *wmsLocationRepository) IsCodeExist(ctx context.Context, code string, excludeID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("code", code),
	}
	if excludeID > 0 {
		conditions = append(conditions, NewNotEqualCondition("id", excludeID))
	}
	return r.Exists(ctx, conditions)
}

// --- 实现 WmsLocationRepository 特有的方法 ---

func (r *wmsLocationRepository) FindByCode(ctx context.Context, warehouseID uint, code string) (*entity.WmsLocation, error) {
	var location entity.WmsLocation
	db := r.db.WithContext(ctx)
	result := db.Where("warehouse_id = ?", warehouseID).Where("code = ?", code).First(&location)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "库位记录未找到").WithCause(result.Error)
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据 Code 查询库位失败: warehouseID=%d, code=%s", warehouseID, code)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询库位失败").WithCause(result.Error)
	}
	return &location, nil
}

func (r *wmsLocationRepository) FindChildrenByParentID(ctx context.Context, parentID uint) ([]*entity.WmsLocation, error) {
	var locations []*entity.WmsLocation
	db := r.db.WithContext(ctx)
	result := db.Where("parent_id = ?", parentID).Find(&locations)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询子库位失败: parentID=%d", parentID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询子库位失败").WithCause(result.Error)
	}
	return locations, nil
}

// FindAllChildrenRecursive 获取所有子孙库位 (使用 GORM 的 Preload 或递归 CTE)
// 简单实现: 多次查询 (效率较低，但易于理解)
// 更好的实现: 使用数据库的递归查询特性 (如 PostgreSQL 的 WITH RECURSIVE) 或一次性加载后内存处理
func (r *wmsLocationRepository) FindAllChildrenRecursive(ctx context.Context, parentID uint) ([]*entity.WmsLocation, error) {
	allChildren := []*entity.WmsLocation{}
	directChildren, err := r.FindChildrenByParentID(ctx, parentID)
	if err != nil {
		return nil, err
	}

	for _, child := range directChildren {
		allChildren = append(allChildren, child)
		grandchildren, err := r.FindAllChildrenRecursive(ctx, child.ID)
		if err != nil {
			// 根据需要决定是否中断或继续
			logger.WithContext(ctx).WithError(err).Warnf("递归查询子库位失败: parentID=%d", child.ID)
			continue // 或者 return nil, err
		}
		allChildren = append(allChildren, grandchildren...)
	}

	return allChildren, nil
}

// FindLocationTree 获取库位树 (使用 GORM Preload)
func (r *wmsLocationRepository) FindLocationTree(ctx context.Context, rootID uint) (*entity.WmsLocation, error) {
	var rootLocation entity.WmsLocation
	db := r.db.WithContext(ctx)

	// 使用 Preload("Children") 会递归加载所有层级的子节点 (如果 GORM 版本支持且关联正确设置)
	// 注意: 如果层级很深或数据量大，可能会有性能问题
	// 如果需要控制加载深度，可以使用嵌套 Preload 或自定义加载逻辑
	// err := db.Preload("Children").First(&rootLocation, rootID).Error // 假设 Children 关联已在 entity 中正确定义

	// GORM v2 默认不会无限递归 Preload, 需要手动实现递归加载
	// 这里使用手动递归 Preload 实现
	err := db.First(&rootLocation, rootID).Error // 先查根节点
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "根库位记录未找到").WithCause(err)
		}
		logger.WithContext(ctx).WithError(err).Errorf("查询库位树根节点失败: rootID=%d", rootID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询库位树失败").WithCause(err)
	}

	// 从根节点开始递归加载子节点
	err = r.preloadChildrenRecursive(db, &rootLocation)
	if err != nil {
		// preloadChildrenRecursive 内部已记录日志
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "递归加载子库位失败").WithCause(err)
	}

	return &rootLocation, nil
}

// preloadChildrenRecursive 辅助函数，递归预加载子节点
func (r *wmsLocationRepository) preloadChildrenRecursive(db *gorm.DB, location *entity.WmsLocation) error {
	if location == nil {
		return nil
	}
	// 加载当前节点的直接子节点
	// 使用 Find 而不是 Association().Find() 来直接查询子节点
	result := db.Where("parent_id = ?", location.ID).Find(&location.Children)
	if result.Error != nil {
		// Find 不会返回 ErrRecordNotFound，而是返回空切片，所以这里只处理真正的数据库错误
		logger.WithError(result.Error).Errorf("预加载子库位失败: parentID=%d", location.ID)
		return result.Error
	}

	// 如果没有子节点，结束当前递归分支
	if len(location.Children) == 0 {
		return nil
	}

	// 对每个子节点递归调用
	for i := range location.Children {
		// 注意：这里需要传递子节点的指针地址给递归函数
		if err := r.preloadChildrenRecursive(db, &location.Children[i]); err != nil {
			return err // 如果递归中出错，则向上层传递错误
		}
	}
	return nil
}

func (r *wmsLocationRepository) CheckCodeExists(ctx context.Context, warehouseID uint, code string, excludeID *uint) (bool, error) {
	var count int64
	db := r.db.WithContext(ctx)
	query := db.Model(&entity.WmsLocation{}).
		Where("warehouse_id = ?", warehouseID).
		Where("code = ?", code)

	if excludeID != nil && *excludeID > 0 {
		query = query.Where("id <> ?", *excludeID)
	}

	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("检查库位 Code 是否存在失败: warehouseID=%d, code=%s, excludeID=%v", warehouseID, code, excludeID)
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查库位 Code 失败").WithCause(result.Error)
	}
	return count > 0, nil
}

func (r *wmsLocationRepository) HasChildren(ctx context.Context, id uint) (bool, error) {
	var count int64
	db := r.db.WithContext(ctx)
	result := db.Model(&entity.WmsLocation{}).Where("parent_id = ?", id).Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("检查库位是否有子节点失败: id=%d", id)
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查库位是否有子节点失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// FindByConditionLimit 实现根据动态条件、排序和限制查询库位记录
func (r *wmsLocationRepository) FindByConditionLimit(ctx context.Context, conditions []Condition, orderBy string, orderDirection string, limit int) ([]*entity.WmsLocation, error) {
	var locations []*entity.WmsLocation
	db := r.GetDB(ctx)             // 获取当前上下文的 DB 连接
	log := logger.WithContext(ctx) // 获取日志记录器

	// 初始化查询构建器
	tx := db.Model(&entity.WmsLocation{}) // <<< 初始化 tx

	// 应用动态条件 - 循环处理每个 Condition
	for _, condition := range conditions {
		// 根据 Condition 类型应用 Where 子句
		// 这里直接使用 GORM 的 Where 方法，模仿 base_repository.go 中 applyCondition 的逻辑
		// 注意：这里假设 Condition 结构包含 Field, Operator, Value 字段
		// 并且 Operator 是 SQL 操作符字符串 (e.g., "=", "LIKE", "IN")
		// 对于 IN 操作符，Value 应该是一个切片
		// 更健壮的实现可能需要检查 Operator 类型并相应处理 Value
		tx = tx.Where(fmt.Sprintf("%s %s ?", condition.Field, condition.Operator), condition.Value)
	}
	// <<< 移除了对 ApplyConditions 的调用

	// 应用排序
	if orderBy != "" {
		// 默认为 ASC，如果指定为 DESC 则使用 DESC
		dir := "ASC"
		if strings.ToLower(orderDirection) == "desc" {
			dir = "DESC"
		}
		// 注意：直接拼接字符串可能存在 SQL 注入风险，如果 orderBy 来自用户输入，需要严格校验
		// 假设这里的 orderBy 是内部定义的、安全的字段名
		tx = tx.Order(fmt.Sprintf("%s %s", orderBy, dir))
	}

	// 应用数量限制
	if limit > 0 {
		tx = tx.Limit(limit)
	}

	// 执行查询
	if err := tx.Find(&locations).Error; err != nil {
		// Find 方法本身不会因为找不到记录而返回 gorm.ErrRecordNotFound，它会返回一个空切片。
		// 因此，这里只处理真正的数据库查询错误。
		log.WithError(err).Error("根据条件查询库位列表失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询库位列表失败").WithCause(err)
	}

	log.Debugf("根据条件查询到 %d 条库位记录", len(locations))
	return locations, nil
}

// FindWarehouses 查询所有类型为 WAREHOUSE 的库位
func (r *wmsLocationRepository) FindWarehouses(ctx context.Context) ([]*entity.WmsLocation, error) {
	const op = "repository.FindWarehouses"
	var warehouses []*entity.WmsLocation

	db := r.GetDB(ctx) // 获取当前事务或主数据库连接
	// 假设 entity 包中有定义 LocationTypeWarehouse 常量
	err := db.WithContext(ctx).
		Where("type = ?", entity.LocationTypeWarehouse). // Corrected: Use 'type' column name
		Order("code asc").                               // 按编码排序
		Find(&warehouses).Error
	if err != nil {
		// 使用 pkg/errors 进行错误包装
		logger.WithContext(ctx).WithError(err).Error(op + ": failed to find warehouses")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询仓库列表失败").WithCause(err)
	}

	return warehouses, nil
}

/*
// HasInventory 示例实现 (需要 WmsInventoryRepository)
func (r *wmsLocationRepository) HasInventory(ctx context.Context, id uint) (bool, error) {
    // 假设 repoManager 存在并且可以获取 inventoryRepo
    // invRepo := r.repoManager.GetWmsInventoryRepository() // 获取库存仓库
    // return invRepo.ExistsByLocation(ctx, id)             // 调用库存仓库的方法检查

    // 或者直接查询
    db := r.db.WithContext(ctx)
    var count int64
    // 检查 WmsInventory 表中是否有对应 location_id 且 quantity > 0 的记录
    result := db.Model(&entity.WmsInventory{}).Where("location_id = ? AND quantity > 0", id).Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("检查库位是否有库存失败: id=%d", id)
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查库位是否有库存失败").WithCause(result.Error)
	}
	return count > 0, nil
}
*/
