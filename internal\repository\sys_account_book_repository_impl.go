package repository

import (
	"context"
	"errors" // For standard error checking like gorm.ErrRecordNotFound

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors" // Use alias
	"backend/pkg/logger"           // Import logger

	"gorm.io/gorm"
)

// AccountBookRepository 账套仓库接口
type AccountBookRepository interface {
	BaseRepository[entity.AccountBook, uint] // 嵌入基础 CRUD

	// 添加 AccountBook 特有的查询方法
	FindByCode(ctx context.Context, code string) (*entity.AccountBook, error)
	FindByName(ctx context.Context, name string) (*entity.AccountBook, error)

	// 移除冗余的 FindByIds 接口定义
	// FindByIds(ctx context.Context, ids []uint) ([]*entity.AccountBook, error)
}

// accountBookRepository 账套仓库实现
type accountBookRepository struct {
	BaseRepository[entity.AccountBook, uint] // 嵌入基础仓库实现
	// 移除冗余的 db 字段
	// db                                       *gorm.DB
}

// NewAccountBookRepository 创建账套仓库
func NewAccountBookRepository(db *gorm.DB) AccountBookRepository {
	return &accountBookRepository{
		BaseRepository: NewBaseRepository[entity.AccountBook, uint](db),
		// 移除 db 赋值
		// db:             db,
	}
}

// getDB 获取带上下文的 DB 连接 (Helper)
// 此辅助函数现在不再需要，因为可以直接使用嵌入的 GetDB
/*
func (r *accountBookRepository) getDB(ctx context.Context) *gorm.DB {
	baseRepoImpl, ok := r.BaseRepository.(*BaseRepositoryImpl[entity.AccountBook, uint])
	if !ok {
        // Fallback or panic, but since BaseRepository is embedded, this shouldn't fail
        // unless BaseRepository interface changes.
        panic("could not access embedded BaseRepositoryImpl")
	}
	return baseRepoImpl.GetDB(ctx)
}
*/

// FindByCode 根据编码查询账套
func (r *accountBookRepository) FindByCode(ctx context.Context, code string) (*entity.AccountBook, error) {
	var accountBook entity.AccountBook
	// 直接使用嵌入的 GetDB
	result := r.GetDB(ctx).Where("code = ?", code).First(&accountBook)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 对于 Not Found，通常不需要记录 Error 级别的日志，可以选择 Debug 或 Info
			// logger.WithContext(ctx).Debugf("账套编码不存在: %s", code)
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "账套编码不存在").WithCause(result.Error)
		}
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("按编码查询账套失败 (Code: %s)", code)
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "按编码查询账套失败")
	}
	return &accountBook, nil
}

// FindByName 根据名称查询账套
func (r *accountBookRepository) FindByName(ctx context.Context, name string) (*entity.AccountBook, error) {
	var accountBook entity.AccountBook
	// 直接使用嵌入的 GetDB
	result := r.GetDB(ctx).Where("name = ?", name).First(&accountBook)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 对于 Not Found，通常不需要记录 Error 级别的日志
			// logger.WithContext(ctx).Debugf("账套名称不存在: %s", name)
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "账套名称不存在").WithCause(result.Error)
		}
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("按名称查询账套失败 (Name: %s)", name)
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "按名称查询账套失败")
	}
	return &accountBook, nil
}

// 移除冗余的 FindByIds 实现
/*
func (r *accountBookRepository) FindByIds(ctx context.Context, ids []uint) ([]*entity.AccountBook, error) {
	if len(ids) == 0 {
		return []*entity.AccountBook{}, nil
	}
	var accountBooks []*entity.AccountBook
	// 使用 Where "id IN ?" 进行批量查询
	result := r.getDB(ctx).Where("id IN ?", ids).Find(&accountBooks)
	if result.Error != nil {
		// 不需要检查 RecordNotFound，因为 Find 在找不到时返回空切片而不是错误
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "按ID列表查询账套失败")
	}
	return accountBooks, nil
}
*/
