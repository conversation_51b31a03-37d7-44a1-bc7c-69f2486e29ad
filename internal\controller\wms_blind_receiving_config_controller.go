package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
)

// WmsBlindReceivingConfigController 盲收配置控制器接口
type WmsBlindReceivingConfigController interface {
	BaseController

	// 基础CRUD操作
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 批量操作
	CreateBatch(ctx iris.Context)
	UpdateBatch(ctx iris.Context)
	BatchDelete(ctx iris.Context)
	BatchToggle(ctx iris.Context)

	// 核心业务方法
	GetEffectiveConfig(ctx iris.Context)
	ValidateBlindReceiving(ctx iris.Context)

	// 配置管理
	GetAvailableTargets(ctx iris.Context)
	GetActiveConfigs(ctx iris.Context)
	GetConfigsByStrategy(ctx iris.Context)

	// 审批和补录
	ProcessApproval(ctx iris.Context)
	ProcessSupplement(ctx iris.Context)

	// 业务流程查询
	GetPendingApprovalList(ctx iris.Context)
	GetPendingSupplementList(ctx iris.Context)
	GetOverdueSupplementList(ctx iris.Context)

	// 统计分析
	GetStats(ctx iris.Context)
}

// wmsBlindReceivingConfigControllerImpl 盲收配置控制器实现
type wmsBlindReceivingConfigControllerImpl struct {
	BaseControllerImpl
	blindReceivingConfigService service.WmsBlindReceivingConfigService
}

// NewWmsBlindReceivingConfigController 创建盲收配置控制器
func NewWmsBlindReceivingConfigController(cm *ControllerManager) WmsBlindReceivingConfigController {
	return &wmsBlindReceivingConfigControllerImpl{
		BaseControllerImpl:          *NewBaseController(cm),
		blindReceivingConfigService: cm.GetServiceManager().GetWmsBlindReceivingConfigService(),
	}
}

// Create 创建盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsBlindReceivingConfig"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_config")

	var req dto.WmsBlindReceivingConfigCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// Update 更新盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsBlindReceivingConfig"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_config")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsBlindReceivingConfigUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// Delete 删除盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsBlindReceivingConfig"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_config")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	if err := ctrl.blindReceivingConfigService.Delete(ctx.Request().Context(), uint(id)); err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, nil)
}

// GetByID 根据ID获取盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsBlindReceivingConfigByID"

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetPage 分页查询盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsBlindReceivingConfigsPage"

	var query dto.WmsBlindReceivingConfigQueryReq
	if err := ctx.ReadQuery(&query); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetPage(ctx.Request().Context(), &query)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// CreateBatch 批量创建盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) CreateBatch(ctx iris.Context) {
	const opName = "BatchCreateWmsBlindReceivingConfigs"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_config")

	var req dto.WmsBlindReceivingConfigBatchCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.CreateBatch(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// UpdateBatch 批量更新盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) UpdateBatch(ctx iris.Context) {
	const opName = "BatchUpdateWmsBlindReceivingConfigs"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_config")

	var req dto.WmsBlindReceivingConfigBatchUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.UpdateBatch(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// BatchDelete 批量删除盲收配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) BatchDelete(ctx iris.Context) {
	const opName = "BatchDeleteWmsBlindReceivingConfigs"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_config")

	var req struct {
		IDs []uint `json:"ids" validate:"required,min=1,max=100"`
	}

	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.BatchDelete(ctx.Request().Context(), req.IDs)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// BatchToggle 批量状态切换
func (ctrl *wmsBlindReceivingConfigControllerImpl) BatchToggle(ctx iris.Context) {
	const opName = "BatchToggleWmsBlindReceivingConfigs"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_config")

	var req struct {
		IDs      []uint `json:"ids" validate:"required,min=1,max=100"`
		IsActive bool   `json:"isActive"`
	}

	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.BatchToggle(ctx.Request().Context(), req.IDs, req.IsActive)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetEffectiveConfig 获取有效配置（核心业务方法）
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetEffectiveConfig(ctx iris.Context) {
	const opName = "GetEffectiveBlindReceivingConfig"

	var req dto.GetEffectiveConfigReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetEffectiveConfig(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// ValidateBlindReceiving 验证盲收（核心业务方法）
func (ctrl *wmsBlindReceivingConfigControllerImpl) ValidateBlindReceiving(ctx iris.Context) {
	const opName = "ValidateBlindReceiving"

	var req dto.BlindReceivingValidationReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.ValidateBlindReceiving(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetAvailableTargets 获取可用配置目标
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetAvailableTargets(ctx iris.Context) {
	const opName = "GetAvailableConfigTargets"

	configLevel := ctx.URLParam("configLevel")
	if configLevel == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "配置级别参数不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	req := &dto.AvailableConfigTargetsReq{
		ConfigLevel: configLevel,
	}

	result, err := ctrl.blindReceivingConfigService.GetAvailableTargets(ctx.Request().Context(), req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetActiveConfigs 获取激活的配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetActiveConfigs(ctx iris.Context) {
	const opName = "GetActiveBlindReceivingConfigs"

	configLevel := ctx.URLParam("configLevel")
	if configLevel == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "配置级别参数不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetActiveConfigs(ctx.Request().Context(), configLevel)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetConfigsByStrategy 根据策略获取配置
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetConfigsByStrategy(ctx iris.Context) {
	const opName = "GetBlindReceivingConfigsByStrategy"

	strategy := ctx.URLParam("strategy")
	if strategy == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "策略参数不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetConfigsByStrategy(ctx.Request().Context(), strategy)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// ProcessApproval 处理盲收审批
func (ctrl *wmsBlindReceivingConfigControllerImpl) ProcessApproval(ctx iris.Context) {
	const opName = "ProcessBlindReceivingApproval"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_approval")

	var req dto.BlindReceivingApprovalReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	if err := ctrl.blindReceivingConfigService.ProcessApproval(ctx.Request().Context(), &req); err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, nil)
}

// ProcessSupplement 处理盲收补录
func (ctrl *wmsBlindReceivingConfigControllerImpl) ProcessSupplement(ctx iris.Context) {
	const opName = "ProcessBlindReceivingSupplement"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_blind_receiving_supplement")

	var req dto.BlindReceivingSupplementReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	if err := ctrl.blindReceivingConfigService.ProcessSupplement(ctx.Request().Context(), &req); err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, nil)
}

// GetStats 获取盲收统计
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetStats(ctx iris.Context) {
	const opName = "GetBlindReceivingStats"

	warehouseIDStr := ctx.URLParam("warehouseId")
	if warehouseIDStr == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "仓库ID参数不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	warehouseID, err := strconv.ParseUint(warehouseIDStr, 10, 64)
	if err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的仓库ID").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	req := &dto.BlindReceivingStatsReq{
		WarehouseID: uint(warehouseID),
	}

	result, err := ctrl.blindReceivingConfigService.GetStats(ctx.Request().Context(), req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetPendingApprovalList 获取待审批列表
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetPendingApprovalList(ctx iris.Context) {
	const opName = "GetPendingApprovalList"

	warehouseIDStr := ctx.URLParam("warehouseId")
	if warehouseIDStr == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "仓库ID参数不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	warehouseID, err := strconv.ParseUint(warehouseIDStr, 10, 32)
	if err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的仓库ID").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetPendingApprovalList(ctx.Request().Context(), uint(warehouseID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetPendingSupplementList 获取待补录列表
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetPendingSupplementList(ctx iris.Context) {
	const opName = "GetPendingSupplementList"

	warehouseIDStr := ctx.URLParam("warehouseId")
	if warehouseIDStr == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "仓库ID参数不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	warehouseID, err := strconv.ParseUint(warehouseIDStr, 10, 32)
	if err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的仓库ID").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetPendingSupplementList(ctx.Request().Context(), uint(warehouseID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}

// GetOverdueSupplementList 获取逾期补录列表
func (ctrl *wmsBlindReceivingConfigControllerImpl) GetOverdueSupplementList(ctx iris.Context) {
	const opName = "GetOverdueSupplementList"

	warehouseIDStr := ctx.URLParam("warehouseId")
	if warehouseIDStr == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "仓库ID参数不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	warehouseID, err := strconv.ParseUint(warehouseIDStr, 10, 32)
	if err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的仓库ID").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.blindReceivingConfigService.GetOverdueSupplementList(ctx.Request().Context(), uint(warehouseID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	ctrl.Success(ctx, result)
}
