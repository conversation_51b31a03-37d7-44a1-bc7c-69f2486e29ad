<script setup lang="ts">
// import HelloWorld from './components/HelloWorld.vue' // Keep or remove HelloWorld based on need
// import AppNav from './components/exampleNav.vue' // Remove AppNav import
</script>

<template>
  <div class="app-container">
    <!-- <AppNav /> --> <!-- Remove AppNav usage -->
    <router-view />
  </div>
</template>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

#app, .app-container {
  height: 100vh;
  width: 100%;
  /* No margin/padding needed here if html,body reset is effective */
}

/* Removed potentially conflicting/unnecessary rules for .app-container */
/*
.app-container {
  max-width: 100%;
  margin: 0 auto;
}
*/
</style>
