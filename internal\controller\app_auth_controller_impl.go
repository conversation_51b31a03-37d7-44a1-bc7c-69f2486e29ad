package controller

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/config"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/util"
	"backend/pkg/validator"

	"github.com/kataras/iris/v12"
)

// AuthController 认证控制器
type AuthControllerImpl struct {
	*BaseControllerImpl
	authService  service.AuthService
	auditService service.AuditLogService
	appCfg       *config.Configuration
}

// NewAuthController 创建认证控制器
func NewAuthControllerImpl(cm *ControllerManager, appCfg *config.Configuration, authSvc service.AuthService, auditSvc service.AuditLogService) *AuthControllerImpl {
	if authSvc == nil {
		cm.GetLogger().Fatal(context.Background(), "AuthService is nil in NewAuthController")
	}
	if auditSvc == nil {
		cm.GetLogger().Fatal(context.Background(), "AuditLogService is nil in NewAuthController")
	}
	return &AuthControllerImpl{
		BaseControllerImpl: NewBaseController(cm),
		authService:        authSvc,
		auditService:       auditSvc,
		appCfg:             appCfg,
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 使用用户名、密码和验证码（如果启用）进行登录
// @Tags Auth
// @Accept json
// @Produce json
// @Param body body dto.UserLoginDTO true "登录凭证"
// @Success 200 {object} response.Response{data=vo.UserLoginVO} "成功响应，包含Token和用户信息"
// @Failure 400 {object} response.Response "请求参数错误或验证码错误"
// @Failure 401 {object} response.Response "用户名或密码错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /auth/login [post]
func (c *AuthControllerImpl) Login(ctx iris.Context) {
	opName := "用户登录"
	var loginDTO dto.UserLoginDTO

	if err := ctx.ReadJSON(&loginDTO); err != nil {
		c.FailWithError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求参数").WithCause(err))
		return
	}

	validationErrs := validator.Struct(loginDTO)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 登录参数验证失败", logger.WithField("username", loginDTO.Username), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	if c.authService == nil {
		c.HandleError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "认证服务不可用"), opName)
		return
	}

	// Get ClientIP from standard Go Context using util function
	reqCtx := ctx.Request().Context()
	clientIP, errClientIP := util.GetClientIPFromStdContext(reqCtx)
	if errClientIP != nil {
		c.GetLogger().Warn(reqCtx, "AuthController.Login: Failed to get ClientIP from context, using fallback.", logger.WithError(errClientIP), logger.WithField("fallbackClientIP", "Unknown"))
		clientIP = "Unknown" // Fallback strategy
	}
	traceID, _ := util.GetTraceIDFromStdContext(reqCtx)

	loginVO, refreshTokenString, err := c.authService.Login(ctx.Request().Context(), loginDTO, clientIP)
	if err != nil {
		// --- 登录失败审计 ---
		failureEvent := &service.AuditEventPlaceholder{
			Action:       "login",
			Username:     loginDTO.Username, // 记录尝试登录的用户名
			IP:           clientIP,
			UserAgent:    ctx.GetHeader("User-Agent"),
			RequestURI:   ctx.Path(),
			Method:       ctx.Method(),
			Time:         time.Now(),
			TraceID:      traceID,
			ResourceType: "authentication",
			ResourceID:   loginDTO.Username,
			Status:       "failure",
			StatusCode:   http.StatusUnauthorized, // 假定登录失败主要是认证问题
			Details:      fmt.Sprintf("Login attempt failed for user '%s'. Reason: %s", loginDTO.Username, err.Error()),
		}
		go c.auditService.RecordEvent(failureEvent)
		ctx.Values().Set(constant.AUDIT_EVENT_RECORDED_CTX_KEY, true)

		c.HandleError(ctx, err, opName)
		return
	}

	// --- 登录成功审计 ---
	successEvent := &service.AuditEventPlaceholder{
		Action:       "login",
		UserID:       uint64(loginVO.User.ID),
		Username:     loginVO.User.Username,
		IP:           clientIP,
		UserAgent:    ctx.GetHeader("User-Agent"),
		RequestURI:   ctx.Path(),
		Method:       ctx.Method(),
		Time:         time.Now(),
		TraceID:      traceID,
		ResourceType: "authentication",
		ResourceID:   loginVO.User.Username,
		Status:       "success",
		StatusCode:   http.StatusOK,
	}
	go c.auditService.RecordEvent(successEvent)
	ctx.Values().Set(constant.AUDIT_EVENT_RECORDED_CTX_KEY, true)

	if refreshTokenString != "" {
		refreshCookieExpire := time.Now().Add(time.Duration(c.appCfg.JWT.Expire) * time.Hour)
		if loginDTO.Remember {
			refreshCookieExpire = time.Now().Add(time.Duration(c.appCfg.JWT.RefreshExpire) * 24 * time.Hour)
		}
		ctx.SetCookie(&http.Cookie{
			Name:     "refresh_token",
			Value:    refreshTokenString,
			Expires:  refreshCookieExpire,
			HttpOnly: true,
			Secure:   ctx.Request().TLS != nil,
			Path:     "/",
		})
		c.GetLogger().Debug(ctx, "Refresh token cookie set", logger.WithField("username", loginDTO.Username))
	}

	c.Success(ctx, loginVO)
}

/*
// RegisterRoutes 注册路由 (示例，实际注册应在应用入口处)
func (c *AuthController) RegisterRoutes(app *iris.Application) {
    authParty := app.Party("/api/v1/auth")
    {
        authParty.Post("/login", c.Login)
        // Add other auth routes like logout, register, refresh token if needed
    }
}
*/
