# `internal/controller` 技术文档

## 1. 概述

`controller` 包是应用的表现层，直接处理来自客户端的 HTTP 请求。它负责解析请求参数、调用相应的服务层（`service`）方法来执行业务逻辑，并将服务层的执行结果（数据或错误）格式化为 HTTP 响应返回给客户端。该包基于 [Iris Web Framework](https://www.iris-go.com/) 构建。

核心设计目标：

- **请求路由与处理**：将特定的 HTTP 端点映射到相应的处理函数。
- **参数解析与校验**：从请求中提取数据（路径参数、查询参数、请求体），并进行有效性验证。
- **服务层编排**：调用一个或多个服务层方法来完成用户请求。
- **响应构建**：将业务逻辑的结果（成功数据或错误信息）封装成统一的 JSON 响应格式。
- **错误处理**：捕获服务层抛出的错误，并将其转换为对客户端友好的错误响应。
- **依赖管理**：通过 `ControllerManager` 集中管理控制器实例及其对 `ServiceManager` 的依赖。

## 2. 核心概念

### 2.1. `BaseController` 和 `BaseControllerImpl` (`base_controller.go`)

`BaseController` 是基础控制器接口，`BaseControllerImpl` 是其具体实现。所有具体的控制器都嵌入 `BaseControllerImpl` 以继承通用功能：

- **服务和日志访问**：
  - `GetServiceManager() *service.ServiceManager`: 获取服务管理器，用于访问服务层。
  - `GetLogger() logger.Logger`: 获取日志记录器。
- **HTTP 响应辅助函数**：
  - `Success(ctx iris.Context, data interface{})`: 返回标准成功的 JSON 响应。
  - `SuccessWithMessage(ctx iris.Context, message string, data interface{})`: 返回带自定义消息的成功 JSON 响应。
  - `Fail(ctx iris.Context, code int, message string)`: 返回标准失败的 JSON 响应。
  - `FailWithError(ctx iris.Context, err error)`: 根据 `error` 对象（特别是 `apperrors.CustomError`）返回失败 JSON 响应。
- **错误处理**:
  - `HandleError(ctx iris.Context, err error, opName string)`: 记录错误日志并调用 `FailWithError`。
- **上下文信息提取**:
  - `GetUserIDFromContext(ctx iris.Context) (uint64, error)`: 从请求上下文中安全提取用户 ID。如果提取失败或 ID 无效，返回错误。
  - `GetAccountBookIDFromContext(ctx iris.Context) (uint64, error)`: 从请求上下文中安全提取账套 ID。如果提取失败或 ID 无效，返回错误。
  - `GetUsernameFromContext(ctx iris.Context) (string, error)`: 从请求上下文中安全提取用户名。
  - `GetRoleIDsFromContext(ctx iris.Context) ([]uint, error)`: 从请求上下文中安全提取角色 ID 列表。
  - `IsAdminFromContext(ctx iris.Context) (bool, error)`: 从请求上下文中安全提取用户是否为管理员的标识。
    这些方法内部通过 `ctx.Request().Context()` 获取标准 `context.Context`，并调用 `pkg/util/context.go` 中对应的辅助函数（如 `GetUserIDFromStdContext`）来完成实际的提取和验证逻辑。这确保了上下文处理的一致性和健壮性。

### 2.2. `ControllerManager` (`controller_manager.go`)

`ControllerManager` 负责管理控制器实例的生命周期和依赖注入，与 `ServiceManager` 的角色类似。

- **主要功能**：
  - `NewControllerManager(serviceManager *service.ServiceManager)`: 创建实例，注入 `ServiceManager`。
  - `GetController[C any](m *ControllerManager, factory func(*ControllerManager) C) C`: 泛型方法，用于懒加载并缓存控制器实例。控制器类型作为缓存键。
  - `WithContext(ctx context.Context) *ControllerManager`: 创建一个新的、与指定请求上下文关联的 `ControllerManager`，并确保其内部的 `ServiceManager` 也关联此上下文。
  - 为每个具体控制器提供便捷的 Getter 方法（如 `GetUserController()`），内部调用 `GetController` 和相应的控制器构造函数。
  - 提供对 `ServiceManager`、`Logger` 和当前上下文的访问。

## 3. 具体控制器实现

所有具体控制器都遵循类似的模式：

1. 定义一个结构体（例如 `UserControllerImpl`），嵌入 `*BaseControllerImpl`。
2. 在结构体中声明其直接依赖的服务接口（例如 `service.UserService`）。
3. 提供一个构造函数（例如 `NewUserControllerImpl(cm *ControllerManager)`），该函数接收 `ControllerManager`，通过它初始化 `BaseControllerImpl` 并获取所需的服务实例。
4. 实现具体的 HTTP Handler 方法，这些方法通常是 `iris.Context` 的接收者。

### 3.1. `AuthControllerImpl` (`app_auth_controller_impl.go`)

- **职责**：处理用户认证，主要是登录。
- **主要端点**：
  - `POST /auth/login`: 用户登录。接收 `dto.UserLoginDTO`，调用 `AuthService.Login()`，成功后设置 `refresh_token` Cookie。
- **依赖**：`service.AuthService`, `config.Configuration`。

### 3.2. `CaptchaControllerImpl` (`app_captcha_controller_impl.go`)

- **职责**：生成和管理图形验证码。
- **主要端点**：
  - `GET /auth/captcha`: 获取验证码。如果配置中启用了验证码，则调用 `CaptchaService.GenerateCaptcha()` 返回验证码 ID 和图像数据。
- **依赖**：`service.CaptchaService`, `config.Configuration`。

### 3.3. `FileControllerImpl` (`app_file_controller_impl.go`)

- **职责**：处理文件上传和下载。
- **主要端点**：
  - `POST /files/upload` (示例路径): 上传文件。接收 `multipart/form-data`（包含 `file`, `businessType`, 可选的 `businessID`），调用 `FileService.UploadFile()`。
  - `GET /files/{id:uint}/download` (示例路径): 下载文件。根据文件 ID 调用 `FileService.DownloadFile()`，设置响应头并流式传输文件内容。
- **依赖**：`service.FileService` (通过 `GetServiceManager()` 获取)。

### 3.4. `LicenseControllerImpl` (`app_license_controller_impl.go`)

- **职责**：处理应用授权文件的上传和更新。
- **主要端点**：
  - `POST /admin/license/upload` (示例路径，通常需管理员权限): 上传授权文件。接收 `multipart/form-data` (包含 `licenseFile`)，读取文件内容并调用 `LicenseService.UpdateLicenseFromUpload()`。
- **依赖**：`service.LicenseService`。

### 3.5. `UserControllerImpl` (`sys_user_controller_impl.go`)

- **职责**：提供全面的用户管理接口。
- **主要端点 (部分)**：
  - 用户 CRUD: `POST /users`, `PUT /users/{id}`, `DELETE /users/{id}`, `GET /users/{id}`, `GET /users/page` (分页查询，支持多种过滤条件)。
  - 密码管理: `PUT /users/password` (用户修改密码), `PUT /users/{id}/reset-password` (管理员重置密码)。
  - 状态与安全: `PUT /users/{id}/status` (更新状态), `PUT /users/{id}/lock` (安全锁定), `PUT /users/{id}/unlock` (安全解锁)。
  - 个人资料: `GET /users/profile`, `PUT /users/profile`, `POST /users/avatar` (上传头像)。
  - 角色关联: `GET /users/{id}/roles`, `PUT /users/{id}/roles`。
- **依赖**：`service.UserService`。

## 4. 请求处理流程

一个典型的 HTTP 请求处理流程如下：

1. **路由匹配**：Iris 框架根据请求的 URL 和 HTTP 方法将请求路由到相应的 Controller 方法。
2. **中间件处理**：请求可能先经过一系列中间件（如日志记录、认证、授权、上下文设置如用户 ID/账套 ID）。
3. **参数提取与校验**：Controller 方法从 `iris.Context` 中读取路径参数、查询参数或请求体。对于请求体（通常是 JSON），会使用 `ctx.ReadJSON()` 解析到对应的 DTO 结构体，并使用 `pkg/validator` 进行校验。
4. **服务调用**：Controller 调用其依赖的 Service 接口的方法，传递必要的参数（包括从 `ctx.Request().Context()` 获取的上下文）。
5. **结果处理**：
   - 如果 Service 调用成功，Controller 使用 `BaseControllerImpl` 的 `Success()` 或 `SuccessWithMessage()` 方法将 Service 返回的 VO (View Object) 封装成标准 JSON 响应体。
   - 如果 Service 调用返回错误，Controller 使用 `BaseControllerImpl` 的 `HandleError()` 或 `FailWithError()` 方法，记录错误日志并将错误信息（通常来自 `apperrors.CustomError`）封装成标准 JSON 错误响应体。
6. **响应发送**：Iris 框架将 Controller 构建的响应发送回客户端。

## 5. 数据交互对象

- **DTO (Data Transfer Object)**：用于封装从客户端请求中传入的数据。定义在 `internal/model/dto` 包。控制器负责将请求数据绑定到 DTO，并进行校验。
- **VO (View Object)**：用于封装向客户端响应的数据。定义在 `internal/model/vo` 包。服务层通常返回 VO，控制器直接将其序列化为 JSON。

## 6. 错误处理与响应

- 控制器层面主要依赖 `BaseControllerImpl` 提供的错误处理方法。
- 服务层抛出的错误通常是 `apperrors.CustomError` 类型，包含了错误码（`Code`）、用户友好的消息（`Message`）和可选的原始错误（`Cause`）。
- `FailWithError` 方法能够识别 `CustomError`，并据此生成包含 `code` 和 `message` 字段的 JSON 响应，同时设置合适的 HTTP 状态码。
- 对于参数校验失败，`validator.Struct()` 返回的错误会被转换为 `apperrors.ValidationError`，其中包含了详细的字段级错误信息，也会被 `FailWithError`妥善处理。

通过这种分层设计和统一的辅助机制，`controller` 包能够清晰、高效地处理外部请求，并与业务逻辑层解耦。
