# VNTable 高级表格组件

VNTable 是一个功能强大的 Vue 3 表格组件，基于 Element Plus 的 Table 组件进行扩展，支持多种布局模式、数据过滤、编辑、排序、分页、导入/导出、行内验证等高级功能。

## 特性

- **多种布局模式**：支持表格、卡片和列表三种展示方式，满足不同场景需求。
- **响应式设计**：可根据屏幕尺寸自动切换布局，提供良好的移动端体验。
- **内置工具栏**：提供常用操作按钮，如新增、批量删除、刷新、筛选、布局切换、列设置、密度调整、全屏、导入、导出等，并支持自定义配置和插槽。
- **高级过滤**：
  - 支持文本、数字范围、日期范围、下拉选择等多种筛选类型。
  - 文本筛选支持多种操作符（包含、等于、开头是、结尾是、不包含、不等于），并可为特定列配置可用操作符。
  - 提供独立的筛选对话框。
- **行内编辑与验证**：
  - 支持行编辑模式 ('row')。
  - 可配置多种编辑组件（输入框、选择器、日期选择器、开关等），并支持自定义编辑组件插槽。
  - 支持基于 Element Plus `FormItemRule` 的 **单元格验证** (通过列配置的 `rules` 属性)。
  - 提供 `ValidateTable` 方法用于验证整个表格的可编辑行。
- **树形数据**：支持展示和操作树形结构数据，包括展开/折叠所有。
- **列设置**：允许用户动态控制列的显示/隐藏和固定。
- **导入/导出**：支持客户端将表格数据导出为 Excel 文件，以及从 Excel 文件导入数据（通过事件将数据传递给父组件处理）。导出前有确认提示。
- **拖拽排序**：(依赖 SortableJS) 支持行拖拽排序功能。
- **自定义能力强**：提供丰富的插槽（工具栏、列内容、列编辑、操作列、展开行、卡片/列表项等），方便进行深度定制。
- **移动端优化**：在小屏幕下自动切换布局，并可配置重要列优先显示。

## 安装

```bash
# 请确保已安装 Element Plus 和 xlsx (用于导入导出)
# 如果需要拖拽功能，还需要安装 sortablejs
npm install element-plus xlsx # sortablejs
# 或者
yarn add element-plus xlsx # sortablejs
```

## 基本用法 (示例)

```vue
<template>
  <VNTable
    ref="basicTableRef"
    :data="tableData"
    :columns="columns"
    :pagination="paginationConfig"
    :loading="loading"
    show-toolbar
    show-operations
    :operation-buttons="opButtons"
    row-key="id"
    @refresh="LoadData"
    @page-change="HandlePageChange"
    @page-size-change="HandlePageSizeChange"
    @sort-change="HandleSortChange"
    @import="HandleImportData"
    @edit-action="HandleEditAction" // 监听操作按钮的 action 事件
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import type { VNTableProps, TableColumn, PaginationConfig, ActionButton, VNTableMethods } from '@/components/VNTable/types';
import { ElMessage } from 'element-plus';

const loading = ref(false);
const basicTableRef = ref<InstanceType<typeof VNTable>>(); // 获取组件实例 ref
const tableData = ref<any[]>([]);

const columns = ref<TableColumn[]>([
  { type: 'selection', width: 50 },
  { prop: 'name', label: '姓名', width: 120, filterable: true, filterType: 'text', sortable: true },
  { prop: 'age', label: '年龄', width: 80, filterable: true, filterType: 'number', sortable: true },
  { prop: 'email', label: '邮箱', minWidth: 180, filterable: true },
  {
    prop: 'status', label: '状态', width: 100, filterable: true, filterType: 'select',
    filterOptions: [{ label: '活跃', value: 'active' }, { label: '禁用', value: 'disabled' }],
    // 使用 formatter 返回 VNode (ElTag)
    formatter: (row) => {
      return h(ElTag, { type: row.status === 'active' ? 'success' : 'info', size: 'small' }, () => row.status === 'active' ? '活跃' : '禁用')
    }
  },
  { prop: 'createTime', label: '创建时间', width: 150, filterable: true, filterType: 'date', sortable: true }
]);

const paginationConfig = reactive<PaginationConfig>({
  currentPage: 1,
  pageSize: 10,
  total: 0 // 初始时为0，由API返回
});

const opButtons = ref<ActionButton[]>([
  { label: '查看', icon: 'View', handler: (row) => console.log('View', row) },
  { label: '编辑', icon: 'Edit', type: 'primary', action: 'edit-action' }, // 点击时触发 @edit-action 事件
]);

const LoadData = async () => {
  loading.value = true;
  console.log('Fetching data...');
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 500));
  const apiResponse = {
    data: Array.from({ length: paginationConfig.pageSize }).map((_, i) => ({
      id: (paginationConfig.currentPage - 1) * paginationConfig.pageSize + i + 1,
      name: `User ${ (paginationConfig.currentPage - 1) * paginationConfig.pageSize + i + 1}`,
      age: 20 + Math.floor(Math.random() * 40),
      email: `user${ (paginationConfig.currentPage - 1) * paginationConfig.pageSize + i + 1}@test.com`,
      status: Math.random() > 0.5 ? 'active' : 'disabled',
      createTime: new Date().toISOString().split('T')[0]
    })),
    total: 58 // 假设总共有 58 条数据
  };
  tableData.value = apiResponse.data;
  paginationConfig.total = apiResponse.total;
  loading.value = false;
};

const HandlePageChange = (page: number) => {
  paginationConfig.currentPage = page;
  LoadData();
};

const HandlePageSizeChange = (size: number) => {
  paginationConfig.pageSize = size;
  paginationConfig.currentPage = 1; // 通常回到第一页
  LoadData();
};

const HandleSortChange = (sort: { prop: string; order: string | null }) => {
  console.log('Sort by:', sort.prop, sort.order);
  // 在API请求中加入排序参数，然后调用 LoadData()
  LoadData();
};

const HandleImportData = (importedData: any[]) => {
  console.log('Imported data received in parent:', importedData);
  // 在这里处理导入的数据，例如发送到后端或更新前端状态
  ElMessage.success(`接收到 ${importedData.length} 条导入数据，请在父组件中处理。`);
  // 可能需要重新加载数据
  // LoadData();
};

const HandleEditAction = (row: any) => {
  console.log('Edit action triggered for row:', row);
  // 执行编辑相关的逻辑，例如打开编辑对话框
};

onMounted(() => {
  LoadData();
});
</script>

## 行内编辑与验证

设置 `editable: true` 开启行编辑。在列配置中使用 `rules` 属性（类型为 Element Plus 的 `FormItemRule[]`）来定义单元格的验证规则。可以通过 `ref` 调用 `ValidateTable()` 方法来触发表格内所有可编辑行的验证。

```vue
<template>
  <div class="controls">
    <el-button type="primary" @click="validateTable">验证表格</el-button>
  </div>
  <VNTable
    ref="editableTableRef"
    :data="editableData"
    :columns="editableColumns"
    :editable="true"
    edit-mode="row"
    :show-operations="true"
    :operation-buttons="editOpButtons"
    row-key="id"
    @row-save="HandleSaveRow"
    @validate-error="handleValidationErrors"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { TableColumn, ActionButton, FormItemRule } from '@/components/VNTable/types';
import { ElMessage } from 'element-plus';

const editableTableRef = ref();
const editableData = ref([
  { id: 1, itemCode: 'A001', itemName: '笔记本', quantity: 10, price: 55.00, category: '办公' },
  { id: 2, itemCode: 'B002', itemName: null, quantity: 5, price: 120.00, category: '电子' }, // itemName 为空
  { id: 3, itemCode: 'C003', itemName: '鼠标', quantity: 0, price: 80.00, category: '电子' }, // quantity 为 0
]);

const editableColumns = ref<TableColumn[]>([
  {
    prop: 'itemCode', label: '物料编码', width: 120, editable: true,
    rules: [{ required: true, message: '编码不能为空', trigger: 'blur' }]
  },
  {
    prop: 'itemName', label: '物料名称', minWidth: 150, editable: true,
    rules: [{ required: true, message: '名称不能为空', trigger: 'blur' }]
  },
  {
    prop: 'quantity', label: '数量', width: 100, editable: true, 
    editComponent: 'input-number',
    editComponentProps: {
      min: 0,
      precision: 0
    }, 
    rules: [
      { required: true, message: '数量不能为空', trigger: 'change' },
      { type: 'number', min: 1, message: '数量必须大于0', trigger: 'change' }
    ]
  },
  {
    prop: 'price', label: '单价', width: 120, editable: true, editComponent: 'input',
    editComponentProps: { type: 'number', step: 0.01, min: 0 },
    formatter: (row, col, val) => val ? parseFloat(val).toFixed(2) : '0.00', // 显示时格式化
    rules: [
      { required: true, message: '单价不能为空', trigger: 'change' },
      { type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'change' }
    ]
  },
  {
    prop: 'category', label: '类别', width: 130, editable: true, editComponent: 'select',
    editComponentProps: {
      options: [ { label: '办公', value: '办公' }, { label: '电子', value: '电子' }, { label: '家居', value: '家居' } ]
    },
    rules: [{ required: true, message: '请选择类别', trigger: 'change' }]
  }
]);

const editOpButtons = ref<ActionButton[]>([
  { label: '保存', action: 'save', type: 'primary', icon: 'Check' },
  { label: '取消', action: 'cancel', icon: 'Close' },
]);

// 处理行保存事件 (保存前会触发表单验证)
const HandleSaveRow = async (row: any, index: number, oldRow: any) => {
  console.log('Saving row:', row);
  // 调用API保存数据...
  // 模拟保存成功
  await new Promise(resolve => setTimeout(resolve, 300));
  ElMessage.success('保存成功');
};

// 验证整个表格
const validateTable = async () => {
  if (editableTableRef.value) {
    const isValid = await editableTableRef.value.ValidateTable();
    if (isValid) {
      ElMessage.success('表格验证通过！');
    } else {
      ElMessage.error('表格验证失败，请检查错误项。');
    }
  }
};

// 统一处理验证错误
const handleValidationErrors = (errors: any) => {
  console.error("Validation Errors:", errors);
  // 可以根据需要进一步处理，比如滚动到第一个错误位置
};
</script>

## API 参考

### Props (`VNTableProps`)

| 属性名             | 类型                     | 默认值                         | 说明                                                                 |
| ------------------ | ------------------------ | ------------------------------ | -------------------------------------------------------------------- |
| `data`             | `Array<any>`             | `[]`                           | 表格数据源                                                             |
| `columns`          | `Array<TableColumn>`     | `[]`                           | 表格列配置                                                             |
| `loading`          | `Boolean`                | `false`                        | 是否显示加载状态                                                         |
| `rowKey`           | `String` / `Function`    | `'id'`                         | 行数据的唯一标识符，用于优化渲染和状态保持                             |
| `selectionType`    | `'none'/'single'/'multiple'` | `'none'`                       | 行选择模式                                                             |
| `defaultExpandAll` | `Boolean`                | `false`                        | 是否默认展开所有树形节点                                                   |
| `expandRowKeys`    | `Array<string>`          | `[]`                           | 默认展开的行的 key 数组                                                |
| `treeProps`        | `Object`                 | `{ children: 'children', hasChildren: 'hasChildren' }` | 树形数据配置                                                         |
| `highlightCurrentRow` | `Boolean`             | `false`                        | 是否高亮当前点击的行                                                      |
| `rowClassName`     | `String` / `Function`    |                                | 行的 className 的回调函数或字符串                                        |
| `pagination`       | `PaginationConfig` / `false` | `false`                      | 分页配置对象，或 `false` 禁用分页                                      |
| `showToolbar`      | `Boolean`                | `true`                         | 是否显示工具栏                                                           |
| `toolbarConfig`    | `Object`                 | `{...}` (大部分按钮默认开启)    | 工具栏按钮显隐配置及其他设置（如 `filterDialogWidth`）                     |
| `showOperations`   | `Boolean`                | `false`                        | 是否显示操作列                                                           |
| `operationWidth`   | `Number` / `String`      | `150`                          | 操作列宽度                                                             |
| `operationFixed`   | `Boolean` / `'left'/'right'` | `'right'`                    | 操作列固定位置                                                         |
| `operationLabel`   | `String`                 | `'操作'`                       | 操作列标题                                                             |
| `operationButtons` | `Array<ActionButton>`    | `[]`                           | 操作列按钮配置数组                                                       |
| `emptyText`        | `String`                 | `'暂无数据'`                   | 数据为空时显示的文本                                                       |
| `showIndex`        | `Boolean`                | `false`                        | 是否显示序号列                                                           |
| `indexLabel`       | `String`                 | `'序号'`                       | 序号列标题                                                             |
| `indexWidth`       | `Number` / `String`      | `60`                           | 序号列宽度                                                             |
| `border`           | `Boolean`                | `true`                         | 是否显示纵向边框                                                         |
| `stripe`           | `Boolean`                | `true`                         | 是否显示斑马纹                                                           |
| `editable`         | `Boolean`                | `false`                        | 是否启用表格编辑功能                                                     |
| `editMode`         | `'row'/'cell'`           | `'row'`                        | 编辑模式：行编辑 (`cell` 模式待完善)                                       |
| `showHeader`       | `Boolean`                | `true`                         | 是否显示表头                                                             |
| `height`           | `String` / `Number`      | `'auto'`                       | 表格固定高度                                                           |
| `maxHeight`        | `String` / `Number`      |                                | 表格最大高度                                                           |
| `layoutMode`       | `LayoutMode`             | `'table'`                      | 初始布局模式 (`'table'`, `'card'`, `'list'`)                           |
| `layoutModes`      | `Array<LayoutMode>`      | `['table', 'card', 'list']`  | 可用的布局模式列表                                                       |
| `showLayoutSwitch` | `Boolean`                | `true`                         | 是否在工具栏显示布局切换按钮 (需 `toolbarConfig.layoutSwitch` 也为 true) |
| `cardConfig`       | `CardConfig`             | `{...}` (默认值)              | 卡片布局配置对象                                                         |
| `listConfig`       | `ListConfig`             | `{...}` (默认值)              | 列表布局配置对象                                                         |
| `autoSwitchLayout` | `Boolean`                | `true`                         | 是否在小屏幕下自动切换到卡片/列表布局                                      |
| `enableDrag`       | `Boolean`                | `false`                        | (暂未集成) 是否启用行拖拽                                              |
| `dragSort`         | `Boolean`                | `false`                        | (暂未集成) 是否启用拖拽排序                                              |
| `resizableColumn`  | `Boolean`                | `false`                        | 列宽是否可通过拖拽调整                                                   |
| `id`               | `String`                 |                                | 表格 DOM 元素的 ID (可选)                                               |

### Column Configuration (`TableColumn`)

| 属性名             | 类型                                           | 默认值 | 说明                                                                                                             |
| ------------------ | ---------------------------------------------- | ------ | -------------------------------------------------------------------------------------------------------------- |
| `prop`             | `String`                                       |        | 对应 `data` 中的字段名                                                                                           |
| `label`            | `String`                                       |        | 列标题                                                                                                         |
| `width`            | `String` / `Number`                            |        | 列宽度                                                                                                         |
| `minWidth`         | `String` / `Number`                            |        | 最小列宽                                                                                                       |
| `fixed`            | `Boolean` / `'left'/'right'`                   |        | 列固定位置                                                                                                     |
| `align`            | `'left'/'center'/'right'`                      |        | 列内容对齐方式                                                                                                 |
| `visible`          | `Boolean`                                      | `true` | 列是否可见 (用于列设置)，默认为 `true`                                                                          |
| `important`        | `Boolean`                                      | `false`| 是否为重要列 (用于移动端优化)，默认为 `false`                                                                 |
| `formatter`        | `Function`                                     |        | 格式化单元格内容，`(row, column, cellValue, index) => string / VNode`， **注意：如果返回 VNode，导出功能可能无法获取纯文本** |
| `className`        | `String`                                       |        | 列的 CSS 类名                                                                                                  |
| `headerClassName`  | `String`                                       |        | 列头部的 CSS 类名                                                                                              |
| `sortable`         | `Boolean` / `'custom'`                         |        | 是否可排序，`'custom'` 表示使用自定义排序 (需监听 `sort-change` 事件)                                          |
| `sortMethod`       | `Function`                                     |        | 本地排序方法 `(a, b) => number`                                                                                |
| `sortBy`           | `String` / `Array<string>` / `Function`        |        | 指定排序依据的字段或方法                                                                                       |
| `sortOrders`       | `Array<'ascending' \| 'descending' \| null>` |        | 支持的排序顺序                                                                                                 |
| `filters`          | `Array<{text: string; value: any}>`            |        | (原生) 表头筛选菜单的选项                                                                                     |
| `filterMethod`     | `Function`                                     |        | (原生) 数据过滤使用的方法                                                                                     |
| `filterMultiple`   | `Boolean`                                      |        | (原生) 是否可多选                                                                                             |
| `filterable`       | `Boolean`                                      | `false`| **(推荐)** 是否启用 VNTable 的高级筛选功能，默认为 `false`                                                      |
| `filterType`       | `'text'/'number'/'date'/'select'`             |        | **(推荐)** VNTable 高级筛选的控件类型                                                                            |
| `filterOptions`    | `Array<{label: string; value: any}>`           |        | **(推荐)** 当 `filterType` 为 `'select'` 时，提供的下拉选项                                                     |
| `filterOperators`  | `Array<string>`                                |        | **(推荐)** 当 `filterType` 为 `'text'` 时，指定允许使用的筛选操作符，默认为所有操作符                          |
| `type`             | `'selection'/'index'/'expand'`                 |        | 列类型 (特殊列)                                                                                                |
| `slot`             | `Boolean`                                      | `false`| 是否使用自定义插槽 `column-[prop]` 来渲染单元格内容                                                              |
| `children`         | `Array<TableColumn>`                           |        | 多级表头配置                                                                                                   |
| `editable`         | `Boolean`                                      |        | 该列是否可编辑                                                                                                 |
| `editComponent`    | `'input'/'select'/'datepicker'/'switch'/'input-number'/'custom'` |        | 编辑时使用的组件类型 (`'custom'` 需要配合 `editor-[prop]` 插槽)                                                 |
| `editComponentProps`| `Record<string, any>`                        |        | 传递给编辑组件的属性                                                                                           |
| `rules`            | `Array<FormItemRule>`                          |        | **(行编辑)** 单元格编辑时的校验规则 (基于 Element Plus `FormItemRule`)                                          |
| `showOverflowTooltip`| `Boolean`                                      |        | 控制是否在内容溢出时显示 tooltip                                                                                 |

### Events (`VNTableEmits`)

| 事件名                  | 参数                                                                          | 说明                                                                                            |
| ----------------------- | ----------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------- |
| `selection-change`      | `rows: Array<any>`                                                            | 当选择项发生变化时触发                                                                          |
| `select`                | `selection: Array<any>, row: any`                                             | 当用户手动勾选数据行的 Checkbox 时触发                                                          |
| `select-all`            | `selection: Array<any>`                                                       | 当用户手动勾选表头 Checkbox 全选时触发                                                          |
| `page-change`           | `page: number`                                                                | `currentPage` 改变时触发                                                                        |
| `page-size-change`      | `size: number`                                                                | `pageSize` 改变时触发                                                                           |
| `sort-change`           | `sortInfo: { column: any, prop: string, order: 'ascending'\|'descending'\|null }` | 当用户点击表头排序时触发                                                                          |
| `row-click`             | `row: any, column: any, event: Event`                                         | 当某一行被点击时触发                                                                            |
| `row-dblclick`          | `row: any, column: any, event: Event`                                         | 当某一行被双击时触发                                                                            |
| `row-contextmenu`       | `row: any, column: any, event: Event`                                         | 当某一行被右键点击时触发                                                                          |
| `expand-change`         | `row: any, expanded: boolean`                                                 | 当用户对某一行进行展开或收起操作时触发                                                          |
| `cell-click`            | `row: any, column: any, cell: any, event: Event`                              | 当某个单元格被点击时触发                                                                        |
| `cell-dblclick`         | `row: any, column: any, cell: any, event: Event`                              | 当某个单元格被双击时触发                                                                        |
| `cell-contextmenu`      | `row: any, column: any, cell: any, event: Event`                              | 当某个单元格被右键点击时触发                                                                    |
| `row-save`              | `row: any, index: number, oldRow: any`                                        | **(行编辑模式)** 当用户点击行内保存按钮 **并且行内验证通过后** 触发                           |
| `row-cancel`            | `row: any, index: number`                                                     | **(行编辑模式)** 当用户点击行内取消按钮时触发                                                   |
| `row-validate-complete` | `prop: string, isValid: boolean, message: string, row: any`                 | **(行编辑模式)** 单个单元格验证完成后触发 (无论成功或失败)                                    |
| `validate-error`        | `errors: Record<string, { errors: any[], row: any }>`                       | **(行编辑模式)** 当调用 `ValidateTable` 方法且存在验证错误时触发 (汇总所有错误)                 |
| `refresh`               |                                                                               | 当用户点击工具栏刷新按钮时触发                                                                  |
| `import`                | `data: Array<any>`                                                            | 当用户选择 Excel 文件并解析成功后触发，`data` 是解析出的 JSON 对象数组                          |
| `export`                |                                                                               | 当用户确认导出并成功开始生成文件时触发                                                          |
| `add`                   |                                                                               | 当用户点击工具栏新增按钮时触发                                                                  |
| `batch-delete`          | `rows: Array<any>`                                                            | 当用户确认批量删除后触发 (传递选中的行)                                                         |
| `expand-all`            |                                                                               | 当用户点击工具栏展开全部按钮时触发                                                              |
| `collapse-all`          |                                                                               | 当用户点击工具栏收起全部按钮时触发                                                              |
| `filter`                | `filteredData: Array<any>, filterValues: Record<string, any>`                | 当用户应用高级筛选条件后触发（`filteredData` 是前端筛选结果，如果使用后端筛选，需自行处理）|
| `filter-clear`          |                                                                               | 当用户点击重置筛选按钮时触发                                                                  |
| `layout-change`         | `mode: LayoutMode`                                                            | 当布局模式切换时触发                                                                          |
| `density-change`        | `density: 'default'\|'small'\|'large'`                                        | 当表格密度改变时触发                                                                          |
| `column-change`         | `columns: Array<{ prop: string; visible: boolean }>`                          | 当列的显示/隐藏状态改变时触发                                                                 |
| `[action: string]`      | `row: any, index: number`                                                     | 当操作按钮配置了 `action` 属性时，会触发对应名称的事件 (如 `action: 'audit'` 触发 `@audit`)   |

### Methods (Exposed via `ref`)

| 方法名               | 参数                                          | 返回值                 | 说明                                                            |
| -------------------- | --------------------------------------------- | ---------------------- | --------------------------------------------------------------- |
| `Refresh`            |                                               | `void`                 | 触发 `refresh` 事件                                               |
| `Reset`              |                                               | `void`                 | 重置表格状态（筛选、排序、选择、编辑状态，**不恢复 data**）         |
| `ImportTable`        |                                               | `void`                 | 触发文件选择框以导入数据                                        |
| `ExportTable`        |                                               | `void`                 | 触发导出确认框并导出当前显示数据                                |
| `Add`                |                                               | `void`                 | 触发 `add` 事件                                                 |
| `BatchDelete`        |                                               | `void`                 | 触发批量删除确认框                                              |
| `ExpandAll`          |                                               | `void`                 | 展开所有树形节点（需表格支持）                                  |
| `CollapseAll`        |                                               | `void`                 | 收起所有树形节点（需表格支持）                                  |
| `SwitchLayout`       | `mode: LayoutMode`                            | `void`                 | 切换到指定的布局模式                                            |
| `GetCurrentLayout`   |                                               | `LayoutMode`           | 获取当前布局模式                                                |
| `ApplyFilters`       |                                               | `void`                 | 手动应用当前筛选条件 (主要用于内部或特殊场景)                   |
| `ClearAllFilters`    |                                               | `void`                 | 清除所有高级筛选条件并触发 `filter-clear`                     |
| `GetFilterValues`    |                                               | `Record<string, any>` | 获取当前的筛选值对象                                            |
| `GetSelectionRows`   |                                               | `Array<any>`           | 获取当前选中的行数据数组                                        |
| `ClearSelection`     |                                               | `void`                 | 清空所有选中行                                                  |
| `ToggleRowSelection` | `row: any, selected?: boolean`                | `void`                 | 切换指定行的选中状态                                            |
| `ToggleAllSelection` |                                               | `void`                 | 切换全选/全不选状态                                             |
| `ToggleRowExpansion` | `row: any, expanded?: boolean`                | `void`                 | 切换指定行的展开状态                                            |
| `Sort`               | `prop: string, order: 'asc'\|'desc'\|null` | `void`                 | 手动对表格按指定列和顺序排序                                    |
| `ValidateTable`      |                                               | `Promise<boolean>`     | **(行编辑)** 验证当前所有可编辑行的规则，返回是否全部通过          |
| `toggleRowEditing`   | `row: any, editing?: boolean`                 | `void`                 | **(行编辑)** 切换指定行的编辑状态                               |
| `resetData`          |                                               | `void`                 | (内部方法别名，等同于 Reset)重置表格状态                      |
| `SetCurrentRow`      | `row?: any`                                   | `void`                 | (Element Plus) 设置当前高亮行                                 |
| `ClearSort`          |                                               | `void`                 | (Element Plus) 清除排序状态                                     |
| `ClearFilter`        | `columnKeys?: string | string[]`            | `void`                 | (Element Plus) 清除指定列的原生筛选状态                       |
| `DoLayout`           |                                               | `void`                 | (Element Plus) 对 Table 进行重新布局，当 Table 或其祖先元素由隐藏切换为显示时使用 |
| `ScrollToRow`        | `row: any`                                    | `void`                 | (暂未完整实现) 滚动到指定行                                     |

### Slots

| 插槽名              | 参数                                       | 说明                                                                             |
| ------------------- | ------------------------------------------ | -------------------------------------------------------------------------------- |
| `toolbar-left`      |                                            | 工具栏左侧区域，用于添加自定义按钮或元素                                         |
| `toolbar-right`     |                                            | 工具栏右侧区域，用于添加自定义按钮或元素                                         |
| `column-[prop]`     | `{ row, column, index, $index }`           | 自定义列单元格的渲染内容，`[prop]` 替换为列配置的 `prop` 值                       |
| `editor-[prop]`     | `{ row, column, index }`                   | 自定义列编辑器的渲染内容，`[prop]` 替换为列配置的 `prop` 值                       |
| `operation`         | `{ row, index }`                           | 自定义整个操作列的内容 (如果定义了此插槽，`operationButtons` prop 将失效)         |
| `expand`            | `{ row, index }`                           | 自定义展开行的内容                                                               |
| `card-content`      | `{ row, index }`                           | 自定义卡片布局的主要内容区域 (不含 Header 和 Footer)                             |
| `card-header`       | `{ row, index }`                           | 自定义卡片布局的头部区域                                                         |
| `card-footer`       | `{ row, index }`                           | 自定义卡片布局的底部区域（默认显示操作按钮，若配置了 `cardConfig.showFooter`）   |
| `list-item`         | `{ row, index }`                           | 自定义列表布局的整个列表项内容 (如果定义了此插槽，`list-actions` 可能需要自行实现) |
| `list-image`        | `{ row, index }`                           | 自定义列表布局的图片区域 (如果 `listConfig.showImage` 为 true)                  |
| `list-content`      | `{ row, index }`                           | 自定义列表布局的主要内容区域 (标题、副标题、描述)                                |
| `list-actions`      | `{ row, index }`                           | 自定义列表布局的操作按钮区域                                                     |

## 潜在优化点与未来方向

以下是一些在进一步开发或特定场景下可以考虑的优化点：

1.  **大数据量下的前端筛选性能 (`applyLocalFilters`)**: 
    -   对于包含数千条以上记录的超大数据集，纯前端筛选可能会遇到性能瓶颈。
    -   **推荐方案**: 优先考虑后端筛选。
    -   **前端备选**: Web Worker (增加复杂度)、预处理/索引 (增加内存和初始时间)。

2.  **行编辑模式下的 `deepCopy` 性能**: 
    -   对于数据结构异常复杂或嵌套层级极深的行，且编辑操作极其频繁时，`deepCopy` 可能带来性能开销。
    -   **备选方案**: Diff/Patch 机制、使用 Immer 等不可变数据库。
    -   **当前策略**: `deepCopy` 是通用场景下复杂度和性能的良好平衡。

3.  **缓存策略**: 
    -   当前使用简单的 `Map` 和大小限制 (`CACHE_SIZE_LIMIT`)。
    -   **可选优化**: 实现 LRU (Least Recently Used) 淘汰策略，以提高高频访问下的缓存命中率，但这会增加实现复杂度。

4.  **`Sort` 方法的类型断言 (`as any`)**: 
    -   当前为解决 Element Plus 类型签名问题使用了 `as any`。
    -   **理想方案**: 持续关注 Element Plus 的类型更新，或深入研究其类型定义，以找到更精确的类型替代 `as any`。

5.  **常量与配置**: 
    -   部分内部常量如 `MOBILE_BREAKPOINT`, `CACHE_SIZE_LIMIT` 等可以考虑提升为 Props，以提供更高的灵活性和可配置性。

6.  **`formatter` 返回 VNode 的处理**: 
    -   `FormatCellValue` 当前直接缓存 `formatter` 的返回值，如果返回的是 VNode，在某些场景（如导出、需要纯文本的地方）可能需要特殊处理。
    -   **可选方案**: 严格化 `formatter` 只返回字符串，或在 `FormatCellValue` 中增加对 VNode 的处理逻辑（如提取文本）。

7.  **国际化 (i18n)**: 
    -   组件包含部分硬编码的中文字符串。
    -   **未来方向**: 如果需要在多语言环境中使用，应引入 `vue-i18n` 等方案进行国际化改造。

## 封装与引用 (Packaging and Usage)

VNTable 组件被设计为一个独立的、可复用的单元，你可以根据需要在不同场景下引用它。

### 1. 项目内部直接引用 (Direct Project Import)

这是最简单的方式，适用于在同一个 Vue 项目的不同部分使用 VNTable。

**方法:**
直接在需要使用组件的 `.vue` 文件中导入即可。

```vue
<script setup lang="ts">
import VNTable from '@/components/VNTable/index.vue';
import type { VNTableProps } from '@/components/VNTable/types';
// ...
</script>

<template>
  <VNTable :data="myData" :columns="myColumns" />
</template>
```

**优点:**

- 简单直接，无需额外配置。
- 代码更改即时生效。

**缺点:**

- 仅限于当前项目内复用。

### 2. 发布为 NPM 包 (Publish as NPM Package)

如果你需要在多个独立的项目之间共享 VNTable 组件，可以将其打包并发布到 NPM（公共或私有仓库）。

**方法:**

1. **配置构建:** 使用构建工具（如 Vite 的库模式或 Vue CLI 的库构建目标）将组件打包成库。
    - 需要配置 `vite.config.ts` 或 `vue.config.js`。
    - 确保生成 JavaScript 文件 (ESM, CommonJS/UMD) 和类型声明文件 (`.d.ts`)。
2. **配置 `package.json`:**
    - 指定入口文件 (`main`, `module`, `types`)。
    - 声明 `peerDependencies` (如 `vue`, `element-plus`, `xlsx`)，这些依赖需要由使用方项目安装。
    - 添加必要的构建和发布脚本。
3. **发布到 NPM:** 使用 `npm publish` 命令。
4. **在其他项目中使用:**

    ```bash
    npm install your-vntable-package-name
    # 或者
    yarn add your-vntable-package-name
    ```

    然后在代码中导入：

    ```vue
    <script setup lang="ts">
    import VNTable from 'your-vntable-package-name';
    import type { VNTableProps } from 'your-vntable-package-name/dist/types'; // 路径可能依构建配置而定
    // ...
    </script>
    ```

**优点:**

- 标准化的跨项目共享方式。
- 便于版本管理和依赖控制。
- 可以发布到公共 NPM，供更广泛的用户使用。

**缺点:**

- 需要配置构建和发布流程，相对复杂。
- 更新组件后需要重新构建、发布和在引用方更新版本。

### 3. 使用 Monorepo 管理 (Manage with Monorepo)

如果你的多个项目关联紧密，并且希望在一个代码库中同时管理它们和共享的组件，可以使用 Monorepo 方案。

**方法:**

1. **选择工具:** 使用如 pnpm workspaces, Turborepo, Lerna 等 Monorepo 管理工具。
2. **项目结构:** 通常将共享组件（如 VNTable）放在 `packages` 目录下，作为一个独立的包。

    ```bash
    your-monorepo/
    ├── packages/
    │   ├── vntable/       # VNTable 组件包
    │   │   ├── src/
    │   │   ├── package.json
    │   │   └── ...
    │   └── ...
    ├── apps/
    │   ├── project-a/     # 应用项目 A
    │   │   ├── package.json # 可以在这里引用本地的 vntable 包
    │   │   └── ...
    │   └── project-b/
    │       └── ...
    ├── package.json       # Monorepo 根配置
    └── ...
    ```

3. **本地引用:** 在应用项目（如 `project-a`）的 `package.json` 中，将 `vntable` 作为依赖项，通常使用 `workspace:` 协议（如 `"vntable": "workspace:*"`）。
4. **工具配置:** 根据所选的 Monorepo 工具进行配置，管理依赖安装、构建、测试等流程。

**优点:**

- 在单一代码库中方便地共享代码和类型。
- 依赖管理更集中。
- 本地开发和调试关联项目更方便。

**缺点:**

- 需要学习和配置 Monorepo 工具。
- 整个代码库可能变得较大。
- 构建和 CI/CD 流程可能更复杂。

根据你的具体需求和团队情况，可以选择最合适的封装和引用方式。
