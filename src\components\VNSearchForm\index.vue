<template>
  <div class="vn-search-form">
    <el-form
      ref="formRef"
      :model="model"
      :inline="inline"
      :label-width="labelWidth"
      class="search-form"
    >
      <el-row :gutter="gutter">
        <el-col
          v-for="field in visibleFields"
          :key="field.field"
          :span="getColSpan(field)"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          :xl="4"
        >
          <el-form-item
            :label="field.label"
            :prop="field.field"
            :label-width="field.labelWidth || labelWidth"
          >
            <!-- 输入框 -->
            <el-input
              v-if="field.type === 'input'"
              v-model="model[field.field]"
              :placeholder="field.placeholder"
              v-bind="field.props"
              clearable
            />
            
            <!-- 选择器 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="model[field.field]"
              :placeholder="field.placeholder"
              v-bind="field.props"
              clearable
              filterable
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </el-select>
            
            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="field.type === 'date'"
              v-model="model[field.field]"
              type="date"
              :placeholder="field.placeholder"
              v-bind="field.props"
              clearable
            />
            
            <!-- 日期范围选择器 -->
            <el-date-picker
              v-else-if="field.type === 'daterange'"
              v-model="model[field.field]"
              type="daterange"
              :range-separator="field.props?.rangeSeparator || '至'"
              :start-placeholder="field.props?.startPlaceholder || '开始日期'"
              :end-placeholder="field.props?.endPlaceholder || '结束日期'"
              v-bind="field.props"
              clearable
            />
            
            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="model[field.field]"
              :placeholder="field.placeholder"
              v-bind="field.props"
              style="width: 100%"
            />
            
            <!-- 开关 -->
            <el-switch
              v-else-if="field.type === 'switch'"
              v-model="model[field.field]"
              v-bind="field.props"
            />
            
            <!-- 级联选择器 -->
            <el-cascader
              v-else-if="field.type === 'cascader'"
              v-model="model[field.field]"
              :options="field.options"
              :placeholder="field.placeholder"
              v-bind="field.props"
              clearable
            />
            
            <!-- 自定义插槽 -->
            <slot
              v-else-if="field.type === 'slot'"
              :name="field.field"
              :field="field"
              :model="model"
            />
          </el-form-item>
        </el-col>
        
        <!-- 操作按钮 -->
        <el-col :span="actionColSpan" class="action-col">
          <el-form-item>
            <div class="action-buttons">
              <el-button
                type="primary"
                :icon="Search"
                @click="handleSearch"
                :loading="loading"
              >
                搜索
              </el-button>
              <el-button
                :icon="RefreshRight"
                @click="handleReset"
              >
                重置
              </el-button>
              <el-button
                v-if="showExpand && fields.length > defaultShowCount"
                type="text"
                :icon="isExpanded ? ArrowUp : ArrowDown"
                @click="toggleExpand"
              >
                {{ isExpanded ? '收起' : '展开' }}
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Search, RefreshRight, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// 字段配置接口
export interface SearchField {
  field: string
  label: string
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'switch' | 'cascader' | 'slot'
  placeholder?: string
  options?: Array<{ label: string; value: any; disabled?: boolean }>
  props?: Record<string, any>
  labelWidth?: string
  span?: number
  show?: boolean
  required?: boolean
}

// Props
interface Props {
  fields: SearchField[]
  model: Record<string, any>
  inline?: boolean
  labelWidth?: string
  gutter?: number
  loading?: boolean
  showExpand?: boolean
  defaultShowCount?: number
  actionColSpan?: number
}

const props = withDefaults(defineProps<Props>(), {
  inline: false,
  labelWidth: '80px',
  gutter: 16,
  loading: false,
  showExpand: true,
  defaultShowCount: 3,
  actionColSpan: 6
})

// Emits
const emit = defineEmits<{
  search: []
  reset: []
  'update:model': [value: Record<string, any>]
}>()

// 响应式数据
const formRef = ref()
const isExpanded = ref(false)

// 计算属性
const visibleFields = computed(() => {
  if (!props.showExpand || isExpanded.value) {
    return props.fields.filter(field => field.show !== false)
  }
  return props.fields
    .filter(field => field.show !== false)
    .slice(0, props.defaultShowCount)
})

// 获取列宽
const getColSpan = (field: SearchField): number => {
  if (field.span) return field.span
  
  // 根据字段类型自动计算列宽
  switch (field.type) {
    case 'daterange':
      return 8
    case 'cascader':
      return 8
    default:
      return 6
  }
}

// 方法
const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置模型数据
  const resetModel: Record<string, any> = {}
  props.fields.forEach(field => {
    resetModel[field.field] = undefined
  })
  
  emit('update:model', resetModel)
  emit('reset')
}

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 监听模型变化
watch(
  () => props.model,
  (newVal) => {
    emit('update:model', newVal)
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.vn-search-form {
  .search-form {
    .action-col {
      display: flex;
      align-items: flex-end;
      
      .el-form-item {
        margin-bottom: 0;
      }
      
      .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .vn-search-form {
    .action-col {
      .action-buttons {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style> 