package entity

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// FinExchangeRate 汇率管理
type FinExchangeRate struct {
	TenantEntity
	FromCurrencyID uint            `gorm:"column:from_currency_id;type:bigint unsigned;not null;uniqueIndex:idx_currency_date;comment:源币种ID" json:"fromCurrencyId"`
	ToCurrencyID   uint            `gorm:"column:to_currency_id;type:bigint unsigned;not null;uniqueIndex:idx_currency_date;comment:目标币种ID" json:"toCurrencyId"`
	Rate           decimal.Decimal `gorm:"column:rate;type:decimal(18,8);not null;comment:汇率" json:"rate"`
	RateDate       time.Time       `gorm:"column:rate_date;type:date;not null;uniqueIndex:idx_currency_date;comment:汇率日期" json:"rateDate"`

	// Associations
	FromCurrency FinCurrency `gorm:"foreignKey:FromCurrencyID" json:"fromCurrency"`
	ToCurrency   FinCurrency `gorm:"foreignKey:ToCurrencyID" json:"toCurrency"`
}

func (FinExchangeRate) TableName() string {
	return "fin_exchange_rate"
}

// Gorm V2 To add a composite index, you can use the gorm tag, for example:
// `gorm:"uniqueIndex:idx_source_target_date"`
// We will apply this to all relevant fields.
func (f *FinExchangeRate) BeforeSave(tx *gorm.DB) (err error) {
	// In GORM, composite unique indexes are typically defined at the struct level
	// using tags on each field that is part of the index.
	// For example:
	// SourceCurrencyID uint `gorm:"uniqueIndex:idx_currency_date"`
	// TargetCurrencyID uint `gorm:"uniqueIndex:idx_currency_date"`
	// EffectiveDate    time.Time `gorm:"uniqueIndex:idx_currency_date"`
	// This approach is clearer and managed by GORM's auto-migration.
	// We will modify the struct definition to include these tags.
	return nil
}

// We will update the struct directly to include the unique index.
// The BeforeSave hook is not the right place for index definition.
