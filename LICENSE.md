# 软件授权机制实现指南

本文档描述了本软件采用的授权生成与验证机制的实现步骤。

## 1. 核心原理

本授权机制基于 **RSA 非对称加密** 实现数字签名，以确保授权文件的真实性和完整性。

*   **生成端**：使用 **私钥** 对包含授权信息（客户、有效期、功能等）的数据进行签名。
*   **认证端**：使用预置的 **公钥** 验证授权文件的签名是否有效，并解析授权信息。

## 2. 文件结构 (`pkg/license`)

授权相关的核心代码位于 `pkg/license` 目录下，文件职责如下：

*   **`model.go`**: 定义核心的 `LicenseInfo` Go 结构体，包含所有授权相关字段，以及通用的辅助方法（如 `IsExpired`, `HasFeature`）。此文件由生成端和认证端共享。
*   **`crypto_shared.go`**: 包含通用的加密原语和辅助函数，如加载 PEM 密钥、底层 RSA 签名/验证、JSON 处理等。此文件由生成端和认证端按需引用。
*   **`generator.go`**: **仅用于生成端**。包含生成授权文件的逻辑，接收 `LicenseInfo` 和 **私钥**，输出带有签名的授权文件。**此文件绝不能包含在最终交付的客户端应用程序中。**
*   **`verifier.go`**: **仅用于认证端（客户端应用程序）**。包含验证授权文件的逻辑，接收授权文件内容和 **公钥**，验证签名并返回解析后的 `LicenseInfo`。**此文件将包含在客户端应用程序中，且不依赖任何私钥信息。**

## 3. 密钥生成

需要生成一对 RSA 密钥（公钥和私钥）。建议使用 2048 位或更高强度的密钥。可以使用 `openssl` 工具生成：

```bash
# 1. 生成 RSA 私钥 (请务必妥善保管 private_key.pem 文件)
openssl genpkey -algorithm RSA -out private_key.pem -pkeyopt rsa_keygen_bits:2048

# 2. 从私钥中提取公钥
openssl rsa -pubout -in private_key.pem -out public_key.pem
```

*   **`private_key.pem`**: **私钥**。必须 **绝对安全地保管** 在生成端，**严禁泄露**。用于对授权文件进行签名。
*   **`public_key.pem`**: **公钥**。需要嵌入到客户端应用程序中（例如，作为 Go 代码中的字节数组常量）或随应用程序安全分发，用于验证授权文件的签名。

## 4. 授权生成步骤 (生成端)

1.  **准备数据**: 根据客户需求，创建一个 `license.LicenseInfo` 结构体实例，并填充所有必要的授权信息（客户名、到期日、功能列表等）。
2.  **加载私钥**: 安全地从 `private_key.pem` 文件或其他安全存储中加载 RSA 私钥。
3.  **调用生成器**: 调用 `pkg/license/generator.go` 中定义的生成函数（例如 `GenerateLicenseFile`），传入 `LicenseInfo` 实例和加载的私钥。
4.  **生成文件**: 该函数会执行以下操作：
    *   将 `LicenseInfo` 序列化（例如，转为 JSON）。
    *   使用私钥对序列化数据进行签名。
    *   将原始数据和签名打包（例如，放入一个包含 `data` 和 `signature` 字段的 JSON 结构中）。
    *   将最终内容写入指定的授权文件（例如 `customer.lic`）。
5.  **分发**: 将生成的授权文件 (`customer.lic`) 安全地分发给客户。

*(可选)* 建议创建一个简单的命令行工具 (`cmd/license-generator`) 来封装步骤 1-4，方便快速生成授权文件。

## 5. 授权认证步骤 (客户端应用程序)

1.  **加载公钥**: 在应用程序初始化时，从嵌入的常量或安全位置加载 `public_key.pem` 的内容（RSA 公钥）。
2.  **读取授权文件**: 找到用户提供的授权文件（例如 `customer.lic`），读取其内容。
3.  **调用验证器**: 调用 `pkg/license/verifier.go` 中定义的验证函数（例如 `VerifyLicense` 或 `LoadAndVerifyLicense`），传入授权文件内容和加载的公钥。
4.  **处理结果**:
    *   **验证成功**: 函数返回解析后的 `license.LicenseInfo` 结构体实例。应用程序可以缓存此实例，并在需要时检查其字段（如 `IsExpired()`, `HasFeature("feature_x")`）来控制程序行为。
    *   **验证失败**: 函数返回错误。应用程序应据此作出反应，例如：
        *   记录详细错误日志。
        *   提示用户授权无效、过期或已损坏。
        *   限制程序功能或直接退出。
5.  **运行时检查**: 在程序的关键功能点或通过中间件，根据缓存的 `LicenseInfo` 进行检查（例如，检查功能是否启用、用户数是否超限、是否过期等）。

## 6. 注意事项

*   **私钥安全**: 私钥的安全性是整个机制的基石。一旦泄露，任何人都可以伪造有效的授权文件。
*   **公钥分发**: 确保证客户端应用程序使用的是正确的、未被篡改的公钥。嵌入代码是相对安全的方式。
*   **错误处理**: 客户端需要对各种验证失败（签名无效、文件损坏、IO 错误、过期、功能缺失等）进行健壮的处理和用户提示。
*   **硬件绑定 (可选)**: 如果需要将授权绑定到特定硬件，需要在 `LicenseInfo` 中添加硬件 ID 字段，并在 `verifier.go` 中添加获取当前硬件 ID 并进行比对的逻辑（这部分逻辑通常是平台相关的）。
*   **时间同步**: 依赖时间的验证（如 `ExpiryDate`）需要客户机器的时间相对准确。可以考虑加入一定的容错或提示。

## 7. 测试步骤

以下步骤用于验证授权生成和认证功能的正确性：

**前提:**

*   确保 Go 环境已正确安装配置。
*   确保项目依赖已通过 `go mod tidy` 或类似命令同步。
*   确保项目根目录下没有旧的 `private_key.pem` 和 `public_key.pem` 文件 (如果希望生成新的)。

**步骤:**

1.  **编译生成器工具**:
    *   打开终端，切换到项目根目录 (例如 `D:/authsoft/backend/`)。
    *   运行: `go build ./cmd/license-generator`
    *   确认根目录下生成了 `license-generator.exe` (或对应操作系统的可执行文件)。

2.  **生成密钥对** (如果还没有):
    *   运行: `./license-generator.exe -genkeys`
    *   确认根目录下生成了 `private_key.pem` 和 `public_key.pem`。
    *   **重要**: 打开 `public_key.pem`，复制其**全部内容**。

3.  **嵌入公钥到代码**: 
    *   打开项目中的 `internal/middleware/license.go` 文件。
    *   找到 `const embeddedPublicKeyPEM = \`...\`` 这一行。
    *   将上一步复制的公钥内容粘贴到反引号 (`) 之间，**完全替换**掉原来的占位符或旧公钥。
    *   保存文件。

4.  **创建授权文件存放目录**: 
    *   在项目根目录下，**手动创建**一个名为 `license` 的文件夹。

5.  **生成一个有效的授权文件**: 
    *   运行生成器，指定客户名、有效期和所需功能。例如：
      ```bash
      ./license-generator.exe -customer=\"Test Company\" -expiry=\"2025-12-31\" -features=\"sys,wms,hr\" -users=5000 -outdir=\"./license\"
      ```
    *   确认 `./license` 目录下生成了名为 `Test_Company_license.lic` 的文件。
    *   检查文件内容，应为包含 `data` 和 `signature` 字段的 JSON 格式。

6.  **配置主应用使用的授权文件**: 
    *   打开 `configs/config.yaml` 文件。
    *   找到 `app.licensePath` 配置项。
    *   将其值修改为您在上一步生成的**具体文件名**，例如： `licensePath: \"./license/Test_Company_license.lic\"`。
    *   保存文件。

7.  **运行主应用程序**: 
    *   在终端中 (仍在项目根目录)，运行: `go run ./cmd/main.go` (或运行编译后的主程序 `main.exe`)

8.  **检查启动日志**: 
    *   仔细观察应用启动时的日志输出。
    *   应看到类似 `正在加载和验证授权文件: ./license/Test_Company_license.lic` 和 `授权文件验证成功` 的日志。
    *   确认日志中打印的客户名、有效期等信息与您生成时指定的一致。
    *   **如果没有看到成功信息，或者看到错误/警告**，请根据日志内容排查问题（公钥是否正确嵌入？文件路径是否正确？文件是否损坏？）。

9.  **测试 API 访问 (需要先登录获取有效 Token)**:
    *   使用 Postman 或类似工具，携带有效的 JWT Token 访问一个需要授权的 API 端点 (例如 `/api/v1/sys/users`)。
    *   预期结果: API 应正常返回数据 (状态码 200 OK)。

10. **测试无效授权 - 篡改文件**: 
    *   停止主应用程序。
    *   **手动编辑** `./license/Test_Company_license.lic` 文件，随意修改 `data` 或 `signature` 字段中的一两个字符。
    *   重新启动主应用程序 (`go run ./cmd/main.go`)。
    *   观察启动日志：应看到授权验证失败的错误，例如包含 "授权签名无效" 或 "signature verification failed" 的信息。
    *   尝试访问需要授权的 API：预期结果为 HTTP 403 Forbidden 错误。

11. **测试无效授权 - 过期**: 
    *   停止主应用程序。
    *   使用生成器生成一个**已过期**的授权文件：
      ```bash
      ./license-generator.exe -customer=\"Expired License\" -expiry=\"2020-01-01\" -outdir=\"./license\"
      ```
    *   修改 `configs/config.yaml` 中的 `app.licensePath` 指向这个新的过期文件 (`./license/Expired_License_license.lic`)。
    *   重新启动主应用程序。
    *   观察启动日志：应看到类似 `警告: 授权已过期!` 的信息。
    *   尝试访问需要授权的 API：预期结果为 HTTP 403 Forbidden 错误，提示授权已过期。

12. **测试无效授权 - 文件不存在**: 
    *   停止主应用程序。
    *   删除或重命名 `configs/config.yaml` 中 `app.licensePath` 指向的授权文件。
    *   重新启动主应用程序。
    *   观察启动日志：应看到类似 `授权文件不存在` 的错误。
    *   尝试访问需要授权的 API：预期结果为 HTTP 403 Forbidden 错误。

13. **(可选) 测试特定功能授权**: 
    *   如果您的某些路由配置了需要特定功能的 `LicenseCheckMiddleware` (例如 `middleware.LicenseCheckMiddleware(\"reporting\")`)：
        *   生成一个**不包含**该功能的授权文件。
        *   配置 `config.yaml` 指向该文件并重启应用。
        *   尝试访问需要该功能的 API：预期结果为 HTTP 403 Forbidden，提示缺少功能授权。
        *   生成一个**包含**该功能的授权文件，配置并重启。
        *   再次尝试访问该 API：预期结果为正常访问 (200 OK)。

完成以上测试步骤，可以较为全面地验证授权机制的有效性。 