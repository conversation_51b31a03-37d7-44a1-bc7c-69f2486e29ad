import { ref, reactive } from 'vue';
import { getDictDataByCode as apiGetDictDataByCode } from '@/api/system/systemDict';
import type { DictDataVO } from '@/api/system/systemDict';
import { ElMessage } from 'element-plus';

// 字典数据项类型（与API兼容）
export interface DictItem {
  label: string;
  value: string;
}

// 字典数据管理Hook
export function useDictionary() {
  // 字典数据缓存
  const dictCache = reactive<Record<string, DictItem[]>>({});
  
  // 加载状态
  const loading = ref(false);

  // 加载单个字典数据
  const loadDictData = async (dictCode: string): Promise<DictItem[]> => {
    // 如果缓存中已存在，直接返回
    if (dictCache[dictCode]) {
      return dictCache[dictCode];
    }

    try {
      const data = await apiGetDictDataByCode(dictCode);
      // 转换API数据为内部格式（实际上格式是兼容的）
      const dictItems: DictItem[] = data.map((item: DictDataVO) => ({
        label: item.label,
        value: item.value
      }));
      dictCache[dictCode] = dictItems;
      return dictItems;
    } catch (error) {
      console.error(`加载字典数据失败 [${dictCode}]:`, error);
      ElMessage.error(`加载字典数据失败: ${dictCode}`);
      return [];
    }
  };

  // 批量加载字典数据
  const loadMultipleDictData = async (dictCodes: string[]): Promise<Record<string, DictItem[]>> => {
    loading.value = true;
    
    try {
      // 过滤出未缓存的字典代码
      const uncachedCodes = dictCodes.filter(code => !dictCache[code]);
      
      if (uncachedCodes.length > 0) {
        // 并行加载未缓存的字典数据
        const promises = uncachedCodes.map(code => apiGetDictDataByCode(code));
        const results = await Promise.all(promises);
        
        // 更新缓存（转换数据格式）
        uncachedCodes.forEach((code, index) => {
          const resultData = results[index];
          if (resultData) {
            const dictItems: DictItem[] = resultData.map((item: DictDataVO) => ({
              label: item.label,
              value: item.value
            }));
            dictCache[code] = dictItems;
          }
        });
      }

      // 返回所有请求的字典数据
      const result: Record<string, DictItem[]> = {};
      dictCodes.forEach(code => {
        result[code] = dictCache[code] || [];
      });
      
      return result;
    } catch (error) {
      console.error('批量加载字典数据失败:', error);
      ElMessage.error('加载字典数据失败，部分功能可能受影响');
      
      // 返回空数据
      const result: Record<string, DictItem[]> = {};
      dictCodes.forEach(code => {
        result[code] = dictCache[code] || [];
      });
      return result;
    } finally {
      loading.value = false;
    }
  };

  // 获取字典标签
  const getDictLabel = (dictCode: string, value: string): string => {
    const items = dictCache[dictCode] || [];
    const item = items.find(item => item.value === value);
    return item ? item.label : value;
  };

  // 获取字典数据（同步方法，用于模板中）
  const getDictItems = (dictCode: string): DictItem[] => {
    return dictCache[dictCode] || [];
  };

  // 同步获取字典数据的别名（用于兼容性）
  const getDictDataByCodeSync = (dictCode: string): DictItem[] => {
    return dictCache[dictCode] || [];
  };

  // 清除缓存
  const clearCache = (dictCode?: string) => {
    if (dictCode) {
      delete dictCache[dictCode];
    } else {
      Object.keys(dictCache).forEach(key => {
        delete dictCache[key];
      });
    }
  };

  // 预定义的CRM/SCM/WMS字典代码
  const BUSINESS_DICT_CODES = {
    // 客户相关
    CUSTOMER_TYPE: 'customer_type',
    CUSTOMER_LEVEL: 'customer_level', 
    CUSTOMER_STATUS: 'customer_status',
    CUSTOMER_SOURCE: 'customer_source',
    
    // 供应商相关
    SUPPLIER_TYPE: 'supplier_type',
    SUPPLIER_LEVEL: 'supplier_level',
    SUPPLIER_STATUS: 'supplier_status',
    SUPPLIER_CATEGORY: 'supplier_category',
    
    // WMS库位相关
    TEMPERATURE_ZONE: 'temperature_zone',
    DANGER_LEVEL: 'danger_level',
    SAFETY_LEVEL: 'safety_level',
    LOAD_CAPACITY_LEVEL: 'load_capacity_level',
    STORAGE_TYPE: 'storage_type',
    
    // 通用字典
    CREDIT_RATING: 'credit_rating',
    PAYMENT_TERMS: 'payment_terms',
    INDUSTRY: 'industry',
    CURRENCY_CODE: 'currency_code',
    CONTACT_ROLE: 'contact_role',
    GENDER: 'gender',
  } as const;

  // 加载CRM相关字典数据
  const loadCrmDictData = async () => {
    const dictCodes = [
      BUSINESS_DICT_CODES.CUSTOMER_TYPE,
      BUSINESS_DICT_CODES.CUSTOMER_LEVEL,
      BUSINESS_DICT_CODES.CUSTOMER_STATUS,
      BUSINESS_DICT_CODES.CUSTOMER_SOURCE,
      BUSINESS_DICT_CODES.CREDIT_RATING,
      BUSINESS_DICT_CODES.PAYMENT_TERMS,
      BUSINESS_DICT_CODES.INDUSTRY,
      BUSINESS_DICT_CODES.CURRENCY_CODE,
      BUSINESS_DICT_CODES.CONTACT_ROLE,
      BUSINESS_DICT_CODES.GENDER,
    ];
    
    return await loadMultipleDictData(dictCodes);
  };

  // 加载SCM相关字典数据
  const loadScmDictData = async () => {
    const dictCodes = [
      BUSINESS_DICT_CODES.SUPPLIER_TYPE,
      BUSINESS_DICT_CODES.SUPPLIER_LEVEL,
      BUSINESS_DICT_CODES.SUPPLIER_STATUS,
      BUSINESS_DICT_CODES.SUPPLIER_CATEGORY,
      BUSINESS_DICT_CODES.CREDIT_RATING,
      BUSINESS_DICT_CODES.PAYMENT_TERMS,
      BUSINESS_DICT_CODES.INDUSTRY,
      BUSINESS_DICT_CODES.CURRENCY_CODE,
      BUSINESS_DICT_CODES.CONTACT_ROLE,
      BUSINESS_DICT_CODES.GENDER,
    ];
    
    return await loadMultipleDictData(dictCodes);
  };

  // 加载WMS库位相关字典数据
  const loadWmsDictData = async () => {
    const dictCodes = [
      BUSINESS_DICT_CODES.TEMPERATURE_ZONE,
      BUSINESS_DICT_CODES.DANGER_LEVEL,
      BUSINESS_DICT_CODES.SAFETY_LEVEL,
      BUSINESS_DICT_CODES.LOAD_CAPACITY_LEVEL,
      BUSINESS_DICT_CODES.STORAGE_TYPE,
    ];
    
    return await loadMultipleDictData(dictCodes);
  };

  return {
    // 状态
    loading,
    dictCache,
    
    // 方法
    loadDictData,
    loadMultipleDictData,
    getDictLabel,
    getDictItems,
    getDictDataByCode: getDictDataByCodeSync, // 同步获取字典数据的别名
    clearCache,
    
    // 预定义方法
    loadCrmDictData,
    loadScmDictData,
    loadWmsDictData,
    
    // 常量
    BUSINESS_DICT_CODES,
  };
}

// 创建全局字典管理实例
export const globalDictionary = useDictionary(); 