import axios, { type AxiosInstance, type InternalAxiosRequestConfig, type AxiosResponse } from 'axios';
// import { ElMessage, ElMessageBox } from 'element-plus'; // Import MessageBox if needed for logout confirmation
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStoreHook } from '@/store/modules/user'; // Use the named export hook
import router from '@/router';
// import { getToken, removeToken } from './auth';
import { getToken } from './auth';

// Define the wrapper structure used by the backend
export interface BackendResponse<T = any> { // Generic for the actual data type
    success: boolean;
    code: number;
    message: string;
    data: T;
}

// 1. 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env['VITE_APP_BASE_API'] || '/api/v1', // 使用环境变量 VITE_APP_BASE_API 或默认值 (修正 linter 错误)
  timeout: 10000, // 请求超时时间 (毫秒)
  headers: { 'Content-Type': 'application/json;charset=utf-8' }
});

// 2. 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const userStore = useUserStoreHook();
    const accountBookId = userStore.currentAccountBookId ?? 0;
    config.headers['X-AccountBook-ID'] = String(accountBookId);
    const token = getToken();
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    // 可以在这里添加其他请求头或逻辑，如 tenant-id 等
    // config.headers['Tenant-Id'] = 'your-tenant-id';
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    console.error('Request Error Interceptor:', error);
    return Promise.reject(error);
  }
);

// 3. 响应拦截器
service.interceptors.response.use(
  // Explicitly type the response using the BackendResponse wrapper
  // and define the successful return type as the extracted data (T)
  <T = any>(response: AxiosResponse<BackendResponse<T>>): T => {
    const res = response.data; // res is now BackendResponse<T>
    const status = response.status;

    // Check HTTP status first (although 2xx is expected here)
    if (status === 200) {
      // Check business logic status
      if (res.success === false || res.code !== 0) {
        // 【注释掉】原因：防止在业务逻辑失败时，全局拦截器弹出重复的 ElMessage。
        // 视图组件将捕获抛出的错误，并使用更详细的 getApiErrorMessage 函数来显示错误信息。
        // 如果将来希望恢复全局的简单业务错误提示，可以取消下面的注释。
        /*
        ElMessage({
          message: res.message || '业务错误',
          type: 'error',
          duration: 5 * 1000
        });
        */

        // Handle specific error codes like token expiration
        if (res.code === 40101) { 
          handleTokenExpired();
        }

        // Throw an error to be caught by the calling async/await block
        // 将 res (BackendResponse<T>) 整体作为 error 对象抛出，或者至少包含 message 和 details
        // 这样视图组件的 getApiErrorMessage 可以更好地解析它。
        // 注意：直接 throw new Error(res.message) 会丢失 details 等其他有用信息。
        // 创建一个包含更多信息的错误对象
        const businessError = new Error(res.message || 'Business Error');
        (businessError as any).response = { data: res }; // 将后端返回的完整 res 附加到 error 对象上
        throw businessError;
      } else {
        // Business success, return the extracted data directly
        const dataToReturn = res.data; // This is of type T
        console.log('[request.ts interceptor] Business success. Extracted res.data (type T) to be returned:', JSON.stringify(dataToReturn));
        return dataToReturn; 
      }
    } else {
      // Handle unexpected non-200 HTTP status within the success handler
      const httpError = new Error(`HTTP Error: ${status}`);
      // 【注释掉】原因：防止在非预期的HTTP状态码（但在成功回调中处理）时，全局拦截器弹出重复的 ElMessage。
      // 视图组件将捕获抛出的错误进行处理。
      // 如果将来希望恢复此处的提示，可以取消下面的注释。
      /*
      ElMessage({ message: httpError.message, type: 'error', duration: 5 * 1000 });
      */
      throw httpError; 
    }
  },
  (error) => {
    // Handle network errors or non-2xx HTTP status codes
    console.error('Response Error Interceptor:', error); 
    let message = error.message;

    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 401:
          message = '未授权或登录已过期，请重新登录';
          handleTokenExpired(); 
          break;
        case 403:
          message = '禁止访问，权限不足';
          break;
        case 404:
          message = '请求资源未找到';
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          message = '服务器错误，请稍后重试';
          break;
        default:
          const backendMessage = error.response.data?.message;
          message = backendMessage ? `请求错误: ${backendMessage} (${status})` : `请求错误 (${status})`;
      }
    } else if (error.code === 'ECONNABORTED' || message.includes('timeout')) {
        message = '请求超时，请检查网络或稍后重试';
    } else if (message.includes('Network Error')){
        message = '网络连接错误，请检查网络连接';
    } else {
        message = error.message || '请求失败，请稍后重试'; 
    }

    // 【注释掉】原因：防止在网络错误或非2xx HTTP状态码时，全局拦截器弹出重复的 ElMessage。
    // 视图组件将捕获抛出的错误，并使用更详细的 getApiErrorMessage 函数来显示错误信息。
    // 如果将来希望恢复全局的通用错误提示（例如，对于非业务逻辑错误），可以取消下面的注释。
    // 但要注意，如果取消注释，某些特定状态码（如401跳转登录）可能仍需特殊处理，避免重复弹窗。
    /*
    ElMessage({
      message: message, // 这里使用的是上面逻辑组装好的通用 message
      type: 'error',
      duration: 5 * 1000,
    });
    */

    // 为了让视图组件的 getApiErrorMessage 能获取到 error.response.data.message 和 error.response.data.details
    // 确保原始的 error 对象被 reject 出去。
    // 如果 error.response 不存在（例如网络错误），error 对象本身就包含 message。
    return Promise.reject(error); 
  }
);

// 处理 Token 过期或失效的辅助函数
let isHandlingTokenExpired = false;
const handleTokenExpired = () => {
    if (isHandlingTokenExpired) return;
    isHandlingTokenExpired = true;

    ElMessage({
        message: '登录状态已过期，将自动跳转到登录页...',
        type: 'warning',
        duration: 2 * 1000,
    });

    // 移除 ElMessageBox.confirm，直接执行登出操作
    setTimeout(() => {
        try {
            useUserStoreHook().Logout();
            router.push('/login');
        } catch (e) {
            console.error('执行 Logout 或 router.push 时出错:', e);
        } finally {
            isHandlingTokenExpired = false;
        }
    }, 2000); // 延迟2秒以确保用户能看到提示信息
}

// 4. 导出 axios 实例
export default service;
