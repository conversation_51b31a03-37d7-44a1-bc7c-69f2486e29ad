<template>
  <div class="dialog-examples-page">
    <h2>VNDialog 对话框组件示例</h2>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础用法</span>
        </div>
      </template>
      <el-button @click="basicVisible = true">打开基础对话框</el-button>
      <VNDialog 
        v-model="basicVisible"
        title="提示"
        width="30%"
        @confirm="handleBasicConfirm"
        @cancel="handleBasicCancel"
        @open="logEvent('open')"
        @opened="logEvent('opened')"
        @close="logEvent('close')"
        @closed="logEvent('closed')"
      >
        <span>这是一段信息</span>
      </VNDialog>
      <p>事件日志: {{ eventLog }}</p>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>自定义内容与底部</span>
        </div>
      </template>
      <el-button @click="customVisible = true">打开自定义对话框</el-button>
      <VNDialog 
        v-model="customVisible"
        title="用户信息"
        :show-confirm-button="false"
        :show-cancel-button="false"
      >
        <el-form label-width="80px">
          <el-form-item label="用户名">
            <el-input v-model="formData.username" />
          </el-form-item>
          <el-form-item label="邮箱">
             <el-input v-model="formData.email" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="customVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleCustomSubmit">提交</el-button>
        </template>
      </VNDialog>
    </el-card>

     <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>异步确认按钮</span>
        </div>
      </template>
      <el-button @click="asyncVisible = true">打开带异步确认的对话框</el-button>
      <VNDialog 
        v-model="asyncVisible"
        title="确认操作"
        :confirm-button-loading="confirmLoading"
        @confirm="handleAsyncConfirm"
      >
        <span>点击确认后会模拟一个异步操作。</span>
      </VNDialog>
    </el-card>

     <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>嵌套对话框</span>
        </div>
      </template>
      <el-button @click="outerVisible = true">打开外层对话框</el-button>
      <VNDialog v-model="outerVisible" title="外层对话框" width="40%">
        <p>外层对话框内容。</p>
        <el-button @click="innerVisible = true">打开内层对话框</el-button>
        
        <VNDialog 
          v-model="innerVisible" 
          title="内层对话框" 
          width="25%" 
          append-to-body 
          @confirm="() => { innerVisible = false; ElMessage.info('内层确认'); }"
        >
           <span>这是内层对话框。使用了 `append-to-body`。</span>
        </VNDialog>
      </VNDialog>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNDialog from './index.vue';
import { ElButton, ElCard, ElMessage, ElForm, ElFormItem, ElInput } from 'element-plus';

const basicVisible = ref(false);
const customVisible = ref(false);
const asyncVisible = ref(false);
const outerVisible = ref(false);
const innerVisible = ref(false);

const confirmLoading = ref(false);
const eventLog = ref<string[]>([]);

const formData = ref({
  username: '',
  email: ''
});

const logEvent = (eventName: string) => {
  const time = new Date().toLocaleTimeString();
  eventLog.value.push(`${time}: ${eventName} 事件触发`);
  // Keep log size manageable
  if (eventLog.value.length > 5) {
    eventLog.value.shift();
  }
};

const handleBasicConfirm = () => {
  logEvent('confirm');
  ElMessage.success('基础对话框确认');
  basicVisible.value = false; // 需要手动关闭
};

const handleBasicCancel = () => {
  logEvent('cancel');
  ElMessage.info('基础对话框取消');
  // 对话框会自动关闭
};

const handleCustomSubmit = () => {
  ElMessage.success(`提交用户信息: ${JSON.stringify(formData.value)}`);
  customVisible.value = false;
};

const handleAsyncConfirm = async () => {
  logEvent('confirm (async)');
  confirmLoading.value = true;
  try {
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 1500));
    ElMessage.success('异步操作完成');
    asyncVisible.value = false; // 操作成功后关闭
  } catch (error) {
    ElMessage.error('异步操作失败');
  } finally {
    confirmLoading.value = false;
  }
};

</script>

<style scoped>
.dialog-examples-page {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

p {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 10px;
  max-height: 100px;
  overflow-y: auto;
}
</style> 