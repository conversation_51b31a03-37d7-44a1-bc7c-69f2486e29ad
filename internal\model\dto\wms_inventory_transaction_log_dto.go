package dto

import (
	"backend/internal/model/entity"
	"backend/pkg/response"
	"time"
)

// WmsInventoryTransactionLogCreateReq 库存事务日志创建请求
type WmsInventoryTransactionLogCreateReq struct {
	TransactionType    entity.TransactionType `json:"transactionType" binding:"required" comment:"事务类型"`
	ItemID             uint                   `json:"itemId" binding:"required" comment:"物料ID"`
	WarehouseID        uint                   `json:"warehouseId" binding:"required" comment:"仓库ID"`
	LocationID         uint                   `json:"locationId" binding:"required" comment:"库位ID"`
	BatchNo            *string                `json:"batchNo" comment:"批次号"`
	QuantityChange     float64                `json:"quantityChange" binding:"required" comment:"数量变化"`
	UnitOfMeasure      string                 `json:"unitOfMeasure" binding:"required" comment:"计量单位"`
	BalanceBefore      float64                `json:"balanceBefore" binding:"required" comment:"事务前数量"`
	BalanceAfter       float64                `json:"balanceAfter" binding:"required" comment:"事务后数量"`
	ReferenceDocType   *string                `json:"referenceDocType" comment:"关联单据类型"`
	ReferenceDocID     *uint                  `json:"referenceDocId" comment:"关联单据ID"`
	ReferenceDocLineID *uint                  `json:"referenceDocLineId" comment:"关联单据行ID"`
	OperatorID         *uint                  `json:"operatorId" comment:"操作员ID"`
	Remark             *string                `json:"remark" comment:"备注"`
}

// WmsInventoryTransactionLogUpdateReq 库存事务日志更新请求
type WmsInventoryTransactionLogUpdateReq struct {
	Remark *string `json:"remark" comment:"备注"`
}

// WmsInventoryTransactionLogQueryReq 库存事务日志查询请求
type WmsInventoryTransactionLogQueryReq struct {
	response.PageQuery
	TransactionType    *entity.TransactionType `json:"transactionType" comment:"事务类型"`
	ItemID             *uint                   `json:"itemId" comment:"物料ID"`
	WarehouseID        *uint                   `json:"warehouseId" comment:"仓库ID"`
	LocationID         *uint                   `json:"locationId" comment:"库位ID"`
	BatchNo            *string                 `json:"batchNo" comment:"批次号"`
	ReferenceDocType   *string                 `json:"referenceDocType" comment:"关联单据类型"`
	ReferenceDocID     *uint                   `json:"referenceDocId" comment:"关联单据ID"`
	OperatorID         *uint                   `json:"operatorId" comment:"操作员ID"`
	TransactionStart   *time.Time              `json:"transactionStart" comment:"事务开始时间"`
	TransactionEnd     *time.Time              `json:"transactionEnd" comment:"事务结束时间"`
	CreatedStart       *time.Time              `json:"createdStart" comment:"创建开始时间"`
	CreatedEnd         *time.Time              `json:"createdEnd" comment:"创建结束时间"`
}

// WmsInventoryTransactionLogStatsReq 库存事务日志统计请求
type WmsInventoryTransactionLogStatsReq struct {
	TransactionType *entity.TransactionType `json:"transactionType" comment:"事务类型"`
	OperatorID      *uint                   `json:"operatorId" comment:"操作员ID"`
	StartDate       string                  `json:"startDate" binding:"required" comment:"开始日期"`
	EndDate         string                  `json:"endDate" binding:"required" comment:"结束日期"`
}

// WmsInventoryTransactionLogCleanupReq 库存事务日志清理请求
type WmsInventoryTransactionLogCleanupReq struct {
	BeforeDate string `json:"beforeDate" binding:"required" comment:"清理此日期之前的日志"`
}
