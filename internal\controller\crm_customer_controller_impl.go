package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"

	"github.com/kataras/iris/v12"
)

// CrmCustomerController CRM客户控制器接口
type CrmCustomerController interface {
	// 客户管理
	Post(ctx iris.Context)       // 创建客户
	PutBy(ctx iris.Context)      // 更新客户
	DeleteBy(ctx iris.Context)   // 删除客户
	GetBy(ctx iris.Context)      // 获取客户详情
	Get(ctx iris.Context)        // 分页查询客户
	GetSummary(ctx iris.Context) // 获取客户统计摘要

	// 客户联系人管理
	GetContacts(ctx iris.Context)       // 获取客户联系人列表
	PostContact(ctx iris.Context)       // 创建客户联系人
	PutContact(ctx iris.Context)        // 更新客户联系人
	DeleteContact(ctx iris.Context)     // 删除客户联系人
	SetPrimaryContact(ctx iris.Context) // 设置主要联系人
	GetPrimaryContact(ctx iris.Context) // 获取主要联系人

	// 业务功能
	ValidateCustomerCode(ctx iris.Context)    // 验证客户编码
	ValidateBusinessLicense(ctx iris.Context) // 验证营业执照号
	ValidateTaxNumber(ctx iris.Context)       // 验证税务登记号
	GetByCustomerCode(ctx iris.Context)       // 根据客户编码获取客户
	GetSimple(ctx iris.Context)               // 获取客户简单列表
}

// crmCustomerControllerImpl CRM客户控制器实现
type crmCustomerControllerImpl struct {
	BaseControllerImpl
	service service.CrmCustomerService
}

// NewCrmCustomerController 创建CRM客户控制器
func NewCrmCustomerController(cm *ControllerManager) CrmCustomerController {
	svc := cm.GetServiceManager().GetCrmCustomerService()
	return &crmCustomerControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		service:            svc,
	}
}

// Post 创建客户
func (c *crmCustomerControllerImpl) Post(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	var req dto.CrmCustomerCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "CreateCrmCustomer")
		return
	}

	result, err := c.service.Create(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "CreateCrmCustomer")
		return
	}

	c.SuccessWithMessage(ctx, "客户创建成功", result)
}

// PutBy 更新客户
func (c *crmCustomerControllerImpl) PutBy(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的客户ID: "+err.Error()), "UpdateCrmCustomer")
		return
	}

	var req dto.CrmCustomerUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "UpdateCrmCustomer")
		return
	}

	result, err := c.service.Update(ctx.Request().Context(), id, &req)
	if err != nil {
		c.HandleError(ctx, err, "UpdateCrmCustomer")
		return
	}

	c.SuccessWithMessage(ctx, "客户更新成功", result)
}

// DeleteBy 删除客户
func (c *crmCustomerControllerImpl) DeleteBy(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的客户ID: "+err.Error()), "DeleteCrmCustomer")
		return
	}

	if err := c.service.Delete(ctx.Request().Context(), id); err != nil {
		c.HandleError(ctx, err, "DeleteCrmCustomer")
		return
	}

	c.SuccessWithMessage(ctx, "客户删除成功", nil)
}

// GetBy 获取客户详情
func (c *crmCustomerControllerImpl) GetBy(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的客户ID: "+err.Error()), "GetCrmCustomer")
		return
	}

	result, err := c.service.GetByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, "GetCrmCustomer")
		return
	}

	c.Success(ctx, result)
}

// Get 分页查询客户
func (c *crmCustomerControllerImpl) Get(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	var req dto.CrmCustomerQueryReq

	// 使用现有分页机制构建查询参数
	pageQuery := response.BuildPageQuery(ctx)
	req.PageQuery = *pageQuery

	// 读取其他查询参数
	if err := ctx.ReadQuery(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数: "+err.Error()), "GetCrmCustomerPage")
		return
	}

	pageResult, err := c.service.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "GetCrmCustomerPage")
		return
	}

	c.Success(ctx, pageResult)
}

// GetSummary 获取客户统计摘要
func (c *crmCustomerControllerImpl) GetSummary(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	result, err := c.service.GetSummary(ctx.Request().Context())
	if err != nil {
		c.HandleError(ctx, err, "GetCrmCustomerSummary")
		return
	}

	c.Success(ctx, result)
}

// GetContacts 获取客户联系人列表
func (c *crmCustomerControllerImpl) GetContacts(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer_contact")

	customerID, err := ctx.Params().GetUint("customerId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的客户ID: "+err.Error()), "GetCrmCustomerContacts")
		return
	}

	result, err := c.service.GetContacts(ctx.Request().Context(), customerID)
	if err != nil {
		c.HandleError(ctx, err, "GetCrmCustomerContacts")
		return
	}

	c.Success(ctx, result)
}

// PostContact 创建客户联系人
func (c *crmCustomerControllerImpl) PostContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer_contact")

	var req dto.CrmCustomerContactCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "CreateCrmCustomerContact")
		return
	}

	result, err := c.service.CreateContact(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "CreateCrmCustomerContact")
		return
	}

	c.SuccessWithMessage(ctx, "联系人创建成功", result)
}

// PutContact 更新客户联系人
func (c *crmCustomerControllerImpl) PutContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer_contact")

	id, err := ctx.Params().GetUint("contactId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的联系人ID: "+err.Error()), "UpdateCrmCustomerContact")
		return
	}

	var req dto.CrmCustomerContactUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "UpdateCrmCustomerContact")
		return
	}

	result, err := c.service.UpdateContact(ctx.Request().Context(), id, &req)
	if err != nil {
		c.HandleError(ctx, err, "UpdateCrmCustomerContact")
		return
	}

	c.SuccessWithMessage(ctx, "联系人更新成功", result)
}

// DeleteContact 删除客户联系人
func (c *crmCustomerControllerImpl) DeleteContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer_contact")

	id, err := ctx.Params().GetUint("contactId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的联系人ID: "+err.Error()), "DeleteCrmCustomerContact")
		return
	}

	if err := c.service.DeleteContact(ctx.Request().Context(), id); err != nil {
		c.HandleError(ctx, err, "DeleteCrmCustomerContact")
		return
	}

	c.SuccessWithMessage(ctx, "联系人删除成功", nil)
}

// SetPrimaryContact 设置主要联系人
func (c *crmCustomerControllerImpl) SetPrimaryContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer_contact")

	customerID, err := ctx.Params().GetUint("customerId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的客户ID: "+err.Error()), "SetPrimaryCrmCustomerContact")
		return
	}

	contactID, err := ctx.Params().GetUint("contactId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的联系人ID: "+err.Error()), "SetPrimaryCrmCustomerContact")
		return
	}

	if err := c.service.SetPrimaryContact(ctx.Request().Context(), customerID, contactID); err != nil {
		c.HandleError(ctx, err, "SetPrimaryCrmCustomerContact")
		return
	}

	c.SuccessWithMessage(ctx, "主要联系人设置成功", nil)
}

// GetPrimaryContact 获取主要联系人
func (c *crmCustomerControllerImpl) GetPrimaryContact(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer_contact")

	customerID, err := ctx.Params().GetUint("customerId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的客户ID: "+err.Error()), "GetPrimaryCrmCustomerContact")
		return
	}

	result, err := c.service.GetPrimaryContact(ctx.Request().Context(), customerID)
	if err != nil {
		c.HandleError(ctx, err, "GetPrimaryCrmCustomerContact")
		return
	}

	c.Success(ctx, result)
}

// ValidateCustomerCode 验证客户编码
func (c *crmCustomerControllerImpl) ValidateCustomerCode(ctx iris.Context) {
	customerCode := ctx.URLParam("customerCode")
	if customerCode == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户编码不能为空"), "ValidateCustomerCode")
		return
	}

	excludeIDStr := ctx.URLParam("excludeId")
	var excludeID uint
	if excludeIDStr != "" {
		id := ctx.URLParamUint64("excludeId")
		excludeID = uint(id)
	}

	var err error
	if excludeID > 0 {
		err = c.service.ValidateCustomerCode(ctx.Request().Context(), customerCode, excludeID)
	} else {
		err = c.service.ValidateCustomerCode(ctx.Request().Context(), customerCode)
	}

	if err != nil {
		c.HandleError(ctx, err, "ValidateCustomerCode")
		return
	}

	c.SuccessWithMessage(ctx, "客户编码可用", map[string]interface{}{"valid": true})
}

// ValidateBusinessLicense 验证营业执照号
func (c *crmCustomerControllerImpl) ValidateBusinessLicense(ctx iris.Context) {
	businessLicense := ctx.URLParam("businessLicense")
	if businessLicense == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "营业执照号不能为空"), "ValidateBusinessLicense")
		return
	}

	excludeIDStr := ctx.URLParam("excludeId")
	var excludeID uint
	if excludeIDStr != "" {
		id := ctx.URLParamUint64("excludeId")
		excludeID = uint(id)
	}

	var err error
	if excludeID > 0 {
		err = c.service.ValidateBusinessLicense(ctx.Request().Context(), businessLicense, excludeID)
	} else {
		err = c.service.ValidateBusinessLicense(ctx.Request().Context(), businessLicense)
	}

	if err != nil {
		c.HandleError(ctx, err, "ValidateBusinessLicense")
		return
	}

	c.SuccessWithMessage(ctx, "营业执照号可用", map[string]interface{}{"valid": true})
}

// ValidateTaxNumber 验证税务登记号
func (c *crmCustomerControllerImpl) ValidateTaxNumber(ctx iris.Context) {
	taxNumber := ctx.URLParam("taxNumber")
	if taxNumber == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "税务登记号不能为空"), "ValidateTaxNumber")
		return
	}

	excludeIDStr := ctx.URLParam("excludeId")
	var excludeID uint
	if excludeIDStr != "" {
		id := ctx.URLParamUint64("excludeId")
		excludeID = uint(id)
	}

	var err error
	if excludeID > 0 {
		err = c.service.ValidateTaxNumber(ctx.Request().Context(), taxNumber, excludeID)
	} else {
		err = c.service.ValidateTaxNumber(ctx.Request().Context(), taxNumber)
	}

	if err != nil {
		c.HandleError(ctx, err, "ValidateTaxNumber")
		return
	}

	c.SuccessWithMessage(ctx, "税务登记号可用", map[string]interface{}{"valid": true})
}

// GetByCustomerCode 根据客户编码获取客户
func (c *crmCustomerControllerImpl) GetByCustomerCode(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	customerCode := ctx.Params().Get("customerCode")
	if customerCode == "" {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "客户编码不能为空"), "GetCrmCustomerByCode")
		return
	}

	result, err := c.service.GetByCustomerCode(ctx.Request().Context(), customerCode)
	if err != nil {
		c.HandleError(ctx, err, "GetCrmCustomerByCode")
		return
	}

	c.Success(ctx, result)
}

// GetSimple 获取客户简单列表
func (c *crmCustomerControllerImpl) GetSimple(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "crm_customer")

	result, err := c.service.GetSimpleList(ctx.Request().Context())
	if err != nil {
		c.HandleError(ctx, err, "GetCrmCustomerSimpleList")
		return
	}

	c.Success(ctx, result)
}
