package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsInventoryRepository 库存数据访问接口
type WmsInventoryRepository interface {
	BaseRepository[entity.WmsInventory, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInventoryQueryReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByLocationID(ctx context.Context, locationId uint) ([]*entity.WmsInventory, error)
	FindByItemID(ctx context.Context, itemId uint) ([]*entity.WmsInventory, error)
	FindByWarehouseID(ctx context.Context, warehouseId uint) ([]*entity.WmsInventory, error)
	FindByBatchNo(ctx context.Context, batchNo string) ([]*entity.WmsInventory, error)
	FindByStatus(ctx context.Context, status string) ([]*entity.WmsInventory, error)

	// 复杂查询
	FindAvailableByItem(ctx context.Context, itemId uint, warehouseId *uint, requiredQty float64) ([]*entity.WmsInventory, error)
	FindExpiringItems(ctx context.Context, days int, warehouseId *uint) ([]*entity.WmsInventory, error)
	FindLowStockItems(ctx context.Context, warehouseId *uint) ([]*entity.WmsInventory, error)
	FindZeroStockItems(ctx context.Context, warehouseId *uint) ([]*entity.WmsInventory, error)

	// 统计查询
	CountByWarehouse(ctx context.Context, warehouseId uint) (int64, error)
	CountByStatus(ctx context.Context, status string) (int64, error)
	SumQuantityByWarehouse(ctx context.Context, warehouseId uint) (float64, error)
	SumQuantityByItem(ctx context.Context, itemId uint) (float64, error)
	GetLocationUsage(ctx context.Context, locationId uint) (float64, error)

	// 批量操作
	BatchUpdate(ctx context.Context, ids []uint, updates map[string]interface{}) error
	BatchUpdateStatus(ctx context.Context, ids []uint, status string, reason string) error

	// 库存可用性检查
	CheckAvailability(ctx context.Context, itemId uint, requiredQty float64, warehouseId *uint) (bool, error)

	// 库存预占和释放
	ReserveInventory(ctx context.Context, inventoryId uint, reservedQty float64) error
	ReleaseReservation(ctx context.Context, inventoryId uint, releasedQty float64) error
}

// wmsInventoryRepository 库存数据访问实现
type wmsInventoryRepository struct {
	BaseRepositoryImpl[entity.WmsInventory, uint]
}

// NewWmsInventoryRepository 创建库存数据访问实例
func NewWmsInventoryRepository(db *gorm.DB) WmsInventoryRepository {
	return &wmsInventoryRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsInventory, uint]{
			db: db,
		},
	}
}

// GetPage 分页查询库存
func (r *wmsInventoryRepository) GetPage(ctx context.Context, query *dto.WmsInventoryQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	// 构建查询条件
	if query.WarehouseID != nil {
		conditions = append(conditions, NewEqualCondition("location.warehouse_id", *query.WarehouseID))
	}
	if query.LocationID != nil {
		conditions = append(conditions, NewEqualCondition("location_id", *query.LocationID))
	}
	if query.ItemID != nil {
		conditions = append(conditions, NewEqualCondition("item_id", *query.ItemID))
	}
	if query.ItemSku != nil && *query.ItemSku != "" {
		conditions = append(conditions, NewLikeCondition("item.sku", *query.ItemSku))
	}
	if query.ItemName != nil && *query.ItemName != "" {
		conditions = append(conditions, NewLikeCondition("item.name", *query.ItemName))
	}
	if query.BatchNo != nil && *query.BatchNo != "" {
		conditions = append(conditions, NewEqualCondition("batch_no", *query.BatchNo))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.QuantityMin != nil {
		conditions = append(conditions, NewGreaterThanEqualCondition("quantity", *query.QuantityMin))
	}
	if query.QuantityMax != nil {
		conditions = append(conditions, NewLessThanEqualCondition("quantity", *query.QuantityMax))
	}
	if query.ExpiryDateStart != nil {
		conditions = append(conditions, NewGreaterThanEqualCondition("expiry_date", *query.ExpiryDateStart))
	}
	if query.ExpiryDateEnd != nil {
		conditions = append(conditions, NewLessThanEqualCondition("expiry_date", *query.ExpiryDateEnd))
	}
	if query.OnlyAvailable != nil && *query.OnlyAvailable {
		conditions = append(conditions, NewGreaterThanCondition("available_qty", 0))
	}
	if query.IncludeZero != nil && !*query.IncludeZero {
		conditions = append(conditions, NewGreaterThanCondition("quantity", 0))
	}

	// 使用BaseRepository的分页查询
	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// FindByLocationID 根据库位ID查询库存
func (r *wmsInventoryRepository) FindByLocationID(ctx context.Context, locationId uint) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewEqualCondition("location_id", locationId),
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, nil)
}

// FindByItemID 根据物料ID查询库存
func (r *wmsInventoryRepository) FindByItemID(ctx context.Context, itemId uint) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewEqualCondition("item_id", itemId),
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, nil)
}

// FindByWarehouseID 根据仓库ID查询库存
func (r *wmsInventoryRepository) FindByWarehouseID(ctx context.Context, warehouseId uint) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewEqualCondition("location.warehouse_id", warehouseId),
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, nil)
}

// FindByBatchNo 根据批次号查找库存
func (r *wmsInventoryRepository) FindByBatchNo(ctx context.Context, batchNo string) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewEqualCondition("batch_no", batchNo),
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, nil)
}

// FindByStatus 根据状态查找库存
func (r *wmsInventoryRepository) FindByStatus(ctx context.Context, status string) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, nil)
}

// FindAvailableByItem 查找物料的可用库存
func (r *wmsInventoryRepository) FindAvailableByItem(ctx context.Context, itemId uint, warehouseId *uint, requiredQty float64) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewEqualCondition("item_id", itemId),
		NewGreaterThanCondition("available_qty", 0),
	}

	if warehouseId != nil {
		conditions = append(conditions, NewEqualCondition("location.warehouse_id", *warehouseId))
	}

	sortInfos := []response.SortInfo{
		{Field: "expiry_date", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// FindExpiringItems 查找即将过期的库存
func (r *wmsInventoryRepository) FindExpiringItems(ctx context.Context, days int, warehouseId *uint) ([]*entity.WmsInventory, error) {
	expiryDate := time.Now().AddDate(0, 0, days)
	conditions := []QueryCondition{
		NewIsNotNullCondition("expiry_date"),
		NewLessThanEqualCondition("expiry_date", expiryDate),
	}

	if warehouseId != nil {
		conditions = append(conditions, NewEqualCondition("location.warehouse_id", *warehouseId))
	}

	sortInfos := []response.SortInfo{
		{Field: "expiry_date", Order: "asc"},
	}

	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// FindLowStockItems 查找低库存物料
func (r *wmsInventoryRepository) FindLowStockItems(ctx context.Context, warehouseId *uint) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewLessThanEqualCondition("quantity", "min_stock_level"),
		NewGreaterThanCondition("min_stock_level", 0),
	}

	if warehouseId != nil {
		conditions = append(conditions, NewEqualCondition("location.warehouse_id", *warehouseId))
	}

	sortInfos := []response.SortInfo{
		{Field: "quantity", Order: "asc"},
	}

	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// FindZeroStockItems 查找零库存物料
func (r *wmsInventoryRepository) FindZeroStockItems(ctx context.Context, warehouseId *uint) ([]*entity.WmsInventory, error) {
	conditions := []QueryCondition{
		NewEqualCondition("quantity", 0),
	}

	if warehouseId != nil {
		conditions = append(conditions, NewEqualCondition("location.warehouse_id", *warehouseId))
	}

	sortInfos := []response.SortInfo{
		{Field: "updated_at", Order: "desc"},
	}

	return r.BaseRepositoryImpl.FindByCondition(ctx, conditions, sortInfos)
}

// 统计查询方法
func (r *wmsInventoryRepository) CountByWarehouse(ctx context.Context, warehouseId uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("wms_inventory").
		Joins("JOIN wms_location ON wms_inventory.location_id = wms_location.id").
		Where("wms_location.warehouse_id = ?", warehouseId).
		Count(&count).Error
	return count, err
}

func (r *wmsInventoryRepository) CountByStatus(ctx context.Context, status string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("wms_inventory").
		Where("status = ?", status).
		Count(&count).Error
	return count, err
}

// SumQuantityByWarehouse 按仓库汇总数量
func (r *wmsInventoryRepository) SumQuantityByWarehouse(ctx context.Context, warehouseId uint) (float64, error) {
	var result float64
	err := r.db.WithContext(ctx).
		Table("wms_inventory").
		Joins("JOIN wms_location ON wms_inventory.location_id = wms_location.id").
		Where("wms_location.warehouse_id = ?", warehouseId).
		Select("COALESCE(SUM(quantity), 0)").
		Scan(&result).Error
	return result, err
}

// SumQuantityByItem 按物料汇总数量
func (r *wmsInventoryRepository) SumQuantityByItem(ctx context.Context, itemId uint) (float64, error) {
	var result float64
	err := r.db.WithContext(ctx).
		Table("wms_inventory").
		Where("item_id = ?", itemId).
		Select("COALESCE(SUM(quantity), 0)").
		Scan(&result).Error
	return result, err
}

// GetLocationUsage 获取库位使用率
func (r *wmsInventoryRepository) GetLocationUsage(ctx context.Context, locationId uint) (float64, error) {
	var result float64
	err := r.db.WithContext(ctx).
		Table("wms_inventory").
		Where("location_id = ?", locationId).
		Select("COALESCE(SUM(quantity), 0)").
		Scan(&result).Error
	return result, err
}

// 批量操作方法
func (r *wmsInventoryRepository) BatchUpdate(ctx context.Context, ids []uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).
		Model(&entity.WmsInventory{}).
		Where("id IN ?", ids).
		Updates(updates).Error
}

func (r *wmsInventoryRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string, reason string) error {
	updates := map[string]interface{}{
		"status": status,
		"remark": reason,
	}
	return r.BatchUpdate(ctx, ids, updates)
}

// 库存可用性检查
func (r *wmsInventoryRepository) CheckAvailability(ctx context.Context, itemId uint, requiredQty float64, warehouseId *uint) (bool, error) {
	var totalAvailable float64
	query := r.db.WithContext(ctx).
		Table("wms_inventory").
		Where("item_id = ? AND available_qty > 0", itemId).
		Select("COALESCE(SUM(available_qty), 0)")

	if warehouseId != nil {
		query = query.Joins("JOIN wms_location ON wms_inventory.location_id = wms_location.id").
			Where("wms_location.warehouse_id = ?", *warehouseId)
	}

	err := query.Scan(&totalAvailable).Error
	if err != nil {
		return false, err
	}

	return totalAvailable >= requiredQty, nil
}

// 库存预占
func (r *wmsInventoryRepository) ReserveInventory(ctx context.Context, inventoryId uint, reservedQty float64) error {
	return r.db.WithContext(ctx).
		Model(&entity.WmsInventory{}).
		Where("id = ? AND available_qty >= ?", inventoryId, reservedQty).
		Updates(map[string]interface{}{
			"reserved_qty":  gorm.Expr("reserved_qty + ?", reservedQty),
			"available_qty": gorm.Expr("available_qty - ?", reservedQty),
		}).Error
}

// 释放库存预占
func (r *wmsInventoryRepository) ReleaseReservation(ctx context.Context, inventoryId uint, releasedQty float64) error {
	return r.db.WithContext(ctx).
		Model(&entity.WmsInventory{}).
		Where("id = ? AND reserved_qty >= ?", inventoryId, releasedQty).
		Updates(map[string]interface{}{
			"reserved_qty":  gorm.Expr("reserved_qty - ?", releasedQty),
			"available_qty": gorm.Expr("available_qty + ?", releasedQty),
		}).Error
}
