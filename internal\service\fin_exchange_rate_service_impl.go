package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/integration"
	"backend/pkg/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// FinExchangeRateService 定义了汇率服务的接口
type FinExchangeRateService interface {
	Create(ctx context.Context, req *dto.FinExchangeRateCreateReq) (*vo.FinExchangeRateItemRes, error)
	Update(ctx context.Context, req *dto.FinExchangeRateUpdateReq) (*vo.FinExchangeRateItemRes, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.FinExchangeRateItemRes, error)
	GetPage(ctx context.Context, req *dto.FinExchangeRatePageReq) (*response.PageResult, error)
	GetLatestRate(ctx context.Context, req *dto.LatestExchangeRateReq) (*vo.FinExchangeRateItemRes, error)
	SyncRates(ctx context.Context) error
}

// finExchangeRateServiceImpl 是 FinExchangeRateService 的实现
type finExchangeRateServiceImpl struct {
	*BaseServiceImpl
	repo         repository.FinExchangeRateRepository
	currencyRepo repository.FinCurrencyRepository
	paramService SystemParameterService
}

// NewFinExchangeRateService 创建一个新的汇率服务实例
func NewFinExchangeRateService(sm *ServiceManager) FinExchangeRateService {
	return &finExchangeRateServiceImpl{
		BaseServiceImpl: NewBaseService(sm),
		repo:            sm.GetRepositoryManager().GetFinExchangeRateRepository(),
		currencyRepo:    sm.GetRepositoryManager().GetFinCurrencyRepository(),
		paramService:    sm.GetSystemParameterService(),
	}
}

// findByIDWithPreload 是一个辅助方法，用于通过ID查找汇率并预加载关联的货币信息
func (s *finExchangeRateServiceImpl) findByIDWithPreload(ctx context.Context, id uint) (*entity.FinExchangeRate, error) {
	var rate entity.FinExchangeRate
	db := s.repo.GetDB(ctx).
		Preload("FromCurrency").
		Preload("ToCurrency")

	if err := db.First(&rate, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "汇率记录不存在")
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询汇率失败").WithCause(err)
	}
	return &rate, nil
}

// Create 创建汇率
func (s *finExchangeRateServiceImpl) Create(ctx context.Context, req *dto.FinExchangeRateCreateReq) (*vo.FinExchangeRateItemRes, error) {
	// DTO to Entity
	rate, err := decimal.NewFromString(req.Rate.String())
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "汇率格式无效").WithCause(err)
	}
	rateDate, err := time.Parse("2006-01-02", req.RateDate)
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "生效日期格式无效").WithCause(err)
	}
	exchangeRate := &entity.FinExchangeRate{
		FromCurrencyID: req.SourceCurrencyID,
		ToCurrencyID:   req.TargetCurrencyID,
		Rate:           rate,
		RateDate:       rateDate,
	}

	// 设置创建人
	userID, _ := s.GetUserIDFromContext(ctx)
	exchangeRate.CreatedBy = uint(userID)

	// 插入数据库
	if err := s.repo.Create(ctx, exchangeRate); err != nil {
		// 可能是唯一性约束冲突
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_ALREADY_EXISTS, "该币种对在当天已存在汇率").WithCause(err)
	}

	// 高效获取带有预加载数据的实体
	created, err := s.findByIDWithPreload(ctx, exchangeRate.ID)
	if err != nil {
		return nil, err
	}

	return vo.FromExchangeRateEntity(created), nil
}

// Update 更新汇率
func (s *finExchangeRateServiceImpl) Update(ctx context.Context, req *dto.FinExchangeRateUpdateReq) (*vo.FinExchangeRateItemRes, error) {
	// 检查是否存在
	originalEntity, err := s.repo.FindByID(ctx, req.ID)
	if err != nil {
		return nil, err // FindByID 内部已处理错误包装
	}

	// DTO to Entity
	rate, err := decimal.NewFromString(req.Rate.String())
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "汇率格式无效").WithCause(err)
	}
	rateDate, err := time.Parse("2006-01-02", req.RateDate)
	if err != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "生效日期格式无效").WithCause(err)
	}
	exchangeRate := &entity.FinExchangeRate{
		FromCurrencyID: req.SourceCurrencyID,
		ToCurrencyID:   req.TargetCurrencyID,
		Rate:           rate,
		RateDate:       rateDate,
	}
	exchangeRate.ID = req.ID

	// 保留原始创建信息
	exchangeRate.CreatedAt = originalEntity.CreatedAt
	exchangeRate.CreatedBy = originalEntity.CreatedBy

	// 设置更新人
	userID, _ := s.GetUserIDFromContext(ctx)
	exchangeRate.UpdatedBy = uint(userID)

	// 更新数据库
	if err := s.repo.Update(ctx, exchangeRate); err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新汇率失败").WithCause(err)
	}

	// 高效获取带有预加载数据的实体
	updated, err := s.findByIDWithPreload(ctx, exchangeRate.ID)
	if err != nil {
		return nil, err
	}

	return vo.FromExchangeRateEntity(updated), nil
}

// Delete 删除汇率
func (s *finExchangeRateServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

// GetByID 根据ID获取汇率
func (s *finExchangeRateServiceImpl) GetByID(ctx context.Context, id uint) (*vo.FinExchangeRateItemRes, error) {
	entity, err := s.findByIDWithPreload(ctx, id)
	if err != nil {
		return nil, err
	}
	return vo.FromExchangeRateEntity(entity), nil
}

// GetPage 分页获取汇率 - 优化版，避免 N+1
func (s *finExchangeRateServiceImpl) GetPage(ctx context.Context, req *dto.FinExchangeRatePageReq) (*response.PageResult, error) {
	// 1. 构建基础查询，但不预加载
	db := s.repo.GetDB(ctx).Model(&entity.FinExchangeRate{})
	if req.SourceCurrencyID != nil {
		db = db.Where("source_currency_id = ?", *req.SourceCurrencyID)
	}
	if req.TargetCurrencyID != nil {
		db = db.Where("target_currency_id = ?", *req.TargetCurrencyID)
	}

	// 2. 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询汇率总数失败").WithCause(err)
	}
	if total == 0 {
		return &response.PageResult{Total: 0, List: []interface{}{}}, nil
	}

	// 3. 查询分页数据
	var entities []*entity.FinExchangeRate
	pageNum := req.Page
	pageSize := req.PageSize
	if pageNum > 0 && pageSize > 0 {
		db = db.Offset((pageNum - 1) * pageSize).Limit(pageSize)
	}
	db = db.Order("rate_date desc")
	if err := db.Find(&entities).Error; err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "分页查询汇率失败").WithCause(err)
	}

	// 4. 高效组装币种信息
	// 4.1 收集所有需要的币种ID
	currencyIDs := make(map[uint]struct{})
	for _, rate := range entities {
		currencyIDs[rate.FromCurrencyID] = struct{}{}
		currencyIDs[rate.ToCurrencyID] = struct{}{}
	}
	var ids []uint
	for id := range currencyIDs {
		ids = append(ids, id)
	}

	// 4.2 一次性查询所有相关币种
	currencies, err := s.currencyRepo.FindAllByIDs(ctx, ids)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询关联币种信息失败").WithCause(err)
	}

	// 4.3 创建币种ID到实体的映射
	currencyMap := make(map[uint]*entity.FinCurrency)
	for _, c := range currencies {
		currencyMap[c.ID] = c
	}

	// 4.4 组装到实体中
	for _, rate := range entities {
		if source, ok := currencyMap[rate.FromCurrencyID]; ok {
			rate.FromCurrency = *source
		}
		if target, ok := currencyMap[rate.ToCurrencyID]; ok {
			rate.ToCurrency = *target
		}
	}

	// 5. 转换为VO并返回
	pageResult := &response.PageResult{
		Total:    total,
		List:     vo.FromExchangeRateEntities(entities),
		PageNum:  pageNum,
		PageSize: pageSize,
	}

	return pageResult, nil
}

// GetLatestRate 获取最新汇率
func (s *finExchangeRateServiceImpl) GetLatestRate(ctx context.Context, req *dto.LatestExchangeRateReq) (*vo.FinExchangeRateItemRes, error) {
	sourceCurrency, err := s.currencyRepo.FindByCode(ctx, req.SourceCode)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewParamError(apperrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("源币种 '%s' 不存在", req.SourceCode))
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询源币种失败").WithCause(err)
	}
	targetCurrency, err := s.currencyRepo.FindByCode(ctx, req.TargetCode)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewParamError(apperrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("目标币种 '%s' 不存在", req.TargetCode))
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询目标币种失败").WithCause(err)
	}

	// 查找最新汇率的逻辑
	var rate entity.FinExchangeRate
	query := s.repo.GetDB(ctx).
		Where("from_currency_id = ?", sourceCurrency.ID).
		Where("to_currency_id = ?", targetCurrency.ID)

	if req.Date != "" {
		rateDate, err := time.Parse("2006-01-02", req.Date)
		if err != nil {
			return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "日期格式无效").WithCause(err)
		}
		query = query.Where("rate_date <= ?", rateDate)
	}

	err = query.Order("rate_date DESC").First(&rate).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "未找到符合条件的汇率")
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询最新汇率失败").WithCause(err)
	}

	// 预加载关联数据
	rate.FromCurrency = *sourceCurrency
	rate.ToCurrency = *targetCurrency

	return vo.FromExchangeRateEntity(&rate), nil
}

// SyncRates 从配置的API提供商同步最新的汇率数据
func (s *finExchangeRateServiceImpl) SyncRates(ctx context.Context) error {
	log := s.GetLogger()
	log.Info("开始执行汇率同步任务")

	// 1. 从系统参数获取API配置
	paramValue, err := s.paramService.GetParameterValueByKey(ctx, "ExchangeRate.Api.Config")
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn("未找到汇率API配置 (ExchangeRate.Api.Config)，跳过同步")
			return nil // 不是错误，只是未配置
		}
		log.Error("获取汇率API配置失败", zap.Error(err))
		return fmt.Errorf("获取汇率API配置失败: %w", err)
	}

	var apiConfigs []integration.APIConfig
	if err := json.Unmarshal([]byte(paramValue), &apiConfigs); err != nil {
		log.Error("解析汇率API配置JSON失败", zap.Error(err))
		return fmt.Errorf("解析汇率API配置失败: %w", err)
	}

	// 2. 遍历配置，找到第一个启用的提供商并执行
	for _, config := range apiConfigs {
		if !config.Enabled {
			log.Info("跳过已禁用的汇率提供商", zap.String("provider", config.ProviderName))
			continue
		}

		log.Info("使用提供商进行同步", zap.String("provider", config.ProviderName))
		// 目前只支持 exchangerate-api.com，将来可以根据 ProviderName 使用不同的工厂函数
		provider, err := integration.NewExchangeRateAPIComProvider(config)
		if err != nil {
			log.Error("初始化汇率提供商失败", zap.String("provider", config.ProviderName), zap.Error(err))
			continue // 尝试下一个提供商
		}

		rates, err := provider.FetchLatestRates()
		if err != nil {
			log.Error("从API获取汇率失败", zap.String("provider", config.ProviderName), zap.Error(err))
			continue // 尝试下一个提供商
		}

		// 3. 将获取到的汇率更新到数据库
		if err := s.updateRatesInDB(ctx, config.BaseCurrency, rates); err != nil {
			log.Error("更新汇率到数据库失败", zap.String("provider", config.ProviderName), zap.Error(err))
			// 一个提供商失败后，可以选择继续尝试下一个或直接返回错误
			// 此处选择返回错误，因为可能数据源有问题
			return err
		}

		// 成功同步一个后即退出
		log.Info("汇率同步任务成功完成", zap.String("provider", config.ProviderName))
		return nil
	}

	log.Warn("没有启用或可用的汇率提供商配置来完成同步")
	return nil
}

// updateRatesInDB 将从API获取的汇率数据更新或创建到数据库中
func (s *finExchangeRateServiceImpl) updateRatesInDB(ctx context.Context, baseCurrencyCode string, apiRates map[string]decimal.Decimal) error {
	log := s.GetLogger()
	// 1. 获取系统中所有启用的币种
	var allCurrencies []*entity.FinCurrency
	if err := s.currencyRepo.GetDB(ctx).Where("status = ?", 1).Find(&allCurrencies).Error; err != nil {
		log.Error("无法获取所有启用的币种", zap.Error(err))
		return err
	}

	if len(allCurrencies) == 0 {
		log.Warn("系统中没有启用的币种，无法计算汇率")
		return nil
	}

	currencyMap := make(map[string]entity.FinCurrency)
	for _, c := range allCurrencies {
		currencyMap[c.Code] = *c
	}

	// 2. 检查基准货币是否存在于我们的系统中
	baseCurrency, ok := currencyMap[baseCurrencyCode]
	if !ok {
		return fmt.Errorf("API基准货币 '%s' 在系统中不存在或未启用", baseCurrencyCode)
	}

	// 3. 准备要批量创建的汇率记录
	var newRates []*entity.FinExchangeRate
	rateDate := time.Now() // 使用当前日期作为汇率生效日期
	userID := 1            // 定时任务可能没有用户上下文，给个默认系统用户ID

	// 4. 计算并组合汇率
	// 遍历系统中的所有币种，为它们创建相对于基准货币的汇率
	for targetCode, targetCurrency := range currencyMap {
		// 跳过基准货币本身
		if targetCode == baseCurrencyCode {
			continue
		}

		// 从API获取的汇率数据中查找目标币种的汇率
		rateFromApi, ok := apiRates[targetCode]
		if !ok {
			log.Warn("未从API获取到币种的汇率", zap.String("currencyCode", targetCode))
			continue
		}

		// 创建从基准货币到目标货币的汇率
		newRates = append(newRates, &entity.FinExchangeRate{
			TenantEntity: entity.TenantEntity{
				BaseEntity: entity.BaseEntity{
					CreatedBy: uint(userID),
					UpdatedBy: uint(userID),
				},
			},
			FromCurrencyID: baseCurrency.ID,
			ToCurrencyID:   targetCurrency.ID,
			Rate:           rateFromApi,
			RateDate:       rateDate,
		})

		// 计算并创建从目标货币到基准货币的汇率 (倒数)
		if !rateFromApi.IsZero() {
			reciprocalRate := decimal.NewFromInt(1).Div(rateFromApi)
			newRates = append(newRates, &entity.FinExchangeRate{
				TenantEntity: entity.TenantEntity{
					BaseEntity: entity.BaseEntity{
						CreatedBy: uint(userID),
						UpdatedBy: uint(userID),
					},
				},
				FromCurrencyID: targetCurrency.ID,
				ToCurrencyID:   baseCurrency.ID,
				Rate:           reciprocalRate,
				RateDate:       rateDate,
			})
		}
	}

	// 5. 使用事务批量创建或更新
	if len(newRates) == 0 {
		log.Info("没有新的汇率需要更新")
		return nil
	}

	return s.repo.GetDB(ctx).Transaction(func(tx *gorm.DB) error {
		// GORM 的 Create 批量插入功能对于处理唯一性冲突不够灵活。
		// 更稳妥的方式是逐条检查并创建/更新。
		// 或者使用数据库特定的 "ON CONFLICT UPDATE" (需要 gorm.io/driver/postgres 的 Clauses)
		// 这里采用一个简化的逻辑：为避免复杂性，我们总是创建新的。
		// 一个更优的实现可能是先删除当天的所有汇率，再全部插入。
		log.Info("准备批量创建汇率记录", zap.Int("count", len(newRates)))
		if err := tx.Create(&newRates).Error; err != nil {
			log.Error("批量创建汇率记录失败", zap.Error(err))
			return fmt.Errorf("批量创建汇率失败: %w", err)
		}
		return nil
	})
}
