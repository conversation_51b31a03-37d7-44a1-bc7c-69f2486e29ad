<template>
  <div class="tabs-examples-page">
    <h2>VNTabs 标签页组件示例</h2>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础用法 (top, default type)</span>
        </div>
      </template>
      <VNTabs v-model="activeName1" :items="items1" @tab-change="logTabChange">
        <template #first="{ item }">
          <p>Content of {{ item.label }}</p>
        </template>
        <template #second="{ item }">
          <p>Content of {{ item.label }}</p>
        </template>
        <template #third="{ item }">
          <p>Content of {{ item.label }} (Lazy)</p>
          <p>This content is loaded only when the tab is clicked.</p>
        </template>
        <template #fourth="{ item }">
          <p>Content of {{ item.label }}</p>
        </template>
      </VNTabs>
      <p class="log-p">Current Active Tab: {{ activeName1 }}</p>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>卡片样式 (card type, left position)</span>
        </div>
      </template>
      <VNTabs v-model="activeName2" :items="items2" type="card" tab-position="left" style="min-height: 150px;">
         <template #user="{ item }">
             <el-form label-width="80px">
                 <el-form-item label="姓名">
                     <el-input :placeholder="item.label" />
                 </el-form-item>
             </el-form>
         </template>
          <template #config="{ item }">
             <el-checkbox>启用 {{ item.label }}</el-checkbox>
         </template>
          <template #role="{ item }">
             <p>{{ item.label }} Content</p>
         </template>
      </VNTabs>
    </el-card>

     <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>可关闭与添加 (editable)</span>
        </div>
      </template>
      <el-button size="small" @click="addTab" style="margin-bottom: 10px;">添加 Tab</el-button>
      <VNTabs 
        v-model="editableTabValue"
        :items="editableTabs"
        type="card"
        editable
        @edit="handleTabsEdit"
        @tab-remove="handleTabsEdit"
      >
        <template v-for="item in editableTabs" #[item.name] :key="item.name">
            Content of {{ item.label }}
        </template>
      </VNTabs>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNTabs from './index.vue';
import type { TabItem } from './types';
import { ElCard, ElButton, ElMessage, ElInput, ElForm, ElFormItem, ElCheckbox } from 'element-plus';
import { User, Setting, List, InfoFilled } from '@element-plus/icons-vue';

// Example 1
const activeName1 = ref<string | number>('first');
const items1 = ref<TabItem[]>([
  { name: 'first', label: 'Tab 1' },
  { name: 'second', label: 'Tab 2 (Disabled)', disabled: true },
  { name: 'third', label: 'Tab 3 (Lazy)', lazy: true, icon: List },
  { name: 'fourth', label: 'Tab 4' },
]);

const logTabChange = (name: string | number) => {
    console.log('Tab changed to:', name);
};

// Example 2
const activeName2 = ref('user');
const items2 = ref<TabItem[]>([
    { name: 'user', label: '用户管理', icon: User },
    { name: 'config', label: '配置管理', icon: Setting },
    { name: 'role', label: '角色管理', icon: InfoFilled }
]);

// Example 3 (Editable)
let tabIndex = 2;
const editableTabValue = ref('2');
const editableTabs = ref<TabItem[]>([
  { label: 'Tab 1', name: '1', closable: false }, // First tab not closable
  { label: 'Tab 2', name: '2', closable: true },
]);

const addTab = () => {
  const newTabName = `${++tabIndex}`;
  editableTabs.value.push({
    label: `New Tab ${tabIndex}`,
    name: newTabName,
    closable: true,
  });
  editableTabValue.value = newTabName;
  ElMessage.success(`Added Tab ${tabIndex}`);
};

const handleTabsEdit = (targetName: string | number | undefined, action: 'remove' | 'add') => {
  if (action === 'add') {
    addTab();
  } else if (action === 'remove' && targetName !== undefined) {
    const tabs = editableTabs.value;
    let activeName = editableTabValue.value;
    if (activeName === targetName) {
      tabs.forEach((tab, index) => {
        if (tab.name === targetName) {
          const nextTab = tabs[index + 1] || tabs[index - 1];
          if (nextTab) {
            activeName = nextTab.name;
          }
        }
      });
    }

    editableTabValue.value = activeName;
    editableTabs.value = tabs.filter((tab) => tab.name !== targetName);
     ElMessage.warning(`Removed Tab ${targetName}`);
  }
};

</script>

<style scoped>
.tabs-examples-page {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.log-p {
    margin-top: 15px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
}

/* Style for card type tabs content area */
:deep(.el-tabs--card > .el-tabs__content),
:deep(.el-tabs--border-card > .el-tabs__content) {
    padding: 15px;
}
</style> 