package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"
)

// WmsBlindReceivingConfigRepository 盲收配置仓储接口
type WmsBlindReceivingConfigRepository interface {
	BaseRepository[entity.WmsBlindReceivingConfig, uint]

	// 基础查询
	GetPage(ctx context.Context, query *dto.WmsBlindReceivingConfigQueryReq) (*response.PageResult, error)

	// 多层级配置查询
	FindByConfigLevel(ctx context.Context, level string, targetID *uint) (*entity.WmsBlindReceivingConfig, error)
	GetEffectiveConfig(ctx context.Context, warehouseID uint, clientID *uint, userID *uint) (*entity.WmsBlindReceivingConfig, error)

	// 批量操作
	CreateBatch(ctx context.Context, configs []*entity.WmsBlindReceivingConfig) error
	UpdateBatch(ctx context.Context, configs []*entity.WmsBlindReceivingConfig) error

	// 业务查询
	GetActiveConfigs(ctx context.Context, configLevel string) ([]*entity.WmsBlindReceivingConfig, error)
	GetConfigsByStrategy(ctx context.Context, strategy string) ([]*entity.WmsBlindReceivingConfig, error)
	FindConflictingConfigs(ctx context.Context, level string, targetID *uint, excludeID *uint) ([]*entity.WmsBlindReceivingConfig, error)

	// 配置目标相关
	GetAvailableTargets(ctx context.Context, configLevel string) ([]dto.ConfigLevelTarget, error)
	ValidateConfigTarget(ctx context.Context, level string, targetID *uint) (bool, error)

	// 统计查询
	GetConfigStats(ctx context.Context, warehouseID *uint) (*dto.BlindReceivingStatsResp, error)
}

// wmsBlindReceivingConfigRepository 盲收配置仓储实现
type wmsBlindReceivingConfigRepository struct {
	BaseRepositoryImpl[entity.WmsBlindReceivingConfig, uint]
}

// NewWmsBlindReceivingConfigRepository 创建盲收配置仓储
func NewWmsBlindReceivingConfigRepository(db *gorm.DB) WmsBlindReceivingConfigRepository {
	return &wmsBlindReceivingConfigRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsBlindReceivingConfig, uint]{
			db: db,
		},
	}
}

// GetPage 分页查询盲收配置
func (r *wmsBlindReceivingConfigRepository) GetPage(ctx context.Context, query *dto.WmsBlindReceivingConfigQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	// 构建查询条件
	if query.ConfigLevel != nil {
		conditions = append(conditions, NewEqualCondition("config_level", *query.ConfigLevel))
	}
	if query.ConfigTargetID != nil {
		conditions = append(conditions, NewEqualCondition("config_target_id", *query.ConfigTargetID))
	}
	if query.Strategy != nil {
		conditions = append(conditions, NewEqualCondition("strategy", *query.Strategy))
	}
	if query.IsActive != nil {
		conditions = append(conditions, NewEqualCondition("is_active", *query.IsActive))
	}

	// 排序
	sortInfos := []response.SortInfo{}
	if query.SortField != "" && query.SortOrder != "" {
		sortInfos = append(sortInfos, response.SortInfo{
			Field: query.SortField,
			Order: query.SortOrder,
		})
	} else {
		// 默认按优先级排序
		sortInfos = append(sortInfos, response.SortInfo{
			Field: "priority",
			Order: "asc",
		})
	}

	// 执行分页查询
	return r.FindByPage(ctx, &query.Pagination, conditions)
}

// FindByConfigLevel 根据配置级别和目标ID查找配置
func (r *wmsBlindReceivingConfigRepository) FindByConfigLevel(ctx context.Context, level string, targetID *uint) (*entity.WmsBlindReceivingConfig, error) {
	conditions := []QueryCondition{
		NewEqualCondition("config_level", level),
		NewEqualCondition("is_active", true),
	}

	// 系统级别配置目标ID为空
	if level == string(entity.ConfigLevelSystem) {
		conditions = append(conditions, NewIsNullCondition("config_target_id"))
	} else if targetID != nil {
		conditions = append(conditions, NewEqualCondition("config_target_id", *targetID))
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	// 按优先级排序，取最高优先级（数值最小）的配置
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "asc"},
	}

	results, err := r.FindByCondition(ctx, conditions, sortInfos)
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	return results[0], nil
}

// GetEffectiveConfig 获取有效配置（多层级优先级算法）
func (r *wmsBlindReceivingConfigRepository) GetEffectiveConfig(ctx context.Context, warehouseID uint, clientID *uint, userID *uint) (*entity.WmsBlindReceivingConfig, error) {
	// 多层级查询优先级：USER > CLIENT > WAREHOUSE > SYSTEM

	// 1. 用户级别配置 (最高优先级)
	if userID != nil && *userID > 0 {
		if config, err := r.FindByConfigLevel(ctx, string(entity.ConfigLevelUser), userID); err == nil {
			return config, nil
		}
	}

	// 2. 客户级别配置
	if clientID != nil && *clientID > 0 {
		if config, err := r.FindByConfigLevel(ctx, string(entity.ConfigLevelClient), clientID); err == nil {
			return config, nil
		}
	}

	// 3. 仓库级别配置
	if warehouseID > 0 {
		if config, err := r.FindByConfigLevel(ctx, string(entity.ConfigLevelWarehouse), &warehouseID); err == nil {
			return config, nil
		}
	}

	// 4. 系统级别配置 (最低优先级)
	if config, err := r.FindByConfigLevel(ctx, string(entity.ConfigLevelSystem), nil); err == nil {
		return config, nil
	}

	// 如果没有找到任何配置，返回默认严格模式配置
	return &entity.WmsBlindReceivingConfig{
		ConfigLevel: string(entity.ConfigLevelSystem),
		Strategy:    string(entity.StrategyStrict),
		IsActive:    true,
		Priority:    10,
	}, nil
}

// CreateBatch 批量创建配置
func (r *wmsBlindReceivingConfigRepository) CreateBatch(ctx context.Context, configs []*entity.WmsBlindReceivingConfig) error {
	if len(configs) == 0 {
		return nil
	}
	return r.GetDB(ctx).CreateInBatches(configs, len(configs)).Error
}

// UpdateBatch 批量更新配置
func (r *wmsBlindReceivingConfigRepository) UpdateBatch(ctx context.Context, configs []*entity.WmsBlindReceivingConfig) error {
	if len(configs) == 0 {
		return nil
	}

	return r.GetDB(ctx).Transaction(func(tx *gorm.DB) error {
		for _, config := range configs {
			if err := tx.Save(config).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetActiveConfigs 获取激活的配置
func (r *wmsBlindReceivingConfigRepository) GetActiveConfigs(ctx context.Context, configLevel string) ([]*entity.WmsBlindReceivingConfig, error) {
	conditions := []QueryCondition{
		NewEqualCondition("is_active", true),
	}
	if configLevel != "" {
		conditions = append(conditions, NewEqualCondition("config_level", configLevel))
	}

	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetConfigsByStrategy 根据策略获取配置
func (r *wmsBlindReceivingConfigRepository) GetConfigsByStrategy(ctx context.Context, strategy string) ([]*entity.WmsBlindReceivingConfig, error) {
	conditions := []QueryCondition{
		NewEqualCondition("strategy", strategy),
		NewEqualCondition("is_active", true),
	}

	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindConflictingConfigs 查找冲突的配置
func (r *wmsBlindReceivingConfigRepository) FindConflictingConfigs(ctx context.Context, level string, targetID *uint, excludeID *uint) ([]*entity.WmsBlindReceivingConfig, error) {
	conditions := []QueryCondition{
		NewEqualCondition("config_level", level),
	}

	if level == string(entity.ConfigLevelSystem) {
		conditions = append(conditions, NewIsNullCondition("config_target_id"))
	} else if targetID != nil {
		conditions = append(conditions, NewEqualCondition("config_target_id", *targetID))
	}

	if excludeID != nil {
		conditions = append(conditions, NewNotEqualCondition("id", *excludeID))
	}

	return r.FindByCondition(ctx, conditions, nil)
}

// GetAvailableTargets 获取可用的配置目标
func (r *wmsBlindReceivingConfigRepository) GetAvailableTargets(ctx context.Context, configLevel string) ([]dto.ConfigLevelTarget, error) {
	var targets []dto.ConfigLevelTarget

	switch configLevel {
	case string(entity.ConfigLevelWarehouse):
		// 查询仓库列表
		var warehouses []struct {
			ID   uint   `json:"id"`
			Name string `json:"name"`
		}
		err := r.GetDB(ctx).Table("wms_location").
			Where("location_type = 'WAREHOUSE'").
			Select("id, location_name as name").
			Find(&warehouses).Error

		if err != nil {
			return nil, err
		}

		for _, wh := range warehouses {
			targets = append(targets, dto.ConfigLevelTarget{
				Level:      configLevel,
				TargetID:   &wh.ID,
				TargetName: wh.Name,
			})
		}

	case string(entity.ConfigLevelClient):
		// 查询客户列表
		var clients []struct {
			ID   uint   `json:"id"`
			Name string `json:"name"`
		}
		err := r.GetDB(ctx).Table("crm_customer").
			Select("id, customer_name as name").
			Find(&clients).Error

		if err != nil {
			return nil, err
		}

		for _, client := range clients {
			targets = append(targets, dto.ConfigLevelTarget{
				Level:      configLevel,
				TargetID:   &client.ID,
				TargetName: client.Name,
			})
		}

	case string(entity.ConfigLevelUser):
		// 查询用户列表
		var users []struct {
			ID   uint   `json:"id"`
			Name string `json:"name"`
		}
		err := r.GetDB(ctx).Table("sys_user").
			Select("id, username as name").
			Find(&users).Error

		if err != nil {
			return nil, err
		}

		for _, user := range users {
			targets = append(targets, dto.ConfigLevelTarget{
				Level:      configLevel,
				TargetID:   &user.ID,
				TargetName: user.Name,
			})
		}
	}

	return targets, nil
}

// ValidateConfigTarget 验证配置目标是否有效
func (r *wmsBlindReceivingConfigRepository) ValidateConfigTarget(ctx context.Context, level string, targetID *uint) (bool, error) {
	if level == string(entity.ConfigLevelSystem) {
		return targetID == nil, nil
	}

	if targetID == nil {
		return false, nil
	}

	var count int64
	var err error

	switch level {
	case string(entity.ConfigLevelWarehouse):
		err = r.GetDB(ctx).Table("wms_location").
			Where("id = ? AND location_type = 'WAREHOUSE'", *targetID).Count(&count).Error
	case string(entity.ConfigLevelClient):
		err = r.GetDB(ctx).Table("crm_customer").
			Where("id = ?", *targetID).Count(&count).Error
	case string(entity.ConfigLevelUser):
		err = r.GetDB(ctx).Table("sys_user").
			Where("id = ?", *targetID).Count(&count).Error
	default:
		return false, fmt.Errorf("unknown config level: %s", level)
	}

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetConfigStats 获取配置统计信息
func (r *wmsBlindReceivingConfigRepository) GetConfigStats(ctx context.Context, warehouseID *uint) (*dto.BlindReceivingStatsResp, error) {
	// 这里实现统计逻辑，目前返回基础结构
	stats := &dto.BlindReceivingStatsResp{
		StrategyDistribution: make(map[string]int),
		DailyStats:           []dto.BlindReceivingDailyStat{},
	}

	// 统计各策略的配置数量
	var strategyStats []struct {
		Strategy string `json:"strategy"`
		Count    int    `json:"count"`
	}

	db := r.GetDB(ctx).Table("wms_blind_receiving_config").
		Select("strategy, count(*) as count").
		Where("is_active = ?", true).
		Group("strategy")

	if warehouseID != nil {
		db = db.Where("config_level = 'WAREHOUSE' AND config_target_id = ?", *warehouseID)
	}

	if err := db.Find(&strategyStats).Error; err != nil {
		return nil, err
	}

	for _, stat := range strategyStats {
		stats.StrategyDistribution[stat.Strategy] = stat.Count
	}

	return stats, nil
}
