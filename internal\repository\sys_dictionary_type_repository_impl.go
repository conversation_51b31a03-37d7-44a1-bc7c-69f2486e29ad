package repository

import (
	"backend/internal/model/dto" // 用于查询 DTO
	"backend/internal/model/entity"
	"context"
	"errors" // For gorm.ErrRecordNotFound
	"fmt"    // For error wrapping

	// For sort order check
	apperrors "backend/pkg/errors" // Assuming custom error package alias
	// "backend/pkg/util" // Removed, pagination helper is in repository_util.go

	"gorm.io/gorm"
	// BaseRepository and NewBaseRepository are assumed to be defined in this package
)

// DictionaryTypeRepository 字典类型仓库接口
type DictionaryTypeRepository interface {
	// 嵌入基础 CRUD
	BaseRepository[entity.DictionaryType, uint]

	// 特殊查询
	FindByCode(ctx context.Context, code string) (*entity.DictionaryType, error)
	FindAllEnabled(ctx context.Context) ([]*entity.DictionaryType, error)
	FindPage(ctx context.Context, query dto.DictTypePageQueryDTO) ([]*entity.DictionaryType, int64, error)
}

// --- Implementation ---

type dictionaryTypeRepository struct {
	BaseRepository[entity.DictionaryType, uint]
	// db *gorm.DB // 移除多余的 db 字段
}

// NewDictionaryTypeRepository 创建字典类型仓库实例
func NewDictionaryTypeRepository(db *gorm.DB) DictionaryTypeRepository {
	baseRepo := NewBaseRepository[entity.DictionaryType, uint](db)
	return &dictionaryTypeRepository{
		BaseRepository: baseRepo,
	}
}

// getDB 获取带上下文的 DB 连接
func (r *dictionaryTypeRepository) getDB(ctx context.Context) *gorm.DB {
	return r.BaseRepository.GetDB(ctx) // 直接调用嵌入的 BaseRepository 的 GetDB 方法
}

// FindByCode 根据编码查询字典类型
func (r *dictionaryTypeRepository) FindByCode(ctx context.Context, code string) (*entity.DictionaryType, error) {
	var dictType entity.DictionaryType
	if err := r.getDB(ctx).Where("code = ?", code).First(&dictType).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("字典类型编码 %s 未找到", code))
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "按编码查询字典类型失败")
	}
	return &dictType, nil
}

// FindAllEnabled 获取所有启用的字典类型
func (r *dictionaryTypeRepository) FindAllEnabled(ctx context.Context) ([]*entity.DictionaryType, error) {
	var dictTypes []*entity.DictionaryType
	if err := r.getDB(ctx).Where("status = ?", 1).Order("id asc").Find(&dictTypes).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*entity.DictionaryType{}, nil
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询所有启用字典类型失败")
	}
	return dictTypes, nil
}

// FindPage 分页查询字典类型 (使用 BaseRepository.FindByPage)
func (r *dictionaryTypeRepository) FindPage(ctx context.Context, query dto.DictTypePageQueryDTO) ([]*entity.DictionaryType, int64, error) {
	// 1. 将 DTO 中的过滤条件转换为 []QueryCondition
	var conditions []QueryCondition
	if query.Code != "" {
		conditions = append(conditions, NewLikeCondition("code", query.Code))
	}
	if query.Name != "" {
		conditions = append(conditions, NewLikeCondition("name", query.Name))
	}
	if query.Status != nil {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}

	// 2. 调用 BaseRepository.FindByPage
	// query.PageQuery 已经包含了排序信息，BaseRepository.FindByPage 会处理它
	// 假设 entity.DictionaryType 的字段有正确的 'sortable:"true"' 和 'json' 标签
	pageResult, err := r.BaseRepository.FindByPage(ctx, &query.PageQuery, conditions)
	if err != nil {
		// BaseRepository.FindByPage 内部已处理错误日志和包装
		return nil, 0, err
	}

	// 类型断言，因为 BaseRepository.FindByPage 返回 []*T
	dictTypes, ok := pageResult.List.([]*entity.DictionaryType)
	if !ok {
		// 这通常不应该发生
		logger := r.BaseRepository.GetDB(ctx).Logger // Get logger instance if possible, or use global logger
		logger.Error(ctx, "分页结果类型断言失败，期望 []*entity.DictionaryType，实际得到 %T", pageResult.List)
		return nil, 0, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "分页结果类型断言失败")
	}

	return dictTypes, pageResult.Total, nil
}
