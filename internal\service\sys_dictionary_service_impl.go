package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity" // Import entity for conversion methods
	"backend/internal/model/vo"
	"context"
	"fmt"     // For sorting items after retrieval
	"strings" // For error message checking

	"backend/internal/repository"
	"backend/pkg/constant"
	"backend/pkg/errors"
	apperrors "backend/pkg/errors"
	"backend/pkg/util" // <<< 确保导入 util 包
	// Assuming util package for helper functions like copying fields and CalculateTotalPage
	// Needed for error checking like IsRecordNotFound
)

// DictionaryService 数据字典服务接口
type DictionaryService interface {
	BaseService // 继承基础服务接口 (如果需要公共方法如获取 Logger)

	// --- 字典类型管理 ---
	CreateDictType(ctx context.Context, req dto.DictTypeCreateOrUpdateDTO) (*vo.DictionaryTypeVO, error)
	UpdateDictType(ctx context.Context, id uint, req dto.DictTypeCreateOrUpdateDTO) (*vo.DictionaryTypeVO, error)
	DeleteDictType(ctx context.Context, id uint) error
	GetDictTypeByID(ctx context.Context, id uint) (*vo.DictionaryTypeVO, error)
	PageDictTypes(ctx context.Context, query dto.DictTypePageQueryDTO) (*vo.PageDictTypeVO, error)
	ListEnabledDictTypes(ctx context.Context) ([]*vo.DictionaryTypeSimpleVO, error) // 获取所有启用的类型简要信息

	// --- 字典项管理 ---
	CreateDictItem(ctx context.Context, req dto.DictItemCreateOrUpdateDTO) (*vo.DictionaryItemVO, error)
	UpdateDictItem(ctx context.Context, id uint, req dto.DictItemCreateOrUpdateDTO) (*vo.DictionaryItemVO, error)
	DeleteDictItem(ctx context.Context, id uint) error
	GetDictItemByID(ctx context.Context, id uint) (*vo.DictionaryItemVO, error)
	PageDictItems(ctx context.Context, query dto.DictItemPageQueryDTO) (*vo.PageDictItemVO, error)

	// --- 字典数据获取 (核心) ---
	GetDictItemsByTypeCode(ctx context.Context, typeCode string) ([]*vo.DictDataVO, error) // 根据类型编码获取启用的字典项列表 (Label, Value) - 需要缓存

	// 可选的辅助转换方法 (如果其他 Service 需要)
	ConvertToDictTypeVO(ctx context.Context, dictType *entity.DictionaryType) (*vo.DictionaryTypeVO, error)
	ConvertToDictItemVO(ctx context.Context, dictItem *entity.DictionaryItem) (*vo.DictionaryItemVO, error)
	ConvertToDictDataVOList(ctx context.Context, dictItems []*entity.DictionaryItem) ([]*vo.DictDataVO, error)
}

// --- Implementation ---

type dictionaryServiceImpl struct {
	BaseServiceImpl // 嵌入基础服务实现
}

// NewDictionaryService 创建数据字典服务实例
func NewDictionaryService(sm *ServiceManager) DictionaryService {
	return &dictionaryServiceImpl{
		BaseServiceImpl: *NewBaseService(sm), // 修正：移除不必要的类型断言
	}
}

// --- Helper functions to get repositories ---

func (s *dictionaryServiceImpl) getDictTypeRepo() repository.DictionaryTypeRepository {
	// 从 ServiceManager 获取 RepositoryManager，再获取具体的 Repository
	// 注意：这里假设 RepositoryManager 中有 GetDictionaryTypeRepository 方法
	return s.GetServiceManager().GetRepositoryManager().GetDictionaryTypeRepository() // 需要在 RepositoryManager 添加此方法
}

func (s *dictionaryServiceImpl) getDictItemRepo() repository.DictionaryItemRepository {
	// 同上
	return s.GetServiceManager().GetRepositoryManager().GetDictionaryItemRepository() // 需要在 RepositoryManager 添加此方法
}

// --- 字典类型管理 ---

func (s *dictionaryServiceImpl) CreateDictType(ctx context.Context, req dto.DictTypeCreateOrUpdateDTO) (*vo.DictionaryTypeVO, error) {
	op := "Service.CreateDictType"
	log := s.GetLogger() // Use logger from BaseServiceImpl

	// 1. 检查 Code 是否已存在
	existing, err := s.getDictTypeRepo().FindByCode(ctx, req.Code)
	if err != nil && !errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
		// 如果不是 "未找到" 错误，则认为是查询失败
		log.WithError(err).Error(op + ": 检查字典类型编码唯一性失败")
		return nil, err // 返回包装后的 Repository 层错误
	}
	if existing != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("字典类型编码 '%s' 已存在", req.Code))
	}

	// 2. 创建实体
	dictType := &entity.DictionaryType{
		Code:     req.Code,
		Name:     req.Name,
		Remark:   req.Remark,
		Status:   1,     // 默认状态为启用
		IsSystem: false, // 新增：IsSystem 默认为 false
	}
	if req.Status != nil {
		dictType.Status = *req.Status
	}
	// 新增：处理 IsSystem，仅在创建时可以设置为 true
	if req.IsSystem != nil {
		dictType.IsSystem = *req.IsSystem
	}
	// 假设 TenantEntity 的 TenantID 由 BaseRepository 或 GORM Hook 处理

	// Set CreatedBy and UpdatedBy
	uid64, err := util.GetUserIDFromStdContext(ctx) // 使用 util.GetUserIDFromStdContext
	if err != nil {
		log.WithError(err).Warn(op + ": CreateDictType: 无法从上下文中获取UserID")
	}
	dictType.CreatedBy = uint(uid64)
	dictType.UpdatedBy = uint(uid64)

	// 3. 调用 Repository 创建
	if err := s.getDictTypeRepo().Create(ctx, dictType); err != nil {
		log.WithError(err).Error(op + ": 创建字典类型失败")
		return nil, err // Repository 层应返回包装好的错误
	}

	// 4. 转换并返回 VO
	return s.ConvertToDictTypeVO(ctx, dictType)
}

func (s *dictionaryServiceImpl) UpdateDictType(ctx context.Context, id uint, req dto.DictTypeCreateOrUpdateDTO) (*vo.DictionaryTypeVO, error) {
	op := "Service.UpdateDictType"
	log := s.GetLogger()

	// 1. 查找要更新的实体
	dictType, err := s.getDictTypeRepo().FindByID(ctx, id)
	if err != nil {
		log.WithError(err).Warn(op + ": 字典类型未找到")
		return nil, err // Repository 层应返回包装好的错误 (NotFound)
	}

	// 2. 更新字段 (只更新 DTO 中非 nil 的字段)
	updated := false
	if req.Name != "" && req.Name != dictType.Name { // Assuming Name is string and not a pointer in DTO
		dictType.Name = req.Name
		updated = true
	}
	if req.Status != nil && *req.Status != dictType.Status {
		dictType.Status = *req.Status
		updated = true
		// 注意：禁用字典类型时，可能需要考虑禁用其下的字典项？(业务逻辑)
		// if *req.Status == 0 { ... }
	}
	if req.Remark != dictType.Remark { // Assuming Remark is string and not a pointer in DTO
		dictType.Remark = req.Remark
		updated = true
	}

	// 新增：处理 IsSystem 字段的更新
	// 规则：一旦 IsSystem 为 true，则不允许修改为 false。
	//       只允许在 IsSystem 为 false 时，通过请求将其设置为 true (需要权限校验，暂时简化为允许)
	if req.IsSystem != nil { // 如果请求中包含 IsSystem
		if dictType.IsSystem && !(*req.IsSystem) {
			// 如果原本是系统内置，但请求想把它改成非系统内置，则报错
			log.Warnf(op+": 尝试将系统内置字典类型 '%s' (ID: %d) 修改为非系统内置", dictType.Code, dictType.ID)
			return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "不允许将系统内置字典类型修改为非系统内置")
		}
		if !dictType.IsSystem && *req.IsSystem { // 如果原本非系统内置，请求想把它改成系统内置
			// TODO: 此处未来可能需要更复杂的权限校验，例如只有超级管理员才能将普通类型标记为系统内置
			// 暂时允许，但记录日志
			log.Infof(op+": 将字典类型 '%s' (ID: %d) 标记为系统内置", dictType.Code, dictType.ID)
			dictType.IsSystem = true
			updated = true
		} else if dictType.IsSystem && *req.IsSystem { // 如果原本是系统内置，请求也是系统内置，则不视为更新（保持不变）
			// No change needed, IsSystem remains true
		} else if !dictType.IsSystem && !*req.IsSystem { // 如果原本非系统内置，请求也是非系统内置，不视为更新
			// No change needed, IsSystem remains false
		}
	}

	// 假设 TenantEntity 的 UpdatedBy 由 BaseRepository 或 GORM Hook 处理

	// Set UpdatedBy before updating
	uid64Update, errUpdate := util.GetUserIDFromStdContext(ctx) // 使用 util.GetUserIDFromStdContext
	if errUpdate != nil {
		log.WithError(errUpdate).Warn(op + ": UpdateDictType: 无法从上下文中获取UserID")
	}
	dictType.UpdatedBy = uint(uid64Update)

	// 3. 如果有更新，则调用 Repository 更新
	if updated {
		// 使用 Update 方法，它会调用 Save 来触发 Hook
		if err := s.getDictTypeRepo().Update(ctx, dictType); err != nil {
			log.WithError(err).Error(op + ": 更新字典类型失败")
			return nil, err
		}
		// TODO: 清除与此字典类型相关的缓存 (如果实现缓存)
		// cache.Delete(fmt.Sprintf("dict_type_%s", dictType.Code))
		// cache.Delete(fmt.Sprintf("dict_items_%s", dictType.Code))
		// cache.Delete("all_enabled_dict_types")
	}

	// 4. 转换并返回 VO
	return s.ConvertToDictTypeVO(ctx, dictType)
}

func (s *dictionaryServiceImpl) DeleteDictType(ctx context.Context, id uint) error {
	op := "Service.DeleteDictType"
	log := s.GetLogger()

	// 1. (可选) 查找实体以获取 Code 用于清除缓存，并检查是否存在
	dictType, err := s.getDictTypeRepo().FindByID(ctx, id)
	if err != nil {
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			log.Warn(op + ": 字典类型未找到，无需删除")
			return nil // 或者根据需要返回 NotFound 错误
		}
		log.WithError(err).Error(op + ": 查询字典类型失败")
		return err
	}

	// 新增：检查是否为系统内置字典类型
	if dictType.IsSystem {
		log.Warnf(op+": 尝试删除系统内置字典类型 '%s' (ID: %d)", dictType.Code, id)
		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "系统内置字典类型不允许删除")
	}

	// 2. 调用 Repository 删除 (Repository 的 BeforeDelete Hook 会检查是否有子项)
	err = s.getDictTypeRepo().Delete(ctx, id)
	if err != nil {
		// 需要区分是 "存在子项不能删除" 的业务错误还是其他数据库错误
		// GORM V2 的 Hook 错误可能不会直接传递文本，需要改进错误处理方式
		// 暂时依赖于 Repository 返回的错误类型或文本
		if strings.Contains(err.Error(), "无法删除：该字典类型下存在字典项") { // 检查 BeforeDelete 返回的错误信息
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, err.Error())
		}
		log.WithError(err).Error(op + ": 删除字典类型失败")
		return err // 返回 Repository 层的错误
	}

	// TODO: 清除与此字典类型相关的缓存 (如果实现缓存)
	if dictType != nil {
		// cache.Delete(fmt.Sprintf("dict_type_%s", dictType.Code))
		// cache.Delete(fmt.Sprintf("dict_items_%s", dictType.Code))
		// cache.Delete("all_enabled_dict_types")
	}

	return nil
}

func (s *dictionaryServiceImpl) GetDictTypeByID(ctx context.Context, id uint) (*vo.DictionaryTypeVO, error) {
	op := "Service.GetDictTypeByID"
	log := s.GetLogger()

	dictType, err := s.getDictTypeRepo().FindByID(ctx, id)
	if err != nil {
		log.WithError(err).Warn(op + ": 查询字典类型失败")
		return nil, err // Repository 层应返回包装好的错误 (NotFound 或 QueryFailed)
	}

	return s.ConvertToDictTypeVO(ctx, dictType)
}

func (s *dictionaryServiceImpl) PageDictTypes(ctx context.Context, query dto.DictTypePageQueryDTO) (*vo.PageDictTypeVO, error) {
	op := "Service.PageDictTypes"
	log := s.GetLogger()

	entities, total, err := s.getDictTypeRepo().FindPage(ctx, query)
	if err != nil {
		log.WithError(err).Error(op + ": 分页查询字典类型失败")
		return nil, err // Repository 层应返回包装好的错误
	}

	voList := make([]*vo.DictionaryTypeVO, 0, len(entities))
	for _, entity := range entities {
		voItem, errConv := s.ConvertToDictTypeVO(ctx, entity)
		if errConv != nil {
			// 选择记录日志并跳过，或直接返回错误
			log.WithError(errConv).Error(op + ": 转换字典类型VO失败")
			continue // 或者 return nil, errConv
		}
		voList = append(voList, voItem)
	}

	// PageNum 和 PageSize 来自 query.PageQuery，由 Controller 填充
	pageNum := query.PageQuery.PageNum
	pageSize := query.PageQuery.PageSize
	// 此处不再需要设置默认值，Controller的BuildPageQuery已处理

	pageResult := &vo.PageDictTypeVO{
		PageVO: vo.PageVO{
			List:     make([]interface{}, 0),
			Total:    total,
			PageNum:  pageNum,
			PageSize: pageSize,
			Pages:    query.PageQuery.Pages, // 使用来自 PageQuery 的总页数 (如果已计算)
		},
		List: voList,
	}
	if pageResult.PageVO.Pages == 0 && pageSize > 0 { // 如果控制器未计算总页数，则在此计算
		pageResult.PageVO.Pages = (int(total) + pageSize - 1) / pageSize
	}

	return pageResult, nil
}

func (s *dictionaryServiceImpl) ListEnabledDictTypes(ctx context.Context) ([]*vo.DictionaryTypeSimpleVO, error) {
	op := "Service.ListEnabledDictTypes"
	log := s.GetLogger()
	// cacheKey := "all_enabled_dict_types" // 移除未使用的变量

	// TODO: 优先从缓存获取?
	// if cachedData, found := cache.Get(cacheKey); found { return cachedData.([]*vo.DictionaryTypeSimpleVO), nil }

	entities, err := s.getDictTypeRepo().FindAllEnabled(ctx)
	if err != nil {
		log.WithError(err).Error(op + ": 查询所有启用字典类型失败")
		return nil, err
	}

	voList := make([]*vo.DictionaryTypeSimpleVO, 0, len(entities))
	for _, entity := range entities {
		voList = append(voList, &vo.DictionaryTypeSimpleVO{
			ID:   entity.ID,
			Code: entity.Code,
			Name: entity.Name,
		})
	}

	// TODO: 查询结果存入缓存?
	// cache.Set(cacheKey, voList, time.Hour)

	return voList, nil
}

// --- 字典项管理 ---

func (s *dictionaryServiceImpl) CreateDictItem(ctx context.Context, req dto.DictItemCreateOrUpdateDTO) (*vo.DictionaryItemVO, error) {
	op := "Service.CreateDictItem"
	log := s.GetLogger()

	// 1. 检查 DictionaryTypeID 是否有效
	_, err := s.getDictTypeRepo().FindByID(ctx, req.DictionaryTypeID)
	if err != nil {
		log.WithError(err).Warn(op + ": 字典类型不存在")
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("字典类型ID %d 不存在", req.DictionaryTypeID))
		}
		return nil, err // 其他查询错误
	}

	// 2. 检查 Value 和 Label 在同一类型下是否唯一
	valueExists, err := s.getDictItemRepo().ExistsByValue(ctx, req.DictionaryTypeID, req.Value, 0)
	if err != nil {
		log.WithError(err).Error(op + ": 检查字典项值唯一性失败")
		return nil, err
	}
	if valueExists {
		return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("当前字典类型下已存在值 '%s'", req.Value))
	}
	labelExists, err := s.getDictItemRepo().ExistsByLabel(ctx, req.DictionaryTypeID, req.Label, 0)
	if err != nil {
		log.WithError(err).Error(op + ": 检查字典项标签唯一性失败")
		return nil, err
	}
	if labelExists {
		return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("当前字典类型下已存在标签 '%s'", req.Label))
	}

	// 3. 创建实体
	dictItem := &entity.DictionaryItem{
		DictionaryTypeID: req.DictionaryTypeID,
		Label:            req.Label,
		Value:            req.Value,
		SortOrder:        0,     // 默认排序值
		Status:           1,     // 默认状态
		IsSystem:         false, // 默认非系统
		Remark:           req.Remark,
	}
	if req.SortOrder != nil {
		dictItem.SortOrder = *req.SortOrder
	}
	if req.Status != nil {
		dictItem.Status = *req.Status
	}
	if req.IsSystem != nil {
		dictItem.IsSystem = *req.IsSystem
	}
	// 假设 BaseEntity 的 CreatedBy/UpdatedBy 由 Hook 或上层处理

	// Set CreatedBy and UpdatedBy
	uid64ItemCreate, errItemCreate := util.GetUserIDFromStdContext(ctx) // 使用 util.GetUserIDFromStdContext
	if errItemCreate != nil {
		log.WithError(errItemCreate).Warn(op + ": CreateDictItem: 无法从上下文中获取UserID")
	}
	dictItem.CreatedBy = uint(uid64ItemCreate)
	dictItem.UpdatedBy = uint(uid64ItemCreate)

	// 4. 调用 Repository 创建
	if err := s.getDictItemRepo().Create(ctx, dictItem); err != nil {
		log.WithError(err).Error(op + ": 创建字典项失败")
		return nil, err
	}

	// TODO: 清除关联字典类型的缓存 (如果实现缓存)
	// dictType, _ := s.getDictTypeRepo().FindByID(ctx, req.DictionaryTypeID) // 需要查询类型获取 Code
	// if dictType != nil { cache.Delete(fmt.Sprintf("dict_items_%s", dictType.Code)) }

	// 5. 转换并返回 VO
	return s.ConvertToDictItemVO(ctx, dictItem)
}

func (s *dictionaryServiceImpl) UpdateDictItem(ctx context.Context, id uint, req dto.DictItemCreateOrUpdateDTO) (*vo.DictionaryItemVO, error) {
	op := "Service.UpdateDictItem"
	log := s.GetLogger()
	// ---- Log Entry ----
	log.Info(fmt.Sprintf("[%s ENTRY] ItemID: %d, ReqIsSystem Ptr: %p", op, id, req.IsSystem))
	if req.IsSystem != nil {
		log.Info(fmt.Sprintf("[%s REQ_IS_SYSTEM_VAL] Value: %t", op, *req.IsSystem))
	}

	// 1. 查找要更新的实体
	dictItem, err := s.getDictItemRepo().FindByID(ctx, id)
	if err != nil {
		log.WithError(err).Warn(op + ": 字典项未找到")
		return nil, err
	}
	// ---- Log Item Found ----
	log.Info(fmt.Sprintf("[%s ITEM_FOUND] ItemID: %d, CurrentIsSystem: %t", op, id, dictItem.IsSystem))

	// 2. 检查唯一性 (Value, Label)
	updated := false
	if req.Value != "" && req.Value != dictItem.Value { // Assuming Value is string and not pointer in DTO
		valueExists, errCheck := s.getDictItemRepo().ExistsByValue(ctx, dictItem.DictionaryTypeID, req.Value, id)
		if errCheck != nil {
			log.WithError(errCheck).Error(op + ": 检查字典项值唯一性失败")
			return nil, errCheck
		}
		if valueExists {
			return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("当前字典类型下已存在值 '%s'", req.Value))
		}
		dictItem.Value = req.Value
		updated = true
	}
	if req.Label != "" && req.Label != dictItem.Label { // Assuming Label is string and not pointer in DTO
		labelExists, errCheck := s.getDictItemRepo().ExistsByLabel(ctx, dictItem.DictionaryTypeID, req.Label, id)
		if errCheck != nil {
			log.WithError(errCheck).Error(op + ": 检查字典项标签唯一性失败")
			return nil, errCheck
		}
		if labelExists {
			return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("当前字典类型下已存在标签 '%s'", req.Label))
		}
		dictItem.Label = req.Label
		updated = true
	}

	// 3. 更新其他字段 (SortOrder, Status)
	if req.SortOrder != nil && *req.SortOrder != dictItem.SortOrder {
		dictItem.SortOrder = *req.SortOrder
		updated = true
	}
	if req.Status != nil && *req.Status != dictItem.Status {
		dictItem.Status = *req.Status
		updated = true
	}

	// --- IsSystem 检查 ---
	// ---- Log Before IsSystem Check ----
	log.Info(fmt.Sprintf("[%s BEFORE_IS_SYSTEM_CHECK] ItemID: %d, ReqIsSystem Ptr: %p, CurrentIsSystem: %t", op, id, req.IsSystem, dictItem.IsSystem))
	if req.IsSystem != nil && *req.IsSystem != dictItem.IsSystem {
		// ---- Log IsSystem Changed ----
		log.Info(fmt.Sprintf("[%s IS_SYSTEM_CHANGED] ItemID: %d, From: %t, To: %t", op, id, dictItem.IsSystem, *req.IsSystem))

		if dictItem.IsSystem && !*req.IsSystem {
			log.Info(fmt.Sprintf("[%s ADMIN_CHECK_ENTER] ItemID: %d", op, id))
			isAdmin := false

			userIDValue := ctx.Value(constant.CONTEXT_USER_ID)

			if userIDValue == nil {
				log.Warn(fmt.Sprintf("[%s ADMIN_CHECK_WARN] Context UserID not found (Key: %s)", op, constant.CONTEXT_USER_ID))
			} else {
				log.Info(fmt.Sprintf("[%s ADMIN_CHECK_CTX_VAL] UserIDValue Type: %T, Value: %v", op, userIDValue, userIDValue))

				userID, ok := userIDValue.(uint64)

				if ok && userID > 0 {
					log.Info(fmt.Sprintf("[%s ADMIN_CHECK_USERID_OK] Parsed UserID: %d", op, userID))

					sm := s.GetServiceManager()

					if sm == nil {
						log.Warn(fmt.Sprintf("[%s ADMIN_CHECK_WARN] ServiceManager is nil!", op))
					} else {
						userService := sm.GetUserService()

						if userService == nil {
							log.Warn(fmt.Sprintf("[%s ADMIN_CHECK_WARN] UserService is nil!", op))
						} else {
							user, userErr := userService.GetUserByID(ctx, uint(userID))

							if userErr != nil {
								log.Warn(fmt.Sprintf("[%s ADMIN_CHECK_WARN] GetUserByID failed: %v", op, userErr))
							} else if user == nil {
								log.Warn(fmt.Sprintf("[%s ADMIN_CHECK_WARN] GetUserByID returned nil user", op))
							} else {
								log.Info(fmt.Sprintf("[%s ADMIN_CHECK_USER_FOUND] User IsAdmin: %t", op, user.IsAdmin))
								if user.IsAdmin {
									isAdmin = true
								}
							}
						}
					}
				} else {
					log.Warn(fmt.Sprintf("[%s ADMIN_CHECK_WARN] Failed to parse UserID from context value or ID is zero", op))
				}
			}

			// ---- Log Admin Check Result ----
			log.Info(fmt.Sprintf("[%s ADMIN_CHECK_RESULT] IsAdmin: %t", op, isAdmin))
			if !isAdmin {
				log.Warn(fmt.Sprintf("[%s ADMIN_CHECK_FAIL] !isAdmin is true, returning error", op))
				return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "不允许将非系统字典项改为系统内置")
			}
			log.Info(fmt.Sprintf("[%s ADMIN_CHECK_PASS] Admin allowed change. ItemID: %d", op, id))
		}

		dictItem.IsSystem = *req.IsSystem
		updated = true
	}

	if req.Remark != dictItem.Remark { // Assuming Remark is string and not pointer in DTO
		dictItem.Remark = req.Remark
		updated = true
	}
	// 假设 BaseEntity 的 UpdatedBy 由 Hook 或上层处理

	// Set UpdatedBy before updating
	uid64ItemUpdate, errItemUpdate := util.GetUserIDFromStdContext(ctx) // 使用 util.GetUserIDFromStdContext
	if errItemUpdate != nil {
		log.WithError(errItemUpdate).Warn(op + ": UpdateDictItem: 无法从上下文中获取UserID")
	}
	dictItem.UpdatedBy = uint(uid64ItemUpdate)

	// 4. 调用 Repository 更新 (仅当有字段变更时)
	if updated {
		if err := s.getDictItemRepo().Update(ctx, dictItem); err != nil {
			log.WithError(err).Error(op + ": 更新字典项失败")
			return nil, err
		}

		// TODO: 清除关联字典类型的缓存 (如果实现缓存)
		dictType, _ := s.getDictTypeRepo().FindByID(ctx, dictItem.DictionaryTypeID)
		if dictType != nil {
			// cache.Delete(fmt.Sprintf("dict_items_%s", dictType.Code))
		}
	}

	// 5. 转换并返回 VO
	return s.ConvertToDictItemVO(ctx, dictItem)
}

func (s *dictionaryServiceImpl) DeleteDictItem(ctx context.Context, id uint) error {
	op := "Service.DeleteDictItem"
	log := s.GetLogger()

	// 1. (可选) 查找实体以获取信息用于清除缓存，并检查是否系统内置
	dictItem, err := s.getDictItemRepo().FindByID(ctx, id)
	if err != nil {
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			log.Warn(op + ": 字典项未找到，无需删除")
			return nil // 或根据需要返回错误
		}
		log.WithError(err).Error(op + ": 查询字典项失败")
		return err
	}
	if dictItem.IsSystem {
		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "系统内置字典项不允许删除")
	}

	// 2. 调用 Repository 删除
	err = s.getDictItemRepo().Delete(ctx, id)
	if err != nil {
		log.WithError(err).Error(op + ": 删除字典项失败")
		return err
	}

	// TODO: 清除关联字典类型的缓存 (如果实现缓存)
	if dictItem != nil {
		dictType, _ := s.getDictTypeRepo().FindByID(ctx, dictItem.DictionaryTypeID)
		if dictType != nil {
			// cache.Delete(fmt.Sprintf("dict_items_%s", dictType.Code))
		}
	}

	return nil
}

func (s *dictionaryServiceImpl) GetDictItemByID(ctx context.Context, id uint) (*vo.DictionaryItemVO, error) {
	op := "Service.GetDictItemByID"
	log := s.GetLogger()

	dictItem, err := s.getDictItemRepo().FindByID(ctx, id)
	if err != nil {
		log.WithError(err).Warn(op + ": 查询字典项失败")
		return nil, err
	}

	return s.ConvertToDictItemVO(ctx, dictItem)
}

func (s *dictionaryServiceImpl) PageDictItems(ctx context.Context, query dto.DictItemPageQueryDTO) (*vo.PageDictItemVO, error) {
	op := "Service.PageDictItems"
	log := s.GetLogger()

	entities, total, err := s.getDictItemRepo().FindPage(ctx, query)
	if err != nil {
		log.WithError(err).Error(op + ": 分页查询字典项失败")
		return nil, err
	}

	voList := make([]*vo.DictionaryItemVO, 0, len(entities))
	// 优化：如果需要显示 TypeCode/TypeName，可以先批量查询关联的 Type
	// typeIDs := make([]uint, 0)
	// for _, item := range entities { if item != nil { typeIDs = append(typeIDs, item.DictionaryTypeID) } }
	// typeMap := make(map[uint]*entity.DictionaryType)
	// if len(typeIDs) > 0 { /* query types by typeIDs ... build map */ }
	for _, entity := range entities {
		if entity == nil {
			continue
		} // Defensive check
		voItem, errConv := s.ConvertToDictItemVO(ctx, entity)
		if errConv != nil {
			log.WithError(errConv).Error(op + ": 转换字典项VO失败")
			continue
		}
		// if typeInfo, ok := typeMap[entity.DictionaryTypeID]; ok { voItem.DictionaryTypeCode = typeInfo.Code ... }
		voList = append(voList, voItem)
	}

	// PageNum 和 PageSize 来自 query.PageQuery，由 Controller 填充
	pageNum := query.PageQuery.PageNum
	pageSize := query.PageQuery.PageSize
	// 此处不再需要设置默认值，Controller的BuildPageQuery已处理

	pageResult := &vo.PageDictItemVO{
		PageVO: vo.PageVO{
			List:     make([]interface{}, 0),
			Total:    total,
			PageNum:  pageNum,
			PageSize: pageSize,
			Pages:    query.PageQuery.Pages, // 使用来自 PageQuery 的总页数 (如果已计算)
		},
		List: voList,
	}
	if pageResult.PageVO.Pages == 0 && pageSize > 0 { // 如果控制器未计算总页数，则在此计算
		pageResult.PageVO.Pages = (int(total) + pageSize - 1) / pageSize
	}

	return pageResult, nil
}

// --- 字典数据获取 (核心) ---

func (s *dictionaryServiceImpl) GetDictItemsByTypeCode(ctx context.Context, typeCode string) ([]*vo.DictDataVO, error) {
	op := "Service.GetDictItemsByTypeCode"
	log := s.GetLogger()
	// cacheKey := fmt.Sprintf("dict_items_%s", typeCode) // 移除未使用的变量

	// TODO: 1. 尝试从缓存获取
	// if cachedData, found := cache.Get(cacheKey); found {
	//    if data, ok := cachedData.([]*vo.DictDataVO); ok {
	//        log.Info(op + ": 从缓存获取数据")
	//        return data, nil
	//    } else {
	//        log.Warn(op + ": 缓存数据类型错误")
	//        cache.Delete(cacheKey) // Delete invalid cache entry
	//    }
	// }

	// 2. 从 Repository 获取 (只获取启用状态的)
	// 使用 FindByTypeCode 更直接
	dictItems, err := s.getDictItemRepo().FindByTypeCode(ctx, typeCode, true) // true for onlyEnabled
	if err != nil {
		log.WithError(err).Error(op + ": 按类型编码查询字典项失败")
		return nil, err
	}

	// 3. 转换为 DictDataVO 列表 (已按 SortOrder, ID 排序)
	voList, errConv := s.ConvertToDictDataVOList(ctx, dictItems)
	if errConv != nil {
		log.WithError(errConv).Error(op + ": 转换 DictDataVO 失败")
		// 即使转换失败，仍然返回从数据库获取的数据（不完美的VO）
		// 或者返回错误： return nil, errConv
	}

	// TODO: 4. 将结果存入缓存
	// if err := cache.Set(cacheKey, voList, time.Hour); err != nil {
	//     log.WithError(err).Error(op + ": 设置字典项缓存失败")
	// }

	return voList, nil // 返回转换后的列表，即使转换可能有错（或在上面返回 errConv）
}

// --- 辅助转换方法 ---

func (s *dictionaryServiceImpl) ConvertToDictTypeVO(ctx context.Context, dictType *entity.DictionaryType) (*vo.DictionaryTypeVO, error) {
	if dictType == nil {
		return nil, nil
	}
	vo := &vo.DictionaryTypeVO{
		BaseVO: vo.BaseVO{ // 假设 BaseVO 包含 ID, CreatedAt, UpdatedAt 等
			ID:        dictType.ID,
			CreatedAt: dictType.CreatedAt,
			UpdatedAt: dictType.UpdatedAt,
			CreatedBy: dictType.CreatedBy, // 假设 TenantEntity 包含这些
			UpdatedBy: dictType.UpdatedBy,
		},
		Code:     dictType.Code,
		Name:     dictType.Name,
		Status:   dictType.Status,
		IsSystem: dictType.IsSystem,
		Remark:   dictType.Remark,
		// ItemCount: 0, // 默认为0，如果需要，在此处或调用处查询
	}
	// 示例：查询 ItemCount (可能影响性能，按需添加)
	// count, err := s.getDictItemRepo().CountByTypeID(ctx, dictType.ID)
	// if err == nil { vo.ItemCount = count }

	return vo, nil
}

func (s *dictionaryServiceImpl) ConvertToDictItemVO(ctx context.Context, dictItem *entity.DictionaryItem) (*vo.DictionaryItemVO, error) {
	if dictItem == nil {
		return nil, nil
	}
	vo := &vo.DictionaryItemVO{
		BaseVO: vo.BaseVO{
			ID:        dictItem.ID,
			CreatedAt: dictItem.CreatedAt,
			UpdatedAt: dictItem.UpdatedAt,
			CreatedBy: dictItem.CreatedBy, // 假设 BaseEntity 包含这些
			UpdatedBy: dictItem.UpdatedBy,
		},
		DictionaryTypeID: dictItem.DictionaryTypeID,
		Label:            dictItem.Label,
		Value:            dictItem.Value,
		SortOrder:        dictItem.SortOrder,
		Status:           dictItem.Status,
		IsSystem:         dictItem.IsSystem,
		Remark:           dictItem.Remark,
	}
	// 可选: 填充关联的 TypeCode/TypeName
	// dictType, err := s.getDictTypeRepo().FindByID(ctx, dictItem.DictionaryTypeID)
	// if err == nil && dictType != nil {
	//     vo.DictionaryTypeCode = dictType.Code
	//     vo.DictionaryTypeName = dictType.Name
	// }
	return vo, nil
}

func (s *dictionaryServiceImpl) ConvertToDictDataVOList(ctx context.Context, dictItems []*entity.DictionaryItem) ([]*vo.DictDataVO, error) {
	if dictItems == nil {
		return []*vo.DictDataVO{}, nil
	}
	voList := make([]*vo.DictDataVO, 0, len(dictItems))
	for _, item := range dictItems {
		if item != nil { // 防御性检查
			voList = append(voList, &vo.DictDataVO{
				Label: item.Label,
				Value: item.Value,
			})
		}
	}
	// 因为 Repository 查询时已排序，这里不需要再排序
	return voList, nil
}
