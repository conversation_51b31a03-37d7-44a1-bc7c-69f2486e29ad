package dto

import (
	"backend/pkg/response"
	"time"
)

// WmsBlindReceivingConfigCreateReq 创建盲收配置请求
type WmsBlindReceivingConfigCreateReq struct {
	ConfigLevel          string   `json:"configLevel" validate:"required,oneof=SYSTEM WAREHOUSE CLIENT USER"` // 配置层级
	ConfigTargetID       *uint    `json:"configTargetId"`                                                     // 目标ID
	Strategy             string   `json:"strategy" validate:"required,oneof=STRICT SUPPLEMENT FULL"`          // 盲收策略
	SupplementTimeLimit  *int     `json:"supplementTimeLimit" validate:"omitempty,min=1,max=720"`             // 补录时限（小时，1小时到30天）
	RequiresApproval     bool     `json:"requiresApproval"`                                                   // 是否需要审批
	ApprovalUserRoles    *string  `json:"approvalUserRoles"`                                                  // 审批用户角色
	MaxBlindReceivingQty *float64 `json:"maxBlindReceivingQty" validate:"omitempty,min=0"`                    // 最大盲收数量限制
	IsActive             bool     `json:"isActive"`                                                           // 是否启用
	Priority             int      `json:"priority" validate:"min=1,max=10"`                                   // 优先级
}

// WmsBlindReceivingConfigUpdateReq 更新盲收配置请求
type WmsBlindReceivingConfigUpdateReq struct {
	Strategy             *string  `json:"strategy" validate:"omitempty,oneof=STRICT SUPPLEMENT FULL"` // 盲收策略
	SupplementTimeLimit  *int     `json:"supplementTimeLimit" validate:"omitempty,min=1,max=720"`     // 补录时限
	RequiresApproval     *bool    `json:"requiresApproval"`                                           // 是否需要审批
	ApprovalUserRoles    *string  `json:"approvalUserRoles"`                                          // 审批用户角色
	MaxBlindReceivingQty *float64 `json:"maxBlindReceivingQty" validate:"omitempty,min=0"`            // 最大盲收数量限制
	IsActive             *bool    `json:"isActive"`                                                   // 是否启用
	Priority             *int     `json:"priority" validate:"omitempty,min=1,max=10"`                 // 优先级
}

// WmsBlindReceivingConfigQueryReq 查询盲收配置请求
type WmsBlindReceivingConfigQueryReq struct {
	ConfigLevel    *string `json:"configLevel,omitempty" form:"configLevel"`       // 配置层级
	ConfigTargetID *uint   `json:"configTargetId,omitempty" form:"configTargetId"` // 目标ID
	Strategy       *string `json:"strategy,omitempty" form:"strategy"`             // 盲收策略
	IsActive       *bool   `json:"isActive,omitempty" form:"isActive"`             // 是否启用
	SortField      string  `json:"sortField,omitempty" form:"sortField"`           // 排序字段
	SortOrder      string  `json:"sortOrder,omitempty" form:"sortOrder"`           // 排序顺序

	Pagination response.PageQuery `json:"-"` // 分页参数，不参与JSON序列化
}

// WmsBlindReceivingConfigBatchCreateReq 批量创建盲收配置请求
type WmsBlindReceivingConfigBatchCreateReq struct {
	Configs []WmsBlindReceivingConfigCreateReq `json:"configs" validate:"required,min=1,max=100,dive"` // 配置列表
}

// WmsBlindReceivingConfigBatchUpdateReq 批量更新盲收配置请求
type WmsBlindReceivingConfigBatchUpdateReq struct {
	Updates []WmsBlindReceivingConfigBatchUpdateItem `json:"updates" validate:"required,min=1,max=100,dive"` // 更新列表
}

// WmsBlindReceivingConfigBatchUpdateItem 批量更新项
type WmsBlindReceivingConfigBatchUpdateItem struct {
	ID         uint                             `json:"id" validate:"required"`         // 配置ID
	UpdateData WmsBlindReceivingConfigUpdateReq `json:"updateData" validate:"required"` // 更新数据
}

// GetEffectiveConfigReq 获取有效配置请求
type GetEffectiveConfigReq struct {
	WarehouseID uint  `json:"warehouseId" form:"warehouseId" validate:"required"` // 仓库ID
	ClientID    *uint `json:"clientId" form:"clientId"`                           // 客户ID
	UserID      *uint `json:"userId" form:"userId"`                               // 用户ID
}

// BlindReceivingValidationReq 盲收验证请求
type BlindReceivingValidationReq struct {
	WarehouseID uint    `json:"warehouseId" validate:"required"`    // 仓库ID
	ClientID    *uint   `json:"clientId"`                           // 客户ID
	UserID      *uint   `json:"userId"`                             // 用户ID
	ItemID      uint    `json:"itemId" validate:"required"`         // 物料ID
	Quantity    float64 `json:"quantity" validate:"required,min=0"` // 数量
}

// BlindReceivingValidationResp 盲收验证响应
type BlindReceivingValidationResp struct {
	IsAllowed          bool       `json:"isAllowed"`          // 是否允许盲收
	Strategy           string     `json:"strategy"`           // 应用的策略
	RequiresApproval   bool       `json:"requiresApproval"`   // 是否需要审批
	MaxQuantityLimit   *float64   `json:"maxQuantityLimit"`   // 最大数量限制
	SupplementDeadline *time.Time `json:"supplementDeadline"` // 补录截止时间
	ValidationMessage  string     `json:"validationMessage"`  // 验证消息
	ConfigLevel        string     `json:"configLevel"`        // 生效的配置级别
	ApprovalUserRoles  []string   `json:"approvalUserRoles"`  // 审批用户角色 - 修复：改为数组类型
}

// BlindReceivingApprovalReq 盲收审批请求
type BlindReceivingApprovalReq struct {
	ReceivingRecordID uint   `json:"receivingRecordId" validate:"required"`                   // 收货记录ID
	ApprovalAction    string `json:"approvalAction" validate:"required,oneof=APPROVE REJECT"` // 审批动作
	ApprovalNotes     string `json:"approvalNotes" validate:"max=500"`                        // 审批备注
}

// BlindReceivingSupplementReq 盲收补录请求
type BlindReceivingSupplementReq struct {
	ReceivingRecordID uint   `json:"receivingRecordId" validate:"required"` // 收货记录ID
	SupplierID        uint   `json:"supplierId" validate:"required"`        // 供应商ID
	ExpectedDelivery  string `json:"expectedDelivery" validate:"required"`  // 预期交付时间
	PurchaseOrderNo   string `json:"purchaseOrderNo" validate:"max=100"`    // 采购订单号
	SupplementNotes   string `json:"supplementNotes" validate:"max=500"`    // 补录备注
}

// BlindReceivingStatsReq 盲收统计请求
type BlindReceivingStatsReq struct {
	WarehouseID uint       `json:"warehouseId" form:"warehouseId" validate:"required"` // 仓库ID
	ClientID    *uint      `json:"clientId" form:"clientId"`                           // 客户ID
	StartDate   *time.Time `json:"startDate" form:"startDate"`                         // 开始日期
	EndDate     *time.Time `json:"endDate" form:"endDate"`                             // 结束日期
	GroupBy     string     `json:"groupBy" form:"groupBy"`                             // 分组方式: DAY/WEEK/MONTH
}

// BlindReceivingStatsResp 盲收统计响应
type BlindReceivingStatsResp struct {
	TotalBlindReceivings   int                       `json:"totalBlindReceivings"`   // 总盲收次数
	TotalBlindQuantity     float64                   `json:"totalBlindQuantity"`     // 总盲收数量
	ApprovedCount          int                       `json:"approvedCount"`          // 审批通过数量
	RejectedCount          int                       `json:"rejectedCount"`          // 审批拒绝数量
	PendingApprovalCount   int                       `json:"pendingApprovalCount"`   // 待审批数量
	SupplementedCount      int                       `json:"supplementedCount"`      // 已补录数量
	PendingSupplementCount int                       `json:"pendingSupplementCount"` // 待补录数量
	OverdueSupplementCount int                       `json:"overdueSupplementCount"` // 逾期补录数量
	AverageProcessingTime  float64                   `json:"averageProcessingTime"`  // 平均处理时间（小时）
	StrategyDistribution   map[string]int            `json:"strategyDistribution"`   // 策略分布
	DailyStats             []BlindReceivingDailyStat `json:"dailyStats"`             // 日统计
}

// BlindReceivingDailyStat 盲收日统计
type BlindReceivingDailyStat struct {
	Date     string  `json:"date"`     // 日期
	Count    int     `json:"count"`    // 盲收次数
	Quantity float64 `json:"quantity"` // 盲收数量
	Strategy string  `json:"strategy"` // 主要策略
}

// ConfigLevelTarget 配置级别目标信息
type ConfigLevelTarget struct {
	Level      string `json:"level"`      // 级别
	TargetID   *uint  `json:"targetId"`   // 目标ID
	TargetName string `json:"targetName"` // 目标名称
}

// AvailableConfigTargetsReq 可用配置目标请求
type AvailableConfigTargetsReq struct {
	ConfigLevel string `json:"configLevel" form:"configLevel" validate:"required,oneof=WAREHOUSE CLIENT USER"` // 配置层级
}

// AvailableConfigTargetsResp 可用配置目标响应
type AvailableConfigTargetsResp struct {
	Targets []ConfigLevelTarget `json:"targets"` // 目标列表
}
