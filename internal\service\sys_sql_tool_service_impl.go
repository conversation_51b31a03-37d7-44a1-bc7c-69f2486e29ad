package service

import (
	"context"
	"database/sql" // 导入标准库 database/sql
	"fmt"
	"strings"
	"time"

	"backend/internal/model/vo"
	"backend/pkg/constant"         // <<< Import constant package
	apperrors "backend/pkg/errors" // 导入自定义错误
	"backend/pkg/logger"
	// 确保你的 ServiceManager 或其他依赖注入方式能够提供 *sql.DB
	// "backend/pkg/database" // 假设有一个包可以获取 *sql.DB
)

// SQLToolService 接口定义
type SQLToolService interface {
	Execute(ctx context.Context, userID uint, sqlQuery string) (*vo.SQLResultVO, error)
}

// sqlToolServiceImpl 实现
type sqlToolServiceImpl struct {
	*BaseServiceImpl
	db  *sql.DB // 直接依赖标准的 *sql.DB
	log logger.Logger
}

// NewSQLToolService 创建 SQLToolService 实例
// 注意：这里需要能够获取到 *sql.DB 实例
func NewSQLToolService(serviceManager *ServiceManager, db *sql.DB) SQLToolService {
	if db == nil {
		serviceManager.GetLogger().Fatal("NewSQLToolService: *sql.DB is required")
	}
	return &sqlToolServiceImpl{
		BaseServiceImpl: NewBaseService(serviceManager),
		db:              db,
		log:             serviceManager.GetLogger().WithField("service", "SQLToolService"),
	}
}

// GetSQLToolService 从服务管理器获取 SQLToolService
// 注意：这里需要调整以确保 *sql.DB 被正确注入
func GetSQLToolService(serviceManager *ServiceManager) SQLToolService {
	// 这个函数的实现依赖于你的 ServiceManager 如何管理 *sql.DB
	// 假设 ServiceManager 有一个方法 GetSQLDB()
	// 如果没有，你需要修改 ServiceManager 或应用初始化代码
	sqlDB := serviceManager.GetSQLDB() // 假设存在此方法
	if sqlDB == nil {
		serviceManager.GetLogger().Fatal("GetSQLToolService: Failed to get *sql.DB from ServiceManager")
		return nil // 或者 panic
	}

	return GetService[SQLToolService](
		serviceManager,
		func(sm *ServiceManager) SQLToolService {
			return NewSQLToolService(sm, sqlDB)
		},
	)
}

// Execute 执行 SQL 语句
func (s *sqlToolServiceImpl) Execute(ctx context.Context, userID uint, sqlQuery string) (*vo.SQLResultVO, error) {
	startTime := time.Now()
	trimmedSQL := strings.TrimSpace(sqlQuery)
	resultVO := &vo.SQLResultVO{
		ExecutedSQL: trimmedSQL,
	}

	// --- 审计日志 (执行前) ---
	s.log.Warn(fmt.Sprintf("Admin SQL Execution Attempt by User %d", userID),
		logger.WithField("sql", trimmedSQL)) // 使用 Warn 级别以示警告

	// --- 简单判断 SQL 类型 ---
	isQuery := strings.HasPrefix(strings.ToUpper(trimmedSQL), "SELECT")

	var execErr error // 用于保存执行过程中的错误

	if isQuery {
		// --- 执行 SELECT 查询 ---
		rows, err := s.db.QueryContext(ctx, trimmedSQL)
		if err != nil {
			execErr = apperrors.NewSystemError(constant.DATABASE_ERROR, "执行 SQL 查询失败").WithCause(err)
			resultVO.ResultType = "Error"
			resultVO.Error = execErr.Error()
		} else {
			defer rows.Close() // 确保关闭 rows

			columns, err := rows.Columns()
			if err != nil {
				execErr = apperrors.NewSystemError(constant.DATABASE_ERROR, "获取查询结果列名失败").WithCause(err)
				resultVO.ResultType = "Error"
				resultVO.Error = execErr.Error()
			} else {
				resultVO.Columns = columns
				resultVO.Rows = make([][]interface{}, 0)

				// 准备用于 Scan 的参数
				scanArgs := make([]interface{}, len(columns))
				values := make([][]byte, len(columns)) // 使用 []byte 接收原始数据
				for i := range values {
					scanArgs[i] = &values[i]
				}

				// 迭代处理每一行
				for rows.Next() {
					err = rows.Scan(scanArgs...)
					if err != nil {
						execErr = apperrors.NewSystemError(constant.DATABASE_ERROR, "扫描查询结果行失败").WithCause(err)
						resultVO.ResultType = "Error"
						resultVO.Error = execErr.Error()
						break // 扫描失败，停止处理后续行
					}

					rowData := make([]interface{}, len(columns))
					for i, colValue := range values {
						if colValue == nil {
							rowData[i] = nil // 处理 NULL 值
						} else {
							// 将 []byte 转换为 string，这是最通用的表示方式
							// 前端可以根据需要尝试解析
							rowData[i] = string(colValue)
						}
					}
					resultVO.Rows = append(resultVO.Rows, rowData)
				}

				// 检查迭代过程中是否发生错误 (例如连接断开)
				if iterErr := rows.Err(); iterErr != nil && execErr == nil {
					execErr = apperrors.NewSystemError(constant.DATABASE_ERROR, "处理查询结果集时出错").WithCause(iterErr)
					resultVO.ResultType = "Error"
					resultVO.Error = execErr.Error()
				}

				// 如果没有发生错误，设置结果类型
				if execErr == nil {
					resultVO.ResultType = "QueryResult"
					resultVO.Message = fmt.Sprintf("查询成功，返回 %d 行数据。", len(resultVO.Rows))
				}
			}
		}
	} else {
		// --- 执行非 SELECT 操作 (INSERT, UPDATE, DELETE, DDL 等) ---
		result, err := s.db.ExecContext(ctx, trimmedSQL)
		if err != nil {
			execErr = apperrors.NewSystemError(constant.DATABASE_ERROR, "执行 SQL 操作失败").WithCause(err)
			resultVO.ResultType = "Error"
			resultVO.Error = execErr.Error()
		} else {
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				// 获取影响行数失败通常不致命，但需要记录
				s.log.Warn("获取 SQL 操作影响行数失败", logger.WithError(err), logger.WithField("sql", trimmedSQL))
				resultVO.ResultType = "RowsAffected"
				resultVO.Message = "操作成功执行，但获取影响行数失败。"
				// rowsAffected 仍然是 0
			} else {
				resultVO.ResultType = "RowsAffected"
				resultVO.RowsAffected = &rowsAffected // 使用指针
				resultVO.Message = fmt.Sprintf("操作成功执行，影响 %d 行数据。", rowsAffected)
			}
		}
	}

	// 计算耗时
	resultVO.DurationMS = time.Since(startTime).Milliseconds()

	// --- 审计日志 (执行后) ---
	logFields := logger.Fields{
		"sql":        trimmedSQL,
		"userId":     userID,
		"durationMs": resultVO.DurationMS,
		"resultType": resultVO.ResultType,
	}
	if resultVO.ResultType == "RowsAffected" && resultVO.RowsAffected != nil {
		logFields["rowsAffected"] = *resultVO.RowsAffected
	}
	if execErr != nil {
		logFields["error"] = execErr.Error()
		s.log.Error(fmt.Sprintf("Admin SQL Execution Failed by User %d", userID), logFields)
	} else {
		s.log.Warn(fmt.Sprintf("Admin SQL Execution Succeeded by User %d", userID), logFields)
	}

	// 返回结果 VO 和可能发生的错误
	return resultVO, execErr
}

// --- 依赖注入问题 ---
// 你需要确保 ServiceManager 或其他启动代码能够提供 *sql.DB
// 示例：假设 ServiceManager 结构体
// type ServiceManager struct {
//     // ... 其他字段
//     sqlDB *sql.DB
//     // ... 其他方法
// }
// func (sm *ServiceManager) GetSQLDB() *sql.DB {
//     return sm.sqlDB
// }
// 在应用启动时，获取 GORM 的 *gorm.DB 后，调用其 DB() 方法获取 *sql.DB 并设置到 ServiceManager 中
// gormDB, err := gorm.Open(...)
// sqlDB, err := gormDB.DB()
// serviceManager.SetSQLDB(sqlDB) // 假设有这个设置方法
