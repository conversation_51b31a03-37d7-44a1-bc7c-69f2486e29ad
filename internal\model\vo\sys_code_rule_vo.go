package vo

import "time"

// SysCodeRuleVO 编码规则视图对象
type SysCodeRuleVO struct {
	ID              uint       `json:"id"`
	TenantID        uint       `json:"tenantId"`
	AccountBookID   uint       `json:"accountBookId"`
	RuleCode        string     `json:"ruleCode"`
	RuleName        string     `json:"ruleName"`
	BusinessType    string     `json:"businessType"`
	CodeFormat      string     `json:"codeFormat"`
	Separator       *string    `json:"separator"`
	ResetFrequency  string     `json:"resetFrequency"`
	SequenceLength  *int       `json:"sequenceLength"`
	SequenceStart   *int       `json:"sequenceStart"`
	CurrentSequence int64      `json:"currentSequence"`
	LastResetTime   *time.Time `json:"lastResetTime"`
	Status          string     `json:"status"`
	IsDefault       bool       `json:"isDefault"`
	Remark          *string    `json:"remark"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       time.Time  `json:"updatedAt"`
	CreatedBy       uint       `json:"createdBy"`
	UpdatedBy       uint       `json:"updatedBy"`
}

// SysCodeFormatComponentVO 编码格式组件视图对象
type SysCodeFormatComponentVO struct {
	ID            uint      `json:"id"`
	ComponentCode string    `json:"componentCode"`
	ComponentName string    `json:"componentName"`
	ComponentType string    `json:"componentType"`
	FormatPattern *string   `json:"formatPattern"`
	Description   *string   `json:"description"`
	ExampleValue  *string   `json:"exampleValue"`
	Status        string    `json:"status"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// SysCodeGenerationLogVO 编码生成历史视图对象
type SysCodeGenerationLogVO struct {
	ID                uint      `json:"id"`
	TenantID          uint      `json:"tenantId"`
	AccountBookID     uint      `json:"accountBookId"`
	RuleID            uint      `json:"ruleId"`
	RuleName          string    `json:"ruleName"` // 关联规则名称
	BusinessType      string    `json:"businessType"`
	BusinessID        *uint     `json:"businessId"`
	GeneratedCode     string    `json:"generatedCode"`
	SequenceNumber    int64     `json:"sequenceNumber"`
	GenerationContext *string   `json:"generationContext"`
	CreatedBy         uint      `json:"createdBy"`
	CreatedByName     string    `json:"createdByName"` // 关联创建人姓名
	CreatedAt         time.Time `json:"createdAt"`
}

// CodeRuleListVO 编码规则列表视图对象
type CodeRuleListVO struct {
	ID               uint       `json:"id"`
	RuleCode         string     `json:"ruleCode"`
	RuleName         string     `json:"ruleName"`
	BusinessType     string     `json:"businessType"`
	BusinessTypeName string     `json:"businessTypeName"` // 业务类型显示名称
	CodeFormat       string     `json:"codeFormat"`
	CurrentSequence  int64      `json:"currentSequence"`
	LastResetTime    *time.Time `json:"lastResetTime"`
	Status           string     `json:"status"`
	IsDefault        bool       `json:"isDefault"`
	CreatedAt        time.Time  `json:"createdAt"`
}

// CodeFormatDesignerVO 编码格式设计器视图对象
type CodeFormatDesignerVO struct {
	Components      []SysCodeFormatComponentVO `json:"components"`      // 可用组件列表
	CurrentFormat   string                     `json:"currentFormat"`   // 当前格式模板
	PreviewCode     string                     `json:"previewCode"`     // 预览编码
	ValidatedFormat bool                       `json:"validatedFormat"` // 格式是否有效
}

// BusinessTypeOptionVO 业务类型选项视图对象
type BusinessTypeOptionVO struct {
	Value       string `json:"value"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

// GetBusinessTypeOptions 获取业务类型选项列表
func GetBusinessTypeOptions() []BusinessTypeOptionVO {
	return []BusinessTypeOptionVO{
		{Value: "CUSTOMER", Label: "客户管理", Description: "客户编码生成规则"},
		{Value: "SUPPLIER", Label: "供应商管理", Description: "供应商编码生成规则"},
		{Value: "MATERIAL", Label: "物料管理", Description: "物料编码生成规则"},
		{Value: "LOCATION", Label: "库位管理", Description: "库位编码生成规则"},
		// WMS入库流程业务类型
		{Value: "INBOUND_NOTIFICATION", Label: "入库通知单", Description: "入库通知单编码生成规则"},
		{Value: "RECEIVING_RECORD", Label: "收货记录", Description: "收货记录编码生成规则"},
		{Value: "PUTAWAY_TASK", Label: "上架任务", Description: "上架任务编码生成规则"},
		{Value: "INTERNAL_BATCH_NO", Label: "内部批次号", Description: "内部批次号编码生成规则"},
	}
}

// ResetFrequencyOptionVO 重置频率选项视图对象
type ResetFrequencyOptionVO struct {
	Value       string `json:"value"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

// GetResetFrequencyOptions 获取重置频率选项列表
func GetResetFrequencyOptions() []ResetFrequencyOptionVO {
	return []ResetFrequencyOptionVO{
		{Value: "NEVER", Label: "从不重置", Description: "序号永不重置，持续累加"},
		{Value: "DAILY", Label: "每日重置", Description: "每天00:00重置序号"},
		{Value: "MONTHLY", Label: "每月重置", Description: "每月1日00:00重置序号"},
		{Value: "YEARLY", Label: "每年重置", Description: "每年1月1日00:00重置序号"},
	}
}
