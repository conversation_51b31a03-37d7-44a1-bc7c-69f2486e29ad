package service

import (
	"context"
	"fmt"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// generateShipmentNo 生成发运单号
func (s *wmsShipmentServiceImpl) generateShipmentNo(ctx context.Context, req *dto.WmsShipmentCreateReq) (string, error) {
	// 使用编码生成服务
	codeGenService := s.GetServiceManager().GetCodeGenerationService()

	// 构建编码生成请求
	codeReq := &dto.CodeGenerationReq{
		BusinessType: entity.BusinessTypeShipment,
		// 可以根据需要添加其他参数
	}

	result, err := codeGenService.GenerateCode(ctx, codeReq)
	if err != nil {
		return "", fmt.Errorf("生成发运单号失败: %w", err)
	}

	return result.GeneratedCode, nil
}

// fillExtendedInfo 填充扩展信息
func (s *wmsShipmentServiceImpl) fillExtendedInfo(ctx context.Context, vo *vo.WmsShipmentVO) error {
	// TODO: 填充拣货任务号、出库通知单号、承运商名称等扩展信息
	// TODO: 计算统计信息（总物料种类数、总数量、运输时效等）
	return nil
}

// fillListExtendedInfo 填充列表扩展信息
func (s *wmsShipmentServiceImpl) fillListExtendedInfo(ctx context.Context, vo *vo.WmsShipmentListVO) {
	// TODO: 填充拣货任务号、出库通知单号、承运商名称等扩展信息
	// TODO: 计算统计信息（总物料种类数、总数量、运输时效等）
}

// PackShipment 打包确认
func (s *wmsShipmentServiceImpl) PackShipment(ctx context.Context, req *dto.WmsShipmentPackReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsShipmentRepository()

		// 检查发运单是否存在
		shipment, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "发运单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询发运单失败").WithCause(err)
		}

		// 检查状态是否允许打包
		if shipment.Status != string(entity.ShipmentStatusPreparing) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有准备中状态的发运单才能打包")
		}

		// 打包确认
		remark := ""
		if req.Remark != nil {
			remark = *req.Remark
		}

		if err := repo.PackShipment(ctx, req.ID, req.PackageCount, req.TotalWeight, req.TotalVolume, remark); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "打包确认失败").WithCause(err)
		}

		return nil
	})
}

// ShipConfirm 发运确认
func (s *wmsShipmentServiceImpl) ShipConfirm(ctx context.Context, req *dto.WmsShipmentShipReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsShipmentRepository()

		// 检查发运单是否存在
		shipment, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "发运单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询发运单失败").WithCause(err)
		}

		// 检查状态是否允许发运
		if shipment.Status != string(entity.ShipmentStatusPacked) &&
			shipment.Status != string(entity.ShipmentStatusReady) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已打包或待发运状态的发运单才能发运")
		}

		// 发运确认
		remark := ""
		if req.Remark != nil {
			remark = *req.Remark
		}

		// 转换日期字符串为时间类型
		var shipmentDate *time.Time
		if req.ShipmentDate != nil {
			if parsedDate, err := time.Parse("2006-01-02", *req.ShipmentDate); err == nil {
				shipmentDate = &parsedDate
			}
		}

		if err := repo.ShipConfirm(ctx, req.ID, req.CarrierID, req.TrackingNo, req.ShippingMethod, shipmentDate, req.ShippingCost, remark); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "发运确认失败").WithCause(err)
		}

		// 更新出库通知单状态为已发运
		pickingTaskRepo := txRepoMgr.GetWmsPickingTaskRepository()
		pickingTask, err := pickingTaskRepo.FindByID(ctx, shipment.PickingTaskID)
		if err != nil {
			return fmt.Errorf("查询拣货任务失败: %w", err)
		}

		notificationRepo := txRepoMgr.GetWmsOutboundNotificationRepository()
		if err := notificationRepo.UpdateStatus(ctx, pickingTask.NotificationID, string(entity.OutboundStatusShipped), "已发运"); err != nil {
			return fmt.Errorf("更新出库通知单状态失败: %w", err)
		}

		return nil
	})
}

// UpdateTrackingStatus 更新运输状态
func (s *wmsShipmentServiceImpl) UpdateTrackingStatus(ctx context.Context, req *dto.WmsShipmentTrackReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsShipmentRepository()

		// 检查发运单是否存在
		shipment, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "发运单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询发运单失败").WithCause(err)
		}

		// 检查状态是否允许更新
		if shipment.Status == string(entity.ShipmentStatusDelivered) ||
			shipment.Status == string(entity.ShipmentStatusReturned) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已送达或已退回的发运单不能更新运输状态")
		}

		// 更新运输状态
		remark := ""
		if req.Remark != nil {
			remark = *req.Remark
		}

		// 转换日期字符串为时间类型
		var actualDelivery *time.Time
		if req.ActualDelivery != nil {
			if parsedDate, err := time.Parse("2006-01-02", *req.ActualDelivery); err == nil {
				actualDelivery = &parsedDate
			}
		}

		if err := repo.UpdateTrackingStatus(ctx, req.ID, &req.Status, req.TrackingInfo, actualDelivery, remark); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新运输状态失败").WithCause(err)
		}

		return nil
	})
}

// DeliveryConfirm 签收确认
func (s *wmsShipmentServiceImpl) DeliveryConfirm(ctx context.Context, req *dto.WmsShipmentDeliverReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsShipmentRepository()

		// 检查发运单是否存在
		shipment, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "发运单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询发运单失败").WithCause(err)
		}

		// 检查状态是否允许签收
		if shipment.Status != string(entity.ShipmentStatusInTransit) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有运输中状态的发运单才能签收")
		}

		// 签收确认
		// 转换日期字符串为时间类型
		var actualDelivery *time.Time
		if req.ActualDelivery != nil {
			if parsedDate, err := time.Parse("2006-01-02", *req.ActualDelivery); err == nil {
				actualDelivery = &parsedDate
			}
		}

		if err := repo.DeliveryConfirm(ctx, req.ID, actualDelivery, req.ReceiverName, req.ReceiverPhone, req.DeliveryProof, req.Remark); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "签收确认失败").WithCause(err)
		}

		// 更新出库通知单状态为已送达
		pickingTaskRepo := txRepoMgr.GetWmsPickingTaskRepository()
		pickingTask, err := pickingTaskRepo.FindByID(ctx, shipment.PickingTaskID)
		if err != nil {
			return fmt.Errorf("查询拣货任务失败: %w", err)
		}

		notificationRepo := txRepoMgr.GetWmsOutboundNotificationRepository()
		if err := notificationRepo.UpdateStatus(ctx, pickingTask.NotificationID, string(entity.OutboundStatusDelivered), "已送达"); err != nil {
			return fmt.Errorf("更新出库通知单状态失败: %w", err)
		}

		return nil
	})
}

// CalculateShippingCost 计算运费
func (s *wmsShipmentServiceImpl) CalculateShippingCost(ctx context.Context, req *dto.WmsShipmentCalculateCostReq) (*vo.WmsShipmentCostVO, error) {
	// TODO: 实现运费计算逻辑
	// 1. 根据承运商和运输方式获取费率
	// 2. 根据重量、体积、距离等计算费用
	// 3. 返回详细的费用明细

	result := &vo.WmsShipmentCostVO{
		ShipmentID:     req.ID,
		CarrierID:      req.CarrierID,
		CarrierName:    "临时承运商", // TODO: 从数据库获取
		ShippingMethod: req.ShippingMethod,
		BaseCost:       100.0,
		WeightCost:     50.0,
		VolumeCost:     30.0,
		DistanceCost:   20.0,
		ServiceCost:    10.0,
		TotalCost:      210.0,
		Currency:       "CNY",
	}

	return result, nil
}

// PrintShippingLabel 打印发运标签
func (s *wmsShipmentServiceImpl) PrintShippingLabel(ctx context.Context, req *dto.WmsShipmentLabelPrintReq) ([]*vo.WmsShipmentLabelVO, error) {
	// TODO: 实现标签打印逻辑
	// 1. 生成标签数据
	// 2. 调用打印服务
	// 3. 记录打印历史

	var results []*vo.WmsShipmentLabelVO
	for _, shipmentID := range req.IDs {
		label := &vo.WmsShipmentLabelVO{
			ShipmentID:  shipmentID,
			ShipmentNo:  fmt.Sprintf("SH%d", shipmentID), // TODO: 从数据库获取
			LabelType:   req.LabelType,
			LabelData:   "base64_encoded_label_data", // TODO: 生成实际标签
			LabelFormat: "PDF",
			PrintCount:  1,
			GeneratedAt: time.Now(),
		}
		results = append(results, label)
	}

	return results, nil
}

// GetByShipmentNo 根据发运单号获取
func (s *wmsShipmentServiceImpl) GetByShipmentNo(ctx context.Context, shipmentNo string) (*vo.WmsShipmentVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipment, err := repo.FindByShipmentNo(ctx, shipmentNo)
	if err != nil {
		return nil, err
	}

	voResult := &vo.WmsShipmentVO{}
	copier.Copy(voResult, shipment)

	// 填充扩展信息
	if err := s.fillExtendedInfo(ctx, voResult); err != nil {
		return nil, err
	}

	return voResult, nil
}

// GetByTrackingNo 根据运单号获取
func (s *wmsShipmentServiceImpl) GetByTrackingNo(ctx context.Context, trackingNo string) (*vo.WmsShipmentVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipment, err := repo.FindByTrackingNo(ctx, trackingNo)
	if err != nil {
		return nil, err
	}

	voResult := &vo.WmsShipmentVO{}
	copier.Copy(voResult, shipment)

	// 填充扩展信息
	if err := s.fillExtendedInfo(ctx, voResult); err != nil {
		return nil, err
	}

	return voResult, nil
}

// GetByPickingTaskID 根据拣货任务ID获取
func (s *wmsShipmentServiceImpl) GetByPickingTaskID(ctx context.Context, pickingTaskID uint) ([]*vo.WmsShipmentVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipments, err := repo.FindByPickingTaskID(ctx, pickingTaskID)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsShipmentVO
	for _, shipment := range shipments {
		voResult := &vo.WmsShipmentVO{}
		copier.Copy(voResult, shipment)

		// 填充扩展信息
		if err := s.fillExtendedInfo(ctx, voResult); err != nil {
			return nil, err
		}

		results = append(results, voResult)
	}

	return results, nil
}

// GetPendingPacking 获取待打包的发运单
func (s *wmsShipmentServiceImpl) GetPendingPacking(ctx context.Context) ([]*vo.WmsShipmentListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipments, err := repo.GetPendingPacking(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsShipmentListVO
	for _, shipment := range shipments {
		listVO := &vo.WmsShipmentListVO{}
		copier.Copy(listVO, shipment)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// GetReadyToShip 获取待发运的发运单
func (s *wmsShipmentServiceImpl) GetReadyToShip(ctx context.Context) ([]*vo.WmsShipmentListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipments, err := repo.GetReadyToShip(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsShipmentListVO
	for _, shipment := range shipments {
		listVO := &vo.WmsShipmentListVO{}
		copier.Copy(listVO, shipment)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// GetInTransit 获取运输中的发运单
func (s *wmsShipmentServiceImpl) GetInTransit(ctx context.Context) ([]*vo.WmsShipmentListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipments, err := repo.GetInTransit(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsShipmentListVO
	for _, shipment := range shipments {
		listVO := &vo.WmsShipmentListVO{}
		copier.Copy(listVO, shipment)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// GetOverdueShipments 获取过期未发运的发运单
func (s *wmsShipmentServiceImpl) GetOverdueShipments(ctx context.Context) ([]*vo.WmsShipmentListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	shipments, err := repo.GetOverdueShipments(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsShipmentListVO
	for _, shipment := range shipments {
		listVO := &vo.WmsShipmentListVO{}
		copier.Copy(listVO, shipment)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// BatchCreate 批量创建发运单
func (s *wmsShipmentServiceImpl) BatchCreate(ctx context.Context, req *dto.WmsShipmentBatchCreateReq) ([]*vo.WmsShipmentVO, error) {
	var results []*vo.WmsShipmentVO

	for _, createReq := range req.Shipments {
		shipment, err := s.Create(ctx, &createReq)
		if err != nil {
			// 记录错误但继续处理其他发运单
			continue
		}
		results = append(results, shipment)
	}

	return results, nil
}

// BatchShip 批量发运
func (s *wmsShipmentServiceImpl) BatchShip(ctx context.Context, req *dto.WmsShipmentBatchShipReq) error {
	for _, id := range req.IDs {
		trackingNo := fmt.Sprintf("TRK%d", id) // TODO: 生成实际运单号
		shipReq := &dto.WmsShipmentShipReq{
			ID:             id,
			CarrierID:      req.CarrierID,
			TrackingNo:     &trackingNo,
			ShippingMethod: req.ShippingMethod,
			ShipmentDate:   req.ShipmentDate,
			ShippingCost:   nil, // 批量发运时不设置运费，需要单独计算
			Remark:         req.Remark,
		}
		if err := s.ShipConfirm(ctx, shipReq); err != nil {
			return fmt.Errorf("发运单ID %d 发运失败: %w", id, err)
		}
	}
	return nil
}

// GetStats 获取统计信息
func (s *wmsShipmentServiceImpl) GetStats(ctx context.Context, req *dto.WmsShipmentStatsReq) (*vo.WmsShipmentStatsVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsShipmentRepository()

	// 获取基础统计数据
	statsData, err := repo.GetShipmentStats(ctx, req.DateStart, req.DateEnd, req.CarrierID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取统计数据失败").WithCause(err)
	}

	result := &vo.WmsShipmentStatsVO{}
	copier.Copy(result, statsData)

	// TODO: 根据GroupBy参数生成趋势数据
	// TODO: 计算效率统计

	return result, nil
}

// ExportToExcel 导出到Excel
func (s *wmsShipmentServiceImpl) ExportToExcel(ctx context.Context, req *dto.WmsShipmentExportReq) ([]byte, error) {
	// TODO: 查询数据
	// TODO: 生成Excel文件
	// TODO: 返回文件字节数组

	// 临时实现，返回未实现错误
	return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "Excel导出功能暂未实现")
}

// GetCarriers 获取承运商列表
func (s *wmsShipmentServiceImpl) GetCarriers(ctx context.Context, req *dto.WmsCarrierQueryReq) ([]*vo.WmsCarrierVO, error) {
	// TODO: 实现承运商查询逻辑
	// 这里可能需要单独的承运商管理模块

	// 临时返回模拟数据
	carriers := []*vo.WmsCarrierVO{
		{
			ID:           1,
			CarrierCode:  "SF",
			CarrierName:  "顺丰速运",
			ContactPhone: &[]string{"************"}[0],
			Status:       "ACTIVE",
		},
		{
			ID:           2,
			CarrierCode:  "YTO",
			CarrierName:  "圆通速递",
			ContactPhone: &[]string{"************"}[0],
			Status:       "ACTIVE",
		},
		{
			ID:           3,
			CarrierCode:  "ZTO",
			CarrierName:  "中通快递",
			ContactPhone: &[]string{"************"}[0],
			Status:       "ACTIVE",
		},
	}

	return carriers, nil
}
