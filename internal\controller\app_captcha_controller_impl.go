package controller

import (
	"context"

	"backend/internal/model/vo"
	"backend/internal/service"
	"backend/pkg/config"
	apperrors "backend/pkg/errors"

	"github.com/kataras/iris/v12"
)

// CaptchaController 验证码控制器
type CaptchaControllerImpl struct {
	*BaseControllerImpl
	cfg            *config.Configuration
	captchaService service.CaptchaService
}

// NewCaptchaController 创建验证码控制器
func NewCaptchaControllerImpl(cm *ControllerManager, cfg *config.Configuration, cacheSvc service.CacheService) *CaptchaControllerImpl {
	// 获取 Logger
	logger := cm.GetLogger()

	// 调用 NewCaptchaService 并处理错误
	captchaSvcInstance, err := service.NewCaptchaService(logger, cfg, cacheSvc)
	if err != nil {
		// 如果初始化失败，记录严重错误并终止，因为 CaptchaController 依赖它
		logger.Fatal(context.Background(), "CaptchaService failed to initialize for CaptchaController", logger.WithError(err))
	}

	return &CaptchaControllerImpl{
		BaseControllerImpl: NewBaseController(cm),
		cfg:                cfg,
		captchaService:     captchaSvcInstance,
	}
}

// GetCaptcha 获取验证码
// @Summary 获取验证码图像和ID
// @Description 生成一个新的验证码并返回其ID和Base64编码的图像数据
// @Tags Auth
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=vo.CaptchaVO} "成功响应"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /auth/captcha [get]
func (c *CaptchaControllerImpl) GetCaptcha(ctx iris.Context) {
	opName := "获取验证码"

	if !c.cfg.Captcha.Enable {
		c.GetLogger().Info(ctx, opName+": 验证码功能已禁用")
		c.Success(ctx, &vo.CaptchaVO{Enabled: false})
		return
	}

	if c.captchaService == nil {
		c.HandleError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "验证码服务不可用"), opName)
		return
	}

	captchaVO, err := c.captchaService.GenerateCaptcha(ctx.Request().Context())
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, captchaVO)
}

/*
// RegisterRoutes 注册路由 (示例，实际注册应在应用入口处)
func (c *CaptchaController) RegisterRoutes(app *iris.Application) {
	authParty := app.Party("/api/v1/auth")
	{
		authParty.Get("/captcha", c.GetCaptcha)
	}
}
*/
