package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	"backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// RoleService 角色服务接口
type RoleService interface {
	// 基础服务
	BaseService

	// 基本CRUD操作
	CreateRole(ctx context.Context, roleDTO dto.RoleCreateOrUpdateDTO) (*vo.RoleVO, error)
	UpdateRole(ctx context.Context, id uint, roleDTO dto.RoleCreateOrUpdateDTO) (*vo.RoleVO, error)
	DeleteRole(ctx context.Context, id uint) error
	GetRoleByID(ctx context.Context, id uint) (*vo.RoleVO, error)
	ListRoles(ctx context.Context, queryDTO dto.RolePageQueryDTO) ([]*vo.RoleVO, error)
	PageRoles(ctx context.Context, pageQueryDTO dto.RolePageQueryDTO) (*vo.PageVO, error)

	// 特殊功能
	GetRoleByCode(ctx context.Context, code string) (*vo.RoleVO, error)
	UpdateStatus(ctx context.Context, id uint, status int) error

	// 角色菜单管理
	GetRoleMenus(ctx context.Context, id uint) ([]*vo.MenuVO, error)
	UpdateRoleMenus(ctx context.Context, id uint, menuIDs []uint) error

	// 角色用户管理
	GetRoleUsers(ctx context.Context, roleID uint, queryDTO dto.RolePageQueryDTO) (*vo.PageVO, error)
	CountRoleUsers(ctx context.Context, id uint) (int64, error)
}

// RoleServiceImpl 角色服务实现
type RoleServiceImpl struct {
	*BaseServiceImpl
}

// NewRoleService 创建角色服务
func NewRoleService(serviceManager *ServiceManager) RoleService {
	return &RoleServiceImpl{
		BaseServiceImpl: NewBaseService(serviceManager),
	}
}

// GetRoleService 从服务管理器获取角色服务
func GetRoleService(serviceManager *ServiceManager) RoleService {
	return GetService[RoleService](
		serviceManager,
		func(sm *ServiceManager) RoleService {
			return NewRoleService(sm)
		},
	)
}

// getRoleRepository 获取角色仓库
func (s *RoleServiceImpl) getRoleRepository() repository.RoleRepository {
	return s.GetServiceManager().GetRepositoryManager().GetRoleRepository()
}

// getRoleMenuRepository 获取角色菜单仓库
func (s *RoleServiceImpl) getRoleMenuRepository() repository.RoleMenuRepository {
	return s.GetServiceManager().GetRepositoryManager().GetRoleMenuRepository()
}

// getUserRoleRepository 获取用户角色仓库
func (s *RoleServiceImpl) getUserRoleRepository() repository.UserRoleRepository {
	return s.GetServiceManager().GetRepositoryManager().GetUserRoleRepository()
}

// CreateRole 创建角色
func (s *RoleServiceImpl) CreateRole(ctx context.Context, roleDTO dto.RoleCreateOrUpdateDTO) (*vo.RoleVO, error) {
	s.GetLogger().Debug(ctx, "创建角色",
		logger.WithField("name", roleDTO.Name),
		logger.WithField("code", roleDTO.Code))

	// 创建角色实体 (事务外准备数据)
	role := &entity.Role{
		Name:     roleDTO.Name,
		Code:     roleDTO.Code,
		Status:   roleDTO.Status,
		IsSystem: roleDTO.IsSystem,
		Remark:   roleDTO.Remark,
	}

	err := s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		txRoleRepo := txSm.GetRepositoryManager().GetRoleRepository()
		txRoleMenuRepo := txSm.GetRepositoryManager().GetRoleMenuRepository()

		// <<< 验证 (事务内，传入事务性 repo) >>>
		if validateErr := s.validateRoleParams(ctx, roleDTO, 0, txRoleRepo); validateErr != nil {
			return validateErr // 直接返回验证错误
		}

		// Set CreatedBy and UpdatedBy before creating the role
		uid64Create, errCreate := s.GetUserIDFromContext(ctx) // Use s.GetUserIDFromContext via BaseServiceImpl
		if errCreate != nil {
			s.GetLogger().WithError(errCreate).Warn(ctx, "CreateRole: 无法从上下文中获取UserID")
		}
		role.CreatedBy = uint(uid64Create)
		role.UpdatedBy = uint(uid64Create)

		// 1. 保存角色 (事务内)
		if createErr := txRoleRepo.Create(ctx, role); createErr != nil {
			s.GetLogger().Error(ctx, "事务中创建角色失败",
				logger.WithField("name", roleDTO.Name),
				logger.WithField("code", roleDTO.Code),
				logger.WithError(createErr))
			return errors.WrapError(createErr, errors.CODE_DATA_CREATE_FAILED, "创建角色数据库操作失败")
		}

		// 2. 设置菜单 (事务内)
		if len(roleDTO.MenuIDs) > 0 {
			if menuErr := txRoleMenuRepo.SetRoleMenus(ctx, role.ID, roleDTO.MenuIDs); menuErr != nil {
				s.GetLogger().Error(ctx, "事务中设置角色菜单失败",
					logger.WithField("roleId", role.ID),
					logger.WithError(menuErr))
				return errors.WrapError(menuErr, errors.CODE_DATA_UPDATE_FAILED, "设置角色菜单关联失败")
			}
		}
		return nil // 事务成功
	})

	if err != nil {
		return nil, err
	}

	return s.convertToRoleVO(role), nil
}

// UpdateRole 更新角色
func (s *RoleServiceImpl) UpdateRole(ctx context.Context, id uint, roleDTO dto.RoleCreateOrUpdateDTO) (*vo.RoleVO, error) {
	s.GetLogger().Debug(ctx, "更新角色",
		logger.WithField("roleId", id),
		logger.WithField("name", roleDTO.Name),
		logger.WithField("code", roleDTO.Code))

	var updatedRole *entity.Role

	err := s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		txRoleRepo := txSm.GetRepositoryManager().GetRoleRepository()
		txRoleMenuRepo := txSm.GetRepositoryManager().GetRoleMenuRepository()
		txLog := logger.WithContext(ctx).WithFields(logger.Fields{"tx": true, "roleId": id})

		role, findErr := txRoleRepo.FindByID(ctx, id)
		if findErr != nil {
			txLog.Error("事务中查询角色失败", logger.WithError(findErr))
			if errors.IsError(findErr, errors.CODE_DATA_NOT_FOUND) {
				return errors.NewDataError(errors.CODE_DATA_NOT_FOUND, "角色不存在")
			}
			return errors.WrapError(findErr, errors.CODE_DATA_QUERY_FAILED, "查询角色失败")
		}

		// <<< 验证 (事务内，传入事务性 repo) >>>
		if validateErr := s.validateRoleParams(ctx, roleDTO, id, txRoleRepo); validateErr != nil {
			return validateErr // 直接返回验证错误
		}

		if role.IsSystem && role.Code != roleDTO.Code {
			txLog.Warn("尝试修改系统角色的编码")
			return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "系统角色不允许修改编码")
		}

		// Set UpdatedBy before updating the role
		uid64Update, errUpdate := s.GetUserIDFromContext(ctx) // Use s.GetUserIDFromContext via BaseServiceImpl
		if errUpdate != nil {
			txLog.WithError(errUpdate).Warn("UpdateRole: 无法从上下文中获取UserID")
		}
		role.UpdatedBy = uint(uid64Update)

		role.Name = roleDTO.Name
		role.Code = roleDTO.Code
		role.Status = roleDTO.Status
		role.IsSystem = roleDTO.IsSystem
		role.Remark = roleDTO.Remark

		if updateErr := txRoleRepo.Update(ctx, role); updateErr != nil {
			txLog.Error("事务中更新角色基础信息失败", logger.WithError(updateErr))
			return errors.WrapError(updateErr, errors.CODE_DATA_UPDATE_FAILED, "更新角色基础信息失败")
		}

		if roleDTO.MenuIDs != nil {
			if menuErr := txRoleMenuRepo.SetRoleMenus(ctx, id, roleDTO.MenuIDs); menuErr != nil {
				txLog.Error("事务中更新角色菜单关联失败", logger.WithError(menuErr))
				return errors.WrapError(menuErr, errors.CODE_DATA_UPDATE_FAILED, "更新角色菜单关联失败")
			}
		}

		updatedRole = role
		return nil
	})

	if err != nil {
		return nil, err
	}

	return s.convertToRoleVO(updatedRole), nil
}

// DeleteRole 删除角色
func (s *RoleServiceImpl) DeleteRole(ctx context.Context, id uint) error {
	s.GetLogger().Debug(ctx, "删除角色", logger.WithField("roleId", id))

	// <<< 使用事务包裹所有检查和删除操作 >>>
	err := s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		// 使用事务性仓库
		txUserRoleRepo := txSm.GetRepositoryManager().GetUserRoleRepository()
		txRoleMenuRepo := txSm.GetRepositoryManager().GetRoleMenuRepository()
		txRoleRepo := txSm.GetRepositoryManager().GetRoleRepository()

		txLog := logger.WithContext(ctx).WithFields(logger.Fields{"tx": true, "roleId": id})

		// <<< 检查 1: 查询角色 (事务内) >>>
		role, findErr := txRoleRepo.FindByID(ctx, id)
		if findErr != nil {
			txLog.Error("事务中查询角色失败", logger.WithError(findErr))
			if errors.IsError(findErr, errors.CODE_DATA_NOT_FOUND) {
				// 如果角色未找到，认为删除成功 (幂等性)，从事务函数返回 nil
				return nil
			}
			// 其他查询错误需要返回
			return errors.WrapError(findErr, errors.CODE_DATA_QUERY_FAILED, "查询角色失败")
		}

		// <<< 检查 2: 是否系统角色 (事务内) >>>
		if role.IsSystem {
			txLog.Warn("尝试删除系统角色")
			return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "系统角色不允许删除")
		}

		// 1/Check 3: 检查角色是否有关联用户 (事务内)
		count, countErr := txUserRoleRepo.CountUsersByRole(ctx, id)
		if countErr != nil {
			// 不再传递 ctx 给 Error 方法
			txLog.Error("事务中查询角色关联用户数失败", logger.WithError(countErr))
			return errors.WrapError(countErr, errors.CODE_DATA_QUERY_FAILED, "检查用户关联失败")
		}

		if count > 0 {
			// 不再传递 ctx 给 Warn 方法
			txLog.Warn("角色已分配给用户，不允许删除")
			return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "角色已分配给用户，不允许删除")
		}

		// 2. 删除角色菜单关联
		if menuErr := txRoleMenuRepo.DeleteRoleMenus(ctx, id); menuErr != nil {
			// 不再传递 ctx 给 Error 方法
			txLog.Error("事务中删除角色菜单关联失败", logger.WithError(menuErr))
			return errors.WrapError(menuErr, errors.CODE_DATA_DELETE_FAILED, "删除角色菜单关联失败")
		}

		// 3. 删除角色
		if deleteErr := txRoleRepo.Delete(ctx, id); deleteErr != nil {
			// 不再传递 ctx 给 Error 方法
			txLog.Error("事务中删除角色失败", logger.WithError(deleteErr))
			// GORM 的 Delete 通常在记录未找到时不报错，但包装一下以防万一
			return errors.WrapError(deleteErr, errors.CODE_DATA_DELETE_FAILED, "删除角色失败")
		}

		return nil // 事务成功
	}) // <<< 结束事务 >>>

	if err != nil {
		// 事务执行失败，返回包装好的错误或业务错误
		return err
	}

	// 仅当事务成功且角色最初存在时记录成功日志 (err 为 nil 暗示了这一点)
	s.GetLogger().Info(ctx, "角色删除成功", logger.WithField("roleId", id))
	return nil
}

// GetRoleByID 根据ID获取角色
func (s *RoleServiceImpl) GetRoleByID(ctx context.Context, id uint) (*vo.RoleVO, error) {
	s.GetLogger().Debug(ctx, "根据ID获取角色",
		logger.WithField("roleId", id))

	// 查询角色
	roleRepo := s.getRoleRepository()
	role, err := roleRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询角色失败",
			logger.WithField("roleId", id),
			logger.WithError(err))
		// 检查是否是未找到错误
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			return nil, errors.NewDataError(errors.CODE_DATA_NOT_FOUND, "角色不存在")
		}
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询角色失败")
	}

	// 转换为基础视图对象 (不含关联数据)
	roleVO := s.convertToRoleVO(role)

	// 单独获取关联数据
	// 获取菜单ID
	roleMenuRepo := s.getRoleMenuRepository()
	menus, menuErr := roleMenuRepo.GetRoleMenus(ctx, id, nil) // 获取菜单实体
	if menuErr != nil {
		s.GetLogger().Error(ctx, "获取角色菜单失败 (GetRoleByID)",
			logger.WithField("roleId", id),
			logger.WithError(menuErr))
		// <<< 立即返回错误 >>>
		return nil, errors.WrapError(menuErr, errors.CODE_DATA_QUERY_FAILED, "获取角色菜单关联失败")
	}
	menuIDs := make([]uint, 0, len(menus))
	for _, menu := range menus {
		menuIDs = append(menuIDs, menu.ID)
	}
	roleVO.MenuIDs = menuIDs

	// 获取用户数量
	userRoleRepo := s.getUserRoleRepository()
	userCount, countErr := userRoleRepo.CountUsersByRole(ctx, id)
	if countErr != nil {
		s.GetLogger().Error(ctx, "统计角色用户数量失败 (GetRoleByID)",
			logger.WithField("roleId", id),
			logger.WithError(countErr))
		// <<< 立即返回错误 >>>
		return nil, errors.WrapError(countErr, errors.CODE_DATA_QUERY_FAILED, "获取角色用户数量失败")
	}
	roleVO.UserCount = int(userCount)

	return roleVO, nil // 只有所有步骤都成功才返回 roleVO 和 nil error
}

// ListRoles 获取角色列表
func (s *RoleServiceImpl) ListRoles(ctx context.Context, queryDTO dto.RolePageQueryDTO) ([]*vo.RoleVO, error) {
	s.GetLogger().Debug(ctx, "获取角色列表")

	// 构建查询条件
	concreteConditions := s.buildRoleQueryConditions(queryDTO)
	// 将 []repository.Condition 转换为 []repository.QueryCondition
	queryConditions := make([]repository.QueryCondition, len(concreteConditions))
	for i, cond := range concreteConditions {
		queryConditions[i] = cond
	}

	// 查询角色列表
	roleRepo := s.getRoleRepository()
	// 从 DTO 获取排序信息，或使用默认排序
	sortInfos := queryDTO.PageQuery.Sort
	if len(sortInfos) == 0 {
		sortInfos = []response.SortInfo{{Field: "sort", Order: "asc"}} // 默认按 sort 升序
	}

	// 使用正确的参数调用 FindByCondition
	roles, err := roleRepo.FindByCondition(ctx, queryConditions, sortInfos)
	if err != nil {
		s.GetLogger().Error(ctx, "查询角色列表失败",
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询角色列表失败")
	}

	// 转换为视图对象列表，并处理错误
	roleVOs, convErr := s.convertToRoleVOList(ctx, roles)
	if convErr != nil {
		// 错误已在 convertToRoleVOList 内部记录
		return nil, convErr // 直接返回转换错误
	}

	return roleVOs, nil
}

// PageRoles 分页获取角色列表
func (s *RoleServiceImpl) PageRoles(ctx context.Context, pageQueryDTO dto.RolePageQueryDTO) (*vo.PageVO, error) {
	s.GetLogger().Debug(ctx, "分页获取角色列表",
		logger.WithField("pageNum", pageQueryDTO.PageQuery.PageNum),
		logger.WithField("pageSize", pageQueryDTO.PageQuery.PageSize))

	// 构建查询条件
	concreteConditions := s.buildRoleQueryConditions(pageQueryDTO)
	// 将 []repository.Condition 转换为 []repository.QueryCondition
	queryConditions := make([]repository.QueryCondition, len(concreteConditions))
	for i, cond := range concreteConditions {
		queryConditions[i] = cond
	}

	// 分页查询角色列表
	roleRepo := s.getRoleRepository()
	// 从 DTO 构造 PageQuery
	pageQuery := &response.PageQuery{
		PageNum:  pageQueryDTO.PageQuery.PageNum,
		PageSize: pageQueryDTO.PageQuery.PageSize,
		Sort:     pageQueryDTO.PageQuery.Sort,
	}
	// 使用正确的参数调用 FindByPage
	pageResult, err := roleRepo.FindByPage(ctx, pageQuery, queryConditions)
	if err != nil {
		s.GetLogger().Error(ctx, "分页查询角色列表失败",
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "分页查询角色列表失败")
	}

	// 转换分页中的角色列表为视图对象列表
	// 注意：需要确保 pageResult.List 的类型是 []*entity.Role
	roleEntities, ok := pageResult.List.([]*entity.Role)
	if !ok {
		s.GetLogger().Error(ctx, "FindByPage 返回的 List 类型断言失败", logger.WithField("actualType", fmt.Sprintf("%T", pageResult.List)))
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "分页查询结果类型错误")
	}
	// 转换视图对象列表，并处理错误
	roleVOs, convErr := s.convertToRoleVOList(ctx, roleEntities)
	if convErr != nil {
		// 错误已在 convertToRoleVOList 内部记录
		return nil, convErr // 直接返回转换错误
	}

	// 构建分页视图对象
	pageVO := &vo.PageVO{
		List:     roleVOs,
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		Pages:    pageResult.Pages,
	}

	return pageVO, nil
}

// GetRoleByCode 根据编码获取角色
func (s *RoleServiceImpl) GetRoleByCode(ctx context.Context, code string) (*vo.RoleVO, error) {
	s.GetLogger().Debug(ctx, "根据编码获取角色",
		logger.WithField("code", code))

	// 查询角色
	roleRepo := s.getRoleRepository()
	role, err := roleRepo.FindByCode(ctx, code)
	if err != nil {
		s.GetLogger().Error(ctx, "查询角色失败",
			logger.WithField("code", code),
			logger.WithError(err))
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			return nil, errors.NewDataError(errors.CODE_DATA_NOT_FOUND, "角色不存在")
		}
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询角色失败")
	}

	// 转换为视图对象
	return s.convertToRoleVO(role), nil
}

// UpdateStatus 更新角色状态
func (s *RoleServiceImpl) UpdateStatus(ctx context.Context, id uint, status int) error {
	s.GetLogger().Debug(ctx, "更新角色状态",
		logger.WithField("roleId", id),
		logger.WithField("status", status))

	// 查询角色
	roleRepo := s.getRoleRepository()
	role, err := roleRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询角色失败",
			logger.WithField("roleId", id),
			logger.WithError(err))
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			return errors.NewDataError(errors.CODE_DATA_NOT_FOUND, "角色不存在")
		}
		return errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询角色失败")
	}

	// 系统角色不允许禁用
	if role.IsSystem && status == 0 {
		return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "系统角色不允许禁用")
	}

	// 更新状态
	if err := roleRepo.UpdateStatus(ctx, id, status); err != nil {
		s.GetLogger().Error(ctx, "更新角色状态失败",
			logger.WithField("roleId", id),
			logger.WithError(err))
		return errors.WrapError(err, errors.CODE_DATA_UPDATE_FAILED, "更新角色状态失败")
	}

	return nil
}

// GetRoleMenus 获取角色菜单
func (s *RoleServiceImpl) GetRoleMenus(ctx context.Context, id uint) ([]*vo.MenuVO, error) {
	s.GetLogger().Debug(ctx, "获取角色菜单",
		logger.WithField("roleId", id))

	// 获取角色菜单实体列表 (添加 nil for sortInfos)
	roleMenuRepo := s.getRoleMenuRepository()
	menus, err := roleMenuRepo.GetRoleMenus(ctx, id, nil) // 直接获取菜单实体
	if err != nil {
		s.GetLogger().Error(ctx, "获取角色菜单失败",
			logger.WithField("roleId", id),
			logger.WithError(err))
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "获取角色菜单关联失败")
	}

	// 直接转换获取到的菜单实体列表
	menuService := GetMenuService(s.GetServiceManager()) // 假设可以获取 MenuService
	menuServiceImpl, ok := menuService.(*MenuServiceImpl)
	if !ok {
		s.GetLogger().Error(ctx, "类型断言 MenuServiceImpl 失败")
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "内部服务错误")
	}
	menuVOs := menuServiceImpl.convertToMenuVOList(menus) // 使用 MenuService 的转换方法

	return menuVOs, nil
}

// UpdateRoleMenus 更新角色菜单
func (s *RoleServiceImpl) UpdateRoleMenus(ctx context.Context, id uint, menuIDs []uint) error {
	s.GetLogger().Debug(ctx, "更新角色菜单",
		logger.WithField("roleId", id),
		logger.WithField("count", len(menuIDs)))

	// 获取角色
	roleRepo := s.getRoleRepository()
	_, err := roleRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询角色失败",
			logger.WithField("roleId", id),
			logger.WithError(err))
		if errors.IsError(err, errors.CODE_DATA_NOT_FOUND) {
			return errors.NewDataError(errors.CODE_DATA_NOT_FOUND, "角色不存在")
		}
		return errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询角色失败")
	}

	// 获取角色菜单仓库
	roleMenuRepo := s.getRoleMenuRepository()

	// 设置角色菜单
	err = roleMenuRepo.SetRoleMenus(ctx, id, menuIDs)
	if err != nil {
		s.GetLogger().Error(ctx, "更新角色菜单失败",
			logger.WithField("roleId", id),
			logger.WithError(err))
		return errors.WrapError(err, errors.CODE_DATA_UPDATE_FAILED, "更新角色菜单关联失败")
	}

	return nil
}

// GetRoleUsers 获取角色关联的用户列表 (分页实现)
func (s *RoleServiceImpl) GetRoleUsers(ctx context.Context, roleID uint, queryDTO dto.RolePageQueryDTO) (*vo.PageVO, error) {
	s.GetLogger().Debug(ctx, "分页获取角色用户",
		logger.WithField("roleId", roleID),
		logger.WithField("pageNum", queryDTO.PageQuery.PageNum),
		logger.WithField("pageSize", queryDTO.PageQuery.PageSize))

	// 从 DTO 构建 PageQuery
	pageQuery := &response.PageQuery{
		PageNum:  queryDTO.PageQuery.PageNum,
		PageSize: queryDTO.PageQuery.PageSize,
		Sort:     queryDTO.PageQuery.Sort, // 使用 DTO 中的排序信息
	}

	// 构建用户查询条件 (这里可以从 queryDTO 提取用户相关的过滤条件，例如用户名、状态等)
	// 暂时只基于 roleID 查询，不添加额外条件
	userConditions := make([]repository.QueryCondition, 0)
	// Example: if queryDTO.Username != "" { userConditions = append(userConditions, repository.NewLikeCondition("username", queryDTO.Username)) }
	// Example: if queryDTO.UserStatus != nil { userConditions = append(userConditions, repository.NewEqualCondition("status", *queryDTO.UserStatus)) }

	// 调用仓库层的分页查询方法
	userRoleRepo := s.getUserRoleRepository()
	pageResult, err := userRoleRepo.FindUsersInRole(ctx, roleID, pageQuery, userConditions)
	if err != nil {
		s.GetLogger().Error(ctx, "分页查询角色用户失败",
			logger.WithField("roleId", roleID),
			logger.WithError(err))
		// FindUsersInRole 内部应已包装错误，但为保险起见再次包装
		return nil, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "分页查询角色用户失败")
	}

	// 获取用户服务用于转换
	userService := GetUserService(s.GetServiceManager())
	userServiceImpl, ok := userService.(*UserServiceImpl)
	if !ok {
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "无法获取 UserService 实现")
	}

	// 转换当前页的用户列表为 VO
	userEntities, ok := pageResult.List.([]*entity.User)
	if !ok {
		s.GetLogger().Error(ctx, "FindUsersInRole 返回的 List 类型断言失败", logger.WithField("actualType", fmt.Sprintf("%T", pageResult.List)))
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "分页查询用户结果类型错误")
	}

	userVOs := make([]*vo.UserVO, 0, len(userEntities))
	// TODO: 这里的循环调用 convertToUserVO 可能存在 N+1 问题，需要后续在 UserServiceImpl 中实现批量转换方法并预加载关联数据（如部门）来优化。
	for _, user := range userEntities {
		// --- 修正: 调用简化后的 convertToUserVO 并手动填充关联字段 ---
		userVO := userServiceImpl.convertToUserVO(user)
		if userVO == nil { // 添加 nil 检查
			s.GetLogger().Warn(ctx, "GetRoleUsers 转换用户VO时遇到nil用户", logger.WithField("userId", user.ID))
			continue
		}
		// 手动填充关联数据 (需要 UserServiceImpl 暴露或复制辅助函数，或直接调用)
		// 假设 userServiceImpl 有可访问的转换函数
		userVO.Roles = userServiceImpl.convertRolesToRoleVOs(user.Roles)
		userVO.AccountBooks = userServiceImpl.convertAccountBooksToSimpleVOs(user.AccountBooks)
		// --- 修正结束 ---

		userVOs = append(userVOs, userVO)
	}
	// 如果在转换过程中有任何错误，则返回第一个遇到的错误 (现在不再有转换错误，移除firstErr)
	// if firstErr != nil {
	// 	 return nil, firstErr
	// }

	// 构建分页视图对象
	pageVO := &vo.PageVO{
		List:     userVOs,             // 当前页的 VO 列表
		Total:    pageResult.Total,    // 数据库中的总记录数
		PageNum:  pageResult.PageNum,  // 当前页码
		PageSize: pageResult.PageSize, // 每页大小
		Pages:    pageResult.Pages,    // 总页数
	}

	return pageVO, nil
}

// CountRoleUsers 统计角色用户数量
func (s *RoleServiceImpl) CountRoleUsers(ctx context.Context, id uint) (int64, error) {
	s.GetLogger().Debug(ctx, "统计角色用户数量",
		logger.WithField("roleId", id))

	// 获取角色用户关联
	userRoleRepo := s.getUserRoleRepository()
	count, err := userRoleRepo.CountUsersByRole(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "统计角色用户数量失败",
			logger.WithField("roleId", id),
			logger.WithError(err))
		return 0, errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "统计角色用户数量失败")
	}

	return count, nil
}

// validateRoleParams 验证角色参数
func (s *RoleServiceImpl) validateRoleParams(ctx context.Context, roleDTO interface{}, id uint, roleRepo repository.RoleRepository) error {
	var name, code string

	switch dto := roleDTO.(type) {
	case dto.RoleCreateOrUpdateDTO:
		name = dto.Name
		code = dto.Code
	default:
		return errors.NewParamError(errors.CODE_PARAMS_INVALID, "角色参数类型错误")
	}

	if name != "" {
		role, err := roleRepo.FindByName(ctx, name)
		if err != nil {
			if errors.GetErrorCode(err) != errors.CODE_DATA_NOT_FOUND {
				s.GetLogger().Error(ctx, "验证角色名称唯一性失败", logger.WithField("name", name), logger.WithError(err))
				return errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询角色名称失败")
			}
		} else if role != nil && role.ID != id {
			return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "角色名称已存在")
		}
	}

	if code != "" {
		role, err := roleRepo.FindByCode(ctx, code)
		if err != nil {
			if errors.GetErrorCode(err) != errors.CODE_DATA_NOT_FOUND {
				s.GetLogger().Error(ctx, "验证角色编码唯一性失败", logger.WithField("code", code), logger.WithError(err))
				return errors.WrapError(err, errors.CODE_DATA_QUERY_FAILED, "查询角色编码失败")
			}
		} else if role != nil && role.ID != id {
			return errors.NewBusinessError(errors.CODE_BUSINESS_ERROR, "角色编码已存在")
		}
	}

	return nil
}

// buildRoleQueryConditions 构建角色查询条件
func (s *RoleServiceImpl) buildRoleQueryConditions(queryDTO dto.RolePageQueryDTO) []repository.Condition {
	conditions := make([]repository.Condition, 0)

	// 角色名称（模糊查询）
	if queryDTO.Name != "" {
		conditions = append(conditions, repository.NewLikeCondition("name", queryDTO.Name))
	}

	// 角色编码（模糊查询）
	if queryDTO.Code != "" {
		conditions = append(conditions, repository.NewLikeCondition("code", queryDTO.Code))
	}

	// 状态
	if queryDTO.Status != nil {
		conditions = append(conditions, repository.NewEqualCondition("status", *queryDTO.Status))
	}

	return conditions
}

// convertToRoleVO 将角色实体转换为视图对象 (仅基础字段)
func (s *RoleServiceImpl) convertToRoleVO(role *entity.Role) *vo.RoleVO {
	if role == nil {
		return nil
	}

	// 创建基础的BaseVO和TenantVO
	baseVO := vo.BaseVO{
		ID:        role.ID,
		CreatedAt: role.CreatedAt,
		UpdatedAt: role.UpdatedAt,
		CreatedBy: role.CreatedBy,
		UpdatedBy: role.UpdatedBy,
	}

	tenantVO := vo.TenantVO{
		BaseVO:   baseVO,
		TenantID: role.TenantID,
	}

	// 只转换基础字段，MenuIDs 和 UserCount 由调用方填充
	return &vo.RoleVO{
		TenantVO: tenantVO,
		Name:     role.Name,
		Code:     role.Code,
		Status:   role.Status,
		IsSystem: role.IsSystem,
		Remark:   role.Remark,
		// MenuIDs 和 UserCount 在这里不填充
	}
}

// convertToRoleVOList 将角色实体列表转换为视图对象列表 (优化 N+1)
func (s *RoleServiceImpl) convertToRoleVOList(ctx context.Context, roles []*entity.Role) ([]*vo.RoleVO, error) {
	if len(roles) == 0 {
		return []*vo.RoleVO{}, nil
	}

	// 1. 提取角色 IDs
	roleIDs := make([]uint, 0, len(roles))
	for _, role := range roles {
		roleIDs = append(roleIDs, role.ID)
	}

	// 2. 批量获取菜单 ID 映射
	roleMenuRepo := s.getRoleMenuRepository()
	menuIDMap, menuErr := roleMenuRepo.FindMenuIDsByRoleIDs(ctx, roleIDs)
	if menuErr != nil {
		s.GetLogger().Error(ctx, "批量获取角色菜单ID失败", logger.WithError(menuErr), logger.WithField("roleIDs", roleIDs))
		// return nil, menuErr // 返回原始错误
		return nil, errors.WrapError(menuErr, errors.CODE_DATA_QUERY_FAILED, "获取角色菜单关联失败") // <<< 返回包装后的错误
	}

	// 3. 批量获取用户数量映射
	userRoleRepo := s.getUserRoleRepository()
	userCountMap, countErr := userRoleRepo.CountUsersByRoleIDs(ctx, roleIDs)
	if countErr != nil {
		s.GetLogger().Error(ctx, "批量获取角色用户数量失败", logger.WithError(countErr), logger.WithField("roleIDs", roleIDs))
		// return nil, countErr // 返回原始错误
		return nil, errors.WrapError(countErr, errors.CODE_DATA_QUERY_FAILED, "获取角色用户数量失败") // <<< 返回包装后的错误
	}

	// 4. 构建 VO 列表
	roleVOs := make([]*vo.RoleVO, 0, len(roles))
	for _, role := range roles {
		// 先进行基础转换
		roleVO := s.convertToRoleVO(role)

		// 从 map 中获取并设置关联数据 (此时 key 必然存在)
		roleVO.MenuIDs = menuIDMap[role.ID]
		roleVO.UserCount = int(userCountMap[role.ID])

		roleVOs = append(roleVOs, roleVO)
	}

	return roleVOs, nil // <<< 成功返回 nil error
}
