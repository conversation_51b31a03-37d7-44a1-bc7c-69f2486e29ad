package vo

import (
	"backend/internal/model/entity"
	"time"
)

// 辅助函数：处理指针类型转换
func getUintValue(ptr *uint) uint {
	if ptr == nil {
		return 0
	}
	return *ptr
}

func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

func getCountStrategyValue(ptr *entity.WmsCycleCountStrategy) entity.WmsCycleCountStrategy {
	if ptr == nil {
		return entity.WmsCycleCountStrategy("")
	}
	return *ptr
}

// WmsCycleCountPlanVO 盘点计划视图对象
type WmsCycleCountPlanVO struct {
	ID                  uint                           `json:"id"`
	PlanNo              string                         `json:"planNo"`
	PlanName            string                         `json:"planName"`
	CountType           entity.WmsCycleCountType       `json:"countType"`
	CountTypeName       string                         `json:"countTypeName"`
	CountStrategy       entity.WmsCycleCountStrategy   `json:"countStrategy"`
	CountStrategyName   string                         `json:"countStrategyName"`
	WarehouseID         uint                           `json:"warehouseId"`
	WarehouseName       string                         `json:"warehouseName"`
	PlannedDate         time.Time                      `json:"plannedDate"`
	ResponsibleUserID   uint                           `json:"responsibleUserId"`
	ResponsibleUserName string                         `json:"responsibleUserName"`
	Status              entity.WmsCycleCountPlanStatus `json:"status"`
	StatusName          string                         `json:"statusName"`
	Description         *string                        `json:"description"`
	ApprovedBy          *uint                          `json:"approvedBy"`
	ApprovedByName      *string                        `json:"approvedByName"`
	ApprovedAt          *time.Time                     `json:"approvedAt"`
	CreatedBy           uint                           `json:"createdBy"`
	CreatedByName       string                         `json:"createdByName"`
	CreatedAt           time.Time                      `json:"createdAt"`
	UpdatedAt           time.Time                      `json:"updatedAt"`

	// 统计信息
	TotalTasks        int64   `json:"totalTasks"`
	CompletedTasks    int64   `json:"completedTasks"`
	TasksWithVariance int64   `json:"tasksWithVariance"`
	CompletionRate    float64 `json:"completionRate"`

	// 业务状态
	CanApprove  bool `json:"canApprove"`
	CanStart    bool `json:"canStart"`
	CanComplete bool `json:"canComplete"`
	CanCancel   bool `json:"canCancel"`
}

// WmsCycleCountTaskVO 盘点任务视图对象
type WmsCycleCountTaskVO struct {
	ID                 uint                           `json:"id"`
	TaskNo             string                         `json:"taskNo"`
	PlanID             uint                           `json:"planId"`
	PlanNo             string                         `json:"planNo"`
	LocationID         uint                           `json:"locationId"`
	LocationCode       string                         `json:"locationCode"`
	LocationName       string                         `json:"locationName"`
	ItemID             uint                           `json:"itemId"`
	ItemSku            string                         `json:"itemSku"`
	ItemName           string                         `json:"itemName"`
	BatchNo            *string                        `json:"batchNo"`
	SystemQuantity     float64                        `json:"systemQuantity"`
	CountedQuantity    *float64                       `json:"countedQuantity"`
	VarianceQuantity   *float64                       `json:"varianceQuantity"`
	VariancePercentage *float64                       `json:"variancePercentage"`
	VarianceReason     *string                        `json:"varianceReason"`
	Status             entity.WmsCycleCountTaskStatus `json:"status"`
	StatusName         string                         `json:"statusName"`
	CounterUserID      *uint                          `json:"counterUserId"`
	CounterUserName    *string                        `json:"counterUserName"`
	AdjustmentRequired bool                           `json:"adjustmentRequired"`
	AdjustmentID       *uint                          `json:"adjustmentId"`
	CountedAt          *time.Time                     `json:"countedAt"`
	CreatedAt          time.Time                      `json:"createdAt"`
	UpdatedAt          time.Time                      `json:"updatedAt"`

	// 业务状态
	CanStart           bool `json:"canStart"`
	CanSubmitCount     bool `json:"canSubmitCount"`
	CanConfirmVariance bool `json:"canConfirmVariance"`
	CanRejectVariance  bool `json:"canRejectVariance"`
	HasVariance        bool `json:"hasVariance"`
}

// WmsCycleCountPlanDetailVO 盘点计划详情视图对象
type WmsCycleCountPlanDetailVO struct {
	WmsCycleCountPlanVO

	// 任务列表
	Tasks []WmsCycleCountTaskVO `json:"tasks"`

	// 统计详情
	Statistics *WmsCycleCountPlanStatsVO `json:"statistics"`

	// 操作历史
	OperationLogs []WmsCycleCountPlanLogVO `json:"operationLogs"`
}

// WmsCycleCountPlanLogVO 盘点计划操作日志视图对象
type WmsCycleCountPlanLogVO struct {
	ID           uint      `json:"id"`
	Operation    string    `json:"operation"`
	OperatorID   uint      `json:"operatorId"`
	OperatorName string    `json:"operatorName"`
	Description  string    `json:"description"`
	CreatedAt    time.Time `json:"createdAt"`
}

// WmsCycleCountPlanStatsVO 盘点计划统计视图对象
type WmsCycleCountPlanStatsVO struct {
	// 任务统计
	TotalTasks        int64 `json:"totalTasks"`
	PendingTasks      int64 `json:"pendingTasks"`
	InProgressTasks   int64 `json:"inProgressTasks"`
	CompletedTasks    int64 `json:"completedTasks"`
	TasksWithVariance int64 `json:"tasksWithVariance"`

	// 进度统计
	CompletionRate float64 `json:"completionRate"`
	VarianceRate   float64 `json:"varianceRate"`

	// 数量统计
	TotalSystemQty     float64 `json:"totalSystemQty"`
	TotalCountedQty    float64 `json:"totalCountedQty"`
	TotalVarianceQty   float64 `json:"totalVarianceQty"`
	AvgVariancePercent float64 `json:"avgVariancePercent"`

	// 按状态分组
	TasksByStatus map[string]int64 `json:"tasksByStatus"`

	// 按库位分组
	TasksByLocation []WmsCycleCountLocationStatsVO `json:"tasksByLocation"`

	// 按物料分组
	TasksByItem []WmsCycleCountItemStatsVO `json:"tasksByItem"`
}

// WmsCycleCountLocationStatsVO 按库位统计
type WmsCycleCountLocationStatsVO struct {
	LocationID     uint    `json:"locationId"`
	LocationCode   string  `json:"locationCode"`
	LocationName   string  `json:"locationName"`
	TaskCount      int64   `json:"taskCount"`
	CompletedCount int64   `json:"completedCount"`
	VarianceCount  int64   `json:"varianceCount"`
	CompletionRate float64 `json:"completionRate"`
	VarianceRate   float64 `json:"varianceRate"`
}

// WmsCycleCountItemStatsVO 按物料统计
type WmsCycleCountItemStatsVO struct {
	ItemID           uint    `json:"itemId"`
	ItemSku          string  `json:"itemSku"`
	ItemName         string  `json:"itemName"`
	TaskCount        int64   `json:"taskCount"`
	CompletedCount   int64   `json:"completedCount"`
	VarianceCount    int64   `json:"varianceCount"`
	TotalSystemQty   float64 `json:"totalSystemQty"`
	TotalCountedQty  float64 `json:"totalCountedQty"`
	TotalVarianceQty float64 `json:"totalVarianceQty"`
}

// WmsCycleCountCounterStatsVO 盘点员统计视图对象
type WmsCycleCountCounterStatsVO struct {
	CounterUserID     uint    `json:"counterUserId"`
	CounterUserName   string  `json:"counterUserName"`
	TotalTasks        int64   `json:"totalTasks"`
	CompletedTasks    int64   `json:"completedTasks"`
	TasksWithVariance int64   `json:"tasksWithVariance"`
	AverageAccuracy   float64 `json:"averageAccuracy"`
	CompletionRate    float64 `json:"completionRate"`
	VarianceRate      float64 `json:"varianceRate"`
	AvgCompletionTime float64 `json:"avgCompletionTime"` // 平均完成时间(分钟)

	// 时间范围
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`

	// 按日期分组的统计
	DailyStats []WmsCycleCountCounterDailyStatsVO `json:"dailyStats"`
}

// WmsCycleCountCounterDailyStatsVO 盘点员每日统计
type WmsCycleCountCounterDailyStatsVO struct {
	Date              string  `json:"date"`
	TaskCount         int64   `json:"taskCount"`
	CompletedCount    int64   `json:"completedCount"`
	VarianceCount     int64   `json:"varianceCount"`
	AccuracyRate      float64 `json:"accuracyRate"`
	AvgCompletionTime float64 `json:"avgCompletionTime"`
}

// WmsCycleCountVarianceVO 盘点差异视图对象
type WmsCycleCountVarianceVO struct {
	TaskID             uint      `json:"taskId"`
	TaskNo             string    `json:"taskNo"`
	LocationCode       string    `json:"locationCode"`
	ItemSku            string    `json:"itemSku"`
	ItemName           string    `json:"itemName"`
	BatchNo            *string   `json:"batchNo"`
	SystemQuantity     float64   `json:"systemQuantity"`
	CountedQuantity    float64   `json:"countedQuantity"`
	VarianceQuantity   float64   `json:"varianceQuantity"`
	VariancePercentage float64   `json:"variancePercentage"`
	VarianceReason     *string   `json:"varianceReason"`
	CounterUserName    string    `json:"counterUserName"`
	CountedAt          time.Time `json:"countedAt"`
}

// WmsCycleCountSummaryVO 盘点汇总视图对象
type WmsCycleCountSummaryVO struct {
	// 计划统计
	TotalPlans      int64 `json:"totalPlans"`
	CompletedPlans  int64 `json:"completedPlans"`
	InProgressPlans int64 `json:"inProgressPlans"`
	PendingPlans    int64 `json:"pendingPlans"`

	// 任务统计
	TotalTasks        int64 `json:"totalTasks"`
	CompletedTasks    int64 `json:"completedTasks"`
	TasksWithVariance int64 `json:"tasksWithVariance"`

	// 效率统计
	AvgCompletionTime float64 `json:"avgCompletionTime"` // 平均完成时间(小时)
	VarianceRate      float64 `json:"varianceRate"`      // 差异率

	// 时间范围
	DateStart *time.Time `json:"dateStart"`
	DateEnd   *time.Time `json:"dateEnd"`

	// 趋势数据
	TrendData []WmsCycleCountTrendVO `json:"trendData"`
}

// WmsCycleCountTrendVO 盘点趋势数据
type WmsCycleCountTrendVO struct {
	Date           string  `json:"date"`
	PlanCount      int64   `json:"planCount"`
	TaskCount      int64   `json:"taskCount"`
	CompletedCount int64   `json:"completedCount"`
	VarianceCount  int64   `json:"varianceCount"`
	VarianceRate   float64 `json:"varianceRate"`
}

// WmsCycleCountPlanPageVO 盘点计划分页视图对象
type WmsCycleCountPlanPageVO struct {
	List  []WmsCycleCountPlanVO `json:"list"`
	Total int64                 `json:"total"`
}

// WmsCycleCountTaskPageVO 盘点任务分页视图对象
type WmsCycleCountTaskPageVO struct {
	List  []WmsCycleCountTaskVO `json:"list"`
	Total int64                 `json:"total"`
}

// NewWmsCycleCountPlanVO 创建盘点计划视图对象
func NewWmsCycleCountPlanVO(plan *entity.WmsCycleCountPlan) *WmsCycleCountPlanVO {
	vo := &WmsCycleCountPlanVO{
		ID:                plan.ID,
		PlanNo:            plan.PlanNo,
		PlanName:          plan.PlanName,
		CountType:         plan.CountType,
		CountTypeName:     plan.GetCountTypeName(),
		CountStrategy:     getCountStrategyValue(plan.CountStrategy),
		CountStrategyName: plan.GetCountStrategyName(),
		WarehouseID:       getUintValue(plan.WarehouseID),
		PlannedDate:       plan.PlannedDate,
		ResponsibleUserID: getUintValue(plan.ResponsibleUserID),
		Status:            plan.Status,
		StatusName:        plan.GetStatusName(),
		Description:       plan.Description,
		ApprovedBy:        plan.ApprovedBy,
		ApprovedAt:        plan.ApprovedAt,
		CreatedBy:         plan.CreatedBy,
		CreatedAt:         plan.CreatedAt,
		UpdatedAt:         plan.UpdatedAt,
		CanApprove:        plan.CanApprove(),
		CanStart:          plan.CanStart(),
		CanComplete:       plan.CanComplete(),
		CanCancel:         plan.CanCancel(),
	}

	// 填充关联信息 - 由于移除了关联关系，这些信息需要在Service层单独查询并填充
	// if plan.Warehouse != nil {
	//     vo.WarehouseName = getStringValue(plan.Warehouse.Name)
	// }

	// if plan.ResponsibleUser != nil {
	//     vo.ResponsibleUserName = plan.ResponsibleUser.Username
	// }

	// CreatedByName should be filled by the service layer through a separate query
	// since we don't have an explicit Creator relationship

	// if plan.Approver != nil {
	//     approverName := plan.Approver.Username
	//     vo.ApprovedByName = &approverName
	// }

	return vo
}

// NewWmsCycleCountTaskVO 创建盘点任务视图对象
func NewWmsCycleCountTaskVO(task *entity.WmsCycleCountTask) *WmsCycleCountTaskVO {
	vo := &WmsCycleCountTaskVO{
		ID:                 task.ID,
		TaskNo:             task.TaskNo,
		PlanID:             task.PlanID,
		LocationID:         task.LocationID,
		ItemID:             getUintValue(task.ItemID),
		BatchNo:            task.BatchNo,
		SystemQuantity:     task.SystemQuantity,
		CountedQuantity:    task.CountedQuantity,
		VarianceQuantity:   task.VarianceQuantity,
		VariancePercentage: task.VariancePercentage,
		VarianceReason:     task.VarianceReason,
		Status:             task.Status,
		StatusName:         task.GetStatusName(),
		CounterUserID:      task.CounterUserID,
		AdjustmentRequired: task.AdjustmentRequired,
		AdjustmentID:       task.AdjustmentID,
		CountedAt:          task.CountedAt,
		CreatedAt:          task.CreatedAt,
		UpdatedAt:          task.UpdatedAt,
		CanStart:           task.CanStart(),
		CanSubmitCount:     task.CanSubmitCount(),
		CanConfirmVariance: task.CanConfirmVariance(),
		CanRejectVariance:  task.CanRejectVariance(),
		HasVariance:        task.HasVariance(),
	}

	// 填充关联信息
	if task.Plan != nil {
		vo.PlanNo = task.Plan.PlanNo
	}

	if task.Location != nil {
		vo.LocationCode = task.Location.Code
		if task.Location.Name != nil {
			vo.LocationName = *task.Location.Name
		}
	}

	if task.Item != nil {
		vo.ItemSku = task.Item.Sku
		vo.ItemName = task.Item.Name
	}

	if task.CounterUser != nil {
		counterUserName := task.CounterUser.Username
		vo.CounterUserName = &counterUserName
	}

	return vo
}

// GetVarianceDirection 获取差异方向描述
func (vo *WmsCycleCountTaskVO) GetVarianceDirection() string {
	if vo.VarianceQuantity == nil {
		return "-"
	}

	if *vo.VarianceQuantity > 0 {
		return "盘盈"
	} else if *vo.VarianceQuantity < 0 {
		return "盘亏"
	}
	return "无差异"
}

// GetVarianceAmount 获取差异数量的绝对值
func (vo *WmsCycleCountTaskVO) GetVarianceAmount() float64 {
	if vo.VarianceQuantity == nil {
		return 0
	}

	if *vo.VarianceQuantity < 0 {
		return -*vo.VarianceQuantity
	}
	return *vo.VarianceQuantity
}
