package vo

import (
	"time"
)

// WmsShipmentVO 发运单视图对象
type WmsShipmentVO struct {
	ID               uint      `json:"id"`
	ShipmentNo       string    `json:"shipmentNo"`
	PickingTaskID    uint      `json:"pickingTaskId"`
	PickingTaskNo    *string   `json:"pickingTaskNo"`    // 扩展字段：拣货任务号
	NotificationID   uint      `json:"notificationId"`
	NotificationNo   *string   `json:"notificationNo"`   // 扩展字段：出库通知单号
	
	// 承运商信息
	CarrierID        *uint     `json:"carrierId"`
	CarrierName      *string   `json:"carrierName"`
	TrackingNo       *string   `json:"trackingNo"`
	ShippingMethod   *string   `json:"shippingMethod"`
	
	// 发运信息
	ShipmentDate     *string   `json:"shipmentDate"`
	EstimatedDelivery *string  `json:"estimatedDelivery"`
	ActualDelivery   *string   `json:"actualDelivery"`
	
	// 包装信息
	PackageCount     int       `json:"packageCount"`
	TotalWeight      *float64  `json:"totalWeight"`
	TotalVolume      *float64  `json:"totalVolume"`
	
	// 费用信息
	ShippingCost     *float64  `json:"shippingCost"`
	InsuranceAmount  *float64  `json:"insuranceAmount"`
	
	// 收货人信息
	ConsigneeName    string    `json:"consigneeName"`
	ConsigneePhone   *string   `json:"consigneePhone"`
	ConsigneeAddress *string   `json:"consigneeAddress"`
	
	Status           string    `json:"status"`
	StatusName       *string   `json:"statusName"`       // 扩展字段：状态名称
	Remark           *string   `json:"remark"`
	
	// 统计信息
	TotalItems       int       `json:"totalItems"`       // 总物料种类数
	TotalQty         float64   `json:"totalQty"`         // 总数量
	
	// 时效信息
	ProcessingTime   *int      `json:"processingTime"`   // 处理时间（小时）
	TransitTime      *int      `json:"transitTime"`      // 运输时间（小时）
	IsOnTime         *bool     `json:"isOnTime"`         // 是否准时送达
	
	// 时间字段
	CreatedAt        time.Time `json:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt"`
	CreatedBy        uint      `json:"createdBy"`
	UpdatedBy        uint      `json:"updatedBy"`
	CreatedByName    *string   `json:"createdByName"`    // 扩展字段：创建人姓名
	UpdatedByName    *string   `json:"updatedByName"`    // 扩展字段：更新人姓名
	
	// 关联数据
	PickingTask      *WmsPickingTaskVO          `json:"pickingTask,omitempty"`
	OutboundNotification *WmsOutboundNotificationVO `json:"outboundNotification,omitempty"`
	TrackingHistory  []WmsShipmentTrackingVO    `json:"trackingHistory,omitempty"`
}

// WmsShipmentListVO 发运单列表视图对象
type WmsShipmentListVO struct {
	ID               uint      `json:"id"`
	ShipmentNo       string    `json:"shipmentNo"`
	NotificationNo   *string   `json:"notificationNo"`
	CarrierName      *string   `json:"carrierName"`
	TrackingNo       *string   `json:"trackingNo"`
	ShippingMethod   *string   `json:"shippingMethod"`
	ShipmentDate     *string   `json:"shipmentDate"`
	EstimatedDelivery *string  `json:"estimatedDelivery"`
	ActualDelivery   *string   `json:"actualDelivery"`
	ConsigneeName    string    `json:"consigneeName"`
	PackageCount     int       `json:"packageCount"`
	TotalWeight      *float64  `json:"totalWeight"`
	ShippingCost     *float64  `json:"shippingCost"`
	Status           string    `json:"status"`
	StatusName       *string   `json:"statusName"`
	TotalItems       int       `json:"totalItems"`
	TotalQty         float64   `json:"totalQty"`
	IsOnTime         *bool     `json:"isOnTime"`
	CreatedAt        time.Time `json:"createdAt"`
	CreatedByName    *string   `json:"createdByName"`
}

// WmsShipmentStatsVO 发运单统计视图对象
type WmsShipmentStatsVO struct {
	// 基础统计
	TotalCount       int     `json:"totalCount"`       // 总发运单数
	TotalPackages    int     `json:"totalPackages"`    // 总包裹数
	TotalWeight      float64 `json:"totalWeight"`      // 总重量
	TotalVolume      float64 `json:"totalVolume"`      // 总体积
	TotalCost        float64 `json:"totalCost"`        // 总运费
	
	// 状态统计
	PreparingCount   int     `json:"preparingCount"`   // 准备中数量
	ReadyCount       int     `json:"readyCount"`       // 待发运数量
	ShippedCount     int     `json:"shippedCount"`     // 已发运数量
	InTransitCount   int     `json:"inTransitCount"`   // 运输中数量
	DeliveredCount   int     `json:"deliveredCount"`   // 已送达数量
	ReturnedCount    int     `json:"returnedCount"`    // 已退回数量
	
	// 效率统计
	AvgProcessingTime float64 `json:"avgProcessingTime"` // 平均处理时间（小时）
	AvgTransitTime   float64 `json:"avgTransitTime"`    // 平均运输时间（小时）
	OnTimeDeliveryRate float64 `json:"onTimeDeliveryRate"` // 准时交付率
	DamageRate       float64 `json:"damageRate"`        // 破损率
	
	// 承运商统计
	CarrierStats     []WmsShipmentCarrierStatsVO `json:"carrierStats,omitempty"`
	
	// 趋势数据
	TrendData        []WmsShipmentTrendVO `json:"trendData,omitempty"`
}

// WmsShipmentCarrierStatsVO 承运商统计
type WmsShipmentCarrierStatsVO struct {
	CarrierID        uint    `json:"carrierId"`
	CarrierName      string  `json:"carrierName"`
	ShipmentCount    int     `json:"shipmentCount"`
	TotalCost        float64 `json:"totalCost"`
	AvgCost          float64 `json:"avgCost"`
	OnTimeRate       float64 `json:"onTimeRate"`
	DamageRate       float64 `json:"damageRate"`
}

// WmsShipmentTrendVO 发运单趋势数据
type WmsShipmentTrendVO struct {
	Date         string  `json:"date"`         // 日期
	Count        int     `json:"count"`        // 发运单数量
	Packages     int     `json:"packages"`     // 包裹数量
	Weight       float64 `json:"weight"`       // 总重量
	Cost         float64 `json:"cost"`         // 总运费
	OnTimeRate   float64 `json:"onTimeRate"`   // 准时率
}

// WmsShipmentTrackingVO 发运跟踪记录视图对象
type WmsShipmentTrackingVO struct {
	ID           uint      `json:"id"`
	ShipmentID   uint      `json:"shipmentId"`
	Status       string    `json:"status"`
	StatusName   *string   `json:"statusName"`
	Location     *string   `json:"location"`
	Description  *string   `json:"description"`
	TrackingTime time.Time `json:"trackingTime"`
	Operator     *string   `json:"operator"`
	Remark       *string   `json:"remark"`
}

// WmsShipmentCostVO 运费计算结果视图对象
type WmsShipmentCostVO struct {
	ShipmentID     uint    `json:"shipmentId"`
	CarrierID      uint    `json:"carrierId"`
	CarrierName    string  `json:"carrierName"`
	ShippingMethod string  `json:"shippingMethod"`
	BaseCost       float64 `json:"baseCost"`       // 基础运费
	WeightCost     float64 `json:"weightCost"`     // 重量费用
	VolumeCost     float64 `json:"volumeCost"`     // 体积费用
	DistanceCost   float64 `json:"distanceCost"`   // 距离费用
	ServiceCost    float64 `json:"serviceCost"`    // 服务费用
	TotalCost      float64 `json:"totalCost"`      // 总费用
	Currency       string  `json:"currency"`       // 币种
	ValidUntil     *time.Time `json:"validUntil"`  // 报价有效期
}

// WmsCarrierVO 承运商视图对象
type WmsCarrierVO struct {
	ID             uint                        `json:"id"`
	CarrierCode    string                      `json:"carrierCode"`
	CarrierName    string                      `json:"carrierName"`
	ContactPerson  *string                     `json:"contactPerson"`
	ContactPhone   *string                     `json:"contactPhone"`
	ContactEmail   *string                     `json:"contactEmail"`
	Address        *string                     `json:"address"`
	Status         string                      `json:"status"`
	StatusName     *string                     `json:"statusName"`
	ServiceArea    *string                     `json:"serviceArea"`    // 服务区域
	Rating         *float64                    `json:"rating"`         // 评级
	Remark         *string                     `json:"remark"`
	
	// 统计信息
	ShipmentCount  int                         `json:"shipmentCount"`  // 发运单数量
	OnTimeRate     float64                     `json:"onTimeRate"`     // 准时率
	DamageRate     float64                     `json:"damageRate"`     // 破损率
	AvgCost        float64                     `json:"avgCost"`        // 平均运费
	
	// 支持的运输方式
	ShippingMethods []WmsCarrierShippingMethodVO `json:"shippingMethods,omitempty"`
	
	// 时间字段
	CreatedAt      time.Time                   `json:"createdAt"`
	UpdatedAt      time.Time                   `json:"updatedAt"`
}

// WmsCarrierShippingMethodVO 承运商运输方式
type WmsCarrierShippingMethodVO struct {
	ID             uint    `json:"id"`
	CarrierID      uint    `json:"carrierId"`
	MethodCode     string  `json:"methodCode"`
	MethodName     string  `json:"methodName"`
	Description    *string `json:"description"`
	BaseCost       float64 `json:"baseCost"`       // 基础费用
	WeightRate     float64 `json:"weightRate"`     // 重量费率
	VolumeRate     float64 `json:"volumeRate"`     // 体积费率
	DistanceRate   float64 `json:"distanceRate"`   // 距离费率
	MinCost        float64 `json:"minCost"`        // 最低费用
	MaxWeight      *float64 `json:"maxWeight"`     // 最大重量限制
	MaxVolume      *float64 `json:"maxVolume"`     // 最大体积限制
	EstimatedDays  *int    `json:"estimatedDays"`  // 预计天数
	IsActive       bool    `json:"isActive"`
}

// WmsShipmentLabelVO 发运标签视图对象
type WmsShipmentLabelVO struct {
	ShipmentID     uint    `json:"shipmentId"`
	ShipmentNo     string  `json:"shipmentNo"`
	LabelType      string  `json:"labelType"`
	LabelData      string  `json:"labelData"`      // 标签数据（Base64编码）
	LabelFormat    string  `json:"labelFormat"`    // 标签格式（PDF, PNG等）
	PrintCount     int     `json:"printCount"`
	GeneratedAt    time.Time `json:"generatedAt"`
}
