<template>
    <div class="rule-explanation">
      <!-- 概述 -->
      <div class="explanation-section">
        <h3 class="section-title">
          <el-icon><InfoFilled /></el-icon>
          盲收验证概述
        </h3>
        <div class="section-content">
          <p class="overview-text">
            盲收验证是WMS系统中的重要功能，用于控制在没有预先创建入库通知单的情况下直接进行收货的权限。
            系统通过多层级配置和策略模式，实现灵活的权限控制和业务流程管理。
          </p>
          
          <el-alert 
            title="重要提示" 
            type="warning" 
            :closable="false"
          >
            盲收验证的结果将直接影响收货操作的执行，请确保配置的准确性和合理性。
          </el-alert>
        </div>
      </div>
  
      <!-- 配置层级说明 -->
      <div class="explanation-section">
        <h3 class="section-title">
          <el-icon><Rank /></el-icon>
          配置层级优先级
        </h3>
        <div class="section-content">
          <div class="priority-flow">
            <div class="priority-item" v-for="(level, index) in configLevels" :key="level.value">
              <div class="priority-card" :class="`priority-${index + 1}`">
                <div class="priority-header">
                  <el-icon class="priority-icon">
                    <component :is="level.icon" />
                  </el-icon>
                  <div class="priority-info">
                    <h4 class="priority-title">{{ level.label }}</h4>
                    <p class="priority-desc">{{ level.description }}</p>
                  </div>
                </div>
                <div class="priority-example">
                  <strong>示例：</strong>{{ level.example }}
                </div>
              </div>
              <div class="priority-arrow" v-if="index < configLevels.length - 1">
                <el-icon><ArrowDown /></el-icon>
              </div>
            </div>
          </div>
          
          <el-alert 
            title="优先级规则" 
            type="info" 
            :closable="false"
          >
            系统按照 用户级 > 客户级 > 仓库级 > 系统级 的优先级顺序查找有效配置。
            一旦找到匹配的配置，将停止向下查找并应用该配置。
          </el-alert>
        </div>
      </div>
  
      <!-- 验证策略说明 -->
      <div class="explanation-section">
        <h3 class="section-title">
          <el-icon><Shield /></el-icon>
          验证策略详解
        </h3>
        <div class="section-content">
          <div class="strategy-grid">
            <div 
              class="strategy-card" 
              v-for="strategy in strategies" 
              :key="strategy.value"
              :class="`strategy-${strategy.value.toLowerCase()}`"
            >
              <div class="strategy-header">
                <el-icon class="strategy-icon">
                  <component :is="strategy.icon" />
                </el-icon>
                <div class="strategy-info">
                  <h4 class="strategy-title">{{ strategy.label }}</h4>
                  <el-tag 
                    :type="strategy.tagType" 
                    size="small" 
                    effect="plain"
                  >
                    {{ strategy.value }}
                  </el-tag>
                </div>
              </div>
              
              <div class="strategy-description">
                {{ strategy.description }}
              </div>
              
              <div class="strategy-details">
                <div class="detail-item">
                  <strong>适用场景：</strong>
                  <span>{{ strategy.scenario }}</span>
                </div>
                <div class="detail-item">
                  <strong>业务影响：</strong>
                  <span>{{ strategy.impact }}</span>
                </div>
                <div class="detail-item" v-if="strategy.workflow">
                  <strong>工作流程：</strong>
                  <span>{{ strategy.workflow }}</span>
                </div>
              </div>
              
              <div class="strategy-example" v-if="strategy.examples">
                <strong>示例：</strong>
                <ul class="example-list">
                  <li v-for="example in strategy.examples" :key="example">
                    {{ example }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 验证流程说明 -->
      <div class="explanation-section">
        <h3 class="section-title">
          <el-icon><Connection /></el-icon>
          验证流程图
        </h3>
        <div class="section-content">
          <div class="flow-diagram">
            <VNChart
              type="flowchart"
              :data="flowchartData"
              :options="flowchartOptions"
              height="400px"
            />
          </div>
          
          <div class="flow-steps">
            <h4>详细步骤说明：</h4>
            <ol class="steps-list">
              <li>
                <strong>参数验证：</strong>
                检查请求参数的完整性和有效性，包括仓库ID、用户信息、商品信息等。
              </li>
              <li>
                <strong>配置查找：</strong>
                按照优先级顺序查找匹配的盲收配置，从用户级开始，逐级向下查找。
              </li>
              <li>
                <strong>策略判断：</strong>
                根据找到的配置确定验证策略（严格/补录/完全模式）。
              </li>
              <li>
                <strong>权限检查：</strong>
                基于选定的策略和配置参数，判断是否允许盲收操作。
              </li>
              <li>
                <strong>审批验证：</strong>
                如果配置要求审批，检查用户是否具备相应的审批权限或流程状态。
              </li>
              <li>
                <strong>数量限制：</strong>
                检查请求数量是否超过配置的最大限制。
              </li>
              <li>
                <strong>结果返回：</strong>
                返回验证结果，包括是否允许、策略信息、审批要求等详细信息。
              </li>
              <li>
                <strong>记录保存：</strong>
                将验证过程和结果保存到历史记录中，用于审计和分析。
              </li>
            </ol>
          </div>
        </div>
      </div>
  
      <!-- 常见问题解答 -->
      <div class="explanation-section">
        <h3 class="section-title">
          <el-icon><QuestionFilled /></el-icon>
          常见问题解答
        </h3>
        <div class="section-content">
          <el-collapse v-model="activeFaq">
            <el-collapse-item 
              v-for="faq in faqs"
              :key="faq.id"
              :title="faq.question"
              :name="faq.id"
            >
              <div class="faq-answer" v-html="faq.answer"></div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
  
      <!-- 最佳实践建议 -->
      <div class="explanation-section">
        <h3 class="section-title">
          <el-icon><Star /></el-icon>
          最佳实践建议
        </h3>
        <div class="section-content">
          <div class="best-practices">
            <div class="practice-item" v-for="practice in bestPractices" :key="practice.id">
              <div class="practice-header">
                <el-icon class="practice-icon" :class="practice.iconClass">
                  <component :is="practice.icon" />
                </el-icon>
                <h4 class="practice-title">{{ practice.title }}</h4>
              </div>
              <p class="practice-content">{{ practice.content }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import {
    InfoFilled,
    Rank,
    Shield,
    Connection,
    QuestionFilled,
    Star,
    ArrowDown,
    User,
    OfficeBuilding,
    Warehouse,
    Setting,
    CircleCloseFilled,
    Clock,
    CircleCheckFilled,
    Warning,
    Check,
    Tools
  } from '@element-plus/icons-vue'
  import VNChart from '@/components/VNChart/index.vue'
  
  // 响应式数据
  const activeFaq = ref<string[]>([])
  
  // 配置层级数据
  const configLevels = [
    {
      value: 'USER',
      label: '用户级配置',
      description: '针对特定用户的个性化配置',
      example: '为资深收货员配置完全模式权限',
      icon: User
    },
    {
      value: 'CLIENT',
      label: '客户级配置',
      description: '针对特定客户的业务配置',
      example: '为重要客户配置补录模式',
      icon: OfficeBuilding
    },
    {
      value: 'WAREHOUSE',
      label: '仓库级配置',
      description: '针对特定仓库的操作配置',
      example: '为主仓配置严格模式',
      icon: Warehouse
    },
    {
      value: 'SYSTEM',
      label: '系统级配置',
      description: '全局默认配置，作为兜底策略',
      example: '系统默认禁止盲收',
      icon: Setting
    }
  ]
  
  // 验证策略数据
  const strategies = [
    {
      value: 'STRICT',
      label: '严格模式',
      description: '严格禁止盲收操作，必须先创建入库通知单才能收货',
      scenario: '高风险商品、严格管控的仓库环境',
      impact: '完全阻止盲收，确保所有收货都有预期',
      workflow: '拒绝 → 提示创建入库通知单',
      tagType: 'danger',
      icon: CircleCloseFilled,
      examples: [
        '医药产品仓库',
        '危化品存储区域',
        '高价值商品管理'
      ]
    },
    {
      value: 'SUPPLEMENT',
      label: '补录模式',
      description: '允许盲收，但要求在指定时间内补录入库通知单',
      scenario: '需要平衡效率和管控的业务场景',
      impact: '允许先收货，后期需要补充单据',
      workflow: '允许收货 → 记录待补录 → 到期提醒',
      tagType: 'warning',
      icon: Clock,
      examples: [
        '紧急收货处理',
        '临时供应商送货',
        '节假日应急收货'
      ]
    },
    {
      value: 'FULL',
      label: '完全模式',
      description: '完全允许盲收操作，无需创建或补录入库通知单',
      scenario: '标准化商品、成熟的供应链环境',
      impact: '完全自由收货，提高操作效率',
      workflow: '直接允许 → 记录收货信息',
      tagType: 'success',    
      icon: CircleCheckFilled,
      examples: [
        '标准化包装商品',
        '长期合作供应商',
        '低风险商品类别'
      ]
    }
  ]
  
  // 流程图数据
  const flowchartData = ref({
    nodes: [
      { id: 'start', label: '开始验证', type: 'start' },
      { id: 'params', label: '参数验证', type: 'process' },
      { id: 'config', label: '查找配置', type: 'process' },
      { id: 'strategy', label: '确定策略', type: 'decision' },
      { id: 'strict', label: '严格模式\n禁止盲收', type: 'process' },
      { id: 'supplement', label: '补录模式\n允许+补录', type: 'process' },
      { id: 'full', label: '完全模式\n完全允许', type: 'process' },
      { id: 'approval', label: '审批检查', type: 'decision' },
      { id: 'quantity', label: '数量限制', type: 'decision' },
      { id: 'result', label: '返回结果', type: 'process' },
      { id: 'end', label: '结束', type: 'end' }
    ],
    edges: [
      { from: 'start', to: 'params' },
      { from: 'params', to: 'config' },
      { from: 'config', to: 'strategy' },
      { from: 'strategy', to: 'strict', label: 'STRICT' },
      { from: 'strategy', to: 'supplement', label: 'SUPPLEMENT' },
      { from: 'strategy', to: 'full', label: 'FULL' },
      { from: 'strict', to: 'result' },
      { from: 'supplement', to: 'approval' },
      { from: 'full', to: 'approval' },
      { from: 'approval', to: 'quantity' },
      { from: 'quantity', to: 'result' },
      { from: 'result', to: 'end' }
    ]
  })
  
  const flowchartOptions = ref({
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'graph',
      layout: 'dagre',
      data: flowchartData.value.nodes,
      links: flowchartData.value.edges,
      roam: true,
      label: {
        show: true,
        position: 'inside'
      },
      lineStyle: {
        color: 'source',
        curveness: 0.3
      }
    }]
  })
  
  // 常见问题数据
  const faqs = [
    {
      id: '1',
      question: '为什么验证结果显示"禁止"？',
      answer: `
        <p>验证结果显示"禁止"可能有以下几种原因：</p>
        <ul>
          <li><strong>策略限制：</strong>当前配置使用严格模式，完全禁止盲收操作</li>
          <li><strong>权限不足：</strong>当前用户没有对应的盲收权限</li>
          <li><strong>数量超限：</strong>请求数量超过了配置的最大限制</li>
          <li><strong>审批未通过：</strong>需要审批但尚未获得批准</li>
        </ul>
        <p>建议检查配置设置或联系管理员处理。</p>
      `
    },
    {
      id: '2',
      question: '补录模式的截止时间如何计算？',
      answer: `
        <p>补录截止时间的计算规则如下：</p>
        <ul>
          <li><strong>从收货时间开始计算：</strong>以实际收货操作的时间为起点</li>
          <li><strong>配置的补录期限：</strong>根据配置中设定的补录天数计算</li>
          <li><strong>工作日计算：</strong>可以配置为仅计算工作日或包含周末</li>
          <li><strong>节假日处理：</strong>系统会自动排除法定节假日</li>
        </ul>
        <p>例如：如果配置补录期限为3个工作日，周五收货，则截止时间为下周三。</p>
      `
    },
    {
      id: '3',
      question: '如何配置多层级的盲收权限？',
      answer: `
        <p>多层级配置的设置步骤：</p>
        <ol>
          <li><strong>系统级：</strong>设置全局默认策略作为兜底</li>
          <li><strong>仓库级：</strong>为不同仓库设置差异化策略</li>
          <li><strong>客户级：</strong>为重要客户设置特殊政策</li>
          <li><strong>用户级：</strong>为特定用户设置个性化权限</li>
        </ol>
        <p>系统会自动按照优先级查找匹配的配置，确保策略的精确性和灵活性。</p>
      `
    },
    {
      id: '4',
      question: '验证失败后如何处理？',
      answer: `
        <p>验证失败后的处理方式：</p>
        <ul>
          <li><strong>严格模式：</strong>必须先创建入库通知单再进行收货</li>
          <li><strong>权限问题：</strong>联系管理员调整配置或申请临时权限</li>
          <li><strong>审批流程：</strong>提交审批申请，等待相关人员批准</li>
          <li><strong>数量调整：</strong>将收货数量调整到允许范围内</li>
        </ul>
        <p>所有验证记录都会保存在历史中，便于后续查看和分析。</p>
      `
    }
  ]
  
  // 最佳实践数据
  const bestPractices = [
    {
      id: '1',
      title: '合理设置配置层级',
      content: '建议按照业务重要性和风险等级设置不同层级的配置，确保关键业务有适当的管控，同时不影响正常操作效率。',
      icon: Rank,
      iconClass: 'practice-primary'
    },
    {
      id: '2',
      title: '定期审查验证记录',
      content: '建议定期查看验证历史和统计数据，分析业务模式和异常情况，及时调整配置策略以适应业务变化。',
      icon: TrendCharts,
      iconClass: 'practice-success'
    },
    {
      id: '3',
      title: '设置合理的补录期限',
      content: '补录期限应该考虑业务流程的实际需要，既要保证单据的及时性，又要避免因期限过短而影响正常业务操作。',
      icon: Clock,
      iconClass: 'practice-warning'
    },
    {
      id: '4',
      title: '建立审批流程',
      content: '对于需要审批的场景，建议建立清晰的审批流程和权限体系，确保审批的及时性和有效性。',
      icon: User,
      iconClass: 'practice-info'
    },
    {
      id: '5',
      title: '做好用户培训',
      content: '对相关操作人员进行充分的培训，确保他们理解各种验证策略的含义和操作流程，减少误操作。',
      icon: Tools,
      iconClass: 'practice-primary'
    }
  ]
  </script>
  
  <style scoped lang="scss">
  .rule-explanation {
    .explanation-section {
      margin-bottom: 32px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 20px 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }
      
      .section-content {
        .overview-text {
          color: #606266;
          line-height: 1.6;
          margin-bottom: 16px;
        }
      }
    }
    
    // 优先级流程
    .priority-flow {
      .priority-item {
        .priority-card {
          border: 2px solid #ebeef5;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 16px;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #409eff;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
          }
          
          &.priority-1 {
            border-color: #e6a23c;
            background: linear-gradient(135deg, #fdf6ec 0%, #fef7ee 100%);
          }
          
          &.priority-2 {
            border-color: #67c23a;
            background: linear-gradient(135deg, #f0f9ff 0%, #f0fdf4 100%);
          }
          
          &.priority-3 {
            border-color: #409eff;
            background: linear-gradient(135deg, #ecf5ff 0%, #eff6ff 100%);
          }
          
          &.priority-4 {
            border-color: #909399;
            background: linear-gradient(135deg, #f4f4f5 0%, #f8f9fa 100%);
          }
          
          .priority-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            
            .priority-icon {
              font-size: 24px;
              color: #409eff;
            }
            
            .priority-info {
              .priority-title {
                margin: 0 0 4px 0;
                color: #303133;
                font-size: 16px;
                font-weight: 600;
              }
              
              .priority-desc {
                margin: 0;
                color: #606266;
                font-size: 14px;
              }
            }
          }
          
          .priority-example {
            color: #909399;
            font-size: 13px;
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
          }
        }
        
        .priority-arrow {
          text-align: center;
          margin: 8px 0;
          
          .el-icon {
            font-size: 20px;
            color: #409eff;
          }
        }
      }
    }
    
    // 策略网格
    .strategy-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      
      .strategy-card {
        border: 2px solid #ebeef5;
        border-radius: 8px;
        padding: 20px;
        background: #fff;
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }
        
        &.strategy-strict {
          border-color: #f56c6c;
          
          &:hover {
            border-color: #f56c6c;
            box-shadow: 0 6px 20px rgba(245, 108, 108, 0.2);
          }
        }
        
        &.strategy-supplement {
          border-color: #e6a23c;
          
          &:hover {
            border-color: #e6a23c;
            box-shadow: 0 6px 20px rgba(230, 162, 60, 0.2);
          }
        }
        
        &.strategy-full {
          border-color: #67c23a;
          
          &:hover {
            border-color: #67c23a;
            box-shadow: 0 6px 20px rgba(103, 194, 58, 0.2);
          }
        }
        
        .strategy-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;
          
          .strategy-icon {
            font-size: 32px;
          }
          
          .strategy-info {
            flex: 1;
            
            .strategy-title {
              margin: 0 0 8px 0;
              color: #303133;
              font-size: 18px;
              font-weight: 600;
            }
          }
        }
        
        .strategy-description {
          color: #606266;
          line-height: 1.5;
          margin-bottom: 16px;
        }
        
        .strategy-details {
          margin-bottom: 16px;
          
          .detail-item {
            margin-bottom: 8px;
            font-size: 14px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            strong {
              color: #303133;
              min-width: 80px;
              display: inline-block;
            }
            
            span {
              color: #606266;
            }
          }
        }
        
        .strategy-example {
          padding: 12px;
          background-color: #f8f9fa;
          border-radius: 4px;
          font-size: 13px;
          
          strong {
            color: #303133;
          }
          
          .example-list {
            margin: 8px 0 0 0;
            padding-left: 16px;
            
            li {
              color: #606266;
              margin-bottom: 4px;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
    
    // 流程图
    .flow-diagram {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 6px;
    }
    
    .flow-steps {
      h4 {
        color: #303133;
        margin: 0 0 16px 0;
      }
      
      .steps-list {
        color: #606266;
        line-height: 1.6;
        
        li {
          margin-bottom: 12px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          strong {
            color: #303133;
          }
        }
      }
    }
    
    // 常见问题
    .faq-answer {
      color: #606266;
      line-height: 1.6;
      
      p {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      ul, ol {
        margin: 12px 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          strong {
            color: #303133;
          }
        }
      }
    }
    
    // 最佳实践
    .best-practices {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      
      .practice-item {
        padding: 20px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        background: #fff;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #409eff;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }
        
        .practice-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          
          .practice-icon {
            font-size: 24px;
            
            &.practice-primary {
              color: #409eff;
            }
            
            &.practice-success {
              color: #67c23a;
            }
            
            &.practice-warning {
              color: #e6a23c;
            }
            
            &.practice-info {
              color: #909399;
            }
          }
          
          .practice-title {
            margin: 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
          }
        }
        
        .practice-content {
          margin: 0;
          color: #606266;
          line-height: 1.5;
        }
      }
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .rule-explanation {
      .strategy-grid {
        grid-template-columns: 1fr;
      }
      
      .best-practices {
        grid-template-columns: 1fr;
      }
      
      .priority-flow .priority-item .priority-card {
        padding: 16px;
      }
    }
  }
  </style>