package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository" // Import repository package
	"backend/pkg/response"        // <<< 添加对 response 包的导入
	"context"
	"errors" // <--- 导入标准库
	"fmt"

	apperrors "backend/pkg/errors"
	"backend/pkg/util" // <<< ADDED IMPORT for util

	// Assuming util package for copying fields
	// Import cache package if used
	// import "backend/pkg/cache"
	// import "time"
	"gorm.io/gorm"
)

// SystemParameterService 系统参数服务接口
type SystemParameterService interface {
	BaseService // 继承基础服务接口

	CreateParameter(ctx context.Context, req dto.SystemParameterCreateOrUpdateDTO) (*vo.SystemParameterVO, error)
	UpdateParameter(ctx context.Context, id uint, req dto.SystemParameterCreateOrUpdateDTO) (*vo.SystemParameterVO, error)
	DeleteParameter(ctx context.Context, id uint) error
	GetParameterByID(ctx context.Context, id uint) (*vo.SystemParameterVO, error)
	PageParameters(ctx context.Context, query dto.SystemParameterPageQueryDTO) (*vo.PageSystemParameterVO, error)

	// Core method to get parameter value by key (implement caching here)
	GetParameterValueByKey(ctx context.Context, key string) (string, error)

	// Optional: Get all enabled parameters as a map
	GetAllEnabledParameters(ctx context.Context) (map[string]string, error)

	// Helper conversion method
	ConvertToSystemParameterVO(ctx context.Context, param *entity.SystemParameter) (*vo.SystemParameterVO, error)
}

type systemParameterServiceImpl struct {
	BaseServiceImpl // 嵌入基础服务实现
	// Inject cache client if caching is implemented
	// cache cache.Cache
}

// NewSystemParameterService 创建系统参数服务实例
// 注意：如果需要缓存，需要在这里注入 cache.Cache 实例
func NewSystemParameterService(sm *ServiceManager /*, cache cache.Cache */) SystemParameterService {
	return &systemParameterServiceImpl{
		BaseServiceImpl: *NewBaseService(sm), // <<< 修正：移除不必要的类型断言
		// cache: cache,
	}
}

// getSystemParameterRepo 获取系统参数仓库的辅助函数
func (s *systemParameterServiceImpl) getSystemParameterRepo() repository.SystemParameterRepository {
	// 从 ServiceManager 获取 RepositoryManager，再获取具体的 Repository
	return s.GetServiceManager().GetRepositoryManager().GetSystemParameterRepository()
}

// CreateParameter 创建参数
func (s *systemParameterServiceImpl) CreateParameter(ctx context.Context, req dto.SystemParameterCreateOrUpdateDTO) (*vo.SystemParameterVO, error) {
	op := "Service.CreateParameter"
	log := s.GetLogger() // Removed .WithContext(ctx) based on previous fixes

	// 1. Check if ParamKey already exists
	existing, err := s.getSystemParameterRepo().FindByKey(ctx, req.ParamKey)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WithError(err).Error(op + ": 检查参数键唯一性失败")
		return nil, err
	}
	if existing != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("参数键 '%s' 已存在", req.ParamKey))
	}

	// 2. Create entity
	param := &entity.SystemParameter{
		ParamKey:   req.ParamKey,
		ParamValue: req.ParamValue,
		Name:       req.Name,
		Remark:     req.Remark,
		ValueType:  req.ValueType,
		Status:     1,     // Default to enabled
		IsSystem:   false, // Default to not system
	}
	if req.Status != nil {
		param.Status = *req.Status
	}
	if req.IsSystem != nil {
		param.IsSystem = *req.IsSystem
	}
	// Assume TenantID, CreatedBy are handled by BaseRepository or GORM hooks

	// >>> ADDED CreatedBy and UpdatedBy logic <<<
	uid64Create, errCreate := util.GetUserIDFromStdContext(ctx)
	if errCreate != nil {
		log.WithError(errCreate).Warn(op + ": CreateParameter: 无法从上下文中获取UserID")
	}
	param.CreatedBy = uint(uid64Create)
	param.UpdatedBy = uint(uid64Create)

	// 3. Call Repository Create
	if err := s.getSystemParameterRepo().Create(ctx, param); err != nil {
		log.WithError(err).Error(op + ": 创建系统参数失败")
		return nil, err
	}

	// TODO: Clear cache if necessary (e.g., clear 'all enabled params' cache)
	// cacheKeyAll := "all_enabled_system_params"
	// if s.cache != nil {
	//     if err := s.cache.Delete(ctx, cacheKeyAll); err != nil {
	//         log.WithError(err).Warn(op + ": 清除全部参数缓存失败")
	//     }
	// }

	// 4. Convert and return VO
	return s.ConvertToSystemParameterVO(ctx, param)
}

// UpdateParameter 更新参数
func (s *systemParameterServiceImpl) UpdateParameter(ctx context.Context, id uint, req dto.SystemParameterCreateOrUpdateDTO) (*vo.SystemParameterVO, error) {
	op := "Service.UpdateParameter"
	log := s.GetLogger()

	// 1. Find the entity to update
	param, err := s.getSystemParameterRepo().FindByID(ctx, id)
	if err != nil {
		log.WithError(err).Warn(op + ": 系统参数未找到")
		return nil, err // Repository should return wrapped error (NotFound)
	}

	// Business rule: Cannot modify key or is_system for system parameters?
	// if param.IsSystem { ... check if forbidden fields are being updated ... }

	// 2. Update fields from DTO (only non-nil fields)
	updated := false
	if req.ParamValue != "" && req.ParamValue != param.ParamValue { // Assuming ParamValue is string, check for empty if it means "not provided" or if it's a pointer
		param.ParamValue = req.ParamValue
		updated = true
	}
	if req.Name != "" && req.Name != param.Name { // Assuming Name is string
		param.Name = req.Name
		updated = true
	}
	if req.Remark != param.Remark { // Assuming Remark can be empty string
		param.Remark = req.Remark
		updated = true
	}
	if req.Status != nil && *req.Status != param.Status {
		param.Status = *req.Status
		updated = true
	}
	if req.ValueType != "" && req.ValueType != param.ValueType { // Assuming ValueType is string
		param.ValueType = req.ValueType
		updated = true
	}
	// Assume UpdatedBy handled by hooks

	// >>> ADDED UpdatedBy logic <<<
	uid64Update, errUpdate := util.GetUserIDFromStdContext(ctx)
	if errUpdate != nil {
		log.WithError(errUpdate).Warn(op + ": UpdateParameter: 无法从上下文中获取UserID")
	}
	param.UpdatedBy = uint(uid64Update)

	// 3. Call Repository Update if changed
	if updated {
		if err := s.getSystemParameterRepo().Update(ctx, param); err != nil {
			log.WithError(err).Error(op + ": 更新系统参数失败")
			return nil, err
		}

		// TODO: Clear relevant cache entries (e.g., the specific key and 'all enabled')
		// cacheKey := fmt.Sprintf("system_param:%s", param.ParamKey)
		// cacheKeyAll := "all_enabled_system_params"
		// if s.cache != nil {
		//     if errDel := s.cache.Delete(ctx, cacheKey); errDel != nil {
		//         log.WithError(errDel).Warn(op + ": 清除参数缓存失败: " + cacheKey)
		//     }
		//     if errDelAll := s.cache.Delete(ctx, cacheKeyAll); errDelAll != nil {
		//         log.WithError(errDelAll).Warn(op + ": 清除全部参数缓存失败")
		//     }
		// }
	}

	// 4. Convert and return VO
	return s.ConvertToSystemParameterVO(ctx, param)
}

// DeleteParameter 删除参数
func (s *systemParameterServiceImpl) DeleteParameter(ctx context.Context, id uint) error {
	op := "Service.DeleteParameter"
	log := s.GetLogger()

	// 1. Find entity to get key and check if it's a system parameter
	param, err := s.getSystemParameterRepo().FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(op + ": 系统参数未找到，无需删除")
			return nil // Or return NotFound error as needed
		}
		log.WithError(err).Error(op + ": 查询系统参数失败")
		return err
	}

	// Business rule: Prevent deletion of system parameters
	if param.IsSystem {
		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "系统内置参数不允许删除")
	}

	// 2. Call Repository Delete
	err = s.getSystemParameterRepo().Delete(ctx, id)
	if err != nil {
		log.WithError(err).Error(op + ": 删除系统参数失败")
		return err // Return repository layer error
	}

	// TODO: Clear relevant cache entries
	// if param != nil {
	//    cacheKey := fmt.Sprintf("system_param:%s", param.ParamKey)
	//    cacheKeyAll := "all_enabled_system_params"
	//    if s.cache != nil {
	// 	      if errDel := s.cache.Delete(ctx, cacheKey); errDel != nil {
	//            log.WithError(errDel).Warn(op + ": 清除参数缓存失败: " + cacheKey)
	//        }
	//        if errDelAll := s.cache.Delete(ctx, cacheKeyAll); errDelAll != nil {
	//            log.WithError(errDelAll).Warn(op + ": 清除全部参数缓存失败")
	//        }
	//    }
	// }

	return nil
}

// GetParameterByID 按 ID 获取参数
func (s *systemParameterServiceImpl) GetParameterByID(ctx context.Context, id uint) (*vo.SystemParameterVO, error) {
	op := "Service.GetParameterByID"
	log := s.GetLogger()

	param, err := s.getSystemParameterRepo().FindByID(ctx, id)
	if err != nil {
		log.WithError(err).Warn(op + ": 查询系统参数失败")
		return nil, err // Repository should return wrapped error
	}

	return s.ConvertToSystemParameterVO(ctx, param)
}

// PageParameters 分页查询参数
func (s *systemParameterServiceImpl) PageParameters(ctx context.Context, query dto.SystemParameterPageQueryDTO) (*vo.PageSystemParameterVO, error) {
	op := "Service.PageParameters"
	log := s.GetLogger()

	// 使用 query.PageQuery (已由 Controller 通过 BuildPageQuery 填充)
	entities, total, err := s.getSystemParameterRepo().FindPage(ctx, query)
	if err != nil {
		log.WithError(err).Error(op + ": 分页查询系统参数失败")
		return nil, err
	}

	voList := make([]*vo.SystemParameterVO, 0, len(entities))
	for _, entity := range entities {
		voItem, errConv := s.ConvertToSystemParameterVO(ctx, entity)
		if errConv != nil {
			log.WithError(errConv).Error(op + ": 转换系统参数 VO 失败")
			continue // Or return nil, errConv
		}
		voList = append(voList, voItem)
	}

	// Create page result
	// PageNum 和 PageSize 直接从 query.PageQuery 中获取
	pageNum := query.PageQuery.PageNum
	pageSize := query.PageQuery.PageSize
	// 确保 PageNum 和 PageSize 合法性 (理论上 Controller 已处理，这里可作防御)
	if pageNum <= 0 {
		pageNum = 1 // Default if somehow still invalid
	}
	if pageSize <= 0 {
		pageSize = 10 // Default if somehow still invalid
	}

	pageResult := &vo.PageSystemParameterVO{
		PageVO: vo.PageVO{
			List:     make([]interface{}, 0),
			Total:    total,
			PageNum:  pageNum,
			PageSize: pageSize,
			// TotalPage: Calculated by PageQuery.SetTotal if that helper is used consistently, or manually here.
		},
		List: voList,
	}
	// 如果 PageQuery.SetTotal 已经计算了 Pages，可以直接使用
	if query.PageQuery.Pages > 0 {
		pageResult.PageVO.Pages = query.PageQuery.Pages
	} else if pageSize > 0 {
		pageResult.PageVO.Pages = (int(total) + pageSize - 1) / pageSize
	}

	return pageResult, nil
}

// GetParameterValueByKey 通过键获取参数值
// This method provides the core logic for retrieving a parameter value by its key.
// It is designed to be a foundational piece, upon which caching can be built.
func (s *systemParameterServiceImpl) GetParameterValueByKey(ctx context.Context, key string) (string, error) {
	op := "Service.GetParameterValueByKey"
	log := s.GetLogger()

	// 1. 调用仓库层进行查询
	param, err := s.getSystemParameterRepo().FindByKey(ctx, key)
	if err != nil {
		// 如果仓库层返回的是 gorm.ErrRecordNotFound，直接透传
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", err
		}
		// 对于其他类型的查询错误，记录日志并返回一个包装后的错误
		log.WithError(err).Error(op + ": 查询系统参数失败")
		return "", apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询系统参数失败").WithCause(err)
	}

	// 2. 增加对返回结果的 nil 检查，确保安全
	if param == nil {
		// 即使仓库层没有返回错误，也要处理实体为 nil 的情况，这是一种防御性编程
		return "", gorm.ErrRecordNotFound
	}

	// 3. 检查参数状态
	if param.Status != 1 {
		// 根据业务需求，可以决定是返回错误还是空字符串
		return "", apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, fmt.Sprintf("系统参数 '%s' 已被禁用", key))
	}

	// 4. 返回参数值
	return param.ParamValue, nil
}

// GetAllEnabledParameters 获取所有启用的参数
// This method retrieves all system parameters that are currently enabled.
// It's useful for scenarios where multiple parameters need to be fetched at once,
// for example, to initialize a configuration struct at application startup.
func (s *systemParameterServiceImpl) GetAllEnabledParameters(ctx context.Context) (map[string]string, error) {
	op := "Service.GetAllEnabledParameters"
	log := s.GetLogger()
	// cacheKey := "all_enabled_system_params"

	// TODO: 1. Implement caching logic here

	// 2. Get from repository
	// NOTE: This requires a new method in the repository, e.g., FindAllEnabled()
	// For now, let's simulate by calling FindPage with status=1 and large page size
	// This is INEFFICIENT for large number of parameters!
	log.Warn(op + ": Using inefficient Page query to get all enabled params. Implement FindAllEnabled in repo.")
	statusEnabled := 1 // 创建一个值为 1 的变量

	// 构建查询 DTO，分页信息使用 response.PageQuery
	queryForRepo := dto.SystemParameterPageQueryDTO{
		PageQuery: response.PageQuery{ // <<< 直接使用 response.PageQuery
			PageNum:  1,
			PageSize: 9999, // Large page size
			Sort: []response.SortInfo{ // <<< 使用 response.SortInfo
				{Field: "id", Order: "asc"},
			},
		},
		Status: &statusEnabled, // 使用该变量的指针
	}
	entities, _, err := s.getSystemParameterRepo().FindPage(ctx, queryForRepo) // <<< 传递修改后的 queryForRepo
	if err != nil {
		log.WithError(err).Error(op + ": 获取所有启用参数失败 (via page query)")
		return nil, err
	}

	// 3. Build map
	paramMap := make(map[string]string)
	for _, p := range entities {
		if p.Status == 1 { // Double check status
			paramMap[p.ParamKey] = p.ParamValue
		}
	}

	// TODO: 4. Store map in cache
	// if s.cache != nil { ... }

	return paramMap, nil
}

// ConvertToSystemParameterVO 辅助转换方法
func (s *systemParameterServiceImpl) ConvertToSystemParameterVO(ctx context.Context, param *entity.SystemParameter) (*vo.SystemParameterVO, error) {
	if param == nil {
		return nil, nil
	}
	vo := &vo.SystemParameterVO{
		BaseVO: vo.BaseVO{ // Populate BaseVO fields - Assuming BaseVO exists
			ID:        param.ID,
			CreatedAt: param.CreatedAt,
			UpdatedAt: param.UpdatedAt,
			CreatedBy: param.CreatedBy, // Assuming BaseEntity has these
			UpdatedBy: param.UpdatedBy,
		},
		ParamKey:   param.ParamKey,
		ParamValue: param.ParamValue,
		Name:       param.Name,
		Remark:     param.Remark,
		Status:     param.Status,
		IsSystem:   param.IsSystem,
		ValueType:  param.ValueType,
	}
	return vo, nil
}
