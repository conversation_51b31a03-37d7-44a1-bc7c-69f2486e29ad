package security

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"
)

// 授权文件相关常量
const (
	// 授权文件默认存储位置
	DEFAULT_LICENSE_PATH = "./license.dat"

	// 授权状态
	LICENSE_STATUS_VALID     = "有效"    // 授权有效
	LICENSE_STATUS_EXPIRED   = "已过期"   // 授权已过期
	LICENSE_STATUS_INVALID   = "无效"    // 授权无效
	LICENSE_STATUS_NOT_FOUND = "未找到授权" // 未找到授权
)

// License 授权信息结构
type License struct {
	TenantCode   string    `json:"tenantCode"`   // 租户编码
	IssueDate    time.Time `json:"issueDate"`    // 颁发日期
	ExpiryDate   time.Time `json:"expiryDate"`   // 有效期截止日期
	MaxUsers     int       `json:"maxUsers"`     // 最大用户数
	Modules      []string  `json:"modules"`      // 已授权模块
	LicenseID    string    `json:"licenseId"`    // 授权ID
	Signature    string    `json:"signature"`    // 签名（保留字段）
	IssuedBy     string    `json:"issuedBy"`     // 颁发者
	IssuedTo     string    `json:"issuedTo"`     // 被颁发者
	ContactEmail string    `json:"contactEmail"` // 联系邮箱
}

// LicenseConfig 授权配置
type LicenseConfig struct {
	// 授权文件路径
	LicensePath string `json:"licensePath" yaml:"licensePath"`

	// 密钥（用于加密/解密授权文件）
	SecretKey string `json:"secretKey" yaml:"secretKey"`

	// 验证失败时的回调函数
	OnLicenseInvalid func() `json:"-" yaml:"-"`
}

// 默认授权配置
var defaultLicenseConfig = LicenseConfig{
	LicensePath: DEFAULT_LICENSE_PATH,
	SecretKey:   "your-license-secret-key-change-me",
}

// LicenseStatus 授权状态
type LicenseStatus struct {
	Status        string    `json:"status"`        // 状态：有效、过期、无效
	Tenant        string    `json:"tenant"`        // 租户
	ExpiryDate    time.Time `json:"expiryDate"`    // 过期日期
	DaysRemaining int       `json:"daysRemaining"` // 剩余天数
	MaxUsers      int       `json:"maxUsers"`      // 最大用户数
	Modules       []string  `json:"modules"`       // 授权模块
	Message       string    `json:"message"`       // 状态消息
}

// CreateLicense 创建授权文件
// 参数:
//   - tenantCode: 租户编码
//   - expiryDate: 有效期截止日期
//   - maxUsers: 最大用户数
//   - modules: 授权模块
//   - issuedTo: 被颁发者
//   - contactEmail: 联系邮箱
//   - config: 可选的授权配置
//
// 返回:
//   - *License: 生成的授权信息
//   - error: 错误信息
func CreateLicense(tenantCode string, expiryDate time.Time, maxUsers int, modules []string, issuedTo string, contactEmail string, config ...LicenseConfig) (*License, error) {
	if tenantCode == "" {
		return nil, errors.New("租户编码不能为空")
	}

	if expiryDate.IsZero() || expiryDate.Before(time.Now()) {
		return nil, errors.New("有效期必须大于当前时间")
	}

	// 我们在此处不使用配置，但保留参数以便未来扩展
	// 创建授权信息
	license := &License{
		TenantCode:   tenantCode,
		IssueDate:    time.Now(),
		ExpiryDate:   expiryDate,
		MaxUsers:     maxUsers,
		Modules:      modules,
		LicenseID:    uuid.New().String(),
		IssuedBy:     "授权系统",
		IssuedTo:     issuedTo,
		ContactEmail: contactEmail,
	}

	// 生成签名（简单实现，实际应用中可能需要更复杂的逻辑）
	licenseData, err := json.Marshal(license)
	if err != nil {
		return nil, fmt.Errorf("序列化授权信息失败: %v", err)
	}

	hash := sha256.Sum256(licenseData)
	license.Signature = base64.StdEncoding.EncodeToString(hash[:])

	return license, nil
}

// SaveLicenseFile 保存授权文件
// 参数:
//   - license: 授权信息
//   - config: 可选的授权配置
//
// 返回:
//   - error: 错误信息
func SaveLicenseFile(license *License, config ...LicenseConfig) error {
	if license == nil {
		return errors.New("授权信息不能为空")
	}

	// 使用默认配置
	cfg := defaultLicenseConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	// 序列化授权信息
	licenseData, err := json.Marshal(license)
	if err != nil {
		return fmt.Errorf("序列化授权信息失败: %v", err)
	}

	// 加密授权信息
	encryptedData, err := encryptData(licenseData, []byte(cfg.SecretKey))
	if err != nil {
		return fmt.Errorf("加密授权信息失败: %v", err)
	}

	// 确保目录存在
	dir := filepath.Dir(cfg.LicensePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(cfg.LicensePath, encryptedData, 0644); err != nil {
		return fmt.Errorf("写入授权文件失败: %v", err)
	}

	return nil
}

// ReadLicenseFile 读取授权文件
// 参数:
//   - config: 可选的授权配置
//
// 返回:
//   - *License: 读取的授权信息
//   - error: 错误信息
func ReadLicenseFile(config ...LicenseConfig) (*License, error) {
	// 使用默认配置
	cfg := defaultLicenseConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	// 读取文件
	encryptedData, err := os.ReadFile(cfg.LicensePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("授权文件不存在: %s", cfg.LicensePath)
		}
		return nil, fmt.Errorf("读取授权文件失败: %v", err)
	}

	// 解密授权信息
	licenseData, err := decryptData(encryptedData, []byte(cfg.SecretKey))
	if err != nil {
		return nil, fmt.Errorf("解密授权文件失败: %v", err)
	}

	// 反序列化授权信息
	var license License
	if err := json.Unmarshal(licenseData, &license); err != nil {
		return nil, fmt.Errorf("解析授权信息失败: %v", err)
	}

	// 验证签名（简单实现，实际应用中可能需要更复杂的逻辑）
	licenseTemp := license
	licenseTemp.Signature = ""

	licenseData, err = json.Marshal(licenseTemp)
	if err != nil {
		return nil, fmt.Errorf("验证签名失败: %v", err)
	}

	hash := sha256.Sum256(licenseData)
	calculatedSignature := base64.StdEncoding.EncodeToString(hash[:])

	if calculatedSignature != license.Signature {
		return nil, errors.New("授权文件签名无效")
	}

	return &license, nil
}

// ReadLicenseFromBytes 从字节数据中读取授权信息
// 参数:
//   - data: 加密的授权文件字节数据
//   - config: 可选的授权配置
//
// 返回:
//   - *License: 读取的授权信息
//   - error: 错误信息
func ReadLicenseFromBytes(data []byte, config ...LicenseConfig) (*License, error) {
	if len(data) == 0 {
		return nil, errors.New("授权数据不能为空")
	}

	// 使用默认配置
	cfg := defaultLicenseConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	// 解密授权信息
	licenseData, err := decryptData(data, []byte(cfg.SecretKey))
	if err != nil {
		return nil, fmt.Errorf("解密授权数据失败: %v", err)
	}

	// 反序列化授权信息
	var license License
	if err := json.Unmarshal(licenseData, &license); err != nil {
		return nil, fmt.Errorf("解析授权信息失败: %v", err)
	}

	// 验证签名
	licenseTemp := license
	licenseTemp.Signature = ""

	licenseData, err = json.Marshal(licenseTemp)
	if err != nil {
		return nil, fmt.Errorf("验证签名失败: %v", err)
	}

	hash := sha256.Sum256(licenseData)
	calculatedSignature := base64.StdEncoding.EncodeToString(hash[:])

	if calculatedSignature != license.Signature {
		return nil, errors.New("授权数据签名无效")
	}

	return &license, nil
}

// ValidateLicense 验证授权是否有效
// 参数:
//   - license: 授权信息
//
// 返回:
//   - *LicenseStatus: 授权状态
func ValidateLicense(license *License) *LicenseStatus {
	if license == nil {
		return &LicenseStatus{
			Status:  LICENSE_STATUS_NOT_FOUND,
			Message: "未找到授权信息",
		}
	}

	now := time.Now()

	// 检查是否过期
	if now.After(license.ExpiryDate) {
		return &LicenseStatus{
			Status:        LICENSE_STATUS_EXPIRED,
			Tenant:        license.TenantCode,
			ExpiryDate:    license.ExpiryDate,
			DaysRemaining: 0,
			MaxUsers:      license.MaxUsers,
			Modules:       license.Modules,
			Message:       fmt.Sprintf("授权已于 %s 过期", license.ExpiryDate.Format("2006-01-02")),
		}
	}

	// 计算剩余天数
	daysRemaining := int(license.ExpiryDate.Sub(now).Hours() / 24)

	// 授权有效
	return &LicenseStatus{
		Status:        LICENSE_STATUS_VALID,
		Tenant:        license.TenantCode,
		ExpiryDate:    license.ExpiryDate,
		DaysRemaining: daysRemaining,
		MaxUsers:      license.MaxUsers,
		Modules:       license.Modules,
		Message:       fmt.Sprintf("授权有效，剩余 %d 天", daysRemaining),
	}
}

// CheckLicenseStatus 检查授权状态
// 参数:
//   - config: 可选的授权配置
//
// 返回:
//   - *LicenseStatus: 授权状态
//   - error: 错误信息
func CheckLicenseStatus(config ...LicenseConfig) (*LicenseStatus, error) {
	license, err := ReadLicenseFile(config...)
	if err != nil {
		return &LicenseStatus{
			Status:  LICENSE_STATUS_INVALID,
			Message: fmt.Sprintf("读取授权失败: %v", err),
		}, err
	}

	return ValidateLicense(license), nil
}

// IsLicenseValid 检查授权是否有效
// 参数:
//   - config: 可选的授权配置
//
// 返回:
//   - bool: 授权是否有效
func IsLicenseValid(config ...LicenseConfig) bool {
	status, _ := CheckLicenseStatus(config...)
	return status.Status == LICENSE_STATUS_VALID
}

// HandleLicenseUpload 处理授权文件上传
// 参数:
//   - data: 上传的文件数据
//   - config: 可选的授权配置
//
// 返回:
//   - *LicenseStatus: 授权状态
//   - error: 错误信息
func HandleLicenseUpload(data []byte, config ...LicenseConfig) (*LicenseStatus, error) {
	// 使用默认配置
	cfg := defaultLicenseConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	// 从上传的数据中读取授权信息
	license, err := ReadLicenseFromBytes(data, cfg)
	if err != nil {
		return &LicenseStatus{
			Status:  LICENSE_STATUS_INVALID,
			Message: fmt.Sprintf("解析授权文件失败: %v", err),
		}, err
	}

	// 验证授权
	status := ValidateLicense(license)
	if status.Status != LICENSE_STATUS_VALID {
		return status, fmt.Errorf("授权无效: %s", status.Message)
	}

	// 保存授权文件
	if err := SaveLicenseFile(license, cfg); err != nil {
		return &LicenseStatus{
			Status:  LICENSE_STATUS_INVALID,
			Message: fmt.Sprintf("保存授权文件失败: %v", err),
		}, err
	}

	return status, nil
}

// 加密数据
func encryptData(data []byte, key []byte) ([]byte, error) {
	// 生成密钥
	hash := sha256.Sum256(key)
	block, err := aes.NewCipher(hash[:])
	if err != nil {
		return nil, err
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// 创建随机数
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, data, nil)

	return ciphertext, nil
}

// 解密数据
func decryptData(data []byte, key []byte) ([]byte, error) {
	// 生成密钥
	hash := sha256.Sum256(key)
	block, err := aes.NewCipher(hash[:])
	if err != nil {
		return nil, err
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// 提取随机数
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, errors.New("密文长度不足")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}
