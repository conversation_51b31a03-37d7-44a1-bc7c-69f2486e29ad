package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsOutboundNotificationRepository 定义出库通知单仓库接口
type WmsOutboundNotificationRepository interface {
	BaseRepository[entity.WmsOutboundNotification, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsOutboundNotificationQueryReq) (*response.PageResult, error)

	// 根据通知单号查找
	FindByNotificationNo(ctx context.Context, notificationNo string) (*entity.WmsOutboundNotification, error)

	// 检查通知单号是否存在
	IsNotificationNoExist(ctx context.Context, notificationNo string, excludeID uint) (bool, error)

	// 批量创建
	BatchCreate(ctx context.Context, notifications []*entity.WmsOutboundNotification) error

	// 更新状态
	UpdateStatus(ctx context.Context, id uint, status string, remark string) error

	// 根据日期范围查询
	GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsOutboundNotification, error)

	// 根据客户ID查询
	GetByClientID(ctx context.Context, clientID uint) ([]*entity.WmsOutboundNotification, error)

	// 根据仓库ID查询
	GetByWarehouseID(ctx context.Context, warehouseID uint) ([]*entity.WmsOutboundNotification, error)

	// 获取指定状态的通知单
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsOutboundNotification, error)

	// 根据客户订单号查询
	FindByClientOrderNo(ctx context.Context, clientOrderNo string) ([]*entity.WmsOutboundNotification, error)

	// 获取待分配库存的通知单
	GetPendingAllocation(ctx context.Context) ([]*entity.WmsOutboundNotification, error)

	// 获取可生成拣货任务的通知单
	GetReadyForPicking(ctx context.Context) ([]*entity.WmsOutboundNotification, error)

	// 根据优先级查询
	GetByPriority(ctx context.Context, priority int) ([]*entity.WmsOutboundNotification, error)

	// 获取指定承运商的通知单
	GetByCarrierID(ctx context.Context, carrierID uint) ([]*entity.WmsOutboundNotification, error)

	// 批量更新状态
	BatchUpdateStatus(ctx context.Context, ids []uint, status string, remark string) error

	// 获取统计数据
	GetStatsByDateRange(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error)

	// 获取过期未发货的通知单
	GetOverdueShipments(ctx context.Context) ([]*entity.WmsOutboundNotification, error)
}

// wmsOutboundNotificationRepository 出库通知单仓库实现
type wmsOutboundNotificationRepository struct {
	BaseRepositoryImpl[entity.WmsOutboundNotification, uint]
}

// NewWmsOutboundNotificationRepository 创建出库通知单仓库
func NewWmsOutboundNotificationRepository(db *gorm.DB) WmsOutboundNotificationRepository {
	return &wmsOutboundNotificationRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsOutboundNotification, uint]{
			db: db,
		},
	}
}

// GetPage 获取出库通知单分页数据
func (r *wmsOutboundNotificationRepository) GetPage(ctx context.Context, query *dto.WmsOutboundNotificationQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.NotificationNo != nil && *query.NotificationNo != "" {
		conditions = append(conditions, NewLikeCondition("notification_no", *query.NotificationNo))
	}
	if query.ClientID != nil {
		conditions = append(conditions, NewEqualCondition("client_id", *query.ClientID))
	}
	if query.ClientOrderNo != nil && *query.ClientOrderNo != "" {
		conditions = append(conditions, NewLikeCondition("client_order_no", *query.ClientOrderNo))
	}
	if query.WarehouseID != nil {
		conditions = append(conditions, NewEqualCondition("warehouse_id", *query.WarehouseID))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.Priority != nil {
		conditions = append(conditions, NewEqualCondition("priority", *query.Priority))
	}
	if query.ConsigneeName != nil && *query.ConsigneeName != "" {
		conditions = append(conditions, NewLikeCondition("consignee_name", *query.ConsigneeName))
	}
	if query.CarrierID != nil {
		conditions = append(conditions, NewEqualCondition("carrier_id", *query.CarrierID))
	}
	if query.RequiredShipDate != nil {
		conditions = append(conditions, NewEqualCondition("required_ship_date", *query.RequiredShipDate))
	}
	if query.CreatedAtStart != nil && query.CreatedAtEnd != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *query.CreatedAtStart, *query.CreatedAtEnd))
	}

	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// FindByNotificationNo 根据通知单号查找
func (r *wmsOutboundNotificationRepository) FindByNotificationNo(ctx context.Context, notificationNo string) (*entity.WmsOutboundNotification, error) {
	var notification entity.WmsOutboundNotification
	db := r.GetDB(ctx)

	// 预加载关联的明细
	result := db.Preload("Details").Where("notification_no = ?", notificationNo).First(&notification)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "出库通知单不存在").WithCause(result.Error)
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据通知单号查询失败: notificationNo=%s", notificationNo)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询出库通知单失败").WithCause(result.Error)
	}

	return &notification, nil
}

// IsNotificationNoExist 检查通知单号是否存在
func (r *wmsOutboundNotificationRepository) IsNotificationNoExist(ctx context.Context, notificationNo string, excludeID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("notification_no", notificationNo),
	}
	if excludeID > 0 {
		conditions = append(conditions, NewNotEqualCondition("id", excludeID))
	}
	return r.Exists(ctx, conditions)
}

// BatchCreate 批量创建出库通知单
func (r *wmsOutboundNotificationRepository) BatchCreate(ctx context.Context, notifications []*entity.WmsOutboundNotification) error {
	if len(notifications) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(notifications, 100) // 每批100条
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建出库通知单失败: count=%d", len(notifications))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建出库通知单失败").WithCause(result.Error)
	}

	return nil
}

// UpdateStatus 更新通知单状态
func (r *wmsOutboundNotificationRepository) UpdateStatus(ctx context.Context, id uint, status string, remark string) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsOutboundNotification{}).Where("id = ?", id).Updates(map[string]interface{}{"status": status, "remark": remark})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新通知单状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新通知单状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "通知单不存在")
	}

	return nil
}

// GetByDateRange 根据日期范围查询
func (r *wmsOutboundNotificationRepository) GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewBetweenCondition("required_ship_date", startDate, endDate),
	}
	sortInfos := []response.SortInfo{
		{Field: "required_ship_date", Order: "asc"},
		{Field: "notification_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByClientID 根据客户ID查询
func (r *wmsOutboundNotificationRepository) GetByClientID(ctx context.Context, clientID uint) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("client_id", clientID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByWarehouseID 根据仓库ID查询
func (r *wmsOutboundNotificationRepository) GetByWarehouseID(ctx context.Context, warehouseID uint) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("warehouse_id", warehouseID),
	}
	sortInfos := []response.SortInfo{
		{Field: "required_ship_date", Order: "asc"},
		{Field: "notification_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetPendingAllocation 获取待分配库存的通知单
func (r *wmsOutboundNotificationRepository) GetPendingAllocation(ctx context.Context) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", string(entity.OutboundStatusApproved)),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "required_ship_date", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetReadyForPicking 获取可生成拣货任务的通知单
func (r *wmsOutboundNotificationRepository) GetReadyForPicking(ctx context.Context) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", string(entity.OutboundStatusAllocated)),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "desc"},
		{Field: "required_ship_date", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByPriority 根据优先级查询
func (r *wmsOutboundNotificationRepository) GetByPriority(ctx context.Context, priority int) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("priority", priority),
	}
	sortInfos := []response.SortInfo{
		{Field: "required_ship_date", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByCarrierID 获取指定承运商的通知单
func (r *wmsOutboundNotificationRepository) GetByCarrierID(ctx context.Context, carrierID uint) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("carrier_id", carrierID),
	}
	sortInfos := []response.SortInfo{
		{Field: "required_ship_date", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// BatchUpdateStatus 批量更新状态
func (r *wmsOutboundNotificationRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string, remark string) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsOutboundNotification{}).Where("id IN ?", ids).Updates(map[string]interface{}{"status": status, "remark": remark})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量更新通知单状态失败: ids=%v, status=%s", ids, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新通知单状态失败").WithCause(result.Error)
	}

	return nil
}

// GetStatsByDateRange 获取统计数据
func (r *wmsOutboundNotificationRepository) GetStatsByDateRange(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var stats struct {
		TotalCount     int64 `json:"total_count"`
		DraftCount     int64 `json:"draft_count"`
		ApprovedCount  int64 `json:"approved_count"`
		AllocatedCount int64 `json:"allocated_count"`
		PickingCount   int64 `json:"picking_count"`
		PickedCount    int64 `json:"picked_count"`
		PackedCount    int64 `json:"packed_count"`
		ShippedCount   int64 `json:"shipped_count"`
		DeliveredCount int64 `json:"delivered_count"`
		CancelledCount int64 `json:"cancelled_count"`
	}

	// 总数统计
	if err := db.Model(&entity.WmsOutboundNotification{}).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Count(&stats.TotalCount).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取出库通知单总数统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取统计数据失败").WithCause(err)
	}

	// 各状态统计
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := db.Model(&entity.WmsOutboundNotification{}).
		Select("status, COUNT(*) as count").
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取出库通知单状态统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取状态统计失败").WithCause(err)
	}

	// 填充状态统计
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(entity.OutboundStatusDraft):
			stats.DraftCount = sc.Count
		case string(entity.OutboundStatusApproved):
			stats.ApprovedCount = sc.Count
		case string(entity.OutboundStatusAllocated):
			stats.AllocatedCount = sc.Count
		case string(entity.OutboundStatusPicking):
			stats.PickingCount = sc.Count
		case string(entity.OutboundStatusPicked):
			stats.PickedCount = sc.Count
		case string(entity.OutboundStatusPacked):
			stats.PackedCount = sc.Count
		case string(entity.OutboundStatusShipped):
			stats.ShippedCount = sc.Count
		case string(entity.OutboundStatusDelivered):
			stats.DeliveredCount = sc.Count
		case string(entity.OutboundStatusCancelled):
			stats.CancelledCount = sc.Count
		}
	}

	result := map[string]interface{}{
		"total_count":     stats.TotalCount,
		"draft_count":     stats.DraftCount,
		"approved_count":  stats.ApprovedCount,
		"allocated_count": stats.AllocatedCount,
		"picking_count":   stats.PickingCount,
		"picked_count":    stats.PickedCount,
		"packed_count":    stats.PackedCount,
		"shipped_count":   stats.ShippedCount,
		"delivered_count": stats.DeliveredCount,
		"cancelled_count": stats.CancelledCount,
	}

	return result, nil
}

// GetOverdueShipments 获取过期未发货的通知单
func (r *wmsOutboundNotificationRepository) GetOverdueShipments(ctx context.Context) ([]*entity.WmsOutboundNotification, error) {
	now := time.Now()
	conditions := []QueryCondition{
		NewInCondition("status", []interface{}{
			string(entity.OutboundStatusApproved),
			string(entity.OutboundStatusAllocated),
			string(entity.OutboundStatusPicking),
			string(entity.OutboundStatusPicked),
			string(entity.OutboundStatusPacked),
		}),
		NewLessThanCondition("required_ship_date", now),
	}
	sortInfos := []response.SortInfo{
		{Field: "required_ship_date", Order: "asc"},
		{Field: "priority", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByStatus 根据状态查询
func (r *wmsOutboundNotificationRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "required_ship_date", Order: "asc"},
		{Field: "notification_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByClientOrderNo 根据客户订单号查询
func (r *wmsOutboundNotificationRepository) FindByClientOrderNo(ctx context.Context, clientOrderNo string) ([]*entity.WmsOutboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("client_order_no", clientOrderNo),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}
