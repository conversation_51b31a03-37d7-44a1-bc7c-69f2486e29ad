package license

import (
	"context" // Import context for logger
	"sync"

	// Import time for formatting
	// Assuming you have a standard logger package, adjust if needed
	log "github.com/sirupsen/logrus"
)

// --- 运行时授权状态 ---

var (
	// verifiedLicenseInfo 存储当前内存中已验证的授权信息
	verifiedLicenseInfo *LicenseInfo
	// licenseError 存储当前的授权错误状态 (例如，文件未找到、无效、过期等)
	licenseError error
	// licenseMutex 保护对 verifiedLicenseInfo 和 licenseError 的并发访问
	licenseMutex sync.RWMutex
)

// --- 嵌入的公钥 (从 middleware 移动过来) ---
// !!重要!!: 将您的 public_key.pem 文件内容粘贴到这里，替换下面的示例
// 确保存换行符被正确转义或使用反引号 ` ` 定义多行字符串
const embeddedPublicKeyPEM = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6q25U0xriAHLnyFyKEgG
7SOM9YQxamhIZh2WpyirOd8KFV8RoIjwo2DfBehHupwZjov9jXpxevNcuefqJzRl
wwEjyAMWQchIdrZy/km6J4cy9Vgy/KMqctxvmTKclRdbGsnARaa5tZlgOaGMC9zo
HiSaroYxg7I1PK7jELzoDQvBbil7tJbBs9y36ilHkIoCEM4kUNI8ULMLRTehmM1a
j0e6alvjFbq+dBhLIGFTAanjhEaBDURnnzea40WF+SCcyh26D4aAD2f6fKq/aj72
HdPZPBcEPeW19KW6tWuC0R+i4Jxjs8B++zA7cssoBbqmgWFDxaGkmUDLd/RCaNkC
IQIDAQAB
-----END PUBLIC KEY-----`

// GetEmbeddedPublicKeyPEM 返回嵌入的公钥 PEM 字符串.
// Service 层会调用此函数获取公钥用于验证上传的文件.
func GetEmbeddedPublicKeyPEM() string {
	return embeddedPublicKeyPEM
}

// UpdateRuntimeLicense 安全地更新全局的运行时授权状态.
// 由 InitLicense (启动时) 和 LicenseService (上传时) 调用.
// 参数:
//
//	newInfo: 新验证通过的授权信息 (如果验证失败或清空状态，则为 nil)
//	newError: 新的错误状态 (如果验证成功，则为 nil)
func UpdateRuntimeLicense(newInfo *LicenseInfo, newError error) {
	licenseMutex.Lock() // 获取写锁
	defer licenseMutex.Unlock()

	verifiedLicenseInfo = newInfo
	licenseError = newError

	// 使用标准日志记录器或您项目中的日志库
	logger := log.WithContext(context.Background()) // 创建一个基础 logger 实例
	if newError == nil && newInfo != nil {
		// 成功更新
		expiryStr := "永久"
		if !newInfo.ExpiryDate.IsZero() {
			expiryStr = newInfo.ExpiryDate.Format("2006-01-02")
		}
		logger.Infof("运行时授权已更新。客户: %s, 类型: %s, 到期日: %s", newInfo.CustomerName, newInfo.LicenseType, expiryStr)
	} else if newError != nil {
		// 更新为错误状态
		logger.Errorf("运行时授权状态更新为错误: %v", newError)
	} else {
		// 清空状态 (例如，上传的文件无效导致清除旧状态，或者初始就没有有效授权)
		logger.Warn("运行时授权状态已被清除 (无有效授权信息)")
	}
}

// GetRuntimeLicenseState 安全地获取当前的运行时授权状态.
// 由中间件调用以检查每个请求的授权.
// 返回:
//
//	*LicenseInfo: 当前有效的授权信息 (如果状态正常)
//	error:        当前的错误状态 (如果授权无效、过期、未找到等)
func GetRuntimeLicenseState() (*LicenseInfo, error) {
	licenseMutex.RLock() // 获取读锁
	defer licenseMutex.RUnlock()

	// 直接返回当前内存中的状态
	return verifiedLicenseInfo, licenseError
}
