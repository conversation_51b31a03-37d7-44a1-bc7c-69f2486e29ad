<template>
  <div>
    <!-- 供应商管理表格 -->
    <VNTable
      ref="supplierTableRef"
      :data="supplierTableData"
      :columns="supplierTableColumns" 
      :loading="supplierLoading"
      :pagination="supplierPagination"
      :toolbar-config="supplierToolbarConfig"
      :operation-buttons="[]" 
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      operation-fixed="right"
      :operation-width="220" 
      @refresh="loadSuppliers" 
      @add="handleAddNewSupplier"
      @selection-change="handleSupplierSelectionChange"
      @batch-delete="handleSupplierBatchDelete"
      @import="handleSupplierImport"
      @export="handleSupplierExport"
      @page-change="handleSupplierPageChange"
      @page-size-change="handleSupplierPageSizeChange"
      @sort-change="handleSupplierSortChange" 
      @filter-change="handleSupplierFilterChange" 
    >
      <!-- 供应商类型列插槽 -->
      <template #column-supplierType="{ row }">
         <el-tag :type="row.supplierType === 'CORPORATE' ? 'success' : 'info'">
           {{ getSupplierTypeLabel(row.supplierType) }}
         </el-tag>
      </template>

      <!-- 供应商级别列插槽 -->
      <template #column-supplierLevel="{ row }">
        <el-tag 
          :type="row.supplierLevel === 'A' ? 'danger' : row.supplierLevel === 'B' ? 'warning' : row.supplierLevel === 'C' ? 'info' : 'primary'"
        >
          {{ getSupplierLevelLabel(row.supplierLevel) }}
        </el-tag>
      </template>

      <!-- 供应商分类列插槽 -->
      <template #column-supplierCategory="{ row }">
        <el-tag type="primary" v-if="row.supplierCategory">
          {{ getSupplierCategoryLabel(row.supplierCategory) }}
        </el-tag>
        <span v-else>-</span>
      </template>

      <!-- 质量评级列插槽 -->
      <template #column-qualityRating="{ row }">
        <el-tag 
          :type="row.qualityRating === 'EXCELLENT' ? 'danger' : row.qualityRating === 'GOOD' ? 'success' : row.qualityRating === 'AVERAGE' ? 'warning' : 'info'"
          v-if="row.qualityRating"
        >
          {{ getQualityRatingLabel(row.qualityRating) }}
        </el-tag>
        <span v-else>-</span>
      </template>

      <!-- 交期评级列插槽 -->
      <template #column-deliveryRating="{ row }">
        <el-tag 
          :type="row.deliveryRating === 'EXCELLENT' ? 'danger' : row.deliveryRating === 'GOOD' ? 'success' : row.deliveryRating === 'AVERAGE' ? 'warning' : 'info'"
          v-if="row.deliveryRating"
        >
          {{ getDeliveryRatingLabel(row.deliveryRating) }}
        </el-tag>
        <span v-else>-</span>
      </template>

      <!-- 状态列插槽 -->
      <template #column-status="{ row }">
         <el-tag :type="row.status === 'ACTIVE' ? 'success' : row.status === 'INACTIVE' ? 'warning' : 'danger'">
           {{ getSupplierStatusLabel(row.status) }}
         </el-tag>
      </template>

      <!-- 是否重点供应商列插槽 -->
      <template #column-isKeySupplier="{ row }">
        <el-tag :type="row.isKeySupplier ? 'danger' : 'info'">
          {{ row.isKeySupplier ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 是否战略供应商列插槽 -->
      <template #column-isStrategicSupplier="{ row }">
        <el-tag :type="row.isStrategicSupplier ? 'success' : 'info'">
          {{ row.isStrategicSupplier ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 供应商表格操作列插槽 -->
      <template #operation="{ row }">
        <el-tooltip content="查看供应商" placement="top" v-if="hasPermission('scm:supplier:view')">
          <el-button circle :icon="View" size="small" @click="handleViewSupplier(row)" />
        </el-tooltip>
        <el-tooltip content="编辑供应商" placement="top" v-if="hasPermission('scm:supplier:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditSupplier(row)" />
        </el-tooltip>
        <el-tooltip content="联系人" placement="top" v-if="hasPermission('scm:supplier:contact:list')">
           <el-button circle :icon="User" type="success" size="small" @click="openContactManageDialog(row)" />
        </el-tooltip>
        <el-tooltip content="删除供应商" placement="top" v-if="hasPermission('scm:supplier:delete')">
          <el-button circle :icon="Delete" type="danger" size="small" @click="handleDeleteSupplier(row)" />
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 供应商新增/编辑弹窗 -->
    <el-dialog
      v-model="supplierDialogVisible"
      :title="supplierDialogTitle"
      width="70%"
      draggable
      align-center
      top="5vh"
      :close-on-click-modal="false"
      @close="handleCloseSupplierDialog" 
    >
      <VNForm
        v-if="supplierDialogVisible" 
        ref="supplierFormRef"
        :header-fields="supplierFormFields"
        v-model="currentEditingSupplier"
        :default-columns="4"
        :label-width="'120px'"
        :loading="supplierFormLoading" 
        :show-actions="false"
        group-title-type="h4"
        @submit="submitSupplierForm"
        @cancel="handleCloseSupplierDialog" 
      >
        <!-- 自定义表单操作按钮插槽 -->
        <template #actions>
            <template v-if="isViewing">
                <el-button
                  v-if="hasPermission('scm:supplier:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreviewSupplier"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('scm:supplier:print')" 
                  type="primary"
                  :icon="Printer" 
                  @click="handlePrintSupplier"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseSupplierDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitSupplierForm" :loading="supplierFormLoading">提交</el-button>
                <el-button @click="handleCloseSupplierDialog">取消</el-button>
            </template>
        </template>
      </VNForm>
    </el-dialog> 

    <!-- 供应商联系人管理模态框 -->
    <el-dialog
      v-model="contactManageDialogVisible"
      :title="'联系人管理 - ' + (currentManagedSupplier?.supplierName || '')"
      width="85%"
      top="5vh"
      draggable
      :close-on-click-modal="false"
      :modal="true"
      destroy-on-close
      @close="currentManagedSupplier = null"
    >
      <!-- 供应商联系人管理组件 -->
      <SupplierContactManager
        v-if="currentManagedSupplier" 
        :supplier-id="currentManagedSupplier.id"
        :supplier-code="currentManagedSupplier.supplierCode"
        :supplier-name="currentManagedSupplier.supplierName"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTooltip, ElTag, ElInput } from 'element-plus';
// 导入所需图标
import { Edit, Delete, View, Printer, User, Refresh } from '@element-plus/icons-vue'; 
// 假设 VNTable 和 VNForm 组件已全局注册或在此导入
import VNTable from '@/components/VNTable/index.vue'; 
import VNForm from '@/components/VNForm/index.vue'; 
// 引入类型定义
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 引入API函数和类型定义
import { 
  getSupplierPage, 
  createSupplier, 
  updateSupplier, 
  deleteSupplier,
  validateBusinessLicense,
  validateTaxNumber,
  getSupplierSummary
} from '@/api/scm/supplier';
// 引入字典数据获取函数
import { getDictDataByCode } from '@/api/system/systemDict';
// 引入币种API
import { getEnabledCurrencyList } from '@/api/fin/currency';
import type { CurrencyListItem } from '@/api/fin/currency';
import type { 
  ScmSupplierVO, 
  ScmSupplierListVO,
  ScmSupplierQueryDTO, 
  ScmSupplierCreateDTO, 
  ScmSupplierUpdateDTO 
} from '@/api/scm/supplier';
// 导入供应商联系人管理组件
import SupplierContactManager from './components/SupplierContactManager.vue'; 
// 导入权限检查函数
import { hasPermission } from '@/hooks/usePermission';

// --- 字典数据状态 ---
const supplierTypes = ref<{ label: string; value: string }[]>([]);
const supplierLevels = ref<{ label: string; value: string }[]>([]);
const supplierStatuses = ref<{ label: string; value: string }[]>([]);
const supplierCategories = ref<{ label: string; value: string }[]>([]);
const creditRatings = ref<{ label: string; value: string }[]>([]);
const paymentTerms = ref<{ label: string; value: string }[]>([]);
const industries = ref<{ label: string; value: string }[]>([]);
const currencyOptions = ref<{ label: string; value: string }[]>([]);
const contactRoles = ref<{ label: string; value: string }[]>([]);
const genders = ref<{ label: string; value: string }[]>([]);
const qualityRatings = ref<{ label: string; value: string }[]>([]);
const deliveryRatings = ref<{ label: string; value: string }[]>([]);
const serviceRatings = ref<{ label: string; value: string }[]>([]);

// --- 辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Refs --- 
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;

const supplierTableRef = ref<VNTableInstance>();
// 用于供应商编辑表单
const supplierFormRef = ref<VNFormInstance>();

// --- State --- 

// 供应商相关状态
const supplierTableData = ref<ScmSupplierListVO[]>([]);
const supplierPagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const supplierLoading = ref(false);
// 供应商增/改弹窗
const supplierDialogVisible = ref(false);
// 当前编辑的供应商数据
const currentEditingSupplier = ref<Partial<ScmSupplierCreateDTO | ScmSupplierUpdateDTO>>({});
// 添加 formMode 和 isViewing
const formMode = ref<'add' | 'edit' | 'view'>('add');
const isViewing = computed(() => formMode.value === 'view');
const selectedSupplierRows = ref<ScmSupplierListVO[]>([]); // 存储选中的行

// 添加排序状态
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);
// 添加筛选状态
const currentFilters = ref<Record<string, any>>({});
// 供应商表单加载状态
const supplierFormLoading = ref(false);

// 供应商联系人管理模态框相关状态
const contactManageDialogVisible = ref(false);
// 当前在模态框中管理的供应商
const currentManagedSupplier = ref<ScmSupplierListVO | null>(null);

// --- Computed --- 
// 计算属性，用于动态设置供应商编辑弹窗标题
const supplierDialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增供应商';
  if (formMode.value === 'edit') return '编辑供应商';
  if (formMode.value === 'view') return '查看供应商';
  return '供应商管理'; // Default
});

// --- Helper: Date Formatting ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) { 
        return '-';
    }
    return date.toLocaleString();
  } catch (e) {
    console.error('Error formatting date:', dateString, e);
    return '-';
  }
};

// --- Helper: Label Functions ---
const getSupplierTypeLabel = (value: string): string => {
  const item = supplierTypes.value.find(t => t.value === value);
  return item ? item.label : value;
};

const getSupplierLevelLabel = (value: string): string => {
  const item = supplierLevels.value.find(l => l.value === value);
  return item ? item.label : value;
};

const getSupplierStatusLabel = (value: string): string => {
  const item = supplierStatuses.value.find(s => s.value === value);
  return item ? item.label : value;
};

const getSupplierCategoryLabel = (value: string): string => {
  const item = supplierCategories.value.find(c => c.value === value);
  return item ? item.label : value;
};

const getCreditRatingLabel = (value: string): string => {
  const item = creditRatings.value.find(r => r.value === value);
  return item ? item.label : value;
};

const getPaymentTermsLabel = (value: string): string => {
  const item = paymentTerms.value.find(p => p.value === value);
  return item ? item.label : value;
};

const getIndustryLabel = (value: string): string => {
  const item = industries.value.find(i => i.value === value);
  return item ? item.label : value;
};

const getCurrencyCodeLabel = (value: string): string => {
  const item = currencyOptions.value.find(c => c.value === value);
  return item ? item.label : value;
};

const getQualityRatingLabel = (value: string): string => {
  const item = qualityRatings.value.find(r => r.value === value);
  return item ? item.label : value;
};

const getDeliveryRatingLabel = (value: string): string => {
  const item = deliveryRatings.value.find(r => r.value === value);
  return item ? item.label : value;
};

const getServiceRatingLabel = (value: string): string => {
  const item = serviceRatings.value.find(r => r.value === value);
  return item ? item.label : value;
};

// --- 计算属性，用于动态设置工具栏配置 ---
const supplierToolbarConfig = computed(() => ({
  refresh: true,
  add: hasPermission('scm:supplier:add'),
  batchDelete: hasPermission('scm:supplier:batchdelete'),
  filter: hasPermission('scm:supplier:search'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('scm:supplier:import'),
  export: hasPermission('scm:supplier:export'),
}));

// --- Table Columns (Supplier) --- 
const supplierTableColumns = computed<TableColumn[]>(() => [
  { prop: 'supplierCode', label: '供应商编码', minWidth: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'supplierName', label: '供应商名称', minWidth: 150, filterable: true, filterType: 'text' },
  { 
    prop: 'supplierType', 
    label: '供应商类型', 
    width: 110, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: supplierTypes.value
  }, 
  { 
    prop: 'supplierLevel', 
    label: '供应商级别', 
    width: 110, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: supplierLevels.value
  },
  { 
    prop: 'supplierCategory', 
    label: '供应商分类', 
    width: 120, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: supplierCategories.value
  },
  { prop: 'contactPerson', label: '联系人', width: 100 },
  { prop: 'contactPhone', label: '联系电话', width: 120 },
  { prop: 'contactEmail', label: '联系邮箱', minWidth: 150, showOverflowTooltip: true },
  { prop: 'province', label: '省份', width: 80 },
  { prop: 'city', label: '城市', width: 80 },
  // { prop: 'purchaseRepresentativeName', label: '采购代表', width: 100 },
  { 
    prop: 'qualityRating', 
    label: '质量评级', 
    width: 100, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: qualityRatings.value
  },
  { 
    prop: 'deliveryRating', 
    label: '交期评级', 
    width: 100, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: deliveryRatings.value
  },
  { 
    prop: 'status', 
    label: '状态', 
    width: 90, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: supplierStatuses.value
  },
  {
    prop: 'isKeySupplier',
    label: '重点供应商',
    width: 100,
    slot: true,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
  {
    prop: 'isStrategicSupplier',
    label: '战略供应商',
    width: 100,
    slot: true,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 180,
    sortable: true,
    filterable: true,
    filterType: 'date',
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

// --- Form Fields (Supplier) --- 
const supplierFormFields = computed<HeaderField[]>(() => [
  // 基本信息分组
  {
    field: 'supplierCode',
    label: '供应商编码',
    disabled: formMode.value === 'edit' || isViewing.value,
    placeholder: '留空可自动生成或手动输入',
    group: '基本信息',
    rules: [
      { pattern: /^[a-zA-Z0-9_-]*$/, message: '编码只能包含字母、数字、下划线和中划线' },
      { max: 50, message: '编码长度不能超过50' }
    ],
  },
  {
    field: 'supplierName',
    label: '供应商名称',
    disabled: isViewing.value,
    placeholder: '请输入供应商名称',
    group: '基本信息',
    rules: [{ max: 255, message: '名称长度不能超过255' }]
  },
  {
    field: 'supplierType',
    label: '供应商类型',
    type: 'select',
    options: supplierTypes,
    defaultValue: 'CORPORATE',
    placeholder: '请选择供应商类型',
    disabled: isViewing.value,
    group: '基本信息',
  },
  {
    field: 'industry',
    label: '所属行业',
    type: 'select',
    options: industries,
    disabled: isViewing.value,
    placeholder: '请选择所属行业',
    group: '基本信息',
    rules: [{ max: 100, message: '行业长度不能超过100' }]
  },
  {
    field: 'isKeySupplier',
    label: '重点供应商',
    type: 'switch',
    props: {
      activeValue: true,
      inactiveValue: false,
    },
    defaultValue: false,
    disabled: isViewing.value,
    group: '基本信息',
  },
  {
    field: 'isStrategicSupplier',
    label: '战略供应商',
    type: 'switch',
    props: {
      activeValue: true,
      inactiveValue: false,
    },
    defaultValue: false,
    disabled: isViewing.value,
    group: '基本信息',
  },
  {
    field: 'status',
    label: '状态',
    type: 'select',
    options: supplierStatuses,
    defaultValue: 'ACTIVE',
    disabled: isViewing.value,
    group: '基本信息',
  },
  {
    field: 'remark',
    label: '备注',
    type: 'textarea',
    props: { rows: 1 },
    disabled: isViewing.value,
    group: '基本信息',
    md: 6,
    lg: 6,
    xl: 6,
    rules: [{ max: 500, message: '备注长度不能超过500' }]
  },
  
  // 财务信息分组
  {
    field: 'businessLicense',
    label: '营业执照号',
    disabled: isViewing.value,
    group: '财务信息',
    placeholder: '请输入营业执行照号',
    rules: [{ max: 100, message: '营业执照号长度不能超过100' }],
    asyncValidator: async (rule: any, value: string) => {
      console.log('rule', rule)
      if (!value || value.trim() === '') return true;
      
      try {
        const result = await validateBusinessLicense(value, formMode.value === 'edit' ? (currentEditingSupplier.value as any)?.id : undefined);
        if (!result.valid) {
          throw new Error(result.message || '营业执照号已存在');
        }
        return true;
      } catch (error: any) {
        throw new Error(error.message || '营业执照号验证失败');
      }
    }
  },
  {
    field: 'taxNumber',
    label: '税务登记号',
    disabled: isViewing.value,
    placeholder: '请输入税务登记号',
    group: '财务信息',
    rules: [{ max: 100, message: '税务登记号长度不能超过100' }],
    asyncValidator: async (rule: any, value: string) => {
      console.log('rule', rule)
      if (!value || value.trim() === '') return true;
      
      try {
        const result = await validateTaxNumber(value, formMode.value === 'edit' ? (currentEditingSupplier.value as any)?.id : undefined);
        if (!result.valid) {
          throw new Error(result.message || '税务登记号已存在');
        }
        return true;
      } catch (error: any) {
        throw new Error(error.message || '税务登记号验证失败');
      }
    }
  },
  {
    field: 'legalRepresentative',
    label: '法定代表人',
    disabled: isViewing.value,
    placeholder: '请输入法定代表人',
    group: '财务信息',
    rules: [{ max: 100, message: '法定代表人长度不能超过100' }]
  },
  {
    field: 'registeredCapital',
    label: '注册资本',
    type: 'number',
    props: { precision: 2, min: 0, step: 10000 },
    disabled: isViewing.value,
    group: '财务信息',
  },
  {
    field: 'creditRating',
    label: '信用等级',
    type: 'select',
    options: creditRatings,
    placeholder: '请选择信用等级',
    disabled: isViewing.value,
    group: '财务信息',
    rules: [{ max: 20, message: '信用等级长度不能超过20' }]
  },
  {
    field: 'creditLimit',
    label: '信用额度',
    type: 'number',
    props: { precision: 2, min: 0, step: 10000 },
    disabled: isViewing.value,
    group: '财务信息',
  },
  {
    field: 'paymentTerms',
    label: '付款条件',
    type: 'select',
    options: paymentTerms,
    placeholder: '请选择付款条件',
    disabled: isViewing.value,
    group: '财务信息',
    rules: [{ max: 50, message: '付款条件长度不能超过50' }]
  },
  {
    field: 'currencyCode',
    label: '结算币种',
    type: 'select',
    options: currencyOptions,
    disabled: isViewing.value,
    defaultValue: 'CNY',
    group: '财务信息',
    rules: [{ max: 10, message: '币种代码长度不能超过10' }]
  },
      
  // 业务信息分组
  {
    field: 'supplierLevel',
    label: '供应商级别',
    type: 'select',
    options: supplierLevels,
    defaultValue: 'C',
    disabled: isViewing.value,
    group: '业务信息',
  },
  {
    field: 'supplierCategory',
    label: '供应商分类',
    type: 'select',
    options: supplierCategories,
    defaultValue: 'OTHER',
    disabled: isViewing.value,
    group: '业务信息',
    rules: [{ max: 50, message: '供应商分类长度不能超过50' }]
  },
  {
    field: 'supplierSource',
    label: '供应商来源',
    disabled: isViewing.value,
    placeholder: '请选择供应商来源',
    group: '业务信息',
    rules: [{ max: 50, message: '供应商来源长度不能超过50' }]
  },
  
  // 供应商专业字段
  {
    field: 'qualityRating',
    label: '质量评级',
    type: 'select',
    options: qualityRatings,
    placeholder: '请选择质量评级',
    disabled: isViewing.value,
    group: '业务信息',
  },
  {
    field: 'deliveryRating',
    label: '交期评级',
    type: 'select',
    options: deliveryRatings,
    placeholder: '请选择交期评级',
    disabled: isViewing.value,
    group: '业务信息',
  },
  {
    field: 'serviceRating',
    label: '服务评级',
    type: 'select',
    options: serviceRatings,
    placeholder: '请选择服务评级',
    disabled: isViewing.value,
    group: '业务信息',
  },
  {
    field: 'annualSupplyCapacity',
    label: '年供货能力',
    type: 'number',
    props: { precision: 2, min: 0, step: 10000 },
    disabled: isViewing.value,
    group: '业务信息',
  },
  {
    field: 'mainProducts',
    label: '主要产品',
    type: 'textarea',
    props: { rows: 1, placeholder: '请输入主要产品' },
    disabled: isViewing.value,
    group: '业务信息',
    md: 12,
    lg: 12,
    xl: 12
  },
  {
    field: 'certificationInfo',
    label: '资质认证',
    type: 'textarea',
    props: { rows: 1, placeholder: '请输入资质认证' },
    disabled: isViewing.value,
    group: '业务信息',
    md: 12,
    lg: 12,
    xl: 12
  },
  // 联系信息分组
  {
    field: 'contactPerson',
    label: '主要联系人',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '联系人长度不能超过100' }]
  },
  {
    field: 'contactPhone',
    label: '联系电话',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 50, message: '电话长度不能超过50' }]
  },
  {
    field: 'contactEmail',
    label: '联系邮箱',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [
      { type: 'email', message: '请输入正确的邮箱格式' },
      { max: 255, message: '邮箱长度不能超过255' }
    ]
  },
  {
    field: 'website',
    label: '公司网站',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 255, message: '网站长度不能超过255' }]
  },
  
  // 地址信息分组
  {
    field: 'country',
    label: '国家',
    disabled: isViewing.value,
    defaultValue: '中国',
    group: '联系信息',
    rules: [{ max: 100, message: '国家长度不能超过100' }]
  },
  {
    field: 'province',
    label: '省份',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '省份长度不能超过100' }]
  },
  {
    field: 'city',
    label: '城市',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '城市长度不能超过100' }]
  },
  {
    field: 'district',
    label: '区县',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '区县长度不能超过100' }]
  },
  {
    field: 'address',
    label: '详细地址',
    type: 'textarea',
    props: { rows: 1 },
    disabled: isViewing.value,
    group: '联系信息',
    md: 12,
    lg: 12,
    xl: 12
  },
  {
    field: 'postalCode',
    label: '邮政编码',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 20, message: '邮政编码长度不能超过20' }]
  },
  
]);

// --- Lifecycle --- 
onMounted(async () => {
  // 组件挂载时加载字典数据和供应商数据
  await loadDictionaryData();
  loadSuppliers();
});

// --- Dictionary Loading Methods ---

// 加载所有字典数据
const loadDictionaryData = async () => {
  try {
    const [
      supplierTypeData,
      supplierLevelData,
      supplierStatusData,
      supplierCategoryData,
      creditRatingData,
      paymentTermsData,
      industryData,
      contactRoleData,
      genderData,
      qualityRatingData,
      deliveryRatingData,
      serviceRatingData,
      currencyData
    ] = await Promise.all([
      getDictDataByCode('supplier_type'),
      getDictDataByCode('supplier_level'),
      getDictDataByCode('supplier_status'),
      getDictDataByCode('supplier_category'),
      getDictDataByCode('credit_rating'),
      getDictDataByCode('payment_terms'),
      getDictDataByCode('industry'),
      getDictDataByCode('contact_role'),
      getDictDataByCode('gender'),
      getDictDataByCode('quality_rating'),
      getDictDataByCode('delivery_rating'),
      getDictDataByCode('service_rating'),
      getEnabledCurrencyList()
    ]);

    supplierTypes.value = supplierTypeData;
    supplierLevels.value = supplierLevelData;
    supplierStatuses.value = supplierStatusData;
    supplierCategories.value = supplierCategoryData;
    creditRatings.value = creditRatingData;
    paymentTerms.value = paymentTermsData;
    industries.value = industryData;
    contactRoles.value = contactRoleData;
    genders.value = genderData;
    qualityRatings.value = qualityRatingData;
    deliveryRatings.value = deliveryRatingData;
    serviceRatings.value = serviceRatingData;
    
    // 转换币种数据格式
    currencyOptions.value = currencyData.map((c: CurrencyListItem) => ({
      label: `${c.name} (${c.code})`,
      value: c.code
    }));

    console.log('供应商字典数据加载完成');
  } catch (error) {
    console.error('加载供应商字典数据失败:', error);
    ElMessage.error('加载字典数据失败，部分功能可能受影响');
  }
};

// --- Methods ---

// 加载供应商数据
const loadSuppliers = async (paramsOverwrite = {}) => {
  supplierLoading.value = true;
  try {
    // 准备排序参数
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop && currentSort.value.order) {
      const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
      sortString = `${currentSort.value.prop},${direction}`;
    }

    // 准备筛选参数
    const filterParams: Record<string, any> = {};
    for (const key in currentFilters.value) {
      const filterValue = currentFilters.value[key];
      if (filterValue !== null && filterValue !== undefined && filterValue !== '' && !(Array.isArray(filterValue) && filterValue.length === 0)) {
         if (key === 'status' && Array.isArray(filterValue) && filterValue.length > 0) {
             filterParams[key] = filterValue[0]; 
         } else if (key === 'createdAt' && Array.isArray(filterValue) && filterValue.length === 2) {
             filterParams['startDate'] = filterValue[0];
             filterParams['endDate'] = filterValue[1];
         } else {
             filterParams[key] = filterValue;
         }
      }
    }

    const params: Partial<ScmSupplierQueryDTO> & { pageNum: number; pageSize: number; sort?: string } = {
      pageNum: supplierPagination.currentPage,
      pageSize: supplierPagination.pageSize,
      sort: sortString,
      ...filterParams, 
      ...paramsOverwrite, 
    };

    // 清理空值参数
    Object.keys(params).forEach(key => {
        const paramKey = key as keyof typeof params;
        if (params[paramKey] === null || params[paramKey] === undefined || params[paramKey] === '') {
            delete params[paramKey];
        }
    });

    console.log('[loadSuppliers] Fetching with params:', params);
    const response = await getSupplierPage(params);

    if (response && Array.isArray(response.list)) { 
        supplierTableData.value = response.list;
        supplierPagination.total = response.total || 0;
    } else {
        console.warn('getSupplierPage 返回的数据格式不符合预期:', response);
        supplierTableData.value = [];
        supplierPagination.total = 0;
    }

  } catch (error) {
    console.error('Error loading suppliers:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    supplierTableData.value = [];
    supplierPagination.total = 0;
  } finally {
    supplierLoading.value = false;
  }
};

// 处理分页变化
const handleSupplierPageChange = (page: number) => {
  supplierPagination.currentPage = page;
  loadSuppliers();
};

// 处理每页数量变化
const handleSupplierPageSizeChange = (size: number) => {
  supplierPagination.pageSize = size;
  supplierPagination.currentPage = 1; 
  loadSuppliers();
};

// 处理排序变化
type SortParams = { prop: string; order: 'ascending' | 'descending' | null };
const handleSupplierSortChange = (sort: SortParams) => {
  console.log('Sort changed:', sort);
  currentSort.value = sort;
  loadSuppliers();
};

// 处理筛选变化
type FilterParams = Record<string, any>;
const handleSupplierFilterChange = (filters: FilterParams) => {
  console.log('Filter changed:', filters);
  currentFilters.value = filters; 
  supplierPagination.currentPage = 1; 
  loadSuppliers();
};

// 处理行选择变化
const handleSupplierSelectionChange = (rows: ScmSupplierListVO[]) => {
  selectedSupplierRows.value = rows;
  console.log('Selected supplier rows:', selectedSupplierRows.value);
};

// 处理新增按钮点击
const handleAddNewSupplier = () => {
  formMode.value = 'add';
  currentEditingSupplier.value = { 
    status: 'ACTIVE',
    supplierType: 'CORPORATE',
    supplierLevel: 'C',
    supplierCategory: 'OTHER',
    isKeySupplier: false,
    isStrategicSupplier: false,
    currencyCode: 'CNY',
    country: '中国'
  };
  supplierDialogVisible.value = true;
  nextTick(() => { supplierFormRef.value?.clearValidate(); });
};

// 处理编辑按钮点击
const handleEditSupplier = (row: ScmSupplierListVO) => {
  formMode.value = 'edit';
  currentEditingSupplier.value = JSON.parse(JSON.stringify(row));
  supplierDialogVisible.value = true;
  nextTick(() => { supplierFormRef.value?.clearValidate(); });
};

// 处理查看按钮点击
const handleViewSupplier = (row: ScmSupplierListVO) => {
  formMode.value = 'view';
  currentEditingSupplier.value = JSON.parse(JSON.stringify(row));
  supplierDialogVisible.value = true;
};

// 关闭供应商编辑/查看弹窗
const handleCloseSupplierDialog = () => {
  supplierDialogVisible.value = false;
};

// 提交供应商表单
const submitSupplierForm = async () => {
  if (isViewing.value) {
    handleCloseSupplierDialog();
    return;
  }

  const formIsValid = await supplierFormRef.value?.validateForm();
  if (!formIsValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  supplierFormLoading.value = true;
  const payload = { ...currentEditingSupplier.value };

  try {
    if (formMode.value === 'edit') {
      const editingData = currentEditingSupplier.value as Partial<ScmSupplierVO>; 
      if (!editingData.id) {
          ElMessage.error('无法获取要编辑的供应商ID');
          supplierFormLoading.value = false;
          return;
      }
      const updatePayload: Partial<ScmSupplierUpdateDTO> = {
         ...editingData
      };
      console.log('Calling updateSupplier API with ID:', editingData.id, 'and payload:', updatePayload);
      await updateSupplier(editingData.id, updatePayload as ScmSupplierUpdateDTO);
      ElMessage.success('编辑成功');
    } else {
      const createPayload = payload as ScmSupplierCreateDTO;
      console.log('Calling createSupplier API with:', createPayload);
      await createSupplier(createPayload);
      ElMessage.success('新增成功');
    }
    handleCloseSupplierDialog();
    loadSuppliers();
  } catch (error) {
    console.error('Error saving supplier:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    supplierFormLoading.value = false;
  }
};

// 打开供应商联系人管理模态框的函数
const openContactManageDialog = (row: ScmSupplierListVO) => {
  console.log('Opening contact management for supplier:', row);
  currentManagedSupplier.value = row; 
  contactManageDialogVisible.value = true; 
};

// 供应商表格行操作 - 删除供应商
const handleDeleteSupplier = async (row: ScmSupplierListVO) => {
  console.log('Deleting supplier:', row);

  try {
      await ElMessageBox.confirm(`确定删除供应商 "${row.supplierName}" (${row.supplierCode}) 吗? 删除后供应商相关数据将无法恢复!`, '危险操作确认', { 
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning' 
      });
      try {
         supplierLoading.value = true;
         await deleteSupplier(row.id);
         ElMessage.success('删除成功');
         loadSuppliers();
      } catch (apiError: any) {
         console.error('Error deleting supplier:', apiError);
         const errorMessage = getApiErrorMessage(apiError);
         ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
         });
      } finally {
         supplierLoading.value = false;
      }
  } catch (error) {
      if (error !== 'cancel') {
          console.error('Error during delete confirmation:', error);
      } else {
           ElMessage.info('已取消删除');
      }
  }
};

// 处理批量删除
const handleSupplierBatchDelete = (selectedRows: ScmSupplierListVO[]) => {
  if (!selectedRows || selectedRows.length === 0) {
    ElMessage.warning('请先选择要删除的供应商');
    return;
  }
  console.log('Batch delete requested for suppliers:', selectedRows.map(r => r.id));
  ElMessageBox.confirm(`确定删除选中的 ${selectedRows.length} 个供应商吗?`, '确认批量删除', { type: 'warning' })
    .then(async () => {
      ElMessage.info('批量删除功能待实现');
    })
    .catch(() => {
      ElMessage.info('已取消批量删除');
    });
};

// 处理导入
const handleSupplierImport = () => {
  console.log('Import requested for suppliers');
  ElMessage.info('导入功能待实现');
};

// 处理导出
const handleSupplierExport = () => {
  console.log('Export requested for suppliers');
  ElMessage.info('导出功能待实现');
};

// 供应商打印处理函数
const handlePrintPreviewSupplier = () => {
  const supplierToPrint = currentEditingSupplier.value as Partial<ScmSupplierVO>; 
  if (!supplierToPrint || !supplierToPrint.id) {
    ElMessage.warning('没有可打印预览的供应商数据。');
    return;
  }
  console.log('触发打印预览，供应商:', supplierToPrint);
  ElMessage.info('供应商打印预览功能待实现');
};

const handlePrintSupplier = () => {
  const supplierToPrint = currentEditingSupplier.value as Partial<ScmSupplierVO>; 
  if (!supplierToPrint || !supplierToPrint.id) {
    ElMessage.warning('没有可打印的供应商数据。');
    return;
  }
  console.log('触发打印，供应商:', supplierToPrint);
  ElMessage.info('供应商打印功能待实现');
};

</script>

<style scoped>
/* Add any specific styles if needed */
</style> 