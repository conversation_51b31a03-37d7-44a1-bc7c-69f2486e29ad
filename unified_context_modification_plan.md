# 后端中间件统一上下文用户信息来源修改方案

## 1. 目标

统一项目中所有后端中间件的用户信息及其他共享请求级信息（如账套 ID、客户端 IP、追踪 ID）的来源，使其全部通过标准的 Go `context.Context` (`ctx.Request().Context()`) 进行传递和获取。`AuthMiddleware` 将作为核心，负责在用户认证成功后将用户信息注入标准 Go Context。

## 2. 核心原则

- **标准 Go Context 优先**：所有共享的请求级信息应存储在 `http.Request` 关联的 `context.Context` 中。
- **统一的键定义**：所有上下文键在 `pkg/constant/constant.go` 中以字符串常量形式统一定义。
- **单一写入点**：对于特定信息（如用户信息），应有单一的中间件负责写入（如 `AuthMiddleware` 写入用户信息）。
- **统一读取点**：所有需要这些信息的中间件或处理器都从标准 Go Context 使用 `pkg/util/context.go` 中的辅助函数读取。

## 3. 详细修改步骤

### 步骤 3.1: 确认并补充标准 Go Context 键定义

项目已在 `backend/pkg/constant/constant.go` 文件中定义了部分上下文键作为字符串常量。

**现有相关键 (摘自 `backend/pkg/constant/constant.go`):**

```go
// 上下文键值常量
const (
	CONTEXT_USER_ID         = "userId"        // 用户ID上下文键
	CONTEXT_USERNAME        = "username"      // 用户名上下文键
	CONTEXT_ROLE_IDS        = "roleIds"       // 角色ID列表上下文键
	CONTEXT_IS_ADMIN        = "isAdmin"       // 是否管理员上下文键
	CONTEXT_ACCOUNT_BOOK_ID = "accountBookId" // 账套ID上下文键
	CONTEXT_REQUEST_ID      = "requestId"     // 请求ID上下文键 (可考虑是否用作 TraceID)
	// ... 其他已有的键 ...
)
```

**建议在 `backend/pkg/constant/constant.go` 中确认或补充以下键 (如果与现有 `CONTEXT_REQUEST_ID` 职责不同或需要更明确的键名):**

```go
// backend/pkg/constant/constant.go (建议补充部分)
const (
	// ... 已有的 context 常量 ...
	CONTEXT_CLIENT_IP        = "clientIP"          // 新增：客户端IP上下文键
	CONTEXT_TRACE_ID         = "traceID"           // 新增或确认：追踪ID上下文键 (若与CONTEXT_REQUEST_ID不同)
	CONTEXT_REQUEST_START_TIME = "requestStartTime"  // 新增：请求开始时间上下文键 (可选)
)
```

**注意:** 后续步骤将假设 `CONTEXT_CLIENT_IP` 和 `CONTEXT_TRACE_ID` 作为独立的常量存在。如果决定复用 `CONTEXT_REQUEST_ID` 作为追踪 ID，请在实现时相应调整。

### 步骤 3.2: 使用现有的上下文工具函数 (`backend/pkg/util/context.go`) 并按需补充

项目已存在 `backend/pkg/util/context.go` 文件，其中包含了一系列用于从标准 `context.Context` 安全提取数据的辅助函数。这些函数已集成 `pkg/errors` 进行错误处理。

**现有相关辅助函数 (摘自 `backend/pkg/util/context.go`):**

- `GetUserIDFromStdContext(ctx context.Context) (uint64, error)`
- `GetUsernameFromStdContext(ctx context.Context) (string, error)`
- `GetRoleIDsFromStdContext(ctx context.Context) ([]uint, error)`
- `IsAdminFromStdContext(ctx context.Context) (bool, error)`
- `GetAccountBookIDFromStdContext(ctx context.Context) (uint64, error)`

**建议在 `backend/pkg/util/context.go` 中补充以下函数 (如果尚未存在):**

```go
// backend/pkg/util/context.go (建议补充部分)
// ... (已有的 import 和函数, 确保引入 "backend/pkg/constant" 和 "backend/pkg/errors")

// GetClientIPFromStdContext 从标准 context.Context 中提取客户端IP
func GetClientIPFromStdContext(ctx context.Context) (string, error) {
	// 使用已有的 getStringFromStdContext 内部辅助函数
	// 错误代码的选择应符合项目 pkg/errors 规范。
	// 考虑使用更具体的错误码，如 errors.CODE_CONTEXT_VALUE_MISSING (如果已定义) 或一个通用的"上下文信息缺失"错误码，
	// 以更准确地反映"信息缺失"的本质，而非直接的"未授权"。
	return getStringFromStdContext(ctx, constant.CONTEXT_CLIENT_IP, // 使用 pkg/constant 中的键
		errors.CODE_AUTH_UNAUTHORIZED, // 示例：如果获取不到IP，可视为未授权或信息缺失
		errors.CODE_PARAMS_TYPE_ERROR, // 类型不匹配时的错误码
		"上下文中缺少客户端IP信息",
		"客户端IP类型无效")
}

// GetTraceIDFromStdContext 从标准 context.Context 中提取追踪ID
func GetTraceIDFromStdContext(ctx context.Context) (string, error) {
	return getStringFromStdContext(ctx, constant.CONTEXT_TRACE_ID, // 使用 pkg/constant 中的键
		errors.CODE_SYSTEM_INTERNAL, // 示例：追踪ID缺失可能是系统问题
		errors.CODE_PARAMS_TYPE_ERROR,
		"上下文中缺少追踪ID信息",
		"追踪ID类型无效")
}

// GetRequestStartTimeFromStdContext 从标准 context.Context 中提取请求开始时间
func GetRequestStartTimeFromStdContext(ctx context.Context) (time.Time, error) {
	if ctx == nil {
		return time.Time{}, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "standard context is nil")
	}
	value := ctx.Value(constant.CONTEXT_REQUEST_START_TIME) // 使用 pkg/constant 中的键
	if value == nil {
		// 根据业务需求，这里可能不应该是一个错误，或者是一个特定类型的"信息未找到"错误。
		// 例如，如果请求开始时间仅用于日志记录或可选的监控，那么记录一个调试信息并继续可能是合适的。
		return time.Time{}, errors.NewSystemError(errors.CODE_CONTEXT_VALUE_MISSING, "上下文中缺少请求开始时间") // 假设 CODE_CONTEXT_VALUE_MISSING 已定义
	}
	startTime, ok := value.(time.Time)
	if !ok {
		return time.Time{}, errors.NewSystemError(errors.CODE_PARAMS_TYPE_ERROR, fmt.Sprintf("请求开始时间类型无效 (actual type: %T)", value))
	}
	return startTime, nil
}
```

**类型注意:** `AuthMiddleware` 应确保存入上下文的值类型与 `pkg/util/context.go` 中 getter 函数期望转换的源类型一致。例如, `CONTEXT_USER_ID` 通常存为 `uint` (如果 Claims 中的 UserID 是`uint`) 或 `uint64`。`GetUserIDFromStdContext` 返回 `uint64`，因此在中间件中如果原始逻辑需要 `uint`，则需要 `uint(userID64)` 转换。

**处理可选的上下文值:** 当调用 `GetXFromStdContext` 系列函数时，如果获取的值对于当前中间件的后续操作是**必需的** (例如，`AuthMiddleware` 中的用户信息)，那么在获取失败（返回错误）时，中间件应中断请求并将错误传递给上层（例如，通过 `response.FailWithError`)。如果获取的值是**可选的** (例如，仅用于丰富日志信息或某些非核心功能)，中间件可以在获取失败时记录一个警告或调试日志，然后继续处理请求，而不是直接失败。

### 步骤 3.3: 修改 `AuthMiddleware` (`internal/middleware/auth_middleware.go`)

**主要改动点：**

1.  确保所有存入上下文的用户信息键都使用 `pkg/constant/constant.go` 中定义的**字符串常量**。
2.  确保存储的类型与 `pkg/util/context.go` 中对应 getter 函数期望的源类型一致 (例如, `CONTEXT_USER_ID` 对应 `uint` 或 `uint64` - 根据 `claims.UserID` 的实际类型决定)。

**示例片段 (令牌验证成功后):**

```diff
// ... 之前的代码 ...
	// 将用户信息直接设置到标准的 context.Context 中
	standardCtx := ctx.Request().Context()
	// 假设 claims.UserID 是 uint64, claims.RoleIDs 是 []uint, claims.IsAdmin 是 bool
	// 使用 pkg/constant/constant.go 中定义的字符串常量键
	standardCtxWithUser := context.WithValue(standardCtx, constant.CONTEXT_USER_ID, claims.UserID)
	standardCtxWithUser = context.WithValue(standardCtxWithUser, constant.CONTEXT_USERNAME, claims.Username)
	standardCtxWithUser = context.WithValue(standardCtxWithUser, constant.CONTEXT_ROLE_IDS, claims.RoleIDs)
	standardCtxWithUser = context.WithValue(standardCtxWithUser, constant.CONTEXT_IS_ADMIN, claims.IsAdmin)

	// 更新请求中的上下文，确保后续处理程序能获取到
	req := ctx.Request().WithContext(standardCtxWithUser)
	ctx.ResetRequest(req)

	m.log.Infof(standardCtxWithUser, "AuthMiddleware: UserID %d, Username '%s', RoleIDs %v, IsAdmin %t explicitly set into standard context (from ORIGINAL token)", claims.UserID, claims.Username, claims.RoleIDs, claims.IsAdmin)

	m.log.Info(standardCtxWithUser, "AuthMiddleware: Context set. Calling Next().")
	ctx.Next()
// ... 后续代码 ...
```

**注意：** 对于 Token 刷新成功的逻辑部分，也需要进行类似的修改。确保 `newClaims` 中的信息（如 `newClaims.UserID`）在存入上下文时使用正确的字符串常量键和类型。如果 `newClaims.UserID` 的类型与 `claims.UserID` 不同，确保进行适当转换以匹配 `GetUserIDFromStdContext` 的期望。

### 步骤 3.4: 修改 `AccountBookContextHandler` (`internal/middleware/account_book.go`)

**主要改动点：**

1.  使用 `userID64, errUser := util.GetUserIDFromStdContext(reqCtx)` 获取 `UserID`。处理返回的 `*errors.CustomError` 类型的 `errUser`。
2.  将 `accountBookID` (确保其类型为 `uint` 或 `uint64`) 使用 `constant.CONTEXT_ACCOUNT_BOOK_ID` 字符串常量键存入上下文。

**示例片段:**

```diff
// ... 之前的代码 ...
 		reqCtx := ctx.Request().Context()
 		userID64, errUser := util.GetUserIDFromStdContext(reqCtx)
+		if errUser != nil { // errUser is *errors.CustomError
+			log.Warn(reqCtx, "AccountBookContextHandler: Failed to get UserID from context", logger.WithError(errUser), logger.Int("errorCode", errUser.Code))
+			response.FailWithError(ctx, errUser) // Pass the custom error directly
 			return
 		}
+		// GetUserIDFromStdContext should already handle UserID == 0 by returning an error with CODE_AUTH_UNAUTHORIZED.
+		// If it could return (0, nil) under some circumstance not covered by its current error codes, that would be a bug in GetUserIDFromStdContext.
+		userID := uint(userID64) // Convert to uint if other logic specifically requires uint
+		// No need to re-check for userID == 0 if GetUserIDFromStdContext guarantees an error for it.


 // ... 权限检查逻辑 (使用 userID) ...

 		// 将验证后的账套ID存入标准 Go 上下文
-		// accountBookID 类型应为 uint 或 uint64，与 pkg/constant 中定义一致
 		reqCtxWithAccountBook := context.WithValue(reqCtx, constant.CONTEXT_ACCOUNT_BOOK_ID, accountBookID) // 使用字符串常量键

 		// 更新请求中的上下文
 		ctx.ResetRequest(ctx.Request().WithContext(reqCtxWithAccountBook))

-		log.Debug(reqCtxWithAccountBook, "账套上下文设置成功", logger.WithField("accountBookID", accountBookID), logger.WithField("userID", userID))
+		log.Debug(reqCtxWithAccountBook, "AccountBookContextHandler: Context updated with AccountBookID", logger.Uint("accountBookID", accountBookID), logger.Uint("userID", userID))
 // ... 后续代码 ...
```

### 步骤 3.5: 修改 `AuditMiddleware` (`internal/middleware/audit.go`)

**主要改动点：**

1.  使用 `util.GetUserIDFromStdContext(reqCtx)` (返回 `uint64`), `util.GetUsernameFromStdContext(reqCtx)` (返回 `string`), 和 `util.GetClientIPFromStdContext(reqCtx)` (返回 `string`) 获取信息。并处理它们可能返回的 `*errors.CustomError`。

**示例片段:**

```diff
// ... 之前的代码 ...
 		reqCtx := ctx.Request().Context()
 		var userID uint = 0
 		var username string
 		var clientIP string

 		userID64, errUser := util.GetUserIDFromStdContext(reqCtx)
+		if errUser == nil { // GetUserIDFromStdContext should return error if UserID is 0
 			userID = uint(userID64)
 		} else {
+			// Log is not part of AuditMiddleware struct 'm', assuming direct use or injected logger
+			logger.GetLogger().Debug(reqCtx, "AuditMiddleware: UserID not found or invalid in context for audit", logger.WithError(errUser))
 		}
+// (注意: 此处及后续 AuditMiddleware 内的日志记录假设使用全局或包级 logger。如果中间件通过依赖注入获得了 logger 实例，例如 m.log，应优先使用该实例进行日志记录。)

 		retrievedUsername, errUsername := util.GetUsernameFromStdContext(reqCtx)
 		if errUsername == nil {
 			username = retrievedUsername
 		} else {
+			logger.GetLogger().Debug(reqCtx, "AuditMiddleware: Username not found or invalid in context for audit", logger.WithError(errUsername))
 		}
+// (注意: 同上，关于 logger 实例的使用。)

 		retrievedClientIP, errClientIP := util.GetClientIPFromStdContext(reqCtx) // 假设已添加到util
 		if errClientIP == nil && retrievedClientIP != "" {
 			clientIP = retrievedClientIP
 		} else {
 			if errClientIP != nil {
+				logger.GetLogger().Debug(reqCtx, "AuditMiddleware: ClientIP not found or invalid in context for audit. Falling back to RemoteAddr.", logger.WithError(errClientIP))
 			}
 			clientIP = ctx.RemoteAddr() // Fallback
 		}
+// (注意: 同上，关于 logger 实例的使用。)


 		// 准备审计事件
 		event := &AuditEvent{
+			UserID:       userID, // userID is uint
 			Username:     username,
 			Action:       getAction(ctx.Method(), cfg.ActionMappings),
 			ResourceType: getResourceType(ctx.Path(), resourceTypeRegex, cfg.IgnoredResourceTypes),
@@ -239,12 +264,12 @@
 				(cfg.LimiterType != LIMITER_TYPE_CACHE_SERVICE && cfg.UserRate > 0) {
 				reqCtx := ctx.Request().Context()
 				userID64, errUser := util.GetUserIDFromStdContext(reqCtx)
+				if errUser == nil { // GetUserIDFromStdContext should return error if UserID is 0
+					userID := uint(userID64) // Convert to uint for key generation if needed
-					userID := uint(userID64)
 					userKey := fmt.Sprintf("user:%d:%s", userID, pathForLog)
 					// ... (后续限流逻辑) ...
 				} else {
+					// Log is not part of RateLimitMiddleware struct 'm', assuming direct use or injected logger
+					logger.GetLogger().Warn(reqCtx, "RateLimitMiddleware: UserID for rate limiting not found or invalid. Path: %s. Error: %v. Allowing request to pass (or configure strict mode).", pathForLog, errUser)
+					// (注意: 此处 RateLimitMiddleware 内的日志记录假设使用全局或包级 logger。如果中间件通过依赖注入获得了 logger 实例，例如 m.log，应优先使用该实例进行日志记录。)
-					log.Warnf(reqCtx, "RateLimit: UserID for rate limiting not found or invalid. Path: %s. Error: %v. Allowing request to pass (or configure strict mode).", pathForLog, errUser)
 				}
 			}

```

### 步骤 3.7: 修改 `RBACMiddleware` (`internal/middleware/rbac.go`)

**主要改动点：**

1.  使用 `util.GetUserIDFromStdContext(reqCtx)` (转为 `uint`), `util.GetRoleIDsFromStdContext(reqCtx)`, `util.IsAdminFromStdContext(reqCtx)` 等函数获取信息，并处理返回的 `*errors.CustomError`。

**示例片段 (`CheckPermission`):**

```diff
// ... 之前的代码 ...
 		reqCtx := ctx.Request().Context()
 		userID64, errUser := util.GetUserIDFromStdContext(reqCtx)
+		if errUser != nil { // GetUserIDFromStdContext should return error if UserID is 0
-		if errUser != nil { // GetUserIDFromStdContext 内部已处理 userID64 == 0 的情况并返回错误
 			m.log.Warn(reqCtx, "RBACMiddleware: Failed to get UserID from context", logger.WithError(errUser))
 			CustomResponse.FailWithError(ctx, errUser) // errUser is *errors.CustomError
+			// (注意: 此处 RBACMiddleware CheckPermission 内的日志记录使用了 m.log，假设 m 是中间件实例且包含 logger。若使用全局 logger，则应相应调整或确认全局 logger 的适用性。下同。)
 			return
 		}
 		userID := uint(userID64)

 		roleIDs, errRoles := util.GetRoleIDsFromStdContext(reqCtx)
 		if errRoles != nil {
 			m.log.Warn(reqCtx, "RBACMiddleware: Failed to get RoleIDs from context", logger.WithError(errRoles))
 			CustomResponse.FailWithError(ctx, errRoles) // errRoles is *errors.CustomError
+			// (注意: 同上，针对 RBACMiddleware CheckPermission 内 m.log 的使用。)
 			return
 		}
+		// GetRoleIDsFromStdContext will return an error if the key is missing.
+		// If the key exists but the value is nil or an empty slice, it will be returned as such (nil error).
+		// Business logic downstream should handle cases like len(roleIDs) == 0 if users must have roles.
-		// GetRoleIDsFromStdContext 对于键不存在会返回错误，如果键存在但值是nil/空slice，它会按原样返回
-		// 根据业务，如果用户必须有角色才能通过某些检查，则需要在这里判断 if len(roleIDs) == 0

  // ... 后续代码 (使用 userID 和 roleIDs) ...
```

**示例片段 (`AdminRequiredMiddleware`):**

```diff
// ... 之前的代码 ...
 func AdminRequiredMiddleware() iris.Handler {
 	return func(ctx iris.Context) {
		reqCtx := ctx.Request().Context()
		isAdmin, errIsAdmin := util.IsAdminFromStdContext(reqCtx)

+		if errIsAdmin != nil { // IsAdminFromStdContext returns (false, nil) if key is missing. This error is for type mismatch.
-		if errIsAdmin != nil { // IsAdminFromStdContext 对于键不存在返回 (false, nil), 此处err通常是类型不匹配
-			// m.log is not defined in this scope, assuming logger.GetLogger() or similar
			logger.GetLogger().Warn(reqCtx, "AdminRequiredMiddleware: Error checking admin status from context (likely type mismatch).", logger.WithError(errIsAdmin))
			CustomResponse.FailWithError(ctx, errIsAdmin) // Pass the *errors.CustomError
+			// (注意: 此处 AdminRequiredMiddleware 内的日志记录假设使用全局或包级 logger。如果中间件结构体中定义了 m.log 并通过依赖注入获得，应优先使用。)
 			return
 		}
 		if !isAdmin {
@@ -300,13 +325,12 @@
 // ... 引入 context 和 pkg/constant ...

 // ... Serve 方法 ...
+	clientIP := util.GetClientIP(ctx.Request()) // This is from Iris's request, not context util yet.
-	clientIP := util.GetClientIP(ctx.Request())
-
--	// 将 IP 存入标准的 Go Context 中
+
 	reqCtx := ctx.Request().Context()
--	reqCtxWithIP := context.WithValue(reqCtx, constant.CONTEXT_CLIENT_IP, clientIP)
--+	// 使用 pkg/constant/constant.go 中定义的字符串常量键
+	// Store the retrieved IP into the standard Go context using the constant key
+	reqCtxWithIP := context.WithValue(reqCtx, constant.CONTEXT_CLIENT_IP, clientIP)

 	// 更新请求中的上下文
-+ ...
+	ctx.ResetRequest(ctx.Request().WithContext(reqCtxWithIP))
+
+	ctx.Next()
```

### 步骤 3.9: (可选) 调整 `LoggerMiddleware`

如果 `LoggerMiddleware` 需要记录 `UserID`, `ClientIP`, 或 `TraceID`，它也应该从标准 Go Context 中尝试获取这些信息，并使用 `pkg/util/context.go` 中的辅助函数。

**主要改动点：**

1.  在生成或获取 `TraceID` (例如，使用 `constant.CONTEXT_TRACE_ID` 字符串常量键) 后，将其存入标准 Go Context。同样可以考虑存入请求开始时间 (`constant.CONTEXT_REQUEST_START_TIME`)。
2.  记录日志时，尝试从上下文中获取 `UserID`, `ClientIP` 等，并处理可能发生的错误。

**示例 (在 `LoggerMiddleware` 中设置和使用 `TraceID`):**

```go
// 伪代码 - LoggerMiddleware
// ...
reqCtx := ctx.Request().Context()
var traceID string

headerTraceID := ctx.GetHeader("X-Request-ID")
if headerTraceID != "" {
    traceID = headerTraceID
} else {
    traceID = GenerateNewUUID()
}

// 使用 pkg/constant/constant.go 中定义的字符串常量键
reqCtxWithTraceID := context.WithValue(reqCtx, constant.CONTEXT_TRACE_ID, traceID)
// 可选：同时存入请求开始时间
// reqCtxWithTraceID = context.WithValue(reqCtxWithTraceID, constant.CONTEXT_REQUEST_START_TIME, time.Now())
ctx.ResetRequest(ctx.Request().WithContext(reqCtxWithTraceID))

// ... 在记录日志时 ...
logFields := []logger.Field{logger.String(constant.CONTEXT_TRACE_ID, traceID)} // Use constant as field name too

userID64, errUser := util.GetUserIDFromStdContext(ctx.Request().Context())
if errUser == nil {
    logFields = append(logFields, logger.Uint64(constant.CONTEXT_USER_ID, userID64))
} else {
    // 可选：记录获取UserID失败的日志，或不记录UserID字段
    logger.GetLogger().Debug(ctx.Request().Context(), "LoggerMiddleware: UserID not available for logging", logger.WithError(errUser))
}

clientIP, errClientIP := util.GetClientIPFromStdContext(ctx.Request().Context())
if errClientIP == nil {
    logFields = append(logFields, logger.String(constant.CONTEXT_CLIENT_IP, clientIP))
}

// requestStartTime, errStartTime := util.GetRequestStartTimeFromStdContext(ctx.Request().Context())
// if errStartTime == nil {
// logFields = append(logFields, logger.Time("requestStartTime", requestStartTime))
// }


// logger.GetLogger().Info(ctx.Request().Context(), "HTTP Request", logFields...)
```

### 步骤 3.10: (重要) 中间件执行顺序

项目中定义了多个中间件，它们之间可能存在依赖关系，特别是对于写入和读取标准 Go Context 中的信息。因此，这些中间件在 Iris 应用中注册的**执行顺序**至关重要。

**推荐的中间件注册顺序及理由：**

1.  **`LoggerMiddleware` (初始部分)**:

    - **职责**: 尽早生成或获取 `TraceID`，记录请求开始时间 `RequestStartTime`。
    - **写入 Context**: `constant.CONTEXT_TRACE_ID`, `constant.CONTEXT_REQUEST_START_TIME`。
    - **理由**: 这些信息对于后续所有中间件和处理器的日志记录和追踪都非常有用，应最先设置。

2.  **`IPMiddleware`**:

    - **职责**: 确定真实的客户端 IP 地址。
    - **写入 Context**: `constant.CONTEXT_CLIENT_IP`。
    - **读取 Context**: 可能间接依赖 `TraceID` 进行日志。
    - **理由**: 客户端 IP 是许多后续操作（如审计、限流、特定业务逻辑）的基础信息。

3.  **`AuthMiddleware`**:

    - **职责**: 用户身份验证与授权，解析 Token。
    - **写入 Context**: `constant.CONTEXT_USER_ID`, `constant.CONTEXT_USERNAME`, `constant.CONTEXT_ROLE_IDS`, `constant.CONTEXT_IS_ADMIN`。
    - **读取 Context**: 可能依赖 `TraceID`, `ClientIP` 进行日志或安全检查。
    - **理由**: 用户信息是进行后续账套绑定、RBAC 权限检查、审计等操作的核心。

4.  **`AccountBookContextHandler` (如果作为中间件使用)**:

    - **职责**: 根据用户信息和请求参数确定并验证当前操作的账套 ID。
    - **写入 Context**: `constant.CONTEXT_ACCOUNT_BOOK_ID`。
    - **读取 Context**: 必须在 `AuthMiddleware` 之后，依赖 `constant.CONTEXT_USER_ID`。也可能依赖 `TraceID`。
    - **理由**: 确保在执行业务逻辑前，账套信息已正确设置并验证。

5.  **其他业务与安全相关的中间件**:

    - **`RateLimitMiddleware`**: 可能依赖 `UserID` 或 `ClientIP` 进行限流。
    - **`AuditMiddleware`**: 依赖 `UserID`, `Username`, `ClientIP`，以及后续处理器执行的操作结果。通常在业务逻辑执行*之后*记录完整的审计事件，但其准备阶段可能在请求处理早期。
    - **`RBACMiddleware`**: 依赖 `UserID`, `RoleIDs`, `IsAdmin` 进行权限校验。
    - **理由**: 这些中间件通常在核心身份和基础请求信息（IP、TraceID、账套）都已确定后再执行。它们的具体顺序也可能相互影响，需根据实际逻辑编排。

6.  **`LoggerMiddleware` (结束部分 - 如果适用)**:
    - **职责**: 记录请求处理完成时的状态、耗时等最终日志。
    - **读取 Context**: 可以读取到整个请求周期中设置的所有上下文信息。
    - **理由**: 确保能记录到最全面的信息。

**总结:** 务必仔细规划中间件的注册顺序，以确保信息写入和读取的依赖关系得到正确满足。不正确的顺序可能导致从 Context 中获取不到期望值或获取到的是陈旧/错误的值。

## 4. 验证和测试

与之前相同，强调代码审查、单元测试、集成测试和日志验证。确保错误处理符合预期。

## 5. 注意事项

与之前相同，强调 Iris Context 与标准 Go Context 的区别、`ctx.ResetRequest` 的重要性、依赖注入和错误处理。特别注意 `pkg/constant/constant.go` 中键的正确使用和 `pkg/util/context.go` 及 `pkg/errors/errors.go` 提供的功能。

通过以上步骤，并结合您项目中已有的 `pkg/constant/constant.go` 和 `pkg/errors/errors.go` 文件，可以更准确地将后端中间件的上下文信息管理统一到标准的 Go `context.Context`。
