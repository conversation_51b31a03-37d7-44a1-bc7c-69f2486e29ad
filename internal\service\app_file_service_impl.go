package service

import (
	"context"
	"errors"
	"io"
	"mime/multipart"

	"backend/internal/model/entity"
	"backend/internal/repository"
	"backend/pkg/config"
	"backend/pkg/logger"
	"backend/pkg/storage"

	apperrors "backend/pkg/errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"mime"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// FileService 定义文件处理服务接口
type FileService interface {
	// UploadFile 上传文件并创建元数据
	// 参数:
	//   - ctx: 上下文
	//   - uploaderID: 上传者用户ID (新增)
	//   - file: 文件内容读取器
	//   - header: 文件头信息 (包含文件名、大小等)
	//   - businessType: 关联的业务类型 (例如 "user_avatar", "excel_import")
	//   - businessID: 关联的业务ID (可选, 取决于业务类型)
	// 返回:
	//   - *entity.FileMetadata: 创建的文件元数据
	//   - error: 错误信息
	UploadFile(ctx context.Context, uploaderID uint, file io.Reader, header *multipart.FileHeader, businessType string, businessID *uint) (*entity.FileMetadata, error)

	// <<< 新增内部方法 >>>
	// UploadGeneratedFile 上传内部生成的文件
	UploadGeneratedFile(ctx context.Context, uploaderID uint, file io.Reader, filename string, fileSize int64, mimeType string, businessType string, businessID *uint) (*entity.FileMetadata, error)

	// DownloadFile 下载文件
	// 参数:
	//   - ctx: 上下文
	//   - fileID: 文件元数据ID
	// 返回:
	//   - *entity.FileMetadata: 文件元数据
	//   - io.ReadCloser: 文件内容读取器 (需要调用者关闭)
	//   - error: 错误信息
	DownloadFile(ctx context.Context, fileID uint) (*entity.FileMetadata, io.ReadCloser, error)

	// DeleteFile 删除文件和元数据
	// 参数:
	//   - ctx: 上下文
	//   - fileID: 文件元数据ID
	// 返回:
	//   - error: 错误信息
	DeleteFile(ctx context.Context, fileID uint) error

	// FindMetadataByID 根据ID查找文件元数据
	// 参数:
	//   - ctx: 上下文
	//   - fileID: 文件元数据ID
	// 返回:
	//   - *entity.FileMetadata: 文件元数据
	//   - error: 错误信息
	FindMetadataByID(ctx context.Context, fileID uint) (*entity.FileMetadata, error)

	// FindMetadataByBusiness 根据业务标识查找文件元数据列表
	// 参数:
	//   - ctx: 上下文
	//   - businessType: 关联的业务类型
	//   - businessID: 关联的业务ID
	// 返回:
	//   - []*entity.FileMetadata: 文件元数据列表
	//   - error: 错误信息
	FindMetadataByBusiness(ctx context.Context, businessType string, businessID uint) ([]*entity.FileMetadata, error)

	// GetFileURL 获取文件的可访问 URL (新增)
	GetFileURL(ctx context.Context, metadata *entity.FileMetadata) (string, error)
}

// FileServiceImpl 文件处理服务实现
type FileServiceImpl struct {
	config       *config.Configuration
	storage      storage.Storage
	metadataRepo repository.FileMetadataRepository
	repoManager  *repository.RepositoryManager // 用于事务
	logger       logger.Logger
}

// NewFileService 创建文件服务实例
func NewFileService(
	cfg *config.Configuration,
	storage storage.Storage,
	metadataRepo repository.FileMetadataRepository,
	repoManager *repository.RepositoryManager,
	logger logger.Logger,
) FileService {
	return &FileServiceImpl{
		config:       cfg,
		storage:      storage,
		metadataRepo: metadataRepo, // 直接传入具体的 repo
		repoManager:  repoManager,  // 传入 manager 以便获取事务性 repo
		logger:       logger,       // <<< 移除 .Named()
	}
}

// UploadFile 上传文件并创建元数据 (处理 HTTP 请求)
func (s *FileServiceImpl) UploadFile(ctx context.Context, uploaderID uint, file io.Reader, header *multipart.FileHeader, businessType string, businessID *uint) (*entity.FileMetadata, error) {
	// <<< 提取信息并调用内部方法 >>>
	filename := header.Filename
	fileSize := header.Size
	mimeType := mime.TypeByExtension(filepath.Ext(filename))

	// <<< 增加日志记录 >>>
	s.logger.Info(ctx, "UploadFile called, delegating to UploadGeneratedFile",
		logger.WithField("uploaderID", uploaderID),
		logger.WithField("originalFilename", filename),
		logger.WithField("size", fileSize),
		logger.WithField("mimeType", mimeType),
		logger.WithField("businessType", businessType),
	)

	return s.UploadGeneratedFile(ctx, uploaderID, file, filename, fileSize, mimeType, businessType, businessID)
}

// UploadGeneratedFile 上传内部生成的文件 (核心逻辑)
func (s *FileServiceImpl) UploadGeneratedFile(ctx context.Context, uploaderID uint, file io.Reader, filename string, fileSize int64, mimeType string, businessType string, businessID *uint) (*entity.FileMetadata, error) {
	// <<< 恢复之前的核心逻辑 >>>
	opName := "FileService.UploadGeneratedFile"
	// <<< 使用 logger 替代 fmt.Printf >>>
	s.logger.Debug(ctx, opName+": 开始处理",
		logger.WithField("uploaderID", uploaderID),
		logger.WithField("filename", filename),
		logger.WithField("size", fileSize),
		logger.WithField("mimeType", mimeType),
		logger.WithField("businessType", businessType),
	)

	// 1. 检查文件大小
	s.logger.Debug(ctx, opName+": 检查文件大小")
	if !s.checkFileSize(fileSize) {
		s.logger.Warn(ctx, opName+": 文件大小检查失败", logger.WithField("size", fileSize))
		return nil, apperrors.NewBusinessError(apperrors.CODE_UPLOAD_FILE_SIZE_EXCEEDED, "文件大小超过限制")
	}

	// 2. 检查文件类型
	s.logger.Debug(ctx, opName+": 检查文件类型")
	if !s.checkFileType(filename) {
		s.logger.Warn(ctx, opName+": 文件类型检查失败", logger.WithField("filename", filename))
		return nil, apperrors.NewBusinessError(apperrors.CODE_UPLOAD_FILE_TYPE_NOT_ALLOWED, "不允许的文件类型")
	}

	// 3. 生成唯一文件名/路径 (存储对象键)
	objectKey := s.generateStoragePath(businessType, filename)
	s.logger.Debug(ctx, opName+": 生成 objectKey", logger.WithField("objectKey", objectKey))

	// 4. 使用 s.storage.Upload() 保存文件
	s.logger.Debug(ctx, opName+": 调用 storage.Upload 保存文件")
	uploadOpts := storage.UploadOptions{
		ContentType: mimeType, // 直接使用传入的 mimeType
	}
	storageInfo, err := s.storage.Upload(ctx, objectKey, file, uploadOpts)
	if err != nil {
		s.logger.Error(ctx, opName+": storage.Upload 失败", logger.WithError(err), logger.WithField("objectKey", objectKey))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "文件保存失败").WithCause(err)
	}
	s.logger.Debug(ctx, opName+": storage.Upload 成功", logger.WithFields(logger.Fields{
		"objectKey":   objectKey,
		"storageSize": storageInfo.Size,
		"contentType": storageInfo.ContentType,
	}))

	// 5. 创建 entity.FileMetadata 实例
	metadata := &entity.FileMetadata{
		OriginalName: filename,
		StoragePath:  objectKey,               // 使用 StoragePath
		FileSize:     storageInfo.Size,        // 使用 FileSize
		FileType:     storageInfo.ContentType, // 使用 FileType
		StorageType:  s.config.Storage.Type,
		BusinessType: businessType,
		// UploaderUserID: uploaderID, // UploaderID 通常由 BaseEntity 的 CreatedBy 处理
	}
	// 设置 BaseEntity 中的创建者信息
	metadata.CreatedBy = uploaderID

	if businessID != nil {
		metadata.BusinessID = *businessID
	}
	s.logger.Debug(ctx, opName+": 创建 FileMetadata 实例", logger.WithField("metadata", metadata))

	// 6. 保存元数据 (不再需要内部事务)
	s.logger.Debug(ctx, opName+": 调用 metadataRepo.Create 保存元数据")
	err = s.metadataRepo.Create(ctx, metadata) // <<< 直接调用 Create
	if err != nil {
		s.logger.Error(ctx, opName+": metadataRepo.Create 失败", logger.WithError(err))
		// 尝试回滚已上传的文件
		s.logger.Warn(ctx, opName+": 元数据保存失败，尝试删除已上传的文件", logger.WithField("objectKey", objectKey))
		if deleteErr := s.storage.Delete(ctx, objectKey); deleteErr != nil {
			s.logger.Error(ctx, opName+": 删除已上传文件失败", logger.WithError(deleteErr), logger.WithField("objectKey", objectKey))
			// 记录删除失败，但仍然返回原始的元数据保存错误
		}
		// <<< 返回包装后的数据错误 >>>
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "保存文件元数据失败").WithCause(err)
	}
	s.logger.Debug(ctx, opName+": metadataRepo.Create 成功", logger.WithField("metadataID", metadata.ID))

	// 7. 如果保存文件成功但保存元数据失败，需要删除已保存的文件 (逻辑已移到错误处理中)
	/*
		if err != nil {
			// ... (旧的错误处理逻辑)
		}
	*/

	s.logger.Info(ctx, opName+": 文件上传和元数据保存成功", logger.WithField("metadataID", metadata.ID), logger.WithField("objectKey", objectKey))
	return metadata, nil
}

// DownloadFile 下载文件
func (s *FileServiceImpl) DownloadFile(ctx context.Context, fileID uint) (*entity.FileMetadata, io.ReadCloser, error) {
	opName := "FileService.DownloadFile"
	s.logger.Debug(ctx, opName+": 开始处理", logger.WithField("fileID", fileID))

	// 1. 使用 s.metadataRepo.FindByID() 查找元数据
	metadata, err := s.metadataRepo.FindByID(ctx, fileID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			s.logger.Warn(ctx, opName+": 文件元数据未找到", logger.WithField("fileID", fileID))
			return nil, nil, apperrors.NewDataError(apperrors.CODE_FILE_NOT_FOUND, "文件记录不存在")
		}
		s.logger.Error(ctx, opName+": 查找文件元数据失败", logger.WithError(err), logger.WithField("fileID", fileID))
		return nil, nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "获取文件元数据时数据库出错")
	}

	// 2. 使用 io.Pipe 和 s.storage.Download 适配接口
	pipeReader, pipeWriter := io.Pipe()

	go func() {
		defer pipeWriter.Close() // 确保写入端关闭
		// 调用 storage.Download 将数据写入 pipeWriter
		downloadOpts := storage.DownloadOptions{}
		downloadErr := s.storage.Download(ctx, metadata.StoragePath, pipeWriter, downloadOpts)
		if downloadErr != nil {
			s.logger.WithError(downloadErr).Error(ctx, "下载文件流到管道失败", "objectKey", metadata.StoragePath)
			// 将错误传递给读取端
			_ = pipeWriter.CloseWithError(apperrors.NewSystemError(apperrors.CODE_SYSTEM_ERROR, "下载文件失败").WithCause(downloadErr))
		}
	}()

	s.logger.Info(ctx, "准备下载文件", "fileID", fileID, "objectKey", metadata.StoragePath)
	// 返回 metadata 和 PipeReader
	return metadata, pipeReader, nil
}

// DeleteFile 删除文件和元数据
func (s *FileServiceImpl) DeleteFile(ctx context.Context, fileID uint) error {
	opName := "FileService.DeleteFile"
	s.logger.Debug(ctx, opName+": 开始处理", logger.WithField("fileID", fileID))

	// 1. 查找元数据以获取 objectKey
	s.logger.Debug(ctx, opName+": 查找元数据")
	metadata, findErr := s.metadataRepo.FindByID(ctx, fileID)
	if findErr != nil {
		if errors.Is(findErr, gorm.ErrRecordNotFound) {
			s.logger.Warn(ctx, opName+": 文件元数据未找到", logger.WithField("fileID", fileID))
			// 对于删除操作，找不到也认为成功 (幂等性)
			return nil
		}
		s.logger.Error(ctx, opName+": 查找元数据失败", logger.WithError(findErr), logger.WithField("fileID", fileID))
		return apperrors.WrapError(findErr, apperrors.CODE_DATA_QUERY_FAILED, "查找待删除文件元数据时数据库出错")
	}
	objectKey := metadata.StoragePath // 使用正确的字段名 StoragePath
	s.logger.Debug(ctx, opName+": 找到元数据", logger.WithField("objectKey", objectKey))

	// 2. 删除数据库中的元数据 (不再需要内部事务)
	s.logger.Debug(ctx, opName+": 调用 metadataRepo.Delete 删除元数据")
	err := s.metadataRepo.Delete(ctx, fileID)
	if err != nil {
		s.logger.Error(ctx, opName+": metadataRepo.Delete 失败", logger.WithError(err), logger.WithField("fileID", fileID))
		// 错误会由外部事务处理回滚
		return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "删除文件元数据失败")
	}
	s.logger.Debug(ctx, opName+": metadataRepo.Delete 成功")

	// 3. 如果元数据删除成功，尝试删除物理文件
	if objectKey == "" {
		s.logger.Warn(ctx, opName+": 元数据删除成功但 objectKey 为空，无法删除物理文件", logger.WithField("fileID", fileID))
		// 通常不应发生，但记录警告
	} else {
		s.logger.Debug(ctx, opName+": 调用 storage.Delete 删除物理文件")
		if deletePhysErr := s.storage.Delete(ctx, objectKey); deletePhysErr != nil {
			// 物理文件删除失败，记录警告，但数据库记录已清除
			s.logger.WithError(deletePhysErr).Warn(ctx, opName+": 删除物理文件失败，但元数据已删除", logger.WithField("fileID", fileID), logger.WithField("objectKey", objectKey))
			// 暂不返回错误，因为核心目标（删除元数据）已完成
		}
	}

	s.logger.Info(ctx, opName+": 文件删除操作完成", logger.WithField("fileID", fileID))
	return nil
}

// FindMetadataByID 根据ID查找文件元数据
func (s *FileServiceImpl) FindMetadataByID(ctx context.Context, fileID uint) (*entity.FileMetadata, error) {
	// 直接调用 s.metadataRepo.FindByID()
	metadata, err := s.metadataRepo.FindByID(ctx, fileID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_FILE_NOT_FOUND, "文件元数据未找到")
		}
		s.logger.WithError(err).Error(ctx, "根据ID查找文件元数据失败", "fileID", fileID)
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查找文件元数据失败")
	}
	return metadata, nil
}

// FindMetadataByBusiness 根据业务标识查找文件元数据列表
func (s *FileServiceImpl) FindMetadataByBusiness(ctx context.Context, businessType string, businessID uint) ([]*entity.FileMetadata, error) {
	// 调用 s.metadataRepo 的条件查询方法，添加 nil 作为 sortInfos 参数
	metadataList, err := s.metadataRepo.FindByBusiness(ctx, businessType, businessID, nil)
	if err != nil {
		// 注意：FindByCondition 通常在底层发生错误时才返回错误，找不到数据会返回空列表
		s.logger.WithError(err).Error(ctx, "根据业务标识查找文件元数据失败", "businessType", businessType, "businessID", businessID)
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查找文件元数据失败")
	}
	return metadataList, nil
}

// GetFileURL 获取文件的可访问 URL (新增)
func (s *FileServiceImpl) GetFileURL(ctx context.Context, metadata *entity.FileMetadata) (string, error) {
	if metadata == nil {
		return "", apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "文件元数据不能为空")
	}

	var baseURL string
	storageType := metadata.StorageType
	if storageType == "" {
		// 如果元数据没有存储类型，尝试从当前配置获取 (可能不太准确，但作为备选)
		storageType = s.config.Storage.Type
	}

	// 根据存储类型获取 BaseURL
	switch storageType {
	case storage.STORAGE_TYPE_LOCAL:
		baseURL = s.config.Storage.Local.BaseURL
	case storage.STORAGE_TYPE_S3:
		baseURL = s.config.Storage.S3.BaseURL
	// case storage.STORAGE_TYPE_OSS: // 假设有 OSS
	// 	baseURL = s.config.Storage.OSS.BaseURL
	default:
		// 对于未知或未配置 BaseURL 的类型，可以考虑返回下载 API URL
		// 或者返回错误，或者返回空字符串，取决于业务需求
		s.logger.Warn(ctx, "未知的存储类型或未配置BaseURL", logger.WithField("storageType", storageType))
	}

	// 如果获取到 BaseURL，则拼接 ObjectKey
	if baseURL != "" {
		// 确保 BaseURL 以 / 结尾
		if !strings.HasSuffix(baseURL, "/") {
			baseURL += "/"
		}
		// 确保 ObjectKey 不以 / 开头
		objectKey := strings.TrimPrefix(metadata.StoragePath, "/")
		return baseURL + objectKey, nil
	}

	// 如果没有 BaseURL，降级返回下载 API 的相对路径
	// 注意：这需要前端能够处理相对路径，或者在前端拼接基础 URL
	// 也可以直接返回空字符串或错误
	downloadURL := fmt.Sprintf("/api/v1/files/%d/download", metadata.ID)
	return downloadURL, nil
}

// --- 辅助函数 ---

func (s *FileServiceImpl) generateStoragePath(businessType, originalFilename string) string {
	// 生成一个基于业务类型、日期和UUID的路径
	now := time.Now()
	datePath := now.Format("2006/01/02") // YYYY/MM/DD
	uniqueID := uuid.NewString()
	ext := filepath.Ext(originalFilename)
	// 保留原始文件名（去除扩展名），并在前面加上UUID，避免潜在的字符问题
	baseName := strings.TrimSuffix(filepath.Base(originalFilename), ext)
	// 替换可能存在于 baseName 中的路径分隔符，虽然 Base 应该已经处理了，但以防万一
	safeBaseName := strings.ReplaceAll(strings.ReplaceAll(baseName, "/", "_"), "\\", "_")
	newFilename := fmt.Sprintf("%s_%s%s", uniqueID, safeBaseName, ext)

	// 路径: businessType/YYYY/MM/DD/uuid_safebasename.ext
	rawPath := filepath.Join(businessType, datePath, newFilename)
	// 确保使用正斜杠 "/" 作为路径分隔符
	return filepath.ToSlash(rawPath)
}

func (s *FileServiceImpl) checkFileType(filename string) bool {
	// 使用更新后的配置路径
	allowedTypes := s.config.Storage.AllowedTypes
	if len(allowedTypes) == 0 || (len(allowedTypes) == 1 && allowedTypes[0] == "*") {
		return true // 如果配置为空或为"*"，则允许所有类型
	}

	// 获取文件扩展名 (小写, 带点)
	ext := strings.ToLower(filepath.Ext(filename))
	if ext == "" {
		return false // 不允许没有扩展名的文件
	}

	// 根据扩展名获取 MIME 类型 (小写)
	fileMimeType := strings.ToLower(mime.TypeByExtension(ext))

	fmt.Printf("--- [DEBUG] checkFileType: ext=%s, mime=%s ---\n", ext, fileMimeType)

	for _, allowedType := range allowedTypes {
		currentAllowedType := strings.ToLower(strings.TrimSpace(allowedType))
		fmt.Printf("--- [DEBUG] checkFileType: Checking against allowed type: %s ---\n", currentAllowedType)

		if strings.HasPrefix(currentAllowedType, ".") {
			// 配置项是扩展名，直接比较扩展名
			if currentAllowedType == ext {
				fmt.Printf("--- [DEBUG] checkFileType: Extension match found: %s == %s ---\n", currentAllowedType, ext)
				return true
			}
		} else {
			// 配置项是 MIME 类型，比较 MIME 类型
			if fileMimeType != "" && currentAllowedType == fileMimeType {
				fmt.Printf("--- [DEBUG] checkFileType: MIME type match found: %s == %s ---\n", currentAllowedType, fileMimeType)
				return true
			}
		}
	}

	fmt.Printf("--- [DEBUG] checkFileType: No match found for %s ---\n", filename)
	return false // 遍历完所有允许类型都没有匹配
}

func (s *FileServiceImpl) checkFileSize(size int64) bool {
	// 使用更新后的配置路径和字段名，并转换单位
	maxFileSizeMB := s.config.Storage.MaxFileSizeMB
	if maxFileSizeMB <= 0 {
		return true // 如果配置为0或负数，表示不限制大小
	}
	maxSizeBytes := maxFileSizeMB * 1024 * 1024 // 从 MB 转换为 Bytes
	return size <= maxSizeBytes
}
