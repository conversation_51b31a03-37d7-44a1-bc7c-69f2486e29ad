<template>
    <div class="detailed-stats">
      <!-- 统计概览 -->
      <div class="stats-overview">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card success">
              <div class="stat-icon">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ overviewStats.successCount }}</div>
                <div class="stat-label">成功验证</div>
                <div class="stat-percentage">{{ successPercentage }}%</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="stat-card warning">
              <div class="stat-icon">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ overviewStats.approvalCount }}</div>
                <div class="stat-label">需要审批</div>
                <div class="stat-percentage">{{ approvalPercentage }}%</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="stat-card danger">
              <div class="stat-icon">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ overviewStats.failureCount }}</div>
                <div class="stat-label">验证失败</div>
                <div class="stat-percentage">{{ failurePercentage }}%</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
  
      <!-- 详细分析表格 -->
      <div class="detailed-analysis">
        <el-tabs v-model="activeTab" type="card">
          <!-- 按仓库统计 -->
          <el-tab-pane label="按仓库统计" name="warehouse">
            <el-table :data="warehouseStats" stripe>
              <el-table-column prop="warehouseName" label="仓库名称" />
              <el-table-column prop="totalValidations" label="总验证次数" align="center" />
              <el-table-column prop="successCount" label="成功次数" align="center" />
              <el-table-column prop="failureCount" label="失败次数" align="center" />
              <el-table-column prop="successRate" label="成功率" align="center">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.successRate"
                    :color="getProgressColor(row.successRate)"
                    :show-text="true"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="avgResponseTime" label="平均响应时间(ms)" align="center" />
              <el-table-column label="策略分布" align="center">
                <template #default="{ row }">
                  <div class="strategy-distribution">
                    <el-tag type="danger" size="small">严格: {{ row.strictCount }}</el-tag>
                    <el-tag type="warning" size="small">补录: {{ row.supplementCount }}</el-tag>
                    <el-tag type="success" size="small">完全: {{ row.fullCount }}</el-tag>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <!-- 按客户统计 -->
          <el-tab-pane label="按客户统计" name="client">
            <el-table :data="clientStats" stripe>
              <el-table-column prop="clientName" label="客户名称" />
              <el-table-column prop="totalValidations" label="总验证次数" align="center" />
              <el-table-column prop="successCount" label="成功次数" align="center" />
              <el-table-column prop="failureCount" label="失败次数" align="center" />
              <el-table-column prop="successRate" label="成功率" align="center">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.successRate"
                    :color="getProgressColor(row.successRate)"
                    :show-text="true"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="avgResponseTime" label="平均响应时间(ms)" align="center" />
              <el-table-column label="主要策略" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStrategyTagType(row.primaryStrategy)">
                    {{ getStrategyName(row.primaryStrategy) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <!-- 按用户统计 -->
          <el-tab-pane label="按用户统计" name="user">
            <el-table :data="userStats" stripe>
              <el-table-column prop="userName" label="用户名称" />
              <el-table-column prop="userRole" label="用户角色" />
              <el-table-column prop="totalValidations" label="总验证次数" align="center" />
              <el-table-column prop="successCount" label="成功次数" align="center" />
              <el-table-column prop="failureCount" label="失败次数" align="center" />
              <el-table-column prop="successRate" label="成功率" align="center">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.successRate"
                    :color="getProgressColor(row.successRate)"
                    :show-text="true"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="lastValidationTime" label="最近验证时间" align="center">
                <template #default="{ row }">
                  {{ formatDateTime(row.lastValidationTime) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <!-- 按时间段统计 -->
          <el-tab-pane label="按时间段统计" name="time">
            <el-table :data="timeStats" stripe>
              <el-table-column prop="timeRange" label="时间段" />
              <el-table-column prop="totalValidations" label="总验证次数" align="center" />
              <el-table-column prop="successCount" label="成功次数" align="center" />
              <el-table-column prop="failureCount" label="失败次数" align="center" />
              <el-table-column prop="successRate" label="成功率" align="center">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.successRate"
                    :color="getProgressColor(row.successRate)"
                    :show-text="true"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="avgResponseTime" label="平均响应时间(ms)" align="center" />
              <el-table-column label="活跃度" align="center">
                <template #default="{ row }">
                  <el-tag :type="getActivityTagType(row.activityLevel)">
                    {{ getActivityLabel(row.activityLevel) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <!-- 异常分析 -->
          <el-tab-pane label="异常分析" name="exception">
            <div class="exception-analysis">
              <el-alert
                title="异常验证分析"
                type="warning"
                :closable="false"
                style="margin-bottom: 20px;"
              >
                以下是系统检测到的异常验证情况，建议重点关注
              </el-alert>
              
              <el-table :data="exceptionStats" stripe>
                <el-table-column prop="exceptionType" label="异常类型" />
                <el-table-column prop="description" label="异常描述" />
                <el-table-column prop="count" label="发生次数" align="center" />
                <el-table-column prop="severity" label="严重程度" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getSeverityTagType(row.severity)">
                      {{ getSeverityLabel(row.severity) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="lastOccurrence" label="最近发生时间" align="center">
                  <template #default="{ row }">
                    {{ formatDateTime(row.lastOccurrence) }}
                  </template>
                </el-table-column>
                <el-table-column label="建议措施" align="center">
                  <template #default="{ row }">
                    <el-button size="small" @click="showSuggestion(row)">
                      查看建议
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
  
      <!-- 建议措施对话框 -->
      <el-dialog
        v-model="showSuggestionDialog"
        title="异常处理建议"
        width="600px"
      >
        <div class="suggestion-content" v-if="currentException">
          <h4>{{ currentException.exceptionType }}</h4>
          <p><strong>问题描述：</strong>{{ currentException.description }}</p>
          <p><strong>发生次数：</strong>{{ currentException.count }}</p>
          <p><strong>严重程度：</strong>{{ getSeverityLabel(currentException.severity) }}</p>
          
          <div class="suggestions">
            <h5>建议措施：</h5>
            <ul>
              <li v-for="suggestion in currentException.suggestions" :key="suggestion">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
        
        <template #footer>
          <el-button @click="showSuggestionDialog = false">关闭</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import {
    CircleCheckFilled,
    WarningFilled,
    CircleCloseFilled
  } from '@element-plus/icons-vue'
  import {
    getBlindReceivingValidationDetailedStats
  } from '@/api/wms/blindReceivingConfig'
  import { STRATEGY_OPTIONS } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    filters?: Record<string, any>
  }
  
  const props = withDefaults(defineProps<Props>(), {
    filters: () => ({})
  })
  
  // 响应式数据
  const activeTab = ref('warehouse')
  const showSuggestionDialog = ref(false)
  const currentException = ref(null)
  
  // 统计数据
  const overviewStats = ref({
    totalCount: 0,
    successCount: 0,
    approvalCount: 0,
    failureCount: 0
  })
  
  const warehouseStats = ref([])
  const clientStats = ref([])
  const userStats = ref([])
  const timeStats = ref([])
  const exceptionStats = ref([])
  
  // 计算属性
  const successPercentage = computed(() => {
    if (!overviewStats.value.totalCount) return 0
    return Math.round((overviewStats.value.successCount / overviewStats.value.totalCount) * 100)
  })
  
  const approvalPercentage = computed(() => {
    if (!overviewStats.value.totalCount) return 0
    return Math.round((overviewStats.value.approvalCount / overviewStats.value.totalCount) * 100)
  })
  
  const failurePercentage = computed(() => {
    if (!overviewStats.value.totalCount) return 0
    return Math.round((overviewStats.value.failureCount / overviewStats.value.totalCount) * 100)
  })
  
  // 工具函数
  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return '#67c23a'
    if (percentage >= 70) return '#e6a23c'
    return '#f56c6c'
  }
  
  const getStrategyName = (strategy: string) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: string) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getActivityTagType = (level: string) => {
    const typeMap = {
      'HIGH': 'success',
      'MEDIUM': 'warning',
      'LOW': 'info'
    }
    return typeMap[level] || 'info'
  }
  
  const getActivityLabel = (level: string) => {
    const labelMap = {
      'HIGH': '高',
      'MEDIUM': '中',
      'LOW': '低'
    }
    return labelMap[level] || level
  }
  
  const getSeverityTagType = (severity: string) => {
    const typeMap = {
      'CRITICAL': 'danger',
      'HIGH': 'warning',
      'MEDIUM': 'info',
      'LOW': 'success'
    }
    return typeMap[severity] || 'info'
  }
  
  const getSeverityLabel = (severity: string) => {
    const labelMap = {
      'CRITICAL': '严重',
      'HIGH': '高',
      'MEDIUM': '中',
      'LOW': '低'
    }
    return labelMap[severity] || severity
  }
  
  const formatDateTime = (dateStr: string) => {
    return new Date(dateStr).toLocaleString()
  }
  
  // 数据加载
  const loadDetailedStats = async () => {
    try {
      const result = await getBlindReceivingValidationDetailedStats(props.filters)
      
      overviewStats.value = result.overview
      warehouseStats.value = result.warehouseStats
      clientStats.value = result.clientStats
      userStats.value = result.userStats
      timeStats.value = result.timeStats
      exceptionStats.value = result.exceptionStats
      
    } catch (error) {
      console.error('加载详细统计失败:', error)
    }
  }
  
  // 事件处理
  const showSuggestion = (exception: any) => {
    currentException.value = exception
    showSuggestionDialog.value = true
  }
  
  // 初始化
  onMounted(() => {
    loadDetailedStats()
  })
  </script>
  
  <style scoped lang="scss">
  .detailed-stats {
    .stats-overview {
      margin-bottom: 24px;
      
      .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        &.success {
          background: linear-gradient(135deg, #f0f9ff 0%, #e6fffa 100%);
          border-left: 4px solid #67c23a;
        }
        
        &.warning {
          background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
          border-left: 4px solid #e6a23c;
        }
        
        &.danger {
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
          border-left: 4px solid #f56c6c;
        }
        
        .stat-icon {
          font-size: 32px;
          margin-right: 16px;
          
          .el-icon {
            &:first-child {
              color: inherit;
            }
          }
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-label {
            color: #606266;
            font-size: 14px;
            margin-bottom: 4px;
          }
          
          .stat-percentage {
            color: #909399;
            font-size: 12px;
          }
        }
        
        &.success .stat-icon {
          color: #67c23a;
        }
        
        &.warning .stat-icon {
          color: #e6a23c;
        }
        
        &.danger .stat-icon {
          color: #f56c6c;
        }
      }
    }
    
    .detailed-analysis {
      .strategy-distribution {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
      }
    }
    
    .exception-analysis {
      .suggestions {
        margin-top: 16px;
        
        h5 {
          margin: 0 0 8px 0;
          color: #303133;
        }
        
        ul {
          margin: 0;
          padding-left: 20px;
          
          li {
            margin-bottom: 4px;
            color: #606266;
            line-height: 1.5;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
  </style>