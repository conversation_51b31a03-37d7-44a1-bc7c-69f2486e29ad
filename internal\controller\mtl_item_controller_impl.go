package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"

	"github.com/kataras/iris/v12"
)

type MtlItemController interface {
	Post(ctx iris.Context)     // 创建物料
	PutBy(ctx iris.Context)    // 更新物料
	DeleteBy(ctx iris.Context) // 删除物料
	GetBy(ctx iris.Context)    // 获取物料详情
	Get(ctx iris.Context)      // 分页查询物料

	// 包装单位管理
	AddPackageUnit(ctx iris.Context)    // 添加包装单位
	UpdatePackageUnit(ctx iris.Context) // 更新包装单位
	RemovePackageUnit(ctx iris.Context) // 删除包装单位
	GetPackageUnits(ctx iris.Context)   // 获取包装单位列表
}

type mtlItemControllerImpl struct {
	BaseControllerImpl
	service service.MtlItemService
}

func NewMtlItemController(cm *ControllerManager) MtlItemController {
	svc := cm.GetServiceManager().GetMtlItemService()
	return &mtlItemControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		service:            svc,
	}
}

// Post 创建物料
func (c *mtlItemControllerImpl) Post(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	var req dto.MtlItemCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "CreateMtlItem")
		return
	}

	result, err := c.service.Create(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "CreateMtlItem")
		return
	}

	c.Success(ctx, result)
}

// PutBy 更新物料
func (c *mtlItemControllerImpl) PutBy(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的物料ID: "+err.Error()), "UpdateMtlItem")
		return
	}

	var req dto.MtlItemUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "UpdateMtlItem")
		return
	}

	// 确保ID一致
	req.ID = id

	result, err := c.service.Update(ctx.Request().Context(), id, &req)
	if err != nil {
		c.HandleError(ctx, err, "UpdateMtlItem")
		return
	}

	c.Success(ctx, result)
}

// DeleteBy 删除物料
func (c *mtlItemControllerImpl) DeleteBy(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的物料ID: "+err.Error()), "DeleteMtlItem")
		return
	}

	if err := c.service.Delete(ctx.Request().Context(), id); err != nil {
		c.HandleError(ctx, err, "DeleteMtlItem")
		return
	}

	c.Success(ctx, nil)
}

// GetBy 获取物料详情
func (c *mtlItemControllerImpl) GetBy(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的物料ID: "+err.Error()), "GetMtlItem")
		return
	}

	result, err := c.service.GetByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, "GetMtlItem")
		return
	}

	c.Success(ctx, result)
}

// Get 分页查询 (参考WmsLocation实现)
func (c *mtlItemControllerImpl) Get(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	var req dto.MtlItemQueryReq

	// 使用现有分页机制构建查询参数
	pageQuery := response.BuildPageQuery(ctx)
	req.PageQuery = *pageQuery

	// 读取其他查询参数
	if err := ctx.ReadQuery(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数: "+err.Error()), "GetMtlItemPage")
		return
	}

	pageResult, err := c.service.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "GetMtlItemPage")
		return
	}

	c.Success(ctx, pageResult)
}

// AddPackageUnit 添加包装单位
func (c *mtlItemControllerImpl) AddPackageUnit(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	itemId, err := ctx.Params().GetUint("itemId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的物料ID: "+err.Error()), "AddPackageUnit")
		return
	}

	var req dto.MtlItemPackageUnitCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "AddPackageUnit")
		return
	}

	result, err := c.service.AddPackageUnit(ctx.Request().Context(), itemId, &req)
	if err != nil {
		c.HandleError(ctx, err, "AddPackageUnit")
		return
	}

	c.Success(ctx, result)
}

// UpdatePackageUnit 更新包装单位
func (c *mtlItemControllerImpl) UpdatePackageUnit(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	itemId, err := ctx.Params().GetUint("itemId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的物料ID: "+err.Error()), "UpdatePackageUnit")
		return
	}

	unitId, err := ctx.Params().GetUint("unitId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的包装单位ID: "+err.Error()), "UpdatePackageUnit")
		return
	}

	var req dto.MtlItemPackageUnitUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的请求体: "+err.Error()), "UpdatePackageUnit")
		return
	}

	result, err := c.service.UpdatePackageUnit(ctx.Request().Context(), itemId, unitId, &req)
	if err != nil {
		c.HandleError(ctx, err, "UpdatePackageUnit")
		return
	}

	c.Success(ctx, result)
}

// RemovePackageUnit 删除包装单位
func (c *mtlItemControllerImpl) RemovePackageUnit(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	itemId, err := ctx.Params().GetUint("itemId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的物料ID: "+err.Error()), "RemovePackageUnit")
		return
	}

	unitId, err := ctx.Params().GetUint("unitId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的包装单位ID: "+err.Error()), "RemovePackageUnit")
		return
	}

	if err := c.service.DeletePackageUnit(ctx.Request().Context(), itemId, unitId); err != nil {
		c.HandleError(ctx, err, "RemovePackageUnit")
		return
	}

	c.Success(ctx, nil)
}

// GetPackageUnits 获取包装单位列表
func (c *mtlItemControllerImpl) GetPackageUnits(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "mtl_item")

	itemId, err := ctx.Params().GetUint("itemId")
	if err != nil {
		c.HandleError(ctx, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "无效的物料ID: "+err.Error()), "GetPackageUnits")
		return
	}

	result, err := c.service.GetPackageUnits(ctx.Request().Context(), itemId)
	if err != nil {
		c.HandleError(ctx, err, "GetPackageUnits")
		return
	}

	c.Success(ctx, result)
}
