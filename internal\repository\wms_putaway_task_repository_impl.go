package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsPutawayTaskRepository 定义上架任务仓库接口
type WmsPutawayTaskRepository interface {
	BaseRepository[entity.WmsPutawayTask, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsPutawayTaskQueryReq) (*response.PageResult, error)

	// 根据任务编号查找
	FindByTaskNo(ctx context.Context, taskNo string) (*entity.WmsPutawayTask, error)

	// 检查任务编号是否存在
	IsTaskNoExist(ctx context.Context, taskNo string, excludeID uint) (bool, error)

	// 获取待执行的任务
	GetPendingTasks(ctx context.Context, warehouseID uint) ([]*entity.WmsPutawayTask, error)

	// 分配任务给用户
	AssignToUser(ctx context.Context, taskID uint, userID uint) error

	// 获取用户的任务
	GetTasksByUser(ctx context.Context, userID uint) ([]*entity.WmsPutawayTask, error)

	// 获取用户的待执行任务
	GetPendingTasksByUser(ctx context.Context, userID uint) ([]*entity.WmsPutawayTask, error)

	// 根据收货记录ID查询任务
	FindByReceivingRecordID(ctx context.Context, receivingRecordID uint) ([]*entity.WmsPutawayTask, error)

	// 获取指定状态的任务
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsPutawayTask, error)

	// 根据优先级查询任务
	GetByPriority(ctx context.Context, priority int) ([]*entity.WmsPutawayTask, error)

	// 更新任务状态
	UpdateStatus(ctx context.Context, id uint, status string) error

	// 完成任务
	CompleteTask(ctx context.Context, id uint, completedAt time.Time) error

	// 批量分配任务
	BatchAssignToUser(ctx context.Context, taskIDs []uint, userID uint) error
}

// wmsPutawayTaskRepository 上架任务仓库实现
type wmsPutawayTaskRepository struct {
	BaseRepositoryImpl[entity.WmsPutawayTask, uint]
}

// NewWmsPutawayTaskRepository 创建上架任务仓库
func NewWmsPutawayTaskRepository(db *gorm.DB) WmsPutawayTaskRepository {
	return &wmsPutawayTaskRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsPutawayTask, uint]{
			db: db,
		},
	}
}

// GetPage 获取上架任务分页数据
func (r *wmsPutawayTaskRepository) GetPage(ctx context.Context, query *dto.WmsPutawayTaskQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.TaskNo != nil && *query.TaskNo != "" {
		conditions = append(conditions, NewLikeCondition("task_no", *query.TaskNo))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if len(query.Statuses) > 0 {
		conditions = append(conditions, NewInCondition("status", query.Statuses))
	}
	if query.AssignedToUserID != nil {
		conditions = append(conditions, NewEqualCondition("assigned_to_user_id", *query.AssignedToUserID))
	}
	if query.CreatedAtFrom != nil && query.CreatedAtTo != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *query.CreatedAtFrom, *query.CreatedAtTo))
	}
	if query.CompletedAtFrom != nil && query.CompletedAtTo != nil {
		conditions = append(conditions, NewBetweenCondition("completed_at", *query.CompletedAtFrom, *query.CompletedAtTo))
	}

	return r.FindByPage(ctx, &query.Pagination, conditions)
}

// FindByTaskNo 根据任务编号查找
func (r *wmsPutawayTaskRepository) FindByTaskNo(ctx context.Context, taskNo string) (*entity.WmsPutawayTask, error) {
	var task entity.WmsPutawayTask
	db := r.GetDB(ctx)

	// 预加载关联的明细
	result := db.Preload("TaskDetails").Where("task_no = ?", taskNo).First(&task)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "上架任务不存在").WithCause(result.Error)
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据任务编号查询失败: taskNo=%s", taskNo)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询上架任务失败").WithCause(result.Error)
	}

	return &task, nil
}

// IsTaskNoExist 检查任务编号是否存在
func (r *wmsPutawayTaskRepository) IsTaskNoExist(ctx context.Context, taskNo string, excludeID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("task_no", taskNo),
	}
	if excludeID > 0 {
		conditions = append(conditions, NewNotEqualCondition("id", excludeID))
	}
	return r.Exists(ctx, conditions)
}

// GetPendingTasks 获取待执行的任务
func (r *wmsPutawayTaskRepository) GetPendingTasks(ctx context.Context, warehouseID uint) ([]*entity.WmsPutawayTask, error) {
	var tasks []*entity.WmsPutawayTask
	db := r.GetDB(ctx)

	// 通过收货记录关联查询仓库的待执行任务
	result := db.Joins("JOIN wms_receiving_records rr ON rr.id = wms_putaway_tasks.receiving_record_id").
		Where("rr.warehouse_id = ?", warehouseID).
		Where("wms_putaway_tasks.status IN ?", []string{"PENDING", "ASSIGNED"}).
		Order("wms_putaway_tasks.priority ASC, wms_putaway_tasks.created_at ASC").
		Find(&tasks)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询待执行任务失败: warehouseID=%d", warehouseID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询待执行任务失败").WithCause(result.Error)
	}

	return tasks, nil
}

// AssignToUser 分配任务给用户
func (r *wmsPutawayTaskRepository) AssignToUser(ctx context.Context, taskID uint, userID uint) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPutawayTask{}).
		Where("id = ?", taskID).
		Where("status = ?", "PENDING").
		Updates(map[string]interface{}{
			"assigned_to_user_id": userID,
			"status":              "ASSIGNED",
		})

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("分配任务失败: taskID=%d, userID=%d", taskID, userID)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "分配任务失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在或已被分配")
	}

	return nil
}

// GetTasksByUser 获取用户的任务
func (r *wmsPutawayTaskRepository) GetTasksByUser(ctx context.Context, userID uint) ([]*entity.WmsPutawayTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("assigned_to_user_id", userID),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetPendingTasksByUser 获取用户的待执行任务
func (r *wmsPutawayTaskRepository) GetPendingTasksByUser(ctx context.Context, userID uint) ([]*entity.WmsPutawayTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("assigned_to_user_id", userID),
		NewInCondition("status", []string{"ASSIGNED", "IN_PROGRESS"}),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByReceivingRecordID 根据收货记录ID查询任务
func (r *wmsPutawayTaskRepository) FindByReceivingRecordID(ctx context.Context, receivingRecordID uint) ([]*entity.WmsPutawayTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("receiving_record_id", receivingRecordID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByStatus 获取指定状态的任务
func (r *wmsPutawayTaskRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsPutawayTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "priority", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByPriority 根据优先级查询任务
func (r *wmsPutawayTaskRepository) GetByPriority(ctx context.Context, priority int) ([]*entity.WmsPutawayTask, error) {
	conditions := []QueryCondition{
		NewEqualCondition("priority", priority),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// UpdateStatus 更新任务状态
func (r *wmsPutawayTaskRepository) UpdateStatus(ctx context.Context, id uint, status string) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPutawayTask{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新任务状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新任务状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在")
	}

	return nil
}

// CompleteTask 完成任务
func (r *wmsPutawayTaskRepository) CompleteTask(ctx context.Context, id uint, completedAt time.Time) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPutawayTask{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       "COMPLETED",
			"completed_at": completedAt,
		})

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("完成任务失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "完成任务失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "任务不存在")
	}

	return nil
}

// BatchAssignToUser 批量分配任务
func (r *wmsPutawayTaskRepository) BatchAssignToUser(ctx context.Context, taskIDs []uint, userID uint) error {
	if len(taskIDs) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPutawayTask{}).
		Where("id IN ?", taskIDs).
		Where("status = ?", "PENDING").
		Updates(map[string]interface{}{
			"assigned_to_user_id": userID,
			"status":              "ASSIGNED",
		})

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量分配任务失败: taskIDs=%v, userID=%d", taskIDs, userID)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量分配任务失败").WithCause(result.Error)
	}

	return nil
}
