package dto

import (
	"backend/internal/model/entity"
	"backend/pkg/response"
	"time"
)

// WmsCycleCountPlanCreateReq 盘点计划创建请求
type WmsCycleCountPlanCreateReq struct {
	AccountBookID     uint                         `json:"accountBookId" binding:"required" comment:"账套ID"`
	PlanName          string                       `json:"planName" binding:"required" comment:"计划名称"`
	CountType         entity.WmsCycleCountType     `json:"countType" binding:"required" comment:"盘点类型"`
	CountStrategy     entity.WmsCycleCountStrategy `json:"countStrategy" binding:"required" comment:"盘点策略"`
	WarehouseID       uint                         `json:"warehouseId" binding:"required" comment:"仓库ID"`
	PlannedDate       time.Time                    `json:"plannedDate" binding:"required" comment:"计划日期"`
	ResponsibleUserID uint                         `json:"responsibleUserId" binding:"required" comment:"负责人ID"`
	Description       *string                      `json:"description" comment:"描述"`
}

// WmsCycleCountPlanUpdateReq 盘点计划更新请求
type WmsCycleCountPlanUpdateReq struct {
	PlanName          *string                       `json:"planName" comment:"计划名称"`
	CountType         *entity.WmsCycleCountType     `json:"countType" comment:"盘点类型"`
	CountStrategy     *entity.WmsCycleCountStrategy `json:"countStrategy" comment:"盘点策略"`
	PlannedDate       *time.Time                    `json:"plannedDate" comment:"计划日期"`
	ResponsibleUserID *uint                         `json:"responsibleUserId" comment:"负责人ID"`
	Description       *string                       `json:"description" comment:"描述"`
}

// WmsCycleCountPlanQueryReq 盘点计划查询请求
type WmsCycleCountPlanQueryReq struct {
	response.PageQuery
	PlanNo            *string                         `json:"planNo" comment:"计划编号"`
	PlanName          *string                         `json:"planName" comment:"计划名称"`
	CountType         *entity.WmsCycleCountType       `json:"countType" comment:"盘点类型"`
	CountStrategy     *entity.WmsCycleCountStrategy   `json:"countStrategy" comment:"盘点策略"`
	WarehouseID       *uint                           `json:"warehouseId" comment:"仓库ID"`
	Status            *entity.WmsCycleCountPlanStatus `json:"status" comment:"计划状态"`
	ResponsibleUserID *uint                           `json:"responsibleUserId" comment:"负责人ID"`
	CreatedBy         *uint                           `json:"createdBy" comment:"创建人ID"`
	PlannedStart      *time.Time                      `json:"plannedStart" comment:"计划开始日期"`
	PlannedEnd        *time.Time                      `json:"plannedEnd" comment:"计划结束日期"`
	CreatedStart      *time.Time                      `json:"createdStart" comment:"创建开始时间"`
	CreatedEnd        *time.Time                      `json:"createdEnd" comment:"创建结束时间"`
}

// WmsCycleCountPlanApprovalReq 盘点计划审批请求
type WmsCycleCountPlanApprovalReq struct {
	ApprovedBy uint   `json:"approvedBy" binding:"required" comment:"审批人ID"`
	Remark     string `json:"remark" comment:"审批备注"`
}

// WmsCycleCountPlanRejectReq 盘点计划拒绝请求
type WmsCycleCountPlanRejectReq struct {
	ApprovedBy uint   `json:"approvedBy" binding:"required" comment:"审批人ID"`
	Reason     string `json:"reason" binding:"required" comment:"拒绝原因"`
}

// WmsCycleCountTaskCreateReq 盘点任务创建请求
type WmsCycleCountTaskCreateReq struct {
	PlanID        uint  `json:"planId" binding:"required" comment:"计划ID"`
	LocationID    uint  `json:"locationId" binding:"required" comment:"库位ID"`
	SkuID         *uint `json:"skuId" comment:"物料ID"`
	CounterUserID *uint `json:"counterUserId" comment:"盘点员ID"`
	CreatorID     uint  `json:"creatorId" binding:"required" comment:"创建人ID"`
}

// WmsCycleCountTaskUpdateReq 盘点任务更新请求
type WmsCycleCountTaskUpdateReq struct {
	CounterUserID *uint `json:"counterUserId" comment:"盘点员ID"`
}

// WmsCycleCountTaskQueryReq 盘点任务查询请求
type WmsCycleCountTaskQueryReq struct {
	response.PageQuery
	TaskNo        *string                         `json:"taskNo" comment:"任务编号"`
	PlanID        *uint                           `json:"planId" comment:"计划ID"`
	LocationID    *uint                           `json:"locationId" comment:"库位ID"`
	ItemID        *uint                           `json:"itemId" comment:"物料ID"`
	ItemSku       *string                         `json:"itemSku" comment:"物料SKU"`
	BatchNo       *string                         `json:"batchNo" comment:"批次号"`
	Status        *entity.WmsCycleCountTaskStatus `json:"status" comment:"任务状态"`
	CounterUserID *uint                           `json:"counterUserId" comment:"盘点员ID"`
	HasVariance   *bool                           `json:"hasVariance" comment:"是否有差异"`
	CreatedStart  *time.Time                      `json:"createdStart" comment:"创建开始时间"`
	CreatedEnd    *time.Time                      `json:"createdEnd" comment:"创建结束时间"`
}

// WmsCycleCountSubmitReq 盘点结果提交请求
type WmsCycleCountSubmitReq struct {
	TaskID          uint    `json:"taskId" binding:"required" comment:"任务ID"`
	CountedQuantity float64 `json:"countedQuantity" binding:"required" comment:"盘点数量"`
	CounterUserID   uint    `json:"counterUserId" binding:"required" comment:"盘点员ID"`
	CountRemark     *string `json:"countRemark" comment:"盘点备注"`
}

// WmsCycleCountTaskStartReq 盘点任务开始请求
type WmsCycleCountTaskStartReq struct {
	CounterUserID uint `json:"counterUserId" binding:"required" comment:"盘点员ID"`
}

// WmsCycleCountTaskAssignReq 盘点任务分配请求
type WmsCycleCountTaskAssignReq struct {
	TaskIDs       []uint `json:"taskIds" binding:"required" comment:"任务ID列表"`
	CounterUserID uint   `json:"counterUserId" binding:"required" comment:"盘点员ID"`
}

// WmsCycleCountTaskCompleteReq 盘点任务完成请求
type WmsCycleCountTaskCompleteReq struct {
	ActualQuantity float64 `json:"actualQuantity" binding:"required" comment:"实际盘点数量"`
	Remark         *string `json:"remark" comment:"备注"`
}

// WmsCycleCountVarianceReq 盘点差异处理请求
type WmsCycleCountVarianceReq struct {
	VarianceReason *string `json:"varianceReason" comment:"差异原因"`
	Remark         *string `json:"remark" comment:"备注"`
}

// WmsCycleCountTaskBatchCreateReq 批量创建盘点任务请求
type WmsCycleCountTaskBatchCreateReq struct {
	Tasks []WmsCycleCountTaskCreateReq `json:"tasks" binding:"required" comment:"任务列表"`
}

// WmsCycleCountVarianceConfirmReq 盘点差异确认请求
type WmsCycleCountVarianceConfirmReq struct {
	Reason string `json:"reason" binding:"required" comment:"确认原因"`
}

// WmsCycleCountVarianceRejectReq 盘点差异拒绝请求
type WmsCycleCountVarianceRejectReq struct {
	Reason string `json:"reason" binding:"required" comment:"拒绝原因"`
}

// WmsCycleCountCreateAdjustmentReq 根据差异创建调整请求
type WmsCycleCountCreateAdjustmentReq struct {
	OperatorID uint `json:"operatorId" binding:"required" comment:"操作员ID"`
}

// WmsCycleCountSummaryReq 盘点汇总请求
type WmsCycleCountSummaryReq struct {
	WarehouseID *uint      `json:"warehouseId" comment:"仓库ID"`
	DateStart   *time.Time `json:"dateStart" comment:"开始日期"`
	DateEnd     *time.Time `json:"dateEnd" comment:"结束日期"`
	GroupBy     *string    `json:"groupBy" comment:"分组方式: day/week/month"`
}

// WmsCycleCountExportReq 盘点导出请求
type WmsCycleCountExportReq struct {
	PlanID       uint     `json:"planId" binding:"required" comment:"计划ID"`
	ExportType   string   `json:"exportType" binding:"required" comment:"导出类型: plan/task/variance"`
	ExportFields []string `json:"exportFields" comment:"导出字段"`
	ExportFormat string   `json:"exportFormat" comment:"导出格式: excel/csv"`
}

// WmsCycleCountImportReq 盘点导入请求
type WmsCycleCountImportReq struct {
	PlanID   uint   `json:"planId" binding:"required" comment:"计划ID"`
	FileData []byte `json:"fileData" binding:"required" comment:"文件数据"`
	FileName string `json:"fileName" binding:"required" comment:"文件名"`
}

// GetDefaultExportFields 获取默认导出字段
func (req *WmsCycleCountExportReq) GetDefaultExportFields() []string {
	if len(req.ExportFields) > 0 {
		return req.ExportFields
	}

	switch req.ExportType {
	case "plan":
		return []string{
			"planNo",
			"planName",
			"countType",
			"countStrategy",
			"warehouseName",
			"status",
			"responsibleUserName",
			"plannedDate",
			"createdAt",
		}
	case "task":
		return []string{
			"taskNo",
			"locationCode",
			"itemSku",
			"itemName",
			"batchNo",
			"systemQuantity",
			"countedQuantity",
			"varianceQuantity",
			"variancePercentage",
			"status",
			"counterUserName",
		}
	case "variance":
		return []string{
			"taskNo",
			"locationCode",
			"itemSku",
			"itemName",
			"batchNo",
			"systemQuantity",
			"countedQuantity",
			"varianceQuantity",
			"variancePercentage",
			"varianceReason",
		}
	default:
		return []string{}
	}
}

// GetExportFormat 获取导出格式
func (req *WmsCycleCountExportReq) GetExportFormat() string {
	if req.ExportFormat == "" {
		return "excel"
	}
	return req.ExportFormat
}
