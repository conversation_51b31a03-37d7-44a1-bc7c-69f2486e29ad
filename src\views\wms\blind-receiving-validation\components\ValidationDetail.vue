<template>
    <div class="validation-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="验证时间">
            {{ formatDateTime(record.createdAt) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="验证用户">
            {{ record.userName || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="验证结果">
            <el-tag
              :type="record.isAllowed ? 'success' : 'danger'"
              effect="plain"
            >
              {{ record.isAllowed ? '允许' : '禁止' }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="验证策略">
            <el-tag
              :type="getStrategyTagType(record.strategy)"
              effect="plain"
            >
              {{ getStrategyName(record.strategy) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="配置层级">
            <el-tag
              :type="getConfigLevelTagType(record.configLevel)"
              effect="plain"
            >
              {{ getConfigLevelName(record.configLevel) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="响应时间">
            {{ record.responseTime || 0 }}ms
          </el-descriptions-item>
        </el-descriptions>
      </div>
  
      <!-- 验证参数 -->
      <div class="detail-section">
        <h4 class="section-title">验证参数</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="仓库">
            {{ record.warehouseName || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="客户">
            {{ record.clientName || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="商品">
            {{ record.itemName || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="请求数量">
            {{ formatNumber(record.requestQuantity || 0) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="来源类型">
            {{ record.sourceType || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="来源ID">
            {{ record.sourceId || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
  
      <!-- 验证结果详情 -->
      <div class="detail-section">
        <h4 class="section-title">验证结果详情</h4>
        <el-card shadow="never" class="result-card">
          <div class="result-message">
            <div class="message-header">
              <el-icon class="message-icon" :class="getMessageIconClass()">
                <component :is="getMessageIcon()" />
              </el-icon>
              <span class="message-title">{{ getResultTitle() }}</span>
            </div>
            <div class="message-content">
              {{ record.validationMessage }}
            </div>
          </div>
          
          <div class="result-details" v-if="hasResultDetails()">
            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="是否需要审批" v-if="record.requiresApproval !== undefined">
                <el-tag :type="record.requiresApproval ? 'warning' : 'success'" size="small">
                  {{ record.requiresApproval ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              
              <el-descriptions-item label="最大数量限制" v-if="record.maxQuantityLimit">
                {{ formatNumber(record.maxQuantityLimit) }}
              </el-descriptions-item>
              
              <el-descriptions-item label="补录截止时间" v-if="record.supplementDeadline">
                {{ formatDateTime(record.supplementDeadline) }}
              </el-descriptions-item>
              
              <el-descriptions-item label="审批角色" v-if="record.approvalUserRoles?.length">
                <div class="role-tags">
                  <el-tag
                    v-for="role in record.approvalUserRoles"
                    :key="role"
                    size="small"
                    effect="plain"
                  >
                    {{ role }}
                  </el-tag>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </div>
  
      <!-- 流程状态 -->
      <div class="detail-section" v-if="hasProcessInfo()">
        <h4 class="section-title">流程状态</h4>
        <el-card shadow="never" class="process-card">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="审批状态" v-if="record.approvalStatus">
              <el-tag
                :type="getApprovalStatusTagType(record.approvalStatus)"
                effect="plain"
              >
                {{ getApprovalStatusName(record.approvalStatus) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="补录状态" v-if="record.supplementStatus">
              <el-tag
                :type="getSupplementStatusTagType(record.supplementStatus)"
                effect="plain"
              >
                {{ getSupplementStatusName(record.supplementStatus) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="处理时间" v-if="record.processedAt">
              {{ formatDateTime(record.processedAt) }}
            </el-descriptions-item>
            
            <el-descriptions-item label="处理人" v-if="record.processedByName">
              {{ record.processedByName }}
            </el-descriptions-item>
            
            <el-descriptions-item label="处理备注" v-if="record.processNote">
              <div class="process-note">{{ record.processNote }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
  
      <!-- 关联记录 -->
      <div class="detail-section" v-if="hasRelatedRecords()">
        <h4 class="section-title">关联记录</h4>
        <el-card shadow="never" class="related-card">
          <div class="related-item" v-if="record.relatedReceivingRecordId">
            <div class="related-label">收货记录:</div>
            <el-link
              type="primary"
              @click="goToReceivingRecord(record.relatedReceivingRecordId)"
            >
              #{{ record.relatedReceivingRecordId }}
            </el-link>
          </div>
          
          <div class="related-item" v-if="record.relatedInboundNotificationId">
            <div class="related-label">入库通知单:</div>
            <el-link
              type="primary"
              @click="goToInboundNotification(record.relatedInboundNotificationId)"
            >
              #{{ record.relatedInboundNotificationId }}
            </el-link>
          </div>
          
          <div class="related-item" v-if="record.relatedApprovalRecordId">
            <div class="related-label">审批记录:</div>
            <el-link
              type="primary"
              @click="goToApprovalRecord(record.relatedApprovalRecordId)"
            >
              #{{ record.relatedApprovalRecordId }}
            </el-link>
          </div>
        </el-card>
      </div>
  
      <!-- 技术信息 -->
      <div class="detail-section" v-if="showTechnicalInfo">
        <h4 class="section-title">技术信息</h4>
        <el-card shadow="never" class="technical-card">
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="记录ID">
              {{ record.id }}
            </el-descriptions-item>
            
            <el-descriptions-item label="配置ID">
              {{ record.configId }}
            </el-descriptions-item>
            
            <el-descriptions-item label="IP地址">
              {{ record.ipAddress || '-' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="用户代理">
              <div class="user-agent">{{ record.userAgent || '-' }}</div>
            </el-descriptions-item>
            
            <el-descriptions-item label="请求追踪ID">
              {{ record.traceId || '-' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(record.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
  
      <!-- 操作按钮 -->
      <div class="detail-actions">
        <el-button
          type="primary"
          :icon="Refresh"
          @click="handleRetry"
          v-if="canRetry()"
        >
          重新验证
        </el-button>
        
        <el-button
          type="success"
          :icon="View"
          @click="handleViewConfig"
          v-if="hasPermission('wms:blind-config:view')"
        >
          查看配置
        </el-button>
        
        <el-button
          type="info"
          :icon="Download"
          @click="handleDownloadDetail"
        >
          下载详情
        </el-button>
        
        <el-button
          type="danger"
          :icon="Delete"
          @click="handleDelete"
          v-if="hasPermission('wms:blind-validation:delete')"
        >
          删除记录
        </el-button>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Refresh,
    View,
    Download,
    Delete,
    CircleCheckFilled,
    CircleCloseFilled,
    WarningFilled
  } from '@element-plus/icons-vue'
  import { usePermission } from '@/hooks/usePermission'
  import type {
    BlindReceivingValidationVO,
    BlindReceivingStrategy,
    ConfigLevel,
    ApprovalStatus,
    SupplementStatus
  } from '@/types/wms/blindReceivingConfig'
  import {
    CONFIG_LEVEL_OPTIONS,
    STRATEGY_OPTIONS,
    APPROVAL_STATUS_OPTIONS,
    SUPPLEMENT_STATUS_OPTIONS
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    record: BlindReceivingValidationVO
    showTechnicalInfo?: boolean
  }
  
  const props = withDefaults(defineProps<Props>(), {
    showTechnicalInfo: false
  })
  
  // Emits
  const emit = defineEmits<{
    retry: []
    'view-config': []
    delete: []
  }>()
  
  // 路由和权限
  const router = useRouter()
  const { hasPermission } = usePermission()
  
  // 工具函数
  const getStrategyName = (strategy: BlindReceivingStrategy) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: BlindReceivingStrategy) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getConfigLevelName = (level: ConfigLevel) => {
    return CONFIG_LEVEL_OPTIONS.find(item => item.value === level)?.label || level
  }
  
  const getConfigLevelTagType = (level: ConfigLevel) => {
    const typeMap = {
      'SYSTEM': 'info',
      'WAREHOUSE': 'warning',
      'CLIENT': 'success',
      'USER': 'primary'
    }
    return typeMap[level] || 'info'
  }
  
  const getApprovalStatusName = (status: ApprovalStatus) => {
    return APPROVAL_STATUS_OPTIONS.find(item => item.value === status)?.label || status
  }
  
  const getApprovalStatusTagType = (status: ApprovalStatus) => {
    return APPROVAL_STATUS_OPTIONS.find(item => item.value === status)?.color || 'info'
  }
  
  const getSupplementStatusName = (status: SupplementStatus) => {
    return SUPPLEMENT_STATUS_OPTIONS.find(item => item.value === status)?.label || status
  }
  
  const getSupplementStatusTagType = (status: SupplementStatus) => {
    return SUPPLEMENT_STATUS_OPTIONS.find(item => item.value === status)?.color || 'info'
  }
  
  const formatNumber = (num: number) => {
    return num.toLocaleString()
  }
  
  const formatDateTime = (dateStr: string) => {
    return new Date(dateStr).toLocaleString()
  }
  
  const getResultTitle = () => {
    if (props.record.isAllowed) {
      return props.record.requiresApproval ? '允许（需审批）' : '允许'
    }
    return '禁止'
  }
  
  const getMessageIcon = () => {
    if (props.record.isAllowed) {
      return props.record.requiresApproval ? WarningFilled : CircleCheckFilled
    }
    return CircleCloseFilled
  }
  
  const getMessageIconClass = () => {
    if (props.record.isAllowed) {
      return props.record.requiresApproval ? 'icon-warning' : 'icon-success'
    }
    return 'icon-danger'
  }
  
  const hasResultDetails = () => {
    return props.record.requiresApproval !== undefined ||
           props.record.maxQuantityLimit ||
           props.record.supplementDeadline ||
           props.record.approvalUserRoles?.length
  }
  
  const hasProcessInfo = () => {
    return props.record.approvalStatus ||
           props.record.supplementStatus ||
           props.record.processedAt ||
           props.record.processedByName ||
           props.record.processNote
  }
  
  const hasRelatedRecords = () => {
    return props.record.relatedReceivingRecordId ||
           props.record.relatedInboundNotificationId ||
           props.record.relatedApprovalRecordId
  }
  
  const canRetry = () => {
    return !props.record.isAllowed
  }
  
  // 事件处理
  const handleRetry = () => {
    emit('retry')
  }
  
  const handleViewConfig = () => {
    emit('view-config')
  }
  
  const handleDownloadDetail = () => {
    const content = JSON.stringify(props.record, null, 2)
    const blob = new Blob([content], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `验证详情_${props.record.id}_${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    
    URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  }
  
  const handleDelete = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要删除这条验证记录吗？删除后将无法恢复。',
        '确认删除',
        {
          type: 'warning',
          confirmButtonText: '删除',
          cancelButtonText: '取消'
        }
      )
      
      emit('delete')
    } catch (error) {
      // 用户取消
    }
  }
  
  // 导航方法
  const goToReceivingRecord = (id: number) => {
    router.push(`/wms/receiving-record/detail/${id}`)
  }
  
  const goToInboundNotification = (id: number) => {
    router.push(`/wms/inbound-notification/detail/${id}`)
  }
  
  const goToApprovalRecord = (id: number) => {
    router.push(`/wms/blind-receiving-validation/approval/detail/${id}`)
  }
  </script>
  
  <style scoped lang="scss">
  .validation-detail {
    .detail-section {
      margin-bottom: 24px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-title {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }
    }
    
    .result-card,
    .process-card,
    .related-card,
    .technical-card {
      .result-message {
        .message-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          
          .message-icon {
            font-size: 20px;
            
            &.icon-success {
              color: #67c23a;
            }
            
            &.icon-warning {
              color: #e6a23c;
            }
            
            &.icon-danger {
              color: #f56c6c;
            }
          }
          
          .message-title {
            font-weight: 600;
            color: #303133;
          }
        }
        
        .message-content {
          padding: 12px;
          background-color: #f8f9fa;
          border-radius: 6px;
          color: #606266;
          line-height: 1.5;
        }
      }
      
      .result-details {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        
        .role-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
      }
    }
    
    .related-card {
      .related-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .related-label {
          color: #909399;
          min-width: 80px;
        }
      }
    }
    
    .technical-card {
      .user-agent {
        word-break: break-all;
        color: #606266;
        font-size: 13px;
      }
      
      .process-note {
        max-width: 300px;
        word-break: break-word;
        line-height: 1.4;
      }
    }
    
    .detail-actions {
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #ebeef5;
      display: flex;
      gap: 12px;
      justify-content: center;
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .validation-detail {
      .detail-actions {
        flex-wrap: wrap;
        justify-content: stretch;
        
        .el-button {
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
  </style>