package response

import (
	"math"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"

	"backend/pkg/constant"
)

// SortInfo 存储单个字段的排序信息
type SortInfo struct {
	Field string `json:"field"`
	Order string `json:"order"` // "asc" or "desc"
}

// PageQuery 分页查询参数
type PageQuery struct {
	PageNum  int        `json:"pageNum"`  // 页码（从1开始）
	PageSize int        `json:"pageSize"` // 每页大小
	Total    int64      `json:"total"`    // 总记录数
	Pages    int        `json:"pages"`    // 总页数
	Sort     []SortInfo `json:"sort"`     // 排序规则列表
}

// PageResult 分页结果
type PageResult struct {
	List     interface{} `json:"list"`           // 数据列表
	PageNum  int         `json:"pageNum"`        // 当前页码
	PageSize int         `json:"pageSize"`       // 每页大小
	Total    int64       `json:"total"`          // 总记录数
	Pages    int         `json:"pages"`          // 总页数
	Sort     []SortInfo  `json:"sort,omitempty"` // 用于生成此结果的排序规则
}

// BuildPageQuery 从请求上下文构建分页查询参数
// 参数:
//   - ctx: Iris上下文
//
// 返回:
//   - *PageQuery: 分页查询参数
func BuildPageQuery(ctx iris.Context) *PageQuery {
	// 获取页码参数
	pageNum, err := strconv.Atoi(ctx.URLParamDefault("pageNum", strconv.Itoa(constant.DEFAULT_PAGE_NUM)))
	if err != nil || pageNum < 1 {
		pageNum = constant.DEFAULT_PAGE_NUM
	}

	// 获取每页大小参数
	pageSize, err := strconv.Atoi(ctx.URLParamDefault("pageSize", strconv.Itoa(constant.DEFAULT_PAGE_SIZE)))
	if err != nil || pageSize < 1 {
		pageSize = constant.DEFAULT_PAGE_SIZE
	}
	if pageSize > constant.MAX_PAGE_SIZE {
		pageSize = constant.MAX_PAGE_SIZE
	}

	// 解析排序参数
	sortParam := ctx.URLParamTrim("sort")
	var sortInfos []SortInfo

	if sortParam == "" {
		// 如果 sort 参数为空，使用默认排序
		sortInfos = append(sortInfos, SortInfo{
			Field: constant.DEFAULT_SORT_FIELD,
			Order: constant.DEFAULT_SORT_ORDER,
		})
	} else {
		// 解析逗号分隔的排序规则
		sortPairs := strings.Split(sortParam, ",")
		for _, pair := range sortPairs {
			parts := strings.SplitN(strings.TrimSpace(pair), ":", 2)
			field := strings.TrimSpace(parts[0])
			if field == "" {
				continue // 跳过无效的空字段
			}

			order := constant.DEFAULT_SORT_ORDER // 默认排序方向
			if len(parts) == 2 {
				parsedOrder := strings.ToLower(strings.TrimSpace(parts[1]))
				if parsedOrder == constant.SORT_ASC || parsedOrder == constant.SORT_DESC {
					order = parsedOrder
				}
			}

			sortInfos = append(sortInfos, SortInfo{
				Field: field,
				Order: order,
			})
		}
		// 如果解析后没有任何有效的排序规则，则应用默认排序
		if len(sortInfos) == 0 {
			sortInfos = append(sortInfos, SortInfo{
				Field: constant.DEFAULT_SORT_FIELD,
				Order: constant.DEFAULT_SORT_ORDER,
			})
		}
	}

	return &PageQuery{
		PageNum:  pageNum,
		PageSize: pageSize,
		Sort:     sortInfos,
	}
}

// CalculatePages 计算总页数
// 参数:
//   - total: 总记录数
//   - pageSize: 每页大小
//
// 返回:
//   - int: 总页数
func CalculatePages(total int64, pageSize int) int {
	if pageSize <= 0 {
		pageSize = constant.DEFAULT_PAGE_SIZE
	}
	return int(math.Ceil(float64(total) / float64(pageSize)))
}

// SetTotal 设置总记录数并计算总页数
// 参数:
//   - total: 总记录数
//
// 返回:
//   - *PageQuery: 当前分页查询参数（链式调用）
func (q *PageQuery) SetTotal(total int64) *PageQuery {
	q.Total = total
	q.Pages = CalculatePages(total, q.PageSize)
	return q
}

// Offset 获取偏移量（用于数据库查询）
// 返回:
//   - int: 查询偏移量
func (q *PageQuery) Offset() int {
	return (q.PageNum - 1) * q.PageSize
}

// Limit 获取限制数（用于数据库查询）
// 返回:
//   - int: 查询限制数
func (q *PageQuery) Limit() int {
	return q.PageSize
}

// OrderBy 获取排序语句（用于数据库查询）
// 返回:
//   - string: 排序语句
func (q *PageQuery) OrderBy() string {
	if len(q.Sort) == 0 {
		// 防御性编程: 理论上 BuildPageQuery 会保证至少有一个默认排序
		return constant.DEFAULT_SORT_FIELD + " " + constant.DEFAULT_SORT_ORDER
	}

	var orderByClauses []string
	for _, s := range q.Sort {
		// 注意: 实际应用中应验证 s.Field 防止 SQL 注入
		orderByClauses = append(orderByClauses, s.Field+" "+s.Order)
	}
	return strings.Join(orderByClauses, ", ")
}

// BuildPageResult 构建分页结果
// 参数:
//   - query: 分页查询参数 (包含 Sort)
//   - list: 数据列表
//
// 返回:
//   - *PageResult: 分页结果 (现在包含 Sort 信息)
func BuildPageResult(query *PageQuery, list interface{}) *PageResult {
	return &PageResult{
		List:     list,
		PageNum:  query.PageNum,
		PageSize: query.PageSize,
		Total:    query.Total,
		Pages:    query.Pages,
		Sort:     query.Sort,
	}
}

// SuccessWithPage 返回分页成功响应
// 参数:
//   - ctx: Iris上下文
//   - pageResult: 分页结果
func SuccessWithPage(ctx iris.Context, pageResult *PageResult) {
	Success(ctx, pageResult)
}

// GetPaginationFromContext 从上下文获取分页信息
// 参数:
//   - ctx: Iris上下文
//
// 返回:
//   - int: 页码
//   - int: 每页大小
//   - []SortInfo: 排序规则
func GetPaginationFromContext(ctx iris.Context) (int, int, []SortInfo) {
	pageQuery := BuildPageQuery(ctx)
	return pageQuery.PageNum, pageQuery.PageSize, pageQuery.Sort
}

// HasNext 是否有下一页
// 返回:
//   - bool: 是否有下一页
func (r *PageResult) HasNext() bool {
	return r.PageNum < r.Pages
}

// HasPrev 是否有上一页
// 返回:
//   - bool: 是否有上一页
func (r *PageResult) HasPrev() bool {
	return r.PageNum > 1
}

// IsFirstPage 是否是第一页
// 返回:
//   - bool: 是否是第一页
func (r *PageResult) IsFirstPage() bool {
	return r.PageNum == 1
}

// IsLastPage 是否是最后一页
// 返回:
//   - bool: 是否是最后一页
func (r *PageResult) IsLastPage() bool {
	return r.PageNum == r.Pages || r.Pages == 0
}

// NextPage 下一页页码
// 返回:
//   - int: 下一页页码
func (r *PageResult) NextPage() int {
	if r.HasNext() {
		return r.PageNum + 1
	}
	return r.PageNum
}

// PrevPage 上一页页码
// 返回:
//   - int: 上一页页码
func (r *PageResult) PrevPage() int {
	if r.HasPrev() {
		return r.PageNum - 1
	}
	return r.PageNum
}

// Empty 创建空分页结果
// 参数:
//   - pageNum: 页码
//   - pageSize: 每页大小
//
// 返回:
//   - *PageResult: 空分页结果 (Sort 为 nil)
func Empty(pageNum, pageSize int) *PageResult {
	return &PageResult{
		List:     []interface{}{},
		PageNum:  pageNum,
		PageSize: pageSize,
		Total:    0,
		Pages:    0,
		Sort:     nil,
	}
}

// ToMap 转换为映射
// 保持不变，主要关注数据列表和基本分页信息
func (r *PageResult) ToMap() map[string]interface{} {
	resultMap := map[string]interface{}{
		"list":     r.List,
		"pageNum":  r.PageNum,
		"pageSize": r.PageSize,
		"total":    r.Total,
		"pages":    r.Pages,
		"hasNext":  r.HasNext(),
		"hasPrev":  r.HasPrev(),
		"isFirst":  r.IsFirstPage(),
		"isLast":   r.IsLastPage(),
	}
	return resultMap
}

// NewPageResult 创建新的分页结果
// 参数:
//   - list: 数据列表
//   - pageNum: 页码
//   - pageSize: 每页大小
//   - total: 总记录数
//
// 返回:
//   - *PageResult: 分页结果 (Sort 为 nil)
func NewPageResult(list interface{}, pageNum, pageSize int, total int64) *PageResult {
	return &PageResult{
		List:     list,
		PageNum:  pageNum,
		PageSize: pageSize,
		Total:    total,
		Pages:    CalculatePages(total, pageSize),
		Sort:     nil,
	}
}

// PageInfoMap 获取分页信息映射
// 返回:
//   - map[string]interface{}: 分页信息映射 (现在包含 sort 信息)
func (r *PageResult) PageInfoMap() map[string]interface{} {
	infoMap := map[string]interface{}{
		"pageNum":  r.PageNum,
		"pageSize": r.PageSize,
		"total":    r.Total,
		"pages":    r.Pages,
		"hasNext":  r.HasNext(),
		"hasPrev":  r.HasPrev(),
		"isFirst":  r.IsFirstPage(),
		"isLast":   r.IsLastPage(),
		"nextPage": r.NextPage(),
		"prevPage": r.PrevPage(),
	}
	if r.Sort != nil {
		infoMap["sort"] = r.Sort
	}
	return infoMap
}
