package dto

import (
	"backend/pkg/response"
	"backend/pkg/util"
)

// 1. 员工创建或更新数据传输对象
// 用于接收员工创建或更新请求参数
type EmployeeCreateOrUpdateDTO struct {
	TenantDTO
	EmployeeCode                         string         `json:"employeeCode" validate:"required,min=1,max=50"`
	EmployeeName                         string         `json:"employeeName" validate:"required,min=1,max=50"`
	EmployeeNamePinyin                   string         `json:"employeeNamePinyin" validate:"max=50"`
	EmployeeNameEn                       string         `json:"employeeNameEn" validate:"max=50"`
	EmployeeIDCard                       string         `json:"employeeIDCard" validate:"required,idcard,max=50"` // 考虑加入身份证格式验证
	EmployeeMobile                       string         `json:"employeeMobile" validate:"required,mobile,max=50"` // 考虑加入手机号格式验证
	EmployeeEmail                        string         `json:"employeeEmail" validate:"omitempty,email,max=50"`  // omitempty 表示可选，但如果提供则必须是email格式
	EmployeeGender                       int            `json:"employeeGender" validate:"oneof=0 1 2"`            // 0-未知, 1-男, 2-女
	EmployeeBirthday                     *util.JsonDate `json:"employeeBirthday" validate:"omitempty"`            // 可选
	EmployeeAvatar                       string         `json:"employeeAvatar" validate:"omitempty,max=255"`      // 可选，url格式
	EmployeeAddress                      string         `json:"employeeAddress" validate:"max=255"`
	EmployeeHouseholdAddress             string         `json:"employeeHouseholdAddress" validate:"omitempty,max=255"` // 新增
	EmployeeCurrentAddress               string         `json:"employeeCurrentAddress" validate:"omitempty,max=255"`   // 新增
	EmployeeInDate                       util.JsonDate  `json:"employeeInDate" validate:"required"`
	EmployeeOutDate                      *util.JsonDate `json:"employeeOutDate" validate:"omitempty"`       // 可选，离职日期
	EmployeeFirstWorkDate                *util.JsonDate `json:"employeeFirstWorkDate" validate:"omitempty"` // 新增
	EmployeeStatus                       int            `json:"employeeStatus" validate:"required"`         // 0-离职, 1-在职, 2-休假 (根据实际情况调整oneof的值)
	EmployeeRemark                       string         `json:"employeeRemark" validate:"max=500"`
	EmployeeEducation                    int            `json:"employeeEducation" validate:"omitempty,min=0"`     // 0-未知...
	EmployeeMaritalStatus                int            `json:"employeeMaritalStatus" validate:"omitempty,min=0"` // 0-未知...
	EmployeeNationality                  string         `json:"employeeNationality" validate:"max=50"`
	EmployeeNation                       string         `json:"employeeNation" validate:"omitempty,max=50"` // 新增：民族
	EmployeeNativePlace                  string         `json:"employeeNativePlace" validate:"max=50"`
	EmployeePoliticalStatus              int            `json:"employeePoliticalStatus" validate:"omitempty,min=0"`
	EmployeeIDCardType                   int            `json:"employeeIDCardType" validate:"omitempty,min=0"`
	EmployeeIDCardValidityPeriod         string         `json:"employeeIDCardValidityPeriod" validate:"max=50"`
	EmployeeDepartmentID                 uint           `json:"employeeDepartmentId" validate:"omitempty,gt=0"`
	EmployeePositionID                   uint           `json:"employeePositionId" validate:"omitempty,gt=0"`
	EmployeeJobTitle                     string         `json:"employeeJobTitle" validate:"max=50"`
	EmployeeWorkType                     int            `json:"employeeWorkType" validate:"omitempty,min=0"`
	EmployeeWorkLocation                 string         `json:"employeeWorkLocation" validate:"max=100"`
	EmployeeEmergencyContactName         string         `json:"employeeEmergencyContactName" validate:"omitempty,max=50"`         // 修改
	EmployeeEmergencyContactMobile       string         `json:"employeeEmergencyContactMobile" validate:"omitempty,max=20"`       // 修改
	EmployeeEmergencyContactRelationship string         `json:"employeeEmergencyContactRelationship" validate:"omitempty,max=20"` // 新增/确认
	EmployeeBankName                     string         `json:"employeeBankName" validate:"max=50"`
	EmployeeBankAccount                  string         `json:"employeeBankAccount" validate:"max=50"`
	EmployeeSocialSecurityNumber         string         `json:"employeeSocialSecurityNumber" validate:"max=50"`
	EmployeeHousingFundNumber            string         `json:"employeeHousingFundNumber" validate:"max=50"`
	EmployeeProbationEndDate             *util.JsonDate `json:"employeeProbationEndDate" validate:"omitempty"`
	EmployeeContractStartDate            *util.JsonDate `json:"employeeContractStartDate" validate:"omitempty"`
	EmployeeContractEndDate              *util.JsonDate `json:"employeeContractEndDate" validate:"omitempty"`
	EmployeeSalary                       *float64       `json:"employeeSalary" validate:"omitempty,min=0"` // 使用指针允许为空或0
	EmployeeWorkYears                    *int           `json:"employeeWorkYears" validate:"omitempty,min=0"`
	EmployeeSkills                       string         `json:"employeeSkills" validate:"max=500"`
	EmployeeCertificates                 string         `json:"employeeCertificates" validate:"max=500"`
	EmployeeHealthStatus                 int            `json:"employeeHealthStatus" validate:"omitempty,min=0"`
	EmployeeBloodType                    string         `json:"employeeBloodType" validate:"max=10"`
	EmployeeHeight                       *float64       `json:"employeeHeight" validate:"omitempty,min=0"`
	EmployeeWeight                       *float64       `json:"employeeWeight" validate:"omitempty,min=0"`
	EmployeeQQ                           string         `json:"employeeQQ" validate:"max=20"`
	EmployeePostCode                     string         `json:"employeePostCode" validate:"omitempty,max=20"`     // 新增/确认
	EmployeeEntrySource                  string         `json:"employeeEntrySource" validate:"omitempty,max=100"` // 新增/确认
	EmployeeReferralName                 string         `json:"employeeReferralName" validate:"omitempty,max=50"` // 新增/确认
	EmployeeHobby                        string         `json:"employeeHobby" validate:"omitempty,max=255"`       // 新增/确认
	EmployeeJobGradeValue                string         `json:"employeeJobGradeValue" validate:"omitempty,max=50"`
	EmployeeJobSubLevelValue             string         `json:"employeeJobSubLevelValue" validate:"omitempty,max=50"`
	EmployeeWorkCategoryValue            string         `json:"employeeWorkCategoryValue" validate:"omitempty,max=50"`
	EmployeeDegree                       string         `json:"employeeDegree" validate:"omitempty,max=50"`  // 确认
	EmployeeMajor                        string         `json:"employeeMajor" validate:"omitempty,max=100"`  // 确认
	EmployeeSchool                       string         `json:"employeeSchool" validate:"omitempty,max=100"` // 确认
	EmployeeGraduationDate               *util.JsonDate `json:"employeeGraduationDate" validate:"omitempty"` // 确认
}

// 2. 员工删除数据传输对象
// 用于接收员工删除请求参数
type EmployeeDeleteDTO struct {
	Ids []uint `json:"ids" validate:"required,min=1,dive,gt=0"`
}

// 3. 员工分页查询数据传输对象
// 用于接收员工分页查询请求参数
type EmployeePageQueryDTO struct {
	EmployeeCode              string             `query:"employeeCode"`
	EmployeeName              string             `query:"employeeName"`
	EmployeeNamePinyin        string             `query:"employeeNamePinyin"`
	EmployeeNameEn            string             `query:"employeeNameEn"`
	EmployeeIDCard            string             `query:"employeeIDCard"`
	EmployeeMobile            string             `query:"employeeMobile"`
	EmployeeEmail             string             `query:"employeeEmail"`
	EmployeeGender            *int               `query:"employeeGender"` // 使用指针允许查询时不传递该参数
	EmployeeStatus            *int               `query:"employeeStatus"` // 使用指针允许查询时不传递该参数
	EmployeeDepartmentID      *uint              `query:"employeeDepartmentId"`
	EmployeePositionID        *uint              `query:"employeePositionId"`
	EmployeeJobTitle          string             `query:"employeeJobTitle"`
	EmployeeWorkType          *int               `query:"employeeWorkType"`
	EmployeeJobGradeValue     *string            `query:"employeeJobGradeValue"`
	EmployeeJobSubLevelValue  *string            `query:"employeeJobSubLevelValue"`
	EmployeeWorkCategoryValue *string            `query:"employeeWorkCategoryValue"`
	PageQuery                 response.PageQuery // 分页查询参数
}

// EmployeeStatusUpdateDTO 员工状态更新DTO
type EmployeeStatusUpdateDTO struct {
	ID     uint `json:"id" validate:"required,gt=0"`
	Status int  `json:"status" validate:"required,oneof=0 1 2"` // 假设0,1,2是有效状态 (根据实际业务调整)
}

// EmployeeSimpleListQueryDTO DTO for querying simple employee list
type EmployeeSimpleListQueryDTO struct {
	Code   string `json:"code" form:"code"`     // 员工代码 (用于精确或模糊搜索，根据仓库层实现)
	Name   string `json:"name" form:"name"`     // 员工姓名 (用于模糊搜索)
	Status *int   `json:"status" form:"status"` // 员工状态 (例如: 1 表示在职)
	// 可以根据需要添加其他查询参数，如 DepartmentID 等
	// DepartmentID *uint `json:"departmentId" form:"departmentId"`
}

// BuildPageQuery 从 iris.Context 构建并填充 EmployeePageQueryDTO 的分页和排序参数
// ... existing code ...
