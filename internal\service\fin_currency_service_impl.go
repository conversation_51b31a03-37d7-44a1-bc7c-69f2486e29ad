package service

import (
	"context"
	"errors"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
)

// FinCurrencyService 定义了币种服务的接口
type FinCurrencyService interface {
	Create(ctx context.Context, req *dto.FinCurrencyCreateReq) (*vo.FinCurrencyItemRes, error)
	Update(ctx context.Context, req *dto.FinCurrencyUpdateReq) (*vo.FinCurrencyItemRes, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.FinCurrencyItemRes, error)
	GetPage(ctx context.Context, req *dto.FinCurrencyPageReq) (*response.PageResult, error)
	GetList(ctx context.Context) ([]*vo.FinCurrencyItemRes, error)
}

// finCurrencyServiceImpl 是 FinCurrencyService 的实现
type finCurrencyServiceImpl struct {
	*BaseServiceImpl
	repo repository.FinCurrencyRepository
}

// NewFinCurrencyService 创建一个新的币种服务实例
func NewFinCurrencyService(sm *ServiceManager, repo repository.FinCurrencyRepository) FinCurrencyService {
	return &finCurrencyServiceImpl{
		BaseServiceImpl: NewBaseService(sm),
		repo:            repo,
	}
}

// Create 创建币种
func (s *finCurrencyServiceImpl) Create(ctx context.Context, req *dto.FinCurrencyCreateReq) (*vo.FinCurrencyItemRes, error) {
	// 检查Code是否已存在
	existing, err := s.repo.FindByCode(ctx, req.Code)
	if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询币种失败").WithCause(err)
	}
	if existing != nil {
		return nil, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("币种代码 '%s' 已存在", req.Code))
	}

	entity := &entity.FinCurrency{
		Code:      req.Code,
		Name:      req.Name,
		IsEnabled: *req.IsEnabled,
	}
	// 设置创建人
	userID, _ := s.GetUserIDFromContext(ctx)
	entity.CreatedBy = uint(userID)

	if err := s.repo.Create(ctx, entity); err != nil {
		return nil, err // 错误已在repo层包装
	}

	return vo.FromCurrencyEntity(entity), nil
}

// Update 更新币种
func (s *finCurrencyServiceImpl) Update(ctx context.Context, req *dto.FinCurrencyUpdateReq) (*vo.FinCurrencyItemRes, error) {
	// 检查Code是否被其他币种占用
	existing, err := s.repo.FindByCode(ctx, req.Code)
	if err != nil && !errors.Is(err, apperrors.ErrNotFound) {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询币种失败").WithCause(err)
	}
	if existing != nil && existing.ID != req.ID {
		return nil, apperrors.NewParamError(apperrors.CODE_DATA_ALREADY_EXISTS, fmt.Sprintf("币种代码 '%s' 已被其他币种占用", req.Code))
	}

	// 查找原始实体
	originalEntity, err := s.repo.FindByID(ctx, req.ID)
	if err != nil {
		return nil, err // 错误已在repo层包装
	}

	entity := &entity.FinCurrency{
		Code:      req.Code,
		Name:      req.Name,
		IsEnabled: *req.IsEnabled,
	}
	// 保留原始创建信息
	entity.CreatedAt = originalEntity.CreatedAt
	entity.CreatedBy = originalEntity.CreatedBy
	// 设置更新人
	userID, _ := s.GetUserIDFromContext(ctx)
	entity.UpdatedBy = uint(userID)

	if err := s.repo.Update(ctx, entity); err != nil {
		return nil, err
	}

	return vo.FromCurrencyEntity(entity), nil
}

// Delete 删除币种
func (s *finCurrencyServiceImpl) Delete(ctx context.Context, id uint) error {
	// 检查币种是否被汇率等其他地方引用，这里暂时省略，后续模块需要补充
	return s.repo.Delete(ctx, id)
}

// GetByID 根据ID获取币种
func (s *finCurrencyServiceImpl) GetByID(ctx context.Context, id uint) (*vo.FinCurrencyItemRes, error) {
	entity, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return vo.FromCurrencyEntity(entity), nil
}

// GetPage 分页获取币种
func (s *finCurrencyServiceImpl) GetPage(ctx context.Context, req *dto.FinCurrencyPageReq) (*response.PageResult, error) {
	var conditions []repository.QueryCondition
	if req.Code != "" {
		conditions = append(conditions, repository.NewLikeCondition("code", req.Code))
	}
	if req.Name != "" {
		conditions = append(conditions, repository.NewLikeCondition("name", req.Name))
	}
	if req.IsEnabled != nil {
		conditions = append(conditions, repository.NewEqualCondition("is_enabled", *req.IsEnabled))
	}

	pageQuery := &response.PageQuery{
		PageNum:  req.Page,
		PageSize: req.PageSize,
	}

	pageResult, err := s.repo.FindByPage(ctx, pageQuery, conditions)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "分页查询币种失败").WithCause(err)
	}

	// 转换VO
	if pageResult.List != nil {
		entities, ok := pageResult.List.([]*entity.FinCurrency)
		if ok {
			pageResult.List = vo.FromCurrencyEntities(entities)
		}
	}

	return pageResult, nil
}

// GetList 获取所有启用的币种列表
func (s *finCurrencyServiceImpl) GetList(ctx context.Context) ([]*vo.FinCurrencyItemRes, error) {
	conditions := []repository.QueryCondition{
		repository.NewEqualCondition("is_enabled", true),
	}
	sortInfos := []response.SortInfo{{Field: "code", Order: "asc"}}

	entities, err := s.repo.FindByCondition(ctx, conditions, sortInfos)
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询启用币种列表失败").WithCause(err)
	}

	return vo.FromCurrencyEntities(entities), nil
}
