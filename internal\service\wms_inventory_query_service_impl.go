package service

import (
	"context"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
)

// WmsInventoryQueryService 库存查询服务接口
type WmsInventoryQueryService interface {
	BaseService

	// 基础查询功能
	GetPage(ctx context.Context, req *dto.WmsInventoryQueryReq) (*vo.PageResult[vo.WmsInventoryVO], error)
	GetByID(ctx context.Context, id uint) (*vo.WmsInventoryVO, error)
	GetByLocation(ctx context.Context, locationId uint) ([]*vo.WmsInventoryVO, error)
	GetByItem(ctx context.Context, itemId uint) ([]*vo.WmsInventoryVO, error)

	// 库存汇总统计
	GetSummary(ctx context.Context, req *dto.WmsInventorySummaryReq) (*vo.WmsInventorySummaryVO, error)
	GetStats(ctx context.Context, warehouseId *uint) (*vo.WmsInventoryStatsVO, error)

	// 库存周转分析
	GetTurnover(ctx context.Context, req *dto.WmsInventoryQueryReq) ([]*vo.WmsInventoryTurnoverVO, error)
	GetABCAnalysis(ctx context.Context, req *dto.WmsInventoryABCAnalysisReq) ([]*vo.WmsInventoryABCAnalysisVO, error)

	// 库存可用性检查
	CheckAvailability(ctx context.Context, req *dto.WmsInventoryAvailabilityReq) (*vo.WmsInventoryAvailabilityVO, error)
	GetAvailable(ctx context.Context, itemId uint, warehouseId *uint, requiredQty float64) ([]*vo.WmsInventoryVO, error)

	// 库存历史记录
	GetTransactionHistory(ctx context.Context, req *dto.WmsInventoryTransactionReq) ([]*vo.WmsInventoryTransactionLogVO, error)
	GetMovementHistory(ctx context.Context, req *dto.WmsInventoryMovementHistoryReq) ([]*vo.WmsInventoryMovementHistoryVO, error)

	// 库存预警
	GetAlerts(ctx context.Context, req *dto.WmsInventoryAlertReq) ([]*vo.WmsInventoryAlertLogVO, error)
	GetLowStockItems(ctx context.Context, warehouseId *uint) ([]*vo.WmsInventoryVO, error)
	GetExpiringItems(ctx context.Context, days int, warehouseId *uint) ([]*vo.WmsInventoryVO, error)

	// 导入导出功能
	ExportToExcel(ctx context.Context, req *dto.WmsInventoryExportReq) ([]byte, string, error)
	ImportFromExcel(ctx context.Context, req *dto.WmsInventoryImportReq) (*vo.ImportResultVO, error)

	// 批量操作
	BatchUpdate(ctx context.Context, req *dto.WmsInventoryBatchUpdateReq) error
	BatchFreeze(ctx context.Context, inventoryIds []uint, reason string) error
	BatchUnfreeze(ctx context.Context, inventoryIds []uint, reason string) error
}

// wmsInventoryQueryServiceImpl 库存查询服务实现
type wmsInventoryQueryServiceImpl struct {
	BaseServiceImpl
}

// NewWmsInventoryQueryService 创建库存查询服务实例
func NewWmsInventoryQueryService(sm *ServiceManager) WmsInventoryQueryService {
	return &wmsInventoryQueryServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// GetPage 分页查询库存
func (s *wmsInventoryQueryServiceImpl) GetPage(ctx context.Context, req *dto.WmsInventoryQueryReq) (*vo.PageResult[vo.WmsInventoryVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存失败").WithCause(err)
	}

	// 类型断言获取实体列表
	entities, ok := pageResult.List.([]*entity.WmsInventory)
	if !ok {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "分页结果类型转换失败")
	}

	// 转换为VO
	result := &vo.PageResult[vo.WmsInventoryVO]{
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		List:     make([]*vo.WmsInventoryVO, 0, len(entities)),
	}

	for _, entity := range entities {
		voItem := &vo.WmsInventoryVO{}
		if err := copier.Copy(voItem, entity); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result.List = append(result.List, voItem)
	}

	return result, nil
}

// GetByID 根据ID获取库存
func (s *wmsInventoryQueryServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventory, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存失败").WithCause(err)
	}
	if inventory == nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存不存在")
	}

	result := &vo.WmsInventoryVO{}
	if err := copier.Copy(result, inventory); err != nil {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
	}

	return result, nil
}

// GetByLocation 根据库位获取库存
func (s *wmsInventoryQueryServiceImpl) GetByLocation(ctx context.Context, locationId uint) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindByLocationID(ctx, locationId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryVO, 0, len(inventories))
	for _, inventory := range inventories {
		voItem := &vo.WmsInventoryVO{}
		if err := copier.Copy(voItem, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, voItem)
	}

	return result, nil
}

// GetByItem 根据物料获取库存
func (s *wmsInventoryQueryServiceImpl) GetByItem(ctx context.Context, itemId uint) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindByItemID(ctx, itemId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询库存失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryVO, 0, len(inventories))
	for _, inventory := range inventories {
		voItem := &vo.WmsInventoryVO{}
		if err := copier.Copy(voItem, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, voItem)
	}

	return result, nil
}

// GetSummary 获取库存汇总
func (s *wmsInventoryQueryServiceImpl) GetSummary(ctx context.Context, req *dto.WmsInventorySummaryReq) (*vo.WmsInventorySummaryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	// 检查仓库ID是否为空
	if req.WarehouseID == nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_PARAMS_MISSING, "仓库ID不能为空")
	}

	// 获取总数量
	totalQty, err := repo.SumQuantityByWarehouse(ctx, *req.WarehouseID)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存总量失败").WithCause(err)
	}

	// 获取总记录数
	totalCount, err := repo.CountByWarehouse(ctx, *req.WarehouseID)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存记录数失败").WithCause(err)
	}

	result := &vo.WmsInventorySummaryVO{
		GroupKey:      "warehouse",
		GroupName:     "仓库汇总",
		TotalItems:    totalCount,
		TotalQuantity: totalQty,
		AvailableQty:  0, // 需要计算可用数量
		AllocatedQty:  0, // 需要计算预占数量
		FrozenQty:     0, // 需要计算冻结数量
		TotalValue:    0, // 需要计算总价值
	}

	return result, nil
}

// GetStats 获取库存统计
func (s *wmsInventoryQueryServiceImpl) GetStats(ctx context.Context, warehouseId *uint) (*vo.WmsInventoryStatsVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager()

	result := &vo.WmsInventoryStatsVO{}

	if warehouseId != nil {
		// 获取指定仓库的统计
		inventoryRepo := repo.GetWmsInventoryRepository()

		totalQty, err := inventoryRepo.SumQuantityByWarehouse(ctx, *warehouseId)
		if err != nil {
			return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存统计失败").WithCause(err)
		}

		_, err = inventoryRepo.CountByWarehouse(ctx, *warehouseId)
		if err != nil {
			return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "获取库存统计失败").WithCause(err)
		}

		// 注意：WmsInventoryStatsVO 结构中没有 TotalQty, TotalCount, WarehouseID 字段
		// 需要根据实际的 VO 结构来设置字段
		result.TotalInbound = totalQty
		result.TotalOutbound = 0
		result.TotalAdjustment = 0
		result.TotalMovement = 0
	}

	return result, nil
}

// CheckAvailability 检查库存可用性
func (s *wmsInventoryQueryServiceImpl) CheckAvailability(ctx context.Context, req *dto.WmsInventoryAvailabilityReq) (*vo.WmsInventoryAvailabilityVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	isAvailable, err := repo.CheckAvailability(ctx, req.ItemID, req.RequiredQty, req.WarehouseID)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "检查库存可用性失败").WithCause(err)
	}

	result := &vo.WmsInventoryAvailabilityVO{
		ItemID:           req.ItemID,
		RequiredQty:      req.RequiredQty,
		AvailableQty:     0, // 需要计算实际可用数量
		AllocatedQty:     0,
		ReservedQty:      0,
		ShortageQty:      req.RequiredQty,
		CanAllocate:      isAvailable,
		AllocationResult: "PENDING",
	}

	return result, nil
}

// GetAvailable 获取可用库存
func (s *wmsInventoryQueryServiceImpl) GetAvailable(ctx context.Context, itemId uint, warehouseId *uint, requiredQty float64) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindAvailableByItem(ctx, itemId, warehouseId, requiredQty)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询可用库存失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryVO, 0, len(inventories))
	for _, inventory := range inventories {
		voItem := &vo.WmsInventoryVO{}
		if err := copier.Copy(voItem, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, voItem)
	}

	return result, nil
}

// GetLowStockItems 获取低库存物料
func (s *wmsInventoryQueryServiceImpl) GetLowStockItems(ctx context.Context, warehouseId *uint) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindLowStockItems(ctx, warehouseId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询低库存物料失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryVO, 0, len(inventories))
	for _, inventory := range inventories {
		voItem := &vo.WmsInventoryVO{}
		if err := copier.Copy(voItem, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, voItem)
	}

	return result, nil
}

// GetExpiringItems 获取即将过期的物料
func (s *wmsInventoryQueryServiceImpl) GetExpiringItems(ctx context.Context, days int, warehouseId *uint) ([]*vo.WmsInventoryVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	inventories, err := repo.FindExpiringItems(ctx, days, warehouseId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_QUERY_FAILED, "查询即将过期物料失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryVO, 0, len(inventories))
	for _, inventory := range inventories {
		voItem := &vo.WmsInventoryVO{}
		if err := copier.Copy(voItem, inventory); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, voItem)
	}

	return result, nil
}

// GetTurnover 获取库存周转分析
func (s *wmsInventoryQueryServiceImpl) GetTurnover(ctx context.Context, req *dto.WmsInventoryQueryReq) ([]*vo.WmsInventoryTurnoverVO, error) {
	// TODO: 实现库存周转分析逻辑
	return []*vo.WmsInventoryTurnoverVO{}, nil
}

// GetABCAnalysis 获取ABC分析
func (s *wmsInventoryQueryServiceImpl) GetABCAnalysis(ctx context.Context, req *dto.WmsInventoryABCAnalysisReq) ([]*vo.WmsInventoryABCAnalysisVO, error) {
	// TODO: 实现ABC分析逻辑
	return []*vo.WmsInventoryABCAnalysisVO{}, nil
}

// GetTransactionHistory 获取库存交易历史
func (s *wmsInventoryQueryServiceImpl) GetTransactionHistory(ctx context.Context, req *dto.WmsInventoryTransactionReq) ([]*vo.WmsInventoryTransactionLogVO, error) {
	// TODO: 实现库存交易历史查询逻辑
	return []*vo.WmsInventoryTransactionLogVO{}, nil
}

// GetMovementHistory 获取库存移动历史
func (s *wmsInventoryQueryServiceImpl) GetMovementHistory(ctx context.Context, req *dto.WmsInventoryMovementHistoryReq) ([]*vo.WmsInventoryMovementHistoryVO, error) {
	// TODO: 实现库存移动历史查询逻辑
	return []*vo.WmsInventoryMovementHistoryVO{}, nil
}

// GetAlerts 获取库存预警
func (s *wmsInventoryQueryServiceImpl) GetAlerts(ctx context.Context, req *dto.WmsInventoryAlertReq) ([]*vo.WmsInventoryAlertLogVO, error) {
	// TODO: 实现库存预警查询逻辑
	return []*vo.WmsInventoryAlertLogVO{}, nil
}

// ExportToExcel 导出到Excel
func (s *wmsInventoryQueryServiceImpl) ExportToExcel(ctx context.Context, req *dto.WmsInventoryExportReq) ([]byte, string, error) {
	// TODO: 实现Excel导出逻辑
	return nil, "", apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "Excel导出功能暂未实现")
}

// ImportFromExcel 从Excel导入
func (s *wmsInventoryQueryServiceImpl) ImportFromExcel(ctx context.Context, req *dto.WmsInventoryImportReq) (*vo.ImportResultVO, error) {
	// TODO: 实现Excel导入逻辑
	return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "Excel导入功能暂未实现")
}

// BatchUpdate 批量更新库存
func (s *wmsInventoryQueryServiceImpl) BatchUpdate(ctx context.Context, req *dto.WmsInventoryBatchUpdateReq) error {
	// TODO: 实现批量更新逻辑
	return apperrors.NewBusinessError(apperrors.CODE_SYSTEM_NOT_IMPLEMENTED, "批量更新功能暂未实现")
}

// BatchFreeze 批量冻结库存
func (s *wmsInventoryQueryServiceImpl) BatchFreeze(ctx context.Context, inventoryIds []uint, reason string) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	// 批量冻结库存
	err := repo.BatchUpdateStatus(ctx, inventoryIds, "FROZEN", reason)
	if err != nil {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_UPDATE_FAILED, "批量冻结库存失败").WithCause(err)
	}

	return nil
}

// BatchUnfreeze 批量解冻库存
func (s *wmsInventoryQueryServiceImpl) BatchUnfreeze(ctx context.Context, inventoryIds []uint, reason string) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()

	// 批量解冻库存
	err := repo.BatchUpdateStatus(ctx, inventoryIds, "AVAILABLE", reason)
	if err != nil {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_UPDATE_FAILED, "批量解冻库存失败").WithCause(err)
	}

	return nil
}
