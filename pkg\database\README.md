# 数据库模块说明文档

## 简介

数据库模块提供了统一的数据库操作接口和实现，支持多种数据库类型（MySQL、PostgreSQL等），并提供事务管理、数据迁移等高级功能。该模块旨在简化数据库操作，提高代码复用性，并确保数据一致性和安全性。

## 目录结构

```go
database/
├── interface.go    # 数据库接口定义
├── mysql.go        # MySQL数据库实现
├── postgresql.go   # PostgreSQL数据库实现
├── transaction.go  # 事务管理实现
├── migration.go    # 数据迁移功能
└── README.md       # 说明文档
```

## 核心接口

### 数据库接口

`Database`接口定义了数据库操作的基本方法：

```go
// Database 数据库接口
type Database interface {
    // 获取数据库连接
    GetDB() *gorm.DB
    
    // 获取数据库类型
    GetType() string
    
    // 获取数据库配置
    GetConfig() *Config
    
    // 开始事务
    Begin() (Transaction, error)
    
    // 关闭数据库连接
    Close() error
    
    // 执行数据库迁移
    Migrate(models ...interface{}) error
    
    // 执行SQL脚本
    ExecScript(script string) error
    
    // 检查表是否存在
    HasTable(tableName string) bool
    
    // 检查数据库连接
    Ping() error
}
```

### 事务接口

`Transaction`接口定义了事务操作的方法：

```go
// Transaction 事务接口
type Transaction interface {
    // 获取事务连接
    GetDB() *gorm.DB
    
    // 提交事务
    Commit() error
    
    // 回滚事务
    Rollback() error
}
```

## 数据库配置

```go
// Config 数据库配置
type Config struct {
    Type         string        // 数据库类型：mysql, postgres
    Host         string        // 主机地址
    Port         int           // 端口号
    Username     string        // 用户名
    Password     string        // 密码
    Database     string        // 数据库名
    Charset      string        // 字符集
    MaxOpenConns int           // 最大打开连接数
    MaxIdleConns int           // 最大空闲连接数
    MaxLifetime  time.Duration // 连接最大生存时间
    SSLMode      string        // SSL模式
    TimeZone     string        // 时区
    LogLevel     string        // 日志级别
    AutoMigrate  bool          // 是否自动迁移
    TablePrefix  string        // 表前缀
}
```

## 数据库实现

### MySQL实现

`MySQLDatabase`实现了`Database`接口，提供MySQL数据库的特定功能：

1. 支持MySQL特有的连接参数
2. 优化的查询性能
3. 适配MySQL特有的数据类型和SQL语法

### PostgreSQL实现

`PostgreSQLDatabase`实现了`Database`接口，提供PostgreSQL数据库的特定功能：

1. 支持PostgreSQL特有的连接参数
2. 支持高级数据类型（如JSON、数组）
3. 支持复杂查询和索引

## 事务管理

事务管理提供了以下功能：

1. 简单的事务操作（开始、提交、回滚）
2. 嵌套事务支持
3. 事务回调函数
4. 事务传播行为

## 数据迁移

数据迁移功能支持：

1. 自动创建表和索引
2. 版本化的迁移脚本
3. 迁移历史记录
4. 向前和向后迁移
5. 数据种子功能

## 使用示例

### 创建数据库连接

```go
// MySQL配置
mysqlConfig := &database.Config{
    Type:         "mysql",
    Host:         "localhost",
    Port:         3306,
    Username:     "root",
    Password:     "password",
    Database:     "test_db",
    Charset:      "utf8mb4",
    MaxOpenConns: 100,
    MaxIdleConns: 10,
    MaxLifetime:  time.Hour,
    TimeZone:     "Asia/Shanghai",
    LogLevel:     "info",
    AutoMigrate:  true,
}

// 创建MySQL数据库连接
mysqlDB, err := database.NewMySQLDatabase(mysqlConfig)
if err != nil {
    log.Fatalf("连接MySQL数据库失败: %v", err)
}
defer mysqlDB.Close()

// PostgreSQL配置
pgConfig := &database.Config{
    Type:         "postgres",
    Host:         "localhost",
    Port:         5432,
    Username:     "postgres",
    Password:     "password",
    Database:     "test_db",
    SSLMode:      "disable",
    MaxOpenConns: 100,
    MaxIdleConns: 10,
    MaxLifetime:  time.Hour,
    TimeZone:     "Asia/Shanghai",
    LogLevel:     "info",
    AutoMigrate:  true,
}

// 创建PostgreSQL数据库连接
pgDB, err := database.NewPostgreSQLDatabase(pgConfig)
if err != nil {
    log.Fatalf("连接PostgreSQL数据库失败: %v", err)
}
defer pgDB.Close()
```

### 基本查询操作

```go
// 获取GORM数据库连接
db := mysqlDB.GetDB()

// 创建记录
user := &User{Name: "张三", Age: 25}
result := db.Create(user)
if result.Error != nil {
    log.Fatalf("创建用户失败: %v", result.Error)
}

// 查询记录
var users []User
result = db.Where("age > ?", 18).Find(&users)
if result.Error != nil {
    log.Fatalf("查询用户失败: %v", result.Error)
}

// 更新记录
result = db.Model(&User{}).Where("id = ?", 1).Update("name", "李四")
if result.Error != nil {
    log.Fatalf("更新用户失败: %v", result.Error)
}

// 删除记录
result = db.Delete(&User{}, 1)
if result.Error != nil {
    log.Fatalf("删除用户失败: %v", result.Error)
}
```

### 事务操作

```go
// 方法1：使用事务接口
tx, err := mysqlDB.Begin()
if err != nil {
    log.Fatalf("开始事务失败: %v", err)
}

// 在事务中执行操作
txDB := tx.GetDB()
result := txDB.Create(&User{Name: "王五", Age: 30})
if result.Error != nil {
    tx.Rollback()
    log.Fatalf("创建用户失败: %v", result.Error)
}

// 提交事务
if err := tx.Commit(); err != nil {
    log.Fatalf("提交事务失败: %v", err)
}

// 方法2：使用事务回调
err = database.Transaction(mysqlDB, func(tx database.Transaction) error {
    txDB := tx.GetDB()
    
    // 创建用户
    if err := txDB.Create(&User{Name: "赵六", Age: 35}).Error; err != nil {
        return err
    }
    
    // 更新部门
    if err := txDB.Model(&Department{}).Where("id = ?", 1).Update("name", "研发部").Error; err != nil {
        return err
    }
    
    return nil
})

if err != nil {
    log.Fatalf("事务执行失败: %v", err)
}
```

```go
// 自动迁移模型
err := mysqlDB.Migrate(&User{}, &Department{}, &Role{})
if err != nil {
    log.Fatalf("迁移模型失败: %v", err)
}

// 执行迁移脚本
migrator := database.NewMigrator(mysqlDB)

// 添加迁移
migrator.AddMigration("create_users_table", &database.Migration{
    Up: func(db *gorm.DB) error {
        return db.Exec(`
            CREATE TABLE users (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                age INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `).Error
    },
    Down: func(db *gorm.DB) error {
        return db.Exec("DROP TABLE IF EXISTS users").Error
    },
})

// 执行迁移
err = migrator.Migrate()
if err != nil {
    log.Fatalf("执行迁移失败: %v", err)
}

// 回滚最后一次迁移
err = migrator.Rollback()
if err != nil {
    log.Fatalf("回滚迁移失败: %v", err)
}

// 重置所有迁移
err = migrator.Reset()
if err != nil {
    log.Fatalf("重置迁移失败: %v", err)
}

// 刷新迁移（重置并重新执行）
err = migrator.Refresh()
if err != nil {
    log.Fatalf("刷新迁移失败: %v", err)
}
```

### 执行原生SQL

```go
// 执行SQL查询
var users []User
err := mysqlDB.GetDB().Raw("SELECT * FROM users WHERE age > ?", 18).Scan(&users).Error
if err != nil {
    log.Fatalf("执行SQL查询失败: %v", err)
}

// 执行SQL脚本
script := `
INSERT INTO users (name, age) VALUES ('张三', 25);
INSERT INTO users (name, age) VALUES ('李四', 30);
UPDATE users SET age = 26 WHERE name = '张三';
`
err = mysqlDB.ExecScript(script)
if err != nil {
    log.Fatalf("执行SQL脚本失败: %v", err)
}
```

## 最佳实践

### 数据库连接管理

1. **连接池配置**：根据应用负载合理设置连接池参数

   ```go
   config.MaxOpenConns = 100  // 根据并发量设置
   config.MaxIdleConns = 10   // 通常为MaxOpenConns的10-20%
   config.MaxLifetime = time.Hour  // 避免连接长时间不被释放
   ```

2. **优雅关闭**：在应用退出时关闭数据库连接

   ```go
   defer db.Close()
   ```

3. **使用事务回调**：避免忘记提交或回滚事务

   ```go
   database.Transaction(db, func(tx database.Transaction) error {
       // 在这里执行事务操作
       return nil  // 返回nil将自动提交，返回错误将自动回滚
   })
   ```

4. **合理的事务范围**：事务应尽可能短，避免长事务

   ```go
   // 不好的做法：事务范围过大
   tx, _ := db.Begin()
   // 执行很多操作，包括耗时的业务逻辑
   tx.Commit()
   
   // 好的做法：事务只包含必要的数据库操作
   // 先执行业务逻辑
   database.Transaction(db, func(tx database.Transaction) error {
       // 只在事务中执行必要的数据库操作
       return nil
   })
   ```

### 查询优化

1. **使用索引**：确保常用查询字段有适当的索引

   ```go
   // 在模型定义中添加索引
   type User struct {
       ID    uint   `gorm:"primaryKey"`
       Email string `gorm:"index"`
       Name  string `gorm:"index:idx_name_age"`
       Age   int    `gorm:"index:idx_name_age"`
   }
   ```

2. **批量操作**：使用批量插入而不是多次单条插入

   ```go
   // 不好的做法
   for _, user := range users {
       db.Create(&user)
   }
   
   // 好的做法
   db.CreateInBatches(users, 100)
   ```

3. **选择性查询**：只查询需要的字段

   ```go
   db.Select("name", "age").Find(&users)
   ```

### 数据迁移1

1. **版本控制**：使用版本化的迁移脚本

   ```go
   migrator.AddMigration("v1.0.0_create_users", createUsersMigration)
   migrator.AddMigration("v1.0.1_add_email_to_users", addEmailMigration)
   ```

2. **测试迁移**：在应用部署前测试迁移脚本

   ```go
   // 在测试环境中
   err := migrator.Migrate()
   assert.NoError(t, err)
   
   // 验证表结构
   assert.True(t, db.HasTable("users"))
   ```

## 注意事项

1. **安全性**：
   - 避免SQL注入，使用参数化查询
   - 敏感信息（如密码）不应明文存储
   - 数据库凭证应妥善保管，考虑使用环境变量或密钥管理服务

2. **性能**：
   - 大型查询应使用分页
   - 避免N+1查询问题，使用预加载或联表查询
   - 监控慢查询，定期优化

3. **兼容性**：
   - 注意不同数据库之间的SQL语法差异
   - 使用GORM的通用API而不是特定数据库的功能可提高可移植性

4. **错误处理**：
   - 始终检查数据库操作的错误返回
   - 区分不同类型的错误（如连接错误、约束错误）
   - 考虑实现重试机制处理临时性错误

5. **并发**：
   - 注意并发操作可能导致的数据竞争
   - 使用乐观锁或悲观锁处理并发更新
   - 考虑使用数据库事务隔离级别控制并发行为
