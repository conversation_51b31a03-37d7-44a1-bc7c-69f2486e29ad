package repository

import (
	"context"
	"errors"
	"time"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"

	"gorm.io/gorm"
)

// FinExchangeRateRepository 定义了汇率仓库的接口
type FinExchangeRateRepository interface {
	BaseRepository[entity.FinExchangeRate, uint]
	FindLatestRate(ctx context.Context, fromCode, toCode string, date time.Time) (*entity.FinExchangeRate, error)
}

// finExchangeRateRepositoryImpl 是 FinExchangeRateRepository 的实现
type finExchangeRateRepositoryImpl struct {
	BaseRepository[entity.FinExchangeRate, uint]
}

// NewFinExchangeRateRepository 创建一个新的汇率仓库实例
func NewFinExchangeRateRepository(db *gorm.DB) FinExchangeRateRepository {
	baseRepo := NewBaseRepository[entity.FinExchangeRate, uint](db)
	return &finExchangeRateRepositoryImpl{
		BaseRepository: baseRepo,
	}
}

// FindLatestRate 查找指定日期或之前的最新汇率
// 通过币种代码进行查询，并在单个数据库查询中完成操作，以提高效率。
func (r *finExchangeRateRepositoryImpl) FindLatestRate(ctx context.Context, fromCode, toCode string, date time.Time) (*entity.FinExchangeRate, error) {
	var rate entity.FinExchangeRate
	db := r.GetDB(ctx)

	// 使用子查询获取币种ID，避免额外的数据库查询
	fromCurrencyQuery := db.Model(&entity.FinCurrency{}).Select("id").Where("code = ?", fromCode)
	toCurrencyQuery := db.Model(&entity.FinCurrency{}).Select("id").Where("code = ?", toCode)

	// 链式查询
	result := db.Preload("FromCurrency").
		Preload("ToCurrency").
		Where("from_currency_id = (?)", fromCurrencyQuery).
		Where("to_currency_id = (?)", toCurrencyQuery).
		Where("rate_date <= ?", date).
		Order("rate_date DESC").
		First(&rate)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "找不到适用的汇率")
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询最新汇率失败").WithCause(result.Error)
	}
	return &rate, nil
}
