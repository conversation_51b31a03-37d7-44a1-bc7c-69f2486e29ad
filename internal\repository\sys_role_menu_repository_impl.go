package repository

import (
	"context"
	"reflect"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// RoleMenuRepository 角色菜单关联仓库接口
// 负责管理角色与菜单之间的多对多关系
type RoleMenuRepository interface {
	// AddMenuToRole 为角色添加菜单
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - menuID: 菜单ID
	// 返回:
	//   - error: 错误信息
	AddMenuToRole(ctx context.Context, roleID, menuID uint) error

	// RemoveMenuFromRole 从角色移除菜单
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - menuID: 菜单ID
	// 返回:
	//   - error: 错误信息
	RemoveMenuFromRole(ctx context.Context, roleID, menuID uint) error

	// GetRoleMenus 获取角色的所有菜单ID
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	// 返回:
	//   - []uint: 菜单ID列表
	//   - error: 错误信息
	GetRoleMenus(ctx context.Context, roleID uint, sortInfos []response.SortInfo) ([]*entity.Menu, error)

	// GetMenuRoles 获取拥有特定菜单的所有角色ID
	// 参数:
	//   - ctx: 上下文
	//   - menuID: 菜单ID
	// 返回:
	//   - []uint: 角色ID列表
	//   - error: 错误信息
	GetMenuRoles(ctx context.Context, menuID uint, sortInfos []response.SortInfo) ([]*entity.Role, error)

	// HasMenu 检查角色是否拥有特定菜单
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - menuID: 菜单ID
	// 返回:
	//   - bool: 是否拥有菜单
	//   - error: 错误信息
	HasMenu(ctx context.Context, roleID, menuID uint) (bool, error)

	// SetRoleMenus 设置角色的菜单（替换现有菜单）
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	//   - menuIDs: 菜单ID列表
	// 返回:
	//   - error: 错误信息
	SetRoleMenus(ctx context.Context, roleID uint, menuIDs []uint) error

	// DeleteRoleMenus 删除角色的所有菜单
	// 参数:
	//   - ctx: 上下文
	//   - roleID: 角色ID
	// 返回:
	//   - error: 错误信息
	DeleteRoleMenus(ctx context.Context, roleID uint) error

	// DeleteMenuRoles 删除菜单的所有角色
	// 参数:
	//   - ctx: 上下文
	//   - menuID: 菜单ID
	// 返回:
	//   - error: 错误信息
	DeleteMenuRoles(ctx context.Context, menuID uint) error

	// FindMenusByRoleIDs 根据角色ID列表查询关联的菜单列表
	// 参数:
	//   - ctx: 上下文
	//   - roleIDs: 角色ID列表
	//   - sortInfos: 排序信息
	// 返回:
	//   - []*entity.Menu: 菜单实体列表
	//   - error: 错误信息
	FindMenusByRoleIDs(ctx context.Context, roleIDs []uint, sortInfos []response.SortInfo) ([]*entity.Menu, error)

	// FindMenuIDsByRoleIDs 根据角色ID列表查询关联的菜单ID列表映射
	// 返回 map[roleID][]menuID
	FindMenuIDsByRoleIDs(ctx context.Context, roleIDs []uint) (map[uint][]uint, error)
}

// RoleMenuRepositoryImpl 角色菜单关联仓库实现
type RoleMenuRepositoryImpl struct {
	db *gorm.DB
}

// NewRoleMenuRepository 创建角色菜单关联仓库
// 参数:
//   - db: 数据库连接
//
// 返回:
//   - RoleMenuRepository: 角色菜单关联仓库接口
func NewRoleMenuRepository(db *gorm.DB) RoleMenuRepository {
	return &RoleMenuRepositoryImpl{
		db: db,
	}
}

// getDB 获取数据库连接
func (r *RoleMenuRepositoryImpl) getDB(ctx context.Context) *gorm.DB {
	// 直接使用注入的 r.db。如果 RepositoryManager.Transaction 启动了事务，
	// 那么 r.db 已经是事务性的 *gorm.DB。
	// 使用 WithContext 将当前请求的上下文与数据库操作关联起来。
	if r.db == nil {
		// 理论上不应该发生，因为 RepositoryManager 会注入 DB
		logger.WithContext(ctx).Error("RoleMenuRepositoryImpl: database connection is nil")
		panic("RoleMenuRepositoryImpl: database connection is nil")
	}
	if ctx != nil {
		return r.db.WithContext(ctx)
	}
	// 如果 ctx 为 nil，则返回原始的 db 连接（不常见，但作为回退）
	return r.db
}

// AddMenuToRole 为角色添加菜单
func (r *RoleMenuRepositoryImpl) AddMenuToRole(ctx context.Context, roleID, menuID uint) error {
	exists, err := r.HasMenu(ctx, roleID, menuID)
	if err != nil {
		return err
	}
	if exists {
		return nil
	}
	newAssoc := entity.RoleMenu{
		RoleID: roleID,
		MenuID: menuID,
	}
	result := r.getDB(ctx).Create(&newAssoc)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("AddMenuToRole: 创建角色菜单关联失败 (RoleID: %d, MenuID: %d)", roleID, menuID)
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "添加角色菜单关联失败").WithCause(result.Error)
	}
	logger.WithContext(ctx).Debugf("AddMenuToRole: 成功添加关联 (RoleID: %d, MenuID: %d)", roleID, menuID)
	return nil
}

// RemoveMenuFromRole 从角色移除菜单
func (r *RoleMenuRepositoryImpl) RemoveMenuFromRole(ctx context.Context, roleID, menuID uint) error {
	result := r.getDB(ctx).Where("role_id = ? AND menu_id = ?", roleID, menuID).Delete(&entity.RoleMenu{})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("RemoveMenuFromRole: 删除角色菜单关联失败 (RoleID: %d, MenuID: %d)", roleID, menuID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "移除角色菜单关联失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		logger.WithContext(ctx).Warnf("RemoveMenuFromRole: 未找到要删除的关联 (RoleID: %d, MenuID: %d)", roleID, menuID)
	} else {
		logger.WithContext(ctx).Debugf("RemoveMenuFromRole: 成功删除关联 (RoleID: %d, MenuID: %d)", roleID, menuID)
	}
	return nil
}

// GetRoleMenus 获取角色拥有的菜单列表
func (r *RoleMenuRepositoryImpl) GetRoleMenus(ctx context.Context, roleID uint, sortInfos []response.SortInfo) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	db := r.getDB(ctx).Model(&entity.RoleMenu{}).
		Select("m.*").
		Joins("INNER JOIN sys_menu m ON m.id = sys_role_menu.menu_id").
		Where("sys_role_menu.role_id = ?", roleID)

	// 获取 Menu 实体的允许排序字段
	menuEntityType := reflect.TypeOf(entity.Menu{})
	allowedMenuColumns := getSortableColumns(menuEntityType)
	// 将允许排序的列传递给 applySortInfos，注意列名前缀 'm.'
	aliasedAllowedColumns := make(map[string]string)
	for k, v := range allowedMenuColumns {
		aliasedAllowedColumns[k] = "m." + v // 添加表别名
	}
	db = applySortInfos(db, sortInfos, aliasedAllowedColumns)

	result := db.Scan(&menus)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("查询角色菜单失败")
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询角色菜单失败")
	}
	return menus, nil
}

// GetMenuRoles 获取拥有菜单的角色列表
func (r *RoleMenuRepositoryImpl) GetMenuRoles(ctx context.Context, menuID uint, sortInfos []response.SortInfo) ([]*entity.Role, error) {
	var roles []*entity.Role
	db := r.getDB(ctx).Model(&entity.RoleMenu{}).
		Select("r.*").
		Joins("INNER JOIN sys_role r ON r.id = sys_role_menu.role_id").
		Where("sys_role_menu.menu_id = ?", menuID)

	// 获取 Role 实体的允许排序字段
	roleEntityType := reflect.TypeOf(entity.Role{})
	allowedRoleColumns := getSortableColumns(roleEntityType)
	// 将允许排序的列传递给 applySortInfos，注意列名前缀 'r.'
	aliasedAllowedColumns := make(map[string]string)
	for k, v := range allowedRoleColumns {
		aliasedAllowedColumns[k] = "r." + v // 添加表别名
	}
	db = applySortInfos(db, sortInfos, aliasedAllowedColumns)

	result := db.Scan(&roles)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("查询菜单角色失败")
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询菜单角色失败")
	}
	return roles, nil
}

// HasMenu 检查角色是否拥有特定菜单
func (r *RoleMenuRepositoryImpl) HasMenu(ctx context.Context, roleID, menuID uint) (bool, error) {
	var count int64
	err := r.getDB(ctx).Model(&entity.RoleMenu{}).Where("role_id = ? AND menu_id = ?", roleID, menuID).Count(&count).Error
	if err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("HasMenu: 查询角色菜单关联失败 (RoleID: %d, MenuID: %d)", roleID, menuID)
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查角色菜单关联失败").WithCause(err)
	}
	return count > 0, nil
}

// SetRoleMenus 设置角色的菜单（替换现有菜单）
func (r *RoleMenuRepositoryImpl) SetRoleMenus(ctx context.Context, roleID uint, menuIDs []uint) error {
	// <<< 假设此操作通常在服务层事务中调用，移除内部事务管理 >>>
	db := r.getDB(ctx) // Use the context-aware DB (could be tx)

	// 1. Delete existing associations for the role
	logger.WithContext(ctx).Debugf("SetRoleMenus: Deleting old associations for roleID: %d", roleID)
	// <<< 使用 entity.RoleMenu 进行删除 >>>
	if err := db.Where("role_id = ?", roleID).Delete(&entity.RoleMenu{}).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("SetRoleMenus: 删除旧角色菜单关联失败 (RoleID: %d)", roleID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除角色现有菜单失败").WithCause(err)
	}
	logger.WithContext(ctx).Debugf("SetRoleMenus: Old associations deleted for roleID: %d", roleID)

	// 2. If the new menu ID list is not empty, insert new associations
	if len(menuIDs) > 0 {
		// <<< 创建 entity.RoleMenu 切片 >>>
		var newAssociations []entity.RoleMenu
		processedIDs := make(map[uint]struct{}) // 防止重复插入相同的 menuID

		for _, menuID := range menuIDs {
			// 跳过无效 ID 或重复 ID
			if menuID == 0 {
				logger.WithContext(ctx).Warnf("SetRoleMenus: Attempted to associate invalid menuID 0 for roleID: %d", roleID)
				continue
			}
			if _, exists := processedIDs[menuID]; exists {
				logger.WithContext(ctx).Warnf("SetRoleMenus: Duplicate menuID %d found for roleID: %d, skipping.", menuID, roleID)
				continue
			}

			newAssociations = append(newAssociations, entity.RoleMenu{RoleID: roleID, MenuID: menuID})
			processedIDs[menuID] = struct{}{} // 标记已处理
		}

		// Perform bulk insert if there are valid associations
		if len(newAssociations) > 0 {
			logger.WithContext(ctx).Debugf("SetRoleMenus: Inserting %d new associations for roleID: %d", len(newAssociations), roleID)
			// <<< 使用 GORM 的 Create 进行批量插入 >>>
			if err := db.Create(&newAssociations).Error; err != nil {
				logger.WithContext(ctx).WithError(err).Errorf("SetRoleMenus: 插入新角色菜单关联失败 (RoleID: %d)", roleID)
				return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "创建角色菜单关联失败").WithCause(err)
			}
			logger.WithContext(ctx).Debugf("SetRoleMenus: New associations inserted for roleID: %d", roleID)
		} else {
			logger.WithContext(ctx).Debugf("SetRoleMenus: No valid new associations to insert after filtering for roleID: %d", roleID)
		}

	} else {
		logger.WithContext(ctx).Debugf("SetRoleMenus: Menu ID list is empty, only performed delete for roleID: %d", roleID)
	}

	logger.WithContext(ctx).Infof("SetRoleMenus: Operation completed successfully for roleID: %d", roleID)
	return nil
}

// DeleteRoleMenus 删除角色的所有菜单
func (r *RoleMenuRepositoryImpl) DeleteRoleMenus(ctx context.Context, roleID uint) error {
	logger.WithContext(ctx).Debugf("DeleteRoleMenus: Deleting all associations for roleID: %d", roleID)
	// <<< 使用 entity.RoleMenu 进行删除 >>>
	if err := r.getDB(ctx).Where("role_id = ?", roleID).Delete(&entity.RoleMenu{}).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("DeleteRoleMenus: 删除角色菜单失败 (RoleID: %d)", roleID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除角色菜单失败").WithCause(err)
	}
	logger.WithContext(ctx).Debugf("DeleteRoleMenus: All associations deleted for roleID: %d", roleID)
	return nil
}

// DeleteMenuRoles 删除菜单的所有角色
func (r *RoleMenuRepositoryImpl) DeleteMenuRoles(ctx context.Context, menuID uint) error {
	logger.WithContext(ctx).Debugf("DeleteMenuRoles: Deleting all associations for menuID: %d", menuID)
	// <<< 使用 entity.RoleMenu 进行删除 >>>
	if err := r.getDB(ctx).Where("menu_id = ?", menuID).Delete(&entity.RoleMenu{}).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("DeleteMenuRoles: 删除菜单角色失败 (MenuID: %d)", menuID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除菜单角色失败").WithCause(err)
	}
	logger.WithContext(ctx).Debugf("DeleteMenuRoles: All associations deleted for menuID: %d", menuID)
	return nil
}

// FindMenuIDsByRoleIDs 根据角色ID列表查询关联的菜单ID列表映射
// 返回 map[roleID][]menuID
func (r *RoleMenuRepositoryImpl) FindMenuIDsByRoleIDs(ctx context.Context, roleIDs []uint) (map[uint][]uint, error) {
	results := make(map[uint][]uint)
	if len(roleIDs) == 0 {
		return results, nil
	}

	// Define a struct to scan the role_id and menu_id pairs
	type RoleMenuPair struct {
		RoleID uint `gorm:"column:role_id"`
		MenuID uint `gorm:"column:menu_id"`
	}

	var pairs []RoleMenuPair

	db := r.getDB(ctx).Model(&entity.RoleMenu{}).
		Select("role_id, menu_id").
		Where("role_id IN ?", roleIDs)

	if err := db.Find(&pairs).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("根据角色ID列表查询菜单ID失败 (RoleIDs: %v)", roleIDs)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询角色菜单关联失败").WithCause(err)
	}

	// Group menu IDs by role ID
	for _, p := range pairs {
		results[p.RoleID] = append(results[p.RoleID], p.MenuID)
	}

	// Ensure all requested roleIDs are keys in the map, even if they have no menus
	for _, id := range roleIDs {
		if _, exists := results[id]; !exists {
			results[id] = []uint{}
		}
	}

	return results, nil
}

// FindMenusByRoleIDs 根据角色ID列表查询关联的菜单列表
func (r *RoleMenuRepositoryImpl) FindMenusByRoleIDs(ctx context.Context, roleIDs []uint, sortInfos []response.SortInfo) ([]*entity.Menu, error) {
	if len(roleIDs) == 0 {
		return []*entity.Menu{}, nil
	}
	var menus []*entity.Menu
	db := r.getDB(ctx).Table("sys_menu as m").Distinct("m.id").
		Select("m.*")

	isSuperAdmin := false
	for _, roleID := range roleIDs {
		if roleID == 1 {
			isSuperAdmin = true
			break
		}
	}

	if isSuperAdmin {
		db = db.Where("m.type IN (?) AND m.status = ? AND m.hidden = ?", []int{1, 2}, 1, false)
	} else {
		db = db.Joins("INNER JOIN sys_role_menu rm ON m.id = rm.menu_id").
			Where("rm.role_id IN (?) AND m.type IN (?) AND m.status = ? AND m.hidden = ?", roleIDs, []int{1, 2}, 1, false)
	}

	// 获取 Menu 实体的允许排序字段
	menuEntityType := reflect.TypeOf(entity.Menu{})
	allowedMenuColumns := getSortableColumns(menuEntityType)
	// 将允许排序的列传递给 applySortInfos，注意列名前缀 'm.'
	aliasedAllowedColumns := make(map[string]string)
	for k, v := range allowedMenuColumns {
		aliasedAllowedColumns[k] = "m." + v // 添加表别名
	}
	db = applySortInfos(db, sortInfos, aliasedAllowedColumns)

	result := db.Find(&menus)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("根据角色ID列表查询菜单失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询菜单失败").WithCause(result.Error)
	}
	return menus, nil
}
