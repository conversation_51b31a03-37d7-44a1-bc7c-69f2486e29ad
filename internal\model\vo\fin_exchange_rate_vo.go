package vo

import (
	"backend/internal/model/entity"
	"time"

	"github.com/shopspring/decimal"
)

// CurrencyInfo 嵌套在汇率响应中的币种信息
type CurrencyInfo struct {
	ID   uint   `json:"id"`
	Code string `json:"code"`
	Name string `json:"name"`
}

// FinExchangeRateItemRes 单个汇率响应
type FinExchangeRateItemRes struct {
	BaseVO
	FromCurrencyID uint                `json:"fromCurrencyId"`
	ToCurrencyID   uint                `json:"toCurrencyId"`
	Rate           decimal.Decimal     `json:"rate"`
	RateDate       string              `json:"rateDate"`
	FromCurrency   *FinCurrencyItemRes `json:"fromCurrency"`
	ToCurrency     *FinCurrencyItemRes `json:"toCurrency"`
}

func FromExchangeRateEntity(e *entity.FinExchangeRate) *FinExchangeRateItemRes {
	if e == nil {
		return nil
	}
	return &FinExchangeRateItemRes{
		BaseVO:         BaseVO{ID: e.ID, CreatedAt: e.CreatedAt, UpdatedAt: e.UpdatedAt},
		FromCurrencyID: e.FromCurrencyID,
		ToCurrencyID:   e.ToCurrencyID,
		Rate:           e.Rate,
		RateDate:       e.RateDate.Format("2006-01-02"),
		FromCurrency:   FromCurrencyEntity(&e.FromCurrency),
		ToCurrency:     FromCurrencyEntity(&e.ToCurrency),
	}
}

func FromExchangeRateEntities(entities []*entity.FinExchangeRate) []*FinExchangeRateItemRes {
	res := make([]*FinExchangeRateItemRes, len(entities))
	for i, e := range entities {
		res[i] = FromExchangeRateEntity(e)
	}
	return res
}

// FinExchangeRateLatestRes 最新汇率响应
type FinExchangeRateLatestRes struct {
	FromCode string          `json:"fromCode"`
	ToCode   string          `json:"toCode"`
	Rate     decimal.Decimal `json:"rate"`
	RateDate time.Time       `json:"rateDate"`
}
