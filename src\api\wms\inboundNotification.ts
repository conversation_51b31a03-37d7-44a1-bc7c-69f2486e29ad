import request from '@/utils/request'

// ==================== 类型定义 ====================

// 分页结果类型
export interface PageResult<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

// 入库通知单状态枚举
export type WmsInboundNotificationStatus = 
  | 'DRAFT'           // 草稿
  | 'PLANNED'         // 已计划
  | 'ARRIVED'         // 已到货
  | 'RECEIVING'       // 收货中
  | 'PARTIALLY_RECEIVED' // 部分收货
  | 'RECEIVED'        // 收货完成
  | 'CLOSED'          // 已关闭
  | 'CANCELLED'       // 已取消

// 通知类型枚举
export type WmsInboundNotificationType = 
  | 'PO_RECEIPT'        // 采购收货
  | 'RETURN_RECEIPT'    // 退货入库
  | 'TRANSFER_RECEIPT'  // 调拨入库
  | 'PRODUCTION_RECEIPT' // 生产入库
  | 'OTHER_RECEIPT'     // 其他入库

// 入库通知单明细创建请求
export interface WmsInboundNotificationDetailCreateReq {
  lineNo: number
  itemId: number
  expectedQuantity: number
  unitOfMeasure: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
}

// 入库通知单明细更新请求
export interface WmsInboundNotificationDetailUpdateReq {
  id?: number
  lineNo?: number
  itemId?: number
  expectedQuantity?: number
  unitOfMeasure?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
}

// 入库通知单创建请求
export interface WmsInboundNotificationCreateReq {
  notificationNo?: string
  notificationType: WmsInboundNotificationType
  expectedArrivalDate: string
  clientId: number
  warehouseId: number
  supplierShipper?: string
  sourceDocNo?: string
  remark?: string
  details: WmsInboundNotificationDetailCreateReq[]
}

// 入库通知单更新请求
export interface WmsInboundNotificationUpdateReq {
  notificationNo?: string
  notificationType?: WmsInboundNotificationType
  expectedArrivalDate?: string
  clientId?: number
  warehouseId?: number
  supplierShipper?: string
  sourceDocNo?: string
  remark?: string
  details?: WmsInboundNotificationDetailUpdateReq[]
}

// 入库通知单查询请求
export interface WmsInboundNotificationQueryReq {
  pageNum?: number
  pageSize?: number
  notificationNo?: string
  notificationType?: WmsInboundNotificationType
  status?: WmsInboundNotificationStatus
  statuses?: WmsInboundNotificationStatus[]
  clientId?: number
  warehouseId?: number
  expectedArrivalDateFrom?: string
  expectedArrivalDateTo?: string
  createdAtFrom?: string
  createdAtTo?: string
  sort?: string
  includeDetails?: boolean
}

// 入库通知单明细响应
export interface WmsInboundNotificationDetailResp {
  id: number
  createdAt: string
  updatedAt: string
  tenantId: number
  accountBookId: number
  notificationId: number
  lineNo: number
  itemId: number
  expectedQuantity: number
  unitOfMeasure: string
  packageQty?: number     // 新增：包装数量
  packageUnit?: string    // 新增：包装单位
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  remark?: string
  // 扩展字段
  itemSku?: string
  itemName?: string
  specification?: string
}

// 入库通知单响应
export interface WmsInboundNotificationResp {
  id: number
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  tenantId: number
  accountBookId: number
  notificationNo: string
  notificationType: WmsInboundNotificationType
  warehouseId: number
  clientId: number
  sourceDocNo?: string
  supplierShipper?: string
  expectedArrivalDate?: string
  status: WmsInboundNotificationStatus
  remark?: string
  // 关联信息
  details?: WmsInboundNotificationDetailResp[]
  clientName?: string
  warehouseName?: string
  supplierName?: string
}

// 状态更新请求
export interface WmsInboundNotificationUpdateStatusReq {
  status: WmsInboundNotificationStatus
  remark?: string
}

// 批量导入请求
export interface WmsInboundNotificationBatchImportReq {
  notifications: WmsInboundNotificationCreateReq[]
  overwriteExisting?: boolean
}

// 批量导入结果
export interface BatchImportResult {
  totalCount: number
  successCount: number
  failureCount: number
  successIds: number[]
  failureReasons: string[]
}

// ==================== API接口函数 ====================

// 基础CRUD操作
export const getInboundNotificationPage = (params: WmsInboundNotificationQueryReq): Promise<PageResult<WmsInboundNotificationResp>> => {
  return request<PageResult<WmsInboundNotificationResp>>({
    url: '/wms/inbound-notifications',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<WmsInboundNotificationResp>>
}

export const getInboundNotificationDetail = (id: number): Promise<WmsInboundNotificationResp> => {
  return request<WmsInboundNotificationResp>({
    url: `/wms/inbound-notifications/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsInboundNotificationResp>
}

export const createInboundNotification = (data: WmsInboundNotificationCreateReq): Promise<WmsInboundNotificationResp> => {
  return request<WmsInboundNotificationResp>({
    url: '/wms/inbound-notifications',
    method: 'post',
    data
  }) as unknown as Promise<WmsInboundNotificationResp>
}

export const updateInboundNotification = (id: number, data: WmsInboundNotificationUpdateReq): Promise<WmsInboundNotificationResp> => {
  return request<WmsInboundNotificationResp>({
    url: `/wms/inbound-notifications/${id}`,
    method: 'put',
    data
  }) as unknown as Promise<WmsInboundNotificationResp>
}

export const deleteInboundNotification = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/inbound-notifications/${id}`,
    method: 'delete'
  }) as unknown as Promise<void>
}

// 业务操作
export const updateInboundNotificationStatus = (id: number, data: WmsInboundNotificationUpdateStatusReq): Promise<void> => {
  return request<void>({
    url: `/wms/inbound-notifications/${id}/status`,
    method: 'put',
    data
  }) as unknown as Promise<void>
}

export const batchImportInboundNotification = (data: WmsInboundNotificationBatchImportReq) => {
  return request<BatchImportResult>({
    url: '/wms/inbound-notifications/batch-import',
    method: 'post',
    data
  }) as unknown as Promise<BatchImportResult>
}

export const generateReceivingFromNotification = (id: number, data: { receivedDate: string; remarks?: string }) => {
  return request<any>({
    url: `/wms/receiving-records/from-notification/${id}`,
    method: 'post',
    data
  }) as unknown as Promise<any>
}

// 批量操作接口
export const batchDeleteInboundNotifications = (ids: number[]): Promise<void> => {
  return request<void>({
    url: '/wms/inbound-notifications/batch-delete',
    method: 'delete',
    data: { ids }
  }) as unknown as Promise<void>
}

export const batchUpdateInboundNotificationStatus = (data: { ids: number[]; status: WmsInboundNotificationStatus; remark?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/inbound-notifications/batch-status',
    method: 'put',
    data
  }) as unknown as Promise<void>
}

// 导出接口
export const exportInboundNotifications = (params: WmsInboundNotificationQueryReq): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/inbound-notifications/export',
    method: 'get',
    params,
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

// 获取导入模板
export const downloadImportTemplate = (): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/inbound-notifications/import-template',
    method: 'get',
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

// 辅助接口 - 这些可能需要根据实际项目中的API路径调整
export const getClientPageV2 = (params: { pageSize?: number; pageNum?: number; name?: string }): Promise<PageResult<{ id: number; name: string; code: string }>> => {
  return request<PageResult<{ id: number; name: string; code: string }>>({
    url: '/crm/customers',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<{ id: number; name: string; code: string }>>
}

export const getWarehouseList = (): Promise<{ id: number; name: string; code: string }[]> => {
  return request<{ id: number; name: string; code: string }[]>({
    url: '/wms/locations/list',
    method: 'get'
  }) as unknown as Promise<{ id: number; name: string; code: string }[]>
}

export const getSupplierList = (params: { pageSize?: number; pageNum?: number; name?: string }): Promise<PageResult<{ id: number; name: string; code: string }>> => {
  return request<PageResult<{ id: number; name: string; code: string }>>({
    url: '/scm/suppliers',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<{ id: number; name: string; code: string }>>
}

// 物料相关接口
export interface WmsItemRespV2 {
  id: number
  sku: string
  name: string
  specification?: string
  groupCode?: string
  groupName?: string
  categoryCode?: string
  categoryName?: string
  baseUnit: string
  shelfLifeDays?: number
  batchManaged: boolean
  serialManaged: boolean
  status: 'ACTIVE' | 'INACTIVE'
  weightKg?: number
  volumeM3?: number
  lengthM?: number
  widthM?: number
  heightM?: number
  defaultLocationId?: number
  defaultLocationName?: string
}

export const getItemPageV2 = (params: { 
  pageSize?: number
  pageNum?: number
  sku?: string
  name?: string
  status?: string
  groupCode?: string
  categoryCode?: string
}): Promise<PageResult<WmsItemRespV2>> => {
  return request<PageResult<WmsItemRespV2>>({
    url: '/wms/items',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<WmsItemRespV2>>
}

// 字典数据接口
export const getDictDataByCode = (code: string) => {
  return request<{ value: string; label: string }[]>({
    url: `/sys/dict/items/list`,
    method: 'get',
    params: { typeCode: code }
  })
} 