import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import type {
  WmsInventoryQueryReq,
  WmsInventoryPageVO,
  WmsInventoryVO,
  WmsInventoryDetailVO,
  InventoryStatus
} from '@/types/wms/inventory'
import {
  getInventoryPage,
  getInventoryDetail,
  getInventoryByLocation,
  getInventoryByItem,
  getInventorySummary,
  checkInventoryAvailability,
  getWarehouseOptions,
  getLocationOptions,
  getItemOptions
} from '@/api/wms/inventoryQuery'
import { ElMessage } from 'element-plus'

export const useInventoryQueryStore = defineStore('inventoryQuery', () => {
  // ==================== 状态定义 ====================
  
  // 库存列表数据
  const list = ref<WmsInventoryVO[]>([])
  const total = ref(0)
  const loading = ref(false)
  
  // 搜索表单
  const searchForm = reactive<WmsInventoryQueryReq>({
    pageNum: 1,
    pageSize: 20,
    warehouseId: undefined,
    locationId: undefined,
    itemId: undefined,
    itemSku: '',
    itemName: '',
    batchNo: '',
    status: undefined,
    quantityMin: undefined,
    quantityMax: undefined,
    expiryDateStart: undefined,
    expiryDateEnd: undefined,
    includeZero: false,
    onlyAvailable: false
  })
  
  // 分页配置
  const pagination = computed(() => ({
    current: searchForm.pageNum || 1,
    pageSize: searchForm.pageSize || 20,
    total: total.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => 
      `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
  }))
  
  // 选项数据
  const warehouseOptions = ref<any[]>([])
  const locationOptions = ref<any[]>([])
  const itemOptions = ref<any[]>([])
  
  // 库存统计数据
  const inventoryStats = reactive({
    totalItems: 0,
    totalQuantity: 0,
    availableQuantity: 0,
    frozenQuantity: 0,
    allocatedQuantity: 0
  })
  
  // 当前选中的库存详情
  const currentInventoryDetail = ref<WmsInventoryDetailVO | null>(null)
  const detailLoading = ref(false)
  
  // ==================== 计算属性 ====================
  
  // 库存状态选项
  const statusOptions = computed(() => [
    { label: '可用', value: InventoryStatus.AVAILABLE },
    { label: '待检', value: InventoryStatus.QUALITY_INSPECTION },
    { label: '冻结', value: InventoryStatus.HOLD },
    { label: '质检冻结', value: InventoryStatus.FROZEN_QC },
    { label: '盘点冻结', value: InventoryStatus.FROZEN_COUNT },
    { label: '客户冻结', value: InventoryStatus.FROZEN_CUSTOMER },
    { label: '损坏', value: InventoryStatus.DAMAGED },
    { label: '过期', value: InventoryStatus.EXPIRED },
    { label: '已分配', value: InventoryStatus.ALLOCATED },
    { label: '待上架', value: InventoryStatus.PENDING_PUTAWAY },
    { label: '待拣货', value: InventoryStatus.PENDING_PICK },
    { label: '移动中', value: InventoryStatus.IN_TRANSIT },
    { label: '打包中', value: InventoryStatus.PACKING }
  ])
  
  // 是否有搜索条件
  const hasSearchConditions = computed(() => {
    return !!(
      searchForm.warehouseId ||
      searchForm.locationId ||
      searchForm.itemId ||
      searchForm.itemSku ||
      searchForm.itemName ||
      searchForm.batchNo ||
      searchForm.status ||
      searchForm.quantityMin !== undefined ||
      searchForm.quantityMax !== undefined ||
      searchForm.expiryDateStart ||
      searchForm.expiryDateEnd
    )
  })
  
  // ==================== 方法定义 ====================
  
  /**
   * 获取库存分页数据
   */
  const fetchList = async () => {
    try {
      loading.value = true
      const response = await getInventoryPage(searchForm)
      
      if (response.success) {
        list.value = response.data.list
        total.value = response.data.total
        
        // 更新统计数据
        updateInventoryStats()
      } else {
        ElMessage.error(response.message || '获取库存数据失败')
      }
    } catch (error) {
      console.error('获取库存数据失败:', error)
      ElMessage.error('获取库存数据失败')
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取库存详情
   */
  const fetchDetail = async (id: number) => {
    try {
      detailLoading.value = true
      const response = await getInventoryDetail(id)
      
      if (response.success) {
        currentInventoryDetail.value = response.data
        return response.data
      } else {
        ElMessage.error(response.message || '获取库存详情失败')
        return null
      }
    } catch (error) {
      console.error('获取库存详情失败:', error)
      ElMessage.error('获取库存详情失败')
      return null
    } finally {
      detailLoading.value = false
    }
  }
  
  /**
   * 按库位查询库存
   */
  const fetchByLocation = async (locationId: number) => {
    try {
      const response = await getInventoryByLocation(locationId)
      
      if (response.success) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取库位库存失败')
        return []
      }
    } catch (error) {
      console.error('获取库位库存失败:', error)
      ElMessage.error('获取库位库存失败')
      return []
    }
  }
  
  /**
   * 按物料查询库存
   */
  const fetchByItem = async (itemId: number) => {
    try {
      const response = await getInventoryByItem(itemId)
      
      if (response.success) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取物料库存失败')
        return []
      }
    } catch (error) {
      console.error('获取物料库存失败:', error)
      ElMessage.error('获取物料库存失败')
      return []
    }
  }
  
  /**
   * 获取库存汇总统计
   */
  const fetchSummary = async (params: any) => {
    try {
      const response = await getInventorySummary(params)
      
      if (response.success) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取库存汇总失败')
        return null
      }
    } catch (error) {
      console.error('获取库存汇总失败:', error)
      ElMessage.error('获取库存汇总失败')
      return null
    }
  }
  
  /**
   * 检查库存可用性
   */
  const checkAvailability = async (params: any) => {
    try {
      const response = await checkInventoryAvailability(params)
      
      if (response.success) {
        return response.data
      } else {
        ElMessage.error(response.message || '检查库存可用性失败')
        return null
      }
    } catch (error) {
      console.error('检查库存可用性失败:', error)
      ElMessage.error('检查库存可用性失败')
      return null
    }
  }
  
  /**
   * 获取仓库选项
   */
  const fetchWarehouseOptions = async () => {
    try {
      const response = await getWarehouseOptions()
      
      if (response.success) {
        warehouseOptions.value = response.data
      } else {
        console.error('获取仓库选项失败:', response.message)
      }
    } catch (error) {
      console.error('获取仓库选项失败:', error)
    }
  }
  
  /**
   * 获取库位选项
   */
  const fetchLocationOptions = async (warehouseId?: number) => {
    try {
      const response = await getLocationOptions(warehouseId)
      
      if (response.success) {
        locationOptions.value = response.data
      } else {
        console.error('获取库位选项失败:', response.message)
      }
    } catch (error) {
      console.error('获取库位选项失败:', error)
    }
  }
  
  /**
   * 获取物料选项
   */
  const fetchItemOptions = async (keyword?: string) => {
    try {
      const response = await getItemOptions(keyword)
      
      if (response.success) {
        itemOptions.value = response.data
      } else {
        console.error('获取物料选项失败:', response.message)
      }
    } catch (error) {
      console.error('获取物料选项失败:', error)
    }
  }
  
  /**
   * 重置搜索表单
   */
  const resetSearchForm = () => {
    Object.assign(searchForm, {
      pageNum: 1,
      pageSize: 20,
      warehouseId: undefined,
      locationId: undefined,
      itemId: undefined,
      itemSku: '',
      itemName: '',
      batchNo: '',
      status: undefined,
      quantityMin: undefined,
      quantityMax: undefined,
      expiryDateStart: undefined,
      expiryDateEnd: undefined,
      includeZero: false,
      onlyAvailable: false
    })
  }
  
  /**
   * 更新库存统计数据
   */
  const updateInventoryStats = () => {
    const stats = {
      totalItems: list.value.length,
      totalQuantity: 0,
      availableQuantity: 0,
      frozenQuantity: 0,
      allocatedQuantity: 0
    }
    
    list.value.forEach(item => {
      stats.totalQuantity += item.quantity
      stats.availableQuantity += item.availableQty
      stats.frozenQuantity += item.frozenQty
      stats.allocatedQuantity += item.allocatedQty
    })
    
    Object.assign(inventoryStats, stats)
  }
  
  /**
   * 设置分页参数
   */
  const setPagination = (pageNum: number, pageSize: number) => {
    searchForm.pageNum = pageNum
    searchForm.pageSize = pageSize
  }
  
  /**
   * 清空当前数据
   */
  const clearData = () => {
    list.value = []
    total.value = 0
    currentInventoryDetail.value = null
    Object.assign(inventoryStats, {
      totalItems: 0,
      totalQuantity: 0,
      availableQuantity: 0,
      frozenQuantity: 0,
      allocatedQuantity: 0
    })
  }
  
  // ==================== 返回状态和方法 ====================
  
  return {
    // 状态
    list,
    total,
    loading,
    searchForm,
    pagination,
    warehouseOptions,
    locationOptions,
    itemOptions,
    inventoryStats,
    currentInventoryDetail,
    detailLoading,
    
    // 计算属性
    statusOptions,
    hasSearchConditions,
    
    // 方法
    fetchList,
    fetchDetail,
    fetchByLocation,
    fetchByItem,
    fetchSummary,
    checkAvailability,
    fetchWarehouseOptions,
    fetchLocationOptions,
    fetchItemOptions,
    resetSearchForm,
    updateInventoryStats,
    setPagination,
    clearData
  }
})
