<template>
  <VNFrame 
    :sidebar-items="menuItems"
    :breadcrumb-items="breadcrumbItems"
    :status-bar-items="statusBarItems"
    v-model:sidebarCollapsed="sidebarCollapsed"
    :breadcrumb-height="'45px'" 
    status-bar-left-title="常规信息"
    status-bar-center-title="进度与状态"
    status-bar-right-title="账户与通知"
  >
    <!-- Main content area of the example page -->
    <div class="frame-example-content">
        <h1>VNFrame 布局组件示例页 (新布局)</h1>
        <p>这个页面本身就是由 VNFrame 组件构建的，采用经典的 **左右布局**。</p>
        <el-alert title="内容区域" type="success" :closable="false">
            这里是主内容区域 (VNFrame 的默认 slot)，它会自动处理垂直滚动。
        </el-alert>

         <div style="margin-top: 20px;">
             <el-button @click="sidebarCollapsed = !sidebarCollapsed">
                 切换侧边栏折叠状态 (v-model)
             </el-button>
            <p>当前侧边栏状态: {{ sidebarCollapsed ? '折叠' : '展开' }}</p>
            <p>(折叠按钮现在位于面包屑左侧)</p>
        </div>

        <div style="margin-top: 20px;">
            <h3>传递给子组件的数据:</h3>
            <p><strong>侧边栏菜单 (sidebarItems):</strong></p>
            <pre>{{ JSON.stringify(menuItems, null, 2) }}</pre>
             <p><strong>面包屑 (breadcrumbItems):</strong></p>
            <pre>{{ JSON.stringify(breadcrumbItems, null, 2) }}</pre>
             <p><strong>状态栏 (statusBarItems):</strong> (样式已调整)</p>
            <pre>{{ JSON.stringify(statusBarItemsForDisplay, null, 2) }}</pre>
        </div>
        
        <!-- Add more content to test scrolling -->
        <div style="margin-top: 30px;">
            <h3>更多内容测试滚动:</h3>
            <p v-for="i in 30" :key="i">这是第 {{ i }} 行滚动测试内容...</p>
        </div>

    </div>
  </VNFrame>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue';
import VNFrame from './index.vue'; // Import the frame component itself
import type { SidebarItem } from '../VNSidebar/types';
import type { BreadcrumbItem } from '../VNBreadcrumb/types';
import type { StatusBarItem } from '../VNStatusBar/types';
import { HomeFilled, Setting, User, Bell, Check, Loading } from '@element-plus/icons-vue';
import { ElButton, ElAlert, ElProgress, ElTag } from 'element-plus';

const sidebarCollapsed = ref(false);

// --- Data for child components --- 
const menuItems = ref<SidebarItem[]>([
  { label: '仪表盘', icon: HomeFilled, index: '/dashboard', to: { path: '/dashboard' } },
  { label: '系统设置', icon: Setting, index: '/settings', to: { path: '/settings' } },
  { 
      label: '用户中心', 
      icon: User, 
      index: '/profile', 
      children: [
          { label: '个人信息', index: '/profile/info', to: { path: '/profile/info' } },
          { label: '修改密码', index: '/profile/password', to: { path: '/profile/password' } }
      ]
   },
]);

// Usually generated based on vue-router route meta
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
    { label: '首页', to: '/' },
    { label: '组件示例' },
    { label: 'VNFrame 示例' }
]);

// Helper function to create a Progress VNode
const createProgress = (percentage: number) => {
  return h(ElProgress, {
    percentage: percentage,
    strokeWidth: 8,
    textInside: true,
    color: '#409EFF',
    style: { width: '100px', verticalAlign: 'middle' }
  });
};

// Adjusted status bar items to match the image layout
const statusBarItems = ref<StatusBarItem[]>([
    // Left section
    { id: 'status', icon: Check, label: '状态', value: '就绪', align: 'left' },
    { id: 'version', label: '版本', value: '1.0.2', align: 'left' },
    // Center section
    { id: 'progress', value: createProgress(60), align: 'center', separator: false }, // Assuming progress bar is in the center
    // Right section
    { id: 'user', icon: User, value: 'Admin', align: 'right' },
    {
      id: 'notifications',
      icon: Bell,
      value: h(ElTag, { type: 'danger', size: 'small', effect: 'dark', round: true }, () => '3'),
      align: 'right',
      tooltip: '3 条未读通知'
    }
]);

// Create a computed property for display that replaces VNodes
const statusBarItemsForDisplay = computed(() => {
    return statusBarItems.value.map(item => {
        // Check if the value is likely a VNode (basic check)
        const isVNode = typeof item.value === 'object' && item.value !== null && '__v_isVNode' in item.value;
        return {
            ...item,
            // Replace VNode with a placeholder string for stringification
            value: isVNode ? '[VNode]' : item.value,
            // Also replace icon if it's a component object
            icon: typeof item.icon === 'object' ? '[Icon Component]' : item.icon
        };
    });
});

</script>

<style scoped>
.frame-example-content {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
}

h1, h3 {
    margin-bottom: 15px;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  overflow: auto;
  margin-top: 5px;
  max-height: 150px;
}
p {
    margin: 10px 0;
}
</style> 