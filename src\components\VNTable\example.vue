<template>
  <div class="example-container vn-table-example">
    <h1>VNTable 组件全功能示例</h1>

    <!-- ==================== 1. 基础与常用功能 ==================== -->
    <section>
      <h3>1. 基础表格 (分页、排序、加载、序号、行点击)</h3>
      <VNTable
        ref="basicTableRef"
        :data="basicData"
        :columns="basicColumns"
        :loading="loading"
        :pagination="pagination"
        :selection-type="'multiple'"
        show-index
        row-key="id"
        highlight-current-row
        @selection-change="HandleSelectionChange('basic', $event)"
        @page-change="HandlePageChange"
        @page-size-change="HandlePageSizeChange"
        @sort-change="HandleSortChange"
        @row-click="HandleRowClick"
        @refresh="LoadData"
        :toolbar-config="{ refresh: true }"
      >
        <!-- 使用作用域插槽自定义状态列 -->
        <template #column-status="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '活跃' : '禁用' }}
          </el-tag>
        </template>
      </VNTable>
      <div class="example-controls">
        <el-button @click="GetSelectedRows('basic')">获取选中行</el-button>
        <el-button @click="ClearSelection('basic')">清空选中</el-button>
        <el-button @click="ToggleRowSelection('basic')">切换第一行选中</el-button>
      </div>
    </section>

    <!-- ==================== 2. 工具栏与操作 ==================== -->
    <section>
      <h3>2. 完整工具栏与自定义操作列</h3>
      <VNTable
        :data="basicData.slice(0, 8)"
        :columns="columnsForOps"
        :loading="loading"
        row-key="id"
        :show-toolbar="true"
        :toolbar-config="{
          refresh: true,
          add: true,
          batchDelete: true,
          filter: true,
          columnSetting: true,
          density: true,
          fullscreen: true,
          import: true,
          export: true,
          layoutSwitch: false // 在此示例中禁用布局切换
        }"
        :show-operations="true"
        :operation-buttons="operationButtons"
        :operation-width="180"
        :operation-fixed="'right'"
        @refresh="LoadData"
        @add="HandleAdd"
        @batch-delete="HandleBatchDelete"
        @import="HandleImport"
        @export="HandleExport"
        @filter="HandleFilterApply"
        @filter-clear="HandleFilterClear"
        @density-change="HandleDensityChange"
        @column-change="HandleColumnChange"
        @edit-action="HandleEditAction"
        @audit-action="HandleAuditAction"
      >
        <!-- 自定义工具栏左侧 -->
        <template #toolbar-left>
          <el-button type="success" size="small" @click="CustomToolbarAction">自定义按钮</el-button>
        </template>
         <!-- 自定义状态列 -->
         <template #column-status="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '活跃' : '禁用' }}
          </el-tag>
        </template>
        <!-- 覆盖默认操作列 -->
        <template #operation="{ row }">
           <el-button link type="primary" @click="HandleView(row)">查看(插槽)</el-button>
           <el-button link type="danger" v-if="row.status !== 'active'" @click="HandleActivate(row)">激活</el-button>
        </template>
      </VNTable>
    </section>

    <!-- ==================== 3. 编辑与验证 ==================== -->
    <section>
      <h3>3. 行编辑与验证</h3>
      <div class="example-controls">
        <el-button type="primary" @click="ValidateEditableTable">验证整个表格</el-button>
        <el-button @click="ResetEditableTable">重置表格状态</el-button>
      </div>
      <VNTable
        ref="editableTableRef"
        :data="editableData"
        :columns="editableColumnsWithRules"
        :loading="loading"
        :editable="true"
        edit-mode="row"
        :show-operations="true"
        row-key="id"
        :operation-buttons="editableOperationButtons"
        :toolbar-config="{ add: true }"
        @row-save="HandleRowSave"
        @row-cancel="HandleRowCancel"
        @add="HandleAddEditableRow"
        @row-validate-complete="HandleRowValidateComplete"
        @validate-error="HandleValidateError"
      >
      </VNTable>
    </section>

    <!-- ==================== 4. 树形表格 ==================== -->
    <section>
      <h3>4. 树形表格</h3>
      <VNTable
        ref="treeTableRef"
        :data="treeData"
        :columns="treeColumns"
        :loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
        :toolbar-config="{
          refresh: true,
          expandAll: true,
          collapseAll: true
        }"
        @refresh="LoadData"
      />
      <div class="example-controls">
        <el-button @click="treeTableRef?.ExpandAll()">展开全部(方法)</el-button>
        <el-button @click="treeTableRef?.CollapseAll()">折叠全部(方法)</el-button>
        <el-button @click="ToggleTreeRowExpansion">切换第一行展开</el-button>
      </div>
    </section>

    <!-- ==================== 5. 多布局视图 ==================== -->
     <section>
      <h3>5. 多布局视图 (表格/卡片/列表)</h3>
      <VNTable
        :data="basicData"
        :columns="layoutColumns"
        :loading="loading"
        :pagination="pagination"
        :layout-mode="currentLayout"
        :layout-modes="['table', 'card', 'list']"
        :show-layout-switch="true"
        :card-config="{
          column: 3,
          headerField: 'name',
          descriptionField: 'description',
          showImage: false, // 假设没有图片字段
          showFooter: true, // 显示底部操作
          hoverable: true
        }"
        :list-config="{
          titleField: 'name',
          subtitleField: 'email',
          descriptionField: 'address',
          showImage: false,
          showDivider: true
        }"
        :toolbar-config="{
          refresh: true,
          filter: true,
          layoutSwitch: true,
          fullscreen: true
        }"
        row-key="id"
        @layout-change="HandleLayoutChange"
        @page-change="HandlePageChange"
        @page-size-change="HandlePageSizeChange"
      >
         <!-- 卡片底部插槽 -->
         <template #card-footer="{ row }">
            <el-button size="small" @click="HandleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="HandleEditAction(row)">编辑</el-button>
         </template>
         <!-- 列表操作插槽 -->
         <template #list-actions="{ row }">
            <el-button link type="primary" @click="HandleView(row)">详情</el-button>
            <el-button link type="danger" @click="HandleDelete(row)">删除</el-button>
         </template>
      </VNTable>
    </section>

     <!-- ==================== 6. 展开行 ==================== -->
    <section>
       <h3>6. 展开行</h3>
       <VNTable
         :data="basicData.slice(0, 4)"
         :columns="expandColumns"
         row-key="id"
       >
         <!-- 状态列 -->
         <template #column-status="{ row }">
           <el-tag :type="row.status === 'active' ? 'success' : 'info'">
             {{ row.status === 'active' ? '活跃' : '禁用' }}
           </el-tag>
         </template>
         <!-- 展开行插槽 -->
         <template #expand="{ row }">
           <div class="expand-content">
             <h4>详细信息 - {{ row.name }} (ID: {{ row.id }})</h4>
             <p><strong>地址:</strong> {{ row.address }}</p>
             <p><strong>邮箱:</strong> {{ row.email }}</p>
             <p><strong>描述:</strong> {{ row.description }}</p>
           </div>
         </template>
       </VNTable>
    </section>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue';
import VNTable from './index.vue';
import type { VNTableProps, TableColumn, PaginationConfig, ActionButton, VNTableMethods, VNTableEmits, LayoutMode } from './types';
import { ElMessage, ElMessageBox, ElTag, ElInputNumber, ElButton } from 'element-plus';
import { View, Edit, Delete, Check, Close } from '@element-plus/icons-vue';
import type { FormItemRule } from 'element-plus';

// 表格 Ref
const basicTableRef = ref<InstanceType<typeof VNTable>>();
const editableTableRef = ref<InstanceType<typeof VNTable>>();
const treeTableRef = ref<InstanceType<typeof VNTable>>();

// 加载状态
const loading = ref(false);

// 布局模式
const currentLayout = ref<LayoutMode>('table');

// ==================== 数据 ====================
const basicData = ref<any[]>([]);
const editableData = ref<any[]>([]);
const treeData = ref<any[]>([]);

// 分页配置
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 5,
  pageSizes: [5, 10, 20, 50],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true
});

// ==================== 列定义 ====================

// 基础列
const basicColumns = ref<TableColumn[]>([
  { type: 'selection', prop: '__selection__', label: '选择', width: 50, fixed: 'left' },
  { prop: 'name', label: '姓名', minWidth: 120, sortable: true, important: true, filterable: true, filterType: 'text' },
  { prop: 'age', label: '年龄', width: 80, sortable: true, filterable: true, filterType: 'number'},
  { prop: 'email', label: '邮箱', minWidth: 180, filterable: true },
  {
    prop: 'status', label: '状态', width: 100, slot: true, // 使用插槽渲染
    filterable: true, filterType: 'select',
    filterOptions: [{ label: '活跃', value: 'active' }, { label: '禁用', value: 'disabled' }]
  },
  { prop: 'createTime', label: '创建时间', width: 150, filterable: true, filterType: 'date' }
]);

// 工具栏/操作示例列
const columnsForOps = ref<TableColumn[]>([
 { type: 'selection', prop: '__selection__', label: '选择', width: 50 },
 { prop: 'name', label: '姓名', width: 120, filterable: true },
 { prop: 'age', label: '年龄', width: 80, filterable: true, filterType: 'number' },
 { prop: 'status', label: '状态', width: 100, slot: true, filterable: true, filterType: 'select', filterOptions: [{ label: '活跃', value: 'active' }, { label: '禁用', value: 'disabled' }] },
 { prop: 'email', label: '邮箱', minWidth: 180, filterable: true },
]);

// 可编辑列与规则
const editableColumnsWithRules = ref<TableColumn[]>([
  {
    prop: 'name', label: '物品名称', editable: true,
    rules: [
      { required: true, message: '物品名称不能为空', trigger: 'blur' },
      { min: 2, message: '名称长度至少2个字符', trigger: 'blur' }
    ]
  },
  {
    prop: 'quantity', label: '数量', width: 120, editable: true, 
    editComponent: 'input-number',
    editComponentProps: { 
      min: 0,
      precision: 0
    },
    rules: [
      { required: true, message: '数量不能为空', trigger: 'change' },
      { type: 'number', min: 1, message: '数量必须大于0', trigger: 'change' }
    ]
  },
  {
    prop: 'price', label: '单价', width: 150, editable: true, 
    editComponent: 'input-number',
    editComponentProps: { 
        precision: 2, 
        step: 0.1, 
        min: 0, 
        controlsPosition: 'right',
        size: 'small', 
        style: 'width: 100%' 
    },
    rules: [
      { required: true, message: '单价不能为空', trigger: 'change' },
      { type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'change' }
    ]
  },
  {
    prop: 'type', label: '类型', width: 130, editable: true, editComponent: 'select',
    editComponentProps: {
      options: [ { label: '类型A', value: 'A' }, { label: '类型B', value: 'B' } ]
    },
    rules: [ { required: true, message: '请选择类型', trigger: 'change' } ]
  },
  { prop: 'isActive', label: '激活', width: 80, editable: true, editComponent: 'switch' },
  { prop: 'expireDate', label: '过期日期', width: 150, editable: true, editComponent: 'datepicker' }
]);

// 树形列
const treeColumns = ref<TableColumn[]>([
  { prop: 'name', label: '部门名称', minWidth: 180 },
  { prop: 'code', label: '部门代码', width: 120 },
  { prop: 'manager', label: '负责人', width: 100 },
  { prop: 'description', label: '描述', minWidth: 200 }
]);

// 多布局列
const layoutColumns = ref<TableColumn[]>([
 { type: 'selection', prop: '__selection__', label: '选择', width: 50 },
 { prop: 'name', label: '姓名', width: 120, sortable: true },
 { prop: 'age', label: '年龄', width: 80, sortable: true },
 { prop: 'status', label: '状态', width: 100, formatter: (row) => row.status === 'active' ? '活跃' : '禁用' }, // Formatter 返回 string
 { prop: 'email', label: '邮箱', minWidth: 180 },
 { prop: 'address', label: '地址', minWidth: 200 },
 { prop: 'description', label: '描述', minWidth: 250, showOverflowTooltip: true },
]);

// 展开行列
const expandColumns = ref<TableColumn[]>([
  { type: 'expand', prop: '__expand__', label: '展开', width: 50 },
  { prop: 'name', label: '姓名', width: 150 },
  { prop: 'age', label: '年龄', width: 80 },
  { prop: 'status', label: '状态', slot: true, width: 100 },
  { prop: 'createTime', label: '创建时间', minWidth: 150 },
]);

// ==================== 操作按钮 ====================
const operationButtons = ref<ActionButton[]>([
  { label: '查看', icon: 'View', handler: (row) => HandleView(row) },
  { label: '编辑', icon: 'Edit', type: 'primary', action: 'edit-action' },
  { label: '审核', icon: 'Check', type: 'success', action: 'audit-action', hidden: (row) => row.status === 'active' },
  {
    label: '删除', icon: 'Delete', type: 'danger',
    disabled: (row) => row.name.includes('Admin'), // "Admin" 用户不可删除
    handler: async (row) => {
       try {
         await ElMessageBox.confirm(`确定删除 ${row.name} 吗?`, '确认删除', { type: 'warning' });
         HandleDelete(row);
       } catch { ElMessage.info('取消删除'); }
    }
  }
]);

const editableOperationButtons = ref<ActionButton[]>([
 { label: '保存', action: 'save', type: 'primary', icon: 'Check' },
 { label: '取消', action: 'cancel', icon: 'Close' }
]);


// ==================== 模拟数据生成 ====================
const GenerateBasicData = (count = 25) => {
  const data = [];
  for (let i = 1; i <= count; i++) {
    data.push({
      id: i,
      name: `用户 ${String.fromCharCode(65 + i % 26)}${i}`,
      age: 20 + Math.floor(Math.random() * 40),
      email: `user${i}@example.com`,
      status: Math.random() > 0.4 ? 'active' : 'disabled',
      createTime: `2024-0${Math.floor(Math.random() * 9) + 1}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      address: `城市 ${i % 5 + 1} 区 测试路 ${i * 10} 号`,
      description: `这是用户 ${i} 的描述信息，用于测试长文本和不同布局下的显示效果。`,
      isProtected: i % 10 === 1 // 模拟某些行受保护
    });
  }
  return data;
};

const GenerateEditableData = () => [
  { id: 1, name: '苹果', quantity: 10, price: 5.5, type: 'A', isActive: true, expireDate: '2024-12-31' },
  { id: 2, name: '香蕉', quantity: null, price: 3.0, type: 'B', isActive: false, expireDate: null }, // 数量为空，触发验证
  { id: 3, name: '橙子', quantity: 5, price: 0, type: 'A', isActive: true, expireDate: '2024-11-30' }, // 单价为0，触发验证
  { id: 4, name: '草莓', quantity: 20, price: 15.8, type: null, isActive: true, expireDate: null }, // 类型为空，触发验证
];

const GenerateTreeData = () => [
  {
    id: 1, name: '总部', code: 'HQ', manager: '张总', description: '公司总部',
    children: [
      { id: 11, name: '研发部', code: 'RD', manager: '李工', description: '核心研发团队' },
      {
        id: 12, name: '市场部', code: 'MKT', manager: '王经理', description: '市场推广与销售',
        children: [
          { id: 121, name: '市场一部', code: 'MKT-1', manager: '赵主管', description: '负责区域A' },
          { id: 122, name: '市场二部', code: 'MKT-2', manager: '孙主管', description: '负责区域B' }
        ]
      }
    ]
  },
  { id: 2, name: '华东分部', code: 'EC', manager: '刘总', description: '负责华东区域业务' },
];

// ==================== 事件处理 ====================
const LoadData = async () => {
  loading.value = true;
  console.log(`加载数据 - 页码: ${pagination.currentPage}, 每页数量: ${pagination.pageSize}`);
  await new Promise(resolve => setTimeout(resolve, 500)); // 模拟 API 延迟
  const total = 53; // 模拟 API 返回的总数
  const data = GenerateBasicData(total);
  // 模拟后端分页
  const start = (pagination.currentPage - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  basicData.value = data.slice(start, end);
  pagination.total = total;

  // 为简化示例，同时加载其他数据类型
  if (editableData.value.length === 0) {
     editableData.value = GenerateEditableData();
  }
   if (treeData.value.length === 0) {
     treeData.value = GenerateTreeData();
   }

  loading.value = false;
  console.log('数据加载完成。');
};

const HandleSelectionChange = (tableName: string, rows: any[]) => {
  console.log(`[${tableName}] 选择变更:`, rows.map(r => r.id));
};

const HandlePageChange = (page: number) => {
  pagination.currentPage = page;
  LoadData();
};

const HandlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  LoadData();
};

const HandleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  console.log('排序变更:', prop, order);
  // 如果使用后端排序，在此处添加排序参数到 LoadData
  LoadData();
};

const HandleRowClick = (row: any) => {
  console.log('行点击:', row);
};

const HandleView = (row: any) => { ElMessage.info(`查看: ${row.name}`); };
const HandleDelete = (row: any) => {
    // 查找索引并移除 (模拟 API 成功)
    const index = basicData.value.findIndex(item => item.id === row.id);
    if (index !== -1) {
        basicData.value.splice(index, 1);
        ElMessage.success(`删除成功: ${row.name}`);
        pagination.total--; // 调整总数
    }
};
const HandleActivate = (row: any) => { ElMessage.success(`激活: ${row.id}`); row.status = 'active'; };

const HandleAdd = () => { ElMessage.info('点击了工具栏新增按钮'); };
const HandleBatchDelete = (selectedRows: any[]) => {
  if (!selectedRows || selectedRows.length === 0) {
    ElMessage.warning('请先选择要删除的行');
    return;
  }
  ElMessageBox.confirm(`确定删除选中的 ${selectedRows.length} 条记录吗?`, '确认批量删除', { type: 'warning' })
    .then(() => {
      console.log('请求批量删除:', selectedRows.map(r => r.id));
      ElMessage.success('批量删除成功 (模拟)');
      // 调用 API 后刷新数据
      LoadData();
    })
    .catch(() => ElMessage.info('取消批量删除'));
};

const HandleImport = (data: any[]) => {
  console.log('接收到导入数据:', data);
  ElMessage.success(`成功解析 ${data.length} 条数据，请在父组件处理。`);
  // 处理导入的数据...
  // 可能需要刷新表格: LoadData();
};
const HandleExport = () => { console.log('导出流程开始。'); ElMessage.info('导出已开始 (检查下载)'); };

const HandleFilterApply = (filteredData: any[], filterValues: Record<string, any>) => {
  console.log('筛选条件已应用:', filterValues);
  console.log('筛选后数据量 (前端):', filteredData.length);
  // 如果使用后端筛选，在此处使用 filterValues 调用 LoadData
};
const HandleFilterClear = () => { console.log('筛选条件已清除。'); LoadData(); }; // 重新加载未筛选的数据

const HandleDensityChange = (density: string) => { console.log('密度变更:', density); };
const HandleColumnChange = (columns: Array<{ prop: string; visible: boolean }>) => { console.log('列可见性变更:', columns); };
const CustomToolbarAction = () => { ElMessage.success('自定义工具栏按钮点击'); };

// Action 事件处理
const HandleEditAction = (row: any) => { ElMessage.info(`编辑 Action 触发: ${row.name}`); };
const HandleAuditAction = (row: any) => { ElMessage.success(`审核 Action 触发: ${row.name}`); row.status = 'active'; }; // 模拟审核成功

// 可编辑表格处理
const HandleRowSave = async (row: any, index: number, oldRow: any) => {
  loading.value = true;
  console.log('保存行:', JSON.parse(JSON.stringify(row)));
  // 模拟 API 保存延迟
  await new Promise(resolve => setTimeout(resolve, 400));
  loading.value = false;
  // 假设 API 成功，VNTable 内部处理状态更新
  ElMessage.success(`行 ${row.id} 保存成功`);
};

const HandleRowCancel = (row: any, index: number) => {
  console.log('取消编辑行:', row);
  ElMessage.info('取消编辑');
};

const HandleAddEditableRow = () => {
  const newId = editableData.value.length > 0 ? Math.max(...editableData.value.map(r => r.id)) + 1 : 1;
  const newRow = { id: newId, name: '', quantity: null, price: null, type: null, isActive: false, expireDate: null };
  editableData.value.push(newRow);
  // 可选：立即将新行设为编辑状态
  editableTableRef.value?.toggleRowEditing(newRow, true);
  ElMessage.info('已添加新行，请编辑');
};

const HandleRowValidateComplete = (prop: string, isValid: boolean, message: string, row: any) => {
  console.log(`行 ${row.id}, 属性 ${prop} 验证: ${isValid ? '通过' : '失败 ('+ message +')'}`);
};

const HandleValidateError = (errors: Record<string, { errors: any[], row: any }>) => {
  console.error('表格验证错误:', errors);
  ElMessage.warning('表格中存在验证错误，请修正。');
};

// 布局变更
const HandleLayoutChange = (mode: LayoutMode) => {
  currentLayout.value = mode;
  console.log('布局变更为:', mode);
};

// ==================== 方法调用 ====================
const GetSelectedRows = (tableName: string) => {
  let rows = [];
  if (tableName === 'basic' && basicTableRef.value) {
    rows = basicTableRef.value.GetSelectionRows();
  }
  // 如果需要，为其他表格添加 ref 和逻辑
  console.log(`[${tableName}] 选中行:`, rows);
  ElMessage.info(`获取到 ${rows.length} 条选中行`);
};

const ClearSelection = (tableName: string) => {
  if (tableName === 'basic' && basicTableRef.value) {
    basicTableRef.value.ClearSelection();
    ElMessage.success(`[${tableName}] 已清空选中`);
  }
};

const ToggleRowSelection = (tableName: string) => {
   if (tableName === 'basic' && basicTableRef.value && basicData.value.length > 0) {
    basicTableRef.value.ToggleRowSelection(basicData.value[0]);
    ElMessage.info(`[${tableName}] 已切换第一行选中状态`);
   }
};

const ToggleTreeRowExpansion = () => {
  if (treeTableRef.value && treeData.value.length > 0) {
    treeTableRef.value.ToggleRowExpansion(treeData.value[0]);
     ElMessage.info(`切换了树表第一行 (${treeData.value[0].name}) 的展开状态`);
  }
};

const ValidateEditableTable = async () => {
  if (editableTableRef.value) {
    const isValid = await editableTableRef.value.ValidateTable();
    if (isValid) {
      ElMessage.success('可编辑表格验证通过！');
    } else {
      ElMessage.error('可编辑表格验证失败，请检查错误项。');
    }
  }
};

const ResetEditableTable = () => {
   if (editableTableRef.value) {
     editableTableRef.value.Reset(); // 重置筛选、排序、编辑状态等
     // 注意：Reset 不会恢复 data prop 的原始值，如果需要恢复数据，需要手动处理
     editableData.value = GenerateEditableData(); // 重新加载模拟数据以达到"重置数据"效果
     ElMessage.info('可编辑表格状态已重置');
   }
};


// ==================== 生命周期 ====================
onMounted(() => {
  LoadData();
});

</script>

<style lang="scss" scoped>
.example-container {
  padding: 20px;
  background-color: #f9f9f9;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
}

section {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 1.4em;
    color: #3a8ee6;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
  }
}

.example-controls {
  margin-top: 15px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #ecf5ff;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 改进控件间距 */
.example-controls .el-button {
  margin-left: 0 !important; // 覆盖 element-plus 默认边距
}


/* 展开行内容样式 */
.expand-content {
  padding: 15px 20px 15px 50px; // 内容缩进
  background-color: #fafafa;
  border-radius: 4px;
  margin: 5px 0;

  h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
  }
  p {
    margin: 5px 0;
    line-height: 1.6;
    color: #666;
  }
}

/* 确保 VNTable 占据合理空间 */
:deep(.vn-table-container) {
   margin-bottom: 0; /* 如果后面跟着控件，移除默认底部边距 */
}
</style> 