<template>
  <div ref="chartRef" :style="{ width: width, height: height }"></div>
</template>

<script setup lang="ts">
import { 
  ref, 
  onMounted, 
  onUnmounted, 
  watch, 
  defineProps, 
  withDefaults, 
  nextTick 
} from 'vue';
import * as echarts from 'echarts/core';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie<PERSON>hart,
  // ... 按需引入其他图表类型
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  // ... 按需引入其他组件
} from 'echarts/components';
import {
  CanvasRenderer // 或者 SVGRenderer
} from 'echarts/renderers';
import type { EChartsType, EChartsOption } from 'echarts';
import type { VNChartProps } from './types';
import { useDebounceFn } from '@vueuse/core'; // 用于窗口大小调整的防抖

// 注册必须的组件和图表
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  <PERSON><PERSON><PERSON>po<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
]);

const props = withDefaults(defineProps<VNChartProps>(), {
  width: '100%',
  height: '400px',
  renderer: 'canvas'
});

const chartRef = ref<HTMLDivElement | null>(null);
let chartInstance: EChartsType | null = null;

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value, props.theme, { renderer: props.renderer });
    setChartOption(props.option);
  }
};

// 设置图表配置
const setChartOption = (option: EChartsOption) => {
  if (chartInstance) {
    chartInstance.setOption(option);
  }
};

// 窗口大小调整时重绘图表 (带防抖)
const resizeChart = useDebounceFn(() => {
  if (chartInstance) {
    chartInstance.resize();
  }
}, 200);

// 监听 props.option 的变化
watch(() => props.option, (newOption) => {
  // 使用 nextTick 确保 DOM 更新后再设置配置
  nextTick(() => {
    setChartOption(newOption);
  });
}, { deep: true }); // 使用 deep watch 监听 option 内部变化

// 组件挂载后初始化图表并监听窗口大小
onMounted(() => {
  initChart();
  window.addEventListener('resize', resizeChart);
});

// 组件卸载前销毁图表实例并移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 如果需要暴露 chartInstance 给父组件
// defineExpose({ chartInstance });
</script>

<style scoped>
/* 可以添加一些基本样式，例如保证容器有基本的显示 */
div {
  min-height: 50px; /* 防止高度为0时无法渲染 */
}
</style> 