<template>
    <div class="blind-receiving-validation-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <VNBreadcrumb :items="breadcrumbItems" />
        <div class="page-title">
          <h2>盲收验证管理</h2>
          <p class="page-description">实时验证盲收权限，查看验证历史和统计分析</p>
        </div>
      </div>
  
      <!-- 快速验证区域 -->
      <el-card class="quick-validation-card" shadow="never">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon><Shield /></el-icon>
              <span>快速验证</span>
            </div>
            <div class="header-actions">
              <el-button
                :icon="QuestionFilled"
                @click="showRulesDialog = true"
                size="small"
              >
                验证规则说明
              </el-button>
            </div>
          </div>
        </template>
        
        <BlindReceivingValidator
          :mode="'full'"
          :auto-validate="false"
          :show-history-button="false"
          @validation-complete="handleValidationComplete"
          @validation-error="handleValidationError"
        />
      </el-card>
  
      <!-- 统计概览 -->
      <div class="stats-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stats-card" shadow="never">
              <el-statistic
                title="今日验证次数"
                :value="todayStats.validationCount"
                :value-style="{ color: '#409eff' }"
              >
                <template #suffix>次</template>
              </el-statistic>
              <div class="stats-trend">
                <el-icon class="trend-icon" :class="getTrendClass(todayStats.countTrend)">
                  <component :is="getTrendIcon(todayStats.countTrend)" />
                </el-icon>
                <span class="trend-text">
                  较昨日 {{ getTrendText(todayStats.countTrend) }}
                </span>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stats-card" shadow="never">
              <el-statistic
                title="验证成功率"
                :value="todayStats.successRate"
                :precision="1"
                :value-style="{ color: '#67c23a' }"
              >
                <template #suffix>%</template>
              </el-statistic>
              <div class="stats-trend">
                <el-icon class="trend-icon" :class="getTrendClass(todayStats.rateTrend)">
                  <component :is="getTrendIcon(todayStats.rateTrend)" />
                </el-icon>
                <span class="trend-text">
                  较昨日 {{ getTrendText(todayStats.rateTrend) }}
                </span>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stats-card" shadow="never">
              <el-statistic
                title="平均响应时间"
                :value="todayStats.averageResponseTime"
                :precision="0"
                :value-style="{ color: '#e6a23c' }"
              >
                <template #suffix>ms</template>
              </el-statistic>
              <div class="stats-trend">
                <el-icon class="trend-icon" :class="getTrendClass(todayStats.timeTrend)">
                  <component :is="getTrendIcon(todayStats.timeTrend)" />
                </el-icon>
                <span class="trend-text">
                  较昨日 {{ getTrendText(todayStats.timeTrend) }}
                </span>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stats-card" shadow="never">
              <el-statistic
                title="待处理审批"
                :value="todayStats.pendingApprovals"
                :value-style="{ color: '#f56c6c' }"
              >
                <template #suffix>项</template>
              </el-statistic>
              <div class="stats-action">
                <el-button
                  type="primary"
                  size="small"
                  @click="goToApprovalWorkbench"
                  :disabled="todayStats.pendingApprovals === 0"
                >
                  去处理
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
  
      <!-- 主要内容区域 -->
      <div class="main-content">
        <el-row :gutter="20">
          <!-- 左侧：验证历史 -->
          <el-col :span="16">
            <el-card class="history-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <div class="header-title">
                    <el-icon><Clock /></el-icon>
                    <span>验证历史</span>
                  </div>
                  <div class="header-actions">
                    <el-button
                      :icon="View"
                      @click="goToHistoryPage"
                      size="small"
                    >
                      查看全部
                    </el-button>
                    <el-button
                      :icon="Refresh"
                      @click="refreshHistory"
                      size="small"
                    >
                      刷新
                    </el-button>
                  </div>
                </div>
              </template>
              
              <ValidationHistory
                :compact="true"
                :show-stats="false"
                :filters="historyFilters"
                @retry-validation="handleRetryValidation"
                @record-deleted="handleRecordDeleted"
              />
            </el-card>
          </el-col>
          
          <!-- 右侧：快捷操作和图表 -->
          <el-col :span="8">
            <!-- 快捷操作 -->
            <el-card class="quick-actions-card" shadow="never">
              <template #header>
                <div class="header-title">
                  <el-icon><Operation /></el-icon>
                  <span>快捷操作</span>
                </div>
              </template>
              
              <div class="quick-actions">
                <div class="action-group">
                  <h4 class="group-title">配置管理</h4>
                  <el-button
                    type="primary"
                    :icon="Setting"
                    @click="goToConfigManagement"
                    class="action-button"
                  >
                    配置管理
                  </el-button>
                  <el-button
                    type="success"
                    :icon="Plus"
                    @click="createNewConfig"
                    class="action-button"
                  >
                    新增配置
                  </el-button>
                </div>
                
                <div class="action-group">
                  <h4 class="group-title">审批管理</h4>
                  <el-button
                    type="warning"
                    :icon="User"
                    @click="goToApprovalWorkbench"
                    class="action-button"
                    :badge="todayStats.pendingApprovals || undefined"
                  >
                    审批工作台
                  </el-button>
                  <el-button
                    type="info"
                    :icon="List"
                    @click="goToApprovalHistory"
                    class="action-button"
                  >
                    审批历史
                  </el-button>
                </div>
                
                <div class="action-group">
                  <h4 class="group-title">数据分析</h4>
                  <el-button
                    type="primary"
                    :icon="TrendCharts"
                    @click="goToAnalytics"
                    class="action-button"
                  >
                    趋势分析
                  </el-button>
                  <el-button
                    type="success"
                    :icon="Download"
                    @click="exportValidationData"
                    class="action-button"
                  >
                    导出数据
                  </el-button>
                </div>
              </div>
            </el-card>
            
            <!-- 策略分布图表 -->
            <el-card class="chart-card" shadow="never">
              <template #header>
                <div class="header-title">
                  <el-icon><PieChart /></el-icon>
                  <span>策略分布</span>
                </div>
              </template>
              
              <div class="strategy-distribution">
                <VNChart
                  type="pie"
                  :data="strategyChartData"
                  :options="chartOptions"
                  height="200px"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
  
      <!-- 规则说明对话框 -->
      <el-dialog
        v-model="showRulesDialog"
        title="验证规则说明"
        width="80%"
        destroy-on-close
      >
        <RuleExplanation />
      </el-dialog>
  
      <!-- 新增配置对话框 -->
      <ConfigForm
        v-model="showConfigForm"
        :is-edit="false"
        @success="handleConfigFormSuccess"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import {
    Shield,
    QuestionFilled,
    Clock,
    View,
    Refresh,
    Operation,
    Setting,
    Plus,
    User,
    List,
    TrendCharts,
    Download,
    PieChart,
    ArrowUp,
    ArrowDown,
    Minus
  } from '@element-plus/icons-vue'
  import VNBreadcrumb from '@/components/VNBreadcrumb/index.vue'
  import VNChart from '@/components/VNChart/index.vue'
  import BlindReceivingValidator from './components/BlindReceivingValidator.vue'
  import ValidationHistory from './components/ValidationHistory.vue'
  import RuleExplanation from './components/RuleExplanation.vue'
  import ConfigForm from '../blind-receiving-config/components/ConfigForm.vue'
  import {
    getBlindReceivingValidationDashboard,
    getBlindReceivingTodayStats,
    exportBlindReceivingValidationData
  } from '@/api/wms/blindReceivingConfig'
  import type {
    BlindReceivingValidationVO
  } from '@/types/wms/blindReceivingConfig'
  
  // 路由
  const router = useRouter()
  
  // 响应式数据
  const showRulesDialog = ref(false)
  const showConfigForm = ref(false)
  
  // 面包屑
  const breadcrumbItems = [
    { label: '首页', to: '/' },
    { label: 'WMS管理', to: '/wms' },
    { label: '盲收验证管理', to: '/wms/blind-receiving-validation' }
  ]
  
  // 今日统计数据
  const todayStats = ref({
    validationCount: 0,
    successRate: 0,
    averageResponseTime: 0,
    pendingApprovals: 0,
    countTrend: 0,
    rateTrend: 0,
    timeTrend: 0
  })
  
  // 历史记录筛选条件
  const historyFilters = reactive({
    // 默认显示今日数据
    dateFrom: new Date().toISOString().split('T')[0],
    pageSize: 10
  })
  
  // 策略分布图表数据
  const strategyChartData = ref([
    { name: '严格模式', value: 0 },
    { name: '补录模式', value: 0 },
    { name: '完全模式', value: 0 }
  ])
  
  // 图表配置
  const chartOptions = computed(() => ({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '策略分布',
        type: 'pie',
        radius: '50%',
        data: strategyChartData.value,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }))
  
  // 工具函数
  const getTrendClass = (trend: number) => {
    if (trend > 0) return 'trend-up'
    if (trend < 0) return 'trend-down'
    return 'trend-flat'
  }
  
  const getTrendIcon = (trend: number) => {
    if (trend > 0) return ArrowUp
    if (trend < 0) return ArrowDown
    return Minus
  }
  
  const getTrendText = (trend: number) => {
    if (trend > 0) return `上升 ${Math.abs(trend)}%`
    if (trend < 0) return `下降 ${Math.abs(trend)}%`
    return '持平'
  }
  
  // 数据加载
  const loadDashboardData = async () => {
    try {
      const [statsResult, dashboardResult] = await Promise.all([
        getBlindReceivingTodayStats(),
        getBlindReceivingValidationDashboard()
      ])
      
      todayStats.value = statsResult
      strategyChartData.value = dashboardResult.strategyDistribution
    } catch (error) {
      console.error('加载仪表板数据失败:', error)
      ElMessage.error('加载仪表板数据失败')
    }
  }
  
  // 事件处理
  const handleValidationComplete = (result: BlindReceivingValidationVO) => {
    ElMessage.success('验证完成')
    // 刷新统计数据
    loadDashboardData()
  }
  
  const handleValidationError = (error: any) => {
    console.error('验证失败:', error)
  }
  
  const handleRetryValidation = (record: BlindReceivingValidationVO) => {
    // 可以在这里实现重试逻辑
    ElMessage.info('功能开发中...')
  }
  
  const handleRecordDeleted = (id: number) => {
    // 刷新数据
    loadDashboardData()
  }
  
  const refreshHistory = () => {
    // 触发历史记录组件刷新
    window.location.reload()
  }
  
  const handleConfigFormSuccess = () => {
    showConfigForm.value = false
    ElMessage.success('配置创建成功')
  }
  
  // 导航方法
  const goToConfigManagement = () => {
    router.push('/wms/blind-receiving-config')
  }
  
  const createNewConfig = () => {
    showConfigForm.value = true
  }
  
  const goToApprovalWorkbench = () => {
    router.push('/wms/blind-receiving-validation/approval')
  }
  
  const goToApprovalHistory = () => {
    router.push('/wms/blind-receiving-validation/approval-history')
  }
  
  const goToHistoryPage = () => {
    router.push('/wms/blind-receiving-validation/history')
  }
  
  const goToAnalytics = () => {
    router.push('/wms/blind-receiving-validation/analytics')
  }
  
  const exportValidationData = async () => {
    try {
      const blob = await exportBlindReceivingValidationData({
        dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        dateTo: new Date().toISOString().split('T')[0]
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `盲收验证数据_${new Date().toISOString().slice(0, 10)}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }
  
  // 初始化
  onMounted(() => {
    loadDashboardData()
  })
  </script>
  
  <style scoped lang="scss">
  .blind-receiving-validation-container {
    padding: 20px;
    background-color: #f5f5f5;
  }
  
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      margin-top: 10px;
      
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }
      
      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .quick-validation-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .stats-overview {
    margin-bottom: 20px;
    
    .stats-card {
      text-align: center;
      
      :deep(.el-card__body) {
        padding: 20px;
      }
      
      .stats-trend {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        margin-top: 8px;
        font-size: 12px;
        
        .trend-icon {
          font-size: 14px;
          
          &.trend-up {
            color: #67c23a;
          }
          
          &.trend-down {
            color: #f56c6c;
          }
          
          &.trend-flat {
            color: #909399;
          }
        }
        
        .trend-text {
          color: #606266;
        }
      }
      
      .stats-action {
        margin-top: 8px;
      }
    }
  }
  
  .main-content {
    .history-card,
    .quick-actions-card,
    .chart-card {
      margin-bottom: 20px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #303133;
        }
        
        .header-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
    
    .quick-actions {
      .action-group {
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .group-title {
          margin: 0 0 12px 0;
          color: #606266;
          font-size: 14px;
          font-weight: 600;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 6px;
        }
        
        .action-button {
          display: block;
          width: 100%;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .strategy-distribution {
      height: 200px;
    }
  }
  
  // 响应式设计
  @media (max-width: 1200px) {
    .main-content {
      .el-col {
        &:nth-child(1) {
          margin-bottom: 20px;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .blind-receiving-validation-container {
      padding: 10px;
    }
    
    .stats-overview {
      .el-row .el-col {
        margin-bottom: 10px;
      }
    }
    
    .main-content {
      .el-row {
        flex-direction: column;
      }
    }
  }
  </style>