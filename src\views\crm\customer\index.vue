<template>
  <div>
    <!-- 客户管理表格 -->
    <VNTable
      ref="customerTableRef"
      :data="customerTableData"
      :columns="customerTableColumns" 
      :loading="customerLoading"
      :pagination="customerPagination"
      :toolbar-config="customerToolbarConfig"
      :operation-buttons="[]" 
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      operation-fixed="right"
      :operation-width="220" 
      @refresh="loadCustomers" 
      @add="handleAddNewCustomer"
      @selection-change="handleCustomerSelectionChange"
      @batch-delete="handleCustomerBatchDelete"
      @import="handleCustomerImport"
      @export="handleCustomerExport"
      @page-change="handleCustomerPageChange"
      @page-size-change="handleCustomerPageSizeChange"
      @sort-change="handleCustomerSortChange" 
      @filter-change="handleCustomerFilterChange" 
    >
      <!-- 客户类型列插槽 -->
      <template #column-customerType="{ row }">
         <el-tag :type="row.customerType === 'CORPORATE' ? 'success' : 'info'">
           {{ getCustomerTypeLabel(row.customerType) }}
         </el-tag>
      </template>

      <!-- 客户级别列插槽 -->
      <template #column-customerLevel="{ row }">
        <el-tag 
          :type="row.customerLevel === 'VIP' ? 'danger' : row.customerLevel === 'GOLD' ? 'warning' : row.customerLevel === 'SILVER' ? 'info' : 'primary'"
        >
          {{ getCustomerLevelLabel(row.customerLevel) }}
        </el-tag>
      </template>

      <!-- 状态列插槽 -->
      <template #column-status="{ row }">
         <el-tag :type="row.status === 'ACTIVE' ? 'success' : row.status === 'INACTIVE' ? 'warning' : 'danger'">
           {{ getCustomerStatusLabel(row.status) }}
         </el-tag>
      </template>

      <!-- 是否重点客户列插槽 -->
      <template #column-isKeyCustomer="{ row }">
        <el-tag :type="row.isKeyCustomer ? 'danger' : 'info'">
          {{ row.isKeyCustomer ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 客户表格操作列插槽 -->
      <template #operation="{ row }">
        <el-tooltip content="查看客户" placement="top" v-if="hasPermission('crm:customer:view')">
          <el-button circle :icon="View" size="small" @click="handleViewCustomer(row)" />
        </el-tooltip>
        <el-tooltip content="编辑客户" placement="top" v-if="hasPermission('crm:customer:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditCustomer(row)" />
        </el-tooltip>
        <el-tooltip content="联系人" placement="top" v-if="hasPermission('crm:customer:contact:list')">
           <el-button circle :icon="User" type="success" size="small" @click="openContactManageDialog(row)" />
        </el-tooltip>
        <el-tooltip content="删除客户" placement="top" v-if="hasPermission('crm:customer:delete')">
          <el-button circle :icon="Delete" type="danger" size="small" @click="handleDeleteCustomer(row)" />
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 客户新增/编辑弹窗 -->
    <el-dialog
      v-model="customerDialogVisible"
      :title="customerDialogTitle"
      width="70%"
      draggable
      align-center
      top="5vh"
      :close-on-click-modal="false"
      @close="handleCloseCustomerDialog" 
    >
      <VNForm
        v-if="customerDialogVisible" 
        ref="customerFormRef"
        :header-fields="customerFormFields"
        v-model="currentEditingCustomer"
        :default-columns="4"
        :label-width="'120px'"
        :loading="customerFormLoading" 
        :show-actions="false"
        group-title-type="h4"
        @submit="submitCustomerForm"
        @cancel="handleCloseCustomerDialog" 
      >
        <!-- 自定义表单操作按钮插槽 -->
        <template #actions>
            <template v-if="isViewing">
                <el-button
                  v-if="hasPermission('crm:customer:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreviewCustomer"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('crm:customer:print')" 
                  type="primary"
                  :icon="Printer" 
                  @click="handlePrintCustomer"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseCustomerDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitCustomerForm" :loading="customerFormLoading">提交</el-button>
                <el-button @click="handleCloseCustomerDialog">取消</el-button>
            </template>
        </template>
      </VNForm>
    </el-dialog> 

    <!-- 客户联系人管理模态框 -->
    <el-dialog
      v-model="contactManageDialogVisible"
      :title="'联系人管理 - ' + (currentManagedCustomer?.customerName || '')"
      width="85%"
      top="5vh"
      draggable
      :close-on-click-modal="false"
      :modal="true"
      destroy-on-close
      @close="currentManagedCustomer = null"
    >
      <!-- 客户联系人管理组件 -->
      <CustomerContactManager
        v-if="currentManagedCustomer" 
        :customer-id="currentManagedCustomer.id"
        :customer-code="currentManagedCustomer.customerCode"
        :customer-name="currentManagedCustomer.customerName"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTooltip, ElTag, ElInput } from 'element-plus';
// 导入所需图标
import { Edit, Delete, View, Printer, User, Refresh } from '@element-plus/icons-vue'; 
// 假设 VNTable 和 VNForm 组件已全局注册或在此导入
import VNTable from '@/components/VNTable/index.vue'; 
import VNForm from '@/components/VNForm/index.vue'; 
// 引入类型定义
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 引入API函数和类型定义
import { 
  getCustomerPage, 
  createCustomer, 
  updateCustomer, 
  deleteCustomer,
  validateBusinessLicense,
  validateTaxNumber,
  getCustomerSummary
} from '@/api/crm/customer';
// 引入字典数据获取函数
import { getDictDataByCode } from '@/api/system/systemDict';
// 引入币种API
import { getEnabledCurrencyList } from '@/api/fin/currency';
import type { CurrencyListItem } from '@/api/fin/currency';
import type { 
  CrmCustomerVO, 
  CrmCustomerListVO,
  CrmCustomerQueryDTO, 
  CrmCustomerCreateDTO, 
  CrmCustomerUpdateDTO 
} from '@/api/crm/customer';
// 导入客户联系人管理组件
import CustomerContactManager from './components/CustomerContactManager.vue'; 
// 导入权限检查函数
import { hasPermission } from '@/hooks/usePermission';
// 导入字典hooks
import { useDictionary } from '@/hooks/useDictionary';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Refs --- 
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;

const customerTableRef = ref<VNTableInstance>();
// 用于客户编辑表单
const customerFormRef = ref<VNFormInstance>();

// --- State --- 

// 字典数据状态
const { loadMultipleDictData, getDictLabel } = useDictionary();
const customerTypes = ref<{ label: string; value: string }[]>([]);
const customerLevels = ref<{ label: string; value: string }[]>([]);
const customerStatuses = ref<{ label: string; value: string }[]>([]);
const customerSources = ref<{ label: string; value: string }[]>([]);
const creditRatings = ref<{ label: string; value: string }[]>([]);
const paymentTerms = ref<{ label: string; value: string }[]>([]);
const industries = ref<{ label: string; value: string }[]>([]);
const currencyOptions = ref<{ label: string; value: string }[]>([]);
const contactRoles = ref<{ label: string; value: string }[]>([]);
const genders = ref<{ label: string; value: string }[]>([]);

// 客户相关状态
const customerTableData = ref<CrmCustomerListVO[]>([]);
const customerPagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const customerLoading = ref(false);
// 客户增/改弹窗
const customerDialogVisible = ref(false);
// 当前编辑的客户数据
const currentEditingCustomer = ref<Partial<CrmCustomerCreateDTO | CrmCustomerUpdateDTO>>({});
// 添加 formMode 和 isViewing
const formMode = ref<'add' | 'edit' | 'view'>('add');
const isViewing = computed(() => formMode.value === 'view');
const selectedCustomerRows = ref<CrmCustomerListVO[]>([]); // 存储选中的行

// 添加排序状态
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);
// 添加筛选状态
const currentFilters = ref<Record<string, any>>({});
// 客户表单加载状态
const customerFormLoading = ref(false);

// 客户联系人管理模态框相关状态
const contactManageDialogVisible = ref(false);
// 当前在模态框中管理的客户
const currentManagedCustomer = ref<CrmCustomerListVO | null>(null);

// --- Computed --- 
// 计算属性，用于动态设置客户编辑弹窗标题
const customerDialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增客户';
  if (formMode.value === 'edit') return '编辑客户';
  if (formMode.value === 'view') return '查看客户';
  return '客户管理'; // Default
});

// --- Helper: Date Formatting ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) { 
        return '-';
    }
    return date.toLocaleString();
  } catch (e) {
    console.error('Error formatting date:', dateString, e);
    return '-';
  }
};

// --- Helper: Dictionary Label Functions ---
const getCustomerTypeLabel = (value: string): string => {
  const item = customerTypes.value.find(t => t.value === value);
  return item ? item.label : value;
};

const getCustomerLevelLabel = (value: string): string => {
  const item = customerLevels.value.find(l => l.value === value);
  return item ? item.label : value;
};

const getCustomerStatusLabel = (value: string): string => {
  const item = customerStatuses.value.find(s => s.value === value);
  return item ? item.label : value;
};

const getCustomerSourceLabel = (value: string): string => {
  const item = customerSources.value.find(s => s.value === value);
  return item ? item.label : value;
};

const getCreditRatingLabel = (value: string): string => {
  const item = creditRatings.value.find(r => r.value === value);
  return item ? item.label : value;
};

const getPaymentTermsLabel = (value: string): string => {
  const item = paymentTerms.value.find(p => p.value === value);
  return item ? item.label : value;
};

const getIndustryLabel = (value: string): string => {
  const item = industries.value.find(i => i.value === value);
  return item ? item.label : value;
};

const getCurrencyCodeLabel = (value: string): string => {
  const item = currencyOptions.value.find(c => c.value === value);
  return item ? item.label : value;
};

// --- 计算属性，用于动态设置工具栏配置 ---
const customerToolbarConfig = computed(() => ({
  refresh: true,
  add: hasPermission('crm:customer:add'),
  batchDelete: hasPermission('crm:customer:batchdelete'),
  filter: hasPermission('crm:customer:search'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('crm:customer:import'),
  export: hasPermission('crm:customer:export'),
}));

// --- Table Columns (Customer) --- 
const customerTableColumns = computed<TableColumn[]>(() => [
  { prop: 'customerCode', label: '客户编码', minWidth: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'customerName', label: '客户名称', minWidth: 150, filterable: true, filterType: 'text' },
  { 
    prop: 'customerType', 
    label: '客户类型', 
    width: 110, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: customerTypes.value
  }, 
  { 
    prop: 'customerLevel', 
    label: '客户级别', 
    width: 110, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: customerLevels.value
  },
  { prop: 'contactPerson', label: '联系人', width: 100 },
  { prop: 'contactPhone', label: '联系电话', width: 120 },
  { prop: 'contactEmail', label: '联系邮箱', minWidth: 150, showOverflowTooltip: true },
  { prop: 'province', label: '省份', width: 80 },
  { prop: 'city', label: '城市', width: 80 },
  // { prop: 'salesRepresentativeName', label: '销售代表', width: 100 },
  { 
    prop: 'status', 
    label: '状态', 
    width: 90, 
    slot: true, 
    filterable: true, 
    filterType: 'select',
    filterOptions: customerStatuses.value
  },
  {
    prop: 'isKeyCustomer',
    label: '重点客户',
    width: 100,
    slot: true,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 180,
    sortable: true,
    filterable: true,
    filterType: 'date',
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

// --- Form Fields (Customer) --- 
const customerFormFields = computed<HeaderField[]>(() => [
  // 基本信息分组
  {
    field: 'customerCode',
    label: '客户编码',
    disabled: formMode.value === 'edit' || isViewing.value,
    placeholder: '留空可自动生成或手动输入',
    group: '基本信息',
    rules: [
      { pattern: /^[a-zA-Z0-9_-]*$/, message: '编码只能包含字母、数字、下划线和中划线' },
      { max: 50, message: '编码长度不能超过50' }
    ],
  },
  {
    field: 'customerName',
    label: '客户名称',
    disabled: isViewing.value,
    placeholder: '请输入客户名称',
    group: '基本信息',
    rules: [
      { required: true, message: '客户名称不能为空' },
      { max: 255, message: '名称长度不能超过255' }
    ]
  },
  {
    field: 'customerType',
    label: '客户类型',
    type: 'select',
    options: customerTypes,
    defaultValue: 'CORPORATE',
    disabled: isViewing.value,
    group: '基本信息',
  },
  {
    field: 'industry',
    label: '所属行业',
    type: 'select',
    options: industries,
    disabled: isViewing.value,
    placeholder: '请选择所属行业',
    group: '基本信息',
    rules: [{ max: 100, message: '行业长度不能超过100' }]
  },
  {
    field: 'customerLevel',
    label: '客户级别',
    type: 'select',
    options: customerLevels,
    defaultValue: 'NORMAL',
    disabled: isViewing.value,
    group: '基本信息',
  },
  {
    field: 'customerSource',
    label: '客户来源',
    type: 'select',
    options: customerSources,
    disabled: isViewing.value,
    placeholder: '请选择客户来源',
    group: '基本信息',
    rules: [{ max: 50, message: '客户来源长度不能超过50' }]
  },
  {
    field: 'isKeyCustomer',
    label: '重点客户',
    type: 'switch',
    props: {
      activeValue: true,
      inactiveValue: false,
    },
    defaultValue: false,
    disabled: isViewing.value,
    group: '基本信息',
  },
  {
    field: 'status',
    label: '状态',
    type: 'select',
    options: customerStatuses,
    defaultValue: 'ACTIVE',
    disabled: isViewing.value,
    group: '基本信息',
  },
  // 状态信息分组
  {
    field: 'remark',
    label: '备注',
    type: 'textarea',
    props: { rows: 1 },
    disabled: isViewing.value,
    group: '基本信息',
    md: 24,
    lg: 24,
    xl: 24,
    rules: [{ max: 500, message: '备注长度不能超过500' }]
  },
  
  // 证件信息分组
  {
    field: 'businessLicense',
    label: '营业执照号',
    disabled: isViewing.value,
    group: '财务信息',
    rules: [{ max: 100, message: '营业执照号长度不能超过100' }],
    placeholder: '请输入营业执照号',
    asyncValidator: async (rule: any, value: string) => {
      console.log('rule', rule)
      if (!value || value.trim() === '') return true;
      
      try {
        const result = await validateBusinessLicense(value, formMode.value === 'edit' ? (currentEditingCustomer.value as any)?.id : undefined);
        if (!result.valid) {
          throw new Error(result.message || '营业执照号已存在');
        }
        return true;
      } catch (error: any) {
        throw new Error(error.message || '营业执照号验证失败');
      }
    }
  },
  {
    field: 'taxNumber',
    label: '税务登记号',
    disabled: isViewing.value,
    group: '财务信息',
    rules: [{ max: 100, message: '税务登记号长度不能超过100' }],
    placeholder: '请输入税务登记号',
    asyncValidator: async (rule: any, value: string) => {
      console.log('rule', rule)
      if (!value || value.trim() === '') return true;
      
      try {
        const result = await validateTaxNumber(value, formMode.value === 'edit' ? (currentEditingCustomer.value as any)?.id : undefined);
        if (!result.valid) {
          throw new Error(result.message || '税务登记号已存在');
        }
        return true;
      } catch (error: any) {
        throw new Error(error.message || '税务登记号验证失败');
      }
    }
  },
  {
    field: 'legalRepresentative',
    label: '法定代表人',
    disabled: isViewing.value,
    group: '财务信息',
    placeholder: '请输入法定代表人',
    rules: [{ max: 100, message: '法定代表人长度不能超过100' }]
  },
  {
    field: 'registeredCapital',
    label: '注册资本',
    type: 'number',
    props: { precision: 2, min: 0, step: 10000 },
    disabled: isViewing.value,
    group: '财务信息',
  },
  // 财务信息分组
  {
    field: 'creditRating',
    label: '信用等级',
    type: 'select',
    options: creditRatings,
    disabled: isViewing.value,
    placeholder: '请选择信用等级',
    group: '财务信息',
    rules: [{ max: 20, message: '信用等级长度不能超过20' }]
  },
  {
    field: 'creditLimit',
    label: '信用额度',
    type: 'number',
    props: { precision: 2, min: 0, step: 10000 },
    disabled: isViewing.value,
    group: '财务信息',
  },
  {
    field: 'paymentTerms',
    label: '付款条件',
    type: 'select',
    options: paymentTerms,
    disabled: isViewing.value,
    placeholder: '请选择付款条件',
    group: '财务信息',
    rules: [{ max: 50, message: '付款条件长度不能超过50' }]
  },
  {
    field: 'currencyCode',
    label: '结算币种',
    type: 'select',
    options: currencyOptions,
    disabled: isViewing.value,
    defaultValue: 'CNY',
    group: '财务信息',
    rules: [{ max: 10, message: '币种代码长度不能超过10' }]
  },
  
  // 联系信息分组
  {
    field: 'contactPerson',
    label: '主要联系人',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '联系人长度不能超过100' }]
  },
  {
    field: 'contactPhone',
    label: '联系电话',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 50, message: '电话长度不能超过50' }]
  },
  {
    field: 'contactEmail',
    label: '联系邮箱',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [
      { type: 'email', message: '请输入正确的邮箱格式' },
      { max: 255, message: '邮箱长度不能超过255' }
    ]
  },
  {
    field: 'website',
    label: '公司网站',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 255, message: '网站长度不能超过255' }]
  },
  
  // 地址信息分组
  {
    field: 'country',
    label: '国家',
    disabled: isViewing.value,
    defaultValue: '中国',
    group: '联系信息',
    rules: [{ max: 100, message: '国家长度不能超过100' }]
  },
  {
    field: 'province',
    label: '省份',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '省份长度不能超过100' }]
  },
  {
    field: 'city',
    label: '城市',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '城市长度不能超过100' }]
  },
  {
    field: 'district',
    label: '区县',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 100, message: '区县长度不能超过100' }]
  },
  {
    field: 'address',
    label: '详细地址',
    type: 'textarea',
    props: { rows: 1 },
    disabled: isViewing.value,
    group: '联系信息',
    md: 12,
    lg: 12,
    xl: 12
  },
  {
    field: 'postalCode',
    label: '邮政编码',
    disabled: isViewing.value,
    group: '联系信息',
    rules: [{ max: 20, message: '邮政编码长度不能超过20' }]
  },
  
]);

// --- Lifecycle --- 
onMounted(async () => {
  // 组件挂载时加载字典数据和客户数据
  await loadDictionaryData();
  loadCustomers();
});

// --- Dictionary Loading Methods ---

// 加载所有字典数据
const loadDictionaryData = async () => {
  try {
    const dictCodes = [
      'customer_type',
      'customer_level',
      'customer_status',
      'customer_source',
      'credit_rating',
      'payment_terms',
      'industry',
      'contact_role',
      'gender'
    ];

    // 使用Promise.all并行加载批量字典数据和货币列表
    const [dictResults, currencyData] = await Promise.all([
      loadMultipleDictData(dictCodes),
      getEnabledCurrencyList()
    ]);

    customerTypes.value = dictResults['customer_type'] || [];
    customerLevels.value = dictResults['customer_level'] || [];
    customerStatuses.value = dictResults['customer_status'] || [];
    customerSources.value = dictResults['customer_source'] || [];
    creditRatings.value = dictResults['credit_rating'] || [];
    paymentTerms.value = dictResults['payment_terms'] || [];
    industries.value = dictResults['industry'] || [];
    contactRoles.value = dictResults['contact_role'] || [];
    genders.value = dictResults['gender'] || [];
    
    // 转换币种数据格式
    currencyOptions.value = currencyData.map((c: CurrencyListItem) => ({
      label: `${c.name} (${c.code})`,
      value: c.code
    }));

    console.log('字典数据加载完成 (已优化)');
  } catch (error) {
    console.error('加载字典数据失败:', error);
    ElMessage.error('加载字典数据失败，部分功能可能受影响');
  }
};

// --- Methods ---

// 加载客户数据
const loadCustomers = async (paramsOverwrite = {}) => {
  customerLoading.value = true;
  try {
    // 准备排序参数
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop && currentSort.value.order) {
      const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
      sortString = `${currentSort.value.prop},${direction}`;
    }

    // 准备筛选参数
    const filterParams: Record<string, any> = {};
    for (const key in currentFilters.value) {
      const filterValue = currentFilters.value[key];
      if (filterValue !== null && filterValue !== undefined && filterValue !== '' && !(Array.isArray(filterValue) && filterValue.length === 0)) {
         if (key === 'status' && Array.isArray(filterValue) && filterValue.length > 0) {
             filterParams[key] = filterValue[0]; 
         } else if (key === 'createdAt' && Array.isArray(filterValue) && filterValue.length === 2) {
             filterParams['startDate'] = filterValue[0];
             filterParams['endDate'] = filterValue[1];
         } else {
             filterParams[key] = filterValue;
         }
      }
    }

    const params: Partial<CrmCustomerQueryDTO> & { pageNum: number; pageSize: number; sort?: string } = {
      pageNum: customerPagination.currentPage,
      pageSize: customerPagination.pageSize,
      sort: sortString,
      ...filterParams, 
      ...paramsOverwrite, 
    };

    // 清理空值参数
    Object.keys(params).forEach(key => {
        const paramKey = key as keyof typeof params;
        if (params[paramKey] === null || params[paramKey] === undefined || params[paramKey] === '') {
            delete params[paramKey];
        }
    });

    console.log('[loadCustomers] Fetching with params:', params);
    const response = await getCustomerPage(params);

    if (response && Array.isArray(response.list)) { 
        customerTableData.value = response.list;
        customerPagination.total = response.total || 0;
    } else {
        console.warn('getCustomerPage 返回的数据格式不符合预期:', response);
        customerTableData.value = [];
        customerPagination.total = 0;
    }

  } catch (error) {
    console.error('Error loading customers:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    customerTableData.value = [];
    customerPagination.total = 0;
  } finally {
    customerLoading.value = false;
  }
};

// 处理分页变化
const handleCustomerPageChange = (page: number) => {
  customerPagination.currentPage = page;
  loadCustomers();
};

// 处理每页数量变化
const handleCustomerPageSizeChange = (size: number) => {
  customerPagination.pageSize = size;
  customerPagination.currentPage = 1; 
  loadCustomers();
};

// 处理排序变化
type SortParams = { prop: string; order: 'ascending' | 'descending' | null };
const handleCustomerSortChange = (sort: SortParams) => {
  console.log('Sort changed:', sort);
  currentSort.value = sort;
  loadCustomers();
};

// 处理筛选变化
type FilterParams = Record<string, any>;
const handleCustomerFilterChange = (filters: FilterParams) => {
  console.log('Filter changed:', filters);
  currentFilters.value = filters; 
  customerPagination.currentPage = 1; 
  loadCustomers();
};

// 处理行选择变化
const handleCustomerSelectionChange = (rows: CrmCustomerListVO[]) => {
  selectedCustomerRows.value = rows;
  console.log('Selected customer rows:', selectedCustomerRows.value);
};

// 处理新增按钮点击
const handleAddNewCustomer = () => {
  formMode.value = 'add';
  currentEditingCustomer.value = { 
    status: 'ACTIVE',
    customerType: 'CORPORATE',
    customerLevel: 'NORMAL',
    isKeyCustomer: false,
    currencyCode: 'CNY',
    country: '中国'
  };
  customerDialogVisible.value = true;
  nextTick(() => { customerFormRef.value?.clearValidate(); });
};

// 处理编辑按钮点击
const handleEditCustomer = (row: CrmCustomerListVO) => {
  formMode.value = 'edit';
  currentEditingCustomer.value = JSON.parse(JSON.stringify(row));
  customerDialogVisible.value = true;
  nextTick(() => { customerFormRef.value?.clearValidate(); });
};

// 处理查看按钮点击
const handleViewCustomer = (row: CrmCustomerListVO) => {
  formMode.value = 'view';
  currentEditingCustomer.value = JSON.parse(JSON.stringify(row));
  customerDialogVisible.value = true;
};

// 关闭客户编辑/查看弹窗
const handleCloseCustomerDialog = () => {
  customerDialogVisible.value = false;
};

// 提交客户表单
const submitCustomerForm = async () => {
  if (isViewing.value) {
    handleCloseCustomerDialog();
    return;
  }

  const formIsValid = await customerFormRef.value?.validateForm();
  if (!formIsValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  customerFormLoading.value = true;
  const payload = { ...currentEditingCustomer.value };

  try {
    if (formMode.value === 'edit') {
      const editingData = currentEditingCustomer.value as Partial<CrmCustomerVO>; 
      if (!editingData.id) {
          ElMessage.error('无法获取要编辑的客户ID');
          customerFormLoading.value = false;
          return;
      }
      const updatePayload: Partial<CrmCustomerUpdateDTO> = {
         ...editingData
      };
      console.log('Calling updateCustomer API with ID:', editingData.id, 'and payload:', updatePayload);
      await updateCustomer(editingData.id, updatePayload);
      ElMessage.success('编辑成功');
    } else {
      const createPayload = payload as CrmCustomerCreateDTO;
      console.log('Calling createCustomer API with:', createPayload);
      await createCustomer(createPayload);
      ElMessage.success('新增成功');
    }
    handleCloseCustomerDialog();
    loadCustomers();
  } catch (error) {
    console.error('Error saving customer:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    customerFormLoading.value = false;
  }
};

// 打开客户联系人管理模态框的函数
const openContactManageDialog = (row: CrmCustomerListVO) => {
  console.log('Opening contact management for customer:', row);
  currentManagedCustomer.value = row; 
  contactManageDialogVisible.value = true; 
};

// 客户表格行操作 - 删除客户
const handleDeleteCustomer = async (row: CrmCustomerListVO) => {
  console.log('Deleting customer:', row);

  try {
      await ElMessageBox.confirm(`确定删除客户 "${row.customerName}" (${row.customerCode}) 吗? 删除后客户相关数据将无法恢复!`, '危险操作确认', { 
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning' 
      });
      try {
         customerLoading.value = true;
         await deleteCustomer(row.id);
         ElMessage.success('删除成功');
         loadCustomers();
      } catch (apiError: any) {
         console.error('Error deleting customer:', apiError);
         const errorMessage = getApiErrorMessage(apiError);
         ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
         });
      } finally {
         customerLoading.value = false;
      }
  } catch (error) {
      if (error !== 'cancel') {
          console.error('Error during delete confirmation:', error);
      } else {
           ElMessage.info('已取消删除');
      }
  }
};

// 处理批量删除
const handleCustomerBatchDelete = (selectedRows: CrmCustomerListVO[]) => {
  if (!selectedRows || selectedRows.length === 0) {
    ElMessage.warning('请先选择要删除的客户');
    return;
  }
  console.log('Batch delete requested for customers:', selectedRows.map(r => r.id));
  ElMessageBox.confirm(`确定删除选中的 ${selectedRows.length} 个客户吗?`, '确认批量删除', { type: 'warning' })
    .then(async () => {
      ElMessage.info('批量删除功能待实现');
    })
    .catch(() => {
      ElMessage.info('已取消批量删除');
    });
};

// 处理导入
const handleCustomerImport = () => {
  console.log('Import requested for customers');
  ElMessage.info('导入功能待实现');
};

// 处理导出
const handleCustomerExport = () => {
  console.log('Export requested for customers');
  ElMessage.info('导出功能待实现');
};

// 客户打印处理函数
const handlePrintPreviewCustomer = () => {
  const customerToPrint = currentEditingCustomer.value as Partial<CrmCustomerVO>; 
  if (!customerToPrint || !customerToPrint.id) {
    ElMessage.warning('没有可打印预览的客户数据。');
    return;
  }
  console.log('触发打印预览，客户:', customerToPrint);
  ElMessage.info('客户打印预览功能待实现');
};

const handlePrintCustomer = () => {
  const customerToPrint = currentEditingCustomer.value as Partial<CrmCustomerVO>; 
  if (!customerToPrint || !customerToPrint.id) {
    ElMessage.warning('没有可打印的客户数据。');
    return;
  }
  console.log('触发打印，客户:', customerToPrint);
  ElMessage.info('客户打印功能待实现');
};

</script>

<style scoped>
/* Add any specific styles if needed */
</style> 