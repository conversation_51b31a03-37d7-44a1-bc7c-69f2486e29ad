<template>
  <div>
    <!-- 客户联系人管理表格 -->
    <VNTable
      ref="contactTableRef"
      :data="contactTableData"
      :columns="contactTableColumns"
      :loading="contactLoading"
      :pagination="contactPagination"
      :toolbar-config="{ refresh: true, add: hasPermission('crm:customer:contact:add') }" 
      :operation-buttons="[]" 
      :show-operations="true"
      show-index
      row-key="id"
      operation-fixed="right"
      :operation-width="180" 
      @refresh="loadCustomerContacts"
      @add="handleAddNewContact"
      @page-change="handleContactPageChange"
      @page-size-change="handleContactPageSizeChange"
      @sort-change="handleContactSortChange" 
      @filter-change="handleContactFilterChange"
    >
      <!-- 性别列插槽 -->
      <template #column-gender="{ row }">
        <el-tag :type="row.gender === '1' ? 'primary' : 'danger'" v-if="row.gender">
          {{ row.gender === '1' ? '男' : '女' }}
        </el-tag>
        <span v-else>-</span>
      </template>
      
      <!-- 是否主要联系人列插槽 -->
              <template #column-contactRole="{ row }">
          <el-tag v-if="row.contactRole" type="primary">
            {{ getDictLabel('contact_role', row.contactRole) }}
          </el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      


      <!-- 联系人表格操作列插槽 -->
      <template #operation="{ row }">
        <el-tooltip content="编辑联系人" placement="top" v-if="hasPermission('crm:customer:contact:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditContact(row)" />
        </el-tooltip>

        <el-tooltip content="删除联系人" placement="top" v-if="hasPermission('crm:customer:contact:delete')">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small"
            @click="handleDeleteContact(row)" 
          />
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 联系人新增/编辑弹窗 -->
    <el-dialog
      v-model="contactCrudDialogVisible"
      :title="contactDialogTitle"
      width="60%"
      draggable
      :close-on-click-modal="false"
      append-to-body 
      @close="handleCloseContactDialog"
    >
      <!-- VNForm 用于编辑/新增联系人 -->
      <VNForm
        v-if="contactCrudDialogVisible" 
        ref="contactFormRef"
        :header-fields="contactFormFields"
        v-model="currentEditingContact"
        :loading="contactFormLoading" 
        @submit="submitContactForm"
        @cancel="handleCloseContactDialog"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTooltip, ElTag } from 'element-plus';
// 导入图标
import { Edit, Delete, Star } from '@element-plus/icons-vue';
// 导入组件
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
// 导入类型
type SortParams = { prop: string; order: 'ascending' | 'descending' | null };
type FilterParams = Record<string, any>;
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 导入API和类型
import { 
  getCustomerContacts, 
  createCustomerContact,
  updateCustomerContact,
  deleteCustomerContact,
  setPrimaryContact
} from '@/api/crm/customer';
import type { 
  CrmCustomerContactVO, 
  CrmCustomerContactCreateDTO,
  CrmCustomerContactUpdateDTO
} from '@/api/crm/customer';
// 导入权限检查函数
import { hasPermission } from '@/hooks/usePermission';
// 导入字典函数
import { useDictionary } from '@/hooks/useDictionary';

// --- 辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Props --- 
interface Props {
  customerId: number;
  customerCode: string;
  customerName: string;
}
const props = defineProps<Props>();

// --- Dictionary --- 
const { getDictLabel, getDictDataByCode, loadCrmDictData } = useDictionary();

// --- Refs ---
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;

const contactTableRef = ref<VNTableInstance>();
const contactFormRef = ref<VNFormInstance>();

// --- State ---
const contactTableData = ref<CrmCustomerContactVO[]>([]);
const contactPagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const contactLoading = ref(false);
const contactCrudDialogVisible = ref(false);
const currentEditingContact = ref<Partial<CrmCustomerContactCreateDTO | CrmCustomerContactUpdateDTO>>({});
const isContactEdit = ref(false);
const contactFormLoading = ref(false);
const currentContactSort = ref<SortParams | null>(null);
const currentContactFilters = ref<FilterParams>({});

// --- Computed --- 
const contactDialogTitle = computed(() => (isContactEdit.value ? '编辑联系人' : '新增联系人'));

// --- Table Columns (Contact) --- 
const contactTableColumns = ref<TableColumn[]>([
  { prop: 'contactName', label: '姓名', minWidth: 100, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'contactTitle', label: '职务', minWidth: 100, filterable: true, filterType: 'text' },
  { prop: 'department', label: '部门', width: 100 },
  { prop: 'phone', label: '电话', width: 120 },
  { prop: 'mobile', label: '手机', width: 120 },
  { prop: 'email', label: '邮箱', minWidth: 150, showOverflowTooltip: true },
  { prop: 'gender', label: '性别', width: 80, slot: true, filterable: true, filterType: 'select', filterOptions: getDictDataByCode('gender') },
  { prop: 'contactRole', label: '联系人角色', width: 120, slot: true },
]);

// --- Form Fields (Contact) --- 
const contactFormFields = computed<HeaderField[]>(() => [
  { 
    field: 'contactName', 
    label: '联系人姓名', 
    placeholder: '请输入联系人姓名',
    rules: [
      { required: true, message: '联系人姓名不能为空' },
      { max: 100, message: '姓名长度不能超过100' }
    ] 
  },
  { 
    field: 'contactTitle', 
    label: '职务', 
    placeholder: '请输入职务',
    rules: [{ max: 100, message: '职务长度不能超过100' }] 
  },
  { 
    field: 'department', 
    label: '所属部门', 
    placeholder: '请输入所属部门',
    rules: [{ max: 100, message: '部门长度不能超过100' }] 
  },
  { 
    field: 'phone', 
    label: '电话', 
    placeholder: '请输入电话',
    rules: [{ max: 50, message: '电话长度不能超过50' }] 
  },
  { 
    field: 'mobile', 
    label: '手机', 
    placeholder: '请输入手机',
    rules: [{ max: 50, message: '手机长度不能超过50' }] 
  },
  { 
    field: 'email', 
    label: '邮箱', 
    placeholder: '请输入邮箱',
    rules: [
      { type: 'email', message: '请输入正确的邮箱格式' },
      { max: 255, message: '邮箱长度不能超过255' }
    ] 
  },
  { 
    field: 'qq', 
    label: 'QQ号', 
    placeholder: '请输入QQ号',
    rules: [{ max: 50, message: 'QQ号长度不能超过50' }] 
  },
  { 
    field: 'wechat', 
    label: '微信号', 
    placeholder: '请输入微信号',
    rules: [{ max: 100, message: '微信号长度不能超过100' }] 
  },
  { 
    field: 'address', 
    label: '联系地址', 
    placeholder: '请输入联系地址',
    type: 'textarea',
    props: { rows: 2 }
  },
  { 
    field: 'postalCode', 
    label: '邮政编码', 
    placeholder: '请输入邮政编码',
    rules: [{ max: 20, message: '邮政编码长度不能超过20' }] 
  },
  { 
    field: 'birthday', 
    label: '生日', 
    placeholder: '请输入生日',
    type: 'date'
  },
  { 
    field: 'gender', 
    label: '性别', 
    placeholder: '请选择性别',
    type: 'select', 
    options: getDictDataByCode('gender')
  },
  { 
    field: 'contactRole', 
    label: '联系人角色', 
    placeholder: '请选择联系人角色',
    type: 'select', 
    options: getDictDataByCode('contact_role')
  },
  { 
    field: 'remark', 
    label: '备注', 
    placeholder: '请填写备注',
    type: 'textarea', 
    props: { rows: 3 },
    rules: [{ max: 500, message: '备注长度不能超过500' }] 
  },
]);

// --- Lifecycle --- 
onMounted(async () => {
  // 加载字典数据
  await loadCrmDictData();
  
  if (props.customerId) {
    loadCustomerContacts();
  }
});

// 监听 customerId 变化，重新加载数据
watch(() => props.customerId, (newCustomerId) => {
  if (newCustomerId) {
    loadCustomerContacts();
  }
});

// --- Methods ---

// 加载客户联系人数据
const loadCustomerContacts = async () => {
  if (!props.customerId) {
    console.warn('customerId is required to load contacts');
    return;
  }

  contactLoading.value = true;
  try {
    console.log('[loadCustomerContacts] Loading contacts for customer:', props.customerId);
    const contacts = await getCustomerContacts(props.customerId);

    if (Array.isArray(contacts)) { 
        contactTableData.value = contacts;
        contactPagination.total = contacts.length;
    } else {
        console.warn('getCustomerContacts 返回的数据格式不符合预期:', contacts);
        contactTableData.value = [];
        contactPagination.total = 0;
    }

  } catch (error) {
    console.error('Error loading customer contacts:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
    contactTableData.value = [];
    contactPagination.total = 0;
  } finally {
    contactLoading.value = false;
  }
};

// 处理分页变化
const handleContactPageChange = (page: number) => {
  contactPagination.currentPage = page;
  // 如果需要后端分页，这里可以调用 loadCustomerContacts()
};

// 处理每页数量变化
const handleContactPageSizeChange = (size: number) => {
  contactPagination.pageSize = size;
  contactPagination.currentPage = 1; 
  // 如果需要后端分页，这里可以调用 loadCustomerContacts()
};

// 处理排序变化
const handleContactSortChange = (sort: SortParams) => {
  console.log('Contact sort changed:', sort);
  currentContactSort.value = sort;
  // 如果需要后端排序，这里可以调用 loadCustomerContacts()
};

// 处理筛选变化
const handleContactFilterChange = (filters: FilterParams) => {
  console.log('Contact filter changed:', filters);
  currentContactFilters.value = filters; 
  // 如果需要后端筛选，这里可以调用 loadCustomerContacts()
};

// 处理新增联系人按钮点击
const handleAddNewContact = () => {
  isContactEdit.value = false;
  currentEditingContact.value = { 
    customerId: props.customerId
  };
  contactCrudDialogVisible.value = true;
  nextTick(() => { contactFormRef.value?.clearValidate(); });
};

// 处理编辑联系人按钮点击
const handleEditContact = (row: CrmCustomerContactVO) => {
  isContactEdit.value = true;
  currentEditingContact.value = JSON.parse(JSON.stringify(row));
  contactCrudDialogVisible.value = true;
  nextTick(() => { contactFormRef.value?.clearValidate(); });
};

// 关闭联系人编辑弹窗
const handleCloseContactDialog = () => {
  contactCrudDialogVisible.value = false;
};

// 提交联系人表单
const submitContactForm = async () => {
  const formIsValid = await contactFormRef.value?.validateForm();
  if (!formIsValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  contactFormLoading.value = true;
  const payload = { ...currentEditingContact.value };

  try {
    if (isContactEdit.value) {
      // 编辑操作
      const editingData = currentEditingContact.value as Partial<CrmCustomerContactVO>; 
      if (!editingData.id) {
          ElMessage.error('无法获取要编辑的联系人ID');
          contactFormLoading.value = false;
          return;
      }
      const updatePayload: Partial<CrmCustomerContactUpdateDTO> = {
         ...editingData
      };
      console.log('Calling updateCustomerContact API with ID:', editingData.id, 'and payload:', updatePayload);
      await updateCustomerContact(editingData.id, updatePayload);
      ElMessage.success('编辑成功');
    } else {
      // 新增操作
      const createPayload = {
        ...payload,
        customerId: props.customerId
      } as CrmCustomerContactCreateDTO;
      console.log('Calling createCustomerContact API with:', createPayload);
      await createCustomerContact(createPayload);
      ElMessage.success('新增成功');
    }
    handleCloseContactDialog();
    loadCustomerContacts(); // 刷新列表
  } catch (error) {
    console.error('Error saving customer contact:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    contactFormLoading.value = false;
  }
};

// 联系人表格行操作 - 删除联系人
const handleDeleteContact = async (row: CrmCustomerContactVO) => {
  console.log('Deleting contact:', row);

  try {
      await ElMessageBox.confirm(`确定删除联系人 "${row.contactName}" 吗?`, '确认删除', { 
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning' 
      });
      try {
         contactLoading.value = true;
         await deleteCustomerContact(row.id);
         ElMessage.success('删除成功');
         loadCustomerContacts(); // 刷新列表
      } catch (apiError: any) {
         console.error('Error deleting contact:', apiError);
         const errorMessage = getApiErrorMessage(apiError);
         ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
         });
      } finally {
         contactLoading.value = false;
      }
  } catch (error) {
      if (error !== 'cancel') {
          console.error('Error during delete confirmation:', error);
      } else {
           ElMessage.info('已取消删除');
      }
  }
};

// 设置主要联系人
const handleSetPrimaryContact = async (row: CrmCustomerContactVO) => {
  console.log('Setting primary contact:', row);

  try {
      await ElMessageBox.confirm(`确定将 "${row.contactName}" 设置为主要联系人吗?`, '确认设置', { 
          confirmButtonText: '确认设置',
          cancelButtonText: '取消',
          type: 'info' 
      });
      try {
         contactLoading.value = true;
         await setPrimaryContact(props.customerId, row.id);
         ElMessage.success('设置成功');
         loadCustomerContacts(); // 刷新列表
      } catch (apiError: any) {
         console.error('Error setting primary contact:', apiError);
         const errorMessage = getApiErrorMessage(apiError);
         ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errorMessage,
            showClose: true,
            duration: 5 * 1000
         });
      } finally {
         contactLoading.value = false;
      }
  } catch (error) {
      if (error !== 'cancel') {
          console.error('Error during set primary confirmation:', error);
      } else {
           ElMessage.info('已取消设置');
      }
  }
};

</script>

<style scoped>
/* Add any specific styles if needed */
</style> 