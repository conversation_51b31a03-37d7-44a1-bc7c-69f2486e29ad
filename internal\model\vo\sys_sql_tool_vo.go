package vo

// SQLResultVO 用于返回 SQL 执行结果
type SQLResultVO struct {
	ResultType   string          `json:"resultType"`             // 结果类型: "QueryResult", "RowsAffected", "Error"
	Columns      []string        `json:"columns,omitempty"`      // 查询结果的列名 (仅当 ResultType="QueryResult")
	Rows         [][]interface{} `json:"rows,omitempty"`         // 查询结果的数据 (仅当 ResultType="QueryResult"), 每个内部数组代表一行
	RowsAffected *int64          `json:"rowsAffected,omitempty"` // 受影响的行数 (用于 INSERT/UPDATE/DELETE, 使用指针区分0和无结果)
	Error        string          `json:"error,omitempty"`        // 错误信息 (仅当 ResultType="Error")
	ExecutedSQL  string          `json:"executedSql"`            // 实际执行的 SQL 语句 (用于确认)
	DurationMS   int64           `json:"durationMs"`             // 执行耗时（毫秒）
	Message      string          `json:"message,omitempty"`      // 附加消息，例如警告或说明
}

// 可选的替代 Rows 格式 (如果前端更容易处理对象数组):
// type SQLResultVO_Alt struct {
//     ...
//     Rows         []map[string]interface{} `json:"rows,omitempty"`
//     ...
// }
