package vo

import (
	"time"
	"backend/internal/model/entity"
)

// WmsLocationVO 单个库位的详细视图对象
type WmsLocationVO struct {
	ID                 uint      `json:"id"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
	CreatedBy          uint      `json:"createdBy"`
	UpdatedBy          uint      `json:"updatedBy"`
	TenantID           uint      `json:"tenantId"`
	AccountBookID      uint      `json:"accountBookId"`
	Code               string    `json:"code"`
	Name               *string   `json:"name,omitempty"`
	Type               string    `json:"type"`
	WarehouseID        uint      `json:"warehouseId"`
	ParentID           *uint     `json:"parentId,omitempty"`
	Status             string    `json:"status"`
	Remark             *string   `json:"remark,omitempty"`
	IsPickable         bool      `json:"isPickable"`
	IsPutaway          bool      `json:"isPutaway"`
	IsInventoryTracked bool      `json:"isInventoryTracked"`
	Address            *string   `json:"address,omitempty"`
	Contact            *string   `json:"contact,omitempty"`
	Phone              *string   `json:"phone,omitempty"`
	TemperatureZone    *string   `json:"temperatureZone,omitempty"`
	HazardLevel        *string   `json:"hazardLevel,omitempty"`
	SecurityLevel      *string   `json:"securityLevel,omitempty"`
	WeightClass        *string   `json:"weightClass,omitempty"`
	StorageType        *string   `json:"storageType,omitempty"`
	RequiresEquipment  *string   `json:"requiresEquipment,omitempty"`
	MaxWeightKg        *float64  `json:"maxWeightKg,omitempty"`
	MaxVolumeM3        *float64  `json:"maxVolumeM3,omitempty"`
	MaxPallets         *int64    `json:"maxPallets,omitempty"`
	MaxLengthM         *float64  `json:"maxLengthM,omitempty"`
	MaxWidthM          *float64  `json:"maxWidthM,omitempty"`
	MaxHeightM         *float64  `json:"maxHeightM,omitempty"`
	MaxItemUnits       *int64    `json:"maxItemUnits,omitempty"`
}

// WmsLocationTreeVO 库位层级树的视图对象
type WmsLocationTreeVO struct {
	ID                 uint                 `json:"id"`
	CreatedAt          time.Time            `json:"createdAt"`
	UpdatedAt          time.Time            `json:"updatedAt"`
	Code               string               `json:"code"`
	Name               *string              `json:"name,omitempty"`
	Type               string               `json:"type"`
	ParentID           *uint                `json:"parentId,omitempty"`
	WarehouseID        uint                 `json:"warehouseId"`
	Status             string               `json:"status"`
	IsPickable         bool                 `json:"isPickable"`
	IsPutaway          bool                 `json:"isPutaway"`
	IsInventoryTracked bool                 `json:"isInventoryTracked"`
	Remark             *string              `json:"remark,omitempty"`
	Children           []*WmsLocationTreeVO `json:"children"` // 子节点列表，为空时返回 [] 而不是 null
}

// WmsWarehouseSimpleVO 简化仓库信息视图对象 (用于下拉列表)
type WmsWarehouseSimpleVO struct {
	ID   uint   `json:"id"`
	Code string `json:"code"`
	Name string `json:"name"`
}

// PageResult 是用于VO的分页结果泛型结构
type PageResult[T any] struct {
	List     []*T  `json:"list"`
	Total    int64 `json:"total"`
	PageNum  int   `json:"pageNum"`
	PageSize int   `json:"pageSize"`
}

// NewWmsLocationVO 创建库位视图对象
func NewWmsLocationVO(location *entity.WmsLocation) *WmsLocationVO {
	if location == nil {
		return nil
	}

	return &WmsLocationVO{
		ID:                 location.ID,
		CreatedAt:          location.CreatedAt,
		UpdatedAt:          location.UpdatedAt,
		CreatedBy:          location.CreatedBy,
		UpdatedBy:          location.UpdatedBy,
		TenantID:           location.TenantID,
		AccountBookID:      location.AccountBookID,
		Code:               location.Code,
		Name:               location.Name,
		Type:               string(location.Type),
		WarehouseID:        location.WarehouseID,
		ParentID:           location.ParentID,
		Status:             string(location.Status),
		Remark:             location.Remark,
		IsPickable:         location.IsPickable,
		IsPutaway:          location.IsPutaway,
		IsInventoryTracked: location.IsInventoryTracked,
		Address:            location.Address,
		Contact:            location.Contact,
		Phone:              location.Phone,
	}
}
