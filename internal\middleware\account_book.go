package middleware

import (
	"context"
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/repository"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util"
)

// AccountBookContextHandler 创建一个处理账套上下文的中间件
// 它需要 RepositoryManager 来验证用户账套权限
func AccountBookContextHandler(repoManager *repository.RepositoryManager) iris.Handler {
	userAccountBookRepo := repoManager.GetUserAccountBookRepository()
	if userAccountBookRepo == nil {
		logger.GetLogger().Fatal("无法获取 UserAccountBookRepository 实例")
	}

	return func(ctx iris.Context) {
		log := logger.GetLogger()
		reqCtx := ctx.Request().Context()

		// 从上下文中获取用户信息
		isAdmin, err := util.IsAdminFromStdContext(reqCtx)
		if err != nil {
			response.FailWithError(ctx, err)
			return
		}

		accountBookIDStr := ctx.GetHeader("X-AccountBook-ID")
		if accountBookIDStr == "" {
			log.Warn(reqCtx, "AccountBookContextHandler: Missing X-AccountBook-ID header")
			response.FailWithError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "缺少 X-AccountBook-ID 请求头"))
			return
		}
		accountBookIDUint64, err := strconv.ParseUint(accountBookIDStr, 10, 32)
		if err != nil {
			log.Warn(reqCtx, "AccountBookContextHandler: Invalid X-AccountBook-ID format", logger.WithError(err), logger.WithField("headerValue", accountBookIDStr))
			response.FailWithError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的 X-AccountBook-ID 格式"))
			return
		}
		accountBookID := uint(accountBookIDUint64)
		if accountBookID == 0 {
			log.Warn(reqCtx, "AccountBookContextHandler: X-AccountBook-ID cannot be 0")
			response.FailWithError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_VALUE_ERROR, "请先选择账套，再进行操作"))
			return
		}

		// 如果是管理员，跳过权限检查但仍然设置账套ID到上下文
		if isAdmin {
			reqCtxWithAccountBook := context.WithValue(reqCtx, constant.CONTEXT_ACCOUNT_BOOK_ID, accountBookID)
			ctx.ResetRequest(ctx.Request().WithContext(reqCtxWithAccountBook))
			log.Debug(reqCtxWithAccountBook, "AccountBookContextHandler: Admin user, skip permission check but set AccountBookID", logger.WithField("accountBookID", accountBookID))
			ctx.Next()
			return
		}

		userID64, errUser := util.GetUserIDFromStdContext(reqCtx)
		if errUser != nil {
			customErr, ok := errUser.(*apperrors.CustomError)
			if ok {
				log.Warn(reqCtx, "AccountBookContextHandler: Failed to get UserID from context", logger.WithError(errUser), logger.WithField("errorCode", customErr.Code))
			} else {
				log.Warn(reqCtx, "AccountBookContextHandler: Failed to get UserID from context (error not CustomError)", logger.WithError(errUser))
			}
			response.FailWithError(ctx, errUser)
			return
		}
		userID := uint(userID64)

		hasAccess, errDb := userAccountBookRepo.Exists(reqCtx, userID, accountBookID)
		if errDb != nil {
			log.Error(reqCtx, "AccountBookContextHandler: Database error checking user account book access", logger.WithError(errDb), logger.WithField("userID", userID), logger.WithField("accountBookID", accountBookID))
			response.FailWithError(ctx, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查用户账套权限失败").WithCause(errDb))
			return
		}
		if !hasAccess {
			log.Warn(reqCtx, "AccountBookContextHandler: User does not have access to the specified account book", logger.WithField("userID", userID), logger.WithField("accountBookID", accountBookID))
			response.FailWithError(ctx, apperrors.NewAuthError(apperrors.CODE_AUTH_FORBIDDEN, "无权访问该账套"))
			return
		}

		reqCtxWithAccountBook := context.WithValue(reqCtx, constant.CONTEXT_ACCOUNT_BOOK_ID, accountBookID)

		ctx.ResetRequest(ctx.Request().WithContext(reqCtxWithAccountBook))

		log.Debug(reqCtxWithAccountBook, "AccountBookContextHandler: Context updated with AccountBookID", logger.WithField("accountBookID", accountBookID), logger.WithField("userID", userID))
		ctx.Next()
	}
}
