package storage

import (
	"context"
	"io"
	"sync"
	"time"
)

// 存储类型常量
const (
	STORAGE_TYPE_LOCAL = "local" // 本地存储
	STORAGE_TYPE_S3    = "s3"    // S3兼容存储
)

// 文件相关常量
const (
	DEFAULT_EXPIRES_TIME = 7 * 24 * time.Hour // 默认过期时间7天
	DEFAULT_CHUNK_SIZE   = 1024 * 1024 * 2    // 默认分块大小2MB
	DEFAULT_BUFFER_SIZE  = 1024 * 1024 * 4    // 默认缓冲区大小4MB
)

// FileInfo 文件信息结构体，包含文件的基本属性和元数据
type FileInfo struct {
	Name         string            `json:"name"`         // 文件名
	Path         string            `json:"path"`         // 路径
	URL          string            `json:"url"`          // 访问URL
	Size         int64             `json:"size"`         // 大小(字节)
	ContentType  string            `json:"contentType"`  // 内容类型
	Extension    string            `json:"extension"`    // 扩展名
	Hash         string            `json:"hash"`         // 哈希值
	CreateTime   time.Time         `json:"createTime"`   // 创建时间
	ModifyTime   time.Time         `json:"modifyTime"`   // 修改时间
	IsDir        bool              `json:"isDir"`        // 是否是目录
	StorageType  string            `json:"storageType"`  // 存储类型
	ExpiresTime  time.Time         `json:"expiresTime"`  // 过期时间
	LastAccessed time.Time         `json:"lastAccessed"` // 最后访问时间
	Metadata     map[string]string `json:"metadata"`     // 元数据
}

// UploadOptions 上传文件时的可选参数
type UploadOptions struct {
	ContentType string            // 内容类型
	Metadata    map[string]string // 元数据
	IsPublic    bool              // 是否公开访问
	Expires     time.Duration     // 过期时间
	ChunkSize   int64             // 分块大小
	BufferSize  int64             // 缓冲区大小
}

// DownloadOptions 下载文件时的可选参数
type DownloadOptions struct {
	Range      string        // 范围请求
	IfModified time.Time     // 如果修改过
	Timeout    time.Duration // 超时时间
	BufferSize int64         // 缓冲区大小
}

// ListOptions 列出文件时的可选参数
type ListOptions struct {
	Prefix    string // 前缀
	Recursive bool   // 是否递归
	MaxKeys   int    // 最大数量
	Marker    string // 起始位置
	Delimiter string // 分隔符
}

// StorageConfig 存储配置，定义存储服务的连接参数和行为设置
type StorageConfig struct {
	Type          string            `json:"type" yaml:"type"`                   // 存储类型：local, s3
	RootDir       string            `json:"rootDir" yaml:"rootDir"`             // 本地存储根目录
	Endpoint      string            `json:"endpoint" yaml:"endpoint"`           // S3端点
	Region        string            `json:"region" yaml:"region"`               // S3区域
	Bucket        string            `json:"bucket" yaml:"bucket"`               // S3桶名
	AccessKey     string            `json:"accessKey" yaml:"accessKey"`         // 访问密钥ID
	SecretKey     string            `json:"secretKey" yaml:"secretKey"`         // 秘密访问密钥
	SSL           bool              `json:"ssl" yaml:"ssl"`                     // 是否使用SSL
	BaseURL       string            `json:"baseUrl" yaml:"baseUrl"`             // 基础URL
	Timeout       int               `json:"timeout" yaml:"timeout"`             // 超时时间(秒)
	ChunkSize     int64             `json:"chunkSize" yaml:"chunkSize"`         // 分块大小(字节)
	MaxFileSize   int64             `json:"maxFileSize" yaml:"maxFileSize"`     // 最大文件大小(字节)
	AllowedTypes  []string          `json:"allowedTypes" yaml:"allowedTypes"`   // 允许的文件类型
	AllowedExts   []string          `json:"allowedExts" yaml:"allowedExts"`     // 允许的扩展名
	CustomHeaders map[string]string `json:"customHeaders" yaml:"customHeaders"` // 自定义HTTP头
}

// Storage 存储接口，定义了文件存储系统需要支持的功能
type Storage interface {
	// 上传相关
	// Upload 上传文件内容
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	//   - reader: 读取器
	//   - options: 上传选项
	// 返回:
	//   - FileInfo: 文件信息
	//   - error: 错误信息
	Upload(ctx context.Context, objectKey string, reader io.Reader, options ...UploadOptions) (FileInfo, error)

	// UploadFile 上传本地文件
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	//   - filePath: 文件路径
	//   - options: 上传选项
	// 返回:
	//   - FileInfo: 文件信息
	//   - error: 错误信息
	UploadFile(ctx context.Context, objectKey, filePath string, options ...UploadOptions) (FileInfo, error)

	// 下载相关
	// Download 下载文件到写入器
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	//   - writer: 写入器
	//   - options: 下载选项
	// 返回:
	//   - error: 错误信息
	Download(ctx context.Context, objectKey string, writer io.Writer, options ...DownloadOptions) error

	// DownloadFile 下载文件到本地
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	//   - filePath: 文件路径
	//   - options: 下载选项
	// 返回:
	//   - error: 错误信息
	DownloadFile(ctx context.Context, objectKey, filePath string, options ...DownloadOptions) error

	// 信息相关
	// Stat 获取文件信息
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	// 返回:
	//   - FileInfo: 文件信息
	//   - error: 错误信息
	Stat(ctx context.Context, objectKey string) (FileInfo, error)

	// List 列出符合条件的文件
	// 参数:
	//   - ctx: 上下文
	//   - prefix: 前缀
	//   - options: 列表选项
	// 返回:
	//   - []FileInfo: 文件信息列表
	//   - error: 错误信息
	List(ctx context.Context, prefix string, options ...ListOptions) ([]FileInfo, error)

	// 管理相关
	// Delete 删除文件
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	// 返回:
	//   - error: 错误信息
	Delete(ctx context.Context, objectKey string) error

	// DeleteBatch 批量删除文件
	// 参数:
	//   - ctx: 上下文
	//   - objectKeys: 对象键列表
	// 返回:
	//   - error: 错误信息
	DeleteBatch(ctx context.Context, objectKeys []string) error

	// Copy 复制文件
	// 参数:
	//   - ctx: 上下文
	//   - sourceKey: 源对象键
	//   - destKey: 目标对象键
	// 返回:
	//   - error: 错误信息
	Copy(ctx context.Context, sourceKey, destKey string) error

	// Move 移动文件
	// 参数:
	//   - ctx: 上下文
	//   - sourceKey: 源对象键
	//   - destKey: 目标对象键
	// 返回:
	//   - error: 错误信息
	Move(ctx context.Context, sourceKey, destKey string) error

	// Rename 重命名文件
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	//   - newName: 新名称
	// 返回:
	//   - error: 错误信息
	Rename(ctx context.Context, objectKey, newName string) error

	// URL相关
	// GetURL 获取文件URL
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	//   - expires: 过期时间
	// 返回:
	//   - string: URL
	//   - error: 错误信息
	GetURL(ctx context.Context, objectKey string, expires ...time.Duration) (string, error)

	// IsExist 检查文件是否存在
	// 参数:
	//   - ctx: 上下文
	//   - objectKey: 对象键
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	IsExist(ctx context.Context, objectKey string) (bool, error)

	// 高级功能
	// CreateDir 创建目录
	// 参数:
	//   - ctx: 上下文
	//   - dirKey: 目录键
	// 返回:
	//   - error: 错误信息
	CreateDir(ctx context.Context, dirKey string) error

	// GetConfig 获取存储配置
	// 返回:
	//   - StorageConfig: 存储配置
	GetConfig() StorageConfig

	// Close 关闭存储
	// 返回:
	//   - error: 错误信息
	Close() error
}

// 全局变量
var (
	defaultStorage  Storage
	storageInstance Storage
	once            sync.Once
)

// Config 存储配置（用于外部调用）
var Config StorageConfig

// SetDefaultStorage 设置默认存储实例
// 参数:
//   - storage: 存储实例
func SetDefaultStorage(storage Storage) {
	defaultStorage = storage
}

// GetStorage 获取当前存储实例，如果未初始化则返回默认存储
// 返回:
//   - Storage: 存储接口实现
func GetStorage() Storage {
	if storageInstance == nil {
		return defaultStorage
	}
	return storageInstance
}

// NewStorage 根据配置创建新的存储实例
// 参数:
//   - config: 存储配置
//
// 返回:
//   - Storage: 存储接口实现
//   - error: 错误信息
func NewStorage(config StorageConfig) (Storage, error) {
	switch config.Type {
	case STORAGE_TYPE_LOCAL:
		return NewLocalStorage(config)
	case STORAGE_TYPE_S3:
		return NewS3Storage(config)
	default:
		return nil, ErrUnsupportedStorageType
	}
}

// WithContext 创建具有上下文的存储操作
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - Storage: 存储接口实现
func WithContext(ctx context.Context) Storage {
	if storageInstance == nil {
		return defaultStorage
	}
	return storageInstance
}

// 错误定义
var (
	ErrUnsupportedStorageType = NewStorageError("不支持的存储类型", "STORAGE_TYPE_ERROR")
	ErrObjectNotFound         = NewStorageError("对象不存在", "OBJECT_NOT_FOUND")
	ErrInvalidObjectKey       = NewStorageError("无效的对象键", "INVALID_OBJECT_KEY")
	ErrInvalidBucket          = NewStorageError("无效的存储桶", "INVALID_BUCKET")
	ErrUploadFailed           = NewStorageError("上传失败", "UPLOAD_FAILED")
	ErrDownloadFailed         = NewStorageError("下载失败", "DOWNLOAD_FAILED")
	ErrFileTooLarge           = NewStorageError("文件太大", "FILE_TOO_LARGE")
	ErrFileTypeNotAllowed     = NewStorageError("不允许的文件类型", "FILE_TYPE_NOT_ALLOWED")
	ErrAccessDenied           = NewStorageError("访问被拒绝", "ACCESS_DENIED")
	ErrTimeout                = NewStorageError("操作超时", "OPERATION_TIMEOUT")
	ErrStorageNotInitialized  = NewStorageError("存储未初始化", "STORAGE_NOT_INITIALIZED")
)

// StorageError 存储错误结构体，包含错误消息和错误代码
type StorageError struct {
	Message string // 错误消息
	Code    string // 错误代码
}

// Error 实现error接口，返回错误消息
// 返回:
//   - string: 错误消息
func (e *StorageError) Error() string {
	return e.Message
}

// NewStorageError 创建新的存储错误
// 参数:
//   - message: 错误消息
//   - code: 错误代码
//
// 返回:
//   - *StorageError: 存储错误
func NewStorageError(message, code string) *StorageError {
	return &StorageError{
		Message: message,
		Code:    code,
	}
}

// 以下是包级别的便捷函数，直接调用当前存储实例的对应方法

// Upload 上传文件内容
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//   - reader: 读取器
//   - options: 上传选项
//
// 返回:
//   - FileInfo: 文件信息
//   - error: 错误信息
func Upload(ctx context.Context, objectKey string, reader io.Reader, options ...UploadOptions) (FileInfo, error) {
	return GetStorage().Upload(ctx, objectKey, reader, options...)
}

// UploadFile 上传本地文件
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//   - filePath: 文件路径
//   - options: 上传选项
//
// 返回:
//   - FileInfo: 文件信息
//   - error: 错误信息
func UploadFile(ctx context.Context, objectKey, filePath string, options ...UploadOptions) (FileInfo, error) {
	return GetStorage().UploadFile(ctx, objectKey, filePath, options...)
}

// Download 下载文件到写入器
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//   - writer: 写入器
//   - options: 下载选项
//
// 返回:
//   - error: 错误信息
func Download(ctx context.Context, objectKey string, writer io.Writer, options ...DownloadOptions) error {
	return GetStorage().Download(ctx, objectKey, writer, options...)
}

// DownloadFile 下载文件到本地
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//   - filePath: 文件路径
//   - options: 下载选项
//
// 返回:
//   - error: 错误信息
func DownloadFile(ctx context.Context, objectKey, filePath string, options ...DownloadOptions) error {
	return GetStorage().DownloadFile(ctx, objectKey, filePath, options...)
}

// Stat 获取文件信息
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//
// 返回:
//   - FileInfo: 文件信息
//   - error: 错误信息
func Stat(ctx context.Context, objectKey string) (FileInfo, error) {
	return GetStorage().Stat(ctx, objectKey)
}

// List 列出符合条件的文件
// 参数:
//   - ctx: 上下文
//   - prefix: 前缀
//   - options: 列表选项
//
// 返回:
//   - []FileInfo: 文件信息列表
//   - error: 错误信息
func List(ctx context.Context, prefix string, options ...ListOptions) ([]FileInfo, error) {
	return GetStorage().List(ctx, prefix, options...)
}

// Delete 删除文件
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//
// 返回:
//   - error: 错误信息
func Delete(ctx context.Context, objectKey string) error {
	return GetStorage().Delete(ctx, objectKey)
}

// DeleteBatch 批量删除文件
// 参数:
//   - ctx: 上下文
//   - objectKeys: 对象键列表
//
// 返回:
//   - error: 错误信息
func DeleteBatch(ctx context.Context, objectKeys []string) error {
	return GetStorage().DeleteBatch(ctx, objectKeys)
}

// Copy 复制文件
// 参数:
//   - ctx: 上下文
//   - sourceKey: 源对象键
//   - destKey: 目标对象键
//
// 返回:
//   - error: 错误信息
func Copy(ctx context.Context, sourceKey, destKey string) error {
	return GetStorage().Copy(ctx, sourceKey, destKey)
}

// Move 移动文件
// 参数:
//   - ctx: 上下文
//   - sourceKey: 源对象键
//   - destKey: 目标对象键
//
// 返回:
//   - error: 错误信息
func Move(ctx context.Context, sourceKey, destKey string) error {
	return GetStorage().Move(ctx, sourceKey, destKey)
}

// Rename 重命名文件
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//   - newName: 新名称
//
// 返回:
//   - error: 错误信息
func Rename(ctx context.Context, objectKey, newName string) error {
	return GetStorage().Rename(ctx, objectKey, newName)
}

// GetURL 获取文件URL
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//   - expires: 过期时间
//
// 返回:
//   - string: URL
//   - error: 错误信息
func GetURL(ctx context.Context, objectKey string, expires ...time.Duration) (string, error) {
	return GetStorage().GetURL(ctx, objectKey, expires...)
}

// IsExist 检查文件是否存在
// 参数:
//   - ctx: 上下文
//   - objectKey: 对象键
//
// 返回:
//   - bool: 是否存在
//   - error: 错误信息
func IsExist(ctx context.Context, objectKey string) (bool, error) {
	return GetStorage().IsExist(ctx, objectKey)
}

// CreateDir 创建目录
// 参数:
//   - ctx: 上下文
//   - dirKey: 目录键
//
// 返回:
//   - error: 错误信息
func CreateDir(ctx context.Context, dirKey string) error {
	return GetStorage().CreateDir(ctx, dirKey)
}

// Close 关闭存储
// 返回:
//   - error: 错误信息
func Close() error {
	if storageInstance != nil {
		return storageInstance.Close()
	}
	if defaultStorage != nil {
		return defaultStorage.Close()
	}
	return nil
}
