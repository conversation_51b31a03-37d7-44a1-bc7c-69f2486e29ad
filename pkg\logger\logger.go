package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// 日志级别常量
const (
	DEBUG_LEVEL = "debug" // 调试级别
	INFO_LEVEL  = "info"  // 信息级别
	WARN_LEVEL  = "warn"  // 警告级别
	ERROR_LEVEL = "error" // 错误级别
	FATAL_LEVEL = "fatal" // 致命级别
)

// 日志格式常量
const (
	JSON_FORMAT    = "json"    // JSON格式
	CONSOLE_FORMAT = "console" // 控制台格式
)

// 日志上下文常量
const (
	CONTEXT_REQUEST_ID = "requestID" // 请求ID
	CONTEXT_USER_ID    = "userID"    // 用户ID
	CONTEXT_USERNAME   = "username"  // 用户名
	CONTEXT_IP         = "ip"        // IP地址
	CONTEXT_PATH       = "path"      // 请求路径
)

// ZapLogger Zap日志实现
type ZapLogger struct {
	logger      *zap.Logger            // 原始zap日志器
	sugarLogger *zap.SugaredLogger     // 语法糖日志器
	fields      map[string]interface{} // 日志字段
}

// LogConfig 日志配置
type LogConfig struct {
	Level        string `json:"level" yaml:"level"`               // 日志级别：debug, info, warn, error, fatal
	Format       string `json:"format" yaml:"format"`             // 日志格式：json, console
	Prefix       string `json:"prefix" yaml:"prefix"`             // 日志前缀
	Director     string `json:"director" yaml:"director"`         // 日志文件夹
	ShowLine     bool   `json:"showLine" yaml:"showLine"`         // 显示行号
	EncodeTime   string `json:"encodeTime" yaml:"encodeTime"`     // 时间格式：RFC3339, ISO8601, millis
	LogInFile    bool   `json:"logInFile" yaml:"logInFile"`       // 是否记录到文件
	LogInConsole bool   `json:"logInConsole" yaml:"logInConsole"` // 是否输出到控制台
	MaxSize      int    `json:"maxSize" yaml:"maxSize"`           // 单个日志文件大小，单位MB
	MaxBackups   int    `json:"maxBackups" yaml:"maxBackups"`     // 日志备份数量
	MaxAge       int    `json:"maxAge" yaml:"maxAge"`             // 日志保留天数
	Compress     bool   `json:"compress" yaml:"compress"`         // 是否压缩日志
}

// Fields 日志字段类型
type Fields map[string]interface{}

// Logger 日志接口，定义了所有日志操作方法
type Logger interface {
	// 基本日志方法
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
	Info(args ...interface{})
	Infof(format string, args ...interface{})
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})
	Error(args ...interface{})
	Errorf(format string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(format string, args ...interface{})

	// 日志上下文方法
	WithField(key string, value interface{}) Logger
	WithFields(fields map[string]interface{}) Logger
	WithError(err error) Logger
}

// LevelEnabler 自定义级别启用器，实现zapcore.LevelEnabler接口
type LevelEnabler struct {
	level  zapcore.Level
	filter func(zapcore.Level) bool
}

// Enabled 实现zapcore.LevelEnabler接口
// 参数:
//   - lvl: 日志级别
//
// 返回:
//   - bool: 是否启用该级别
func (l *LevelEnabler) Enabled(lvl zapcore.Level) bool {
	return l.filter(lvl)
}

// InfoLevelEnabler 创建信息级别启用器
// 参数:
//   - level: 基础日志级别
//
// 返回:
//   - *LevelEnabler: 信息级别启用器
func InfoLevelEnabler(level zapcore.Level) *LevelEnabler {
	return &LevelEnabler{
		level: level,
		filter: func(lvl zapcore.Level) bool {
			return lvl >= level && lvl < zapcore.ErrorLevel
		},
	}
}

// ErrorLevelEnabler 创建错误级别启用器
// 返回:
//   - *LevelEnabler: 错误级别启用器
func ErrorLevelEnabler() *LevelEnabler {
	return &LevelEnabler{
		level: zapcore.ErrorLevel,
		filter: func(lvl zapcore.Level) bool {
			return lvl >= zapcore.ErrorLevel
		},
	}
}

// 确保初始化的线程安全
var (
	defaultLogger Logger
	once          sync.Once
)

// 默认日志配置
var defaultConfig = LogConfig{
	Level:        INFO_LEVEL,
	Format:       CONSOLE_FORMAT,
	Prefix:       "",
	Director:     "logs",
	ShowLine:     true,
	EncodeTime:   "ISO8601",
	LogInFile:    true,
	LogInConsole: true,
	MaxSize:      10,
	MaxBackups:   10,
	MaxAge:       30,
	Compress:     false,
}

// Init 初始化日志系统
// 参数:
//   - config: 日志配置，如果为nil则使用默认配置
func Init(config *LogConfig) {
	once.Do(func() {
		// 如果未传入配置，使用默认配置
		if config == nil {
			config = &defaultConfig
		}
		defaultLogger = NewZapLogger(config)
	})
}

// GetLogger 获取全局日志实例
// 返回:
//   - Logger: 日志接口实例
func GetLogger() Logger {
	if defaultLogger == nil {
		Init(nil)
	}
	return defaultLogger
}

// NewZapLogger 创建新的zap日志实例
// 参数:
//   - config: 日志配置
//
// 返回:
//   - Logger: 日志接口实例
func NewZapLogger(config *LogConfig) Logger {
	// 创建日志目录
	if config.LogInFile {
		if err := os.MkdirAll(config.Director, 0766); err != nil {
			fmt.Printf("创建日志目录失败: %v\n", err)
			// 如果无法创建日志目录，禁用文件日志
			config.LogInFile = false
		}
	}

	// 设置基础配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     GetTimeEncoder(config.EncodeTime),
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 设置日志级别
	level := GetLogLevel(config.Level)

	// 设置日志编码器
	var encoder zapcore.Encoder
	if config.Format == JSON_FORMAT {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 设置日志写入器
	var coreArr []zapcore.Core

	// 添加控制台输出
	if config.LogInConsole {
		consoleWriter := zapcore.AddSync(os.Stdout)
		coreArr = append(coreArr, zapcore.NewCore(encoder, consoleWriter, level))
	}

	// 添加文件输出
	if config.LogInFile {
		// 配置日志轮转
		infoWriter := GetLogWriter(filepath.Join(config.Director, "info.log"), config)
		errorWriter := GetLogWriter(filepath.Join(config.Director, "error.log"), config)

		// 创建日志级别过滤器
		infoLevel := InfoLevelEnabler(level) // 使用从配置解析的 level
		errorLevel := ErrorLevelEnabler()

		// 创建CoreArr
		coreArr = append(coreArr,
			zapcore.NewCore(encoder, zapcore.AddSync(infoWriter), infoLevel),
			zapcore.NewCore(encoder, zapcore.AddSync(errorWriter), errorLevel),
		)
	}

	// 如果 coreArr 为空（例如控制台和文件都禁用），添加一个 NopCore 防止 zap panic
	if len(coreArr) == 0 {
		coreArr = append(coreArr, zapcore.NewNopCore())
	}

	// 创建zap logger
	core := zapcore.NewTee(coreArr...)
	zapOptions := []zap.Option{zap.AddCaller()}
	if config.ShowLine {
		zapOptions = append(zapOptions, zap.AddCallerSkip(1))
	}

	logger := zap.New(core, zapOptions...)

	// 返回封装的logger
	return &ZapLogger{
		logger:      logger,
		sugarLogger: logger.Sugar(),
		fields:      make(map[string]interface{}),
	}
}

// GetLogWriter 获取日志写入器
// 参数:
//   - filename: 日志文件名
//   - config: 日志配置
//
// 返回:
//   - io.Writer: 日志写入器
func GetLogWriter(filename string, config *LogConfig) io.Writer {
	return &lumberjack.Logger{
		Filename:   filename,
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   config.Compress,
	}
}

// GetTimeEncoder 获取时间编码器
// 参数:
//   - encodeTime: 时间编码格式
//
// 返回:
//   - zapcore.TimeEncoder: 时间编码器
func GetTimeEncoder(encodeTime string) zapcore.TimeEncoder {
	switch strings.ToLower(encodeTime) {
	case "rfc3339":
		return zapcore.RFC3339TimeEncoder
	case "millis":
		return zapcore.EpochMillisTimeEncoder
	default:
		return zapcore.ISO8601TimeEncoder
	}
}

// GetLogLevel 获取日志级别
// 参数:
//   - level: 日志级别字符串
//
// 返回:
//   - zapcore.Level: zap日志级别
func GetLogLevel(level string) zapcore.Level {
	switch strings.ToLower(level) {
	case DEBUG_LEVEL:
		return zapcore.DebugLevel
	case INFO_LEVEL:
		return zapcore.InfoLevel
	case WARN_LEVEL:
		return zapcore.WarnLevel
	case ERROR_LEVEL:
		return zapcore.ErrorLevel
	case FATAL_LEVEL:
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

// Debug 输出调试日志
// 参数:
//   - args: 日志内容
func (l *ZapLogger) Debug(args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Debug(args...)
}

// Debugf 输出调试日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func (l *ZapLogger) Debugf(format string, args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Debugf(format, args...)
}

// Info 输出信息日志
// 参数:
//   - args: 日志内容
func (l *ZapLogger) Info(args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Info(args...)
}

// Infof 输出信息日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func (l *ZapLogger) Infof(format string, args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Infof(format, args...)
}

// Warn 输出警告日志
// 参数:
//   - args: 日志内容
func (l *ZapLogger) Warn(args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Warn(args...)
}

// Warnf 输出警告日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func (l *ZapLogger) Warnf(format string, args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Warnf(format, args...)
}

// Error 输出错误日志
// 参数:
//   - args: 日志内容
func (l *ZapLogger) Error(args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Error(args...)
}

// Errorf 输出错误日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func (l *ZapLogger) Errorf(format string, args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Errorf(format, args...)
}

// Fatal 输出致命错误日志
// 参数:
//   - args: 日志内容
func (l *ZapLogger) Fatal(args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Fatal(args...)
}

// Fatalf 输出致命错误日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func (l *ZapLogger) Fatalf(format string, args ...interface{}) {
	l.sugarLogger.With(ZapFields(l.fields)...).Fatalf(format, args...)
}

// WithField 添加单个字段到日志
// 参数:
//   - key: 字段名
//   - value: 字段值
//
// 返回:
//   - Logger: 新的日志实例
func (l *ZapLogger) WithField(key string, value interface{}) Logger {
	newLogger := &ZapLogger{
		logger:      l.logger,
		sugarLogger: l.sugarLogger,
		fields:      make(map[string]interface{}),
	}

	// 复制现有字段
	for k, v := range l.fields {
		newLogger.fields[k] = v
	}

	// 添加新字段
	newLogger.fields[key] = value
	return newLogger
}

// WithFields 添加多个字段到日志
// 参数:
//   - fields: 字段映射
//
// 返回:
//   - Logger: 新的日志实例
func (l *ZapLogger) WithFields(fields map[string]interface{}) Logger {
	newLogger := &ZapLogger{
		logger:      l.logger,
		sugarLogger: l.sugarLogger,
		fields:      make(map[string]interface{}),
	}

	// 复制现有字段
	for k, v := range l.fields {
		newLogger.fields[k] = v
	}

	// 添加新字段
	for k, v := range fields {
		newLogger.fields[k] = v
	}
	return newLogger
}

// WithError 添加错误信息到日志
func (l *ZapLogger) WithError(err error) Logger {
	if err == nil {
		// 如果错误是 nil，直接返回原始 logger
		return l
	}
	// 如果错误不是 nil，使用 WithField 添加错误信息
	// 注意：这里假设 l.WithField 会返回一个新的 Logger 实例，并且不会修改原始实例
	return l.WithField("error", err.Error())
}

// ZapFields 将map转换为zap字段
// 参数:
//   - fields: 字段映射
//
// 返回:
//   - []interface{}: zap字段列表
func ZapFields(fields map[string]interface{}) []interface{} {
	var result []interface{}
	for k, v := range fields {
		result = append(result, k)
		result = append(result, v)
	}
	return result
}

// 包级函数，方便直接使用

// Debug 输出调试日志
// 参数:
//   - args: 日志内容
func Debug(args ...interface{}) {
	GetLogger().Debug(args...)
}

// Debugf 输出调试日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func Debugf(format string, args ...interface{}) {
	GetLogger().Debugf(format, args...)
}

// Info 输出信息日志
// 参数:
//   - args: 日志内容
func Info(args ...interface{}) {
	GetLogger().Info(args...)
}

// Infof 输出信息日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func Infof(format string, args ...interface{}) {
	GetLogger().Infof(format, args...)
}

// Warn 输出警告日志
// 参数:
//   - args: 日志内容
func Warn(args ...interface{}) {
	GetLogger().Warn(args...)
}

// Warnf 输出警告日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func Warnf(format string, args ...interface{}) {
	GetLogger().Warnf(format, args...)
}

// Error 输出错误日志
// 参数:
//   - args: 日志内容
func Error(args ...interface{}) {
	GetLogger().Error(args...)
}

// Errorf 输出错误日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func Errorf(format string, args ...interface{}) {
	GetLogger().Errorf(format, args...)
}

// Fatal 输出致命错误日志
// 参数:
//   - args: 日志内容
func Fatal(args ...interface{}) {
	GetLogger().Fatal(args...)
}

// Fatalf 输出致命错误日志（格式化）
// 参数:
//   - format: 格式化字符串
//   - args: 参数列表
func Fatalf(format string, args ...interface{}) {
	GetLogger().Fatalf(format, args...)
}

// WithField 添加单个字段到日志
// 参数:
//   - key: 字段名
//   - value: 字段值
//
// 返回:
//   - Logger: 新的日志实例
func WithField(key string, value interface{}) Logger {
	return GetLogger().WithField(key, value)
}

// WithFields 添加多个字段到日志
// 参数:
//   - fields: 字段映射
//
// 返回:
//   - Logger: 新的日志实例
func WithFields(fields map[string]interface{}) Logger {
	return GetLogger().WithFields(fields)
}

// WithError 添加错误字段到日志
// 参数:
//   - err: 错误对象
//
// 返回:
//   - Logger: 新的日志实例
func WithError(err error) Logger {
	return GetLogger().WithError(err)
}

// Now 获取当前日期时间字符串
// 返回:
//   - string: 格式化的当前时间
func Now() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

// WithContext 从上下文中获取日志字段
// 参数:
//   - ctx: 上下文
//
// 返回:
//   - Logger: 新的日志实例
func WithContext(ctx context.Context) Logger {
	if ctx == nil {
		return GetLogger()
	}

	// 从上下文中提取请求相关信息
	fields := Fields{}

	// 尝试从上下文中获取请求ID
	if requestID, ok := ctx.Value(CONTEXT_REQUEST_ID).(string); ok && requestID != "" {
		fields["request_id"] = requestID
	}

	// 尝试从上下文中获取用户ID
	if userID, ok := ctx.Value(CONTEXT_USER_ID).(uint64); ok && userID > 0 {
		fields["user_id"] = userID
	}

	// 尝试从上下文中获取用户名
	if username, ok := ctx.Value(CONTEXT_USERNAME).(string); ok && username != "" {
		fields["username"] = username
	}

	// 尝试从上下文中获取IP
	if ip, ok := ctx.Value(CONTEXT_IP).(string); ok && ip != "" {
		fields["ip"] = ip
	}

	// 尝试从上下文中获取路径
	if path, ok := ctx.Value(CONTEXT_PATH).(string); ok && path != "" {
		fields["path"] = path
	}

	return GetLogger().WithFields(fields)
}
