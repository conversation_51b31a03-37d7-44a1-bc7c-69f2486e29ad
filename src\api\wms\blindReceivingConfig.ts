import request from '@/utils/request'
import type {
  // 基础类型
  PageResult,
  ApiResponse,
  BatchOperationResult,
  
  // 实体类型
  BlindReceivingConfigEntity,
  BlindReceivingValidationEntity,
  BlindReceivingStatsEntity,
  
  // DTO类型
  BlindReceivingConfigCreateReq,
  BlindReceivingConfigUpdateReq,
  BlindReceivingConfigQueryReq,
  BlindReceivingValidationReq,
  BlindReceivingApprovalReq,
  BlindReceivingSupplementReq,
  BlindReceivingConfigBatchCreateReq,
  BlindReceivingConfigBatchUpdateReq,
  BlindReceivingStatsQueryReq,
  
  // VO类型
  BlindReceivingConfigVO,
  BlindReceivingValidationVO,
  AvailableTargetVO,
  BlindReceivingStatsVO,
  
  // 枚举类型
  ConfigLevel,
  BlindReceivingStrategy,
  ApprovalStatus,
  SupplementStatus
} from '@/types/wms/blindReceivingConfig'

// ==================== 基础 CRUD 操作 ====================

/**
 * 分页查询盲收配置
 */
export const getBlindReceivingConfigPage = (params: BlindReceivingConfigQueryReq): Promise<PageResult<BlindReceivingConfigVO>> => {
  return request<PageResult<BlindReceivingConfigVO>>({
    url: '/api/v1/wms/blind-receiving-configs',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<BlindReceivingConfigVO>>
}

/**
 * 获取盲收配置详情
 */
export const getBlindReceivingConfigDetail = (id: number): Promise<BlindReceivingConfigVO> => {
  return request<BlindReceivingConfigVO>({
    url: `/api/v1/wms/blind-receiving-configs/${id}`,
    method: 'get'
  }) as unknown as Promise<BlindReceivingConfigVO>
}

/**
 * 创建盲收配置
 */
export const createBlindReceivingConfig = (data: BlindReceivingConfigCreateReq): Promise<BlindReceivingConfigVO> => {
  return request<BlindReceivingConfigVO>({
    url: '/api/v1/wms/blind-receiving-configs',
    method: 'post',
    data
  }) as unknown as Promise<BlindReceivingConfigVO>
}

/**
 * 更新盲收配置
 */
export const updateBlindReceivingConfig = (id: number, data: BlindReceivingConfigUpdateReq): Promise<BlindReceivingConfigVO> => {
  return request<BlindReceivingConfigVO>({
    url: `/api/v1/wms/blind-receiving-configs/${id}`,
    method: 'put',
    data
  }) as unknown as Promise<BlindReceivingConfigVO>
}

/**
 * 删除盲收配置
 */
export const deleteBlindReceivingConfig = (id: number): Promise<void> => {
  return request<void>({
    url: `/api/v1/wms/blind-receiving-configs/${id}`,
    method: 'delete'
  }) as unknown as Promise<void>
}

// ==================== 批量操作 ====================

/**
 * 批量创建盲收配置
 */
export const batchCreateBlindReceivingConfig = (data: BlindReceivingConfigBatchCreateReq): Promise<BatchOperationResult> => {
  return request<BatchOperationResult>({
    url: '/api/v1/wms/blind-receiving-configs/batch-create',
    method: 'post',
    data
  }) as unknown as Promise<BatchOperationResult>
}

/**
 * 批量更新盲收配置
 */
export const batchUpdateBlindReceivingConfig = (data: BlindReceivingConfigBatchUpdateReq): Promise<BatchOperationResult> => {
  return request<BatchOperationResult>({
    url: '/api/v1/wms/blind-receiving-configs/batch-update',
    method: 'put',
    data
  }) as unknown as Promise<BatchOperationResult>
}

/**
 * 批量删除盲收配置
 */
export const batchDeleteBlindReceivingConfig = (ids: number[]): Promise<BatchOperationResult> => {
  return request<BatchOperationResult>({
    url: '/api/v1/wms/blind-receiving-configs/batch-delete',
    method: 'delete',
    data: { ids }
  }) as unknown as Promise<BatchOperationResult>
}

/**
 * 批量启用/禁用配置
 */
export const batchToggleBlindReceivingConfig = (ids: number[], isActive: boolean): Promise<BatchOperationResult> => {
  return request<BatchOperationResult>({
    url: '/api/v1/wms/blind-receiving-configs/batch-toggle',
    method: 'put',
    data: { ids, isActive }
  }) as unknown as Promise<BatchOperationResult>
}

// ==================== 核心业务功能 ====================

/**
 * 获取有效配置
 */
export const getEffectiveBlindReceivingConfig = (params: {
  warehouseId: number
  clientId?: number
  userId?: number
}): Promise<BlindReceivingConfigVO> => {
  return request<BlindReceivingConfigVO>({
    url: '/api/v1/wms/blind-receiving-configs/effective',
    method: 'get',
    params
  }) as unknown as Promise<BlindReceivingConfigVO>
}

/**
 * 验证盲收请求
 */
export const validateBlindReceiving = (data: BlindReceivingValidationReq): Promise<BlindReceivingValidationVO> => {
  return request<BlindReceivingValidationVO>({
    url: '/api/v1/wms/blind-receiving-configs/validate',
    method: 'post',
    data
  }) as unknown as Promise<BlindReceivingValidationVO>  
}

/**
 * 获取配置冲突检查
 */
export const checkBlindReceivingConfigConflict = (data: BlindReceivingConfigCreateReq): Promise<{
  hasConflict: boolean
  conflictConfigs: BlindReceivingConfigVO[]
  suggestions: string[]
}> => {
  return request<{
    hasConflict: boolean
    conflictConfigs: BlindReceivingConfigVO[]
    suggestions: string[]
  }>({
    url: '/api/v1/wms/blind-receiving-configs/check-conflict',
    method: 'post',
    data
  }) as unknown as Promise<{
    hasConflict: boolean
    conflictConfigs: BlindReceivingConfigVO[]
    suggestions: string[]
  }>
}

// ==================== 配置管理 ====================

/**
 * 获取可用配置目标
 */
export const getAvailableConfigTargets = (params: {
  configLevel: ConfigLevel
  warehouseId?: number
  keyword?: string
  pageNum?: number
  pageSize?: number
}): Promise<PageResult<AvailableTargetVO>> => {
  return request<PageResult<AvailableTargetVO>>({
    url: '/api/v1/wms/blind-receiving-configs/available-targets',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<AvailableTargetVO>>
}

/**
 * 获取激活的配置列表
 */
export const getActiveBlindReceivingConfigs = (params: {
  configLevel?: ConfigLevel
  strategy?: BlindReceivingStrategy
  warehouseId?: number
}): Promise<BlindReceivingConfigVO[]> => {
  return request<BlindReceivingConfigVO[]>({
    url: '/api/v1/wms/blind-receiving-configs/active-configs',
    method: 'get',
    params
  }) as unknown as Promise<BlindReceivingConfigVO[]>
}

/**
 * 按策略查询配置
 */
export const getBlindReceivingConfigsByStrategy = (params: {
  strategy: BlindReceivingStrategy
  includeInactive?: boolean
  warehouseId?: number
}): Promise<BlindReceivingConfigVO[]> => {
  return request<BlindReceivingConfigVO[]>({
    url: '/api/v1/wms/blind-receiving-configs/configs-by-strategy',
    method: 'get',
    params
  }) as unknown as Promise<BlindReceivingConfigVO[]>
}

/**
 * 获取配置层级统计
 */
export const getBlindReceivingConfigLevelStats = (): Promise<{
  [key in ConfigLevel]: {
    total: number
    active: number
    strategies: Record<BlindReceivingStrategy, number>
  }
}> => {
  return request<{
    [key in ConfigLevel]: {
      total: number
      active: number
      strategies: Record<BlindReceivingStrategy, number>
    }
  }>({
    url: '/api/v1/wms/blind-receiving-configs/level-stats',
    method: 'get'
  }) as unknown as Promise<{
    [key in ConfigLevel]: {
      total: number
      active: number
      strategies: Record<BlindReceivingStrategy, number>
    }
  }>
}

// ==================== 业务流程 ====================

/**
 * 处理审批
 */
export const processBlindReceivingApproval = (data: BlindReceivingApprovalReq): Promise<BlindReceivingValidationVO> => {
  return request<BlindReceivingValidationVO>({
    url: '/api/v1/wms/blind-receiving-configs/process-approval',
    method: 'post',
    data
  }) as unknown as Promise<BlindReceivingValidationVO>
}

/**
 * 处理补录
 */
export const processBlindReceivingSupplement = (data: BlindReceivingSupplementReq): Promise<BlindReceivingValidationVO> => {
  return request<BlindReceivingValidationVO>({
    url: '/api/v1/wms/blind-receiving-configs/process-supplement',
    method: 'post',
    data
  }) as unknown as Promise<BlindReceivingValidationVO>
}

/**
 * 获取待审批列表
 */
export const getPendingApprovalList = (params: {
  pageNum?: number
  pageSize?: number
  warehouseId?: number
  clientId?: number
  strategy?: BlindReceivingStrategy
  createdAtFrom?: string
  createdAtTo?: string
}): Promise<PageResult<BlindReceivingValidationVO>> => {
  return request<PageResult<BlindReceivingValidationVO>>({
    url: '/api/v1/wms/blind-receiving-configs/pending-approvals',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<BlindReceivingValidationVO>>
}

/**
 * 获取待补录列表
 */
export const getPendingSupplementList = (params: {
  pageNum?: number
  pageSize?: number
  warehouseId?: number
  clientId?: number
  strategy?: BlindReceivingStrategy
  deadlineFrom?: string
  deadlineTo?: string
}): Promise<PageResult<BlindReceivingValidationVO>> => {
  return request<PageResult<BlindReceivingValidationVO>>({
    url: '/api/v1/wms/blind-receiving-configs/pending-supplements',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<BlindReceivingValidationVO>>
}

/**
 * 获取逾期补录列表
 */
export const getOverdueSupplementList = (params: {
  pageNum?: number
  pageSize?: number
  warehouseId?: number
  clientId?: number
}): Promise<PageResult<BlindReceivingValidationVO>> => {
  return request<PageResult<BlindReceivingValidationVO>>({
    url: '/api/v1/wms/blind-receiving-configs/overdue-supplements',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<BlindReceivingValidationVO>>
}

// ==================== 统计分析 ====================

/**
 * 获取盲收统计数据
 */
export const getBlindReceivingStats = (params: BlindReceivingStatsQueryReq): Promise<BlindReceivingStatsVO> => {
  return request<BlindReceivingStatsVO>({
    url: '/api/v1/wms/blind-receiving-configs/stats',
    method: 'get',
    params
  }) as unknown as Promise<BlindReceivingStatsVO>   
}

/**
 * 获取配置使用情况统计
 */
export const getBlindReceivingConfigUsageStats = (params: {
  configId?: number
  dateFrom?: string
  dateTo?: string
  groupBy?: 'day' | 'week' | 'month'
}): Promise<{
  configId: number
  configName: string
  usageCount: number
  successRate: number
  averageQuantity: number
  trend: Array<{
    date: string
    count: number
    quantity: number
  }>
}[]> => {
  return request<{
    configId: number
    configName: string
    usageCount: number
    successRate: number
    averageQuantity: number
    trend: Array<{
      date: string
      count: number
      quantity: number
    }>
  }[]>({
    url: '/api/v1/wms/blind-receiving-configs/usage-stats',
    method: 'get',
    params
  }) as unknown as Promise<{
    configId: number
    configName: string
    usageCount: number
    successRate: number
    averageQuantity: number
    trend: Array<{
      date: string
      count: number
      quantity: number
    }>
  }[]>
}

/**
 * 获取盲收趋势分析
 */
export const getBlindReceivingTrendAnalysis = (params: {
  warehouseId?: number
  dateFrom?: string
  dateTo?: string
  interval?: 'hour' | 'day' | 'week' | 'month'
}): Promise<{
  totalTrend: Array<{
    date: string
    count: number
    quantity: number
  }>
  strategyTrend: {
    [key in BlindReceivingStrategy]: Array<{
      date: string
      count: number
      quantity: number
    }>
  }
  approvalTrend: Array<{
    date: string
    approvedCount: number
    rejectedCount: number
    pendingCount: number
  }>
  supplementTrend: Array<{
    date: string
    completedCount: number
    pendingCount: number
    overdueCount: number
  }>
}> => {
  return request<{
    totalTrend: Array<{
      date: string
      count: number
      quantity: number
    }>
    strategyTrend: {
      [key in BlindReceivingStrategy]: Array<{
        date: string
        count: number
        quantity: number
      }>
    }
    approvalTrend: Array<{
      date: string
      approvedCount: number
      rejectedCount: number
      pendingCount: number
    }>
    supplementTrend: Array<{
      date: string
      completedCount: number
      pendingCount: number
      overdueCount: number
    }>
  }>({
    url: '/api/v1/wms/blind-receiving-configs/trend-analysis',
    method: 'get',
    params
  }) 
}

// ==================== 导入导出 ====================

/**
 * 导出配置数据
 */
export const exportBlindReceivingConfigs = (params: BlindReceivingConfigQueryReq): Promise<Blob> => {
  return request<Blob>({
    url: '/api/v1/wms/blind-receiving-configs/export',
    method: 'get',
    params,
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

/**
 * 导入配置数据
 */
export const importBlindReceivingConfigs = (file: File, overwriteExisting = false): Promise<BatchOperationResult> => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('overwriteExisting', String(overwriteExisting))
  
  return request<BatchOperationResult>({
    url: '/api/v1/wms/blind-receiving-configs/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }) as unknown as Promise<BatchOperationResult>
}

/**
 * 下载导入模板
 */
export const downloadBlindReceivingConfigTemplate = (): Promise<Blob> => {
  return request<Blob>({
    url: '/api/v1/wms/blind-receiving-configs/template',
    method: 'get',
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

// ==================== 辅助功能 ====================

/**
 * 获取仓库列表
 */
export const getWarehouseListForConfig = (params: {
  keyword?: string
  pageNum?: number
  pageSize?: number
}): Promise<PageResult<AvailableTargetVO>> => {
  return request<PageResult<AvailableTargetVO>>({
    url: '/api/v1/wms/blind-receiving-configs/warehouses',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<AvailableTargetVO>>
}

/**
 * 获取客户列表
 */
export const getClientListForConfig = (params: {
  keyword?: string
  pageNum?: number
  pageSize?: number
}): Promise<PageResult<AvailableTargetVO>> => {
  return request<PageResult<AvailableTargetVO>>({
    url: '/api/v1/wms/blind-receiving-configs/clients',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<AvailableTargetVO>>
}

/**
 * 获取用户列表
 */
export const getUserListForConfig = (params: {
  keyword?: string
  roleCode?: string
  pageNum?: number
  pageSize?: number
}): Promise<PageResult<AvailableTargetVO>> => {
  return request<PageResult<AvailableTargetVO>>({
    url: '/api/v1/wms/blind-receiving-configs/users',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<AvailableTargetVO>>
}

/**
 * 获取角色列表
 */
export const getRoleListForConfig = (): Promise<Array<{
  code: string
  name: string
  description?: string
}>> => {
  return request<Array<{
    code: string
    name: string
    description?: string
  }>>({
    url: '/api/v1/wms/blind-receiving-configs/roles',
    method: 'get'
  }) as unknown as Promise<Array<{
    code: string
    name: string
    description?: string
  }>>
}

/**
 * 获取商品列表
 */
export const getItemListForConfig = (params: {
  keyword?: string
  categoryId?: number
  pageNum?: number
  pageSize?: number
}): Promise<PageResult<{
  id: number
  sku: string
  name: string
  specification?: string
  unit: string
  category?: string
}>> => {
  return request<PageResult<{
    id: number
    sku: string
    name: string
    specification?: string
    unit: string
    category?: string
  }>>({
    url: '/api/v1/wms/blind-receiving-configs/items',
    method: 'get',
    params
    }) as unknown as Promise<PageResult<{
    id: number
    sku: string
    name: string
    specification?: string
    unit: string
    category?: string
  }>>
}

// ==================== 配置验证 ====================

/**
 * 验证配置有效性
 */
export const validateBlindReceivingConfig = (data: BlindReceivingConfigCreateReq): Promise<{
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
}> => {
  return request<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    suggestions: string[]
  }>({
    url: '/api/v1/wms/blind-receiving-configs/validate-config',
    method: 'post',
    data
  }) as unknown as Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    suggestions: string[]
  }>
}

/**
 * 测试配置效果
 */
export const testBlindReceivingConfig = (data: {
  configData: BlindReceivingConfigCreateReq
  testCases: BlindReceivingValidationReq[]
}): Promise<{
  configId: number
  testResults: Array<{
    testCase: BlindReceivingValidationReq
    result: BlindReceivingValidationVO
    expectedResult?: BlindReceivingValidationVO
    match: boolean
  }>
}> => {
  return request<{
    configId: number
    testResults: Array<{
      testCase: BlindReceivingValidationReq
      result: BlindReceivingValidationVO
      expectedResult?: BlindReceivingValidationVO
      match: boolean
    }>
  }>({
    url: '/api/v1/wms/blind-receiving-configs/test-config',
    method: 'post',
    data
  }) as unknown as Promise<{
    configId: number
    testResults: Array<{
      testCase: BlindReceivingValidationReq
      result: BlindReceivingValidationVO
      expectedResult?: BlindReceivingValidationVO
      match: boolean
    }>
  }>
}