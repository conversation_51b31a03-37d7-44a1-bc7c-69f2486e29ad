package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"

	"github.com/kataras/iris/v12"
)

// AuditLogControllerImpl handles HTTP requests related to audit logs.
// It embeds BaseControllerImpl for common controller functionalities.
type AuditLogControllerImpl struct {
	*BaseControllerImpl // Embeds BaseControllerImpl for common methods like Fail, HandleError etc.
	auditService        service.AuditLogService
}

// NewAuditLogControllerImpl creates a new instance of AuditLogControllerImpl.
// It requires a ControllerManager (presumably for BaseController dependencies) and an AuditLogService.
func NewAuditLogControllerImpl(cm *ControllerManager, auditService service.AuditLogService) *AuditLogControllerImpl {
	return &AuditLogControllerImpl{
		BaseControllerImpl: NewBaseController(cm), // Assumes NewBaseController is defined and returns *BaseControllerImpl
		auditService:       auditService,
	}
}

// ListAuditLogs retrieves a paginated list of audit logs based on query parameters.
// @Summary List audit logs
// @Description Get a paginated list of audit logs with filters. The API handles pagination and filtering based on the provided query parameters. Sorting can also be applied.
// @Tags Audit
// @Accept json
// @Produce json
// @Param pageNum query int false "Page number (default is 1). Used for pagination." default(1)
// @Param pageSize query int false "Number of items per page (default is 20). Value is capped by constant.MAX_PAGE_SIZE." default(20)
// @Param sort query string false "Sort order for the results. Comma-separated fields with :asc or :desc. Example: timestamp:desc,username:asc. If not provided, a system default sort will be applied (e.g., timestamp:desc)."
// @Param userId query int false "Filter by User ID. Exact match."
// @Param username query string false "Filter by Username. Partial match (contains)."
// @Param action query string false "Filter by Action. Exact match."
// @Param resourceType query string false "Filter by Resource Type. Exact match."
// @Param clientIp query string false "Filter by Client IP. Exact match."
// @Param traceId query string false "Filter by Trace ID. Exact match."
// @Param status query string false "Filter by Status (e.g., 'success', 'failure'). Exact match."
// @Param startDate query string false "Filter by start date (format: YYYY-MM-DD). Records on or after this date."
// @Param endDate query string false "Filter by end date (format: YYYY-MM-DD). Records on or before this date."
// @Success 200 {object} response.Response{data=response.PageResult{list=[]vo.AuditLogVO}} "Successfully retrieved audit logs. The 'data' field contains pagination information and the list of audit logs as vo.AuditLogVO objects."
// @Failure 400 {object} response.Response "Bad Request. Indicates invalid query parameters or other client-side errors."
// @Failure 500 {object} response.Response "Internal Server Error. Indicates a problem on the server side."
// @Router /admin/audit-logs [get]
func (c *AuditLogControllerImpl) ListAuditLogs(ctx iris.Context) {
	var queryDTO dto.AuditLogQueryDTO

	// Read filter-specific query parameters directly into the queryDTO struct.
	// Iris handles type conversion for basic types like int, string based on struct tags.
	// For time.Time with time_format, Iris should also handle it.
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		// Corrected c.Fail call to match expected signature (ctx, int, string)
		c.Fail(ctx, iris.StatusBadRequest, "Invalid query filter parameters: "+err.Error())
		return
	}

	// Build PageQuery for pagination (pageNum, pageSize) and sorting (sort) parameters.
	// This function should parse these specific params from ctx and apply defaults/validation.
	pageQueryInstance := response.BuildPageQuery(ctx)
	queryDTO.Pagination = *pageQueryInstance // Assign the parsed pagination/sorting info to the DTO.

	// Call the service layer to fetch audit logs.
	// The service is expected to return a PageResult containing a list of vo.AuditLogVO and pagination details.
	pageResultWithVOs, err := c.auditService.ListAuditLogs(queryDTO)
	if err != nil {
		// BaseControllerImpl.HandleError is assumed for consistent error handling.
		c.HandleError(ctx, err, "ListAuditLogs failed while fetching data from service")
		return
	}

	// If successful, the service returns a PageResult ready for the response.
	// response.Success is assumed to wrap this data in a standard success response structure.
	response.Success(ctx, pageResultWithVOs)
}
