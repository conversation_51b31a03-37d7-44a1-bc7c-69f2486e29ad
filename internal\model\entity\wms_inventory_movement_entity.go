package entity

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// WmsInventoryMovementType 库存移动类型枚举
type WmsInventoryMovementType string

const (
	MovementTypeManual  WmsInventoryMovementType = "MANUAL"  // 手动移动
	MovementTypeSystem  WmsInventoryMovementType = "SYSTEM"  // 系统移动
	MovementTypePutaway WmsInventoryMovementType = "PUTAWAY" // 上架移动
	MovementTypePicking WmsInventoryMovementType = "PICKING" // 拣货移动
)

// WmsInventoryMovementStatus 库存移动状态枚举
type WmsInventoryMovementStatus string

const (
	MovementStatusPending    WmsInventoryMovementStatus = "PENDING"     // 待执行
	MovementStatusInProgress WmsInventoryMovementStatus = "IN_PROGRESS" // 执行中
	MovementStatusCompleted  WmsInventoryMovementStatus = "COMPLETED"   // 已完成
	MovementStatusCancelled  WmsInventoryMovementStatus = "CANCELLED"   // 已取消
)

// WmsInventoryMovement 对应数据库中的 wms_inventory_movement 表
// 存储库存移动记录信息
type WmsInventoryMovement struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	MovementNo     string                     `gorm:"column:movement_no;size:50;not null;uniqueIndex:idx_movement_no_account;comment:移动单号" json:"movementNo"`
	ItemID         uint                       `gorm:"column:item_id;not null;comment:物料ID" json:"itemId"`
	FromLocationID uint                       `gorm:"column:from_location_id;not null;comment:源库位ID" json:"fromLocationId"`
	ToLocationID   uint                       `gorm:"column:to_location_id;not null;comment:目标库位ID" json:"toLocationId"`
	BatchNo        *string                    `gorm:"column:batch_no;size:100;comment:批次号" json:"batchNo"`
	Quantity       float64                    `gorm:"column:quantity;type:numeric(12,4);not null;comment:移动数量" json:"quantity"`
	UnitOfMeasure  string                     `gorm:"column:unit_of_measure;size:20;not null;comment:计量单位" json:"unitOfMeasure"`
	MovementType   WmsInventoryMovementType   `gorm:"column:movement_type;type:varchar(20);not null;comment:移动类型" json:"movementType"`
	MovementReason *string                    `gorm:"column:movement_reason;size:100;comment:移动原因" json:"movementReason"`
	Status         WmsInventoryMovementStatus `gorm:"column:status;type:varchar(20);default:'PENDING';comment:移动状态" json:"status"`
	OperatorID     uint                       `gorm:"column:operator_id;not null;comment:操作员ID" json:"operatorId"`
	StartedAt      *time.Time                 `gorm:"column:started_at;comment:开始时间" json:"startedAt"`
	CompletedAt    *time.Time                 `gorm:"column:completed_at;comment:完成时间" json:"completedAt"`

	// 关联关系
	Item         *MtlItem     `gorm:"foreignKey:ItemID;references:ID" json:"item,omitempty"`
	FromLocation *WmsLocation `gorm:"foreignKey:FromLocationID;references:ID" json:"fromLocation,omitempty"`
	ToLocation   *WmsLocation `gorm:"foreignKey:ToLocationID;references:ID" json:"toLocation,omitempty"`
	Operator     *User        `gorm:"foreignKey:OperatorID;references:ID" json:"operator,omitempty"`
}

// TableName 指定数据库表名方法
func (WmsInventoryMovement) TableName() string {
	return "wms_inventory_movement"
}

// BeforeCreate GORM钩子：创建前自动生成移动单号
func (w *WmsInventoryMovement) BeforeCreate(tx *gorm.DB) error {
	if w.MovementNo == "" {
		// 这里应该调用编码生成服务生成移动单号
		// 暂时使用简单的时间戳格式
		w.MovementNo = fmt.Sprintf("MOV%s", time.Now().Format("20060102150405"))
	}
	return nil
}

// CanStart 判断是否可以开始移动
func (w *WmsInventoryMovement) CanStart() bool {
	return w.Status == MovementStatusPending
}

// CanComplete 判断是否可以完成移动
func (w *WmsInventoryMovement) CanComplete() bool {
	return w.Status == MovementStatusInProgress
}

// CanCancel 判断是否可以取消移动
func (w *WmsInventoryMovement) CanCancel() bool {
	return w.Status == MovementStatusPending || w.Status == MovementStatusInProgress
}

// Start 开始移动
func (w *WmsInventoryMovement) Start() error {
	if !w.CanStart() {
		return fmt.Errorf("移动状态不允许开始操作")
	}

	now := time.Now()
	w.Status = MovementStatusInProgress
	w.StartedAt = &now

	return nil
}

// Complete 完成移动
func (w *WmsInventoryMovement) Complete() error {
	if !w.CanComplete() {
		return fmt.Errorf("移动状态不允许完成操作")
	}

	now := time.Now()
	w.Status = MovementStatusCompleted
	w.CompletedAt = &now

	return nil
}

// Cancel 取消移动
func (w *WmsInventoryMovement) Cancel() error {
	if !w.CanCancel() {
		return fmt.Errorf("移动状态不允许取消操作")
	}

	w.Status = MovementStatusCancelled

	return nil
}

// GetMovementTypeName 获取移动类型中文名称
func (w *WmsInventoryMovement) GetMovementTypeName() string {
	switch w.MovementType {
	case MovementTypeManual:
		return "手动移动"
	case MovementTypeSystem:
		return "系统移动"
	case MovementTypePutaway:
		return "上架移动"
	case MovementTypePicking:
		return "拣货移动"
	default:
		return "未知类型"
	}
}

// GetStatusName 获取移动状态中文名称
func (w *WmsInventoryMovement) GetStatusName() string {
	switch w.Status {
	case MovementStatusPending:
		return "待执行"
	case MovementStatusInProgress:
		return "执行中"
	case MovementStatusCompleted:
		return "已完成"
	case MovementStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetDuration 获取移动耗时（秒）
func (w *WmsInventoryMovement) GetDuration() *int64 {
	if w.StartedAt == nil || w.CompletedAt == nil {
		return nil
	}

	duration := w.CompletedAt.Sub(*w.StartedAt).Seconds()
	durationInt := int64(duration)
	return &durationInt
}
