package database

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	stdlog "log" // Import standard log package with an alias
	"os"
	"strings"
	"sync"
	"time"

	"github.com/jackc/pgconn"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	// <<< Add blank import for the standard postgres driver >>>
	_ "github.com/jackc/pgx/v4/stdlib"

	"backend/pkg/config"
	appLogger "backend/pkg/logger" // Keep aliased app logger
)

// PostgreSQLConfig struct (REMOVE THIS DUPLICATE DEFINITION)
// type PostgreSQLConfig struct { ... }

// PostgreSQLClient PostgreSQL数据库客户端
type PostgreSQLClient struct {
	config *config.PostgreSQLConfig
	DB     *gorm.DB
	once   sync.Once
}

// NewPostgreSQLClient 创建PostgreSQL客户端
func NewPostgreSQLClient(cfg *config.PostgreSQLConfig) *PostgreSQLClient {
	if cfg == nil {
		appLogger.GetLogger().Fatal("PostgreSQL配置不能为空")
	}
	return &PostgreSQLClient{
		config: cfg,
	}
}

// connectToPostgresAdminDB connects to the 'postgres' database to perform admin tasks like CREATE DATABASE.
func connectToPostgresAdminDB(cfg *config.PostgreSQLConfig) (*sql.DB, error) {
	log := appLogger.GetLogger()
	// DSN for connecting to the 'postgres' database or a template database
	// Use 'postgres' db as it usually exists. Ensure user has necessary permissions.
	adminDSN := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=postgres sslmode=%s TimeZone=%s",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.SSLMode, cfg.TimeZone)
	db, err := sql.Open("pgx", adminDSN) // Changed driver name from "postgres" to "pgx"
	if err != nil {
		log.Error("连接到 postgres admin 数据库失败 (sql.Open)", appLogger.WithError(err))
		return nil, fmt.Errorf("连接到 postgres admin 数据库失败 (sql.Open): %w", err)
	}
	if err = db.Ping(); err != nil {
		db.Close() // Close the connection if ping fails
		log.Error("ping postgres admin 数据库失败", appLogger.WithError(err))
		return nil, fmt.Errorf("ping postgres admin 数据库失败: %w", err)
	}
	log.Debug("成功连接到 postgres admin 数据库")
	return db, nil
}

// createDatabase creates the target database if it doesn't exist.
func createDatabase(adminDB *sql.DB, dbName string) error {
	log := appLogger.GetLogger()
	quotedDBName := fmt.Sprintf(`"%s"`, strings.ReplaceAll(dbName, `"`, `""`))
	query := fmt.Sprintf("CREATE DATABASE %s", quotedDBName)
	log.Debugf("Executing database creation query: %s", query) // Log the query

	_, err := adminDB.Exec(query)
	if err != nil {
		pgErr, ok := err.(*pgconn.PgError)
		if ok && pgErr.Code == "42P04" { // 42P04 is "duplicate_database"
			log.Infof("数据库 '%s' 已存在，无需创建", dbName)
			return nil // Database already exists, which is fine
		}
		// Log more details on failure
		var errCode string = "N/A"
		if ok {
			errCode = pgErr.Code
		}
		log.Errorf("执行 CREATE DATABASE '%s' 失败. Error Code: %s, Details: %v", dbName, errCode, err)
		return fmt.Errorf("创建数据库 '%s' 失败: %w", dbName, err)
	}
	log.Infof("数据库 '%s' 创建成功", dbName)
	return nil
}

// Connect 连接数据库, 如果配置了 auto_init 则会尝试创建数据库
// 修改返回值为 (*gorm.DB, error) 以匹配 DBClient 接口
//
//nolint:all
func (p *PostgreSQLClient) Connect() (*gorm.DB, error) {
	log := appLogger.GetLogger()
	var finalErr error
	p.once.Do(func() {
		targetDSN := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
			p.config.Host, p.config.Port, p.config.Username, p.config.Password, p.config.Database,
			p.config.SSLMode, p.config.TimeZone)

		var logLevel gormLogger.LogLevel
		switch p.config.LogLevel {
		case 1:
			logLevel = gormLogger.Silent
		case 2:
			logLevel = gormLogger.Error
		case 3:
			logLevel = gormLogger.Warn
		case 4:
			logLevel = gormLogger.Info
		default:
			logLevel = gormLogger.Warn
		}
		stdLogger := stdlog.New(os.Stdout, "\r\n", stdlog.LstdFlags)
		gormLog := gormLogger.New(
			stdLogger,
			gormLogger.Config{
				SlowThreshold:             time.Duration(p.config.SlowThreshold) * time.Millisecond,
				LogLevel:                  logLevel,
				IgnoreRecordNotFoundError: true,
				Colorful:                  true,
			},
		)

		// Try initial connection to target DB
		log.Debugf("Attempting initial connection to target DSN: %s", targetDSN)
		var err error
		p.DB, err = gorm.Open(postgres.Open(targetDSN), &gorm.Config{
			Logger:         gormLog,
			NamingStrategy: schema.NamingStrategy{SingularTable: true},
		})

		if err != nil {
			log.Warnf("Initial connection failed: %v", err)

			var pgErr *pgconn.PgError
			isPgError := errors.As(err, &pgErr)
			pgErrCode := "N/A"
			if isPgError && pgErr != nil {
				pgErrCode = pgErr.Code
			}
			log.Debugf("[Config Check] Checking AutoInit flag. Value from config: %t", p.config.AutoInit)
			log.Debugf("[Error Check] Is PostgreSQL error (errors.As)? %t. Error Code (if found): %s", isPgError, pgErrCode)

			if p.config.AutoInit && strings.Contains(err.Error(), "SQLSTATE 3D000") {
				log.Warnf("数据库 '%s' 不存在 (Detected SQLSTATE 3D000)，将尝试创建 (auto_init=true)...", p.config.Database)

				adminDB, adminErr := connectToPostgresAdminDB(p.config)
				if adminErr != nil {
					log.Errorf("连接 admin DB 失败，无法创建目标数据库. Error: %v", adminErr)
					finalErr = fmt.Errorf("无法连接到 admin 数据库以创建目标数据库: %w", adminErr)
					return
				}
				defer adminDB.Close()

				createErr := createDatabase(adminDB, p.config.Database)
				if createErr != nil {
					log.Errorf("尝试自动创建数据库 '%s' 时出错. Error: %v", p.config.Database, createErr)
					finalErr = fmt.Errorf("自动创建数据库 '%s' 失败: %w", p.config.Database, createErr)
					return
				}
				log.Info("尝试重新连接到新创建的数据库...")
				p.DB, err = gorm.Open(postgres.Open(targetDSN), &gorm.Config{
					Logger:         gormLog,
					NamingStrategy: schema.NamingStrategy{SingularTable: true},
				})
				if err != nil {
					log.Errorf("连接到新创建的数据库 '%s' 失败: %v", p.config.Database, err)
					finalErr = fmt.Errorf("连接到新创建的数据库 '%s' 失败: %w", p.config.Database, err)
					return
				}

			} else {
				// Log why creation wasn't attempted (at Debug level)
				if !p.config.AutoInit {
					log.Debugf("数据库 '%s' 不存在, 但 auto_init 读取为 false, 跳过创建.", p.config.Database)
					// } else if !(err != nil && strings.Contains(err.Error(), "SQLSTATE 3D000")) { //nolint:all 原代码：冗余的err!=nil检查
				} else if !strings.Contains(err.Error(), "SQLSTATE 3D000") { //nolint:all 简化后：去掉冗余的err!=nil检查
					log.Debugf("连接失败, 但错误信息中未找到 'SQLSTATE 3D000' 或 auto_init 为 false, 跳过创建. 原始错误: %v", err)
				}
				finalErr = fmt.Errorf("连接 PostgreSQL 数据库失败: %w", err)
				return
			}
		}

		sqlDB, poolErr := p.DB.DB()
		if poolErr != nil {
			finalErr = fmt.Errorf("获取数据库连接池失败: %w", poolErr)
			return
		}
		sqlDB.SetMaxIdleConns(p.config.MaxIdleConns)
		sqlDB.SetMaxOpenConns(p.config.MaxOpenConns)
		sqlDB.SetConnMaxLifetime(time.Duration(p.config.ConnMaxLifetime) * time.Second)

		log.Info("PostgreSQL 数据库连接和设置成功")
	})

	if finalErr != nil {
		log.Errorf("PostgreSQL Connect 过程最终失败: %v", finalErr)
		return nil, finalErr // Return nil DB on error
	}
	// Return the connected DB instance and nil error on success
	return p.DB, nil
}

// Disconnect 断开数据库连接
func (p *PostgreSQLClient) Disconnect() error {
	if p.DB == nil {
		return nil
	}

	sqlDB, err := p.DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// Ping 测试数据库连接
func (p *PostgreSQLClient) Ping(ctx context.Context) error {
	if p.DB == nil {
		return fmt.Errorf("数据库未连接")
	}

	sqlDB, err := p.DB.DB()
	if err != nil {
		return err
	}

	// Use PingContext
	return sqlDB.PingContext(ctx)
}

// GetDB 获取数据库连接 (确保已连接)
func (p *PostgreSQLClient) GetDB() *gorm.DB {
	if p.DB == nil {
		appLogger.GetLogger().Fatal("GetDB called before Connect completed successfully")
	}
	return p.DB
}

// GetSQLDB 获取底层的 *sql.DB 连接池对象
func (p *PostgreSQLClient) GetSQLDB() (*sql.DB, error) {
	if p.DB == nil {
		return nil, fmt.Errorf("PostgreSQL GORM DB instance is not initialized")
	}
	return p.DB.DB()
}

// Remove DefaultConfig and GetDB as config is loaded from file now
