package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"

	"github.com/kataras/iris/v12"
)

// FinCurrencyController 定义币种控制器接口
type FinCurrencyController interface {
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetPage(ctx iris.Context)
	GetList(ctx iris.Context)
	GetByID(ctx iris.Context)
}

// FinCurrencyControllerImpl 是 FinCurrencyController 的实现
type FinCurrencyControllerImpl struct {
	*BaseControllerImpl
	finCurrencyService service.FinCurrencyService
}

// NewFinCurrencyController 创建一个新的币种控制器
func NewFinCurrencyControllerImpl(cm *ControllerManager) *FinCurrencyControllerImpl {
	return &FinCurrencyControllerImpl{
		BaseControllerImpl: NewBaseController(cm),
		finCurrencyService: cm.GetServiceManager().FinCurrencyService(),
	}
}

// Create godoc
// @Summary      创建币种
// @Description  创建一个新的币种
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        currency  body      dto.FinCurrencyCreateReq  true  "币种信息"
// @Success      200      {object}  response.Response{data=vo.FinCurrencyItemRes}
// @Router       /api/v1/fin/currencies [post]
func (c *FinCurrencyControllerImpl) Create(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_currency")

	var req dto.FinCurrencyCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}

	result, err := c.finCurrencyService.Create(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "创建币种失败")
		return
	}
	c.Success(ctx, result)
}

// Update godoc
// @Summary      更新币种
// @Description  更新一个已有的币种
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id        path      int                  true  "币种ID"
// @Param        currency  body      dto.FinCurrencyUpdateReq  true  "币种信息"
// @Success      200      {object}  response.Response{data=vo.FinCurrencyItemRes}
// @Router       /api/v1/fin/currencies/{id} [put]
func (c *FinCurrencyControllerImpl) Update(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_currency")

	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	var req dto.FinCurrencyUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}
	req.ID = uint(id)

	result, err := c.finCurrencyService.Update(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "更新币种失败")
		return
	}
	c.Success(ctx, result)
}

// Delete godoc
// @Summary      删除币种
// @Description  根据ID删除一个币种
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "币种ID"
// @Success      200  {object}  response.Response
// @Router       /api/v1/fin/currencies/{id} [delete]
func (c *FinCurrencyControllerImpl) Delete(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_currency")

	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	err = c.finCurrencyService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		c.HandleError(ctx, err, "删除币种失败")
		return
	}
	c.Success(ctx, nil)
}

// GetByID godoc
// @Summary      获取币种详情
// @Description  根据ID获取单个币种的详细信息
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "币种ID"
// @Success      200  {object}  response.Response{data=vo.FinCurrencyItemRes}
// @Router       /api/v1/fin/currencies/{id} [get]
func (c *FinCurrencyControllerImpl) GetByID(ctx iris.Context) {
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_currency")

	id, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, "获取ID失败")
		return
	}

	result, err := c.finCurrencyService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		c.HandleError(ctx, err, "获取币种详情失败")
		return
	}
	c.Success(ctx, result)
}

// GetPage godoc
// @Summary      分页查询币种
// @Description  根据条件分页获取币种列表
// @Tags         fin
// @Accept       json
// @Produce      json
// @Param        query  query     dto.FinCurrencyPageReq  false  "查询参数"
// @Success      200    {object}  response.Response{data=response.PageResult}
// @Router       /api/v1/fin/currencies/page [get]
func (c *FinCurrencyControllerImpl) GetPage(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_currency")

	var req dto.FinCurrencyPageReq
	if err := ctx.ReadQuery(&req); err != nil {
		c.HandleError(ctx, err, "参数绑定失败")
		return
	}

	result, err := c.finCurrencyService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		c.HandleError(ctx, err, "查询币种失败")
		return
	}
	c.Success(ctx, result)
}

// GetList godoc
// @Summary      获取已启用币种列表
// @Description  获取所有已启用的币种，用于下拉框
// @Tags         fin
// @Accept       json
// @Produce      json
// @Success      200  {object}  response.Response{data=[]vo.FinCurrencyItemRes}
// @Router       /api/v1/fin/currencies/list [get]
func (c *FinCurrencyControllerImpl) GetList(ctx iris.Context) {
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "fin_currency")

	result, err := c.finCurrencyService.GetList(ctx.Request().Context())
	if err != nil {
		c.HandleError(ctx, err, "获取列表失败")
		return
	}
	c.Success(ctx, result)
}
