package entity

import (
	"github.com/shopspring/decimal"
)

// CrmCustomer 客户实体
// 继承自AccountBookEntity，提供多账套数据隔离
// 包含客户的基本信息、联系方式、财务信息和业务信息
type CrmCustomer struct {
	AccountBookEntity

	// 客户基本信息
	CustomerCode        string           `gorm:"column:customer_code;size:50;not null;comment:客户编码" json:"customerCode" validation:"required,max=50"`          // 客户编码：唯一标识客户的编码，支持自动生成
	CustomerName        string           `gorm:"column:customer_name;size:255;not null;comment:客户名称" json:"customerName" validation:"required,max=255"`        // 客户名称：客户的正式名称
	CustomerType        string           `gorm:"column:customer_type;default:'CORPORATE';comment:客户类型" json:"customerType"`             // 客户类型：CORPORATE(企业)/INDIVIDUAL(个人)
	Industry            *string          `gorm:"column:industry;size:100;comment:所属行业" json:"industry" validation:"omitempty,max=100"`                         // 所属行业：客户所在的行业分类
	BusinessLicense     *string          `gorm:"column:business_license;size:100;comment:营业执照号" json:"businessLicense" validation:"omitempty,max=100"`         // 营业执照号：企业客户的营业执照编号
	TaxNumber           *string          `gorm:"column:tax_number;size:100;comment:税务登记号" json:"taxNumber" validation:"omitempty,max=100"`                     // 税务登记号：税务部门分配的唯一编号
	LegalRepresentative *string          `gorm:"column:legal_representative;size:100;comment:法定代表人" json:"legalRepresentative" validation:"omitempty,max=100"` // 法定代表人：企业的法定代表人姓名
	RegisteredCapital   *decimal.Decimal `gorm:"column:registered_capital;type:decimal(15,2);comment:注册资本" json:"registeredCapital"`                           // 注册资本：企业的注册资本金额

	// 联系信息
	ContactPerson *string `gorm:"column:contact_person;size:100;comment:主要联系人" json:"contactPerson" validation:"omitempty,max=100"`    // 主要联系人：客户的主要联系人姓名
	ContactPhone  *string `gorm:"column:contact_phone;size:50;comment:联系电话" json:"contactPhone" validation:"omitempty,max=50"`         // 联系电话：客户的主要联系电话
	ContactEmail  *string `gorm:"column:contact_email;size:255;comment:联系邮箱" json:"contactEmail" validation:"omitempty,email,max=255"` // 联系邮箱：客户的联系邮箱地址
	Website       *string `gorm:"column:website;size:255;comment:公司网站" json:"website" validation:"omitempty,url,max=255"`              // 公司网站：客户的官方网站地址

	// 地址信息
	Country    *string `gorm:"column:country;size:100;comment:国家" json:"country" validation:"omitempty,max=100"`        // 国家：客户所在国家
	Province   *string `gorm:"column:province;size:100;comment:省份" json:"province" validation:"omitempty,max=100"`      // 省份：客户所在省份
	City       *string `gorm:"column:city;size:100;comment:城市" json:"city" validation:"omitempty,max=100"`              // 城市：客户所在城市
	District   *string `gorm:"column:district;size:100;comment:区县" json:"district" validation:"omitempty,max=100"`      // 区县：客户所在区县
	Address    *string `gorm:"column:address;type:text;comment:详细地址" json:"address"`                                    // 详细地址：客户的详细地址信息
	PostalCode *string `gorm:"column:postal_code;size:20;comment:邮政编码" json:"postalCode" validation:"omitempty,max=20"` // 邮政编码：客户地址的邮政编码

	// 财务信息
	CreditRating *string          `gorm:"column:credit_rating;size:20;comment:信用等级" json:"creditRating" validation:"omitempty,max=20"`     // 信用等级：客户的信用评级
	CreditLimit  *decimal.Decimal `gorm:"column:credit_limit;type:decimal(15,2);comment:信用额度" json:"creditLimit"`                          // 信用额度：给予客户的信用额度
	PaymentTerms *string          `gorm:"column:payment_terms;size:50;comment:付款条件" json:"paymentTerms" validation:"omitempty,max=50"`     // 付款条件：与客户约定的付款方式和期限
	CurrencyCode string           `gorm:"column:currency_code;size:10;default:'CNY';comment:结算币种" json:"currencyCode" validation:"max=10"` // 结算币种：与客户交易的币种，默认人民币

	// 业务信息
	CustomerLevel         string  `gorm:"column:customer_level;default:'NORMAL';comment:客户级别" json:"customerLevel"` // 客户级别：VIP/GOLD/SILVER/NORMAL
	CustomerSource        *string `gorm:"column:customer_source;size:50;comment:客户来源" json:"customerSource" validation:"omitempty,max=50"`  // 客户来源：客户的获取渠道
	SalesRepresentativeID *uint   `gorm:"column:sales_representative_id;comment:销售代表ID" json:"salesRepresentativeId"`                       // 销售代表ID：负责该客户的销售人员

	// 状态信息
	Status        string  `gorm:"column:status;default:'ACTIVE';comment:状态" json:"status"` // 状态：ACTIVE(正常)/INACTIVE(禁用)/BLACKLIST(黑名单)
	IsKeyCustomer bool    `gorm:"column:is_key_customer;default:false;comment:是否重点客户" json:"isKeyCustomer"`         // 是否重点客户：标识客户的重要程度
	Remark        *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                 // 备注：其他相关信息

	// 关联关系
	Contacts []CrmCustomerContact `gorm:"foreignKey:CustomerID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;comment:客户联系人" json:"contacts,omitempty"` // 客户联系人：一对多关系
}

// TableName 设置表名
func (CrmCustomer) TableName() string {
	return "crm_customer"
}
