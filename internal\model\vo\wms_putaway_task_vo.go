package vo

import (
	"time"
)

// WmsPutawayTaskVO 上架任务详细视图对象
type WmsPutawayTaskVO struct {
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	CreatedBy     uint      `json:"createdBy"`
	UpdatedBy     uint      `json:"updatedBy"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	TaskNo             string     `json:"taskNo"`
	ReceivingRecordID  uint       `json:"receivingRecordId"`
	AssignedToUserID   *uint      `json:"assignedToUserId,omitempty"`
	AssignedToUserName string     `json:"assignedToUserName"`
	Priority           int        `json:"priority"`
	Status             string     `json:"status"`
	Remark             *string    `json:"remark,omitempty"`
	CompletedAt        *time.Time `json:"completedAt,omitempty"`

	// 关联对象
	TaskDetails []WmsPutawayTaskDetailVO `json:"taskDetails,omitempty"`
}

// WmsPutawayTaskSimpleVO 上架任务简单视图对象（用于列表显示）
type WmsPutawayTaskSimpleVO struct {
	ID                uint       `json:"id"`
	TaskNo            string     `json:"taskNo"`
	ReceivingRecordID uint       `json:"receivingRecordId"`
	AssignedToUserID  *uint      `json:"assignedToUserId,omitempty"`
	Priority          int        `json:"priority"`
	Status            string     `json:"status"`
	CompletedAt       *time.Time `json:"completedAt,omitempty"`
	CreatedAt         time.Time  `json:"createdAt"`
}


// WmsPutawayTaskPageResult 上架任务分页结果
type WmsPutawayTaskPageResult = PageResult[WmsPutawayTaskSimpleVO]
