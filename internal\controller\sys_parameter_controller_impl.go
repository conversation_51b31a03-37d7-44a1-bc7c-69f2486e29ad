package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // 引入 apperrors
	"backend/pkg/response"         // 导入 response 包用于分页

	"github.com/kataras/iris/v12" // 引入 Iris
	// "strconv" // Iris 的 Params().GetUint 已经处理了转换
)

// SystemParameterControllerImpl 系统参数控制器实现
type SystemParameterControllerImpl struct {
	*BaseControllerImpl
	systemParameterService service.SystemParameterService // Store the specific service instance
}

// NewSystemParameterControllerImpl 创建系统参数控制器实例
func NewSystemParameterControllerImpl(cm *ControllerManager) *SystemParameterControllerImpl {
	base := NewBaseController(cm)
	// Get the specific SystemParameterService from the ServiceManager
	systemParameterService := cm.GetServiceManager().GetSystemParameterService()
	if systemParameterService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 SystemParameterService 实例")
	}
	return &SystemParameterControllerImpl{
		BaseControllerImpl:     base,
		systemParameterService: systemParameterService, // Store the specific service instance
	}
}

// Create godoc
// @Summary      创建系统参数
// @Description  创建一个新的系统参数
// @Tags         SystemParameters
// @Accept       json
// @Produce      json
// @Param        req   body      dto.SystemParameterCreateOrUpdateDTO  true  "创建参数请求" // <<< 修改 DTO 类型
// @Success      200  {object}  response.Response{data=vo.SystemParameterVO} "成功响应"
// @Failure      400  {object}  response.Response "请求错误"
// @Failure      500  {object}  response.Response "内部错误"
// @Router       /system/parameters [post]
// @Security     ApiKeyAuth
func (c *SystemParameterControllerImpl) Create(ctx iris.Context) {
	opName := "创建系统参数"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "system_parameter")

	var req dto.SystemParameterCreateOrUpdateDTO // <<< 修改 DTO 类型
	if err := ctx.ReadJSON(&req); err != nil {
		// 使用继承的 HandleError 和 apperrors
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "请求体解析失败").WithCause(err), opName)
		return
	}

	resultVO, err := c.systemParameterService.CreateParameter(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, resultVO) // 使用继承的 Success
}

// Update godoc
// @Summary      更新系统参数
// @Description  根据ID更新一个已存在的系统参数
// @Tags         SystemParameters
// @Accept       json
// @Produce      json
// @Param        id    path      int                     true  "参数ID"
// @Param        req   body      dto.SystemParameterCreateOrUpdateDTO  true  "更新参数请求" // <<< 修改 DTO 类型
// @Success      200  {object}  response.Response{data=vo.SystemParameterVO} "成功响应"
// @Failure      400  {object}  response.Response "请求错误"
// @Failure      404  {object}  response.Response "未找到错误"
// @Failure      500  {object}  response.Response "内部错误"
// @Router       /system/parameters/{id} [put]
// @Security     ApiKeyAuth
func (c *SystemParameterControllerImpl) Update(ctx iris.Context) {
	opName := "更新系统参数"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "system_parameter")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的ID格式"), opName)
		return
	}

	var req dto.SystemParameterCreateOrUpdateDTO // <<< 修改 DTO 类型
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "请求体解析失败").WithCause(err), opName)
		return
	}

	resultVO, err := c.systemParameterService.UpdateParameter(ctx.Request().Context(), id, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, resultVO)
}

// Delete godoc
// @Summary      删除系统参数
// @Description  根据ID删除一个系统参数 (系统内置参数可能无法删除)
// @Tags         SystemParameters
// @Accept       json
// @Produce      json
// @Param        id    path      int  true  "参数ID"
// @Success      200  {object}  response.Response "成功响应"
// @Failure      400  {object}  response.Response "请求错误"
// @Failure      404  {object}  response.Response "未找到错误"
// @Failure      500  {object}  response.Response "内部错误"
// @Router       /system/parameters/{id} [delete]
// @Security     ApiKeyAuth
func (c *SystemParameterControllerImpl) Delete(ctx iris.Context) {
	opName := "删除系统参数"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "system_parameter")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的ID格式"), opName)
		return
	}

	err = c.systemParameterService.DeleteParameter(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.SuccessWithMessage(ctx, "删除成功", nil) // 使用继承的 SuccessWithMessage
}

// GetByID godoc
// @Summary      获取单个系统参数
// @Description  根据ID获取系统参数详情
// @Tags         SystemParameters
// @Accept       json
// @Produce      json
// @Param        id    path      int  true  "参数ID"
// @Success      200  {object}  response.Response{data=vo.SystemParameterVO} "成功响应"
// @Failure      400  {object}  response.Response "请求错误"
// @Failure      404  {object}  response.Response "未找到错误"
// @Failure      500  {object}  response.Response "内部错误"
// @Router       /system/parameters/{id} [get]
// @Security     ApiKeyAuth
func (c *SystemParameterControllerImpl) GetByID(ctx iris.Context) {
	opName := "获取系统参数详情"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "system_parameter")

	id, err := ctx.Params().GetUint("id")
	if err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的ID格式"), opName)
		return
	}

	resultVO, err := c.systemParameterService.GetParameterByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, resultVO)
}

// Page godoc
// @Summary      分页获取系统参数列表
// @Description  根据条件分页获取系统参数
// @Tags         SystemParameters
// @Accept       json
// @Produce      json
// @Param        query query     dto.SystemParameterPageQueryDTO false "查询参数"
// @Success      200  {object}  response.Response{data=vo.PageSystemParameterVO} "成功响应"
// @Failure      400  {object}  response.Response "请求错误"
// @Failure      500  {object}  response.Response "内部错误"
// @Router       /system/parameters [get]
// @Security     ApiKeyAuth
func (c *SystemParameterControllerImpl) Page(ctx iris.Context) {
	opName := "分页查询系统参数"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "system_parameter")

	var queryDTO dto.SystemParameterPageQueryDTO
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "查询参数解析失败").WithCause(err), opName)
		return
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)

	// 将 BuildPageQuery 的结果赋值给 DTO 中的嵌入字段
	// queryDTO.PageQuery 结构与 response.PageQuery 一致
	queryDTO.PageQuery.PageNum = pageQueryFromBuild.PageNum
	queryDTO.PageQuery.PageSize = pageQueryFromBuild.PageSize
	queryDTO.PageQuery.Sort = pageQueryFromBuild.Sort
	// Total 和 Pages 字段由 Service 层或 BuildPageResult 填充

	pageResultVO, err := c.systemParameterService.PageParameters(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, pageResultVO) // 对于分页结果，通常会有 c.SuccessWithPage(ctx, pageResultVO)
}
