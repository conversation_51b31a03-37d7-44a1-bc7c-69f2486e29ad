<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :show-operations="true"
      show-index
      row-key="id"
      operation-fixed="right"
      :operation-width="120"
      @refresh="loadData"
      @add="handleGenerate"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
    >
      <template #column-status="{ row }">
        <el-tag :type="statusTagType(row.status)">
          {{ formatStatus(row.status) }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-button
          v-if="row.status === 'NEVER_OPENED' && showEnableButton"
          type="primary"
          link
          @click="handleChangeStatus(row, 'OP<PERSON>')"
        >
          启用
        </el-button>
        <el-button
          v-if="row.status === 'OPEN' && showCloseButton"
          type="danger"
          link
          @click="handleChangeStatus(row, 'CLOSED')"
        >
          关闭
        </el-button>
      </template>

    </VNTable>

    <!-- 生成帐期对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="生成帐期"
      width="40%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2"
        :label-width="'100px'"
        :loading="loading"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed, watch } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElTag, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import {
  getFiscalPeriodPage,
  generateFiscalPeriods,
  changeFiscalPeriodStatus,
} from '@/api/fin/fiscalPeriod';
import type {
    FiscalPeriodListItem,
    FiscalPeriodGenerateFormData
} from '@/api/fin/fiscalPeriod';
import { hasPermission } from '@/hooks/usePermission';

// --- Refs and State ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
const loading = ref(false);
const tableData = ref<FiscalPeriodListItem[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 12, // 默认显示12个月
});
const formData = ref<Partial<FiscalPeriodGenerateFormData>>({});
const currentFilters = ref<Record<string, any>>({});
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);
const userStore = useUserStore();
const currentAccountBookId = computed(() => userStore.currentAccountBookId);

// --- Computed Properties ---
const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('fin:periods:generate'),
  addText: '生成帐期', // 自定义新增按钮文本
  filter: true,
  columnSetting: true,
  density: true,
  fullscreen: true,
}));

const showEnableButton = computed(() => hasPermission('fin:periods:open'));
const showCloseButton = computed(() => hasPermission('fin:periods:close'));

const tableColumns = ref<TableColumn[]>([
  { prop: 'code', label: '期间代码', width: 120, sortable: true },
  { prop: 'fiscalYear', label: '财务年度', width: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'fiscalMonth', label: '财务月份', width: 120, sortable: true },
  { prop: 'startDate', label: '开始日期', width: 140 },
  { prop: 'endDate', label: '结束日期', width: 140 },
  { prop: 'status', label: '状态', width: 100, slot: true, filterable: true, filterType: 'select', 
    filterOptions: [
      { label: '未启用', value: 'NEVER_OPENED' },
      { label: '已打开', value: 'OPEN' },
      { label: '已关闭', value: 'CLOSED' },
    ]
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    minWidth: 160,
    sortable: true,
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

const formFields = computed<HeaderField[]>(() => [
  { field: 'fiscalYear', label: '财务年度', type: 'number', props: { 'value-format': 'YYYY', placeholder: '请输入4位年份', min: 2000, max: 2099 }, rules: [{ required: true, message: '财务年度为必填项' }] },
  { field: 'startYear', label: '开始年份', type: 'number', props: { 'value-format': 'YYYY', placeholder: '请输入4位年份', min: 2000, max: 2099 }, rules: [{ required: true, message: '开始年份为必填项' }] },
  { field: 'startMonth', label: '开始月份', type: 'number', props: { min: 1, max: 12 }, rules: [{ required: true, message: '开始月份为必填项' }] },
  { field: 'startDay', label: '开始日期', type: 'number', props: { min: 1, max: 31 }, rules: [{ required: true, message: '开始日期为必填项' }] },
]);

// --- Watchers ---
watch(() => formData.value.fiscalYear, (newYear) => {
  if (newYear && !formData.value.startYear) {
    formData.value.startYear = newYear;
  }
});

// --- Lifecycle Hooks ---
onMounted(() => {
  if (currentAccountBookId.value) {
    loadData();
  }
});

watch(currentAccountBookId, (newId) => {
  if (newId) {
    pagination.currentPage = 1;
    loadData();
  } else {
    tableData.value = [];
    pagination.total = 0;
  }
});

// --- Methods ---
const loadData = async () => {
  if (!currentAccountBookId.value) {
    ElMessage.warning('请先选择一个账簿');
    tableData.value = [];
    pagination.total = 0;
    return;
  }
  loading.value = true;
  try {
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop) {
        const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
        sortString = `${currentSort.value.prop},${direction}`;
    }

    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...currentFilters.value,
      sort: sortString,
      accountBookId: currentAccountBookId.value,
    };

    const response = await getFiscalPeriodPage(params);
    tableData.value = response.list;
    pagination.total = response.total;

  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const handleGenerate = () => {
  const currentYear = new Date().getFullYear();
  formData.value = {
    fiscalYear: currentYear,
    startYear: currentYear,
    startMonth: 1,
    startDay: 1,
  };
  dialogVisible.value = true;
  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
};

const submitForm = async () => {
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  if (!currentAccountBookId.value) {
      ElMessage.error('无法获取当前账簿ID，操作失败');
      return;
  }
  
  const dataToSend: FiscalPeriodGenerateFormData = {
    ...formData.value,
    accountBookId: currentAccountBookId.value,
  } as FiscalPeriodGenerateFormData;

  loading.value = true;
  try {
    await generateFiscalPeriods(dataToSend);
    ElMessage.success('帐期生成成功');
    handleCloseDialog();
    loadData();
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  } finally {
    loading.value = false;
  }
};

const handleFilterChange = (filters: Record<string, any>) => {
    currentFilters.value = filters;
    pagination.currentPage = 1; 
    loadData(); 
};

const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

const handleSortChange = (sort: { prop: string; order: 'ascending' | 'descending' | null }) => {
  currentSort.value = sort;
  loadData(); 
};

const handleChangeStatus = async (row: FiscalPeriodListItem, newStatus: string) => {
  const statusToActionText: Record<string, string> = {
    'OPEN': '启用',
    'CLOSED': '关闭',
  };
  const actionText = statusToActionText[newStatus] || '更改状态';
  
  try {
    await ElMessageBox.confirm(
      `确定要<strong style="color:red">${actionText}</strong>【${row.fiscalYear}年${row.fiscalMonth}月】这个会计期间吗？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      }
    );
    
    loading.value = true;
    await changeFiscalPeriodStatus(row.id, newStatus);
    ElMessage.success(`${actionText}成功`);
    await loadData();

  } catch (error) {
    if (error !== 'cancel') {
        const errorMessage = getApiErrorMessage(error);
        ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
        loading.value = false;
    }
  }
};

// --- 辅助函数：格式化日期时间 ---
const formatDateTime = (dateString: string | null | undefined): string => {
  // Explicitly handle null, undefined, or the zero-date string from backend
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    // Check if the date is valid AFTER attempting to parse
    if (isNaN(date.getTime())) { 
        console.warn('formatDateTime received invalid date string after initial check:', dateString);
        return '-'; // 无效日期返回占位符
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); 
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    // Log the error and the input string for better debugging
    console.error('Error formatting date:', dateString, '\nError:', e);
    return '-'; // 格式化出错返回占位符
  }
};

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  // 仅当它与 apiError.message (如果存在) 不同或 apiError.message 不存在时添加
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      // const listItemPrefix = detail.field ? `字段 ${detail.field}` : `${index + 1}`;
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Formatting Helpers ---
const formatStatus = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    NEVER_OPENED: '未启用',
    OPEN: '已打开',
    CLOSED: '已关闭',
  };
  return statusMap[status] || '未知';
};

const statusTagType = (status: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' => {
  switch (status) {
    case 'OPEN':
      return 'success';
    case 'CLOSED':
      return 'info';
    case 'NEVER_OPENED':
      return 'primary';
    default:
      return 'warning';
  }
};
</script> 