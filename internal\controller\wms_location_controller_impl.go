package controller

import (
	// "errors" // 标准错误包不再需要

	"github.com/kataras/iris/v12"
	// "github.com/kataras/iris/v12/mvc"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // 导入自定义错误包
	// "backend/pkg/validator"
)

// WmsLocationController 定义库位控制器接口
type WmsLocationController interface {
	Post(ctx iris.Context)
	PutBy(id uint, ctx iris.Context)
	DeleteBy(id uint, ctx iris.Context)
	GetBy(id uint, ctx iris.Context)
	Get(ctx iris.Context)
	GetWarehouses(ctx iris.Context)
	GetTree(ctx iris.Context)
}

// wmsLocationControllerImpl 库位控制器实现
type WmsLocationControllerImpl struct {
	BaseControllerImpl
	wmsLocationService service.WmsLocationService
}

// NewWmsLocationControllerImpl 创建库位控制器
func NewWmsLocationControllerImpl(cm *ControllerManager) *WmsLocationControllerImpl {
	return &WmsLocationControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		wmsLocationService: cm.GetServiceManager().GetWmsLocationService(),
	}
}

// Create 创建库位
// POST /wms/locations
func (ctrl *WmsLocationControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsLocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_location")

	var req dto.WmsLocationCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsLocationService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新库位
// PUT /wms/locations/{id:uint}
func (ctrl *WmsLocationControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsLocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_location")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsLocationUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsLocationService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除库位
// DELETE /wms/locations/{id:uint}
func (ctrl *WmsLocationControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsLocation"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_location")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsLocationService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个库位
// GET /wms/locations/{id:uint}
func (ctrl *WmsLocationControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsLocationByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_location")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsLocationService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取分页
// GET /wms/locations/page
func (ctrl *WmsLocationControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsLocationsPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_location")

	var req dto.WmsLocationQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	pageResult, err := ctrl.wmsLocationService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// GetList 获取仓库列表 (顶层库位)
// GET /wms/locations/list
func (ctrl *WmsLocationControllerImpl) GetList(ctx iris.Context) {
	const opName = "ListWarehouses"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_location")

	vos, err := ctrl.wmsLocationService.GetWarehouses(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vos)
}

// GetTree 获取库位树
// GET /wms/locations/tree
func (ctrl *WmsLocationControllerImpl) GetTree(ctx iris.Context) {
	const opName = "GetWmsLocationTree"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_location")

	// warehouseId 参数是可选的，如果提供则获取指定仓库的树，否则获取全部库位树
	warehouseID := ctx.URLParamUint64("warehouseId")

	tree, err := ctrl.wmsLocationService.GetLocationTree(ctx.Request().Context(), uint(warehouseID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, tree)
}
