package repository

import (
	"context"
	stdErrors "errors" // 统一标准 errors 别名
	"reflect"          // 导入 reflect 包
	"time"

	"backend/internal/model/entity"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // 统一自定义 errors 别名
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util"

	"gorm.io/gorm"
)

// UserRepository 用户仓库接口
// 负责用户数据的增删改查操作
type UserRepository interface {
	// 继承基础仓库接口
	BaseRepository[entity.User, uint]

	// FindByUsername 根据用户名查询用户
	// 参数:
	//   - ctx: 上下文
	//   - username: 用户名
	// 返回:
	//   - *entity.User: 用户实体 (包含预加载的 Roles 和 AccountBooks)
	//   - error: 错误信息
	FindByUsername(ctx context.Context, username string) (*entity.User, error)

	// FindByEmail 根据邮箱查询用户
	// 参数:
	//   - ctx: 上下文
	//   - email: 邮箱
	// 返回:
	//   - *entity.User: 用户实体 (包含预加载的 Roles 和 AccountBooks)
	//   - error: 错误信息
	FindByEmail(ctx context.Context, email string) (*entity.User, error)

	// FindByMobile 根据手机号查询用户
	// 参数:
	//   - ctx: 上下文
	//   - mobile: 手机号
	// 返回:
	//   - *entity.User: 用户实体 (包含预加载的 Roles 和 AccountBooks)
	//   - error: 错误信息
	FindByMobile(ctx context.Context, mobile string) (*entity.User, error)

	// FindByEmployeeID 根据员工ID查询用户
	// 参数:
	//   - ctx: 上下文
	//   - employeeID: 员工ID
	// 返回:
	//   - *entity.User: 用户实体
	//   - error: 错误信息
	FindByEmployeeID(ctx context.Context, employeeID uint) (*entity.User, error)

	// UpdatePassword 更新用户密码
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - password: 新密码（已加密）
	// 返回:
	//   - error: 错误信息
	UpdatePassword(ctx context.Context, userID uint, password string) error

	// UpdateStatus 更新用户状态
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - status: 新状态
	// 返回:
	//   - error: 错误信息
	UpdateStatus(ctx context.Context, userID uint, status int) error

	// UpdateLoginInfo 更新用户登录信息
	// 参数:
	//   - ctx: 上下文
	//   - userID: 用户ID
	//   - ip: 登录IP
	// 返回:
	//   - error: 错误信息
	UpdateLoginInfo(ctx context.Context, userID uint, ip string) error

	// UpdateProfile 更新用户个人信息
	// 参数:
	//   - ctx: 上下文
	//   - user: 用户实体
	// 返回:
	//   - error: 错误信息
	UpdateProfile(ctx context.Context, user *entity.User) error

	// CheckUsernameExists 检查用户名是否存在
	// 参数:
	//   - ctx: 上下文
	//   - username: 用户名
	//   - excludeID: 排除的用户ID（可选，用于更新时检查）
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	CheckUsernameExists(ctx context.Context, username string, excludeID ...uint) (bool, error)

	// CheckEmailExists 检查邮箱是否存在
	// 参数:
	//   - ctx: 上下文
	//   - email: 邮箱
	//   - excludeID: 排除的用户ID（可选，用于更新时检查）
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	CheckEmailExists(ctx context.Context, email string, excludeID ...uint) (bool, error)

	// CheckMobileExists 检查手机号是否存在
	// 参数:
	//   - ctx: 上下文
	//   - mobile: 手机号
	//   - excludeID: 排除的用户ID（可选，用于更新时检查）
	// 返回:
	//   - bool: 是否存在
	//   - error: 错误信息
	CheckMobileExists(ctx context.Context, mobile string, excludeID ...uint) (bool, error)

	// --- 重写 BaseRepository 的 FindByPage 以添加 Preload ---
	// FindByPage 分页查询用户 (包含 Roles 和 AccountBooks)
	// 参数:
	//   - ctx: 上下文
	//   - pageQuery: 分页和排序参数
	//   - conditions: 查询条件列表 (实现 QueryCondition 接口)
	// 返回:
	//   - *response.PageResult: 分页结果 (List 中包含预加载数据)
	//   - error: 错误信息
	FindByPage(ctx context.Context, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error)

	// --- 重写 BaseRepository 的 FindByIds 以添加 Preload ---
	// FindByIds 根据 ID 列表批量查询用户 (包含 Roles 和 AccountBooks)
	// 参数:
	//   - ctx: 上下文
	//   - ids: ID列表
	// 返回:
	//   - []*entity.User: 用户实体列表 (包含预加载的 Roles 和 AccountBooks)
	//   - error: 错误信息
	FindByIds(ctx context.Context, ids []uint) ([]*entity.User, error)

	// --- 重写 BaseRepository 的 FindByID 以添加 Preload ---
	// FindByID 根据ID查询实体 (包含 Roles 和 AccountBooks)
	// 参数:
	//   - ctx: 上下文
	//   - id: 实体ID
	// 返回:
	//   - *entity.User: 用户实体 (包含预加载的 Roles 和 AccountBooks)
	//   - error: 错误信息
	FindByID(ctx context.Context, id uint) (*entity.User, error)

	// LockSecurity 安全锁定用户 (新增)
	LockSecurity(ctx context.Context, userID uint) error

	// UnlockSecurity 安全解锁用户 (新增)
	UnlockSecurity(ctx context.Context, userID uint) error
}

// UserRepositoryImpl 用户仓库实现
type UserRepositoryImpl struct {
	BaseRepository[entity.User, uint] // 嵌入基础仓库接口
}

// NewUserRepository 创建用户仓库
// 参数:
//   - db: 数据库连接
//
// 返回:
//   - UserRepository: 用户仓库接口
func NewUserRepository(db *gorm.DB) UserRepository {
	return &UserRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.User, uint](db), // 使用构造函数初始化接口
	}
}

// FindByEmployeeID 根据员工ID查询用户
func (r *UserRepositoryImpl) FindByEmployeeID(ctx context.Context, employeeID uint) (*entity.User, error) {
	var user entity.User
	result := r.GetDB(ctx).Where("employee_id = ?", employeeID).First(&user)
	if result.Error != nil {
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 对于Find这类操作，未找到应该返回一个特定的、可识别的错误
			return nil, apperrors.ErrNotFound
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据员工ID查询用户失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败").WithCause(result.Error)
	}
	return &user, nil
}

// --- 重写 FindByID 以添加 Preload ---
// FindByID 根据ID查询用户
func (r *UserRepositoryImpl) FindByID(ctx context.Context, id uint) (*entity.User, error) {
	var user entity.User
	result := r.GetDB(ctx).
		Preload("Roles").
		Preload("AccountBooks").
		First(&user, id)
	if result.Error != nil {
		// 统一使用 stdErrors.Is 和 apperrors
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("查询用户失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败").WithCause(result.Error)
	}
	return &user, nil
}

// FindByUsername 根据用户名查询用户
func (r *UserRepositoryImpl) FindByUsername(ctx context.Context, username string) (*entity.User, error) {
	var user entity.User
	result := r.GetDB(ctx).
		Preload("Roles").
		Preload("AccountBooks").
		Where("username = ?", username).First(&user)
	if result.Error != nil {
		// 统一使用 stdErrors.Is 和 apperrors
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据用户名查询用户失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败").WithCause(result.Error)
	}
	return &user, nil
}

// FindByEmail 根据邮箱查询用户
func (r *UserRepositoryImpl) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
	var user entity.User
	result := r.GetDB(ctx).
		Preload("Roles").
		Preload("AccountBooks").
		Where("email = ?", email).First(&user)
	if result.Error != nil {
		// 统一使用 stdErrors.Is 和 apperrors
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据邮箱查询用户失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败").WithCause(result.Error)
	}
	return &user, nil
}

// FindByMobile 根据手机号查询用户
func (r *UserRepositoryImpl) FindByMobile(ctx context.Context, mobile string) (*entity.User, error) {
	var user entity.User
	result := r.GetDB(ctx).
		Preload("Roles").
		Preload("AccountBooks").
		Where("mobile = ?", mobile).First(&user)
	if result.Error != nil {
		// 统一使用 stdErrors.Is 和 apperrors (这里之前就是对的)
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据手机号查询用户失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败").WithCause(result.Error)
	}
	return &user, nil
}

// UpdatePassword 更新用户密码
func (r *UserRepositoryImpl) UpdatePassword(ctx context.Context, userID uint, password string) error {
	db := r.GetDB(ctx)
	log := logger.WithContext(ctx)

	uid64, err := util.GetUserIDFromStdContext(ctx)
	if err != nil {
		log.WithError(err).Warn("UserRepository.UpdatePassword: 无法从上下文中获取UserID，UpdatedBy将不会被设置")
	}

	updates := map[string]interface{}{
		"password":      password,
		"last_pwd_time": time.Now(),
	}
	if uid64 > 0 {
		updates["updated_by"] = uint(uid64)
		log.Infof("UserRepository.UpdatePassword: Setting updated_by to %d for user ID %d", uint(uid64), userID)
	} else {
		log.Warnf("UserRepository.UpdatePassword: updated_by will not be set for user ID %d as UserID could not be retrieved or was invalid (0).", userID)
	}

	result := db.Model(&entity.User{}).Where("id = ?", userID).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("更新用户密码失败")
		// 统一使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新密码失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
	}
	return nil
}

// UpdateStatus 更新用户状态
func (r *UserRepositoryImpl) UpdateStatus(ctx context.Context, userID uint, status int) error {
	db := r.GetDB(ctx)
	log := logger.WithContext(ctx)

	uid64, err := util.GetUserIDFromStdContext(ctx)
	if err != nil {
		log.WithError(err).Warn("UserRepository.UpdateStatus: 无法从上下文中获取UserID，UpdatedBy将不会被设置")
	}

	updates := map[string]interface{}{
		"status": status,
	}
	if uid64 > 0 {
		updates["updated_by"] = uint(uid64)
		log.Infof("UserRepository.UpdateStatus: Setting updated_by to %d for user ID %d", uint(uid64), userID)
	} else {
		log.Warnf("UserRepository.UpdateStatus: updated_by will not be set for user ID %d as UserID could not be retrieved or was invalid (0).", userID)
	}

	result := db.Model(&entity.User{}).Where("id = ?", userID).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("更新用户状态失败")
		// 统一使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新状态失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
	}
	return nil
}

// UpdateLoginInfo 更新用户登录信息
func (r *UserRepositoryImpl) UpdateLoginInfo(ctx context.Context, userID uint, ip string) error {
	updates := map[string]interface{}{
		"login_ip":    ip,
		"login_time":  time.Now(),
		"login_count": gorm.Expr("login_count + 1"),
	}
	result := r.GetDB(ctx).Model(&entity.User{}).Where("id = ?", userID).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("更新用户登录信息失败")
		// 统一使用 apperrors
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新登录信息失败").WithCause(result.Error)
	}
	// RowsAffected == 0 在这里可能是正常的 (e.g., IP and time haven't changed enough)
	// 通常不检查 RowsAffected，除非业务明确要求
	return nil
}

// UpdateProfile 更新用户个人信息
func (r *UserRepositoryImpl) UpdateProfile(ctx context.Context, user *entity.User) error {
	// 使用 Select 更新指定字段，避免更新密码等敏感信息
	result := r.GetDB(ctx).Model(&entity.User{}).Where("id = ?", user.ID).Select(
		"Nickname", "RealName", "Avatar", "Gender", "Email", "Mobile", "Remark", "UpdatedBy", "DefaultAccountBookID", // 添加 DefaultAccountBookID
	).Updates(user)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("更新用户个人信息失败")
		// 修改错误返回，使用 apperrors.WrapError
		return apperrors.WrapError(result.Error, apperrors.CODE_DATA_UPDATE_FAILED, "更新个人信息失败")
	}
	if result.RowsAffected == 0 {
		// 如果需要确保用户存在，可以返回 Not Found
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在或信息无变化")
	}
	return nil
}

// CheckUsernameExists 检查用户名是否存在
func (r *UserRepositoryImpl) CheckUsernameExists(ctx context.Context, username string, excludeID ...uint) (bool, error) {
	var count int64
	query := r.GetDB(ctx).Model(&entity.User{}).Where("username = ?", username)
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查用户名是否存在失败")
		// 统一使用 apperrors
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查用户名失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// CheckEmailExists 检查邮箱是否存在
func (r *UserRepositoryImpl) CheckEmailExists(ctx context.Context, email string, excludeID ...uint) (bool, error) {
	if email == "" {
		return false, nil // 空邮箱不视为存在
	}
	var count int64
	query := r.GetDB(ctx).Model(&entity.User{}).Where("email = ?", email)
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查邮箱是否存在失败")
		// 统一使用 apperrors
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查邮箱失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// CheckMobileExists 检查手机号是否存在
func (r *UserRepositoryImpl) CheckMobileExists(ctx context.Context, mobile string, excludeID ...uint) (bool, error) {
	if mobile == "" {
		return false, nil // 空手机号不视为存在
	}
	var count int64
	query := r.GetDB(ctx).Model(&entity.User{}).Where("mobile = ?", mobile)
	if len(excludeID) > 0 && excludeID[0] > 0 {
		query = query.Where("id != ?", excludeID[0])
	}
	result := query.Count(&count)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("检查手机号是否存在失败")
		// 统一使用 apperrors
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查手机号失败").WithCause(result.Error)
	}
	return count > 0, nil
}

// --- 重写 FindByPage 以添加 Preload ---
// FindByPage 分页查询用户 (包含 Roles 和 AccountBooks)
func (r *UserRepositoryImpl) FindByPage(ctx context.Context, pageQuery *response.PageQuery, conditions []QueryCondition) (*response.PageResult, error) {
	page := pageQuery.PageNum
	size := pageQuery.PageSize
	if page < 1 {
		page = constant.DEFAULT_PAGE_NUM
	}
	if size < 1 {
		size = constant.DEFAULT_PAGE_SIZE
	}
	if size > constant.MAX_PAGE_SIZE {
		size = constant.MAX_PAGE_SIZE
	}

	var total int64
	var list []*entity.User
	db := r.GetDB(ctx).Model(&entity.User{})

	// 应用查询条件
	db = ApplyConditions(db, conditions)

	// 计算总数
	err := db.Count(&total).Error
	if err != nil {
		logger.WithContext(ctx).WithError(err).Error("分页查询用户总数失败")
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询总数失败")
	}

	// 如果总数为0，则无需查询列表
	if total == 0 {
		return &response.PageResult{
			Total:    0,
			List:     []*entity.User{}, // 返回空列表而不是 nil
			PageNum:  page,
			PageSize: size,
		}, nil
	}

	// 应用分页和排序
	offset := (page - 1) * size
	// 获取 User 实体的允许排序字段
	entityType := reflect.TypeOf(entity.User{})
	allowedColumns := getSortableColumns(entityType)
	db = applySortInfos(db, pageQuery.Sort, allowedColumns) // 传递 allowedColumns

	// 查询数据并进行 Preload
	err = db.Offset(offset).Limit(size).
		Preload("Roles").
		Preload("AccountBooks").
		Find(&list).Error
	if err != nil {
		// 不需要检查 gorm.ErrRecordNotFound，因为 Find 返回空切片
		logger.WithContext(ctx).WithError(err).Error("分页查询用户列表失败")
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "分页查询失败")
	}

	// 返回分页结果
	return &response.PageResult{
		Total:    total,
		List:     list,
		PageNum:  page,
		PageSize: size,
	}, nil
}

// FindByIds 根据 ID 列表批量查询用户 (重写以添加 Preload)
func (r *UserRepositoryImpl) FindByIds(ctx context.Context, ids []uint) ([]*entity.User, error) {
	if len(ids) == 0 {
		return []*entity.User{}, nil
	}
	var users []*entity.User
	// 使用 Where "id IN ?", ids 进行批量查询，并添加 Preload
	result := r.GetDB(ctx).
		Preload("Roles").
		Preload("AccountBooks").
		Where("id IN ?", ids).Find(&users)
	if result.Error != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据ID列表查询用户失败 (IDs: %v)", ids)
		// 统一使用 apperrors
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询用户列表失败").WithCause(result.Error)
	}
	return users, nil
}

// LockSecurity 安全锁定用户 (新增)
func (r *UserRepositoryImpl) LockSecurity(ctx context.Context, userID uint) error {
	result := r.GetDB(ctx).Model(&entity.User{}).Where("id = ?", userID).Update("security_lock", true)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("锁定用户失败")
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "锁定用户失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
	}
	return nil
}

// UnlockSecurity 安全解锁用户 (新增)
func (r *UserRepositoryImpl) UnlockSecurity(ctx context.Context, userID uint) error {
	result := r.GetDB(ctx).Model(&entity.User{}).Where("id = ?", userID).Update("security_lock", false)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("解锁用户失败")
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "解锁用户失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
	}
	return nil
}
