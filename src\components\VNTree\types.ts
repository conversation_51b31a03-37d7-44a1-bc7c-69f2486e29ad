import type { TreeOptionProps, TreeProps } from 'element-plus/es/components/tree/src/tree.type';
import type { ElTree } from 'element-plus';

// 基础树节点接口，可以根据需要扩展
export interface TreeNode {
  id: string | number; // 节点的唯一标识
  label: string; // 节点显示的文本
  children?: TreeNode[]; // 子节点
  disabled?: boolean; // 是否禁用
  isLeaf?: boolean; // 是否叶子节点 (用于懒加载)
  [key: string]: any; // 允许添加其他自定义属性
}

// 获取 ElTree 组件实例的类型
export type VNTreeInstance = InstanceType<typeof ElTree>;

// 继承 ElTree 的 Props (使用 Partial 使其可选，并排除 data 和 node-key)
// Omit 'filterNodeMethod', 'load', 'renderContent', 'renderAfterExpand' as they are better handled via events/slots
export interface VNTreeProps extends Partial<Omit<TreeProps, 'data' | 'nodeKey' | 'props' | 'filterNodeMethod' | 'load' | 'renderContent' | 'renderAfterExpand' >> {
  data: TreeNode[]; // 树形结构数据
  nodeKey?: string; // 每个树节点用来作为唯一标识的属性，整棵树唯一。默认为 'id'
  treeProps?: TreeOptionProps; // 配置选项，指定节点标签、子节点、禁用、叶子节点的字段名。默认 { label: 'label', children: 'children', disabled: 'disabled', isLeaf: 'isLeaf' }
  
  // 可以添加一些自定义的简化 Props
  showCheckbox?: boolean; // 是否显示复选框，简化 el-tree 的 show-checkbox
  defaultExpandAll?: boolean; // 是否默认展开所有节点
  filterText?: string; // 用于前端过滤的文本

  // 懒加载相关 (替代 el-tree 的 load prop)
  lazyLoad?: (node: any, resolve: (data: TreeNode[]) => void) => void;
}

// 定义组件 Emits (可以转发 el-tree 的事件)
export interface VNTreeEmits {
  (e: 'node-click', data: TreeNode, node: any, event: MouseEvent): void;
  (e: 'node-contextmenu', event: Event, data: TreeNode, node: any): void;
  (e: 'check-change', data: TreeNode, checked: boolean, indeterminate: boolean): void;
  (e: 'check', data: TreeNode, checkedInfo: { checkedNodes: TreeNode[], checkedKeys: (string|number)[], halfCheckedNodes: TreeNode[], halfCheckedKeys: (string|number)[] }): void;
  (e: 'current-change', data: TreeNode, node: any): void;
  (e: 'node-expand', data: TreeNode, node: any): void;
  (e: 'node-collapse', data: TreeNode, node: any): void;
  // 可以添加自定义事件
} 