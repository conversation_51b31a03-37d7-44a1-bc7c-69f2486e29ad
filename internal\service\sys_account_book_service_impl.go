package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// AccountBookService 账套服务接口
type AccountBookService interface {
	BaseService // Embed base service if applicable

	CreateAccountBook(ctx context.Context, req dto.AccountBookCreateOrUpdateDTO) (*vo.AccountBookVO, error)
	UpdateAccountBook(ctx context.Context, id uint, req dto.AccountBookCreateOrUpdateDTO) (*vo.AccountBookVO, error)
	DeleteAccountBook(ctx context.Context, id uint) error
	GetAccountBookByID(ctx context.Context, id uint) (*vo.AccountBookVO, error)
	ListAccountBooks(ctx context.Context, queryDTO dto.AccountBookPageQueryDTO) ([]*vo.AccountBookVO, error)
	PageAccountBooks(ctx context.Context, pageQueryDTO dto.AccountBookPageQueryDTO) (*vo.PageVO, error)
	UpdateAccountBookStatus(ctx context.Context, id uint, status int) error

	// ConvertToAccountBookVOList 转换账套实体列表为VO列表 (新增)
	ConvertToAccountBookVOList(ctx context.Context, accountBooks []*entity.AccountBook) ([]*vo.AccountBookVO, error)
}

// accountBookServiceImpl 账套服务实现
type accountBookServiceImpl struct {
	*BaseServiceImpl
}

// NewAccountBookService 创建账套服务
func NewAccountBookService(sm *ServiceManager) AccountBookService {
	return &accountBookServiceImpl{
		BaseServiceImpl: NewBaseService(sm),
	}
}

// GetAccountBookService 从服务管理器获取账套服务
func GetAccountBookService(sm *ServiceManager) AccountBookService {
	return GetService[AccountBookService](
		sm,
		func(sm *ServiceManager) AccountBookService {
			return NewAccountBookService(sm)
		},
	)
}

// getAccountBookRepository 获取账套仓库
func (s *accountBookServiceImpl) getAccountBookRepository() repository.AccountBookRepository {
	return s.GetServiceManager().GetRepositoryManager().GetAccountBookRepository()
}

// CreateAccountBook 创建账套
func (s *accountBookServiceImpl) CreateAccountBook(ctx context.Context, req dto.AccountBookCreateOrUpdateDTO) (*vo.AccountBookVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "创建账套开始", logger.WithField("code", req.Code), logger.WithField("name", req.Name))

	if err := s.validateAccountBookParams(ctx, req.Code, req.Name, 0); err != nil {
		return nil, err
	}

	accountBook := &entity.AccountBook{
		Code:           req.Code,
		Name:           req.Name,
		CompanyName:    req.CompanyName,
		TaxID:          req.TaxID,
		CompanyAddress: req.CompanyAddress,
		CompanyPhone:   req.CompanyPhone,
		BankName:       req.BankName,
		BankAccount:    req.BankAccount,
		IsGroup:        derefBoolPointer(req.IsGroup, false),   // 使用内联辅助函数
		IsVirtual:      derefBoolPointer(req.IsVirtual, false), // 使用内联辅助函数
		Status:         req.Status,
	}

	// Set CreatedBy and UpdatedBy
	uid64, err := s.GetUserIDFromContext(ctx) // Use s.GetUserIDFromContext via BaseServiceImpl
	if err != nil {
		log.WithError(err).Warn("CreateAccountBook: 无法从上下文中获取UserID")
	}
	accountBook.CreatedBy = uint(uid64)
	accountBook.UpdatedBy = uint(uid64)

	repo := s.getAccountBookRepository()
	if err := repo.Create(ctx, accountBook); err != nil {
		log.Error(ctx, "创建账套数据库操作失败", logger.WithError(err))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_CREATE_FAILED, "创建账套失败")
	}

	log.Info(ctx, "创建账套成功", logger.WithField("id", accountBook.ID))

	return s.convertToAccountBookVO(accountBook), nil
}

// UpdateAccountBook 更新账套
func (s *accountBookServiceImpl) UpdateAccountBook(ctx context.Context, id uint, req dto.AccountBookCreateOrUpdateDTO) (*vo.AccountBookVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "更新账套开始", logger.WithField("id", id))

	repo := s.getAccountBookRepository()

	accountBook, err := repo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "更新账套：查询账套失败", logger.WithError(err), logger.WithField("id", id))
		return nil, err
	}

	if err := s.validateAccountBookParams(ctx, req.Code, req.Name, id); err != nil {
		return nil, err
	}

	accountBook.Code = req.Code
	accountBook.Name = req.Name
	accountBook.CompanyName = req.CompanyName
	accountBook.TaxID = req.TaxID
	accountBook.CompanyAddress = req.CompanyAddress
	accountBook.CompanyPhone = req.CompanyPhone
	accountBook.BankName = req.BankName
	accountBook.BankAccount = req.BankAccount
	accountBook.IsGroup = derefBoolPointer(req.IsGroup, false)     // 使用内联辅助函数
	accountBook.IsVirtual = derefBoolPointer(req.IsVirtual, false) // 使用内联辅助函数
	accountBook.Status = req.Status

	// Set UpdatedBy
	uid64Update, errUpdate := s.GetUserIDFromContext(ctx) // Use s.GetUserIDFromContext via BaseServiceImpl
	if errUpdate != nil {
		log.WithError(errUpdate).Warn("UpdateAccountBook: 无法从上下文中获取UserID")
	}
	accountBook.UpdatedBy = uint(uid64Update)

	if err := repo.Update(ctx, accountBook); err != nil {
		log.Error(ctx, "更新账套数据库操作失败", logger.WithError(err), logger.WithField("id", id))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新账套失败")
	}

	log.Info(ctx, "更新账套成功", logger.WithField("id", accountBook.ID))

	return s.convertToAccountBookVO(accountBook), nil
}

// DeleteAccountBook 删除账套
func (s *accountBookServiceImpl) DeleteAccountBook(ctx context.Context, id uint) error {
	log := s.GetLogger()
	log.Debug(ctx, "删除账套开始", logger.WithField("id", id))

	// <<< 先在事务外检查账套是否存在 >>>
	accountBookRepo := s.getAccountBookRepository() // Non-tx repo for initial check
	_, err := accountBookRepo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "删除账套：查询账套失败 (删除前检查)", logger.WithError(err), logger.WithField("id", id))
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return nil // Not found, consider delete successful (idempotent)
		}
		return err // Return wrapped error from FindByID if configured
	}

	// <<< 使用事务包裹检查和删除操作 >>>
	err = s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		txUserAccountBookRepo := txSm.GetRepositoryManager().GetUserAccountBookRepository()
		txAccountBookRepo := txSm.GetRepositoryManager().GetAccountBookRepository()
		txLog := logger.WithContext(ctx).WithFields(logger.Fields{"tx": true, "accountBookId": id})

		// 1. 检查账套是否有关联用户 (事务内检查，使用 FindUserIDsByAccountBookID 获取总数)
		// We only need the total count, so page/pageSize can be minimal.
		_, totalUsers, checkErr := txUserAccountBookRepo.FindUserIDsByAccountBookID(ctx, id, 1, 0) // Use page=1, pageSize=0 to just get total
		if checkErr != nil {
			txLog.Error("事务中查询账套关联用户数失败", logger.WithError(checkErr))
			return apperrors.WrapError(checkErr, apperrors.CODE_DATA_QUERY_FAILED, "检查用户关联失败")
		}

		// Check the total count
		if totalUsers > 0 {
			txLog.Warn("账套已分配给用户，不允许删除", logger.WithField("userCount", totalUsers))
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "账套已分配给用户，不允许删除")
		}

		// 2. 删除账套
		if deleteErr := txAccountBookRepo.Delete(ctx, id); deleteErr != nil {
			txLog.Error("事务中删除账套失败", logger.WithError(deleteErr))
			return apperrors.WrapError(deleteErr, apperrors.CODE_DATA_DELETE_FAILED, "删除账套失败")
		}

		return nil // 事务成功
	})

	if err != nil {
		// 事务执行失败，错误已在内部记录和包装
		return err
	}

	log.Info(ctx, "删除账套成功", logger.WithField("id", id))
	return nil
}

// GetAccountBookByID 获取单个账套
func (s *accountBookServiceImpl) GetAccountBookByID(ctx context.Context, id uint) (*vo.AccountBookVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "获取账套详情", logger.WithField("id", id))

	repo := s.getAccountBookRepository()
	accountBook, err := repo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "获取账套详情：查询失败", logger.WithError(err), logger.WithField("id", id))
		return nil, err
	}

	return s.convertToAccountBookVO(accountBook), nil
}

// ListAccountBooks 获取账套列表
func (s *accountBookServiceImpl) ListAccountBooks(ctx context.Context, queryDTO dto.AccountBookPageQueryDTO) ([]*vo.AccountBookVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "获取账套列表")

	// 构建具体条件切片
	concreteConditions := s.buildAccountBookQueryConditions(queryDTO)
	repo := s.getAccountBookRepository()

	// 从 DTO 获取排序信息
	sortInfos := queryDTO.PageQuery.Sort
	// 如果 DTO 没有提供排序，可以设置默认排序
	if len(sortInfos) == 0 {
		sortInfos = []response.SortInfo{{Field: "created_at", Order: "desc"}}
	}

	// 将 []repository.Condition 转换为 []repository.QueryCondition
	queryConditions := make([]repository.QueryCondition, len(concreteConditions))
	for i, cond := range concreteConditions {
		queryConditions[i] = cond // Condition 实现了 QueryCondition 接口
	}

	// 调用 repo.FindByCondition，使用正确的参数
	accountBooks, err := repo.FindByCondition(ctx, queryConditions, sortInfos)
	if err != nil {
		log.Error(ctx, "获取账套列表：查询失败", logger.WithError(err))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询账套列表失败")
	}

	// 转换结果为 VO 列表
	return s.convertToAccountBookVOList(accountBooks), nil
}

// PageAccountBooks 分页获取账套列表
func (s *accountBookServiceImpl) PageAccountBooks(ctx context.Context, pageQueryDTO dto.AccountBookPageQueryDTO) (*vo.PageVO, error) {
	log := s.GetLogger()
	page := pageQueryDTO.PageQuery.PageNum
	size := pageQueryDTO.PageQuery.PageSize
	log.Debug(ctx, "分页获取账套列表", logger.WithField("page", page), logger.WithField("size", size))

	// 构建具体条件切片
	concreteConditions := s.buildAccountBookQueryConditions(pageQueryDTO)
	repo := s.getAccountBookRepository()

	pageQuery := &response.PageQuery{
		PageNum:  page,
		PageSize: size,
		Sort:     pageQueryDTO.PageQuery.Sort,
	}

	// 将 []repository.Condition 转换为 []repository.QueryCondition
	queryConditions := make([]repository.QueryCondition, len(concreteConditions))
	for i, cond := range concreteConditions {
		queryConditions[i] = cond // Condition 实现了 QueryCondition 接口
	}

	// 使用转换后的接口切片调用 FindByPage
	pageResult, err := repo.FindByPage(ctx, pageQuery, queryConditions)
	if err != nil {
		log.Error(ctx, "分页获取账套列表：查询失败", logger.WithError(err))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "分页查询账套列表失败")
	}

	accountBookEntities, ok := pageResult.List.([]*entity.AccountBook)
	if !ok {
		log.Error(ctx, "分页查询结果类型断言失败", logger.WithField("type", fmt.Sprintf("%T", pageResult.List)))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "分页查询账套结果类型错误")
	}
	accountBookVOs, _ := s.ConvertToAccountBookVOList(ctx, accountBookEntities)

	pageVO := &vo.PageVO{
		List:     accountBookVOs,
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
		Pages:    pageResult.Pages,
	}

	return pageVO, nil
}

// UpdateAccountBookStatus 更新账套状态
func (s *accountBookServiceImpl) UpdateAccountBookStatus(ctx context.Context, id uint, status int) error {
	log := s.GetLogger()
	log.Debug(ctx, "更新账套状态", logger.WithField("id", id), logger.WithField("status", status))

	repo := s.getAccountBookRepository()

	accountBook, err := repo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "更新账套状态：查询失败", logger.WithError(err), logger.WithField("id", id))
		return err
	}

	if accountBook.Status == status {
		log.Debug(ctx, "账套状态未改变，无需更新", logger.WithField("id", id))
		return nil
	}

	accountBook.Status = status

	// Set UpdatedBy
	uid64StatusUpdate, errStatusUpdate := s.GetUserIDFromContext(ctx) // Use s.GetUserIDFromContext via BaseServiceImpl
	if errStatusUpdate != nil {
		log.WithError(errStatusUpdate).Warn("UpdateAccountBookStatus: 无法从上下文中获取UserID")
	}
	accountBook.UpdatedBy = uint(uid64StatusUpdate)

	if err := repo.Update(ctx, accountBook); err != nil {
		log.Error(ctx, "更新账套状态数据库操作失败", logger.WithError(err), logger.WithField("id", id))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新账套状态失败")
	}

	log.Info(ctx, "更新账套状态成功", logger.WithField("id", id))
	return nil
}

// validateAccountBookParams 验证账套参数 (唯一性检查)
func (s *accountBookServiceImpl) validateAccountBookParams(ctx context.Context, code, name string, id uint) error {
	repo := s.getAccountBookRepository()

	if code != "" {
		existingByCode, err := repo.FindByCode(ctx, code)
		if err != nil && apperrors.GetErrorCode(err) != apperrors.CODE_DATA_NOT_FOUND {
			s.GetLogger().Error(ctx, "验证账套编码唯一性查询失败", logger.WithError(err), logger.WithField("code", code))
			return apperrors.NewSystemError(apperrors.CODE_DATA_QUERY_FAILED, "验证账套编码唯一性时出错")
		}
		if existingByCode != nil && existingByCode.ID != id {
			return apperrors.NewParamError(apperrors.CODE_DATA_ALREADY_EXISTS, fmt.Sprintf("账套编码 '%s' 已存在", code))
		}
	}

	if name != "" {
		existingByName, err := repo.FindByName(ctx, name)
		if err != nil && apperrors.GetErrorCode(err) != apperrors.CODE_DATA_NOT_FOUND {
			s.GetLogger().Error(ctx, "验证账套名称唯一性查询失败", logger.WithError(err), logger.WithField("name", name))
			return apperrors.NewSystemError(apperrors.CODE_DATA_QUERY_FAILED, "验证账套名称唯一性时出错")
		}
		if existingByName != nil && existingByName.ID != id {
			return apperrors.NewParamError(apperrors.CODE_DATA_ALREADY_EXISTS, fmt.Sprintf("账套名称 '%s' 已存在", name))
		}
	}

	return nil
}

// buildAccountBookQueryConditions 构建账套查询条件
func (s *accountBookServiceImpl) buildAccountBookQueryConditions(pageQueryDTO dto.AccountBookPageQueryDTO) []repository.Condition {
	conditions := make([]repository.Condition, 0)
	if pageQueryDTO.Code != "" {
		conditions = append(conditions, repository.NewLikeCondition("code", pageQueryDTO.Code))
	}
	if pageQueryDTO.Name != "" {
		conditions = append(conditions, repository.NewLikeCondition("name", pageQueryDTO.Name))
	}
	if pageQueryDTO.CompanyName != "" {
		conditions = append(conditions, repository.NewLikeCondition("company_name", pageQueryDTO.CompanyName))
	}
	if pageQueryDTO.Status != nil {
		conditions = append(conditions, repository.NewEqualCondition("status", *pageQueryDTO.Status))
	}
	if pageQueryDTO.IsGroup != nil {
		conditions = append(conditions, repository.NewEqualCondition("is_group", *pageQueryDTO.IsGroup))
	}
	if pageQueryDTO.IsVirtual != nil {
		conditions = append(conditions, repository.NewEqualCondition("is_virtual", *pageQueryDTO.IsVirtual))
	}
	return conditions
}

// convertToAccountBookVO 将账套实体转换为视图对象
func (s *accountBookServiceImpl) convertToAccountBookVO(ab *entity.AccountBook) *vo.AccountBookVO {
	if ab == nil {
		return nil
	}
	baseVO := vo.BaseVO{
		ID:        ab.ID,
		CreatedAt: ab.CreatedAt,
		UpdatedAt: ab.UpdatedAt,
		CreatedBy: ab.CreatedBy,
		UpdatedBy: ab.UpdatedBy,
	}
	tenantVO := vo.TenantVO{
		BaseVO:   baseVO,
		TenantID: ab.TenantID,
	}

	return &vo.AccountBookVO{
		TenantVO:       tenantVO,
		Code:           ab.Code,
		Name:           ab.Name,
		CompanyName:    ab.CompanyName,
		TaxID:          ab.TaxID,
		CompanyAddress: ab.CompanyAddress,
		CompanyPhone:   ab.CompanyPhone,
		BankName:       ab.BankName,
		BankAccount:    ab.BankAccount,
		IsGroup:        &ab.IsGroup,   // 取地址赋给指针
		IsVirtual:      &ab.IsVirtual, // 取地址赋给指针
		Status:         ab.Status,
	}
}

// convertToAccountBookVOList 将账套实体列表转换为视图对象列表
func (s *accountBookServiceImpl) convertToAccountBookVOList(abs []*entity.AccountBook) []*vo.AccountBookVO {
	voList := make([]*vo.AccountBookVO, 0, len(abs))
	for _, ab := range abs {
		voList = append(voList, s.convertToAccountBookVO(ab))
	}
	return voList
}

// derefBoolPointer 安全地解引用 *bool 指针，如果指针为 nil 则返回默认值
func derefBoolPointer(ptr *bool, defaultValue bool) bool {
	if ptr != nil {
		return *ptr
	}
	return defaultValue
}

// ConvertToAccountBookVOList 转换账套实体列表为VO列表 (新增实现)
func (s *accountBookServiceImpl) ConvertToAccountBookVOList(ctx context.Context, accountBooks []*entity.AccountBook) ([]*vo.AccountBookVO, error) {
	s.GetLogger().Debug(ctx, "转换账套列表为VO列表", logger.WithField("count", len(accountBooks)))
	if len(accountBooks) == 0 {
		return []*vo.AccountBookVO{}, nil
	}

	voList := make([]*vo.AccountBookVO, 0, len(accountBooks))
	for _, book := range accountBooks {
		voList = append(voList, s.convertToAccountBookVO(book))
	}
	return voList, nil
}
