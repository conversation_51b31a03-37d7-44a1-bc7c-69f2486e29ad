package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsInboundNotificationRepository 定义入库通知单仓库接口
type WmsInboundNotificationRepository interface {
	BaseRepository[entity.WmsInboundNotification, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInboundNotificationQueryReq) (*response.PageResult, error)

	// 根据通知单号查找
	FindByNotificationNo(ctx context.Context, notificationNo string) (*entity.WmsInboundNotification, error)

	// 检查通知单号是否存在
	IsNotificationNoExist(ctx context.Context, notificationNo string, excludeID uint) (bool, error)

	// 批量创建
	BatchCreate(ctx context.Context, notifications []*entity.WmsInboundNotification) error

	// 更新状态
	UpdateStatus(ctx context.Context, id uint, status string, remark string) error

	// 根据日期范围查询
	GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInboundNotification, error)

	// 根据客户ID查询
	GetByClientID(ctx context.Context, clientID uint) ([]*entity.WmsInboundNotification, error)

	// 根据仓库ID查询
	GetByWarehouseID(ctx context.Context, warehouseID uint) ([]*entity.WmsInboundNotification, error)

	// 获取指定状态的通知单
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsInboundNotification, error)

	// 根据源单据号查询
	FindBySourceDocNo(ctx context.Context, sourceDocNo string) ([]*entity.WmsInboundNotification, error)
}

// wmsInboundNotificationRepository 入库通知单仓库实现
type wmsInboundNotificationRepository struct {
	BaseRepositoryImpl[entity.WmsInboundNotification, uint]
}

// NewWmsInboundNotificationRepository 创建入库通知单仓库
func NewWmsInboundNotificationRepository(db *gorm.DB) WmsInboundNotificationRepository {
	return &wmsInboundNotificationRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsInboundNotification, uint]{
			db: db,
		},
	}
}

// GetPage 获取入库通知单分页数据
func (r *wmsInboundNotificationRepository) GetPage(ctx context.Context, query *dto.WmsInboundNotificationQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.NotificationNo != nil && *query.NotificationNo != "" {
		conditions = append(conditions, NewLikeCondition("notification_no", *query.NotificationNo))
	}
	if query.NotificationType != nil && *query.NotificationType != "" {
		conditions = append(conditions, NewEqualCondition("notification_type", *query.NotificationType))
	}
	if query.WarehouseID != nil {
		conditions = append(conditions, NewEqualCondition("warehouse_id", *query.WarehouseID))
	}
	if query.ClientID != nil {
		conditions = append(conditions, NewEqualCondition("client_id", *query.ClientID))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if len(query.Statuses) > 0 {
		conditions = append(conditions, NewInCondition("status", query.Statuses))
	}
	if query.ExpectedArrivalDateFrom != nil && query.ExpectedArrivalDateTo != nil {
		conditions = append(conditions, NewBetweenCondition("expected_arrival_date", *query.ExpectedArrivalDateFrom, *query.ExpectedArrivalDateTo))
	}
	if query.CreatedAtFrom != nil && query.CreatedAtTo != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *query.CreatedAtFrom, *query.CreatedAtTo))
	}

	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// FindByNotificationNo 根据通知单号查找
func (r *wmsInboundNotificationRepository) FindByNotificationNo(ctx context.Context, notificationNo string) (*entity.WmsInboundNotification, error) {
	var notification entity.WmsInboundNotification
	db := r.GetDB(ctx)

	// 预加载关联的明细
	result := db.Preload("Details").Where("notification_no = ?", notificationNo).First(&notification)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "入库通知单不存在").WithCause(result.Error)
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据通知单号查询失败: notificationNo=%s", notificationNo)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询入库通知单失败").WithCause(result.Error)
	}

	return &notification, nil
}

// IsNotificationNoExist 检查通知单号是否存在
func (r *wmsInboundNotificationRepository) IsNotificationNoExist(ctx context.Context, notificationNo string, excludeID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("notification_no", notificationNo),
	}
	if excludeID > 0 {
		conditions = append(conditions, NewNotEqualCondition("id", excludeID))
	}
	return r.Exists(ctx, conditions)
}

// BatchCreate 批量创建入库通知单
func (r *wmsInboundNotificationRepository) BatchCreate(ctx context.Context, notifications []*entity.WmsInboundNotification) error {
	if len(notifications) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(notifications, 100) // 每批100条
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建入库通知单失败: count=%d", len(notifications))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建入库通知单失败").WithCause(result.Error)
	}

	return nil
}

// UpdateStatus 更新通知单状态
func (r *wmsInboundNotificationRepository) UpdateStatus(ctx context.Context, id uint, status string, remark string) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsInboundNotification{}).Where("id = ?", id).Updates(map[string]interface{}{"status": status, "remark": remark})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新通知单状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新通知单状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "通知单不存在")
	}

	return nil
}

// GetByDateRange 根据日期范围查询
func (r *wmsInboundNotificationRepository) GetByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsInboundNotification, error) {
	conditions := []QueryCondition{
		NewBetweenCondition("expected_arrival_date", startDate, endDate),
	}
	sortInfos := []response.SortInfo{
		{Field: "expected_arrival_date", Order: "asc"},
		{Field: "notification_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByClientID 根据客户ID查询
func (r *wmsInboundNotificationRepository) GetByClientID(ctx context.Context, clientID uint) ([]*entity.WmsInboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("client_id", clientID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByWarehouseID 根据仓库ID查询
func (r *wmsInboundNotificationRepository) GetByWarehouseID(ctx context.Context, warehouseID uint) ([]*entity.WmsInboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("warehouse_id", warehouseID),
	}
	sortInfos := []response.SortInfo{
		{Field: "expected_arrival_date", Order: "asc"},
		{Field: "notification_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetByStatus 获取指定状态的通知单
func (r *wmsInboundNotificationRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsInboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "expected_arrival_date", Order: "asc"},
		{Field: "notification_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindBySourceDocNo 根据源单据号查询
func (r *wmsInboundNotificationRepository) FindBySourceDocNo(ctx context.Context, sourceDocNo string) ([]*entity.WmsInboundNotification, error) {
	conditions := []QueryCondition{
		NewEqualCondition("source_doc_no", sourceDocNo),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}
