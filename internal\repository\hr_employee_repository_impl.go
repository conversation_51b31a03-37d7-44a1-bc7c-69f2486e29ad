package repository

import (
	"context"
	stdErrors "errors"
	"reflect"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util"

	"gorm.io/gorm"
)

// EmployeeRepository defines the interface for employee data operations.
// 这个接口定义在 impl 文件内部，因为用户不希望有单独的 hr_employee_repository.go 文件。
// 为了符合 RepositoryManager 的模式，NewEmployeeRepository 将返回此接口类型。
type EmployeeRepository interface {
	// Create creates a new employee record.
	Create(ctx context.Context, employee *entity.Employee) error
	// Update updates an existing employee record.
	Update(ctx context.Context, employee *entity.Employee) error
	// Delete soft deletes employee records by their IDs.
	Delete(ctx context.Context, ids []uint) error
	// FindByID retrieves an employee by their ID.
	FindByID(ctx context.Context, id uint) (*entity.Employee, error)
	// FindByCode retrieves an employee by their employee code.
	FindByCode(ctx context.Context, code string) (*entity.Employee, error)
	// FindPage retrieves a paginated list of employees based on query criteria.
	FindPage(ctx context.Context, query dto.EmployeePageQueryDTO) (*response.PageResult, error)
	// UpdateStatus updates the status of an employee.
	UpdateStatus(ctx context.Context, id uint, status int) error
	// IsCodeExist checks if an employee code already exists, optionally excluding a given ID.
	IsCodeExist(ctx context.Context, code string, excludeID uint) (bool, error)
	// FindSimpleList retrieves a list of employees with minimal fields.
	FindSimpleList(ctx context.Context, query dto.EmployeeSimpleListQueryDTO) ([]*entity.Employee, error)
	// GetDB 获取当前DB实例 (EmployeeRepositoryImpl 特有，用于 Delete 等方法内部确保使用正确的DB)
	// 注意：BaseRepository 接口没有 GetDB，所以这里需要 employeeRepositoryImpl 自己实现或暴露
	// 如果 employeeRepositoryImpl 需要调用其 BaseRepository 实现中的 GetDB，则 BaseRepository 接口需要有 GetDB 方法
	// 鉴于 BaseRepository 接口 (在 base_repository.go) 确实有 GetDB，这里可以不重复定义，而是通过嵌入的接口调用
}

// employeeRepositoryImpl implements EmployeeRepository
type employeeRepositoryImpl struct {
	// 嵌入 BaseRepository 接口，而不是具体实现，以符合标准实践
	BaseRepository[entity.Employee, uint]
}

// NewEmployeeRepository creates a new EmployeeRepository instance.
func NewEmployeeRepository(db *gorm.DB) EmployeeRepository {
	return &employeeRepositoryImpl{
		// 使用 NewBaseRepository 工厂函数初始化嵌入的接口
		BaseRepository: NewBaseRepository[entity.Employee, uint](db),
	}
}

// Create creates a new employee record.
// 由于 BaseRepository 接口已经有 Create 方法，这些可以直接通过嵌入的接口调用
// func (r *employeeRepositoryImpl) Create(ctx context.Context, employee *entity.Employee) error {
// 	return r.BaseRepository.Create(ctx, employee)
// }

// Update updates an existing employee record.
// func (r *employeeRepositoryImpl) Update(ctx context.Context, employee *entity.Employee) error {
// 	return r.BaseRepository.Update(ctx, employee)
// }

// FindByID retrieves an employee by their ID.
// func (r *employeeRepositoryImpl) FindByID(ctx context.Context, id uint) (*entity.Employee, error) {
// 	return r.BaseRepository.FindByID(ctx, id)
// }

// Delete soft deletes employee records by their IDs.
func (r *employeeRepositoryImpl) Delete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}
	// 通过嵌入的 BaseRepository 接口获取 DB 实例
	db := r.BaseRepository.GetDB(ctx)

	var emp entity.Employee
	if _, hasDeletedAt := reflect.TypeOf(emp).FieldByName("DeletedAt"); hasDeletedAt {
		result := db.Model(&entity.Employee{}).Where("id IN ?", ids).Delete(&entity.Employee{})
		if result.Error != nil {
			logger.WithContext(ctx).WithError(result.Error).Error("批量软删除员工失败")
			return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "批量删除员工数据失败").WithCause(result.Error)
		}
		// 可选：检查 result.RowsAffected
		return nil
	}

	result := db.Model(&entity.Employee{}).Unscoped().Where("id IN ?", ids).Delete(&entity.Employee{})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("批量物理删除员工失败")
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "批量删除员工数据失败").WithCause(result.Error)
	}
	return nil
}

// FindByCode retrieves an employee by their employee code.
func (r *employeeRepositoryImpl) FindByCode(ctx context.Context, code string) (*entity.Employee, error) {
	var employee entity.Employee
	// 通过嵌入的 BaseRepository 接口获取 DB 实例
	db := r.BaseRepository.GetDB(ctx)
	result := db.Model(&entity.Employee{}).Where("employee_code = ?", code).First(&employee)
	if result.Error != nil {
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "员工数据不存在")
		}
		logger.WithContext(ctx).WithError(result.Error).Error("根据员工代码查询失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询员工数据失败").WithCause(result.Error)
	}
	return &employee, nil
}

// FindPage retrieves a paginated list of employees.
func (r *employeeRepositoryImpl) FindPage(ctx context.Context, query dto.EmployeePageQueryDTO) (*response.PageResult, error) {
	conditions := make([]QueryCondition, 0)

	if query.EmployeeCode != "" {
		conditions = append(conditions, NewLikeCondition("employee_code", query.EmployeeCode))
	}
	if query.EmployeeName != "" {
		conditions = append(conditions, NewLikeCondition("employee_name", query.EmployeeName))
	}
	if query.EmployeeNamePinyin != "" {
		conditions = append(conditions, NewLikeCondition("employee_name_pinyin", query.EmployeeNamePinyin))
	}
	if query.EmployeeNameEn != "" {
		conditions = append(conditions, NewLikeCondition("employee_name_en", query.EmployeeNameEn))
	}
	if query.EmployeeIDCard != "" {
		conditions = append(conditions, NewEqualCondition("employee_id_card", query.EmployeeIDCard))
	}
	if query.EmployeeMobile != "" {
		conditions = append(conditions, NewEqualCondition("employee_mobile", query.EmployeeMobile))
	}
	if query.EmployeeEmail != "" {
		conditions = append(conditions, NewEqualCondition("employee_email", query.EmployeeEmail))
	}
	if query.EmployeeGender != nil {
		conditions = append(conditions, NewEqualCondition("employee_gender", *query.EmployeeGender))
	}
	if query.EmployeeStatus != nil {
		conditions = append(conditions, NewEqualCondition("employee_status", *query.EmployeeStatus))
	}
	if query.EmployeeDepartmentID != nil && *query.EmployeeDepartmentID > 0 {
		conditions = append(conditions, NewEqualCondition("employee_department_id", *query.EmployeeDepartmentID))
	}
	if query.EmployeePositionID != nil && *query.EmployeePositionID > 0 {
		conditions = append(conditions, NewEqualCondition("employee_position_id", *query.EmployeePositionID))
	}
	if query.EmployeeJobTitle != "" {
		conditions = append(conditions, NewLikeCondition("employee_job_title", query.EmployeeJobTitle))
	}
	if query.EmployeeWorkType != nil {
		conditions = append(conditions, NewEqualCondition("employee_work_type", *query.EmployeeWorkType))
	}
	if query.EmployeeJobGradeValue != nil && *query.EmployeeJobGradeValue != "" {
		conditions = append(conditions, NewEqualCondition("employee_job_grade_value", *query.EmployeeJobGradeValue))
	}
	if query.EmployeeJobSubLevelValue != nil && *query.EmployeeJobSubLevelValue != "" {
		conditions = append(conditions, NewEqualCondition("employee_job_sub_level_value", *query.EmployeeJobSubLevelValue))
	}
	if query.EmployeeWorkCategoryValue != nil && *query.EmployeeWorkCategoryValue != "" {
		conditions = append(conditions, NewEqualCondition("employee_work_category_value", *query.EmployeeWorkCategoryValue))
	}

	// 调用嵌入的 BaseRepository 接口的 FindByPage 方法
	return r.BaseRepository.FindByPage(ctx, &query.PageQuery, conditions)
}

// UpdateStatus updates the status of an employee.
func (r *employeeRepositoryImpl) UpdateStatus(ctx context.Context, id uint, status int) error {
	// 通过嵌入的 BaseRepository 接口获取 DB 实例
	db := r.BaseRepository.GetDB(ctx)
	log := logger.WithContext(ctx)

	uid64, err := util.GetUserIDFromStdContext(ctx)
	if err != nil {
		log.WithError(err).Warn("UpdateStatus: 无法从上下文中获取UserID，UpdatedBy将不会被设置")
	}

	updates := map[string]interface{}{
		"employee_status": status,
	}
	if uid64 > 0 {
		updates["updated_by"] = uint(uid64)
		log.Infof("UpdateStatus: Setting updated_by to %d for employee ID %d", uint(uid64), id)
	} else {
		log.Warnf("UpdateStatus: updated_by will not be set for employee ID %d as UserID could not be retrieved or was invalid (0).", id)
	}

	result := db.Model(&entity.Employee{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		log.WithError(result.Error).Error("更新员工状态失败")
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新员工状态失败").WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		var count int64
		if errCheck := db.Model(&entity.Employee{}).Where("id = ?", id).Count(&count).Error; errCheck != nil {
			log.WithError(errCheck).Error("UpdateStatus: 检查员工是否存在失败")
			return apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查员工是否存在失败").WithCause(errCheck)
		}
		if count == 0 {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "员工不存在")
		}
		log.Warnf("UpdateStatus: RowsAffected is 0 for employee ID %d. Record exists, so status was likely already the target value or no change was needed.", id)
		return nil
	}
	return nil
}

// IsCodeExist checks if an employee code already exists, optionally excluding a given ID.
func (r *employeeRepositoryImpl) IsCodeExist(ctx context.Context, code string, excludeID uint) (bool, error) {
	var count int64
	// 通过嵌入的 BaseRepository 接口获取 DB 实例
	db := r.BaseRepository.GetDB(ctx).Model(&entity.Employee{}).Where("employee_code = ?", code)
	if excludeID > 0 {
		db = db.Where("id <> ?", excludeID)
	}
	err := db.Count(&count).Error
	if err != nil {
		logger.WithContext(ctx).WithError(err).Error("检查员工代码是否存在失败")
		return false, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "检查员工代码存在性失败").WithCause(err)
	}
	return count > 0, nil
}

// FindSimpleList retrieves a list of employees with minimal fields.
func (r *employeeRepositoryImpl) FindSimpleList(ctx context.Context, query dto.EmployeeSimpleListQueryDTO) ([]*entity.Employee, error) {
	db := r.BaseRepository.GetDB(ctx).Model(&entity.Employee{})

	if query.Code != "" {
		db = db.Where("employee_code LIKE ?", "%"+query.Code+"%")
	}

	if query.Name != "" {
		db = db.Where("employee_name LIKE ?", "%"+query.Name+"%")
	}
	if query.Status != nil {
		db = db.Where("employee_status = ?", *query.Status)
	}
	// Example: if you add DepartmentID to EmployeeSimpleListQueryDTO
	// if query.DepartmentID != nil && *query.DepartmentID > 0 {
	// 	db = db.Where("employee_department_id = ?", *query.DepartmentID)
	// }

	var employees []*entity.Employee
	// 只选择需要的字段，并添加默认排序
	result := db.Select("id", "employee_code", "employee_name").Order("employee_name asc").Find(&employees)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("查询员工简单列表失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询员工简单列表失败").WithCause(result.Error)
	}
	return employees, nil
}
