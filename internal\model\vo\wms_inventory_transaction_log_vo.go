package vo

import (
	"backend/internal/model/entity"
	"time"
)

// WmsInventoryTransactionLogVO 库存事务日志视图对象
type WmsInventoryTransactionLogVO struct {
	ID              uint                   `json:"id"`
	TransactionTime time.Time              `json:"transactionTime"`
	TransactionType entity.TransactionType `json:"transactionType"`
	TransactionName string                 `json:"transactionName"`
	ItemID          uint                   `json:"itemId"`
	ItemSku         string                 `json:"itemSku"`
	ItemName        string                 `json:"itemName"`
	WarehouseID     uint                   `json:"warehouseId"`
	WarehouseName   string                 `json:"warehouseName"`
	LocationID      uint                   `json:"locationId"`
	LocationCode    string                 `json:"locationCode"`
	BatchNo         *string                `json:"batchNo"`
	QuantityBefore  float64                `json:"quantityBefore"`
	QuantityAfter   float64                `json:"quantityAfter"`
	QuantityChange  float64                `json:"quantityChange"`
	UnitOfMeasure   string                 `json:"unitOfMeasure"`
	ReferenceType   *string                `json:"referenceType"`
	ReferenceID     *uint                  `json:"referenceId"`
	ReferenceNo     *string                `json:"referenceNo"`
	OperatorID      *uint                  `json:"operatorId"`
	OperatorName    string                 `json:"operatorName"`
	Remark          *string                `json:"remark"`
	CreatedAt       time.Time              `json:"createdAt"`
}

// WmsInventoryTransactionLogDetailVO 库存事务日志详情视图对象
type WmsInventoryTransactionLogDetailVO struct {
	WmsInventoryTransactionLogVO

	// 关联信息
	Item      *MtlItemVO     `json:"item"`
	Warehouse *WmsLocationVO `json:"warehouse"`
	Location  *WmsLocationVO `json:"location"`
	Operator  *UserVO        `json:"operator"`
}

// WmsInventoryTransactionLogPageVO 库存事务日志分页视图对象
type WmsInventoryTransactionLogPageVO struct {
	List  []WmsInventoryTransactionLogVO `json:"list"`
	Total int64                          `json:"total"`
}

// WmsInventoryTransactionLogSummaryVO 库存事务日志汇总视图对象
type WmsInventoryTransactionLogSummaryVO struct {
	// 基础统计
	TotalTransactions int64   `json:"totalTransactions"`
	TotalInbound      float64 `json:"totalInbound"`
	TotalOutbound     float64 `json:"totalOutbound"`
	TotalAdjustment   float64 `json:"totalAdjustment"`
	TotalMovement     float64 `json:"totalMovement"`

	// 按类型统计
	TransactionsByType map[string]int64 `json:"transactionsByType"`

	// 按时间统计
	TransactionsByDate []WmsInventoryTransactionLogDailyVO `json:"transactionsByDate"`

	// 时间范围
	DateStart *time.Time `json:"dateStart"`
	DateEnd   *time.Time `json:"dateEnd"`
}

// WmsInventoryTransactionStatsVO 库存事务统计视图对象
type WmsInventoryTransactionStatsVO struct {
	// 操作员基础信息
	OperatorID   uint   `json:"operatorId"`
	OperatorName string `json:"operatorName"`

	// 基础统计
	TotalTransactions int64   `json:"totalTransactions"`
	TotalInbound      float64 `json:"totalInbound"`
	TotalOutbound     float64 `json:"totalOutbound"`
	TotalAdjustment   float64 `json:"totalAdjustment"`
	TotalMovement     float64 `json:"totalMovement"`

	// 按类型统计
	InboundCount    int64 `json:"inboundCount"`
	OutboundCount   int64 `json:"outboundCount"`
	AdjustmentCount int64 `json:"adjustmentCount"`
	MovementCount   int64 `json:"movementCount"`

	// 时间统计
	FirstTransactionAt *time.Time `json:"firstTransactionAt"`
	LastTransactionAt  *time.Time `json:"lastTransactionAt"`

	// 效率统计
	AvgTransactionsPerDay float64 `json:"avgTransactionsPerDay"`
	MostActiveDate        *string `json:"mostActiveDate"`
	MostActiveHour        *int    `json:"mostActiveHour"`

	// 时间范围
	DateStart *time.Time `json:"dateStart"`
	DateEnd   *time.Time `json:"dateEnd"`
}

// WmsInventoryTransactionLogDailyVO 库存事务日志每日统计视图对象
type WmsInventoryTransactionLogDailyVO struct {
	Date               string  `json:"date"`
	TransactionCount   int64   `json:"transactionCount"`
	InboundQuantity    float64 `json:"inboundQuantity"`
	OutboundQuantity   float64 `json:"outboundQuantity"`
	AdjustmentQuantity float64 `json:"adjustmentQuantity"`
	MovementQuantity   float64 `json:"movementQuantity"`
}

// NewWmsInventoryTransactionLogVO 创建库存事务日志视图对象
func NewWmsInventoryTransactionLogVO(log *entity.WmsInventoryTransactionLog) *WmsInventoryTransactionLogVO {
	vo := &WmsInventoryTransactionLogVO{
		ID:              log.ID,
		TransactionTime: log.TransactionTime,
		TransactionType: log.TransactionType,
		TransactionName: log.GetTransactionTypeName(),
		ItemID:          log.ItemID,
		WarehouseID:     log.WarehouseID,
		LocationID:      log.LocationID,
		BatchNo:         log.BatchNo,
		QuantityBefore:  log.BalanceBefore,
		QuantityAfter:   log.BalanceAfter,
		QuantityChange:  log.QuantityChange,
		UnitOfMeasure:   log.UnitOfMeasure,
		ReferenceType:   log.ReferenceDocType,
		ReferenceID:     log.ReferenceDocID,
		ReferenceNo:     nil, // 实体中没有ReferenceNo字段
		OperatorID:      log.OperatorID,
		Remark:          log.Remark,
		CreatedAt:       log.CreatedAt,
	}

	// 关联信息需要通过预加载或单独查询获取
	// 这里暂时不处理，由调用方负责填充

	return vo
}

// NewWmsInventoryTransactionLogDetailVO 创建库存事务日志详情视图对象
func NewWmsInventoryTransactionLogDetailVO(log *entity.WmsInventoryTransactionLog) *WmsInventoryTransactionLogDetailVO {
	vo := &WmsInventoryTransactionLogDetailVO{
		WmsInventoryTransactionLogVO: *NewWmsInventoryTransactionLogVO(log),
	}

	// 详细关联信息需要通过预加载或单独查询获取
	// 这里暂时不处理，由调用方负责填充

	return vo
}

// GetTransactionDirection 获取事务方向描述
func (vo *WmsInventoryTransactionLogVO) GetTransactionDirection() string {
	if vo.QuantityChange > 0 {
		return "入库"
	} else if vo.QuantityChange < 0 {
		return "出库"
	}
	return "无变化"
}

// GetQuantityChangeAbs 获取数量变化的绝对值
func (vo *WmsInventoryTransactionLogVO) GetQuantityChangeAbs() float64 {
	if vo.QuantityChange < 0 {
		return -vo.QuantityChange
	}
	return vo.QuantityChange
}
