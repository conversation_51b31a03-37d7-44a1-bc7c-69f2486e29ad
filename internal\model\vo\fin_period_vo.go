package vo

// FiscalPeriodVO 是用于前端展示的会计期间视图对象。
type FiscalPeriodVO struct {
	ID          uint64 `json:"id"`
	AccountBookID uint   `json:"accountBookId"`
	// Code 是会计期间的唯一代码，格式为 YYYYMM (例如 "202401")。
	Code        string `json:"code"`
	FiscalYear  int    `json:"fiscalYear"`
	FiscalMonth int    `json:"fiscalMonth"`
	StartDate   string `json:"startDate"` // 格式化为 YYYY-MM-DD
	EndDate     string `json:"endDate"`   // 格式化为 YYYY-MM-DD
	Status      string `json:"status"`
	CreatedAt   string `json:"createdAt"` // 格式化为 YYYY-MM-DD HH:mm:ss
}
