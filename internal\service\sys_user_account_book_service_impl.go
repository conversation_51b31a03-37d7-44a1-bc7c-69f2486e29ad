package service

import (
	"context"
	"errors" // Import standard errors for gorm.ErrRecordNotFound
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors" // Use alias for custom errors
	"backend/pkg/logger"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// UserAccountBookService 定义用户账套服务接口
type UserAccountBookService interface {
	BaseService // 继承基础服务接口

	// AssignAccountBookToUser 将账套分配给用户
	AssignAccountBookToUser(ctx context.Context, req *dto.AssignAccountBookToUserReq) error

	// RemoveAccountBookFromUser 从用户移除账套
	RemoveAccountBookFromUser(ctx context.Context, req *dto.RemoveAccountBookFromUserReq) error

	// GetAccountBooksByUserID 获取用户有权访问的账套列表
	GetAccountBooksByUserID(ctx context.Context, userID uint) ([]*vo.AccountBookVO, error)

	// GetUsersByAccountBookID 获取有权访问指定账套的用户列表 (可能需要分页)
	GetUsersByAccountBookID(ctx context.Context, accountBookID uint, page int, pageSize int) ([]*vo.UserSimpleVO, int64, error)

	// CheckUserAccountBookAccess 检查用户是否有权访问特定账套
	CheckUserAccountBookAccess(ctx context.Context, userID uint, accountBookID uint) (bool, error)

	// GetAccountBooksForUser 获取指定用户的账套简要信息列表
	GetAccountBooksForUser(ctx context.Context, userID uint) ([]*vo.AccountBookSimpleVO, error)

	// 新增：批量更新用户的账套关联
	UpdateUserAccountBooks(ctx context.Context, userID uint, accountBookIDs []uint) error
}

// userAccountBookServiceImpl 用户账套服务实现
type userAccountBookServiceImpl struct {
	*BaseServiceImpl                                         // Embed pointer type
	userAccountBookRepo repository.UserAccountBookRepository // 用户账套仓库
	userRepo            repository.UserRepository            // 用户仓库 (可能需要，用于获取用户信息)
	accountBookRepo     repository.AccountBookRepository     // 账套仓库 (可能需要，用于获取账套信息)
}

// NewUserAccountBookService 创建用户账套服务实例
func NewUserAccountBookService(serviceManager *ServiceManager) UserAccountBookService {
	baseService := NewBaseService(serviceManager) // 创建 BaseService
	return &userAccountBookServiceImpl{
		BaseServiceImpl:     baseService, // Now assigning *BaseServiceImpl to *BaseServiceImpl
		userAccountBookRepo: serviceManager.GetRepositoryManager().GetUserAccountBookRepository(),
		userRepo:            serviceManager.GetRepositoryManager().GetUserRepository(),
		accountBookRepo:     serviceManager.GetRepositoryManager().GetAccountBookRepository(),
	}
}

// AssignAccountBookToUser 实现将账套分配给用户
func (s *userAccountBookServiceImpl) AssignAccountBookToUser(ctx context.Context, req *dto.AssignAccountBookToUserReq) error {
	log := s.GetLogger()
	log.Info(ctx, "AssignAccountBookToUser called", logger.WithField("userId", req.UserID), logger.WithField("accountBookId", req.AccountBookID))

	// 1. 检查用户是否存在
	_, err := s.userRepo.FindByID(ctx, req.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) || apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			log.Warn(ctx, "AssignAccountBookToUser: User not found", logger.WithField("userId", req.UserID))
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		log.Error(ctx, "AssignAccountBookToUser: Failed to find user", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	// 2. 检查账套是否存在
	_, err = s.accountBookRepo.FindByID(ctx, req.AccountBookID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) || apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			log.Warn(ctx, "AssignAccountBookToUser: AccountBook not found", logger.WithField("accountBookId", req.AccountBookID))
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "账套不存在")
		}
		log.Error(ctx, "AssignAccountBookToUser: Failed to find account book", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询账套失败")
	}

	// 3. 检查是否已存在分配关系
	exists, err := s.userAccountBookRepo.Exists(ctx, req.UserID, req.AccountBookID)
	if err != nil {
		log.Error(ctx, "AssignAccountBookToUser: Failed to check existence", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "检查关联是否存在失败")
	}
	if exists {
		log.Warn(ctx, "AssignAccountBookToUser: Association already exists", logger.WithField("userId", req.UserID), logger.WithField("accountBookId", req.AccountBookID))
		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "用户已分配该账套")
	}

	// 4. 创建 UserAccountBook 记录
	userAccountBook := &entity.UserAccountBook{
		UserID:        req.UserID,
		AccountBookID: req.AccountBookID,
		// CreatedAt will be set automatically by GORM
	}
	err = s.userAccountBookRepo.Create(ctx, userAccountBook)
	if err != nil {
		log.Error(ctx, "AssignAccountBookToUser: Failed to create association", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_CREATE_FAILED, "创建用户账套关联失败")
	}

	log.Info(ctx, "AssignAccountBookToUser successful", logger.WithField("userId", req.UserID), logger.WithField("accountBookId", req.AccountBookID))
	return nil
}

// RemoveAccountBookFromUser 实现从用户移除账套
func (s *userAccountBookServiceImpl) RemoveAccountBookFromUser(ctx context.Context, req *dto.RemoveAccountBookFromUserReq) error {
	log := s.GetLogger()
	log.Info(ctx, "RemoveAccountBookFromUser called", logger.WithField("userId", req.UserID), logger.WithField("accountBookId", req.AccountBookID))

	// 1. 检查分配关系是否存在 (Optional but good practice)
	exists, err := s.userAccountBookRepo.Exists(ctx, req.UserID, req.AccountBookID)
	if err != nil {
		log.Error(ctx, "RemoveAccountBookFromUser: Failed to check existence", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "检查关联是否存在失败")
	}

	if !exists {
		log.Warn(ctx, "RemoveAccountBookFromUser: Association not found, nothing to remove", logger.WithField("userId", req.UserID), logger.WithField("accountBookId", req.AccountBookID))
		return nil // Or return apperrors.NewDataNotFoundError("用户未分配该账套") depending on requirement
	}

	// 2. 删除 UserAccountBook 记录
	err = s.userAccountBookRepo.DeleteByUserIDAndAccountBookID(ctx, req.UserID, req.AccountBookID)
	if err != nil {
		log.Error(ctx, "RemoveAccountBookFromUser: Failed to delete association", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "删除用户账套关联失败")
	}

	log.Info(ctx, "RemoveAccountBookFromUser successful", logger.WithField("userId", req.UserID), logger.WithField("accountBookId", req.AccountBookID))
	return nil
}

// GetAccountBooksByUserID 实现获取用户有权访问的账套列表
func (s *userAccountBookServiceImpl) GetAccountBooksByUserID(ctx context.Context, userID uint) ([]*vo.AccountBookVO, error) {
	s.GetLogger().Info(ctx, "GetAccountBooksByUserID called with UserID: %d", userID)

	var accountBooks []*entity.AccountBook
	var err error

	// --- Check if the user is an admin ---
	user, userErr := s.userRepo.FindByID(ctx, userID)
	if userErr != nil {
		s.GetLogger().Error(ctx, "查询用户信息失败", logger.WithField("userId", userID), logger.WithError(userErr))
		return nil, userErr
	}

	if user.IsAdmin {
		// --- Admin User: Get all active account books ---
		s.GetLogger().Info(ctx, "用户是管理员，获取所有有效账套", logger.WithField("userId", userID))
		conditions := []repository.Condition{
			{Field: "status", Operator: repository.OP_EQ, Value: 1}, // 使用常量
		}
		// 将 []repository.Condition 转换为 []repository.QueryCondition
		queryConditions := make([]repository.QueryCondition, len(conditions))
		for i, cond := range conditions {
			queryConditions[i] = cond
		}
		// 创建 SortInfo
		sortInfos := []response.SortInfo{{Field: "id", Order: "asc"}}
		// 使用正确的参数调用 FindByCondition
		accountBooks, err = s.accountBookRepo.FindByCondition(ctx, queryConditions, sortInfos)
		if err != nil {
			s.GetLogger().Error(ctx, "管理员获取所有账套失败", logger.WithError(err))
			return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "管理员获取所有账套失败")
		}
	} else {
		// --- Non-Admin User: Original logic based on association ---
		s.GetLogger().Debug(ctx, "非管理员用户，执行基于关联的账套查询", logger.WithField("userId", userID))
		userAccountBooks, findErr := s.userAccountBookRepo.FindByUserIDReturnModels(ctx, userID)
		if findErr != nil {
			s.GetLogger().Error(ctx, "查询用户账套关联失败", logger.WithError(findErr))
			return nil, apperrors.WrapError(findErr, apperrors.CODE_DATA_QUERY_FAILED, "查询用户账套关联失败")
		}

		if len(userAccountBooks) == 0 {
			return []*vo.AccountBookVO{}, nil
		}

		accountBookIDs := make([]uint, 0, len(userAccountBooks))
		for _, uab := range userAccountBooks {
			accountBookIDs = append(accountBookIDs, uab.AccountBookID)
		}

		accountBooks, err = s.accountBookRepo.FindByIds(ctx, accountBookIDs)
		if err != nil {
			s.GetLogger().Error(ctx, "根据ID查询账套失败", logger.WithField("ids", accountBookIDs), logger.WithError(err))
			return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "根据ID查询账套详情失败")
		}
	}

	// Convert to VO list (Common step) - 恢复原始逻辑
	accountBookSvc := s.GetServiceManager().GetAccountBookService()
	// 检查 GetAccountBookService 是否成功返回
	if accountBookSvc == nil {
		s.GetLogger().Error(ctx, "无法获取 AccountBookService 实例")
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "获取账套服务失败")
	}
	// 恢复错误处理，因为 ConvertToAccountBookVOList 返回两个值
	voList, convertErr := accountBookSvc.ConvertToAccountBookVOList(ctx, accountBooks)
	if convertErr != nil { // 恢复错误检查
		s.GetLogger().Error(ctx, "转换账套列表为VO失败", logger.WithError(convertErr))
		return nil, apperrors.WrapError(convertErr, apperrors.CODE_SYSTEM_INTERNAL, "转换账套列表为VO失败")
	}

	return voList, nil
}

// GetUsersByAccountBookID 实现获取有权访问指定账套的用户列表
func (s *userAccountBookServiceImpl) GetUsersByAccountBookID(ctx context.Context, accountBookID uint, page int, pageSize int) ([]*vo.UserSimpleVO, int64, error) {
	// TODO: 实现业务逻辑
	// 1. 从 UserAccountBook 表分页查询指定账套的记录，获取 UserID 列表和总数
	// 2. 根据 UserID 批量查询 User 信息
	// 3. 转换为 UserSimpleVO 列表
	s.GetLogger().Info(ctx, "GetUsersByAccountBookID called with AccountBookID: %d, Page: %d, PageSize: %d", accountBookID, page, pageSize)

	userIDs, total, err := s.userAccountBookRepo.FindUserIDsByAccountBookID(ctx, accountBookID, page, pageSize)
	if err != nil {
		return nil, 0, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "分页查询账套用户ID失败")
	}

	if len(userIDs) == 0 {
		return []*vo.UserSimpleVO{}, total, nil
	}

	users, err := s.userRepo.FindByIds(ctx, userIDs)
	if err != nil {
		return nil, 0, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "根据用户ID查询用户信息失败")
	}

	userSvc := s.GetServiceManager().GetUserService()
	userVOs, err := userSvc.ConvertToUserSimpleVOList(ctx, users)
	if err != nil {
		s.GetLogger().WithError(err).Error(ctx, "ConvertToUserSimpleVOList failed")
		return nil, 0, apperrors.WrapError(err, apperrors.CODE_SYSTEM_INTERNAL, "转换用户简要信息列表失败")
	}

	return userVOs, total, nil
}

// CheckUserAccountBookAccess 实现检查用户是否有权访问特定账套
func (s *userAccountBookServiceImpl) CheckUserAccountBookAccess(ctx context.Context, userID uint, accountBookID uint) (bool, error) {
	s.GetLogger().Info(ctx, "CheckUserAccountBookAccess called with UserID: %d, AccountBookID: %d", userID, accountBookID)

	// --- Check if the user is an admin ---
	user, userErr := s.userRepo.FindByID(ctx, userID)
	if userErr != nil {
		s.GetLogger().Error(ctx, "查询用户信息失败", logger.WithField("userId", userID), logger.WithError(userErr))
		// Return false and error if user cannot be found
		return false, userErr
	}

	if user.IsAdmin {
		// --- Admin User: Always has access ---
		s.GetLogger().Debug(ctx, "用户是管理员，直接授予访问权限", logger.WithField("userId", userID), logger.WithField("accountBookId", accountBookID))
		return true, nil
	}

	// --- Non-Admin User: Check the association table ---
	s.GetLogger().Debug(ctx, "非管理员用户，检查账套关联", logger.WithField("userId", userID), logger.WithField("accountBookId", accountBookID))
	access, err := s.userAccountBookRepo.Exists(ctx, userID, accountBookID)
	if err != nil {
		s.GetLogger().Error(ctx, "检查用户账套关联失败", logger.WithError(err))
		return false, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "检查用户账套关联失败")
	}
	return access, nil
}

// GetAccountBooksForUser 实现获取指定用户的账套简要信息列表
func (s *userAccountBookServiceImpl) GetAccountBooksForUser(ctx context.Context, userID uint) ([]*vo.AccountBookSimpleVO, error) {
	s.GetLogger().Info(ctx, "GetAccountBooksForUser called with UserID: %d", userID)

	// 1. 获取用户关联的账套 ID 列表 (使用 FindByUser 更直接)
	accountBookIDs, err := s.userAccountBookRepo.FindByUser(ctx, userID)
	if err != nil {
		s.GetLogger().Error(ctx, "查询用户账套关联ID失败", logger.WithError(err))
		// 根据 Repository 返回的错误类型决定是否包装
		// 如果是 gorm.ErrRecordNotFound，可以返回空列表和 nil 错误
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*vo.AccountBookSimpleVO{}, nil
		}
		// 其他错误则包装返回
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, fmt.Sprintf("查询用户 %d 的账套ID失败", userID))
	}

	if len(accountBookIDs) == 0 {
		s.GetLogger().Debug(ctx, "用户无关联账套", logger.WithField("userId", userID))
		return []*vo.AccountBookSimpleVO{}, nil
	}
	s.GetLogger().Debug(ctx, "获取到用户关联账套ID列表", logger.WithField("userId", userID), logger.WithField("ids", accountBookIDs))

	// 2. 根据 ID 列表获取账套实体列表
	accountBooks, err := s.accountBookRepo.FindByIds(ctx, accountBookIDs)
	if err != nil {
		s.GetLogger().Error(ctx, "根据ID查询账套实体失败", logger.WithField("ids", accountBookIDs), logger.WithError(err))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "根据ID查询账套详情失败")
	}
	s.GetLogger().Debug(ctx, "获取到账套实体列表", logger.WithField("count", len(accountBooks)))

	// 3. 将实体列表转换为简要视图对象列表
	simpleVOs := make([]*vo.AccountBookSimpleVO, 0, len(accountBooks))
	for _, ab := range accountBooks {
		if ab != nil { // 添加 nil 检查以防万一
			simpleVOs = append(simpleVOs, &vo.AccountBookSimpleVO{
				ID:   ab.ID,
				Name: ab.Name, // 假设 entity.AccountBook 有 Name 字段
			})
		}
	}
	s.GetLogger().Info(ctx, "成功获取并转换用户账套简要列表", logger.WithField("userId", userID), logger.WithField("count", len(simpleVOs)))
	return simpleVOs, nil
}

// UpdateUserAccountBooks 实现批量更新用户的账套关联
func (s *userAccountBookServiceImpl) UpdateUserAccountBooks(ctx context.Context, userID uint, accountBookIDs []uint) error {
	opName := "Service:UpdateUserAccountBooks"
	log := s.GetLogger()
	log.Debug(ctx, opName+" called",
		logger.WithField("userId", userID),
		logger.WithField("accountBookCount", len(accountBookIDs)))

	// 1. (可选但推荐) 检查用户是否存在
	_, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) || apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			log.Warn(ctx, opName+": User not found", logger.WithField("userId", userID))
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("用户 ID %d 不存在", userID))
		}
		log.Error(ctx, opName+": Failed to find user", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	// 2. 调用 Repository 执行数据库操作
	// 注意: Repository 的 SetUserAccountBooks 应该处理事务
	err = s.userAccountBookRepo.SetUserAccountBooks(ctx, userID, accountBookIDs)
	if err != nil {
		log.Error(ctx, opName+": Failed to set user account books in repository",
			logger.WithField("userId", userID),
			logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户账套关联失败")
	}

	log.Info(ctx, opName+" successful", logger.WithField("userId", userID))
	return nil
}

// 注意: 上面的实现依赖于 Repository 和其他 Service 中可能存在的方法 (如 ConvertTo...VOList)。
// 你需要确保这些依赖的方法已经实现或将要实现。
// DTO 和 VO 结构也需要在对应的包中定义。
