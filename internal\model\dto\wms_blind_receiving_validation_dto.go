package dto

import "time"

// ==================== 验证记录相关DTO ====================

// WmsBlindReceivingValidationCreateReq 创建验证记录请求
type WmsBlindReceivingValidationCreateReq struct {
	WarehouseID       uint64 `json:"warehouse_id" validate:"required"`       // 仓库ID
	CustomerID        uint64 `json:"customer_id" validate:"required"`        // 客户ID
	ConfigID          uint64 `json:"config_id,omitempty"`                    // 配置ID
	RequestedQuantity uint64 `json:"requested_quantity" validate:"required"` // 请求数量
	Strategy          string `json:"strategy,omitempty"`                     // 验证策略
	ValidationData    string `json:"validation_data,omitempty"`              // 验证数据
}

// WmsBlindReceivingValidationUpdateReq 更新验证记录请求
type WmsBlindReceivingValidationUpdateReq struct {
	ID               uint64 `json:"id" validate:"required"`      // 验证记录ID
	ValidationResult string `json:"validation_result,omitempty"` // 验证结果
	ActualQuantity   uint64 `json:"actual_quantity,omitempty"`   // 实际数量
	ProcessingNotes  string `json:"processing_notes,omitempty"`  // 处理备注
	ValidationData   string `json:"validation_data,omitempty"`   // 验证数据
}

// WmsBlindReceivingValidationHistoryReq 验证历史查询请求
type WmsBlindReceivingValidationHistoryReq struct {
	WarehouseID uint64 `json:"warehouse_id" validate:"required"` // 仓库ID
	CustomerID  uint64 `json:"customer_id,omitempty"`            // 客户ID（可选）
	Days        int    `json:"days,omitempty"`                   // 查询天数（默认30天）
}

// WmsBlindReceivingValidationProcessReq 处理验证记录请求
type WmsBlindReceivingValidationProcessReq struct {
	ID               uint64 `json:"id" validate:"required"`                                      // 验证记录ID
	ValidationResult string `json:"validation_result" validate:"required,oneof=SUCCESS FAILURE"` // 验证结果
	ActualQuantity   uint64 `json:"actual_quantity" validate:"required"`                         // 实际数量
	ProcessingNotes  string `json:"processing_notes,omitempty"`                                  // 处理备注
}

// WmsBlindReceivingValidationStatsReq 验证统计请求
type WmsBlindReceivingValidationStatsReq struct {
	WarehouseID uint64    `json:"warehouse_id" validate:"required"` // 仓库ID
	StartTime   time.Time `json:"start_time,omitempty"`             // 开始时间
	EndTime     time.Time `json:"end_time,omitempty"`               // 结束时间
}
