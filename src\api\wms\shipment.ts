import request from '@/utils/request'

// ==================== 类型定义 ====================

// 发运单状态枚举
export type WmsShipmentStatus = 
  | 'PREPARING'       // 准备中
  | 'PACKED'          // 已打包
  | 'READY_TO_SHIP'   // 待发运
  | 'SHIPPED'         // 已发运
  | 'IN_TRANSIT'      // 运输中
  | 'DELIVERED'       // 已送达
  | 'CANCELLED'       // 已取消

// 运输方式枚举
export type WmsShippingMethod = 
  | 'EXPRESS'         // 快递
  | 'LOGISTICS'       // 物流
  | 'SELF_PICKUP'     // 自提
  | 'DIRECT_DELIVERY' // 直送

// 发运单响应类型
export interface WmsShipmentResp {
  id: number
  shipmentNo: string
  outboundNotificationId?: number
  outboundNotificationNo?: string
  pickingTaskId?: number
  pickingTaskNo?: string
  carrierId?: number
  carrierName?: string
  trackingNo?: string
  shippingMethod?: WmsShippingMethod
  status: WmsShipmentStatus
  packageCount?: number
  totalWeight?: number
  totalVolume?: number
  shippingCost?: number
  currency?: string
  shipmentDate?: string
  estimatedDeliveryDate?: string
  actualDeliveryDate?: string
  consigneeName?: string
  consigneePhone?: string
  consigneeAddress?: string
  receiverName?: string
  receiverPhone?: string
  deliveryProof?: string
  remark?: string
  createdAt: string
  updatedAt: string

  // 扩展字段
  actualShipDate?: string
  estimatedShipDate?: string
  consigneeEmail?: string
  postalCode?: string
  driverName?: string
  driverPhone?: string
  vehicleNo?: string
  insuranceFee?: number
  packingCost?: number
  totalPackages?: number
}

// 发运单创建请求类型
export interface WmsShipmentCreateReq {
  pickingTaskId?: number
  carrierId?: number
  shippingMethod?: WmsShippingMethod
  packageCount?: number
  totalWeight?: number
  totalVolume?: number
  estimatedDeliveryDate?: string
  consigneeName?: string
  consigneePhone?: string
  consigneeAddress?: string
  specialInstructions?: string
  remark?: string
}

// 发运单更新请求类型
export interface WmsShipmentUpdateReq {
  carrierId?: number
  trackingNo?: string
  shippingMethod?: WmsShippingMethod
  packageCount?: number
  totalWeight?: number
  totalVolume?: number
  shippingCost?: number
  currency?: string
  shipmentDate?: string
  estimatedDeliveryDate?: string
  actualDeliveryDate?: string
  consigneeName?: string
  consigneePhone?: string
  consigneeAddress?: string
  receiverName?: string
  receiverPhone?: string
  deliveryProof?: string
  remark?: string
}

// 发运单查询请求类型
export interface WmsShipmentQueryReq {
  pageNum?: number
  pageSize?: number
  shipmentNo?: string
  pickingTaskId?: number
  pickingTaskNo?: string
  carrierId?: number
  trackingNo?: string
  shippingMethod?: WmsShippingMethod
  status?: WmsShipmentStatus
  shipmentDateStart?: string
  shipmentDateEnd?: string
  consigneeName?: string
  createdAtStart?: string
  createdAtEnd?: string
}

// 承运商响应类型
export interface WmsCarrierResp {
  id: number
  carrierCode: string
  carrierName: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  address?: string
  serviceRating?: number
  supportedMethods: WmsShippingMethod[]
  isActive: boolean
}

// 承运商查询请求类型
export interface WmsCarrierQueryReq {
  pageNum?: number
  pageSize?: number
  carrierCode?: string
  carrierName?: string
  isActive?: boolean
}

// 运费计算请求类型
export interface WmsShipmentCostCalculateReq {
  carrierId: number
  shippingMethod: WmsShippingMethod
  originAddress?: string
  destinationAddress: string
  packageCount: number
  totalWeight: number
  totalVolume: number
  declaredValue?: number
  requiresInsurance?: boolean
}

// 运费计算响应类型
export interface WmsShipmentCostResp {
  shipmentId?: number
  carrierId: number
  carrierName: string
  shippingMethod: WmsShippingMethod
  baseCost: number
  weightCost: number
  volumeCost: number
  distanceCost: number
  serviceCost: number
  insuranceCost?: number
  totalCost: number
  currency: string
  validUntil?: string
}

// 发运标签请求类型
export interface WmsShipmentLabelReq {
  shipmentId: number
  labelType: 'SHIPPING' | 'RETURN' | 'FRAGILE' | 'URGENT'
  copies?: number
  format?: 'PDF' | 'PNG' | 'ZPL'
}

// 发运标签响应类型
export interface WmsShipmentLabelResp {
  shipmentId: number
  shipmentNo: string
  labelType: string
  labelData: string
  labelFormat: string
  printCount: number
  generatedAt: string
}

// 跟踪信息响应类型
export interface WmsShipmentTrackingResp {
  shipmentId: number
  trackingNo: string
  status: WmsShipmentStatus
  currentLocation?: string
  estimatedDelivery?: string
  events: WmsShipmentTrackingEventResp[]
}

// 跟踪事件响应类型
export interface WmsShipmentTrackingEventResp {
  timestamp: string
  location: string
  status: string
  description: string
  operator?: string
}

// 签收确认请求类型
export interface WmsShipmentDeliveryConfirmReq {
  shipmentId: number
  receiverName: string
  receiverPhone?: string
  deliveryDate: string
  deliveryProof?: string
  remark?: string
}

// 分页响应类型
export interface WmsShipmentPageResp {
  list: WmsShipmentResp[]
  total: number
}

export interface WmsCarrierPageResp {
  list: WmsCarrierResp[]
  total: number
}

// ==================== API接口 ====================

// 发运单基础CRUD接口
export const getShipmentPage = (params: WmsShipmentQueryReq): Promise<WmsShipmentPageResp> => {
  return request<WmsShipmentPageResp>({
    url: '/wms/shipments',
    method: 'get',
    params
  }) as unknown as Promise<WmsShipmentPageResp>
}

export const getShipmentById = (id: number): Promise<WmsShipmentResp> => {
  return request<WmsShipmentResp>({
    url: `/wms/shipments/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsShipmentResp>
}

export const createShipment = (data: WmsShipmentCreateReq): Promise<WmsShipmentResp> => {
  return request<WmsShipmentResp>({
    url: '/wms/shipments',
    method: 'post',
    data
  }) as unknown as Promise<WmsShipmentResp>
}

export const updateShipment = (id: number, data: WmsShipmentUpdateReq): Promise<WmsShipmentResp> => {
  return request<WmsShipmentResp>({
    url: `/wms/shipments/${id}`,
    method: 'put',
    data
  }) as unknown as Promise<WmsShipmentResp>
}

export const deleteShipment = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${id}`,
    method: 'delete'
  }) as unknown as Promise<void>
}

// 发运状态管理接口
export const updateShipmentStatus = (id: number, data: { status: WmsShipmentStatus; remark?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${id}/status`,
    method: 'put',
    data
  }) as unknown as Promise<void>
}

// 发运流程接口
export const packShipment = (id: number, data: any): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${id}/pack`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const shipOut = (id: number, data: any): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${id}/ship`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const confirmDelivery = (id: number, data: any): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${id}/delivery`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const cancelShipment = (id: number, data: { reason?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${id}/cancel`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 运费计算接口
export const calculateShippingCost = (data: any): Promise<any> => {
  return request<any>({
    url: '/wms/shipments/calculate-cost',
    method: 'post',
    data
  }) as unknown as Promise<any>
}

// 打印标签接口
export const printShippingLabel = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${id}/print-label`,
    method: 'post'
  }) as unknown as Promise<void>
}

// 跟踪信息接口
export const getTrackingInfo = (id: number): Promise<any> => {
  return request<any>({
    url: `/wms/shipments/${id}/tracking`,
    method: 'get'
  }) as unknown as Promise<any>
}

// 批量操作接口
export const batchPackShipments = (ids: number[], data: any): Promise<void> => {
  return request<void>({
    url: '/wms/shipments/batch-pack',
    method: 'post',
    data: { ids, ...data }
  }) as unknown as Promise<void>
}

export const batchShipOut = (ids: number[], data: any): Promise<void> => {
  return request<void>({
    url: '/wms/shipments/batch-ship',
    method: 'post',
    data: { ids, ...data }
  }) as unknown as Promise<void>
}

export const batchCancelShipments = (ids: number[], data: { reason?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/shipments/batch-cancel',
    method: 'post',
    data: { ids, ...data }
  }) as unknown as Promise<void>
}

// 承运商管理接口
export const getCarrierPage = (params: WmsCarrierQueryReq): Promise<WmsCarrierPageResp> => {
  return request<WmsCarrierPageResp>({
    url: '/wms/carriers/page',
    method: 'get',
    params
  }) as unknown as Promise<WmsCarrierPageResp>
}

export const getCarrierList = (): Promise<WmsCarrierResp[]> => {
  return request<WmsCarrierResp[]>({
    url: '/wms/carriers/list',
    method: 'get'
  }) as unknown as Promise<WmsCarrierResp[]>
}

export const getShippingMethodList = (): Promise<any[]> => {
  return request<any[]>({
    url: '/wms/shipping-methods/list',
    method: 'get'
  }) as unknown as Promise<any[]>
}

// 运费计算接口
export const calculateShippingCost = (data: WmsShipmentCostCalculateReq): Promise<WmsShipmentCostResp> => {
  return request<WmsShipmentCostResp>({
    url: '/wms/shipments/calculate-cost',
    method: 'post',
    data
  }) as unknown as Promise<WmsShipmentCostResp>
}

export const batchCalculateShippingCost = (requests: WmsShipmentCostCalculateReq[]): Promise<WmsShipmentCostResp[]> => {
  return request<WmsShipmentCostResp[]>({
    url: '/wms/shipments/batch-calculate-cost',
    method: 'post',
    data: { requests }
  }) as unknown as Promise<WmsShipmentCostResp[]>
}

// 标签打印接口
export const generateShipmentLabel = (data: WmsShipmentLabelReq): Promise<WmsShipmentLabelResp> => {
  return request<WmsShipmentLabelResp>({
    url: '/wms/shipments/generate-label',
    method: 'post',
    data
  }) as unknown as Promise<WmsShipmentLabelResp>
}

export const printShipmentLabel = (shipmentId: number, labelType?: string): Promise<Blob> => {
  return request<Blob>({
    url: `/wms/shipments/${shipmentId}/print-label`,
    method: 'get',
    params: { labelType },
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

export const batchPrintShipmentLabels = (shipmentIds: number[], labelType?: string): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/shipments/batch-print-labels',
    method: 'post',
    data: { shipmentIds, labelType },
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

// 跟踪管理接口
export const getShipmentTracking = (shipmentId: number): Promise<WmsShipmentTrackingResp> => {
  return request<WmsShipmentTrackingResp>({
    url: `/wms/shipments/${shipmentId}/tracking`,
    method: 'get'
  }) as unknown as Promise<WmsShipmentTrackingResp>
}

export const updateShipmentTracking = (shipmentId: number, data: { location?: string; status?: string; description?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/shipments/${shipmentId}/tracking`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const syncShipmentTracking = (shipmentId: number): Promise<WmsShipmentTrackingResp> => {
  return request<WmsShipmentTrackingResp>({
    url: `/wms/shipments/${shipmentId}/sync-tracking`,
    method: 'post'
  }) as unknown as Promise<WmsShipmentTrackingResp>
}

// 签收确认接口
export const confirmDelivery = (data: WmsShipmentDeliveryConfirmReq): Promise<void> => {
  return request<void>({
    url: '/wms/shipments/confirm-delivery',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const uploadDeliveryProof = (shipmentId: number, file: File): Promise<{ fileUrl: string }> => {
  const formData = new FormData()
  formData.append('file', file)

  return request<{ fileUrl: string }>({
    url: `/wms/shipments/${shipmentId}/delivery-proof`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }) as unknown as Promise<{ fileUrl: string }>
}

// 批量操作接口
export const batchDeleteShipments = (ids: number[]): Promise<void> => {
  return request<void>({
    url: '/wms/shipments/batch-delete',
    method: 'delete',
    data: { ids }
  }) as unknown as Promise<void>
}

export const batchUpdateShipmentStatus = (data: { ids: number[]; status: WmsShipmentStatus; remark?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/shipments/batch-status',
    method: 'put',
    data
  }) as unknown as Promise<void>
}

export const batchShipShipments = (data: { ids: number[]; shipmentDate?: string; remark?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/shipments/batch-ship',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 导出接口
export const exportShipments = (params: WmsShipmentQueryReq): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/shipments/export',
    method: 'get',
    params,
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

// 统计接口
export const getShipmentStatistics = (params: { dateStart?: string; dateEnd?: string; carrierId?: number }): Promise<{
  totalShipments: number
  totalCost: number
  averageCost: number
  onTimeDeliveryRate: number
  statusDistribution: Record<WmsShipmentStatus, number>
  carrierPerformance: Array<{
    carrierId: number
    carrierName: string
    shipmentCount: number
    totalCost: number
    onTimeRate: number
  }>
}> => {
  return request<{
    totalShipments: number
    totalCost: number
    averageCost: number
    onTimeDeliveryRate: number
    statusDistribution: Record<WmsShipmentStatus, number>
    carrierPerformance: Array<{
      carrierId: number
      carrierName: string
      shipmentCount: number
      totalCost: number
      onTimeRate: number
    }>
  }>({
    url: '/wms/shipments/statistics',
    method: 'get',
    params
  }) as unknown as Promise<{
    totalShipments: number
    totalCost: number
    averageCost: number
    onTimeDeliveryRate: number
    statusDistribution: Record<WmsShipmentStatus, number>
    carrierPerformance: Array<{
      carrierId: number
      carrierName: string
      shipmentCount: number
      totalCost: number
      onTimeRate: number
    }>
  }>
}
