package audit

import (
	"time"
)

// 审计操作类型
const (
	// 基础操作类型
	OPERATION_CREATE = "CREATE" // 创建
	OPERATION_READ   = "READ"   // 读取
	OPERATION_UPDATE = "UPDATE" // 更新
	OPERATION_DELETE = "DELETE" // 删除

	// 扩展操作类型
	OPERATION_LOGIN    = "LOGIN"    // 登录
	OPERATION_LOGOUT   = "LOGOUT"   // 登出
	OPERATION_IMPORT   = "IMPORT"   // 导入
	OPERATION_EXPORT   = "EXPORT"   // 导出
	OPERATION_UPLOAD   = "UPLOAD"   // 上传
	OPERATION_DOWNLOAD = "DOWNLOAD" // 下载
	OPERATION_GRANT    = "GRANT"    // 授权
	OPERATION_REVOKE   = "REVOKE"   // 撤销
	OPERATION_ENABLE   = "ENABLE"   // 启用
	OPERATION_DISABLE  = "DISABLE"  // 禁用
	OPERATION_OTHER    = "OTHER"    // 其他
)

// 审计实体类型
const (
	ENTITY_USER       = "USER"       // 用户
	ENTITY_ROLE       = "ROLE"       // 角色
	ENTITY_PERMISSION = "PERMISSION" // 权限
	ENTITY_MENU       = "MENU"       // 菜单
	ENTITY_DEPT       = "DEPT"       // 部门
	ENTITY_DICT       = "DICT"       // 字典
	ENTITY_DICT_ITEM  = "DICT_ITEM"  // 字典项
	ENTITY_FILE       = "FILE"       // 文件
	ENTITY_CONFIG     = "CONFIG"     // 配置
	ENTITY_LOG        = "LOG"        // 日志
	ENTITY_LOGIN      = "LOGIN"      // 登录
	ENTITY_JOB        = "JOB"        // 任务
	ENTITY_NOTICE     = "NOTICE"     // 通知
	ENTITY_SYSTEM     = "SYSTEM"     // 系统
	ENTITY_API        = "API"        // API
	ENTITY_SETTINGS   = "SETTINGS"   // 设置
	ENTITY_OTHER      = "OTHER"      // 其他
)

// 审计结果类型
const (
	RESULT_SUCCESS = "SUCCESS" // 成功
	RESULT_FAILURE = "FAILURE" // 失败
	RESULT_WARNING = "WARNING" // 警告
	RESULT_ERROR   = "ERROR"   // 错误
	RESULT_INFO    = "INFO"    // 信息
)

// 审计级别
const (
	LEVEL_TRACE = "TRACE" // 跟踪
	LEVEL_DEBUG = "DEBUG" // 调试
	LEVEL_INFO  = "INFO"  // 信息
	LEVEL_WARN  = "WARN"  // 警告
	LEVEL_ERROR = "ERROR" // 错误
	LEVEL_FATAL = "FATAL" // 致命
)

// 审计跟踪相关常量
const (
	// 追踪字段名
	TRACE_ID       = "traceId"  // 追踪ID
	TRACE_USER_ID  = "userId"   // 用户ID
	TRACE_USERNAME = "username" // 用户名
	TRACE_IP       = "ip"       // IP地址
	TRACE_MODULE   = "module"   // 模块
	TRACE_FUNCTION = "function" // 功能
)

// 审计日志保留天数
const (
	AUDIT_LOG_RETENTION_DAYS = 365 // 审计日志保留天数
)

// 敏感字段列表
var SensitiveFields = []string{
	"password",
	"oldPassword",
	"newPassword",
	"confirmPassword",
	"salt",
	"secret",
	"token",
	"refreshToken",
	"accessToken",
	"creditCard",
	"idCard",
	"bankAccount",
	"privateKey",
	"secretKey",
}

// TraceInfo 追踪信息
type TraceInfo struct {
	TraceID    string    `json:"traceId"`    // 追踪ID
	UserID     uint      `json:"userId"`     // 用户ID
	Username   string    `json:"username"`   // 用户名
	IP         string    `json:"ip"`         // IP地址
	IPLocation string    `json:"ipLocation"` // IP归属地
	UserAgent  string    `json:"userAgent"`  // 用户代理
	Time       time.Time `json:"time"`       // 时间
	Module     string    `json:"module"`     // 模块
	Function   string    `json:"function"`   // 功能
}

// AuditEvent 审计事件
type AuditEvent struct {
	TraceInfo                          // 追踪信息
	Action      string                 `json:"action"`      // 操作
	EntityType  string                 `json:"entityType"`  // 实体类型
	EntityID    interface{}            `json:"entityId"`    // 实体ID
	Status      string                 `json:"status"`      // 状态
	Message     string                 `json:"message"`     // 消息
	Level       string                 `json:"level"`       // 级别
	OldValue    interface{}            `json:"oldValue"`    // 旧值
	NewValue    interface{}            `json:"newValue"`    // 新值
	Changes     []FieldChange          `json:"changes"`     // 变更字段
	Duration    int64                  `json:"duration"`    // 持续时间(毫秒)
	Detail      string                 `json:"detail"`      // 详细描述
	RelatedData map[string]interface{} `json:"relatedData"` // 相关数据
}

// FieldChange 字段变更
type FieldChange struct {
	Field       string      `json:"field"`       // 字段名称
	FieldDesc   string      `json:"fieldDesc"`   // 字段描述
	OldValue    interface{} `json:"oldValue"`    // 旧值
	NewValue    interface{} `json:"newValue"`    // 新值
	IsSensitive bool        `json:"isSensitive"` // 是否敏感字段
}

// AuditOptions 审计选项
type AuditOptions struct {
	IgnoreFields    []string               // 忽略的字段
	IgnoreZeroValue bool                   // 忽略零值
	ExtraData       map[string]interface{} // 额外数据
	Async           bool                   // 异步处理
}

// AuditHandler 审计处理器接口
type AuditHandler interface {
	Handle(event AuditEvent) error
}

// AuditLogger 审计日志接口
type AuditLogger interface {
	Log(event AuditEvent) error
}

// AuditStorage 审计存储接口
type AuditStorage interface {
	Save(event AuditEvent) error
	Query(query AuditQuery) ([]AuditEvent, int64, error)
	Delete(beforeTime time.Time) error
}

// AuditQuery 审计查询条件
type AuditQuery struct {
	UserID     uint      `json:"userId"`     // 用户ID
	Username   string    `json:"username"`   // 用户名
	Module     string    `json:"module"`     // 模块
	Action     string    `json:"action"`     // 操作
	EntityType string    `json:"entityType"` // 实体类型
	EntityID   uint      `json:"entityId"`   // 实体ID
	Status     string    `json:"status"`     // 状态
	StartTime  time.Time `json:"startTime"`  // 开始时间
	EndTime    time.Time `json:"endTime"`    // 结束时间
	IP         string    `json:"ip"`         // IP地址
	Level      string    `json:"level"`      // 级别
	PageNum    int       `json:"pageNum"`    // 页码
	PageSize   int       `json:"pageSize"`   // 页大小
	SortField  string    `json:"sortField"`  // 排序字段
	SortOrder  string    `json:"sortOrder"`  // 排序方向
}

// AuditResult 审计结果
type AuditResult struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 消息
	EventID string `json:"eventId"` // 事件ID
}

// AuditFilter 审计过滤器
type AuditFilter struct {
	IncludeModules  []string // 包含的模块
	ExcludeModules  []string // 排除的模块
	IncludeActions  []string // 包含的操作
	ExcludeActions  []string // 排除的操作
	IncludeEntities []string // 包含的实体
	ExcludeEntities []string // 排除的实体
	IncludeUsers    []uint   // 包含的用户
	ExcludeUsers    []uint   // 排除的用户
	MinLevel        string   // 最小级别
}

// AuditConfig 审计配置
type AuditConfig struct {
	Enabled        bool        // 是否启用审计
	AsyncEnabled   bool        // 是否启用异步审计
	LogEnabled     bool        // 是否启用日志记录
	StorageEnabled bool        // 是否启用存储
	DefaultLevel   string      // 默认级别
	RetentionDays  int         // 保留天数
	Filter         AuditFilter // 过滤器
}

