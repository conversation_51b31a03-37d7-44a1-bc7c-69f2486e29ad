package license

import (
	"time"
)

// LicenseInfo 定义了授权文件的核心信息
type LicenseInfo struct {
	// --- 基本信息 ---
	LicenseID string    `json:"license_id"` // 唯一许可证ID，便于追踪和管理
	Issuer    string    `json:"issuer"`     // 许可证颁发者名称 (例如: "YourCompanyName")
	IssueDate time.Time `json:"issue_date"` // 许可证颁发日期

	// --- 客户信息 ---
	CustomerID   string `json:"customer_id,omitempty"`   // 客户唯一标识符 (可选)
	CustomerName string `json:"customer_name"`           // 客户名称或公司名称
	ContactEmail string `json:"contact_email,omitempty"` // 客户联系邮箱 (可选, 便于支持)

	// --- 授权有效期 ---
	LicenseType string    `json:"license_type"`                // 许可证类型 (例如: "Trial", "Subscription", "Perpetual" - 试用、订阅、永久)
	StartDate   time.Time `json:"start_date,omitempty"`        // 授权开始日期 (适用于订阅或试用)
	ExpiryDate  time.Time `json:"expiry_date"`                 // 授权到期日期 (适用于试用或订阅; 对于永久许可，可以设为零值或一个非常遥远的未来)
	GracePeriod int       `json:"grace_period_days,omitempty"` // 到期后的宽限期天数 (可选, omitempty 表示如果为0则不在JSON中显示)

	// --- 功能和限制 ---
	ProductVersion string   `json:"product_version,omitempty"` // 适用的产品版本 (例如: "1.x", ">=2.0")
	Features       []string `json:"features,omitempty"`        // 启用的功能模块列表 (例如: "reporting", "api_access", "advanced_analytics")
	MaxUsers       int      `json:"max_users,omitempty"`       // 最大并发用户数或总用户数 (0或省略表示无限制)
	MaxInstances   int      `json:"max_instances,omitempty"`   // 允许的最大部署实例数 (0或省略表示无限制)
	// 可以添加其他资源限制，例如:
	// MaxStorageGB  int `json:"max_storage_gb,omitempty"` // 最大存储空间 (GB)
	// MaxDataVolume int `json:"max_data_volume_mb,omitempty"` // 最大数据处理量 (MB/day)

	// --- 硬件/环境绑定 (可选) ---
	// 这些字段增加了安全性，防止许可证被滥用，但也可能增加用户更换硬件或网络环境时的复杂性
	HardwareIDs []string `json:"hardware_ids,omitempty"` // 绑定的硬件标识符列表 (例如: MAC地址, CPU序列号, 主板UUID)
	IPAddresses []string `json:"ip_addresses,omitempty"` // 允许运行的IP地址或网段 (可选)
	Hostnames   []string `json:"hostnames,omitempty"`    // 允许运行的主机名 (可选)

	// --- 更新和支持 ---
	SupportExpiryDate time.Time `json:"support_expiry_date,omitempty"` // 技术支持服务到期日期 (可选)
	UpdateExpiryDate  time.Time `json:"update_expiry_date,omitempty"`  // 软件更新服务到期日期 (可选)

	// --- 其他元数据 ---
	Metadata map[string]string `json:"metadata,omitempty"` // 附加的自定义元数据 (键值对，灵活性高)
	Notes    string            `json:"notes,omitempty"`    // 备注信息，给人看的
}

// --- 辅助方法示例 ---

// IsExpired 检查许可证是否已过期 (考虑宽限期)
func (li *LicenseInfo) IsExpired() bool {
	// 对于非零且非遥远未来的 ExpiryDate 才进行判断
	if li.ExpiryDate.IsZero() || li.ExpiryDate.Year() > 9000 { // 简单判断是否为 "永久"
		return false
	}
	// 计算包含宽限期的最终到期时间点
	expiryWithGrace := li.ExpiryDate.AddDate(0, 0, li.GracePeriod)
	return time.Now().After(expiryWithGrace)
}

// HasFeature 检查许可证是否包含特定功能
func (li *LicenseInfo) HasFeature(feature string) bool {
	if li.Features == nil {
		return false // 如果 features 列表为 nil，则不包含任何特定功能
	}
	for _, f := range li.Features {
		if f == feature {
			return true
		}
	}
	return false
}

// CheckUserLimit 检查当前用户数是否超过限制
func (li *LicenseInfo) CheckUserLimit(currentUserCount int) bool {
	if li.MaxUsers <= 0 { // 0 或负数表示无限制
		return true
	}
	return currentUserCount <= li.MaxUsers
}

// CheckInstanceLimit 检查当前实例数是否超过限制
func (li *LicenseInfo) CheckInstanceLimit(currentInstanceCount int) bool {
	if li.MaxInstances <= 0 { // 0 或负数表示无限制
		return true
	}
	return currentInstanceCount <= li.MaxInstances
}

// IsSupportActive 检查技术支持是否仍然有效
func (li *LicenseInfo) IsSupportActive() bool {
	// 如果 SupportExpiryDate 是零值或非常遥远，可能表示未购买支持或永久支持
	// 具体业务逻辑需要根据您的策略定义
	if li.SupportExpiryDate.IsZero() || li.SupportExpiryDate.Year() > 9000 {
		// 假设零值表示无效，需要明确业务规则
		return false
	}
	return time.Now().Before(li.SupportExpiryDate)
}

// CanUpdate 检查是否允许获取软件更新
func (li *LicenseInfo) CanUpdate() bool {
	// 逻辑同上
	if li.UpdateExpiryDate.IsZero() || li.UpdateExpiryDate.Year() > 9000 {
		return false
	}
	return time.Now().Before(li.UpdateExpiryDate)
}

/*
// ValidateHardware 检查当前硬件ID是否在允许列表中 (需要平台相关的实现来获取当前硬件ID)
func (li *LicenseInfo) ValidateHardware(getCurrentHardwareIDs func() ([]string, error)) (bool, error) {
	if len(li.HardwareIDs) == 0 {
		return true, nil // 没有绑定硬件
	}
	currentIDs, err := getCurrentHardwareIDs()
	if err != nil {
		// 返回错误，例如无法获取硬件信息
		return false, fmt.Errorf("无法获取当前硬件ID: %w", err)
	}
	allowedIDSet := make(map[string]struct{})
	for _, id := range li.HardwareIDs {
		allowedIDSet[id] = struct{}{}
	}
	for _, currentID := range currentIDs {
		if _, ok := allowedIDSet[currentID]; ok {
			return true, nil // 找到匹配的硬件ID
		}
	}
	return false, nil // 当前硬件ID不在允许列表中
}
*/
