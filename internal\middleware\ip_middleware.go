package middleware

import (
	"context"

	"github.com/kataras/iris/v12"

	"backend/pkg/constant"
	"backend/pkg/util"
)

// IPMiddleware 结构体，可以持有依赖（如果需要）
type IPMiddleware struct {
	// 可选：添加日志记录器或其他依赖
}

// NewIPMiddleware 创建 IP 中间件实例
func NewIPMiddleware() *IPMiddleware {
	return &IPMiddleware{}
}

// Serve 是 Iris 中间件的处理函数
func (m *IPMiddleware) Serve(ctx iris.Context) {
	// 使用 pkg/util 中的函数获取客户端 IP
	clientIP := util.GetClientIP(ctx.Request())

	// 将 IP 存入标准的 Go Context 中
	reqCtx := ctx.Request().Context()
	// 使用 pkg/constant/constant.go 中定义的字符串常量键
	reqCtxWithIP := context.WithValue(reqCtx, constant.CONTEXT_CLIENT_IP, clientIP)

	// 更新请求中的上下文
	ctx.ResetRequest(ctx.Request().WithContext(reqCtxWithIP))

	// 继续下一个中间件或处理器
	ctx.Next()
}
