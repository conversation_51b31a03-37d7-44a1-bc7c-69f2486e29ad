# WMS 收货记录行状态枚举拆分与字段完善执行计划

## 1. 背景与目标

当前 `WmsReceivingRecordDetail.LineStatus` 直接复用主表 `wms_receiving_record_status` 枚举，无法精确表达行级流转阶段。同时明细表缺少 `inspected_qty`、`putaway_qty` 等数量字段。需要：

1. 将行状态枚举与主表拆分，定义更细粒度流程；
2. 补充缺失数量字段；
3. 确保常量、数据库 ENUM、实体定义及自动迁移保持一致。

## 2. 差异分析

| 级别                 | 现有枚举                                                                                                     | 目标枚举                                                                                                                                |
| -------------------- | ------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------- |
| 主表 `status`        | PENDING / RECEIVING / PENDING_INSPECTION / INSPECTING / COMPLETED / PARTIALLY_COMPLETED / CLOSED / CANCELLED | 不变                                                                                                                                    |
| 明细行 `line_status` | 同主表                                                                                                       | **PENDING / RECEIVING / RECEIVED / PENDING_INSPECTION / INSPECTING / INSPECTED / PENDING_PUTAWAY / PUTAWAYING / PUTAWAYED / CANCELLED** |

字段差异：

- 已有 `received_quantity` 对应设计中的 `received_qty`。
- 缺少 `inspected_quantity`、`putaway_quantity` 字段。

## 3. 设计方案

1. **新增 Go 枚举** `WmsReceivingRecordDetailStatus` 及常量列表（见 §2）。
   - 位置：`internal/model/entity/wms_receiving_record_detail_entity.go`。
2. **新增数据库 ENUM** `wms_receiving_record_detail_status`（PostgreSQL）并在 `pkg/database/init.go` → `runMigrations` 的手工 SQL 中创建。
3. **调整实体字段**
   - `LineStatus string` → `LineStatus string 0;column:line_status;type:wms_receiving_record_detail_status;not null;default:'PENDING';index;comment:行状态0;`
   - 新增：
     ```go
     InspectedQuantity float64  `gorm:"column:inspected_quantity;type:numeric(12,4);not null;default:0;comment:检验合格数量" json:"inspectedQuantity"`
     PutawayQuantity   float64  `gorm:"column:putaway_quantity;type:numeric(12,4);not null;default:0;comment:上架数量" json:"putawayQuantity"`
     ```
4. **统一常量**
   - 若后续模块需跨层调用，可在 `pkg/constant/wms_receiving_status.go` 新建文件导出同名常量。
5. **业务逻辑调整（概要）**
   - Service 层：
     - 在收货、质检、上架节点分别更新对应数量与 `LineStatus`；
     - 当所有明细 `LineStatus` 达到终态时，驱动主表 `Status` 汇总。
   - Repository 层：
     - `UpdateLineStatusAndQty(ctx, detailID, status, qtyFields...)` 封装行级更新。
6. **迁移与数据兼容**
   - 利用 GORM `AutoMigrate` 自动添加新列；
   - 对历史数据执行：`UPDATE wms_receiving_record_detail SET line_status = 'RECEIVED' WHERE line_status IN ('COMPLETED','PARTIALLY_COMPLETED');` 具体脚本待评估。

## 4. 影响范围 & 预估改动文件

- `internal/model/entity/wms_receiving_record_detail_entity.go`
- `pkg/constant/wms_receiving_status.go` _(新建)_
- `pkg/database/init.go`
- `internal/service/wms_receiving_record_service_impl.go`
- `internal/repository/wms_receiving_record_repository_impl.go`
- 单元测试 & Mock

## 5. 任务拆分 & 时间预估

| 序号     | 任务                          | 负责人 | 预计人天     |
| -------- | ----------------------------- | ------ | ------------ |
| 1        | 定义枚举 & 字段、更新 init.go | BE     | 0.5          |
| 2        | Repository 方法调整           | BE     | 0.5          |
| 3        | Service 业务逻辑 & 单测       | BE     | 1.0          |
| 4        | 常量包同步 & refactor         | BE     | 0.2          |
| 5        | SQL 数据迁移脚本编写          | DBA    | 0.2          |
| 6        | 前端枚举同步及状态流配置      | FE     | 0.5          |
| **合计** |                               |        | **2.9 人天** |

## 6. 风险与回滚

1. 数据库枚举变更需谨慎：生产环境执行前要做好备份。
2. 业务逻辑调整可能影响收货流程，需全链路回归测试。
3. 若需回滚，保留旧 ENUM 与字段，停用新逻辑并恢复旧代码分支即可。

## 7. 待讨论/开放问题

- 新增字段的默认值及历史数据回填策略是否需要脚本化处理？
- 主表状态汇总触发策略（定时 vs 事务内实时）。
- 行状态与检验状态的耦合度及界面展示方式。

---

_编写人：AI Code Assistant_ \*时间：{{DATE}}
