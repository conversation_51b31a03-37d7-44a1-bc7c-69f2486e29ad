package middleware

import (
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/kataras/iris/v12"

	"backend/internal/service"
	pk_constant "backend/pkg/constant"
	"backend/pkg/logger"
	CustomResponse "backend/pkg/response"
	"backend/pkg/util"
)

// 限流器类型常量
const (
	LIMITER_TYPE_MEMORY        = "memory"        // 内存限流器
	LIMITER_TYPE_REDIS         = "redis"         // Redis限流器 (当前未完全实现，但可由CacheService间接支持)
	LIMITER_TYPE_CACHE_SERVICE = "cache_service" // 基于 CacheService 的限流器
)

// 限流算法常量 (主要用于 memory/redis 类型)
const (
	ALGORITHM_TOKEN_BUCKET   = "token_bucket"   // 令牌桶算法
	ALGORITHM_LEAKY_BUCKET   = "leaky_bucket"   // 漏桶算法
	ALGORITHM_SLIDING_WINDOW = "sliding_window" // 滑动窗口算法
	ALGORITHM_FIXED_WINDOW   = "fixed_window"   // 固定窗口算法
)

// RateLimitConfig 限流中间件配置
type RateLimitConfig struct {
	Enabled bool `json:"enabled" yaml:"enabled"`
	// --- 通用配置 ---
	LimiterType   string   `json:"limiterType" yaml:"limiterType"`     // 限流器类型 (memory, redis, cache_service)
	ExcludedPaths []string `json:"excludedPaths" yaml:"excludedPaths"` // 排除的路径

	// --- CacheService 类型配置 ---
	GlobalLimit    int64  `json:"globalLimit" yaml:"globalLimit"`       // 全局限制次数 (cache_service)
	IPLimit        int64  `json:"ipLimit" yaml:"ipLimit"`               // 每个IP限制次数 (cache_service)
	UserLimit      int64  `json:"userLimit" yaml:"userLimit"`           // 每个用户限制次数 (cache_service)
	WindowDuration string `json:"windowDuration" yaml:"windowDuration"` // 时间窗口 (例如 "1m", "60s") (cache_service)

	// --- Memory/Redis 类型配置 ---
	Algorithm      string  `json:"algorithm" yaml:"algorithm"`           // 限流算法 (memory/redis)
	GlobalRate     float64 `json:"globalRate" yaml:"globalRate"`         // 全局速率 (每秒请求数) (memory/redis)
	IPRate         float64 `json:"ipRate" yaml:"ipRate"`                 // 每个IP的速率 (每秒请求数) (memory/redis)
	UserRate       float64 `json:"userRate" yaml:"userRate"`             // 每个用户的速率 (每秒请求数) (memory/redis)
	BucketCapacity int     `json:"bucketCapacity" yaml:"bucketCapacity"` // 令牌桶容量 (memory/redis)
	WindowSize     int     `json:"windowSize" yaml:"windowSize"`         // 窗口大小（秒）(memory/redis sliding/fixed window)

	// --- 自定义规则 ---
	CustomRules []RateLimitRule `json:"customRules" yaml:"customRules"`

	// --- Redis 特定配置 (如果直接使用 Redis 限流器) ---
	RedisConfig RedisConfig `json:"redisConfig" yaml:"redisConfig"`
}

// RedisConfig Redis配置 (如果直接使用 Redis 限流器)
type RedisConfig struct {
	Addr     string `json:"addr" yaml:"addr"`
	Password string `json:"password" yaml:"password"`
	DB       int    `json:"db" yaml:"db"`
}

// RateLimitRule 自定义限流规则
type RateLimitRule struct {
	PathPattern string   `json:"pathPattern" yaml:"pathPattern"` // 路径模式
	Methods     []string `json:"methods" yaml:"methods"`         // 方法
	// --- CacheService 类型配置 ---
	Limit          int64  `json:"limit" yaml:"limit"`                   // 限制次数 (cache_service)
	WindowDuration string `json:"windowDuration" yaml:"windowDuration"` // 时间窗口 (cache_service)
	// --- Memory/Redis 类型配置 ---
	Rate     float64 `json:"rate" yaml:"rate"`         // 速率 (每秒请求数) (memory/redis)
	Capacity int     `json:"capacity" yaml:"capacity"` // 容量 (memory/redis)
}

// 默认限流配置
var defaultRateLimitConfig = RateLimitConfig{
	Enabled:        true,
	LimiterType:    LIMITER_TYPE_MEMORY, // 默认使用内存
	Algorithm:      ALGORITHM_TOKEN_BUCKET,
	GlobalRate:     100,
	IPRate:         10,
	UserRate:       5,
	BucketCapacity: 100,
	WindowSize:     60,
	// CacheService 默认值
	GlobalLimit:    1000, // 默认每分钟 1000 次
	IPLimit:        100,  // 默认每分钟 100 次
	UserLimit:      60,   // 默认每分钟 60 次
	WindowDuration: "1m", // 默认窗口 1 分钟
	ExcludedPaths:  []string{"/health", "/metrics", "/favicon.ico"},
	CustomRules:    []RateLimitRule{},
	RedisConfig: RedisConfig{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	},
}

// RateLimiter 限流器接口 (用于 memory/redis 类型)
type RateLimiter interface {
	Allow(key string) bool
	AllowN(key string, n int) bool
	Reset(key string)
	Close() error
}

// MemoryLimiter 内存限流器 (保持不变)
type MemoryLimiter struct {
	mu           sync.RWMutex
	tokens       map[string]float64
	lastTime     map[string]time.Time
	rate         float64
	capacity     int
	algorithm    string
	windowCounts map[string]map[int64]int
	windowSize   int
}

// NewMemoryLimiter 创建内存限流器 (保持不变)
func NewMemoryLimiter(rate float64, capacity int, algorithm string, windowSize int) *MemoryLimiter {
	return &MemoryLimiter{
		tokens:       make(map[string]float64),
		lastTime:     make(map[string]time.Time),
		rate:         rate,
		capacity:     capacity,
		algorithm:    algorithm,
		windowCounts: make(map[string]map[int64]int),
		windowSize:   windowSize,
	}
}

// Allow (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) Allow(key string) bool {
	return l.AllowN(key, 1)
}

// AllowN (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) AllowN(key string, n int) bool {
	now := time.Now()

	switch l.algorithm {
	case ALGORITHM_TOKEN_BUCKET:
		return l.tokenBucketAllow(key, n, now)
	case ALGORITHM_LEAKY_BUCKET:
		return l.leakyBucketAllow(key, n, now)
	case ALGORITHM_SLIDING_WINDOW:
		return l.slidingWindowAllow(key, n, now)
	case ALGORITHM_FIXED_WINDOW:
		return l.fixedWindowAllow(key, n, now)
	default:
		// 默认使用令牌桶算法
		return l.tokenBucketAllow(key, n, now)
	}
}

// tokenBucketAllow (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) tokenBucketAllow(key string, n int, now time.Time) bool {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 获取令牌桶的当前令牌数和上次更新时间
	tokens, ok := l.tokens[key]
	if !ok {
		// 如果是新的key，初始化为满桶
		tokens = float64(l.capacity)
	}

	lastTime, ok := l.lastTime[key]
	if !ok {
		lastTime = now
	}

	// 计算从上次请求到现在生成的新令牌
	elapsed := now.Sub(lastTime).Seconds()
	newTokens := elapsed * l.rate

	// 更新令牌数，但不超过容量
	tokens = min(float64(l.capacity), tokens+newTokens)

	// 检查是否有足够的令牌
	if tokens < float64(n) {
		return false
	}

	// 消耗令牌
	tokens -= float64(n)

	// 更新状态
	l.tokens[key] = tokens
	l.lastTime[key] = now

	return true
}

// leakyBucketAllow (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) leakyBucketAllow(key string, n int, now time.Time) bool {
	// 这里只是简化版实现，实际上漏桶算法需要更复杂的实现
	return l.tokenBucketAllow(key, n, now)
}

// slidingWindowAllow (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) slidingWindowAllow(key string, n int, now time.Time) bool {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 获取当前时间窗口
	currentWindow := now.Unix() / int64(l.windowSize) * int64(l.windowSize)

	// 初始化窗口计数
	if _, ok := l.windowCounts[key]; !ok {
		l.windowCounts[key] = make(map[int64]int)
	}

	// 清理过期的窗口
	for window := range l.windowCounts[key] {
		if window < currentWindow-int64(l.windowSize) {
			delete(l.windowCounts[key], window)
		}
	}

	// 计算当前窗口中的请求数
	currentCount := 0
	for window, count := range l.windowCounts[key] {
		if window >= currentWindow-int64(l.windowSize) {
			currentCount += count
		}
	}

	// 检查是否超过速率限制
	if float64(currentCount+n) > l.rate*float64(l.windowSize) {
		return false
	}

	// 更新计数
	l.windowCounts[key][currentWindow] += n

	return true
}

// fixedWindowAllow (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) fixedWindowAllow(key string, n int, now time.Time) bool {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 获取当前时间窗口
	currentWindow := now.Unix() / int64(l.windowSize) * int64(l.windowSize)

	// 初始化窗口计数
	if _, ok := l.windowCounts[key]; !ok {
		l.windowCounts[key] = make(map[int64]int)
	}

	// 清理过期的窗口
	for window := range l.windowCounts[key] {
		if window < currentWindow {
			delete(l.windowCounts[key], window)
		}
	}

	// 获取当前窗口的计数
	count, ok := l.windowCounts[key][currentWindow]
	if !ok {
		count = 0
	}

	// 检查是否超过速率限制
	if float64(count+n) > l.rate*float64(l.windowSize) {
		return false
	}

	// 更新计数
	l.windowCounts[key][currentWindow] = count + n

	return true
}

// Reset (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) Reset(key string) {
	l.mu.Lock()
	defer l.mu.Unlock()

	delete(l.tokens, key)
	delete(l.lastTime, key)
	delete(l.windowCounts, key)
}

// Close (MemoryLimiter) (保持不变)
func (l *MemoryLimiter) Close() error {
	return nil
}

// min (保持不变)
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// RateLimitMiddleware 限流中间件
// 参数:
//   - cacheService: 必须传入 CacheService 实例
//   - config: 可选的限流配置，不提供则使用默认配置
//
// 返回:
//   - iris.Handler: Iris中间件处理函数
func RateLimitMiddleware(cacheService service.CacheService, config ...RateLimitConfig) iris.Handler {
	// 使用默认配置
	cfg := defaultRateLimitConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	// --- 初始化 Memory/Redis 类型的限流器 (仅当类型不是 cache_service 时) ---
	var globalLimiter RateLimiter
	var ipLimiter RateLimiter
	var userLimiter RateLimiter
	customLimiters := make(map[string]RateLimiter)

	if cfg.LimiterType != LIMITER_TYPE_CACHE_SERVICE {
		if cfg.GlobalRate > 0 {
			globalLimiter = createLimiter(cfg, cfg.GlobalRate)
		}
		if cfg.IPRate > 0 {
			ipLimiter = createLimiter(cfg, cfg.IPRate)
		}
		if cfg.UserRate > 0 {
			userLimiter = createLimiter(cfg, cfg.UserRate)
		}
		for _, rule := range cfg.CustomRules {
			// Memory/Redis 类型的自定义规则使用 Rate
			if rule.Rate > 0 {
				customLimiters[rule.PathPattern] = createLimiterForRule(cfg, rule)
			}
		}
	}
	// --- 结束 Memory/Redis 限流器初始化 ---

	return func(ctx iris.Context) {
		log := logger.WithContext(ctx) // 获取带上下文的 logger

		// 如果未启用，则跳过
		if !cfg.Enabled {
			ctx.Next()
			return
		}

		// 检查是否为排除的路径
		path := ctx.Path()
		for _, excludedPath := range cfg.ExcludedPaths {
			if strings.HasPrefix(path, excludedPath) {
				ctx.Next()
				return
			}
		}

		// 获取客户端IP和用户ID
		clientIP := ctx.RemoteAddr()
		userID := ctx.Values().GetString("userID") // 假设 userID 通过其他中间件设置

		// --- 根据 LimiterType 选择限流逻辑 ---
		if cfg.LimiterType == LIMITER_TYPE_CACHE_SERVICE {
			// === 使用 CacheService 进行限流 ===
			window, err := time.ParseDuration(cfg.WindowDuration)
			if err != nil {
				log.Warn("RateLimiter: 解析 WindowDuration 配置失败，使用默认值 1 分钟", logger.WithError(err), logger.WithField("configValue", cfg.WindowDuration))
				window = time.Minute // 解析失败时使用默认值
			}

			// 检查自定义规则 (CacheService 类型)
			for _, rule := range cfg.CustomRules {
				// CacheService 类型的自定义规则使用 Limit
				if rule.Limit > 0 && customRuleMatching(path, rule.PathPattern, rule.Methods, ctx.Method()) {
					ruleWindow := window // 默认使用全局窗口
					if rule.WindowDuration != "" {
						parsedRuleWindow, err := time.ParseDuration(rule.WindowDuration)
						if err == nil {
							ruleWindow = parsedRuleWindow
						} else {
							log.Warn("RateLimiter: 解析自定义规则 WindowDuration 失败", logger.WithError(err), logger.WithField("rulePattern", rule.PathPattern), logger.WithField("configValue", rule.WindowDuration))
						}
					}
					// 自定义规则 Key: rule:{pattern}:{ip} (或加用户ID?)
					key := fmt.Sprintf("rate_limit:cs:rule:%s:%s", rule.PathPattern, clientIP)
					limit := rule.Limit
					if !checkLimitWithCacheService(ctx, cacheService, key, limit, ruleWindow) {
						log.Warn("RateLimiter: 请求被限流 (CacheService - Custom Rule)",
							logger.WithField("key", key),
							logger.WithField("limit", limit),
							logger.WithField("ip", clientIP),
							logger.WithField("path", path),
						)
						CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, 7007, "请求频率超限 (规则)")
						ctx.StopExecution() // 停止处理
						return
					}
					// 如果匹配了自定义规则，是否还需要检查全局/IP/用户？ 暂定不检查，规则优先
					ctx.Next()
					return
				}
			}

			// 检查全局限流 (CacheService 类型)
			if cfg.GlobalLimit > 0 {
				key := "rate_limit:cs:global"
				if !checkLimitWithCacheService(ctx, cacheService, key, cfg.GlobalLimit, window) {
					log.Warn("RateLimiter: 请求被限流 (CacheService - Global)", logger.WithField("key", key), logger.WithField("limit", cfg.GlobalLimit))
					CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, 7007, "请求频率超限 (全局)")
					ctx.StopExecution()
					return
				}
			}

			// 检查IP限流 (CacheService 类型)
			if cfg.IPLimit > 0 && clientIP != "" {
				key := fmt.Sprintf("rate_limit:cs:ip:%s", clientIP)
				if !checkLimitWithCacheService(ctx, cacheService, key, cfg.IPLimit, window) {
					log.Warn("RateLimiter: 请求被限流 (CacheService - IP)", logger.WithField("key", key), logger.WithField("limit", cfg.IPLimit), logger.WithField("ip", clientIP))
					CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, 7007, "请求频率超限 (IP)")
					ctx.StopExecution()
					return
				}
			}

			// 检查用户限流 (CacheService 类型)
			if cfg.UserLimit > 0 && userID != "" {
				key := fmt.Sprintf("rate_limit:cs:user:%s", userID)
				if !checkLimitWithCacheService(ctx, cacheService, key, cfg.UserLimit, window) {
					log.Warn("RateLimiter: 请求被限流 (CacheService - User)", logger.WithField("key", key), logger.WithField("limit", cfg.UserLimit), logger.WithField("userId", userID))
					CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, 7007, "请求频率超限 (用户)")
					ctx.StopExecution()
					return
				}
			}

		} else {
			// === 使用 Memory/Redis Limiter 进行限流 ===

			// 检查自定义规则限流 (Memory/Redis 类型)
			for _, rule := range cfg.CustomRules {
				if customRuleMatching(path, rule.PathPattern, rule.Methods, ctx.Method()) {
					limiter, ok := customLimiters[rule.PathPattern]
					if !ok {
						continue // 没有为这个规则配置限流器 (可能是因为 Rate <= 0)
					}
					// 使用路径+IP作为限流key
					key := fmt.Sprintf("%s:%s", rule.PathPattern, clientIP)
					if !limiter.Allow(key) {
						log.Warn("RateLimiter: 请求被限流 (Memory/Redis - Custom Rule)",
							logger.WithFields(logger.Fields{
								"path":   path,
								"method": ctx.Method(),
								"ip":     clientIP,
								"userId": userID,
								"type":   "custom",
								"rule":   rule.PathPattern,
							}))
						CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, 7007, "请求频率超限 (规则)")
						ctx.StopExecution()
						return
					}
					// 如果匹配了自定义规则，是否还需要检查全局/IP/用户？ 暂定不检查，规则优先
					ctx.Next()
					return
				}
			}

			// 检查全局限流 (Memory/Redis 类型)
			if globalLimiter != nil {
				if !globalLimiter.Allow("global") {
					log.Warn("RateLimiter: 请求被限流 (Memory/Redis - Global)",
						logger.WithFields(logger.Fields{
							"path": path, "method": ctx.Method(), "ip": clientIP, "userId": userID, "type": "global",
						}))
					CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, 7007, "请求频率超限 (全局)")
					ctx.StopExecution()
					return
				}
			}

			// 检查IP限流 (Memory/Redis 类型)
			if ipLimiter != nil && clientIP != "" {
				if !ipLimiter.Allow(clientIP) {
					log.Warn("RateLimiter: 请求被限流 (Memory/Redis - IP)",
						logger.WithFields(logger.Fields{
							"path": path, "method": ctx.Method(), "ip": clientIP, "userId": userID, "type": "ip",
						}))
					CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, 7007, "请求频率超限 (IP)")
					ctx.StopExecution()
					return
				}
			}

			// 检查用户限流 (Memory/Redis 类型)
			if cfg.LimiterType != LIMITER_TYPE_CACHE_SERVICE && cfg.UserRate > 0 && userLimiter != nil {
				reqCtx := ctx.Request().Context()
				userID64, errUser := util.GetUserIDFromStdContext(reqCtx)
				if errUser == nil {
					userID := uint(userID64)
					userKey := fmt.Sprintf("user:%d:%s", userID, path)
					if !userLimiter.Allow(userKey) {
						logger.GetLogger().Debug(reqCtx, "RateLimitMiddleware: User rate limit exceeded by userKey", logger.WithField("key", userKey), logger.WithField("path", path))
						CustomResponse.FailWithHttpStatus(ctx, http.StatusTooManyRequests, pk_constant.REQUEST_RATE_LIMIT, pk_constant.GetCodeMessage(pk_constant.REQUEST_RATE_LIMIT))
						ctx.StopExecution()
						return
					}
				} else {
					logger.GetLogger().Warn(reqCtx, fmt.Sprintf("RateLimitMiddleware: UserID for rate limiting not found or invalid. Path: %s. Error: %v. Allowing request to pass (or configure strict mode).", path, errUser))
				}
			}
		}

		// 所有检查通过，继续处理请求
		ctx.Next()
	}
}

// checkLimitWithCacheService 使用 CacheService 检查限流的核心逻辑
// 返回 true 表示允许，false 表示拒绝
func checkLimitWithCacheService(ctx iris.Context, cacheService service.CacheService, key string, limit int64, window time.Duration) bool {
	if limit <= 0 { // 如果限制次数小于等于0，则不限制
		return true
	}
	log := logger.WithContext(ctx)

	currentCount, err := cacheService.Increment(ctx.Request().Context(), key)
	if err != nil {
		// 缓存错误，策略：记录日志并放行，避免缓存问题影响服务
		log.Error("RateLimiter: CacheService.Increment failed", logger.WithError(err), logger.WithField("key", key))
		return true // 放行
	}

	// 如果是窗口内的第一次请求 (计数为 1)，设置过期时间
	if currentCount == 1 {
		err := cacheService.Expire(ctx.Request().Context(), key, window)
		if err != nil {
			// 设置过期失败，记录日志，但不影响本次请求的判断
			log.Error("RateLimiter: CacheService.Expire failed", logger.WithError(err), logger.WithField("key", key), logger.WithField("window", window.String()))
		}
	}

	// 检查是否超限
	if currentCount > limit {
		// log.Warn(...) // 日志已在 RateLimitMiddleware 中记录
		return false // 拒绝
	}

	return true // 允许
}

// createLimiter 创建 Memory/Redis 限流器 (全局/IP/用户)
func createLimiter(cfg RateLimitConfig, rate float64) RateLimiter {
	switch cfg.LimiterType {
	case LIMITER_TYPE_MEMORY:
		return NewMemoryLimiter(rate, cfg.BucketCapacity, cfg.Algorithm, cfg.WindowSize)
	case LIMITER_TYPE_REDIS:
		// TODO: 实现 Redis 限流器
		logger.Warn("Redis限流器未直接实现，回退到内存限流器")
		return NewMemoryLimiter(rate, cfg.BucketCapacity, cfg.Algorithm, cfg.WindowSize)
	default: // 默认为 Memory
		return NewMemoryLimiter(rate, cfg.BucketCapacity, cfg.Algorithm, cfg.WindowSize)
	}
}

// createLimiterForRule 为自定义规则创建 Memory/Redis 限流器
func createLimiterForRule(cfg RateLimitConfig, rule RateLimitRule) RateLimiter {
	// 自定义规则使用自己的 Rate 和 Capacity
	rate := rule.Rate
	capacity := rule.Capacity
	if capacity <= 0 { // 如果未指定容量，使用全局容量
		capacity = cfg.BucketCapacity
	}

	switch cfg.LimiterType {
	case LIMITER_TYPE_MEMORY:
		return NewMemoryLimiter(rate, capacity, cfg.Algorithm, cfg.WindowSize)
	case LIMITER_TYPE_REDIS:
		logger.Warn("Redis限流器未直接实现 (规则)，回退到内存限流器")
		return NewMemoryLimiter(rate, capacity, cfg.Algorithm, cfg.WindowSize)
	default:
		return NewMemoryLimiter(rate, capacity, cfg.Algorithm, cfg.WindowSize)
	}
}

// customRuleMatching 检查请求是否匹配自定义规则 (保持不变)
func customRuleMatching(path, pattern string, methods []string, method string) bool {
	if !MatchPath(path, pattern) {
		return false
	}

	return len(methods) == 0 || SliceContains(methods, method) || SliceContains(methods, "*")
}

// --- 需要 MatchPath 和 SliceContains 的实现，假设它们在 middleware_util.go 或类似文件中 ---
// // MatchPath 检查路径是否匹配模式 (示例，可能需要更复杂的实现)
// func MatchPath(path, pattern string) bool {
// 	// 实现路径匹配逻辑，例如支持通配符 *
// 	if strings.Contains(pattern, "*") {
// 		// 简单通配符匹配
// 		parts := strings.Split(pattern, "*")
// 		matched := true
// 		tempPath := path
// 		for i, part := range parts {
// 			if part == "" {
// 				continue
// 			}
// 			idx := strings.Index(tempPath, part)
// 			if i == 0 && idx != 0 { // 第一个部分必须在开头
// 				matched = false
// 				break
// 			}
// 			if idx == -1 {
// 				matched = false
// 				break
// 			}
// 			tempPath = tempPath[idx+len(part):]
// 		}
// 		// 如果最后一个部分是 '*'，或者匹配到最后
// 		if matched && (strings.HasSuffix(pattern, "*") || tempPath == "") {
// 			return true
// 		}
// 		return false
// 	}
// 	return path == pattern
// }

// // SliceContains 检查切片是否包含某个字符串
// func SliceContains(slice []string, item string) bool {
// 	for _, s := range slice {
// 		if s == item {
// 			return true
// 		}
// 	}
// 	return false
// }
