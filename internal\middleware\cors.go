package middleware

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
)

// CORSConfig CORS中间件配置
type CORSConfig struct {
	// 是否启用CORS
	Enabled bool `json:"enabled" yaml:"enabled"`
	// 允许的源
	AllowedOrigins []string `json:"allowedOrigins" yaml:"allowedOrigins"`
	// 允许的方法
	AllowedMethods []string `json:"allowedMethods" yaml:"allowedMethods"`
	// 允许的头
	AllowedHeaders []string `json:"allowedHeaders" yaml:"allowedHeaders"`
	// 允许的凭证
	AllowCredentials bool `json:"allowCredentials" yaml:"allowCredentials"`
	// 公开的头
	ExposedHeaders []string `json:"exposedHeaders" yaml:"exposedHeaders"`
	// 预检请求的缓存时间
	MaxAge int `json:"maxAge" yaml:"maxAge"`
	// 排除的路径
	ExcludedPaths []string `json:"excludedPaths" yaml:"excludedPaths"`
}

// 默认CORS配置
var defaultCORSConfig = CORSConfig{
	Enabled:          true,
	AllowedOrigins:   []string{"*"},
	AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"},
	AllowedHeaders:   []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
	AllowCredentials: true,
	ExposedHeaders:   []string{"Content-Length", "Content-Type", "X-Total-Count"},
	MaxAge:           86400, // 24小时
	ExcludedPaths:    []string{"/public/doc"},
}

// CORSMiddleware CORS中间件
// 参数:
//   - config: 可选的CORS配置，不提供则使用默认配置
//
// 返回:
//   - iris.Handler: Iris中间件处理函数
func CORSMiddleware(config ...CORSConfig) iris.Handler {
	// 使用默认配置
	cfg := defaultCORSConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(ctx iris.Context) {
		// 如果未启用，则跳过
		if !cfg.Enabled {
			ctx.Next()
			return
		}

		// 检查是否为排除的路径
		for _, path := range cfg.ExcludedPaths {
			if strings.HasPrefix(ctx.Path(), path) {
				ctx.Next()
				return
			}
		}

		// 获取来源
		origin := ctx.GetHeader("Origin")
		if origin == "" {
			// 没有Origin头，直接继续
			ctx.Next()
			return
		}

		// 检查来源是否被允许
		allowedOrigin := getAllowedOrigin(origin, cfg.AllowedOrigins)
		if allowedOrigin != "" {
			ctx.Header("Access-Control-Allow-Origin", allowedOrigin)
		}

		// 如果是预检请求
		if ctx.Method() == "OPTIONS" {
			// 设置允许的方法
			ctx.Header("Access-Control-Allow-Methods", strings.Join(cfg.AllowedMethods, ", "))

			// 设置允许的头
			ctx.Header("Access-Control-Allow-Headers", strings.Join(cfg.AllowedHeaders, ", "))

			// 设置允许的凭证
			if cfg.AllowCredentials {
				ctx.Header("Access-Control-Allow-Credentials", "true")
			}

			// 设置公开的头
			if len(cfg.ExposedHeaders) > 0 {
				ctx.Header("Access-Control-Expose-Headers", strings.Join(cfg.ExposedHeaders, ", "))
			}

			// 设置预检请求的缓存时间
			if cfg.MaxAge > 0 {
				ctx.Header("Access-Control-Max-Age", strconv.Itoa(cfg.MaxAge))
			}

			// 预检请求直接返回200
			ctx.StatusCode(http.StatusOK)
			return
		}

		// 非预检请求，设置CORS头部
		if len(cfg.ExposedHeaders) > 0 {
			ctx.Header("Access-Control-Expose-Headers", strings.Join(cfg.ExposedHeaders, ", "))
		}

		if cfg.AllowCredentials {
			ctx.Header("Access-Control-Allow-Credentials", "true")
		}

		// 继续处理请求
		ctx.Next()
	}
}

// getAllowedOrigin 获取允许的源
// 参数:
//   - origin: 请求的源
//   - allowedOrigins: 允许的源列表
//
// 返回:
//   - string: 允许的源，如果不允许则返回空字符串
func getAllowedOrigin(origin string, allowedOrigins []string) string {
	// 如果允许所有源
	if SliceContains(allowedOrigins, "*") {
		// 如果允许凭证，则不能使用通配符，必须指定确切的源
		// 因此我们返回请求的Origin
		return origin
	}

	// 检查是否允许特定源
	if SliceContains(allowedOrigins, origin) {
		return origin
	}

	// 检查是否有通配符域名匹配
	for _, allowedOrigin := range allowedOrigins {
		// 检查域名通配符，例如 *.example.com
		if strings.HasPrefix(allowedOrigin, "*.") {
			suffix := allowedOrigin[1:] // 去掉*号
			if strings.HasSuffix(origin, suffix) {
				return origin
			}
		}
	}

	// 源不被允许
	return ""
}
