package entity

import (
	"time"
)

// FiscalPeriod 定义了会计期间的实体模型。
// 它包含了财务年度和月份的标识，以及精确到日的自然日历期间，
// 同时还有一个唯一的期间代码用于快速引用。
type FiscalPeriod struct {
	AccountBookEntity // AccountBookEntity 符合项目规范

	// Code 是会计期间的唯一代码，格式为 YYYYMM (例如 "202401")。
	// YYYY 代表财务年度，MM 代表财务月份。
	Code string `gorm:"column:code;type:varchar(6);not null;comment:期间代码 (YYYYMM)"`

	// FiscalYear 是财务年度的标签，例如 2024。
	FiscalYear int `gorm:"column:fiscal_year;not null;comment:财务年度"`

	// FiscalMonth 是指当前期间是其所在财务年度的第几个月 (范围 1-12)。
	FiscalMonth int `gorm:"column:fiscal_month;not null;comment:财务月份 (1-12)"`

	// StartDate 是该会计期间在日历上的确切开始日期。
	StartDate time.Time `gorm:"column:start_date;type:date;not null;comment:期间开始日期"`

	// EndDate 是该会计期间在日历上的确切结束日期。
	EndDate time.Time `gorm:"column:end_date;type:date;not null;comment:期间结束日期"`

	// Status 表示会计期间的当前状态。
	// - NEVER_OPENED: 从未启用
	// - OPEN: 已启用 (开放记账)
	// - CLOSED: 已关闭 (禁止记账)
	Status string `gorm:"column:status;type:varchar(20);not null;default:'NEVER_OPENED';comment:状态"`
}

// TableName 指定了 FiscalPeriod 实体在数据库中的表名。
func (FiscalPeriod) TableName() string {
	return "fin_period"
}
