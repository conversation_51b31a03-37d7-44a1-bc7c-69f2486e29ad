<template>
  <el-dialog
    v-model="dialogVisible"
    title="确认发运"
    width="60%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="shipping-dialog-container" v-if="shipmentData">
      <!-- 发运单信息 -->
      <el-card class="shipment-info-card" shadow="never">
        <template #header>
          <span>发运单信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">发运单号:</span>
              <span class="value">{{ shipmentData.shipmentNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">承运商:</span>
              <span class="value">{{ shipmentData.carrierName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">运输方式:</span>
              <span class="value">{{ shipmentData.shippingMethod }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">包装总数:</span>
              <span class="value">{{ shipmentData.totalPackages }}件</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">总重量:</span>
              <span class="value">{{ shipmentData.actualWeight }}kg</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">总体积:</span>
              <span class="value">{{ shipmentData.actualVolume }}m³</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 发运信息 -->
      <el-card class="shipping-info-card" shadow="never">
        <template #header>
          <span>发运信息</span>
        </template>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="运单号" prop="trackingNo">
                <el-input
                  v-model="formData.trackingNo"
                  placeholder="请输入运单号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发运时间" prop="actualShipDate">
                <el-date-picker
                  v-model="formData.actualShipDate"
                  type="datetime"
                  placeholder="请选择发运时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="司机姓名" prop="driverName">
                <el-input
                  v-model="formData.driverName"
                  placeholder="请输入司机姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="司机电话" prop="driverPhone">
                <el-input
                  v-model="formData.driverPhone"
                  placeholder="请输入司机电话"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="车牌号" prop="vehicleNo">
                <el-input
                  v-model="formData.vehicleNo"
                  placeholder="请输入车牌号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预计送达时间" prop="estimatedDeliveryDate">
                <el-date-picker
                  v-model="formData.estimatedDeliveryDate"
                  type="datetime"
                  placeholder="请选择预计送达时间"
                  style="width: 100%"
                  :disabled-date="disabledDate"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="发运备注" prop="shippingRemark">
            <el-input
              v-model="formData.shippingRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入发运备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 运费确认 -->
      <el-card class="cost-info-card" shadow="never">
        <template #header>
          <span>运费确认</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="cost-item">
              <span class="label">运费:</span>
              <span class="value">¥{{ shipmentData.shippingCost || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="cost-item">
              <span class="label">保险费:</span>
              <span class="value">¥{{ shipmentData.insuranceFee || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="cost-item">
              <span class="label">包装费:</span>
              <span class="value">¥{{ shipmentData.packingCost || 0 }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="cost-item total">
              <span class="label">总费用:</span>
              <span class="value">¥{{ totalCost }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 收货人信息确认 -->
      <el-card class="consignee-info-card" shadow="never">
        <template #header>
          <span>收货人信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">收货人:</span>
              <span class="value">{{ shipmentData.consigneeName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">联系电话:</span>
              <span class="value">{{ shipmentData.consigneePhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">邮箱:</span>
              <span class="value">{{ shipmentData.consigneeEmail || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="16">
            <div class="info-item">
              <span class="label">收货地址:</span>
              <span class="value">{{ shipmentData.consigneeAddress }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">邮政编码:</span>
              <span class="value">{{ shipmentData.postalCode || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="!canConfirm"
        >
          确认发运
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElDatePicker } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useShipmentStore } from '@/store/wms/shipment'

// 组件接口定义
interface ShippingDialogEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<ShippingDialogEmits>()

// Store
const shipmentStore = useShipmentStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const shipmentData = ref<any>(null)

// 表单数据
const formData = reactive({
  trackingNo: '',
  actualShipDate: new Date(),
  driverName: '',
  driverPhone: '',
  vehicleNo: '',
  estimatedDeliveryDate: '',
  shippingRemark: ''
})

// 表单验证规则
const formRules: FormRules = {
  trackingNo: [
    { required: true, message: '请输入运单号', trigger: 'blur' }
  ],
  actualShipDate: [
    { required: true, message: '请选择发运时间', trigger: 'change' }
  ],
  driverName: [
    { required: true, message: '请输入司机姓名', trigger: 'blur' }
  ],
  driverPhone: [
    { required: true, message: '请输入司机电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  vehicleNo: [
    { required: true, message: '请输入车牌号', trigger: 'blur' }
  ]
}

// 计算属性
const canConfirm = computed(() => {
  return formData.trackingNo && formData.actualShipDate && formData.driverName && formData.driverPhone && formData.vehicleNo
})

const totalCost = computed(() => {
  if (!shipmentData.value) return 0
  const shipping = shipmentData.value.shippingCost || 0
  const insurance = shipmentData.value.insuranceFee || 0
  const packing = shipmentData.value.packingCost || 0
  return (shipping + insurance + packing).toFixed(2)
})

// 表单引用
const formRef = ref<FormInstance>()

// 方法
const loadShipmentData = async (shipmentId: number) => {
  try {
    const data = await shipmentStore.fetchDetail(shipmentId)
    shipmentData.value = data
    
    // 设置默认的预计送达时间（发运时间后1-3天）
    const shipDate = new Date(formData.actualShipDate)
    const deliveryDate = new Date(shipDate)
    deliveryDate.setDate(shipDate.getDate() + 2) // 默认2天后送达
    formData.estimatedDeliveryDate = deliveryDate.toISOString()
  } catch (error) {
    ElMessage.error('加载发运单数据失败')
    console.error(error)
  }
}

const handleOpen = () => {
  // 在open方法中会传入shipmentId
}

const handleClose = () => {
  resetData()
}

const resetData = () => {
  shipmentData.value = null
  Object.assign(formData, {
    trackingNo: '',
    actualShipDate: new Date(),
    driverName: '',
    driverPhone: '',
    vehicleNo: '',
    estimatedDeliveryDate: '',
    shippingRemark: ''
  })
  formRef.value?.clearValidate()
}

const disabledDate = (time: Date) => {
  const shipDate = new Date(formData.actualShipDate)
  return time.getTime() < shipDate.getTime()
}

const handleConfirm = async () => {
  if (!formRef.value || !shipmentData.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const shippingData = {
      shipmentId: shipmentData.value.id,
      trackingNo: formData.trackingNo,
      actualShipDate: formData.actualShipDate,
      driverName: formData.driverName,
      driverPhone: formData.driverPhone,
      vehicleNo: formData.vehicleNo,
      estimatedDeliveryDate: formData.estimatedDeliveryDate,
      shippingRemark: formData.shippingRemark
    }
    
    await shipmentStore.shipOut(shipmentData.value.id, shippingData)
    
    ElMessage.success('发运成功')
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('发运失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (shipmentId: number) => {
  dialogVisible.value = true
  loadShipmentData(shipmentId)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.shipping-dialog-container {
  padding: 16px 0;
}

.shipment-info-card,
.shipping-info-card,
.cost-info-card,
.consignee-info-card {
  margin-bottom: 16px;
}

.shipment-info-card :deep(.el-card__header),
.shipping-info-card :deep(.el-card__header),
.cost-info-card :deep(.el-card__header),
.consignee-info-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.cost-item.total {
  background-color: #e8f4fd;
  font-weight: bold;
  font-size: 16px;
}

.cost-item .label {
  color: #606266;
}

.cost-item .value {
  color: #303133;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}
</style>
