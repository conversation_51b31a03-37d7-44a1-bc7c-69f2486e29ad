import request from '@/utils/request';

// --- 类型定义 (根据后端 fin_currency VO/DTO) ---

// 币种列表项
export interface CurrencyListItem {
  id: number;
  name: string;
  code: string;
  symbol: string;
  precision: number;
  isEnabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 币种列表响应
export interface CurrencyListResponse {
  list: CurrencyListItem[];
  total: number;
}

// 币种查询参数
export interface CurrencyQueryParams {
  pageNum: number;
  pageSize: number;
  name?: string;
  code?: string;
  isEnabled?: boolean;
  sort?: string;
}

// 币种创建/更新数据
export interface CurrencyFormData {
  name: string;
  code: string;
  symbol: string;
  precision: number;
  isEnabled: boolean;
  description?: string;
}

// --- API 函数定义 ---

const BASE_URL = '/fin/currencies';

/**
 * 获取币种列表 (分页)
 * @param params 查询参数
 */
export function getCurrencyPage(params: CurrencyQueryParams): Promise<CurrencyListResponse> {
  return request.get<CurrencyListResponse>(`${BASE_URL}/page`, { params }) as unknown as Promise<CurrencyListResponse>;
}

/**
 * 获取所有启用的币种列表 (用于下拉框)
 */
export function getEnabledCurrencyList(): Promise<CurrencyListItem[]> {
    return request.get<CurrencyListItem[]>(`${BASE_URL}/list`) as unknown as Promise<CurrencyListItem[]>;
}

/**
 * 获取单个币种详情
 * @param id 币种ID
 */
export function getCurrencyDetail(id: number): Promise<CurrencyListItem> {
    return request.get<CurrencyListItem>(`${BASE_URL}/${id}`) as unknown as Promise<CurrencyListItem>;
}


/**
 * 新增币种
 * @param data 币种数据
 */
export function addCurrency(data: CurrencyFormData) {
  return request.post<any>(BASE_URL, data);
}

/**
 * 更新币种信息
 * @param id 币种 ID
 * @param data 币种数据
 */
export function updateCurrency(id: number, data: Partial<CurrencyFormData>) {
  return request.put<any>(`${BASE_URL}/${id}`, data);
}

/**
 * 删除币种
 * @param id 币种 ID
 */
export function deleteCurrency(id: number) {
  return request.delete<any>(`${BASE_URL}/${id}`);
}

/**
 * 批量删除币种
 * @param ids 币种 ID 数组
 */
// export function batchDeleteCurrencies(ids: number[]) {
//   return request.delete<any>(BASE_URL, { data: { ids } });
// } 