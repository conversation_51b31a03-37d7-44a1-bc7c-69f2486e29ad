package dto

import (
	"backend/pkg/response"
	"time"
)

// WmsPickingTaskCreateReq 创建拣货任务请求
type WmsPickingTaskCreateReq struct {
	NotificationID  uint    `json:"notificationId" validate:"required"`
	PickingStrategy string  `json:"pickingStrategy" validate:"required,oneof=BY_ORDER BATCH WAVE ZONE"`
	WaveNo          *string `json:"waveNo" validate:"omitempty,max=50"`
	Priority        int     `json:"priority" validate:"min=1,max=10"`
	AssignedUserID  *uint   `json:"assignedUserId"`
	Remark          *string `json:"remark"`
}

// WmsPickingTaskUpdateReq 更新拣货任务请求
type WmsPickingTaskUpdateReq struct {
	ID              uint    `json:"id" validate:"required"`
	PickingStrategy string  `json:"pickingStrategy" validate:"required,oneof=BY_ORDER BATCH WAVE ZONE"`
	WaveNo          *string `json:"waveNo" validate:"omitempty,max=50"`
	Priority        int     `json:"priority" validate:"min=1,max=10"`
	AssignedUserID  *uint   `json:"assignedUserId"`
	Remark          *string `json:"remark"`
}

// WmsPickingTaskQueryReq 查询拣货任务请求
type WmsPickingTaskQueryReq struct {
	PageQuery       response.PageQuery `json:"-"` // 分页查询参数，由Controller填充
	TaskNo          *string            `json:"taskNo"`
	NotificationID  *uint              `json:"notificationId"`
	PickingStrategy *string            `json:"pickingStrategy"`
	WaveNo          *string            `json:"waveNo"`
	Status          *string            `json:"status"`
	Priority        *int               `json:"priority"`
	AssignedUserID  *uint              `json:"assignedUserId"`
	CreatedAtStart  *time.Time         `json:"createdAtStart"`
	CreatedAtEnd    *time.Time         `json:"createdAtEnd"`
}

// WmsPickingTaskAssignReq 分配拣货任务请求
type WmsPickingTaskAssignReq struct {
	ID             uint `json:"id" validate:"required"`
	AssignedUserID uint `json:"assignedUserId" validate:"required"`
}

// WmsPickingTaskBatchAssignReq 批量分配拣货任务请求
type WmsPickingTaskBatchAssignReq struct {
	IDs            []uint `json:"ids" validate:"required,min=1,max=100"`
	AssignedUserID uint   `json:"assignedUserId" validate:"required"`
}

// WmsPickingTaskStartReq 开始拣货任务请求
type WmsPickingTaskStartReq struct {
	ID uint `json:"id" validate:"required"`
}

// WmsPickingTaskCompleteReq 完成拣货任务请求
type WmsPickingTaskCompleteReq struct {
	ID     uint    `json:"id" validate:"required"`
	Remark *string `json:"remark"`
}

// WmsPickingTaskCancelReq 取消拣货任务请求
type WmsPickingTaskCancelReq struct {
	ID     uint    `json:"id" validate:"required"`
	Reason *string `json:"reason"`
}

// WmsPickingExecuteReq 拣货执行请求
type WmsPickingExecuteReq struct {
	TaskDetailID uint    `json:"taskDetailId" validate:"required"`
	PickedQty    float64 `json:"pickedQty" validate:"required,gt=0"`
	LocationID   uint    `json:"locationId" validate:"required"`
	BatchNo      *string `json:"batchNo"`
	Remark       *string `json:"remark"`
}

// WmsPickingBatchExecuteReq 批量拣货执行请求
type WmsPickingBatchExecuteReq struct {
	TaskID  uint                   `json:"taskId" validate:"required"`
	Details []WmsPickingExecuteReq `json:"details" validate:"required,min=1,dive"`
}

// WmsPickingExceptionReq 拣货异常处理请求
type WmsPickingExceptionReq struct {
	TaskDetailID    uint    `json:"taskDetailId" validate:"required"`
	ExceptionType   string  `json:"exceptionType" validate:"required,oneof=SHORTAGE DAMAGE LOCATION_ERROR OTHER"`
	ShortageQty     float64 `json:"shortageQty" validate:"omitempty,gte=0"`
	ExceptionDesc   string  `json:"exceptionDesc" validate:"required,max=500"`
	SuggestedAction *string `json:"suggestedAction"`
}

// WmsPickingWaveCreateReq 创建拣货波次请求
type WmsPickingWaveCreateReq struct {
	WaveNo          string  `json:"waveNo" validate:"required,max=50"`
	NotificationIDs []uint  `json:"notificationIds" validate:"required,min=1,max=100"`
	PickingStrategy string  `json:"pickingStrategy" validate:"required,oneof=BATCH ZONE"`
	Priority        int     `json:"priority" validate:"min=1,max=10"`
	AssignedUserID  *uint   `json:"assignedUserId"`
	Remark          *string `json:"remark"`
}

// WmsPickingWaveReleaseReq 释放拣货波次请求
type WmsPickingWaveReleaseReq struct {
	WaveNo string `json:"waveNo" validate:"required"`
}

// WmsPickingTaskStatsReq 拣货任务统计请求
type WmsPickingTaskStatsReq struct {
	DateStart       *time.Time `json:"dateStart"`
	DateEnd         *time.Time `json:"dateEnd"`
	GroupBy         string     `json:"groupBy" validate:"oneof=day week month status user strategy"`
	AssignedUserID  *uint      `json:"assignedUserId"`
	PickingStrategy *string    `json:"pickingStrategy"`
}

// WmsPickingTaskExportReq 拣货任务导出请求
type WmsPickingTaskExportReq struct {
	WmsPickingTaskQueryReq
	ExportFormat string   `json:"exportFormat" validate:"required,oneof=excel pdf"`
	ExportFields []string `json:"exportFields"`
}

// WmsPickingTaskOptimizeReq 拣货任务路径优化请求
type WmsPickingTaskOptimizeReq struct {
	TaskID          uint   `json:"taskId" validate:"required"`
	OptimizeType    string `json:"optimizeType" validate:"required,oneof=DISTANCE TIME ZONE"`
	StartLocationID *uint  `json:"startLocationId"`
}

// WmsPickingTaskBatchOptimizeReq 批量拣货任务路径优化请求
type WmsPickingTaskBatchOptimizeReq struct {
	TaskIDs         []uint `json:"taskIds" validate:"required,min=1,max=50"`
	OptimizeType    string `json:"optimizeType" validate:"required,oneof=DISTANCE TIME ZONE"`
	StartLocationID *uint  `json:"startLocationId"`
}

// Mobile端专用DTO

// WmsMobilePickingTaskQueryReq 移动端拣货任务查询请求
type WmsMobilePickingTaskQueryReq struct {
	PageQuery response.PageQuery `json:"-"` // 分页查询参数，由Controller填充
	UserID    *uint              `json:"userId"`
	Status    *string            `json:"status"`
}

// WmsMobilePickingExecuteReq 移动端拣货执行请求
type WmsMobilePickingExecuteReq struct {
	TaskDetailID uint    `json:"taskDetailId" validate:"required"`
	PickedQty    float64 `json:"pickedQty" validate:"required,gt=0"`
	ScanCode     *string `json:"scanCode"`
	LocationCode *string `json:"locationCode"`
	BatchNo      *string `json:"batchNo"`
	Remark       *string `json:"remark"`
}

// WmsMobilePickingConfirmReq 移动端拣货确认请求
type WmsMobilePickingConfirmReq struct {
	TaskID uint `json:"taskId" validate:"required"`
}
