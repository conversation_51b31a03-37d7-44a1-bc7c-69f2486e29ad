import type { SidebarItem } from '../VNSidebar/types';
import type { StatusBarItem } from '../VNStatusBar/types';
import type { BreadcrumbItem } from '../VNBreadcrumb/types';

export interface VNFrameProps {
  // Sidebar related
  showSidebar?: boolean; // 是否显示侧边栏
  sidebarItems?: SidebarItem[]; // 传递给 VNSidebar 的菜单项
  sidebarWidth?: string; // 侧边栏展开宽度
  sidebarCollapsedWidth?: string; // 侧边栏折叠宽度 (如果 VNSidebar 支持)
  sidebarCollapsed?: boolean; // 控制侧边栏折叠状态 (可 v-model)

  // Breadcrumb related
  showBreadcrumb?: boolean; // 是否显示面包屑区域
  breadcrumbItems?: BreadcrumbItem[]; // 传递给 VNBreadcrumb 的项
  breadcrumbHeight?: string; // Optional: Define height for the breadcrumb area

  // Statusbar/Footer related
  showStatusBar?: boolean; // 是否显示状态栏
  statusBarItems?: StatusBarItem[]; // 传递给 VNStatusBar 的项
  footerHeight?: string; // 底部高度

  // Layout related
  // fixedSidebar: boolean; // Keeping sidebar fixed is implicit in this layout
}

export interface VNFrameEmits {
  (e: 'update:sidebarCollapsed', value: boolean): void; // v-model for sidebarCollapsed
  // 可以添加其他从 Frame 发出的事件，例如 header 中的用户菜单点击等
} 