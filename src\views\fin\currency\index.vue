<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="180"
      @refresh="loadData"
      @add="handleAdd"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
    >
      <template #column-isEnabled="{ row }">
        <el-tag :type="row.isEnabled ? 'success' : 'info'">
          {{ row.isEnabled ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEdit(row)" v-if="hasPermission('fin:currency:edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            @click="handleDelete(row)" 
            v-if="hasPermission('fin:currency:delete')"
          ></el-button>
        </el-tooltip>
      </template>

    </VNTable>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2"
        :label-width="'100px'"
        :loading="loading"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <template #actions> 
          <template v-if="isViewing">
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else> 
            <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
            <el-button @click="handleCloseDialog">取消</el-button>
          </template>
        </template>

      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton } from 'element-plus';
import { View, Edit, Delete } from '@element-plus/icons-vue';
import {
  getCurrencyPage,
  addCurrency,
  updateCurrency,
  deleteCurrency,
  getCurrencyDetail,
} from '@/api/fin/currency';
import type { 
    CurrencyListItem,
    CurrencyFormData
} from '@/api/fin/currency';
import { hasPermission } from '@/hooks/usePermission';

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentRow = ref<CurrencyListItem | null>(null);
const isViewing = computed(() => formMode.value === 'view');

// --- Reactive State ---
const loading = ref(false);
const tableData = ref<CurrencyListItem[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const formData = ref<Partial<CurrencyFormData>>({});

const currentFilters = ref<Record<string, any>>({});
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);

const tableColumns = ref<TableColumn[]>([
  { prop: 'name', label: '名称', minWidth: 150, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'code', label: '代码', width: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'symbol', label: '符号', width: 80 },
  { prop: 'precision', label: '精度', width: 80, sortable: true },
  {
    prop: 'isEnabled', label: '状态', width: 90, slot: true,
    filterable: true, filterType: 'select',
    filterOptions: [{ label: '启用', value: true }, { label: '禁用', value: false }]
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    sortable: true,
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('fin:currency:add'),
  filter: true,
  columnSetting: true,
  density: true,
  fullscreen: true,
}));

const formFields = computed<HeaderField[]>(() => [
  { field: 'name', label: '币种名称', rules: [{ required: true, message: '币种名称为必填项' }], disabled: isViewing.value },
  { field: 'code', label: '币种代码', rules: [{ required: true, message: '币种代码为必填项' }], disabled: isViewing.value || formMode.value === 'edit' },
  { field: 'symbol', label: '货币符号', rules: [{ required: true, message: '货币符号为必填项' }], disabled: isViewing.value },
  { field: 'precision', label: '小数位数', type: 'number', props: { min: 0, max: 8, precision: 0 }, defaultValue: 2, rules: [{ required: true, message: '小数位数为必填项' }], disabled: isViewing.value },
  { field: 'isEnabled', label: '状态', type: 'switch', defaultValue: true, disabled: isViewing.value, span: 24 },
  { field: 'description', label: '描述', type: 'textarea', rows: 3, span: 24, disabled: isViewing.value },
]);

const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增币种';
  if (formMode.value === 'edit') return '编辑币种';
  if (formMode.value === 'view') return '查看币种';
  return '币种管理';
});

onMounted(() => {
  loadData();
});

const loadData = async () => {
  loading.value = true;
  try {
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop) {
        const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
        sortString = `${currentSort.value.prop},${direction}`;
    }

    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...currentFilters.value,
      sort: sortString
    };

    const response = await getCurrencyPage(params);
    tableData.value = response.list;
    pagination.total = response.total;

  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: CurrencyListItem) => {
  formMode.value = mode;
  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id) return;
    currentRow.value = rowData;
    dialogVisible.value = true;
    loading.value = true;
    try {
        const detailData = await getCurrencyDetail(rowData.id);
        formData.value = { ...detailData };
    } catch (error) {
        const errorMessage = getApiErrorMessage(error);
        ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: `获取详情失败: ${errorMessage}`, showClose: true, duration: 5000 });
        formData.value = { ...rowData };
    } finally {
         loading.value = false;
    }
  } else {
    formData.value = {
      name: '',
      code: '',
      symbol: '',
      precision: 2,
      isEnabled: true,
      description: '',
    };
    currentRow.value = null;
    dialogVisible.value = true;
  }

  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentRow.value = null;
};

const submitForm = async () => {
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }
  
  const dataToSend = { ...formData.value };

  loading.value = true;
  try {
    if (formMode.value === 'add') {
      await addCurrency(dataToSend as CurrencyFormData);
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentRow.value) {
      await updateCurrency(currentRow.value.id, dataToSend);
      ElMessage.success('编辑成功');
    }
    handleCloseDialog();
    loadData();
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  } finally {
    loading.value = false;
  }
};

const handleView = (row: CurrencyListItem) => {
  handleOpenForm('view', row);
};

const handleAdd = () => {
  handleOpenForm('add');
};

const handleEdit = (row: CurrencyListItem) => {
  handleOpenForm('edit', row);
};

const handleDelete = async (row: CurrencyListItem) => {
  try {
    await ElMessageBox.confirm(`确定删除币种 "${row.name}" 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    await deleteCurrency(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      const errorMessage = getApiErrorMessage(error);
      ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

const handleFilterChange = (filters: Record<string, any>) => {
    currentFilters.value = filters;
    pagination.currentPage = 1; 
    loadData(); 
};

const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

const handleSortChange = (sort: { prop: string; order: 'ascending' | 'descending' | null }) => {
  currentSort.value = sort;
  loadData(); 
};

// --- 辅助函数：格式化日期时间 ---
const formatDateTime = (dateString: string | null | undefined): string => {
  // Explicitly handle null, undefined, or the zero-date string from backend
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateString);
    // Check if the date is valid AFTER attempting to parse
    if (isNaN(date.getTime())) { 
        console.warn('formatDateTime received invalid date string after initial check:', dateString);
        return '-'; // 无效日期返回占位符
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); 
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    // Log the error and the input string for better debugging
    console.error('Error formatting date:', dateString, '\nError:', e);
    return '-'; // 格式化出错返回占位符
  }
};

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  // 仅当它与 apiError.message (如果存在) 不同或 apiError.message 不存在时添加
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      // const listItemPrefix = detail.field ? `字段 ${detail.field}` : `${index + 1}`;
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

</script> 