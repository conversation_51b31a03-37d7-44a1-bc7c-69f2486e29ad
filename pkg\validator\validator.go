package validator

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"sync"

	// --- 新增导入 ---
	"github.com/go-playground/validator/v10"
	// ----------------
)

// 常量定义
const (
	VALIDATION_TAG_REQUIRED  = "required"  // 必填
	VALIDATION_TAG_EMAIL     = "email"     // 电子邮箱
	VALIDATION_TAG_URL       = "url"       // URL
	VALIDATION_TAG_MAX       = "max"       // 最大值
	VALIDATION_TAG_MIN       = "min"       // 最小值
	VALIDATION_TAG_LEN       = "len"       // 长度
	VALIDATION_TAG_EQ        = "eq"        // 等于
	VALIDATION_TAG_NE        = "ne"        // 不等于
	VALIDATION_TAG_GT        = "gt"        // 大于
	VALIDATION_TAG_GTE       = "gte"       // 大于等于
	VALIDATION_TAG_LT        = "lt"        // 小于
	VALIDATION_TAG_LTE       = "lte"       // 小于等于
	VALIDATION_TAG_ALPHA     = "alpha"     // 字母
	VALIDATION_TAG_ALPHANUM  = "alphanum"  // 字母数字
	VALIDATION_TAG_NUMERIC   = "numeric"   // 数字
	VALIDATION_TAG_BOOLEAN   = "boolean"   // 布尔
	VALIDATION_TAG_DATETIME  = "datetime"  // 日期时间
	VALIDATION_TAG_MOBILE    = "mobile"    // 手机号码
	VALIDATION_TAG_ID_CARD   = "idcard"    // 身份证号码
	VALIDATION_TAG_IP        = "ip"        // IP地址
	VALIDATION_TAG_IPV4      = "ipv4"      // IPv4地址
	VALIDATION_TAG_IPV6      = "ipv6"      // IPv6地址
	VALIDATION_TAG_UUID      = "uuid"      // UUID
	VALIDATION_TAG_ENUM      = "enum"      // 枚举
	VALIDATION_TAG_BASE64    = "base64"    // Base64
	VALIDATION_TAG_JSON      = "json"      // JSON
	VALIDATION_TAG_UNIQUE    = "unique"    // 唯一
	VALIDATION_TAG_LOWERCASE = "lowercase" // 小写
	VALIDATION_TAG_UPPERCASE = "uppercase" // 大写
)

// ValidateFunc 验证函数类型
type ValidateFunc validator.Func

// RegisterCallOption 注册调用选项
type RegisterCallOption struct {
	// 选项具体内容根据需要定义
}

var (
	once             sync.Once
	defaultValidator *CustomValidator
)

// ValidationError 验证错误信息
type ValidationError struct {
	Field     string `json:"field"`     // 字段名
	Tag       string `json:"tag"`       // 标签名
	Value     string `json:"value"`     // 值
	Param     string `json:"param"`     // 参数
	Message   string `json:"message"`   // 错误消息
	Namespace string `json:"namespace"` // 命名空间
}

// ValidationErrors 验证错误集合
type ValidationErrors []ValidationError

// Validator 验证器接口
type Validator interface {
	Struct(s interface{}) ValidationErrors
	Var(field interface{}, tag string) ValidationErrors
	RegisterValidation(tag string, fn ValidateFunc, callOpt ...RegisterCallOption) error
	RegisterAlias(alias, tags string)
}

// CustomValidator 自定义验证器实现
type CustomValidator struct {
	// --- 新增 validator 实例 ---
	validate *validator.Validate
	// -------------------------
}

// 初始化验证器
func Init() {
	once.Do(func() {
		// --- 初始化 go-playground/validator ---
		v := validator.New()

		// 注册获取 json tag 的函数，用于错误信息显示更友好的字段名
		v.RegisterTagNameFunc(func(fld reflect.StructField) string {
			name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
			if name == "-" {
				return ""
			}
			// 如果 json tag 为空，尝试使用 form tag
			if name == "" {
				name = strings.SplitN(fld.Tag.Get("form"), ",", 2)[0]
			}
			// 如果 form tag 也为空，使用原始字段名
			if name == "" {
				name = fld.Name
			}
			return name
		})

		// --- 注册自定义验证函数 ---
		err := v.RegisterValidation(VALIDATION_TAG_MOBILE, validateMobileFunc)
		if err != nil {
			// 处理注册错误，例如 panic 或记录日志
			panic(fmt.Sprintf("failed to register mobile validation: %v", err))
		}
		err = v.RegisterValidation(VALIDATION_TAG_ID_CARD, validateIDCardFunc)
		if err != nil {
			panic(fmt.Sprintf("failed to register idcard validation: %v", err))
		}
		// --- 可在此处注册更多自定义验证 ---

		defaultValidator = &CustomValidator{
			validate: v,
		}
		// ----------------------------------
	})
}

// GetValidator 获取验证器实例
func GetValidator() Validator {
	if defaultValidator == nil {
		Init()
	}
	return defaultValidator
}

// Struct 验证结构体
func (cv *CustomValidator) Struct(s interface{}) ValidationErrors {
	err := cv.validate.Struct(s)
	if err == nil {
		return nil
	}

	// --- 将 validator.ValidationErrors 转换为自定义的 ValidationErrors ---
	validationErrs, ok := err.(validator.ValidationErrors)
	if !ok {
		// 如果不是 validator.ValidationErrors 类型，返回一个通用错误
		// 或者可以根据具体错误类型处理，例如 validator.InvalidValidationError
		return ValidationErrors{
			{Message: fmt.Sprintf("validation error type assertion failed: %T", err)},
		}
	}

	var customErrs ValidationErrors
	for _, fieldErr := range validationErrs {
		// TODO: 实现更友好的错误消息转换逻辑 (例如 i18n)
		// 这里暂时使用 fieldErr.Translate()，如果未注册翻译器，则返回默认英文消息
		customErrs = append(customErrs, ValidationError{
			Field:     fieldErr.Field(),     // 结构体字段名
			Namespace: fieldErr.Namespace(), // 完整字段路径
			Tag:       fieldErr.Tag(),
			Value:     fmt.Sprintf("%v", fieldErr.Value()),
			Param:     fieldErr.Param(),
			Message:   translateFieldError(fieldErr), // 使用翻译函数获取消息
		})
	}
	return customErrs
	// ---------------------------------------------------------------
}

// Var 验证变量
func (cv *CustomValidator) Var(field interface{}, tag string) ValidationErrors {
	err := cv.validate.Var(field, tag)
	if err == nil {
		return nil
	}

	// --- 转换错误 ---
	validationErrs, ok := err.(validator.ValidationErrors)
	if !ok {
		return ValidationErrors{
			{Message: fmt.Sprintf("validation error type assertion failed: %T", err)},
		}
	}

	var customErrs ValidationErrors
	for _, fieldErr := range validationErrs {
		customErrs = append(customErrs, ValidationError{
			// Var 验证时 Field 通常为空或根命名空间
			Field:     fieldErr.Field(),
			Namespace: fieldErr.Namespace(),
			Tag:       fieldErr.Tag(),
			Value:     fmt.Sprintf("%v", fieldErr.Value()),
			Param:     fieldErr.Param(),
			Message:   translateFieldError(fieldErr),
		})
	}
	return customErrs
	// -----------------
}

// RegisterValidation 注册验证函数
func (cv *CustomValidator) RegisterValidation(tag string, fn ValidateFunc, callOpt ...RegisterCallOption) error {
	// 注意：callOpt 在 go-playground/validator 中没有直接对应项，如果需要可以扩展
	return cv.validate.RegisterValidation(tag, validator.Func(fn))
}

// RegisterAlias 注册别名
func (cv *CustomValidator) RegisterAlias(alias, tags string) {
	cv.validate.RegisterAlias(alias, tags)
}

// 验证手机号码
func validateMobileFunc(fl validator.FieldLevel) bool {
	mobile, ok := fl.Field().Interface().(string)
	if !ok {
		// 如果字段类型不是 string，则认为验证失败
		return false
	}
	// 使用之前的正则表达式
	return regexp.MustCompile(`^1[3-9]\d{9}$`).MatchString(mobile)
}

// 验证身份证号码
func validateIDCardFunc(fl validator.FieldLevel) bool {
	idCard, ok := fl.Field().Interface().(string)
	if !ok {
		return false
	}
	// 使用之前的正则表达式
	return regexp.MustCompile(`(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)`).MatchString(idCard)
}

// 简单的错误消息翻译函数
func translateFieldError(err validator.FieldError) string {
	// 这里可以根据 err.Tag() 和 err.Param() 返回自定义的中文错误消息
	switch err.Tag() {
	case "required":
		return fmt.Sprintf("字段 [%s] 是必填项", err.Field())
	case "email":
		return fmt.Sprintf("字段 [%s] 必须是有效的邮箱地址", err.Field())
	case "max":
		return fmt.Sprintf("字段 [%s] 的值不能超过 %s", err.Field(), err.Param())
	case "min":
		return fmt.Sprintf("字段 [%s] 的值不能小于 %s", err.Field(), err.Param())
	case "len":
		return fmt.Sprintf("字段 [%s] 的长度必须是 %s", err.Field(), err.Param())
	case VALIDATION_TAG_MOBILE:
		return fmt.Sprintf("字段 [%s] 必须是有效的手机号码", err.Field())
	case VALIDATION_TAG_ID_CARD:
		return fmt.Sprintf("字段 [%s] 必须是有效的身份证号码", err.Field())
	// ... 添加更多标签的翻译 ...
	default:
		// 返回默认的 validator 错误消息
		// return err.Error() // 或者可以提供一个通用的格式
		return fmt.Sprintf("字段 [%s] 验证失败，规则: %s", err.Field(), err.Tag())
	}
}

// 包级函数，方便直接调用

// Struct 验证结构体
func Struct(s interface{}) ValidationErrors {
	return GetValidator().Struct(s)
}

// Var 验证变量
func Var(field interface{}, tag string) ValidationErrors {
	return GetValidator().Var(field, tag)
}
