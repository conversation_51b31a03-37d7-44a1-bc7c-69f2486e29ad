package integration

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/shopspring/decimal"

	"backend/pkg/logger"
)

// APIConfig defines the structure for a single API provider configuration.
// This matches the structure stored in the system parameter JSON.
type APIConfig struct {
	ProviderName string `json:"providerName"`
	APIURL       string `json:"apiUrl"`
	APIKey       string `json:"apiKey"`
	BaseCurrency string `json:"baseCurrency"`
	Timeout      int    `json:"timeout"` // Timeout in seconds
	Enabled      bool   `json:"enabled"`
}

// ExchangeRateProvider defines the interface for any exchange rate API provider.
type ExchangeRateProvider interface {
	FetchLatestRates() (map[string]decimal.Decimal, error)
}

// --- Implementation for exchangerate-api.com ---

// ExchangeRateAPIComProvider implements the ExchangeRateProvider for exchangerate-api.com.
type ExchangeRateAPIComProvider struct {
	config APIConfig
	client *http.Client
	log    logger.Logger
}

// ExchangeRateAPIComResponse defines the structure of the JSON response from exchangerate-api.com.
type ExchangeRateAPIComResponse struct {
	Result          string                     `json:"result"`
	Documentation   string                     `json:"documentation"`
	TermsOfUse      string                     `json:"terms_of_use"`
	BaseCode        string                     `json:"base_code"`
	ConversionRates map[string]decimal.Decimal `json:"conversion_rates"`
	ErrorType       string                     `json:"error-type"` // Field for errors
}

// NewExchangeRateAPIComProvider creates a new provider instance for exchangerate-api.com.
func NewExchangeRateAPIComProvider(config APIConfig) (ExchangeRateProvider, error) {
	if config.APIKey == "" {
		return nil, fmt.Errorf("API key for %s is required but not provided", config.ProviderName)
	}

	timeout := time.Duration(config.Timeout) * time.Second
	if config.Timeout <= 0 {
		timeout = 10 * time.Second // Default timeout
	}

	return &ExchangeRateAPIComProvider{
		config: config,
		client: &http.Client{
			Timeout: timeout,
		},
		log: logger.GetLogger(),
	}, nil
}

// FetchLatestRates fetches the latest exchange rates from exchangerate-api.com.
func (p *ExchangeRateAPIComProvider) FetchLatestRates() (map[string]decimal.Decimal, error) {
	p.log.Info("Fetching latest exchange rates",
		logger.WithField("provider", p.config.ProviderName),
		logger.WithField("baseCurrency", p.config.BaseCurrency),
	)

	// Construct the full request URL: https://v6.exchangerate-api.com/v6/YOUR-API-KEY/latest/USD
	requestURL := fmt.Sprintf("%s/%s/latest/%s", p.config.APIURL, p.config.APIKey, p.config.BaseCurrency)

	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		p.log.Error("Failed to create new HTTP request", logger.WithError(err))
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		p.log.Error("Failed to execute HTTP request", logger.WithError(err))
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	var apiResponse ExchangeRateAPIComResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		p.log.Error("Failed to decode JSON response", logger.WithError(err))
		return nil, fmt.Errorf("failed to decode JSON: %w", err)
	}

	if apiResponse.Result != "success" {
		p.log.Error("API call was not successful",
			logger.WithField("result", apiResponse.Result),
			logger.WithField("errorType", apiResponse.ErrorType),
		)
		return nil, fmt.Errorf("API error: %s", apiResponse.ErrorType)
	}

	if len(apiResponse.ConversionRates) == 0 {
		p.log.Warn("API call successful but no conversion rates returned")
		return nil, fmt.Errorf("no conversion rates returned from API")
	}

	p.log.Info("Successfully fetched exchange rates", logger.WithField("count", len(apiResponse.ConversionRates)))

	return apiResponse.ConversionRates, nil
}
