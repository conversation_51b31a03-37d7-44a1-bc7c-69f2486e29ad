import request from '@/utils/request'; // 假设请求库路径

// --- TypeScript 类型定义 (基于 Go VO 和 DTO) ---

// 假设基础类型 (如果未在全局定义)
interface BaseQueryDTO {
  pageNum?: number;
  pageSize?: number;
  sort?: string; // 例如 "paramKey,asc"
}

interface BaseVO {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: string | number;
  updatedBy?: string | number;
}

// 系统参数相关类型

export interface SystemParameterCreateDTO {
  paramKey: string;
  paramValue: string;
  name: string;
  remark?: string;
  status?: number; // 使用 number, 对应 *int
  isSystem?: boolean; // 使用 boolean, 对应 *bool
  valueType?: string;
}

export interface SystemParameterUpdateDTO {
  paramValue?: string;
  name?: string;
  remark?: string;
  status?: number;
  valueType?: string;
  // isSystem 不包含在更新 DTO 中
}

export interface SystemParameterQueryDTO extends BaseQueryDTO {
  paramKey?: string;
  name?: string;
  status?: number;
  isSystem?: boolean;
  valueType?: string;
}

export interface SystemParameterVO extends BaseVO {
  paramKey: string;
  paramValue: string;
  name: string;
  remark: string;
  status: number;
  isSystem: boolean;
  valueType: string;
}

export interface PageSystemParameterVO {
  list: SystemParameterVO[];
  total: number;
  pageNum?: number; 
  pageSize?: number;
}

// --- API 请求函数 ---

const API_BASE_URL = '/sys/parameters'; // 后端路由基础路径

// 分页查询系统参数
export function getParameterPage(params: Partial<SystemParameterQueryDTO>): Promise<PageSystemParameterVO> {
  return request.get(`${API_BASE_URL}/page`, { params }); // GET 请求到基础路径
}

// 创建系统参数
export function createParameter(data: SystemParameterCreateDTO): Promise<SystemParameterVO> {
  return request.post(API_BASE_URL, data);
}

// 更新系统参数
export function updateParameter(id: number, data: Partial<SystemParameterUpdateDTO>): Promise<SystemParameterVO> {
  return request.put(`${API_BASE_URL}/${id}`, data);
}

// 删除系统参数
export function deleteParameter(id: number): Promise<void> {
  return request.delete(`${API_BASE_URL}/${id}`);
}

// 获取单个系统参数详情
export function getParameter(id: number): Promise<SystemParameterVO> {
  return request.get(`${API_BASE_URL}/${id}`);
}

// 根据 Key 获取参数值
export async function getSystemParameterByKey(key: string): Promise<string | null> {
  const params: SystemParameterQueryDTO = {
    paramKey: key,
    pageSize: 1, // 我们只需要一条记录
  };
  try {
    const response = await getParameterPage(params);
    return response?.list?.[0]?.paramValue ?? null;
  } catch (error) {
    console.error(`Error fetching system parameter by key "${key}":`, error);
    throw error; // 或者返回 null，取决于错误处理策略
  }
}

// (可选) 根据 Key 获取参数值
// export function getParameterValue(key: string): Promise<string> {
//   // 需要后端提供相应接口，例如 /api/v1/sys/parameters/value/{key}
//   return request.get(`${API_BASE_URL}/value/${key}`).then(res => res.value); 
// } 