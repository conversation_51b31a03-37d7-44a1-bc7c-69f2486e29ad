<template>
  <div class="vn-table-container" ref="tableContainer">
    <!-- 隐藏的文件输入框，用于导入 -->
    <input
      type="file"
      ref="fileInputRef"
      style="display: none"
      accept=".xlsx, .xls"
      @change="HandleFileChange"
    />

    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <el-tooltip v-if="toolbarConfig?.add" content="新增" placement="top">
          <el-button :icon="Plus" circle plain @click="HandleAdd" />
        </el-tooltip>
        <el-tooltip v-if="toolbarConfig?.filter" content="筛选" placement="top">
          <el-button
            :icon="Filter"
            circle
            plain
            :class="{ 'has-filter': hasActiveFilters }"
            @click="showFilterDialog = true"
          />
        </el-tooltip>
        <el-tooltip
          v-if="toolbarConfig?.batchDelete"
          content="批量删除"
          placement="top"
        >
          <el-button
            :icon="Delete"
            circle
            plain
            :disabled="!hasSelection"
            @click="HandleBatchDelete"
          />
        </el-tooltip>

        <!-- 展开按钮 -->
        <el-tooltip
          v-if="toolbarConfig?.expandAll"
          content="展开"
          placement="top"
        >
          <el-button :icon="ArrowDown" circle plain @click="HandleExpandAll" />
        </el-tooltip>

        <!-- 收缩按钮 -->
        <el-tooltip
          v-if="toolbarConfig?.collapseAll"
          content="折叠"
          placement="top"
        >
          <el-button :icon="ArrowUp" circle plain @click="HandleCollapseAll" />
        </el-tooltip>

        <slot name="toolbar-left"></slot>
      </div>
      <div class="toolbar-right">
        <!-- 布局切换 -->
        <el-tooltip
          v-if="toolbarConfig?.layoutSwitch && availableLayoutModes.length > 1"
          content="布局切换"
          placement="top"
        >
          <el-dropdown trigger="click" @command="HandleLayoutChange">
            <el-button :icon="Switch" circle plain />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="mode in availableLayoutModes"
                  :key="mode"
                  :command="mode"
                  :class="{ 'is-active': currentLayoutMode === mode }"
                  @click.stop
                >
                  <el-icon v-if="mode === 'table'"><Grid /></el-icon>
                  <el-icon v-else-if="mode === 'card'"><CreditCard /></el-icon>
                  <el-icon v-else-if="mode === 'list'"><List /></el-icon>
                  {{ getLayoutModeName(mode) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>

        <el-tooltip
          v-if="toolbarConfig?.refresh"
          content="刷新"
          placement="top"
        >
          <el-button :icon="Refresh" circle plain @click="HandleRefresh" />
        </el-tooltip>
        <el-tooltip
          v-if="toolbarConfig?.density"
          content="密度"
          placement="top"
        >
          <el-dropdown trigger="click" @command="HandleDensity">
            <el-button :icon="Grid" circle plain />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="large">宽松</el-dropdown-item>
                <el-dropdown-item command="default">默认</el-dropdown-item>
                <el-dropdown-item command="small">紧凑</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip
          v-if="toolbarConfig?.columnSetting"
          content="列设置"
          placement="top"
        >
          <el-dropdown trigger="click">
            <el-button :icon="Setting" circle plain />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="column in columnsSettable"
                  :key="column.prop"
                >
                  <el-checkbox
                    v-model="column.visible"
                    @change="HandleColumnVisibleChange"
                  >
                    {{ column.label }}
                  </el-checkbox>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip
          v-if="toolbarConfig?.fullscreen"
          content="全屏"
          placement="top"
        >
          <el-button
            :icon="isFullscreen ? Crop : FullScreen"
            circle
            plain
            @click="ToggleFullscreen"
          />
        </el-tooltip>
        <el-tooltip v-if="toolbarConfig?.import" content="导入" placement="top">
          <el-button :icon="Upload" circle plain @click="HandleImport" />
        </el-tooltip>
        <el-tooltip v-if="toolbarConfig?.export" content="导出" placement="top">
          <el-button :icon="Download" circle plain @click="HandleExport" />
        </el-tooltip>
        <slot name="toolbar-right"></slot>
      </div>
    </div>

    <!-- 表格布局模式 -->
    <div v-if="currentLayoutMode === 'table'" class="table-layout">
      <!-- 表格主体 -->
      <el-form
        ref="tableFormRef"
        :model="displayData"
        :rules="tableFormRules"
        class="table-form-wrapper"
      >
        <el-table
          ref="tableRef"
          v-loading="props.loading"
          :data="displayData"
          :row-key="rowKey"
          :empty-text="emptyText"
          :height="tableHeight"
          :max-height="maxHeight"
          :highlight-current-row="highlightCurrentRow"
          :row-class-name="rowClassName"
          :cell-class-name="props.cellClassName"
          :default-expand-all="defaultExpandAll"
          :expand-row-keys="expandRowKeys"
          :tree-props="treeProps"
          :border="border"
          :stripe="stripe"
          :size="tableSize"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          :show-header="showHeader"
          :row-draggable="props.enableDrag && props.dragSort"
          @select="HandleSelect"
          @select-all="HandleSelectAll"
          @selection-change="HandleSelectionChange"
          @sort-change="HandleSortChange"
          @row-click="HandleRowClick"
          @row-dblclick="HandleRowDblclick"
          @row-contextmenu="HandleRowContextmenu"
          @cell-click="HandleCellClick"
          @cell-dblclick="HandleCellDblclick"
          @cell-contextmenu="HandleCellContextmenu"
          @expand-change="HandleExpandChange"
          @row-drag-end="HandleRowDragEnd"
        >
          <!-- 选择列 -->
          <el-table-column
            v-if="selectionType === 'multiple'"
            type="selection"
            width="50"
            align="center"
            fixed="left"
          />

          <!-- 单选列 -->
          <el-table-column
            v-if="selectionType === 'single'"
            width="50"
            align="center"
            fixed="left"
          >
            <template #default="{ row, $index }">
              <el-radio
                v-model="selectedRowKey"
                :label="GetRowKey(row)"
                @change="() => HandleSingleSelect(row)"
              >
                <span></span>
              </el-radio>
            </template>
          </el-table-column>

          <!-- 索引列 -->
          <el-table-column
            v-if="showIndex"
            type="index"
            :label="indexLabel"
            :width="indexWidth"
            align="center"
            fixed="left"
          />

          <!-- 展开列 -->
          <el-table-column
            v-if="hasExpandSlot"
            type="expand"
            width="50"
            fixed="left"
          >
            <template #default="scope">
              <slot name="expand" :row="scope.row" :index="scope.$index"></slot>
            </template>
          </el-table-column>

          <!-- 动态列 -->
          <template v-for="column in mobileOptimizedColumns" :key="column.prop">
            <el-table-column
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :align="column.align || 'left'"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
              :sort-method="column.sortMethod"
              :sort-by="column.sortBy"
              :sort-orders="column.sortOrders"
              :class-name="column.className"
              :header-class-name="column.headerClassName"
              :filters="column.filters"
              :filter-method="(column.filterMethod as any)"
              :filter-multiple="column.filterMultiple"
            >
              <template #default="scope">
                <!-- 自定义插槽 -->
                <template v-if="column.slot">
                  <slot
                    :name="`column-${column.prop}`"
                    :row="scope.row"
                    :column="column"
                    :index="scope.$index"
                    :$index="scope.$index"
                  ></slot>
                </template>

                <!-- 可编辑单元格 (使用 el-form-item) -->
                <template v-else-if="editable && column.editable !== false">
                  <!-- Editing State: Keep ElFormItem for validation during edit -->
                  <template
                    v-if="
                      editMode === 'row'
                        ? IsRowEditing(scope.row)
                        : IsCellEditing(scope.row, column.prop)
                    "
                  >
                    <el-form-item
                      :prop="`[${scope.$index}].${column.prop}`"
                      class="table-form-item"
                      :validate-status="''"
                    >
                      <!-- 输入框 -->
                      <el-input
                        v-if="
                          column.editComponent === 'input' ||
                          !column.editComponent
                        "
                        v-model="scope.row[column.prop]"
                        size="default"
                        v-bind="
                          typeof column.editComponentProps === 'function'
                            ? column.editComponentProps(scope.row)
                            : column.editComponentProps || {}
                        "
                      />
                      <!-- 数字输入框 -->
                      <el-input-number
                        v-else-if="column.editComponent === 'input-number'"
                        v-model="scope.row[column.prop]"
                        size="default"
                        style="width: 100%"
                        controls-position="right"
                        v-bind="
                          typeof column.editComponentProps === 'function'
                            ? column.editComponentProps(scope.row)
                            : column.editComponentProps || {}
                        "
                      />
                      <!-- 选择框 -->
                      <el-select
                        v-else-if="column.editComponent === 'select'"
                        v-model="scope.row[column.prop]"
                        size="default"
                        style="width: 100%"
                        v-bind="
                          typeof column.editComponentProps === 'function'
                            ? column.editComponentProps(scope.row)
                            : column.editComponentProps || {}
                        "
                      >
                        <el-option
                          v-for="option in (typeof column.editComponentProps ===
                          'function'
                            ? column.editComponentProps(scope.row).options
                            : column.editComponentProps?.['options']) || []"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>
                      <!-- 日期选择器 -->
                      <el-date-picker
                        v-else-if="column.editComponent === 'datepicker'"
                        v-model="scope.row[column.prop]"
                        size="default"
                        style="width: 100%"
                        v-bind="
                          typeof column.editComponentProps === 'function'
                            ? column.editComponentProps(scope.row)
                            : column.editComponentProps || {}
                        "
                      />
                      <!-- 开关 -->
                      <el-switch
                        v-else-if="column.editComponent === 'switch'"
                        v-model="scope.row[column.prop]"
                        v-bind="
                          typeof column.editComponentProps === 'function'
                            ? column.editComponentProps(scope.row)
                            : column.editComponentProps || {}
                        "
                      />
                      <!-- 自定义编辑组件 -->
                      <slot
                        v-else-if="column.editComponent === 'custom'"
                        :name="`editor-${column.prop}`"
                        :row="scope.row"
                        :column="column"
                        :index="scope.$index"
                      ></slot>
                    </el-form-item>
                  </template>
                  <!-- Display State: Render directly without ElFormItem -->
                  <template v-else>
                    <span
                      @click="
                        editMode === 'cell' &&
                          HandleCellEdit(scope.row, column.prop)
                      "
                    >
                      {{ FormatCellValue(scope.row, column, scope.$index) }}
                    </span>
                  </template>
                </template>

                <!-- 普通单元格 -->
                <template v-else>
                  {{ FormatCellValue(scope.row, column, scope.$index) }}
                </template>
              </template>
            </el-table-column>
          </template>

          <!-- 操作列 -->
          <el-table-column
            v-if="showOperations"
            :label="operationLabel || '操作'"
            :width="operationWidth || 150"
            :fixed="operationFixed"
            align="center"
          >
            <template #default="scope">
              <slot name="operation" :row="scope.row" :index="scope.$index">
                <!-- 行编辑模式下的保存/取消按钮 -->
                <template
                  v-if="
                    editable && editMode === 'row' && IsRowEditing(scope.row)
                  "
                >
                  <el-tooltip content="保存" placement="top" :enterable="false">
                    <el-button
                      type="primary"
                      circle
                      size="small"
                      @click="HandleSaveRow(scope.row, scope.$index)"
                    >
                      <el-icon><Check /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="取消" placement="top" :enterable="false">
                    <el-button
                      type="danger"
                      circle
                      size="small"
                      @click="HandleCancelEdit(scope.row, scope.$index)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </el-tooltip>
                </template>
                <template v-else>
                  <!-- 编辑按钮 -->
                  <el-tooltip
                    v-if="editable && editMode === 'row'"
                    content="编辑"
                    placement="top"
                    :enterable="false"
                  >
                    <el-button
                      type="primary"
                      circle
                      size="small"
                      @click="HandleEditRow(scope.row)"
                    >
                      <el-icon><Edit /></el-icon>
                    </el-button>
                  </el-tooltip>

                  <!-- 操作按钮 -->
                  <template
                    v-for="(button, buttonIndex) in GetVisibleButtons(
                      scope.row,
                      scope.$index
                    )"
                    :key="buttonIndex"
                  >
                    <el-tooltip
                      :content="button.label"
                      placement="top"
                      :enterable="false"
                    >
                      <el-button
                        :type="(button.type as any) || 'default'"
                        circle
                        size="small"
                        :disabled="
                          GetButtonDisabled(button, scope.row, scope.$index)
                        "
                        @click="
                          HandleButtonClick(button, scope.row, scope.$index)
                        "
                      >
                        <el-icon>
                          <component :is="button.icon || 'More'" />
                        </el-icon>
                      </el-button>
                    </el-tooltip>
                  </template>
                </template>
              </slot>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>

    <!-- 卡片布局模式 -->
    <div
      v-else-if="currentLayoutMode === 'card'"
      class="card-layout"
      v-loading="loading"
    >
      <el-empty
        v-if="!displayData || displayData.length === 0"
        :description="emptyText"
      />
      <el-row v-else :gutter="mergedCardConfig.gutter">
        <el-col
          v-for="(item, index) in displayData"
          :key="GetRowKey(item)"
          :span="24 / mergedCardConfig.column"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="24 / mergedCardConfig.column"
          class="card-item-col"
        >
          <el-card
            :shadow="(mergedCardConfig.shadow as any)"
            :body-style="mergedCardConfig.bodyStyle"
            :class="{ 'is-hoverable': mergedCardConfig.hoverable }"
            @click="HandleCardClick(item, index, $event)"
          >
            <template
              v-if="
                mergedCardConfig.showImage &&
                item[mergedCardConfig.imageField?.toString() || '']
              "
              #cover
            >
              <div
                class="card-image-container"
                :style="{ height: mergedCardConfig.imageHeight + 'px' }"
              >
                <img
                  :src="item[mergedCardConfig.imageField?.toString() || '']"
                  class="card-image"
                  loading="lazy"
                />
              </div>
            </template>

            <template v-if="mergedCardConfig.showHeader" #header>
              <div class="card-header">
                {{ item[mergedCardConfig.headerField?.toString() || ""] }}
              </div>
            </template>

            <div class="card-content">
              <slot name="card-content" :row="item" :index="index">
                <div
                  v-if="
                    item[mergedCardConfig.descriptionField?.toString() || '']
                  "
                  class="card-description"
                >
                  {{
                    item[mergedCardConfig.descriptionField?.toString() || ""]
                  }}
                </div>

                <div class="card-fields">
                  <template
                    v-for="column in visibleCardColumns"
                    :key="column.prop"
                  >
                    <div v-if="column.slot" class="card-field">
                      <div class="card-field-label">{{ column.label }}:</div>
                      <div class="card-field-value">
                        <slot
                          :name="`column-${column.prop}`"
                          :row="item"
                          :column="column"
                          :index="index"
                        ></slot>
                      </div>
                    </div>
                    <div v-else class="card-field">
                      <div class="card-field-label">{{ column.label }}:</div>
                      <div class="card-field-value">
                        {{ FormatCellValue(item, column, index) }}
                      </div>
                    </div>
                  </template>
                </div>
              </slot>
            </div>

            <div v-if="mergedCardConfig.showFooter" class="card-footer">
              <slot name="card-footer" :row="item" :index="index">
                <template v-if="showOperations">
                  <!-- 添加操作按钮区域标题 -->
                  <div class="card-operation-title">操作</div>
                  <!-- 固定显示两个默认操作按钮 -->
                  <el-tooltip content="查看" placement="top">
                    <el-button
                      type="primary"
                      circle
                      size="small"
                      @click.stop="HandleCardView(item, index)"
                    >
                      <el-icon><View /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="编辑" placement="top">
                    <el-button
                      type="warning"
                      circle
                      size="small"
                      @click.stop="HandleCardEdit(item, index)"
                    >
                      <el-icon><Edit /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <!-- 动态操作按钮 -->
                  <template
                    v-for="(button, buttonIndex) in GetVisibleButtons(
                      item,
                      index
                    )"
                    :key="buttonIndex"
                  >
                    <el-tooltip
                      :content="button.label"
                      placement="top"
                      :enterable="false"
                    >
                      <el-button
                        :type="(button.type as any) || 'default'"
                        circle
                        size="small"
                        :disabled="GetButtonDisabled(button, item, index)"
                        @click.stop="HandleButtonClick(button, item, index)"
                      >
                        <el-icon>
                          <component :is="button.icon || 'More'" />
                        </el-icon>
                      </el-button>
                    </el-tooltip>
                  </template>
                </template>
              </slot>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 列表布局模式 -->
    <div
      v-else-if="currentLayoutMode === 'list'"
      class="list-layout"
      v-loading="loading"
      ref="listContainer"
    >
      <el-empty
        v-if="!displayData || displayData.length === 0"
        :description="emptyText"
      />
      <div v-else class="list-container">
        <div
          v-for="(item, index) in displayData"
          :key="GetRowKey(item)"
          class="list-item"
          :data-key="GetRowKey(item)"
          :class="{ 'is-dense': mergedListConfig.dense }"
          :style="{
            /* height: mergedListConfig.itemHeight + 'px' */
          }"
          @click="HandleListItemClick(item, index, $event)"
        >
          <!-- 列表项图片 -->
          <div
            v-if="
              mergedListConfig.showImage &&
              item[mergedListConfig.imageField?.toString() || '']
            "
            class="list-item-image"
          >
            <img
              :src="item[mergedListConfig.imageField?.toString() || '']"
              :style="{
                width: mergedListConfig.imageWidth + 'px',
                height: mergedListConfig.imageHeight + 'px',
                borderRadius: mergedListConfig.imageRadius,
              }"
            />
          </div>

          <!-- 列表项内容 -->
          <div class="list-item-content">
            <slot name="list-item" :row="item" :index="index">
              <div
                v-if="item[mergedListConfig.titleField?.toString() || '']"
                class="list-item-title"
              >
                {{ item[mergedListConfig.titleField?.toString() || ""] }}
              </div>
              <div
                v-if="item[mergedListConfig.subtitleField?.toString() || '']"
                class="list-item-subtitle"
              >
                {{ item[mergedListConfig.subtitleField?.toString() || ""] }}
              </div>
              <div
                v-if="item[mergedListConfig.descriptionField?.toString() || '']"
                class="list-item-description"
              >
                {{ item[mergedListConfig.descriptionField?.toString() || ""] }}
              </div>
            </slot>
          </div>

          <!-- 列表项操作 -->
          <div v-if="showOperations" class="list-item-actions">
            <slot name="list-actions" :row="item" :index="index">
              <!-- 固定显示两个默认操作按钮 -->
              <el-tooltip content="查看" placement="top">
                <el-button
                  type="primary"
                  circle
                  size="small"
                  @click.stop="HandleCardView(item, index)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <el-button
                  type="warning"
                  circle
                  size="small"
                  @click.stop="HandleCardEdit(item, index)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <!-- 动态操作按钮 -->
              <template
                v-for="(button, buttonIndex) in GetVisibleButtons(item, index)"
                :key="buttonIndex"
              >
                <el-tooltip
                  :content="button.label"
                  placement="top"
                  :enterable="false"
                >
                  <el-button
                    :type="(button.type as any) || 'default'"
                    circle
                    size="small"
                    :disabled="GetButtonDisabled(button, item, index)"
                    @click.stop="HandleButtonClick(button, item, index)"
                  >
                    <el-icon>
                      <component :is="button.icon || 'More'" />
                    </el-icon>
                  </el-button>
                </el-tooltip>
              </template>
            </slot>
          </div>

          <div
            v-if="
              mergedListConfig.showDivider && index < displayData.length - 1
            "
            class="list-item-divider"
          ></div>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <div v-if="pagination" class="table-pagination">
      <el-config-provider :locale="zhCn">
        <el-pagination
          v-model:current-page="paginationConfig.currentPage"
          v-model:page-size="paginationConfig.pageSize"
          :page-sizes="paginationConfig.pageSizes || [10, 20, 50, 100]"
          :background="paginationConfig.background"
          :layout="
            paginationConfig.layout || 'total, sizes, prev, pager, next, jumper'
          "
          :total="paginationConfig.total"
          :hide-on-single-page="paginationConfig.hideOnSinglePage"
          @size-change="HandleSizeChange"
          @current-change="HandleCurrentChange"
        />
      </el-config-provider>
    </div>

    <!-- 筛选对话框 -->
    <el-dialog
      v-model="showFilterDialog"
      title="数据筛选"
      :width="props.toolbarConfig?.filterDialogWidth || FILTER_DIALOG_WIDTH"
      align-center
      :close-on-click-modal="false"
      :append-to-body="true"
      destroy-on-close
    >
      <div class="filter-dialog-container">
        <el-scrollbar max-height="350px">
          <div
            v-for="column in filterableColumns"
            :key="column.prop"
            class="filter-item"
          >
            <div class="filter-item-header">
              <span>{{ column.label }}</span>
            </div>

            <!-- 不同类型的筛选控件 -->
            <div class="filter-item-content">
              <!-- 数字类型筛选 -->
              <template v-if="column.filterType === 'number'">
                <div class="filter-number">
                  <el-input-number
                    v-model="filterValues[column.prop].min"
                    :placeholder="'最小值'"
                    size="default"
                    @change="UpdateFilter"
                  />
                  <span class="filter-range-separator">至</span>
                  <el-input-number
                    v-model="filterValues[column.prop].max"
                    :placeholder="'最大值'"
                    size="default"
                    @change="UpdateFilter"
                  />
                </div>
              </template>

              <!-- 日期类型筛选 -->
              <template v-else-if="column.filterType === 'date'">
                <div class="filter-date">
                  <el-date-picker
                    v-model="filterValues[column.prop].dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="default"
                    style="width: 100%"
                    @change="UpdateFilter"
                  />
                </div>
              </template>

              <!-- 选择类型筛选 -->
              <template
                v-else-if="
                  column.filterType === 'select' && column.filterOptions
                "
              >
                <div class="filter-select">
                  <el-select
                    v-model="filterValues[column.prop].selected"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    placeholder="请选择"
                    size="default"
                    style="width: 100%"
                    @change="UpdateFilter"
                  >
                    <el-option
                      v-for="option in column.filterOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </div>
              </template>

              <!-- 默认文本筛选 - 加入操作符选择 -->
              <template v-else>
                <div class="filter-text-complex">
                  <el-select
                    v-model="filterValues[column.prop].operator"
                    placeholder="操作符"
                    size="default"
                    style="width: 120px; margin-right: 8px"
                  >
                    <el-option
                      v-for="op in column.filterOperators
                        ? column.filterOperators
                            .map((opValue) =>
                              DEFAULT_TEXT_FILTER_OPERATORS.find(
                                (o) => o.value === opValue
                              )
                            )
                            .filter((op) => op !== undefined)
                        : DEFAULT_TEXT_FILTER_OPERATORS"
                      :key="op!.value"
                      :label="op!.label"
                      :value="op!.value"
                    />
                  </el-select>
                  <el-input
                    v-model="filterValues[column.prop].text"
                    :placeholder="`搜索${column.label}`"
                    size="default"
                    clearable
                    style="flex: 1"
                    @input="UpdateFilter"
                  />
                </div>
              </template>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="
              ApplyFilters();
              showFilterDialog = false;
            "
            >应用筛选</el-button
          >
          <el-button @click="ClearAllFilters">重置</el-button>
          <el-button @click="showFilterDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  nextTick,
  getCurrentInstance,
  shallowRef,
  onBeforeUnmount,
} from "vue";
import {
  ElTable,
  ElMessage,
  ElMessageBox,
  ElConfigProvider,
} from "element-plus";
import type { FormInstance } from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import * as XLSX from "xlsx";
import { cloneDeep, get as getNestedValue } from "lodash-es"; // <<< Import cloneDeep and getNestedValue
import {
  Refresh,
  Setting,
  Grid,
  FullScreen,
  Crop,
  Download,
  Upload,
  Plus,
  Delete,
  ArrowDown,
  ArrowUp,
  Check,
  Close,
  Edit,
  More,
  Filter,
  List,
  CreditCard,
  Switch,
  View,
} from "@element-plus/icons-vue";
import type {
  VNTableProps,
  TableColumn,
  ActionButton,
  LayoutMode,
  CardConfig,
  ListConfig,
} from "./types";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElRadioGroup,
  ElRadio,
} from "element-plus"; // Added ElRadioGroup, ElRadio
import type { FormItemRule } from "element-plus";
import { h } from "vue"; // Import h function

/**
 * VNTable 组件
 *
 * 一个功能丰富的表格组件，集成了显示、工具栏操作、多种布局（表格、卡片、列表）、
 * 筛选、排序、分页、行内编辑、导入导出和验证等多种功能。
 *
 * @component VNTable
 * @description 提供一个灵活且强大的数据表格。
 *
 * @important **关于验证行为的重要说明:**
 * 当 `editable` 属性为 true 时，由 `ValidateTable` 方法触发的内部验证
 * **仅对当前显示的数据** (会考虑筛选和排序) 进行操作。它使用基于*可见*顺序的行索引。
 * 被筛选掉的行将**不会**被此方法验证。
 *
 * 为了在保存前进行全面的数据完整性检查（尤其是在筛选或排序可能隐藏无效数据的情况下），
 * **强烈建议**在父组件或业务逻辑层中，对**完整、原始的数据集**执行验证，
 * 然后再持久化更改。
 */

// ========= Constants =========
const TABLE_DEFAULT_SIZE = "default";
const TABLE_DEFAULT_HEIGHT = "auto";
const OPERATION_DEFAULT_WIDTH = 150;
const OPERATION_DEFAULT_LABEL = "操作";
const INDEX_DEFAULT_WIDTH = 60;
const INDEX_DEFAULT_LABEL = "序号";
const MOBILE_BREAKPOINT = 768;
const CACHE_SIZE_LIMIT = 1000;
const MAX_RECURSION_DEPTH = 100;
const FILTER_DIALOG_WIDTH = "500px";
const DEFAULT_CARD_CONFIG = {
  column: 3,
  gutter: 16,
  showHeader: true,
  showFooter: true,
  showImage: false,
  imageHeight: 200,
  shadow: "hover",
  hoverable: true,
};
const DEFAULT_LIST_CONFIG = {
  itemHeight: 80,
  showImage: false,
  showDivider: true,
  dense: false,
};
const DEFAULT_TEXT_FILTER_OPERATORS = [
  { value: "contains", label: "包含" },
  { value: "equals", label: "等于" },
  { value: "startsWith", label: "开头是" },
  { value: "endsWith", label: "结尾是" },
  { value: "notContains", label: "不包含" },
  { value: "notEquals", label: "不等于" },
];

// ========= Helper Functions (used early) =========
const debounce = (fn: Function, delay: number) => {
  let timer: any = null;
  return function (this: any, ...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

// ========= Props =========
const props = withDefaults(defineProps<VNTableProps>(), {
  data: () => [],
  columns: () => [],
  loading: false,
  rowKey: "id",
  selectionType: "none",
  defaultExpandAll: false,
  expandRowKeys: () => [],
  treeProps: () => ({}),
  highlightCurrentRow: false,
  showToolbar: true,
  toolbarConfig: () => ({
    refresh: true,
    fullscreen: true,
    columnSetting: true,
    export: true,
    import: true,
    density: true,
    add: true,
    batchDelete: true,
    expandAll: true,
    collapseAll: true,
    filter: true,
    layoutSwitch: true,
  }),
  showOperations: false,
  operationWidth: OPERATION_DEFAULT_WIDTH,
  operationFixed: "right",
  operationLabel: OPERATION_DEFAULT_LABEL,
  operationButtons: () => [],
  emptyText: "暂无数据",
  showIndex: false,
  indexLabel: INDEX_DEFAULT_LABEL,
  indexWidth: INDEX_DEFAULT_WIDTH,
  border: true,
  stripe: true,
  editable: false,
  editMode: "row",
  showHeader: true,
  layoutMode: "table",
  layoutModes: () => ["table", "card", "list"],
  showLayoutSwitch: true,
  cardConfig: () => ({}),
  listConfig: () => ({}),
  autoSwitchLayout: true,
  enableDrag: false,
  dragSort: false,
  resizableColumn: false,
  importColumnMapping: () => ({}),
  singleRowEdit: false, // 新增：默认关闭单行编辑模式
});

// ========= Emits =========
const emit = defineEmits([
  "selection-change",
  "select",
  "select-all",
  "page-change",
  "page-size-change",
  "sort-change",
  "row-click",
  "row-dblclick",
  "row-contextmenu",
  "cell-click",
  "cell-dblclick",
  "cell-contextmenu",
  "cell-edit",
  "row-save",
  "row-cancel",
  "row-delete",
  "refresh",
  "import",
  "export",
  "add",
  "batch-delete",
  "expand-all",
  "collapse-all",
  "filter",
  "filter-clear",
  "layout-change",
  "density-change",
  "column-change",
  "view",
  "edit",
  "expand-change",
  "row-drag-end",
]);

// ========= Refs & Reactives (Internal State - Initialized Early) =========
const isRowSaving = ref(false); // <<< Rename internal loading state
const tableRef = shallowRef<InstanceType<typeof ElTable>>();
const tableFormRef = ref<FormInstance>();
const currentLayoutMode = ref<LayoutMode>(props.layoutMode);
const userPreferredLayout = ref<LayoutMode | null>(props.layoutMode); // <<< 初始化为 props.layoutMode
const filterValues = ref<Record<string, any>>({});
const selectedRowKey = ref<string | number>(""); // <<< Change initial value from null to ''
const selectedRows = shallowRef<any[]>([]);
const editingRows = shallowRef<any[]>([]);
const editingCells = shallowRef<{ rowKey: string; prop: string }[]>([]);
const originalRowData = shallowRef<Record<string | number, any>>({});
const columnsSettable = shallowRef(
  props.columns.map((column) => ({
    ...column,
    visible: column.visible ?? true,
  }))
);
const columnsNeedUpdate = ref(true);
const internalData = ref<any[]>([]); // <<< Add internal data state

// ========= Exposed Functions Definition =========
const HandleRefresh = () => {
  emit("refresh");
};

const HandleImport = () => {
  // 触发隐藏的文件输入框
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

const HandleExport = () => {
  exportTypeDialogActiveChoice.value = "data"; // 重置为默认值

  // 将 messageVNode 的创建封装在一个函数中
  const getMessageVNode = () =>
    h("div", null, [
      h("p", null, "请选择导出类型："),
      h(
        ElRadioGroup,
        {
          modelValue: exportTypeDialogActiveChoice.value,
          "onUpdate:modelValue": (
            newValue: string | number | boolean | undefined
          ) => {
            // 接受 undefined
            console.log(
              "[VNTable HandleExport] Radio selection changed to:",
              newValue
            );
            if (typeof newValue === "string") {
              // 仅当是字符串时更新
              exportTypeDialogActiveChoice.value = newValue;
            }
            // 如果 newValue 不是 string (例如 undefined), exportTypeDialogActiveChoice.value 保持不变
            // 因为我们的 exportTypeDialogActiveChoice 被定义为 ref('data')，期望它总是 string
          },
        },
        {
          default: () => [
            h(
              ElRadio,
              { value: "data" },
              { default: () => "导出数据 (包含标题和数据)" }
            ),
            h(
              ElRadio,
              { value: "template" },
              { default: () => "导出模板 (仅标题)" }
            ),
          ],
        }
      ),
    ]);

  ElMessageBox.confirm(
    getMessageVNode, // 直接传递函数
    "导出选项",
    {
      confirmButtonText: "确定导出",
      cancelButtonText: "取消",
      type: "info",
    }
  )
    .then(() => {
      try {
        const columnsToExport = visibleColumns.value.filter(
          (col) =>
            col.prop &&
            !["selection", "index", "expand", "operation"].includes(
              col.type || ""
            ) &&
            !col.slot
        );
        const header = columnsToExport.map((col) => col.label);

        if (header.length === 0) {
          ElMessage.warning("没有可导出的列");
          return;
        }

        const wb = XLSX.utils.book_new();
        let ws;
        let filenameSuffix = "";

        if (exportTypeDialogActiveChoice.value === "template") {
          // 使用组件级别的 ref
          ws = XLSX.utils.aoa_to_sheet([header]);
          filenameSuffix = "template";
          ElMessage.success("模板导出成功");
        } else {
          // 默认导出数据
          if (!displayData.value || displayData.value.length === 0) {
            ElMessage.warning("没有数据可以导出");
            return;
          }
          const data = displayData.value.map((row, rowIndex) => {
            const rowData: Record<string, any> = {};
            columnsToExport.forEach((col) => {
              rowData[col.label] = FormatCellValue(row, col, rowIndex);
            });
            return rowData;
          });
          ws = XLSX.utils.json_to_sheet(data, {
            header: header,
            skipHeader: false,
          });
          filenameSuffix = "data";
          ElMessage.success("数据导出成功");
        }

        XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
        const filename = `${filenameSuffix}_export_${new Date().getTime()}.xlsx`;
        XLSX.writeFile(wb, filename);

        emit("export", { type: exportTypeDialogActiveChoice.value }); // 使用组件级别的 ref
      } catch (error) {
        handleError(error, "导出数据");
        ElMessage.error("导出失败");
      }
    })
    .catch(() => {
      ElMessage.info("导出已取消");
    });
};

const HandleAdd = () => {
  emit("add");
};

const HandleBatchDelete = () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning("请先选择要删除的数据");
    return;
  }
  ElMessageBox.confirm("确定要批量删除选中的数据吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      emit("batch-delete", selectedRows.value);
    })
    .catch(() => {});
};

const HandleExpandAll = () => {
  if (!tableRef.value) return;
  const expandTreeRows = (rows: any[]) => {
    if (!rows || !rows.length) return;
    rows.forEach((row) => {
      tableRef.value?.toggleRowExpansion(row, true);
      if (row.children && row.children.length > 0) {
        expandTreeRows(row.children);
      }
    });
  };
  nextTick(() => {
    expandTreeRows(internalData.value); // Use internalData
    emit("expand-all");
  });
};

const HandleCollapseAll = () => {
  if (!tableRef.value) return;
  const allRows: any[] = [];
  const collectRows = (rows: any[]) => {
    if (!rows || !rows.length) return;
    rows.forEach((row) => {
      if (row.children && row.children.length > 0) {
        collectRows(row.children);
      }
      allRows.push(row);
    });
  };
  collectRows(internalData.value); // Use internalData
  nextTick(() => {
    allRows.forEach((row) => {
      tableRef.value?.toggleRowExpansion(row, false);
    });
    emit("collapse-all");
  });
};

const HandleLayoutChange = (mode: LayoutMode) => {
  if (currentLayoutMode.value !== mode) {
    nextTick(() => {
      currentLayoutMode.value = mode;
      userPreferredLayout.value = mode;
      emit("layout-change", mode);
    });
  }
};

// --- Filter related exposed functions need applyLocalFilters, checkHasActiveFilters ---
// --- Define applyLocalFilters and checkHasActiveFilters first ---
const checkHasActiveFilters = () => {
  for (const key in filterValues.value) {
    const filter = filterValues.value[key];
    if (
      (filter.text && filter.text.trim() !== "") ||
      (filter.dateRange && filter.dateRange.length === 2) ||
      (filter.min !== undefined && filter.min !== null) ||
      (filter.max !== undefined && filter.max !== null) ||
      (filter.selected && filter.selected.length > 0)
    ) {
      hasActiveFilters.value = true;
      return;
    }
  }
  hasActiveFilters.value = false;
};

// --- Refactored applyLocalFilters ---
// Helper to check if a single row matches a specific filter criteria
const checkRowFilter = (row: any, prop: string, filter: any): boolean => {
  const column = props.columns.find((col) => col.prop === prop);
  if (!column) return true;
  const value = row[prop];

  // Text filter
  if (filter.text && filter.operator) {
    const filterText = filter.text.trim().toLowerCase();
    const cellValueStr = String(value ?? "").toLowerCase();
    let match = false;
    switch (filter.operator) {
      case "contains":
        match = cellValueStr.includes(filterText);
        break;
      case "equals":
        match = cellValueStr === filterText;
        break;
      case "startsWith":
        match = cellValueStr.startsWith(filterText);
        break;
      case "endsWith":
        match = cellValueStr.endsWith(filterText);
        break;
      case "notContains":
        match = !cellValueStr.includes(filterText);
        break;
      case "notEquals":
        match = cellValueStr !== filterText;
        break;
      default:
        match = cellValueStr.includes(filterText);
    }
    if (!match) return false;
  }

  // Date range filter
  if (filter.dateRange && filter.dateRange.length === 2) {
    if (value === null || value === undefined) return false;
    try {
      const dateValue = new Date(value);
      const startDate = new Date(filter.dateRange[0]);
      const endDate = new Date(filter.dateRange[1]);
      endDate.setHours(23, 59, 59, 999);
      if (
        isNaN(dateValue.getTime()) ||
        dateValue < startDate ||
        dateValue > endDate
      ) {
        return false;
      }
    } catch (e) {
      console.warn(`Error parsing date value for filter: ${value}`, e);
      return false;
    }
  }

  // Number range filter (min)
  if (filter.min !== undefined && filter.min !== null) {
    const numValue = Number(value);
    if (
      value === undefined ||
      value === null ||
      isNaN(numValue) ||
      numValue < filter.min
    ) {
      return false;
    }
  }
  // Number range filter (max)
  if (filter.max !== undefined && filter.max !== null) {
    const numValue = Number(value);
    if (
      value === undefined ||
      value === null ||
      isNaN(numValue) ||
      numValue > filter.max
    ) {
      return false;
    }
  }

  // Select filter
  if (filter.selected && filter.selected.length > 0) {
    if (
      value === undefined ||
      value === null ||
      !filter.selected.includes(value)
    ) {
      return false;
    }
  }

  return true; // Row matches this filter criteria
};

// Main filter function, iterates rows and applies all active filters
const applyLocalFilters = () => {
  const activeFilterProps = Object.keys(filterValues.value).filter((prop) => {
    const filter = filterValues.value[prop];
    return (
      (filter.text && filter.text.trim() !== "") ||
      (filter.dateRange && filter.dateRange.length === 2) ||
      (filter.min !== undefined && filter.min !== null) ||
      (filter.max !== undefined && filter.max !== null) ||
      (filter.selected && filter.selected.length > 0)
    );
  });

  if (activeFilterProps.length === 0) {
    filteredData.value = []; // Clear filtered data when no filters active
    hasActiveFilters.value = false;
    // Emit with the full internal data when filters are cleared/inactive
    emit("filter", internalData.value, filterValues.value);
    return;
  }

  // Filter the internalData
  filteredData.value = internalData.value.filter((row) => {
    return activeFilterProps.every((prop) =>
      checkRowFilter(row, prop, filterValues.value[prop])
    );
  });

  hasActiveFilters.value = true;
  // Emit with the filtered subset of internalData
  emit("filter", filteredData.value, filterValues.value);
};

const handleError = (error: any, context: string) => {
  console.error(`VNTable错误 (${context}):`, error);
  ElMessage.error(`操作失败: ${error.message || context}`);
};

const ApplyFilters = () => {
  try {
    // Check internalData length instead of props.data
    if (!internalData.value || internalData.value.length === 0) {
      filteredData.value = [];
      // Determine if filters are actually active even with no data
      hasActiveFilters.value = Object.keys(filterValues.value).some((prop) => {
        const filter = filterValues.value[prop];
        return (
          (filter.text && filter.text.trim() !== "") ||
          (filter.dateRange && filter.dateRange.length === 2) ||
          (filter.min !== undefined && filter.min !== null) ||
          (filter.max !== undefined && filter.max !== null) ||
          (filter.selected && filter.selected.length > 0)
        );
      });
      emit("filter", [], filterValues.value);
      return;
    }
    applyLocalFilters(); // applyLocalFilters now works on internalData
  } catch (error) {
    handleError(error, "应用筛选");
    filteredData.value = [];
    hasActiveFilters.value = false; // Reset filter state on error
  }
};

const ClearAllFilters = () => {
  const filterableCols = props.columns.filter(
    (column) => column.filterable !== false
  );
  filterableCols.forEach((column) => {
    filterValues.value[column.prop] = {
      text: "",
      dateRange: null,
      min: undefined,
      max: undefined,
      selected: [],
      operator:
        column.filterType === "text" && DEFAULT_TEXT_FILTER_OPERATORS.length > 0
          ? DEFAULT_TEXT_FILTER_OPERATORS[0]!.value // <<< Add !
          : undefined,
    };
  });
  hasActiveFilters.value = false;
  filteredData.value = []; // Clear filtered data
  emit("filter-clear");
  // After clearing, emit the full internalData as the current "filtered" view
  emit("filter", internalData.value, filterValues.value);
};

const Reset = () => {
  ClearAllFilters();
  tableRef.value?.clearSort();
  selectedRowKey.value = "";
  selectedRows.value = [];
  tableRef.value?.clearSelection();
  editingRows.value = [];
  editingCells.value = [];
  originalRowData.value = {};
  console.log("VNTable 已重置");
};

// --- Validation Logic ---
/**
 * @description 使用 Element Plus 表单验证机制，校验表格中当前可见且可编辑的数据。
 * 重要提示: 此验证基于 `displayData`（反映了筛选和排序）及其在显示数据中的行索引进行操作。
 * 它不会验证当前被筛选掉的行。
 * 为了在保存前进行全面的数据完整性检查（尤其是在使用了筛选/排序的情况下），
 * 建议在父组件中对完整的原始数据集进行验证。
 * 此方法主要用于在可见的表格单元格内提供交互式的验证反馈。
 * @returns {Promise<boolean>} 一个 Promise，如果所有被校验的字段都通过，则解析为 `true`，否则解析为 `false`。
 * @public
 * @expose
 */
const validateTable = async (): Promise<boolean> => {
  if (!props.editable) {
    return true;
  }

  if (!tableFormRef.value) {
    console.warn("[VNTable] tableFormRef is not available for validation.");
    return true;
  }

  const validationPromises: Promise<void>[] = [];
  const dataToValidate = displayData.value;
  const columnsToValidate = visibleColumns.value;

  for (let i = 0; i < dataToValidate.length; i++) {
    const row = dataToValidate[i];
    for (const column of columnsToValidate) {
      const hasRules = !!column.rules;
      const isEditableImplicitly = column.editable !== false;

      if (hasRules && isEditableImplicitly) {
        const propPath = `[${i}].${column.prop}`;
        // Ensure the promise pushed matches Promise<void>
        const validationPromise = tableFormRef
          .value!.validateField([propPath])
          .then(() => {}) // Map successful validation to void
          .catch((error) => {
            console.warn(
              `[VNTable] Field validation failed for ${propPath}:`,
              error
            );
            throw error; // Re-throw to make Promise.all fail
          });
        validationPromises.push(validationPromise);
      }
    }
  }

  if (validationPromises.length === 0) {
    return true;
  }

  try {
    await Promise.all(validationPromises);
    return true;
  } catch (error: any) {
    console.warn(
      "[VNTable] One or more fields failed validation via validateField. First error:",
      error
    );
    ElMessage.error({
      message: `表格数据校验失败，请检查标红的单元格`,
      duration: 3000,
    });
    return false;
  }
};

/**
 * 切换指定行的编辑状态
 * @param row 要切换编辑状态的行数据
 * @param editing 可选，布尔值。true: 进入编辑状态；false: 取消编辑状态。如果省略，则行为类似于点击编辑按钮（如果未编辑则开始编辑）。
 * @public
 * @expose
 */
const toggleRowEditing = (row: any, editing?: boolean) => {
  const rowKey = GetRowKey(row);
  const currentlyEditing = IsRowEditing(row);

  if (editing === true) {
    if (!currentlyEditing) {
      // Logic from HandleEditRow
      originalRowData.value[rowKey] = cloneDeep(row);
      editingRows.value = [...editingRows.value, row]; // Ensure reactivity
      // Focus first editable cell? (Optional enhancement)
    }
  } else if (editing === false) {
    if (currentlyEditing) {
      // Logic from HandleCancelEdit
      const originalData = originalRowData.value[rowKey];
      if (originalData) {
        Object.assign(row, cloneDeep(originalData));
      } else {
        console.warn("找不到原始行数据，无法恢复：", rowKey);
      }
      editingRows.value = editingRows.value.filter(
        (r: any) => GetRowKey(r) !== rowKey
      );
      editingCells.value = editingCells.value.filter(
        (c) => c.rowKey !== rowKey
      );
      delete originalRowData.value[rowKey];
      // No event emitted here as it's a programmatic toggle off
    }
  } else {
    // If editing param is omitted, toggle based on current state (like clicking edit button)
    if (!currentlyEditing) {
      HandleEditRow(row); // Start editing
    }
    // If already editing, calling without 'false' does nothing
  }
};

// defineExpose 将在文件末尾定义

// ========= Internal State (Refs, Reactives, Computed, Watch) =========
const tableHeight = ref<string | number>(props.height || TABLE_DEFAULT_HEIGHT);
const tableSize = ref<"default" | "small" | "large">(TABLE_DEFAULT_SIZE);
const isFullscreen = ref(false);
const rowKeyCache = new Map();
const cellValueCache = new Map();
const buttonCache = new Map();
const hasActiveFilters = ref(false);
const filteredData = ref<any[]>([]);
const showFilterDialog = ref(false);
const fileInputRef = ref<HTMLInputElement | null>(null); // 文件输入框的 ref
const exportTypeDialogActiveChoice = ref("data"); // 组件级别的 ref

// Computed Properties
const hasSelection = computed(
  () => selectedRows.value && selectedRows.value.length > 0
);
const paginationConfig = computed(() => {
  if (!props.pagination) return { pageSize: 10, currentPage: 1, total: 0 };
  return props.pagination;
});
const displayData = computed(() => {
  if (hasActiveFilters.value) {
    return filteredData.value; // Return pre-filtered data if filters are active
  }
  return internalData.value; // Otherwise, return the full internal data
});
const filterableColumns = computed(() =>
  props.columns.filter((column) => column.filterable !== false)
);
const availableLayoutModes = computed(() => [
  ...(props.layoutModes || ["table"]),
]);
const visibleColumns = computed(() => {
  if (columnsNeedUpdate.value) {
    cachedColumns.value = columnsSettable.value
      .filter((column) => column.visible)
      .filter((column) => {
        if (typeof column.show === "boolean") return column.show;
        if (typeof column.show === "function")
          return column.show({} as any, column, 0);
        return true;
      });
    columnsNeedUpdate.value = false;
  }
  return cachedColumns.value;
});
const cachedColumns = ref<TableColumn[]>([]);
const visibleCardColumns = computed(() =>
  visibleColumns.value.filter(
    (column) =>
      column.type !== "selection" &&
      column.type !== "index" &&
      column.type !== "expand"
  )
);
const mobileOptimizedColumns = computed(() => {
  if (
    window.innerWidth < MOBILE_BREAKPOINT &&
    currentLayoutMode.value === "table"
  ) {
    return visibleColumns.value.filter(
      (column) =>
        column.important || ["name", "title", "id"].includes(column.prop)
    );
  }
  return visibleColumns.value;
});
const mergedCardConfig = computed(() => ({
  ...DEFAULT_CARD_CONFIG,
  ...props.cardConfig,
}));
const mergedListConfig = computed(() => ({
  ...DEFAULT_LIST_CONFIG,
  ...props.listConfig,
}));
const hasExpandSlot = computed(() => !!getCurrentInstance()?.slots["expand"]);

// NEW: Computed property for table form rules
const tableFormRules = computed(() => {
  const rules: Record<string, FormItemRule[]> = {};
  if (!props.editable) return rules; // No rules if not editable

  const data = displayData.value; // Use the same data source as the model
  const columns = visibleColumns.value;

  for (let i = 0; i < data.length; i++) {
    for (const column of columns) {
      if (column.rules && column.editable !== false) {
        // Only for editable columns with rules
        const propPath = `[${i}].${column.prop}`;
        // Ensure rules is always an array
        const columnRules = Array.isArray(column.rules)
          ? column.rules
          : [column.rules];
        rules[propPath] = columnRules;
      }
    }
  }
  // console.log('[VNTable] Generated tableFormRules:', JSON.stringify(rules)); // Optional: for debugging
  return rules;
});

// Watchers
watch(
  filterableColumns,
  (columns) => {
    columns.forEach((column) => {
      if (!filterValues.value[column.prop]) {
        filterValues.value[column.prop] = {
          text: "",
          dateRange: null,
          min: undefined,
          max: undefined,
          selected: [],
          operator:
            column.filterType === "text" &&
            DEFAULT_TEXT_FILTER_OPERATORS.length > 0
              ? DEFAULT_TEXT_FILTER_OPERATORS[0]!.value
              : undefined,
        };
      }
    });
  },
  { immediate: true }
);

watch(
  () => columnsSettable.value,
  () => {
    columnsNeedUpdate.value = true;
  },
  { deep: true }
);

// ADD Watch for external data changes when not using internal fetching
watch(
  () => props.data,
  (newData) => {
    // <<< 移除 ApplyFilters 调用以修复无限循环问题 >>>
    // 当父组件通过 filter 事件驱动数据加载时，不应在此处重新应用筛选
    if (!props.fetchData) {
      console.log("VNTable: props.data changed, updating internalData.");
      // Clear caches that depend on row data identity
      if (cellValueCache) cellValueCache.clear();
      if (buttonCache) buttonCache.clear();

      // Update internal data using deep clone
      internalData.value = cloneDeep(newData || []);

      // // Re-apply filters if they exist based on the new internalData
      // if (hasActiveFilters.value) {
      //   // ApplyFilters(); // <<< REMOVE THIS LINE >>>
      // }

      // Reset pagination ONLY if using frontend pagination and data changes externally
      if (props.useFrontendPagination && paginationConfig.value) {
        paginationConfig.value.currentPage = 1;
      }
    } else {
      console.log(
        "VNTable: props.data changed, but fetchData is enabled, ignoring direct update."
      );
    }
  },
  { deep: true, immediate: true }
);

// ========= Internal Helper Functions =========
const UpdateFilter = debounce(() => {
  checkHasActiveFilters();
}, 300);

const HandleFilterCommand = (command: string) => {
  if (command === "apply") ApplyFilters();
  else if (command === "clear") ClearAllFilters();
};

const getLayoutModeName = (mode: LayoutMode): string => {
  const nameMap: Record<LayoutMode, string> = {
    table: "表格视图",
    card: "卡片视图",
    list: "列表视图",
  };
  return nameMap[mode] || mode;
};

const HandleDensity = (density: "default" | "small" | "large") => {
  nextTick(() => {
    tableSize.value = density;
    emit("density-change", density);
  });
};

const HandleColumnVisibleChange = () => {
  columnsNeedUpdate.value = true;
  nextTick(() => {
    emit(
      "column-change",
      columnsSettable.value.map((col) => ({
        prop: col.prop,
        visible: col.visible,
      }))
    );
  });
};

// --- Helper: Function to manage cache with FIFO eviction ---
const manageCache = (
  cache: Map<any, any>,
  key: any,
  value: any,
  limit: number
) => {
  if (cache.has(key)) {
    cache.delete(key);
  } else if (cache.size >= limit) {
    const oldestKey = cache.keys().next().value;
    if (oldestKey !== undefined) {
      cache.delete(oldestKey);
    }
  }
  cache.set(key, value);
};

// --- Cached Functions with LRU/FIFO ---

const GetRowKey = (row: any): string => {
  const key = row;
  if (rowKeyCache.has(key)) {
    return rowKeyCache.get(key);
  }
  let result: string;
  if (typeof props.rowKey === "function") {
    result = props.rowKey(row);
  } else {
    result = row[(props.rowKey as string) ?? "id"];
  }
  result = String(result);
  manageCache(rowKeyCache, key, result, CACHE_SIZE_LIMIT);
  return result;
};

// Modify FormatCellValue to return original type and use rowKey value for cache
const FormatCellValue = (
  row: any,
  column: TableColumn,
  index: number
): string => {
  // <<< Return type explicitly string
  const rowKeyValue = GetRowKey(row);
  const key = `${rowKeyValue}_${column.prop}`;
  if (cellValueCache.has(key)) {
    // Ensure cached value is also treated as string
    const cachedValue = cellValueCache.get(key);
    return cachedValue === null || cachedValue === undefined
      ? ""
      : String(cachedValue);
  }

  let value: any;
  if (column.formatter) {
    try {
      // Call formatter, assuming it returns string or handleable type
      value = column.formatter(
        row,
        column,
        getNestedValue(row, column.prop),
        index
      );
    } catch (e) {
      console.error(
        `Error executing formatter for column ${column.prop}:`,
        e,
        "Row:",
        row
      );
      value = getNestedValue(row, column.prop); // Fallback to raw value on formatter error
    }
  } else {
    // Use getNestedValue for safe property access
    value = getNestedValue(row, column.prop);
  }

  // --- Restore String Conversion ---
  const result = value === null || value === undefined ? "" : String(value);
  // -------------------------------

  manageCache(cellValueCache, key, result, CACHE_SIZE_LIMIT); // Cache the string result
  return result;
};

// Modify GetVisibleButtons to use rowKey value for cache
const GetVisibleButtons = (row: any, index: number): ActionButton[] => {
  const rowKeyValue = GetRowKey(row); // Get the actual key value
  const key = rowKeyValue; // Use key value as cache key
  if (buttonCache.has(key)) {
    return buttonCache.get(key);
  }
  const buttons = props.operationButtons.filter((button) => {
    if (typeof button.hidden === "function") return !button.hidden(row, index);
    return !button.hidden;
  });
  manageCache(buttonCache, key, buttons, CACHE_SIZE_LIMIT);
  return buttons;
};

// --- Table Event Handlers ---
const HandleSelect = (selection: any[], row: any) => {
  emit("select", selection, row);
};
const HandleSelectAll = (selection: any[]) => {
  emit("select-all", selection);
};
const HandleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
  emit("selection-change", selection);
};
const HandleSortChange = (sort: any) => {
  emit("sort-change", sort);
};
const HandleRowClick = (row: any, column: any, event: Event) => {
  emit("row-click", row, column, event);
};
const HandleRowDblclick = (row: any, column: any, event: Event) => {
  emit("row-dblclick", row, column, event);
};
const HandleRowContextmenu = (row: any, column: any, event: Event) => {
  emit("row-contextmenu", row, column, event);
};
const HandleCellClick = (row: any, column: any, cell: any, event: Event) => {
  emit("cell-click", row, column, cell, event);
};
const HandleCellDblclick = (row: any, column: any, cell: any, event: Event) => {
  emit("cell-dblclick", row, column, cell, event);
};
const HandleCellContextmenu = (
  row: any,
  column: any,
  cell: any,
  event: Event
) => {
  emit("cell-contextmenu", row, column, cell, event);
};
const HandleExpandChange = (row: any, expanded: boolean) => {
  emit("expand-change", row, expanded);
};
const HandleRowDragEnd = (
  draggingRecord: any,
  newIndex: number,
  oldIndex: number
) => {
  emit("row-drag-end", draggingRecord, newIndex, oldIndex);
};
const HandleSingleSelect = (row: any) => {
  selectedRowKey.value = GetRowKey(row);
  selectedRows.value = [row];
  emit("selection-change", [row]);
  emit("select", [row], row);
};

// --- Editing related internal functions ---
// REMOVED original GetRowKey here
const IsRowEditing = (row: any): boolean =>
  editingRows.value.some((r: any) => GetRowKey(r) === GetRowKey(row));
const IsCellEditing = (row: any, prop: string): boolean =>
  editingCells.value.some(
    (c: { rowKey: string; prop: string }) =>
      c.rowKey === GetRowKey(row) && c.prop === prop
  );
const HandleCellEdit = (row: any, prop: string) => {
  if (!IsCellEditing(row, prop)) {
    editingCells.value.push({ rowKey: GetRowKey(row), prop });
  }
};
const HandleEditRow = (row: any) => {
  // 如果当前行已在编辑，则不执行任何操作
  if (IsRowEditing(row)) {
    return;
  }

  // 如果开启了单行编辑模式，先取消所有其他正在编辑的行
  if (props.singleRowEdit) {
    // 创建一个当前编辑行的副本进行遍历，以避免在迭代时修改原始数组
    [...editingRows.value].forEach((editingRow) => {
      // 调用取消编辑的函数，它会处理数据恢复和状态清理
      HandleCancelEdit(editingRow, -1); // -1 作为索引表示非用户交互触发
    });
  }

  const rowKey = GetRowKey(row);
  // 使用 cloneDeep 从 lodash-es
  originalRowData.value[rowKey] = cloneDeep(row);
  if (!IsRowEditing(row)) {
    // editingRows.value.push(row); // <<< Incorrect way for shallowRef
    editingRows.value = [...editingRows.value, row]; // <<< Correct way: create a new array
  }
};
const HandleSaveRow = async (row: any, index: number) => {
  const rowKey = GetRowKey(row);
  const oldRow = originalRowData.value[rowKey];

  const isValid = await validateTableRow(row);
  if (!isValid) {
    ElMessage.warning("请修正错误后再保存。");
    return;
  }

  isRowSaving.value = true;
  try {
    // Simulate API save delay (can be removed if not needed)
    // await new Promise(resolve => setTimeout(resolve, 400));

    emit("row-save", row, index, oldRow); // Emit first

    await nextTick(); // <<< Wait for Vue's next update cycle

    // <<< 尝试添加第二个 nextTick，给渲染更多时间 >>>
    await nextTick();

    // Now exit edit mode
    editingRows.value = editingRows.value.filter(
      (r: any) => GetRowKey(r) !== rowKey
    );
    delete originalRowData.value[rowKey];

    // <<< 尝试强制表格重新布局 (直接调用) >>>
    if (tableRef.value) {
      tableRef.value.doLayout();
    } else {
      console.warn(
        `[VNTable LOG] HandleSaveRow - tableRef not available to call doLayout()`
      );
    }
  } catch (error) {
    handleError(error, "保存行数据时出错");
    // Optionally handle save failure (e.g., keep row in editing state?)
  } finally {
    isRowSaving.value = false;
  }
};

// Helper function to validate a single row
const validateTableRow = async (row: any): Promise<boolean> => {
  if (!tableFormRef.value) return true;
  const rowIndex = displayData.value.findIndex(
    (item) => GetRowKey(item) === GetRowKey(row)
  );
  if (rowIndex === -1) return true; // Row not visible?

  const rowValidationPromises: Promise<void>[] = [];
  for (const column of visibleColumns.value) {
    if (column.rules && column.editable !== false) {
      const propPath = `[${rowIndex}].${column.prop}`;
      const validationPromise = tableFormRef
        .value!.validateField([propPath])
        .then(() => {})
        .catch((error) => {
          console.warn(
            `[VNTable] Row validation failed for ${propPath}:`,
            error
          );
          throw error;
        });
      rowValidationPromises.push(validationPromise);
    }
  }
  if (rowValidationPromises.length === 0) return true;

  try {
    await Promise.all(rowValidationPromises);
    return true;
  } catch {
    return false;
  }
};

const HandleCancelEdit = (row: any, index: number) => {
  const rowKey = GetRowKey(row);
  const originalData = originalRowData.value[rowKey];

  // 判断是否是新增的行（通过 tempId 或其他标识）
  const isNewRow =
    String(rowKey).startsWith("temp_") ||
    (row.tempId && String(row.tempId).startsWith("temp_")) ||
    (row.id && String(row.id).startsWith("temp_"));

  if (!isNewRow && originalData) {
    // 已存在的行：恢复到原始状态
    Object.assign(row, cloneDeep(originalData));
    // Clear validation errors for this row after canceling
    if (tableFormRef.value) {
      const rowIndex = displayData.value.findIndex(
        (item) => GetRowKey(item) === GetRowKey(row)
      );
      if (rowIndex !== -1) {
        visibleColumns.value.forEach((column) => {
          if (column.rules && column.editable !== false) {
            tableFormRef.value!.clearValidate([`[${rowIndex}].${column.prop}`]);
          }
        });
      }
    }
    // 清理编辑状态
    editingRows.value = editingRows.value.filter(
      (r: any) => GetRowKey(r) !== rowKey
    );
    editingCells.value = editingCells.value.filter((c) => c.rowKey !== rowKey);
    delete originalRowData.value[rowKey];
    emit("row-cancel", row, index);
  } else {
    // 新增的空行：通知父组件删除这一行
    console.log("[VNTable] 取消新增的空行，通知父组件删除该行：", rowKey, row);

    // 清理编辑状态
    editingRows.value = editingRows.value.filter(
      (r: any) => GetRowKey(r) !== rowKey
    );
    editingCells.value = editingCells.value.filter((c) => c.rowKey !== rowKey);
    delete originalRowData.value[rowKey];

    // 发出删除事件，让父组件处理数据删除
    emit("row-delete", row, index);
  }
};

// --- Button/Card/List Click Handlers ---
const HandleButtonClick = (button: ActionButton, row: any, index: number) => {
  if (button.handler) button.handler(row, index);
  else if (button.action) emit(button.action as any, row, index);
};
const HandleCardClick = (row: any, index: number, event: Event) => {
  emit("row-click", row, { type: "card" }, event);
};
const HandleCardView = (row: any, index: number) => {
  emit("view", row, index);
};
const HandleCardEdit = (row: any, index: number) => {
  emit("edit", row, index);
};
const HandleListItemClick = (row: any, index: number, event: Event) => {
  emit("row-click", row, { type: "list" }, event);
};

// --- Pagination Handlers ---
const HandleSizeChange = (size: number) => {
  emit("page-size-change", size);
};
const HandleCurrentChange = (page: number) => {
  emit("page-change", page);
};

// --- Other Helpers ---
const GetButtonDisabled = (
  button: ActionButton,
  row: any,
  index: number
): boolean => {
  if (typeof button.disabled === "function") return button.disabled(row, index);
  return !!button.disabled;
};

// --- Restore accidentally deleted functions ---
const adjustLayoutForScreenSize = () => {
  if (!props.autoSwitchLayout) return;
  const width = window.innerWidth;
  if (width < MOBILE_BREAKPOINT && currentLayoutMode.value !== "card") {
    // 窄屏强制卡片
    currentLayoutMode.value = "card";
  } else if (width >= MOBILE_BREAKPOINT && currentLayoutMode.value === "card") {
    // <<< 只在当前是卡片时尝试恢复
    // 宽屏时恢复到用户偏好或默认
    currentLayoutMode.value = userPreferredLayout.value || props.layoutMode;
  }
};

const handleWindowResize = debounce(adjustLayoutForScreenSize, 200);

const ToggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  if (!tableContainer.value) return;
  if (isFullscreen.value) {
    if (tableContainer.value.requestFullscreen)
      tableContainer.value.requestFullscreen();
    tableContainer.value.classList.add("fullscreen");
  } else {
    if (document.exitFullscreen) document.exitFullscreen();
    tableContainer.value.classList.remove("fullscreen");
  }
};

// --- Refactored HandleFileChange ---
// Helper to map a single Excel row to a data object based on header and mapping
const mapExcelRowToObject = (
  rowData: any[],
  headerRowValues: (string | undefined)[],
  columnMapping: Record<string, string> | undefined,
  tableColumns: TableColumn[]
): { rowObject: Record<string, any>; isEmpty: boolean } => {
  const rowObject: Record<string, any> = {};
  let isEmptyRow = true;

  for (let C = 0; C < headerRowValues.length; ++C) {
    const excelHeader = headerRowValues[C];
    if (excelHeader === undefined || excelHeader === null) continue;

    const cellValue = rowData[C];

    if (cellValue !== undefined && cellValue !== null && cellValue !== "") {
      isEmptyRow = false;
    }

    let propName: string | undefined = undefined;

    if (columnMapping && columnMapping[excelHeader]) {
      propName = columnMapping[excelHeader];
    } else {
      const columnConfig = tableColumns.find(
        (col) => col.label === excelHeader
      );
      if (columnConfig && columnConfig.prop) {
        propName = columnConfig.prop;
      }
    }

    if (propName) {
      rowObject[propName] = cellValue;
    } else {
      if (cellValue !== undefined && cellValue !== null && cellValue !== "") {
        console.warn(
          `[VNTable Import] 未能将 Excel 列 "${excelHeader}" 映射到任何表格属性 (prop)，已跳过此单元格的值。请检查 importColumnMapping prop 或确保列 label 匹配。`
        );
      }
    }
  }
  return { rowObject, isEmpty: isEmptyRow };
};

// Main file change handler
const HandleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;

  const file = input.files[0];
  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const data = e.target?.result;
      if (!data) throw new Error("无法读取文件内容");
      const workbook = XLSX.read(data, { type: "binary" });
      const firstSheetName = workbook.SheetNames[0];
      // <<< 添加检查 >>>
      if (!firstSheetName) {
        ElMessage.warning("无法找到 Excel 文件中的工作表");
        return;
      }
      const worksheet = workbook.Sheets[firstSheetName];
      if (!worksheet) {
        // <<< 额外检查 worksheet 是否有效
        ElMessage.warning("无法读取工作表内容");
        return;
      }

      const excelData: any[][] = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      });

      // <<< 修改检查条件 >>>
      if (!excelData || excelData.length === 0) {
        ElMessage.warning("导入的文件为空或格式不正确");
        return;
      }

      // <<< 添加对 excelData[0] 的检查 >>>
      if (!excelData[0]) {
        ElMessage.warning("导入的文件缺少表头行");
        return;
      }
      const headerRowValues = excelData[0].map((header) =>
        header ? String(header) : undefined
      );
      const dataRows = excelData.slice(1);

      const jsonDataMapped: any[] = [];
      for (const rowData of dataRows) {
        const { rowObject, isEmpty } = mapExcelRowToObject(
          rowData,
          headerRowValues,
          props.importColumnMapping,
          props.columns
        );
        if (!isEmpty) {
          jsonDataMapped.push(rowObject);
        }
      }

      if (jsonDataMapped.length === 0) {
        ElMessage.warning("导入的文件中没有有效数据或未能成功映射");
        return;
      }

      ElMessage.success(`成功映射并准备导入 ${jsonDataMapped.length} 条数据`);
      emit("import", jsonDataMapped);
    } catch (error) {
      handleError(error, "导入数据失败");
      ElMessage.error("文件解析或数据映射失败，请检查文件格式和映射配置");
    } finally {
      if (input) input.value = "";
    }
  };

  // <<< 添加检查 >>>
  if (file) {
    reader.readAsBinaryString(file);
  } else {
    console.error("HandleFileChange: file is undefined or null");
    ElMessage.error("无法获取文件，请重试");
    if (input) input.value = ""; // 确保在出错时也清空输入
  }
};

// ========= Lifecycle Hooks =========
onMounted(() => {
  window.addEventListener("resize", handleWindowResize);
  adjustLayoutForScreenSize();
});

onBeforeUnmount(() => {
  rowKeyCache.clear();
  cellValueCache.clear();
  buttonCache.clear();
  window.removeEventListener("resize", handleWindowResize);
});

// ========= Template Refs =========
const tableContainer = ref<HTMLElement | null>(null);
const listContainer = ref<HTMLElement | null>(null);

// ========= defineExpose =========
defineExpose({
  Refresh: HandleRefresh,
  Reset: Reset,
  ImportTable: HandleImport,
  ExportTable: HandleExport,
  Add: HandleAdd,
  BatchDelete: HandleBatchDelete,
  ExpandAll: HandleExpandAll,
  CollapseAll: HandleCollapseAll,
  SwitchLayout: HandleLayoutChange,
  GetCurrentLayout: () => currentLayoutMode.value,
  ApplyFilters: ApplyFilters,
  ClearAllFilters: ClearAllFilters,
  GetFilterValues: () => filterValues.value,
  GetSelectionRows: () => selectedRows.value,
  ClearSelection: () => tableRef.value?.clearSelection(),
  ToggleRowSelection: (row: any, selected?: boolean) =>
    tableRef.value?.toggleRowSelection(row, selected),
  ToggleAllSelection: () => tableRef.value?.toggleAllSelection(),
  ToggleRowExpansion: (row: any, expanded?: boolean) =>
    tableRef.value?.toggleRowExpansion(row, expanded),
  Sort: (prop: string, order: "ascending" | "descending" | null) => {
    if (prop && tableRef.value) {
      tableRef.value.sort(prop, (order === null ? undefined : order) as any);
    }
  },
  // 获取当前表格显示的数据
  GetData: () => {
    return displayData.value;
  },
  // 重置表格状态
  resetData: Reset, // Alias Reset to resetData for expose
  // Potentially expose other useful methods from el-table
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: any, selected?: boolean) =>
    tableRef.value?.toggleRowSelection(row, selected),
  toggleAllSelection: () => tableRef.value?.toggleAllSelection(),
  toggleRowExpansion: (row: any, expanded?: boolean) =>
    tableRef.value?.toggleRowExpansion(row, expanded),
  setCurrentRow: (row?: any) => tableRef.value?.setCurrentRow(row),
  clearSort: () => tableRef.value?.clearSort(),
  clearFilter: (columnKeys?: string | string[]) => {
    const keysArray = columnKeys
      ? Array.isArray(columnKeys)
        ? columnKeys
        : [columnKeys]
      : undefined;
    tableRef.value?.clearFilter(keysArray);
  },
  doLayout: () => tableRef.value?.doLayout(),
  sort: (prop: string, order: string) => tableRef.value?.sort(prop, order),
  scrollToRow: (row: any) => {
    /* Logic needed */
  },
  ValidateTable: validateTable, // Expose the validation method with documentation
  HandleEditRow: HandleEditRow, // <<< 暴露编辑行方法
});
</script>

<style scoped>
/* Use :deep() to forcefully override el-form-item internal styles */
:deep(.table-form-wrapper .el-table .el-table__cell .el-form-item) {
  margin-bottom: 0 !important; /* Force remove margin */
}

:deep(.table-form-wrapper .el-table .el-table__cell .el-form-item__content) {
  line-height: normal !important; /* Force override fixed line-height */
  margin-bottom: 2px !important; /* Force margin below content */
}

:deep(.table-form-wrapper .el-table .el-table__cell .el-form-item__error) {
  position: relative !important; /* <<< Force relative positioning */
  font-size: 12px !important; /* <<< Change font size to 12px */
  line-height: 1.4 !important; /* <<< Force line height */
  color: var(--el-color-danger) !important; /* Force color */
  padding-top: 0 !important; /* Force padding */
  display: block !important; /* Force display block */
  bottom: auto !important; /* Reset absolute positioning props */
  left: auto !important;
  top: auto !important;
  min-height: 1em !important; /* Force min-height */
}

/* Keep cell padding */
.table-form-wrapper .el-table .el-table__cell {
  padding-top: 8px;
  padding-bottom: 24px;
}

/* General VNTable styles */
.vn-table-container {
  width: 100%;
  position: relative; /* Needed for fullscreen */
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-wrap: wrap; /* <<< Allow toolbar sections to wrap */
  row-gap: 8px; /* <<< Add gap between wrapped rows */
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap; /* <<< Allow items within sections to wrap */
  row-gap: 8px; /* <<< Add gap for wrapped items */
}

/* 添加按钮固定样式 */
.toolbar-left :deep(.el-button),
.toolbar-right :deep(.el-button) {
  width: 32px;
  height: 32px;
  padding: 0;
  margin: 0;
  font-size: 16px;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

/* 保持图标居中对齐 */
.toolbar-left :deep(.el-button .el-icon),
.toolbar-right :deep(.el-button .el-icon) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.el-table {
  flex: 1;
}

.cell-editor {
  padding: 0 8px;
}

.table-pagination {
  padding: 8px 16px;
  /* text-align: right; (Removed as flex handles alignment) */
  border-top: 1px solid #ebeef5;
  display: flex; /* <<< Ensure pagination container is flex */
  flex-wrap: wrap; /* <<< Allow pagination elements to wrap */
  justify-content: flex-end; /* <<< Default to right alignment */
  row-gap: 8px; /* <<< Add gap for wrapped elements */
}

/* Keep deep selector for inner el-pagination if needed, but flex-wrap on container might suffice */
.table-pagination :deep(.el-pagination) {
  /* justify-content: flex-end; (Now handled by container) */
  /* display: flex; (Already flex by default probably) */
  flex-wrap: wrap; /* <<< Ensure inner elements wrap too */
  /* width: 100%; (Remove this to allow container justify-content to work) */
}

/* Center pagination on smaller screens */
@media (max-width: 768px) {
  .table-pagination {
    justify-content: center;
  }
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

/* 全屏样式 */
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #fff;
}

.filter-dialog-container {
  padding: 0 10px;
}

.filter-item {
  margin-bottom: 16px;
}

.filter-item-header {
  margin-bottom: 8px;
  font-weight: bold;
}

.filter-number {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-range-separator {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

/* 布局模式样式 */
.card-layout {
  padding: 16px;
}

.card-item-col {
  margin-bottom: 16px;
}

.is-hoverable:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card-image-container {
  overflow: hidden;
  position: relative;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  margin: 12px 0;
}

.card-description {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.card-fields {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-field {
  display: flex;
  align-items: flex-start;
}

.card-field-label {
  width: 80px;
  color: #909399;
  font-size: 13px;
  margin-right: 8px;
  flex-shrink: 0;
}

.card-field-value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.card-footer {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.card-operation-title {
  color: #909399;
  font-size: 14px;
  margin-right: auto;
}

/* 列表样式 */
.list-layout {
  padding: 0;
}

.list-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  position: relative;
  cursor: pointer;
}

.list-item:hover {
  background-color: #f5f7fa;
}

.list-item.is-dense {
  padding: 8px 16px;
}

.list-item-image {
  margin-right: 16px;
  flex-shrink: 0;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
  color: #303133;
}

.list-item-subtitle {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.list-item-description {
  font-size: 13px;
  color: #909399;
}

.list-item-actions {
  margin-left: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.list-item-divider {
  position: absolute;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 1px;
  background-color: #ebeef5;
}

/* 布局切换菜单激活态 */
.el-dropdown-menu :deep(.is-active) {
  color: var(--el-color-primary);
  font-weight: bold;
}

/* 卡片底部操作按钮区域样式 */
.card-footer {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.card-operation-title {
  color: #909399;
  font-size: 14px;
  margin-right: auto;
}

/* 列表项操作区域样式 */
.list-item-actions {
  margin-left: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 确保操作区域中的按钮保持一致的尺寸 */
.card-footer :deep(.el-button),
.list-item-actions :deep(.el-button) {
  width: 32px;
  height: 32px;
  padding: 0;
  margin: 0;
  font-size: 16px;
}

/* 保持图标居中对齐 */
.card-footer :deep(.el-button .el-icon),
.list-item-actions :deep(.el-button .el-icon) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* 复杂文本筛选样式 */
.filter-text-complex {
  display: flex;
  align-items: center;
}

/* 新增：用于控制单元格内容不换行的样式 */
:deep(.cell-no-wrap .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
