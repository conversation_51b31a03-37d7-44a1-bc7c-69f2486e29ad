# VNForm 通用表单组件

这是一个基于 Vue 3 Composition API 和 TypeScript 构建的通用表单组件，集成了 Element Plus 的表单和表格组件。

## 特性

- **多种模式**: 支持仅表头模式和表头+多明细表模式。
- **灵活配置**: 通过 `headerFields` (表头) 和 `detailTables` (明细表) props 进行灵活配置。
- **数据绑定**: 使用 `v-model` 绑定完整的表单数据 (包括表头和所有明细表数据)。
- **表单布局**:
  - 支持字段分组显示 (通过 `group` 属性和 `groupTitleType` 控制样式)。
  - 支持响应式布局 (通过 `md`, `lg`, `xl` 等属性控制不同屏幕尺寸下的栅格宽度)。
- **动态表单**: 支持字段的动态可见性 (通过 `visible` 属性，可绑定函数)。
- **丰富的字段类型**: 内置支持多种常用输入类型 (text, textarea, number, select, date, radio, checkbox-group, checkbox, upload)。
- **自定义字段**: 支持通过插槽 (`form-item-[field]`) 渲染自定义表单项。
- **明细表集成**:
  - 可嵌套一个或多个 `VNTable` 组件作为明细表。
  - 自动合并默认的 `VNTable` 配置，方便使用。
  - 通过 `onRowAdd` 和 `onRowDelete` 回调处理明细表的增删逻辑。
- **表单校验**:
  - 集成 Element Plus 的表单校验规则。
  - 支持对表头和所有可编辑的明细表进行统一校验 (`validateForm` 方法)。
  - 提供校验加载状态的视觉反馈。
- **事件**: 提供 `submit`, `cancel`, `change` 等事件。
- **方法**: 提供 `validateForm`, `resetForm`, `clearValidate`, `scrollToField` 等常用方法。
- **自定义操作**: 支持通过 `actions` 插槽自定义底部的操作按钮。
- **状态控制**: 支持 `loading` 和 `disabled` 状态。

## 使用

### 1. 引入组件

```vue
<script setup lang="ts">
import VNForm from '@/components/VNForm/index.vue';
import type { HeaderField, DetailTableConfig, VNFormMethods } from '@/components/VNForm/types';
import { ref } from 'vue';

// ... setup logic
const vnFormRef = ref<InstanceType<typeof VNForm>>();
</script>
```

### 2. Props (`VNFormProps`)

| 属性名              | 类型                                         | 默认值                  | 说明                                                                                                      |
| ------------------- | -------------------------------------------- | ----------------------- | --------------------------------------------------------------------------------------------------------- |
| `title`             | `String`                                     | `''`                    | 表单顶部显示的标题                                                                                            |
| `headerFields`      | `Array<HeaderField>`                         | `[]`                    | 定义表头字段的数组                                                                                            |
| `modelValue`        | `Record<string, any>`                        | `{}`                    | **(v-model)** 绑定的表单数据对象，应包含表头字段和名为 `details` 的明细数据数组 (`{ headerField1: ..., details: [ [...], [...] ] }`) |
| `detailTables`      | `Array<DetailTableConfig>`                   | `[]`                    | 定义明细表的数组。每个对象包含 `title` 和 `tableProps` (透传给 VNTable)                                        |
| `defaultColumns`    | `Number`                                     | `2`                     | **(新增)** 表头区域默认的栅格布局列数 (例如 2 或 3)。字段配置中的 `md`/`lg`/`xl` 优先级更高。         |
| `groupTitleType`    | `'h4' \| 'divider' \| 'none'`                | `'h4'`                  | 表单字段分组的标题显示样式                                                                                    |
| `labelWidth`        | `String` / `Number`                          | `'100px'`               | 表单项标签宽度                                                                                              |
| `labelPosition`     | `'left' \| 'right' \| 'top'`                 | `'right'`               | 表单项标签位置                                                                                              |
| `size`              | `'large' \| 'default' \| 'small'`            | `'default'`             | 表单内组件尺寸                                                                                              |
| `inline`            | `Boolean`                                    | `false`                 | 是否开启行内表单模式 (不推荐与复杂布局或明细表混用)                                                           |
| `disabled`          | `Boolean`                                    | `false`                 | 是否禁用整个表单                                                                                              |
| `showSubmitButtons` | `Boolean`                                    | `true`                  | 是否显示默认的提交和取消按钮组 (可通过 `actions` 插槽覆盖)                                                    |
| `submitButtonText`  | `String`                                     | `'提交'`                | 默认提交按钮的文本                                                                                            |
| `cancelButtonText`  | `String`                                     | `'取消'`                | 默认取消按钮的文本 (点击时触发 `resetForm`)                                                                  |
| `loading`           | `Boolean`                                    | `false`                 | 是否显示表单加载/提交状态                                                                                     |
| `onRowAdd`          | `Function`                                   |                         | `(tableIndex: number) => Promise<Record<string, any> \| null> \| Record<string, any> \| null` 添加明细行时的回调函数，返回新行数据或 null |
| `onRowDelete`       | `Function`                                   |                         | `(tableIndex: number, row: any, rowIndex: number) => Promise<boolean> \| boolean` 删除明细行时的回调函数，返回是否确认删除 |

### 3. 类型定义 (`types.ts`)

```typescript
import type { FormItemRule } from 'element-plus';
import type { VNTableProps } from '../VNTable/types';

export interface HeaderField {
  field: string;               // 字段名
  label: string;               // 字段标签
  type?: 'text' | 'textarea' | 'number' | 'select' | 'date' | 'radio' | 'checkbox-group' | 'checkbox' | 'upload' | 'slot';
  group?: string;             // 分组名称
  placeholder?: string;
  span?: number;              // 栅格宽度 (基础)
  xs?: number;                // 响应式布局
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  disabled?: boolean;
  clearable?: boolean;
  options?: Array<{ value: any; label: string; disabled?: boolean }>; // select, radio, checkbox-group 选项
  props?: Record<string, any>; // 透传给底层组件的属性 (如 el-input 的 props)
  rules?: FormItemRule[];     // 验证规则
  visible?: boolean | ((form: Record<string, any>) => boolean); // 动态显示
  // ... (其他特定类型属性如 min, max, rows, format, action 等)
}

export interface DetailTableConfig {
  title?: string;
  tableProps: Partial<VNTableProps>; // 透传给 VNTable 的 props (部分属性会被默认值覆盖，如 toolbarConfig, operationButtons)
}

export interface VNFormProps { /* ...见上表... */ }
export interface VNFormEmits { /* ...见下文... */ }
export interface VNFormMethods { /* ...见下文... */ }
```

### 4. 事件 (`VNFormEmits`)

| 事件名             | 参数                                                      | 说明                                                                              |
| ------------------ | --------------------------------------------------------- | --------------------------------------------------------------------------------- |
| `update:modelValue`| `value: Record<string, any>`                              | v-model 数据更新时触发                                                              |
| `submit`           | `value: { header: Record<string, any>, details: any[][] }` | 表单验证通过并点击提交时触发，参数包含表头数据和所有明细表的数据数组                |
| `cancel`           |                                                           | 点击默认取消按钮或调用 `resetForm` 时触发                                           |
| `change`           | `field: string, value: any, form: Record<string, any>`    | 表单内任何字段值改变时触发 (可能来自表头字段、明细表编辑等，注意 form 可能非最新) |

### 5. 方法 (`VNFormMethods` - 通过 ref 调用)

| 方法名         | 参数                              | 返回值           | 说明                                                     |
| -------------- | --------------------------------- | ---------------- | -------------------------------------------------------- |
| `validateForm` |                                   | `Promise<boolean>` | **异步** 校验整个表单 (包括表头和所有明细表)，返回是否校验通过 |
| `resetForm`    |                                   | `void`           | 重置表单字段及所有明细表的状态，并触发 `cancel` 事件         |
| `clearValidate`| `fields?: string \| string[]`     | `void`           | 清除指定或所有表头字段的校验状态                           |
| `scrollToField`| `field: string`                   | `void`           | 滚动到指定的表头字段                                     |

### 6. 插槽

| 插槽名             | 参数                             | 说明                                                                       |
| ------------------ | -------------------------------- | -------------------------------------------------------------------------- |
| `form-item-[field]`| `{ field, formData }`            | 自定义表头字段的渲染内容，`[field]` 替换为字段配置的 `field` 值              |
| `actions`          | `{ formData, submit, reset }`    | 自定义表单底部的操作按钮区域 (submit 和 reset 是内部提交和重置方法的引用) |

### 7. 示例 (`example.vue`)

请参考 `example.vue` 文件查看更全面的功能展示，包括所有字段类型、分组、响应式布局、动态可见性、插槽用法、方法调用和明细表交互。

## 注意

- `v-model` 绑定的是一个包含 `header` 和 `details` (如果存在) 的对象。在 `example.vue` 中为了方便，将表单1的数据直接绑定到 `form1.data` (无 `details` 属性)，将表单2的数据绑定到 `form2.data` (包含 `header` 和 `details` 属性)。请根据实际数据结构调整。
- 明细表的 `tableProps` 中的部分配置 (如 `toolbarConfig`, `operationButtons`, `selectionType`, `showIndex`) 会被 `VNForm` 内部的 `defaultTableProps` 合并或覆盖，以提供一致的基础体验。如果需要完全自定义，请确保覆盖所有相关默认配置。
- `onRowAdd` 和 `onRowDelete` 回调是处理明细表增删的核心，需要父组件实现具体逻辑。
- 确保 `VNTable` 组件的路径在 `index.vue` 中正确导入。
- **布局规则**: `VNForm` 现在提供 `defaultColumns` prop (默认为 2) 来控制表头字段的默认栅格布局。`textarea` 类型的字段默认会占据整行 (24 列)。如果某个字段在 `headerFields` 配置中明确指定了 `md`, `lg`, 或 `xl` 属性，则这些特定配置会**覆盖**默认的布局规则。
