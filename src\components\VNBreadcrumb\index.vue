<template>
  <el-breadcrumb>
    <el-breadcrumb-item 
      v-for="(item, index) in items" 
      :key="index" 
      :to="item.to ? item.to : undefined"
      @click="handleClick(item)"
    >
      {{ item.label }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { ElBreadcrumb, ElBreadcrumbItem } from 'element-plus';
import type { BreadcrumbItem, VNBreadcrumbProps } from './types';

const props = defineProps<VNBreadcrumbProps>();

const handleClick = (item: BreadcrumbItem) => {
  if (item.handler && !item.to) {
    item.handler();
  }
};
</script>

<style scoped>
.el-breadcrumb {
  line-height: inherit;
}
</style> 