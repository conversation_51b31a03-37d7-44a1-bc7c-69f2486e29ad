<template>
    <div class="config-preview">
      <div class="preview-grid">
        <!-- 基础信息 -->
        <div class="preview-section">
          <h5 class="section-title">基础信息</h5>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">配置层级:</span>
              <el-tag
                :type="getConfigLevelTagType(configData.configLevel)"
                size="small"
                effect="plain"
              >
                {{ getConfigLevelName(configData.configLevel) }}
              </el-tag>
            </div>
            
            <div class="info-item" v-if="configData.configTargetId">
              <span class="label">配置目标:</span>
              <span class="value">{{ getTargetDisplay() }}</span>
            </div>
            
            <div class="info-item">
              <span class="label">盲收策略:</span>
              <el-tag
                :type="getStrategyTagType(configData.strategy)"
                size="small"
                effect="plain"
              >
                {{ getStrategyName(configData.strategy) }}
              </el-tag>
            </div>
            
            <div class="info-item">
              <span class="label">优先级:</span>
              <el-tag
                :type="getPriorityTagType(configData.priority)"
                size="small"
                effect="plain"
              >
                P{{ configData.priority }}
              </el-tag>
            </div>
          </div>
        </div>
  
        <!-- 策略配置 -->
        <div class="preview-section">
          <h5 class="section-title">策略配置</h5>
          <div class="strategy-details">
            <div class="strategy-description">
              <el-icon class="strategy-icon" :color="getStrategyColor(configData.strategy)">
                <component :is="getStrategyIcon(configData.strategy)" />
              </el-icon>
              <div class="strategy-text">
                <div class="strategy-name">
                  {{ getStrategyName(configData.strategy) }}
                </div>
                <div class="strategy-desc">
                  {{ getStrategyDescription(configData.strategy) }}
                </div>
              </div>
            </div>
            
            <div class="strategy-rules">
              <div class="rule-item" v-if="configData.maxBlindReceivingQty">
                <el-icon><Warning /></el-icon>
                <span>最大盲收数量: {{ formatNumber(configData.maxBlindReceivingQty) }}</span>
              </div>
              
              <div class="rule-item" v-if="configData.supplementTimeLimit">
                <el-icon><Clock /></el-icon>
                <span>补录时限: {{ configData.supplementTimeLimit }}小时</span>
              </div>
              
              <div class="rule-item" v-if="configData.requiresApproval">
                <el-icon><User /></el-icon>
                <span>需要审批: {{ getApprovalRolesDisplay() }}</span>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 生效范围 -->
        <div class="preview-section">
          <h5 class="section-title">生效范围</h5>
          <div class="scope-info">
            <div class="scope-item">
              <el-icon><Location /></el-icon>
              <span>{{ getScopeDescription() }}</span>
            </div>
            
            <div class="scope-hierarchy">
              <div class="hierarchy-item" :class="{ active: isLevelActive('SYSTEM') }">
                <span class="level-name">系统级</span>
                <span class="level-priority">P4</span>
              </div>
              <el-icon class="hierarchy-arrow"><ArrowRight /></el-icon>
              
              <div class="hierarchy-item" :class="{ active: isLevelActive('WAREHOUSE') }">
                <span class="level-name">仓库级</span>
                <span class="level-priority">P3</span>
              </div>
              <el-icon class="hierarchy-arrow"><ArrowRight /></el-icon>
              
              <div class="hierarchy-item" :class="{ active: isLevelActive('CLIENT') }">
                <span class="level-name">客户级</span>
                <span class="level-priority">P2</span>
              </div>
              <el-icon class="hierarchy-arrow"><ArrowRight /></el-icon>
              
              <div class="hierarchy-item" :class="{ active: isLevelActive('USER') }">
                <span class="level-name">用户级</span>
                <span class="level-priority">P1</span>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 业务影响 -->
        <div class="preview-section">
          <h5 class="section-title">业务影响</h5>
          <div class="impact-analysis">
            <div class="impact-item" :class="getImpactClass('receiving')">
              <div class="impact-icon">
                <el-icon><Box /></el-icon>
              </div>
              <div class="impact-content">
                <div class="impact-title">盲收操作</div>
                <div class="impact-desc">{{ getReceivingImpact() }}</div>
              </div>
            </div>
            
            <div class="impact-item" :class="getImpactClass('approval')" v-if="configData.requiresApproval">
              <div class="impact-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="impact-content">
                <div class="impact-title">审批流程</div>
                <div class="impact-desc">需要指定角色审批后才能执行</div>
              </div>
            </div>
            
            <div class="impact-item" :class="getImpactClass('supplement')" v-if="configData.strategy === 'SUPPLEMENT'">
              <div class="impact-icon">
                <el-icon><EditPen /></el-icon>
              </div>
              <div class="impact-content">
                <div class="impact-title">补录要求</div>
                <div class="impact-desc">
                  需在{{ configData.supplementTimeLimit }}小时内补录入库通知单
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 配置摘要 -->
      <div class="config-summary">
        <div class="summary-header">
          <el-icon><InfoFilled /></el-icon>
          <span>配置摘要</span>
        </div>
        <div class="summary-content">
          {{ getConfigSummary() }}
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'
  import {
    Warning,
    Clock,
    User,
    Location,
    ArrowRight,
    Box,
    UserFilled,
    EditPen,
    InfoFilled,
    Shield,
    Document,
    Check
  } from '@element-plus/icons-vue'
  import type {
    BlindReceivingConfigFormModel,
    ConfigLevel,
    BlindReceivingStrategy
  } from '@/types/wms/blindReceivingConfig'
  import {
    CONFIG_LEVEL_OPTIONS,
    STRATEGY_OPTIONS
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    configData: BlindReceivingConfigFormModel
  }
  
  const props = defineProps<Props>()
  
  // 工具函数
  const getConfigLevelName = (level: ConfigLevel) => {
    return CONFIG_LEVEL_OPTIONS.find(item => item.value === level)?.label || level
  }
  
  const getConfigLevelTagType = (level: ConfigLevel) => {
    const typeMap = {
      'SYSTEM': 'info',
      'WAREHOUSE': 'warning',
      'CLIENT': 'success',
      'USER': 'primary'
    }
    return typeMap[level] || 'info'
  }
  
  const getStrategyName = (strategy: BlindReceivingStrategy) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: BlindReceivingStrategy) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getStrategyColor = (strategy: BlindReceivingStrategy) => {
    const colorMap = {
      'STRICT': '#f56c6c',
      'SUPPLEMENT': '#e6a23c',
      'FULL': '#67c23a'
    }
    return colorMap[strategy] || '#909399'
  }
  
  const getStrategyIcon = (strategy: BlindReceivingStrategy) => {
    const iconMap = {
      'STRICT': Shield,
      'SUPPLEMENT': Document,
      'FULL': Check
    }
    return iconMap[strategy] || Shield
  }
  
  const getStrategyDescription = (strategy: BlindReceivingStrategy) => {
    const descMap = {
      'STRICT': '严格控制，不允许进行盲收操作',
      'SUPPLEMENT': '允许盲收，但需要在规定时间内补录相关信息',
      'FULL': '完全允许盲收，无需额外补录操作'
    }
    return descMap[strategy] || ''
  }
  
  const getPriorityTagType = (priority: number) => {
    if (priority <= 2) return 'danger'
    if (priority <= 4) return 'warning'
    return 'success'
  }
  
  const formatNumber = (num: number) => {
    return num.toLocaleString()
  }
  
  const getTargetDisplay = () => {
    if (!props.configData.configTargetId) return '-'
    // 这里应该根据配置层级显示不同的目标信息
    return `目标ID: ${props.configData.configTargetId}`
  }
  
  const getApprovalRolesDisplay = () => {
    if (!props.configData.approvalUserRoles?.length) return '未指定'
    return props.configData.approvalUserRoles.join(', ')
  }
  
  const getScopeDescription = () => {
    const levelMap = {
      'SYSTEM': '应用于整个系统的所有仓库和用户',
      'WAREHOUSE': '仅应用于指定仓库的所有用户',
      'CLIENT': '仅应用于指定客户的相关操作',
      'USER': '仅应用于指定用户的个人操作'
    }
    return levelMap[props.configData.configLevel] || ''
  }
  
  const isLevelActive = (level: ConfigLevel) => {
    const levelPriority = {
      'USER': 1,
      'CLIENT': 2,
      'WAREHOUSE': 3,
      'SYSTEM': 4
    }
    const currentPriority = levelPriority[props.configData.configLevel]
    const checkPriority = levelPriority[level]
    return checkPriority >= currentPriority
  }
  
  const getReceivingImpact = () => {
    const impactMap = {
      'STRICT': '完全禁止盲收操作，必须有对应的入库通知单',
      'SUPPLEMENT': '允许盲收，但需要在规定时间内补录入库通知单',
      'FULL': '完全允许盲收，无需补录入库通知单'
    }
    return impactMap[props.configData.strategy] || ''
  }
  
  const getImpactClass = (type: string) => {
    if (type === 'receiving') {
      return props.configData.strategy === 'STRICT' ? 'negative' : 'positive'
    }
    if (type === 'approval') {
      return 'warning'
    }
    if (type === 'supplement') {
      return 'info'
    }
    return ''
  }
  
  const getConfigSummary = () => {
    const parts = []
    
    // 配置层级
    parts.push(`${getConfigLevelName(props.configData.configLevel)}配置`)
    
    // 策略
    parts.push(`采用${getStrategyName(props.configData.strategy)}`)
    
    // 数量限制
    if (props.configData.maxBlindReceivingQty) {
      parts.push(`最大数量${formatNumber(props.configData.maxBlindReceivingQty)}`)
    }
    
    // 补录时限
    if (props.configData.supplementTimeLimit) {
      parts.push(`${props.configData.supplementTimeLimit}小时内补录`)
    }
    
    // 审批要求
    if (props.configData.requiresApproval) {
      parts.push('需要审批')
    }
    
    // 优先级
    parts.push(`优先级P${props.configData.priority}`)
    
    return parts.join('，') + '。'
  }
  </script>
  
  <style scoped lang="scss">
  .config-preview {
    .preview-grid {
      display: grid;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .preview-section {
      .section-title {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 6px;
      }
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      
      .info-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .label {
          color: #909399;
          font-size: 13px;
          font-weight: 500;
          min-width: 70px;
        }
        
        .value {
          color: #303133;
          font-size: 13px;
        }
      }
    }
    
    .strategy-details {
      .strategy-description {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 15px;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
        
        .strategy-icon {
          font-size: 24px;
        }
        
        .strategy-text {
          .strategy-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 2px;
          }
          
          .strategy-desc {
            font-size: 13px;
            color: #606266;
          }
        }
      }
      
      .strategy-rules {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .rule-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 13px;
          color: #606266;
          
          .el-icon {
            color: #909399;
          }
        }
      }
    }
    
    .scope-info {
      .scope-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 15px;
        font-size: 13px;
        color: #606266;
      }
      
      .scope-hierarchy {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .hierarchy-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px 12px;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          background-color: #f8f9fa;
          
          &.active {
            background-color: #e6f7ff;
            border-color: #409eff;
            color: #409eff;
          }
          
          .level-name {
            font-size: 12px;
            font-weight: 500;
          }
          
          .level-priority {
            font-size: 11px;
            margin-top: 2px;
            opacity: 0.8;
          }
        }
        
        .hierarchy-arrow {
          color: #c0c4cc;
          font-size: 12px;
        }
      }
    }
    
    .impact-analysis {
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .impact-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px;
        border-radius: 6px;
        
        &.positive {
          background-color: #f0f9ff;
          border: 1px solid #b3e5fc;
        }
        
        &.negative {
          background-color: #fff5f5;
          border: 1px solid #ffcdd2;
        }
        
        &.warning {
          background-color: #fffbf0;
          border: 1px solid #ffe0b2;
        }
        
        &.info {
          background-color: #f5f5f5;
          border: 1px solid #e0e0e0;
        }
        
        .impact-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: rgba(64, 158, 255, 0.1);
          
          .el-icon {
            font-size: 16px;
            color: #409eff;
          }
        }
        
        .impact-content {
          .impact-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .impact-desc {
            font-size: 13px;
            color: #606266;
            line-height: 1.4;
          }
        }
      }
    }
    
    .config-summary {
      border-top: 1px solid #ebeef5;
      padding-top: 15px;
      
      .summary-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        color: #409eff;
        font-weight: 600;
      }
      
      .summary-content {
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
        background-color: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
      }
    }
  }
  
  // 响应式设计
  @media (min-width: 768px) {
    .preview-grid {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  @media (min-width: 1024px) {
    .scope-hierarchy {
      .hierarchy-item {
        min-width: 60px;
      }
    }
  }
  </style>