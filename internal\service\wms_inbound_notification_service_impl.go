package service

import (
	"context"
	"fmt"
	"strings"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsInboundNotificationService 定义入库通知单服务接口
type WmsInboundNotificationService interface {
	BaseService
	Create(ctx context.Context, req *dto.WmsInboundNotificationCreateReq) (*vo.WmsInboundNotificationVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsInboundNotificationUpdateReq) (*vo.WmsInboundNotificationVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsInboundNotificationVO, error)
	GetPage(ctx context.Context, req *dto.WmsInboundNotificationQueryReq) (*vo.PageResult[vo.WmsInboundNotificationVO], error)
	BatchImport(ctx context.Context, req *dto.WmsInboundNotificationBatchImportReq) (*dto.BatchImportResult, error)
	UpdateStatus(ctx context.Context, id uint, req *dto.WmsInboundNotificationUpdateStatusReq) error
}

// wmsInboundNotificationServiceImpl 入库通知单服务实现
type wmsInboundNotificationServiceImpl struct {
	BaseServiceImpl
}

// NewWmsInboundNotificationService 创建入库通知单服务
func NewWmsInboundNotificationService(sm *ServiceManager) WmsInboundNotificationService {
	return &wmsInboundNotificationServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建入库通知单
func (s *wmsInboundNotificationServiceImpl) Create(ctx context.Context, req *dto.WmsInboundNotificationCreateReq) (*vo.WmsInboundNotificationVO, error) {
	var notification *entity.WmsInboundNotification
	var voResult *vo.WmsInboundNotificationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInboundNotificationRepository()

		// 编码生成逻辑
		notificationNo := req.NotificationNo
		if notificationNo == "" || notificationNo == "AUTO" {
			generatedNo, err := s.generateNotificationNo(ctx, req)
			if err != nil {
				return err
			}
			notificationNo = generatedNo
		}

		notification = &entity.WmsInboundNotification{}
		copier.Copy(notification, req)
		notification.NotificationNo = notificationNo
		notification.Status = string(entity.InboundNotificationStatusDraft) // 新增时默认为草稿状态

		// 强制从上下文获取账套ID - 账套强绑定
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空")
		}
		notification.AccountBookID = uint(accountBookID)

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}
		notification.CreatedBy = uint(userID)

		// 处理明细表数据
		if len(req.Details) > 0 {
			details := make([]entity.WmsInboundNotificationDetail, len(req.Details))
			for i, detailReq := range req.Details {
				if err := s.validateNotificationDetail(ctx, txRepoMgr, &detailReq); err != nil {
					return err
				}

				detail := &entity.WmsInboundNotificationDetail{}
				copier.Copy(detail, &detailReq)
				detail.AccountBookID = notification.AccountBookID // 明细表继承主表的账套ID
				detail.CreatedBy = notification.CreatedBy

				// 批次号自动编码逻辑
				if detail.BatchNo == nil || *detail.BatchNo == "" || *detail.BatchNo == "AUTO" {
					generatedBatchNo, err := s.generateBatchNo(ctx, &detailReq, notification)
					if err != nil {
						return fmt.Errorf("生成批次号失败: %w", err)
					}
					detail.BatchNo = &generatedBatchNo
				}

				details[i] = *detail
			}
			notification.Details = details
		}

		// 创建通知单（包含明细表）
		if err := repo.Create(ctx, notification); err != nil {
			if apperrors.IsDuplicateKeyError(err) {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "通知单号已存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建入库通知单失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, notification)
	return voResult, nil
}

// Update 更新入库通知单（支持主表+明细表差异更新）
func (s *wmsInboundNotificationServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsInboundNotificationUpdateReq) (*vo.WmsInboundNotificationVO, error) {
	var voResult *vo.WmsInboundNotificationVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInboundNotificationRepository()

		// 1. 获取当前记录（预加载明细表）
		current, err := repo.FindByID(ctx, id)
		if err != nil {
			return fmt.Errorf("查询记录失败: %w", err)
		}

		// 预加载明细表
		if err := repo.GetDB(ctx).Preload("Details").First(current, id).Error; err != nil {
			return fmt.Errorf("预加载明细表失败: %w", err)
		}

		// 2. 更新主表字段
		updates := make(map[string]interface{})
		if req.NotificationNo != nil {
			updates["notification_no"] = *req.NotificationNo
		}
		if req.NotificationType != nil {
			updates["notification_type"] = *req.NotificationType
		}
		if req.ExpectedArrivalDateStr != nil {
			updates["expected_arrival_date"] = *req.ExpectedArrivalDateStr
		}
		if req.SupplierShipper != nil {
			updates["supplier_shipper"] = *req.SupplierShipper
		}
		if req.SourceDocNo != nil {
			updates["source_doc_no"] = *req.SourceDocNo
		}
		if req.ClientID != nil {
			updates["client_id"] = *req.ClientID
		}
		if req.WarehouseID != nil {
			updates["warehouse_id"] = *req.WarehouseID
		}
		if req.Remark != nil {
			updates["remark"] = *req.Remark
		}

		// 更新主表
		if len(updates) > 0 {
			// 强制从上下文获取用户ID - 用户强绑定
			userID, err := s.GetUserIDFromContext(ctx)
			if err != nil {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
			}
			if userID == 0 {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
			}
			updates["updated_by"] = uint(userID)

			if err := repo.GetDB(ctx).Model(current).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新主表失败: %w", err)
			}
		}

		// 3. 处理明细表差异更新
		if req.Details != nil {
			if err := s.updateNotificationDetails(ctx, repo.GetDB(ctx), current, *req.Details); err != nil {
				return fmt.Errorf("更新明细表失败: %w", err)
			}
		}

		// 4. 重新查询完整数据返回
		var result entity.WmsInboundNotification
		if err := repo.GetDB(ctx).Preload("Details").First(&result, id).Error; err != nil {
			return fmt.Errorf("查询更新后数据失败: %w", err)
		}

		copier.Copy(&voResult, &result)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return voResult, nil
}

// updateNotificationDetails 处理明细表的差异更新（增删改）
func (s *wmsInboundNotificationServiceImpl) updateNotificationDetails(
	ctx context.Context,
	tx *gorm.DB,
	notification *entity.WmsInboundNotification,
	detailReqs []dto.WmsInboundNotificationDetailUpdateReq,
) error {
	// 1. 构建现有明细的映射（ID -> Detail）
	existingDetails := make(map[uint]*entity.WmsInboundNotificationDetail)
	for i := range notification.Details {
		detail := &notification.Details[i]
		existingDetails[detail.ID] = detail
	}

	// 2. 构建请求明细的映射，分为更新和新增
	var toUpdate []dto.WmsInboundNotificationDetailUpdateReq
	var toCreate []dto.WmsInboundNotificationDetailUpdateReq
	requestedIDs := make(map[uint]bool)

	for _, detailReq := range detailReqs {
		// 数据验证
		if err := s.validateDetailUpdateRequest(&detailReq); err != nil {
			return fmt.Errorf("明细数据验证失败: %w", err)
		}

		if detailReq.ID != nil && *detailReq.ID > 0 {
			// 存在ID，标记为更新
			toUpdate = append(toUpdate, detailReq)
			requestedIDs[*detailReq.ID] = true
		} else {
			// 无ID，标记为新增
			toCreate = append(toCreate, detailReq)
		}
	}

	// 3. 删除操作：删除未在请求中的现有明细
	var toDeleteIDs []uint
	for id := range existingDetails {
		if !requestedIDs[id] {
			toDeleteIDs = append(toDeleteIDs, id)
		}
	}

	if len(toDeleteIDs) > 0 {
		if err := tx.Where("id IN ?", toDeleteIDs).Delete(&entity.WmsInboundNotificationDetail{}).Error; err != nil {
			return fmt.Errorf("删除明细失败: %w", err)
		}
	}

	// 4. 更新操作：更新现有明细
	for _, detailReq := range toUpdate {
		detailID := *detailReq.ID
		existingDetail, exists := existingDetails[detailID]
		if !exists {
			return fmt.Errorf("要更新的明细ID %d 不存在", detailID)
		}

		// 构建更新字段
		updates := make(map[string]interface{})
		if detailReq.LineNo != nil {
			updates["line_no"] = *detailReq.LineNo
		}
		if detailReq.ItemID != nil {
			updates["item_id"] = *detailReq.ItemID
		}
		if detailReq.ExpectedQuantity != nil {
			updates["expected_quantity"] = *detailReq.ExpectedQuantity
		}
		if detailReq.UnitOfMeasure != nil {
			updates["unit_of_measure"] = *detailReq.UnitOfMeasure
		}
		if detailReq.BatchNo != nil {
			updates["batch_no"] = *detailReq.BatchNo
		}
		if detailReq.PackageQty != nil {
			updates["package_qty"] = *detailReq.PackageQty
		}
		if detailReq.PackageUnit != nil {
			updates["package_unit"] = *detailReq.PackageUnit
		}
		if detailReq.ProductionDateStr != nil {
			updates["production_date"] = *detailReq.ProductionDateStr
		}
		if detailReq.ExpiryDateStr != nil {
			updates["expiry_date"] = *detailReq.ExpiryDateStr
		}
		if detailReq.Remark != nil {
			updates["remark"] = *detailReq.Remark
		}

		if len(updates) > 0 {
			updates["updated_by"] = notification.UpdatedBy
			if err := tx.Model(existingDetail).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新明细ID %d 失败: %w", detailID, err)
			}
		}
	}

	// 5. 创建操作：新增明细
	if len(toCreate) > 0 {
		var newDetails []entity.WmsInboundNotificationDetail
		for _, detailReq := range toCreate {
			detail := entity.WmsInboundNotificationDetail{
				NotificationID: notification.ID,
			}
			detail.AccountBookID = notification.AccountBookID
			detail.CreatedBy = notification.CreatedBy
			// 设置字段
			if detailReq.LineNo != nil {
				detail.LineNo = *detailReq.LineNo
			}
			if detailReq.ItemID != nil {
				detail.ItemID = *detailReq.ItemID
			}
			if detailReq.ExpectedQuantity != nil {
				detail.ExpectedQuantity = *detailReq.ExpectedQuantity
			}
			if detailReq.UnitOfMeasure != nil {
				detail.UnitOfMeasure = *detailReq.UnitOfMeasure
			}
			if detailReq.PackageQty != nil {
				detail.PackageQty = *detailReq.PackageQty
			}
			if detailReq.PackageUnit != nil {
				detail.PackageUnit = *detailReq.PackageUnit
			}

			// 批次号自动编码逻辑
			if detailReq.BatchNo == nil || *detailReq.BatchNo == "" || *detailReq.BatchNo == "AUTO" {
				// 构造创建请求用于批次号生成
				createReq := &dto.WmsInboundNotificationDetailCreateReq{
					LineNo:            *detailReq.LineNo,
					ItemID:            *detailReq.ItemID,
					ExpectedQuantity:  *detailReq.ExpectedQuantity,
					UnitOfMeasure:     *detailReq.UnitOfMeasure,
					PackageQty:        *detailReq.PackageQty,
					PackageUnit:       *detailReq.PackageUnit,
					ProductionDateStr: detailReq.ProductionDateStr,
					ExpiryDateStr:     detailReq.ExpiryDateStr,
				}
				generatedBatchNo, err := s.generateBatchNo(ctx, createReq, notification)
				if err != nil {
					return fmt.Errorf("生成批次号失败: %w", err)
				}
				detail.BatchNo = &generatedBatchNo
			} else {
				detail.BatchNo = detailReq.BatchNo
			}

			if detailReq.ProductionDateStr != nil {
				detail.ProductionDateStr = detailReq.ProductionDateStr
			}
			if detailReq.ExpiryDateStr != nil {
				detail.ExpiryDateStr = detailReq.ExpiryDateStr
			}
			if detailReq.Remark != nil {
				detail.Remark = detailReq.Remark
			}

			newDetails = append(newDetails, detail)
		}

		if err := tx.Create(&newDetails).Error; err != nil {
			return fmt.Errorf("创建新明细失败: %w", err)
		}
	}

	return nil
}

// validateDetailUpdateRequest 验证明细更新请求
func (s *wmsInboundNotificationServiceImpl) validateDetailUpdateRequest(req *dto.WmsInboundNotificationDetailUpdateReq) error {
	if req.LineNo != nil && *req.LineNo <= 0 {
		return fmt.Errorf("行号必须大于0")
	}
	if req.ExpectedQuantity != nil && *req.ExpectedQuantity <= 0 {
		return fmt.Errorf("预期数量必须大于0")
	}
	if req.UnitOfMeasure != nil && strings.TrimSpace(*req.UnitOfMeasure) == "" {
		return fmt.Errorf("数量单位不能为空")
	}
	if req.ProductionDateStr != nil && req.ExpiryDateStr != nil && *req.ProductionDateStr > *req.ExpiryDateStr {
		return fmt.Errorf("生产日期不能晚于过期日期")
	}
	return nil
}

// Delete 删除入库通知单
func (s *wmsInboundNotificationServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInboundNotificationRepository()

		notification, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "入库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询入库通知单失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if notification.Status != "DRAFT" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有草稿状态的通知单才能删除")
		}

		// 删除主表（GORM会自动处理明细表的级联删除）
		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除入库通知单失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取入库通知单
func (s *wmsInboundNotificationServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsInboundNotificationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInboundNotificationRepository()

	// 先获取基本信息
	notification, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "入库通知单不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询入库通知单失败").WithCause(err)
	}

	// 使用BaseRepository的DB连接直接预加载明细和物料信息
	db := repo.GetDB(ctx)
	if err := db.Preload("Details").Where("id = ?", id).First(notification).Error; err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "加载通知单明细失败").WithCause(err)
	}

	voResult := &vo.WmsInboundNotificationVO{}
	if err := copier.Copy(voResult, notification); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "转换数据失败").WithCause(err)
	}

	// 手动填充明细的物料信息
	if len(notification.Details) > 0 {
		s.GetServiceManager().GetLogger().Info("开始处理明细物料信息",
			s.GetServiceManager().GetLogger().WithField("detailCount", len(notification.Details)))

		voResult.Details = make([]vo.WmsInboundNotificationDetailVO, len(notification.Details))
		for i, detail := range notification.Details {
			s.GetServiceManager().GetLogger().Info("处理明细",
				s.GetServiceManager().GetLogger().WithField("index", i),
				s.GetServiceManager().GetLogger().WithField("detailId", detail.ID),
				s.GetServiceManager().GetLogger().WithField("itemId", detail.ItemID))

			// 复制基本字段
			if err := copier.Copy(&voResult.Details[i], &detail); err != nil {
				s.GetServiceManager().GetLogger().Error("转换明细数据失败",
					s.GetServiceManager().GetLogger().WithField("detailId", detail.ID),
					s.GetServiceManager().GetLogger().WithError(err))
				continue
			}

			// 查询物料信息
			var item struct {
				SKU           string  `gorm:"column:sku"`
				Name          string  `gorm:"column:name"`
				Specification *string `gorm:"column:specification"`
			}

			s.GetServiceManager().GetLogger().Info("查询物料信息",
				s.GetServiceManager().GetLogger().WithField("itemId", detail.ItemID))

			// 使用新的数据库会话来避免上下文污染
			freshDB := s.GetServiceManager().GetRepositoryManager().GetDB()
			if err := freshDB.Table("mtl_item").
				Select("sku, name, specification").
				Where("id = ? AND account_book_id = ?", detail.ItemID, detail.AccountBookID).
				First(&item).Error; err != nil {
				s.GetServiceManager().GetLogger().Error("查询物料信息失败",
					s.GetServiceManager().GetLogger().WithField("itemId", detail.ItemID),
					s.GetServiceManager().GetLogger().WithError(err))
				continue
			}

			s.GetServiceManager().GetLogger().Info("查询到物料信息",
				s.GetServiceManager().GetLogger().WithField("itemId", detail.ItemID),
				s.GetServiceManager().GetLogger().WithField("sku", item.SKU),
				s.GetServiceManager().GetLogger().WithField("name", item.Name),
				s.GetServiceManager().GetLogger().WithField("specification", item.Specification))

			// 填充物料信息
			voResult.Details[i].ItemSku = &item.SKU
			voResult.Details[i].ItemName = &item.Name
			voResult.Details[i].ItemSpecification = item.Specification

			s.GetServiceManager().GetLogger().Info("填充物料信息完成",
				s.GetServiceManager().GetLogger().WithField("index", i),
				s.GetServiceManager().GetLogger().WithField("itemSku", voResult.Details[i].ItemSku),
				s.GetServiceManager().GetLogger().WithField("itemName", voResult.Details[i].ItemName))
		}

		s.GetServiceManager().GetLogger().Info("所有明细处理完成",
			s.GetServiceManager().GetLogger().WithField("processedCount", len(voResult.Details)))
	}

	return voResult, nil
}

// GetPage 分页查询入库通知单
func (s *wmsInboundNotificationServiceImpl) GetPage(ctx context.Context, req *dto.WmsInboundNotificationQueryReq) (*vo.PageResult[vo.WmsInboundNotificationVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInboundNotificationRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询入库通知单失败").WithCause(err)
	}

	voResult := &vo.PageResult[vo.WmsInboundNotificationVO]{
		List:     make([]*vo.WmsInboundNotificationVO, 0),
		Total:    pageResult.Total,
		PageNum:  pageResult.PageNum,
		PageSize: pageResult.PageSize,
	}

	// --- 优化：解决 N+1 问题 ---

	// 1. 转换实体到VO，并收集需要关联查询的ID
	notifications, ok := pageResult.List.([]*entity.WmsInboundNotification)
	if !ok || len(notifications) == 0 {
		return voResult, nil // 如果没有数据或类型不匹配，直接返回空结果
	}

	clientIDs := make(map[uint]struct{})
	warehouseIDs := make(map[uint]struct{})
	supplierCodes := make(map[string]struct{})
	voList := make([]*vo.WmsInboundNotificationVO, len(notifications))

	for i, notification := range notifications {
		notificationVO := &vo.WmsInboundNotificationVO{}
		if err := copier.Copy(notificationVO, notification); err != nil {
			s.GetServiceManager().GetLogger().Error("转换入库通知单数据失败",
				s.GetServiceManager().GetLogger().WithField("notificationId", notification.ID),
				s.GetServiceManager().GetLogger().WithError(err))
			continue // 跳过此条记录
		}
		voList[i] = notificationVO

		if notification.ClientID > 0 {
			clientIDs[notification.ClientID] = struct{}{}
		}
		if notification.WarehouseID > 0 {
			warehouseIDs[notification.WarehouseID] = struct{}{}
		}
		if notification.SupplierShipper != nil && *notification.SupplierShipper != "" {
			supplierCodes[*notification.SupplierShipper] = struct{}{}
		}
	}

	// 2. 批量查询关联名称
	clientNameMap, _ := s.fetchClientNames(ctx, clientIDs)
	warehouseNameMap, _ := s.fetchWarehouseNames(ctx, warehouseIDs)
	supplierNameMap, _ := s.fetchSupplierNames(ctx, supplierCodes)

	// 3. 将名称映射回VO列表
	for _, notificationVO := range voList {
		if notificationVO == nil {
			continue
		}
		if name, found := clientNameMap[notificationVO.ClientID]; found {
			notificationVO.ClientName = &name
		}
		if name, found := warehouseNameMap[notificationVO.WarehouseID]; found {
			notificationVO.WarehouseName = &name
		}
		if notificationVO.SupplierShipper != nil {
			if name, found := supplierNameMap[*notificationVO.SupplierShipper]; found {
				notificationVO.SupplierName = &name
			}
		}
	}

	voResult.List = voList
	return voResult, nil
}

// BatchImport 批量导入入库通知单
func (s *wmsInboundNotificationServiceImpl) BatchImport(ctx context.Context, req *dto.WmsInboundNotificationBatchImportReq) (*dto.BatchImportResult, error) {
	result := &dto.BatchImportResult{
		TotalCount:     len(req.Notifications),
		SuccessCount:   0,
		FailureCount:   0,
		SuccessIDs:     []uint{},
		FailureReasons: []string{},
	}

	// 逐个处理导入数据
	for _, createReq := range req.Notifications {
		notification, err := s.Create(ctx, &createReq)
		if err != nil {
			result.FailureCount++
			result.FailureReasons = append(result.FailureReasons, err.Error())
		} else {
			result.SuccessCount++
			result.SuccessIDs = append(result.SuccessIDs, notification.ID)
		}
	}

	return result, nil
}

// UpdateStatus 更新入库通知单状态
func (s *wmsInboundNotificationServiceImpl) UpdateStatus(ctx context.Context, id uint, req *dto.WmsInboundNotificationUpdateStatusReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInboundNotificationRepository()

		notification, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "入库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询入库通知单失败").WithCause(err)
		}

		// 验证状态转换是否合法
		if !s.isValidStatusTransition(notification.Status, req.Status) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "状态转换不合法")
		}

		// 生成带中文状态备注
		labelMap := map[string]string{
			"DRAFT":              "草稿",
			"PLANNED":            "已计划",
			"ARRIVED":            "已到货",
			"RECEIVING":          "收货中",
			"PARTIALLY_RECEIVED": "部分收货",
			"RECEIVED":           "收货完成",
			"CLOSED":             "已关闭",
			"CANCELLED":          "已取消",
		}
		label, ok := labelMap[req.Status]
		if !ok {
			label = req.Status
		}

		existingRemark := ""
		if notification.Remark != nil {
			existingRemark = strings.TrimSpace(*notification.Remark)
		}
		newSegment := label + "：" + req.Remark
		combined := newSegment
		if existingRemark != "" {
			combined = existingRemark + "\n" + newSegment
		}

		if err := repo.UpdateStatus(ctx, id, req.Status, combined); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新状态失败").WithCause(err)
		}

		return nil
	})
}

// generateNotificationNo 生成入库通知单号
func (s *wmsInboundNotificationServiceImpl) generateNotificationNo(ctx context.Context, req *dto.WmsInboundNotificationCreateReq) (string, error) {
	// 最大重试次数（编码生成失败时）
	const maxRetries = 3
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		s.GetServiceManager().GetLogger().Info("开始生成入库通知单号",
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
			s.GetServiceManager().GetLogger().WithField("maxRetries", maxRetries))

		codeGenService := s.GetServiceManager().GetCodeGenerationService()
		if codeGenService == nil {
			s.GetServiceManager().GetLogger().Error("编码生成服务不可用")
			return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "入库通知单号生成服务暂时不可用，请稍后重试或联系系统管理员。")
		}

		s.GetServiceManager().GetLogger().Info("编码生成服务可用，准备生成编码")

		contextData := map[string]interface{}{
			"warehouseId":      req.WarehouseID,
			"supplierShipper":  req.SupplierShipper,
			"sourceDocNo":      req.SourceDocNo,
			"clientId":         req.ClientID,
			"notificationType": req.NotificationType,
		}

		s.GetServiceManager().GetLogger().Info("调用编码生成服务",
			s.GetServiceManager().GetLogger().WithField("businessType", "INBOUND_NOTIFICATION"),
			s.GetServiceManager().GetLogger().WithField("contextData", contextData))

		generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
			BusinessType: "INBOUND_NOTIFICATION",
			ContextData:  contextData,
		})
		if err != nil {
			s.GetServiceManager().GetLogger().Error("生成入库通知单号失败",
				s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
				s.GetServiceManager().GetLogger().WithError(err))

			// 检查是否为配置问题（不需要重试）
			if apperrors.IsParamError(err) {
				return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "入库通知单号自动生成失败。可能原因：1) 缺少入库通知单编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
			}

			lastErr = err
			continue // 重试
		}

		s.GetServiceManager().GetLogger().Info("入库通知单号生成成功",
			s.GetServiceManager().GetLogger().WithField("generatedCode", generatedCode.GeneratedCode),
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1))

		return generatedCode.GeneratedCode, nil
	}

	// 所有重试都失败了
	if lastErr != nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "入库通知单号生成失败，系统重试次数已达上限。请稍后重试或联系系统管理员。").WithCause(lastErr)
	}

	return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "入库通知单号生成失败，编码生成重试次数已达上限。请联系系统管理员。")
}

// isValidStatusTransition 验证状态转换是否合法
func (s *wmsInboundNotificationServiceImpl) isValidStatusTransition(fromStatus, toStatus string) bool {
	// 定义状态转换规则
	transitions := map[string][]string{
		"DRAFT":              {"PLANNED", "CANCELLED"},                        // 草稿状态可以转换为已计划或取消
		"PLANNED":            {"ARRIVED", "CANCELLED"},                        // 已计划状态可以转换为到货或取消
		"ARRIVED":            {"RECEIVING", "CANCELLED"},                      // 已到货状态可以转换为收货中或取消
		"RECEIVING":          {"PARTIALLY_RECEIVED", "RECEIVED", "CANCELLED"}, // 收货中状态可以转换为部分收货、已收货或取消
		"PARTIALLY_RECEIVED": {"RECEIVING", "RECEIVED", "CANCELLED"},          // 部分收货状态可以转换为继续收货、已收货或取消
		"RECEIVED":           {"CLOSED"},                                      // 已收货状态可以转换为已关闭
		"CANCELLED":          {},                                              // 已取消状态为终态
		"CLOSED":             {},                                              // 已关闭状态为终态
	}

	allowedStatuses, exists := transitions[fromStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == toStatus {
			return true
		}
	}

	return false
}

// generateBatchNo 生成批次号
func (s *wmsInboundNotificationServiceImpl) generateBatchNo(ctx context.Context, detailReq *dto.WmsInboundNotificationDetailCreateReq, notification *entity.WmsInboundNotification) (string, error) {
	// 最大重试次数（编码生成失败时）
	const maxRetries = 3
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		s.GetServiceManager().GetLogger().Info("开始生成批次号",
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
			s.GetServiceManager().GetLogger().WithField("maxRetries", maxRetries),
			s.GetServiceManager().GetLogger().WithField("itemId", detailReq.ItemID),
			s.GetServiceManager().GetLogger().WithField("notificationNo", notification.NotificationNo))

		codeGenService := s.GetServiceManager().GetCodeGenerationService()
		if codeGenService == nil {
			s.GetServiceManager().GetLogger().Error("编码生成服务不可用")
			return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "批次号生成服务暂时不可用，请稍后重试或联系系统管理员。")
		}

		s.GetServiceManager().GetLogger().Info("编码生成服务可用，准备生成批次号")

		contextData := map[string]interface{}{
			"itemId":           detailReq.ItemID,
			"warehouseId":      notification.WarehouseID,
			"supplierShipper":  notification.SupplierShipper,
			"sourceDocNo":      notification.SourceDocNo,
			"clientId":         notification.ClientID,
			"notificationId":   notification.ID,
			"lineNo":           detailReq.LineNo,
			"notificationNo":   notification.NotificationNo,
			"notificationType": notification.NotificationType,
		}

		s.GetServiceManager().GetLogger().Info("调用编码生成服务",
			s.GetServiceManager().GetLogger().WithField("businessType", "BATCH_NUMBER"),
			s.GetServiceManager().GetLogger().WithField("contextData", contextData))

		generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
			BusinessType: "BATCH_NUMBER",
			ContextData:  contextData,
		})
		if err != nil {
			s.GetServiceManager().GetLogger().Error("生成批次号失败",
				s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
				s.GetServiceManager().GetLogger().WithField("itemId", detailReq.ItemID),
				s.GetServiceManager().GetLogger().WithError(err))

			// 检查是否为配置问题（不需要重试）
			if apperrors.IsParamError(err) {
				return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "批次号自动生成失败。可能原因：1) 缺少批次号编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
			}

			lastErr = err
			continue // 重试
		}

		s.GetServiceManager().GetLogger().Info("批次号生成成功",
			s.GetServiceManager().GetLogger().WithField("generatedCode", generatedCode.GeneratedCode),
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
			s.GetServiceManager().GetLogger().WithField("itemId", detailReq.ItemID))

		return generatedCode.GeneratedCode, nil
	}

	// 所有重试都失败了
	if lastErr != nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "批次号生成失败，系统重试次数已达上限。请稍后重试或联系系统管理员。").WithCause(lastErr)
	}

	return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "批次号生成失败，编码生成重试次数已达上限。请联系系统管理员。")
}

// validateNotificationDetail 验证明细表数据
func (s *wmsInboundNotificationServiceImpl) validateNotificationDetail(ctx context.Context, txRepoMgr *repository.RepositoryManager, detailReq *dto.WmsInboundNotificationDetailCreateReq) error {
	// 1. 验证数量必须大于0
	if detailReq.ExpectedQuantity <= 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "预期数量必须大于0")
	}

	// 2. 验证行号必须大于0且唯一（这里简化处理，实际可以在数据库层面处理）
	if detailReq.LineNo <= 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "行号必须大于0")
	}

	// 3. 验证计量单位不能为空
	if detailReq.UnitOfMeasure == "" {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "计量单位不能为空")
	}

	// 4. 验证日期逻辑：生产日期不能晚于过期日期
	if detailReq.ProductionDateStr != nil && detailReq.ExpiryDateStr != nil {
		if *detailReq.ProductionDateStr > *detailReq.ExpiryDateStr {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "生产日期不能晚于过期日期")
		}
	}

	return nil
}

// --- Private Helper Functions for Bulk Fetching ---

// fetchClientNames 批量获取客户名称
func (s *wmsInboundNotificationServiceImpl) fetchClientNames(ctx context.Context, ids map[uint]struct{}) (map[uint]string, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	idList := make([]uint, 0, len(ids))
	for id := range ids {
		idList = append(idList, id)
	}

	var clients []struct {
		ID           uint   `gorm:"column:id"`
		CustomerName string `gorm:"column:customer_name"`
	}
	db := s.GetServiceManager().GetRepositoryManager().GetDB()
	if err := db.Table("crm_customer").Select("id, customer_name").Where("id IN ?", idList).Find(&clients).Error; err != nil {
		s.GetServiceManager().GetLogger().Error("批量查询客户名称失败", s.GetServiceManager().GetLogger().WithError(err))
		return nil, err
	}
	nameMap := make(map[uint]string, len(clients))
	for _, c := range clients {
		nameMap[c.ID] = c.CustomerName
	}
	return nameMap, nil
}

// fetchWarehouseNames 批量获取仓库名称
func (s *wmsInboundNotificationServiceImpl) fetchWarehouseNames(ctx context.Context, ids map[uint]struct{}) (map[uint]string, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	idList := make([]uint, 0, len(ids))
	for id := range ids {
		idList = append(idList, id)
	}

	var locations []struct {
		ID   uint   `gorm:"column:id"`
		Name string `gorm:"column:name"`
	}
	db := s.GetServiceManager().GetRepositoryManager().GetDB()
	if err := db.Table("wms_location").Select("id, name").Where("id IN ?", idList).Find(&locations).Error; err != nil {
		s.GetServiceManager().GetLogger().Error("批量查询仓库名称失败", s.GetServiceManager().GetLogger().WithError(err))
		return nil, err
	}
	nameMap := make(map[uint]string, len(locations))
	for _, l := range locations {
		nameMap[l.ID] = l.Name
	}
	return nameMap, nil
}

// fetchSupplierNames 批量获取供应商名称
func (s *wmsInboundNotificationServiceImpl) fetchSupplierNames(ctx context.Context, codes map[string]struct{}) (map[string]string, error) {
	if len(codes) == 0 {
		return nil, nil
	}
	codeList := make([]string, 0, len(codes))
	for code := range codes {
		codeList = append(codeList, code)
	}

	var suppliers []struct {
		SupplierCode string `gorm:"column:supplier_code"`
		SupplierName string `gorm:"column:supplier_name"`
	}
	db := s.GetServiceManager().GetRepositoryManager().GetDB()
	if err := db.Table("scm_supplier").Select("supplier_code, supplier_name").Where("supplier_code IN ?", codeList).Find(&suppliers).Error; err != nil {
		s.GetServiceManager().GetLogger().Error("批量查询供应商名称失败", s.GetServiceManager().GetLogger().WithError(err))
		return nil, err
	}
	nameMap := make(map[string]string, len(suppliers))
	for _, s := range suppliers {
		nameMap[s.SupplierCode] = s.SupplierName
	}
	return nameMap, nil
}
