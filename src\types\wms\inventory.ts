// WMS库存管理相关的TypeScript类型定义

// ==================== 基础类型定义 ====================

// 库存状态枚举
export enum InventoryStatus {
  AVAILABLE = 'AVAILABLE',
  QUALITY_INSPECTION = 'QUALITY_INSPECTION',
  HOLD = 'HOLD',
  FROZEN_QC = 'FROZEN_QC',
  FROZEN_COUNT = 'FROZEN_COUNT',
  FROZEN_CUSTOMER = 'FROZEN_CUSTOMER',
  DAMAGED = 'DAMAGED',
  EXPIRED = 'EXPIRED',
  ALLOCATED = 'ALLOCATED',
  PENDING_PUTAWAY = 'PENDING_PUTAWAY',
  PENDING_PICK = 'PENDING_PICK',
  IN_TRANSIT = 'IN_TRANSIT',
  PACKING = 'PACKING'
}

// 库存调整类型枚举
export enum InventoryAdjustmentType {
  INCREASE = 'INCREASE',
  DECREASE = 'DECREASE',
  STATUS_CHANGE = 'STATUS_CHANGE'
}

// 库存调整状态枚举
export enum InventoryAdjustmentStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  EXECUTED = 'EXECUTED'
}

// 库存移动类型枚举
export enum InventoryMovementType {
  MANUAL = 'MANUAL',
  SYSTEM = 'SYSTEM',
  PUTAWAY = 'PUTAWAY',
  PICKING = 'PICKING'
}

// 库存移动状态枚举
export enum InventoryMovementStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 盘点类型枚举
export enum CycleCountType {
  FULL = 'FULL',
  CYCLE = 'CYCLE',
  SPOT = 'SPOT',
  ABC = 'ABC'
}

// 盘点计划状态枚举
export enum CycleCountPlanStatus {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 盘点任务状态枚举
export enum CycleCountTaskStatus {
  PENDING = 'PENDING',
  COUNTING = 'COUNTING',
  COMPLETED = 'COMPLETED',
  VARIANCE_CONFIRMED = 'VARIANCE_CONFIRMED'
}

// 库存预警类型枚举
export enum InventoryAlertType {
  LOW_STOCK = 'LOW_STOCK',
  HIGH_STOCK = 'HIGH_STOCK',
  EXPIRY = 'EXPIRY',
  SLOW_MOVING = 'SLOW_MOVING'
}

// 库存预警级别枚举
export enum InventoryAlertLevel {
  INFO = 'INFO',
  WARNING = 'WARNING',
  CRITICAL = 'CRITICAL'
}

// ==================== 库存查询相关类型 ====================

// 库存基础信息
export interface WmsInventoryVO {
  id: number
  warehouseId: number
  warehouseName: string
  locationId: number
  locationCode: string
  itemId: number
  itemSku: string
  itemName: string
  itemSpec?: string
  batchNo?: string
  quantity: number
  allocatedQty: number
  frozenQty: number
  availableQty: number
  unitOfMeasure: string
  status: InventoryStatus
  statusName: string
  productionDate?: string
  expiryDate?: string
  lastUpdated: string
  
  // 扩展信息
  itemCategory?: string
  itemBrand?: string
  locationType?: string
  locationZone?: string
  
  // 业务状态
  isExpired: boolean
  isNearExpiry: boolean
  daysToExpiry?: number
  isLowStock: boolean
  isOverStock: boolean
}

// 库存详情信息
export interface WmsInventoryDetailVO extends WmsInventoryVO {
  item?: WmsItemVO
  location?: WmsLocationVO
  warehouse?: WmsWarehouseVO
  transactionLogs?: WmsInventoryTransactionLogVO[]
  movementHistory?: WmsInventoryMovementHistoryVO[]
  stats?: WmsInventoryStatsVO
}

// 库存事务日志
export interface WmsInventoryTransactionLogVO {
  id: number
  transactionType: string
  transactionName: string
  quantityBefore: number
  quantityAfter: number
  quantityChange: number
  referenceType?: string
  referenceId?: number
  referenceNo?: string
  operatorId: number
  operatorName: string
  remark?: string
  createdAt: string
}

// 库存移动历史
export interface WmsInventoryMovementHistoryVO {
  id: number
  movementNo: string
  fromLocationCode: string
  toLocationCode: string
  quantity: number
  movementType: InventoryMovementType
  movementTypeName: string
  movementReason?: string
  operatorId: number
  operatorName: string
  completedAt: string
}

// 库存统计信息
export interface WmsInventoryStatsVO {
  totalInbound: number
  totalOutbound: number
  totalAdjustment: number
  totalMovement: number
  firstInboundAt?: string
  lastInboundAt?: string
  lastOutboundAt?: string
  lastMovementAt?: string
  turnoverRate?: number
  daysInStock?: number
  averageStayDays?: number
}

// 库存查询请求
export interface WmsInventoryQueryReq {
  pageNum?: number
  pageSize?: number
  warehouseId?: number
  locationId?: number
  itemId?: number
  itemSku?: string
  itemName?: string
  batchNo?: string
  status?: InventoryStatus
  quantityMin?: number
  quantityMax?: number
  expiryDateStart?: string
  expiryDateEnd?: string
  includeZero?: boolean
  onlyAvailable?: boolean
}

// 库存分页响应
export interface WmsInventoryPageVO {
  list: WmsInventoryVO[]
  total: number
}

// ==================== 库存调整相关类型 ====================

// 库存调整信息
export interface WmsInventoryAdjustmentVO {
  id: number
  adjustmentNo: string
  inventoryId: number
  adjustmentType: InventoryAdjustmentType
  adjustmentTypeName: string
  quantityBefore: number
  quantityAfter: number
  quantityChange: number
  statusBefore?: string
  statusAfter?: string
  reasonCode?: string
  reasonDescription?: string
  approvalStatus: InventoryAdjustmentStatus
  approvalStatusName: string
  approvedBy?: number
  approvedByName?: string
  approvedAt?: string
  operatorId: number
  operatorName: string
  createdAt: string
  updatedAt: string
  
  // 关联信息
  itemSku?: string
  itemName?: string
  locationCode?: string
  warehouseName?: string
  batchNo?: string
  unitOfMeasure?: string
  
  // 业务状态
  canApprove: boolean
  canExecute: boolean
  canCancel: boolean
  isApprovalRequired: boolean
}

// 库存调整创建请求
export interface WmsInventoryAdjustmentCreateReq {
  inventoryId: number
  adjustmentType: InventoryAdjustmentType
  quantityChange: number
  statusAfter?: string
  reasonCode?: string
  reasonDescription?: string
}

// 库存调整查询请求
export interface WmsInventoryAdjustmentQueryReq {
  pageNum?: number
  pageSize?: number
  adjustmentNo?: string
  inventoryId?: number
  itemSku?: string
  adjustmentType?: InventoryAdjustmentType
  approvalStatus?: InventoryAdjustmentStatus
  operatorId?: number
  createdStart?: string
  createdEnd?: string
}

// ==================== 辅助类型定义 ====================

// 物料信息
export interface WmsItemVO {
  id: number
  sku: string
  name: string
  spec?: string
  category?: string
  brand?: string
  unitOfMeasure: string
}

// 库位信息
export interface WmsLocationVO {
  id: number
  code: string
  name: string
  type?: string
  zone?: string
  warehouseId: number
}

// 仓库信息
export interface WmsWarehouseVO {
  id: number
  code: string
  name: string
  type?: string
  status: string
}

// 分页请求基类
export interface PageReq {
  pageNum?: number
  pageSize?: number
}

// 分页响应基类
export interface PageResult<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
  pages: number
}

// API响应基类
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}
