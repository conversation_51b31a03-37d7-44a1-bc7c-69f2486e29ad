package entity

import (
	"errors" // Import errors for BeforeDelete hook

	"gorm.io/gorm"
)

// DictionaryType 字典类型实体
// 用于对字典项进行分类管理
type DictionaryType struct {
	TenantEntity        // 继承租户实体 (假设包含 BaseEntity 和 TenantID)
	Code         string `gorm:"column:code;type:varchar(100);uniqueIndex;not null;comment:字典类型编码" json:"code"` // 字典类型编码 (唯一, 程序中使用)
	Name         string `gorm:"column:name;type:varchar(100);not null;comment:字典类型名称" json:"name"`             // 字典类型名称 (展示用)
	Status       int    `gorm:"column:status;type:smallint;default:1;comment:状态 (1:启用, 0:禁用)" json:"status"`   // 状态 (1:启用, 0:禁用)
	IsSystem     bool   `gorm:"column:is_system;type:boolean;default:false;comment:是否系统内置" json:"isSystem"`    // 新增：是否系统内置
	Remark       string `gorm:"column:remark;type:varchar(500);comment:备注" json:"remark"`                      // 备注

	// 关联字段 (一对多)
	Items []DictionaryItem `gorm:"foreignKey:DictionaryTypeID;references:ID" json:"items,omitempty"` // 该类型下的所有字典项
}

// TableName 指定表名
func (DictionaryType) TableName() string {
	return "sys_dictionary_type"
}

// BeforeDelete 钩子：删除类型前检查是否有关联的字典项 (可选，增强健壮性)
func (dt *DictionaryType) BeforeDelete(tx *gorm.DB) (err error) {
	var count int64
	// 注意：这里需要确保 DictionaryItem 结构体定义能够被正确引用
	// 如果 DictionaryItem 在同一个包内，可以直接使用
	// 如果在不同包，需要正确导入
	if err = tx.Model(&DictionaryItem{}).Where("dictionary_type_id = ?", dt.ID).Count(&count).Error; err != nil {
		// 返回原始数据库错误，让上层处理
		return err
	}
	if count > 0 {
		// 返回一个业务逻辑错误
		return errors.New("无法删除：该字典类型下存在字典项")
	}
	return nil
}
