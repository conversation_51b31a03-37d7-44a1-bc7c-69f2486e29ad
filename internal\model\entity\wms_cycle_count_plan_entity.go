package entity

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// WmsCycleCountType 盘点类型枚举
type WmsCycleCountType string

const (
	CountTypeFull  WmsCycleCountType = "FULL"  // 全盘
	CountTypeCycle WmsCycleCountType = "CYCLE" // 循环盘点
	CountTypeSpot  WmsCycleCountType = "SPOT"  // 抽盘
	CountTypeABC   WmsCycleCountType = "ABC"   // ABC分类盘点
)

// WmsCycleCountPlanStatus 盘点计划状态枚举
type WmsCycleCountPlanStatus string

const (
	CountPlanStatusDraft      WmsCycleCountPlanStatus = "DRAFT"       // 草稿
	CountPlanStatusApproved   WmsCycleCountPlanStatus = "APPROVED"    // 已审批
	CountPlanStatusInProgress WmsCycleCountPlanStatus = "IN_PROGRESS" // 执行中
	CountPlanStatusCompleted  WmsCycleCountPlanStatus = "COMPLETED"   // 已完成
	CountPlanStatusCancelled  WmsCycleCountPlanStatus = "CANCELLED"   // 已取消
)

// WmsCycleCountStrategy 盘点策略枚举
type WmsCycleCountStrategy string

const (
	CountStrategyByLocation WmsCycleCountStrategy = "BY_LOCATION" // 按库位
	CountStrategyByItem     WmsCycleCountStrategy = "BY_ITEM"     // 按物料
	CountStrategyByABC      WmsCycleCountStrategy = "BY_ABC"      // 按ABC分类
	CountStrategyRandom     WmsCycleCountStrategy = "RANDOM"      // 随机
)

// WmsCycleCountPlan 对应数据库中的 wms_cycle_count_plan 表
// 存储盘点计划信息
type WmsCycleCountPlan struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	PlanNo            string                  `gorm:"column:plan_no;size:50;not null;uniqueIndex:idx_plan_no_account;comment:计划编号" json:"planNo"`
	PlanName          string                  `gorm:"column:plan_name;size:200;not null;comment:计划名称" json:"planName"`
	CountType         WmsCycleCountType       `gorm:"column:count_type;type:varchar(20);not null;comment:盘点类型" json:"countType"`
	WarehouseID       *uint                   `gorm:"column:warehouse_id;comment:仓库ID" json:"warehouseId"`
	PlannedDate       time.Time               `gorm:"column:planned_date;type:date;not null;comment:计划日期" json:"plannedDate"`
	Status            WmsCycleCountPlanStatus `gorm:"column:status;type:varchar(20);default:'DRAFT';comment:计划状态" json:"status"`
	CountStrategy     *WmsCycleCountStrategy  `gorm:"column:count_strategy;type:varchar(50);comment:盘点策略" json:"countStrategy"`
	IncludeZeroStock  bool                    `gorm:"column:include_zero_stock;default:false;comment:是否包含零库存" json:"includeZeroStock"`
	FreezeInventory   bool                    `gorm:"column:freeze_inventory;default:true;comment:是否冻结库存" json:"freezeInventory"`
	ResponsibleUserID *uint                   `gorm:"column:responsible_user_id;comment:负责人ID" json:"responsibleUserId"`
	Description       *string                 `gorm:"column:description;type:text;comment:计划描述" json:"description"`
	ApprovedBy        *uint                   `gorm:"column:approved_by;comment:审批人ID" json:"approvedBy"`
	ApprovedAt        *time.Time              `gorm:"column:approved_at;comment:审批时间" json:"approvedAt"`

	// 关联关系 - 移除显式外键定义以避免GORM创建错误的约束
	// 这些关联关系可以在Service层通过手动查询来实现
	// Warehouse       *WmsLocation        `gorm:"foreignKey:WarehouseID;references:ID" json:"warehouse,omitempty"`
	// ResponsibleUser *User               `gorm:"foreignKey:ResponsibleUserID;references:ID" json:"responsibleUser,omitempty"`
	// Approver        *User               `gorm:"foreignKey:ApprovedBy;references:ID" json:"approver,omitempty"`
	Tasks []WmsCycleCountTask `gorm:"foreignKey:PlanID;references:ID" json:"tasks,omitempty"`
}

// TableName 指定数据库表名方法
func (WmsCycleCountPlan) TableName() string {
	return "wms_cycle_count_plan"
}

// BeforeCreate GORM钩子：创建前自动生成计划编号
func (w *WmsCycleCountPlan) BeforeCreate(tx *gorm.DB) error {
	if w.PlanNo == "" {
		// 这里应该调用编码生成服务生成计划编号
		// 暂时使用简单的时间戳格式
		w.PlanNo = fmt.Sprintf("CP%s", time.Now().Format("20060102150405"))
	}
	return nil
}

// CanApprove 判断是否可以审批
func (w *WmsCycleCountPlan) CanApprove() bool {
	return w.Status == CountPlanStatusDraft
}

// CanStart 判断是否可以开始执行
func (w *WmsCycleCountPlan) CanStart() bool {
	return w.Status == CountPlanStatusApproved
}

// CanComplete 判断是否可以完成
func (w *WmsCycleCountPlan) CanComplete() bool {
	return w.Status == CountPlanStatusInProgress
}

// CanCancel 判断是否可以取消
func (w *WmsCycleCountPlan) CanCancel() bool {
	return w.Status == CountPlanStatusDraft || w.Status == CountPlanStatusApproved
}

// Approve 审批计划
func (w *WmsCycleCountPlan) Approve(approverID uint) error {
	if !w.CanApprove() {
		return fmt.Errorf("计划状态不允许审批")
	}

	now := time.Now()
	w.Status = CountPlanStatusApproved
	w.ApprovedBy = &approverID
	w.ApprovedAt = &now

	return nil
}

// Start 开始执行计划
func (w *WmsCycleCountPlan) Start() error {
	if !w.CanStart() {
		return fmt.Errorf("计划状态不允许开始执行")
	}

	w.Status = CountPlanStatusInProgress

	return nil
}

// Complete 完成计划
func (w *WmsCycleCountPlan) Complete() error {
	if !w.CanComplete() {
		return fmt.Errorf("计划状态不允许完成")
	}

	w.Status = CountPlanStatusCompleted

	return nil
}

// Cancel 取消计划
func (w *WmsCycleCountPlan) Cancel() error {
	if !w.CanCancel() {
		return fmt.Errorf("计划状态不允许取消")
	}

	w.Status = CountPlanStatusCancelled

	return nil
}

// GetCountTypeName 获取盘点类型中文名称
func (w *WmsCycleCountPlan) GetCountTypeName() string {
	switch w.CountType {
	case CountTypeFull:
		return "全盘"
	case CountTypeCycle:
		return "循环盘点"
	case CountTypeSpot:
		return "抽盘"
	case CountTypeABC:
		return "ABC分类盘点"
	default:
		return "未知类型"
	}
}

// GetStatusName 获取计划状态中文名称
func (w *WmsCycleCountPlan) GetStatusName() string {
	switch w.Status {
	case CountPlanStatusDraft:
		return "草稿"
	case CountPlanStatusApproved:
		return "已审批"
	case CountPlanStatusInProgress:
		return "执行中"
	case CountPlanStatusCompleted:
		return "已完成"
	case CountPlanStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetCountStrategyName 获取盘点策略中文名称
func (w *WmsCycleCountPlan) GetCountStrategyName() string {
	if w.CountStrategy == nil {
		return ""
	}

	switch *w.CountStrategy {
	case CountStrategyByLocation:
		return "按库位"
	case CountStrategyByItem:
		return "按物料"
	case CountStrategyByABC:
		return "按ABC分类"
	case CountStrategyRandom:
		return "随机"
	default:
		return "未知策略"
	}
}

// IsOverdue 判断是否已过期
func (w *WmsCycleCountPlan) IsOverdue() bool {
	return time.Now().After(w.PlannedDate) && w.Status != CountPlanStatusCompleted
}
