package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
)

// WmsInboundNotificationController 定义入库通知单控制器接口
type WmsInboundNotificationController interface {
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)
	BatchImport(ctx iris.Context)
	UpdateStatus(ctx iris.Context)
}

// wmsInboundNotificationControllerImpl 入库通知单控制器实现
type WmsInboundNotificationControllerImpl struct {
	BaseControllerImpl
	wmsInboundNotificationService service.WmsInboundNotificationService
}

// NewWmsInboundNotificationControllerImpl 创建入库通知单控制器
func NewWmsInboundNotificationControllerImpl(cm *ControllerManager) *WmsInboundNotificationControllerImpl {
	return &WmsInboundNotificationControllerImpl{
		BaseControllerImpl:            *NewBaseController(cm),
		wmsInboundNotificationService: cm.GetServiceManager().GetWmsInboundNotificationService(),
	}
}

// Create 创建入库通知单
// POST /api/v1/wms/inbound-notifications
func (ctrl *WmsInboundNotificationControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsInboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inbound_notification")

	var req dto.WmsInboundNotificationCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsInboundNotificationService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新入库通知单
// PUT /api/v1/wms/inbound-notifications/{id:uint}
func (ctrl *WmsInboundNotificationControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsInboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsInboundNotificationUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsInboundNotificationService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除入库通知单
// DELETE /api/v1/wms/inbound-notifications/{id:uint}
func (ctrl *WmsInboundNotificationControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsInboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsInboundNotificationService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个入库通知单
// GET /api/v1/wms/inbound-notifications/{id:uint}
func (ctrl *WmsInboundNotificationControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsInboundNotificationByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsInboundNotificationService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取入库通知单分页
// GET /api/v1/wms/inbound-notifications
func (ctrl *WmsInboundNotificationControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsInboundNotificationsPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inbound_notification")

	var req dto.WmsInboundNotificationQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)
	req.PageQuery.PageNum = pageQueryFromBuild.PageNum
	req.PageQuery.PageSize = pageQueryFromBuild.PageSize
	req.PageQuery.Sort = pageQueryFromBuild.Sort

	pageResult, err := ctrl.wmsInboundNotificationService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// BatchImport 批量导入入库通知单
// POST /api/v1/wms/inbound-notifications/batch-import
func (ctrl *WmsInboundNotificationControllerImpl) BatchImport(ctx iris.Context) {
	const opName = "BatchImportWmsInboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inbound_notification")

	var req dto.WmsInboundNotificationBatchImportReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsInboundNotificationService.BatchImport(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// UpdateStatus 更新入库通知单状态
// PUT /api/v1/wms/inbound-notifications/{id:uint}/status
func (ctrl *WmsInboundNotificationControllerImpl) UpdateStatus(ctx iris.Context) {
	const opName = "UpdateWmsInboundNotificationStatus"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsInboundNotificationUpdateStatusReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsInboundNotificationService.UpdateStatus(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}
