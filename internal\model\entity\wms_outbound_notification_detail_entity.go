package entity

import (
	"time"

	"gorm.io/gorm"
)

// WmsOutboundNotificationDetail 对应数据库中的 wms_outbound_notification_detail 表
// 存储出库通知单的明细行信息
type WmsOutboundNotificationDetail struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	NotificationID uint    `gorm:"column:notification_id;not null;comment:关联通知单头ID" json:"notificationId"` // 关联的 wms_outbound_notification ID
	LineNo         int     `gorm:"column:line_no;not null;comment:行号" json:"lineNo"`                        // 明细行号
	ItemID         uint    `gorm:"column:item_id;not null;comment:物料ID" json:"itemId"`                      // 关联的物料ID
	RequiredQty    float64 `gorm:"column:required_qty;type:numeric(12,4);not null;comment:要求数量" json:"requiredQty"`
	AllocatedQty   float64 `gorm:"column:allocated_qty;type:numeric(12,4);default:0;comment:已分配数量" json:"allocatedQty"`
	PickedQty      float64 `gorm:"column:picked_qty;type:numeric(12,4);default:0;comment:已拣货数量" json:"pickedQty"`
	UnitOfMeasure  string  `gorm:"column:unit_of_measure;size:20;not null;comment:单位" json:"unitOfMeasure"`

	// 批次要求
	RequiredBatchNo        *string    `gorm:"column:required_batch_no;size:100;comment:指定批次号" json:"requiredBatchNo"`
	RequiredProductionDate *time.Time `gorm:"column:required_production_date;type:date;comment:指定生产日期" json:"-"`
	RequiredExpiryDate     *time.Time `gorm:"column:required_expiry_date;type:date;comment:指定过期日期" json:"-"`

	LineStatus string  `gorm:"column:line_status;size:20;default:'PENDING';comment:行状态" json:"lineStatus"`
	Remark     *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`

	// 扩展显示字段
	RequiredProductionDateStr *string `gorm:"-" json:"requiredProductionDate"` // 指定生产日期字符串格式
	RequiredExpiryDateStr     *string `gorm:"-" json:"requiredExpiryDate"`     // 指定过期日期字符串格式

	// 关联字段
	OutboundNotification WmsOutboundNotification   `gorm:"foreignKey:NotificationID;references:ID" json:"outboundNotification,omitempty"` // 关联的出库通知单
	Item                 MtlItem                   `gorm:"foreignKey:ItemID;references:ID" json:"item,omitempty"`                         // 关联的物料信息
	Allocations          []WmsInventoryAllocation  `gorm:"foreignKey:OutboundDetailID;references:ID" json:"allocations,omitempty"`        // 关联的库存分配记录
}

// TableName 指定数据库表名方法
func (WmsOutboundNotificationDetail) TableName() string {
	return "wms_outbound_notification_detail"
}

// BeforeCreate GORM钩子：创建前处理
func (w *WmsOutboundNotificationDetail) BeforeCreate(tx *gorm.DB) error {
	// 处理生产日期字段的字符串转换
	if w.RequiredProductionDateStr != nil && *w.RequiredProductionDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.RequiredProductionDateStr); err == nil {
			w.RequiredProductionDate = &parsedDate
		}
	}
	// 处理过期日期字段的字符串转换
	if w.RequiredExpiryDateStr != nil && *w.RequiredExpiryDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.RequiredExpiryDateStr); err == nil {
			w.RequiredExpiryDate = &parsedDate
		}
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前处理
func (w *WmsOutboundNotificationDetail) BeforeUpdate(tx *gorm.DB) error {
	// 处理生产日期字段的字符串转换
	if w.RequiredProductionDateStr != nil && *w.RequiredProductionDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.RequiredProductionDateStr); err == nil {
			w.RequiredProductionDate = &parsedDate
		}
	}
	// 处理过期日期字段的字符串转换
	if w.RequiredExpiryDateStr != nil && *w.RequiredExpiryDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", *w.RequiredExpiryDateStr); err == nil {
			w.RequiredExpiryDate = &parsedDate
		}
	}
	return nil
}

// AfterFind GORM钩子：查询后处理
func (w *WmsOutboundNotificationDetail) AfterFind(tx *gorm.DB) error {
	// 将生产日期字段转换为字符串格式用于前端显示
	if w.RequiredProductionDate != nil {
		dateStr := w.RequiredProductionDate.Format("2006-01-02")
		w.RequiredProductionDateStr = &dateStr
	}
	// 将过期日期字段转换为字符串格式用于前端显示
	if w.RequiredExpiryDate != nil {
		dateStr := w.RequiredExpiryDate.Format("2006-01-02")
		w.RequiredExpiryDateStr = &dateStr
	}
	return nil
}
