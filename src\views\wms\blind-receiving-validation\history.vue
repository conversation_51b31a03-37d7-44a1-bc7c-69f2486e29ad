<template>
    <div class="config-detail" v-if="config">
      <!-- 配置基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">配置基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="配置ID">
            {{ config.id }}
          </el-descriptions-item>
          
          <el-descriptions-item label="配置层级">
            <el-tag
              :type="getConfigLevelTagType(config.configLevel)"
              effect="plain"
            >
              {{ getConfigLevelName(config.configLevel) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="配置名称">
            {{ config.configName }}
          </el-descriptions-item>
          
          <el-descriptions-item label="配置状态">
            <el-tag
              :type="config.isActive ? 'success' : 'danger'"
              effect="plain"
            >
              {{ config.isActive ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(config.createdAt) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(config.updatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
  
      <!-- 目标配置 -->
      <div class="detail-section">
        <h4 class="section-title">目标配置</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="目标仓库" v-if="config.warehouseId">
            {{ config.warehouseName || `仓库ID: ${config.warehouseId}` }}
          </el-descriptions-item>
          
          <el-descriptions-item label="目标客户" v-if="config.clientId">
            {{ config.clientName || `客户ID: ${config.clientId}` }}
          </el-descriptions-item>
          
          <el-descriptions-item label="目标用户" v-if="config.userId">
            {{ config.userName || `用户ID: ${config.userId}` }}
          </el-descriptions-item>
          
          <el-descriptions-item label="适用范围">
            {{ getConfigScope() }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
  
      <!-- 验证策略配置 -->
      <div class="detail-section">
        <h4 class="section-title">验证策略配置</h4>
        <div class="strategy-config">
          <div class="strategy-header">
            <el-tag
              :type="getStrategyTagType(config.strategy)"
              size="large"
              effect="plain"
            >
              <el-icon class="strategy-icon">
                <component :is="getStrategyIcon(config.strategy)" />
              </el-icon>
              <span class="strategy-name">{{ getStrategyName(config.strategy) }}</span>
            </el-tag>
          </div>
          
          <div class="strategy-description">
            {{ getStrategyDescription(config.strategy) }}
          </div>
          
          <div class="strategy-params" v-if="hasStrategyParams()">
            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="是否允许盲收">
                <el-tag :type="config.allowBlindReceiving ? 'success' : 'danger'" size="small">
                  {{ config.allowBlindReceiving ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              
              <el-descriptions-item label="最大数量限制" v-if="config.maxQuantityLimit">
                {{ formatNumber(config.maxQuantityLimit) }}
              </el-descriptions-item>
              
              <el-descriptions-item label="需要审批" v-if="config.requireApproval !== undefined">
                <el-tag :type="config.requireApproval ? 'warning' : 'success'" size="small">
                  {{ config.requireApproval ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              
              <el-descriptions-item label="补录期限" v-if="config.supplementDays">
                {{ config.supplementDays }}天
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>
  
      <!-- 审批配置 -->
      <div class="detail-section" v-if="config.requireApproval">
        <h4 class="section-title">审批配置</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="审批用户角色">
            <div class="role-tags" v-if="config.approvalUserRoles?.length">
              <el-tag
                v-for="role in config.approvalUserRoles"
                :key="role"
                size="small"
                effect="plain"
              >
                {{ role }}
              </el-tag>
            </div>
            <span v-else class="text-muted">未配置</span>
          </el-descriptions-item>
          
          <el-descriptions-item label="审批模式">
            {{ getApprovalMode() }}
          </el-descriptions-item>
          
          <el-descriptions-item label="审批超时时间" v-if="config.approvalTimeout">
            {{ config.approvalTimeout }}小时
          </el-descriptions-item>
          
          <el-descriptions-item label="自动审批条件" v-if="config.autoApprovalConditions">
            {{ config.autoApprovalConditions }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
  
      <!-- 时间配置 -->
      <div class="detail-section" v-if="hasTimeConfig()">
        <h4 class="section-title">时间配置</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="生效开始时间" v-if="config.effectiveStartTime">
            {{ formatDateTime(config.effectiveStartTime) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="生效结束时间" v-if="config.effectiveEndTime">
            {{ formatDateTime(config.effectiveEndTime) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="工作时间限制" v-if="config.workingHoursOnly">
            <el-tag :type="config.workingHoursOnly ? 'warning' : 'success'" size="small">
              {{ config.workingHoursOnly ? '仅工作时间' : '全天有效' }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="节假日限制" v-if="config.excludeHolidays">
            <el-tag :type="config.excludeHolidays ? 'warning' : 'success'" size="small">
              {{ config.excludeHolidays ? '排除节假日' : '包含节假日' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
  
      <!-- 扩展配置 -->
      <div class="detail-section" v-if="config.extendedConfig">
        <h4 class="section-title">扩展配置</h4>
        <div class="extended-config">
          <el-card shadow="never" class="config-card">
            <pre class="config-json">{{ formatJson(config.extendedConfig) }}</pre>
          </el-card>
        </div>
      </div>
  
      <!-- 配置历史 -->
      <div class="detail-section" v-if="showHistory && configHistory.length > 0">
        <h4 class="section-title">配置历史</h4>
        <div class="config-history">
          <el-timeline>
            <el-timeline-item
              v-for="(history, index) in configHistory"
              :key="history.id"
              :timestamp="formatDateTime(history.createdAt)"
              :type="index === 0 ? 'primary' : 'info'"
            >
              <div class="history-item">
                <div class="history-header">
                  <span class="history-action">{{ history.action }}</span>
                  <span class="history-user">{{ history.operatorName }}</span>
                </div>
                <div class="history-description">{{ history.description }}</div>
                <div class="history-changes" v-if="history.changes">
                  <el-tag
                    v-for="change in history.changes"
                    :key="change"
                    size="small"
                    effect="plain"
                  >
                    {{ change }}
                  </el-tag>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
  
      <!-- 使用统计 -->
      <div class="detail-section" v-if="showStats && configStats">
        <h4 class="section-title">使用统计</h4>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic
              title="总验证次数"
              :value="configStats.totalValidations"
              :value-style="{ color: '#409eff' }"
            >
              <template #suffix>次</template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="验证成功次数"
              :value="configStats.successfulValidations"
              :value-style="{ color: '#67c23a' }"
            >
              <template #suffix>次</template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="最近使用时间"
              :value="formatDateTime(configStats.lastUsedAt)"
              :value-style="{ color: '#e6a23c' }"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="成功率"
              :value="getSuccessRate()"
              :precision="1"
              :value-style="{ color: '#f56c6c' }"
            >
              <template #suffix>%</template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>
    </div>
    
    <div class="config-empty" v-else>
      <el-empty description="无配置信息" />
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'
  import {
    CircleCheckFilled,
    CircleCloseFilled,
    Clock
  } from '@element-plus/icons-vue'
  import type {
    BlindReceivingConfigVO,
    BlindReceivingStrategy,
    ConfigLevel
  } from '@/types/wms/blindReceivingConfig'
  import {
    CONFIG_LEVEL_OPTIONS,
    STRATEGY_OPTIONS
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    config?: BlindReceivingConfigVO | null
    showHistory?: boolean
    showStats?: boolean
  }
  
  const props = withDefaults(defineProps<Props>(), {
    config: null,
    showHistory: false,
    showStats: false
  })
  
  // 模拟数据（实际应用中应该通过API获取）
  const configHistory = [
    {
      id: 1,
      action: '创建配置',
      operatorName: '张三',
      description: '创建盲收配置',
      createdAt: new Date().toISOString(),
      changes: ['初始创建']
    },
    {
      id: 2,
      action: '修改配置',
      operatorName: '李四',
      description: '调整最大数量限制',
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      changes: ['最大数量限制: 100 → 200']
    }
  ]
  
  const configStats = {
    totalValidations: 156,
    successfulValidations: 142,
    lastUsedAt: new Date().toISOString()
  }
  
  // 工具函数
  const getConfigLevelName = (level: ConfigLevel) => {
    return CONFIG_LEVEL_OPTIONS.find(item => item.value === level)?.label || level
  }
  
  const getConfigLevelTagType = (level: ConfigLevel) => {
    const typeMap = {
      'SYSTEM': 'info',
      'WAREHOUSE': 'warning',
      'CLIENT': 'success',
      'USER': 'primary'
    }
    return typeMap[level] || 'info'
  }
  
  const getStrategyName = (strategy: BlindReceivingStrategy) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: BlindReceivingStrategy) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getStrategyIcon = (strategy: BlindReceivingStrategy) => {
    const iconMap = {
      'STRICT': CircleCloseFilled,
      'SUPPLEMENT': Clock,
      'FULL': CircleCheckFilled
    }
    return iconMap[strategy] || CircleCheckFilled
  }
  
  const getStrategyDescription = (strategy: BlindReceivingStrategy) => {
    const descMap = {
      'STRICT': '严格禁止盲收操作，必须先创建入库通知单',
      'SUPPLEMENT': '允许盲收，但需要在指定时间内补录入库通知单',
      'FULL': '完全允许盲收操作，无需创建或补录入库通知单'
    }
    return descMap[strategy] || ''
  }
  
  const getConfigScope = () => {
    if (!props.config) return ''
    
    const scopes = []
    if (props.config.warehouseId) scopes.push('特定仓库')
    if (props.config.clientId) scopes.push('特定客户')
    if (props.config.userId) scopes.push('特定用户')
    
    return scopes.join('、') || '全局'
  }
  
  const getApprovalMode = () => {
    if (!props.config?.requireApproval) return '无需审批'
    // 这里可以根据实际的审批配置返回不同的模式
    return '手动审批'
  }
  
  const getSuccessRate = () => {
    if (!configStats.totalValidations) return 0
    return (configStats.successfulValidations / configStats.totalValidations) * 100
  }
  
  const hasStrategyParams = () => {
    return props.config && (
      props.config.allowBlindReceiving !== undefined ||
      props.config.maxQuantityLimit ||
      props.config.requireApproval !== undefined ||
      props.config.supplementDays
    )
  }
  
  const hasTimeConfig = () => {
    return props.config && (
      props.config.effectiveStartTime ||
      props.config.effectiveEndTime ||
      props.config.workingHoursOnly ||
      props.config.excludeHolidays
    )
  }
  
  const formatNumber = (num: number) => {
    return num.toLocaleString()
  }
  
  const formatDateTime = (dateStr: string) => {
    return new Date(dateStr).toLocaleString()
  }
  
  const formatJson = (obj: any) => {
    try {
      return JSON.stringify(obj, null, 2)
    } catch (error) {
      return String(obj)
    }
  }
  </script>
  
  <style scoped lang="scss">
  .config-detail {
    .detail-section {
      margin-bottom: 24px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-title {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }
    }
    
    .strategy-config {
      .strategy-header {
        margin-bottom: 12px;
        
        .el-tag {
          padding: 8px 16px;
          font-size: 14px;
          
          .strategy-icon {
            margin-right: 8px;
            font-size: 16px;
          }
          
          .strategy-name {
            font-weight: 600;
          }
        }
      }
      
      .strategy-description {
        color: #606266;
        line-height: 1.5;
        margin-bottom: 16px;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
      }
      
      .strategy-params {
        border-top: 1px solid #ebeef5;
        padding-top: 16px;
      }
    }
    
    .role-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }
    
    .text-muted {
      color: #c0c4cc;
    }
    
    .extended-config {
      .config-card {
        .config-json {
          margin: 0;
          padding: 16px;
          background-color: #f8f9fa;
          border-radius: 4px;
          color: #606266;
          font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.4;
          overflow-x: auto;
        }
      }
    }
    
    .config-history {
      .history-item {
        .history-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          
          .history-action {
            font-weight: 600;
            color: #303133;
          }
          
          .history-user {
            color: #909399;
            font-size: 13px;
          }
        }
        
        .history-description {
          color: #606266;
          margin-bottom: 8px;
        }
        
        .history-changes {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
      }
    }
  }
  
  .config-empty {
    text-align: center;
    padding: 40px 20px;
  }
  </style>