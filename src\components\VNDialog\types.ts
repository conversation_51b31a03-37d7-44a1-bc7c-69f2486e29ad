import type { DialogProps } from 'element-plus';

// 继承 ElDialog 的部分 Props，并添加 v-model 控制
// 使用 Omit 来排除我们自己处理的 'modelValue' 和 'update:modelValue'
// 使用 Partial 使大部分继承的 props 变为可选
export interface VNDialogProps extends Partial<Omit<DialogProps, 'modelValue'>> {
  modelValue: boolean; // 控制对话框显示与隐藏 (v-model)
  showConfirmButton?: boolean; // 是否显示默认的确认按钮
  showCancelButton?: boolean; // 是否显示默认的取消按钮
  confirmButtonText?: string; // 确认按钮文字
  cancelButtonText?: string; // 取消按钮文字
  confirmButtonLoading?: boolean; // 确认按钮加载状态
  // 可以根据需要添加更多自定义 Props
}

// 定义组件 Emits
export interface VNDialogEmits {
  (e: 'update:modelValue', value: boolean): void; // v-model 更新
  (e: 'open'): void; // Dialog 打开的回调
  (e: 'opened'): void; // Dialog 打开动画结束时的回调
  (e: 'close'): void; // Dialog 关闭的回调
  (e: 'closed'): void; // Dialog 关闭动画结束时的回调
  (e: 'confirm'): void; // 点击默认确认按钮时触发
  (e: 'cancel'): void; // 点击默认取消按钮或关闭图标时触发
} 