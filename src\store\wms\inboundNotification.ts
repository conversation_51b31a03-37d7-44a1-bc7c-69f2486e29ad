import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  WmsInboundNotificationResp,
  WmsInboundNotificationQueryReq,
  WmsInboundNotificationCreateReq,
  WmsInboundNotificationUpdateReq,
  WmsInboundNotificationStatus,
  WmsItemRespV2
} from '@/api/wms/inboundNotification'
import type {
  InboundNotificationFormData,
  SearchFormData,
  PaginationParams,
  ClientOption,
  WarehouseOption,
  SupplierOption
} from '@/types/wms/inboundNotification'
import {
  getInboundNotificationPage,
  getInboundNotificationDetail,
  createInboundNotification,
  updateInboundNotification,
  deleteInboundNotification,
  updateInboundNotificationStatus,
  getClientPageV2,
  getWarehouseList,
  getSupplierList,
  getItemPageV2,
  batchDeleteInboundNotifications,
  batchUpdateInboundNotificationStatus,
  exportInboundNotifications,
  downloadImportTemplate
} from '@/api/wms/inboundNotification'

export const useInboundNotificationStore = defineStore('inboundNotification', () => {
  // ==================== 状态数据 ====================
  
  // 列表数据
  const list = ref<WmsInboundNotificationResp[]>([])
  const loading = ref(false)
  const pagination = ref<PaginationParams>({
    pageNum: 1,
    pageSize: 20,
    total: 0
  })
  
  // 当前选中的记录
  const currentRecord = ref<WmsInboundNotificationResp | null>(null)
  const currentRecordLoading = ref(false)
  
  // 表单数据
  const formData = ref<InboundNotificationFormData>({
    notificationType: '',
    expectedArrivalDate: '',
    clientId: null,
    warehouseId: null,
    details: []
  })
  const formLoading = ref(false)
  
  // 搜索条件
  const searchForm = ref<SearchFormData>({})
  
  // ==================== 缓存数据 ====================
  
  // 字典数据缓存
  const clientOptions = ref<ClientOption[]>([])
  const warehouseOptions = ref<WarehouseOption[]>([])
  const supplierOptions = ref<SupplierOption[]>([])
  const itemOptions = ref<WmsItemRespV2[]>([])
  
  // 缓存过期时间（5分钟）
  const CACHE_EXPIRE_TIME = 5 * 60 * 1000
  const cacheTimestamps = ref<Record<string, number>>({})
  
  // ==================== 计算属性 ====================
  
  // 是否有数据
  const hasData = computed(() => list.value.length > 0)
  
  // 是否为空状态
  const isEmpty = computed(() => !loading.value && list.value.length === 0)
  
  // 当前页数据
  const currentPageData = computed(() => list.value)
  
  // 表单验证状态
  const isFormValid = computed(() => {
    const form = formData.value
    return !!(
      form.notificationType &&
      form.expectedArrivalDate &&
      form.clientId &&
      form.warehouseId &&
      form.details.length > 0 &&
      form.details.every(detail => 
        detail.itemId && 
        detail.expectedQuantity > 0 && 
        detail.unitOfMeasure
      )
    )
  })
  
  // ==================== 缓存工具函数 ====================
  
  const isCacheValid = (key: string): boolean => {
    const timestamp = cacheTimestamps.value[key]
    return !!(timestamp && (Date.now() - timestamp) < CACHE_EXPIRE_TIME)
  }
  
  const setCacheTimestamp = (key: string) => {
    cacheTimestamps.value[key] = Date.now()
  }
  
  // ==================== 异步操作 Actions ====================
  
  // 获取列表数据
  const fetchList = async (params?: WmsInboundNotificationQueryReq) => {
    try {
      loading.value = true
      
      const queryParams = {
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        ...searchForm.value,
        ...params
      }
      
      // 过滤掉空字符串的notificationType
      if (queryParams.notificationType === '') {
        delete queryParams.notificationType
      }
      
      const response = await getInboundNotificationPage(queryParams as unknown as WmsInboundNotificationQueryReq)
      
      list.value = response.list || []
      pagination.value.total = response.total || 0
      
      return response
    } catch (error) {
      console.error('获取入库通知单列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取详情数据
  const fetchDetail = async (id: number) => {
    try {
      currentRecordLoading.value = true
      
      const response = await getInboundNotificationDetail(id)
      currentRecord.value = response
      
      return response
    } catch (error) {
      console.error('获取入库通知单详情失败:', error)
      throw error
    } finally {
      currentRecordLoading.value = false
    }
  }
  
  // 创建记录
  const create = async (data: WmsInboundNotificationCreateReq) => {
    try {
      formLoading.value = true
      
      const response = await createInboundNotification(data)
      
      // 刷新列表
      await fetchList()
      
      return response
    } catch (error) {
      console.error('创建入库通知单失败:', error)
      throw error
    } finally {
      formLoading.value = false
    }
  }
  
  // 更新记录
  const update = async (id: number, data: WmsInboundNotificationUpdateReq) => {
    try {
      formLoading.value = true
      
      const response = await updateInboundNotification(id, data)
      
      // 更新列表中的记录
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1) {
        list.value[index] = { ...list.value[index], ...response }
      }
      
      // 更新当前记录
      if (currentRecord.value?.id === id) {
        currentRecord.value = { ...currentRecord.value, ...response }
      }
      
      return response
    } catch (error) {
      console.error('更新入库通知单失败:', error)
      throw error
    } finally {
      formLoading.value = false
    }
  }
  
  // 删除记录
  const remove = async (id: number) => {
    try {
      await deleteInboundNotification(id)
      
      // 从列表中移除
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1) {
        list.value.splice(index, 1)
        pagination.value.total -= 1
      }
      
      // 清除当前记录
      if (currentRecord.value?.id === id) {
        currentRecord.value = null
      }
    } catch (error) {
      console.error('删除入库通知单失败:', error)
      throw error
    }
  }
  
  // 更新状态
  const updateStatus = async (id: number, status: WmsInboundNotificationStatus, remark?: string) => {
    try {
      await updateInboundNotificationStatus(id, { status, remark })

      // 更新列表中的记录状态
      const index = list.value.findIndex(item => item.id === id)
      if (index !== -1 && list.value[index]) {
        list.value[index].status = status
      }

      // 更新当前记录状态
      if (currentRecord.value?.id === id) {
        currentRecord.value.status = status
      }
    } catch (error) {
      console.error('更新入库通知单状态失败:', error)
      throw error
    }
  }

  // 批量删除记录
  const batchRemove = async (ids: number[]) => {
    try {
      await batchDeleteInboundNotifications(ids)

      // 从列表中移除
      ids.forEach(id => {
        const index = list.value.findIndex(item => item.id === id)
        if (index !== -1) {
          list.value.splice(index, 1)
          pagination.value.total -= 1
        }
      })

      // 清除当前记录（如果被删除）
      if (currentRecord.value && ids.includes(currentRecord.value.id)) {
        currentRecord.value = null
      }
    } catch (error) {
      console.error('批量删除入库通知单失败:', error)
      throw error
    }
  }

  // 批量更新状态
  const batchUpdateStatus = async (ids: number[], status: WmsInboundNotificationStatus, remark?: string) => {
    try {
      await batchUpdateInboundNotificationStatus({ ids, status, remark })

      // 更新列表中的记录状态
      ids.forEach(id => {
        const index = list.value.findIndex(item => item.id === id)
        if (index !== -1 && list.value[index]) {
          list.value[index].status = status
        }
      })

      // 更新当前记录状态
      if (currentRecord.value && ids.includes(currentRecord.value.id)) {
        currentRecord.value.status = status
      }
    } catch (error) {
      console.error('批量更新入库通知单状态失败:', error)
      throw error
    }
  }

  // 导出数据
  const exportData = async (params?: WmsInboundNotificationQueryReq) => {
    try {
      const queryParams = {
        ...searchForm.value,
        ...params
      }

      // 过滤掉空字符串的notificationType
      if (queryParams.notificationType === '') {
        delete queryParams.notificationType
      }

      const blob = await exportInboundNotifications(queryParams as unknown as WmsInboundNotificationQueryReq)

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `入库通知单_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('导出入库通知单失败:', error)
      throw error
    }
  }

  // 下载导入模板
  const downloadTemplate = async () => {
    try {
      const blob = await downloadImportTemplate()

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '入库通知单导入模板.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载导入模板失败:', error)
      throw error
    }
  }
  
  // ==================== 字典数据获取 ====================
  
  // 获取客户选项
  const fetchClientOptions = async (refresh = false) => {
    if (!refresh && isCacheValid('clients') && clientOptions.value.length > 0) {
      return clientOptions.value
    }
    
    try {
      const response = await getClientPageV2({ pageSize: 1000 })
      clientOptions.value = response.list?.map((item: any) => ({
        value: item.id,
        label: item.name,
        code: item.code
      })) || []
      
      setCacheTimestamp('clients')
      return clientOptions.value
    } catch (error) {
      console.error('获取客户选项失败:', error)
      return []
    }
  }
  
  // 获取仓库选项
  const fetchWarehouseOptions = async (refresh = false) => {
    if (!refresh && isCacheValid('warehouses') && warehouseOptions.value.length > 0) {
      return warehouseOptions.value
    }
    
    try {
      const response = await getWarehouseList()
      warehouseOptions.value = response?.map(item => ({
        value: item.id,
        label: item.name,
        code: item.code
      })) || []
      
      setCacheTimestamp('warehouses')
      return warehouseOptions.value
    } catch (error) {
      console.error('获取仓库选项失败:', error)
      return []
    }
  }
  
  // 获取供应商选项
  const fetchSupplierOptions = async (refresh = false) => {
    if (!refresh && isCacheValid('suppliers') && supplierOptions.value.length > 0) {
      return supplierOptions.value
    }
    
    try {
      const response = await getSupplierList({ pageSize: 1000 })
      supplierOptions.value = response.list?.map((item: any) => ({
        value: item.id,
        label: item.name,
        code: item.code
      })) || []
      
      setCacheTimestamp('suppliers')
      return supplierOptions.value
    } catch (error) {
      console.error('获取供应商选项失败:', error)
      return []
    }
  }
  
  // 获取物料选项
  const fetchItemOptions = async (params?: { sku?: string; name?: string }) => {
    try {
      const response = await getItemPageV2({
        pageSize: 50,
        ...params
      })
      
      const items = response.list || []
      
      // 合并到现有选项中，避免重复
      items.forEach((item: any) => {
        const exists = itemOptions.value.find(existing => existing.id === item.id)
        if (!exists) {
          itemOptions.value.push(item)
        }
      })
      
      return items
    } catch (error) {
      console.error('获取物料选项失败:', error)
      return []
    }
  }
  
  // ==================== 表单操作 ====================
  
  // 初始化表单
  const initForm = (mode: 'create' | 'edit' | 'view' = 'create', data?: WmsInboundNotificationResp) => {
    if (data) {
      formData.value = {
        id: data.id,
        notificationNo: data.notificationNo,
        notificationType: data.notificationType,
        expectedArrivalDate: data.expectedArrivalDate || '',
        clientId: data.clientId,
        warehouseId: data.warehouseId,
        supplierId: data.supplierShipper ? parseInt(data.supplierShipper) : null,
        sourceDocNo: data.sourceDocNo,
        remark: data.remark,
        details: data.details?.map((detail, index) => ({
          id: detail.id,
          lineNo: detail.lineNo,
          itemId: detail.itemId,
          itemSku: detail.itemSku,
          itemName: detail.itemName,
          expectedQuantity: detail.expectedQuantity,
          unitOfMeasure: detail.unitOfMeasure,
          batchNo: detail.batchNo,
          productionDate: detail.productionDate,
          expiryDate: detail.expiryDate,
          remark: detail.remark
        })) || [],
        _mode: mode
      }
    } else {
      formData.value = {
        notificationType: '',
        expectedArrivalDate: '',
        clientId: null,
        warehouseId: null,
        details: [],
        _mode: mode
      }
    }
  }
  
  // 重置表单
  const resetForm = () => {
    formData.value = {
      notificationType: '',
      expectedArrivalDate: '',
      clientId: null,
      warehouseId: null,
      details: []
    }
  }
  
  // 添加明细行
  const addDetailRow = () => {
    const newLineNo = Math.max(0, ...formData.value.details.map(d => d.lineNo)) + 1
    formData.value.details.push({
      lineNo: newLineNo,
      expectedQuantity: 1,
      unitOfMeasure: '',
      _isNew: true
    })
  }
  
  // 删除明细行
  const removeDetailRow = (index: number) => {
    const detail = formData.value.details[index]
    if (detail && detail.id) {
      // 已存在的记录标记为删除
      detail._toDelete = true
    } else {
      // 新增的记录直接移除
      formData.value.details.splice(index, 1)
    }
  }
  
  // ==================== 搜索操作 ====================
  
  // 设置搜索条件
  const setSearchForm = (data: Partial<SearchFormData>) => {
    searchForm.value = { ...searchForm.value, ...data }
  }
  
  // 重置搜索条件
  const resetSearchForm = () => {
    searchForm.value = {}
  }
  
  // 执行搜索
  const search = async () => {
    pagination.value.pageNum = 1
    await fetchList()
  }
  
  // ==================== 分页操作 ====================
  
  // 设置分页参数
  const setPagination = (params: Partial<PaginationParams>) => {
    pagination.value = { ...pagination.value, ...params }
  }
  
  // 跳转到指定页
  const goToPage = async (page: number) => {
    pagination.value.pageNum = page
    await fetchList()
  }
  
  // 改变页大小
  const changePageSize = async (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.pageNum = 1
    await fetchList()
  }
  
  // ==================== 工具方法 ====================
  
  // 根据ID查找记录
  const findById = (id: number) => {
    return list.value.find(item => item.id === id)
  }
  
  // 清除缓存
  const clearCache = () => {
    clientOptions.value = []
    warehouseOptions.value = []
    supplierOptions.value = []
    itemOptions.value = []
    cacheTimestamps.value = {}
  }
  
  // 刷新所有字典数据
  const refreshDictData = async () => {
    await Promise.all([
      fetchClientOptions(true),
      fetchWarehouseOptions(true),
      fetchSupplierOptions(true)
    ])
  }
  
  return {
    // 状态数据
    list,
    loading,
    pagination,
    currentRecord,
    currentRecordLoading,
    formData,
    formLoading,
    searchForm,
    
    // 缓存数据
    clientOptions,
    warehouseOptions,
    supplierOptions,
    itemOptions,
    
    // 计算属性
    hasData,
    isEmpty,
    currentPageData,
    isFormValid,
    
    // 异步操作
    fetchList,
    fetchDetail,
    create,
    update,
    remove,
    updateStatus,
    batchRemove,
    batchUpdateStatus,
    exportData,
    downloadTemplate,
    
    // 字典数据
    fetchClientOptions,
    fetchWarehouseOptions,
    fetchSupplierOptions,
    fetchItemOptions,
    
    // 表单操作
    initForm,
    resetForm,
    addDetailRow,
    removeDetailRow,
    
    // 搜索操作
    setSearchForm,
    resetSearchForm,
    search,
    
    // 分页操作
    setPagination,
    goToPage,
    changePageSize,
    
    // 工具方法
    findById,
    clearCache,
    refreshDictData
  }
}) 