package dto

import "backend/pkg/response"

// OrganizationNodeCreateDTO 创建组织节点 DTO
type OrganizationNodeCreateOrUpdateDTO struct {
	TenantDTO
	ParentID          *uint  `json:"parentId"`                                                       // 上级节点ID (可选, 默认为顶层)
	NodeType          string `json:"nodeType" validate:"required,oneof=company department position"` // 节点类型 (必填, company, department 或 position)
	Name              string `json:"name" validate:"required,max=100"`                               // 节点名称 (必填, 最长100)
	Code              string `json:"code,omitempty" validate:"omitempty,max=50"`                     // 节点编码 (可选, 最长50)
	LeaderUserID      *uint  `json:"leaderUserId,omitempty"`                                         // 负责人用户ID (可选)
	OrderNum          *int   `json:"orderNum,omitempty"`                                             // 排序号 (可选, Repository 会自动处理)
	Weight            *int   `json:"weight,omitempty" validate:"omitempty,min=1"`                    // 岗位权重 (可选, 仅用于 position)
	IsVirtual         *bool  `json:"isVirtual,omitempty"`                                            // 是否虚拟节点 (可选, 默认 false)
	StandardHeadcount *int   `json:"standardHeadcount,omitempty" validate:"omitempty,min=0"`         // 标配人数 (可选, 仅用于 position)
	Status            *int   `json:"status,omitempty" validate:"omitempty,oneof=0 1"`                // 状态 (可选, 默认 1)
	Remarks           string `json:"remarks,omitempty" validate:"max=500"`                           // 备注 (可选, 最长500)
	CompanyID         *uint  `json:"companyId,omitempty"`                                           // 所属公司ID (可选)
}

// OrganizationNodeQueryDTO 查询组织节点 DTO
type OrganizationNodePageQueryDTO struct {
	PageQuery response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
	Name      string             `json:"name,omitempty" form:"name"`           // 节点名称 (模糊查询)
	Code      string             `json:"code,omitempty" form:"code"`           // 节点编码 (模糊查询)
	NodeType  string             `json:"nodeType,omitempty" form:"nodeType"`   // 节点类型 (department/position)
	Status    *int               `json:"status,omitempty" form:"status"`       // 状态
	ParentID  *uint              `json:"parentId,omitempty" form:"parentId"`   // 父节点ID
	SortField string             `json:"sortField,omitempty" form:"sortField"` // 排序字段
	SortOrder string             `json:"sortOrder,omitempty" form:"sortOrder"` // 排序顺序 asc/desc
	CompanyID *uint              `json:"companyId,omitempty" form:"companyId"` // 所属公司ID
}

// UpdateNodeStatusDTO 更新组织节点状态 DTO
type UpdateNodeStatusDTO struct {
	Status int `json:"status" validate:"required,oneof=0 1"` // 状态 (必填, 0=禁用, 1=启用)
}
