package entity

// RoleMenu maps to the sys_role_menu join table
// It represents the many-to-many relationship between roles and menus.
type RoleMenu struct {
	RoleID uint `gorm:"primaryKey;column:role_id;comment:角色ID"` // 角色ID (主键)
	MenuID uint `gorm:"primaryKey;column:menu_id;comment:菜单ID"` // 菜单ID (主键)
	// Consider adding gorm.Model or Timestamps if tracking creation/update time is needed
}

// TableName explicitly sets the table name for GORM.
func (RoleMenu) TableName() string {
	return "sys_role_menu"
}
