package entity

import (
	"github.com/shopspring/decimal"
)

// ScmSupplier 供应商实体
// 继承自AccountBookEntity，提供多账套数据隔离
// 包含供应商的基本信息、联系方式、财务信息和业务信息
type ScmSupplier struct {
	AccountBookEntity

	// 供应商基本信息
	SupplierCode        string           `gorm:"column:supplier_code;size:50;not null;comment:供应商编码" json:"supplierCode" validation:"required,max=50"`         // 供应商编码：唯一标识供应商的编码，支持自动生成
	SupplierName        string           `gorm:"column:supplier_name;size:255;not null;comment:供应商名称" json:"supplierName" validation:"required,max=255"`       // 供应商名称：供应商的正式名称
	SupplierType        string           `gorm:"column:supplier_type;comment:供应商类型" json:"supplierType"`                                                       // 供应商类型：CORPORATE(企业)/INDIVIDUAL(个人)
	Industry            *string          `gorm:"column:industry;size:100;comment:所属行业" json:"industry" validation:"omitempty,max=100"`                         // 所属行业：供应商所在的行业分类
	BusinessLicense     *string          `gorm:"column:business_license;size:100;comment:营业执照号" json:"businessLicense" validation:"omitempty,max=100"`         // 营业执照号：企业供应商的营业执照编号
	TaxNumber           *string          `gorm:"column:tax_number;size:100;comment:税务登记号" json:"taxNumber" validation:"omitempty,max=100"`                     // 税务登记号：税务部门分配的唯一编号
	LegalRepresentative *string          `gorm:"column:legal_representative;size:100;comment:法定代表人" json:"legalRepresentative" validation:"omitempty,max=100"` // 法定代表人：企业的法定代表人姓名
	RegisteredCapital   *decimal.Decimal `gorm:"column:registered_capital;type:decimal(15,2);comment:注册资本" json:"registeredCapital"`                           // 注册资本：企业的注册资本金额

	// 联系信息
	ContactPerson *string `gorm:"column:contact_person;size:100;comment:主要联系人" json:"contactPerson" validation:"omitempty,max=100"`    // 主要联系人：供应商的主要联系人姓名
	ContactPhone  *string `gorm:"column:contact_phone;size:50;comment:联系电话" json:"contactPhone" validation:"omitempty,max=50"`         // 联系电话：供应商的主要联系电话
	ContactEmail  *string `gorm:"column:contact_email;size:255;comment:联系邮箱" json:"contactEmail" validation:"omitempty,email,max=255"` // 联系邮箱：供应商的联系邮箱地址
	Website       *string `gorm:"column:website;size:255;comment:公司网站" json:"website" validation:"omitempty,url,max=255"`              // 公司网站：供应商的官方网站地址

	// 地址信息
	Country    *string `gorm:"column:country;size:100;comment:国家" json:"country" validation:"omitempty,max=100"`        // 国家：供应商所在国家
	Province   *string `gorm:"column:province;size:100;comment:省份" json:"province" validation:"omitempty,max=100"`      // 省份：供应商所在省份
	City       *string `gorm:"column:city;size:100;comment:城市" json:"city" validation:"omitempty,max=100"`              // 城市：供应商所在城市
	District   *string `gorm:"column:district;size:100;comment:区县" json:"district" validation:"omitempty,max=100"`      // 区县：供应商所在区县
	Address    *string `gorm:"column:address;type:text;comment:详细地址" json:"address"`                                    // 详细地址：供应商的详细地址信息
	PostalCode *string `gorm:"column:postal_code;size:20;comment:邮政编码" json:"postalCode" validation:"omitempty,max=20"` // 邮政编码：供应商地址的邮政编码

	// 财务信息
	CreditRating *string          `gorm:"column:credit_rating;size:20;comment:信用等级" json:"creditRating" validation:"omitempty,max=20"`     // 信用等级：供应商的信用评级
	CreditLimit  *decimal.Decimal `gorm:"column:credit_limit;type:decimal(15,2);comment:信用额度" json:"creditLimit"`                          // 信用额度：给予供应商的信用额度
	PaymentTerms *string          `gorm:"column:payment_terms;size:50;comment:付款条件" json:"paymentTerms" validation:"omitempty,max=50"`     // 付款条件：与供应商约定的付款方式和期限
	CurrencyCode string           `gorm:"column:currency_code;size:10;default:'CNY';comment:结算币种" json:"currencyCode" validation:"max=10"` // 结算币种：与供应商交易的币种，默认人民币

	// 业务信息
	SupplierLevel               string  `gorm:"column:supplier_level;default:'C';comment:供应商等级" json:"supplierLevel"`                                 // 供应商等级：A/B/C/D级别评定
	SupplierCategory            *string `gorm:"column:supplier_category;size:50;comment:供应商分类" json:"supplierCategory" validation:"omitempty,max=50"` // 供应商分类：供应商的业务分类
	SupplierSource              *string `gorm:"column:supplier_source;size:50;comment:供应商来源" json:"supplierSource" validation:"omitempty,max=50"`     // 供应商来源：供应商的获取渠道
	ProcurementRepresentativeID *uint   `gorm:"column:procurement_representative_id;comment:采购代表ID" json:"procurementRepresentativeId"`               // 采购代表ID：负责该供应商的采购人员

	// 资质信息
	QualificationCertificate *string `gorm:"column:qualification_certificate;type:text;comment:资质证书" json:"qualificationCertificate"` // 资质证书：供应商的各类资质证书
	AnnualSupplyCapacity *decimal.Decimal `gorm:"column:annual_supply_capacity;type:decimal(15,2);comment:年供应量" json:"annualSupplyCapacity"` // 年供应量：供应商的年供应量
	MainProducts      *string `gorm:"column:main_products;size:255;comment:主要产品" json:"mainProducts" validation:"omitempty,max=255"` // 主要产品：供应商的主要产品
	CertificationInfo *string `gorm:"column:certification_info;type:text;comment:资质认证信息" json:"certificationInfo"`                     // 资质认证信息：供应商的各类认证信息
	QualityRating     *string `gorm:"column:quality_rating;size:20;comment:质量评级" json:"qualityRating" validation:"omitempty,max=20"`   // 质量评级：供应商的质量评估等级
	DeliveryRating    *string `gorm:"column:delivery_rating;size:20;comment:交付评级" json:"deliveryRating" validation:"omitempty,max=20"` // 交付评级：供应商的交付能力评级
	ServiceRating     *string `gorm:"column:service_rating;size:20;comment:服务评级" json:"serviceRating" validation:"omitempty,max=20"`   // 服务评级：供应商的服务质量评级

	// 状态信息
	Status              string  `gorm:"column:status;default:'ACTIVE';comment:状态" json:"status"`                               // 状态：ACTIVE(正常)/INACTIVE(禁用)/BLACKLIST(黑名单)
	IsKeySupplier       bool    `gorm:"column:is_key_supplier;default:false;comment:是否重点供应商" json:"isKeySupplier"`             // 是否重点供应商：标识供应商的重要程度
	IsStrategicSupplier bool    `gorm:"column:is_strategic_supplier;default:false;comment:是否战略供应商" json:"isStrategicSupplier"` // 是否战略供应商：标识供应商的战略重要程度
	Remark              *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                      // 备注：其他相关信息

	// 关联关系
	Contacts []ScmSupplierContact `gorm:"foreignKey:SupplierID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;comment:供应商联系人" json:"contacts,omitempty"` // 供应商联系人：一对多关系
}

// TableName 设置表名
func (ScmSupplier) TableName() string {
	return "scm_supplier"
}
