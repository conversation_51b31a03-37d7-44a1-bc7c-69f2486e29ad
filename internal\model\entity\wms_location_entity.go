package entity

// GORM 支持 sql.Null* 类型处理 NULLable 字段

// LocationType 定义库位类型常量 (确保与 database_schema.md 中的 wms_location_type ENUM 一致)
type LocationType string

const (
	LocationTypeWarehouse   LocationType = "WAREHOUSE"    // 仓库 (虚拟顶层)
	LocationTypeZone        LocationType = "ZONE"         // 库区 (例如: 常温区, 冷藏区)
	LocationTypeArea        LocationType = "AREA"         // 作业区 (例如: 收货区, 存储区, 发货区, 暂存区)
	LocationTypeAisle       LocationType = "AISLE"        // 巷道
	LocationTypeRack        LocationType = "RACK"         // 货架
	LocationTypeLevel       LocationType = "LEVEL"        // 货架层
	LocationTypeBin         LocationType = "BIN"          // 货位/储位 (物理或逻辑单元, 可能包含多个 Slot)
	LocationTypeSlot        LocationType = "SLOT"         // 存储槽 (最小库存单元, 如果 Bin 需要细分)
	LocationTypeFloorSlot   LocationType = "FLOOR_SLOT"   // 地板槽
	LocationTypeDockDoor    LocationType = "DOCK_DOOR"    // 月台门
	LocationTypeStagingArea LocationType = "STAGING_AREA" // 暂存区/集货区
	LocationTypeQcArea      LocationType = "QC_AREA"      // 质量控制区
	// 可根据需要添加其他类型
)

// LocationStatus 定义库位状态常量 (确保与 database_schema.md 中的 wms_location_status ENUM 一致)
type LocationStatus string

const (
	LocationStatusActive      LocationStatus = "ACTIVE"
	LocationStatusInactive    LocationStatus = "INACTIVE"
	LocationStatusMaintenance LocationStatus = "MAINTENANCE"
	LocationStatusCounting    LocationStatus = "COUNTING"
)

// WmsLocation 对应数据库中的 wms_locations 表
// 存储仓库内所有层级的库位信息 (递归结构)
type WmsLocation struct {
	AccountBookEntity // AccountBookEntity 符合项目规范

	Code               string       `gorm:"column:code;type:varchar(50);not null;index;comment:库位/仓库代码" json:"code"`                         // 库位/仓库代码 (仅保留普通索引)
	Name               *string      `gorm:"column:name;type:varchar(100);comment:库位/仓库名称" json:"name"`                                       // 库位/仓库名称
	Type               LocationType `gorm:"column:type;type:wms_location_type;not null;index;comment:库位类型" json:"type"`                      // 库位类型 (使用 DB ENUM)
	WarehouseID        uint         `gorm:"column:warehouse_id;not null;index;comment:所属顶层仓库Location的ID" json:"warehouseId"`                 // 所属顶层仓库Location的ID (顶层记录的WarehouseID等于其自身ID)
	ParentID           *uint        `gorm:"column:parent_id;index;comment:父级库位ID" json:"parentId"`                                           // 父级库位ID (用于构建层级关系)
	Status             string       `gorm:"column:status;type:wms_location_status;not null;default:'ACTIVE';index;comment:状态" json:"status"` // 状态 (使用 DB ENUM)
	Remark             *string      `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                                // 备注
	IsPickable         bool         `gorm:"column:is_pickable;default:false;comment:是否可拣货位置" json:"isPickable"`
	IsPutaway          bool         `gorm:"column:is_putaway;default:false;comment:是否可上架位置" json:"isPutaway"`
	IsInventoryTracked bool         `gorm:"column:is_inventory_tracked;default:true;comment:是否计算库存" json:"isInventoryTracked"`

	// --- Warehouse Specific Fields (Only meaningful when Type='WAREHOUSE') ---
	Address *string `gorm:"column:address;type:text;comment:仓库地址 (仅仓库类型有效)" json:"address,omitempty"`
	Contact *string `gorm:"column:contact;type:varchar(50);comment:联系人 (仅仓库类型有效)" json:"contact,omitempty"`
	Phone   *string `gorm:"column:phone;type:varchar(30);comment:联系电话 (仅仓库类型有效)" json:"phone,omitempty"`
	// --- End Warehouse Specific Fields ---

	// 特殊属性 (来自 schema)
	TemperatureZone   *string `gorm:"column:temperature_zone;type:varchar(30);comment:温区" json:"temperatureZone"`
	HazardLevel       *string `gorm:"column:hazard_level;type:varchar(30);comment:危险品/防火等级" json:"hazardLevel"`
	SecurityLevel     *string `gorm:"column:security_level;type:varchar(30);comment:安全级别" json:"securityLevel"`
	WeightClass       *string `gorm:"column:weight_class;type:varchar(30);comment:承重等级" json:"weightClass"`
	StorageType       *string `gorm:"column:storage_type;type:varchar(50);comment:存储类型" json:"storageType"`
	RequiresEquipment *string `gorm:"column:requires_equipment;type:varchar(100);comment:所需设备" json:"requiresEquipment"`

	// 容量定义 (来自 schema)
	MaxWeightKg  *float64 `gorm:"column:max_weight_kg;type:numeric(10,2);comment:最大承重(kg)" json:"maxWeightKg"`
	MaxVolumeM3  *float64 `gorm:"column:max_volume_m3;type:numeric(10,4);comment:最大体积(m³)" json:"maxVolumeM3"`
	MaxPallets   *int64   `gorm:"column:max_pallets;type:integer;comment:最大托盘数" json:"maxPallets"`
	MaxLengthM   *float64 `gorm:"column:max_length_m;type:numeric(10,2);comment:最大长度(m)" json:"maxLengthM"`
	MaxWidthM    *float64 `gorm:"column:max_width_m;type:numeric(10,2);comment:最大宽度(m)" json:"maxWidthM"`
	MaxHeightM   *float64 `gorm:"column:max_height_m;type:numeric(10,2);comment:最大高度(m)" json:"maxHeightM"`
	MaxItemUnits *int64   `gorm:"column:max_item_units;type:integer;comment:最大存储单元数(例如箱)" json:"maxItemUnits"`

	// 内存字段，用于构建树形结构，不映射到数据库
	Children []WmsLocation `gorm:"-" json:"children,omitempty"`
}

func (WmsLocation) TableName() string {
	return "wms_location"
}
