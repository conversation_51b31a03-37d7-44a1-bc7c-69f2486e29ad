<template>
    <div class="blind-receiving-validator" :class="{ compact: compact }">
      <!-- 验证表单区域 -->
      <div class="validator-form" v-if="mode !== 'result-only'">
        <el-form
          ref="formRef"
          :model="validationParams"
          :rules="formRules"
          :inline="inline"
          :size="compact ? 'small' : 'default'"
          @submit.prevent="handleManualValidate"
        >
          <el-row :gutter="16">
            <el-col :span="inline ? 6 : 24" v-if="showWarehouse">
              <el-form-item label="仓库" prop="warehouseId">
                <el-select
                  v-model="validationParams.warehouseId"
                  placeholder="请选择仓库"
                  filterable
                  @change="handleParamChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="warehouse in warehouseOptions"
                    :key="warehouse.id"
                    :label="warehouse.name"
                    :value="warehouse.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
  
            <el-col :span="inline ? 6 : 24" v-if="showClient">
              <el-form-item label="客户" prop="clientId">
                <el-select
                  v-model="validationParams.clientId"
                  placeholder="请选择客户"
                  filterable
                  clearable
                  @change="handleParamChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="client in clientOptions"
                    :key="client.id"
                    :label="client.name"
                    :value="client.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
  
            <el-col :span="inline ? 6 : 24" v-if="showUser">
              <el-form-item label="用户" prop="userId">
                <el-select
                  v-model="validationParams.userId"
                  placeholder="请选择用户"
                  filterable
                  clearable
                  @change="handleParamChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.id"
                    :label="user.name"
                    :value="user.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
  
            <el-col :span="inline ? 6 : 24" v-if="showQuantity">
              <el-form-item label="数量" prop="quantity">
                <el-input-number
                  v-model="validationParams.quantity"
                  :min="0"
                  :precision="3"
                  placeholder="请输入数量"
                  @change="handleParamChange"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
  
          <!-- 验证按钮 -->
          <div class="validator-actions" v-if="showValidateButton">
            <el-button
              type="primary"
              :icon="Shield"
              :loading="validating"
              @click="handleManualValidate"
              :size="compact ? 'small' : 'default'"
            >
              {{ validateButtonText }}
            </el-button>
            
            <el-button
              v-if="showHistoryButton"
              :icon="Clock"
              @click="showHistoryDialog = true"
              :size="compact ? 'small' : 'default'"
            >
              验证历史
            </el-button>
          </div>
        </el-form>
      </div>
  
      <!-- 验证结果区域 -->
      <div class="validator-result" v-if="showResult && (validationResult || validating)">
        <ValidationResult
          :result="validationResult"
          :loading="validating"
          :compact="compact"
          @retry="handleManualValidate"
          @view-config="handleViewConfig"
        />
      </div>
  
      <!-- 历史记录对话框 -->
      <el-dialog
        v-model="showHistoryDialog"
        title="验证历史记录"
        width="80%"
        destroy-on-close
      >
        <ValidationHistory
          :filters="historyFilters"
          :compact="true"
        />
      </el-dialog>
  
      <!-- 配置详情对话框 -->
      <el-dialog
        v-model="showConfigDialog"
        title="生效配置详情"
        width="600px"
        destroy-on-close
      >
        <ConfigDetail :config="effectiveConfig" />
      </el-dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Shield, Clock } from '@element-plus/icons-vue'
  import { debounce } from 'lodash-es'
  import ValidationResult from './ValidationResult.vue'
  import ValidationHistory from './ValidationHistory.vue'
  import ConfigDetail from './ConfigDetail.vue'
  import { usePermission } from '@/hooks/usePermission'
  import {
    validateBlindReceiving,
    getWarehouseListForConfig,
    getClientListForConfig,
    getUserListForConfig
  } from '@/api/wms/blindReceivingConfig'
  import type {
    BlindReceivingValidationReq,
    BlindReceivingValidationVO,
    BlindReceivingConfigVO,
    AvailableTargetVO
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    // 验证参数
    warehouseId?: number
    clientId?: number
    userId?: number
    itemId?: number
    quantity?: number
    sourceType?: string
    sourceId?: number
    
    // 显示模式
    mode?: 'full' | 'inline' | 'result-only'
    compact?: boolean
    inline?: boolean
    
    // 显示控制
    showWarehouse?: boolean
    showClient?: boolean
    showUser?: boolean
    showQuantity?: boolean
    showResult?: boolean
    showValidateButton?: boolean
    showHistoryButton?: boolean
    
    // 自动验证
    autoValidate?: boolean
    autoValidateDelay?: number
    
    // 按钮文案
    validateButtonText?: string
  }
  
  const props = withDefaults(defineProps<Props>(), {
    mode: 'full',
    compact: false,
    inline: true,
    showWarehouse: true,
    showClient: true,
    showUser: true,
    showQuantity: true,
    showResult: true,
    showValidateButton: true,
    showHistoryButton: true,
    autoValidate: true,
    autoValidateDelay: 500,
    validateButtonText: '验证盲收权限'
  })
  
  // Emits
  const emit = defineEmits<{
    'validation-change': [result: BlindReceivingValidationVO | null]
    'validation-start': []
    'validation-complete': [result: BlindReceivingValidationVO]
    'validation-error': [error: any]
  }>()
  
  // 权限检查
  const { hasPermission } = usePermission()
  
  // 响应式数据
  const formRef = ref()
  const validating = ref(false)
  const validationResult = ref<BlindReceivingValidationVO | null>(null)
  const effectiveConfig = ref<BlindReceivingConfigVO | null>(null)
  const showHistoryDialog = ref(false)
  const showConfigDialog = ref(false)
  
  // 验证参数
  const validationParams = reactive<BlindReceivingValidationReq>({
    warehouseId: props.warehouseId || 0,
    clientId: props.clientId,
    userId: props.userId,
    itemId: props.itemId,
    quantity: props.quantity || 0,
    sourceType: props.sourceType,
    sourceId: props.sourceId
  })
  
  // 选项数据
  const warehouseOptions = ref<AvailableTargetVO[]>([])
  const clientOptions = ref<AvailableTargetVO[]>([])
  const userOptions = ref<AvailableTargetVO[]>([])
  
  // 表单验证规则
  const formRules = computed(() => ({
    warehouseId: [
      { required: true, message: '请选择仓库', trigger: 'change' }
    ],
    quantity: [
      { required: true, message: '请输入数量', trigger: 'blur' },
      { type: 'number', min: 0, message: '数量必须大于等于0', trigger: 'blur' }
    ]
  }))
  
  // 历史记录筛选条件
  const historyFilters = computed(() => ({
    warehouseId: validationParams.warehouseId,
    clientId: validationParams.clientId,
    userId: validationParams.userId
  }))
  
  // 执行验证
  const executeValidation = async () => {
    try {
      emit('validation-start')
      validating.value = true
      
      // 表单验证
      if (formRef.value) {
        await formRef.value.validate()
      }
      
      // 调用API验证
      const result = await validateBlindReceiving(validationParams)
      
      validationResult.value = result
      emit('validation-complete', result)
      emit('validation-change', result)
      
      // 记录验证历史（通过API自动记录）
      
    } catch (error) {
      console.error('验证失败:', error)
      validationResult.value = null
      emit('validation-error', error)
      emit('validation-change', null)
      
      if (error !== 'validation-failed') {
        ElMessage.error('验证失败，请重试')
      }
    } finally {
      validating.value = false
    }
  }
  
  // 防抖的自动验证
  const debouncedValidation = debounce(executeValidation, props.autoValidateDelay)
  
  // 事件处理
  const handleParamChange = () => {
    if (props.autoValidate && isValidParams()) {
      debouncedValidation()
    }
  }
  
  const handleManualValidate = () => {
    debouncedValidation.cancel()
    executeValidation()
  }
  
  const handleViewConfig = () => {
    if (validationResult.value) {
      showConfigDialog.value = true
      // 这里可以加载配置详情
    }
  }
  
  // 工具函数
  const isValidParams = () => {
    return validationParams.warehouseId > 0 && 
           validationParams.quantity >= 0
  }
  
  const loadOptions = async () => {
    try {
      // 加载选项数据
      const [warehouses, clients, users] = await Promise.all([
        getWarehouseListForConfig({ pageSize: 100 }),
        getClientListForConfig({ pageSize: 100 }),
        getUserListForConfig({ pageSize: 100 })
      ])
      
      warehouseOptions.value = warehouses.list
      clientOptions.value = clients.list
      userOptions.value = users.list
    } catch (error) {
      console.error('加载选项数据失败:', error)
    }
  }
  
  // 监听外部参数变化
  watch(
    () => [props.warehouseId, props.clientId, props.userId, props.quantity],
    ([warehouseId, clientId, userId, quantity]) => {
      if (warehouseId !== undefined) validationParams.warehouseId = warehouseId
      if (clientId !== undefined) validationParams.clientId = clientId
      if (userId !== undefined) validationParams.userId = userId
      if (quantity !== undefined) validationParams.quantity = quantity
      
      if (props.autoValidate && isValidParams()) {
        debouncedValidation()
      }
    },
    { immediate: true }
  )
  
  // 暴露方法给父组件
  defineExpose({
    validate: executeValidation,
    reset: () => {
      validationResult.value = null
      emit('validation-change', null)
    },
    getResult: () => validationResult.value,
    isValidating: () => validating.value
  })
  
  // 初始化
  onMounted(() => {
    loadOptions()
    
    // 如果有初始参数且开启自动验证，则执行验证
    if (props.autoValidate && isValidParams()) {
      debouncedValidation()
    }
  })
  </script>
  
  <style scoped lang="scss">
  .blind-receiving-validator {
    .validator-form {
      margin-bottom: 16px;
      
      .validator-actions {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-top: 16px;
        
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
    
    .validator-result {
      border-top: 1px solid #ebeef5;
      padding-top: 16px;
    }
    
    &.compact {
      .validator-form {
        margin-bottom: 12px;
        
        .validator-actions {
          margin-top: 12px;
        }
      }
      
      .validator-result {
        padding-top: 12px;
      }
      
      :deep(.el-form-item) {
        margin-bottom: 12px;
      }
    }
  }
  
  // 内联模式样式
  .validator-form {
    :deep(.el-form--inline) {
      .el-form-item {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }
  }
  
  // 紧凑模式下的表单项间距
  .compact {
    :deep(.el-form-item) {
      margin-bottom: 8px;
    }
    
    :deep(.el-form--inline .el-form-item) {
      margin-right: 12px;
    }
  }
  </style>