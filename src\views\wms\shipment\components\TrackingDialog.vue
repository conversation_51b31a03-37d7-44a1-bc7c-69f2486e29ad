<template>
  <el-dialog
    v-model="dialogVisible"
    title="运输跟踪信息"
    width="70%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="tracking-dialog-container" v-if="shipmentData">
      <!-- 发运单基本信息 -->
      <el-card class="shipment-info-card" shadow="never">
        <template #header>
          <span>发运单信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">发运单号:</span>
              <span class="value">{{ shipmentData.shipmentNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">运单号:</span>
              <span class="value">{{ shipmentData.trackingNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">承运商:</span>
              <span class="value">{{ shipmentData.carrierName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">当前状态:</span>
              <el-tag :type="getStatusTagType(shipmentData.status)">
                {{ formatStatus(shipmentData.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">发运时间:</span>
              <span class="value">{{ shipmentData.actualShipDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">预计送达:</span>
              <span class="value">{{ shipmentData.estimatedDeliveryDate || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">司机:</span>
              <span class="value">{{ shipmentData.driverName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">车牌号:</span>
              <span class="value">{{ shipmentData.vehicleNo || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 运输进度 -->
      <el-card class="progress-card" shadow="never">
        <template #header>
          <div class="section-header">
            <span>运输进度</span>
            <el-button
              type="primary"
              size="small"
              @click="handleRefreshTracking"
              :loading="refreshing"
            >
              刷新跟踪
            </el-button>
          </div>
        </template>
        
        <div class="progress-timeline">
          <el-steps
            :active="currentStep"
            :process-status="processStatus"
            direction="horizontal"
            align-center
          >
            <el-step
              v-for="step in progressSteps"
              :key="step.key"
              :title="step.title"
              :description="step.description"
              :status="step.status"
            />
          </el-steps>
        </div>
      </el-card>
      
      <!-- 跟踪记录 -->
      <el-card class="tracking-records-card" shadow="never">
        <template #header>
          <span>跟踪记录</span>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="record in trackingRecords"
            :key="record.id"
            :timestamp="record.timestamp"
            :type="getTimelineType(record.type)"
            :icon="getTimelineIcon(record.type)"
            placement="top"
          >
            <el-card class="timeline-card">
              <div class="record-header">
                <span class="record-title">{{ record.title }}</span>
                <el-tag :type="getRecordTagType(record.type)" size="small">
                  {{ record.typeName }}
                </el-tag>
              </div>
              <div class="record-content">
                <p>{{ record.description }}</p>
                <div v-if="record.location" class="record-location">
                  <el-icon><Location /></el-icon>
                  <span>{{ record.location }}</span>
                </div>
                <div v-if="record.operator" class="record-operator">
                  <el-icon><User /></el-icon>
                  <span>操作人: {{ record.operator }}</span>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <!-- 空状态 -->
        <div v-if="trackingRecords.length === 0" class="empty-state">
          <el-empty description="暂无跟踪记录" :image-size="80" />
        </div>
      </el-card>
      
      <!-- 地图跟踪 -->
      <el-card class="map-card" shadow="never" v-if="showMap">
        <template #header>
          <span>实时位置</span>
        </template>
        
        <div class="map-container">
          <div class="map-placeholder">
            <el-icon size="48"><Location /></el-icon>
            <p>地图功能开发中...</p>
            <p>当前位置: {{ currentLocation || '获取中...' }}</p>
          </div>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleExportTracking"
          v-if="trackingRecords.length > 0"
        >
          导出跟踪记录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElTag, ElSteps, ElStep, ElTimeline, ElTimelineItem, ElIcon, ElEmpty } from 'element-plus'
import { Location, User, Truck, CircleCheck, Warning } from '@element-plus/icons-vue'
import { useShipmentStore } from '@/store/wms/shipment'

// Store
const shipmentStore = useShipmentStore()

// 响应式数据
const dialogVisible = ref(false)
const refreshing = ref(false)
const shipmentData = ref<any>(null)
const trackingRecords = ref<any[]>([])
const currentLocation = ref('')
const showMap = ref(false)

// 运输进度步骤
const progressSteps = ref([
  {
    key: 'SHIPPED',
    title: '已发运',
    description: '货物已发出',
    status: 'finish'
  },
  {
    key: 'IN_TRANSIT',
    title: '运输中',
    description: '货物在途中',
    status: 'process'
  },
  {
    key: 'ARRIVED',
    title: '已到达',
    description: '货物已到达目的地',
    status: 'wait'
  },
  {
    key: 'DELIVERED',
    title: '已签收',
    description: '货物已签收',
    status: 'wait'
  }
])

// 计算属性
const currentStep = computed(() => {
  if (!shipmentData.value) return 0
  
  const statusStepMap: Record<string, number> = {
    'SHIPPED': 1,
    'IN_TRANSIT': 2,
    'DELIVERED': 4
  }
  
  return statusStepMap[shipmentData.value.status] || 0
})

const processStatus = computed(() => {
  if (!shipmentData.value) return 'process'
  
  if (shipmentData.value.status === 'DELIVERED') {
    return 'success'
  } else if (shipmentData.value.status === 'CANCELLED') {
    return 'error'
  }
  
  return 'process'
})

// 方法
const loadShipmentData = async (shipmentId: number) => {
  try {
    const data = await shipmentStore.fetchDetail(shipmentId)
    shipmentData.value = data
    
    // 加载跟踪信息
    await loadTrackingInfo(shipmentId)
  } catch (error) {
    ElMessage.error('加载发运单数据失败')
    console.error(error)
  }
}

const loadTrackingInfo = async (shipmentId: number) => {
  try {
    const trackingInfo = await shipmentStore.getTrackingInfo(shipmentId)
    trackingRecords.value = trackingInfo.records || []
    currentLocation.value = trackingInfo.currentLocation || ''
    
    // 更新进度步骤状态
    updateProgressSteps(trackingInfo.records)
  } catch (error) {
    console.error('加载跟踪信息失败:', error)
    // 模拟跟踪记录
    generateMockTrackingRecords()
  }
}

const generateMockTrackingRecords = () => {
  if (!shipmentData.value) return
  
  const mockRecords = [
    {
      id: 1,
      timestamp: shipmentData.value.actualShipDate || new Date().toISOString(),
      type: 'SHIPPED',
      typeName: '已发运',
      title: '货物已发出',
      description: '货物已从发货仓库发出，开始运输',
      location: '上海市浦东新区物流园区',
      operator: shipmentData.value.driverName || '张师傅'
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      type: 'IN_TRANSIT',
      typeName: '运输中',
      title: '货物运输中',
      description: '货物正在运输途中，一切正常',
      location: '江苏省苏州市服务区',
      operator: shipmentData.value.driverName || '张师傅'
    }
  ]
  
  if (shipmentData.value.status === 'DELIVERED') {
    mockRecords.push({
      id: 3,
      timestamp: shipmentData.value.actualDeliveryDate || new Date().toISOString(),
      type: 'DELIVERED',
      typeName: '已签收',
      title: '货物已签收',
      description: '货物已送达并完成签收',
      location: shipmentData.value.consigneeAddress,
      operator: shipmentData.value.consigneeName
    })
  }
  
  trackingRecords.value = mockRecords.reverse() // 最新的在前面
}

const updateProgressSteps = (records: any[]) => {
  const statusMap: Record<string, number> = {
    'SHIPPED': 0,
    'IN_TRANSIT': 1,
    'ARRIVED': 2,
    'DELIVERED': 3
  }
  
  records.forEach(record => {
    const stepIndex = statusMap[record.type]
    if (stepIndex !== undefined && progressSteps.value[stepIndex]) {
      progressSteps.value[stepIndex].status = 'finish'
      progressSteps.value[stepIndex].description = record.timestamp
    }
  })
}

const handleOpen = () => {
  // 在open方法中会传入shipmentId
}

const handleClose = () => {
  resetData()
  dialogVisible.value = false
}

const resetData = () => {
  shipmentData.value = null
  trackingRecords.value = []
  currentLocation.value = ''
  
  // 重置进度步骤状态
  progressSteps.value.forEach((step, index) => {
    step.status = index === 0 ? 'finish' : 'wait'
  })
}

const handleRefreshTracking = async () => {
  if (!shipmentData.value) return
  
  refreshing.value = true
  try {
    await loadTrackingInfo(shipmentData.value.id)
    ElMessage.success('跟踪信息已更新')
  } catch (error) {
    ElMessage.error('刷新跟踪信息失败')
  } finally {
    refreshing.value = false
  }
}

const handleExportTracking = () => {
  // 导出跟踪记录功能
  ElMessage.info('导出功能开发中...')
}

// 工具方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'SHIPPED': 'success',
    'IN_TRANSIT': 'primary',
    'DELIVERED': 'success',
    'CANCELLED': 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'SHIPPED': '已发运',
    'IN_TRANSIT': '运输中',
    'DELIVERED': '已送达',
    'CANCELLED': '已取消'
  }
  return labelMap[status] || '未知'
}

const getTimelineType = (type: string) => {
  const typeMap: Record<string, string> = {
    'SHIPPED': 'success',
    'IN_TRANSIT': 'primary',
    'ARRIVED': 'warning',
    'DELIVERED': 'success',
    'EXCEPTION': 'danger'
  }
  return typeMap[type] || 'info'
}

const getTimelineIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'SHIPPED': Truck,
    'IN_TRANSIT': Location,
    'ARRIVED': Warning,
    'DELIVERED': CircleCheck,
    'EXCEPTION': Warning
  }
  return iconMap[type] || Location
}

const getRecordTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'SHIPPED': 'success',
    'IN_TRANSIT': 'primary',
    'ARRIVED': 'warning',
    'DELIVERED': 'success',
    'EXCEPTION': 'danger'
  }
  return typeMap[type] || 'info'
}

// 暴露方法
const open = (shipmentId: number) => {
  dialogVisible.value = true
  loadShipmentData(shipmentId)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.tracking-dialog-container {
  padding: 16px 0;
}

.shipment-info-card,
.progress-card,
.tracking-records-card,
.map-card {
  margin-bottom: 16px;
}

.shipment-info-card :deep(.el-card__header),
.progress-card :deep(.el-card__header),
.tracking-records-card :deep(.el-card__header),
.map-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-timeline {
  padding: 20px 0;
}

.timeline-card {
  margin-bottom: 0;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-title {
  font-weight: 600;
  color: #303133;
}

.record-content p {
  margin: 0 0 8px 0;
  color: #606266;
}

.record-location,
.record-operator {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #909399;
}

.record-location .el-icon,
.record-operator .el-icon {
  margin-right: 4px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.map-container {
  height: 300px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.map-placeholder {
  text-align: center;
  color: #909399;
}

.map-placeholder .el-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
}

.dialog-footer {
  text-align: right;
}
</style>
