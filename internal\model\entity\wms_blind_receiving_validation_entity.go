package entity

import (
	"time"

	"gorm.io/gorm"
)

// WmsBlindReceivingValidation 盲收验证记录实体
type WmsBlindReceivingValidation struct {
	AccountBookEntity // 继承账套实体

	ValidationID       string     `gorm:"column:validation_id;type:varchar(50);not null;uniqueIndex;comment:验证ID"`
	WarehouseID        uint       `gorm:"column:warehouse_id;not null;index;comment:仓库ID"`
	ClientID           *uint      `gorm:"column:client_id;index;comment:客户ID"`
	UserID             *uint      `gorm:"column:user_id;index;comment:用户ID"`
	ConfigID           uint       `gorm:"column:config_id;not null;index;comment:生效配置ID"`
	Strategy           string     `gorm:"column:strategy;type:varchar(20);not null;comment:应用策略"`
	RequestQuantity    float64    `gorm:"column:request_quantity;type:numeric(15,3);not null;comment:请求数量"`
	IsAllowed          bool       `gorm:"column:is_allowed;not null;comment:是否允许"`
	ValidationMessage  string     `gorm:"column:validation_message;type:text;comment:验证消息"`
	ItemID             *uint      `gorm:"column:item_id;index;comment:物料ID"`
	SourceType         *string    `gorm:"column:source_type;type:varchar(20);comment:来源类型"`
	SourceID           *uint      `gorm:"column:source_id;comment:来源ID"`
	ApprovalStatus     *string    `gorm:"column:approval_status;type:varchar(20);comment:审批状态"`
	SupplementStatus   *string    `gorm:"column:supplement_status;type:varchar(20);comment:补录状态"`
	SupplementDeadline *time.Time `gorm:"column:supplement_deadline;comment:补录截止时间"`
	ProcessedAt        *time.Time `gorm:"column:processed_at;comment:处理时间"`
	ProcessedBy        *uint      `gorm:"column:processed_by;comment:处理人ID"`
	Remark             *string    `gorm:"column:remark;type:varchar(500);comment:备注"`
}

// TableName 指定表名
func (WmsBlindReceivingValidation) TableName() string {
	return "wms_blind_receiving_validation"
}

// BeforeUpdate GORM钩子：更新前
func (v *WmsBlindReceivingValidation) BeforeUpdate(tx *gorm.DB) error {
	// 设置更新时间
	v.UpdatedAt = time.Now()
	return nil
}

// GetValidationResult 获取验证结果摘要
func (v *WmsBlindReceivingValidation) GetValidationResult() string {
	if v.IsAllowed {
		return "ALLOWED"
	}
	return "DENIED"
}

// IsProcessed 检查是否已处理
func (v *WmsBlindReceivingValidation) IsProcessed() bool {
	return v.ProcessedAt != nil
}

// IsSupplementOverdue 检查补录是否逾期
func (v *WmsBlindReceivingValidation) IsSupplementOverdue() bool {
	if v.SupplementDeadline == nil {
		return false
	}
	return time.Now().After(*v.SupplementDeadline)
}

// GetProcessingDuration 获取处理耗时（小时）
func (v *WmsBlindReceivingValidation) GetProcessingDuration() *float64 {
	if !v.IsProcessed() {
		return nil
	}
	duration := v.ProcessedAt.Sub(v.CreatedAt).Hours()
	return &duration
}
