package service

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/copier"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
)

// WmsBlindReceivingConfigService 盲收配置服务接口
type WmsBlindReceivingConfigService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsBlindReceivingConfigCreateReq) (*vo.WmsBlindReceivingConfigVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsBlindReceivingConfigUpdateReq) (*vo.WmsBlindReceivingConfigVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsBlindReceivingConfigVO, error)
	GetPage(ctx context.Context, query *dto.WmsBlindReceivingConfigQueryReq) (*response.PageResult, error)

	// 批量操作
	CreateBatch(ctx context.Context, req *dto.WmsBlindReceivingConfigBatchCreateReq) (*dto.BatchOperationResult, error)
	UpdateBatch(ctx context.Context, req *dto.WmsBlindReceivingConfigBatchUpdateReq) (*dto.BatchOperationResult, error)
	BatchDelete(ctx context.Context, ids []uint) (*dto.BatchOperationResult, error)
	BatchToggle(ctx context.Context, ids []uint, isActive bool) (*dto.BatchOperationResult, error)

	// 核心业务方法
	GetEffectiveConfig(ctx context.Context, req *dto.GetEffectiveConfigReq) (*vo.WmsBlindReceivingConfigVO, error)
	ValidateBlindReceiving(ctx context.Context, req *dto.BlindReceivingValidationReq) (*dto.BlindReceivingValidationResp, error)

	// 配置管理
	GetAvailableTargets(ctx context.Context, req *dto.AvailableConfigTargetsReq) (*dto.AvailableConfigTargetsResp, error)
	GetActiveConfigs(ctx context.Context, configLevel string) ([]*vo.WmsBlindReceivingConfigVO, error)
	GetConfigsByStrategy(ctx context.Context, strategy string) ([]*vo.WmsBlindReceivingConfigVO, error)

	// 审批和补录
	ProcessApproval(ctx context.Context, req *dto.BlindReceivingApprovalReq) error
	ProcessSupplement(ctx context.Context, req *dto.BlindReceivingSupplementReq) error

	// 业务流程查询
	GetPendingApprovalList(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error)
	GetPendingSupplementList(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error)
	GetOverdueSupplementList(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error)

	// 统计分析
	GetStats(ctx context.Context, req *dto.BlindReceivingStatsReq) (*dto.BlindReceivingStatsResp, error)
}

// wmsBlindReceivingConfigServiceImpl 盲收配置服务实现
type wmsBlindReceivingConfigServiceImpl struct {
	*BaseServiceImpl
}

// NewWmsBlindReceivingConfigService 创建盲收配置服务
func NewWmsBlindReceivingConfigService(sm *ServiceManager) WmsBlindReceivingConfigService {
	return &wmsBlindReceivingConfigServiceImpl{
		BaseServiceImpl: NewBaseService(sm),
	}
}

// Create 创建盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) Create(ctx context.Context, req *dto.WmsBlindReceivingConfigCreateReq) (*vo.WmsBlindReceivingConfigVO, error) {
	var result *vo.WmsBlindReceivingConfigVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		configRepo := txRepoMgr.GetWmsBlindReceivingConfigRepository()

		// 1. 验证配置目标的有效性
		isValid, err := configRepo.ValidateConfigTarget(ctx, req.ConfigLevel, req.ConfigTargetID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证配置目标失败").WithCause(err)
		}
		if !isValid {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "配置目标无效")
		}

		// 2. 检查是否存在冲突的配置
		conflictingConfigs, err := configRepo.FindConflictingConfigs(ctx, req.ConfigLevel, req.ConfigTargetID, nil)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "检查配置冲突失败").WithCause(err)
		}
		if len(conflictingConfigs) > 0 {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "同级别配置已存在，请先删除或修改现有配置")
		}

		// 3. 创建配置实体
		config := &entity.WmsBlindReceivingConfig{
			ConfigLevel:          req.ConfigLevel,
			ConfigTargetID:       req.ConfigTargetID,
			Strategy:             req.Strategy,
			SupplementTimeLimit:  req.SupplementTimeLimit,
			RequiresApproval:     req.RequiresApproval,
			ApprovalUserRoles:    req.ApprovalUserRoles,
			MaxBlindReceivingQty: req.MaxBlindReceivingQty,
			IsActive:             req.IsActive,
			Priority:             req.Priority,
		}

		// 4. 保存配置
		if err := configRepo.Create(ctx, config); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建盲收配置失败").WithCause(err)
		}

		// 5. 转换为VO
		result = &vo.WmsBlindReceivingConfigVO{}
		copier.Copy(result, config)

		// 6. 填充完整配置信息
		s.fillConfigInfo(ctx, result, txRepoMgr)

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// Update 更新盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsBlindReceivingConfigUpdateReq) (*vo.WmsBlindReceivingConfigVO, error) {
	var result *vo.WmsBlindReceivingConfigVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		configRepo := txRepoMgr.GetWmsBlindReceivingConfigRepository()

		// 1. 查询现有配置
		config, err := configRepo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_DATA_NOT_FOUND, "盲收配置不存在").WithCause(err)
		}

		// 2. 更新字段
		if req.Strategy != nil {
			config.Strategy = *req.Strategy
		}
		if req.SupplementTimeLimit != nil {
			config.SupplementTimeLimit = req.SupplementTimeLimit
		}
		if req.RequiresApproval != nil {
			config.RequiresApproval = *req.RequiresApproval
		}
		if req.ApprovalUserRoles != nil {
			config.ApprovalUserRoles = req.ApprovalUserRoles
		}
		if req.MaxBlindReceivingQty != nil {
			config.MaxBlindReceivingQty = req.MaxBlindReceivingQty
		}
		if req.IsActive != nil {
			config.IsActive = *req.IsActive
		}
		if req.Priority != nil {
			config.Priority = *req.Priority
		}

		// 3. 保存更新
		if err := configRepo.Update(ctx, config); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新盲收配置失败").WithCause(err)
		}

		// 4. 转换为VO
		result = &vo.WmsBlindReceivingConfigVO{}
		copier.Copy(result, config)

		// 5. 填充完整配置信息
		s.fillConfigInfo(ctx, result, txRepoMgr)

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// Delete 删除盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		configRepo := txRepoMgr.GetWmsBlindReceivingConfigRepository()

		// 检查配置是否存在
		_, err := configRepo.FindByID(ctx, id)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_DATA_NOT_FOUND, "盲收配置不存在").WithCause(err)
		}

		// 删除配置
		if err := configRepo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除盲收配置失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsBlindReceivingConfigVO, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()

	config, err := configRepo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_DATA_NOT_FOUND, "盲收配置不存在").WithCause(err)
	}

	var result vo.WmsBlindReceivingConfigVO
	copier.Copy(&result, config)

	// 填充完整配置信息
	s.fillConfigInfo(ctx, &result, s.GetServiceManager().GetRepositoryManager())

	return &result, nil
}

// GetPage 分页查询盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) GetPage(ctx context.Context, query *dto.WmsBlindReceivingConfigQueryReq) (*response.PageResult, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()
	return configRepo.GetPage(ctx, query)
}

// CreateBatch 批量创建盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) CreateBatch(ctx context.Context, req *dto.WmsBlindReceivingConfigBatchCreateReq) (*dto.BatchOperationResult, error) {
	result := &dto.BatchOperationResult{
		TotalCount:     len(req.Configs),
		SuccessCount:   0,
		FailureCount:   0,
		SuccessIDs:     []uint{},
		FailureReasons: []string{},
	}

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		for i, configReq := range req.Configs {
			configVO, err := s.Create(ctx, &configReq)
			if err != nil {
				result.FailureCount++
				result.FailureReasons = append(result.FailureReasons, fmt.Sprintf("索引 %d: %s", i, err.Error()))
			} else {
				result.SuccessCount++
				result.SuccessIDs = append(result.SuccessIDs, configVO.ID)
			}
		}
		return nil
	})

	if err != nil {
		return result, err
	}

	return result, nil
}

// UpdateBatch 批量更新盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) UpdateBatch(ctx context.Context, req *dto.WmsBlindReceivingConfigBatchUpdateReq) (*dto.BatchOperationResult, error) {
	result := &dto.BatchOperationResult{
		TotalCount:     len(req.Updates),
		SuccessCount:   0,
		FailureCount:   0,
		SuccessIDs:     []uint{},
		FailureReasons: []string{},
	}

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		for _, updateReq := range req.Updates {
			_, err := s.Update(ctx, updateReq.ID, &updateReq.UpdateData)
			if err != nil {
				result.FailureCount++
				result.FailureReasons = append(result.FailureReasons, fmt.Sprintf("ID %d: %s", updateReq.ID, err.Error()))
			} else {
				result.SuccessCount++
				result.SuccessIDs = append(result.SuccessIDs, updateReq.ID)
			}
		}
		return nil
	})

	if err != nil {
		return result, err
	}

	return result, nil
}

// BatchDelete 批量删除盲收配置
func (s *wmsBlindReceivingConfigServiceImpl) BatchDelete(ctx context.Context, ids []uint) (*dto.BatchOperationResult, error) {
	result := &dto.BatchOperationResult{
		TotalCount:     len(ids),
		SuccessCount:   0,
		FailureCount:   0,
		SuccessIDs:     []uint{},
		FailureReasons: []string{},
	}

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		for _, id := range ids {
			err := s.Delete(ctx, id)
			if err != nil {
				result.FailureCount++
				result.FailureReasons = append(result.FailureReasons, fmt.Sprintf("ID %d: %s", id, err.Error()))
			} else {
				result.SuccessCount++
				result.SuccessIDs = append(result.SuccessIDs, id)
			}
		}
		return nil
	})

	if err != nil {
		return result, err
	}

	return result, nil
}

// BatchToggle 批量状态切换
func (s *wmsBlindReceivingConfigServiceImpl) BatchToggle(ctx context.Context, ids []uint, isActive bool) (*dto.BatchOperationResult, error) {
	result := &dto.BatchOperationResult{
		TotalCount:     len(ids),
		SuccessCount:   0,
		FailureCount:   0,
		SuccessIDs:     []uint{},
		FailureReasons: []string{},
	}

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		configRepo := txRepoMgr.GetWmsBlindReceivingConfigRepository()

		for _, id := range ids {
			// 查询配置是否存在
			config, err := configRepo.FindByID(ctx, id)
			if err != nil {
				result.FailureCount++
				result.FailureReasons = append(result.FailureReasons, fmt.Sprintf("ID %d: 配置不存在", id))
				continue
			}

			// 更新状态
			config.IsActive = isActive
			if err := configRepo.Update(ctx, config); err != nil {
				result.FailureCount++
				result.FailureReasons = append(result.FailureReasons, fmt.Sprintf("ID %d: %s", id, err.Error()))
			} else {
				result.SuccessCount++
				result.SuccessIDs = append(result.SuccessIDs, id)
			}
		}
		return nil
	})

	if err != nil {
		return result, err
	}

	return result, nil
}

// GetEffectiveConfig 获取有效配置（核心业务方法）
func (s *wmsBlindReceivingConfigServiceImpl) GetEffectiveConfig(ctx context.Context, req *dto.GetEffectiveConfigReq) (*vo.WmsBlindReceivingConfigVO, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()

	// 使用多层级优先级算法
	config, err := configRepo.GetEffectiveConfig(ctx, req.WarehouseID, req.ClientID, req.UserID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取有效盲收配置失败").WithCause(err)
	}

	var result vo.WmsBlindReceivingConfigVO
	copier.Copy(&result, config)

	// 填充完整配置信息
	s.fillConfigInfo(ctx, &result, s.GetServiceManager().GetRepositoryManager())

	return &result, nil
}

// ValidateBlindReceiving 验证盲收（核心业务方法）
func (s *wmsBlindReceivingConfigServiceImpl) ValidateBlindReceiving(ctx context.Context, req *dto.BlindReceivingValidationReq) (*dto.BlindReceivingValidationResp, error) {
	// 1. 获取有效配置
	effectiveConfigReq := &dto.GetEffectiveConfigReq{
		WarehouseID: req.WarehouseID,
		ClientID:    req.ClientID,
		UserID:      req.UserID,
	}

	configVO, err := s.GetEffectiveConfig(ctx, effectiveConfigReq)
	if err != nil {
		return nil, err
	}

	// 2. 根据策略进行验证
	resp := &dto.BlindReceivingValidationResp{
		Strategy:          configVO.Strategy,
		ConfigLevel:       configVO.ConfigLevel,
		ApprovalUserRoles: configVO.ApprovalUserRoles,
	}

	switch configVO.Strategy {
	case string(entity.StrategyStrict):
		// 严格模式：不允许盲收
		resp.IsAllowed = false
		resp.ValidationMessage = "当前配置不允许盲收，请先创建入库通知单"

	case string(entity.StrategySupplement):
		// 补录模式：允许盲收但需补录ASN
		resp.IsAllowed = true
		resp.RequiresApproval = configVO.RequiresApproval
		resp.MaxQuantityLimit = configVO.MaxBlindReceivingQty

		// 计算补录截止时间
		if configVO.SupplementTimeLimit != nil && *configVO.SupplementTimeLimit > 0 {
			deadline := time.Now().Add(time.Duration(*configVO.SupplementTimeLimit) * time.Hour)
			resp.SupplementDeadline = &deadline
		}

		// 检查数量限制
		if configVO.MaxBlindReceivingQty != nil && req.Quantity > *configVO.MaxBlindReceivingQty {
			resp.IsAllowed = false
			resp.ValidationMessage = fmt.Sprintf("盲收数量 %.2f 超过限制 %.2f", req.Quantity, *configVO.MaxBlindReceivingQty)
		} else {
			resp.ValidationMessage = "允许盲收，请在规定时间内补录入库通知单"
		}

	case string(entity.StrategyFull):
		// 完全模式：允许盲收，无需补录
		resp.IsAllowed = true
		resp.RequiresApproval = configVO.RequiresApproval
		resp.MaxQuantityLimit = configVO.MaxBlindReceivingQty

		// 检查数量限制
		if configVO.MaxBlindReceivingQty != nil && req.Quantity > *configVO.MaxBlindReceivingQty {
			resp.IsAllowed = false
			resp.ValidationMessage = fmt.Sprintf("盲收数量 %.2f 超过限制 %.2f", req.Quantity, *configVO.MaxBlindReceivingQty)
		} else {
			resp.ValidationMessage = "允许盲收，无需补录通知单"
		}

	default:
		resp.IsAllowed = false
		resp.ValidationMessage = "未知的盲收策略配置"
	}

	return resp, nil
}

// GetAvailableTargets 获取可用配置目标
func (s *wmsBlindReceivingConfigServiceImpl) GetAvailableTargets(ctx context.Context, req *dto.AvailableConfigTargetsReq) (*dto.AvailableConfigTargetsResp, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()

	targets, err := configRepo.GetAvailableTargets(ctx, req.ConfigLevel)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取可用配置目标失败").WithCause(err)
	}

	return &dto.AvailableConfigTargetsResp{
		Targets: targets,
	}, nil
}

// GetActiveConfigs 获取激活的配置
func (s *wmsBlindReceivingConfigServiceImpl) GetActiveConfigs(ctx context.Context, configLevel string) ([]*vo.WmsBlindReceivingConfigVO, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()

	configs, err := configRepo.GetActiveConfigs(ctx, configLevel)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取激活配置失败").WithCause(err)
	}

	var results []*vo.WmsBlindReceivingConfigVO
	for _, config := range configs {
		var vo vo.WmsBlindReceivingConfigVO
		copier.Copy(&vo, config)
		s.fillConfigInfo(ctx, &vo, s.GetServiceManager().GetRepositoryManager())
		results = append(results, &vo)
	}

	return results, nil
}

// GetConfigsByStrategy 根据策略获取配置
func (s *wmsBlindReceivingConfigServiceImpl) GetConfigsByStrategy(ctx context.Context, strategy string) ([]*vo.WmsBlindReceivingConfigVO, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()

	configs, err := configRepo.GetConfigsByStrategy(ctx, strategy)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "根据策略获取配置失败").WithCause(err)
	}

	var results []*vo.WmsBlindReceivingConfigVO
	for _, config := range configs {
		var vo vo.WmsBlindReceivingConfigVO
		copier.Copy(&vo, config)
		s.fillConfigInfo(ctx, &vo, s.GetServiceManager().GetRepositoryManager())
		results = append(results, &vo)
	}

	return results, nil
}

// ProcessApproval 处理盲收审批
func (s *wmsBlindReceivingConfigServiceImpl) ProcessApproval(ctx context.Context, req *dto.BlindReceivingApprovalReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		receivingRepo := txRepoMgr.GetWmsReceivingRecordRepository()
		validationRepo := txRepoMgr.GetWmsBlindReceivingValidationRepository()

		// 查询收货记录
		record, err := receivingRepo.FindByID(ctx, req.ReceivingRecordID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_DATA_NOT_FOUND, "收货记录不存在").WithCause(err)
		}

		// 验证是否为盲收记录
		if record.ReceivingType != entity.ReceivingTypeBlind {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "该记录不是盲收记录")
		}

		// 查询关联的验证记录
		validations, err := validationRepo.GetByWarehouseAndCustomer(ctx, record.WarehouseID, record.ClientID, 10)
		if err != nil {
			return err
		}

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}

		// 更新审批状态
		switch req.ApprovalAction {
		case "APPROVE":
			record.Status = "APPROVED"
			// 更新验证记录状态
			for _, validation := range validations {
				if validation.ApprovalStatus == nil || *validation.ApprovalStatus == "PENDING" {
					validation.ApprovalStatus = &req.ApprovalAction
					validation.Remark = &req.ApprovalNotes
					now := time.Now()
					validation.ProcessedAt = &now
					userIDUint := uint(userID)
					validation.ProcessedBy = &userIDUint
					validation.UpdatedBy = uint(userID)
					validation.UpdatedAt = now
					validationRepo.Update(ctx, validation)
				}
			}
		case "REJECT":
			record.Status = "REJECTED"
			// 更新验证记录状态
			for _, validation := range validations {
				if validation.ApprovalStatus == nil || *validation.ApprovalStatus == "PENDING" {
					validation.ApprovalStatus = &req.ApprovalAction
					validation.Remark = &req.ApprovalNotes
					now := time.Now()
					validation.ProcessedAt = &now
					userIDUint := uint(userID)
					validation.ProcessedBy = &userIDUint
					validation.UpdatedBy = uint(userID)
					validation.UpdatedAt = now
					validationRepo.Update(ctx, validation)
				}
			}
		default:
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "无效的审批动作")
		}

		// 保存收货记录更新
		if err := receivingRepo.Update(ctx, record); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新审批状态失败").WithCause(err)
		}

		return nil
	})
}

// ProcessSupplement 处理盲收补录
func (s *wmsBlindReceivingConfigServiceImpl) ProcessSupplement(ctx context.Context, req *dto.BlindReceivingSupplementReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		receivingRepo := txRepoMgr.GetWmsReceivingRecordRepository()
		validationRepo := txRepoMgr.GetWmsBlindReceivingValidationRepository()
		inboundRepo := txRepoMgr.GetWmsInboundNotificationRepository()
		supplierRepo := txRepoMgr.GetScmSupplierRepository()

		// 1. 查询并验证收货记录
		record, err := receivingRepo.FindByID(ctx, req.ReceivingRecordID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_DATA_NOT_FOUND, "收货记录不存在").WithCause(err)
		}
		if record.ReceivingType != entity.ReceivingTypeBlind {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "该记录不是盲收记录")
		}
		// 根据实体定义，使用COMPLETED作为最终状态
		if record.Status == string(entity.ReceivingRecordStatusCompleted) {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "该记录已完成，请勿重复操作")
		}

		// 2. 强制从上下文获取用户ID
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}

		// 3. 查询供应商信息以获取 SupplierCode
		supplier, err := supplierRepo.FindByID(ctx, req.SupplierID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_DATA_NOT_FOUND, "供应商信息不存在").WithCause(err)
		}

		now := time.Now()
		dateStr := now.Format("2006-01-02")

		// 4. 创建入库通知单
		inboundNotification := &entity.WmsInboundNotification{
			WarehouseID:            record.WarehouseID,
			ClientID:               record.ClientID,
			NotificationType:       "PO", // 默认为采购订单
			Status:                 string(entity.InboundNotificationStatusPlanned),
			SupplierShipper:        &supplier.SupplierCode,
			ExpectedArrivalDateStr: &dateStr,
			Remark:                 &req.SupplementNotes,
		}
		// 正确设置审计字段
		inboundNotification.CreatedBy = uint(userID)

		if err := inboundRepo.Create(ctx, inboundNotification); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建入库通知单失败").WithCause(err)
		}

		// 5. 更新收货记录
		record.Status = string(entity.ReceivingRecordStatusCompleted)
		record.SupplierShipper = &supplier.SupplierCode // 根据指示，使用 supplierCode
		record.NotificationID = &inboundNotification.ID // 关联新创建的通知单ID
		record.Remark = &req.SupplementNotes

		if err := receivingRepo.Update(ctx, record); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新收货记录失败").WithCause(err)
		}

		// 6. 更新关联的验证记录
		validations, err := validationRepo.GetByWarehouseAndCustomer(ctx, record.WarehouseID, record.ClientID, 10)
		if err != nil {
			return err
		}

		userIDUint := uint(userID)
		supplementStatus := "COMPLETED" // 使用字符串，因为验证实体没有定义状态常量

		for _, validation := range validations {
			// 确保是待处理的补录策略
			if validation.Strategy == string(entity.StrategySupplement) && (validation.SupplementStatus == nil || *validation.SupplementStatus == "PENDING") {
				validation.SupplementStatus = &supplementStatus
				validation.Remark = &req.SupplementNotes
				validation.ProcessedAt = &now
				validation.ProcessedBy = &userIDUint
				if err := validationRepo.Update(ctx, validation); err != nil {
					s.GetLogger().Errorf("更新验证记录 %d 失败: %v", validation.ID, err)
				}
			}
		}

		return nil
	})
}

// GetStats 获取盲收统计
func (s *wmsBlindReceivingConfigServiceImpl) GetStats(ctx context.Context, req *dto.BlindReceivingStatsReq) (*dto.BlindReceivingStatsResp, error) {
	configRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingConfigRepository()
	return configRepo.GetConfigStats(ctx, &req.WarehouseID)
}

// GetPendingApprovalList 获取待审批列表
func (s *wmsBlindReceivingConfigServiceImpl) GetPendingApprovalList(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error) {
	validationService := s.GetServiceManager().GetWmsBlindReceivingValidationService()
	return validationService.GetPendingValidations(ctx, warehouseID)
}

// GetPendingSupplementList 获取待补录列表
func (s *wmsBlindReceivingConfigServiceImpl) GetPendingSupplementList(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error) {
	// 通过验证记录Service获取待补录的记录
	// 这里简化实现，实际应该根据补录状态和截止时间过滤
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()

	// 查询需要补录的验证记录（假设验证结果为PENDING且策略为SUPPLEMENT）
	validations, err := validationRepo.GetPendingValidations(ctx, warehouseID)
	if err != nil {
		return nil, err
	}

	// 过滤出需要补录的记录
	var pendingSupplements []*entity.WmsBlindReceivingValidation
	for _, validation := range validations {
		if validation.Strategy == string(entity.StrategySupplement) {
			pendingSupplements = append(pendingSupplements, validation)
		}
	}

	// 转换为VO
	validationService := s.GetServiceManager().GetWmsBlindReceivingValidationService()
	var result []*vo.WmsBlindReceivingValidationVO
	for _, validation := range pendingSupplements {
		vo, err := validationService.GetByID(ctx, uint64(validation.ID))
		if err == nil && vo != nil {
			result = append(result, vo)
		}
	}

	return result, nil
}

// GetOverdueSupplementList 获取逾期补录列表
func (s *wmsBlindReceivingConfigServiceImpl) GetOverdueSupplementList(ctx context.Context, warehouseID uint) ([]*vo.WmsBlindReceivingValidationVO, error) {
	// 通过验证记录Service获取逾期补录的记录
	validationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsBlindReceivingValidationRepository()

	// 查询待处理的验证记录
	validations, err := validationRepo.GetPendingValidations(ctx, warehouseID)
	if err != nil {
		return nil, err
	}

	// 过滤出逾期补录的记录
	var overdueSupplements []*entity.WmsBlindReceivingValidation
	now := time.Now()

	for _, validation := range validations {
		if validation.Strategy == string(entity.StrategySupplement) {
			// 检查是否逾期（这里简化为创建24小时后视为逾期）
			if now.Sub(validation.CreatedAt).Hours() > 24 {
				overdueSupplements = append(overdueSupplements, validation)
			}
		}
	}

	// 转换为VO
	validationService := s.GetServiceManager().GetWmsBlindReceivingValidationService()
	var result []*vo.WmsBlindReceivingValidationVO
	for _, validation := range overdueSupplements {
		vo, err := validationService.GetByID(ctx, uint64(validation.ID))
		if err == nil && vo != nil {
			result = append(result, vo)
		}
	}

	return result, nil
}

// fillConfigInfo 填充配置信息（辅助方法）- 扩展版本
func (s *wmsBlindReceivingConfigServiceImpl) fillConfigInfo(ctx context.Context, vo *vo.WmsBlindReceivingConfigVO, repoMgr *repository.RepositoryManager) {
	// 原有目标名称填充逻辑
	s.fillConfigTargetName(ctx, vo, repoMgr)

	// 新增：策略名称填充
	vo.StrategyName = s.getStrategyDisplayName(vo.Strategy)

	// 新增：创建者、更新者名称填充
	s.fillUserNames(ctx, vo, repoMgr)

	// 新增：使用统计填充
	s.fillUsageStats(ctx, vo, repoMgr)

	// 新增：审批角色处理
	s.fillApprovalRoles(ctx, vo, repoMgr)
}

// fillConfigTargetName 填充配置目标名称（辅助方法）
func (s *wmsBlindReceivingConfigServiceImpl) fillConfigTargetName(ctx context.Context, vo *vo.WmsBlindReceivingConfigVO, repoMgr *repository.RepositoryManager) {
	if vo.ConfigTargetID == nil {
		vo.ConfigTargetName = "系统默认"
		return
	}

	switch vo.ConfigLevel {
	case string(entity.ConfigLevelWarehouse):
		// 查询仓库名称
		var warehouseName string
		repoMgr.GetDB().WithContext(ctx).Table("wms_location").
			Where("id = ? AND location_type = 'WAREHOUSE'", *vo.ConfigTargetID).
			Select("location_name").Scan(&warehouseName)
		if warehouseName != "" {
			vo.ConfigTargetName = warehouseName
		} else {
			vo.ConfigTargetName = fmt.Sprintf("仓库ID:%d", *vo.ConfigTargetID)
		}

	case string(entity.ConfigLevelClient):
		// 查询客户名称
		var clientName string
		repoMgr.GetDB().WithContext(ctx).Table("crm_customer").
			Where("id = ?", *vo.ConfigTargetID).
			Select("customer_name").Scan(&clientName)
		if clientName != "" {
			vo.ConfigTargetName = clientName
		} else {
			vo.ConfigTargetName = fmt.Sprintf("客户ID:%d", *vo.ConfigTargetID)
		}

	case string(entity.ConfigLevelUser):
		// 查询用户名称
		var userName string
		repoMgr.GetDB().WithContext(ctx).Table("sys_user").
			Where("id = ?", *vo.ConfigTargetID).
			Select("username").Scan(&userName)
		if userName != "" {
			vo.ConfigTargetName = userName
		} else {
			vo.ConfigTargetName = fmt.Sprintf("用户ID:%d", *vo.ConfigTargetID)
		}

	default:
		vo.ConfigTargetName = fmt.Sprintf("未知类型:%d", *vo.ConfigTargetID)
	}
}

// getStrategyDisplayName 获取策略显示名称
func (s *wmsBlindReceivingConfigServiceImpl) getStrategyDisplayName(strategy string) string {
	strategyNames := map[string]string{
		string(entity.StrategyStrict):     "严格模式",
		string(entity.StrategySupplement): "补录模式",
		string(entity.StrategyFull):       "完全模式",
	}
	if name, exists := strategyNames[strategy]; exists {
		return name
	}
	return strategy
}

// fillUserNames 填充用户名称
func (s *wmsBlindReceivingConfigServiceImpl) fillUserNames(ctx context.Context, vo *vo.WmsBlindReceivingConfigVO, repoMgr *repository.RepositoryManager) {
	// 查询创建者姓名
	var createdByName string
	repoMgr.GetDB().WithContext(ctx).Table("sys_user").
		Where("id = ?", vo.CreatedBy).
		Select("real_name").Scan(&createdByName)
	vo.CreatedByName = createdByName

	// 查询更新者姓名
	var updatedByName string
	repoMgr.GetDB().WithContext(ctx).Table("sys_user").
		Where("id = ?", vo.UpdatedBy).
		Select("real_name").Scan(&updatedByName)
	vo.UpdatedByName = updatedByName
}

// fillUsageStats 填充使用统计信息
func (s *wmsBlindReceivingConfigServiceImpl) fillUsageStats(ctx context.Context, vo *vo.WmsBlindReceivingConfigVO, repoMgr *repository.RepositoryManager) {
	// 查询使用次数统计（这里简化实现，实际应该从验证记录表查询）
	// 由于验证记录表还未实现，暂时设置默认值
	vo.UsageCount = 0
	vo.LastUsedAt = nil
	vo.EffectiveRanking = 1
}

// fillApprovalRoles 填充审批角色信息
func (s *wmsBlindReceivingConfigServiceImpl) fillApprovalRoles(ctx context.Context, vo *vo.WmsBlindReceivingConfigVO, repoMgr *repository.RepositoryManager) {
	// 从实体的*string字段转换为VO的[]string字段
	// 这里需要从数据库实体获取原始的ApprovalUserRoles字段
	configRepo := repoMgr.GetWmsBlindReceivingConfigRepository()
	config, err := configRepo.FindByID(ctx, vo.ID)
	if err == nil && config != nil {
		vo.ApprovalUserRoles = config.GetApprovalUserRolesList()
	} else {
		vo.ApprovalUserRoles = []string{}
	}
}
