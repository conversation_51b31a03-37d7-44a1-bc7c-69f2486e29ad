<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="80%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="isViewMode"
    >
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="任务编号" prop="taskNo">
              <el-input
                v-model="formData.taskNo"
                placeholder="系统自动生成"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库通知单" prop="notificationId">
              <div class="input-with-button">
                <el-input
                  v-model="formData.notificationNo"
                  placeholder="请选择出库通知单"
                  readonly
                />
                <el-button
                  @click="handleSelectNotification"
                  :disabled="isViewMode"
                >
                  选择
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="拣货策略" prop="pickingStrategy">
              <el-select
                v-model="formData.pickingStrategy"
                placeholder="请选择拣货策略"
                style="width: 100%"
              >
                <el-option
                  v-for="option in strategyOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="波次编号" prop="waveNo">
              <el-input
                v-model="formData.waveNo"
                placeholder="可选，留空则不分配波次"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分配人员" prop="assignedUserId">
              <div class="input-with-button">
                <el-input
                  v-model="formData.assignedUserName"
                  placeholder="请选择分配人员"
                  readonly
                />
                <el-button
                  @click="handleSelectUser"
                  :disabled="isViewMode"
                >
                  选择
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级" prop="priority">
              <el-select
                v-model="formData.priority"
                placeholder="请选择优先级"
                style="width: 100%"
              >
                <el-option
                  v-for="option in priorityOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="预计时长" prop="estimatedDuration">
              <el-input-number
                v-model="formData.estimatedDuration"
                :min="1"
                :max="480"
                placeholder="分钟"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                placeholder="请输入备注信息"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 拣货明细 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span>拣货明细</span>
            <el-button
              type="primary"
              size="small"
              @click="handleAddDetail"
              :disabled="isViewMode || !formData.notificationId"
            >
              添加明细
            </el-button>
          </div>
        </template>
        
        <VNTable
          ref="detailTableRef"
          :data="formData.details"
          :columns="detailColumns"
          :loading="false"
          :show-operations="true"
          :operation-width="120"
          operation-fixed="right"
          row-key="lineNo"
          show-index
          max-height="300"
        >
          <template #column-sourceLocationCode="{ row }">
            <div class="input-with-button">
              <el-input
                v-model="row.sourceLocationCode"
                placeholder="请选择库位"
                readonly
              />
              <el-button
                size="small"
                @click="handleSelectLocation(row)"
                :disabled="isViewMode"
              >
                选择
              </el-button>
            </div>
          </template>
          
          <template #column-requiredQty="{ row }">
            <el-input-number
              v-model="row.requiredQty"
              :min="1"
              :precision="0"
              style="width: 100%"
              :disabled="isViewMode"
            />
          </template>
          
          <template #operation="{ row, index }">
            <el-button
              type="danger"
              size="small"
              @click="handleRemoveDetail(index)"
              :disabled="isViewMode"
            >
              删除
            </el-button>
          </template>
        </VNTable>
      </el-card>
      
      <!-- 执行信息 -->
      <el-card class="form-section" shadow="never" v-if="mode !== 'create'">
        <template #header>
          <span>执行信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="实际时长">
              <el-input
                :value="formData.actualDuration ? `${formData.actualDuration}分钟` : '-'"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始时间">
              <el-input
                :value="formData.startTime || '-'"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束时间">
              <el-input
                :value="formData.endTime || '-'"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ isViewMode ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!isViewMode"
          @click="handleSave"
          :loading="submitting"
        >
          保存
        </el-button>
        <el-button
          v-if="!isViewMode"
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          提交
        </el-button>
      </div>
    </template>
    
    <!-- 出库通知单选择器 -->
    <OutboundNotificationSelector
      ref="notificationSelectorRef"
      @confirm="handleNotificationConfirm"
      @cancel="handleNotificationCancel"
    />
    
    <!-- 用户选择器 -->
    <UserSelector
      ref="userSelectorRef"
      @confirm="handleUserConfirm"
      @cancel="handleUserCancel"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, onMounted } from 'vue'
import { ElDialog, ElButton, ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElInputNumber, ElCard, ElRow, ElCol } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import OutboundNotificationSelector from './OutboundNotificationSelector.vue'
import UserSelector from './UserSelector.vue'
import type { TableColumn } from '@/components/VNTable/types'
import type { PickingTaskFormData, PickingTaskDetailRowData } from '@/types/wms/pickingTask'
import type { WmsUserResp } from '@/api/wms/pickingTask'
import { usePickingTaskStore } from '@/store/wms/pickingTask'

// 组件接口定义
interface PickingTaskFormEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<PickingTaskFormEmits>()

// Store
const pickingTaskStore = usePickingTaskStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const mode = ref<'create' | 'edit' | 'view'>('create')
const recordId = ref<number | null>(null)

// 表单数据
const formData = reactive<PickingTaskFormData>({
  notificationId: null,
  pickingStrategy: '',
  priority: 3,
  details: [],
  _mode: 'create'
})

// 表单验证规则
const formRules: FormRules = {
  notificationId: [
    { required: true, message: '请选择出库通知单', trigger: 'change' }
  ],
  pickingStrategy: [
    { required: true, message: '请选择拣货策略', trigger: 'change' }
  ]
}

// 选项数据
const strategyOptions = [
  { label: '按订单拣货', value: 'BY_ORDER' },
  { label: '按物料拣货', value: 'BY_ITEM' },
  { label: '按库位拣货', value: 'BY_LOCATION' },
  { label: '批量拣货', value: 'BATCH_PICKING' }
]

const priorityOptions = [
  { label: '最高', value: 1 },
  { label: '高', value: 2 },
  { label: '中', value: 3 },
  { label: '低', value: 4 },
  { label: '最低', value: 5 }
]

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    create: '新建拣货任务',
    edit: '编辑拣货任务',
    view: '查看拣货任务'
  }
  return titles[mode.value]
})

const isViewMode = computed(() => mode.value === 'view')

// 明细表格列配置
const detailColumns = computed<TableColumn[]>(() => [
  { prop: 'itemCode', label: '物料编码', minWidth: 120 },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'requiredQty', label: '需求数量', width: 120, slot: true },
  { prop: 'sourceLocationCode', label: '源库位', width: 150, slot: true },
  { prop: 'targetLocationCode', label: '目标库位', width: 120 },
  { prop: 'batchNo', label: '批次号', width: 120 },
  { prop: 'remark', label: '备注', width: 120 }
])

// 表单引用
const formRef = ref<FormInstance>()
const detailTableRef = ref<InstanceType<typeof VNTable>>()
const notificationSelectorRef = ref<InstanceType<typeof OutboundNotificationSelector>>()
const userSelectorRef = ref<InstanceType<typeof UserSelector>>()

// 方法
const loadFormData = async (id: number) => {
  try {
    const record = await pickingTaskStore.fetchDetail(id)
    
    // 填充表单数据
    Object.assign(formData, {
      id: record.id,
      taskNo: record.taskNo,
      notificationId: record.notificationId,
      notificationNo: record.notificationNo,
      pickingStrategy: record.pickingStrategy,
      waveNo: record.waveNo,
      assignedUserId: record.assignedUserId,
      assignedUserName: record.assignedUserName,
      priority: record.priority,
      estimatedDuration: record.estimatedDuration,
      actualDuration: record.actualDuration,
      startTime: record.startTime,
      endTime: record.endTime,
      remark: record.remark,
      details: record.details?.map((detail, index) => ({
        ...detail,
        lineNo: index + 1,
        _isNew: false
      })) || []
    })
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const handleOpen = () => {
  if (mode.value !== 'create' && recordId.value) {
    loadFormData(recordId.value)
  }
}

const handleClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    notificationId: null,
    notificationNo: '',
    pickingStrategy: '',
    waveNo: '',
    assignedUserId: null,
    assignedUserName: '',
    priority: 3,
    estimatedDuration: null,
    remark: '',
    details: []
  })
  formRef.value?.clearValidate()
}

const handleSelectNotification = () => {
  notificationSelectorRef.value?.open()
}

const handleNotificationConfirm = (notification: any) => {
  formData.notificationId = notification.id
  formData.notificationNo = notification.notificationNo
  // 自动加载明细
  loadNotificationDetails(notification.id)
}

const handleNotificationCancel = () => {
  // 通知单选择取消处理
}

const loadNotificationDetails = async (notificationId: number) => {
  try {
    // 这里应该调用API获取出库通知单的明细
    // 然后转换为拣货任务明细格式
    formData.details = []
  } catch (error) {
    ElMessage.error('加载明细失败')
    console.error(error)
  }
}

const handleSelectUser = () => {
  userSelectorRef.value?.open()
}

const handleUserConfirm = (user: WmsUserResp) => {
  formData.assignedUserId = user.id
  formData.assignedUserName = user.nickname
}

const handleUserCancel = () => {
  // 用户选择取消处理
}

const handleAddDetail = () => {
  const newDetail: PickingTaskDetailRowData = {
    lineNo: formData.details.length + 1,
    outboundDetailId: 0,
    itemId: 0,
    itemCode: '',
    itemName: '',
    requiredQty: 1,
    sourceLocationCode: '',
    targetLocationCode: '',
    batchNo: '',
    remark: '',
    _isNew: true
  }
  formData.details.push(newDetail)
}

const handleRemoveDetail = (index: number) => {
  formData.details.splice(index, 1)
  // 重新编号
  formData.details.forEach((detail, idx) => {
    detail.lineNo = idx + 1
  })
}

const handleSelectLocation = (row: PickingTaskDetailRowData) => {
  // 打开库位选择器
  ElMessage.info('库位选择功能开发中...')
}

const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    
    // 验证明细
    if (formData.details.length === 0) {
      ElMessage.error('请添加至少一条明细')
      return false
    }
    
    return true
  } catch (error) {
    return false
  }
}

const handleSave = async () => {
  if (!(await validateForm())) return
  
  submitting.value = true
  try {
    const submitData = {
      ...formData,
      details: formData.details.map(detail => ({
        lineNo: detail.lineNo,
        outboundDetailId: detail.outboundDetailId,
        itemId: detail.itemId,
        requiredQty: detail.requiredQty,
        sourceLocationId: detail.sourceLocationId,
        targetLocationId: detail.targetLocationId,
        batchNo: detail.batchNo,
        remark: detail.remark
      }))
    }
    
    if (mode.value === 'create') {
      await pickingTaskStore.create(submitData)
      ElMessage.success('保存成功')
    } else {
      await pickingTaskStore.update(recordId.value!, submitData)
      ElMessage.success('更新成功')
    }
    
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleSubmit = async () => {
  await handleSave()
  // 提交后可以自动分配或其他操作
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (openMode: 'create' | 'edit' | 'view', id?: number) => {
  mode.value = openMode
  recordId.value = id || null
  formData._mode = openMode
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.form-section {
  margin-bottom: 16px;
}

.form-section :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-with-button {
  display: flex;
  gap: 8px;
}

.input-with-button .el-input {
  flex: 1;
}

.dialog-footer {
  text-align: right;
}
</style>
