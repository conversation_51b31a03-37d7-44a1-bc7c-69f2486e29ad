package entity

import (
	"time"
)

// User 用户实体
// 该实体用于存储系统中的用户信息，包括基本信息、认证信息、状态信息等
// 继承自TenantEntity，支持多租户功能
type User struct {
	TenantEntity                    // 继承租户实体基类，包含ID、创建时间、更新时间等基础字段
	Username             string     `gorm:"column:username;size:50;not null" json:"username" sortable:"true"`     // 用户名：用户登录系统的唯一标识，不可重复 (允许排序)
	Password             string     `gorm:"column:password;size:100;not null" json:"-"`                           // 密码：存储加密后的密码，json序列化时忽略此字段
	Nickname             string     `gorm:"column:nickname;size:50" json:"nickname" sortable:"true"`              // 昵称：用户在系统中显示的名称，可自定义 (允许排序)
	RealName             string     `gorm:"column:real_name;size:50" json:"realName" sortable:"true"`             // 真实姓名 (允许排序)
	DefaultAccountBookID *uint      `gorm:"column:default_account_book_id" json:"defaultAccountBookId,omitempty"` // 新增：默认账套ID
	IsAdmin              bool       `gorm:"column:is_admin;default:false" json:"isAdmin"`                         // 是否是管理员：标识用户是否具有管理员权限，默认为false
	Avatar               string     `gorm:"column:avatar;size:255" json:"avatar"`                                 // 头像URL：用户头像的访问地址，最大长度255字符
	Gender               int        `gorm:"column:gender;default:0" json:"gender" sortable:"true"`                // 性别：用户性别（0-未知，1-男，2-女）(允许排序)
	Email                string     `gorm:"column:email;size:100" json:"email"`                                   // 电子邮箱：用户的邮箱地址，可用于登录和通知
	Mobile               string     `gorm:"column:mobile;size:20" json:"mobile"`                                  // 手机号码：用户的手机号，可用于登录和通知
	EmployeeID           *uint      `gorm:"uniqueIndex;comment:关联员工ID" json:"employeeId,omitempty"`               // 关联员工ID (外键, 指向 employees 表, unique确保一对一)
	ExpireTime           *time.Time `gorm:"column:expire_time" json:"expireTime"`                                 // 账号过期时间：用户账号的有效期截止时间，超过此时间账号将无法使用
	Status               int        `gorm:"column:status;default:1;index" json:"status" sortable:"true"`          // 状态：用户状态（0-禁用，1-正常），默认为正常，建立索引提高查询效率 (允许排序)
	Remark               string     `gorm:"column:remark;size:500" json:"remark"`                                 // 备注：关于用户的附加说明信息，最大500字符
	Salt                 string     `gorm:"column:salt;size:64" json:"-"`                                         // 密码盐值：用于密码加密的随机字符串，提高密码安全性，json序列化时忽略
	LoginIP              string     `gorm:"column:login_ip;size:50" json:"loginIp"`                               // 最后登录IP：记录用户最近一次登录的IP地址
	LoginTime            time.Time  `gorm:"column:login_time" json:"loginTime"`                                   // 最后登录时间：记录用户最近一次登录的时间戳
	LoginCount           int        `gorm:"column:login_count;default:0" json:"loginCount"`                       // 登录次数：记录用户累计登录系统的次数
	LockedTime           time.Time  `gorm:"column:locked_time" json:"lockedTime"`                                 // 账号锁定时间：用户账号被锁定的时间，用于实现账号锁定功能
	LockedCount          int        `gorm:"column:locked_count;default:0" json:"lockedCount"`                     // 锁定次数：记录用户账号被锁定的累计次数，用于安全策略控制
	LastPwdTime          *time.Time `gorm:"column:last_pwd_time" json:"lastPwdTime,omitempty"`                    // 最后密码修改时间

	// 关联字段
	Roles        []Role        `gorm:"many2many:sys_user_role;foreignKey:ID;joinForeignKey:UserID;References:ID;joinReferences:RoleID" json:"roles,omitempty"`                       // 用户角色列表
	AccountBooks []AccountBook `gorm:"many2many:sys_user_account_book;foreignKey:ID;joinForeignKey:UserID;References:ID;joinReferences:AccountBookID" json:"accountBooks,omitempty"` // 用户可访问账套列表
}

// TableName 指定用户表名
// 当前被注释掉，使用GORM默认的表名规则
// 取消注释后可自定义表名为"sys_user"
func (User) TableName() string {
	return "sys_user"
}
