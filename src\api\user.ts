import request from '@/utils/request';
import type { BackendResponse } from '@/utils/request'; // 使用 request 工具中定义的响应类型

// --- 类型定义 ---

// 菜单项 (对应后端 vo.MenuTreeVO)
export interface MenuTreeItem {
  id: number;
  parentId: number;
  title: string;
  name: string; // 路由 name
  path: string; // 路由 path
  component?: string; // 组件路径，目录可能为空
  icon?: string;
  type: 1 | 2; // 1:目录 2:菜单 (导航菜单通常只包含这两种)
  sort: number;
  hidden: boolean;
  children?: MenuTreeItem[]; // 子菜单
  permission?: string | null; // 新增：权限标识
  // 注意：根据实际情况，可能还需要其他后端返回的字段
  // 例如：keepAlive, alwaysShow 等路由元信息 (meta) 字段
  meta?: Record<string, any>; // 可以将 title, icon, hidden 等放入 meta
}

// 账套项 (对应后端 vo.AccountBookVO)
export interface AccountBookItem {
  id: number | string;
  name: string;
  // Add other relevant properties if needed
}

// Define the actual unwrapped response type for getUserAccountBooks
export interface UserAccountBooksResponse {
  list: AccountBookItem[];
}

// Backend RoleVO (simplified)
interface RoleVO {
  id: number;
  name: string;
  code: string;
}

// Backend AccountBookSimpleVO (simplified)
export interface AccountBookSimpleVO {
  id: number;
  name: string;
  code: string;
}

// Matches the Go UserProfileVO struct
export interface UserProfileVO {
  id: number;
  username: string;
  nickname: string;
  realName: string;
  empCode: string;
  empName: string;
  avatar: string;
  gender: number;
  email: string;
  mobile: string;
  loginIp: string;
  loginTime: string; // Keep as string for simplicity, conversion handled in component
  employeeId?: number;
  roles?: RoleVO[];
  accountBooks?: AccountBookSimpleVO[];
  defaultAccountBookId?: number | string | null; // 新增：匹配后端返回
}

// DTO for updating user profile (only editable fields)
export interface UserProfileUpdateDTO {
  nickname?: string;
  realName?: string; // Include if editable
  avatar?: string;
  gender?: number;
  email?: string;
  mobile?: string;
  defaultAccountBookId?: number | string | null; // 新增：匹配后端 (使用 null 表示清空)
}

// DTO for changing password
export interface ChangePasswordDTO {
  oldPassword: string;
  newPassword: string;
}

// --- API 函数 ---

/**
 * 获取当前用户的导航菜单树
 * @returns Promise<MenuTreeItem[]>
 */
export function getUserMenuTree() {
  // 假设 request 拦截器处理了认证 Token
  // 且响应拦截器返回 res.data
  return request.get<MenuTreeItem[]>('/user/menus/tree');
}

/**
 * 获取当前用户的操作权限标识列表
 * @returns Promise<string[]>
 */
export function getUserPermissions() {
  return request.get<string[]>('/user/permissions');
}


/**
 * 获取当前登录用户的个人信息
 */
export function getUserProfile(): Promise<UserProfileVO> {
  return request({
    url: '/user/profile', // Matches GET /api/v1/user/profile
    method: 'get'
  });
}

/**
 * 更新当前登录用户的个人信息
 * @param data 用户更新数据
 */
export function updateUserProfile(data: UserProfileUpdateDTO): Promise<BackendResponse<null>> {
  return request({
    url: '/user/profile', // Assuming PUT /api/v1/user/profile
    method: 'put',
    data
  });
}

/**
 * 修改当前登录用户的密码
 * @param userId 当前用户的 ID
 * @param data 包含旧密码和新密码
 */
export function changePassword(userId: number | string, data: ChangePasswordDTO): Promise<BackendResponse<null>> {
  return request({
    url: `/sys/user/${userId}/password`, // 修正：包含完整的 /sys/users/ 路径
    method: 'put',
    data
  });
}

/**
 * 获取当前用户的可用帐套列表
 */
export function getUserAccountBooks(): Promise<UserAccountBooksResponse> {
  // Assumes the response structure is { code: 0, data: { list: AccountBookItem[] }, ... }
  // The request utility will unwrap the outer data, returning { list: AccountBookItem[] }.
  return request.get<UserAccountBooksResponse>('/user/account-books') as unknown as Promise<UserAccountBooksResponse>;
}



// 如果有其他用户相关的API，例如获取用户详情，也可以加在这里
// export function getUserProfile() {
//   return request.get<UserProfileData>('/api/v1/user/profile');
// } 