<template>
  <div>
    <el-card class="license-management-card">
      <template #header>
        <div class="card-header">
          <span>授权管理</span>
        </div>
      </template>

      <!-- Action Feedback Alert -->
      <el-alert
        v-if="actionMessage"
        :title="actionMessage"
        :type="actionMessageType"
        show-icon
        :closable="true" 
        @close="actionMessage = null"
        style="margin-bottom: 20px;"
      />

      <el-upload
        ref="uploadRef"
        drag
        :action="uploadUrl" 
        :headers="uploadHeaders"
        :name="'licenseFile'"
        :limit="1"
        accept=".lic"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :before-upload="beforeUpload"
        v-loading="isUploading"
        element-loading-text="上传中..."
        style="margin-bottom: 20px;"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将 .lic 文件拖到此处，或 <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 .lic 格式的授权文件
          </div>
        </template>
      </el-upload>

      <el-button 
        type="primary" 
        @click="submitUpload"
        :disabled="!selectedFile || isUploading || isLoadingInfo"
      >
        上传授权文件
      </el-button>

    </el-card>

    <el-card class="license-info-card" v-loading="isLoadingInfo" element-loading-text="正在加载授权信息...">
       <template #header>
        <div class="card-header">
          <span>当前授权信息</span>
           <el-button type="primary" link @click="fetchCurrentLicenseInfo" :disabled="isLoadingInfo">
            <el-icon><RefreshRight /></el-icon>刷新
          </el-button>
        </div>
      </template>
      
      <div v-if="licenseDisplayInfo.status === 'active'">
        <el-descriptions :column="1" border>
          <el-descriptions-item label-align="right" align="left" label="状态">
            <el-tag :type="licenseDisplayInfo.statusTag">{{ licenseDisplayInfo.statusText }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" label="消息">{{ licenseDisplayInfo.apiMessage }}</el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" label="授权给">{{ licenseDisplayInfo.licensedTo || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" label="授权类型">{{ licenseDisplayInfo.licenseTypeText }}</el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" label="颁发日期">{{ formatDate(licenseDisplayInfo.issueDate) }}</el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" label="有效期至">{{ formatDate(licenseDisplayInfo.expiresAt) }}</el-descriptions-item>
          <el-descriptions-item label-align="right" align="left" label="授权功能">
            <span v-if="licenseDisplayInfo.features && licenseDisplayInfo.features.length > 0">
              <el-tag v-for="feature in licenseDisplayInfo.features" :key="feature" type="info" style="margin-right: 5px; margin-bottom: 5px;">{{ feature }}</el-tag>
            </span>
            <span v-else>N/A</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
       <div v-else-if="!isLoadingInfo"> 
        <el-empty description="暂无有效的授权信息，或无法连接到服务器" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { ElMessage, type UploadInstance, type UploadProps, type UploadRawFile, ElEmpty, ElDescriptions, ElDescriptionsItem, ElTag, ElAlert } from 'element-plus';
import { UploadFilled, RefreshRight } from '@element-plus/icons-vue';
import { useUserStoreHook } from '@/store/modules/user';
import { getLicenseInfo, type LicenseDetails } from '@/api/license'; // Corrected path

const userStore = useUserStoreHook();
const uploadRef = ref<UploadInstance>();

const isUploading = ref(false); // For upload operation
const isLoadingInfo = ref(false); // For fetching license info

const actionMessage = ref<string | null>(null); // For upload/action specific feedback
const actionMessageType = ref<'success' | 'error' | 'warning' | 'info'>('info');

const selectedFile = ref<UploadRawFile | null>(null);

// Reactive state for storing detailed license information from the API
const currentLicense = reactive<Partial<LicenseDetails>>({
  status: undefined,
  message: undefined,
  licensedTo: undefined,
  expiresAt: null,
  licenseType: undefined,
  features: [],
  issueDate: undefined,
});

// Computed property to derive display-friendly values and tags
const licenseDisplayInfo = computed(() => {
  let statusText = '未知';
  let statusTag: 'success' | 'danger' | 'warning' | 'info' = 'info';
  let licenseTypeText = currentLicense.licenseType || 'N/A'; // Default to original or N/A

  switch (currentLicense.status) {
    case 'active':
      statusText = '有效';
      statusTag = 'success';
      break;
    case 'expired':
      statusText = '已过期';
      statusTag = 'danger';
      break;
    case 'not_found':
      statusText = '未找到/未配置';
      statusTag = 'warning';
      break;
    default:
      if(currentLicense.message && !currentLicense.status) statusText = "状态异常"; 
      break;
  }

  // Convert licenseType to Chinese
  switch (currentLicense.licenseType) {
    case 'Trial':
      licenseTypeText = '试用版';
      break;
    case 'Subscription':
      licenseTypeText = '订阅版';
      break;
    case 'Perpetual':
      licenseTypeText = '永久版';
      break;
    case 'Standard':
      licenseTypeText = '标准版';
      break;
    case 'Premium':
      licenseTypeText = '高级版';
      break;
    case 'Enterprise':
      licenseTypeText = '企业版';
      break;
    // 可以根据后端 pkg/license/model.go 中 LicenseInfo 的 LicenseType 可能的值添加更多 case
    default:
      // 如果 currentLicense.licenseType 有值但未匹配到，则 licenseTypeText 保持原样或设为特定中文
      if (currentLicense.licenseType) {
        licenseTypeText = currentLicense.licenseType; // 或者 licenseTypeText = `未知类型 (${currentLicense.licenseType})`;
      }
      break;
  }

  return {
    ...currentLicense,
    statusText,
    statusTag,
    licenseTypeText, // Use the new Chinese version
    apiMessage: currentLicense.message || '-',
  };
});

// --- 动态构建上传 URL ---
const uploadUrl = computed(() => {
  const baseApi = import.meta.env['VITE_APP_BASE_API'] || '/api/v1'; // Add fallback and fix linter error
  return `${baseApi}/admin/license/upload`;
});

const uploadHeaders = computed(() => {
  const token = userStore.token;
  return token ? { Authorization: `Bearer ${token}` } : {};
});

const fetchCurrentLicenseInfo = async () => {
  isLoadingInfo.value = true;
  actionMessage.value = null; // Clear previous action messages
  try {
    const data = await getLicenseInfo();
    Object.assign(currentLicense, data); // Update all fields
    // Set general alert based on fetched status if needed, or rely on detailed display
    // For example, if you want a prominent message at the top:
    // actionMessage.value = data.message;
    // actionMessageType.value = data.status === 'active' ? 'success' : (data.status === 'expired' ? 'warning' : 'info');
  } catch (error: any) {
    console.error("Failed to fetch license info:", error);
    Object.keys(currentLicense).forEach(key => (currentLicense as any)[key] = undefined); // Clear currentLicense
    currentLicense.expiresAt = null;
    currentLicense.features = [];
    actionMessage.value = error.message || '无法加载授权信息，请检查网络或联系管理员。';
    actionMessageType.value = 'error';
  } finally {
    isLoadingInfo.value = false;
  }
};

onMounted(() => {
  fetchCurrentLicenseInfo();
});

const handleFileChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
  actionMessage.value = null;
  if (uploadFile.raw && uploadFile.status === 'ready') {
    selectedFile.value = uploadFile.raw;
  } else if (uploadFiles.length === 0) {
    selectedFile.value = null;
  }
};

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (!rawFile.name.endsWith('.lic')) {
    ElMessage.error('只能上传 .lic 格式的文件');
    return false;
  }
  isUploading.value = true;
  actionMessage.value = null;
  return true;
};

const submitUpload = () => {
  actionMessage.value = null; 
  uploadRef.value?.submit();
};

// Renamed to handleUploadSuccess
const handleUploadSuccess: UploadProps['onSuccess'] = async (response: any, uploadFile) => {
  isUploading.value = false;
  console.log("Upload Success Raw Response:", response);
  console.log("uploadFile:",uploadFile);
  // Assuming `response` is already the `data` part (LicenseDetails compatible) due to api/license.ts modifications
  // And backend controller for upload returns a similar structure to GetLicenseInfo
  
  // Backend controller for upload has a specific success message, use it.
  // And it contains fields: customer, expiry, features, licenseType, issueDate, message
  actionMessage.value = response.message || '授权文件上传成功！正在刷新授权信息...';
  actionMessageType.value = 'success';
  
  selectedFile.value = null; 
  uploadRef.value?.clearFiles();

  // Crucially, fetch the canonical license info after upload
  await fetchCurrentLicenseInfo(); 
};

// Renamed to handleUploadError
const handleUploadError: UploadProps['onError'] = (error: any, uploadFile) => {
  isUploading.value = false;
  console.error("Upload Error Raw:", error);
  console.error("Upload File:", uploadFile);

  let parsedMessage = '上传失败，请稍后重试';
  // Error already contains message from interceptor in api/license.ts, or it's an Axios error object
  if (error && error.message) {
      parsedMessage = error.message;
  }
  // Further specific parsing like in original might be needed if interceptor doesn't fully normalize
  // For now, rely on the message from the error object passed from api.ts catch block

  actionMessage.value = parsedMessage;
  actionMessageType.value = 'error';
};

const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value?.clearFiles();
  const file = files[0] as UploadRawFile;
  uploadRef.value?.handleStart(file);
  selectedFile.value = file;
  ElMessage.warning('只能上传一个文件，已替换为新选择的文件');
};

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
        throw new Error('Invalid date string for formatting');
    }
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return dateString; // Return original if formatting fails
  }
};

</script>

<style scoped>
.license-management-card,
.license-info-card {
  max-width: 700px; /* Increased width slightly */
  margin: 20px auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-descriptions {
  margin-top: 10px;
}

.el-tag {
  margin-right: 5px;
}

/* Removed p and strong styles as el-descriptions is used */
</style> 