// ==================== 导入类型 ====================
import type { 
  WmsShipmentStatus,
  WmsShippingMethod,
  WmsShipmentResp,
  WmsCarrierResp,
  WmsShipmentCostResp,
  WmsShipmentLabelResp,
  WmsShipmentTrackingResp,
  WmsShipmentTrackingEventResp
} from '@/api/wms/shipment'

// ==================== 基础类型 ====================

// 选择器选项类型
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  color?: string
}

// ==================== 表单数据类型 ====================

// 发运单表单数据
export interface ShipmentFormData {
  // 主表数据
  id?: number
  shipmentNo?: string
  outboundNotificationId?: number | null
  outboundNotificationNo?: string
  carrierId?: number | null
  carrierName?: string
  trackingNo?: string
  shippingMethod?: WmsShippingMethod | ''
  status?: WmsShipmentStatus

  // 时间字段
  estimatedShipDate?: string
  estimatedDeliveryDate?: string
  actualShipDate?: string
  actualDeliveryDate?: string

  // 收货信息
  consigneeName?: string
  consigneePhone?: string
  consigneeEmail?: string
  consigneeAddress?: string
  postalCode?: string

  // 运费信息
  totalWeight?: number
  totalVolume?: number
  shippingCost?: number
  insuranceFee?: number
  packingCost?: number
  currency?: string

  // 司机信息
  driverName?: string
  driverPhone?: string
  vehicleNo?: string

  // 包装信息
  totalPackages?: number
  packageType?: string

  // 备注
  remark?: string
  shippingRemark?: string
  packingRemark?: string
  deliveryRemark?: string
  specialInstructions?: string
  remark?: string
  
  // 表单状态
  _mode?: 'create' | 'edit' | 'view'
  _loading?: boolean
  _submitting?: boolean
}

// 运费计算表单数据
export interface ShippingCostFormData {
  carrierId: number | null
  carrierName?: string
  shippingMethod: WmsShippingMethod | ''
  originAddress?: string
  destinationAddress: string
  packageCount: number
  totalWeight: number
  totalVolume: number
  declaredValue?: number
  requiresInsurance?: boolean
  urgentDelivery?: boolean
  specialServices?: string[]
}

// 标签打印表单数据
export interface LabelPrintFormData {
  shipmentId: number
  labelType: 'SHIPPING' | 'RETURN' | 'FRAGILE' | 'URGENT'
  copies: number
  format: 'PDF' | 'PNG' | 'ZPL'
  includeBarcode?: boolean
  includeQRCode?: boolean
  customText?: string
}

// 签收确认表单数据
export interface DeliveryConfirmFormData {
  shipmentId: number
  receiverName: string
  receiverPhone?: string
  deliveryDate: string
  deliveryTime?: string
  deliveryProof?: string
  signatureImage?: string
  photoEvidence?: string[]
  remark?: string
  satisfactionRating?: number
}

// ==================== 组件Props类型 ====================

// 承运商选择器Props
export interface CarrierSelectorProps {
  modelValue?: number | null
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  filterable?: boolean
  size?: 'large' | 'default' | 'small'
  shippingMethodFilter?: WmsShippingMethod[]
}

// 运费计算器Props
export interface CostCalculatorProps {
  visible?: boolean
  shipmentData?: Partial<ShipmentFormData>
}

// 标签打印器Props
export interface LabelPrinterProps {
  shipmentId: number
  visible?: boolean
}

// 跟踪管理器Props
export interface TrackingManagerProps {
  shipmentId: number
  readonly?: boolean
}

// ==================== 常量定义 ====================

// 发运单状态选项
export const SHIPMENT_STATUS_OPTIONS: SelectOption[] = [
  { value: 'PREPARING', label: '准备中', color: '#909399' },
  { value: 'PACKED', label: '已打包', color: '#409EFF' },
  { value: 'READY_TO_SHIP', label: '待发运', color: '#E6A23C' },
  { value: 'SHIPPED', label: '已发运', color: '#67C23A' },
  { value: 'IN_TRANSIT', label: '运输中', color: '#409EFF' },
  { value: 'DELIVERED', label: '已送达', color: '#67C23A' },
  { value: 'CANCELLED', label: '已取消', color: '#F56C6C' }
]

// 运输方式选项
export const SHIPPING_METHOD_OPTIONS: SelectOption[] = [
  { value: 'EXPRESS', label: '快递' },
  { value: 'LOGISTICS', label: '物流' },
  { value: 'SELF_PICKUP', label: '自提' },
  { value: 'DIRECT_DELIVERY', label: '直送' }
]

// 标签类型选项
export const LABEL_TYPE_OPTIONS: SelectOption[] = [
  { value: 'SHIPPING', label: '发运标签' },
  { value: 'RETURN', label: '退货标签' },
  { value: 'FRAGILE', label: '易碎标签' },
  { value: 'URGENT', label: '紧急标签' }
]

// 标签格式选项
export const LABEL_FORMAT_OPTIONS: SelectOption[] = [
  { value: 'PDF', label: 'PDF格式' },
  { value: 'PNG', label: 'PNG图片' },
  { value: 'ZPL', label: 'ZPL打印码' }
]

// 货币选项
export const CURRENCY_OPTIONS: SelectOption[] = [
  { value: 'CNY', label: '人民币(¥)' },
  { value: 'USD', label: '美元($)' },
  { value: 'EUR', label: '欧元(€)' },
  { value: 'JPY', label: '日元(¥)' }
]

// 包装类型选项
export const PACKAGE_TYPE_OPTIONS: SelectOption[] = [
  { value: 'BOX', label: '纸箱' },
  { value: 'ENVELOPE', label: '信封' },
  { value: 'TUBE', label: '管状包装' },
  { value: 'PALLET', label: '托盘' },
  { value: 'CUSTOM', label: '自定义' }
]

// ==================== 工具函数类型 ====================

// 状态格式化函数类型
export type StatusFormatter = (status: WmsShipmentStatus) => string

// 状态颜色配置类型
export interface StatusColorConfig {
  color: string
  bgColor: string
  borderColor: string
}

// 状态颜色映射类型
export type StatusColorMap = Record<WmsShipmentStatus, StatusColorConfig>

// 表单验证规则类型
export interface FormValidationRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  validator?: (rule: any, value: any, callback: any) => void
}

// 表单验证规则映射类型
export type FormValidationRules = Record<string, FormValidationRule[]>

// 批量操作类型
export interface BatchOperation {
  type: 'delete' | 'ship' | 'cancel' | 'print' | 'export'
  label: string
  icon?: string
  permission?: string
  disabled?: boolean
  handler: (selectedRows: any[]) => void | Promise<void>
}

// ==================== 业务逻辑类型 ====================

// 运费计算配置
export interface ShippingCostConfig {
  baseCostRules: CostRule[]
  weightCostRules: CostRule[]
  volumeCostRules: CostRule[]
  distanceCostRules: CostRule[]
  serviceCostRules: CostRule[]
  discountRules: DiscountRule[]
  surchargeRules: SurchargeRule[]
}

// 费用规则
export interface CostRule {
  id: string
  name: string
  condition: string
  formula: string
  minValue?: number
  maxValue?: number
  enabled: boolean
}

// 折扣规则
export interface DiscountRule {
  id: string
  name: string
  type: 'PERCENTAGE' | 'FIXED_AMOUNT'
  value: number
  condition: string
  enabled: boolean
}

// 附加费规则
export interface SurchargeRule {
  id: string
  name: string
  type: 'PERCENTAGE' | 'FIXED_AMOUNT'
  value: number
  condition: string
  enabled: boolean
}

// 承运商服务配置
export interface CarrierServiceConfig {
  carrierId: number
  supportedMethods: WmsShippingMethod[]
  serviceAreas: string[]
  timeWindows: TimeWindow[]
  specialServices: SpecialService[]
  restrictions: ServiceRestriction[]
}

// 时间窗口
export interface TimeWindow {
  method: WmsShippingMethod
  area: string
  pickupStart: string
  pickupEnd: string
  deliveryStart: string
  deliveryEnd: string
  workdays: number[]
}

// 特殊服务
export interface SpecialService {
  code: string
  name: string
  description: string
  cost: number
  available: boolean
}

// 服务限制
export interface ServiceRestriction {
  type: 'WEIGHT' | 'VOLUME' | 'VALUE' | 'ITEM_TYPE'
  minValue?: number
  maxValue?: number
  excludedItems?: string[]
  message: string
}

// ==================== 跟踪相关类型 ====================

// 跟踪配置
export interface TrackingConfig {
  enableAutoSync: boolean
  syncInterval: number
  webhookUrl?: string
  notificationSettings: TrackingNotificationSettings
}

// 跟踪通知设置
export interface TrackingNotificationSettings {
  enableNotifications: boolean
  notifyOnShipped: boolean
  notifyOnInTransit: boolean
  notifyOnDelivered: boolean
  notifyOnException: boolean
  recipients: string[]
  channels: ('EMAIL' | 'SMS' | 'WEBHOOK')[]
}

// 跟踪异常
export interface TrackingException {
  type: 'DELAY' | 'DAMAGE' | 'LOST' | 'REFUSED' | 'ADDRESS_ERROR' | 'OTHER'
  description: string
  location?: string
  timestamp: string
  resolved: boolean
  resolution?: string
}

// 配送路线
export interface DeliveryRoute {
  routeId: string
  carrierId: number
  vehicle: string
  driver: string
  stops: DeliveryStop[]
  estimatedDuration: number
  actualDuration?: number
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
}

// 配送站点
export interface DeliveryStop {
  stopId: string
  shipmentId: number
  address: string
  estimatedArrival: string
  actualArrival?: string
  estimatedDeparture: string
  actualDeparture?: string
  status: 'PENDING' | 'ARRIVED' | 'DELIVERED' | 'FAILED'
  notes?: string
}

// ==================== 事件类型 ====================

// 表单事件
export interface FormEvents {
  submit: (data: ShipmentFormData) => void
  cancel: () => void
  reset: () => void
  validate: () => Promise<boolean>
}

// 发运事件
export interface ShipmentEvents {
  ship: (shipmentId: number, data: any) => void
  cancel: (shipmentId: number, reason?: string) => void
  track: (shipmentId: number) => void
  confirm: (shipmentId: number, data: any) => void
}

// 标签打印事件
export interface LabelPrintEvents {
  generate: (data: LabelPrintFormData) => void
  print: (labelData: any) => void
  download: (labelData: any) => void
}

// ==================== 统计分析类型 ====================

// 发运统计
export interface ShipmentStatistics {
  totalShipments: number
  totalCost: number
  averageCost: number
  onTimeDeliveryRate: number
  averageDeliveryTime: number
  statusDistribution: Record<WmsShipmentStatus, number>
  methodDistribution: Record<WmsShippingMethod, number>
  carrierPerformance: CarrierPerformance[]
  monthlyTrends: MonthlyTrend[]
}

// 承运商绩效
export interface CarrierPerformance {
  carrierId: number
  carrierName: string
  shipmentCount: number
  totalCost: number
  averageCost: number
  onTimeRate: number
  damageRate: number
  customerSatisfaction: number
  rating: number
}

// 月度趋势
export interface MonthlyTrend {
  month: string
  shipmentCount: number
  totalCost: number
  onTimeRate: number
  averageDeliveryTime: number
}

// ==================== 响应类型扩展 ====================

// 扩展的发运单响应类型
export interface ExtendedShipmentResp extends WmsShipmentResp {
  // 计算字段
  deliveryProgress?: number
  isDelayed?: boolean
  estimatedDelay?: number
  
  // 状态标识
  canEdit?: boolean
  canDelete?: boolean
  canShip?: boolean
  canCancel?: boolean
  canTrack?: boolean
  canPrintLabel?: boolean
  
  // 关联数据
  pickingTask?: any
  carrier?: WmsCarrierResp
  tracking?: WmsShipmentTrackingResp
  costBreakdown?: WmsShipmentCostResp
}

// 扩展的承运商响应类型
export interface ExtendedCarrierResp extends WmsCarrierResp {
  // 统计数据
  totalShipments?: number
  averageCost?: number
  onTimeRate?: number
  customerRating?: number
  
  // 服务能力
  serviceConfig?: CarrierServiceConfig
  currentCapacity?: number
  maxCapacity?: number
  
  // 状态标识
  isPreferred?: boolean
  hasContract?: boolean
}

// ==================== 导出类型 ====================
export type {
  WmsShipmentStatus,
  WmsShippingMethod,
  WmsShipmentResp,
  WmsCarrierResp,
  WmsShipmentCostResp,
  WmsShipmentLabelResp,
  WmsShipmentTrackingResp,
  WmsShipmentTrackingEventResp
}
