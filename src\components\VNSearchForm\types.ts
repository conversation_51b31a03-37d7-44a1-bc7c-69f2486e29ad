// VNSearchForm 组件类型定义

// 字段配置接口
export interface SearchField {
  field: string
  label: string
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'switch' | 'cascader' | 'slot'
  placeholder?: string
  options?: Array<{ label: string; value: any; disabled?: boolean }>
  props?: Record<string, any>
  labelWidth?: string
  span?: number
  show?: boolean
  required?: boolean
}

// Props 接口
export interface VNSearchFormProps {
  fields: SearchField[]
  model: Record<string, any>
  inline?: boolean
  labelWidth?: string
  gutter?: number
  loading?: boolean
  showExpand?: boolean
  defaultShowCount?: number
  actionColSpan?: number
}

// Emits 接口
export interface VNSearchFormEmits {
  search: []
  reset: []
  'update:model': [value: Record<string, any>]
}

// 组件实例方法接口
export interface VNSearchFormMethods {
  resetFields: () => void
  validate: () => Promise<boolean>
  clearValidate: () => void
  toggleExpand: () => void
} 