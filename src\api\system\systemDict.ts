import request from '@/utils/request'; // 假设您的请求库路径

// --- TypeScript 类型定义 (基于 Go VO 和 DTO) ---

// 基础分页查询参数 (假设结构)
interface BaseQueryDTO {
  pageNum?: number;
  pageSize?: number;
  sort?: string; // 例如 "code,asc" 或 "createdAt,desc"
}

// 基础视图对象 (假设结构)
interface BaseVO {
  id: number;
  createdAt: string;
  updatedAt: string;
  // createdBy?: string;
  // updatedBy?: string;
}

// --- 字典类型相关类型 ---

export interface DictTypeCreateDTO {
  code: string; 
  name: string; 
  status?: number;
  isSystem?: boolean;
  remark?: string;
}

export interface DictTypeUpdateDTO {
  name?: string;
  status?: number;
  isSystem?: boolean;
  remark?: string;
}

export interface DictTypeQueryDTO extends BaseQueryDTO {
  code?: string; 
  name?: string; 
  status?: number;
}

export interface DictionaryTypeVO extends BaseVO {
  code: string;
  name: string;
  status: number;
  isSystem: boolean;
  remark: string;
  itemCount?: number; // 后端 VO 里似乎没有，前端需要时可能得单独处理
}

export interface DictionaryTypeSimpleVO {
  id: number;
  code: string;
  name: string;
}

export interface PageDictTypeVO {
  list: DictionaryTypeVO[];
  total: number;
  pageNum?: number; // 假设分页信息包含在内
  pageSize?: number;
}

// --- 字典项相关类型 ---

export interface DictItemCreateDTO {
  dictionaryTypeId: number;
  label: string;
  value: string;
  sortOrder?: number;
  status?: number;
  isSystem?: boolean;
  remark?: string;
}

export interface DictItemUpdateDTO {
  label?: string;
  value?: string;
  sortOrder?: number;
  status?: number;
  isSystem?: boolean; // 通常不允许修改
  remark?: string;
}

export interface DictItemQueryDTO extends BaseQueryDTO {
  dictionaryTypeId?: number;
  label?: string;
  value?: string;
  status?: number;
}

export interface DictionaryItemVO extends BaseVO {
  dictionaryTypeId: number;
  label: string;
  value: string;
  sortOrder: number;
  status: number;
  isSystem: boolean;
  remark: string;
  // dictionaryTypeCode?: string; // 可选，如果后端填充
  // dictionaryTypeName?: string; // 可选，如果后端填充
}

export interface PageDictItemVO {
  list: DictionaryItemVO[];
  total: number;
  pageNum?: number;
  pageSize?: number;
}

// 公共接口使用
export interface DictDataVO {
  label: string;
  value: string;
}

// --- API 请求函数 ---

const API_BASE_URL = '/sys/dict'; // API 基础路径

// --- 字典类型 API ---

// 分页查询字典类型
export function getDictTypePage(params: Partial<DictTypeQueryDTO>): Promise<PageDictTypeVO> {
  return request.get(`${API_BASE_URL}/types/page`, { params });
}

// 创建字典类型
export function createDictType(data: DictTypeCreateDTO): Promise<DictionaryTypeVO> {
  return request.post(`${API_BASE_URL}/types`, data);
}

// 更新字典类型
export function updateDictType(id: number, data: Partial<DictTypeUpdateDTO>): Promise<DictionaryTypeVO> {
  return request.put(`${API_BASE_URL}/types/${id}`, data);
}

// 删除字典类型
export function deleteDictType(id: number): Promise<void> { // 通常返回空或成功消息
  return request.delete(`${API_BASE_URL}/types/${id}`);
}

// 获取字典类型详情 (如果需要)
export function getDictType(id: number): Promise<DictionaryTypeVO> {
  return request.get(`${API_BASE_URL}/types/${id}`);
}

// 获取启用的字典类型简要列表 (如果需要)
export function getEnabledDictTypeList(): Promise<DictionaryTypeSimpleVO[]> {
  return request.get(`${API_BASE_URL}/types/list`);
}


// --- 字典项 API ---

// 分页查询字典项
export function getDictItemPage(params: Partial<DictItemQueryDTO>): Promise<PageDictItemVO> {
  return request.get(`${API_BASE_URL}/items/page`, { params });
}

// 创建字典项
export function createDictItem(data: DictItemCreateDTO): Promise<DictionaryItemVO> {
  return request.post(`${API_BASE_URL}/items`, data);
}

// 更新字典项
export function updateDictItem(id: number, data: Partial<DictItemUpdateDTO>): Promise<DictionaryItemVO> {
  return request.put(`${API_BASE_URL}/items/${id}`, data);
}

// 删除字典项
export function deleteDictItem(id: number): Promise<void> {
  return request.delete(`${API_BASE_URL}/items/${id}`);
}

// 获取字典项详情 (如果需要)
export function getDictItem(id: number): Promise<DictionaryItemVO> {
  return request.get(`${API_BASE_URL}/items/${id}`);
}

// 根据类型编码获取字典数据项列表
export function getDictDataByCode(typeCode: string): Promise<DictDataVO[]> {
  return request.get(`${API_BASE_URL}/items/list`, { params: { typeCode } });
} 