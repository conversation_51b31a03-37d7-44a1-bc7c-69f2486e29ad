import request from '@/utils/request';
import type { CurrencyListItem } from './currency'; // 引用币种类型

// --- 类型定义 (根据后端 fin_exchange_rate VO/DTO) ---

// 汇率列表项
export interface ExchangeRateListItem {
  id: number;
  fromCurrencyId: number;
  toCurrencyId: number;
  rate: string; // 使用字符串以保持精度
  rateDate: string; // YYYY-MM-DD
  fromCurrency: CurrencyListItem;
  toCurrency: CurrencyListItem;
  createdAt: string;
  updatedAt: string;
}

// 汇率查询参数
export interface ExchangeRateQueryParams {
  pageNum: number;
  pageSize: number;
  fromCurrencyId?: number;
  toCurrencyId?: number;
  rateDate?: string;
  sort?: string;
}

// 汇率列表响应
export interface ExchangeRateListResponse {
  list: ExchangeRateListItem[];
  total: number;
}

// 汇率创建/更新数据
export interface ExchangeRateFormData {
  fromCurrencyId: number | null;
  toCurrencyId: number | null;
  rate: string;
  rateDate: string; // YYYY-MM-DD
}

// 最新汇率查询请求
export interface LatestRateReq {
  sourceCode: string;
  targetCode: string;
  date?: string; // YYYY-MM-DD
}

// 最新汇率响应
export interface LatestRateRes {
  sourceCode: string;
  targetCode: string;
  rate: string;
  rateDate: string;
}

// --- API 函数定义 ---

const BASE_URL = '/fin/exchange-rates';

/**
 * 获取汇率列表 (分页)
 * @param params 查询参数
 */
export function getExchangeRatePage(params: ExchangeRateQueryParams): Promise<ExchangeRateListResponse> {
  return request.get(`${BASE_URL}/page`, { params }) as unknown as Promise<ExchangeRateListResponse>;
}

/**
 * 获取单个汇率详情
 * @param id 汇率ID
 */
export function getExchangeRateDetail(id: number): Promise<ExchangeRateListItem> {
  return request.get(`${BASE_URL}/${id}`) as unknown as Promise<ExchangeRateListItem>;
}

/**
 * 新增汇率
 * @param data 汇率数据
 */
export function addExchangeRate(data: ExchangeRateFormData) {
  return request.post(BASE_URL, data);
}

/**
 * 更新汇率信息
 * @param id 汇率 ID
 * @param data 汇率数据
 */
export function updateExchangeRate(id: number, data: Partial<ExchangeRateFormData>) {
  return request.put(`${BASE_URL}/${id}`, data);
}

/**
 * 删除汇率
 * @param id 汇率 ID
 */
export function deleteExchangeRate(id: number) {
  return request.delete(`${BASE_URL}/${id}`);
}

/**
 * 批量删除汇率
 * @param ids 汇率 ID 数组
 */
// export function batchDeleteExchangeRates(ids: number[]) {
//   return request.delete(BASE_URL, { data: { ids } });
// }

/**
 * 查询最新汇率
 * @param params 查询参数
 */
export function getLatestRate(params: LatestRateReq): Promise<LatestRateRes> {
  return request.get(`${BASE_URL}/latest`, { params }) as unknown as Promise<LatestRateRes>;
} 