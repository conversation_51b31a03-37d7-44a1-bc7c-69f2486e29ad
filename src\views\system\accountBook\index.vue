<template>
  <div>
    <!-- VNTable for displaying account books -->
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'" 
      highlight-current-row
      operation-fixed="right"
      :operation-width="180" 
      @refresh="loadData"
      @add="handleAdd"
      @batch-delete="handleBatchDelete" 
      @filter="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      @import="handleImport"
      @export="handleExport"
    >
      <!-- Status column slot -->
      <template #column-status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- Boolean column slots (IsGroup, IsVirtual) -->
      <template #column-isGroup="{ row }">
        <span>{{ row.isGroup === true ? '是' : '否' }}</span>
      </template>
      <template #column-isVirtual="{ row }">
        <span>{{ row.isVirtual === true ? '是' : '否' }}</span>
      </template>

    </VNTable>

    <!-- Dialog for Add/Edit -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="60%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <!-- Adjust width as needed -->
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2" 
        :label-width="'100px'"
        :loading="loading"
        :show-actions="false"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <!-- 新增：自定义表单操作按钮插槽 -->
        <template #actions>
            <template v-if="isViewing">
                <el-button
                  v-if="hasPermission('system:accountbook:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreview"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('system:accountbook:print')" 
                  type="primary"
                  :icon="Printer" 
                  @click="handlePrint"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
                <el-button @click="handleCloseDialog">取消</el-button>
            </template>
        </template>
        <!-- No slots needed for standard fields -->
      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, PaginationConfig, ActionButton } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton } from 'element-plus';
// import type { FormInstance, FormRules } from 'element-plus';

// --- Import API Service ---
import {
  pageAccountBooks,
  addAccountBook,
  updateAccountBook,
  deleteAccountBook,
  getAccountBookByID,
  // updateAccountBookStatus,
  importAccountBooks,
} from '@/api/system/systemAccountBook';
import type {
  AccountBookVO,
  AccountBookCreateDTO,
  AccountBookUpdateDTO,
  AccountBookQueryDTO,
  AccountBookListResponse
} from '@/api/system/systemAccountBook';

import { hasPermission } from '@/hooks/usePermission';
import { View, Printer } from '@element-plus/icons-vue';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Component Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentRow = ref<AccountBookVO | null>(null);
const isViewing = computed(() => formMode.value === 'view');

// --- Reactive State ---
const loading = ref(false);
const tableData = ref<AccountBookVO[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const formData = ref<Partial<AccountBookCreateDTO | AccountBookUpdateDTO>>({});

const currentFilters = ref<AccountBookQueryDTO>({});
const currentSort = ref<{ prop: string; order: 'asc' | 'desc' } | null>(null);
const selectedIds = ref<number[]>([]);

// --- Table Columns Definition ---
const tableColumns = ref<TableColumn[]>([
  { prop: 'code', label: '账套编码', minWidth: 120, sortable: true, filterable: true },
  { prop: 'name', label: '账套名称', minWidth: 150, sortable: true, filterable: true },
  { prop: 'companyName', label: '公司名称', minWidth: 180, filterable: true },
  { prop: 'taxId', label: '税号', minWidth: 150, filterable: false }, // Example: disable filter for taxId
  {
    prop: 'status',
    label: '状态',
    width: 90,
    slot: true,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 },
    ]
  },
  {
    prop: 'isGroup',
    label: '集团账套',
    width: 100,
    slot: true,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false },
    ]
  },
  {
    prop: 'isVirtual',
    label: '虚拟账套',
    width: 100,
    slot: true,
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false },
    ]
  },
  // Add other fields as needed, e.g., CompanyAddress, BankInfo etc.
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    sortable: true,
    formatter: (row) => row.createdAt ? new Date(row.createdAt).toLocaleString() : '-' // Simple formatter
  },
]);

// --- Toolbar Configuration ---
const toolbarConfig = computed(() => ({
  refresh: true,
  add: hasPermission('system:accountbook:add'), // Use appropriate permission keys
  batchDelete: hasPermission('system:accountbook:batchdelete'),
  filter: hasPermission('system:accountbook:search'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('system:accountbook:import'),
  export: hasPermission('system:accountbook:export'),
}));

// --- Row Operation Buttons ---
const operationButtons = computed<ActionButton[]>(() => [
  {
    label: '查看',
    icon: 'View',
    handler: (row) => handleView(row),
    hidden: !hasPermission('system:accountbook:view')
  },
  {
    label: '编辑',
    icon: 'Edit',
    type: 'primary',
    handler: (row) => handleEdit(row),
    hidden: !hasPermission('system:accountbook:edit')
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    handler: (row) => handleDelete(row),
    hidden: !hasPermission('system:accountbook:delete')
  },
  // Add button for status toggle if needed
  // {
  //   label: (row) => row.status === 1 ? '禁用' : '启用',
  //   icon: (row) => row.status === 1 ? 'CircleClose' : 'CircleCheck',
  //   type: (row) => row.status === 1 ? 'warning' : 'success',
  //   handler: (row) => handleStatusToggle(row),
  //   hidden: !hasPermission('system:accountbook:status')
  // },
]);

// --- Form Fields Definition (添加 disabled 属性) ---
const formFields = computed<HeaderField[]>(() => [
  // --- 基本信息组 ---
  // 修改 code 字段的 disabled 条件
  { field: 'code', label: '账套编码', group: '基本信息', rules: [{ required: true, message: '账套编码为必填项' }, { max: 50, message: '编码长度不能超过50' }], disabled: formMode.value === 'edit' || isViewing.value },
  { field: 'name', label: '账套名称', group: '基本信息', rules: [{ required: true, message: '账套名称为必填项' }, { max: 100, message: '名称长度不能超过100' }], disabled: isViewing.value },
  { field: 'isGroup', label: '集团账套', group: '基本信息', type: 'switch', disabled: isViewing.value },
  { field: 'isVirtual', label: '虚拟账套', group: '基本信息', type: 'switch', disabled: isViewing.value },
  { field: 'status', label: '状态', group: '基本信息', type: 'radio', options: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }], defaultValue: 1, rules: [{ required: true, message: '请选择状态'}], disabled: isViewing.value },
  // --- 开票资料组 ---
  { field: 'companyName', label: '公司名称', group: '开票资料', props: { maxlength: 100 }, disabled: isViewing.value },
  { field: 'taxId', label: '税号', group: '开票资料', props: { maxlength: 50 }, disabled: isViewing.value },
  { field: 'companyAddress', label: '公司地址', group: '开票资料', props: { maxlength: 255 }, md: 24, lg: 24, xl: 24, disabled: isViewing.value },
  { field: 'companyPhone', label: '公司电话', group: '开票资料', props: { maxlength: 30 }, disabled: isViewing.value },
  { field: 'bankName', label: '开户银行', group: '开票资料', props: { maxlength: 100 }, disabled: isViewing.value },
  { field: 'bankAccount', label: '银行账号', group: '开票资料', props: { maxlength: 50 }, disabled: isViewing.value },
]);

// --- Dialog Title ---
const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增账套';
  if (formMode.value === 'edit') return '编辑账套';
  if (formMode.value === 'view') return '查看账套';
  return '账套管理'; // Default title
});

// --- Lifecycle Hooks ---
onMounted(() => {
  loadData();
});

// --- Methods ---

// Load table data
const loadData = async () => {
  loading.value = true;
  try {
    const params: AccountBookQueryDTO & { page: number; size: number } = {
      page: pagination.currentPage,
      size: pagination.pageSize,
      ...currentFilters.value,
      sortField: currentSort.value?.prop,
      sortOrder: currentSort.value?.order,
    };
    const res = await pageAccountBooks(params) as unknown as AccountBookListResponse;
      tableData.value = res.list || [];
      pagination.total = res.total || 0;
  } catch (error) {
    console.error('加载账套列表失败:', error);
    tableData.value = [];
    pagination.total = 0;
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
};

// Handle Add button click
const handleAdd = () => {
  handleOpenForm('add');
};

// Handle Edit button click
const handleEdit = (row: AccountBookVO) => {
  handleOpenForm('edit', row);
};

// Handle View button click
const handleView = (row: AccountBookVO) => {
  handleOpenForm('view', row);
};

// Handle Dialog Close
const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentRow.value = null;
};

// Handle Form Submit (Add/Edit)
const submitForm = async () => {
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  loading.value = true;
  try {
    if (formMode.value === 'add') {
      await addAccountBook(formData.value as AccountBookCreateDTO);
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentRow.value) {
      await updateAccountBook(currentRow.value.id, formData.value as AccountBookUpdateDTO);
      ElMessage.success('编辑成功');
    }
    handleCloseDialog();
    loadData(); // Refresh table data
  } catch (error) {
    console.error(`保存账套时出错 (${formMode.value}):`, error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
};

// Handle Delete button click
const handleDelete = async (row: AccountBookVO) => {
  try {
    await ElMessageBox.confirm(`确定删除账套 "${row.name}" (${row.code}) 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    await deleteAccountBook(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除账套时出错:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    }
  } finally {
    loading.value = false;
  }
};

// Handle Status Toggle (Example - requires button in operationButtons)
// const handleStatusToggle = async (row: AccountBookVO) => {
//   const newStatus = row.status === 1 ? 0 : 1;
//   const actionText = newStatus === 1 ? '启用' : '禁用';
//   try {
//     await ElMessageBox.confirm(`确定要${actionText}账套 "${row.name}" 吗?`, `确认${actionText}`, { type: 'warning' });
//     loading.value = true;
//     await updateAccountBookStatus(row.id, newStatus);
//     ElMessage.success(`${actionText}成功`);
//     loadData();
//   } catch (error) {
//     if (error !== 'cancel') {
//       console.error(`更新账套状态时出错:`, error);
//     }
//   } finally {
//     loading.value = false;
//   }
// };

// Handle Batch Delete
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请先选择要删除的账套');
    return;
  }
  try {
    await ElMessageBox.confirm(`确定删除选中的 ${selectedIds.value.length} 个账套吗?`, '确认批量删除', { type: 'warning' });
    loading.value = true;
    // await batchDeleteAccountBooks(selectedIds.value); // Uncomment when API is ready
    console.warn('后端批量删除接口尚未实现');
    ElMessage.info('批量删除功能暂未启用'); // Temporary message
    // loadData(); // Refresh after successful batch delete
    // vnTableRef.value?.clearSelection();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除账套时出错:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    }
  } finally {
    loading.value = false;
  }
};

// Handle Export button click (using VNTable's method)
const handleExport = () => {
  vnTableRef.value?.ExportTable();
};

// Handle Import event from VNTable
const handleImport = async (importedData: any[]) => {
  if (!importedData || importedData.length === 0) {
    ElMessage.warning('导入的数据为空');
    return;
  }
  // TODO: Add more validation if needed based on AccountBookCreateDTO
  console.log('即将导入的数据:', importedData);
  loading.value = true;
  try {
    // Assuming the imported data structure matches AccountBookCreateDTO after mapping
    await importAccountBooks(importedData as AccountBookCreateDTO[]);
    ElMessage.success(`成功导入 ${importedData.length} 条数据`);
    loadData(); // Refresh data after import
  } catch (error) {
    console.error('导入账套时出错:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
    });
  } finally {
    loading.value = false;
  }
};

// --- VNTable Event Handlers ---

// Filter change
const handleFilterChange = (filters: Record<string, any>) => {
    console.log('筛选条件变化 (AccountBook): ', filters);
    // Transform filters if necessary, e.g., boolean strings to actual booleans
    const processedFilters: Partial<AccountBookQueryDTO> = {}; // Use Partial for incremental assignment
    for (const key in filters) {
        if (Object.prototype.hasOwnProperty.call(filters, key)) {
            const value = filters[key];
            // Example: convert potential boolean string filters from URL to boolean
            if ((key === 'isGroup' || key === 'isVirtual')) { // Check the key specifically
                 if (typeof value === 'string') {
                    if (value === 'true') processedFilters[key] = true; // Direct assignment
                    else if (value === 'false') processedFilters[key] = false; // Direct assignment
                 } else if (typeof value === 'boolean') {
                    processedFilters[key] = value; // Direct assignment if already boolean
                 }
                 // else ignore invalid boolean string or other types for these specific keys
            } else if (value !== null && value !== undefined && value !== '') { // Handle other keys
                 // --- FIX: Keep Type Assertion ONLY for other keys --- 
                 processedFilters[key as keyof AccountBookQueryDTO] = value; // Use assertion here
            }
        }
    }
    currentFilters.value = processedFilters;
    pagination.currentPage = 1;
    loadData();
};

// Page change
const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

// Page size change
const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1; // Reset to first page
  loadData();
};

// Sort change
const handleSortChange = (sort: { prop: string; order: 'ascending' | 'descending' | null }) => {
  currentSort.value = sort.order ? { prop: sort.prop, order: sort.order === 'ascending' ? 'asc' : 'desc' } : null;
  loadData();
};

// Selection change
const handleSelectionChange = (rows: AccountBookVO[]) => {
  selectedIds.value = rows.map(row => row.id);
};

// Open form
const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: AccountBookVO) => {
  formMode.value = mode;
  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id) {
      console.error('编辑或查看模式下缺少账套数据或ID');
      return;
    }
    currentRow.value = rowData;
    dialogVisible.value = true;
    loading.value = true;
    try {
      const detail = await getAccountBookByID(rowData.id);
      if (detail && typeof detail === 'object') {
        formData.value = { ...detail };
      } else {
        console.warn(`[handleOpenForm] Received invalid detailData from API for account book ${rowData.id}:`, detail);
        const errDetailMsg = getApiErrorMessage({message: '获取账套详细信息失败，数据格式不正确。'});
        ElMessage({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: errDetailMsg,
            showClose: true,
            duration: 5 * 1000
        });
        dialogVisible.value = false;
        return;
      }
    } catch (error) {
      console.error('获取账套详情失败:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
      dialogVisible.value = false;
      return;
    } finally {
      loading.value = false;
    }
  } else {
    formData.value = { status: 1, isGroup: false, isVirtual: false };
    currentRow.value = null;
    dialogVisible.value = true;
  }

  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

// --- 新增：打印处理函数（占位）---
const handlePrintPreview = () => {
  if (!currentRow.value) return;
  console.log('触发打印预览，账套:', currentRow.value);
  ElMessage.info('打印预览功能待实现');
  // TODO: Implement print preview logic for account books
};

const handlePrint = () => {
  if (!currentRow.value) return;
  console.log('触发打印，账套:', currentRow.value);
  ElMessage.info('打印功能待实现');
  // TODO: Implement print logic for account books
};

</script>

<style scoped>
/* Add specific styles if needed */
/* 移除直接对 el-pagination 的 justify-content 设置，由 VNTable 内部容器控制 */
:deep(.el-pagination) {
  margin-top: 15px;
  /* justify-content: flex-end; <<< Remove this line */
}
</style> 