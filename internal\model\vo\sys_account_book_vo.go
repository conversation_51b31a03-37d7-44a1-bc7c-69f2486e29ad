package vo

// AccountBookVO 账套视图对象
type AccountBookVO struct {
	TenantVO              // 嵌入 TenantVO，包含 ID, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, TenantID
	Code           string `json:"code"`
	Name           string `json:"name"`
	CompanyName    string `json:"companyName,omitempty"`
	TaxID          string `json:"taxId,omitempty"`
	CompanyAddress string `json:"companyAddress,omitempty"`
	CompanyPhone   string `json:"companyPhone,omitempty"`
	BankName       string `json:"bankName,omitempty"`
	BankAccount    string `json:"bankAccount,omitempty"`
	IsGroup        *bool  `json:"isGroup"` // 使用指针与 DTO/Entity 保持一致，并能表示 null
	IsVirtual      *bool  `json:"isVirtual"`
	Status         int    `json:"status"`
}

// AccountBookSimpleVO 账套简要信息视图对象
type AccountBookSimpleVO struct {
	ID   uint   `json:"id"`   // ID：账套的唯一标识
	Name string `json:"name"` // Name：账套的名称
}

// 你可以在此文件继续添加其他与账套相关的 VO 定义
