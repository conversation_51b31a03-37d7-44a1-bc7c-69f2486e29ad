// ==================== 导入类型 ====================
import type { 
  WmsOutboundNotificationStatus, 
  WmsOutboundNotificationPriority,
  WmsOutboundNotificationResp,
  WmsOutboundNotificationDetailResp,
  WmsClientResp,
  WmsWarehouseResp,
  WmsItemResp,
  WmsCarrierResp
} from '@/api/wms/outboundNotification'

// ==================== 基础类型 ====================

// 选择器选项类型
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  color?: string
}

// ==================== 表单数据类型 ====================

// 明细行数据（用于表单编辑）
export interface DetailRowData {
  id?: number
  lineNo: number
  itemId?: number
  itemCode?: string
  itemName?: string
  itemSpec?: string
  requiredQty: number
  allocatedQty?: number
  pickedQty?: number
  shippedQty?: number
  unitOfMeasure: string
  requiredBatchNo?: string
  requiredProductionDate?: string
  requiredExpiryDate?: string
  remark?: string
  // 表单状态标识
  _isNew?: boolean
  _toDelete?: boolean
  _hasChanged?: boolean
  _editing?: boolean
}

// 出库通知单表单数据
export interface OutboundNotificationFormData {
  // 主表数据
  id?: number
  notificationNo?: string
  clientId: number | null
  clientName?: string
  clientOrderNo?: string
  warehouseId: number | null
  warehouseName?: string
  requiredShipDate?: string
  priority: WmsOutboundNotificationPriority
  consigneeName?: string
  consigneePhone?: string
  consigneeAddress?: string
  carrierId?: number | null
  carrierName?: string
  shippingMethod?: string
  remark?: string
  status?: WmsOutboundNotificationStatus
  
  // 明细表数据
  details: DetailRowData[]
  
  // 表单状态
  _mode?: 'create' | 'edit' | 'view'
  _loading?: boolean
  _submitting?: boolean
}

// ==================== 组件Props类型 ====================

// 客户选择器Props
export interface CustomerSelectorProps {
  modelValue?: number | null
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  filterable?: boolean
  multiple?: boolean
  size?: 'large' | 'default' | 'small'
}

// 库存分配组件Props
export interface InventoryAllocationProps {
  notificationId: number
  details: DetailRowData[]
  visible?: boolean
}

// 状态流转组件Props
export interface StatusFlowProps {
  status: WmsOutboundNotificationStatus
  statusHistory?: StatusHistoryItem[]
  readonly?: boolean
}

// 状态历史项
export interface StatusHistoryItem {
  status: WmsOutboundNotificationStatus
  timestamp: string
  operator: string
  remark?: string
}

// ==================== 常量定义 ====================

// 状态选项
export const STATUS_OPTIONS: SelectOption[] = [
  { value: 'DRAFT', label: '草稿', color: '#909399' },
  { value: 'PLANNED', label: '已计划', color: '#409EFF' },
  { value: 'APPROVED', label: '已审核', color: '#67C23A' },
  { value: 'ALLOCATING', label: '库存分配中', color: '#E6A23C' },
  { value: 'ALLOCATED', label: '已分配', color: '#67C23A' },
  { value: 'PICKING', label: '拣货中', color: '#F56C6C' },
  { value: 'PICKED', label: '已拣货', color: '#67C23A' },
  { value: 'SHIPPED', label: '已发运', color: '#409EFF' },
  { value: 'COMPLETED', label: '已完成', color: '#67C23A' },
  { value: 'CANCELLED', label: '已取消', color: '#F56C6C' }
]

// 优先级选项
export const PRIORITY_OPTIONS: SelectOption[] = [
  { value: 1, label: '最高', color: '#F56C6C' },
  { value: 2, label: '高', color: '#E6A23C' },
  { value: 3, label: '中', color: '#409EFF' },
  { value: 4, label: '低', color: '#909399' },
  { value: 5, label: '最低', color: '#C0C4CC' }
]

// 运输方式选项
export const SHIPPING_METHOD_OPTIONS: SelectOption[] = [
  { value: 'EXPRESS', label: '快递' },
  { value: 'LOGISTICS', label: '物流' },
  { value: 'SELF_PICKUP', label: '自提' },
  { value: 'DIRECT_DELIVERY', label: '直送' }
]

// 单位选项
export const UNIT_OPTIONS: SelectOption[] = [
  { value: 'PCS', label: '件' },
  { value: 'BOX', label: '箱' },
  { value: 'KG', label: '千克' },
  { value: 'G', label: '克' },
  { value: 'L', label: '升' },
  { value: 'ML', label: '毫升' },
  { value: 'M', label: '米' },
  { value: 'CM', label: '厘米' }
]

// ==================== 工具函数类型 ====================

// 状态格式化函数类型
export type StatusFormatter = (status: WmsOutboundNotificationStatus) => string

// 状态颜色配置类型
export interface StatusColorConfig {
  color: string
  bgColor: string
  borderColor: string
}

// 状态颜色映射类型
export type StatusColorMap = Record<WmsOutboundNotificationStatus, StatusColorConfig>

// 表单验证规则类型
export interface FormValidationRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  validator?: (rule: any, value: any, callback: any) => void
}

// 表单验证规则映射类型
export type FormValidationRules = Record<string, FormValidationRule[]>

// 批量操作类型
export interface BatchOperation {
  type: 'delete' | 'approve' | 'cancel' | 'allocate' | 'export'
  label: string
  icon?: string
  permission?: string
  disabled?: boolean
  handler: (selectedRows: any[]) => void | Promise<void>
}

// 导入导出配置类型
export interface ImportExportConfig {
  templateUrl?: string
  maxFileSize?: number
  allowedExtensions?: string[]
  validateHeaders?: boolean
  requiredColumns?: string[]
}

// 打印配置类型
export interface PrintConfig {
  templateId?: string
  orientation?: 'portrait' | 'landscape'
  paperSize?: 'A4' | 'A5' | 'Letter'
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

// ==================== 业务逻辑类型 ====================

// 库存分配策略
export type AllocationStrategy = 
  | 'FIFO'            // 先进先出
  | 'LIFO'            // 后进先出
  | 'BATCH_PRIORITY'  // 批次优先
  | 'EXPIRY_DATE_PRIORITY' // 过期日期优先
  | 'LOCATION_PRIORITY'    // 库位优先

// 库存分配结果
export interface AllocationResult {
  itemId: number
  itemCode: string
  requiredQty: number
  allocatedQty: number
  unallocatedQty: number
  allocations: AllocationDetail[]
}

// 分配明细
export interface AllocationDetail {
  inventoryId: number
  locationCode: string
  batchNo?: string
  availableQty: number
  allocatedQty: number
  productionDate?: string
  expiryDate?: string
}

// 拣货任务生成配置
export interface PickingTaskConfig {
  strategy: 'BY_ORDER' | 'BY_ITEM' | 'BY_LOCATION' | 'BATCH_PICKING'
  waveSize?: number
  assignToUser?: number
  priority?: number
  estimatedDuration?: number
}

// 发运配置
export interface ShipmentConfig {
  carrierId?: number
  shippingMethod?: string
  packageCount?: number
  totalWeight?: number
  totalVolume?: number
  estimatedCost?: number
  requiresInsurance?: boolean
  specialInstructions?: string
}

// ==================== 事件类型 ====================

// 表单事件
export interface FormEvents {
  submit: (data: OutboundNotificationFormData) => void
  cancel: () => void
  reset: () => void
  validate: () => Promise<boolean>
}

// 列表事件
export interface ListEvents {
  refresh: () => void
  search: (params: any) => void
  sort: (column: string, order: 'asc' | 'desc') => void
  filter: (filters: Record<string, any>) => void
  select: (rows: any[]) => void
}

// 状态变更事件
export interface StatusChangeEvents {
  approve: (id: number, remark?: string) => void
  cancel: (id: number, reason?: string) => void
  allocate: (id: number, strategy?: AllocationStrategy) => void
  generatePickingTask: (id: number, config?: PickingTaskConfig) => void
}

// ==================== 响应类型扩展 ====================

// 扩展的出库通知单响应类型（包含计算字段）
export interface ExtendedOutboundNotificationResp extends WmsOutboundNotificationResp {
  // 统计字段（与后端VO保持一致）
  totalItems?: number
  allocatedItems?: number
  pickedItems?: number
  shippedItems?: number
  totalRequiredQty?: number
  totalAllocatedQty?: number
  totalPickedQty?: number
  totalShippedQty?: number
  allocationProgress?: number
  pickingProgress?: number
  shippingProgress?: number

  // 状态标识
  canEdit?: boolean
  canDelete?: boolean
  canApprove?: boolean
  canCancel?: boolean
  canAllocate?: boolean
  canGeneratePickingTask?: boolean

  // 关联数据
  client?: WmsClientResp
  warehouse?: WmsWarehouseResp
  carrier?: WmsCarrierResp
}

// 扩展的明细响应类型
export interface ExtendedOutboundNotificationDetailResp extends WmsOutboundNotificationDetailResp {
  // 计算字段
  unallocatedQty?: number
  unpickedQty?: number
  unshippedQty?: number
  allocationProgress?: number
  pickingProgress?: number
  shippingProgress?: number
  
  // 关联数据
  item?: WmsItemResp
  
  // 状态标识
  isFullyAllocated?: boolean
  isFullyPicked?: boolean
  isFullyShipped?: boolean
}

// ==================== 导出类型 ====================
export type {
  WmsOutboundNotificationStatus,
  WmsOutboundNotificationPriority,
  WmsOutboundNotificationResp,
  WmsOutboundNotificationDetailResp,
  WmsClientResp,
  WmsWarehouseResp,
  WmsItemResp,
  WmsCarrierResp
}
