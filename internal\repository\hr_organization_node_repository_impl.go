package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"backend/internal/model/entity"
	pkgerrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/util"

	"gorm.io/gorm"
	"gorm.io/gorm/clause" // 引入 clause 用于锁

	"backend/pkg/constant"
)

// OrganizationNodeRepository 组织节点仓库接口
type OrganizationNodeRepository interface {
	BaseRepository[entity.OrganizationNode, uint] // 嵌入基础仓库接口

	// FindByParentID 查找指定父节点下的所有子节点 (按 OrderNum 排序)
	FindByParentID(ctx context.Context, parentID *uint) ([]*entity.OrganizationNode, error)

	// FindRootNodes 查找顶层节点 (ParentID IS NULL, 按 OrderNum 排序)
	FindRootNodes(ctx context.Context) ([]*entity.OrganizationNode, error)

	// FindByCode 根据编码查找节点
	FindByCode(ctx context.Context, code string) (*entity.OrganizationNode, error)

	// FindByNameAndParent 根据名称和父ID查找节点
	FindByNameAndParent(ctx context.Context, parentID *uint, name string) (*entity.OrganizationNode, error)

	// UpdateStatus 更新节点状态
	UpdateStatus(ctx context.Context, id uint, status int) error

	// GetMaxOrderNum 获取指定父节点下的最大排序号
	GetMaxOrderNum(ctx context.Context, parentID *uint) (int, error)

	// GetNodeWithLock 获取节点并加行锁 (PESSIMISTIC_WRITE)
	GetNodeWithLock(ctx context.Context, id uint) (*entity.OrganizationNode, error)

	// CreateInTx 在事务中创建组织节点
	CreateInTx(ctx context.Context, tx *gorm.DB, node *entity.OrganizationNode) error

	// UpdateInTx 在事务中更新组织节点，并处理子孙节点的 Level 和 CompanyID 递归更新
	UpdateInTx(ctx context.Context, tx *gorm.DB, node *entity.OrganizationNode, originalParentID *uint, originalCompanyID *uint) error

	// UpdateCompanyIDForNodeInTx 在事务中为指定节点更新 CompanyID (通常用于顶级公司创建后回填)
	UpdateCompanyIDForNodeInTx(ctx context.Context, tx *gorm.DB, nodeID uint, companyID *uint) error

	// GetTree 获取指定根节点下的所有子孙节点，用于构建树
	GetTree(ctx context.Context, rootParentID *uint) ([]*entity.OrganizationNode, error)

	// UpdateStandardHeadcountInTx 在事务中更新指定节点的标配人数
	UpdateStandardHeadcountInTx(ctx context.Context, tx *gorm.DB, nodeID uint, headcount int) error

	// DeleteInTx 在事务中删除组织节点
	DeleteInTx(ctx context.Context, tx *gorm.DB, id uint) error
}

// organizationNodeRepository 组织节点仓库实现
type organizationNodeRepository struct {
	*BaseRepositoryImpl[entity.OrganizationNode, uint] // 嵌入基础仓库实现
}

// NewOrganizationNodeRepository 创建组织节点仓库
func NewOrganizationNodeRepository(db *gorm.DB) OrganizationNodeRepository {
	baseRepo := NewBaseRepository[entity.OrganizationNode, uint](db)
	return &organizationNodeRepository{
		BaseRepositoryImpl: baseRepo.(*BaseRepositoryImpl[entity.OrganizationNode, uint]),
	}
}

// GetNodeWithLock 获取节点并加行锁
func (r *organizationNodeRepository) GetNodeWithLock(ctx context.Context, id uint) (*entity.OrganizationNode, error) {
	var node entity.OrganizationNode
	db := r.GetDB(ctx)
	// 使用 Clauses(clause.Locking{Strength: "UPDATE"}) 进行悲观写锁
	// 这通常对应于 SELECT ... FOR UPDATE
	if err := db.Clauses(clause.Locking{Strength: "UPDATE"}).First(&node, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, pkgerrors.ErrDataNotFound
		}
		logger.WithContext(ctx).WithError(err).WithField("id", id).Error("获取组织节点并加锁失败")
		return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "获取组织节点失败").WithCause(err)
	}
	return &node, nil
}

// --- 实现自定义查询方法 --- //

// FindByParentID 实现查找指定父节点下的所有子节点
func (r *organizationNodeRepository) FindByParentID(ctx context.Context, parentID *uint) ([]*entity.OrganizationNode, error) {
	var nodes []*entity.OrganizationNode
	db := r.GetDB(ctx)
	query := db.Order("order_num asc")
	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}
	result := query.Find(&nodes)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("查询子组织节点失败")
		return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "查询子组织节点失败").WithCause(result.Error)
	}
	return nodes, nil
}

// FindRootNodes 实现查找顶层节点 (ParentID IS NULL)
func (r *organizationNodeRepository) FindRootNodes(ctx context.Context) ([]*entity.OrganizationNode, error) {
	return r.FindByParentID(ctx, nil) // 顶层节点的 ParentID 为 NULL
}

// FindByCode 实现根据编码查找节点
func (r *organizationNodeRepository) FindByCode(ctx context.Context, code string) (*entity.OrganizationNode, error) {
	var node entity.OrganizationNode
	db := r.GetDB(ctx)
	// 编码在同一账套下应该是唯一的
	result := db.Where("code = ?", code).First(&node)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 对于 FindByCode，记录未找到不一定是错误，返回标准未找到错误
			return nil, pkgerrors.ErrDataNotFound
		}
		logger.WithContext(ctx).WithError(result.Error).WithField("code", code).Error("根据编码查询组织节点失败")
		return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "查询组织节点失败").WithCause(result.Error)
	}
	return &node, nil
}

// FindByNameAndParent 实现根据名称和父ID查找节点
func (r *organizationNodeRepository) FindByNameAndParent(ctx context.Context, parentID *uint, name string) (*entity.OrganizationNode, error) {
	var node entity.OrganizationNode
	db := r.GetDB(ctx)
	query := db.Where("name = ?", name)
	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}
	result := query.First(&node)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, pkgerrors.ErrDataNotFound
		}
		logger.WithContext(ctx).WithError(result.Error).WithField("name", name).WithField("parentID", parentID).Error("根据名称和父ID查询组织节点失败")
		return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "查询组织节点失败").WithCause(result.Error)
	}
	return &node, nil
}

// UpdateStatus 实现更新节点状态
func (r *organizationNodeRepository) UpdateStatus(ctx context.Context, id uint, status int) error {
	db := r.GetDB(ctx)
	log := logger.WithContext(ctx) // Get logger for context-aware logging

	uid64, err := util.GetUserIDFromStdContext(ctx) // Assuming util is imported and GetUserIDFromStdContext is available
	if err != nil {
		log.WithError(err).Warn("OrganizationNodeRepository.UpdateStatus: 无法从上下文中获取UserID，UpdatedBy将不会被设置")
		// Depending on business rules, you might choose to proceed without UpdatedBy or return an error.
		// Here, we proceed but log a warning. uid64 will be 0.
	}

	updates := map[string]interface{}{
		"status": status,
	}
	if uid64 > 0 { // Only add updated_by if a valid user ID was retrieved
		updates["updated_by"] = uint(uid64)
		log.Infof("OrganizationNodeRepository.UpdateStatus: Setting updated_by to %d for node ID %d", uint(uid64), id)
	} else {
		log.Warnf("OrganizationNodeRepository.UpdateStatus: updated_by will not be set for node ID %d as UserID could not be retrieved or was invalid (0).", id)
	}

	result := db.Model(&entity.OrganizationNode{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		log.WithError(result.Error).Error("更新组织节点状态数据库操作失败") // Log with more context
		return pkgerrors.NewDataError(pkgerrors.CODE_DATA_UPDATE_FAILED, fmt.Sprintf("更新组织节点状态失败, id=%d", id)).WithCause(result.Error)
	}
	if result.RowsAffected == 0 {
		var count int64
		// Check if the record exists to differentiate between "not found" and "no change needed"
		checkErr := db.Model(&entity.OrganizationNode{}).Where("id = ?", id).Count(&count).Error
		if checkErr != nil {
			log.WithError(checkErr).Error("OrganizationNodeRepository.UpdateStatus: 检查节点是否存在失败")
			return pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "检查节点是否存在失败").WithCause(checkErr)
		}
		if count == 0 {
			return pkgerrors.NewDataError(pkgerrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("组织节点记录未找到, id=%d", id))
		}
		// Record exists, but RowsAffected is 0. This means status was already the target value or no effective change.
		log.Warnf("OrganizationNodeRepository.UpdateStatus: RowsAffected is 0 for node ID %d. Record exists, so status was likely already the target value or no change was needed.", id)
		return nil // Considered a success as the state is as intended (idempotent)
	}
	return nil
}

// GetMaxOrderNum 获取指定父节点下的最大排序号
func (r *organizationNodeRepository) GetMaxOrderNum(ctx context.Context, parentID *uint) (int, error) {
	var maxOrderNum int
	db := r.GetDB(ctx)
	query := db.Model(&entity.OrganizationNode{})
	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}
	result := query.Select("COALESCE(MAX(order_num), 0)").Scan(&maxOrderNum)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).WithField("parentID", parentID).Error("获取最大排序号失败")
		return 0, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "获取最大排序号失败").WithCause(result.Error)
	}
	return maxOrderNum, nil
}

// CreateInTx 在事务中创建组织节点
func (r *organizationNodeRepository) CreateInTx(ctx context.Context, tx *gorm.DB, node *entity.OrganizationNode) error {
	log := logger.WithContext(ctx)

	// 1. 唯一性检查 (Code 和 Name)
	if node.Code != "" {
		var count int64
		if err := tx.Model(&entity.OrganizationNode{}).Where("code = ?", node.Code).Count(&count).Error; err != nil {
			log.WithError(err).Error("检查节点编码唯一性失败")
			return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "检查节点编码唯一性失败").WithCause(err)
		}
		if count > 0 {
			return pkgerrors.NewDataError(constant.DATA_ALREADY_EXISTS, fmt.Sprintf("节点编码 [%s] 已存在", node.Code))
		}
	}

	var countName int64
	nameQuery := tx.Model(&entity.OrganizationNode{}).Where("name = ?", node.Name)
	if node.ParentID == nil {
		nameQuery = nameQuery.Where("parent_id IS NULL")
	} else {
		nameQuery = nameQuery.Where("parent_id = ?", *node.ParentID)
	}
	if err := nameQuery.Count(&countName).Error; err != nil {
		log.WithError(err).Error("检查节点名称唯一性失败")
		return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "检查节点名称唯一性失败").WithCause(err)
	}
	if countName > 0 {
		return pkgerrors.NewDataError(constant.DATA_ALREADY_EXISTS, fmt.Sprintf("同级下已存在名称为 [%s] 的节点", node.Name))
	}

	// 2. 设置 Level 和 CompanyID
	if node.ParentID != nil && *node.ParentID > 0 { // 明确有父节点ID
		var parentNode entity.OrganizationNode
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&parentNode, *node.ParentID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return pkgerrors.NewDataError(constant.DATA_NOT_FOUND, fmt.Sprintf("父节点 [ID=%d] 不存在", *node.ParentID))
			}
			log.WithError(err).Error("查询父节点失败")
			return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "查询父节点失败").WithCause(err)
		}
		node.Level = parentNode.Level + 1
		// node.CompanyID 由 Service 层在调用此方法前设置，或对于顶级公司在创建后回填。
		// 如果 parentNode.CompanyID 是 *uint，这里需要相应处理
		// if parentNode.CompanyID != nil { node.CompanyID = parentNode.CompanyID } // 示例：如果子节点总是继承父节点的CompanyID
	} else { // 顶级节点 (ParentID is nil)
		node.Level = 1
		// CompanyID 对于顶级公司，由 Service 层在创建后获取其自身 ID 再回填。
		// 初始插入时，如果 node.CompanyID 是 nil，则会插入 NULL (如果列允许)
	}

	// 3. 设置 OrderNum (如果未提供)
	if node.OrderNum == 0 {
		var maxOrderNum int
		orderQuery := tx.Model(&entity.OrganizationNode{})
		if node.ParentID == nil {
			orderQuery = orderQuery.Where("parent_id IS NULL")
		} else {
			orderQuery = orderQuery.Where("parent_id = ?", *node.ParentID)
		}
		if err := orderQuery.Select("COALESCE(MAX(order_num), 0)").Scan(&maxOrderNum).Error; err != nil {
			log.WithError(err).Warn("获取最大排序号失败，将使用默认值 1")
			node.OrderNum = 1
		} else {
			node.OrderNum = maxOrderNum + 1
		}
	}

	// 4. 执行创建
	if err := tx.Create(node).Error; err != nil {
		log.WithError(err).Error("创建组织节点失败")
		// 检查是否是外键约束错误，特别是 parent_id
		if strings.Contains(err.Error(), "fk_hr_organization_node_children") || (strings.Contains(err.Error(), "FOREIGN KEY constraint failed") && node.ParentID != nil) {
			log.Error("创建节点时可能违反了 parent_id 外键约束", logger.WithField("parentID", node.ParentID))
			return pkgerrors.NewBusinessError(pkgerrors.CODE_PARAMS_INVALID, fmt.Sprintf("指定的父节点 (ID: %v) 可能不存在或无效", node.ParentID)).WithCause(err)
		}
		return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "创建组织节点失败").WithCause(err)
	}
	log.Info("组织节点创建成功", logger.WithField("nodeID", node.ID))
	return nil
}

// UpdateInTx 在事务中更新组织节点，并处理子孙节点的 Level 和 CompanyID 递归更新
func (r *organizationNodeRepository) UpdateInTx(ctx context.Context, tx *gorm.DB, node *entity.OrganizationNode, originalParentID *uint, originalCompanyID *uint) error {
	log := logger.WithContext(ctx)
	log.Debug("UpdateInTx 开始", logger.WithField("nodeID", node.ID))

	if node.ID == 0 {
		return pkgerrors.NewParamError(pkgerrors.CODE_PARAMS_INVALID, "更新节点时ID不能为空")
	}

	var originalNode entity.OrganizationNode
	if err := tx.First(&originalNode, node.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return pkgerrors.NewDataError(pkgerrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("尝试更新的节点 [ID=%d] 不存在", node.ID))
		}
		log.WithError(err).Error("获取原始节点信息失败")
		return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "获取原始节点信息失败").WithCause(err)
	}
	// originalParentID 和 originalCompanyID 参数现在是 *uint, 与从 originalNode 获取的一致

	if node.Code != originalNode.Code && node.Code != "" {
		var existingCodeNode entity.OrganizationNode
		codeQuery := tx.Model(&entity.OrganizationNode{}).Where("code = ?", node.Code)
		if err := codeQuery.First(&existingCodeNode).Error; err == nil && existingCodeNode.ID != node.ID {
			log.Warn("更新节点失败: 编码已存在", logger.WithField("code", node.Code), logger.WithField("existingNodeID", existingCodeNode.ID))
			return pkgerrors.NewDataError(constant.DATA_ALREADY_EXISTS, fmt.Sprintf("节点编码 [%s] 已被节点 [ID=%d] 使用", node.Code, existingCodeNode.ID))
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WithError(err).Error("检查节点编码唯一性失败")
			return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "检查节点编码唯一性失败").WithCause(err)
		}
	}

	// 比较 ParentID: nil vs nil, nil vs value, value vs nil, value vs value
	parentChanged := (node.ParentID == nil && originalNode.ParentID != nil) ||
		(node.ParentID != nil && originalNode.ParentID == nil) ||
		(node.ParentID != nil && originalNode.ParentID != nil && *node.ParentID != *originalNode.ParentID)

	if node.Name != originalNode.Name || parentChanged {
		var existingNameNode entity.OrganizationNode
		nameQuery := tx.Model(&entity.OrganizationNode{}).Where("name = ?", node.Name)
		if node.ParentID == nil {
			nameQuery = nameQuery.Where("parent_id IS NULL")
		} else {
			nameQuery = nameQuery.Where("parent_id = ?", *node.ParentID)
		}
		if err := nameQuery.First(&existingNameNode).Error; err == nil && existingNameNode.ID != node.ID {
			log.Warn("更新节点失败: 名称已存在", logger.WithField("name", node.Name), logger.WithField("parentID", node.ParentID), logger.WithField("existingNodeID", existingNameNode.ID))
			return pkgerrors.NewDataError(constant.DATA_ALREADY_EXISTS, fmt.Sprintf("父节点 [ID=%v] 下已存在名称为 [%s] 的节点 [ID=%d]", node.ParentID, node.Name, existingNameNode.ID))
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WithError(err).Error("检查节点名称唯一性失败")
			return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "检查节点名称唯一性失败").WithCause(err)
		}
	}

	if err := tx.Save(node).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return pkgerrors.NewDataError(pkgerrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("要更新的组织节点 [ID=%d] 不存在", node.ID))
		}
		// 检查是否是外键约束错误，特别是 parent_id
		if strings.Contains(err.Error(), "fk_hr_organization_node_children") || (strings.Contains(err.Error(), "FOREIGN KEY constraint failed") && node.ParentID != nil) {
			log.Error("更新节点时可能违反了 parent_id 外键约束", logger.WithField("parentID", node.ParentID))
			return pkgerrors.NewBusinessError(pkgerrors.CODE_PARAMS_INVALID, fmt.Sprintf("指定的父节点 (ID: %v) 可能不存在或无效", node.ParentID)).WithCause(err)
		}
		log.WithError(err).Error("更新组织节点失败", logger.WithField("nodeID", node.ID))
		return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "更新组织节点失败").WithCause(err)
	}

	companyChanged := (node.CompanyID == nil && originalCompanyID != nil) ||
		(node.CompanyID != nil && originalCompanyID == nil) ||
		(node.CompanyID != nil && originalCompanyID != nil && *node.CompanyID != *originalCompanyID)

	needsRecursiveUpdate := false
	if parentChanged || companyChanged {
		needsRecursiveUpdate = true
		log.Info("节点 ParentID 或 CompanyID 变更，需要递归更新子孙",
			logger.WithField("nodeID", node.ID),
			logger.WithField("newParentID", node.ParentID), logger.WithField("originalParentID", originalParentID),
			logger.WithField("newCompanyID", node.CompanyID), logger.WithField("originalCompanyID", originalCompanyID),
			logger.WithField("newLevelForNode", node.Level),
		)
	}

	if needsRecursiveUpdate && tx.Dialector.Name() == "postgres" {
		log.Info("父节点或CompanyID变更，触发子孙节点递归更新 (PostgreSQL)", logger.WithField("nodeID", node.ID), logger.WithField("newBaseLevel", node.Level), logger.WithField("newCompanyID", node.CompanyID))
		// 假设 hr_update_org_node_descendants_level_company_id 函数能处理 newCompanyID 为 NULL (如果 node.CompanyID 是 nil)
		// 如果数据库函数期望 company_id 不为 null，则需要传递一个特殊值或调整函数
		var companyIDParam interface{}
		if node.CompanyID != nil {
			companyIDParam = *node.CompanyID
		} else {
			companyIDParam = gorm.Expr("NULL") // 或者直接传 nil，看驱动如何处理
		}
		sql := "SELECT hr_update_org_node_descendants_level_company_id(?, ?, ?);"
		if err := tx.Exec(sql, node.ID, node.Level, companyIDParam).Error; err != nil {
			log.WithError(err).Error("执行 hr_update_org_node_descendants_level_company_id 函数失败",
				logger.WithField("targetNodeID", node.ID),
				logger.WithField("newLevel", node.Level),
				logger.WithField("newCompanyID", node.CompanyID),
			)
			return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "递归更新组织节点子孙信息失败 (PostgreSQL)").WithCause(err)
		}
		log.Info("成功调用 hr_update_org_node_descendants_level_company_id 函数", logger.WithField("nodeID", node.ID))
	} else if needsRecursiveUpdate {
		log.Warn("需要递归更新组织节点子孙，使用 GORM 回退逻辑。", logger.WithField("nodeID", node.ID), logger.WithField("newBaseLevel", node.Level), logger.WithField("newCompanyID", node.CompanyID))
		if err := r.updateDescendantsInGORM(ctx, tx, node.ID, node.Level, node.CompanyID); err != nil { // newCompanyID 是 *uint
			log.WithError(err).Error("GORM 递归更新组织节点子孙信息失败",
				logger.WithField("targetNodeID", node.ID),
				logger.WithField("newBaseLevel", node.Level),
				logger.WithField("newCompanyID", node.CompanyID),
			)
			return err
		}
		log.Info("GORM 递归更新组织节点子孙信息成功", logger.WithField("nodeID", node.ID))
	}

	log.Info("组织节点更新成功", logger.WithField("nodeID", node.ID))
	return nil
}

// UpdateCompanyIDForNodeInTx 在事务中为指定节点更新 CompanyID
func (r *organizationNodeRepository) UpdateCompanyIDForNodeInTx(ctx context.Context, tx *gorm.DB, nodeID uint, companyID *uint) error {
	log := logger.WithContext(ctx)
	if nodeID == 0 {
		return pkgerrors.NewParamError(constant.INVALID_PARAM, "更新节点CompanyID时缺少节点ID")
	}

	updateData := map[string]interface{}{
		// "company_id" 会被 gorm.Save(node) 处理，或者显式更新
		// 如果 companyID 是 nil，则数据库列应该被更新为 NULL
		"company_id": companyID, // GORM应该能处理 *uint，如果为nil则更新为NULL
		"updated_at": gorm.Expr("CURRENT_TIMESTAMP"),
	}

	result := tx.Model(&entity.OrganizationNode{}).Where("id = ?", nodeID).Updates(updateData)
	if result.Error != nil {
		log.WithError(result.Error).Error("更新节点 CompanyID 失败", logger.WithField("nodeID", nodeID), logger.WithField("companyID", companyID))
		return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "更新节点CompanyID失败").WithCause(result.Error)
	}
	// RowsAffected 检查可能不那么重要了，因为 nil 也是一个有效更新
	log.Info("节点 CompanyID 更新成功", logger.WithField("nodeID", nodeID), logger.WithField("companyID", companyID))
	return nil
}

// GetTree 获取指定根节点下的所有子孙节点，用于构建树
func (r *organizationNodeRepository) GetTree(ctx context.Context, rootParentID *uint) ([]*entity.OrganizationNode, error) {
	var allNodes []*entity.OrganizationNode
	db := r.GetDB(ctx)

	if rootParentID != nil && *rootParentID > 0 { // 明确指定一个根节点
		var rootNode entity.OrganizationNode
		if err := db.First(&rootNode, *rootParentID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, pkgerrors.ErrDataNotFound
			}
			return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "获取根节点失败").WithCause(err)
		}
		// 获取该公司下的所有节点。如果 rootNode.CompanyID 是 nil，这个查询可能需要调整
		if rootNode.CompanyID != nil { // 只在 CompanyID 存在时按其过滤
			if err := db.Where("company_id = ?", *rootNode.CompanyID).Order("level asc, order_num asc").Find(&allNodes).Error; err != nil {
				return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "获取组织树节点失败").WithCause(err)
			}
		} else {
			// 如果根节点没有CompanyID（例如，它本身是一个尚未完全初始化的顶级公司，或者是一个不属于任何公司的孤立树？）
			// 这种情况下的行为需要明确。是只获取这个根节点，还是获取其parent_id链下的节点？
			// 为了安全，如果根节点没有 CompanyID，我们可能只返回根节点及其直接子孙（或按 parent_id 递归）
			// 这里简化为：如果根节点没有 company_id，我们只获取所有以它为直接或间接父的节点
			// 这需要递归查询，或获取所有节点后在service层构建。
			// 暂时保持原逻辑，但标记这里如果 rootNode.CompanyID is nil，行为可能不符合预期。
			// 或者，我们可以获取所有节点，然后在service层构建以 rootParentID 为根的子树。
			// 当前：如果rootNode.CompanyID为nil，则不会通过company_id过滤，下面获取所有节点的逻辑会执行。
			// 这其实是不对的，应该是获取以 rootParentID 为根的子树，不应跳到获取所有节点。
			// 更正：如果指定了 rootParentID，就应该获取其子树。
			// 一个简单的方法是获取所有节点，然后在service层构建。
			// 或者使用递归CTE（如果数据库支持）
			// 这里先用获取所有节点，由service层处理子树构建
			if err := db.Order("level asc, order_num asc").Find(&allNodes).Error; err != nil { // 获取所有节点，让service层过滤
				return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "获取所有组织节点失败 (GetTree fallback)").WithCause(err)
			}
		}
	} else { // rootParentID is nil, 获取所有顶级节点及其子孙 (即所有树)
		if err := db.Order("level asc, order_num asc").Find(&allNodes).Error; err != nil {
			return nil, pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "获取所有组织节点失败").WithCause(err)
		}
	}

	return allNodes, nil
}

// updateDescendantsInGORM 递归更新子孙节点的 Level 和 CompanyID
func (r *organizationNodeRepository) updateDescendantsInGORM(ctx context.Context, tx *gorm.DB, parentNodeID uint, parentNewLevel int, newCompanyID *uint) error {
	// parentNodeID 是 uint 因为它是现有节点的ID。
	// newCompanyID 是 *uint 因为新的公司ID可能是nil（虽然业务上可能不允许，但类型上支持）

	// 查找直接子节点。findDirectChildrenInTx 应该接收 uint parentID
	children, err := r.findDirectChildrenInTx(tx, parentNodeID)
	if err != nil {
		logger.WithContext(ctx).WithError(err).Error("查找子节点失败（递归更新中）")
		return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "查找子节点失败").WithCause(err)
	}

	for _, child := range children {
		newChildLevel := parentNewLevel + 1
		updateFields := map[string]interface{}{
			"level":      newChildLevel,
			"company_id": newCompanyID, // GORM 会将 nil 转为 NULL
		}

		if err := tx.Model(&entity.OrganizationNode{}).Where("id = ?", child.ID).Updates(updateFields).Error; err != nil {
			logger.WithContext(ctx).WithError(err).WithField("childID", child.ID).Error("递归更新子节点 Level 和 CompanyID 失败")
			return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "递归更新子节点失败").WithCause(err)
		}

		if err := r.updateDescendantsInGORM(ctx, tx, child.ID, newChildLevel, newCompanyID); err != nil {
			return err
		}
	}
	return nil
}

// findDirectChildrenInTx 是一个辅助方法，在事务中查找直接子节点
// 保持 parentID 为 uint，因为它总是查找现有父节点的子节点
func (r *organizationNodeRepository) findDirectChildrenInTx(tx *gorm.DB, parentID uint) ([]*entity.OrganizationNode, error) {
	var children []*entity.OrganizationNode
	// 如果顶级节点 parent_id 为 NULL，那么父节点为0的节点不应该被此方法找到
	// 此方法是给 updateDescendantsInGORM 用的，它传入的 parentID 是一个已存在的节点 ID
	if err := tx.Where("parent_id = ?", parentID).Find(&children).Error; err != nil {
		return nil, err
	}
	return children, nil
}

// UpdateStandardHeadcountInTx 在事务中更新指定节点的标配人数
func (r *organizationNodeRepository) UpdateStandardHeadcountInTx(ctx context.Context, tx *gorm.DB, nodeID uint, headcount int) error {
	log := logger.WithContext(ctx)
	uid64, err := util.GetUserIDFromStdContext(ctx) // Ensure util is imported
	if err != nil {
		log.WithError(err).Warn("UpdateStandardHeadcountInTx: 无法从上下文中获取UserID，UpdatedBy将不会被设置")
		// Proceeding without UpdatedBy, but logged a warning.
	}

	updates := map[string]interface{}{
		"standard_headcount": headcount,
	}
	if uid64 > 0 {
		updates["updated_by"] = uint(uid64)
		log.Infof("UpdateStandardHeadcountInTx: Setting updated_by to %d for node ID %d", uint(uid64), nodeID)
	} else {
		log.Warnf("UpdateStandardHeadcountInTx: updated_by will not be set for node ID %d as UserID could not be retrieved or was invalid (0).", nodeID)
	}

	result := tx.Model(&entity.OrganizationNode{}).Where("id = ?", nodeID).Updates(updates)
	if result.Error != nil {
		log.WithError(result.Error).Error("更新节点标配人数失败", logger.WithField("nodeID", nodeID))
		return pkgerrors.NewDataError(pkgerrors.CODE_DATA_UPDATE_FAILED, fmt.Sprintf("更新节点标配人数失败, id=%d", nodeID)).WithCause(result.Error)
	}
	// GORM's Updates method with a map might return RowsAffected = 0 if the map values match existing db values.
	// If RowsAffected is 0, we might want to check if the record actually exists.
	if result.RowsAffected == 0 {
		var count int64
		checkErr := tx.Model(&entity.OrganizationNode{}).Where("id = ?", nodeID).Count(&count).Error
		if checkErr != nil {
			log.WithError(checkErr).Error("UpdateStandardHeadcountInTx: 检查节点是否存在失败")
			return pkgerrors.NewDataError(pkgerrors.CODE_DATA_QUERY_FAILED, "检查节点是否存在失败").WithCause(checkErr)
		}
		if count == 0 {
			return pkgerrors.NewDataError(pkgerrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("组织节点记录未找到, id=%d", nodeID))
		}
		log.Warnf("UpdateStandardHeadcountInTx: RowsAffected is 0 for node ID %d. Record exists, so values might not have changed.", nodeID)
		// Consider it a success if the record exists, as the state is either already correct or GORM optimized the update.
	}
	return nil
}

// DeleteInTx 在事务中删除组织节点
func (r *organizationNodeRepository) DeleteInTx(ctx context.Context, tx *gorm.DB, id uint) error {
	log := logger.WithContext(ctx)
	var node entity.OrganizationNode
	if err := tx.First(&node, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn("尝试在事务中删除的节点不存在", logger.WithField("id", id))
			return pkgerrors.NewDataError(pkgerrors.CODE_DATA_NOT_FOUND, fmt.Sprintf("尝试删除的组织节点 [ID=%d] 未找到", id))
		}
		log.WithError(err).Error("删除前检查节点是否存在失败", logger.WithField("id", id))
		return pkgerrors.NewSystemError(pkgerrors.CODE_SYSTEM_ERROR, "删除组织节点前检查失败").WithCause(err)
	}

	result := tx.Delete(&entity.OrganizationNode{}, id)
	if result.Error != nil {
		log.WithError(result.Error).Error("在事务中删除组织节点失败", logger.WithField("id", id))
		return pkgerrors.NewSystemError(pkgerrors.CODE_DATA_DELETE_FAILED, "删除组织节点失败").WithCause(result.Error)
	}
	log.Info("在事务中成功发起删除组织节点操作", logger.WithField("id", id), logger.WithField("rowsAffected", result.RowsAffected))
	return nil
}

// --- 确保实现 OrganizationNodeRepository 接口 ---
var _ OrganizationNodeRepository = (*organizationNodeRepository)(nil)
