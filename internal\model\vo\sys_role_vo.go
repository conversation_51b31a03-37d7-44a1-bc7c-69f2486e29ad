package vo

// RoleVO 角色视图对象
// 用于向前端展示角色信息
type RoleVO struct {
	TenantVO
	Name        string    `json:"name"`        // 角色名称：角色的显示名称，如"系统管理员"
	Code        string    `json:"code"`        // 角色编码：角色的唯一标识符，如"ADMIN"，用于程序中引用角色
	Status      int       `json:"status"`      // 状态：角色状态（0-禁用，1-正常）
	Sort        int       `json:"sort"`        // 排序：角色的显示顺序，数值越小越靠前
	DataScope   int       `json:"dataScope"`   // 数据权限范围：控制角色可访问的数据范围（1-全部，2-自定义，3-本部门，4-本部门及以下，5-仅本人）
	Remark      string    `json:"remark"`      // 备注：关于角色的附加说明信息
	IsSystem    bool      `json:"isSystem"`    // 是否是系统角色：标识角色是否为系统内置角色
	IsDefault   bool      `json:"isDefault"`   // 是否是默认角色：标识角色是否为新用户默认分配的角色
	Description string    `json:"description"` // 描述：角色的详细描述信息
	MenuIDs     []uint    `json:"menuIds"`     // 菜单ID列表：角色关联的菜单ID集合
	Menus       []*MenuVO `json:"menus"`       // 菜单列表：角色关联的菜单列表
	UserCount   int       `json:"userCount"`   // 用户数量：拥有该角色的用户数量
}

// RoleSimpleVO 角色简要信息视图对象
// 用于列表展示或下拉选择等场景
// 只包含角色的基本信息
type RoleSimpleVO struct {
	ID     uint   `json:"id"`     // ID：角色的唯一标识
	Name   string `json:"name"`   // 角色名称：角色的显示名称
	Code   string `json:"code"`   // 角色编码：角色的唯一标识符
	Status int    `json:"status"` // 状态：角色状态（0-禁用，1-正常）
}

// RoleMenuVO 角色菜单视图对象
// 用于展示角色的菜单权限
type RoleMenuVO struct {
	ID      uint   `json:"id"`      // ID：角色的唯一标识
	Name    string `json:"name"`    // 角色名称：角色的显示名称
	Code    string `json:"code"`    // 角色编码：角色的唯一标识符
	MenuIDs []uint `json:"menuIds"` // 菜单ID列表：角色关联的菜单ID集合
}

// RoleUserVO 角色用户视图对象
// 用于展示角色下的用户列表
type RoleUserVO struct {
	ID    uint            `json:"id"`    // ID：角色的唯一标识
	Name  string          `json:"name"`  // 角色名称：角色的显示名称
	Code  string          `json:"code"`  // 角色编码：角色的唯一标识符
	Users []*UserSimpleVO `json:"users"` // 用户列表：拥有该角色的用户列表
}

// RolePermissionVO 角色权限视图对象
// 用于展示角色的权限标识列表
type RolePermissionVO struct {
	ID          uint     `json:"id"`          // ID：角色的唯一标识
	Name        string   `json:"name"`        // 角色名称：角色的显示名称
	Code        string   `json:"code"`        // 角色编码：角色的唯一标识符
	Permissions []string `json:"permissions"` // 权限标识列表：角色拥有的权限标识集合
}
