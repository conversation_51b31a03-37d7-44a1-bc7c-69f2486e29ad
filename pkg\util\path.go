package util

import "strings"

// PathMatch 检查给定路径是否与模式匹配。
// 模式可以包含一个在末尾的通配符 '*'，例如 "/api/*" 或 "/assets/*any"。
// 此函数旨在处理审计中间件的 ExcludedPaths 中的各种模式。
//
// 示例：
// PathMatch("/api/users", "/api/*") -> true
// PathMatch("/api/", "/api/*")      -> true
// PathMatch("/api", "/api/*")        -> true
// PathMatch("/login", "/api/*")      -> false
// PathMatch("/", "/*")             -> true
func PathMatch(path, pattern string) bool {
	// 查找我们视为通配符序列的 "/*" 的位置。
	wildcardIndex := strings.Index(pattern, "/*")

	if wildcardIndex == -1 {
		// 没有 "/*" 通配符。视为精确匹配。
		// 为提高健壮性，我们忽略长度大于1的路径的尾部斜杠。
		p1 := path
		if len(p1) > 1 {
			p1 = strings.TrimRight(p1, "/")
		}
		p2 := pattern
		if len(p2) > 1 {
			p2 = strings.TrimRight(p2, "/")
		}
		return p1 == p2
	}

	basePattern := pattern[:wildcardIndex] // 例如，从 "/swagger/*any" 中得到 "/swagger"

	// 如果基本模式为空，意味着模式类似于 "/*something"，
	// 应该匹配任何以 "/" 开头的路径。
	if basePattern == "" {
		return strings.HasPrefix(path, "/")
	}

	// 检查路径是基本模式本身，还是一个子路径。
	// 例如，path="/swagger", basePattern="/swagger" -> 匹配
	// 例如，path="/swagger/", basePattern="/swagger" -> 匹配
	// 例如，path="/swagger/api", basePattern="/swagger" -> 匹配
	if path == basePattern {
		return true
	}
	if strings.HasPrefix(path, basePattern+"/") {
		return true
	}

	return false
}
