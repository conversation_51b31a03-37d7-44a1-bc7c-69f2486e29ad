<template>
  <el-menu
    :default-active="activeRoute"
    :router="true"
    :collapse="collapse"
    :unique-opened="uniqueOpened"
    :default-openeds="defaultOpeneds"
    :background-color="backgroundColor"
    :text-color="textColor"
    :active-text-color="activeTextColor"
    class="vn-sidebar"
  >
    <template v-for="item in items" :key="item.index || item.title">
      <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.index || item.title">
        <template #title>
          <el-icon v-if="item.icon">
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.title }}</span>
        </template>
        <!-- Recursive rendering for sub-menu items -->
        <VNSidebarItem v-for="child in item.children" :key="child.index || child.title" :item="child" />
      </el-sub-menu>
      <el-menu-item v-else :index="item.index || (item.to ? resolveRoute(item.to) : item.title)" :route="item.to">
        <el-icon v-if="item.icon">
          <component :is="item.icon" />
        </el-icon>
        <template #title>{{ item.title }}</template>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, withDefaults } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { RouteLocationRaw } from 'vue-router';
import { ElMenu, ElMenuItem, ElSubMenu, ElIcon } from 'element-plus';
import type { SidebarItem, VNSidebarProps } from './types';
import VNSidebarItem from './VNSidebarItem.vue'; // Uncommented to import the component

const props = withDefaults(defineProps<VNSidebarProps>(), {
  items: () => [], // Add default for items
  collapse: false,
  uniqueOpened: true,
  defaultOpeneds: () => [],
  backgroundColor: '#304156', 
  textColor: '#bfcbd9',
  activeTextColor: '#409EFF',
});

const route = useRoute();
const router = useRouter();
const activeRoute = ref('');

// Watch for route changes to update the active menu item
watch(() => route.path, (newPath) => {
  activeRoute.value = newPath;
}, { immediate: true });

// Helper function to resolve route path from RouteLocationRaw
// This is needed because el-menu-item index should be a string
const resolveRoute = (to: RouteLocationRaw): string => {
  try {
    const resolved = router.resolve(to);
    return resolved.path;
  } catch (e) {
    console.error('Error resolving route:', to, e);
    // Fallback or default index if resolution fails
    return typeof to === 'string' ? to : (to as any).path || '#'; 
  }
};

// Expose VNSidebarItem if using the recursive component approach
// defineExpose({ VNSidebarItem }); // This is usually not needed in <script setup> if only used in template

</script>

<style scoped>
.vn-sidebar:not(.el-menu--collapse) {
  width: 200px; /* Default width when not collapsed */
}

.vn-sidebar {
  border-right: none; /* Remove default border if needed */
  transition: width 0.28s ease-in-out; /* 添加平滑过渡效果 */
  height: 100%; /* <<< Add this line */
}

/* Ensure icons have proper alignment */
.el-icon {
  vertical-align: middle;
  margin-right: 5px;
}

/* Adjust padding/margin for collapsed state if needed */
.el-menu--collapse .el-sub-menu__title span,
.el-menu--collapse .el-menu-item span {
  display: none;
}

.el-menu--collapse .el-sub-menu__icon-arrow {
  display: none;
}
</style> 