# WMS 库存管理框架合规性修复执行计划

## 概述
基于框架合规性评估报告，本执行计划旨在全面重构WMS库存管理模块，使其符合项目框架标准，达到与入库模块相同的架构质量和代码规范。

## 问题总结

### 🔴 严重问题 (Critical)
1. **Service层完全禁用**: 所有库存管理Service返回nil，功能不可用
2. **Repository层框架不合规**: 未继承BaseRepository接口
3. **Controller层大部分禁用**: 除移动模块外都是.bak文件
4. **Manager集成不完整**: 无法正常初始化和使用

### 🟡 一般问题 (Major)
1. **自定义查询构建器**: 未使用框架的QueryCondition系统
2. **事务管理不统一**: 未使用框架统一的事务管理
3. **错误处理不标准**: 未使用框架标准的错误处理机制

### 🟢 优化问题 (Minor)
1. **DTO命名不一致**: 存在两套命名体系
2. **VO转换逻辑分散**: 转换逻辑位置不统一

## 修复执行计划

### 阶段1: Repository层框架合规性重构 (优先级: 🔴 Critical)

#### 任务1.1: 重构WmsInventoryRepository
**预计时间**: 2天

**修改文件**: `internal/repository/wms_inventory_repository.go`

**重构内容**:
```go
// 新的Repository接口定义
type WmsInventoryRepository interface {
    BaseRepository[entity.WmsInventory, uint]
    
    // 业务特定方法
    FindByLocationID(ctx context.Context, locationId uint) ([]*entity.WmsInventory, error)
    FindByItemID(ctx context.Context, itemId uint) ([]*entity.WmsInventory, error)
    FindAvailableByItem(ctx context.Context, itemId uint, warehouseId *uint, requiredQty float64) ([]*entity.WmsInventory, error)
    // ... 其他业务方法
}

// 新的Repository实现
type wmsInventoryRepository struct {
    BaseRepositoryImpl[entity.WmsInventory, uint]
}

func NewWmsInventoryRepository(db *gorm.DB) WmsInventoryRepository {
    return &wmsInventoryRepository{
        BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsInventory, uint]{
            db: db,
        },
    }
}
```

**使用QueryCondition系统**:
```go
func (r *wmsInventoryRepository) GetPage(ctx context.Context, query *dto.WmsInventoryQueryReq) (*response.PageResult, error) {
    conditions := []QueryCondition{}
    
    if query.WarehouseID != nil {
        conditions = append(conditions, NewEqualCondition("warehouse_id", *query.WarehouseID))
    }
    if query.ItemSku != nil && *query.ItemSku != "" {
        conditions = append(conditions, NewLikeCondition("item.sku", *query.ItemSku))
    }
    // ... 其他条件
    
    return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}
```

#### 任务1.2: 重构其他Repository
**预计时间**: 3天

**修改文件**:
- `internal/repository/wms_inventory_adjustment_repository.go`
- `internal/repository/wms_inventory_movement_repository.go`
- `internal/repository/wms_inventory_alert_rule_repository.go`
- `internal/repository/wms_inventory_alert_log_repository.go`

**统一重构模式**: 所有Repository都按照任务1.1的模式进行重构。

### 阶段2: Service层框架合规性重构 (优先级: 🔴 Critical)

#### 任务2.1: 重构WmsInventoryQueryService
**预计时间**: 3天

**修改文件**: `internal/service/wms_inventory_query_service.go` (移除.bak)

**重构内容**:
```go
type WmsInventoryQueryService interface {
    BaseService
    // 业务方法...
}

type wmsInventoryQueryServiceImpl struct {
    BaseServiceImpl
}

func NewWmsInventoryQueryService(sm *ServiceManager) WmsInventoryQueryService {
    return &wmsInventoryQueryServiceImpl{
        BaseServiceImpl: *NewBaseService(sm),
    }
}
```

**使用框架事务管理**:
```go
func (s *wmsInventoryQueryServiceImpl) GetInventoryPage(ctx context.Context, req *dto.WmsInventoryQueryReq) (*vo.WmsInventoryPageVO, error) {
    // 获取账套ID
    accountBookID, err := s.GetAccountBookIDFromContext(ctx)
    if err != nil {
        return nil, err
    }
    
    repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()
    
    // 使用Repository的分页查询
    result, err := repo.GetPage(ctx, req)
    if err != nil {
        return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "查询库存失败").WithCause(err)
    }
    
    return s.convertToPageVO(result), nil
}
```

#### 任务2.2: 重构其他Service
**预计时间**: 4天

**修改文件**:
- `internal/service/wms_inventory_adjustment_service.go` (移除.bak)
- `internal/service/wms_inventory_alert_service.go` (移除.bak)
- `internal/service/wms_cycle_count_service.go` (移除.bak)

**统一重构模式**: 所有Service都按照任务2.1的模式进行重构。

#### 任务2.3: 修复ServiceManager集成
**预计时间**: 1天

**修改文件**: `internal/service/service_manager.go`

**修复内容**:
```go
// 修复库存查询服务
func (m *ServiceManager) GetWmsInventoryQueryService() WmsInventoryQueryService {
    return GetService(m, func(sm *ServiceManager) WmsInventoryQueryService {
        return NewWmsInventoryQueryService(sm)
    })
}

// 修复库存调整服务
func (m *ServiceManager) GetWmsInventoryAdjustmentService() WmsInventoryAdjustmentService {
    return GetService(m, func(sm *ServiceManager) WmsInventoryAdjustmentService {
        return NewWmsInventoryAdjustmentService(sm)
    })
}

// 修复盘点管理服务
func (m *ServiceManager) GetWmsCycleCountService() WmsCycleCountService {
    return GetService(m, func(sm *ServiceManager) WmsCycleCountService {
        return NewWmsCycleCountService(sm)
    })
}

// 修复库存预警服务
func (m *ServiceManager) GetWmsInventoryAlertService() WmsInventoryAlertService {
    return GetService(m, func(sm *ServiceManager) WmsInventoryAlertService {
        return NewWmsInventoryAlertService(sm)
    })
}
```

### 阶段3: Controller层框架合规性重构 (优先级: 🔴 Critical)

#### 任务3.1: 重构WmsInventoryQueryController
**预计时间**: 2天

**修改文件**: `internal/controller/wms_inventory_query_controller.go` (移除.bak)

**重构内容**:
```go
type WmsInventoryQueryController interface {
    GetInventoryPage(ctx iris.Context)
    GetInventoryDetail(ctx iris.Context)
    // ... 其他方法
}

type wmsInventoryQueryControllerImpl struct {
    BaseControllerImpl
    wmsInventoryQueryService service.WmsInventoryQueryService
}

func NewWmsInventoryQueryControllerImpl(cm *ControllerManager) *wmsInventoryQueryControllerImpl {
    return &wmsInventoryQueryControllerImpl{
        BaseControllerImpl:       *NewBaseController(cm),
        wmsInventoryQueryService: cm.GetServiceManager().GetWmsInventoryQueryService(),
    }
}
```

**使用框架错误处理**:
```go
func (ctrl *wmsInventoryQueryControllerImpl) GetInventoryPage(ctx iris.Context) {
    const opName = "GetInventoryPage"
    ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_inventory")
    
    var req dto.WmsInventoryQueryReq
    if err := ctx.ReadJSON(&req); err != nil {
        paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
        ctrl.HandleError(ctx, paramErr, opName)
        return
    }
    
    result, err := ctrl.wmsInventoryQueryService.GetInventoryPage(ctx.Request().Context(), &req)
    if err != nil {
        ctrl.HandleError(ctx, err, opName)
        return
    }
    ctrl.Success(ctx, result)
}
```

#### 任务3.2: 重构其他Controller
**预计时间**: 3天

**修改文件**:
- `internal/controller/wms_inventory_adjustment_controller.go` (移除.bak)
- `internal/controller/wms_inventory_alert_controller.go` (移除.bak)
- `internal/controller/wms_cycle_count_controller.go` (移除.bak)
- `internal/controller/wms_inventory_movement_controller.go` (重构现有文件)

#### 任务3.3: 添加Controller到ControllerManager
**预计时间**: 0.5天

**修改文件**: `internal/controller/controller_manager.go`

**添加内容**:
```go
// ==================== 库存管理模块 Controller 获取方法 ====================

// GetWmsInventoryQueryController 获取库存查询控制器
func (m *ControllerManager) GetWmsInventoryQueryController() *wmsInventoryQueryControllerImpl {
    return GetController(m, NewWmsInventoryQueryControllerImpl)
}

// GetWmsInventoryAdjustmentController 获取库存调整控制器
func (m *ControllerManager) GetWmsInventoryAdjustmentController() *wmsInventoryAdjustmentControllerImpl {
    return GetController(m, NewWmsInventoryAdjustmentControllerImpl)
}

// GetWmsCycleCountController 获取盘点管理控制器
func (m *ControllerManager) GetWmsCycleCountController() *wmsCycleCountControllerImpl {
    return GetController(m, NewWmsCycleCountControllerImpl)
}

// GetWmsInventoryAlertController 获取库存预警控制器
func (m *ControllerManager) GetWmsInventoryAlertController() *wmsInventoryAlertControllerImpl {
    return GetController(m, NewWmsInventoryAlertControllerImpl)
}

// GetWmsInventoryMovementController 获取库存移动控制器
func (m *ControllerManager) GetWmsInventoryMovementController() *wmsInventoryMovementControllerImpl {
    return GetController(m, NewWmsInventoryMovementControllerImpl)
}
```

#### 任务3.4: 修复main.go中的Controller初始化
**预计时间**: 0.5天

**修改文件**: `main.go`

**修复内容**:
```go
// 替换nil值初始化
wmsInventoryQueryController := cm.GetWmsInventoryQueryController()
wmsInventoryAdjustmentController := cm.GetWmsInventoryAdjustmentController()
wmsCycleCountController := cm.GetWmsCycleCountController()
wmsInventoryAlertController := cm.GetWmsInventoryAlertController()
wmsInventoryMovementController := cm.GetWmsInventoryMovementController()
```

### 阶段4: 路由和集成测试 (优先级: 🟡 Major)

#### 任务4.1: 修复路由注册
**预计时间**: 1天

**修改文件**: `internal/router/router.go`

移除之前添加的nil检查，确保路由正常注册。

#### 任务4.2: 集成测试
**预计时间**: 2天

- 测试所有CRUD操作
- 测试分页查询功能
- 测试业务流程
- 测试错误处理

### 阶段5: 优化和完善 (优先级: 🟢 Minor)

#### 任务5.1: DTO命名统一
**预计时间**: 1天

统一DTO命名规范，移除旧版本的DTO定义。

#### 任务5.2: VO转换逻辑优化
**预计时间**: 1天

将VO转换逻辑统一到service层，简化VO文件。

## 详细实施步骤

### 步骤1: Repository层重构 (第1-5天)

1. **分析现有Repository**: 理解当前实现的业务逻辑
2. **设计新的接口**: 按照BaseRepository标准设计接口
3. **实现新的Repository**: 使用QueryCondition系统
4. **测试Repository功能**: 确保数据访问正常

### 步骤2: Service层重构 (第6-12天)

1. **分析现有Service**: 理解业务逻辑和流程
2. **设计新的Service接口**: 继承BaseService
3. **实现Service逻辑**: 使用框架标准
4. **修复ServiceManager**: 移除nil返回
5. **测试Service功能**: 确保业务逻辑正确

### 步骤3: Controller层重构 (第13-18天)

1. **设计Controller接口**: 继承BaseController
2. **实现Controller逻辑**: 使用框架错误处理
3. **注册到ControllerManager**: 添加getter方法
4. **修复main.go**: 正确初始化Controller
5. **测试API接口**: 确保接口正常工作

### 步骤4: 集成测试和优化 (第19-21天)

1. **端到端测试**: 测试完整业务流程
2. **性能测试**: 确保性能符合要求
3. **代码优化**: 优化代码结构和逻辑
4. **文档更新**: 更新相关文档

## 验收标准

### 功能验收
- [ ] 所有库存管理CRUD操作正常
- [ ] 库存查询、调整、移动、预警功能正常
- [ ] 盘点管理功能正常
- [ ] 分页查询功能正常
- [ ] 批量操作功能正常
- [ ] 错误处理和验证正常

### 框架合规性验收
- [ ] 所有Repository正确继承BaseRepository
- [ ] 所有Service正确继承BaseService
- [ ] 所有Controller正确继承BaseController
- [ ] 正确集成到各个Manager中
- [ ] 使用框架统一的事务管理
- [ ] 使用框架统一的错误处理
- [ ] 审计日志正常记录
- [ ] 使用框架的QueryCondition系统

### 性能验收
- [ ] 查询性能符合要求
- [ ] 并发处理能力正常
- [ ] 内存使用合理

## 时间估算

- **阶段1 (Repository重构)**: 5天
- **阶段2 (Service重构)**: 7天
- **阶段3 (Controller重构)**: 6天
- **阶段4 (集成测试)**: 3天

**总计**: 21天

## 风险评估与应对

### 高风险
- **业务逻辑复杂**: 
  - 风险: 库存管理业务逻辑复杂，重构可能遗漏功能
  - 应对: 详细分析现有代码，制定完整的功能清单

- **数据一致性**: 
  - 风险: Repository重构可能影响数据访问
  - 应对: 充分测试，确保数据访问逻辑正确

### 中风险
- **性能影响**: 
  - 风险: 框架标准化可能带来性能开销
  - 应对: 进行性能测试，优化关键路径

- **集成复杂性**: 
  - 风险: 多个模块同时重构，集成复杂
  - 应对: 分阶段实施，逐步集成测试

### 低风险
- **接口兼容性**: 
  - 风险: API接口变化可能影响前端
  - 应对: 保持接口兼容，逐步迁移

## 回滚计划

1. **代码回滚**: 
   - 保留.bak文件作为回滚基准
   - 使用Git分支管理，确保可以快速回滚

2. **配置回滚**: 
   - 恢复原有的nil值配置
   - 准备配置文件备份

3. **数据库回滚**: 
   - 确保数据库结构不变
   - 准备数据回滚脚本

## 后续优化建议

1. **代码生成工具**: 考虑使用代码生成工具自动生成符合框架标准的代码
2. **单元测试**: 完善单元测试覆盖率
3. **性能监控**: 建立性能监控机制
4. **文档完善**: 更新开发文档和API文档
5. **培训计划**: 对开发团队进行框架标准培训
