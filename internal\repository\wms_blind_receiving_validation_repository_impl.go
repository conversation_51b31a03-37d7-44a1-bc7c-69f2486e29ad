package repository

import (
	"context"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsBlindReceivingValidationRepository 盲收验证记录仓库接口
type WmsBlindReceivingValidationRepository interface {
	BaseRepository[entity.WmsBlindReceivingValidation, uint]

	// GetByID a convenience method, often just calling FindByID.
	// The service layer seems to expect this specific name.
	GetByID(ctx context.Context, id uint) (*entity.WmsBlindReceivingValidation, error)

	// GetByConfigID 根据配置ID获取最新的验证记录
	GetByConfigID(ctx context.Context, configID uint, limit int) ([]*entity.WmsBlindReceivingValidation, error)

	// GetByWarehouseAndCustomer 根据仓库和客户获取最新的验证记录
	GetByWarehouseAndCustomer(ctx context.Context, warehouseID uint, clientID uint, limit int) ([]*entity.WmsBlindReceivingValidation, error)

	// GetPendingValidations 获取待处理的验证记录 (待审批或待补录)
	GetPendingValidations(ctx context.Context, warehouseID uint) ([]*entity.WmsBlindReceivingValidation, error)

	// GetValidationHistory 获取指定天数内的验证历史
	GetValidationHistory(ctx context.Context, warehouseID uint, clientID *uint, days int) ([]*entity.WmsBlindReceivingValidation, error)

	// GetValidationStats 获取验证统计信息
	GetValidationStats(ctx context.Context, warehouseID uint, startTime, endTime time.Time) (map[string]interface{}, error)
}

// wmsBlindReceivingValidationRepository 盲收验证记录仓库实现
type wmsBlindReceivingValidationRepository struct {
	BaseRepositoryImpl[entity.WmsBlindReceivingValidation, uint]
}

// NewWmsBlindReceivingValidationRepository 创建仓库实例
func NewWmsBlindReceivingValidationRepository(db *gorm.DB) WmsBlindReceivingValidationRepository {
	return &wmsBlindReceivingValidationRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsBlindReceivingValidation, uint]{
			db: db,
		},
	}
}

// GetByID a simple wrapper around FindByID to match service layer expectations.
func (r *wmsBlindReceivingValidationRepository) GetByID(ctx context.Context, id uint) (*entity.WmsBlindReceivingValidation, error) {
	return r.FindByID(ctx, id)
}

// GetByConfigID 根据配置ID获取验证记录
func (r *wmsBlindReceivingValidationRepository) GetByConfigID(ctx context.Context, configID uint, limit int) ([]*entity.WmsBlindReceivingValidation, error) {
	var results []*entity.WmsBlindReceivingValidation
	db := r.GetDB(ctx).Model(&entity.WmsBlindReceivingValidation{}).
		Where("config_id = ?", configID).
		Order("created_at DESC")

	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&results).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("根据配置ID查询盲收验证记录失败: configID=%d", configID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询数据失败").WithCause(err)
	}
	return results, nil
}

// GetByWarehouseAndCustomer 根据仓库和客户获取验证记录
func (r *wmsBlindReceivingValidationRepository) GetByWarehouseAndCustomer(ctx context.Context, warehouseID uint, clientID uint, limit int) ([]*entity.WmsBlindReceivingValidation, error) {
	var results []*entity.WmsBlindReceivingValidation
	db := r.GetDB(ctx).Model(&entity.WmsBlindReceivingValidation{}).
		Where("warehouse_id = ? AND client_id = ?", warehouseID, clientID).
		Order("created_at DESC")

	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&results).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("根据仓库和客户查询盲收验证记录失败: warehouseID=%d, clientID=%d", warehouseID, clientID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询数据失败").WithCause(err)
	}
	return results, nil
}

// GetPendingValidations 获取待处理的验证记录
func (r *wmsBlindReceivingValidationRepository) GetPendingValidations(ctx context.Context, warehouseID uint) ([]*entity.WmsBlindReceivingValidation, error) {
	conditions := []QueryCondition{
		NewEqualCondition("warehouse_id", warehouseID),
		NewExprCondition("(approval_status = ? OR supplement_status = ?)", "PENDING", "PENDING"),
	}
	sortInfos := []response.SortInfo{{Field: "created_at", Order: "asc"}}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetValidationHistory 获取验证历史
func (r *wmsBlindReceivingValidationRepository) GetValidationHistory(ctx context.Context, warehouseID uint, clientID *uint, days int) ([]*entity.WmsBlindReceivingValidation, error) {
	timeLimit := time.Now().AddDate(0, 0, -days)

	conditions := []QueryCondition{
		NewEqualCondition("warehouse_id", warehouseID),
		NewGreaterThanEqualCondition("created_at", timeLimit),
	}
	if clientID != nil {
		conditions = append(conditions, NewEqualCondition("client_id", *clientID))
	}

	sortInfos := []response.SortInfo{{Field: "created_at", Order: "desc"}}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetValidationStats 获取验证统计信息
func (r *wmsBlindReceivingValidationRepository) GetValidationStats(ctx context.Context, warehouseID uint, startTime, endTime time.Time) (map[string]interface{}, error) {
	var stats struct {
		TotalCount   int64   `json:"total_count"`
		SuccessCount int64   `json:"success_count"`
		FailureCount int64   `json:"failure_count"`
	}

	query := r.GetDB(ctx).Model(&entity.WmsBlindReceivingValidation{}).
		Where("warehouse_id = ?", warehouseID).
		Where("created_at BETWEEN ? AND ?", startTime, endTime)

	result := query.Select(`
		COUNT(*) as total_count,
		COUNT(CASE WHEN approval_status = 'APPROVED' THEN 1 END) as success_count,
		COUNT(CASE WHEN approval_status = 'REJECTED' THEN 1 END) as failure_count
	`).First(&stats)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Error("获取盲收验证统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取统计数据失败").WithCause(result.Error)
	}

	var successRate float64
	if stats.TotalCount > 0 {
		successRate = (float64(stats.SuccessCount) / float64(stats.TotalCount)) * 100
	}

	return map[string]interface{}{
		"total_count":   stats.TotalCount,
		"success_count": stats.SuccessCount,
		"failure_count": stats.FailureCount,
		"success_rate":  successRate,
	}, nil
}
