import request from '@/utils/request'; // 导入封装好的 axios 实例
// import type { UserLoginDTO } from '@/types/dto/user'; // 如果定义了请求体类型
// import type { CaptchaResponse, LoginResponse } from '@/types/api/auth'; // 如果定义了响应类型

// 定义登录请求的数据结构 (与后端 UserLoginDTO 对应)
// 注意字段名要与后端接收的一致 (经过 request.ts 处理，这里是 Go struct 字段名，
// 但通常我们在发送时使用 json 标签名，所以这里定义一个接口匹配 json 标签名更清晰)
export interface LoginCredentials {
  username: string;
  password?: string; // 密码通常需要
  captchaId: string;
  captcha: string;
  rememberMe: boolean;
}

// 定义获取验证码 API 的响应数据类型 (对应后端 CaptchaVO)
// 注意：request 拦截器配置为直接返回 data 部分
export interface CaptchaData {
  captchaId: string; // Changed from captcha_id
  imageData: string; // Changed from image_data
  enabled?: boolean;
}

// 定义登录 API 的响应数据类型 (对应后端 UserLoginVO)
export interface LoginData {
  token: string;
  expireTime: number;
  user: { // 对应后端 UserSimpleVO
    id: number;
    username: string;
    nickname: string;
    realName: string;
    avatar: string;
    status: number;
    isAdmin: boolean;
  };
}


/**
 * 获取验证码
 * @returns Promise<CaptchaData>
 */
export function getCaptcha() {
  // request.get<ExpectedResponseType>(url, config)
  // 由于响应拦截器会返回 res.data，这里的 ExpectedResponseType 应该是 res.data 的类型
  return request.get<CaptchaData>('/auth/captcha');
}

/**
 * 用户登录
 * @param data - 包含 username, password, captchaId, captcha 的对象
 * @returns Promise<LoginData>
 */
export function login(data: LoginCredentials) {
  // request.post<ExpectedResponseType>(url, data, config)
  return request.post<LoginData>('/auth/login', data);
}

/**
 * 用户登出 (示例)
 * @returns Promise<any>
 */
// export function logout() {
//   return request.post('/auth/logout');
// }

/**
 * 刷新 Token (示例)
 * @param refreshToken - 刷新令牌
 * @returns Promise<{ accessToken: string; expiresAt: number; }>
 */
// export function refreshToken(refreshToken: string) {
//   return request.post('/auth/refresh', { refreshToken });
// }
