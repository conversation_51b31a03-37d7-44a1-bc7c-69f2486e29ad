package service

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
)

// WmsInventoryTransactionLogService 库存事务日志服务接口
type WmsInventoryTransactionLogService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsInventoryTransactionLogCreateReq) (*vo.WmsInventoryTransactionLogVO, error)
	GetByID(ctx context.Context, id uint) (*vo.WmsInventoryTransactionLogVO, error)
	GetPage(ctx context.Context, req *dto.WmsInventoryTransactionLogQueryReq) (*vo.PageResult[vo.WmsInventoryTransactionLogVO], error)

	// 业务查询
	GetByInventoryID(ctx context.Context, inventoryId uint) ([]*vo.WmsInventoryTransactionLogVO, error)
	GetByTransactionType(ctx context.Context, transactionType entity.TransactionType) ([]*vo.WmsInventoryTransactionLogVO, error)
	GetByReferenceDoc(ctx context.Context, refDocType string, refDocID uint) ([]*vo.WmsInventoryTransactionLogVO, error)
	GetByOperator(ctx context.Context, operatorId uint) ([]*vo.WmsInventoryTransactionLogVO, error)

	// 统计查询
	GetSummaryByType(ctx context.Context, transactionType entity.TransactionType, startDate, endDate string) (*vo.WmsInventoryTransactionLogSummaryVO, error)
	GetOperatorStats(ctx context.Context, operatorId uint, startDate, endDate string) (*vo.WmsInventoryTransactionStatsVO, error)

	// 批量操作
	BatchCreate(ctx context.Context, logs []*dto.WmsInventoryTransactionLogCreateReq) ([]*vo.WmsInventoryTransactionLogVO, error)

	// 清理操作
	CleanupOldLogs(ctx context.Context, beforeDate string) error
}

// wmsInventoryTransactionLogServiceImpl 库存事务日志服务实现
type wmsInventoryTransactionLogServiceImpl struct {
	BaseServiceImpl
}

// NewWmsInventoryTransactionLogService 创建库存事务日志服务
func NewWmsInventoryTransactionLogService(sm *ServiceManager) WmsInventoryTransactionLogService {
	return &wmsInventoryTransactionLogServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建库存事务日志
func (s *wmsInventoryTransactionLogServiceImpl) Create(ctx context.Context, req *dto.WmsInventoryTransactionLogCreateReq) (*vo.WmsInventoryTransactionLogVO, error) {
	var result *vo.WmsInventoryTransactionLogVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryTransactionLogRepository()

		// 创建事务日志实体
		log := &entity.WmsInventoryTransactionLog{
			TransactionType:    req.TransactionType,
			ItemID:             req.ItemID,
			WarehouseID:        req.WarehouseID,
			LocationID:         req.LocationID,
			BatchNo:            req.BatchNo,
			QuantityChange:     req.QuantityChange,
			UnitOfMeasure:      req.UnitOfMeasure,
			BalanceBefore:      req.BalanceBefore,
			BalanceAfter:       req.BalanceAfter,
			ReferenceDocType:   req.ReferenceDocType,
			ReferenceDocID:     req.ReferenceDocID,
			ReferenceDocLineID: req.ReferenceDocLineID,
			OperatorID:         req.OperatorID,
			Remark:             req.Remark,
		}

		if err := repo.Create(ctx, log); err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "创建库存事务日志失败").WithCause(err)
		}

		// 转换为VO
		result = &vo.WmsInventoryTransactionLogVO{}
		if err := copier.Copy(result, log); err != nil {
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetByID 根据ID获取库存事务日志
func (s *wmsInventoryTransactionLogServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsInventoryTransactionLogVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryTransactionLogRepository()

	log, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "获取库存事务日志失败").WithCause(err)
	}
	if log == nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "库存事务日志不存在")
	}

	result := &vo.WmsInventoryTransactionLogVO{}
	if err := copier.Copy(result, log); err != nil {
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
	}

	return result, nil
}

// GetPage 分页查询库存事务日志
func (s *wmsInventoryTransactionLogServiceImpl) GetPage(ctx context.Context, req *dto.WmsInventoryTransactionLogQueryReq) (*vo.PageResult[vo.WmsInventoryTransactionLogVO], error) {
	// TODO: 实现分页查询逻辑
	// 这里需要根据实际业务需求实现分页查询
	result := &vo.PageResult[vo.WmsInventoryTransactionLogVO]{
		Total:    0,
		PageNum:  req.PageNum,
		PageSize: req.PageSize,
		List:     make([]*vo.WmsInventoryTransactionLogVO, 0),
	}

	return result, nil
}

// GetByInventoryID 根据库存ID获取事务日志
func (s *wmsInventoryTransactionLogServiceImpl) GetByInventoryID(ctx context.Context, inventoryId uint) ([]*vo.WmsInventoryTransactionLogVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryTransactionLogRepository()

	logs, err := repo.FindByInventoryID(ctx, inventoryId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "查询库存事务日志失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryTransactionLogVO, 0, len(logs))
	for _, log := range logs {
		vo := &vo.WmsInventoryTransactionLogVO{}
		if err := copier.Copy(vo, log); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, vo)
	}

	return result, nil
}

// GetByTransactionType 根据事务类型获取日志
func (s *wmsInventoryTransactionLogServiceImpl) GetByTransactionType(ctx context.Context, transactionType entity.TransactionType) ([]*vo.WmsInventoryTransactionLogVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryTransactionLogRepository()

	logs, err := repo.FindByTransactionType(ctx, transactionType)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "查询库存事务日志失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryTransactionLogVO, 0, len(logs))
	for _, log := range logs {
		vo := &vo.WmsInventoryTransactionLogVO{}
		if err := copier.Copy(vo, log); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, vo)
	}

	return result, nil
}

// GetByReferenceDoc 根据参考单据获取事务日志
func (s *wmsInventoryTransactionLogServiceImpl) GetByReferenceDoc(ctx context.Context, refDocType string, refDocID uint) ([]*vo.WmsInventoryTransactionLogVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryTransactionLogRepository()

	logs, err := repo.FindByReferenceDoc(ctx, refDocType, refDocID)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "查询库存事务日志失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryTransactionLogVO, 0, len(logs))
	for _, log := range logs {
		vo := &vo.WmsInventoryTransactionLogVO{}
		if err := copier.Copy(vo, log); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, vo)
	}

	return result, nil
}

// GetByOperator 根据操作员获取事务日志
func (s *wmsInventoryTransactionLogServiceImpl) GetByOperator(ctx context.Context, operatorId uint) ([]*vo.WmsInventoryTransactionLogVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryTransactionLogRepository()

	logs, err := repo.FindByOperatorID(ctx, operatorId)
	if err != nil {
		return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "查询库存事务日志失败").WithCause(err)
	}

	result := make([]*vo.WmsInventoryTransactionLogVO, 0, len(logs))
	for _, log := range logs {
		vo := &vo.WmsInventoryTransactionLogVO{}
		if err := copier.Copy(vo, log); err != nil {
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
		}
		result = append(result, vo)
	}

	return result, nil
}

// GetSummaryByType 根据事务类型获取汇总统计
func (s *wmsInventoryTransactionLogServiceImpl) GetSummaryByType(ctx context.Context, transactionType entity.TransactionType, startDate, endDate string) (*vo.WmsInventoryTransactionLogSummaryVO, error) {
	// TODO: 实现汇总统计逻辑
	// 这里需要根据实际业务需求实现统计逻辑
	return &vo.WmsInventoryTransactionLogSummaryVO{}, nil
}

// GetOperatorStats 获取操作员统计信息
func (s *wmsInventoryTransactionLogServiceImpl) GetOperatorStats(ctx context.Context, operatorId uint, startDate, endDate string) (*vo.WmsInventoryTransactionStatsVO, error) {
	// TODO: 实现操作员统计逻辑
	// 这里需要根据实际业务需求实现统计逻辑
	return &vo.WmsInventoryTransactionStatsVO{}, nil
}

// BatchCreate 批量创建库存事务日志
func (s *wmsInventoryTransactionLogServiceImpl) BatchCreate(ctx context.Context, reqs []*dto.WmsInventoryTransactionLogCreateReq) ([]*vo.WmsInventoryTransactionLogVO, error) {
	var result []*vo.WmsInventoryTransactionLogVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsInventoryTransactionLogRepository()

		// 转换为实体
		logs := make([]*entity.WmsInventoryTransactionLog, 0, len(reqs))
		for _, req := range reqs {
			log := &entity.WmsInventoryTransactionLog{
				TransactionType:    req.TransactionType,
				ItemID:             req.ItemID,
				WarehouseID:        req.WarehouseID,
				LocationID:         req.LocationID,
				BatchNo:            req.BatchNo,
				QuantityChange:     req.QuantityChange,
				UnitOfMeasure:      req.UnitOfMeasure,
				BalanceBefore:      req.BalanceBefore,
				BalanceAfter:       req.BalanceAfter,
				ReferenceDocType:   req.ReferenceDocType,
				ReferenceDocID:     req.ReferenceDocID,
				ReferenceDocLineID: req.ReferenceDocLineID,
				OperatorID:         req.OperatorID,
				Remark:             req.Remark,
			}
			logs = append(logs, log)
		}

		if err := repo.BatchCreate(ctx, logs); err != nil {
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "批量创建库存事务日志失败").WithCause(err)
		}

		// 转换为VO
		result = make([]*vo.WmsInventoryTransactionLogVO, 0, len(logs))
		for _, log := range logs {
			vo := &vo.WmsInventoryTransactionLogVO{}
			if err := copier.Copy(vo, log); err != nil {
				return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
			}
			result = append(result, vo)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// CleanupOldLogs 清理旧的事务日志
func (s *wmsInventoryTransactionLogServiceImpl) CleanupOldLogs(ctx context.Context, beforeDate string) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryTransactionLogRepository()

	// 解析日期字符串
	parsedDate, err := time.Parse("2006-01-02", beforeDate)
	if err != nil {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_FORMAT_ERROR, "日期格式错误").WithCause(err)
	}

	err = repo.CleanupOldLogs(ctx, parsedDate)
	if err != nil {
		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_GENERIC, "清理旧事务日志失败").WithCause(err)
	}

	return nil
}
