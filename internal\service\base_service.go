package service

import (
	"context"

	"backend/pkg/logger"
	"backend/pkg/util"
)

// BaseService 基础服务接口
type BaseService interface {
	// 获取服务管理器
	GetServiceManager() *ServiceManager

	// 获取日志记录器
	GetLogger() logger.Logger

	// GetAccountBookIDFromContext 从上下文中获取账套ID
	GetAccountBookIDFromContext(ctx context.Context) (uint64, error)

	// GetUserIDFromContext 从上下文中获取用户ID
	GetUserIDFromContext(ctx context.Context) (uint64, error)

	// GetUsernameFromContext 从上下文中获取用户名
	GetUsernameFromContext(ctx context.Context) (string, error)

	// GetRoleIDsFromContext 从上下文中获取角色ID列表
	GetRoleIDsFromContext(ctx context.Context) ([]uint, error)

	// IsAdminFromContext 从上下文中判断用户是否为管理员
	IsAdminFromContext(ctx context.Context) (bool, error)
}

// BaseServiceImpl 基础服务实现
type BaseServiceImpl struct {
	serviceManager *ServiceManager
	logger         logger.Logger
}

// NewBaseService 创建基础服务
func NewBaseService(sm *ServiceManager) *BaseServiceImpl {
	return &BaseServiceImpl{
		serviceManager: sm,
		logger:         sm.GetLogger(),
	}
}

// GetServiceManager 获取服务管理器
func (s *BaseServiceImpl) GetServiceManager() *ServiceManager {
	return s.serviceManager
}

// GetLogger 获取日志记录器
func (s *BaseServiceImpl) GetLogger() logger.Logger {
	if s.logger == nil {
		return logger.GetLogger()
	}
	return s.logger
}

// GetAccountBookIDFromContext 从上下文中安全地获取账套ID
func (s *BaseServiceImpl) GetAccountBookIDFromContext(ctx context.Context) (uint64, error) {
	return util.GetAccountBookIDFromStdContext(ctx)
}

// GetUserIDFromContext 从上下文中安全地获取用户ID
func (s *BaseServiceImpl) GetUserIDFromContext(ctx context.Context) (uint64, error) {
	return util.GetUserIDFromStdContext(ctx)
}

// GetUsernameFromContext 从上下文中安全地获取用户名
func (s *BaseServiceImpl) GetUsernameFromContext(ctx context.Context) (string, error) {
	return util.GetUsernameFromStdContext(ctx)
}

// GetRoleIDsFromContext 从上下文中安全地获取角色ID列表
func (s *BaseServiceImpl) GetRoleIDsFromContext(ctx context.Context) ([]uint, error) {
	return util.GetRoleIDsFromStdContext(ctx)
}

// IsAdminFromContext 从上下文中安全地判断用户是否为管理员
func (s *BaseServiceImpl) IsAdminFromContext(ctx context.Context) (bool, error) {
	return util.IsAdminFromStdContext(ctx)
}
