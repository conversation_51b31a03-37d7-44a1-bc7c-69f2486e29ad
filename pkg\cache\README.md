# 缓存模块说明文档

## 简介

缓存模块提供了高性能的内存缓存实现，支持键值对存储、过期时间设置、自动清理等功能。该模块主要用于提高系统性能，减少数据库访问，加速数据读取。

## 目录结构

```go
cache/
├── memory_cache.go  # 内存缓存实现
└── README.md        # 说明文档
```

## 常量定义

```go
// 缓存相关常量
const (
    CACHE_NEVER_EXPIRE   = time.Duration(-1) // 永不过期
    CACHE_DEFAULT_EXPIRE = time.Hour * 24    // 默认过期时间（24小时）
    CACHE_PREFIX_USER    = "user:"           // 用户缓存前缀
    CACHE_PREFIX_ROLE    = "role:"           // 角色缓存前缀
    CACHE_PREFIX_MENU    = "menu:"           // 菜单缓存前缀
    CACHE_PREFIX_DICT    = "dict:"           // 字典缓存前缀
    CACHE_PREFIX_CONFIG  = "config:"         // 配置缓存前缀
    CACHE_PREFIX_TOKEN   = "token:"          // 令牌缓存前缀
    CACHE_PREFIX_API     = "api:"            // API缓存前缀
    CACHE_PREFIX_PERM    = "perm:"           // 权限缓存前缀
)
```

## 核心接口

`Cache`接口定义了缓存的基本操作：

```go
type Cache interface {
    // 基本操作
    Set(key string, value interface{}, duration time.Duration) error
    Get(key string) (interface{}, bool)
    Delete(key string) bool
    Clear() error

    // 批量操作
    SetMany(items map[string]interface{}, duration time.Duration) error
    GetMany(keys []string) map[string]interface{}
    DeleteMany(keys []string) int

    // 特殊操作
    Increment(key string, value int64) (int64, error)
    Decrement(key string, value int64) (int64, error)
    GetOrSet(key string, getter func() (interface{}, error), duration time.Duration) (interface{}, error)

    // 其他操作
    Keys(pattern string) []string
    HasKey(key string) bool
    GetTTL(key string) time.Duration
    SetTTL(key string, duration time.Duration) bool
    Size() int
    Close() error
}
```

## 内存缓存实现

`MemoryCache`是`Cache`接口的内存实现，具有以下特点：

1. 高性能：基于Go原生map实现，读写性能优异
2. 线程安全：使用读写锁保证并发安全
3. 自动清理：定期清理过期缓存项
4. 灵活过期：支持永不过期、默认过期和自定义过期时间
5. 批量操作：支持批量设置、获取和删除
6. 计数器功能：支持原子递增和递减操作
7. 模式匹配：支持前缀匹配查询键

## 使用示例

### 创建缓存实例

```go
// 创建默认缓存（24小时过期，5分钟清理一次）
cache := cache.DefaultCache

// 创建自定义缓存（1小时过期，1分钟清理一次）
customCache := cache.NewMemoryCache(time.Hour, time.Minute)
```

### 基本操作

```go
// 设置缓存（10分钟过期）
err := cache.Set("key1", "value1", time.Minute*10)

// 设置永不过期的缓存
err = cache.Set("key2", "value2", cache.CACHE_NEVER_EXPIRE)

// 获取缓存
value, found := cache.Get("key1")
if found {
    // 使用value
}

// 删除缓存
deleted := cache.Delete("key1")

// 清空所有缓存
err = cache.Clear()
```

### 批量操作

```go
// 批量设置
items := map[string]interface{}{
    "key1": "value1",
    "key2": 123,
    "key3": true,
}
err := cache.SetMany(items, time.Hour)

// 批量获取
keys := []string{"key1", "key2", "key3"}
values := cache.GetMany(keys)

// 批量删除
count := cache.DeleteMany(keys)
```

### 计数器操作

```go
// 递增
newValue, err := cache.Increment("counter", 1)

// 递减
newValue, err = cache.Decrement("counter", 1)
```

### 高级操作

```go
// 获取或设置（如果不存在则调用getter函数获取值并缓存）
value, err := cache.GetOrSet("key", func() (interface{}, error) {
    // 从数据库或其他地方获取数据
    return "computed_value", nil
}, time.Hour)

// 获取所有以"user:"开头的键
keys := cache.Keys("user:*")

// 检查键是否存在
exists := cache.HasKey("key")

// 获取剩余生存时间
ttl := cache.GetTTL("key")

// 设置新的过期时间
success := cache.SetTTL("key", time.Hour*2)

// 获取缓存大小
size := cache.Size()

// 关闭缓存（停止清理器并清空缓存）
err = cache.Close()
```

## 缓存前缀使用建议

为了避免键名冲突，建议使用预定义的缓存前缀：

```go
// 用户缓存
userKey := cache.CACHE_PREFIX_USER + "123"
cache.Set(userKey, userObject, time.Hour)

// 角色缓存
roleKey := cache.CACHE_PREFIX_ROLE + "admin"
cache.Set(roleKey, roleObject, time.Hour)

// 菜单缓存
menuKey := cache.CACHE_PREFIX_MENU + "main"
cache.Set(menuKey, menuObject, time.Hour)
```

## 错误处理

缓存操作可能返回以下错误：

1. `ERROR_EMPTY_KEY`：键为空
2. `ERROR_TYPE_ERROR`：类型错误（如尝试对非数字类型执行递增操作）
3. `ERROR_KEY_NOT_FOUND`：键不存在
4. `ERROR_CACHE_CLOSED`：缓存已关闭

示例：

```go
err := cache.Set("", "value", time.Hour)
if err != nil {
    // 处理错误
    if metadata, ok := err.(errors.MetadataError); ok {
        if metadata.GetMetadata("error_code") == cache.ERROR_EMPTY_KEY {
            // 处理空键错误
        }
    }
}
```

## 性能优化建议

1. 合理设置过期时间，避免缓存过多无用数据
2. 对于频繁访问的数据，考虑使用永不过期的设置
3. 使用批量操作代替多次单个操作
4. 合理使用`GetOrSet`方法避免缓存穿透
5. 定期监控缓存大小，避免内存溢出
6. 在应用关闭时调用`Close`方法释放资源

## 线程安全性

`MemoryCache`实现是线程安全的，可以在多个goroutine中并发使用。内部使用读写锁保证并发安全，读操作之间不会互相阻塞，写操作会阻塞所有其他操作。

## 注意事项

1. 缓存的值可以是任何类型，但建议使用可序列化的类型
2. 递增和递减操作只适用于数字类型或可转换为数字的字符串
3. 模式匹配目前只支持前缀匹配（如"user:*"）
4. 缓存项过多可能导致内存占用过高，请合理设置过期时间和清理间隔
5. 缓存不应该用于存储关键业务数据，应该视为可丢失的临时数据
