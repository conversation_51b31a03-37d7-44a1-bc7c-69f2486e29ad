package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/model/vo"
	"backend/internal/service"     // <-- 确保导入 constant 包
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // 使用别名以避免与标准 errors 冲突
	"backend/pkg/logger"           // 导入 logger 包
	"backend/pkg/validator"        // 导入 validator 包
)

// MenuController 菜单控制器
type MenuControllerImpl struct {
	*BaseControllerImpl
	menuService service.MenuService
}

// NewMenuControllerImpl 创建菜单控制器实例
func NewMenuControllerImpl(cm *ControllerManager) *MenuControllerImpl {
	base := NewBaseController(cm)
	// 使用 service manager 的方法获取 MenuService 实例
	menuService := cm.GetServiceManager().GetMenuService()
	if menuService == nil {
		// 使用一致的 Fatal 日志记录，包含上下文
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 MenuService 实例")
	}
	return &MenuControllerImpl{
		BaseControllerImpl: base,
		menuService:        menuService,
	}
}

// CreateMenu 创建菜单
// @Summary 创建新菜单
// @Description 创建一个新的菜单项（目录、菜单或按钮）
// @Tags Menus
// @Accept json
// @Produce json
// @Param menu body dto.MenuCreateOrUpdateDTO true "菜单创建请求"
// @Success 200 {object} response.Response{data=vo.MenuVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /menus [post]
func (c *MenuControllerImpl) CreateMenu(ctx iris.Context) {
	opName := "创建菜单"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	var req dto.MenuCreateOrUpdateDTO
	// 读取并绑定 JSON 请求体到 DTO
	if err := ctx.ReadJSON(&req); err != nil {
		// 处理请求体读取或解析错误
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 参数校验 ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}
	// --- 校验结束 ---

	// 调用服务层创建菜单
	menuVO, err := c.menuService.CreateMenu(ctx.Request().Context(), req)
	if err != nil {
		// 处理服务层返回的错误
		c.HandleError(ctx, err, opName)
		return
	}
	// 返回成功响应
	c.Success(ctx, menuVO)
}

// UpdateMenu 更新菜单
// @Summary 更新菜单信息
// @Description 根据菜单ID更新菜单信息
// @Tags Menus
// @Accept json
// @Produce json
// @Param id path uint true "菜单ID"
// @Param menu body dto.MenuCreateOrUpdateDTO true "菜单更新请求"
// @Success 200 {object} response.Response{data=vo.MenuVO} "成功响应"
// @Failure 400 {object} response.Response "请求参数错误或ID无效"
// @Failure 404 {object} response.Response "菜单未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /menus/{id} [put]
func (c *MenuControllerImpl) UpdateMenu(ctx iris.Context) {
	opName := "更新菜单"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	// 从路径参数获取菜单 ID
	menuIDUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// 将 uint64 转换为 uint
	menuID := uint(menuIDUint64)

	var req dto.MenuCreateOrUpdateDTO
	// 读取并绑定 JSON 请求体
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	// --- 参数校验 ---
	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("menuID", menuID), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}
	// --- 校验结束 ---

	// 调用服务层更新菜单
	menuVO, err := c.menuService.UpdateMenu(ctx.Request().Context(), menuID, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, menuVO)
}

// DeleteMenu 删除菜单
// @Summary 删除菜单
// @Description 根据菜单ID删除菜单 (会检查是否有子菜单)
// @Tags Menus
// @Produce json
// @Param id path uint true "菜单ID"
// @Success 200 {object} response.Response "成功响应"
// @Failure 400 {object} response.Response "ID无效或存在子菜单"
// @Failure 404 {object} response.Response "菜单未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /menus/{id} [delete]
func (c *MenuControllerImpl) DeleteMenu(ctx iris.Context) {
	opName := "删除菜单"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	menuIDUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	menuID := uint(menuIDUint64)

	// 调用服务层删除菜单
	err = c.menuService.DeleteMenu(ctx.Request().Context(), menuID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// 返回成功的消息响应
	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetMenuByID 获取菜单详情
// @Summary 获取菜单详情
// @Description 根据菜单ID获取菜单详细信息
// @Tags Menus
// @Produce json
// @Param id path uint true "菜单ID"
// @Success 200 {object} response.Response{data=vo.MenuVO} "成功响应"
// @Failure 400 {object} response.Response "ID无效"
// @Failure 404 {object} response.Response "菜单未找到"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /menus/{id} [get]
func (c *MenuControllerImpl) GetMenuByID(ctx iris.Context) {
	opName := "获取菜单详情"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	menuIDUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	menuID := uint(menuIDUint64)

	// 调用服务层获取菜单
	menuVO, err := c.menuService.GetMenuByID(ctx.Request().Context(), menuID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	c.Success(ctx, menuVO)
}

// ListMenus 获取菜单列表 (分页)
// @Summary 获取菜单列表
// @Description 获取菜单列表 (分页)，支持按名称/标题/状态/类型过滤
// @Tags Menus
// @Produce json
// @Param name query string false "菜单名称过滤 (模糊匹配)"
// @Param title query string false "菜单标题过滤 (模糊匹配)"
// @Param status query int false "状态过滤 (0:禁用, 1:启用)" Enums(0, 1)
// @Param type query int false "类型过滤 (1:目录, 2:菜单, 3:按钮)" Enums(1, 2, 3)
// @Param pageNum query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param sort query string false "排序字段 (例如：sortOrder asc, menuName desc)"
// @Success 200 {object} response.Response{data=vo.PageVO} "成功响应，返回分页数据"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /menus/list [get]
func (c *MenuControllerImpl) ListMenus(ctx iris.Context) {
	opName := "获取菜单列表 (分页)"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	var queryDTO dto.MenuPageQueryDTO // DTO 类型保持不变，因为它包含了分页参数

	// 使用 ReadQuery 绑定查询参数
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析查询参数失败").WithCause(err), opName)
		return
	}

	// 调用服务层的 PageMenus 方法
	pageData, err := c.menuService.PageMenus(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// PageMenus 返回 *vo.PageVO，其中已经包含了列表数据和分页信息
	c.Success(ctx, pageData)
}

// GetMenuTree 获取菜单树
// @Summary 获取菜单树形结构
// @Description 获取菜单的树形结构，支持按状态/类型过滤
// @Tags Menus
// @Produce json
// @Param status query int false "状态过滤 (0:禁用, 1:启用)" Enums(0, 1)
// @Param type query int false "类型过滤 (1:目录, 2:菜单, 3:按钮)" Enums(1, 2, 3)
// @Success 200 {object} response.Response{data=[]vo.MenuTreeVO} "成功响应"
// @Failure 400 {object} response.Response "查询参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /menus/tree [get]
func (c *MenuControllerImpl) GetMenuTree(ctx iris.Context) {
	opName := "获取菜单树"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	var queryDTO dto.MenuTreeDTO // 使用 var 声明以便 ReadQuery 可以填充它

	// 使用 ReadQuery 绑定查询参数 (status, type)
	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析查询参数失败").WithCause(err), opName)
		return
	}

	// 调用服务层获取菜单树
	menuTree, err := c.menuService.GetMenuTree(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	// 确保返回空数组而不是 nil
	if menuTree == nil {
		menuTree = []*vo.MenuTreeVO{}
	}
	c.Success(ctx, menuTree)
}

// GetUserMenuTree 获取当前登录用户的菜单树
// @Summary 获取当前用户菜单树
// @Description 获取当前登录用户有权限访问的菜单树形结构 (通常用于侧边栏)
// @Tags Menus Users
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.MenuTreeVO} "成功响应"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /user/menus/tree [get]
func (c *MenuControllerImpl) GetUserMenuTree(ctx iris.Context) {
	opName := "获取用户菜单树"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	// 从上下文中获取用户ID
	userIDUint64, err := c.GetUserIDFromContext(ctx)
	if err != nil {
		c.HandleError(ctx, err, opName) // GetUserIDFromContext 已经返回了合适的 apperrors
		return
	}
	userID := uint(userIDUint64) // 服务层需要 uint

	// 调用服务层获取用户菜单树
	menuTree, err := c.menuService.GetUserMenuTree(ctx.Request().Context(), userID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// 确保返回空数组而不是 nil
	if menuTree == nil {
		menuTree = []*vo.MenuTreeVO{}
	}
	c.Success(ctx, menuTree)
}

// GetUserPermissions 获取当前登录用户的权限标识列表
// @Summary 获取当前用户权限标识
// @Description 获取当前登录用户拥有的所有权限标识符 (字符串列表)
// @Tags Menus Users Permissions
// @Produce json
// @Success 200 {object} response.Response{data=[]string} "成功响应"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /user/permissions [get]
func (c *MenuControllerImpl) GetUserPermissions(ctx iris.Context) {
	opName := "获取用户权限列表"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "menu")

	// 从上下文中获取用户ID
	userIDUint64, err := c.GetUserIDFromContext(ctx)
	if err != nil {
		c.HandleError(ctx, err, opName) // GetUserIDFromContext 已经返回了合适的 apperrors
		return
	}
	userID := uint(userIDUint64) // 服务层需要 uint

	// 调用服务层获取用户权限
	permissions, err := c.menuService.GetUserPermissions(ctx.Request().Context(), userID)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	// 确保返回空数组而不是 nil
	if permissions == nil {
		permissions = []string{}
	}
	c.Success(ctx, permissions)
}
