package vo

import (
	"time"

	"github.com/shopspring/decimal"
)

// ScmSupplierVO 供应商详细视图对象
type ScmSupplierVO struct {
	// 基础字段
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	CreatedBy     uint      `json:"createdBy"`
	UpdatedBy     uint      `json:"updatedBy"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	// 供应商基本信息
	SupplierCode        string           `json:"supplierCode"`
	SupplierName        string           `json:"supplierName"`
	SupplierType        string           `json:"supplierType"`
	Industry            *string          `json:"industry,omitempty"`
	BusinessLicense     *string          `json:"businessLicense,omitempty"`
	TaxNumber           *string          `json:"taxNumber,omitempty"`
	LegalRepresentative *string          `json:"legalRepresentative,omitempty"`
	RegisteredCapital   *decimal.Decimal `json:"registeredCapital,omitempty"`

	// 联系信息
	ContactPerson *string `json:"contactPerson,omitempty"`
	ContactPhone  *string `json:"contactPhone,omitempty"`
	ContactEmail  *string `json:"contactEmail,omitempty"`
	Website       *string `json:"website,omitempty"`

	// 地址信息
	Country    *string `json:"country,omitempty"`
	Province   *string `json:"province,omitempty"`
	City       *string `json:"city,omitempty"`
	District   *string `json:"district,omitempty"`
	Address    *string `json:"address,omitempty"`
	PostalCode *string `json:"postalCode,omitempty"`

	// 财务信息
	CreditRating *string          `json:"creditRating,omitempty"`
	CreditLimit  *decimal.Decimal `json:"creditLimit,omitempty"`
	PaymentTerms *string          `json:"paymentTerms,omitempty"`
	CurrencyCode string           `json:"currencyCode"`

	// 业务信息
	SupplierLevel               string  `json:"supplierLevel"`
	SupplierCategory            *string `json:"supplierCategory,omitempty"`
	SupplierSource              *string `json:"supplierSource,omitempty"`
	ProcurementRepresentativeId *uint   `json:"procurementRepresentativeId,omitempty"`

	// 供应商特有字段
	QualificationCertificate *string          `json:"qualificationCertificate,omitempty"`
	QualityRating            *string          `json:"qualityRating,omitempty"`
	DeliveryRating           *string          `json:"deliveryRating,omitempty"`
	ServiceRating            *string          `json:"serviceRating,omitempty"`
	AnnualSupplyCapacity     *decimal.Decimal `json:"annualSupplyCapacity,omitempty"`

	// 状态信息
	Status        string  `json:"status"`
	IsKeySupplier bool    `json:"isKeySupplier"`
	Remark        *string `json:"remark,omitempty"`

	// 关联信息
	Contacts []ScmSupplierContactVO `json:"contacts,omitempty"`

	// 扩展信息（可用于显示关联实体的名称）
	ProcurementRepresentativeName *string `json:"procurementRepresentativeName,omitempty"`
}

// ScmSupplierListVO 供应商列表视图对象（简化版，用于列表展示）
type ScmSupplierListVO struct {
	ID                            uint             `json:"id"`
	SupplierCode                  string           `json:"supplierCode"`
	SupplierName                  string           `json:"supplierName"`
	SupplierType                  string           `json:"supplierType"`
	Industry                      *string          `json:"industry,omitempty"`
	ContactPerson                 *string          `json:"contactPerson,omitempty"`
	ContactPhone                  *string          `json:"contactPhone,omitempty"`
	ContactEmail                  *string          `json:"contactEmail,omitempty"`
	SupplierLevel                 string           `json:"supplierLevel"`
	SupplierCategory              *string          `json:"supplierCategory,omitempty"`
	Status                        string           `json:"status"`
	IsKeySupplier                 bool             `json:"isKeySupplier"`
	QualityRating                 *string          `json:"qualityRating,omitempty"`
	DeliveryRating                *string          `json:"deliveryRating,omitempty"`
	ServiceRating                 *string          `json:"serviceRating,omitempty"`
	CreditLimit                   *decimal.Decimal `json:"creditLimit,omitempty"`
	AnnualSupplyCapacity          *decimal.Decimal `json:"annualSupplyCapacity,omitempty"`
	ProcurementRepresentativeId   *uint            `json:"procurementRepresentativeId,omitempty"`
	ProcurementRepresentativeName *string          `json:"procurementRepresentativeName,omitempty"`
	CreatedAt                     time.Time        `json:"createdAt"`
	UpdatedAt                     time.Time        `json:"updatedAt"`
}

// ScmSupplierContactVO 供应商联系人详细视图对象
type ScmSupplierContactVO struct {
	// 基础字段
	ID            uint      `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	CreatedBy     uint      `json:"createdBy"`
	UpdatedBy     uint      `json:"updatedBy"`
	TenantID      uint      `json:"tenantId"`
	AccountBookID uint      `json:"accountBookId"`

	// 关联信息
	SupplierID uint `json:"supplierId"`

	// 联系人信息
	ContactName  string     `json:"contactName"`
	ContactTitle *string    `json:"contactTitle,omitempty"`
	Department   *string    `json:"department,omitempty"`
	Phone        *string    `json:"phone,omitempty"`
	Mobile       *string    `json:"mobile,omitempty"`
	Email        *string    `json:"email,omitempty"`
	QQ           *string    `json:"qq,omitempty"`
	Wechat       *string    `json:"wechat,omitempty"`
	Address      *string    `json:"address,omitempty"`
	PostalCode   *string    `json:"postalCode,omitempty"`
	Birthday     *time.Time `json:"birthday,omitempty"`
	Gender       *string    `json:"gender,omitempty"`
	ContactRole  *string    `json:"contactRole"`
	Remark       *string    `json:"remark,omitempty"`

	// 扩展信息
	SupplierName *string `json:"supplierName,omitempty"`
}

// ScmSupplierContactListVO 供应商联系人列表视图对象（简化版，用于列表展示）
type ScmSupplierContactListVO struct {
	ID           uint      `json:"id"`
	SupplierID   uint      `json:"supplierId"`
	SupplierName *string   `json:"supplierName,omitempty"`
	ContactName  string    `json:"contactName"`
	ContactTitle *string   `json:"contactTitle,omitempty"`
	Department   *string   `json:"department,omitempty"`
	Phone        *string   `json:"phone,omitempty"`
	Mobile       *string   `json:"mobile,omitempty"`
	Email        *string   `json:"email,omitempty"`
	ContactRole  *string   `json:"contactRole"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// ScmSupplierSummaryVO 供应商摘要视图对象（用于统计和仪表板）
type ScmSupplierSummaryVO struct {
	TotalSuppliers      int64 `json:"totalSuppliers"`      // 供应商总数
	ActiveSuppliers     int64 `json:"activeSuppliers"`     // 活跃供应商数
	InactiveSuppliers   int64 `json:"inactiveSuppliers"`   // 停用供应商数
	BlacklistSuppliers  int64 `json:"blacklistSuppliers"`  // 黑名单供应商数
	ASuppliers          int64 `json:"aSuppliers"`          // A级供应商数
	BSuppliers          int64 `json:"bSuppliers"`          // B级供应商数
	CSuppliers          int64 `json:"cSuppliers"`          // C级供应商数
	DSuppliers          int64 `json:"dSuppliers"`          // D级供应商数
	KeySuppliers        int64 `json:"keySuppliers"`        // 重点供应商数
	CorporateSuppliers  int64 `json:"corporateSuppliers"`  // 企业供应商数
	IndividualSuppliers int64 `json:"individualSuppliers"` // 个人供应商数
}

// ScmSupplierRatingVO 供应商评级视图对象（用于评级分析）
type ScmSupplierRatingVO struct {
	SupplierID         uint       `json:"supplierId"`
	SupplierCode       string     `json:"supplierCode"`
	SupplierName       string     `json:"supplierName"`
	QualityRating      *string    `json:"qualityRating,omitempty"`
	DeliveryRating     *string    `json:"deliveryRating,omitempty"`
	ServiceRating      *string    `json:"serviceRating,omitempty"`
	OverallRating      *string    `json:"overallRating,omitempty"`      // 综合评级
	LastEvaluationDate *time.Time `json:"lastEvaluationDate,omitempty"` // 最后评估日期
	NextEvaluationDate *time.Time `json:"nextEvaluationDate,omitempty"` // 下次评估日期
}

// ScmSupplierPerformanceVO 供应商绩效视图对象（用于绩效分析）
type ScmSupplierPerformanceVO struct {
	SupplierID               uint             `json:"supplierId"`
	SupplierCode             string           `json:"supplierCode"`
	SupplierName             string           `json:"supplierName"`
	TotalOrders              int64            `json:"totalOrders"`              // 总订单数
	OnTimeDeliveryRate       *decimal.Decimal `json:"onTimeDeliveryRate"`       // 按时交货率
	QualityPassRate          *decimal.Decimal `json:"qualityPassRate"`          // 质量合格率
	TotalPurchaseAmount      *decimal.Decimal `json:"totalPurchaseAmount"`      // 总采购金额
	AverageOrderValue        *decimal.Decimal `json:"averageOrderValue"`        // 平均订单价值
	AnnualSupplyCapacity     *decimal.Decimal `json:"annualSupplyCapacity"`     // 年供货能力
	CurrentSupplyUtilization *decimal.Decimal `json:"currentSupplyUtilization"` // 当前供货利用率
	LastOrderDate            *time.Time       `json:"lastOrderDate,omitempty"`  // 最后订单日期
}

// ScmSupplierSimpleVO 供应商简单视图对象（用于下拉选择）
type ScmSupplierSimpleVO struct {
	ID           uint   `json:"id"`
	SupplierCode string `json:"supplierCode"`
	SupplierName string `json:"supplierName"`
	SupplierType string `json:"supplierType"`
	Status       string `json:"status"`
}
