<template>
  <el-dialog
    v-model="dialogVisible"
    title="分配详情"
    width="80%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="allocation-detail-container" v-if="allocationData">
      <!-- 基本信息 -->
      <el-card class="basic-info-card" shadow="never">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">出库通知单号:</span>
              <span class="value">{{ allocationData.notificationNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">客户名称:</span>
              <span class="value">{{ allocationData.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">分配状态:</span>
              <el-tag :type="getStatusTagType(allocationData.allocationStatus)">
                {{ formatStatus(allocationData.allocationStatus) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">分配策略:</span>
              <span class="value">{{ formatStrategy(allocationData.strategy) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">分配进度:</span>
              <div class="progress-container">
                <el-progress
                  :percentage="allocationData.allocationProgress"
                  :color="getProgressColor(allocationData.allocationProgress)"
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="progress-text">{{ allocationData.allocationProgress }}%</span>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">分配时间:</span>
              <span class="value">{{ allocationData.allocatedAt || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 统计信息 -->
      <el-card class="stats-card" shadow="never">
        <template #header>
          <span>统计信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ allocationData.totalItems }}</div>
              <div class="stat-label">总物料数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ allocationData.allocatedItems }}</div>
              <div class="stat-label">已分配数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ allocationData.totalQty }}</div>
              <div class="stat-label">总数量</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ allocationData.allocatedQty }}</div>
              <div class="stat-label">已分配量</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 分配明细 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="section-header">
            <span>分配明细</span>
            <el-button
              type="primary"
              size="small"
              @click="handleRefresh"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </template>
        
        <VNTable
          ref="detailTableRef"
          :data="allocationDetails"
          :columns="detailColumns"
          :loading="loading"
          row-key="id"
          show-index
          max-height="400"
        >
          <template #column-status="{ row }">
            <el-tag :type="getDetailStatusTagType(row.status)" size="small">
              {{ formatDetailStatus(row.status) }}
            </el-tag>
          </template>
          
          <template #column-progress="{ row }">
            <div class="progress-container">
              <el-progress
                :percentage="getDetailProgress(row)"
                :color="getProgressColor(getDetailProgress(row))"
                :stroke-width="4"
                :show-text="false"
              />
              <span class="progress-text">{{ getDetailProgress(row) }}%</span>
            </div>
          </template>
        </VNTable>
      </el-card>
      
      <!-- 分配历史 -->
      <el-card class="history-card" shadow="never">
        <template #header>
          <span>分配历史</span>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="record in allocationHistory"
            :key="record.id"
            :timestamp="record.timestamp"
            :type="getTimelineType(record.type)"
            placement="top"
          >
            <el-card class="timeline-card">
              <div class="record-header">
                <span class="record-title">{{ record.title }}</span>
                <el-tag :type="getRecordTagType(record.type)" size="small">
                  {{ record.typeName }}
                </el-tag>
              </div>
              <div class="record-content">
                <p>{{ record.description }}</p>
                <div v-if="record.operator" class="record-operator">
                  <span>操作人: {{ record.operator }}</span>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <!-- 空状态 -->
        <div v-if="allocationHistory.length === 0" class="empty-state">
          <el-empty description="暂无分配历史" :image-size="80" />
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleExport"
          v-if="allocationDetails.length > 0"
        >
          导出详情
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElRow, ElCol, ElTag, ElProgress, ElTimeline, ElTimelineItem, ElEmpty } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import type { TableColumn } from '@/components/VNTable/types'
import { useInventoryAllocationStore } from '@/store/wms/inventoryAllocation'

// Store
const inventoryAllocationStore = useInventoryAllocationStore()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const allocationData = ref<any>(null)
const allocationDetails = ref<any[]>([])
const allocationHistory = ref<any[]>([])

// 明细表格列配置
const detailColumns = computed<TableColumn[]>(() => [
  { prop: 'itemCode', label: '物料编码', width: 120 },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'requiredQty', label: '需求数量', width: 100 },
  { prop: 'allocatedQty', label: '分配数量', width: 100 },
  { prop: 'pickedQty', label: '拣货数量', width: 100 },
  { prop: 'locationCode', label: '库位', width: 120 },
  { prop: 'batchNo', label: '批次号', width: 120 },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'progress', label: '进度', width: 120, slot: true }
])

// 表单引用
const detailTableRef = ref<InstanceType<typeof VNTable>>()

// 方法
const loadAllocationData = async (allocationId: number) => {
  try {
    loading.value = true
    
    // 加载基本信息
    const data = await inventoryAllocationStore.fetchDetail(allocationId)
    allocationData.value = data
    
    // 加载分配明细（模拟数据）
    allocationDetails.value = [
      {
        id: 1,
        itemCode: 'ITEM001',
        itemName: '测试物料1',
        requiredQty: 100,
        allocatedQty: 80,
        pickedQty: 60,
        locationCode: 'A01-01-01',
        batchNo: 'BATCH001',
        status: 'ALLOCATED'
      },
      {
        id: 2,
        itemCode: 'ITEM002',
        itemName: '测试物料2',
        requiredQty: 50,
        allocatedQty: 50,
        pickedQty: 50,
        locationCode: 'A01-01-02',
        batchNo: 'BATCH002',
        status: 'PICKED'
      }
    ]
    
    // 加载分配历史
    const history = await inventoryAllocationStore.getAllocationHistory(allocationId)
    allocationHistory.value = history || []
  } catch (error) {
    ElMessage.error('加载分配详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  // 在open方法中会传入allocationId
}

const handleClose = () => {
  resetData()
  dialogVisible.value = false
}

const resetData = () => {
  allocationData.value = null
  allocationDetails.value = []
  allocationHistory.value = []
}

const handleRefresh = () => {
  if (allocationData.value) {
    loadAllocationData(allocationData.value.id)
  }
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 工具方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING': 'info',
    'PARTIAL': 'warning',
    'ALLOCATED': 'success',
    'FAILED': 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'PENDING': '待分配',
    'PARTIAL': '部分分配',
    'ALLOCATED': '已分配',
    'FAILED': '分配失败'
  }
  return labelMap[status] || '未知'
}

const formatStrategy = (strategy: string) => {
  const labelMap: Record<string, string> = {
    'FIFO': 'FIFO (先进先出)',
    'LIFO': 'LIFO (后进先出)',
    'BATCH_FIRST': '批次优先',
    'NEAREST': '就近原则',
    'COST_FIRST': '成本优先',
    'QUALITY_FIRST': '质量优先'
  }
  return labelMap[strategy] || strategy
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const getDetailStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING': 'info',
    'ALLOCATED': 'warning',
    'PICKED': 'success',
    'FAILED': 'danger'
  }
  return typeMap[status] || 'info'
}

const formatDetailStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'PENDING': '待分配',
    'ALLOCATED': '已分配',
    'PICKED': '已拣货',
    'FAILED': '分配失败'
  }
  return labelMap[status] || '未知'
}

const getDetailProgress = (row: any) => {
  if (row.requiredQty === 0) return 0
  return Math.round((row.allocatedQty / row.requiredQty) * 100)
}

const getTimelineType = (type: string) => {
  const typeMap: Record<string, string> = {
    'CREATE': 'primary',
    'ALLOCATE': 'success',
    'PICK': 'warning',
    'RELEASE': 'info',
    'FAIL': 'danger'
  }
  return typeMap[type] || 'primary'
}

const getRecordTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'CREATE': 'primary',
    'ALLOCATE': 'success',
    'PICK': 'warning',
    'RELEASE': 'info',
    'FAIL': 'danger'
  }
  return typeMap[type] || 'primary'
}

// 暴露方法
const open = (allocationId: number) => {
  dialogVisible.value = true
  loadAllocationData(allocationId)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.allocation-detail-container {
  padding: 16px 0;
}

.basic-info-card,
.stats-card,
.detail-card,
.history-card {
  margin-bottom: 16px;
}

.basic-info-card :deep(.el-card__header),
.stats-card :deep(.el-card__header),
.detail-card :deep(.el-card__header),
.history-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 100px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-container .el-progress {
  flex: 1;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 35px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeline-card {
  margin-bottom: 0;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-title {
  font-weight: 600;
  color: #303133;
}

.record-content p {
  margin: 0 0 8px 0;
  color: #606266;
}

.record-operator {
  font-size: 12px;
  color: #909399;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.dialog-footer {
  text-align: right;
}
</style>
