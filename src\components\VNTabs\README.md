# VNTabs 组件

一个基于 Element Plus `el-tabs` 和 `el-tab-pane` 封装的通用标签页组件，允许通过 `items` prop 动态配置标签页，并使用具名插槽来定义每个标签页的内容。

## Props

继承了 `el-tabs` 的部分 Props (做了部分排除和简化)，并将它们设为可选。常用和自定义的 Props 包括：

| Prop Name     | Type                             | Description                                      | Default     |
| ------------- | -------------------------------- | ------------------------------------------------ | ----------- |
| `modelValue`  | `string \| number`               | 当前激活的标签页 `name` (v-model)                  | (第一个 item 的 name) |
| `items`       | `TabItem[]`                      | 标签页配置数组，**必须提供**。                     | `[]`        |
| `type`        | `'card' \| 'border-card'`          | 可选，标签页样式类型                             | (无)        |
| `tabPosition` | `'top' \| 'right' \| 'bottom' \| 'left'` | 可选，标签页位置                                 | `'top'`     |
| `editable`    | `boolean`                        | 标签是否可增加和删除 (需要监听 `@edit` 事件)       | `false`     |
| `closable`    | `boolean`                        | 标签是否可关闭 (优先级低于 `editable`，需要监听 `@tab-remove`) | `false`     |
| `addable`     | `boolean`                        | 标签是否可增加 (优先级低于 `editable`，需要监听 `@edit`)    | `false`     |
| `stretch`     | `boolean`                        | 标签的宽度是否自撑开                             | `false`     |
| `beforeLeave` | `Function`                       | 切换标签之前的钩子，若返回 `false` 或 `Promise.reject`，则阻止切换。 | `undefined` |
| ...           |                                  | 其他继承自 `el-tabs` 的 props。                  |             |

### TabItem 接口

`items` 数组中的每个对象应符合 `TabItem` 接口：

```typescript
interface TabItem {
  name: string | number; // 标签页的唯一标识符
  label: string; // 标签页显示的标题
  icon?: string | object; // 可选，标签页图标
  disabled?: boolean; // 是否禁用
  lazy?: boolean; // 内容是否懒加载
  closable?: boolean; // 单个标签是否可关闭 (需要 editable=true 或 closable=true)
  [key: string]: any; // 允许其他自定义属性
}
```

## Emits

转发了 `el-tabs` 的常用事件：

*   `update:modelValue(name)`: `v-model` 值改变时触发。
*   `tab-click(pane, event)`: tab 被选中时触发。
*   `tab-change(name)`: `activeName` 改变时触发。
*   `tab-remove(name)`: 点击 tab 移除按钮后触发。
*   `edit(paneName, action)`: 点击 tab 的新增按钮或删除按钮时触发。

## Slots

*   **`[name]` (具名内容插槽):** **必需**。使用与 `TabItem` 的 `name` 属性同名的插槽来定义对应标签页的内容。可以通过 `#` 语法获取当前 `item` 数据，例如 `<template #tab1="{ item }">...</template>`。
*   `label({ item, name })` (作用域插槽): 可选。自定义标签页头部的渲染内容。如果未使用此插槽，则默认显示 `item.label`，若提供了 `item.icon` 则会显示图标+文字。

## 使用示例

```vue
<template>
  <VNTabs v-model="activeTabName" :items="tabItems" type="card" @tab-remove="handleTabRemove">
    <!-- 使用具名插槽定义每个 tab 的内容 -->
    <template #tab1="{ item }">
      <div>
        <h3>{{ item.label }} 内容</h3>
        <p>这是第一个标签页的具体内容。</p>
      </div>
    </template>

    <template #tab2="{ item }">
       <div>
          <h3>配置项</h3>
          <el-input placeholder="输入配置..."></el-input>
       </div>
    </template>

     <template #tab3>
       <div>
         <p>用户列表 (懒加载)</p>
         <!-- 这里可以放用户列表组件 -->
       </div>
    </template>

     <!-- 自定义标签头示例 (可选) -->
     <!--
     <template #label="{ item }">
        <span style="color: red;">{{ item.label }} !</span>
     </template>
     -->

  </VNTabs>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNTabs from '@/components/VNTabs/index.vue';
import type { TabItem } from '@/components/VNTabs/types';
import { ElInput, ElMessage } from 'element-plus';
import { User, Setting } from '@element-plus/icons-vue';

const activeTabName = ref<string | number>('tab1');

const tabItems = ref<TabItem[]>([
  { name: 'tab1', label: '用户信息', icon: User },
  { name: 'tab2', label: '配置', icon: Setting, closable: true }, // 可关闭
  { name: 'tab3', label: '用户列表', lazy: true }, // 懒加载
  { name: 'tab4', label: '禁用标签', disabled: true },
]);

const handleTabRemove = (targetName: string | number) => {
  const index = tabItems.value.findIndex(tab => tab.name === targetName);
  if (index !== -1) {
    tabItems.value.splice(index, 1);
    ElMessage.warning(`移除了标签: ${targetName}`);
    // 如果移除的是当前激活的 tab，需要切换到其他 tab
    if (activeTabName.value === targetName) {
      activeTabName.value = tabItems.value[Math.max(0, index - 1)]?.name || tabItems.value[0]?.name || '';
    }
  }
};

</script>
```

## 注意事项

*   组件基于 `element-plus` 的 `ElTabs` 和 `ElTabPane`。
*   **必须** 使用与 `TabItem.name` 同名的具名插槽来提供每个标签页的内容。
*   可以通过 `type` prop 设置标签页样式 (`card` 或 `border-card`)。
*   可以通过 `tabPosition` prop 设置标签页位置。
*   可编辑/可关闭功能需要设置相应的 props (`editable`, `closable`, 或 `item.closable`) 并监听对应的事件 (`@edit`, `@tab-remove`) 来处理数据变化。 