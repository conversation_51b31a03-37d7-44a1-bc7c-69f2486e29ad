<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="80%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="isViewMode"
    >
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="发运单号" prop="shipmentNo">
              <el-input
                v-model="formData.shipmentNo"
                placeholder="系统自动生成"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库通知单" prop="outboundNotificationId">
              <div class="input-with-button">
                <el-input
                  v-model="formData.outboundNotificationNo"
                  placeholder="请选择出库通知单"
                  readonly
                />
                <el-button
                  @click="handleSelectOutboundNotification"
                  :disabled="isViewMode"
                >
                  选择
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="承运商" prop="carrierId">
              <el-select
                v-model="formData.carrierId"
                placeholder="请选择承运商"
                style="width: 100%"
                @change="handleCarrierChange"
              >
                <el-option
                  v-for="carrier in carriers"
                  :key="carrier.id"
                  :label="carrier.carrierName"
                  :value="carrier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="运输方式" prop="shippingMethod">
              <el-select
                v-model="formData.shippingMethod"
                placeholder="请选择运输方式"
                style="width: 100%"
                @change="handleShippingMethodChange"
              >
                <el-option
                  v-for="method in shippingMethods"
                  :key="method.value"
                  :label="method.label"
                  :value="method.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计发货日期" prop="estimatedShipDate">
              <el-date-picker
                v-model="formData.estimatedShipDate"
                type="date"
                placeholder="请选择发货日期"
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-date-picker>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计送达日期" prop="estimatedDeliveryDate">
              <el-date-picker
                v-model="formData.estimatedDeliveryDate"
                type="date"
                placeholder="请选择送达日期"
                style="width: 100%"
                :disabled-date="disabledDeliveryDate"
              />
            </el-date-picker>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 收货信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>收货信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="收货人" prop="consigneeName">
              <el-input
                v-model="formData.consigneeName"
                placeholder="请输入收货人姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收货电话" prop="consigneePhone">
              <el-input
                v-model="formData.consigneePhone"
                placeholder="请输入收货电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收货邮箱" prop="consigneeEmail">
              <el-input
                v-model="formData.consigneeEmail"
                placeholder="请输入收货邮箱"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="16">
            <el-form-item label="收货地址" prop="consigneeAddress">
              <el-input
                v-model="formData.consigneeAddress"
                placeholder="请输入收货地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮政编码" prop="postalCode">
              <el-input
                v-model="formData.postalCode"
                placeholder="请输入邮政编码"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 运费信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span>运费信息</span>
            <el-button
              type="primary"
              size="small"
              @click="handleCalculateShippingCost"
              :disabled="isViewMode || !canCalculateCost"
            >
              计算运费
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="总重量(kg)" prop="totalWeight">
              <el-input-number
                v-model="formData.totalWeight"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总体积(m³)" prop="totalVolume">
              <el-input-number
                v-model="formData.totalVolume"
                :min="0"
                :precision="3"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运费金额" prop="shippingCost">
              <el-input-number
                v-model="formData.shippingCost"
                :min="0"
                :precision="2"
                style="width: 100%"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="保险费" prop="insuranceFee">
              <el-input-number
                v-model="formData.insuranceFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 发运明细 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>发运明细</span>
        </template>
        
        <VNTable
          ref="detailTableRef"
          :data="formData.details"
          :columns="detailColumns"
          :loading="false"
          row-key="id"
          show-index
          max-height="300"
        >
          <template #column-packageType="{ row }">
            <el-select
              v-model="row.packageType"
              placeholder="包装类型"
              size="small"
              style="width: 100%"
              :disabled="isViewMode"
            >
              <el-option
                v-for="option in packageTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
        </VNTable>
      </el-card>
      
      <!-- 备注信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>备注信息</span>
        </template>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-card>
      
      <!-- 执行信息 -->
      <el-card class="form-section" shadow="never" v-if="mode !== 'create'">
        <template #header>
          <span>执行信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="运单号">
              <el-input
                :value="formData.trackingNo || '-'"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实际发货时间">
              <el-input
                :value="formData.actualShipDate || '-'"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实际送达时间">
              <el-input
                :value="formData.actualDeliveryDate || '-'"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ isViewMode ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!isViewMode"
          @click="handleSave"
          :loading="submitting"
        >
          保存
        </el-button>
        <el-button
          v-if="!isViewMode"
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          提交
        </el-button>
      </div>
    </template>
    
    <!-- 出库通知单选择器 -->
    <OutboundNotificationSelector
      ref="outboundNotificationSelectorRef"
      @confirm="handleOutboundNotificationConfirm"
      @cancel="handleOutboundNotificationCancel"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose, onMounted } from 'vue'
import { ElDialog, ElButton, ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElInputNumber, ElCard, ElRow, ElCol } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import VNTable from '@/components/VNTable/index.vue'
import OutboundNotificationSelector from '../../picking-task/components/OutboundNotificationSelector.vue'
import type { TableColumn } from '@/components/VNTable/types'
import type { ShipmentFormData } from '@/types/wms/shipment'
import { useShipmentStore } from '@/store/wms/shipment'

// 组件接口定义
interface ShipmentFormEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<ShipmentFormEmits>()

// Store
const shipmentStore = useShipmentStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const mode = ref<'create' | 'edit' | 'view'>('create')
const recordId = ref<number | null>(null)

// 基础数据
const carriers = computed(() => shipmentStore.carriers)
const shippingMethods = ref([
  { label: '快递', value: 'EXPRESS' },
  { label: '物流', value: 'LOGISTICS' },
  { label: '专车', value: 'DEDICATED' },
  { label: '自提', value: 'SELF_PICKUP' }
])

const packageTypeOptions = [
  { label: '纸箱', value: 'CARTON' },
  { label: '木箱', value: 'WOODEN_BOX' },
  { label: '托盘', value: 'PALLET' },
  { label: '袋装', value: 'BAG' }
]

// 表单数据
const formData = reactive<ShipmentFormData>({
  outboundNotificationId: null,
  carrierId: null,
  shippingMethod: '',
  details: [],
  _mode: 'create'
})

// 表单验证规则
const formRules: FormRules = {
  outboundNotificationId: [
    { required: true, message: '请选择出库通知单', trigger: 'change' }
  ],
  carrierId: [
    { required: true, message: '请选择承运商', trigger: 'change' }
  ],
  shippingMethod: [
    { required: true, message: '请选择运输方式', trigger: 'change' }
  ],
  consigneeName: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' }
  ],
  consigneePhone: [
    { required: true, message: '请输入收货电话', trigger: 'blur' }
  ],
  consigneeAddress: [
    { required: true, message: '请输入收货地址', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    create: '新建发运单',
    edit: '编辑发运单',
    view: '查看发运单'
  }
  return titles[mode.value]
})

const isViewMode = computed(() => mode.value === 'view')

const canCalculateCost = computed(() => {
  return formData.carrierId && formData.shippingMethod && formData.totalWeight && formData.totalVolume
})

// 明细表格列配置
const detailColumns = computed<TableColumn[]>(() => [
  { prop: 'itemCode', label: '物料编码', minWidth: 120 },
  { prop: 'itemName', label: '物料名称', minWidth: 180 },
  { prop: 'quantity', label: '数量', width: 100 },
  { prop: 'weight', label: '重量(kg)', width: 100 },
  { prop: 'volume', label: '体积(m³)', width: 100 },
  { prop: 'packageType', label: '包装类型', width: 120, slot: true },
  { prop: 'packageCount', label: '包装数量', width: 100 }
])

// 表单引用
const formRef = ref<FormInstance>()
const detailTableRef = ref<InstanceType<typeof VNTable>>()
const outboundNotificationSelectorRef = ref<InstanceType<typeof OutboundNotificationSelector>>()

// 方法
const loadFormData = async (id: number) => {
  try {
    const record = await shipmentStore.fetchDetail(id)
    
    // 填充表单数据
    Object.assign(formData, {
      id: record.id,
      shipmentNo: record.shipmentNo,
      outboundNotificationId: record.outboundNotificationId,
      outboundNotificationNo: record.outboundNotificationNo,
      carrierId: record.carrierId,
      shippingMethod: record.shippingMethod,
      estimatedShipDate: record.estimatedShipDate,
      estimatedDeliveryDate: record.estimatedDeliveryDate,
      consigneeName: record.consigneeName,
      consigneePhone: record.consigneePhone,
      consigneeEmail: record.consigneeEmail,
      consigneeAddress: record.consigneeAddress,
      postalCode: record.postalCode,
      totalWeight: record.totalWeight,
      totalVolume: record.totalVolume,
      shippingCost: record.shippingCost,
      insuranceFee: record.insuranceFee,
      trackingNo: record.trackingNo,
      actualShipDate: record.actualShipDate,
      actualDeliveryDate: record.actualDeliveryDate,
      remark: record.remark,
      details: record.details || []
    })
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const handleOpen = () => {
  if (mode.value !== 'create' && recordId.value) {
    loadFormData(recordId.value)
  }
}

const handleClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    outboundNotificationId: null,
    outboundNotificationNo: '',
    carrierId: null,
    shippingMethod: '',
    estimatedShipDate: '',
    estimatedDeliveryDate: '',
    consigneeName: '',
    consigneePhone: '',
    consigneeEmail: '',
    consigneeAddress: '',
    postalCode: '',
    totalWeight: null,
    totalVolume: null,
    shippingCost: null,
    insuranceFee: null,
    remark: '',
    details: []
  })
  formRef.value?.clearValidate()
}

const handleSelectOutboundNotification = () => {
  outboundNotificationSelectorRef.value?.open()
}

const handleOutboundNotificationConfirm = (notification: any) => {
  formData.outboundNotificationId = notification.id
  formData.outboundNotificationNo = notification.notificationNo
  // 自动填充收货信息
  formData.consigneeName = notification.consigneeName
  formData.consigneePhone = notification.consigneePhone
  formData.consigneeAddress = notification.consigneeAddress
  // 加载明细
  loadNotificationDetails(notification.id)
}

const handleOutboundNotificationCancel = () => {
  // 通知单选择取消处理
}

const loadNotificationDetails = async (notificationId: number) => {
  try {
    // 这里应该调用API获取出库通知单的明细
    // 然后转换为发运明细格式
    formData.details = []
  } catch (error) {
    ElMessage.error('加载明细失败')
    console.error(error)
  }
}

const handleCarrierChange = () => {
  // 承运商变更时重新计算运费
  if (canCalculateCost.value) {
    handleCalculateShippingCost()
  }
}

const handleShippingMethodChange = () => {
  // 运输方式变更时重新计算运费
  if (canCalculateCost.value) {
    handleCalculateShippingCost()
  }
}

const handleCalculateShippingCost = async () => {
  if (!canCalculateCost.value) {
    ElMessage.warning('请先完善承运商、运输方式、重量和体积信息')
    return
  }
  
  try {
    const costData = {
      carrierId: formData.carrierId,
      shippingMethod: formData.shippingMethod,
      totalWeight: formData.totalWeight,
      totalVolume: formData.totalVolume,
      consigneeAddress: formData.consigneeAddress
    }
    
    const result = await shipmentStore.calculateShippingCost(costData)
    formData.shippingCost = result.shippingCost
    formData.insuranceFee = result.insuranceFee
    
    ElMessage.success('运费计算成功')
  } catch (error) {
    ElMessage.error('运费计算失败')
    console.error(error)
  }
}

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
}

const disabledDeliveryDate = (time: Date) => {
  const shipDate = formData.estimatedShipDate ? new Date(formData.estimatedShipDate) : new Date()
  return time.getTime() < shipDate.getTime()
}

const validateForm = async (): Promise<boolean> => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

const handleSave = async () => {
  if (!(await validateForm())) return
  
  submitting.value = true
  try {
    const submitData = {
      ...formData,
      details: formData.details.map(detail => ({
        itemId: detail.itemId,
        quantity: detail.quantity,
        weight: detail.weight,
        volume: detail.volume,
        packageType: detail.packageType,
        packageCount: detail.packageCount
      }))
    }
    
    if (mode.value === 'create') {
      await shipmentStore.create(submitData)
      ElMessage.success('保存成功')
    } else {
      await shipmentStore.update(recordId.value!, submitData)
      ElMessage.success('更新成功')
    }
    
    emit('confirm')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleSubmit = async () => {
  await handleSave()
  // 提交后可以自动进入下一个状态
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 暴露方法
const open = (openMode: 'create' | 'edit' | 'view', id?: number) => {
  mode.value = openMode
  recordId.value = id || null
  formData._mode = openMode
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.form-section {
  margin-bottom: 16px;
}

.form-section :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-with-button {
  display: flex;
  gap: 8px;
}

.input-with-button .el-input {
  flex: 1;
}

.dialog-footer {
  text-align: right;
}
</style>
