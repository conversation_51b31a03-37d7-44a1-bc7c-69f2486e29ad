<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="180"
      @refresh="loadData"
      @add="handleAdd"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
    >
      <template #column-isEnabled="{ row }">
        <el-tag :type="row.isEnabled ? 'success' : 'info'">
          {{ row.isEnabled ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEdit(row)" v-if="hasPermission('fin:taxrate:edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            @click="handleDelete(row)" 
            v-if="hasPermission('fin:taxrate:delete')"
          ></el-button>
        </el-tooltip>
      </template>

    </VNTable>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2"
        :label-width="'100px'"
        :loading="loading"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <template #actions> 
          <template v-if="isViewing">
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else> 
            <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
            <el-button @click="handleCloseDialog">取消</el-button>
          </template>
        </template>
      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton } from 'element-plus';
import { View, Edit, Delete } from '@element-plus/icons-vue';
import {
  getTaxRatePage,
  addTaxRate,
  updateTaxRate,
  deleteTaxRate,
  getTaxRateDetail,
} from '@/api/fin/taxRate';
import type { 
    TaxRateListItem,
    TaxRateFormData
} from '@/api/fin/taxRate';
import { hasPermission } from '@/hooks/usePermission';

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentRow = ref<TaxRateListItem | null>(null);
const isViewing = computed(() => formMode.value === 'view');

// --- Reactive State ---
const loading = ref(false);
const tableData = ref<TaxRateListItem[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const formData = ref<Partial<TaxRateFormData>>({});

const currentFilters = ref<Record<string, any>>({});
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);

const tableColumns = ref<TableColumn[]>([
  { prop: 'name', label: '名称', minWidth: 200, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'rate', label: '税率 (%)', width: 120, sortable: true, formatter: (row) => `${parseFloat(row.rate).toFixed(2)} %` },
  { prop: 'description', label: '说明', minWidth: 250, sortable: false, filterable: true, filterType: 'text', showOverflowTooltip: true },
  {
    prop: 'isEnabled', label: '状态', width: 90, slot: true,
    filterable: true, filterType: 'select',
    filterOptions: [{ label: '启用', value: true }, { label: '禁用', value: false }]
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    sortable: true,
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('fin:taxrate:add'),
  filter: true,
  columnSetting: true,
  density: true,
  fullscreen: true,
}));

// 辅助函数：校验百分比格式
const validateRate = (rule: any, value: any, callback: any) => {
  console.log('validateRate', rule);
  if (!value) {
    return callback(new Error('税率为必填项'));
  }
  const num = parseFloat(value);
  if (isNaN(num) || num < 0 || num > 100) {
    return callback(new Error('请输入 0-100 之间的有效数字'));
  }
  callback();
};


const formFields = computed<HeaderField[]>(() => [
  { field: 'name', label: '税率名称', rules: [{ required: true, message: '税率名称为必填项' }], disabled: isViewing.value, span: 24 },
  { field: 'rate', label: '税率 (%)', type: 'number', props: { min: 0, max: 100, precision: 2, placeholder: '请输入0-100之间的数字' }, rules: [{ validator: validateRate, trigger: 'blur' }], disabled: isViewing.value },
  { field: 'isEnabled', label: '状态', type: 'switch', defaultValue: true, disabled: isViewing.value },
  { field: 'description', label: '描述', type: 'textarea', rows: 3, span: 24, disabled: isViewing.value },
]);

const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增税率';
  if (formMode.value === 'edit') return '编辑税率';
  if (formMode.value === 'view') return '查看税率';
  return '税率管理';
});

onMounted(() => {
  loadData();
});

const loadData = async () => {
  loading.value = true;
  try {
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop) {
        const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
        sortString = `${currentSort.value.prop},${direction}`;
    }

    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...currentFilters.value,
      sort: sortString
    };

    const response = await getTaxRatePage(params);
    tableData.value = response.list;
    pagination.total = response.total;

  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: TaxRateListItem) => {
  formMode.value = mode;
  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id) return;
    currentRow.value = rowData;
    dialogVisible.value = true;
    loading.value = true;
    try {
        const detailData = await getTaxRateDetail(rowData.id);
        formData.value = { ...detailData };
    } catch (error) {
        const errorMessage = getApiErrorMessage(error);
        ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: `获取详情失败: ${errorMessage}`, showClose: true, duration: 5000 });
        formData.value = { ...rowData };
    } finally {
         loading.value = false;
    }
  } else {
    formData.value = {
      name: '',
      rate: '0.00',
      isEnabled: true,
      description: '',
    };
    currentRow.value = null;
    dialogVisible.value = true;
  }

  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentRow.value = null;
};

const submitForm = async () => {
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }
  
  const dataToSend = { ...formData.value };

  loading.value = true;
  try {
    if (formMode.value === 'add') {
      await addTaxRate(dataToSend as TaxRateFormData);
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentRow.value) {
      await updateTaxRate(currentRow.value.id, dataToSend);
      ElMessage.success('编辑成功');
    }
    handleCloseDialog();
    loadData();
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  } finally {
    loading.value = false;
  }
};

const handleView = (row: TaxRateListItem) => {
  handleOpenForm('view', row);
};

const handleAdd = () => {
  handleOpenForm('add');
};

const handleEdit = (row: TaxRateListItem) => {
  handleOpenForm('edit', row);
};

const handleDelete = async (row: TaxRateListItem) => {
  try {
    await ElMessageBox.confirm(`确定删除税率 "${row.name}" 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    await deleteTaxRate(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      const errorMessage = getApiErrorMessage(error);
      ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

const handleFilterChange = (filters: Record<string, any>) => {
    currentFilters.value = filters;
    pagination.currentPage = 1; 
    loadData(); 
};

const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

const handleSortChange = (sort: { prop: string; order: 'ascending' | 'descending' | null }) => {
  currentSort.value = sort;
  loadData(); 
};

// --- 辅助函数：格式化日期时间 ---
const formatDateTime = (dateStr: string | null | undefined): string => {
  if (!dateStr) return '';
  if (dateStr.startsWith('0001-01-01')) {
    return '-'; 
  }
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) { 
        console.warn('formatDateTime received invalid date string after initial check:', dateStr);
        return '-';
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); 
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error('Error formatting date:', dateStr, '\nError:', e);
    return '-';
  }
};

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

</script> 