package vo

import (
	"time"
)

// WmsInboundNotificationVO 入库通知单详细视图对象
type WmsInboundNotificationVO struct {
	ID                     uint                             `json:"id"`
	CreatedAt              time.Time                        `json:"createdAt"`
	UpdatedAt              time.Time                        `json:"updatedAt"`
	CreatedBy              uint                             `json:"createdBy"`
	UpdatedBy              uint                             `json:"updatedBy"`
	TenantID               uint                             `json:"tenantId"`
	AccountBookID          uint                             `json:"accountBookId"`
	NotificationNo         string                           `json:"notificationNo"`
	NotificationType       string                           `json:"notificationType"`
	WarehouseID            uint                             `json:"warehouseId"`
	ClientID               uint                             `json:"clientId"`
	SourceDocNo            *string                          `json:"sourceDocNo,omitempty"`
	SupplierShipper        *string                          `json:"supplierShipper,omitempty"`
	ExpectedArrivalDateStr *string                          `json:"expectedArrivalDate,omitempty"`
	Status                 string                           `json:"status"`
	Remark                 *string                          `json:"remark,omitempty"`
	ClientName             *string                          `json:"clientName,omitempty"`    // 扩展列 客户/货主名称
	WarehouseName          *string                          `json:"warehouseName,omitempty"` // 扩展列 仓库名称
	SupplierName           *string                          `json:"supplierName,omitempty"`  // 扩展列 供应商名称
	Details                []WmsInboundNotificationDetailVO `json:"details,omitempty"`       // 关联对象
}

// WmsInboundNotificationSimpleVO 入库通知单简单视图对象（用于列表显示）
type WmsInboundNotificationSimpleVO struct {
	ID                     uint      `json:"id"`
	NotificationNo         string    `json:"notificationNo"`
	NotificationType       string    `json:"notificationType"`
	ClientID               uint      `json:"clientId"`
	SupplierShipper        *string   `json:"supplierShipper,omitempty"`
	ExpectedArrivalDateStr *string   `json:"expectedArrivalDate,omitempty"`
	Status                 string    `json:"status"`
	CreatedAt              time.Time `json:"createdAt"`
}

// WmsInboundNotificationDetailVO 入库通知单明细视图对象
type WmsInboundNotificationDetailVO struct {
	ID                uint      `json:"id"`
	CreatedAt         time.Time `json:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt"`
	TenantID          uint      `json:"tenantId"`
	AccountBookID     uint      `json:"accountBookId"`
	NotificationID    uint      `json:"notificationId"`
	LineNo            int       `json:"lineNo"`
	ItemID            uint      `json:"itemId"`
	ExpectedQuantity  float64   `json:"expectedQuantity"`
	UnitOfMeasure     string    `json:"unitOfMeasure"`
	BatchNo           *string   `json:"batchNo,omitempty"`
	PackageQty        float64   `json:"packageQty"`
	PackageUnit       string    `json:"packageUnit"`
	ProductionDateStr *string   `json:"productionDate,omitempty"`
	ExpiryDateStr     *string   `json:"expiryDate,omitempty"`
	Remark            *string   `json:"remark,omitempty"`

	// 关联物料信息
	ItemSku           *string `json:"itemSku,omitempty"`
	ItemName          *string `json:"itemName,omitempty"`
	ItemSpecification *string `json:"itemSpecification,omitempty"`
}

// WmsInboundNotificationPageResult 入库通知单分页结果
type WmsInboundNotificationPageResult = PageResult[WmsInboundNotificationSimpleVO]
