package entity

import (
	"math"
	"time"

	"gorm.io/gorm"
)

// WmsInventoryAdjustmentType 库存调整类型枚举
type WmsInventoryAdjustmentType string

const (
	AdjustmentTypeIncrease     WmsInventoryAdjustmentType = "INCREASE"      // 增加
	AdjustmentTypeDecrease     WmsInventoryAdjustmentType = "DECREASE"      // 减少
	AdjustmentTypeStatusChange WmsInventoryAdjustmentType = "STATUS_CHANGE" // 状态变更
)

// WmsInventoryAdjustmentStatus 库存调整审批状态枚举
type WmsInventoryAdjustmentStatus string

const (
	AdjustmentStatusPending  WmsInventoryAdjustmentStatus = "PENDING"  // 待审批
	AdjustmentStatusApproved WmsInventoryAdjustmentStatus = "APPROVED" // 已审批
	AdjustmentStatusRejected WmsInventoryAdjustmentStatus = "REJECTED" // 已拒绝
	AdjustmentStatusExecuted WmsInventoryAdjustmentStatus = "EXECUTED" // 已执行
)

// WmsInventoryAdjustment 对应数据库中的 wms_inventory_adjustment 表
// 存储库存调整记录信息
type WmsInventoryAdjustment struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	AdjustmentNo      string                       `gorm:"column:adjustment_no;size:50;not null;uniqueIndex:idx_adjustment_no_account;comment:调整单号" json:"adjustmentNo"`
	InventoryID       uint                         `gorm:"column:inventory_id;not null;comment:库存ID" json:"inventoryId"`
	AdjustmentType    WmsInventoryAdjustmentType   `gorm:"column:adjustment_type;type:varchar(20);not null;comment:调整类型" json:"adjustmentType"`
	QuantityBefore    float64                      `gorm:"column:quantity_before;type:numeric(12,4);not null;comment:调整前数量" json:"quantityBefore"`
	QuantityAfter     float64                      `gorm:"column:quantity_after;type:numeric(12,4);not null;comment:调整后数量" json:"quantityAfter"`
	QuantityChange    float64                      `gorm:"column:quantity_change;type:numeric(12,4);not null;comment:调整数量" json:"quantityChange"`
	StatusBefore      *string                      `gorm:"column:status_before;type:varchar(20);comment:调整前状态" json:"statusBefore"`
	StatusAfter       *string                      `gorm:"column:status_after;type:varchar(20);comment:调整后状态" json:"statusAfter"`
	ReasonCode        *string                      `gorm:"column:reason_code;type:varchar(50);comment:原因代码" json:"reasonCode"`
	ReasonDescription *string                      `gorm:"column:reason_description;type:text;comment:原因描述" json:"reasonDescription"`
	ApprovalStatus    WmsInventoryAdjustmentStatus `gorm:"column:approval_status;type:varchar(20);default:'PENDING';comment:审批状态" json:"approvalStatus"`
	ApprovedBy        *uint                        `gorm:"column:approved_by;comment:审批人ID" json:"approvedBy"`
	ApprovedAt        *time.Time                   `gorm:"column:approved_at;comment:审批时间" json:"approvedAt"`
	OperatorID        uint                         `gorm:"column:operator_id;not null;comment:操作员ID" json:"operatorId"`

	// 关联关系
	Inventory *WmsInventory `gorm:"foreignKey:InventoryID;references:ID" json:"inventory,omitempty"`
	Operator  *User         `gorm:"foreignKey:OperatorID;references:ID" json:"operator,omitempty"`
	Approver  *User         `gorm:"foreignKey:ApprovedBy;references:ID" json:"approver,omitempty"`
}

// TableName 指定数据库表名方法
func (WmsInventoryAdjustment) TableName() string {
	return "wms_inventory_adjustment"
}

// BeforeCreate GORM钩子：创建前处理
func (w *WmsInventoryAdjustment) BeforeCreate(tx *gorm.DB) error {
	// 编码生成现在由service层处理，这里不再需要生成编码
	return nil
}

// IsApprovalRequired 判断是否需要审批
func (w *WmsInventoryAdjustment) IsApprovalRequired() bool {
	// 根据业务规则判断是否需要审批
	// 例如：数量变化超过一定阈值需要审批
	return math.Abs(w.QuantityChange) > 100 // 示例：变化量超过100需要审批
}

// CanExecute 判断是否可以执行调整
func (w *WmsInventoryAdjustment) CanExecute() bool {
	if w.IsApprovalRequired() {
		return w.ApprovalStatus == AdjustmentStatusApproved
	}
	return w.ApprovalStatus == AdjustmentStatusPending
}

// GetAdjustmentTypeName 获取调整类型中文名称
func (w *WmsInventoryAdjustment) GetAdjustmentTypeName() string {
	switch w.AdjustmentType {
	case AdjustmentTypeIncrease:
		return "库存增加"
	case AdjustmentTypeDecrease:
		return "库存减少"
	case AdjustmentTypeStatusChange:
		return "状态变更"
	default:
		return "未知类型"
	}
}

// GetApprovalStatusName 获取审批状态中文名称
func (w *WmsInventoryAdjustment) GetApprovalStatusName() string {
	switch w.ApprovalStatus {
	case AdjustmentStatusPending:
		return "待审批"
	case AdjustmentStatusApproved:
		return "已审批"
	case AdjustmentStatusRejected:
		return "已拒绝"
	case AdjustmentStatusExecuted:
		return "已执行"
	default:
		return "未知状态"
	}
}
