package service

import (
	"context"
	stdErrors "errors"
	"fmt"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// FiscalPeriodService 定义了会计期间服务需要实现的接口。
// 所有方法都接收 context.Context作为第一个参数，以支持链路追踪和上下文信息传递。
type FiscalPeriodService interface {
	// GenerateForYear 为指定年份生成12个月的会计期间。
	GenerateForYear(ctx context.Context, dto dto.FiscalPeriodGenerateDTO) error
	// ChangeStatus 更改指定会计期间的状态。
	ChangeStatus(ctx context.Context, id uint64, dto dto.UpdateFiscalPeriodStatusDTO) error
	// List 根据查询条件分页列出会计期间。
	List(ctx context.Context, query dto.FiscalPeriodQueryDTO) (*response.PageResult, error)
}

// fiscalPeriodServiceImpl 是 FiscalPeriodService 的具体实现。
// 它嵌入了 BaseServiceImpl 以复用公共服务逻辑，如日志记录和从上下文中获取用户信息。
type fiscalPeriodServiceImpl struct {
	*BaseServiceImpl
	repo repository.FiscalPeriodRepository
}

// NewFiscalPeriodService 创建一个新的会计期间服务实例。
// 它遵循服务管理器的标准工厂模式，接收一个 *ServiceManager 作为参数。
func NewFiscalPeriodService(sm *ServiceManager) FiscalPeriodService {
	return &fiscalPeriodServiceImpl{
		BaseServiceImpl: NewBaseService(sm),
		repo:            sm.GetRepositoryManager().GetFiscalPeriodRepository(),
	}
}

// GenerateForYear 为指定年份生成会计期间。
// 它从上下文中获取创建者ID，并依赖GORM钩子来自动填充实体的创建者和更新者信息。
func (s *fiscalPeriodServiceImpl) GenerateForYear(ctx context.Context, dto dto.FiscalPeriodGenerateDTO) error {
	// 强制从上下文获取账套ID
	accountBookID, err := s.GetAccountBookIDFromContext(ctx)
	if err != nil {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息，会计期间和账套是强绑定的").WithCause(err)
	}
	if accountBookID == 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空，会计期间和账套是强绑定的")
	}

	// 1. 检查财务年度标签是否已存在
	exists, err := s.repo.CheckYearExists(ctx, dto.FiscalYear)
	if err != nil {
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "检查财务年度失败")
	}
	if exists {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_CONFLICT, fmt.Sprintf("财务年度 %d 已存在，无法重复生成", dto.FiscalYear))
	}

	// 2. 验证起始日期的有效性
	startDate, err := time.Parse("2006-1-2", fmt.Sprintf("%d-%d-%d", dto.StartYear, dto.StartMonth, dto.StartDay))
	if err != nil || startDate.Day() != dto.StartDay {
		// 如果解析失败或日期发生了溢出（例如，输入了2月30日），则视为无效
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("无效的开始日期：%d-%d-%d", dto.StartYear, dto.StartMonth, dto.StartDay))
	}

	// 3. 生成12个会计期间
	var periods []*entity.FiscalPeriod
	currentStartDate := startDate
	for month := 1; month <= 12; month++ {
		// 计算结束日期：下个月的同一天减1天
		endDate := currentStartDate.AddDate(0, 1, 0).AddDate(0, 0, -1)

		period := &entity.FiscalPeriod{
			Code:        fmt.Sprintf("%d%02d", dto.FiscalYear, month),
			FiscalYear:  dto.FiscalYear,
			FiscalMonth: month,
			StartDate:   currentStartDate,
			EndDate:     endDate,
			Status:      "NEVER_OPENED",
			// 注意：CreatorID 和 UpdaterID 等审计字段由 BaseEntity 的 GORM 钩子自动填充
		}
		// 设置账套ID
		period.AccountBookID = uint(accountBookID)

		periods = append(periods, period)

		// 更新下一个期间的开始日期
		currentStartDate = endDate.AddDate(0, 0, 1)
	}

	// 4. 批量创建
	err = s.repo.CreateBatch(ctx, periods)
	if err != nil {
		return apperrors.WrapError(err, apperrors.CODE_DATA_CREATE_FAILED, "生成会计期间失败")
	}
	return nil
}

// ChangeStatus 更改会计期间的状态。
// 它从上下文中获取当前操作的用户ID作为更新者ID。
func (s *fiscalPeriodServiceImpl) ChangeStatus(ctx context.Context, id uint64, dto dto.UpdateFiscalPeriodStatusDTO) error {
	updaterID, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		return err // 如果无法获取用户ID，则返回错误
	}

	period, err := s.repo.FindByID(ctx, id)
	if err != nil {
		if stdErrors.Is(err, gorm.ErrRecordNotFound) {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_NOT_FOUND, "帐期不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询帐期信息失败")
	}

	// 业务规则校验
	if period.Status == "CLOSED" && dto.Status == "OPEN" {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_UPDATE_FAILED, "已关闭的帐期不能重新打开")
	}
	if period.Status == "NEVER_OPENED" && dto.Status == "CLOSED" {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_UPDATE_FAILED, "未启用的帐期不能直接关闭")
	}
	if period.Status == dto.Status {
		return apperrors.NewBusinessError(apperrors.CODE_DATA_UPDATE_FAILED, fmt.Sprintf("帐期已经是 %s 状态", dto.Status))
	}

	err = s.repo.UpdateStatus(ctx, id, dto.Status, updaterID)
	if err != nil {
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新帐期状态失败")
	}
	return nil
}

// List 根据查询条件分页列出会计期间。
// 此方法体现了分层架构的最佳实践：服务层负责构建查询条件，仓库层负责执行查询。
func (s *fiscalPeriodServiceImpl) List(ctx context.Context, query dto.FiscalPeriodQueryDTO) (*response.PageResult, error) {
	// 1. 根据查询DTO构建QueryCondition切片
	var conditions []repository.QueryCondition
	if query.FiscalYear != nil {
		conditions = append(conditions, repository.NewEqualCondition("fiscal_year", *query.FiscalYear))
	}
	if query.Status != "" {
		conditions = append(conditions, repository.NewEqualCondition("status", query.Status))
	}

	// 2. 调用仓库层的FindByPage方法
	pageResult, err := s.repo.FindByPage(ctx, &query.Pagination, conditions)
	if err != nil {
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询帐期列表失败")
	}

	// 3. 将查询到的实体列表（entity.FiscalPeriod）转换为视图对象列表（vo.FiscalPeriodVO）
	entityList, ok := pageResult.List.([]*entity.FiscalPeriod)
	if !ok {
		// 如果发生此错误，通常表示代码逻辑有问题
		s.GetLogger().Error("帐期数据类型转换失败", "context", ctx)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "帐期数据类型转换失败")
	}

	voList := make([]*vo.FiscalPeriodVO, 0, len(entityList))
	for _, p := range entityList {
		voList = append(voList, &vo.FiscalPeriodVO{
			ID:          uint64(p.ID),
			Code:        p.Code,
			FiscalYear:  p.FiscalYear,
			FiscalMonth: p.FiscalMonth,
			StartDate:   p.StartDate.Format("2006-01-02"),
			EndDate:     p.EndDate.Format("2006-01-02"),
			Status:      p.Status,
			CreatedAt:   p.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	pageResult.List = voList
	return pageResult, nil
}
