# VNChart 组件

一个基于 Apache ECharts 的可重用图表组件，用于在 Vue 应用中方便地渲染各种图表。

## 依赖

*   `echarts`: 核心库，需要手动安装 (`npm install echarts` 或 `yarn add echarts`)。
*   `@vueuse/core`: 用于窗口大小调整的防抖 (`npm install @vueuse/core` 或 `yarn add @vueuse/core`)。

## Props

| Prop Name  | Type                       | Description                           | Default     |
| ---------- | -------------------------- | ------------------------------------- | ----------- |
| `option`   | `EChartsOption`            | ECharts 图表配置项，**必须提供**。     | (无)        |
| `width`    | `string`                   | 可选，图表容器宽度 (CSS 长度值)      | `'100%'`    |
| `height`   | `string`                   | 可选，图表容器高度 (CSS 长度值)      | `'400px'`   |
| `theme`    | `string \| object`          | 可选，ECharts 主题名称或主题对象      | (无)        |
| `renderer` | `'canvas' \| 'svg'`        | 可选，渲染器类型                    | `'canvas'`  |

**注意:** `EChartsOption` 类型已从此组件的 `types.ts` 中导出，方便在父组件中进行类型提示。

## 按需引入

为了优化性能和减小打包体积，此组件默认采用 ECharts 的按需引入方式。当前已引入并注册了常用的图表和组件：

*   **图表:** `BarChart`, `LineChart`, `PieChart`
*   **组件:** `TitleComponent`, `TooltipComponent`, `GridComponent`, `LegendComponent`
*   **渲染器:** `CanvasRenderer`

如果需要使用 **其他图表类型或组件** (例如地图 `MapChart`、数据缩放 `DataZoomComponent` 等)，您需要：

1.  修改 `VNChart/index.vue` 文件。
2.  从 `echarts/charts` 或 `echarts/components` 中导入所需的模块。
3.  在 `echarts.use([...])` 数组中注册它们。

## 使用示例

```vue
<template>
  <div>
    <VNChart :option="chartOption" height="300px" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNChart from '@/components/VNChart/index.vue';
import type { EChartsOption } from '@/components/VNChart/types'; // 导入类型

const chartOption = ref<EChartsOption>({
  title: {
    text: '示例图表'
  },
  tooltip: {},
  legend: {
    data:['销量']
  },
  xAxis: {
    data: ["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子"]
  },
  yAxis: {},
  series: [{
    name: '销量',
    type: 'bar', // 使用已引入的 bar chart
    data: [5, 20, 36, 10, 10, 20]
  }]
});
</script>
```

## 注意事项

*   确保在使用此组件前已正确安装 `echarts` 和 `@vueuse/core`。
*   `option` prop 是响应式的 (deep watch)，当父组件中的 `option` 对象发生变化时，图表会自动更新。
*   组件会自动监听窗口大小变化并调用 ECharts 的 `resize` 方法以实现图表自适应。
*   如果需要获取 ECharts 实例进行更高级的操作，可以取消 `index.vue` 中 `defineExpose({ chartInstance });` 的注释，并通过 `ref` 在父组件中访问。 