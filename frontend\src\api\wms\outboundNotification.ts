import request from '@/utils/request'

// ==================== 类型定义 ====================

// 出库通知单状态枚举
export type WmsOutboundNotificationStatus = 
  | 'DRAFT'           // 草稿
  | 'PLANNED'         // 已计划
  | 'APPROVED'        // 已审核
  | 'ALLOCATING'      // 库存分配中
  | 'ALLOCATED'       // 已分配
  | 'PICKING'         // 拣货中
  | 'PICKED'          // 已拣货
  | 'SHIPPED'         // 已发运
  | 'COMPLETED'       // 已完成
  | 'CANCELLED'       // 已取消

// 优先级枚举
export type WmsOutboundNotificationPriority = 1 | 2 | 3 | 4 | 5

// 出库通知单响应类型
export interface WmsOutboundNotificationResp {
  id: number
  notificationNo: string
  clientId: number
  clientName?: string
  clientOrderNo?: string
  warehouseId: number
  warehouseName?: string
  requiredShipDate?: string
  priority: WmsOutboundNotificationPriority
  consigneeName?: string
  consigneePhone?: string
  consigneeAddress?: string
  carrierId?: number
  carrierName?: string
  shippingMethod?: string
  status: WmsOutboundNotificationStatus
  remark?: string
  details?: WmsOutboundNotificationDetailResp[]
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
}

// 出库通知单明细响应类型
export interface WmsOutboundNotificationDetailResp {
  id: number
  lineNo: number
  itemId: number
  itemCode?: string
  itemName?: string
  itemSpec?: string
  requiredQty: number
  allocatedQty?: number
  pickedQty?: number
  shippedQty?: number
  unitOfMeasure: string
  requiredBatchNo?: string
  requiredProductionDate?: string
  requiredExpiryDate?: string
  remark?: string
}

// 出库通知单创建请求类型
export interface WmsOutboundNotificationCreateReq {
  clientId: number
  clientOrderNo?: string
  warehouseId: number
  requiredShipDate?: string
  priority: WmsOutboundNotificationPriority
  consigneeName?: string
  consigneePhone?: string
  consigneeAddress?: string
  carrierId?: number
  shippingMethod?: string
  remark?: string
  details: WmsOutboundNotificationDetailCreateReq[]
}

// 出库通知单明细创建请求类型
export interface WmsOutboundNotificationDetailCreateReq {
  lineNo: number
  itemId: number
  requiredQty: number
  unitOfMeasure: string
  requiredBatchNo?: string
  requiredProductionDate?: string
  requiredExpiryDate?: string
  remark?: string
}

// 出库通知单更新请求类型
export interface WmsOutboundNotificationUpdateReq {
  clientId?: number
  clientOrderNo?: string
  warehouseId?: number
  requiredShipDate?: string
  priority?: WmsOutboundNotificationPriority
  consigneeName?: string
  consigneePhone?: string
  consigneeAddress?: string
  carrierId?: number
  shippingMethod?: string
  remark?: string
  details?: WmsOutboundNotificationDetailCreateReq[]
}

// 出库通知单查询请求类型
export interface WmsOutboundNotificationQueryReq {
  pageNum?: number
  pageSize?: number
  notificationNo?: string
  clientId?: number
  clientOrderNo?: string
  warehouseId?: number
  status?: WmsOutboundNotificationStatus
  priority?: WmsOutboundNotificationPriority
  requiredShipDateStart?: string
  requiredShipDateEnd?: string
  consigneeName?: string
  carrierId?: number
  createdBy?: number
  createdAtStart?: string
  createdAtEnd?: string
}

// 状态更新请求类型
export interface WmsOutboundNotificationUpdateStatusReq {
  status: WmsOutboundNotificationStatus
  remark?: string
}

// 分页响应类型
export interface WmsOutboundNotificationPageResp {
  list: WmsOutboundNotificationResp[]
  total: number
}

// 客户响应类型
export interface WmsClientResp {
  id: number
  clientCode: string
  clientName: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  address?: string
  status: string
}

// 仓库响应类型
export interface WmsWarehouseResp {
  id: number
  warehouseCode: string
  warehouseName: string
  address?: string
  status: string
}

// 物料响应类型
export interface WmsItemResp {
  id: number
  itemCode: string
  itemName: string
  itemSpec?: string
  unitOfMeasure: string
  category?: string
  status: string
}

// 承运商响应类型
export interface WmsCarrierResp {
  id: number
  carrierCode: string
  carrierName: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  address?: string
  serviceRating?: number
  isActive: boolean
}

// 客户查询请求类型
export interface WmsClientQueryReq {
  pageNum?: number
  pageSize?: number
  clientCode?: string
  clientName?: string
  status?: string
}

// 仓库查询请求类型
export interface WmsWarehouseQueryReq {
  pageNum?: number
  pageSize?: number
  warehouseCode?: string
  warehouseName?: string
  status?: string
}

// 物料查询请求类型
export interface WmsItemQueryReq {
  pageNum?: number
  pageSize?: number
  itemCode?: string
  itemName?: string
  category?: string
  status?: string
}

// 承运商查询请求类型
export interface WmsCarrierQueryReq {
  pageNum?: number
  pageSize?: number
  carrierCode?: string
  carrierName?: string
  isActive?: boolean
}

// ==================== API接口 ====================

// 出库通知单基础CRUD接口
export const getOutboundNotificationPage = (params: WmsOutboundNotificationQueryReq): Promise<WmsOutboundNotificationPageResp> => {
  return request<WmsOutboundNotificationPageResp>({
    url: '/wms/outbound-notifications/page',
    method: 'get',
    params
  }) as unknown as Promise<WmsOutboundNotificationPageResp>
}

export const getOutboundNotificationById = (id: number): Promise<WmsOutboundNotificationResp> => {
  return request<WmsOutboundNotificationResp>({
    url: `/wms/outbound-notifications/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsOutboundNotificationResp>
}

export const createOutboundNotification = (data: WmsOutboundNotificationCreateReq): Promise<WmsOutboundNotificationResp> => {
  return request<WmsOutboundNotificationResp>({
    url: '/wms/outbound-notifications',
    method: 'post',
    data
  }) as unknown as Promise<WmsOutboundNotificationResp>
}

export const updateOutboundNotification = (id: number, data: WmsOutboundNotificationUpdateReq): Promise<WmsOutboundNotificationResp> => {
  return request<WmsOutboundNotificationResp>({
    url: `/wms/outbound-notifications/${id}`,
    method: 'put',
    data
  }) as unknown as Promise<WmsOutboundNotificationResp>
}

export const deleteOutboundNotification = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/outbound-notifications/${id}`,
    method: 'delete'
  }) as unknown as Promise<void>
}

// 状态管理接口
export const updateOutboundNotificationStatus = (id: number, data: WmsOutboundNotificationUpdateStatusReq): Promise<void> => {
  return request<void>({
    url: `/wms/outbound-notifications/${id}/status`,
    method: 'put',
    data
  }) as unknown as Promise<void>
}

export const approveOutboundNotification = (id: number, data: { remark?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/outbound-notifications/${id}/approve`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const cancelOutboundNotification = (id: number, data: { reason?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/outbound-notifications/${id}/cancel`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 库存分配接口
export const allocateInventory = (id: number, data: { strategy?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/outbound-notifications/${id}/allocate`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const releaseInventoryAllocation = (id: number, data: { reason?: string }): Promise<void> => {
  return request<void>({
    url: `/wms/outbound-notifications/${id}/release-allocation`,
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 拣货任务生成接口
export const generatePickingTask = (id: number, data: { strategy?: string }): Promise<{ taskId: number; taskNo: string }> => {
  return request<{ taskId: number; taskNo: string }>({
    url: `/wms/outbound-notifications/${id}/generate-picking-task`,
    method: 'post',
    data
  }) as unknown as Promise<{ taskId: number; taskNo: string }>
}

// 批量操作接口
export const batchDeleteOutboundNotifications = (ids: number[]): Promise<void> => {
  return request<void>({
    url: '/wms/outbound-notifications/batch-delete',
    method: 'delete',
    data: { ids }
  }) as unknown as Promise<void>
}

export const batchUpdateOutboundNotificationStatus = (data: { ids: number[]; status: WmsOutboundNotificationStatus; remark?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/outbound-notifications/batch-status',
    method: 'put',
    data
  }) as unknown as Promise<void>
}

export const batchApproveOutboundNotifications = (data: { ids: number[]; remark?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/outbound-notifications/batch-approve',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

export const batchAllocateInventory = (data: { ids: number[]; strategy?: string }): Promise<void> => {
  return request<void>({
    url: '/wms/outbound-notifications/batch-allocate',
    method: 'post',
    data
  }) as unknown as Promise<void>
}

// 导入导出接口
export const exportOutboundNotifications = (params: WmsOutboundNotificationQueryReq): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/outbound-notifications/export',
    method: 'get',
    params,
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

export const downloadOutboundNotificationTemplate = (): Promise<Blob> => {
  return request<Blob>({
    url: '/wms/outbound-notifications/import-template',
    method: 'get',
    responseType: 'blob'
  }) as unknown as Promise<Blob>
}

export const importOutboundNotifications = (file: File): Promise<{ successCount: number; failureCount: number; errors?: string[] }> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request<{ successCount: number; failureCount: number; errors?: string[] }>({
    url: '/wms/outbound-notifications/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }) as unknown as Promise<{ successCount: number; failureCount: number; errors?: string[] }>
}

// 关联数据查询接口
export const getClientPage = (params: WmsClientQueryReq): Promise<{ list: WmsClientResp[]; total: number }> => {
  return request<{ list: WmsClientResp[]; total: number }>({
    url: '/wms/clients/page',
    method: 'get',
    params
  }) as unknown as Promise<{ list: WmsClientResp[]; total: number }>
}

export const getWarehouseList = (params?: WmsWarehouseQueryReq): Promise<WmsWarehouseResp[]> => {
  return request<WmsWarehouseResp[]>({
    url: '/wms/warehouses/list',
    method: 'get',
    params
  }) as unknown as Promise<WmsWarehouseResp[]>
}

export const getItemPage = (params: WmsItemQueryReq): Promise<{ list: WmsItemResp[]; total: number }> => {
  return request<{ list: WmsItemResp[]; total: number }>({
    url: '/wms/items/page',
    method: 'get',
    params
  }) as unknown as Promise<{ list: WmsItemResp[]; total: number }>
}

export const getCarrierPage = (params: WmsCarrierQueryReq): Promise<{ list: WmsCarrierResp[]; total: number }> => {
  return request<{ list: WmsCarrierResp[]; total: number }>({
    url: '/wms/carriers/page',
    method: 'get',
    params
  }) as unknown as Promise<{ list: WmsCarrierResp[]; total: number }>
}
