{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.4", "codemirror": "^5.65.19", "codemirror-editor-vue3": "^2.8.0", "echarts": "^5.6.0", "element-plus": "^2.9.7", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/codemirror": "^5.60.15", "@types/lodash-es": "^4.17.12", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "lodash-es": "^4.17.21", "sass": "^1.86.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}