<template>
  <div class="vn-tree-wrapper">
    <el-tree
      ref="treeRef" 
      :data="data"
      :node-key="nodeKeyComputed"
      :props="treePropsComputed"
      :show-checkbox="showCheckbox"
      :default-expand-all="defaultExpandAll"
      :expand-on-click-node="expandOnClickNode"
      :check-on-click-node="checkOnClickNode"
      :check-strictly="checkStrictly"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="defaultCheckedKeys"
      :current-node-key="currentNodeKey"
      :filter-node-method="filterNodeMethodComputed"
      :accordion="accordion"
      :indent="indent"
      :icon="icon"
      :lazy="!!lazyLoad" 
      :load="lazyLoadInternal" 
      :draggable="draggable"
      :allow-drag="allowDrag"
      :allow-drop="allowDrop"
      @node-click="handleNodeClick"
      @node-contextmenu="handleNodeContextmenu"
      @check-change="handleCheckChange"
      @check="handleCheck"
      @current-change="handleCurrentChange"
      @node-expand="handleNodeExpand"
      @node-collapse="handleNodeCollapse"
      v-bind="$attrs" 
    >
      <!-- Default slot for custom node content -->
      <template #default="{ node, data: nodeData }">
        <slot :node="node" :data="nodeData">
          <!-- Default node rendering -->
          <span class="custom-tree-node">{{ node.label }}</span>
        </slot>
      </template>

      <!-- Empty slot -->
       <template #empty v-if="!data || data.length === 0">
         <slot name="empty">
            <span class="el-tree__empty-text">暂无数据</span>
         </slot>
       </template>

    </el-tree>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, defineProps, defineEmits, withDefaults, defineExpose } from 'vue';
import { ElTree } from 'element-plus';
import type { TreeNode, VNTreeEmits, VNTreeInstance } from './types';
import type { TreeOptionProps, AllowDropType, NodeDropType, Node } from 'element-plus/es/components/tree/src/tree.type';
import type { PropType, Component } from 'vue';

const props = defineProps({
    data: {
        type: Array as PropType<TreeNode[]>,
        required: true,
    },
    nodeKey: {
        type: String,
        default: 'id',
    },
    treeProps: {
        type: Object as PropType<TreeOptionProps>,
        default: () => ({
            label: 'label',
            children: 'children',
            disabled: 'disabled',
            isLeaf: 'isLeaf'
        }),
    },
    showCheckbox: {
        type: Boolean,
        default: false,
    },
    defaultExpandAll: {
        type: Boolean,
        default: false,
    },
    filterText: String,
    lazyLoad: {
        type: Function as PropType<(node: Node, resolve: (data: TreeNode[]) => void) => void>,
    },
    expandOnClickNode: {
        type: Boolean,
        default: true,
    },
    checkOnClickNode: {
        type: Boolean,
        default: false,
    },
    checkStrictly: {
        type: Boolean,
        default: false,
    },
    defaultExpandedKeys: {
        type: Array as PropType<(string | number)[]>,
        default: () => [],
    },
    defaultCheckedKeys: {
         type: Array as PropType<(string | number)[]>,
         default: () => [],
    },
    currentNodeKey: [String, Number],
    accordion: {
        type: Boolean,
        default: false,
    },
    indent: {
        type: Number,
        default: 16,
    },
    icon: [String, Object] as PropType<string | Component>,
    draggable: {
        type: Boolean,
        default: false,
    },
    allowDrag: {
        type: Function as PropType<(draggingNode: Node) => boolean>
    },
    allowDrop: {
        type: Function as PropType<(draggingNode: Node, dropNode: Node, type: AllowDropType) => boolean>
    },
});

const emit = defineEmits<VNTreeEmits>();

const treeRef = ref<VNTreeInstance | null>(null);

// --- Computed Properties for Mapping --- 
const nodeKeyComputed = computed(() => props.nodeKey || 'id');
const treePropsComputed = computed<TreeOptionProps>(() => {
    return {
        label: props.treeProps?.label ?? 'label',
        children: props.treeProps?.children ?? 'children',
        disabled: props.treeProps?.disabled ?? 'disabled',
        isLeaf: props.treeProps?.isLeaf ?? 'isLeaf'
    }
});

// --- Filtering --- 
const defaultFilterNodeMethod = (value: string, data: TreeNode): boolean => {
  if (!value) return true;
  const labelKey = treePropsComputed.value.label as string || 'label';
  return data[labelKey]?.toLowerCase().includes(value.toLowerCase());
};

const filterNodeMethodComputed = computed(() => {
    return defaultFilterNodeMethod; 
});

watch(() => props.filterText, (val) => {
  treeRef.value?.filter(val);
});

// --- Lazy Loading --- 
const lazyLoadInternal = (node: Node, resolve: (data: TreeNode[]) => void) => {
    if (props.lazyLoad) {
        props.lazyLoad(node, resolve);
    }
};

// --- Event Handlers (Forwarding) --- 
const handleNodeClick = (data: TreeNode, node: any, event: MouseEvent) => {
  emit('node-click', data, node, event);
};
const handleNodeContextmenu = (event: Event, data: TreeNode, node: any) => {
  emit('node-contextmenu', event, data, node);
};
const handleCheckChange = (data: TreeNode, checked: boolean, indeterminate: boolean) => {
  emit('check-change', data, checked, indeterminate);
};
const handleCheck = (data: TreeNode, checkedInfo: any) => {
  emit('check', data, checkedInfo);
};
const handleCurrentChange = (data: TreeNode, node: any) => {
  emit('current-change', data, node);
};
const handleNodeExpand = (data: TreeNode, node: any) => {
  emit('node-expand', data, node);
};
const handleNodeCollapse = (data: TreeNode, node: any) => {
  emit('node-collapse', data, node);
};

// --- Expose ElTree Instance Methods --- 
const getTreeInstance = (): VNTreeInstance | null => treeRef.value;

defineExpose<{ getTreeInstance: () => VNTreeInstance | null; }>({ getTreeInstance });

</script>

<style scoped>
.vn-tree-wrapper {
  /* Add wrapper styles if needed, e.g., border, padding */
  /* border: 1px solid #eee; */
}

/* Default slot styling example */
.custom-tree-node {
  flex-grow: 1; 
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

/* Style for empty state */
.el-tree__empty-text {
    color: var(--el-text-color-secondary);
    font-size: 14px;
}
</style> 