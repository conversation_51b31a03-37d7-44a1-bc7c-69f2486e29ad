package service

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/util"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsReceivingRecordService 定义收货记录服务接口
type WmsReceivingRecordService interface {
	BaseService
	Create(ctx context.Context, req *dto.WmsReceivingRecordCreateReq) (*vo.WmsReceivingRecordVO, error)
	CreateFromNotification(ctx context.Context, notificationID uint) (*vo.WmsReceivingRecordVO, error)
	CreateBlindReceiving(ctx context.Context, req *dto.WmsBlindReceivingCreateReq) (*vo.WmsReceivingRecordVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsReceivingRecordUpdateReq) (*vo.WmsReceivingRecordVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsReceivingRecordVO, error)
	GetPage(ctx context.Context, req *dto.WmsReceivingRecordQueryReq) (*vo.PageResult[vo.WmsReceivingRecordVO], error)
	UpdateInspectionResult(ctx context.Context, detailID uint, req *dto.InspectionResultUpdateReq) error
	GetBlindReceivingRecords(ctx context.Context, warehouseID uint64) ([]*vo.WmsReceivingRecordVO, error)
	UpdateStatus(ctx context.Context, id uint, req *dto.WmsReceivingRecordUpdateStatusReq) error
}

// wmsReceivingRecordServiceImpl 收货记录服务实现
type wmsReceivingRecordServiceImpl struct {
	BaseServiceImpl
}

// NewWmsReceivingRecordService 创建收货记录服务
func NewWmsReceivingRecordService(sm *ServiceManager) WmsReceivingRecordService {
	return &wmsReceivingRecordServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建收货记录
func (s *wmsReceivingRecordServiceImpl) Create(ctx context.Context, req *dto.WmsReceivingRecordCreateReq) (*vo.WmsReceivingRecordVO, error) {
	var record *entity.WmsReceivingRecord
	var voResult vo.WmsReceivingRecordVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsReceivingRecordRepository()

		// 生成收货单号
		receivingNo, err := s.generateReceivingNo(ctx)
		if err != nil {
			return err
		}

		record = &entity.WmsReceivingRecord{}
		copier.Copy(record, req)
		record.ReceivingNo = receivingNo

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}

		// 强制从上下文获取账套ID
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		record.AccountBookID = uint(accountBookID)
		record.CreatedBy = uint(userID)

		// 验证通知单是否存在
		if req.NotificationID != nil && *req.NotificationID > 0 {
			notificationRepo := txRepoMgr.GetWmsInboundNotificationRepository()
			_, err := notificationRepo.FindByID(ctx, *req.NotificationID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "入库通知单不存在")
				}
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证通知单失败").WithCause(err)
			}
		}

		// 处理明细表数据
		if len(req.Details) > 0 {
			details := make([]entity.WmsReceivingRecordDetail, len(req.Details))
			for i, detailReq := range req.Details {
				// 🔥 新增：验证明细表数据
				allowZero := req.ReceivingType == string(entity.ReceivingTypeBlind)
				if err := s.validateReceivingDetail(ctx, txRepoMgr, &detailReq, allowZero); err != nil {
					return err
				}

				detail := &entity.WmsReceivingRecordDetail{}
				copier.Copy(detail, &detailReq)

				// 生成内部批次号
				if detail.InternalBatchNo == nil || *detail.InternalBatchNo == "" {
					nb, err := s.generateInternalBatchNo(ctx)
					if err != nil {
						return err
					}
					detail.InternalBatchNo = &nb
				}

				detail.AccountBookID = record.AccountBookID // 明细表继承主表的账套ID
				detail.CreatedBy = uint(userID)
				details[i] = *detail
			}
			record.Details = details
		}

		// 创建收货记录（包含明细表）
		if err := repo.Create(ctx, record); err != nil {
			if apperrors.IsDuplicateKeyError(err) {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货单号已存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建收货记录失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	_ = copier.Copy(&voResult, record)
	if err := s.enrichReceivingDetails(ctx, voResult.Details); err != nil {
		return nil, err
	}
	return &voResult, nil
}

// CreateFromNotification 从入库通知单创建收货记录
func (s *wmsReceivingRecordServiceImpl) CreateFromNotification(ctx context.Context, notificationID uint) (*vo.WmsReceivingRecordVO, error) {
	var record *entity.WmsReceivingRecord
	var voResult vo.WmsReceivingRecordVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		notificationRepo := txRepoMgr.GetWmsInboundNotificationRepository()
		receivingRepo := txRepoMgr.GetWmsReceivingRecordRepository()

		// 查询通知单（需要预加载明细）
		notification, err := notificationRepo.FindByID(ctx, notificationID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "入库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询通知单失败").WithCause(err)
		}

		// 预加载通知单明细
		db := notificationRepo.GetDB(ctx)
		if err := db.Preload("Details").Where("id = ?", notificationID).First(notification).Error; err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "加载通知单明细失败").WithCause(err)
		}

		// 检查通知单状态
		if notification.Status != "PUBLISHED" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已发布的通知单才能创建收货记录")
		}

		// 检查是否有明细
		if len(notification.Details) == 0 {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "通知单没有明细，无法创建收货记录")
		}

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}

		// 生成收货单号
		receivingNo, err := s.generateReceivingNo(ctx)
		if err != nil {
			return err
		}

		// 创建收货记录主表
		record = &entity.WmsReceivingRecord{
			ReceivingNo:       receivingNo,
			NotificationID:    &notificationID,
			WarehouseID:       notification.WarehouseID,
			ClientID:          notification.ClientID,
			ActualArrivalDate: notification.ExpectedArrivalDate,
			Status:            "PENDING",
			ReceivingType:     entity.ReceivingTypeASN,
		}
		record.AccountBookID = notification.AccountBookID
		record.CreatedBy = uint(userID)

		// 🔥 新增：将通知单明细转换为收货记录明细
		if len(notification.Details) > 0 {
			details := make([]entity.WmsReceivingRecordDetail, len(notification.Details))
			for i, notificationDetail := range notification.Details {
				expectedQty := notificationDetail.ExpectedQuantity
				detail := &entity.WmsReceivingRecordDetail{
					NotificationDetailID: &notificationDetail.ID,
					LineNo:               notificationDetail.LineNo,
					ItemID:               notificationDetail.ItemID,
					ExpectedQuantity:     &expectedQty,
					ReceivedQuantity:     0, // 初始收货数量为0
					UnitOfMeasure:        notificationDetail.UnitOfMeasure,
					ReceivedAtLocationID: record.WarehouseID, // 默认使用仓库ID作为收货库位
					BatchNo:              notificationDetail.BatchNo,
					ProductionDate:       notificationDetail.ProductionDate,
					ExpiryDate:           notificationDetail.ExpiryDate,
					InspectionStatus:     "NOT_REQUIRED", // 默认无需检验
					Remark:               notificationDetail.Remark,
					InternalBatchNo:      nil, // 稍后生成
				}

				// 生成内部批次号
				nb, err := s.generateInternalBatchNo(ctx)
				if err != nil {
					return err
				}
				detail.InternalBatchNo = &nb

				detail.AccountBookID = record.AccountBookID
				detail.CreatedBy = uint(userID)
				details[i] = *detail
			}
			record.Details = details
		}

		// 创建收货记录（包含明细）
		if err := receivingRepo.Create(ctx, record); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建收货记录失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	_ = copier.Copy(&voResult, record)
	if err := s.enrichReceivingDetails(ctx, voResult.Details); err != nil {
		return nil, err
	}
	return &voResult, nil
}

// CreateBlindReceiving 创建盲收记录
func (s *wmsReceivingRecordServiceImpl) CreateBlindReceiving(ctx context.Context, req *dto.WmsBlindReceivingCreateReq) (*vo.WmsReceivingRecordVO, error) {
	var record *entity.WmsReceivingRecord
	var voResult vo.WmsReceivingRecordVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		receivingRepo := txRepoMgr.GetWmsReceivingRecordRepository()

		// 生成收货单号
		receivingNo, err := s.generateReceivingNo(ctx)
		if err != nil {
			return err
		}

		// 解析日期字段
		var actualArrivalDate *time.Time
		if req.ActualArrivalDateStr != nil && *req.ActualArrivalDateStr != "" {
			t, err := time.Parse("2006-01-02", *req.ActualArrivalDateStr)
			if err != nil {
				return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "actualArrivalDate 格式错误，应为 YYYY-MM-DD").WithCause(err)
			}
			actualArrivalDate = &t
		}

		// 强制从上下文获取账套ID
		accountBookID, err := s.GetAccountBookIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取账套信息").WithCause(err)
		}
		if accountBookID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "账套ID不能为空")
		}

		// 强制从上下文获取用户ID - 用户强绑定
		userID, err := s.GetUserIDFromContext(ctx)
		if err != nil {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "无法获取用户信息").WithCause(err)
		}
		if userID == 0 {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "用户ID不能为空")
		}

		// 创建盲收记录主表
		record = &entity.WmsReceivingRecord{
			ReceivingNo:           receivingNo,
			NotificationID:        nil,
			WarehouseID:           req.WarehouseID,
			ClientID:              req.ClientID,
			ActualArrivalDate:     actualArrivalDate,
			Status:                "PENDING",
			ReceivingType:         entity.ReceivingTypeBlind,
			SupplementDeadlineStr: req.SupplementDeadlineStr,
			Remark:                req.Remark,
			SupplierShipper:       req.SupplierShipper,
			SourceDocNo:           req.SourceDocNo,
		}
		record.AccountBookID = uint(accountBookID)
		record.CreatedBy = uint(userID)

		// 处理明细
		if len(req.Details) > 0 {
			details := make([]entity.WmsReceivingRecordDetail, len(req.Details))
			for i, dReq := range req.Details {
				// 基础校验：收货数量不可为负
				if dReq.ReceivedQuantity < 0 {
					return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "收货数量不能为负值")
				}

				detail := &entity.WmsReceivingRecordDetail{}
				copier.Copy(detail, &dReq)

				// 盲收允许 ExpectedQuantity = 0
				if detail.ExpectedQuantity != nil && *detail.ExpectedQuantity < 0 {
					return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "预期数量不能为负值")
				}

				// 生成内部批次号
				nb, err := s.generateInternalBatchNo(ctx)
				if err != nil {
					return err
				}
				detail.InternalBatchNo = &nb
				detail.AccountBookID = record.AccountBookID
				detail.CreatedBy = uint(userID)
				details[i] = *detail
			}
			record.Details = details
		}

		// 创建记录（含明细）
		if err := receivingRepo.Create(ctx, record); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建盲收记录失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	_ = copier.Copy(&voResult, record)
	if err := s.enrichReceivingDetails(ctx, voResult.Details); err != nil {
		return nil, err
	}
	return &voResult, nil
}

// Update 更新收货记录
func (s *wmsReceivingRecordServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsReceivingRecordUpdateReq) (*vo.WmsReceivingRecordVO, error) {
	var record *entity.WmsReceivingRecord
	// var voResult *vo.WmsReceivingRecordVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsReceivingRecordRepository()
		var err error

		record, err = repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货记录不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询收货记录失败").WithCause(err)
		}

		// 检查状态是否允许修改
		if record.Status == "COMPLETED" || record.Status == "CANCELLED" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已完成或已取消的收货记录不能修改")
		}

		// 更新字段
		copier.Copy(record, req)

		// 设置更新人
		if userID, err := util.GetUserIDFromStdContext(ctx); err == nil {
			record.UpdatedBy = uint(userID)
		}

		if err := repo.Update(ctx, record); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新收货记录失败").WithCause(err)
		}

		// 如果包含明细更新，逐条写入 UpdatedBy
		if len(req.Details) > 0 {
			db := repo.GetDB(ctx)
			for _, d := range req.Details {
				if d.ID == 0 {
					continue // 新增或无效行跳过
				}
				updateMap := map[string]interface{}{}
				// 仅设置 UpdatedBy，不修改其他字段，避免误覆盖
				if userID, err := util.GetUserIDFromStdContext(ctx); err == nil {
					updateMap["updated_by"] = uint(userID)
				}
				if len(updateMap) > 0 {
					if err := db.Model(&entity.WmsReceivingRecordDetail{}).
						Where("id = ?", d.ID).
						Updates(updateMap).Error; err != nil {
						return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新收货明细失败").WithCause(err)
					}
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	var voResult vo.WmsReceivingRecordVO
	_ = copier.Copy(&voResult, record)
	if err := s.enrichReceivingDetails(ctx, voResult.Details); err != nil {
		return nil, err
	}
	return &voResult, nil
}

// Delete 删除收货记录
func (s *wmsReceivingRecordServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsReceivingRecordRepository()

		record, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货记录不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询收货记录失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if record.Status != "PENDING" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待处理状态的收货记录才能删除")
		}

		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除收货记录失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取收货记录
func (s *wmsReceivingRecordServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsReceivingRecordVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsReceivingRecordRepository()

	// 先获取基本信息
	record, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货记录不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询收货记录失败").WithCause(err)
	}

	// 预加载收货明细
	db := repo.GetDB(ctx)
	if err := db.Preload("Details").Where("id = ?", id).First(record).Error; err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "加载收货记录明细失败").WithCause(err)
	}

	var voResult vo.WmsReceivingRecordVO
	_ = copier.Copy(&voResult, record)
	if err := s.enrichReceivingDetails(ctx, voResult.Details); err != nil {
		return nil, err
	}
	return &voResult, nil
}

// GetPage 分页查询收货记录
func (s *wmsReceivingRecordServiceImpl) GetPage(ctx context.Context, req *dto.WmsReceivingRecordQueryReq) (*vo.PageResult[vo.WmsReceivingRecordVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsReceivingRecordRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询收货记录失败").WithCause(err)
	}

	// 正确初始化结果对象，避免返回空指针导致前端 data 为 null
	var voResult vo.PageResult[vo.WmsReceivingRecordVO]
	_ = copier.Copy(&voResult, pageResult)
	return &voResult, nil
}

// UpdateInspectionResult 更新检验结果
func (s *wmsReceivingRecordServiceImpl) UpdateInspectionResult(ctx context.Context, detailID uint, req *dto.InspectionResultUpdateReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		// 由于明细表Repository已删除，直接使用GORM更新
		receivingRepo := txRepoMgr.GetWmsReceivingRecordRepository()
		db := receivingRepo.GetDB(ctx)

		// 构建更新字段映射
		updateFields := make(map[string]interface{})
		if req.InspectionStatus != "" {
			updateFields["inspection_status"] = req.InspectionStatus
		}
		if req.InspectionNotes != nil {
			updateFields["inspection_notes"] = *req.InspectionNotes
		}

		// 更新收货记录明细的检验信息
		result := db.Model(&entity.WmsReceivingRecordDetail{}).
			Where("id = ?", detailID).
			Updates(updateFields)

		if result.Error != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新检验结果失败").WithCause(result.Error)
		}

		if result.RowsAffected == 0 {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货记录明细不存在")
		}

		return nil
	})
}

// GetBlindReceivingRecords 获取盲收记录
func (s *wmsReceivingRecordServiceImpl) GetBlindReceivingRecords(ctx context.Context, warehouseID uint64) ([]*vo.WmsReceivingRecordVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsReceivingRecordRepository()

	records, err := repo.GetBlindReceivingRecords(ctx, uint(warehouseID))
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询盲收记录失败").WithCause(err)
	}

	var voResults []*vo.WmsReceivingRecordVO
	_ = copier.Copy(&voResults, records)
	return voResults, nil
}

// UpdateStatus 更新状态
func (s *wmsReceivingRecordServiceImpl) UpdateStatus(ctx context.Context, id uint, req *dto.WmsReceivingRecordUpdateStatusReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsReceivingRecordRepository()

		// 生成带中文状态的备注
		statusLabelMap := map[string]string{
			"PENDING":             "待收货",
			"RECEIVING":           "收货中",
			"PENDING_INSPECTION":  "待检验",
			"INSPECTING":          "检验中",
			"PARTIALLY_COMPLETED": "部分完成",
			"COMPLETED":           "已完成",
			"CLOSED":              "已关闭",
			"CANCELLED":           "已取消",
		}

		label, ok := statusLabelMap[req.Status]
		if !ok {
			label = req.Status
		}

		// 构造新的 remark（旧remark + 换行 + 新内容）
		existing, _ := repo.FindByID(ctx, id)
		newSegment := label + "：" + req.Remark
		fullRemark := newSegment
		if existing != nil && existing.Remark != nil && *existing.Remark != "" {
			fullRemark = *existing.Remark + "\n" + newSegment
		}

		if err := repo.UpdateStatus(ctx, id, req.Status, fullRemark); err != nil {
			return err
		}

		return nil
	})
}

// generateReceivingNo 生成收货单号
func (s *wmsReceivingRecordServiceImpl) generateReceivingNo(ctx context.Context) (string, error) {
	// 最大重试次数（编码生成失败时）
	const maxRetries = 3
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		s.GetServiceManager().GetLogger().Info("开始生成收货记录编号",
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
			s.GetServiceManager().GetLogger().WithField("maxRetries", maxRetries))

		codeGenService := s.GetServiceManager().GetCodeGenerationService()
		if codeGenService == nil {
			s.GetServiceManager().GetLogger().Error("编码生成服务不可用")
			return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "收货记录编号生成服务暂时不可用，请稍后重试或联系系统管理员。")
		}

		s.GetServiceManager().GetLogger().Info("编码生成服务可用，准备生成收货记录编号")

		s.GetServiceManager().GetLogger().Info("调用编码生成服务",
			s.GetServiceManager().GetLogger().WithField("businessType", "RECEIVING_RECORD"))

		generatedCode, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
			BusinessType: "RECEIVING_RECORD",
		})
		if err != nil {
			s.GetServiceManager().GetLogger().Error("生成收货记录编号失败",
				s.GetServiceManager().GetLogger().WithField("attempt", attempt+1),
				s.GetServiceManager().GetLogger().WithError(err))

			// 检查是否为配置问题（不需要重试）
			if apperrors.IsParamError(err) {
				return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "收货记录编号自动生成失败。可能原因：1) 缺少收货记录编码规则配置 2) 编码模板格式错误。请联系系统管理员。").WithCause(err)
			}

			lastErr = err
			continue // 重试
		}

		s.GetServiceManager().GetLogger().Info("收货记录编号生成成功",
			s.GetServiceManager().GetLogger().WithField("generatedCode", generatedCode.GeneratedCode),
			s.GetServiceManager().GetLogger().WithField("attempt", attempt+1))

		return generatedCode.GeneratedCode, nil
	}

	// 所有重试都失败了
	if lastErr != nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "收货记录编号生成失败，系统重试次数已达上限。请稍后重试或联系系统管理员。").WithCause(lastErr)
	}

	return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "收货记录编号生成失败，编码生成重试次数已达上限。请联系系统管理员。")
}

// validateReceivingDetail 验证收货记录明细数据
func (s *wmsReceivingRecordServiceImpl) validateReceivingDetail(ctx context.Context, txRepoMgr *repository.RepositoryManager, detailReq *dto.WmsReceivingRecordDetailCreateReq, allowZeroExpected bool) error {

	// 取 expected，注意 nil 指针
	exp := 0.0
	if detailReq.ExpectedQuantity != nil {
		exp = *detailReq.ExpectedQuantity
	}

	// 1. 验证预期数量（盲收允许等于0）
	if !allowZeroExpected && exp <= 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "预期数量必须大于0")
	}

	// 2. 验证初始收货数量不能为负数
	if detailReq.ReceivedQuantity < 0 {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "收货数量不能为负数")
	}

	// 3. 验证收货数量不能超过预期数量（盲收 ExpectedQuantity 可能为 0，此时跳过此校验）
	if (!allowZeroExpected || exp > 0) && detailReq.ReceivedQuantity > exp {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "收货数量不能超过预期数量")
	}

	// 4. TODO: 验证物料ID是否存在
	// 5. TODO: 验证关联的通知单明细ID是否存在

	return nil
}

// generateInternalBatchNo 生成内部批次号
func (s *wmsReceivingRecordServiceImpl) generateInternalBatchNo(ctx context.Context) (string, error) {
	codeGenService := s.GetServiceManager().GetCodeGenerationService()
	if codeGenService == nil {
		return "", apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "编码生成服务不可用")
	}

	gen, err := codeGenService.GenerateCode(ctx, &dto.CodeGenerationReq{
		BusinessType: "INTERNAL_BATCH_NO",
	})
	if err != nil {
		return "", err
	}
	return gen.GeneratedCode, nil
}

// enrichReceivingDetails 为明细补充物料的 SKU/名称/规格信息
func (s *wmsReceivingRecordServiceImpl) enrichReceivingDetails(ctx context.Context, details []vo.WmsReceivingRecordDetailVO) error {
	if len(details) == 0 {
		return nil
	}

	itemRepo := s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()
	for i := range details {
		item, err := itemRepo.FindByID(ctx, details[i].ItemID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// 物料已被删除，跳过但记录日志
				s.GetServiceManager().GetLogger().Warn("物料不存在，跳过填充", s.GetServiceManager().GetLogger().WithField("itemId", details[i].ItemID))
				continue
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取物料信息失败").WithCause(err)
		}
		if item != nil {
			details[i].ItemSku = item.Sku
			details[i].ItemName = item.Name
			details[i].Specification = item.Specification
		}
	}
	return nil
}
