package entity

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// WmsBlindReceivingConfigLevel 配置级别常量
type WmsBlindReceivingConfigLevel string

const (
	ConfigLevelSystem    WmsBlindReceivingConfigLevel = "SYSTEM"    // 系统级别
	ConfigLevelWarehouse WmsBlindReceivingConfigLevel = "WAREHOUSE" // 仓库级别
	ConfigLevelClient    WmsBlindReceivingConfigLevel = "CLIENT"    // 客户级别
	ConfigLevelUser      WmsBlindReceivingConfigLevel = "USER"      // 用户级别
)

// WmsBlindReceivingStrategy 盲收策略常量
type WmsBlindReceivingStrategy string

const (
	StrategyStrict     WmsBlindReceivingStrategy = "STRICT"     // 严格模式：不允许盲收
	StrategySupplement WmsBlindReceivingStrategy = "SUPPLEMENT" // 补录模式：允许盲收但需补录ASN
	StrategyFull       WmsBlindReceivingStrategy = "FULL"       // 完全模式：允许盲收，无需补录
)

// WmsBlindReceivingConfig 盲收配置实体
// 管理不同层级的盲收策略配置，支持系统、仓库、客户、用户四个级别
type WmsBlindReceivingConfig struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	ConfigLevel          string   `gorm:"column:config_level;type:varchar(20);not null;index;comment:配置层级" json:"configLevel"`            // SYSTEM/WAREHOUSE/CLIENT/USER
	ConfigTargetID       *uint    `gorm:"column:config_target_id;index;comment:目标ID" json:"configTargetId"`                               // 目标ID（仓库ID/客户ID/用户ID）
	Strategy             string   `gorm:"column:strategy;type:varchar(20);not null;comment:盲收策略" json:"strategy"`                         // STRICT/SUPPLEMENT/FULL
	SupplementTimeLimit  *int     `gorm:"column:supplement_time_limit;comment:补录时限(小时)" json:"supplementTimeLimit"`                       // 补录时限（小时）
	RequiresApproval     bool     `gorm:"column:requires_approval;not null;default:false;comment:是否需要审批" json:"requiresApproval"`         // 是否需要审批
	ApprovalUserRoles    *string  `gorm:"column:approval_user_roles;type:varchar(200);comment:审批用户角色" json:"approvalUserRoles"`           // 审批用户角色（JSON格式）
	MaxBlindReceivingQty *float64 `gorm:"column:max_blind_receiving_qty;type:numeric(15,3);comment:最大盲收数量限制" json:"maxBlindReceivingQty"` // 最大盲收数量限制
	IsActive             bool     `gorm:"column:is_active;not null;default:true;comment:是否启用" json:"isActive"`                            // 是否启用
	Priority             int      `gorm:"column:priority;not null;default:5;comment:优先级" json:"priority"`                                 // 优先级（数值越小优先级越高）

	// 关联字段（仅用于查询，不存储）
	ConfigTargetName string `gorm:"-" json:"configTargetName"` // 配置目标名称
}

// TableName 指定数据库表名
func (WmsBlindReceivingConfig) TableName() string {
	return "wms_blind_receiving_config"
}

// BeforeCreate 创建前处理
func (c *WmsBlindReceivingConfig) BeforeCreate(tx *gorm.DB) error {
	// 设置创建时间
	c.CreatedAt = time.Now()
	c.UpdatedAt = time.Now()

	// 系统级别配置的目标ID为空
	if c.ConfigLevel == string(ConfigLevelSystem) {
		c.ConfigTargetID = nil
	}

	return nil
}

// BeforeUpdate 更新前处理
func (c *WmsBlindReceivingConfig) BeforeUpdate(tx *gorm.DB) error {
	// 更新时间
	c.UpdatedAt = time.Now()
	return nil
}

// IsStrategyStrict 判断是否为严格策略
func (c *WmsBlindReceivingConfig) IsStrategyStrict() bool {
	return c.Strategy == string(StrategyStrict)
}

// IsStrategySupplement 判断是否为补录策略
func (c *WmsBlindReceivingConfig) IsStrategySupplement() bool {
	return c.Strategy == string(StrategySupplement)
}

// IsStrategyFull 判断是否为完全策略
func (c *WmsBlindReceivingConfig) IsStrategyFull() bool {
	return c.Strategy == string(StrategyFull)
}

// NeedsApproval 判断是否需要审批
func (c *WmsBlindReceivingConfig) NeedsApproval() bool {
	return c.RequiresApproval && c.IsActive
}

// HasQuantityLimit 判断是否有数量限制
func (c *WmsBlindReceivingConfig) HasQuantityLimit() bool {
	return c.MaxBlindReceivingQty != nil && *c.MaxBlindReceivingQty > 0
}

// IsQuantityExceeded 判断数量是否超限
func (c *WmsBlindReceivingConfig) IsQuantityExceeded(quantity float64) bool {
	if !c.HasQuantityLimit() {
		return false
	}
	return quantity > *c.MaxBlindReceivingQty
}

// HasSupplementTimeLimit 判断是否有补录时限
func (c *WmsBlindReceivingConfig) HasSupplementTimeLimit() bool {
	return c.SupplementTimeLimit != nil && *c.SupplementTimeLimit > 0
}

// GetSupplementDeadline 获取补录截止时间
func (c *WmsBlindReceivingConfig) GetSupplementDeadline(baseTime time.Time) *time.Time {
	if !c.HasSupplementTimeLimit() {
		return nil
	}
	deadline := baseTime.Add(time.Duration(*c.SupplementTimeLimit) * time.Hour)
	return &deadline
}

// GetApprovalUserRolesList 获取审批用户角色列表
func (c *WmsBlindReceivingConfig) GetApprovalUserRolesList() []string {
	if c.ApprovalUserRoles == nil {
		return []string{}
	}
	var roles []string
	if err := json.Unmarshal([]byte(*c.ApprovalUserRoles), &roles); err != nil {
		// 如果解析失败，尝试作为单个字符串处理
		return []string{*c.ApprovalUserRoles}
	}
	return roles
}

// SetApprovalUserRolesList 设置审批用户角色列表
func (c *WmsBlindReceivingConfig) SetApprovalUserRolesList(roles []string) error {
	if len(roles) == 0 {
		c.ApprovalUserRoles = nil
		return nil
	}
	rolesBytes, err := json.Marshal(roles)
	if err != nil {
		return err
	}
	rolesStr := string(rolesBytes)
	c.ApprovalUserRoles = &rolesStr
	return nil
}
