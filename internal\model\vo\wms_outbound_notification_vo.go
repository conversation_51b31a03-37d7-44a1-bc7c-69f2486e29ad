package vo

import (
	"time"
)

// WmsOutboundNotificationVO 出库通知单视图对象
type WmsOutboundNotificationVO struct {
	ID               uint                                `json:"id"`
	NotificationNo   string                              `json:"notificationNo"`
	ClientID         uint                                `json:"clientId"`
	ClientName       *string                             `json:"clientName"`       // 扩展字段：客户名称
	ClientOrderNo    *string                             `json:"clientOrderNo"`
	WarehouseID      uint                                `json:"warehouseId"`
	WarehouseName    *string                             `json:"warehouseName"`    // 扩展字段：仓库名称
	RequiredShipDate *string                             `json:"requiredShipDate"`
	Priority         int                                 `json:"priority"`
	Status           string                              `json:"status"`
	StatusName       *string                             `json:"statusName"`       // 扩展字段：状态名称
	ConsigneeName    string                              `json:"consigneeName"`
	ConsigneePhone   *string                             `json:"consigneePhone"`
	ConsigneeAddress *string                             `json:"consigneeAddress"`
	CarrierID        *uint                               `json:"carrierId"`
	CarrierName      *string                             `json:"carrierName"`      // 扩展字段：承运商名称
	ShippingMethod   *string                             `json:"shippingMethod"`
	Remark           *string                             `json:"remark"`
	
	// 统计字段
	TotalItems       int                                 `json:"totalItems"`       // 总物料种类数
	TotalQty         float64                             `json:"totalQty"`         // 总数量
	AllocatedItems   int                                 `json:"allocatedItems"`   // 已分配物料种类数
	AllocatedQty     float64                             `json:"allocatedQty"`     // 已分配数量
	PickedItems      int                                 `json:"pickedItems"`      // 已拣货物料种类数
	PickedQty        float64                             `json:"pickedQty"`        // 已拣货数量
	
	// 进度信息
	AllocationRate   float64                             `json:"allocationRate"`   // 分配进度百分比
	PickingRate      float64                             `json:"pickingRate"`      // 拣货进度百分比
	
	// 时间字段
	CreatedAt        time.Time                           `json:"createdAt"`
	UpdatedAt        time.Time                           `json:"updatedAt"`
	CreatedBy        uint                                `json:"createdBy"`
	UpdatedBy        uint                                `json:"updatedBy"`
	CreatedByName    *string                             `json:"createdByName"`    // 扩展字段：创建人姓名
	UpdatedByName    *string                             `json:"updatedByName"`    // 扩展字段：更新人姓名
	
	// 关联数据
	Details          []WmsOutboundNotificationDetailVO   `json:"details,omitempty"`
	PickingTasks     []WmsPickingTaskVO                  `json:"pickingTasks,omitempty"`
}

// WmsOutboundNotificationDetailVO 出库通知单明细视图对象
type WmsOutboundNotificationDetailVO struct {
	ID                     uint    `json:"id"`
	NotificationID         uint    `json:"notificationId"`
	LineNo                 int     `json:"lineNo"`
	ItemID                 uint    `json:"itemId"`
	ItemCode               *string `json:"itemCode"`               // 扩展字段：物料编码
	ItemName               *string `json:"itemName"`               // 扩展字段：物料名称
	ItemSpec               *string `json:"itemSpec"`               // 扩展字段：物料规格
	RequiredQty            float64 `json:"requiredQty"`
	AllocatedQty           float64 `json:"allocatedQty"`
	PickedQty              float64 `json:"pickedQty"`
	UnitOfMeasure          string  `json:"unitOfMeasure"`
	RequiredBatchNo        *string `json:"requiredBatchNo"`
	RequiredProductionDate *string `json:"requiredProductionDate"`
	RequiredExpiryDate     *string `json:"requiredExpiryDate"`
	LineStatus             string  `json:"lineStatus"`
	LineStatusName         *string `json:"lineStatusName"`         // 扩展字段：行状态名称
	Remark                 *string `json:"remark"`
	
	// 进度信息
	AllocationRate         float64 `json:"allocationRate"`         // 分配进度百分比
	PickingRate            float64 `json:"pickingRate"`            // 拣货进度百分比
	AvailableQty           float64 `json:"availableQty"`           // 可用库存数量
	
	// 时间字段
	CreatedAt              time.Time `json:"createdAt"`
	UpdatedAt              time.Time `json:"updatedAt"`
	
	// 关联数据
	Allocations            []WmsInventoryAllocationVO `json:"allocations,omitempty"`
}

// WmsOutboundNotificationListVO 出库通知单列表视图对象
type WmsOutboundNotificationListVO struct {
	ID               uint    `json:"id"`
	NotificationNo   string  `json:"notificationNo"`
	ClientName       *string `json:"clientName"`
	ClientOrderNo    *string `json:"clientOrderNo"`
	WarehouseName    *string `json:"warehouseName"`
	RequiredShipDate *string `json:"requiredShipDate"`
	Priority         int     `json:"priority"`
	Status           string  `json:"status"`
	StatusName       *string `json:"statusName"`
	ConsigneeName    string  `json:"consigneeName"`
	CarrierName      *string `json:"carrierName"`
	TotalItems       int     `json:"totalItems"`
	TotalQty         float64 `json:"totalQty"`
	AllocationRate   float64 `json:"allocationRate"`
	PickingRate      float64 `json:"pickingRate"`
	CreatedAt        time.Time `json:"createdAt"`
	CreatedByName    *string `json:"createdByName"`
}

// WmsOutboundNotificationStatsVO 出库通知单统计视图对象
type WmsOutboundNotificationStatsVO struct {
	// 基础统计
	TotalCount       int     `json:"totalCount"`       // 总单据数
	TotalQty         float64 `json:"totalQty"`         // 总数量
	TotalAmount      float64 `json:"totalAmount"`      // 总金额
	
	// 状态统计
	DraftCount       int     `json:"draftCount"`       // 草稿状态数量
	ApprovedCount    int     `json:"approvedCount"`    // 已审核数量
	AllocatedCount   int     `json:"allocatedCount"`   // 已分配数量
	PickingCount     int     `json:"pickingCount"`     // 拣货中数量
	PickedCount      int     `json:"pickedCount"`      // 已拣货数量
	PackedCount      int     `json:"packedCount"`      // 已打包数量
	ShippedCount     int     `json:"shippedCount"`     // 已发运数量
	DeliveredCount   int     `json:"deliveredCount"`   // 已送达数量
	CancelledCount   int     `json:"cancelledCount"`   // 已取消数量
	
	// 效率统计
	AvgProcessingTime float64 `json:"avgProcessingTime"` // 平均处理时间（小时）
	AvgPickingTime    float64 `json:"avgPickingTime"`    // 平均拣货时间（小时）
	OnTimeDeliveryRate float64 `json:"onTimeDeliveryRate"` // 准时交付率
	
	// 趋势数据
	TrendData        []WmsOutboundNotificationTrendVO `json:"trendData,omitempty"`
}

// WmsOutboundNotificationTrendVO 出库通知单趋势数据
type WmsOutboundNotificationTrendVO struct {
	Date        string  `json:"date"`        // 日期
	Count       int     `json:"count"`       // 单据数量
	Qty         float64 `json:"qty"`         // 总数量
	Amount      float64 `json:"amount"`      // 总金额
}

// WmsOutboundNotificationAllocationStatusVO 出库通知单分配状态视图对象
type WmsOutboundNotificationAllocationStatusVO struct {
	NotificationID   uint                                        `json:"notificationId"`
	NotificationNo   string                                      `json:"notificationNo"`
	TotalItems       int                                         `json:"totalItems"`
	AllocatedItems   int                                         `json:"allocatedItems"`
	AllocationRate   float64                                     `json:"allocationRate"`
	CanAllocate      bool                                        `json:"canAllocate"`      // 是否可以分配
	AllocationResult string                                      `json:"allocationResult"` // 分配结果：FULL, PARTIAL, NONE
	Details          []WmsOutboundNotificationDetailAllocationVO `json:"details"`
}

// WmsOutboundNotificationDetailAllocationVO 出库通知单明细分配状态
type WmsOutboundNotificationDetailAllocationVO struct {
	DetailID         uint    `json:"detailId"`
	LineNo           int     `json:"lineNo"`
	ItemCode         *string `json:"itemCode"`
	ItemName         *string `json:"itemName"`
	RequiredQty      float64 `json:"requiredQty"`
	AllocatedQty     float64 `json:"allocatedQty"`
	AvailableQty     float64 `json:"availableQty"`
	AllocationRate   float64 `json:"allocationRate"`
	CanAllocate      bool    `json:"canAllocate"`
	ShortageQty      float64 `json:"shortageQty"`      // 缺货数量
	ShortageReason   *string `json:"shortageReason"`   // 缺货原因
}
