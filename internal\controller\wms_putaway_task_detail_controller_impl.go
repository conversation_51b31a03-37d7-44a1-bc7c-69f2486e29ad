package controller

import (
	"github.com/kataras/iris/v12"
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
)

// WmsPutawayTaskDetailController 定义上架任务明细控制器接口
type WmsPutawayTaskDetailController interface {
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetByTaskID(ctx iris.Context)
	GetPage(ctx iris.Context)
	BatchCreate(ctx iris.Context)
	BatchUpdate(ctx iris.Context)
	BatchDelete(ctx iris.Context)
	Execute(ctx iris.Context)
	BatchExecute(ctx iris.Context)
	GetDetailsSummary(ctx iris.Context)
}

// wmsPutawayTaskDetailControllerImpl 上架任务明细控制器实现
type WmsPutawayTaskDetailControllerImpl struct {
	BaseControllerImpl
	wmsPutawayTaskDetailService service.WmsPutawayTaskDetailService
}

// NewWmsPutawayTaskDetailControllerImpl 创建上架任务明细控制器
func NewWmsPutawayTaskDetailControllerImpl(cm *ControllerManager) *WmsPutawayTaskDetailControllerImpl {
	return &WmsPutawayTaskDetailControllerImpl{
		BaseControllerImpl:          *NewBaseController(cm),
		wmsPutawayTaskDetailService: cm.GetServiceManager().GetWmsPutawayTaskDetailService(),
	}
}

// Create 创建上架任务明细
// POST /api/v1/wms/putaway-task-details
func (ctrl *WmsPutawayTaskDetailControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsPutawayTaskDetail"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	var req dto.WmsPutawayTaskDetailCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsPutawayTaskDetailService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新上架任务明细
// PUT /api/v1/wms/putaway-task-details/{id:uint}
func (ctrl *WmsPutawayTaskDetailControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsPutawayTaskDetail"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPutawayTaskDetailUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsPutawayTaskDetailService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除上架任务明细
// DELETE /api/v1/wms/putaway-task-details/{id:uint}
func (ctrl *WmsPutawayTaskDetailControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsPutawayTaskDetail"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsPutawayTaskDetailService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个上架任务明细
// GET /api/v1/wms/putaway-task-details/{id:uint}
func (ctrl *WmsPutawayTaskDetailControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsPutawayTaskDetailByID"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsPutawayTaskDetailService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetByTaskID 根据任务ID获取明细列表
// GET /api/v1/wms/putaway-tasks/{taskId:uint}/details
func (ctrl *WmsPutawayTaskDetailControllerImpl) GetByTaskID(ctx iris.Context) {
	const opName = "GetWmsPutawayTaskDetailsByTaskID"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	taskID, err := ctrl.GetPathUintParamWithError(ctx, "taskId")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	details, err := ctrl.wmsPutawayTaskDetailService.GetByTaskID(ctx.Request().Context(), uint(taskID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, details)
}

// GetPage 获取上架任务明细分页
// GET /api/v1/wms/putaway-task-details
func (ctrl *WmsPutawayTaskDetailControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsPutawayTaskDetailsPage"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	var req dto.WmsPutawayTaskDetailQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	pageResult, err := ctrl.wmsPutawayTaskDetailService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// BatchCreate 批量创建上架任务明细
// POST /api/v1/wms/putaway-task-details/batch
func (ctrl *WmsPutawayTaskDetailControllerImpl) BatchCreate(ctx iris.Context) {
	const opName = "BatchCreateWmsPutawayTaskDetails"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	var req dto.WmsPutawayTaskDetailBatchCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPutawayTaskDetailService.BatchCreate(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// BatchUpdate 批量更新上架任务明细
// PUT /api/v1/wms/putaway-task-details/batch
func (ctrl *WmsPutawayTaskDetailControllerImpl) BatchUpdate(ctx iris.Context) {
	const opName = "BatchUpdateWmsPutawayTaskDetails"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	var req dto.WmsPutawayTaskDetailBatchUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPutawayTaskDetailService.BatchUpdate(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// BatchDelete 批量删除上架任务明细
// DELETE /api/v1/wms/putaway-task-details/batch
func (ctrl *WmsPutawayTaskDetailControllerImpl) BatchDelete(ctx iris.Context) {
	const opName = "BatchDeleteWmsPutawayTaskDetails"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPutawayTaskDetailService.BatchDelete(ctx.Request().Context(), req.IDs)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// Execute 执行上架任务明细
// POST /api/v1/wms/putaway-task-details/{id:uint}/execute
func (ctrl *WmsPutawayTaskDetailControllerImpl) Execute(ctx iris.Context) {
	const opName = "ExecuteWmsPutawayTaskDetail"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPutawayTaskDetailExecuteReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsPutawayTaskDetailService.Execute(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// BatchExecute 批量执行上架任务明细
// POST /api/v1/wms/putaway-task-details/batch-execute
func (ctrl *WmsPutawayTaskDetailControllerImpl) BatchExecute(ctx iris.Context) {
	const opName = "BatchExecuteWmsPutawayTaskDetails"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	var req []dto.PutawayTaskDetailExecuteItem
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPutawayTaskDetailService.BatchExecute(ctx.Request().Context(), req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetDetailsSummary 获取明细汇总信息
// GET /api/v1/wms/putaway-tasks/{taskId:uint}/details/summary
func (ctrl *WmsPutawayTaskDetailControllerImpl) GetDetailsSummary(ctx iris.Context) {
	const opName = "GetPutawayTaskDetailsSummary"
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task_detail")

	taskID, err := ctrl.GetPathUintParamWithError(ctx, "taskId")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	summary, err := ctrl.wmsPutawayTaskDetailService.GetDetailsSummary(ctx.Request().Context(), uint(taskID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, summary)
} 