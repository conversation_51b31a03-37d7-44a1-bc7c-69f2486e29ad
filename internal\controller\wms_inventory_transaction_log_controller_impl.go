package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/service"
	"backend/pkg/response"
)

// WmsInventoryTransactionLogController 定义库存事务日志控制器接口
type WmsInventoryTransactionLogController interface {
	// 基础查询操作
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 业务查询
	GetByInventoryID(ctx iris.Context)
	GetByTransactionType(ctx iris.Context)
	GetByReferenceDoc(ctx iris.Context)
	GetByOperator(ctx iris.Context)

	// 统计查询
	GetSummaryByType(ctx iris.Context)
	GetOperatorStats(ctx iris.Context)

	// 批量操作
	BatchCreate(ctx iris.Context)

	// 清理操作
	CleanupOldLogs(ctx iris.Context)
}

// wmsInventoryTransactionLogControllerImpl 库存事务日志控制器实现
type WmsInventoryTransactionLogControllerImpl struct {
	BaseControllerImpl
	inventoryTransactionLogService service.WmsInventoryTransactionLogService
}

// NewWmsInventoryTransactionLogControllerImpl 创建库存事务日志控制器实例
func NewWmsInventoryTransactionLogControllerImpl(cm *ControllerManager) *WmsInventoryTransactionLogControllerImpl {
	return &WmsInventoryTransactionLogControllerImpl{
		BaseControllerImpl:             *NewBaseController(cm),
		inventoryTransactionLogService: cm.GetServiceManager().GetWmsInventoryTransactionLogService(),
	}
}

// GetByID 根据ID获取库存事务日志
func (c *WmsInventoryTransactionLogControllerImpl) GetByID(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID参数", ctx)
		return
	}

	result, err := c.inventoryTransactionLogService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetPage 分页查询库存事务日志
func (c *WmsInventoryTransactionLogControllerImpl) GetPage(ctx iris.Context) {
	var req dto.WmsInventoryTransactionLogQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryTransactionLogService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetByInventoryID 根据库存ID获取事务日志
func (c *WmsInventoryTransactionLogControllerImpl) GetByInventoryID(ctx iris.Context) {
	inventoryIdStr := ctx.Params().Get("inventoryId")
	inventoryId, err := strconv.ParseUint(inventoryIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的库存ID参数", ctx)
		return
	}

	result, err := c.inventoryTransactionLogService.GetByInventoryID(ctx.Request().Context(), uint(inventoryId))
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetByTransactionType 根据事务类型获取日志
func (c *WmsInventoryTransactionLogControllerImpl) GetByTransactionType(ctx iris.Context) {
	transactionTypeStr := ctx.Params().Get("transactionType")

	// 将字符串转换为事务类型枚举
	var transactionType entity.TransactionType
	switch transactionTypeStr {
	case "RECEIPT":
		transactionType = entity.TxTypeReceipt
	case "PUTAWAY":
		transactionType = entity.TxTypePutaway
	case "PICKING":
		transactionType = entity.TxTypePicking
	case "SHIPMENT":
		transactionType = entity.TxTypeShipment
	case "ADJUSTMENT_GAIN":
		transactionType = entity.TxTypeAdjustmentGain
	case "ADJUSTMENT_LOSS":
		transactionType = entity.TxTypeAdjustmentLoss
	case "TRANSFER_OUT":
		transactionType = entity.TxTypeTransferOut
	case "TRANSFER_IN":
		transactionType = entity.TxTypeTransferIn
	case "STOCKTAKE_GAIN":
		transactionType = entity.TxTypeStocktakeGain
	case "STOCKTAKE_LOSS":
		transactionType = entity.TxTypeStocktakeLoss
	case "MOVE_OUT":
		transactionType = entity.TxTypeMoveOut
	case "MOVE_IN":
		transactionType = entity.TxTypeMoveIn
	case "STATUS_CHANGE":
		transactionType = entity.TxTypeStatusChange
	case "RECEIPT_CORRECTION":
		transactionType = entity.TxTypeReceiptCorrection
	case "RECEIPT_CANCELLED":
		transactionType = entity.TxTypeReceiptCancelled
	case "FREEZE":
		transactionType = entity.TxTypeFreeze
	case "UNFREEZE":
		transactionType = entity.TxTypeUnfreeze
	case "ALLOCATE":
		transactionType = entity.TxTypeAllocate
	case "DEALLOCATE":
		transactionType = entity.TxTypeDeallocate
	default:
		response.FailWithMessage("无效的事务类型参数", ctx)
		return
	}

	result, err := c.inventoryTransactionLogService.GetByTransactionType(ctx.Request().Context(), transactionType)
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetByReferenceDoc 根据参考单据获取日志
func (c *WmsInventoryTransactionLogControllerImpl) GetByReferenceDoc(ctx iris.Context) {
	refDocType := ctx.URLParam("refDocType")
	if refDocType == "" {
		response.FailWithMessage("参考单据类型不能为空", ctx)
		return
	}

	refDocIDStr := ctx.URLParam("refDocID")
	refDocID, err := strconv.ParseUint(refDocIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的参考单据ID参数", ctx)
		return
	}

	result, err := c.inventoryTransactionLogService.GetByReferenceDoc(ctx.Request().Context(), refDocType, uint(refDocID))
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetByOperator 根据操作员获取日志
func (c *WmsInventoryTransactionLogControllerImpl) GetByOperator(ctx iris.Context) {
	operatorIdStr := ctx.Params().Get("operatorId")
	operatorId, err := strconv.ParseUint(operatorIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的操作员ID参数", ctx)
		return
	}

	result, err := c.inventoryTransactionLogService.GetByOperator(ctx.Request().Context(), uint(operatorId))
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetSummaryByType 根据事务类型获取汇总统计
func (c *WmsInventoryTransactionLogControllerImpl) GetSummaryByType(ctx iris.Context) {
	transactionTypeStr := ctx.URLParam("transactionType")
	startDate := ctx.URLParam("startDate")
	endDate := ctx.URLParam("endDate")

	// 将字符串转换为事务类型枚举
	var transactionType entity.TransactionType
	switch transactionTypeStr {
	case "RECEIPT":
		transactionType = entity.TxTypeReceipt
	case "PUTAWAY":
		transactionType = entity.TxTypePutaway
	case "PICKING":
		transactionType = entity.TxTypePicking
	case "SHIPMENT":
		transactionType = entity.TxTypeShipment
	case "ADJUSTMENT_GAIN":
		transactionType = entity.TxTypeAdjustmentGain
	case "ADJUSTMENT_LOSS":
		transactionType = entity.TxTypeAdjustmentLoss
	case "TRANSFER_OUT":
		transactionType = entity.TxTypeTransferOut
	case "TRANSFER_IN":
		transactionType = entity.TxTypeTransferIn
	case "STOCKTAKE_GAIN":
		transactionType = entity.TxTypeStocktakeGain
	case "STOCKTAKE_LOSS":
		transactionType = entity.TxTypeStocktakeLoss
	case "MOVE_OUT":
		transactionType = entity.TxTypeMoveOut
	case "MOVE_IN":
		transactionType = entity.TxTypeMoveIn
	case "STATUS_CHANGE":
		transactionType = entity.TxTypeStatusChange
	case "RECEIPT_CORRECTION":
		transactionType = entity.TxTypeReceiptCorrection
	case "RECEIPT_CANCELLED":
		transactionType = entity.TxTypeReceiptCancelled
	case "FREEZE":
		transactionType = entity.TxTypeFreeze
	case "UNFREEZE":
		transactionType = entity.TxTypeUnfreeze
	case "ALLOCATE":
		transactionType = entity.TxTypeAllocate
	case "DEALLOCATE":
		transactionType = entity.TxTypeDeallocate
	default:
		response.FailWithMessage("无效的事务类型参数", ctx)
		return
	}

	result, err := c.inventoryTransactionLogService.GetSummaryByType(ctx.Request().Context(), transactionType, startDate, endDate)
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetOperatorStats 获取操作员统计
func (c *WmsInventoryTransactionLogControllerImpl) GetOperatorStats(ctx iris.Context) {
	operatorIdStr := ctx.URLParam("operatorId")
	operatorId, err := strconv.ParseUint(operatorIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的操作员ID参数", ctx)
		return
	}

	startDate := ctx.URLParam("startDate")
	endDate := ctx.URLParam("endDate")

	result, err := c.inventoryTransactionLogService.GetOperatorStats(ctx.Request().Context(), uint(operatorId), startDate, endDate)
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// BatchCreate 批量创建库存事务日志
func (c *WmsInventoryTransactionLogControllerImpl) BatchCreate(ctx iris.Context) {
	var reqs []*dto.WmsInventoryTransactionLogCreateReq
	if err := ctx.ReadJSON(&reqs); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryTransactionLogService.BatchCreate(ctx.Request().Context(), reqs)
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// CleanupOldLogs 清理旧日志
func (c *WmsInventoryTransactionLogControllerImpl) CleanupOldLogs(ctx iris.Context) {
	beforeDate := ctx.URLParam("beforeDate")
	if beforeDate == "" {
		response.FailWithMessage("清理日期不能为空", ctx)
		return
	}

	err := c.inventoryTransactionLogService.CleanupOldLogs(ctx.Request().Context(), beforeDate)
	if err != nil {
		response.FailWithMessage(err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}
