package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsShipmentRepository 定义发运单仓库接口
type WmsShipmentRepository interface {
	BaseRepository[entity.WmsShipment, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsShipmentQueryReq) (*response.PageResult, error)

	// 根据发运单号查找
	FindByShipmentNo(ctx context.Context, shipmentNo string) (*entity.WmsShipment, error)

	// 检查发运单号是否存在
	IsShipmentNoExist(ctx context.Context, shipmentNo string, excludeID uint) (bool, error)

	// 根据拣货任务ID查询发运单
	FindByPickingTaskID(ctx context.Context, pickingTaskID uint) ([]*entity.WmsShipment, error)

	// 根据出库通知单ID查询发运单
	FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsShipment, error)

	// 根据承运商ID查询发运单
	FindByCarrierID(ctx context.Context, carrierID uint) ([]*entity.WmsShipment, error)

	// 根据运单号查询发运单
	FindByTrackingNo(ctx context.Context, trackingNo string) (*entity.WmsShipment, error)

	// 获取指定状态的发运单
	GetByStatus(ctx context.Context, status string) ([]*entity.WmsShipment, error)

	// 批量创建发运单
	BatchCreate(ctx context.Context, shipments []*entity.WmsShipment) error

	// 更新状态
	UpdateStatus(ctx context.Context, id uint, status string, remark string) error

	// 批量更新状态
	BatchUpdateStatus(ctx context.Context, ids []uint, status string, remark string) error

	// 打包确认
	PackShipment(ctx context.Context, id uint, packageCount int, totalWeight, totalVolume *float64, remark string) error

	// 发运确认
	ShipConfirm(ctx context.Context, id uint, carrierID *uint, trackingNo, shippingMethod *string, shipmentDate *time.Time, shippingCost *float64, remark string) error

	// 更新运输状态
	UpdateTrackingStatus(ctx context.Context, id uint, status, trackingInfo *string, actualDelivery *time.Time, remark string) error

	// 签收确认
	DeliveryConfirm(ctx context.Context, id uint, actualDelivery *time.Time, receiverName, receiverPhone, deliveryProof, remark *string) error

	// 根据发运日期范围查询
	GetByShipmentDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsShipment, error)

	// 获取待打包的发运单
	GetPendingPacking(ctx context.Context) ([]*entity.WmsShipment, error)

	// 获取待发运的发运单
	GetReadyToShip(ctx context.Context) ([]*entity.WmsShipment, error)

	// 获取运输中的发运单
	GetInTransit(ctx context.Context) ([]*entity.WmsShipment, error)

	// 获取过期未发运的发运单
	GetOverdueShipments(ctx context.Context) ([]*entity.WmsShipment, error)

	// 获取发运统计信息
	GetShipmentStats(ctx context.Context, startDate, endDate *time.Time, carrierID *uint) (map[string]interface{}, error)

	// 根据收货人查询发运单
	FindByConsignee(ctx context.Context, consigneeName string) ([]*entity.WmsShipment, error)

	// 获取运费统计
	GetShippingCostStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error)
}

// wmsShipmentRepository 发运单仓库实现
type wmsShipmentRepository struct {
	BaseRepositoryImpl[entity.WmsShipment, uint]
}

// NewWmsShipmentRepository 创建发运单仓库
func NewWmsShipmentRepository(db *gorm.DB) WmsShipmentRepository {
	return &wmsShipmentRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsShipment, uint]{
			db: db,
		},
	}
}

// GetPage 获取发运单分页数据
func (r *wmsShipmentRepository) GetPage(ctx context.Context, query *dto.WmsShipmentQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	if query.ShipmentNo != nil && *query.ShipmentNo != "" {
		conditions = append(conditions, NewLikeCondition("shipment_no", *query.ShipmentNo))
	}
	if query.PickingTaskID != nil {
		conditions = append(conditions, NewEqualCondition("picking_task_id", *query.PickingTaskID))
	}
	if query.NotificationID != nil {
		conditions = append(conditions, NewEqualCondition("notification_id", *query.NotificationID))
	}
	if query.CarrierID != nil {
		conditions = append(conditions, NewEqualCondition("carrier_id", *query.CarrierID))
	}
	if query.TrackingNo != nil && *query.TrackingNo != "" {
		conditions = append(conditions, NewLikeCondition("tracking_no", *query.TrackingNo))
	}
	if query.ShippingMethod != nil && *query.ShippingMethod != "" {
		conditions = append(conditions, NewEqualCondition("shipping_method", *query.ShippingMethod))
	}
	if query.Status != nil && *query.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.ConsigneeName != nil && *query.ConsigneeName != "" {
		conditions = append(conditions, NewLikeCondition("consignee_name", *query.ConsigneeName))
	}
	if query.ShipmentDateStart != nil && query.ShipmentDateEnd != nil {
		conditions = append(conditions, NewBetweenCondition("shipment_date", *query.ShipmentDateStart, *query.ShipmentDateEnd))
	}
	if query.CreatedAtStart != nil && query.CreatedAtEnd != nil {
		conditions = append(conditions, NewBetweenCondition("created_at", *query.CreatedAtStart, *query.CreatedAtEnd))
	}

	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// FindByShipmentNo 根据发运单号查找
func (r *wmsShipmentRepository) FindByShipmentNo(ctx context.Context, shipmentNo string) (*entity.WmsShipment, error) {
	var shipment entity.WmsShipment
	db := r.GetDB(ctx)

	result := db.Where("shipment_no = ?", shipmentNo).First(&shipment)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "发运单不存在").WithCause(result.Error)
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("根据发运单号查询失败: shipmentNo=%s", shipmentNo)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询发运单失败").WithCause(result.Error)
	}

	return &shipment, nil
}

// IsShipmentNoExist 检查发运单号是否存在
func (r *wmsShipmentRepository) IsShipmentNoExist(ctx context.Context, shipmentNo string, excludeID uint) (bool, error) {
	conditions := []QueryCondition{
		NewEqualCondition("shipment_no", shipmentNo),
	}
	if excludeID > 0 {
		conditions = append(conditions, NewNotEqualCondition("id", excludeID))
	}
	return r.Exists(ctx, conditions)
}

// FindByPickingTaskID 根据拣货任务ID查询发运单
func (r *wmsShipmentRepository) FindByPickingTaskID(ctx context.Context, pickingTaskID uint) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("picking_task_id", pickingTaskID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByNotificationID 根据出库通知单ID查询发运单
func (r *wmsShipmentRepository) FindByNotificationID(ctx context.Context, notificationID uint) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("notification_id", notificationID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByCarrierID 根据承运商ID查询发运单
func (r *wmsShipmentRepository) FindByCarrierID(ctx context.Context, carrierID uint) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("carrier_id", carrierID),
	}
	sortInfos := []response.SortInfo{
		{Field: "shipment_date", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByTrackingNo 根据运单号查询发运单
func (r *wmsShipmentRepository) FindByTrackingNo(ctx context.Context, trackingNo string) (*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("tracking_no", trackingNo),
	}

	return r.FindOneByCondition(ctx, conditions)
}

// GetByStatus 获取指定状态的发运单
func (r *wmsShipmentRepository) GetByStatus(ctx context.Context, status string) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", status),
	}
	sortInfos := []response.SortInfo{
		{Field: "shipment_date", Order: "asc"},
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// BatchCreate 批量创建发运单
func (r *wmsShipmentRepository) BatchCreate(ctx context.Context, shipments []*entity.WmsShipment) error {
	if len(shipments) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(shipments, 100) // 每批100条
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建发运单失败: count=%d", len(shipments))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建发运单失败").WithCause(result.Error)
	}

	return nil
}

// UpdateStatus 更新发运单状态
func (r *wmsShipmentRepository) UpdateStatus(ctx context.Context, id uint, status string, remark string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{"status": status}
	if remark != "" {
		updates["remark"] = remark
	}

	result := db.Model(&entity.WmsShipment{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新发运单状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新发运单状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "发运单不存在")
	}

	return nil
}

// BatchUpdateStatus 批量更新状态
func (r *wmsShipmentRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string, remark string) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	updates := map[string]interface{}{"status": status}
	if remark != "" {
		updates["remark"] = remark
	}

	result := db.Model(&entity.WmsShipment{}).Where("id IN ?", ids).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量更新发运单状态失败: ids=%v, status=%s", ids, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新发运单状态失败").WithCause(result.Error)
	}

	return nil
}

// PackShipment 打包确认
func (r *wmsShipmentRepository) PackShipment(ctx context.Context, id uint, packageCount int, totalWeight, totalVolume *float64, remark string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{
		"package_count": packageCount,
		"status":        string(entity.ShipmentStatusPacked),
	}
	if totalWeight != nil {
		updates["total_weight"] = *totalWeight
	}
	if totalVolume != nil {
		updates["total_volume"] = *totalVolume
	}
	if remark != "" {
		updates["remark"] = remark
	}

	result := db.Model(&entity.WmsShipment{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("打包确认失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "打包确认失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "发运单不存在")
	}

	return nil
}

// ShipConfirm 发运确认
func (r *wmsShipmentRepository) ShipConfirm(ctx context.Context, id uint, carrierID *uint, trackingNo, shippingMethod *string, shipmentDate *time.Time, shippingCost *float64, remark string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{
		"status": string(entity.ShipmentStatusShipped),
	}
	if carrierID != nil {
		updates["carrier_id"] = *carrierID
	}
	if trackingNo != nil {
		updates["tracking_no"] = *trackingNo
	}
	if shippingMethod != nil {
		updates["shipping_method"] = *shippingMethod
	}
	if shipmentDate != nil {
		updates["shipment_date"] = *shipmentDate
	}
	if shippingCost != nil {
		updates["shipping_cost"] = *shippingCost
	}
	if remark != "" {
		updates["remark"] = remark
	}

	result := db.Model(&entity.WmsShipment{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("发运确认失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "发运确认失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "发运单不存在")
	}

	return nil
}

// UpdateTrackingStatus 更新运输状态
func (r *wmsShipmentRepository) UpdateTrackingStatus(ctx context.Context, id uint, status, trackingInfo *string, actualDelivery *time.Time, remark string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{}

	if status != nil {
		updates["status"] = *status
	}
	if trackingInfo != nil {
		updates["tracking_info"] = *trackingInfo
	}
	if actualDelivery != nil {
		updates["actual_delivery"] = *actualDelivery
	}
	if remark != "" {
		updates["remark"] = remark
	}

	if len(updates) == 0 {
		return nil // 没有需要更新的字段
	}

	result := db.Model(&entity.WmsShipment{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新运输状态失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新运输状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "发运单不存在")
	}

	return nil
}

// DeliveryConfirm 签收确认
func (r *wmsShipmentRepository) DeliveryConfirm(ctx context.Context, id uint, actualDelivery *time.Time, receiverName, receiverPhone, deliveryProof, remark *string) error {
	db := r.GetDB(ctx)
	updates := map[string]interface{}{
		"status": string(entity.ShipmentStatusDelivered),
	}

	if actualDelivery != nil {
		updates["actual_delivery"] = *actualDelivery
	}
	if receiverName != nil {
		updates["receiver_name"] = *receiverName
	}
	if receiverPhone != nil {
		updates["receiver_phone"] = *receiverPhone
	}
	if deliveryProof != nil {
		updates["delivery_proof"] = *deliveryProof
	}
	if remark != nil {
		updates["remark"] = *remark
	}

	result := db.Model(&entity.WmsShipment{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("签收确认失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "签收确认失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "发运单不存在")
	}

	return nil
}

// GetByShipmentDateRange 根据发运日期范围查询
func (r *wmsShipmentRepository) GetByShipmentDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewBetweenCondition("shipment_date", startDate, endDate),
	}
	sortInfos := []response.SortInfo{
		{Field: "shipment_date", Order: "asc"},
		{Field: "shipment_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetPendingPacking 获取待打包的发运单
func (r *wmsShipmentRepository) GetPendingPacking(ctx context.Context) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", string(entity.ShipmentStatusPreparing)),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetReadyToShip 获取待发运的发运单
func (r *wmsShipmentRepository) GetReadyToShip(ctx context.Context) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewInCondition("status", []interface{}{
			string(entity.ShipmentStatusReady),
			string(entity.ShipmentStatusPacked),
		}),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetInTransit 获取运输中的发运单
func (r *wmsShipmentRepository) GetInTransit(ctx context.Context) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", string(entity.ShipmentStatusInTransit)),
	}
	sortInfos := []response.SortInfo{
		{Field: "shipment_date", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetOverdueShipments 获取过期未发运的发运单
func (r *wmsShipmentRepository) GetOverdueShipments(ctx context.Context) ([]*entity.WmsShipment, error) {
	now := time.Now()
	conditions := []QueryCondition{
		NewInCondition("status", []interface{}{
			string(entity.ShipmentStatusPreparing),
			string(entity.ShipmentStatusReady),
			string(entity.ShipmentStatusPacked),
		}),
		NewLessThanCondition("estimated_delivery", now),
	}
	sortInfos := []response.SortInfo{
		{Field: "estimated_delivery", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetShipmentStats 获取发运统计信息
func (r *wmsShipmentRepository) GetShipmentStats(ctx context.Context, startDate, endDate *time.Time, carrierID *uint) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var stats struct {
		TotalCount     int64   `json:"total_count"`
		TotalPackages  int64   `json:"total_packages"`
		TotalWeight    float64 `json:"total_weight"`
		TotalVolume    float64 `json:"total_volume"`
		TotalCost      float64 `json:"total_cost"`
		PreparingCount int64   `json:"preparing_count"`
		ReadyCount     int64   `json:"ready_count"`
		ShippedCount   int64   `json:"shipped_count"`
		InTransitCount int64   `json:"in_transit_count"`
		DeliveredCount int64   `json:"delivered_count"`
		ReturnedCount  int64   `json:"returned_count"`
	}

	query := db.Model(&entity.WmsShipment{})
	if carrierID != nil {
		query = query.Where("carrier_id = ?", *carrierID)
	}
	if startDate != nil && endDate != nil {
		query = query.Where("created_at BETWEEN ? AND ?", *startDate, *endDate)
	}

	// 基础统计
	if err := query.Select(`
		COUNT(*) as total_count,
		COALESCE(SUM(package_count), 0) as total_packages,
		COALESCE(SUM(total_weight), 0) as total_weight,
		COALESCE(SUM(total_volume), 0) as total_volume,
		COALESCE(SUM(shipping_cost), 0) as total_cost
	`).Scan(&stats).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取发运单基础统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取统计数据失败").WithCause(err)
	}

	// 各状态统计
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	statusQuery := db.Model(&entity.WmsShipment{}).Select("status, COUNT(*) as count")
	if carrierID != nil {
		statusQuery = statusQuery.Where("carrier_id = ?", *carrierID)
	}
	if startDate != nil && endDate != nil {
		statusQuery = statusQuery.Where("created_at BETWEEN ? AND ?", *startDate, *endDate)
	}

	if err := statusQuery.Group("status").Scan(&statusCounts).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取发运单状态统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取状态统计失败").WithCause(err)
	}

	// 填充状态统计
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(entity.ShipmentStatusPreparing):
			stats.PreparingCount = sc.Count
		case string(entity.ShipmentStatusReady):
			stats.ReadyCount = sc.Count
		case string(entity.ShipmentStatusShipped):
			stats.ShippedCount = sc.Count
		case string(entity.ShipmentStatusInTransit):
			stats.InTransitCount = sc.Count
		case string(entity.ShipmentStatusDelivered):
			stats.DeliveredCount = sc.Count
		case string(entity.ShipmentStatusReturned):
			stats.ReturnedCount = sc.Count
		}
	}

	result := map[string]interface{}{
		"total_count":      stats.TotalCount,
		"total_packages":   stats.TotalPackages,
		"total_weight":     stats.TotalWeight,
		"total_volume":     stats.TotalVolume,
		"total_cost":       stats.TotalCost,
		"preparing_count":  stats.PreparingCount,
		"ready_count":      stats.ReadyCount,
		"shipped_count":    stats.ShippedCount,
		"in_transit_count": stats.InTransitCount,
		"delivered_count":  stats.DeliveredCount,
		"returned_count":   stats.ReturnedCount,
	}

	return result, nil
}

// FindByConsignee 根据收货人查询发运单
func (r *wmsShipmentRepository) FindByConsignee(ctx context.Context, consigneeName string) ([]*entity.WmsShipment, error) {
	conditions := []QueryCondition{
		NewLikeCondition("consignee_name", consigneeName),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetShippingCostStats 获取运费统计
func (r *wmsShipmentRepository) GetShippingCostStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var stats struct {
		TotalCost   float64 `json:"total_cost"`
		AvgCost     float64 `json:"avg_cost"`
		MaxCost     float64 `json:"max_cost"`
		MinCost     float64 `json:"min_cost"`
		TotalOrders int64   `json:"total_orders"`
	}

	if err := db.Model(&entity.WmsShipment{}).
		Where("created_at BETWEEN ? AND ? AND shipping_cost IS NOT NULL", startDate, endDate).
		Select(`
			COALESCE(SUM(shipping_cost), 0) as total_cost,
			COALESCE(AVG(shipping_cost), 0) as avg_cost,
			COALESCE(MAX(shipping_cost), 0) as max_cost,
			COALESCE(MIN(shipping_cost), 0) as min_cost,
			COUNT(*) as total_orders
		`).
		Scan(&stats).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Error("获取运费统计失败")
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取运费统计失败").WithCause(err)
	}

	result := map[string]interface{}{
		"total_cost":   stats.TotalCost,
		"avg_cost":     stats.AvgCost,
		"max_cost":     stats.MaxCost,
		"min_cost":     stats.MinCost,
		"total_orders": stats.TotalOrders,
	}

	return result, nil
}
