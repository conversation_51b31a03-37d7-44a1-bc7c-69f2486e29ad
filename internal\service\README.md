# `internal/service` 技术文档

## 1. 概述

`service` 包是应用的业务逻辑核心层，负责处理具体的业务规则、数据编排、与外部服务交互以及协调数据访问层（`repository`）的操作。它充当着表现层（如 API Controllers/Handlers）和数据持久化层之间的桥梁。

该包的核心设计目标是：

- **封装业务逻辑**：将复杂的业务流程和规则集中在服务中。
- **服务编排**：协调多个仓库或其他服务共同完成一个业务操作。
- **可测试性**：服务接口清晰，易于进行单元测试和集成测试。
- **事务管理**：通过 `ServiceManager` 提供统一的事务处理机制。
- **依赖管理**：通过 `ServiceManager` 集中管理服务实例及其依赖。

## 2. 核心概念

### 2.1. `BaseService` 和 `BaseServiceImpl` (`base_service.go`)

`BaseService` 是一个基础服务接口，`BaseServiceImpl` 是其具体实现。它们为所有具体服务提供了一些共享功能：

- **`GetServiceManager() *ServiceManager`**: 获取对 `ServiceManager` 的引用，允许服务访问其他服务或仓库。
- **`GetLogger() logger.Logger`**: 获取日志记录器实例。
- **`GetUserIDFromContext(ctx context.Context) (uint64, error)`**: 从标准 `context.Context` 中安全地提取用户 ID。如果提取失败或 ID 无效，返回错误。
- **`GetAccountBookIDFromContext(ctx context.Context) (uint64, error)`**: 从标准 `context.Context` 中安全地提取账套 ID。如果提取失败或 ID 无效，返回错误。
- **`GetUsernameFromContext(ctx context.Context) (string, error)`**: 从标准 `context.Context` 中安全地提取用户名。
- **`GetRoleIDsFromContext(ctx context.Context) ([]uint, error)`**: 从标准 `context.Context` 中安全地提取角色 ID 列表。
- **`IsAdminFromContext(ctx context.Context) (bool, error)`**: 从标准 `context.Context` 中安全地提取用户是否为管理员的标识。
  这些方法内部直接调用 `pkg/util/context.go` 中对应的基于标准上下文的辅助函数（如 `GetUserIDFromStdContext`）来完成实际的提取和验证逻辑。这确保了上下文处理的一致性和健壮性。

具体服务通过嵌入 `BaseServiceImpl` 来继承这些通用能力。

### 2.2. `ServiceManager` (`service_manager.go`)

`ServiceManager` 是服务层的核心，负责管理服务实例的生命周期、依赖注入和事务。

**主要功能：**

- **服务实例管理**：
  - `NewServiceManager(...)`: 创建服务管理器实例，注入核心依赖如 `RepositoryManager`、应用配置、数据库连接等。
  - `GetService[S any](m *ServiceManager, factory func(*ServiceManager) S) S`: 泛型方法，用于懒加载并缓存服务实例。服务类型作为缓存键，确保在单个 `ServiceManager` 实例范围内服务的单例性。
  - 为每个具体服务提供了便捷的 Getter 方法（如 `GetUserService()`, `GetAuthService()`），内部调用 `GetService`。
- **依赖注入**：向创建的服务实例注入其所需的依赖（如其他服务、配置、仓库管理器等）。
- **事务管理**：
  - `WithTransaction(fn func(txServiceManager *ServiceManager) error) error`: 执行数据库事务。它利用 `RepositoryManager` 的事务能力，并创建一个事务性的 `ServiceManager` (`txServiceManager`) 传递给回调函数 `fn`。在 `fn` 内部通过 `txServiceManager` 获取的任何服务或仓库都将参与当前事务。
  - `WithTransactionByLevel(level string, fn func(txServiceManager *ServiceManager) error) error`: 功能同上，但允许指定事务隔离级别。
- **上下文传递**：
  - `WithContext(ctx context.Context) *ServiceManager`: 创建一个新的 `ServiceManager` 实例，并关联指定的请求上下文。
- **共享资源访问**：提供对 `RepositoryManager`、`*sql.DB`、`config.Configuration`、`storage.Storage`、`redis.Client` (懒加载) 等共享资源的访问方法。

## 3. 具体服务实现

所有具体服务都遵循类似的模式：

1. 定义一个接口（例如 `UserService`），该接口可以嵌入 `BaseService`，并声明该服务特有的业务方法。
2. 定义一个结构体（例如 `UserServiceImpl`），该结构体嵌入 `BaseServiceImpl`，并实现接口中定义的方法。
3. 提供一个构造函数（例如 `NewUserService(sm *ServiceManager) UserService`），接收 `ServiceManager` 作为参数以解决依赖。
4. 服务方法通常接收 `context.Context` 和 DTO (Data Transfer Object) 作为输入，执行业务逻辑（可能调用一个或多个 Repository 方法，或者其他 Service 方法），并返回 VO (View Object) 或错误。

### 3.1. `AccountBookService` (`sys_account_book_service_impl.go`)

- **职责**：负责账套（AccountBook）相关的业务逻辑。
- **主要功能**：
  - 创建、更新、删除、查询单个/列表/分页账套信息。
  - 更新账套状态。
  - 参数校验：账套编码和名称的唯一性。
  - 数据转换：DTO -> Entity -> VO。
  - 事务性删除：删除前检查账套是否已分配给用户。
- **依赖**：`UserRepository`, `AccountBookRepository`, `UserAccountBookRepository`.

### 3.2. `RoleService` (`sys_role_service_impl.go`)

- **职责**：负责角色（Role）相关的业务逻辑。
- **主要功能**：
  - 创建、更新、删除、查询单个/列表/分页角色信息。
  - 更新角色状态。
  - 角色菜单管理：获取和更新角色的菜单关联。
  - 角色用户管理：分页查询角色下的用户，统计用户数。
  - 参数校验：角色名称和编码的唯一性。
  - 事务性操作：创建、更新、删除角色（删除时检查是否为系统角色、是否关联用户，并清理菜单关联）。
- **依赖**：`RoleRepository`, `RoleMenuRepository`, `UserRoleRepository`, `MenuService`.

### 3.3. `UserService` (`sys_user_service_impl.go`)

- **职责**：负责用户（User）相关的核心业务逻辑。
- **主要功能**：
  - 全面的用户 CRUD 操作（创建、更新、删除、按 ID/用户名查询）。
  - 多种分页查询：简单分页、完整信息分页、在线用户分页（规划中）。
  - 用户密码管理：用户修改密码（需旧密码）、管理员重置密码。
  - 用户状态管理、头像更新、安全锁定/解锁。
  - 用户个人资料管理。
  - 用户关系管理：更新/获取用户的角色列表、账套列表、设置默认账套。
  - 参数校验：用户名、邮箱、手机号的唯一性。
  - 事务性操作：创建、更新、删除用户（删除时检查是否为系统管理员，并清理相关联的角色、账套）。
- **依赖**：`UserRepository`, `UserRoleRepository`, `UserAccountBookRepository`, `RoleRepository`, `AccountBookRepository`.

### 3.4. `TokenService` (`app_token_service_impl.go`)

- **职责**：负责 JWT (JSON Web Tokens) 的生成、解析和管理，用于用户认证。
- **主要功能**：
  - `GenerateTokens`: 生成访问令牌（Access Token）和刷新令牌（Refresh Token）。访问令牌包含用户信息，刷新令牌用于获取新的访问令牌。
  - `ParseAccessToken`: 解析并验证访问令牌。
  - `ParseRefreshToken`: 解析并验证刷新令牌（结合缓存状态）。
  - `RevokeRefreshToken`: 使指定的刷新令牌失效（从缓存中移除）。
  - 刷新令牌状态管理：将刷新令牌及其关联的用户 ID 存入缓存服务，并设置相应的过期时间。
- **依赖**：`CacheService`, `config.Configuration`.

### 3.5. `MenuService` (`sys_menu_service_impl.go`)

- **职责**：负责菜单（Menu）和权限相关的业务逻辑。
- **主要功能**：
  - 菜单 CRUD 操作。
  - 菜单树构建：`GetMenuTree`（获取完整或过滤后的菜单树），`GetUserMenuTree`（获取用户有权访问的菜单树）。
  - 用户权限查询：`GetUserMenus`（获取用户有权的扁平菜单列表），`GetUserPermissions`（获取用户的权限标识符列表）。
  - 更新菜单状态。
  - 参数校验：同级菜单名称唯一性，权限标识全局唯一性。
  - 事务性删除：删除菜单前检查是否有子菜单，并清理与角色的关联。
- **依赖**：`MenuRepository`, `RoleMenuRepository`, `UserRoleRepository`.

### 3.6. `AuthService` (`app_auth_service_impl.go`)

- **职责**：负责用户认证的核心流程，主要是登录。
- **主要功能**：
  - `Login`: 处理用户登录请求。
    - 验证码校验（如果启用）。
    - 在事务中执行：查找用户、校验状态、校验密码、获取用户角色、生成令牌对、更新用户登录信息（IP 和时间）。
- **依赖**：`UserService`, `CaptchaService`, `TokenService`, `UserRepository`, `config.Configuration`.

### 3.7. `FileService` (`app_file_service_impl.go`)

- **职责**：负责文件的上传、下载、删除及相关元数据管理。
- **主要功能**：
  - `UploadFile` / `UploadGeneratedFile`: 处理文件上传，包括文件大小和类型校验、生成唯一存储路径、调用存储驱动保存文件、创建和保存文件元数据。如果元数据保存失败，会尝试删除已上传的物理文件。
  - `DownloadFile`: 根据文件 ID 提供文件流式下载。
  - `DeleteFile`: 事务性删除文件，包括从存储后端删除物理文件和从数据库删除元数据。
  - 元数据查询：按 ID 查询、按业务标识查询。
  - `GetFileURL`: 生成文件的可访问 URL。
- **依赖**：`config.Configuration`, `storage.Storage` (存储驱动接口), `FileMetadataRepository`, `RepositoryManager`.

### 3.8. `CaptchaService` (`app_captcha_service_impl.go`)

- **职责**：负责生成和验证图形验证码。
- **主要功能**：
  - `GenerateCaptcha`: 生成验证码图片（Base64 编码）和唯一 ID，并将 ID 和答案存入缓存。
  - `VerifyCaptcha`: 验证用户提交的验证码 ID 和答案是否匹配，验证成功后会清除缓存中的记录。
  - 依赖第三方库 `base64Captcha`，并使用 `CacheService` 作为其存储后端。
- **依赖**：`logger.Logger`, `config.Configuration`, `CacheService`.

### 3.9. `UserAccountBookService` (`sys_user_account_book_service_impl.go`)

- **职责**：管理用户与账套之间的关联关系。
- **主要功能**：
  - 将账套分配给用户、从用户移除账套关联、批量更新用户的账套。
  - 查询用户有权访问的账套列表（管理员可访问所有有效账套）。
  - 分页查询有权访问指定账套的用户列表。
  - 检查用户对特定账套的访问权限。
- **依赖**：`UserAccountBookRepository`, `UserRepository`, `AccountBookRepository`, `AccountBookService` (用于 VO 转换), `UserService` (用于 VO 转换).

### 3.10. `CacheService` (`app_cache_service_impl.go`)

- **职责**：提供通用的缓存抽象层，支持 Redis 和内存缓存作为后端。
- **主要功能**：
  - 提供 `Set`, `Get`, `Delete`, `Exists`, `Increment`, `Expire` 等标准缓存操作接口。
  - 根据配置和 Redis 可用性自动选择后端（优先 Redis）。
  - `GetCaptchaStore`: 为 `CaptchaService` 提供一个适配 `base64Captcha.Store` 接口的存储实例（基于 Redis 或内存）。
- **依赖**：`redis.Client` (可选), `config.Configuration`.

### 3.11. `LicenseService` (`app_license_service_impl.go`)

- **职责**：处理应用的授权（License）文件。
- **主要功能**：
  - `UpdateLicenseFromUpload`: 接收上传的授权文件内容，进行验证（签名、有效期）、持久化保存到服务器指定路径，并调用 `pkg/license.UpdateRuntimeLicense` 更新应用的运行时授权状态。
- **依赖**：`config.Configuration`, `pkg/license` (授权处理库).

### 3.12. `SequenceService` (`app_sequence_service_impl.go`)

- **职责**：生成唯一的、按天重置的序列号或单据号。
- **主要功能**：
  - `GenerateDailyDocumentNumber`: 根据单据类型前缀和日期，利用 `CacheService` 的原子递增功能生成序列号，并格式化为完整的单据号。当日首次生成时会为缓存键设置过期时间。
  - 如果 `CacheService` 不可用，服务将返回错误表明自身不可用。
- **依赖**：`CacheService`.

## 4. 事务处理

服务层的事务主要通过 `ServiceManager` 的 `WithTransaction` 或 `WithTransactionByLevel` 方法实现。当一个业务操作需要跨多个数据仓库的写操作，或者需要确保一系列操作的原子性时，应将这些操作封装在传递给事务方法的回调函数中。

```go
err := serviceManager.WithTransaction(func(txSm *service.ServiceManager) error {
    // 通过 txSm 获取事务性的服务实例
    txUserSvc := txSm.GetUserService()
    txRoleSvc := txSm.GetRoleService()

    // ... 执行业务操作 ...
    if err := txUserSvc.SomeTransactionalOperation(ctx, ...); err != nil {
        return err // 返回错误将导致事务回滚
    }
    // ...
    return nil // 返回 nil 将导致事务提交
})
```

在回调函数内部，通过事务性的 `ServiceManager` (`txSm`) 获取的服务实例，其底层的仓库操作将自动使用同一个数据库事务连接。

## 5. 错误处理和日志

- **错误处理**：服务层方法在遇到业务校验失败、数据访问错误或依赖服务错误时，会返回封装后的错误。通常使用 `pkg/errors` (`apperrors`) 包中定义的 `CustomError` 类型，包含错误码、消息和可选的原始错误 (`cause`)。错误会向上传播到表现层进行统一处理。
- **日志记录**：服务层在关键业务操作的开始、结束、遇到错误或重要分支时，会使用 `pkg/logger` 进行结构化日志记录。日志通常包含请求上下文信息（如 Trace ID）、操作相关的参数和错误详情。

## 6. 与其他层的交互

- **与表现层 (Controller/Handler)**：服务层接收来自表现层的调用，通常是 DTO 对象。处理完毕后，向表现层返回 VO 对象或错误。
- **与数据访问层 (Repository)**：服务层调用 `Repository` 接口执行数据持久化和检索操作。它不直接与 GORM 或数据库驱动交互，而是通过 `RepositoryManager` 获取仓库实例。
- **服务间调用**：一个服务可以调用另一个服务来复用业务逻辑，这通过 `ServiceManager` 实现。

通过这种分层和依赖管理机制，`service` 包实现了业务逻辑的清晰分离和高内聚，提升了代码的可维护性和可测试性。
