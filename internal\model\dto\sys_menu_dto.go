package dto

import "backend/pkg/response" // <<< 确保导入 response 包

// MenuCreateOrUpdateDTO 菜单创建/更新数据传输对象
// 用于接收创建或更新菜单的请求参数
type MenuCreateOrUpdateDTO struct {
	TenantDTO         // 继承租户信息
	ParentID   uint   `json:"parentId"`                             // 父菜单ID：上级菜单的ID，0表示顶级菜单
	Name       string `json:"name" validate:"required,max=50"`      // 菜单名称：菜单在系统中的名称标识，必填，最大长度50
	Title      string `json:"title" validate:"required,max=50"`     // 标题：菜单在界面上显示的名称，必填，最大长度50
	Path       string `json:"path" validate:"max=200"`              // 路由地址：前端路由的访问路径，可选，最大长度200
	Component  string `json:"component" validate:"max=255"`         // 组件路径：前端组件的路径，可选，最大长度255
	Redirect   string `json:"redirect" validate:"max=255"`          // 重定向路径：访问该菜单时重定向的目标路径，可选，最大长度255
	Icon       string `json:"icon" validate:"max=100"`              // 菜单图标：菜单的图标名称或图标URL，可选，最大长度100
	Type       int    `json:"type" validate:"required,oneof=1 2 3"` // 菜单类型：必填，1-目录，2-菜单，3-按钮
	Permission string `json:"permission" validate:"max=100"`        // 权限标识：用于权限控制的标识符，如"system:user:list"，可选，最大长度100
	Status     int    `json:"status" validate:"oneof=0 1"`          // 状态：菜单状态，0-禁用，1-正常
	Sort       int    `json:"sort" validate:"gte=0"`                // 排序：菜单的显示顺序，数值越小越靠前，必须大于等于0
	Hidden     bool   `json:"hidden"`                               // 是否隐藏：true-隐藏（不在菜单中显示），false-显示
	NoCache    bool   `json:"noCache"`                              // 是否不缓存：true-不缓存（每次都重新加载），false-缓存（使用缓存提高性能）
	AlwaysShow bool   `json:"alwaysShow"`                           // 是否总是显示：true-总是显示（即使只有一个子菜单也显示父菜单），false-自动判断
	Breadcrumb bool   `json:"breadcrumb"`                           // 是否显示面包屑：true-在面包屑中显示，false-在面包屑中隐藏
	ActiveMenu string `json:"activeMenu" validate:"max=255"`        // 高亮菜单：当前路由为子路由时，指定高亮的父级菜单，可选，最大长度255
	Remark     string `json:"remark" validate:"max=255"`            // 备注：关于菜单的附加说明信息，可选，最大长度255
	IsExternal bool   `json:"isExternal"`                           // 是否外链：true-是外链（在新窗口打开），false-非外链
}

// MenuDeleteDTO 删除菜单
// 用于接收删除菜单的查询参数
type MenuDeleteDTO struct {
	Ids []uint `json:"ids" validate:"required,min=1,dive,gt=0"` //
}

// MenuPageQueryDTO 菜单分页查询数据传输对象 (新增)
// 用于接收菜单列表的分页查询参数，包含过滤、分页和排序
type MenuPageQueryDTO struct {
	PageQuery response.PageQuery // 分页查询参数：包含页码和每页大小 (不需要 query 标签，由 BuildPageQuery 处理)
	Name               *string `query:"name"`       // 菜单名称 (模糊查询), 可选
	Title              *string `query:"title"`      // 菜单标题 (模糊查询), 可选
	Status             *int    `query:"status"`     // 菜单状态 (精确查询), 可选
	Type               *int    `query:"type"`       // 菜单类型 (精确查询), 可选
	Permission         *string `query:"permission"` // 权限标识 (模糊查询), 可选
}

// MenuUpdateStatusDTO 菜单状态更新数据传输对象
// 用于接收更新菜单状态的请求参数
type MenuUpdateStatusDTO struct {
	Status int `json:"status" validate:"required,oneof=0 1"` // 状态：菜单状态，必填，0-禁用，1-正常
}

// MenuTreeDTO 菜单树形结构数据传输对象
// 用于接收获取菜单树形结构的请求参数
type MenuTreeDTO struct {
	Status *int `json:"status"` // 状态：菜单状态，可选，0-禁用，1-正常
	Type   *int `json:"type"`   // 菜单类型：可选，1-目录，2-菜单，3-按钮
}

// MenuChildrenQueryDTO 菜单子节点懒加载查询数据传输对象 (新增)
// 用于根据父节点 ID 懒加载子菜单
type MenuChildrenQueryDTO struct {
	ParentID uint `json:"parentId" validate:"required,gte=0"`    // 父菜单ID：必需，指定要加载哪个节点的子菜单 (0 表示加载顶级菜单)
	Status   *int `json:"status" validate:"omitempty,oneof=0 1"` // 状态：可选，过滤子菜单状态，0-禁用，1-正常
	Type     *int `json:"type" validate:"omitempty,oneof=1 2 3"` // 菜单类型：可选，过滤子菜单类型，1-目录，2-菜单，3-按钮
}

// MenuMetaDTO 菜单元数据数据传输对象
// 用于前端路由配置中的meta字段
type MenuMetaDTO struct {
	Title      string `json:"title"`      // 标题：菜单在界面上显示的名称
	Icon       string `json:"icon"`       // 图标：菜单的图标名称或图标URL
	NoCache    bool   `json:"noCache"`    // 是否不缓存：true-不缓存，false-缓存
	Hidden     bool   `json:"hidden"`     // 是否隐藏：true-隐藏，false-显示
	AlwaysShow bool   `json:"alwaysShow"` // 是否总是显示：true-总是显示，false-自动判断
	Breadcrumb bool   `json:"breadcrumb"` // 是否显示面包屑：true-显示，false-隐藏
	ActiveMenu string `json:"activeMenu"` // 高亮菜单：当前路由为子路由时，指定高亮的父级菜单
}
