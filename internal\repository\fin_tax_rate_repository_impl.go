package repository

import (
	"context"

	"backend/internal/model/entity"
	"gorm.io/gorm"
)

// FinTaxRateRepository 定义了税率仓库的接口
type FinTaxRateRepository interface {
	BaseRepository[entity.FinTaxRate, uint]
	FindByName(ctx context.Context, name string) (*entity.FinTaxRate, error)
}

// finTaxRateRepositoryImpl 是 FinTaxRateRepository 的实现
type finTaxRateRepositoryImpl struct {
	BaseRepository[entity.FinTaxRate, uint]
}

// NewFinTaxRateRepository 创建一个新的税率仓库实例
func NewFinTaxRateRepository(db *gorm.DB) FinTaxRateRepository {
	baseRepo := NewBaseRepository[entity.FinTaxRate, uint](db)
	return &finTaxRateRepositoryImpl{
		BaseRepository: baseRepo,
	}
}

// FindByName 根据税率名称查询
func (r *finTaxRateRepositoryImpl) FindByName(ctx context.Context, name string) (*entity.FinTaxRate, error) {
	return r.FindOneByCondition(ctx, []QueryCondition{NewEqualCondition("name", name)})
} 