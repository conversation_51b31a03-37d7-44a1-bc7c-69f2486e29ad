package controller

import (
	"strconv"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
)

// WmsInventoryQueryController 定义库存查询控制器接口
type WmsInventoryQueryController interface {
	// 基础查询功能
	GetPage(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetByLocation(ctx iris.Context)
	GetByItem(ctx iris.Context)

	// 库存汇总统计
	GetSummary(ctx iris.Context)
	GetStats(ctx iris.Context)

	// 库存周转分析
	GetTurnover(ctx iris.Context)
	GetABCAnalysis(ctx iris.Context)

	// 库存可用性检查
	CheckAvailability(ctx iris.Context)
	GetAvailable(ctx iris.Context)

	// 库存历史记录
	GetTransactionHistory(ctx iris.Context)
	GetMovementHistory(ctx iris.Context)

	// 库存预警
	GetAlerts(ctx iris.Context)
	GetLowStockItems(ctx iris.Context)
	GetExpiringItems(ctx iris.Context)

	// 导入导出功能
	ExportToExcel(ctx iris.Context)
	ImportFromExcel(ctx iris.Context)

	// 批量操作
	BatchUpdate(ctx iris.Context)
	BatchFreeze(ctx iris.Context)
	BatchUnfreeze(ctx iris.Context)
}

// wmsInventoryQueryControllerImpl 库存查询控制器实现
type WmsInventoryQueryControllerImpl struct {
	BaseControllerImpl
	inventoryQueryService service.WmsInventoryQueryService
}

// NewWmsInventoryQueryControllerImpl 创建库存查询控制器实例
func NewWmsInventoryQueryControllerImpl(cm *ControllerManager) *WmsInventoryQueryControllerImpl {
	return &WmsInventoryQueryControllerImpl{
		BaseControllerImpl:    *NewBaseController(cm),
		inventoryQueryService: cm.GetServiceManager().GetWmsInventoryQueryService(),
	}
}

// GetPage 分页查询库存
func (c *WmsInventoryQueryControllerImpl) GetPage(ctx iris.Context) {
	var req dto.WmsInventoryQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5000, "请求参数格式错误")
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryQueryService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetByID 根据ID获取库存详情
func (c *WmsInventoryQueryControllerImpl) GetByID(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5000, "无效的ID参数")
		return
	}

	result, err := c.inventoryQueryService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetByLocation 根据库位获取库存
func (c *WmsInventoryQueryControllerImpl) GetByLocation(ctx iris.Context) {
	locationIdStr := ctx.Params().Get("locationId")
	locationId, err := strconv.ParseUint(locationIdStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5000, "无效的库位ID参数")
		return
	}

	result, err := c.inventoryQueryService.GetByLocation(ctx.Request().Context(), uint(locationId))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetByItem 根据物料获取库存
func (c *WmsInventoryQueryControllerImpl) GetByItem(ctx iris.Context) {
	itemIdStr := ctx.Params().Get("itemId")
	itemId, err := strconv.ParseUint(itemIdStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5000, "无效的物料ID参数")
		return
	}

	result, err := c.inventoryQueryService.GetByItem(ctx.Request().Context(), uint(itemId))
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetSummary 获取库存汇总
func (c *WmsInventoryQueryControllerImpl) GetSummary(ctx iris.Context) {
	var req dto.WmsInventorySummaryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5000, "请求参数格式错误")
		return
	}

	result, err := c.inventoryQueryService.GetSummary(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetStats 获取库存统计
func (c *WmsInventoryQueryControllerImpl) GetStats(ctx iris.Context) {
	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			response.Fail(ctx, 5000, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	result, err := c.inventoryQueryService.GetStats(ctx.Request().Context(), warehouseId)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// CheckAvailability 检查库存可用性
func (c *WmsInventoryQueryControllerImpl) CheckAvailability(ctx iris.Context) {
	var req dto.WmsInventoryAvailabilityReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.Fail(ctx, 5000, "请求参数格式错误")
		return
	}

	result, err := c.inventoryQueryService.CheckAvailability(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetAvailable 获取可用库存
func (c *WmsInventoryQueryControllerImpl) GetAvailable(ctx iris.Context) {
	itemIdStr := ctx.URLParam("itemId")
	itemId, err := strconv.ParseUint(itemIdStr, 10, 32)
	if err != nil {
		response.Fail(ctx, 5000, "无效的物料ID参数")
		return
	}

	requiredQtyStr := ctx.URLParam("requiredQty")
	requiredQty, err := strconv.ParseFloat(requiredQtyStr, 64)
	if err != nil {
		response.Fail(ctx, 5000, "无效的需求数量参数")
		return
	}

	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			response.Fail(ctx, 5000, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	result, err := c.inventoryQueryService.GetAvailable(ctx.Request().Context(), uint(itemId), warehouseId, requiredQty)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetLowStockItems 获取低库存物料
func (c *WmsInventoryQueryControllerImpl) GetLowStockItems(ctx iris.Context) {
	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			response.Fail(ctx, 5000, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	result, err := c.inventoryQueryService.GetLowStockItems(ctx.Request().Context(), warehouseId)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}

// GetExpiringItems 获取即将过期的物料
func (c *WmsInventoryQueryControllerImpl) GetExpiringItems(ctx iris.Context) {
	daysStr := ctx.URLParam("days")
	days := 30 // 默认30天
	if daysStr != "" {
		d, err := strconv.Atoi(daysStr)
		if err != nil {
			response.Fail(ctx, 5000, "无效的天数参数")
			return
		}
		days = d
	}

	var warehouseId *uint
	warehouseIdStr := ctx.URLParam("warehouseId")
	if warehouseIdStr != "" {
		id, err := strconv.ParseUint(warehouseIdStr, 10, 32)
		if err != nil {
			response.Fail(ctx, 5000, "无效的仓库ID参数")
			return
		}
		warehouseIdUint := uint(id)
		warehouseId = &warehouseIdUint
	}

	result, err := c.inventoryQueryService.GetExpiringItems(ctx.Request().Context(), days, warehouseId)
	if err != nil {
		response.FailWithError(ctx, err)
		return
	}

	response.Success(ctx, result)
}
