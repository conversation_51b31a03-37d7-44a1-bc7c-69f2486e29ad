package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
	"backend/pkg/util"

	"golang.org/x/crypto/bcrypt"
)

// UserService 用户服务接口
type UserService interface {
	// 基础服务
	BaseService

	// 基本CRUD操作
	// 创建用户
	// 用于创建用户(新增页)
	CreateUser(ctx context.Context, userDTO dto.UserCreateDTO) (*vo.UserVO, error)
	// 更新用户
	// 用于更新用户(编辑页)
	UpdateUser(ctx iris.Context, id uint, userDTO dto.UserUpdateDTO) (*vo.UserVO, error)
	// 删除用户
	// 用于删除用户(删除页)
	DeleteUser(ctx context.Context, id uint) error
	// 获取用户详情
	// 用于获取用户详情(详情页)
	GetUserByID(ctx context.Context, id uint) (*vo.UserVO, error)
	// 获取用户详情
	// 用于获取用户详情(详情页)
	GetUserByUsername(ctx context.Context, username string) (*vo.UserVO, error)

	// 分页查询
	// 简单分页查询 只返回用户ID、用户名、真实姓名、性别、状态、是否是管理员
	// 用于列表展示(列表页)
	SimplePageUsers(ctx context.Context, queryDTO dto.UserPageQueryDTO) ([]*vo.UserSimpleVO, error)
	// 完整分页查询 返回用户所有信息
	// 用于详情展示(详情页)
	FullPageUsers(ctx context.Context, queryDTO dto.UserPageQueryDTO) (*response.PageResult, error)
	// 在线分页查询 返回在线用户信息
	// 用于在线用户展示(在线用户页)
	OnlinePageUsers(ctx context.Context, queryDTO dto.UserPageQueryDTO) ([]*vo.UserOnlineVO, error)

	// 特殊功能
	UpdatePassword(ctx context.Context, id uint, oldPassword, newPassword string, requesterID uint) error
	UpdateStatus(ctx context.Context, id uint, status int) error

	// 用户个人资料管理
	// 用于获取用户个人资料(个人资料页)
	GetUserProfile(ctx context.Context, id uint) (*vo.UserProfileVO, error)
	// 用于更新用户个人资料(个人资料页)
	UpdateProfile(ctx context.Context, id uint, profileDTO dto.UserProfileUpdateDTO) error

	// 用户角色管理
	// 用于获取用户角色(角色管理页)
	GetUserRoles(ctx context.Context, id uint) ([]*vo.RoleVO, error)
	// 用于更新用户角色(角色管理页)
	UpdateUserRoles(ctx context.Context, id uint, roleIDs []uint) error

	// 用户关联帐套管理
	// 用于获取用户关联帐套(关联帐套页)
	GetUserAccountBooks(ctx context.Context, id uint) ([]*vo.AccountBookVO, error)
	// 用于更新用户关联帐套(关联帐套页)
	UpdateUserAccountBooks(ctx context.Context, id uint, accountBookIDs []uint) error
	// 用于更新默认帐套(默认帐套页)
	// 更新默认帐套
	UpdateDefaultAccountBook(ctx context.Context, id uint, accountBookID uint) error

	// 实体转换方法
	ConvertToUserSimpleVOList(ctx context.Context, users []*entity.User) ([]*vo.UserSimpleVO, error)

	// 管理员重置密码 (无需旧密码)
	AdminResetPassword(ctx context.Context, id uint, newPassword string) error

	// 更新用户头像 URL (新增)
	UpdateAvatarURL(ctx context.Context, userID uint, avatarURL string) error

	// 获取用户个人资料 (新增)
	GetProfile(ctx context.Context, userID uint) (*vo.UserProfileVO, error)

	// 安全锁定/解锁 (新增)
	LockUserSecurity(ctx context.Context, userID uint) error
	UnlockUserSecurity(ctx context.Context, userID uint) error
}

// UserServiceImpl 用户服务实现
type UserServiceImpl struct {
	*BaseServiceImpl
}

// NewUserService 创建用户服务
func NewUserService(serviceManager *ServiceManager) UserService {
	return &UserServiceImpl{
		BaseServiceImpl: NewBaseService(serviceManager),
	}
}

// GetUserService 从服务管理器获取用户服务
func GetUserService(serviceManager *ServiceManager) UserService {
	return GetService[UserService](
		serviceManager,
		func(sm *ServiceManager) UserService {
			return NewUserService(sm)
		},
	)
}

// getUserRepository 获取用户仓库
func (s *UserServiceImpl) getUserRepository() repository.UserRepository {
	return s.GetServiceManager().GetRepositoryManager().GetUserRepository()
}

// getUserRoleRepository 获取用户角色仓库
func (s *UserServiceImpl) getUserRoleRepository() repository.UserRoleRepository {
	return s.GetServiceManager().GetRepositoryManager().GetUserRoleRepository()
}

// GetUserRoles 获取用户角色
func (s *UserServiceImpl) GetUserRoles(ctx context.Context, id uint) ([]*vo.RoleVO, error) {
	s.GetLogger().Debug(ctx, "获取用户角色",
		logger.WithField("userId", id))

	// 这个方法可能仍然被 Controller 直接调用，所以保留其原始逻辑
	// 但 convertToUserVO/convertToUserProfileVO 将不再调用此方法
	userRoleRepo := s.getUserRoleRepository()
	roles, err := userRoleRepo.GetUserRoles(ctx, id, nil) // Returns []*entity.Role
	if err != nil {
		s.GetLogger().Error(ctx, "获取用户角色失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "获取用户角色数据库操作失败")
	}

	// --- 使用新的辅助函数处理指针切片 ---
	return s.convertRolePtrsToRoleVOs(roles), nil // <<< 修改此处调用
}

// UpdateUserRoles 更新用户角色
func (s *UserServiceImpl) UpdateUserRoles(ctx context.Context, id uint, roleIDs []uint) error {
	s.GetLogger().Debug(ctx, "更新用户角色",
		logger.WithField("userId", id),
		logger.WithField("count", len(roleIDs)))

	userRepo := s.getUserRepository()
	_, err := userRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询用户失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	userRoleRepo := s.getUserRoleRepository()
	err = userRoleRepo.SetUserRoles(ctx, id, roleIDs)
	if err != nil {
		s.GetLogger().Error(ctx, "更新用户角色失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户角色数据库操作失败")
	}

	return nil
}

// CreateUser 创建用户
func (s *UserServiceImpl) CreateUser(ctx context.Context, userDTO dto.UserCreateDTO) (*vo.UserVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "创建用户", logger.WithField("username", userDTO.Username))

	if err := s.validateUserCreateParams(ctx, userDTO, 0); err != nil {
		return nil, err
	}

	hashedPassword, err := util.EncryptPassword(userDTO.Password)
	if err != nil {
		log.Error(ctx, "加密密码失败", logger.WithField("username", userDTO.Username), logger.WithError(err))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "密码加密失败").WithCause(err)
	}

	user := &entity.User{
		Username:   userDTO.Username,
		Password:   hashedPassword,
		Nickname:   ValueFromPointer(userDTO.Nickname),
		RealName:   ValueFromPointer(userDTO.RealName),
		Email:      ValueFromPointer(userDTO.Email),
		Mobile:     ValueFromPointer(userDTO.Mobile),
		Remark:     ValueFromPointer(userDTO.Remark),
		Avatar:     ValueFromPointer(userDTO.Avatar),
		EmployeeID: userDTO.EmployeeID,
		ExpireTime: userDTO.ExpireTime,
	}
	if userDTO.Gender != nil {
		user.Gender = *userDTO.Gender
	}
	if userDTO.Status != nil {
		user.Status = *userDTO.Status
	} else {
		user.Status = 1
	}
	if userDTO.IsAdmin != nil {
		user.IsAdmin = *userDTO.IsAdmin
	}

	var createdUserID uint // Variable to store the ID after creation within the transaction

	// <<< 使用事务包裹数据库操作 >>>
	err = s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		txUserRepo := txSm.GetRepositoryManager().GetUserRepository()

		// 在事务开始时，首先检查 EmployeeID 是否唯一 (如果提供)
		if user.EmployeeID != nil && *user.EmployeeID > 0 {
			existingUser, findErr := txUserRepo.FindByEmployeeID(ctx, *user.EmployeeID)
			if findErr != nil && !errors.Is(findErr, apperrors.ErrNotFound) {
				// 如果查询本身出错，而不是"未找到"
				log.Error(ctx, "检查员工ID唯一性时查询失败", logger.WithError(findErr), logger.WithField("employeeId", *user.EmployeeID))
				return apperrors.WrapError(findErr, apperrors.CODE_DATA_QUERY_FAILED, "检查员工ID唯一性时数据库操作失败")
			}
			if existingUser != nil {
				// 如果找到了用户，说明 EmployeeID 已被占用
				return apperrors.NewBizError(apperrors.CODE_DATA_CONFLICT, "员工ID已被占用")
			}
		}

		// Set CreatedBy and UpdatedBy
		uid64Create, errCreate := s.GetUserIDFromContext(ctx)
		if errCreate != nil {
			log.WithError(errCreate).Warn(ctx, "CreateUser: 无法从上下文中获取UserID")
			// 根据策略，这里可能需要返回错误或继续（如果允许匿名创建）
			// 为保持一致性，我们记录警告并继续，CreatedBy/UpdatedBy 将为 0
		}
		user.CreatedBy = uint(uid64Create)
		user.UpdatedBy = uint(uid64Create)

		// 1. 创建用户实体
		if createErr := txUserRepo.Create(ctx, user); createErr != nil {
			log.Error(ctx, "事务中创建用户失败", logger.WithError(createErr), logger.WithField("username", user.Username))
			return apperrors.WrapError(createErr, apperrors.CODE_DATA_CREATE_FAILED, "创建用户数据库操作失败")
		}
		createdUserID = user.ID // Capture the ID

		// 2. 更新用户关联关系 (角色和账套)
		if relationErr := s.updateUserRelations(ctx, txSm, user.ID, userDTO.RoleIDs, userDTO.AccountBookIDs); relationErr != nil {
			log.Error(ctx, "事务中更新用户关联失败", logger.WithError(relationErr), logger.WithField("userId", user.ID))
			// Error from updateUserRelations is already wrapped
			return relationErr
		}

		return nil // 事务成功
	})

	if err != nil {
		// 事务执行失败，错误已在内部记录和包装
		return nil, err
	}

	// <<< 事务成功后，查询包含关联数据的新用户 >>>
	userRepo := s.getUserRepository() // Use non-transactional repo for final read
	createdUser, findErr := userRepo.FindByID(ctx, createdUserID)
	if findErr != nil {
		log.Error(ctx, "创建用户后查询失败 (事务成功后)", logger.WithError(findErr), logger.WithField("userID", createdUserID))
		// 即使这里查询失败，用户和关联数据已成功创建，但返回前端可能需要处理
		return nil, apperrors.WrapError(findErr, apperrors.CODE_DATA_QUERY_FAILED, "创建用户后获取信息失败")
	}

	// --- 修正调用 convertToUserVO ---
	userVO := s.convertToUserVO(createdUser) // 调用简化版，无 ctx, 无 error
	if userVO == nil {                       // 理论上不应发生，但作为防御
		log.Error(ctx, "创建用户后转换VO失败 (返回nil)", logger.WithField("userID", createdUserID))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "创建用户后转换视图对象失败")
	}
	// 手动填充关联数据
	userVO.Roles = s.convertRolesToRoleVOs(createdUser.Roles)
	userVO.AccountBooks = s.convertAccountBooksToSimpleVOs(createdUser.AccountBooks)

	return userVO, nil
}

// UpdateUser 更新用户
func (s *UserServiceImpl) UpdateUser(ctx iris.Context, id uint, userDTO dto.UserUpdateDTO) (*vo.UserVO, error) {
	log := s.GetLogger()
	log.Debug(ctx, "更新用户", logger.WithField("userId", id))

	// <<< 先查询用户是否存在 >>>
	userRepo := s.getUserRepository() // Use non-tx repo for initial check
	user, err := userRepo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "更新用户前查询用户失败", logger.WithError(err), logger.WithField("userId", id))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	// --- 审计：记录 old_value ---
	// 在修改实体前，先序列化原始数据
	// 注意：脱敏处理，防止密码等敏感信息泄露到审计日志
	userForAudit := *user      // 创建一个副本以进行修改
	userForAudit.Password = "" // 清除敏感信息
	oldValueBytes, jsonErr := json.Marshal(userForAudit)
	if jsonErr != nil {
		log.Warn(ctx, "序列化 old_value 失败", logger.WithError(jsonErr), logger.WithField("userId", id))
		// 序列化失败不应阻塞主流程
	} else {
		ctx.Values().Set(constant.AUDIT_OLD_VALUE_CTX_KEY, string(oldValueBytes))
	}
	// --- 审计结束 ---

	if err := s.validateUserUpdateParams(ctx, userDTO, id); err != nil {
		return nil, err
	}

	// 更新实体字段 (在事务外准备好)
	user.Nickname = ValueFromPointer(userDTO.Nickname)
	user.RealName = ValueFromPointer(userDTO.RealName)
	user.Email = ValueFromPointer(userDTO.Email)
	user.Mobile = ValueFromPointer(userDTO.Mobile)
	user.Remark = ValueFromPointer(userDTO.Remark)
	user.EmployeeID = userDTO.EmployeeID
	user.ExpireTime = userDTO.ExpireTime
	if userDTO.Gender != nil {
		user.Gender = *userDTO.Gender
	}
	if userDTO.Status != nil {
		user.Status = *userDTO.Status
	}
	if userDTO.IsAdmin != nil {
		user.IsAdmin = *userDTO.IsAdmin
	}

	// <<< 使用事务包裹数据库操作 >>>
	err = s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		txUserRepo := txSm.GetRepositoryManager().GetUserRepository()

		// Set UpdatedBy
		uid64Update, errUpdate := s.GetUserIDFromContext(ctx)
		if errUpdate != nil {
			log.WithError(errUpdate).Warn(ctx, "UpdateUser: 无法从上下文中获取UserID")
			// 继续执行，UpdatedBy 可能不会被更新，或者如果 user 结构体有 gorm.Model，它可能会自动更新 UpdatedAt
		}
		user.UpdatedBy = uint(uid64Update)

		// 1. 更新用户基本信息
		if updateErr := txUserRepo.Update(ctx, user); updateErr != nil {
			log.Error(ctx, "事务中更新用户失败", logger.WithError(updateErr), logger.WithField("userId", id))
			return apperrors.WrapError(updateErr, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户数据库操作失败")
		}

		// 2. 更新关联关系
		if userDTO.RoleIDs != nil || userDTO.AccountBookIDs != nil { // Only call if there are relations to update
			if relationErr := s.updateUserRelations(ctx, txSm, id, userDTO.RoleIDs, userDTO.AccountBookIDs); relationErr != nil {
				log.Error(ctx, "事务中更新用户关联失败", logger.WithError(relationErr), logger.WithField("userId", id))
				// Error from updateUserRelations is already wrapped
				return relationErr
			}
		} else {
			log.Debug(ctx, "事务中跳过关联更新 (RoleIDs and AccountBookIDs are nil)", logger.WithField("userId", id))
		}

		return nil // 事务成功
	})

	if err != nil {
		// 事务执行失败，错误已在内部记录和包装
		return nil, err
	}

	// <<< 事务成功后，查询更新后的用户 >>>
	updatedUser, findErr := userRepo.FindByID(ctx, id) // Use non-tx repo for final read
	if findErr != nil {
		log.Error(ctx, "更新用户后查询失败 (事务成功后)", logger.WithError(findErr), logger.WithField("userID", id))
		return nil, apperrors.WrapError(findErr, apperrors.CODE_DATA_QUERY_FAILED, "更新用户后获取信息失败")
	}

	log.Debug(ctx, "准备调用 convertToUserVO", logger.WithField("userId", id))
	// --- 修正调用 convertToUserVO ---
	userVO := s.convertToUserVO(updatedUser) // 调用简化版，无 ctx, 无 error
	if userVO == nil {                       // 理论上不应发生
		log.Error(ctx, "更新用户后转换VO失败 (返回nil)", logger.WithField("userID", id))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "更新用户后转换视图对象失败")
	}
	// 手动填充关联数据
	userVO.Roles = s.convertRolesToRoleVOs(updatedUser.Roles)
	userVO.AccountBooks = s.convertAccountBooksToSimpleVOs(updatedUser.AccountBooks)

	return userVO, nil
}

// DeleteUser 删除用户
func (s *UserServiceImpl) DeleteUser(ctx context.Context, id uint) error {
	log := s.GetLogger()
	log.Debug(ctx, "删除用户", logger.WithField("userId", id))

	// <<< 先查询用户是否存在及是否为 admin >>>
	userRepo := s.getUserRepository() // Use non-tx repo for initial check
	user, err := userRepo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "删除用户前查询用户失败", logger.WithError(err), logger.WithField("userId", id))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return nil // 用户不存在，认为删除成功或幂等
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}
	if user.Username == "admin" {
		log.Warn(ctx, "尝试删除 admin 用户", logger.WithField("userId", id))
		return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "超级管理员不允许删除")
	}

	// <<< 使用事务包裹数据库操作 >>>
	err = s.GetServiceManager().WithTransaction(func(txSm *ServiceManager) error {
		// 1. 清理用户角色关联
		txUserRoleRepo := txSm.GetRepositoryManager().GetUserRoleRepository()
		if err := txUserRoleRepo.SetUserRoles(ctx, id, []uint{}); err != nil {
			log.Error(ctx, "事务中删除用户角色关联失败", logger.WithError(err), logger.WithField("userId", id))
			// 根据业务决定是否因为关联删除失败而回滚，这里选择回滚
			return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "删除用户角色关联失败")
		}

		// 2. 清理用户账套关联
		txUserAccountBookRepo := txSm.GetRepositoryManager().GetUserAccountBookRepository()
		if err := txUserAccountBookRepo.SetUserAccountBooks(ctx, id, []uint{}); err != nil {
			log.Error(ctx, "事务中删除用户账套关联失败", logger.WithError(err), logger.WithField("userId", id))
			// 根据业务决定是否因为关联删除失败而回滚，这里选择回滚
			return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "删除用户账套关联失败")
		}

		// 3. 删除用户
		txUserRepo := txSm.GetRepositoryManager().GetUserRepository()
		if err := txUserRepo.Delete(ctx, id); err != nil {
			log.Error(ctx, "事务中删除用户失败", logger.WithError(err), logger.WithField("userId", id))
			return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "删除用户数据库操作失败")
		}

		return nil // 事务成功
	})

	if err != nil {
		// 事务执行失败，错误已在内部记录和包装
		return err
	}

	log.Info(ctx, "用户删除成功", logger.WithField("userId", id))
	return nil
}

// GetUserByID 获取用户详情
func (s *UserServiceImpl) GetUserByID(ctx context.Context, id uint) (*vo.UserVO, error) {
	s.GetLogger().Debug(ctx, "获取用户详情",
		logger.WithField("userId", id))

	userRepo := s.getUserRepository()
	// FindByID 已经预加载了 Roles 和 AccountBooks
	user, err := userRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询用户失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	// --- 修改开始: 使用简化版转换并填充关联数据 ---
	userVO := s.convertToUserVO(user) // 调用简化版
	if userVO == nil {
		s.GetLogger().Error(ctx, "获取用户后转换VO失败 (返回nil)", logger.WithField("userID", id))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "获取用户后转换视图对象失败")
	}

	// 使用辅助函数转换预加载的数据
	userVO.Roles = s.convertRolesToRoleVOs(user.Roles)
	userVO.AccountBooks = s.convertAccountBooksToSimpleVOs(user.AccountBooks)

	return userVO, nil
	// --- 修改结束 ---
}

// PageUsers 分页获取用户列表 (注意：此方法已弃用，请使用 FullPageUsers 或 SimplePageUsers)
// 保留此方法以兼容旧代码，但标记为弃用
// Deprecated: use FullPageUsers or SimplePageUsers instead.
func (s *UserServiceImpl) PageUsers(ctx context.Context, queryDTO dto.UserPageQueryDTO) (*response.PageResult, error) {
	s.GetLogger().Warn(ctx, "调用了已弃用的 PageUsers 方法，请改用 FullPageUsers 或 SimplePageUsers")
	// --- 修正错误码 ---
	return nil, apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "此方法已弃用，请使用 FullPageUsers 或 SimplePageUsers")
}

// GetUserByUsername 根据用户名获取用户
func (s *UserServiceImpl) GetUserByUsername(ctx context.Context, username string) (*vo.UserVO, error) {
	s.GetLogger().Debug(ctx, "根据用户名获取用户",
		logger.WithField("username", username))

	userRepo := s.getUserRepository()
	// FindByUsername 已经预加载了 Roles 和 AccountBooks
	user, err := userRepo.FindByUsername(ctx, username)
	if err != nil {
		s.GetLogger().Error(ctx, "查询用户失败",
			logger.WithField("username", username),
			logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户名不存在")
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "按用户名查询用户失败")
	}

	// --- 修改: 使用简化版转换并填充关联数据 ---
	userVO := s.convertToUserVO(user)
	if userVO == nil {
		s.GetLogger().Error(ctx, "获取用户(按用户名)后转换VO失败 (返回nil)", logger.WithField("userID", user.ID))
		return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "获取用户(按用户名)后转换视图对象失败")
	}

	// --- 再次修正: 确保使用 convertRolesToRoleVOs ---
	userVO.Roles = s.convertRolesToRoleVOs(user.Roles)
	userVO.AccountBooks = s.convertAccountBooksToSimpleVOs(user.AccountBooks)

	return userVO, nil
}

// UpdatePassword 更新用户密码
func (s *UserServiceImpl) UpdatePassword(ctx context.Context, id uint, oldPassword, newPassword string, requesterID uint) error {
	log := s.GetLogger()
	log.Debug(ctx, "更新用户密码请求",
		logger.WithField("targetUserId", id),
		logger.WithField("requesterId", requesterID))

	userRepo := s.getUserRepository()
	targetUser, err := userRepo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "查询目标用户失败", logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "目标用户不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询目标用户失败")
	}

	if targetUser.Username == "admin" {
		if requesterID != targetUser.ID {
			log.Warn(ctx, "非admin用户尝试修改admin密码")
			return apperrors.NewBusinessError(apperrors.CODE_AUTH_FORBIDDEN, "不允许修改超级管理员的密码")
		}
		log.Debug(ctx, "Admin用户正在修改自己的密码")
	}

	if requesterID == targetUser.ID {
		log.Debug(ctx, "用户正在修改自己的密码，需要验证旧密码")
		if oldPassword == "" {
			return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "需要提供旧密码")
		}
		passwordCompareErr := util.ComparePassword(targetUser.Password, oldPassword)
		if passwordCompareErr != nil {
			log.Warn(ctx, "旧密码不正确", logger.WithError(passwordCompareErr))
			if errors.Is(passwordCompareErr, bcrypt.ErrMismatchedHashAndPassword) || (apperrors.GetErrorCode(passwordCompareErr) == constant.PASSWORD_ERROR) {
				return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "旧密码不正确")
			} else {
				log.Error(ctx, "验证旧密码时发生错误", logger.WithError(passwordCompareErr))
				return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "密码验证失败").WithCause(passwordCompareErr)
			}
		}
	} else {
		log.Debug(ctx, "管理员正在修改其他用户的密码，跳过旧密码验证")
	}

	if newPassword == "" {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "新密码不能为空")
	}
	hashedPassword, hashErr := util.EncryptPassword(newPassword)
	if hashErr != nil {
		log.Error(ctx, "加密新密码失败", logger.WithError(hashErr))
		return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "新密码处理失败").WithCause(hashErr)
	}

	if err := userRepo.UpdatePassword(ctx, id, hashedPassword); err != nil {
		log.Error(ctx, "更新用户密码数据库操作失败", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户密码数据库操作失败")
	}

	log.Info(ctx, "用户密码更新成功")
	return nil
}

// UpdateStatus 更新用户状态
func (s *UserServiceImpl) UpdateStatus(ctx context.Context, id uint, status int) error {
	s.GetLogger().Debug(ctx, "更新用户状态",
		logger.WithField("userId", id),
		logger.WithField("status", status))

	userRepo := s.getUserRepository()
	_, err := userRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询用户失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	if err := userRepo.UpdateStatus(ctx, id, status); err != nil {
		s.GetLogger().Error(ctx, "更新用户状态失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户状态数据库操作失败")
	}

	return nil
}

// UpdateProfile 更新用户个人信息
func (s *UserServiceImpl) UpdateProfile(ctx context.Context, id uint, profileDTO dto.UserProfileUpdateDTO) error {
	s.GetLogger().Debug(ctx, "更新用户个人信息",
		logger.WithField("userId", id))

	userRepo := s.getUserRepository()
	user, err := userRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询用户失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	// 验证新的 Email 和 Mobile 是否已被其他用户占用
	if profileDTO.Email != nil && *profileDTO.Email != user.Email {
		exists, checkErr := userRepo.CheckEmailExists(ctx, *profileDTO.Email, id)
		if checkErr != nil {
			return apperrors.WrapError(checkErr, apperrors.CODE_DATA_QUERY_FAILED, "检查邮箱唯一性失败")
		}
		if exists {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "邮箱已被其他用户使用")
		}
		user.Email = *profileDTO.Email
	}
	if profileDTO.Mobile != nil && *profileDTO.Mobile != user.Mobile {
		exists, checkErr := userRepo.CheckMobileExists(ctx, *profileDTO.Mobile, id)
		if checkErr != nil {
			return apperrors.WrapError(checkErr, apperrors.CODE_DATA_QUERY_FAILED, "检查手机号唯一性失败")
		}
		if exists {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "手机号已被其他用户使用")
		}
		user.Mobile = *profileDTO.Mobile
	}

	// 验证默认账套访问权限
	if profileDTO.DefaultAccountBookID != nil {
		userAccountBookSvc := s.GetServiceManager().GetUserAccountBookService()
		hasAccess, checkErr := userAccountBookSvc.CheckUserAccountBookAccess(ctx, id, *profileDTO.DefaultAccountBookID)
		if checkErr != nil {
			s.GetLogger().Error(ctx, "检查用户账套权限失败", logger.WithError(checkErr), logger.WithField("userId", id), logger.WithField("accountBookId", *profileDTO.DefaultAccountBookID))
			return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "检查用户账套权限时出错")
		}
		if !hasAccess {
			s.GetLogger().Warn(ctx, "用户尝试设置无权访问的账套为默认账套", logger.WithField("userId", id), logger.WithField("accountBookId", *profileDTO.DefaultAccountBookID))
			return apperrors.NewBusinessError(apperrors.CODE_BUSINESS_ERROR, "无法将无权访问的账套设为默认账套")
		}
		user.DefaultAccountBookID = profileDTO.DefaultAccountBookID
	} else {
		// 如果传入 null 或未传入，则清空默认账套
		user.DefaultAccountBookID = nil
	}

	// 更新其他字段
	user.Nickname = ValueFromPointer(profileDTO.Nickname)
	user.RealName = ValueFromPointer(profileDTO.RealName)
	user.Avatar = ValueFromPointer(profileDTO.Avatar)
	if profileDTO.Gender != nil {
		user.Gender = *profileDTO.Gender
	}

	// Set UpdatedBy
	uid64ProfileUpdate, errProfileUpdate := s.GetUserIDFromContext(ctx)
	if errProfileUpdate != nil {
		s.GetLogger().WithError(errProfileUpdate).Warn(ctx, "UpdateProfile: 无法从上下文中获取UserID, UpdatedBy 将不会被显式设置")
	}
	user.UpdatedBy = uint(uid64ProfileUpdate)

	// 调用 Repository 的 UpdateProfile 方法，该方法只更新允许的字段
	if err := userRepo.UpdateProfile(ctx, user); err != nil {
		s.GetLogger().Error(ctx, "更新用户个人信息失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户个人信息数据库操作失败")
	}

	return nil
}

// GetUserProfile 获取用户个人信息
func (s *UserServiceImpl) GetUserProfile(ctx context.Context, id uint) (*vo.UserProfileVO, error) {
	s.GetLogger().Debug(ctx, "获取用户个人信息",
		logger.WithField("userId", id))

	userRepo := s.getUserRepository()
	// FindByID 已经预加载了 Roles 和 AccountBooks
	user, err := userRepo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger().Error(ctx, "查询用户失败",
			logger.WithField("userId", id),
			logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	profileVO, err := s.convertToUserProfileVO(ctx, user)
	if err != nil {
		s.GetLogger().Error(ctx, "获取用户Profile后转换VO失败", logger.WithError(err), logger.WithField("userID", user.ID))
		return nil, apperrors.WrapError(err, apperrors.CODE_SYSTEM_INTERNAL, "获取用户Profile后转换视图对象失败")
	}
	return profileVO, nil
}

// validateUserCreateParams 验证用户参数
func (s *UserServiceImpl) validateUserCreateParams(ctx context.Context, userDTO dto.UserCreateDTO, id uint) error {
	var username string
	var isCreate bool = (id == 0)

	if isCreate {
		username = userDTO.Username
	}
	emailFromDTO := ValueFromPointer(userDTO.Email)
	mobileFromDTO := ValueFromPointer(userDTO.Mobile)

	userRepo := s.getUserRepository()

	// 仅在创建时检查用户名
	if username != "" && isCreate {
		exists, err := userRepo.CheckUsernameExists(ctx, username)
		if err != nil {
			s.GetLogger().Error(ctx, "验证用户名唯一性失败", logger.WithError(err))
			return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "验证用户名唯一性查询失败")
		}
		if exists {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "用户名已存在")
		}
	}

	if emailFromDTO != "" { // 只对非空字符串进行校验
		// TODO: 在此处添加 Email 格式校验逻辑, 例如使用正则表达式
		// Example:
		// emailRegex := regexp.MustCompile(`^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,4}$`)
		// if !emailRegex.MatchString(emailFromDTO) {
		// 	return apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, "邮箱格式无效")
		// }

		exists, err := userRepo.CheckEmailExists(ctx, emailFromDTO, id) // 传入 excludeID (create时id为0)
		if err != nil {
			s.GetLogger().Error(ctx, "验证邮箱唯一性失败", logger.WithError(err))
			return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "验证邮箱唯一性查询失败")
		}
		if exists {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "邮箱已被其他用户使用")
		}
	}

	if mobileFromDTO != "" { // 只对非空字符串进行校验
		// TODO: 在此处添加 Mobile 格式校验逻辑, 例如使用正则表达式
		// Example:
		// mobileRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
		// if !mobileRegex.MatchString(mobileFromDTO) {
		// 	return apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, "手机号码格式无效")
		// }

		exists, err := userRepo.CheckMobileExists(ctx, mobileFromDTO, id) // 传入 excludeID (create时id为0)
		if err != nil {
			s.GetLogger().Error(ctx, "验证手机号唯一性查询失败", logger.WithError(err))
			return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "验证手机号唯一性查询失败")
		}
		if exists {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "手机号已被其他用户使用")
		}
	}

	return nil
}

// validateUserParams 验证用户参数
func (s *UserServiceImpl) validateUserUpdateParams(ctx context.Context, userDTO dto.UserUpdateDTO, id uint) error {
	// Username is not updated here, so no need to validate its uniqueness against others.
	// It's assumed username is either immutable or handled by a different process if changeable.

	emailFromDTO := ValueFromPointer(userDTO.Email)
	mobileFromDTO := ValueFromPointer(userDTO.Mobile)

	userRepo := s.getUserRepository()

	if emailFromDTO != "" { // 只对非空字符串进行校验
		// TODO: 在此处添加 Email 格式校验逻辑, 例如使用正则表达式
		// Example:
		// emailRegex := regexp.MustCompile(`^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,4}$`)
		// if !emailRegex.MatchString(emailFromDTO) {
		// 	return apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, "邮箱格式无效")
		// }

		// 传入 excludeID (id of current user being updated) to allow user to keep their own email
		exists, err := userRepo.CheckEmailExists(ctx, emailFromDTO, id)
		if err != nil {
			s.GetLogger().Error(ctx, "验证邮箱唯一性失败", logger.WithError(err))
			return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "验证邮箱唯一性查询失败")
		}
		if exists {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "邮箱已被其他用户使用")
		}
	}

	if mobileFromDTO != "" { // 只对非空字符串进行校验
		// TODO: 在此处添加 Mobile 格式校验逻辑, 例如使用正则表达式
		// Example:
		// mobileRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
		// if !mobileRegex.MatchString(mobileFromDTO) {
		// 	return apperrors.NewBusinessError(apperrors.CODE_PARAMS_INVALID, "手机号码格式无效")
		// }

		// 传入 excludeID (id of current user being updated)
		exists, err := userRepo.CheckMobileExists(ctx, mobileFromDTO, id)
		if err != nil {
			s.GetLogger().Error(ctx, "验证手机号唯一性查询失败", logger.WithError(err))
			return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "验证手机号唯一性查询失败")
		}
		if exists {
			return apperrors.NewBusinessError(apperrors.CODE_DATA_ALREADY_EXISTS, "手机号已被其他用户使用")
		}
	}

	return nil
}

// buildUserQueryConditions 构建用户查询条件
func (s *UserServiceImpl) buildUserQueryConditions(queryDTO dto.UserPageQueryDTO) []repository.Condition {
	conditions := make([]repository.Condition, 0)

	if username := ValueFromPointer(queryDTO.Username); username != "" {
		conditions = append(conditions, repository.NewLikeCondition("username", username))
	}
	if nickname := ValueFromPointer(queryDTO.Nickname); nickname != "" {
		conditions = append(conditions, repository.NewLikeCondition("nickname", nickname))
	}
	if realName := ValueFromPointer(queryDTO.RealName); realName != "" {
		conditions = append(conditions, repository.NewLikeCondition("real_name", realName))
	}
	if email := ValueFromPointer(queryDTO.Email); email != "" {
		conditions = append(conditions, repository.NewLikeCondition("email", email))
	}
	if mobile := ValueFromPointer(queryDTO.Mobile); mobile != "" {
		// 假设手机号是精确匹配
		conditions = append(conditions, repository.NewEqualCondition("mobile", mobile))
	}
	if queryDTO.Gender != nil {
		conditions = append(conditions, repository.NewEqualCondition("gender", *queryDTO.Gender))
	}
	if queryDTO.Status != nil {
		conditions = append(conditions, repository.NewEqualCondition("status", *queryDTO.Status))
	}
	if queryDTO.IsAdmin != nil {
		conditions = append(conditions, repository.NewEqualCondition("is_admin", *queryDTO.IsAdmin))
	}

	return conditions
}

// convertToUserVO 将单个用户实体转换为 VO (简化版，只做基础字段映射，正确处理嵌套结构)
// 注意：不再需要 ctx 参数，也不再返回 error
func (s *UserServiceImpl) convertToUserVO(user *entity.User) *vo.UserVO {
	if user == nil {
		return nil
	}

	// --- 修正: 正确初始化嵌套结构 ---
	baseVO := vo.BaseVO{
		ID:        user.ID,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
		CreatedBy: user.CreatedBy,
		UpdatedBy: user.UpdatedBy,
	}
	tenantVO := vo.TenantVO{
		BaseVO:   baseVO,
		TenantID: user.TenantID,
	}

	userVO := &vo.UserVO{
		TenantVO:             tenantVO, // 嵌入修正后的 TenantVO
		Username:             user.Username,
		Nickname:             user.Nickname,
		RealName:             user.RealName,
		Avatar:               user.Avatar,
		Gender:               user.Gender,
		Email:                user.Email,
		Mobile:               user.Mobile,
		Status:               user.Status,
		IsAdmin:              user.IsAdmin,
		Remark:               user.Remark,
		EmployeeID:           user.EmployeeID,
		LoginIP:              user.LoginIP,
		LoginCount:           user.LoginCount,
		DefaultAccountBookID: user.DefaultAccountBookID,
		ExpireTime:           user.ExpireTime,
		// --- 修正: 明确处理可能为零值的时间和计数器指针 ---
		LoginTime:   PointerIfNonZero(user.LoginTime),
		LockedTime:  PointerIfNonZero(user.LockedTime),
		LockedCount: PointerIfNonZero(user.LockedCount),
		LastPwdTime: user.LastPwdTime,
		// Roles 和 AccountBooks 在调用方填充
		Roles:        []*vo.RoleVO{},              // 初始化为空切片
		AccountBooks: []*vo.AccountBookSimpleVO{}, // 初始化为空的 SimpleVO 切片
	}

	return userVO
}

// convertToUserVOList 将用户实体列表转换为 VO 列表 (使用 Preload 数据)
func (s *UserServiceImpl) convertToUserVOList(ctx context.Context, users []*entity.User) ([]*vo.UserVO, error) {
	if len(users) == 0 {
		return []*vo.UserVO{}, nil
	}

	userVOs := make([]*vo.UserVO, 0, len(users))
	// log := s.GetLogger() // 日志不再需要

	// 不再需要在循环外获取 RoleService 和 AccountBookService

	for _, user := range users {
		if user == nil {
			continue
		}
		// 1. 基础转换
		userVO := s.convertToUserVO(user) // 使用简化后的基础转换
		if userVO == nil {
			continue // 跳过 nil 用户
		}

		// 2. 转换 Roles (使用预加载的数据和辅助函数)
		userVO.Roles = s.convertRolesToRoleVOs(user.Roles)

		// 3. 转换 AccountBooks (使用预加载的数据和辅助函数)
		userVO.AccountBooks = s.convertAccountBooksToSimpleVOs(user.AccountBooks)

		userVOs = append(userVOs, userVO)
	}

	// 不再需要检查和返回 firstErr
	return userVOs, nil
}

// convertToUserProfileVO 转换用户实体到用户配置视图对象
func (s *UserServiceImpl) convertToUserProfileVO(ctx context.Context, user *entity.User) (*vo.UserProfileVO, error) {
	if user == nil {
		return nil, nil
	}

	profileVO := &vo.UserProfileVO{
		ID:                   user.ID,
		Username:             user.Username,
		Nickname:             user.Nickname,
		RealName:             user.RealName,
		Avatar:               user.Avatar,
		Gender:               user.Gender,
		Email:                user.Email,
		Mobile:               user.Mobile,
		LoginIP:              user.LoginIP,
		LoginTime:            user.LoginTime, // Keep as time.Time
		EmployeeID:           user.EmployeeID,
		DefaultAccountBookID: user.DefaultAccountBookID,
		IsAdmin:              user.IsAdmin, // Add IsAdmin field
	}

	// --- 使用预加载的角色数据 ---
	profileVO.Roles = s.convertRolesToRoleVOs(user.Roles)

	// --- 使用预加载的账套数据 ---
	profileVO.AccountBooks = s.convertAccountBooksToSimpleVOs(user.AccountBooks)

	return profileVO, nil
}

// ConvertToUserSimpleVOList 将用户实体列表转换为用户简要信息视图对象列表
func (s *UserServiceImpl) ConvertToUserSimpleVOList(ctx context.Context, users []*entity.User) ([]*vo.UserSimpleVO, error) {
	s.GetLogger().Debug(ctx, "转换用户列表为 UserSimpleVO 列表", logger.WithField("count", len(users)))
	if len(users) == 0 {
		return []*vo.UserSimpleVO{}, nil
	}

	simpleVOs := make([]*vo.UserSimpleVO, 0, len(users))
	for _, user := range users {
		simpleVOs = append(simpleVOs, &vo.UserSimpleVO{
			ID:       user.ID,
			Username: user.Username,
			RealName: user.RealName,
			Status:   user.Status,
			IsAdmin:  user.IsAdmin,
			Gender:   user.Gender, // Add Gender
		})
	}

	return simpleVOs, nil
}

// AdminResetPassword 管理员重置密码 (无需旧密码)
func (s *UserServiceImpl) AdminResetPassword(ctx context.Context, id uint, newPassword string) error {
	log := s.GetLogger()
	log.Debug(ctx, "管理员重置密码请求", logger.WithField("targetUserId", id))

	if newPassword == "" {
		return apperrors.NewParamError(apperrors.CODE_PARAMS_MISSING, "新密码不能为空")
	}
	userRepo := s.getUserRepository()
	targetUser, err := userRepo.FindByID(ctx, id)
	if err != nil {
		log.Error(ctx, "查询目标用户失败", logger.WithError(err))
		if apperrors.GetErrorCode(err) == apperrors.CODE_DATA_NOT_FOUND {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "目标用户不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询目标用户失败")
	}

	if targetUser.Username == "admin" {
		log.Warn(ctx, "尝试通过管理员接口重置 admin 密码")
		return apperrors.NewBusinessError(apperrors.CODE_AUTH_FORBIDDEN, "不允许通过此接口重置超级管理员的密码")
	}

	hashedPassword, hashErr := util.EncryptPassword(newPassword)
	if hashErr != nil {
		log.Error(ctx, "加密新密码失败", logger.WithError(hashErr))
		return apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "新密码处理失败").WithCause(hashErr)
	}

	if err := userRepo.UpdatePassword(ctx, id, hashedPassword); err != nil {
		log.Error(ctx, "更新用户密码数据库操作失败", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户密码数据库操作失败")
	}

	log.Info(ctx, "管理员成功重置用户密码", logger.WithField("targetUserId", id))
	return nil
}

// UpdateAvatarURL 更新用户头像 URL
func (s *UserServiceImpl) UpdateAvatarURL(ctx context.Context, userID uint, avatarURL string) error {
	s.GetLogger().Debug(ctx, "更新用户头像URL",
		logger.WithField("userID", userID),
		logger.WithField("avatarURL", avatarURL))

	userRepo := s.getUserRepository()
	user, err := userRepo.FindByID(ctx, userID)
	if err != nil {
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}

	user.Avatar = avatarURL

	// Set UpdatedBy before calling Update
	uid64AvatarUpdate, errAvatarUpdate := s.GetUserIDFromContext(ctx)
	if errAvatarUpdate != nil {
		s.GetLogger().WithError(errAvatarUpdate).Warn(ctx, "UpdateAvatarURL: 无法从上下文中获取UserID")
	}
	user.UpdatedBy = uint(uid64AvatarUpdate)

	if err := userRepo.Update(ctx, user); err != nil { // Use Update instead of UpdateProfile for just avatar
		s.GetLogger().Error(ctx, "更新用户头像URL数据库操作失败",
			logger.WithField("userID", userID),
			logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新头像URL失败")
	}

	return nil
}

// GetProfile 获取用户个人资料 (复用 GetUserProfile)
func (s *UserServiceImpl) GetProfile(ctx context.Context, userID uint) (*vo.UserProfileVO, error) {
	return s.GetUserProfile(ctx, userID)
}

// LockUserSecurity 安全锁定用户
func (s *UserServiceImpl) LockUserSecurity(ctx context.Context, userID uint) error {
	log := s.GetLogger()
	log.Debug(ctx, "安全锁定用户请求", logger.WithField("userID", userID))

	userRepo := s.getUserRepository()

	// <<< 直接调用仓库层的 LockSecurity 方法 >>>
	if err := userRepo.LockSecurity(ctx, userID); err != nil {
		log.Error(ctx, "安全锁定用户失败 (调用仓库层)", logger.WithError(err), logger.WithField("userID", userID))
		// 错误类型转换/包装 (如果需要，或者直接返回仓库层的错误)
		// 例如，如果仓库层返回了特定的 DataError，可以直接返回
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		// 其他错误包装为业务或系统错误
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "安全锁定用户数据库操作失败")
	}

	log.Info(ctx, "用户安全锁定成功", logger.WithField("userID", userID))
	return nil
}

// UnlockUserSecurity 安全解锁用户
func (s *UserServiceImpl) UnlockUserSecurity(ctx context.Context, userID uint) error {
	log := s.GetLogger()
	log.Debug(ctx, "安全解锁用户请求", logger.WithField("userID", userID))

	userRepo := s.getUserRepository()

	// <<< 直接调用仓库层的 UnlockSecurity 方法 >>>
	if err := userRepo.UnlockSecurity(ctx, userID); err != nil {
		log.Error(ctx, "安全解锁用户失败 (调用仓库层)", logger.WithError(err), logger.WithField("userID", userID))
		// 错误处理逻辑同上
		if apperrors.IsError(err, apperrors.CODE_DATA_NOT_FOUND) {
			return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户不存在")
		}
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "安全解锁用户数据库操作失败")
	}

	log.Info(ctx, "用户安全解锁成功", logger.WithField("userID", userID))
	return nil
}

// Implement methods from interface comments if missing
// Implement GetUserAccountBooks
func (s *UserServiceImpl) GetUserAccountBooks(ctx context.Context, id uint) ([]*vo.AccountBookVO, error) {
	// Placeholder implementation - needs actual logic
	s.GetLogger().Warn(ctx, "GetUserAccountBooks not fully implemented", logger.WithField("userId", id))
	// Example: Get IDs from repo, then get details from AccountBookService
	return []*vo.AccountBookVO{}, nil // Return empty slice for now
}

// Implement UpdateUserAccountBooks
func (s *UserServiceImpl) UpdateUserAccountBooks(ctx context.Context, id uint, accountBookIDs []uint) error {
	// Placeholder implementation - needs actual logic
	s.GetLogger().Debug(ctx, "UpdateUserAccountBooks called", logger.WithField("userId", id), logger.WithField("accountBookIds", accountBookIDs))
	userAccountBookRepo := s.GetServiceManager().GetRepositoryManager().GetUserAccountBookRepository()
	if err := userAccountBookRepo.SetUserAccountBooks(ctx, id, accountBookIDs); err != nil {
		s.GetLogger().Error(ctx, "设置用户账套关联失败", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "设置用户账套关联失败")
	}
	return nil
}

// Implement UpdateDefaultAccountBook
func (s *UserServiceImpl) UpdateDefaultAccountBook(ctx context.Context, id uint, accountBookID uint) error {
	s.GetLogger().Debug(ctx, "UpdateDefaultAccountBook called", logger.WithField("userId", id), logger.WithField("accountBookId", accountBookID))
	userRepo := s.getUserRepository()
	user, err := userRepo.FindByID(ctx, id)
	if err != nil {
		// ... error handling ...
		return apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户失败")
	}
	// Validate access to account book
	userAccountBookSvc := s.GetServiceManager().GetUserAccountBookService()
	hasAccess, err := userAccountBookSvc.CheckUserAccountBookAccess(ctx, id, accountBookID)
	if err != nil || !hasAccess {
		s.GetLogger().Warn(ctx, "用户无权访问要设置为默认的账套", logger.WithError(err), logger.WithField("hasAccess", hasAccess))
		return apperrors.NewBusinessError(apperrors.CODE_AUTH_FORBIDDEN, "无权将此账套设为默认")
	}
	user.DefaultAccountBookID = &accountBookID
	if err := userRepo.Update(ctx, user); err != nil {
		s.GetLogger().Error(ctx, "更新默认账套失败", logger.WithError(err))
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新默认账套失败")
	}
	return nil
}

// Implement SimplePageUsers
func (s *UserServiceImpl) SimplePageUsers(ctx context.Context, queryDTO dto.UserPageQueryDTO) ([]*vo.UserSimpleVO, error) {
	s.GetLogger().Debug(ctx, "简单分页查询用户", logger.WithField("pageNum", queryDTO.PageQuery.PageNum), logger.WithField("pageSize", queryDTO.PageQuery.PageSize))
	concreteConditions := s.buildUserQueryConditions(queryDTO)
	userRepo := s.getUserRepository()

	// <<< 转换 []Condition 为 []QueryCondition >>>
	conditions := make([]repository.QueryCondition, len(concreteConditions))
	for i, c := range concreteConditions {
		conditions[i] = c
	}

	// <<< 使用 BaseRepository 的 FindByPage 方法，传递指针和接口切片 >>>
	pageResultEntity, err := userRepo.FindByPage(ctx, &queryDTO.PageQuery, conditions)
	if err != nil {
		s.GetLogger().Error(ctx, "简单分页查询用户失败", logger.WithError(err))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "简单分页查询用户失败")
	}
	// <<< 直接从 PageResult 中获取实体列表 >>>
	userEntities, ok := pageResultEntity.List.([]*entity.User)
	if !ok {
		// GORM Find 返回的是指向切片的指针，需要解引用并断言
		if listPtr, okPtr := pageResultEntity.List.(*[]*entity.User); okPtr && listPtr != nil {
			userEntities = *listPtr
			ok = true // 标记断言成功
		} else {
			s.GetLogger().Error(ctx, "分页查询结果类型断言失败", logger.WithField("type", fmt.Sprintf("%T", pageResultEntity.List)))
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "分页查询结果类型错误")
		}
	}
	return s.ConvertToUserSimpleVOList(ctx, userEntities)
}

// Implement FullPageUsers
func (s *UserServiceImpl) FullPageUsers(ctx context.Context, queryDTO dto.UserPageQueryDTO) (*response.PageResult, error) {
	s.GetLogger().Debug(ctx, "完整分页查询用户", logger.WithField("pageNum", queryDTO.PageQuery.PageNum), logger.WithField("pageSize", queryDTO.PageQuery.PageSize))
	concreteConditions := s.buildUserQueryConditions(queryDTO)
	userRepo := s.getUserRepository()

	// <<< 转换 []Condition 为 []QueryCondition >>>
	conditions := make([]repository.QueryCondition, len(concreteConditions))
	for i, c := range concreteConditions {
		conditions[i] = c
	}

	// <<< 使用 BaseRepository 的 FindByPage 方法，传递指针和接口切片 >>>
	pageResultEntity, err := userRepo.FindByPage(ctx, &queryDTO.PageQuery, conditions)
	if err != nil {
		s.GetLogger().Error(ctx, "完整分页查询用户失败", logger.WithError(err))
		return nil, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "完整分页查询用户失败")
	}
	// <<< 直接从 PageResult 中获取实体列表 >>>
	userEntities, ok := pageResultEntity.List.([]*entity.User)
	if !ok {
		// GORM Find 返回的是指向切片的指针，需要解引用并断言
		if listPtr, okPtr := pageResultEntity.List.(*[]*entity.User); okPtr && listPtr != nil {
			userEntities = *listPtr
			ok = true // 标记断言成功
		} else {
			s.GetLogger().Error(ctx, "分页查询结果类型断言失败", logger.WithField("type", fmt.Sprintf("%T", pageResultEntity.List)))
			return nil, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "分页查询结果类型错误")
		}
	}
	// 将实体列表转换为VO列表
	userVOs, convErr := s.convertToUserVOList(ctx, userEntities)
	if convErr != nil {
		// 如果转换本身出错（例如内部查询关联数据失败），则记录并返回错误
		s.GetLogger().Error(ctx, "转换用户实体列表为VO列表失败", logger.WithError(convErr))
		return nil, apperrors.WrapError(convErr, apperrors.CODE_SYSTEM_INTERNAL, "转换用户视图对象失败")
	}

	// 构建新的 PageResult，其中 List 是转换后的 VO 列表
	return &response.PageResult{
		List:     userVOs,
		Total:    pageResultEntity.Total,
		PageNum:  pageResultEntity.PageNum,
		PageSize: pageResultEntity.PageSize,
		// Pages: pageResultEntity.Pages, // 如果 PageResultEntity 中有 Pages 字段且需要
	}, nil
}

// Implement OnlinePageUsers
func (s *UserServiceImpl) OnlinePageUsers(ctx context.Context, queryDTO dto.UserPageQueryDTO) ([]*vo.UserOnlineVO, error) {
	// Placeholder implementation - needs actual logic involving sessions/tokens
	s.GetLogger().Warn(ctx, "OnlinePageUsers not fully implemented")
	// Example: Query online session store based on filters in queryDTO
	return []*vo.UserOnlineVO{}, nil // Return empty slice for now
}

// <<< 添加转换辅助函数 >>>
func ConvertConditionsToInterfaces(conditions []repository.Condition) []interface{} {
	interfaces := make([]interface{}, len(conditions))
	for i, c := range conditions {
		interfaces[i] = c
	}
	return interfaces
}

// <<< 辅助函数 >>>
func ValueFromPointer[T any](ptr *T) T {
	var zero T
	if ptr == nil {
		return zero
	}
	return *ptr
}

func PointerIfNonZero[T comparable](val T) *T {
	var zero T
	if val == zero {
		return nil
	}
	return &val
}

// --- 新增辅助函数：将实体列表转换为VO列表 ---

// convertRolesToRoleVOs 转换角色实体列表为视图对象列表
// 接收 []entity.Role (值切片)
func (s *UserServiceImpl) convertRolesToRoleVOs(roles []entity.Role) []*vo.RoleVO {
	roleVOs := make([]*vo.RoleVO, 0, len(roles))
	for _, role := range roles { // role is now entity.Role (value)
		// --- role 是值类型，直接用 . 访问字段 ---
		baseVO := vo.BaseVO{
			ID:        role.ID,
			CreatedAt: role.CreatedAt,
			UpdatedAt: role.UpdatedAt,
			CreatedBy: role.CreatedBy,
			UpdatedBy: role.UpdatedBy,
		}
		tenantVO := vo.TenantVO{
			BaseVO:   baseVO,
			TenantID: role.TenantID,
		}
		roleVOs = append(roleVOs, &vo.RoleVO{
			TenantVO: tenantVO,
			Name:     role.Name,
			Code:     role.Code,
			Status:   role.Status,
			IsSystem: role.IsSystem,
			Remark:   role.Remark,
		})
	}
	return roleVOs
}

// --- 新增辅助函数：转换角色指针切片 ---
func (s *UserServiceImpl) convertRolePtrsToRoleVOs(roles []*entity.Role) []*vo.RoleVO {
	roleVOs := make([]*vo.RoleVO, 0, len(roles))
	for _, role := range roles {
		if role == nil {
			continue
		}
		baseVO := vo.BaseVO{
			ID:        role.ID,
			CreatedAt: role.CreatedAt,
			UpdatedAt: role.UpdatedAt,
			CreatedBy: role.CreatedBy,
			UpdatedBy: role.UpdatedBy,
		}
		tenantVO := vo.TenantVO{
			BaseVO:   baseVO,
			TenantID: role.TenantID,
		}
		roleVOs = append(roleVOs, &vo.RoleVO{
			TenantVO: tenantVO,
			Name:     role.Name,
			Code:     role.Code,
			Status:   role.Status,
			IsSystem: role.IsSystem,
			Remark:   role.Remark,
		})
	}
	return roleVOs
}

// convertAccountBooksToSimpleVOs 转换账套实体列表为简要视图对象列表
func (s *UserServiceImpl) convertAccountBooksToSimpleVOs(accountBooks []entity.AccountBook) []*vo.AccountBookSimpleVO {
	abVOs := make([]*vo.AccountBookSimpleVO, 0, len(accountBooks))
	for _, ab := range accountBooks {
		abVOs = append(abVOs, &vo.AccountBookSimpleVO{
			ID:   ab.ID,
			Name: ab.Name,
		})
	}
	return abVOs
}

// updateUserRoles 更新用户角色关联 (内部方法)
func (s *UserServiceImpl) updateUserRoles(ctx context.Context, txSm *ServiceManager, userID uint, roleIDs []uint) error {
	if roleIDs == nil {
		s.GetLogger().Debug(ctx, "RoleIDs is nil, skipping role update", logger.WithField("userId", userID))
		return nil
	}
	txUserRoleRepo := txSm.GetRepositoryManager().GetUserRoleRepository()
	if err := txUserRoleRepo.SetUserRoles(ctx, userID, roleIDs); err != nil {
		s.GetLogger().Error(ctx, "更新用户角色关联失败", logger.WithField("userId", userID), logger.WithError(err))
		// Wrap error
		return apperrors.WrapError(err, apperrors.CODE_DATA_UPDATE_FAILED, "更新用户角色关联失败")
	}
	return nil
}

// updateUserAccountBooks 更新用户账套关联 (内部方法)
func (s *UserServiceImpl) updateUserAccountBooks(ctx context.Context, txSm *ServiceManager, userID uint, accountBookIDs []uint) error {
	if accountBookIDs == nil {
		s.GetLogger().Debug(ctx, "AccountBookIDs is nil, skipping account book update", logger.WithField("userId", userID))
		return nil
	}
	// Use the dedicated service obtained via the transactional service manager
	txUserAccountBookSvc := txSm.GetUserAccountBookService()
	if err := txUserAccountBookSvc.UpdateUserAccountBooks(ctx, userID, accountBookIDs); err != nil {
		// Assume UpdateUserAccountBooks returns wrapped error
		s.GetLogger().Error(ctx, "调用UserAccountBookService更新用户账套失败", logger.WithField("userId", userID), logger.WithError(err))
		return err // Return the already wrapped error
	}
	return nil
}

// updateUserRelations 统一处理用户关联更新 (内部方法)
func (s *UserServiceImpl) updateUserRelations(ctx context.Context, txSm *ServiceManager, userID uint, roleIDs []uint, accountBookIDs []uint) error {
	if err := s.updateUserRoles(ctx, txSm, userID, roleIDs); err != nil {
		// Error is already wrapped by updateUserRoles
		return err
	}
	if err := s.updateUserAccountBooks(ctx, txSm, userID, accountBookIDs); err != nil {
		// Error is already wrapped by updateUserAccountBooks (or the service it calls)
		return err
	}
	return nil
}
