package service

import (
	"context"
	"fmt"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// BatchCreate 批量创建出库通知单
func (s *wmsOutboundNotificationServiceImpl) BatchCreate(ctx context.Context, req *dto.WmsOutboundNotificationBatchCreateReq) (*dto.BatchImportResult, error) {
	result := &dto.BatchImportResult{
		TotalCount:     len(req.Notifications),
		SuccessCount:   0,
		FailureCount:   0,
		SuccessIDs:     []uint{},
		FailureReasons: []string{},
	}

	for _, notificationReq := range req.Notifications {
		notification, err := s.Create(ctx, &notificationReq)
		if err != nil {
			result.FailureCount++
			result.FailureReasons = append(result.FailureReasons, err.Error())
		} else {
			result.SuccessCount++
			result.SuccessIDs = append(result.SuccessIDs, notification.ID)
		}
	}

	return result, nil
}

// AllocateInventory 分配库存
func (s *wmsOutboundNotificationServiceImpl) AllocateInventory(ctx context.Context, req *dto.WmsOutboundNotificationAllocateReq) (*vo.WmsOutboundNotificationAllocationStatusVO, error) {
	var result *vo.WmsOutboundNotificationAllocationStatusVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsOutboundNotificationRepository()

		// 检查通知单是否存在
		notification, err := repo.FindByID(ctx, req.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
		}

		// 检查状态是否允许分配
		if notification.Status != string(entity.OutboundStatusApproved) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已审核状态的通知单才能分配库存")
		}

		// 获取明细
		detailRepo := txRepoMgr.GetWmsOutboundNotificationDetailRepository()
		details, err := detailRepo.FindByNotificationID(ctx, req.ID)
		if err != nil {
			return fmt.Errorf("查询明细失败: %w", err)
		}

		if len(details) == 0 {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单没有明细")
		}

		// 执行库存分配
		allocationService := s.GetServiceManager().GetWmsInventoryAllocationService()
		allocationResult, err := s.performInventoryAllocation(ctx, txRepoMgr, allocationService, details, req)
		if err != nil {
			return fmt.Errorf("执行库存分配失败: %w", err)
		}

		// 更新通知单状态
		if allocationResult.AllocationResult == "FULL" {
			if err := repo.UpdateStatus(ctx, req.ID, string(entity.OutboundStatusAllocated), "库存分配完成"); err != nil {
				return fmt.Errorf("更新通知单状态失败: %w", err)
			}
		} else if allocationResult.AllocationResult == "PARTIAL" {
			if err := repo.UpdateStatus(ctx, req.ID, string(entity.OutboundStatusAllocated), "库存部分分配"); err != nil {
				return fmt.Errorf("更新通知单状态失败: %w", err)
			}
		}

		result = allocationResult
		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// BatchAllocateInventory 批量分配库存
func (s *wmsOutboundNotificationServiceImpl) BatchAllocateInventory(ctx context.Context, req *dto.WmsOutboundNotificationBatchAllocateReq) ([]*vo.WmsOutboundNotificationAllocationStatusVO, error) {
	var results []*vo.WmsOutboundNotificationAllocationStatusVO

	for _, id := range req.IDs {
		allocateReq := &dto.WmsOutboundNotificationAllocateReq{
			ID:                 id,
			AllocationStrategy: req.AllocationStrategy,
			ForceAllocate:      req.ForceAllocate,
		}

		result, err := s.AllocateInventory(ctx, allocateReq)
		if err != nil {
			// 记录错误但继续处理其他单据
			result = &vo.WmsOutboundNotificationAllocationStatusVO{
				NotificationID:   id,
				AllocationResult: "ERROR",
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// GetAllocationStatus 获取分配状态
func (s *wmsOutboundNotificationServiceImpl) GetAllocationStatus(ctx context.Context, id uint) (*vo.WmsOutboundNotificationAllocationStatusVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	// 获取通知单
	notification, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
	}

	// 获取明细和分配状态
	detailRepo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationDetailRepository()
	details, err := detailRepo.FindByNotificationID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询明细失败: %w", err)
	}

	result := &vo.WmsOutboundNotificationAllocationStatusVO{
		NotificationID: id,
		NotificationNo: notification.NotificationNo,
		TotalItems:     len(details),
		Details:        make([]vo.WmsOutboundNotificationDetailAllocationVO, len(details)),
	}

	allocationRepo := s.GetServiceManager().GetRepositoryManager().GetWmsInventoryAllocationRepository()
	allocatedItems := 0

	for i, detail := range details {
		// 获取该明细的分配情况
		allocations, err := allocationRepo.FindByOutboundDetailID(ctx, detail.ID)
		if err != nil {
			return nil, fmt.Errorf("查询分配记录失败: %w", err)
		}

		allocatedQty := float64(0)
		for _, allocation := range allocations {
			if allocation.Status == string(entity.AllocationStatusAllocated) {
				allocatedQty += allocation.AllocatedQty
			}
		}

		if allocatedQty > 0 {
			allocatedItems++
		}

		detailVO := vo.WmsOutboundNotificationDetailAllocationVO{
			DetailID:       detail.ID,
			LineNo:         detail.LineNo,
			RequiredQty:    detail.RequiredQty,
			AllocatedQty:   allocatedQty,
			AllocationRate: allocatedQty / detail.RequiredQty * 100,
			CanAllocate:    detail.RequiredQty > allocatedQty,
			ShortageQty:    detail.RequiredQty - allocatedQty,
		}

		// TODO: 填充物料信息和可用库存
		result.Details[i] = detailVO
	}

	result.AllocatedItems = allocatedItems
	if result.TotalItems > 0 {
		result.AllocationRate = float64(allocatedItems) / float64(result.TotalItems) * 100
	}

	// 判断分配结果
	if allocatedItems == 0 {
		result.AllocationResult = "NONE"
		result.CanAllocate = true
	} else if allocatedItems == result.TotalItems {
		result.AllocationResult = "FULL"
		result.CanAllocate = false
	} else {
		result.AllocationResult = "PARTIAL"
		result.CanAllocate = true
	}

	return result, nil
}

// GeneratePickingTask 生成拣货任务
func (s *wmsOutboundNotificationServiceImpl) GeneratePickingTask(ctx context.Context, id uint, strategy string) (*vo.WmsPickingTaskVO, error) {
	var result *vo.WmsPickingTaskVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsOutboundNotificationRepository()

		// 检查通知单是否存在
		notification, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "出库通知单不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询出库通知单失败").WithCause(err)
		}

		// 检查状态是否允许生成拣货任务
		if notification.Status != string(entity.OutboundStatusAllocated) {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有已分配状态的通知单才能生成拣货任务")
		}

		// 调用拣货任务服务生成任务
		pickingTaskService := s.GetServiceManager().GetWmsPickingTaskService()
		createReq := &dto.WmsPickingTaskCreateReq{
			NotificationID:  id,
			PickingStrategy: strategy,
			Priority:        notification.Priority,
		}

		taskVO, err := pickingTaskService.Create(ctx, createReq)
		if err != nil {
			return fmt.Errorf("创建拣货任务失败: %w", err)
		}

		// 更新通知单状态为拣货中
		if err := repo.UpdateStatus(ctx, id, string(entity.OutboundStatusPicking), "已生成拣货任务"); err != nil {
			return fmt.Errorf("更新通知单状态失败: %w", err)
		}

		result = taskVO
		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// BatchGeneratePickingTask 批量生成拣货任务
func (s *wmsOutboundNotificationServiceImpl) BatchGeneratePickingTask(ctx context.Context, ids []uint, strategy string) ([]*vo.WmsPickingTaskVO, error) {
	var results []*vo.WmsPickingTaskVO

	for _, id := range ids {
		taskVO, err := s.GeneratePickingTask(ctx, id, strategy)
		if err != nil {
			// 记录错误但继续处理其他单据
			continue
		}
		results = append(results, taskVO)
	}

	return results, nil
}

// GetByNotificationNo 根据通知单号获取
func (s *wmsOutboundNotificationServiceImpl) GetByNotificationNo(ctx context.Context, notificationNo string) (*vo.WmsOutboundNotificationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	notification, err := repo.FindByNotificationNo(ctx, notificationNo)
	if err != nil {
		return nil, err
	}

	voResult := &vo.WmsOutboundNotificationVO{}
	copier.Copy(voResult, notification)

	// 填充扩展信息
	if err := s.fillExtendedInfo(ctx, voResult); err != nil {
		return nil, err
	}

	return voResult, nil
}

// GetByClientOrderNo 根据客户订单号获取
func (s *wmsOutboundNotificationServiceImpl) GetByClientOrderNo(ctx context.Context, clientOrderNo string) ([]*vo.WmsOutboundNotificationVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	notifications, err := repo.FindByClientOrderNo(ctx, clientOrderNo)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsOutboundNotificationVO
	for _, notification := range notifications {
		voResult := &vo.WmsOutboundNotificationVO{}
		copier.Copy(voResult, notification)

		// 填充扩展信息
		if err := s.fillExtendedInfo(ctx, voResult); err != nil {
			return nil, err
		}

		results = append(results, voResult)
	}

	return results, nil
}

// GetPendingAllocation 获取待分配的通知单
func (s *wmsOutboundNotificationServiceImpl) GetPendingAllocation(ctx context.Context) ([]*vo.WmsOutboundNotificationListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	notifications, err := repo.GetPendingAllocation(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsOutboundNotificationListVO
	for _, notification := range notifications {
		listVO := &vo.WmsOutboundNotificationListVO{}
		copier.Copy(listVO, notification)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// GetReadyForPicking 获取可生成拣货任务的通知单
func (s *wmsOutboundNotificationServiceImpl) GetReadyForPicking(ctx context.Context) ([]*vo.WmsOutboundNotificationListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	notifications, err := repo.GetReadyForPicking(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsOutboundNotificationListVO
	for _, notification := range notifications {
		listVO := &vo.WmsOutboundNotificationListVO{}
		copier.Copy(listVO, notification)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}

// GetOverdueShipments 获取过期未发货的通知单
func (s *wmsOutboundNotificationServiceImpl) GetOverdueShipments(ctx context.Context) ([]*vo.WmsOutboundNotificationListVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsOutboundNotificationRepository()

	notifications, err := repo.GetOverdueShipments(ctx)
	if err != nil {
		return nil, err
	}

	var results []*vo.WmsOutboundNotificationListVO
	for _, notification := range notifications {
		listVO := &vo.WmsOutboundNotificationListVO{}
		copier.Copy(listVO, notification)

		// 填充扩展信息
		s.fillListExtendedInfo(ctx, listVO)

		results = append(results, listVO)
	}

	return results, nil
}
