package controller

import (
	// "strconv"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/validator"

	"github.com/kataras/iris/v12"
	// "github.com/kataras/iris/v12/context"
	// "github.com/kataras/iris/v12/mvc"
)

// AccountBookController 账套控制器
type AccountBookControllerImpl struct {
	*BaseControllerImpl                            // 嵌入 BaseControllerImpl
	accountBookService  service.AccountBookService // Renamed field
}

// NewAccountBookControllerImpl 创建账套控制器实例
func NewAccountBookControllerImpl(cm *ControllerManager) *AccountBookControllerImpl {
	base := NewBaseController(cm)                                        // 使用 ControllerManager 初始化 BaseController
	accountBookService := cm.GetServiceManager().GetAccountBookService() // Corrected: Call as a method on ServiceManager
	if accountBookService == nil {
		base.GetLogger().Fatal(cm.GetContext(), "无法获取 AccountBookService 实例") // Consistent logging with context
	}
	return &AccountBookControllerImpl{
		BaseControllerImpl: base,
		accountBookService: accountBookService, // Use renamed field
	}
}

// CreateAccountBook godoc
// @Summary      创建账套
// @Description  创建一个新的账套
// @Tags         AccountBooks
// @Accept       json
// @Produce      json
// @Param        body body dto.AccountBookCreateOrUpdateDTO true "创建账套请求体"
// @Success      200 {object} response.Response{data=vo.AccountBookVO} "成功响应"
// @Failure      400 {object} response.Response "请求参数错误"
// @Failure      401 {object} response.Response "未授权"
// @Failure      500 {object} response.Response "服务器内部错误"
// @Security     ApiKeyAuth
// @Router       /api/v1/sys/account-books [post]
func (c *AccountBookControllerImpl) CreateAccountBook(ctx iris.Context) {
	opName := "创建账套"
	var req dto.AccountBookCreateOrUpdateDTO

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "account_book")

	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	accountBookVO, err := c.accountBookService.CreateAccountBook(ctx.Request().Context(), req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, accountBookVO)
}

// UpdateAccountBook godoc
// @Summary      更新账套
// @Description  根据 ID 更新账套信息
// @Tags         AccountBooks
// @Accept       json
// @Produce      json
// @Param        id path uint true "账套 ID"
// @Param        body body dto.AccountBookCreateOrUpdateDTO true "更新账套请求体"
// @Success      200 {object} response.Response{data=vo.AccountBookVO} "成功响应"
// @Failure      400 {object} response.Response "请求参数错误或 ID 无效"
// @Failure      401 {object} response.Response "未授权"
// @Failure      404 {object} response.Response "账套未找到"
// @Failure      500 {object} response.Response "服务器内部错误"
// @Security     ApiKeyAuth
// @Router       /api/v1/sys/account-books/{id} [put]
func (c *AccountBookControllerImpl) UpdateAccountBook(ctx iris.Context) {
	opName := "更新账套"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "account_book")

	idUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	id := uint(idUint64)

	var req dto.AccountBookCreateOrUpdateDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("id", id), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	accountBookVO, err := c.accountBookService.UpdateAccountBook(ctx.Request().Context(), id, req)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, accountBookVO)
}

// DeleteAccountBook godoc
// @Summary      删除账套
// @Description  根据 ID 删除账套
// @Tags         AccountBooks
// @Produce      json
// @Param        id path uint true "账套 ID"
// @Success      200 {object} response.Response "成功响应"
// @Failure      400 {object} response.Response "ID 无效"
// @Failure      401 {object} response.Response "未授权"
// @Failure      404 {object} response.Response "账套未找到"
// @Failure      500 {object} response.Response "服务器内部错误"
// @Security     ApiKeyAuth
// @Router       /api/v1/sys/account-books/{id} [delete]
func (c *AccountBookControllerImpl) DeleteAccountBook(ctx iris.Context) {
	opName := "删除账套"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "account_book")

	idUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	id := uint(idUint64)

	if err := c.accountBookService.DeleteAccountBook(ctx.Request().Context(), id); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.SuccessWithMessage(ctx, "删除成功", nil)
}

// GetAccountBookByID godoc
// @Summary      获取单个账套
// @Description  根据 ID 获取账套详细信息
// @Tags         AccountBooks
// @Produce      json
// @Param        id path uint true "账套 ID"
// @Success      200 {object} response.Response{data=vo.AccountBookVO} "成功响应"
// @Failure      400 {object} response.Response "ID 无效"
// @Failure      401 {object} response.Response "未授权"
// @Failure      404 {object} response.Response "账套未找到"
// @Failure      500 {object} response.Response "服务器内部错误"
// @Security     ApiKeyAuth
// @Router       /api/v1/sys/account-books/{id} [get]
func (c *AccountBookControllerImpl) GetAccountBookByID(ctx iris.Context) {
	opName := "获取账套详情"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "account_book")

	idUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	id := uint(idUint64)

	accountBookVO, err := c.accountBookService.GetAccountBookByID(ctx.Request().Context(), id)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, accountBookVO)
}

// PageAccountBooks godoc
// @Summary      分页获取账套列表
// @Description  根据查询条件分页获取账套列表
// @Tags         AccountBooks
// @Produce      json
// @Param        code query string false "账套编码 (模糊查询)"
// @Param        name query string false "账套名称 (模糊查询)"
// @Param        companyName query string false "公司名称 (模糊查询)"
// @Param        status query int false "状态 (0=禁用, 1=启用)"
// @Param        isGroup query bool false "是否集团账套"
// @Param        isVirtual query bool false "是否虚拟账套"
// @Param        pageNum query int false "页码" default(1)
// @Param        pageSize query int false "每页数量" default(10)
// @Param        sort query string false "排序字段 (例如: 'name asc, code desc')"
// @Success      200 {object} response.Response{data=vo.PageVO{list=[]vo.AccountBookVO}} "成功响应"
// @Failure      400 {object} response.Response "查询参数错误"
// @Failure      401 {object} response.Response "未授权"
// @Failure      500 {object} response.Response "服务器内部错误"
// @Security     ApiKeyAuth
// @Router       /api/v1/sys/account-books [get]
func (c *AccountBookControllerImpl) PageAccountBooks(ctx iris.Context) {
	opName := "分页查询账套"
	var queryDTO dto.AccountBookPageQueryDTO

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "account_book")

	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析查询参数失败").WithCause(err), opName)
		return
	}

	pageVO, err := c.accountBookService.PageAccountBooks(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, pageVO)
}

// ListAccountBooks godoc
// @Summary      获取所有账套列表
// @Description  获取所有符合条件的账套列表（不分页）
// @Tags         AccountBooks
// @Produce      json
// @Param        code query string false "账套编码 (模糊查询)"
// @Param        name query string false "账套名称 (模糊查询)"
// @Param        companyName query string false "公司名称 (模糊查询)"
// @Param        status query int false "状态 (0=禁用, 1=启用)"
// @Param        isGroup query bool false "是否集团账套"
// @Param        isVirtual query bool false "是否虚拟账套"
// @Param        sort query string false "排序字段 (例如: 'name asc, code desc')"
// @Success      200 {object} response.Response{data=[]vo.AccountBookVO} "成功响应"
// @Failure      400 {object} response.Response "查询参数错误"
// @Failure      401 {object} response.Response "未授权"
// @Failure      500 {object} response.Response "服务器内部错误"
// @Security     ApiKeyAuth
// @Router       /api/v1/sys/account-books/list [get]
func (c *AccountBookControllerImpl) ListAccountBooks(ctx iris.Context) {
	opName := "查询账套列表"
	var queryDTO dto.AccountBookPageQueryDTO

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "account_book")

	if err := ctx.ReadQuery(&queryDTO); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析查询参数失败").WithCause(err), opName)
		return
	}

	listVO, err := c.accountBookService.ListAccountBooks(ctx.Request().Context(), queryDTO)
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.Success(ctx, listVO)
}

// UpdateAccountBookStatus godoc
// @Summary      更新账套状态
// @Description  根据 ID 更新账套的状态
// @Tags         AccountBooks
// @Accept       json
// @Produce      json
// @Param        id path uint true "账套 ID"
// @Param        body body dto.AccountBookUpdateStatusDTO true "更新状态请求体 (包含 status 字段)"
// @Success      200 {object} response.Response "成功响应"
// @Failure      400 {object} response.Response "请求参数错误或 ID 无效"
// @Failure      401 {object} response.Response "未授权"
// @Failure      404 {object} response.Response "账套未找到"
// @Failure      500 {object} response.Response "服务器内部错误"
// @Security     ApiKeyAuth
// @Router       /api/v1/sys/account-books/{id}/status [patch]
func (c *AccountBookControllerImpl) UpdateAccountBookStatus(ctx iris.Context) {
	opName := "更新账套状态"

	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "account_book")

	idUint64, err := c.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		c.HandleError(ctx, err, opName)
		return
	}
	id := uint(idUint64)

	var req dto.AccountBookUpdateStatusDTO
	if err := ctx.ReadJSON(&req); err != nil {
		c.HandleError(ctx, apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "读取或解析请求体失败").WithCause(err), opName)
		return
	}

	validationErrs := validator.Struct(req)
	if validationErrs != nil {
		c.GetLogger().Warn(ctx.Request().Context(), opName+" - 参数验证失败", logger.WithField("id", id), logger.WithField("errors", validationErrs))
		customErr := apperrors.NewValidationErrorFromValidator(validationErrs)
		c.FailWithError(ctx, customErr)
		return
	}

	if err := c.accountBookService.UpdateAccountBookStatus(ctx.Request().Context(), id, req.Status); err != nil {
		c.HandleError(ctx, err, opName)
		return
	}

	c.SuccessWithMessage(ctx, "更新状态成功", nil)
}
