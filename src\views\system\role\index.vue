<template>
  <div>
    <!-- Add role table, permissions management, etc. here -->
    <!-- 表格组件 -->
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="180"  
      @refresh="loadData"
      @add="handleAdd"
      @batch-delete="handleBatchDelete"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 状态列插槽 -->
      <template #column-status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- 新增：系统角色列插槽 -->
      <template #column-isSystem="{ row }">
        <el-tag :type="row.isSystem ? 'warning' : 'info'">
          {{ row.isSystem ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 卡片视图操作按钮插槽 (如果使用卡片视图) -->
      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top">
          <el-button circle :icon="View" size="small" @click="handleView(row)" v-if="!operationButtons.find(b => b.label === '查看')?.hidden"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button 
            circle 
            :icon="Edit" 
            type="primary" 
            size="small" 
            @click="handleEdit(row)" 
            v-if="!operationButtons.find(b => b.label === '编辑')?.hidden && hasPermission('sys:role:edit')"
            :disabled="row.isSystem"
          ></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            @click="handleDelete(row)" 
            v-if="!operationButtons.find(b => b.label === '删除')?.hidden && hasPermission('sys:role:delete')"
            :disabled="row.isSystem"
          ></el-button>
        </el-tooltip>
        <!-- 可根据需要添加其他按钮，例如分配用户 -->
      </template>

    </VNTable>

    <!-- 新增/编辑 弹窗 -->
    <!-- 调整弹窗宽度 -->
    <!-- 弹窗打开时加载菜单树 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%" 
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      @open="handleDialogOpen"
      draggable
      align-center
    >
      <!-- 恢复 VNForm -->
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2"
        :label-width="labelWidth || '100px'"
        :loading="loading"
        :group-title-type="groupTitleType || 'h4'"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <template #form-item-menuIds>
          <el-scrollbar max-height="300px" style="width: 100%;">
            <el-tree
              ref="menuTreeRef"
              :data="processedMenuTreeData"
              :props="{ label: 'title', children: 'children' }"
              show-checkbox
              node-key="id"
              :default-expand-all="false"
              :check-strictly="false"
              @check="handleMenuCheckChange"
              :default-checked-keys="formData.menuIds || []"
              :disabled="isViewing"
            >
              <!-- 新增：自定义树节点插槽以显示图标 -->
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  <el-icon v-if="data.icon" style="margin-right: 4px; vertical-align: middle;">
                    <component :is="data.icon" />
                  </el-icon>
                  <span style="vertical-align: middle;">{{ node.label }}</span>
                </span>
              </template>
            </el-tree>
          </el-scrollbar>
        </template>

        <!-- === 开始复制 user/index.vue 的 #actions 插槽 === -->
        <template #actions>
          <template v-if="isViewing">
            <el-button
              v-if="hasPermission('sys:role:printpreview')" 
              :icon="View" 
              @click="handlePrintPreview"
            >
              打印预览
            </el-button>
            <el-button
              v-if="hasPermission('sys:role:print')" 
              type="primary"
              :icon="Printer" 
              @click="handlePrint"
            >
              打印
            </el-button>
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else> 
            <el-button type="primary" @click="submitForm" :loading="loading">{{ formMode === 'add' ? '新增' : '提交'}}</el-button> 
            <el-button @click="handleCloseDialog">取消</el-button>
          </template>
        </template>
        <!-- === 结束复制 === -->
      </VNForm> 
      <!-- ================== -->

       <!-- 如果隐藏了 VNForm 的按钮，可以在这里定义弹窗底部按钮 -->
       <!--
       <template #footer>
          <span class="dialog-footer">
             <el-button @click="handleCloseDialog">取 消</el-button>
             <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
          </span>
       </template>
       -->
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
// import type { VNTableProps, TableColumn, PaginationConfig, ActionButton } from '@/components/VNTable/types';
import type { TableColumn, PaginationConfig, ActionButton } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton, ElTree, ElScrollbar, ElIcon } from 'element-plus';
// import type { FormInstance, FormRules } from 'element-plus';
import type { ElTree as ElTreeType } from 'element-plus';

// --- 引入角色管理 API 函数 ---
import {
  getRoleList,
  addRole,
  updateRole,
  deleteRole,
  getRoleDetail,
  getMenuTree,
  batchDeleteRoles,
  getRoleMenus,
} from '@/api/system/systemRole';
import type { RoleFormData, RoleListItem, RoleQueryParams, MenuTreeItem, RoleListResponse, RoleDetail } from '@/api/system/systemRole';

import { hasPermission } from '@/hooks/usePermission';
// --- 引入所需图标 ---
// import { View, Edit, Delete, Plus, Printer } from '@element-plus/icons-vue';
import { View, Edit, Delete, Printer } from '@element-plus/icons-vue';

// +++ 新增：辅助函数：获取并格式化API错误消息 +++
// 正确的、能生成结构化 HTML 的 getApiErrorMessage 函数
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  // 仅当它与 apiError.message (如果存在) 不同或 apiError.message 不存在时添加
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      // 尝试使用 field 作为行号提示，如果没有 field，则使用数组索引
      // const listItemPrefix = detail.field ? `字段 ${detail.field}` : `${index + 1}`;
      // detailsHtml += `<li style="margin-bottom: 4px;">${listItemPrefix}: ${detail.message}</li>`;
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  // 组合与回退
  if (messageParts.length > 0) {
    // 使用 <div> 包裹每个主要部分，并通过 style 控制间距，而不是依赖 <br> 的不精确控制
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试'; // 通用回退消息
};
// +++ 结束新增 +++

// --- 日期时间格式化辅助函数 (如果表格中需要显示时间) ---
const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString || dateString.startsWith('0001-01-01')) {
    return '-';
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '-';
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error('日期格式化错误:', dateString, e);
    return '-';
  }
};

// --- 组件 Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const menuTreeRef = ref<InstanceType<typeof ElTreeType>>();
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentRow = ref<RoleListItem | null>(null);

// +++ 新增 isViewing 计算属性 +++
const isViewing = computed(() => formMode.value === 'view');
// +++ 结束新增 +++

// --- 响应式状态 ---
const loading = ref(false);
const tableData = ref<RoleListItem[]>([]);
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const formData = ref<Partial<RoleFormData>>({});
const menuTreeData = ref<MenuTreeItem[]>([]);

// +++ 新增：用于在查看模式下给树节点添加 disabled 属性的计算属性 +++
const processedMenuTreeData = computed(() => {
  const disableNodes = (nodes: MenuTreeItem[]): MenuTreeItem[] => {
    if (!nodes) return [];
    return nodes.map(node => ({
      ...node,
      disabled: isViewing.value, // 如果是查看模式，则禁用节点
      children: node.children ? disableNodes(node.children) : [],
    }));
  };
  return isViewing.value ? disableNodes(menuTreeData.value) : menuTreeData.value;
});
// +++ 结束新增 +++

const currentFilters = ref<Record<string, any>>({});
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null);

// --- 表格列配置 ---
const tableColumns = ref<TableColumn[]>([
  { prop: 'name', label: '角色名称', minWidth: 150, sortable: true, filterable: true },
  { prop: 'code', label: '角色编码', minWidth: 150, sortable: true, filterable: true },
  {
    prop: 'isSystem', // 新增列定义
    label: '系统角色',
    width: 100,
    slot: true, // 使用插槽渲染
    filterable: true, // 允许筛选
    filterType: 'select',
    filterOptions: [{ label: '是', value: true }, { label: '否', value: false }]
  },
  {
    prop: 'status', label: '状态', width: 90, slot: true,
    filterable: true, filterType: 'select',
    filterOptions: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }]
  },
  { prop: 'remark', label: '备注', minWidth: 200 },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    sortable: true,
    formatter: (row) => formatDateTime(row.createdAt)
  },
]);

// --- 工具栏配置 ---
const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('sys:role:add'),
  batchDelete: hasPermission('sys:role:batchdelete'),
  filter: hasPermission('sys:role:search'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('sys:role:import'),
  export: hasPermission('sys:role:export'),
}));

// --- 行操作按钮配置 ---
const operationButtons = computed<ActionButton[]>(() => [
  {
    label: '查看',
    icon: 'View',
    handler: (row) => handleView(row),
    hidden: !hasPermission('sys:role:view')
  },
  {
    label: '编辑',
    icon: 'Edit',
    type: 'primary',
    handler: (row) => handleEdit(row),
    hidden: !hasPermission('sys:role:edit')
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    handler: (row) => handleDelete(row),
    hidden: !hasPermission('sys:role:delete')
  },
]);

// --- 表单字段配置 ---
const formFields = computed<HeaderField[]>(() => [
  // --- 基本信息组 ---
  { field: 'code', label: '角色编码', group: '基本信息', rules: [{ required: true, message: '角色编码为必填项' }], disabled: formMode.value === 'edit' || isViewing.value },
  { field: 'name', label: '角色名称', group: '基本信息', rules: [{ required: true, message: '角色名称为必填项' }], disabled: isViewing.value },
  { field: 'status', label: '状态', group: '基本信息', type: 'radio', options: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }], defaultValue: 1, disabled: isViewing.value },
  { field: 'remark', label: '备注', group: '基本信息', type: 'textarea', rows: 3, md: 24, lg: 24, xl: 24, disabled: isViewing.value }, // Span full width
  // --- 权限设置组 ---
  {
      field: 'menuIds',
      label: '菜单权限',
      group: '权限设置',
      type: 'slot',
      md: 24,
      lg: 24,
      xl: 24,
      className: 'menu-tree-field',
      disabled: isViewing.value // 在查看模式下禁用
  },
]);

// --- 弹窗标题 ---
const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增角色';
  if (formMode.value === 'edit') return '编辑角色';
  if (formMode.value === 'view') return '查看角色';
  return '角色管理'; // 默认标题
});

// --- 生命周期钩子 ---
onMounted(() => {
  console.log('[onMounted] 开始执行');
  loadData();
  console.log('[onMounted] 调用 loadMenuTree');
  loadMenuTree(); 
  console.log('[onMounted] 执行完毕');
});

// --- 方法 ---

// 加载表格数据
const loadData = async () => {
  loading.value = true; 
  try {
    let sortString: string | undefined = undefined;
    if (currentSort.value && currentSort.value.prop) {
        const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc';
        sortString = `${currentSort.value.prop},${direction}`;
    }
    const params: RoleQueryParams = { 
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...(currentFilters.value as Omit<RoleQueryParams, 'pageNum' | 'pageSize' | 'sort'>), 
      sort: sortString
    };
    
    console.log('[加载数据] 请求角色列表参数:', params);
    const res = await getRoleList(params) as unknown as RoleListResponse;
    console.log('[加载数据] 收到角色列表响应 (拦截器处理后):', JSON.stringify(res));

    if (res && Array.isArray(res.list)) { 
      tableData.value = res.list; 
      pagination.total = res.total || 0; 
    } else {
      tableData.value = []; 
      pagination.total = 0;
      console.warn('getRoleList 返回的业务数据结构不符合预期 (拦截器处理后):', res);
    }
  } catch (error) {
     console.error('[加载数据] 出错:', error);
     ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: getApiErrorMessage(error), showClose: true, duration: 5*1000 });
     tableData.value = []; 
     pagination.total = 0;
  } finally {
    loading.value = false; 
    console.log('[loadData] Loading finished, loading.value set to:', loading.value); 
  }
};

// 加载菜单树数据
const loadMenuTree = async () => {
    console.log('[loadMenuTree] 开始加载...'); 
    try {
        console.log('[loadMenuTree] 调用 getMenuTree API');
        const res = await getMenuTree(); 
        console.log('[loadMenuTree] 收到 API 响应 (拦截器处理后?): ', JSON.stringify(res)); 

        if (Array.isArray(res)) {
            console.log('[loadMenuTree] 响应直接是数组。准备设置 menuTreeData');
            menuTreeData.value = res as MenuTreeItem[];
            console.log('[loadMenuTree] menuTreeData 已设置, length:', menuTreeData.value.length);
        } 
        else if (res && Array.isArray((res as any).data)) {
             console.log('[loadMenuTree] 响应是对象且包含 data 数组。准备设置 menuTreeData');
             menuTreeData.value = (res as any).data as MenuTreeItem[];
             console.log('[loadMenuTree] menuTreeData 已设置, length:', menuTreeData.value.length);
        } 
        else {
            console.warn('[loadMenuTree] 无法从 API 响应中提取菜单数组:', res);
            menuTreeData.value = [];
        }
    } catch (error) {
        ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: getApiErrorMessage(error), showClose: true, duration: 5*1000 });
        console.error('[loadMenuTree] 加载菜单树时捕获到错误:', error);
        menuTreeData.value = [];
    } finally {
        console.log('[loadMenuTree] 加载结束');
    }
};

// 处理弹窗打开事件 (简化)
const handleDialogOpen = () => {
    console.log('[handleDialogOpen] 弹窗已打开，formMode:', formMode.value);
    // 只处理新增模式的清空和校验清理
    if (formMode.value === 'add') {
         console.log('[handleDialogOpen] 新增模式，准备 nextTick 清空选中 key');
         nextTick(() => {
            console.log('[handleDialogOpen] nextTick 执行 (add)，menuTreeRef:', menuTreeRef.value);
            if (menuTreeRef.value) { 
               console.log('[handleDialogOpen] 清空选中 key (add)');
               menuTreeRef.value.setCheckedKeys([], false);
            } else {
               console.warn('[handleDialogOpen] nextTick (add) 时 menuTreeRef 不可用');
            }
         });
    } 
    // 清理校验状态总是需要
    nextTick(() => {
       vnFormRef.value?.clearValidate();
    });
};

const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: RoleListItem) => {
  console.log(`[handleOpenForm] 开始，模式: ${mode}`);
  formMode.value = mode;

  // 在打开任何模式的表单前，先重置可能的展开状态
  // 确保 menuTreeRef 存在
  if (menuTreeRef.value) {
    // 折叠所有节点
    Object.values(menuTreeRef.value.store.nodesMap).forEach(node => {
      node.expanded = false;
    });
  }

  if (mode === 'edit' || mode === 'view') { 
    console.log(`[handleOpenForm] ${mode}模式，角色 ID: ${rowData?.id}`);
    if (!rowData || !rowData.id) {
      console.error('[handleOpenForm] 编辑模式下缺少角色数据或 ID');
      ElMessage.error('无法编辑角色：缺少必要信息');
      return;
    }
    currentRow.value = rowData!;
    console.log('[handleOpenForm] 开始加载编辑数据 (角色详情和菜单权限)');
    loading.value = true;
    let fetchedMenuIdsForEdit: number[] = []; // 暂存 menuIds
    try {
      console.log('[handleOpenForm] 调用 getRoleDetail');
      const detailData = await getRoleDetail(rowData!.id) as unknown as Omit<RoleDetail, 'menuIds'>;
      console.log('[handleOpenForm] getRoleDetail 响应 (原始): ', JSON.stringify(detailData));

      if (detailData && typeof detailData === 'object') {
          formData.value = { ...detailData, menuIds: [] }; 
          console.log('[handleOpenForm] 角色详情设置到 formData');
          try {
              console.log('[handleOpenForm] 调用 getRoleMenus');
              const menuResponseData = await getRoleMenus(rowData!.id) as unknown as { menuIds: number[] } | number[];
              console.log('[handleOpenForm] getRoleMenus 响应 (原始): ', JSON.stringify(menuResponseData));
              if (Array.isArray(menuResponseData)) {
                  // 修正：从对象数组中提取 id，并添加类型检查
                  if (menuResponseData.length > 0 && typeof menuResponseData[0] === 'object' && menuResponseData[0] !== null && 'id' in menuResponseData[0]) {
                     fetchedMenuIdsForEdit = menuResponseData.map((item: any) => item.id).filter(id => typeof id === 'number');
                  } else {
                     // 如果数组为空或元素不是预期的对象结构，则清空
                     fetchedMenuIdsForEdit = [];
                     console.warn('[handleOpenForm] getRoleMenus 返回的数组结构不符合预期 (元素非对象或缺少id)');
                  }
              } else if (typeof menuResponseData === 'object' && menuResponseData !== null && Array.isArray(menuResponseData.menuIds)) {
                  fetchedMenuIdsForEdit = menuResponseData.menuIds.filter(id => typeof id === 'number');
              } else {
                  console.warn('[handleOpenForm] getRoleMenus 返回的数据结构未知:', menuResponseData);
              }
              console.log('[handleOpenForm] 编辑模式，获取到菜单 ID:', fetchedMenuIdsForEdit);
          } catch (menuError) {
              console.error('[handleOpenForm] 获取角色菜单时出错:', menuError);
              ElMessage.error('获取角色菜单权限失败');
          }
      } else {
          console.error('[handleOpenForm] Invalid detailData structure:', detailData);
          throw new Error('从 getRoleDetail 获取的数据结构无效。');
      }
    } catch (error) {
         console.error('[handleOpenForm] 获取角色详情/菜单时出错:', error);
         ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: getApiErrorMessage(error), showClose: true, duration: 5*1000 });
    } finally {
        loading.value = false;
        console.log('[handleOpenForm] 编辑数据加载完成 (finally)');
    }
    dialogVisible.value = true;
    console.log(`[handleOpenForm] (${mode}) dialogVisible 已设置为 true, 准备 nextTick 设置选中 keys`);
    nextTick(() => {
      // --- 新增：在 nextTick 开始时确保树是折叠的 --- 
      if (menuTreeRef.value) {
        console.log(`[handleOpenForm] (${mode}) nextTick - 强制折叠所有节点`);
        Object.values(menuTreeRef.value.store.nodesMap).forEach(node => {
          node.expanded = false;
        });
      }
      // --- 结束新增 ---
      console.log(`[handleOpenForm] (${mode}) nextTick 执行, menuTreeRef:`, menuTreeRef.value);
      setTimeout(() => {
        console.log(`[handleOpenForm] (${mode}) setTimeout 触发, menuTreeRef:`, menuTreeRef.value);
        console.log(`[handleOpenForm] (${mode}) setTimeout - 当前 menuTreeData (部分):`, JSON.stringify(menuTreeData.value?.slice(0, 5))); 
        console.log(`[handleOpenForm] (${mode}) setTimeout - 待设置的 fetchedMenuIdsForEdit:`, JSON.stringify(fetchedMenuIdsForEdit));
        if (menuTreeRef.value) {
            console.log(`[handleOpenForm] (${mode}) setTimeout - 准备调用 setCheckedKeys(ids, true)`);
            menuTreeRef.value.setCheckedKeys(fetchedMenuIdsForEdit.filter(id => typeof id === 'number') || [], false); 
            formData.value = { ...formData.value, menuIds: fetchedMenuIdsForEdit.filter(id => typeof id === 'number') }; 
            console.log(`[handleOpenForm] (${mode}) setTimeout - setCheckedKeys 调用完毕`);
        } else {
             console.warn(`[handleOpenForm] (${mode}) setTimeout 时 menuTreeRef 不可用`);
        }
      }, 0); 
    });
  } else { 
    formData.value = { status: 1, menuIds: [] };
    currentRow.value = null;
    console.log('[handleOpenForm] 新增模式，设置 formData');
    console.log('[handleOpenForm] (Add) 准备设置 dialogVisible 为 true');
    dialogVisible.value = true; 
    console.log('[handleOpenForm] (Add) dialogVisible 已设置为 true');
    // --- 新增：在 nextTick 开始时确保树是折叠的 (也适用于新增模式) --- 
    nextTick(() => {
      if (menuTreeRef.value) {
        console.log('[handleOpenForm] (Add) nextTick - 强制折叠所有节点');
        Object.values(menuTreeRef.value.store.nodesMap).forEach(node => {
          node.expanded = false;
        });
         // 对于新增模式，也清空选中状态
        menuTreeRef.value.setCheckedKeys([], false);
      }
    });
    // --- 结束新增 ---
  }
  // --- 对于所有模式，在 nextTick 中清理表单校验 --- 
  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
  // --- 结束 --- 
};

// 关闭弹窗
const handleCloseDialog = () => {
  console.log('[handleCloseDialog] 开始关闭弹窗');
  dialogVisible.value = false;
  formData.value = {};
  currentRow.value = null;
};

// 修改：在查看模式下，如果菜单树被禁用，handleMenuCheckChange 不应更新数据
const handleMenuCheckChange = () => {
    if (!menuTreeRef.value || isViewing.value) return; // 如果是查看模式，则不执行更新
    const checkedKeys = menuTreeRef.value.getCheckedKeys(false); 
    formData.value.menuIds = checkedKeys.filter(key => typeof key === 'number') as number[];
    console.log('菜单权限 ID 更新 (来自 Tree Check - 修正): ', formData.value.menuIds);
};

// 提交表单 (新增/编辑)
const submitForm = async () => {
  // +++ 新增：如果是查看模式，直接关闭弹窗 +++
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  // +++ 结束新增 +++
  const formRef = vnFormRef.value;
  if (!formRef) {
     console.error("VNForm ref is not available for submission.");
     return;
  }

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  // 恢复手动调用 handleMenuCheckChange
  handleMenuCheckChange();

  // 准备要发送到后端的数据
  const dataToSend: RoleFormData = {
    name: formData.value.name!,
    code: formData.value.code!,
    status: formData.value.status,
    remark: formData.value.remark,
    menuIds: formData.value.menuIds || [],
  };

  console.log('[提交表单] 准备发送的数据:', JSON.stringify(dataToSend));

  loading.value = true; 
  try {
    if (formMode.value === 'add') { 
      await addRole(dataToSend); 
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentRow.value) { 
      await updateRole(currentRow.value.id, dataToSend); 
      ElMessage.success('编辑成功');
    }
    handleCloseDialog(); 
    loadData(); 
  } catch (error) {
    console.error(`保存角色时出错 (${formMode.value}):`, error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: getApiErrorMessage(error), showClose: true, duration: 5*1000 });
  } finally {
    loading.value = false; 
  }
};

// --- 删除操作 ---
const handleDelete = async (row: RoleListItem) => {
  try {
    await ElMessageBox.confirm(`确定删除角色 "${row.name}" (${row.code}) 吗?`, '确认删除', { type: 'warning' });
    loading.value = true;
    await deleteRole(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
       console.error('删除角色时出错:', error);
       ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: getApiErrorMessage(error), showClose: true, duration: 5*1000 });
    } else {
       ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

// --- 批量删除操作 ---
const handleBatchDelete = async (selectedRows: RoleListItem[]) => {
  if (!selectedRows || selectedRows.length === 0) {
    ElMessage.warning('请先选择要删除的角色');
    return;
  }
  try {
    await ElMessageBox.confirm(`确定删除选中的 ${selectedRows.length} 个角色吗?`, '确认批量删除', { type: 'warning' });
    loading.value = true;
    const ids = selectedRows.map(row => row.id);
    await batchDeleteRoles(ids);
    ElMessage.success('批量删除成功');
    loadData();
    vnTableRef.value?.clearSelection();
  } catch (error) {
     if (error !== 'cancel') {
        console.error('批量删除角色时出错:', error);
        ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: getApiErrorMessage(error), showClose: true, duration: 5*1000 });
     } else {
        ElMessage.info('已取消批量删除');
     }
  } finally {
     loading.value = false;
  }
};

// --- 处理 VNTable 组件触发的事件 ---

// 筛选条件变化
const handleFilterChange = (filters: Record<string, any>) => {
    console.log('筛选条件变化:', filters);
    currentFilters.value = filters; 
    pagination.currentPage = 1; 
    loadData(); 
};

// 页码变化
const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  loadData();
};

// 每页显示数量变化
const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadData();
};

// 排序条件变化
const handleSortChange = (sort: { prop: string; order: 'ascending' | 'descending' | null }) => {
  console.log('排序条件变化:', sort);
  currentSort.value = sort.order ? sort : null;
  loadData(); 
};

// 表格行选中状态变化
const handleSelectionChange = (rows: RoleListItem[]) => {
  // 可在此处处理选中行数据，例如用于批量删除按钮的状态更新
  console.log('选中行变更:', rows);
};

// --- 处理按钮点击事件 ---
const handleAdd = () => {
  handleOpenForm('add');
};

const handleEdit = (row: RoleListItem) => {
  handleOpenForm('edit', row);
};

// +++ 新增 handleView 函数 +++
const handleView = (row: RoleListItem) => {
  handleOpenForm('view', row);
};
// +++ 结束新增 +++

// +++ 新增：打印处理函数（占位）+++
const handlePrintPreview = () => {
  if (!currentRow.value) return;
  console.log('触发角色打印预览，角色:', currentRow.value);
  ElMessage.info('角色打印预览功能待实现');
  // TODO: Implement print preview logic
};

const handlePrint = () => {
  if (!currentRow.value) return;
  console.log('触发角色打印，角色:', currentRow.value);
  ElMessage.info('角色打印功能待实现');
  // TODO: Implement print logic
};
// +++ 结束新增 +++

// 确保以下 prop 存在或有默认值，以便与 user/index.vue 对齐 VNForm 调用
const labelWidth = ref('100px'); // 或从 props 传入
const groupTitleType = ref<'h4' | 'divider' | 'none'>('h4'); // 明确指定类型

</script>

<style scoped>
/* 如果需要，可以在这里添加特定于角色管理页面的样式 */
.dialog-footer {
  text-align: right;
}

/* 确保分页组件靠右对齐 */
:deep(.el-pagination) {
  margin-top: 15px;
  justify-content: flex-end;
}

:deep(.menu-tree-field .el-scrollbar) {
  width: 100%; /* 基础宽度 */
  flex: 1;     /* 在 flex 容器中伸展 */
  min-width: 0; /* 防止溢出 */
}

/* 可选：为 el-scrollbar 添加边框 */
</style>
