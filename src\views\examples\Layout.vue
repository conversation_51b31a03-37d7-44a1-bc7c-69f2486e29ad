<template>
  <div class="component-examples-layout">
    <!-- 示例导航 -->
    <ExampleNav />
    <!-- 具体的示例组件将在这里渲染 -->
    <div class="example-content">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import ExampleNav from '@/components/exampleNav.vue'; // 引入示例导航栏
</script>

<style scoped>
.component-examples-layout {
  /* 可以根据需要添加整体布局样式 */
}
.example-content {
  padding: 20px; /* 给示例内容一些内边距 */
  background-color: #fff; /* 示例区域白色背景 */
  margin: 20px; /* 与导航栏和边框有一些间距 */
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style> 