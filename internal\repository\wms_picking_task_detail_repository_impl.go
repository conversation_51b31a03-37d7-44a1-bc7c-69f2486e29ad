package repository

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
	"backend/pkg/response"
)

// WmsPickingTaskDetailRepository 定义拣货任务明细仓库接口
type WmsPickingTaskDetailRepository interface {
	BaseRepository[entity.WmsPickingTaskDetail, uint]

	// 根据任务ID查询明细
	FindByTaskID(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error)

	// 根据任务ID和行号查询明细
	FindByTaskIDAndLineNo(ctx context.Context, taskID uint, lineNo int) (*entity.WmsPickingTaskDetail, error)

	// 根据分配ID查询明细
	FindByAllocationID(ctx context.Context, allocationID uint) (*entity.WmsPickingTaskDetail, error)

	// 根据物料ID查询明细
	FindByItemID(ctx context.Context, itemID uint) ([]*entity.WmsPickingTaskDetail, error)

	// 根据库位ID查询明细
	FindByLocationID(ctx context.Context, locationID uint) ([]*entity.WmsPickingTaskDetail, error)

	// 批量创建明细
	BatchCreate(ctx context.Context, details []*entity.WmsPickingTaskDetail) error

	// 批量更新明细
	BatchUpdate(ctx context.Context, details []*entity.WmsPickingTaskDetail) error

	// 批量删除明细
	BatchDeleteByTaskID(ctx context.Context, taskID uint) error

	// 更新拣货数量
	UpdatePickedQty(ctx context.Context, id uint, pickedQty float64) error

	// 批量更新拣货数量
	BatchUpdatePickedQty(ctx context.Context, updates map[uint]float64) error

	// 更新状态
	UpdateStatus(ctx context.Context, id uint, status string) error

	// 批量更新状态
	BatchUpdateStatus(ctx context.Context, ids []uint, status string) error

	// 获取未完成的明细
	GetUncompletedDetails(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error)

	// 获取已完成的明细
	GetCompletedDetails(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error)

	// 根据拣货序号查询明细
	FindByPickingSequence(ctx context.Context, taskID uint, sequence int) (*entity.WmsPickingTaskDetail, error)

	// 获取下一个拣货明细
	GetNextPickingDetail(ctx context.Context, taskID uint, currentSequence int) (*entity.WmsPickingTaskDetail, error)

	// 获取明细统计信息
	GetDetailStats(ctx context.Context, taskID uint) (map[string]interface{}, error)

	// 根据批次号查询明细
	FindByBatchNo(ctx context.Context, batchNo string) ([]*entity.WmsPickingTaskDetail, error)

	// 获取异常明细
	GetExceptionDetails(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error)

	// 更新拣货时间
	UpdatePickedTime(ctx context.Context, id uint) error

	// 批量更新拣货序号
	BatchUpdatePickingSequence(ctx context.Context, updates map[uint]int) error
}

// wmsPickingTaskDetailRepository 拣货任务明细仓库实现
type wmsPickingTaskDetailRepository struct {
	BaseRepositoryImpl[entity.WmsPickingTaskDetail, uint]
}

// NewWmsPickingTaskDetailRepository 创建拣货任务明细仓库
func NewWmsPickingTaskDetailRepository(db *gorm.DB) WmsPickingTaskDetailRepository {
	return &wmsPickingTaskDetailRepository{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsPickingTaskDetail, uint]{
			db: db,
		},
	}
}

// FindByTaskID 根据任务ID查询明细
func (r *wmsPickingTaskDetailRepository) FindByTaskID(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("task_id", taskID),
	}
	sortInfos := []response.SortInfo{
		{Field: "picking_sequence", Order: "asc"},
		{Field: "line_no", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByTaskIDAndLineNo 根据任务ID和行号查询明细
func (r *wmsPickingTaskDetailRepository) FindByTaskIDAndLineNo(ctx context.Context, taskID uint, lineNo int) (*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("task_id", taskID),
		NewEqualCondition("line_no", lineNo),
	}

	return r.FindOneByCondition(ctx, conditions)
}

// FindByAllocationID 根据分配ID查询明细
func (r *wmsPickingTaskDetailRepository) FindByAllocationID(ctx context.Context, allocationID uint) (*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("allocation_id", allocationID),
	}

	return r.FindOneByCondition(ctx, conditions)
}

// FindByItemID 根据物料ID查询明细
func (r *wmsPickingTaskDetailRepository) FindByItemID(ctx context.Context, itemID uint) ([]*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("item_id", itemID),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByLocationID 根据库位ID查询明细
func (r *wmsPickingTaskDetailRepository) FindByLocationID(ctx context.Context, locationID uint) ([]*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("location_id", locationID),
	}
	sortInfos := []response.SortInfo{
		{Field: "picking_sequence", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// BatchCreate 批量创建明细
func (r *wmsPickingTaskDetailRepository) BatchCreate(ctx context.Context, details []*entity.WmsPickingTaskDetail) error {
	if len(details) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.CreateInBatches(details, 100) // 每批100条
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量创建拣货任务明细失败: count=%d", len(details))
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "批量创建拣货任务明细失败").WithCause(result.Error)
	}

	return nil
}

// BatchUpdate 批量更新明细
func (r *wmsPickingTaskDetailRepository) BatchUpdate(ctx context.Context, details []*entity.WmsPickingTaskDetail) error {
	if len(details) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for _, detail := range details {
		if err := db.Save(detail).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新拣货任务明细失败: id=%d", detail.ID)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新拣货任务明细失败").WithCause(err)
		}
	}

	return nil
}

// BatchDeleteByTaskID 批量删除明细
func (r *wmsPickingTaskDetailRepository) BatchDeleteByTaskID(ctx context.Context, taskID uint) error {
	db := r.GetDB(ctx)
	result := db.Where("task_id = ?", taskID).Delete(&entity.WmsPickingTaskDetail{})
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量删除拣货任务明细失败: taskID=%d", taskID)
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "批量删除拣货任务明细失败").WithCause(result.Error)
	}

	return nil
}

// UpdatePickedQty 更新拣货数量
func (r *wmsPickingTaskDetailRepository) UpdatePickedQty(ctx context.Context, id uint, pickedQty float64) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPickingTaskDetail{}).Where("id = ?", id).Update("picked_qty", pickedQty)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新明细拣货数量失败: id=%d, pickedQty=%f", id, pickedQty)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新明细拣货数量失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "明细不存在")
	}

	return nil
}

// BatchUpdatePickedQty 批量更新拣货数量
func (r *wmsPickingTaskDetailRepository) BatchUpdatePickedQty(ctx context.Context, updates map[uint]float64) error {
	if len(updates) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for id, qty := range updates {
		if err := db.Model(&entity.WmsPickingTaskDetail{}).Where("id = ?", id).Update("picked_qty", qty).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新明细拣货数量失败: id=%d, qty=%f", id, qty)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新明细拣货数量失败").WithCause(err)
		}
	}

	return nil
}

// UpdateStatus 更新状态
func (r *wmsPickingTaskDetailRepository) UpdateStatus(ctx context.Context, id uint, status string) error {
	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPickingTaskDetail{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新明细状态失败: id=%d, status=%s", id, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新明细状态失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "明细不存在")
	}

	return nil
}

// BatchUpdateStatus 批量更新状态
func (r *wmsPickingTaskDetailRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string) error {
	if len(ids) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	result := db.Model(&entity.WmsPickingTaskDetail{}).Where("id IN ?", ids).Update("status", status)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("批量更新明细状态失败: ids=%v, status=%s", ids, status)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新明细状态失败").WithCause(result.Error)
	}

	return nil
}

// GetUncompletedDetails 获取未完成的明细
func (r *wmsPickingTaskDetailRepository) GetUncompletedDetails(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error) {
	db := r.GetDB(ctx)
	var details []*entity.WmsPickingTaskDetail

	result := db.Where("task_id = ? AND picked_qty < required_qty", taskID).
		Order("picking_sequence ASC, line_no ASC").
		Find(&details)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("获取未完成明细失败: taskID=%d", taskID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取未完成明细失败").WithCause(result.Error)
	}

	return details, nil
}

// GetCompletedDetails 获取已完成的明细
func (r *wmsPickingTaskDetailRepository) GetCompletedDetails(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("task_id", taskID),
		NewEqualCondition("status", string(entity.PickingDetailStatusCompleted)),
	}
	sortInfos := []response.SortInfo{
		{Field: "picking_sequence", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindByPickingSequence 根据拣货序号查询明细
func (r *wmsPickingTaskDetailRepository) FindByPickingSequence(ctx context.Context, taskID uint, sequence int) (*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("task_id", taskID),
		NewEqualCondition("picking_sequence", sequence),
	}

	return r.FindOneByCondition(ctx, conditions)
}

// GetNextPickingDetail 获取下一个拣货明细
func (r *wmsPickingTaskDetailRepository) GetNextPickingDetail(ctx context.Context, taskID uint, currentSequence int) (*entity.WmsPickingTaskDetail, error) {
	db := r.GetDB(ctx)
	var detail entity.WmsPickingTaskDetail

	result := db.Where("task_id = ? AND picking_sequence > ? AND status = ?",
		taskID, currentSequence, string(entity.PickingDetailStatusPending)).
		Order("picking_sequence ASC").
		First(&detail)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // 没有下一个明细，返回nil而不是错误
		}
		logger.WithContext(ctx).WithError(result.Error).Errorf("获取下一个拣货明细失败: taskID=%d, currentSequence=%d", taskID, currentSequence)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取下一个拣货明细失败").WithCause(result.Error)
	}

	return &detail, nil
}

// GetDetailStats 获取明细统计信息
func (r *wmsPickingTaskDetailRepository) GetDetailStats(ctx context.Context, taskID uint) (map[string]interface{}, error) {
	db := r.GetDB(ctx)

	var stats struct {
		TotalItems      int64   `json:"total_items"`
		TotalQty        float64 `json:"total_qty"`
		PickedQty       float64 `json:"picked_qty"`
		CompletedItems  int64   `json:"completed_items"`
		PendingItems    int64   `json:"pending_items"`
		InProgressItems int64   `json:"in_progress_items"`
	}

	// 基础统计
	if err := db.Model(&entity.WmsPickingTaskDetail{}).
		Where("task_id = ?", taskID).
		Select("COUNT(*) as total_items, SUM(required_qty) as total_qty, SUM(picked_qty) as picked_qty").
		Scan(&stats).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取明细统计信息失败: taskID=%d", taskID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取明细统计信息失败").WithCause(err)
	}

	// 各状态统计
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := db.Model(&entity.WmsPickingTaskDetail{}).
		Select("status, COUNT(*) as count").
		Where("task_id = ?", taskID).
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		logger.WithContext(ctx).WithError(err).Errorf("获取明细状态统计失败: taskID=%d", taskID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取明细状态统计失败").WithCause(err)
	}

	// 填充状态统计
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(entity.PickingDetailStatusCompleted):
			stats.CompletedItems = sc.Count
		case string(entity.PickingDetailStatusPending):
			stats.PendingItems = sc.Count
		case string(entity.PickingDetailStatusInProgress):
			stats.InProgressItems = sc.Count
		}
	}

	result := map[string]interface{}{
		"total_items":       stats.TotalItems,
		"total_qty":         stats.TotalQty,
		"picked_qty":        stats.PickedQty,
		"completed_items":   stats.CompletedItems,
		"pending_items":     stats.PendingItems,
		"in_progress_items": stats.InProgressItems,
	}

	return result, nil
}

// FindByBatchNo 根据批次号查询明细
func (r *wmsPickingTaskDetailRepository) FindByBatchNo(ctx context.Context, batchNo string) ([]*entity.WmsPickingTaskDetail, error) {
	conditions := []QueryCondition{
		NewEqualCondition("batch_no", batchNo),
	}
	sortInfos := []response.SortInfo{
		{Field: "created_at", Order: "desc"},
	}

	return r.FindByCondition(ctx, conditions, sortInfos)
}

// GetExceptionDetails 获取异常明细
func (r *wmsPickingTaskDetailRepository) GetExceptionDetails(ctx context.Context, taskID uint) ([]*entity.WmsPickingTaskDetail, error) {
	db := r.GetDB(ctx)
	var details []*entity.WmsPickingTaskDetail

	result := db.Where("task_id = ? AND shortage_qty > 0", taskID).
		Order("picking_sequence ASC").
		Find(&details)

	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("获取异常明细失败: taskID=%d", taskID)
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "获取异常明细失败").WithCause(result.Error)
	}

	return details, nil
}

// UpdatePickedTime 更新拣货时间
func (r *wmsPickingTaskDetailRepository) UpdatePickedTime(ctx context.Context, id uint) error {
	db := r.GetDB(ctx)
	now := time.Now()
	result := db.Model(&entity.WmsPickingTaskDetail{}).Where("id = ?", id).Update("picked_time", &now)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("更新明细拣货时间失败: id=%d", id)
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新明细拣货时间失败").WithCause(result.Error)
	}

	if result.RowsAffected == 0 {
		return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "明细不存在")
	}

	return nil
}

// BatchUpdatePickingSequence 批量更新拣货序号
func (r *wmsPickingTaskDetailRepository) BatchUpdatePickingSequence(ctx context.Context, updates map[uint]int) error {
	if len(updates) == 0 {
		return nil
	}

	db := r.GetDB(ctx)
	for id, sequence := range updates {
		if err := db.Model(&entity.WmsPickingTaskDetail{}).Where("id = ?", id).Update("picking_sequence", sequence).Error; err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("批量更新明细拣货序号失败: id=%d, sequence=%d", id, sequence)
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "批量更新明细拣货序号失败").WithCause(err)
		}
	}

	return nil
}
