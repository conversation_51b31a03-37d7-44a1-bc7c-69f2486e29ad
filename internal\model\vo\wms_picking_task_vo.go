package vo

import (
	"time"
)

// WmsPickingTaskVO 拣货任务视图对象
type WmsPickingTaskVO struct {
	ID                uint                        `json:"id"`
	TaskNo            string                      `json:"taskNo"`
	NotificationID    uint                        `json:"notificationId"`
	NotificationNo    *string                     `json:"notificationNo"`    // 扩展字段：出库通知单号
	PickingStrategy   string                      `json:"pickingStrategy"`
	PickingStrategyName *string                   `json:"pickingStrategyName"` // 扩展字段：拣货策略名称
	WaveNo            *string                     `json:"waveNo"`
	Priority          int                         `json:"priority"`
	Status            string                      `json:"status"`
	StatusName        *string                     `json:"statusName"`        // 扩展字段：状态名称
	
	// 分配信息
	AssignedUserID    *uint                       `json:"assignedUserId"`
	AssignedUserName  *string                     `json:"assignedUserName"`  // 扩展字段：分配拣货员姓名
	AssignedTime      *time.Time                  `json:"assignedTime"`
	
	// 执行信息
	StartTime         *time.Time                  `json:"startTime"`
	CompleteTime      *time.Time                  `json:"completeTime"`
	
	// 统计信息
	TotalItems        int                         `json:"totalItems"`
	TotalQty          float64                     `json:"totalQty"`
	PickedItems       int                         `json:"pickedItems"`
	PickedQty         float64                     `json:"pickedQty"`
	
	// 路径优化信息
	EstimatedDistance *float64                    `json:"estimatedDistance"`
	EstimatedTime     *int                        `json:"estimatedTime"`
	ActualDistance    *float64                    `json:"actualDistance"`
	ActualTime        *int                        `json:"actualTime"`
	
	// 进度信息
	CompletionRate    float64                     `json:"completionRate"`    // 完成率
	Efficiency        *float64                    `json:"efficiency"`        // 效率（实际时间/预计时间）
	
	Remark            *string                     `json:"remark"`
	
	// 时间字段
	CreatedAt         time.Time                   `json:"createdAt"`
	UpdatedAt         time.Time                   `json:"updatedAt"`
	CreatedBy         uint                        `json:"createdBy"`
	UpdatedBy         uint                        `json:"updatedBy"`
	CreatedByName     *string                     `json:"createdByName"`     // 扩展字段：创建人姓名
	UpdatedByName     *string                     `json:"updatedByName"`     // 扩展字段：更新人姓名
	
	// 关联数据
	Details           []WmsPickingTaskDetailVO    `json:"details,omitempty"`
	OutboundNotification *WmsOutboundNotificationVO `json:"outboundNotification,omitempty"`
	Shipment          *WmsShipmentVO              `json:"shipment,omitempty"`
}

// WmsPickingTaskDetailVO 拣货任务明细视图对象
type WmsPickingTaskDetailVO struct {
	ID              uint      `json:"id"`
	TaskID          uint      `json:"taskId"`
	LineNo          int       `json:"lineNo"`
	AllocationID    uint      `json:"allocationId"`
	ItemID          uint      `json:"itemId"`
	ItemCode        *string   `json:"itemCode"`        // 扩展字段：物料编码
	ItemName        *string   `json:"itemName"`        // 扩展字段：物料名称
	ItemSpec        *string   `json:"itemSpec"`        // 扩展字段：物料规格
	LocationID      uint      `json:"locationId"`
	LocationCode    *string   `json:"locationCode"`    // 扩展字段：库位编码
	LocationName    *string   `json:"locationName"`    // 扩展字段：库位名称
	
	RequiredQty     float64   `json:"requiredQty"`
	PickedQty       float64   `json:"pickedQty"`
	UnitOfMeasure   string    `json:"unitOfMeasure"`
	
	// 批次信息
	BatchNo         *string   `json:"batchNo"`
	ProductionDate  *string   `json:"productionDate"`
	ExpiryDate      *string   `json:"expiryDate"`
	
	// 执行信息
	PickingSequence int       `json:"pickingSequence"`
	Status          string    `json:"status"`
	StatusName      *string   `json:"statusName"`      // 扩展字段：状态名称
	PickedTime      *time.Time `json:"pickedTime"`
	PickedUserID    *uint     `json:"pickedUserId"`
	PickedUserName  *string   `json:"pickedUserName"`  // 扩展字段：拣货员姓名
	
	// 异常信息
	ShortageQty     float64   `json:"shortageQty"`
	ShortageReason  *string   `json:"shortageReason"`
	
	// 进度信息
	CompletionRate  float64   `json:"completionRate"`  // 完成率
	RemainingQty    float64   `json:"remainingQty"`    // 剩余待拣数量
	
	Remark          *string   `json:"remark"`
	
	// 关联数据
	Allocation      *WmsInventoryAllocationVO `json:"allocation,omitempty"`
}

// WmsPickingTaskListVO 拣货任务列表视图对象
type WmsPickingTaskListVO struct {
	ID                uint      `json:"id"`
	TaskNo            string    `json:"taskNo"`
	NotificationNo    *string   `json:"notificationNo"`
	PickingStrategyName *string `json:"pickingStrategyName"`
	WaveNo            *string   `json:"waveNo"`
	Priority          int       `json:"priority"`
	Status            string    `json:"status"`
	StatusName        *string   `json:"statusName"`
	AssignedUserName  *string   `json:"assignedUserName"`
	TotalItems        int       `json:"totalItems"`
	TotalQty          float64   `json:"totalQty"`
	CompletionRate    float64   `json:"completionRate"`
	EstimatedTime     *int      `json:"estimatedTime"`
	ActualTime        *int      `json:"actualTime"`
	CreatedAt         time.Time `json:"createdAt"`
	CreatedByName     *string   `json:"createdByName"`
}

// WmsPickingTaskStatsVO 拣货任务统计视图对象
type WmsPickingTaskStatsVO struct {
	// 基础统计
	TotalCount        int     `json:"totalCount"`        // 总任务数
	TotalQty          float64 `json:"totalQty"`          // 总数量
	TotalItems        int     `json:"totalItems"`        // 总物料种类数
	
	// 状态统计
	PendingCount      int     `json:"pendingCount"`      // 待分配数量
	AssignedCount     int     `json:"assignedCount"`     // 已分配数量
	InProgressCount   int     `json:"inProgressCount"`   // 进行中数量
	CompletedCount    int     `json:"completedCount"`    // 已完成数量
	CancelledCount    int     `json:"cancelledCount"`    // 已取消数量
	
	// 效率统计
	AvgPickingTime    float64 `json:"avgPickingTime"`    // 平均拣货时间（分钟）
	AvgEfficiency     float64 `json:"avgEfficiency"`     // 平均效率
	AvgAccuracy       float64 `json:"avgAccuracy"`       // 平均准确率
	
	// 策略统计
	ByOrderCount      int     `json:"byOrderCount"`      // 按单拣货数量
	BatchCount        int     `json:"batchCount"`        // 批量拣货数量
	WaveCount         int     `json:"waveCount"`         // 波次拣货数量
	ZoneCount         int     `json:"zoneCount"`         // 分区拣货数量
	
	// 趋势数据
	TrendData         []WmsPickingTaskTrendVO `json:"trendData,omitempty"`
}

// WmsPickingTaskTrendVO 拣货任务趋势数据
type WmsPickingTaskTrendVO struct {
	Date           string  `json:"date"`           // 日期
	Count          int     `json:"count"`          // 任务数量
	Qty            float64 `json:"qty"`            // 总数量
	AvgTime        float64 `json:"avgTime"`        // 平均时间
	Efficiency     float64 `json:"efficiency"`     // 效率
}

// WmsPickingWaveVO 拣货波次视图对象
type WmsPickingWaveVO struct {
	WaveNo            string                    `json:"waveNo"`
	Status            string                    `json:"status"`
	StatusName        *string                   `json:"statusName"`
	PickingStrategy   string                    `json:"pickingStrategy"`
	PickingStrategyName *string                 `json:"pickingStrategyName"`
	Priority          int                       `json:"priority"`
	AssignedUserID    *uint                     `json:"assignedUserId"`
	AssignedUserName  *string                   `json:"assignedUserName"`
	TotalTasks        int                       `json:"totalTasks"`
	CompletedTasks    int                       `json:"completedTasks"`
	TotalItems        int                       `json:"totalItems"`
	TotalQty          float64                   `json:"totalQty"`
	CompletionRate    float64                   `json:"completionRate"`
	EstimatedTime     *int                      `json:"estimatedTime"`
	ActualTime        *int                      `json:"actualTime"`
	CreatedAt         time.Time                 `json:"createdAt"`
	ReleasedAt        *time.Time                `json:"releasedAt"`
	CompletedAt       *time.Time                `json:"completedAt"`
	Remark            *string                   `json:"remark"`
	
	// 关联数据
	Tasks             []WmsPickingTaskListVO    `json:"tasks,omitempty"`
}

// Mobile端专用VO

// WmsMobilePickingTaskVO 移动端拣货任务视图对象
type WmsMobilePickingTaskVO struct {
	ID              uint                            `json:"id"`
	TaskNo          string                          `json:"taskNo"`
	NotificationNo  *string                         `json:"notificationNo"`
	Priority        int                             `json:"priority"`
	Status          string                          `json:"status"`
	StatusName      *string                         `json:"statusName"`
	TotalItems      int                             `json:"totalItems"`
	TotalQty        float64                         `json:"totalQty"`
	PickedItems     int                             `json:"pickedItems"`
	PickedQty       float64                         `json:"pickedQty"`
	CompletionRate  float64                         `json:"completionRate"`
	EstimatedTime   *int                            `json:"estimatedTime"`
	StartTime       *time.Time                      `json:"startTime"`
	
	// 当前拣货项
	CurrentItem     *WmsMobilePickingTaskDetailVO   `json:"currentItem,omitempty"`
	
	// 拣货明细列表
	Details         []WmsMobilePickingTaskDetailVO  `json:"details,omitempty"`
}

// WmsMobilePickingTaskDetailVO 移动端拣货任务明细视图对象
type WmsMobilePickingTaskDetailVO struct {
	ID              uint    `json:"id"`
	LineNo          int     `json:"lineNo"`
	ItemCode        *string `json:"itemCode"`
	ItemName        *string `json:"itemName"`
	ItemSpec        *string `json:"itemSpec"`
	LocationCode    *string `json:"locationCode"`
	LocationName    *string `json:"locationName"`
	RequiredQty     float64 `json:"requiredQty"`
	PickedQty       float64 `json:"pickedQty"`
	RemainingQty    float64 `json:"remainingQty"`
	UnitOfMeasure   string  `json:"unitOfMeasure"`
	BatchNo         *string `json:"batchNo"`
	PickingSequence int     `json:"pickingSequence"`
	Status          string  `json:"status"`
	StatusName      *string `json:"statusName"`
	CompletionRate  float64 `json:"completionRate"`
}
