import { fileURLToPath, URL } from 'node:url' // Import path resolution helpers
import path from 'path' // Import path module

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: { // Add resolve section
    alias: { // Add alias configuration
      '@': path.resolve(__dirname, './src') // Map '@' to the src directory
    },
    // --- 添加 dedupe 配置 ---
    dedupe: [
      '@codemirror/state' // 强制去重 @codemirror/state
      // 可以根据需要添加其他 CodeMirror 包，但 state 通常是关键
      // '@codemirror/view',
      // '@codemirror/commands',
      // ...
    ]
    // --- dedupe 结束 ---
  },
  // --- Add server proxy configuration ---
  server: {
    host: '0.0.0.0', // Optional: Listen on all network interfaces
    port: 5173,      // Optional: Specify dev server port (default is 5173)
    proxy: {
      // Proxy /api/v1 requests to your Go backend
      '/api/v1': {
        target: 'http://localhost:8080', // IMPORTANT: Your Go backend address
        changeOrigin: true, // Needed for virtual hosted sites
        // Optional: Rewrite path if backend expects different prefix
        // rewrite: (path) => path.replace(/^\/api\/v1/, '')
      },
       // --- 新增：代理后端提供的静态资源 (如上传的头像) ---
       '/static': {
        target: 'http://localhost:8080', // 同样指向后端服务器
        changeOrigin: true,
        // 通常不需要 rewrite，因为后端期望的路径就是 /static/...
      }
      // --- 代理结束 ---
     // You can add other proxy rules here if needed
      // '/other-api': { ... }
    }
  }
})
