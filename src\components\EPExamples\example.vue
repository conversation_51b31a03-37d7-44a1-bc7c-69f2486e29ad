<template>
  <div class="ep-examples-page">
    <h2>Element Plus 常用组件示例</h2>

    <!-- Date Picker Section -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>el-date-picker 日期选择器</span>
        </div>
      </template>
      <div class="example-group">
        <h4>选择单日</h4>
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
        <p>选择的日期: {{ selectedDate || '无' }}</p>
      </div>
       <el-divider />
       <div class="example-group">
         <h4>选择日期范围</h4>
         <el-date-picker
           v-model="dateRange"
           type="daterange"
           range-separator="至"
           start-placeholder="开始日期"
           end-placeholder="结束日期"
           format="YYYY/MM/DD"
           value-format="YYYY-MM-DD"
         />
         <p>选择的范围: {{ dateRange ? dateRange.join(' 到 ') : '无' }}</p>
       </div>
       <el-divider />
       <div class="example-group">
         <h4>选择月份</h4>
         <el-date-picker
           v-model="selectedMonth"
           type="month"
           placeholder="选择月份"
           format="YYYY-MM"
           value-format="YYYY-MM"
         />
         <p>选择的月份: {{ selectedMonth || '无' }}</p>
       </div>
        <el-divider />
       <div class="example-group">
         <h4>选择日期时间</h4>
          <el-date-picker
            v-model="selectedDateTime"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
         <p>选择的日期时间: {{ selectedDateTime || '无' }}</p>
       </div>
    </el-card>

    <!-- Message Section -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>ElMessage 消息提示</span>
        </div>
      </template>
      <div class="button-group">
        <el-button @click="showMessage('success')">成功 (Success)</el-button>
        <el-button @click="showMessage('warning')">警告 (Warning)</el-button>
        <el-button @click="showMessage('info')">消息 (Info)</el-button>
        <el-button @click="showMessage('error')">错误 (Error)</el-button>
      </div>
      <p>点击按钮显示不同类型的全局消息提示。</p>
    </el-card>

    <!-- Notification Section -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>ElNotification 通知</span>
        </div>
      </template>
       <div class="button-group">
        <el-button @click="showNotification('success')">成功通知</el-button>
        <el-button @click="showNotification('warning')">警告通知</el-button>
        <el-button @click="showNotification('info')">信息通知</el-button>
        <el-button @click="showNotification('error')">错误通知</el-button>
      </div>
      <div class="button-group" style="margin-top: 10px;">
         <el-button @click="showNotificationWithPosition('top-right')">右上角</el-button>
         <el-button @click="showNotificationWithPosition('top-left')">左上角</el-button>
         <el-button @click="showNotificationWithPosition('bottom-right')">右下角</el-button>
         <el-button @click="showNotificationWithPosition('bottom-left')">左下角</el-button>
      </div>
      <p>点击按钮显示不同类型和位置的通知框。</p>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  ElDatePicker,
  ElCard,
  ElButton,
  ElMessage,
  ElNotification,
  ElDivider
} from 'element-plus';
import type { MessageType } from 'element-plus';

// Date Picker Data
const selectedDate = ref<string | null>(null);
const dateRange = ref<[string, string] | null>(null);
const selectedMonth = ref<string | null>(null);
const selectedDateTime = ref<string | null>(null);

// Message Handler
const showMessage = (type: MessageType) => {
  ElMessage({
    message: `这是一条 ${type} 类型的消息提示。`, // Chinese message
    type: type,
    showClose: true, // Optionally show close button
  });
};

// Notification Handler
const showNotification = (type: 'success' | 'warning' | 'info' | 'error') => {
  ElNotification({
    title: getNotificationTitle(type), // Get title based on type
    message: `这是一条 ${type} 类型的通知消息，通常用于更重要的提醒。`, // Chinese message
    type: type,
    duration: 3000, // Duration in milliseconds (3 seconds)
  });
};

const showNotificationWithPosition = (position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left') => {
  ElNotification({
    title: `位于 ${position} 的通知`, // Chinese title
    message: `此通知显示在屏幕的 ${position} 位置。`, // Chinese message
    type: 'info',
    position: position,
  });
};

// Helper to get notification title
const getNotificationTitle = (type: string): string => {
  switch (type) {
    case 'success': return '成功';
    case 'warning': return '警告';
    case 'info': return '提示';
    case 'error': return '错误';
    default: return '通知';
  }
}

</script>

<style scoped>
.ep-examples-page {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.example-group {
  margin-bottom: 15px;
}

.example-group h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--el-text-color-regular);
}

.button-group .el-button {
  margin-right: 10px;
  margin-bottom: 10px; /* Add bottom margin for wrapping */
}

p {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 10px;
}

.el-divider {
    margin: 15px 0;
}
</style> 