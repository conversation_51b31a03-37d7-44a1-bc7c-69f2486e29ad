<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card shadow="never" class="search-wrapper">
      <el-form ref="searchFormRef" :inline="true" :model="searchData">
        <el-form-item prop="taskNo" label="任务单号">
          <el-input v-model="searchData.taskNo" placeholder="请输入任务单号" clearable />
        </el-form-item>
        <el-form-item prop="receivingRecordNo" label="收货单号">
          <el-input v-model="searchData.receivingRecordNo" placeholder="请输入收货单号" clearable />
        </el-form-item>
        <el-form-item prop="status" label="状态">
          <el-select v-model="searchData.status" placeholder="请选择状态" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="assignedToUserId" label="分配给">
           <el-select v-model="searchData.assignedToUserId" placeholder="请选择分配人" clearable filterable>
            <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
          <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格 -->
    <el-card shadow="never">
      <el-table v-loading="loading" :data="list" border>
        <el-table-column prop="taskNo" label="任务单号" align="center" width="180" />
        <el-table-column prop="receivingRecordNo" label="关联收货单号" align="center" width="180" />
        <el-table-column prop="status" label="状态" align="center">
           <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ formatStatus(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" align="center" />
        <el-table-column prop="assignedToUserName" label="分配给" align="center" />
        <el-table-column prop="createdAt" label="创建时间" align="center" width="180" />
        <el-table-column prop="completedAt" label="完成时间" align="center" width="180" />

        <el-table-column fixed="right" label="操作" width="280" align="center">
          <template #default="{ row }">
            <el-button type="primary" text bg size="small" @click="openDialog('view', row.id)">查看</el-button>
            <el-button v-if="row.status === 'PENDING'" type="success" text bg size="small" @click="handleOpenAssignDialog(row.id)">分配</el-button>
            <el-button v-if="['ASSIGNED', 'IN_PROGRESS'].includes(row.status)" type="warning" text bg size="small" @click="openDialog('execute', row.id)">执行</el-button>
            <el-button v-if="['ASSIGNED', 'IN_PROGRESS'].includes(row.status)" type="info" text bg size="small" @click="handleMobileEntry(row.id)">
              <el-icon><Cellphone /></el-icon>
              移动端
            </el-button>
            <el-button v-if="row.status === 'PENDING'" type="danger" text bg size="small" @click="handleDelete(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
       <!-- 分页 -->
      <el-pagination
        :total="pagination.total"
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        @size-change="fetchList"
        @current-change="fetchList"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
      />
    </el-card>

    <!-- 详情/执行 对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="70%" @close="dialogVisible = false">
      <el-descriptions :column="3" border class="description-form">
        <el-descriptions-item label="任务单号">{{ formData.taskNo }}</el-descriptions-item>
        <el-descriptions-item label="关联收货单号">{{ formData.receivingRecordNo }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(formData.status)">{{ formatStatus(formData.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="优先级">{{ formData.priority }}</el-descriptions-item>
        <el-descriptions-item label="分配给">{{ formData.assignedToUserName }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ formData.remark }}</el-descriptions-item>
      </el-descriptions>

      <el-divider />
      <h4>任务明细</h4>
      <el-table :data="formData.details" border class="detail-table">
          <el-table-column prop="lineNo" label="行号" width="60" align="center" />
          <el-table-column prop="itemSku" label="物料SKU" width="150" />
          <el-table-column prop="itemName" label="物料名称" width="200" />
          <el-table-column prop="putawayQuantity" label="需上架数量" align="center" />
          <el-table-column prop="unitOfMeasure" label="单位" align="center" />
          <el-table-column prop="sourceLocationCode" label="源库位" align="center" />
          <el-table-column prop="suggestedLocationCode" label="建议库位" align="center" />
          <el-table-column prop="status" label="行状态" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">{{ formatStatus(row.status) }}</el-tag>
            </template>
          </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button v-if="!isReadonly" type="primary" @click="handleCompleteTask()">确认完成</el-button>
      </template>
    </el-dialog>

    <!-- 分配任务对话框 -->
    <el-dialog v-model="assignDialogVisible" title="分配任务" width="30%">
      <el-form>
        <el-form-item label="分配给">
          <el-select v-model="assignUserId" placeholder="请选择操作员" filterable>
            <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign">确定</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useWmsPutawayTaskStore } from '@/store/wms/putawayTask'
import { Search, Refresh, Cellphone } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getUserList } from '@/api/system/systemUser'

const router = useRouter()
const store = useWmsPutawayTaskStore()
const {
  list,
  total,
  loading,
  dialogVisible,
  dialogTitle,
  formData,
  searchData,
  pagination,
  isReadonly,
  userOptions
} = storeToRefs(store)

const { fetchList, handleSearch, resetSearch, openDialog, handleDelete, handleAssignTask, handleCompleteTask, fetchUserOptions } = store

const searchFormRef = ref<FormInstance>()
const assignDialogVisible = ref(false)
const assignUserId = ref<number | null>(null)

const statusOptions = ref([
  { value: 'PENDING', label: '待处理' },
  { value: 'ASSIGNED', label: '已分配' },
  { value: 'IN_PROGRESS', label: '进行中' },
  { value: 'COMPLETED', label: '已完成' },
  { value: 'CANCELLED', label: '已取消' },
  { value: 'EXCEPTION', label: '异常' }
])

// 打开分配弹窗
const handleOpenAssignDialog = (id: number) => {
  store.currentId = id
  assignUserId.value = null
  assignDialogVisible.value = true
}

// 确认分配
const confirmAssign = async () => {
  if (assignUserId.value) {
    await handleAssignTask(assignUserId.value)
    assignDialogVisible.value = false
  } else {
    ElMessage.warning('请选择分配人')
  }
}

const getStatusTagType = (status?: string) => {
  switch (status) {
    case 'PENDING':
      return 'info'
    case 'ASSIGNED':
      return ''
    case 'IN_PROGRESS':
      return 'primary'
    case 'COMPLETED':
      return 'success'
    case 'CANCELLED':
      return 'warning'
    case 'EXCEPTION':
      return 'danger'
    default:
      return 'info'
  }
}

const formatStatus = (status?: string) => {
  if (!status) return '未知'
  const option = statusOptions.value.find((opt) => opt.value === status)
  return option ? option.label : '未知'
}

// 移动端入口
const handleMobileEntry = (taskId: number) => {
  // 跳转到移动端上架页面
  router.push({
    name: 'WmsPutawayTaskMobile',
    params: { id: taskId }
  })
}

onMounted(() => {
  fetchList()
  // 在这里可以调用 fetchUserOptions，如果需要预加载用户列表
  // fetchUserOptions();
})
</script>

<style scoped>
.search-wrapper {
  margin-bottom: 20px;
}
.pagination {
  margin-top: 20px;
  justify-content: flex-end;
}
.description-form {
  margin-bottom: 20px;
}
.detail-table {
    margin-top: 10px;
}
</style> 