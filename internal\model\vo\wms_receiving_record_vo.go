package vo

import (
	"time"
)

// WmsReceivingRecordVO 收货记录详细视图对象
type WmsReceivingRecordVO struct {
	ID                   uint                         `json:"id"`
	CreatedAt            time.Time                    `json:"createdAt"`
	UpdatedAt            time.Time                    `json:"updatedAt"`
	CreatedBy            uint                         `json:"createdBy"`
	UpdatedBy            uint                         `json:"updatedBy"`
	TenantID             uint                         `json:"tenantId"`
	AccountBookID        uint                         `json:"accountBookId"`
	ReceivingNo          string                       `json:"receivingNo"`
	NotificationID       *uint                        `json:"notificationId,omitempty"`
	WarehouseID          uint                         `json:"warehouseId"`
	WarehouseName        string                       `json:"warehouseName"`
	ClientID             uint                         `json:"clientId"`
	SupplierShipper      *string                      `json:"supplierShipper,omitempty"`
	ActualArrivalDateStr *string                      `json:"actualArrivalDate,omitempty"`
	ReceivingType        string                       `json:"receivingType"` // 收货类型 ASN/BLIND
	Status               string                       `json:"status"`
	SupplementDeadline   *time.Time                   `json:"supplementDeadline,omitempty"`
	Remark               *string                      `json:"remark,omitempty"`
	SourceDocNo          *string                      `json:"sourceDocNo,omitempty"` // 来源单据号
	Details              []WmsReceivingRecordDetailVO `json:"details,omitempty"`
}

// WmsReceivingRecordSimpleVO 收货记录简单视图对象（用于列表显示）
type WmsReceivingRecordSimpleVO struct {
	ID                   uint      `json:"id"`
	ReceivingNo          string    `json:"receivingNo"`
	NotificationID       *uint     `json:"notificationId,omitempty"`
	ClientID             uint      `json:"clientId"`
	SupplierShipper      *string   `json:"supplierShipper,omitempty"`
	ActualArrivalDateStr *string   `json:"actualArrivalDate,omitempty"`
	ReceivingType        string    `json:"receivingType"` // 收货类型 ASN/BLIND
	WarehouseName        string    `json:"warehouseName"` // 仓库名称
	Status               string    `json:"status"`
	CreatedAt            time.Time `json:"createdAt"`
}

// WmsReceivingRecordDetailVO 收货记录明细视图对象
type WmsReceivingRecordDetailVO struct {
	ID                   uint       `json:"id"`
	CreatedAt            time.Time  `json:"createdAt"`
	UpdatedAt            time.Time  `json:"updatedAt"`
	TenantID             uint       `json:"tenantId"`
	AccountBookID        uint       `json:"accountBookId"`
	ReceivingRecordID    uint       `json:"receivingRecordId"`
	NotificationDetailID *uint      `json:"notificationDetailId,omitempty"`
	ItemID               uint       `json:"itemId"`
	ItemSku              string     `json:"itemSku"`                 // 物料 SKU
	ItemName             string     `json:"itemName"`                // 物料名称
	Specification        *string    `json:"specification,omitempty"` // 规格型号
	ExpectedQuantity     *float64   `json:"expectedQuantity,omitempty"`
	UnitOfMeasure        string     `json:"unitOfMeasure"`
	BatchNo              *string    `json:"batchNo,omitempty"`
	InternalBatchNo      *string    `json:"internalBatchNo,omitempty"`
	Remark               *string    `json:"remark,omitempty"`
	LineNo               int        `json:"lineNo"`                         // 行号
	ReceivedQuantity     float64    `json:"receivedQuantity,omitempty"`     // 实际收货数量
	ReceivedAtLocationID uint       `json:"receivedAtLocationId,omitempty"` // 收货库位ID (e.g., Staging Area / Dock Door)
	InspectionStatus     string     `json:"inspectionStatus,omitempty"`     // 检验状态
	InspectorID          *uint      `json:"inspectorId,omitempty"`          // 检验员ID (关联用户表)
	InspectionNotes      *string    `json:"inspectionNotes,omitempty"`      // 检验备注
	InspectionAt         *time.Time `json:"inspectionAt,omitempty"`
	QuantityDifference   *float64   `json:"quantityDifference,omitempty"` // 差异数量 (计算得到)
	DiscrepancyReason    *string    `json:"discrepancyReason,omitempty"`  // 差异原因
	PackageQty           *float64   `json:"packageQty,omitempty"`
	PackageUnit          *string    `json:"packageUnit,omitempty"`
	ProductionDateStr    *string    `json:"productionDate,omitempty"`
	ExpiryDateStr        *string    `json:"expiryDate,omitempty"`
	LineStatus           string     `json:"lineStatus,omitempty"` // 收货单明细行状态 (使用数据库定义的 ENUM)                                            // 过期日期 (实收)
}

type WmsReceivingRecordPageResult = PageResult[WmsReceivingRecordSimpleVO]
