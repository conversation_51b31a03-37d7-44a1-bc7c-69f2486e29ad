# WMS出库流程模块 API文档

## 概述

WMS出库流程模块提供完整的出库业务流程管理，包括出库通知单、拣货任务、库存分配和发运管理等功能。

## 基础信息

- **基础URL**: `/api/v1/wms`
- **认证方式**: <PERSON><PERSON>
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 5
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 1. 出库通知单管理

### 1.1 创建出库通知单

**接口地址**: `POST /outbound-notifications`

**请求参数**:
```json
{
  "clientId": 1,
  "clientOrderNo": "CO20240101001",
  "warehouseId": 1,
  "requiredShipDate": "2024-01-15",
  "priority": 5,
  "consigneeName": "张三",
  "consigneePhone": "13800138000",
  "consigneeAddress": "北京市朝阳区xxx街道xxx号",
  "carrierId": 1,
  "shippingMethod": "快递",
  "remark": "备注信息",
  "details": [
    {
      "lineNo": 1,
      "itemId": 1,
      "requiredQty": 100.0,
      "unitOfMeasure": "PCS",
      "requiredBatchNo": "BATCH001",
      "requiredProductionDate": "2024-01-01",
      "requiredExpiryDate": "2024-12-31",
      "remark": "明细备注"
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "notificationNo": "ON20240101001",
    "status": "PENDING"
  }
}
```

### 1.2 更新出库通知单

**接口地址**: `PUT /outbound-notifications/{id}`

**路径参数**:
- `id`: 出库通知单ID

**请求参数**: 同创建接口，需包含ID字段

### 1.3 删除出库通知单

**接口地址**: `DELETE /outbound-notifications/{id}`

**路径参数**:
- `id`: 出库通知单ID

### 1.4 获取出库通知单详情

**接口地址**: `GET /outbound-notifications/{id}`

**路径参数**:
- `id`: 出库通知单ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "notificationNo": "ON20240101001",
    "clientId": 1,
    "clientOrderNo": "CO20240101001",
    "warehouseId": 1,
    "status": "PENDING",
    "priority": 5,
    "consigneeName": "张三",
    "consigneePhone": "13800138000",
    "consigneeAddress": "北京市朝阳区xxx街道xxx号",
    "carrierId": 1,
    "shippingMethod": "快递",
    "requiredShipDate": "2024-01-15",
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-01T10:00:00Z",
    "details": [
      {
        "id": 1,
        "lineNo": 1,
        "itemId": 1,
        "requiredQty": 100.0,
        "allocatedQty": 80.0,
        "pickedQty": 0.0,
        "unitOfMeasure": "PCS",
        "requiredBatchNo": "BATCH001",
        "item": {
          "id": 1,
          "itemCode": "ITEM001",
          "itemName": "测试物料"
        }
      }
    ]
  }
}
```

### 1.5 分页查询出库通知单

**接口地址**: `GET /outbound-notifications`

**查询参数**:
- `pageNum`: 页码（默认1）
- `pageSize`: 每页大小（默认20）
- `notificationNo`: 通知单号（模糊查询）
- `clientId`: 客户ID
- `clientOrderNo`: 客户订单号
- `warehouseId`: 仓库ID
- `status`: 状态
- `priority`: 优先级
- `consigneeName`: 收货人姓名
- `carrierId`: 承运商ID
- `requiredShipDate`: 要求发货日期
- `createdAtStart`: 创建时间开始
- `createdAtEnd`: 创建时间结束

### 1.6 批量操作

#### 批量创建
**接口地址**: `POST /outbound-notifications/batch-create`

#### 批量审核
**接口地址**: `POST /outbound-notifications/batch-approve`

#### 批量取消
**接口地址**: `POST /outbound-notifications/batch-cancel`

### 1.7 状态管理

#### 审核通知单
**接口地址**: `POST /outbound-notifications/{id}/approve`

#### 取消通知单
**接口地址**: `POST /outbound-notifications/{id}/cancel`

### 1.8 库存分配

#### 分配库存
**接口地址**: `POST /outbound-notifications/{id}/allocate-inventory`

#### 批量分配库存
**接口地址**: `POST /outbound-notifications/batch-allocate-inventory`

#### 获取分配状态
**接口地址**: `GET /outbound-notifications/{id}/allocation-status`

### 1.9 拣货任务生成

#### 生成拣货任务
**接口地址**: `POST /outbound-notifications/{id}/generate-picking-task`

#### 批量生成拣货任务
**接口地址**: `POST /outbound-notifications/batch-generate-picking-task`

### 1.10 业务查询

#### 根据通知单号查询
**接口地址**: `GET /outbound-notifications/by-notification-no/{notificationNo}`

#### 根据客户订单号查询
**接口地址**: `GET /outbound-notifications/by-client-order-no/{clientOrderNo}`

#### 获取待分配通知单
**接口地址**: `GET /outbound-notifications/pending-allocation`

#### 获取待拣货通知单
**接口地址**: `GET /outbound-notifications/ready-for-picking`

#### 获取逾期发运通知单
**接口地址**: `GET /outbound-notifications/overdue-shipments`

### 1.11 统计分析

#### 获取统计数据
**接口地址**: `GET /outbound-notifications/stats`

**查询参数**:
- `dateStart`: 开始日期
- `dateEnd`: 结束日期
- `groupBy`: 分组方式（day/week/month/status/client/warehouse）
- `clientId`: 客户ID
- `warehouseId`: 仓库ID

### 1.12 导入导出

#### Excel导入
**接口地址**: `POST /outbound-notifications/import-excel`

#### Excel导出
**接口地址**: `GET /outbound-notifications/export-excel`

## 2. 拣货任务管理

### 2.1 创建拣货任务

**接口地址**: `POST /picking-tasks`

**请求参数**:
```json
{
  "notificationId": 1,
  "pickingStrategy": "BY_ORDER",
  "waveNo": "WAVE001",
  "priority": 5,
  "assignedUserId": 1,
  "remark": "备注"
}
```

### 2.2 更新拣货任务

**接口地址**: `PUT /picking-tasks/{id}`

### 2.3 删除拣货任务

**接口地址**: `DELETE /picking-tasks/{id}`

### 2.4 获取拣货任务详情

**接口地址**: `GET /picking-tasks/{id}`

### 2.5 分页查询拣货任务

**接口地址**: `GET /picking-tasks`

**查询参数**:
- `pageNum`: 页码
- `pageSize`: 每页大小
- `taskNo`: 任务号
- `notificationId`: 出库通知单ID
- `pickingStrategy`: 拣货策略
- `waveNo`: 波次号
- `status`: 状态
- `priority`: 优先级
- `assignedUserId`: 分配拣货员ID
- `createdAtStart`: 创建时间开始
- `createdAtEnd`: 创建时间结束

### 2.6 任务管理

#### 分配任务
**接口地址**: `POST /picking-tasks/{id}/assign`

#### 批量分配任务
**接口地址**: `POST /picking-tasks/batch-assign`

#### 开始任务
**接口地址**: `POST /picking-tasks/{id}/start`

#### 完成任务
**接口地址**: `POST /picking-tasks/{id}/complete`

#### 取消任务
**接口地址**: `POST /picking-tasks/{id}/cancel`

### 2.7 拣货执行

#### 执行拣货
**接口地址**: `POST /picking-tasks/execute-picking`

#### 批量执行拣货
**接口地址**: `POST /picking-tasks/batch-execute-picking`

#### 处理异常
**接口地址**: `POST /picking-tasks/handle-exception`

### 2.8 业务查询

#### 根据任务号查询
**接口地址**: `GET /picking-tasks/by-task-no/{taskNo}`

#### 根据通知单查询
**接口地址**: `GET /picking-tasks/by-notification/{notificationID}`

#### 获取待分配任务
**接口地址**: `GET /picking-tasks/pending-assignment`

## 3. 库存分配管理

### 3.1 创建库存分配

**接口地址**: `POST /inventory-allocations`

**请求参数**:
```json
{
  "outboundDetailId": 1,
  "inventoryId": 1,
  "allocatedQty": 50.0,
  "allocationStrategy": "FIFO",
  "allocationReason": "正常分配",
  "remark": "备注"
}
```

### 3.2 更新库存分配

**接口地址**: `PUT /inventory-allocations/{id}`

### 3.3 删除库存分配

**接口地址**: `DELETE /inventory-allocations/{id}`

### 3.4 获取库存分配详情

**接口地址**: `GET /inventory-allocations/{id}`

### 3.5 分页查询库存分配

**接口地址**: `GET /inventory-allocations`

**查询参数**:
- `pageNum`: 页码
- `pageSize`: 每页大小
- `outboundDetailId`: 出库明细ID
- `inventoryId`: 库存ID
- `status`: 状态
- `allocationStrategy`: 分配策略
- `createdAtStart`: 创建时间开始
- `createdAtEnd`: 创建时间结束

### 3.6 自动分配

#### 自动分配库存
**接口地址**: `POST /inventory-allocations/auto-allocate`

**请求参数**:
```json
{
  "outboundDetailId": 1,
  "allocationStrategy": "FIFO",
  "forceAllocate": false,
  "maxAllocations": 5
}
```

#### 批量自动分配
**接口地址**: `POST /inventory-allocations/batch-auto-allocate`

### 3.7 拣货确认和释放

#### 拣货确认
**接口地址**: `POST /inventory-allocations/{id}/pick-confirm`

#### 释放分配
**接口地址**: `POST /inventory-allocations/{id}/release`

### 3.8 库存可用性检查

#### 检查库存可用性
**接口地址**: `POST /inventory-allocations/check-availability`

**请求参数**:
```json
{
  "itemId": 1,
  "requiredQty": 100.0,
  "requiredBatchNo": "BATCH001",
  "requiredProductionDate": "2024-01-01",
  "requiredExpiryDate": "2024-12-31",
  "warehouseId": 1
}
```

#### 批量检查库存可用性
**接口地址**: `POST /inventory-allocations/batch-check-availability`

### 3.9 业务查询

#### 根据出库明细ID查询
**接口地址**: `GET /inventory-allocations/by-outbound-detail/{outboundDetailID}`

#### 根据库存ID查询
**接口地址**: `GET /inventory-allocations/by-inventory/{inventoryID}`

#### 获取分配汇总
**接口地址**: `GET /inventory-allocations/summary/{outboundDetailID}`

## 4. 发运单管理

### 4.1 创建发运单

**接口地址**: `POST /shipments`

**请求参数**:
```json
{
  "pickingTaskId": 1,
  "carrierId": 1,
  "shippingMethod": "快递",
  "estimatedShipDate": "2024-01-15",
  "consigneeName": "张三",
  "consigneePhone": "13800138000",
  "consigneeAddress": "北京市朝阳区xxx街道xxx号",
  "packageCount": 2,
  "totalWeight": 10.5,
  "totalVolume": 0.5,
  "remark": "备注"
}
```

### 4.2 更新发运单

**接口地址**: `PUT /shipments/{id}`

### 4.3 删除发运单

**接口地址**: `DELETE /shipments/{id}`

### 4.4 获取发运单详情

**接口地址**: `GET /shipments/{id}`

### 4.5 分页查询发运单

**接口地址**: `GET /shipments`

**查询参数**:
- `pageNum`: 页码
- `pageSize`: 每页大小
- `shipmentNo`: 发运单号
- `pickingTaskId`: 拣货任务ID
- `notificationId`: 出库通知单ID
- `carrierId`: 承运商ID
- `trackingNo`: 运单号
- `shippingMethod`: 运输方式
- `status`: 状态
- `consigneeName`: 收货人姓名
- `shipmentDateStart`: 发运日期开始
- `shipmentDateEnd`: 发运日期结束
- `createdAtStart`: 创建时间开始
- `createdAtEnd`: 创建时间结束

### 4.6 发运流程

#### 打包发运单
**接口地址**: `POST /shipments/{id}/pack`

#### 发运确认
**接口地址**: `POST /shipments/{id}/ship`

#### 更新跟踪状态
**接口地址**: `POST /shipments/{id}/tracking`

#### 签收确认
**接口地址**: `POST /shipments/{id}/delivery`

### 4.7 运费管理和标签打印

#### 计算运费
**接口地址**: `POST /shipments/calculate-cost`

#### 打印发运标签
**接口地址**: `POST /shipments/print-label`

### 4.8 业务查询

#### 根据发运单号查询
**接口地址**: `GET /shipments/by-shipment-no/{shipmentNo}`

#### 根据运单号查询
**接口地址**: `GET /shipments/by-tracking-no/{trackingNo}`

#### 根据拣货任务ID查询
**接口地址**: `GET /shipments/by-picking-task/{pickingTaskID}`

#### 获取待打包发运单
**接口地址**: `GET /shipments/pending-packing`

#### 获取待发运发运单
**接口地址**: `GET /shipments/ready-to-ship`

## 5. 状态码说明

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 业务逻辑错误 |
| 500 | 服务器内部错误 |

### 业务状态码

#### 出库通知单状态
- `PENDING`: 待审核
- `APPROVED`: 已审核
- `ALLOCATED`: 已分配
- `PICKING`: 拣货中
- `PICKED`: 已拣货
- `SHIPPED`: 已发运
- `DELIVERED`: 已送达
- `CANCELLED`: 已取消

#### 拣货任务状态
- `PENDING`: 待分配
- `ASSIGNED`: 已分配
- `IN_PROGRESS`: 进行中
- `COMPLETED`: 已完成
- `CANCELLED`: 已取消

#### 库存分配状态
- `ALLOCATED`: 已分配
- `PICKED`: 已拣货
- `RELEASED`: 已释放

#### 发运单状态
- `PREPARING`: 准备中
- `PACKED`: 已打包
- `READY`: 待发运
- `SHIPPED`: 已发运
- `IN_TRANSIT`: 运输中
- `DELIVERED`: 已送达
- `RETURNED`: 已退回

## 6. 错误处理

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 10001 | 参数验证失败 | 请求参数格式或值不正确 |
| 10002 | 资源不存在 | 请求的资源ID不存在 |
| 10003 | 权限不足 | 用户没有执行该操作的权限 |
| 20001 | 出库通知单状态错误 | 当前状态不允许执行该操作 |
| 20002 | 库存不足 | 可用库存数量不足 |
| 20003 | 拣货任务已分配 | 任务已分配给其他用户 |
| 20004 | 发运单已发运 | 发运单状态不允许修改 |

### 错误响应示例

```json
{
  "code": 20002,
  "message": "库存不足，当前可用库存：50，需求数量：100",
  "data": {
    "availableQty": 50,
    "requiredQty": 100,
    "itemId": 1
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

## 7. 数据模型

### 出库通知单模型

```json
{
  "id": 1,
  "notificationNo": "ON20240101001",
  "clientId": 1,
  "clientOrderNo": "CO20240101001",
  "warehouseId": 1,
  "status": "PENDING",
  "priority": 5,
  "consigneeName": "张三",
  "consigneePhone": "13800138000",
  "consigneeAddress": "北京市朝阳区xxx街道xxx号",
  "carrierId": 1,
  "shippingMethod": "快递",
  "requiredShipDate": "2024-01-15",
  "totalQty": 100.0,
  "allocatedQty": 80.0,
  "pickedQty": 0.0,
  "remark": "备注",
  "createdAt": "2024-01-01T10:00:00Z",
  "updatedAt": "2024-01-01T10:00:00Z",
  "createdBy": 1,
  "updatedBy": 1
}
```

### 拣货任务模型

```json
{
  "id": 1,
  "taskNo": "PT20240101001",
  "notificationId": 1,
  "pickingStrategy": "BY_ORDER",
  "waveNo": "WAVE001",
  "status": "PENDING",
  "priority": 5,
  "assignedUserId": 1,
  "totalQty": 100.0,
  "pickedQty": 0.0,
  "estimatedTime": 30,
  "actualTime": 0,
  "remark": "备注",
  "createdAt": "2024-01-01T10:00:00Z",
  "updatedAt": "2024-01-01T10:00:00Z"
}
```

### 库存分配模型

```json
{
  "id": 1,
  "outboundDetailId": 1,
  "inventoryId": 1,
  "allocatedQty": 50.0,
  "pickedQty": 0.0,
  "status": "ALLOCATED",
  "allocationStrategy": "FIFO",
  "allocationReason": "正常分配",
  "allocationTime": "2024-01-01T10:00:00Z",
  "remark": "备注"
}
```

### 发运单模型

```json
{
  "id": 1,
  "shipmentNo": "SH20240101001",
  "pickingTaskId": 1,
  "notificationId": 1,
  "carrierId": 1,
  "trackingNo": "TN20240101001",
  "shippingMethod": "快递",
  "status": "PREPARING",
  "consigneeName": "张三",
  "consigneePhone": "13800138000",
  "consigneeAddress": "北京市朝阳区xxx街道xxx号",
  "packageCount": 2,
  "totalWeight": 10.5,
  "totalVolume": 0.5,
  "shippingCost": 15.0,
  "estimatedShipDate": "2024-01-15",
  "actualShipDate": null,
  "estimatedDeliveryDate": "2024-01-17",
  "actualDeliveryDate": null,
  "remark": "备注",
  "createdAt": "2024-01-01T10:00:00Z",
  "updatedAt": "2024-01-01T10:00:00Z"
}
```

## 8. 使用示例

### 完整出库流程示例

1. **创建出库通知单**
```bash
curl -X POST /api/v1/wms/outbound-notifications \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": 1,
    "clientOrderNo": "CO20240101001",
    "warehouseId": 1,
    "requiredShipDate": "2024-01-15",
    "details": [...]
  }'
```

2. **审核通知单**
```bash
curl -X POST /api/v1/wms/outbound-notifications/1/approve \
  -H "Authorization: Bearer {token}"
```

3. **分配库存**
```bash
curl -X POST /api/v1/wms/outbound-notifications/1/allocate-inventory \
  -H "Authorization: Bearer {token}"
```

4. **生成拣货任务**
```bash
curl -X POST /api/v1/wms/outbound-notifications/1/generate-picking-task \
  -H "Authorization: Bearer {token}"
```

5. **执行拣货**
```bash
curl -X POST /api/v1/wms/picking-tasks/execute-picking \
  -H "Authorization: Bearer {token}" \
  -d '{
    "detailId": 1,
    "pickedQty": 100.0
  }'
```

6. **创建发运单**
```bash
curl -X POST /api/v1/wms/shipments \
  -H "Authorization: Bearer {token}" \
  -d '{
    "pickingTaskId": 1,
    "carrierId": 1,
    "shippingMethod": "快递"
  }'
```

7. **发运确认**
```bash
curl -X POST /api/v1/wms/shipments/1/ship \
  -H "Authorization: Bearer {token}"
```

## 9. 注意事项

1. **权限控制**: 所有接口都需要有效的认证token
2. **数据一致性**: 修改操作会检查数据状态，确保业务逻辑正确
3. **并发控制**: 关键操作使用乐观锁防止并发冲突
4. **审计日志**: 所有操作都会记录审计日志
5. **性能优化**: 大批量操作建议使用批量接口
6. **错误重试**: 网络错误可以重试，业务错误需要修正后重试
