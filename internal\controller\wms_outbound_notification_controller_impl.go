package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
)

// WmsOutboundNotificationController 定义出库通知单控制器接口
type WmsOutboundNotificationController interface {
	// 基础CRUD操作
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 批量操作
	BatchCreate(ctx iris.Context)
	BatchApprove(ctx iris.Context)
	BatchCancel(ctx iris.Context)

	// 状态管理
	Approve(ctx iris.Context)
	Cancel(ctx iris.Context)

	// 库存分配
	AllocateInventory(ctx iris.Context)
	BatchAllocateInventory(ctx iris.Context)
	GetAllocationStatus(ctx iris.Context)

	// 拣货任务生成
	GeneratePickingTask(ctx iris.Context)
	BatchGeneratePickingTask(ctx iris.Context)

	// 统计分析
	GetStats(ctx iris.Context)

	// 导入导出
	ImportFromExcel(ctx iris.Context)
	ExportToExcel(ctx iris.Context)

	// 业务查询
	GetByNotificationNo(ctx iris.Context)
	GetByClientOrderNo(ctx iris.Context)
	GetPendingAllocation(ctx iris.Context)
	GetReadyForPicking(ctx iris.Context)
	GetOverdueShipments(ctx iris.Context)
}

// wmsOutboundNotificationControllerImpl 出库通知单控制器实现
type WmsOutboundNotificationControllerImpl struct {
	BaseControllerImpl
	wmsOutboundNotificationService service.WmsOutboundNotificationService
}

// NewWmsOutboundNotificationControllerImpl 创建出库通知单控制器
func NewWmsOutboundNotificationControllerImpl(cm *ControllerManager) *WmsOutboundNotificationControllerImpl {
	return &WmsOutboundNotificationControllerImpl{
		BaseControllerImpl:             *NewBaseController(cm),
		wmsOutboundNotificationService: cm.GetServiceManager().GetWmsOutboundNotificationService(),
	}
}

// Create 创建出库通知单
// POST /api/v1/wms/outbound-notifications
func (ctrl *WmsOutboundNotificationControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsOutboundNotificationService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新出库通知单
// PUT /api/v1/wms/outbound-notifications/{id:uint}
func (ctrl *WmsOutboundNotificationControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsOutboundNotificationUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsOutboundNotificationService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除出库通知单
// DELETE /api/v1/wms/outbound-notifications/{id:uint}
func (ctrl *WmsOutboundNotificationControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsOutboundNotificationService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个出库通知单
// GET /api/v1/wms/outbound-notifications/{id:uint}
func (ctrl *WmsOutboundNotificationControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsOutboundNotificationByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsOutboundNotificationService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取出库通知单分页
// GET /api/v1/wms/outbound-notifications
func (ctrl *WmsOutboundNotificationControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsOutboundNotificationsPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)
	req.PageQuery.PageNum = pageQueryFromBuild.PageNum
	req.PageQuery.PageSize = pageQueryFromBuild.PageSize
	req.PageQuery.Sort = pageQueryFromBuild.Sort

	pageResult, err := ctrl.wmsOutboundNotificationService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// BatchCreate 批量创建出库通知单
// POST /api/v1/wms/outbound-notifications/batch-create
func (ctrl *WmsOutboundNotificationControllerImpl) BatchCreate(ctx iris.Context) {
	const opName = "BatchCreateWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationBatchCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsOutboundNotificationService.BatchCreate(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// BatchApprove 批量审核出库通知单
// POST /api/v1/wms/outbound-notifications/batch-approve
func (ctrl *WmsOutboundNotificationControllerImpl) BatchApprove(ctx iris.Context) {
	const opName = "BatchApproveWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationBatchApproveReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsOutboundNotificationService.BatchApprove(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// BatchCancel 批量取消出库通知单
// POST /api/v1/wms/outbound-notifications/batch-cancel
func (ctrl *WmsOutboundNotificationControllerImpl) BatchCancel(ctx iris.Context) {
	const opName = "BatchCancelWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationBatchCancelReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsOutboundNotificationService.BatchCancel(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// Approve 审核出库通知单
// POST /api/v1/wms/outbound-notifications/{id:uint}/approve
func (ctrl *WmsOutboundNotificationControllerImpl) Approve(ctx iris.Context) {
	const opName = "ApproveWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsOutboundNotificationApproveReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsOutboundNotificationService.Approve(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// Cancel 取消出库通知单
// POST /api/v1/wms/outbound-notifications/{id:uint}/cancel
func (ctrl *WmsOutboundNotificationControllerImpl) Cancel(ctx iris.Context) {
	const opName = "CancelWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsOutboundNotificationCancelReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsOutboundNotificationService.Cancel(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// AllocateInventory 分配库存
// POST /api/v1/wms/outbound-notifications/{id:uint}/allocate-inventory
func (ctrl *WmsOutboundNotificationControllerImpl) AllocateInventory(ctx iris.Context) {
	const opName = "AllocateInventoryWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsOutboundNotificationAllocateReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsOutboundNotificationService.AllocateInventory(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// BatchAllocateInventory 批量分配库存
// POST /api/v1/wms/outbound-notifications/batch-allocate-inventory
func (ctrl *WmsOutboundNotificationControllerImpl) BatchAllocateInventory(ctx iris.Context) {
	const opName = "BatchAllocateInventoryWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req dto.WmsOutboundNotificationBatchAllocateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsOutboundNotificationService.BatchAllocateInventory(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetAllocationStatus 获取分配状态
// GET /api/v1/wms/outbound-notifications/{id:uint}/allocation-status
func (ctrl *WmsOutboundNotificationControllerImpl) GetAllocationStatus(ctx iris.Context) {
	const opName = "GetAllocationStatusWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	result, err := ctrl.wmsOutboundNotificationService.GetAllocationStatus(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// GeneratePickingTask 生成拣货任务
// POST /api/v1/wms/outbound-notifications/{id:uint}/generate-picking-task
func (ctrl *WmsOutboundNotificationControllerImpl) GeneratePickingTask(ctx iris.Context) {
	const opName = "GeneratePickingTaskWmsOutboundNotification"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	strategy := ctx.URLParamDefault("strategy", "FIFO")

	result, err := ctrl.wmsOutboundNotificationService.GeneratePickingTask(ctx.Request().Context(), uint(id), strategy)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// BatchGeneratePickingTask 批量生成拣货任务
// POST /api/v1/wms/outbound-notifications/batch-generate-picking-task
func (ctrl *WmsOutboundNotificationControllerImpl) BatchGeneratePickingTask(ctx iris.Context) {
	const opName = "BatchGeneratePickingTaskWmsOutboundNotifications"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_outbound_notification")

	var req struct {
		IDs      []uint `json:"ids" validate:"required"`
		Strategy string `json:"strategy"`
	}
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	if req.Strategy == "" {
		req.Strategy = "FIFO"
	}

	results, err := ctrl.wmsOutboundNotificationService.BatchGeneratePickingTask(ctx.Request().Context(), req.IDs, req.Strategy)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}
