package repository

import (
	"backend/internal/model/entity"
	"time"
	"gorm.io/gorm"
)

// WmsCycleCountPlanQueryBuilderImpl 盘点计划查询构建器实现
type WmsCycleCountPlanQueryBuilderImpl struct {
	db    *gorm.DB
	query *gorm.DB
}

// WherePlanNo 按计划编号过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WherePlanNo(planNo string) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("plan_no = ?", planNo)
	return b
}

// WherePlanName 按计划名称过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WherePlanName(planName string) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("plan_name LIKE ?", "%"+planName+"%")
	return b
}

// WhereCountType 按盘点类型过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WhereCountType(countType entity.WmsCycleCountType) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("count_type = ?", countType)
	return b
}

// WhereWarehouseID 按仓库ID过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WhereWarehouseID(warehouseId uint) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("warehouse_id = ?", warehouseId)
	return b
}

// WhereStatus 按状态过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WhereStatus(status entity.WmsCycleCountPlanStatus) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("status = ?", status)
	return b
}

// WhereResponsibleUser 按负责人过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WhereResponsibleUser(userId uint) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("responsible_user_id = ?", userId)
	return b
}

// WhereCreatedBy 按创建人过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WhereCreatedBy(creatorId uint) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("created_by = ?", creatorId)
	return b
}

// WherePlannedDateRange 按计划日期范围过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WherePlannedDateRange(startDate, endDate time.Time) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("planned_date BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereCreatedDateRange 按创建日期范围过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WhereCreatedDateRange(startDate, endDate time.Time) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereCountStrategy 按盘点策略过滤
func (b *WmsCycleCountPlanQueryBuilderImpl) WhereCountStrategy(strategy entity.WmsCycleCountStrategy) WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Where("count_strategy = ?", strategy)
	return b
}

// WithWarehouse 包含仓库信息
func (b *WmsCycleCountPlanQueryBuilderImpl) WithWarehouse() WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Preload("Warehouse")
	return b
}

// WithResponsibleUser 包含负责人信息
func (b *WmsCycleCountPlanQueryBuilderImpl) WithResponsibleUser() WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Preload("ResponsibleUser")
	return b
}

// WithCreator 包含创建人信息
func (b *WmsCycleCountPlanQueryBuilderImpl) WithCreator() WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Preload("Creator")
	return b
}

// WithApprover 包含审批人信息
func (b *WmsCycleCountPlanQueryBuilderImpl) WithApprover() WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Preload("Approver")
	return b
}

// WithTasks 包含盘点任务信息
func (b *WmsCycleCountPlanQueryBuilderImpl) WithTasks() WmsCycleCountPlanQueryBuilder {
	b.query = b.query.Preload("Tasks")
	return b
}

// OrderByCreatedAt 按创建时间排序
func (b *WmsCycleCountPlanQueryBuilderImpl) OrderByCreatedAt(desc bool) WmsCycleCountPlanQueryBuilder {
	if desc {
		b.query = b.query.Order("created_at DESC")
	} else {
		b.query = b.query.Order("created_at ASC")
	}
	return b
}

// OrderByPlannedDate 按计划日期排序
func (b *WmsCycleCountPlanQueryBuilderImpl) OrderByPlannedDate(desc bool) WmsCycleCountPlanQueryBuilder {
	if desc {
		b.query = b.query.Order("planned_date DESC")
	} else {
		b.query = b.query.Order("planned_date ASC")
	}
	return b
}

// OrderByApprovedAt 按审批时间排序
func (b *WmsCycleCountPlanQueryBuilderImpl) OrderByApprovedAt(desc bool) WmsCycleCountPlanQueryBuilder {
	if desc {
		b.query = b.query.Order("approved_at DESC")
	} else {
		b.query = b.query.Order("approved_at ASC")
	}
	return b
}

// OrderByPlanNo 按计划编号排序
func (b *WmsCycleCountPlanQueryBuilderImpl) OrderByPlanNo(desc bool) WmsCycleCountPlanQueryBuilder {
	if desc {
		b.query = b.query.Order("plan_no DESC")
	} else {
		b.query = b.query.Order("plan_no ASC")
	}
	return b
}

// Find 执行查询并返回结果列表
func (b *WmsCycleCountPlanQueryBuilderImpl) Find() ([]entity.WmsCycleCountPlan, error) {
	var plans []entity.WmsCycleCountPlan
	err := b.query.Find(&plans).Error
	return plans, err
}

// First 执行查询并返回第一个结果
func (b *WmsCycleCountPlanQueryBuilderImpl) First() (*entity.WmsCycleCountPlan, error) {
	var plan entity.WmsCycleCountPlan
	err := b.query.First(&plan).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &plan, nil
}

// Count 执行查询并返回记录数
func (b *WmsCycleCountPlanQueryBuilderImpl) Count() (int64, error) {
	var count int64
	err := b.query.Count(&count).Error
	return count, err
}

// Paginate 执行分页查询
func (b *WmsCycleCountPlanQueryBuilderImpl) Paginate(pageNum, pageSize int) ([]entity.WmsCycleCountPlan, int64, error) {
	var plans []entity.WmsCycleCountPlan
	var total int64

	// 先获取总数
	countQuery := b.db.Model(&entity.WmsCycleCountPlan{})
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 执行分页查询
	offset := (pageNum - 1) * pageSize
	err := b.query.Offset(offset).Limit(pageSize).Find(&plans).Error
	return plans, total, err
}
