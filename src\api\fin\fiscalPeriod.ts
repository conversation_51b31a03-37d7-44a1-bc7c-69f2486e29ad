import request from '@/utils/request';

// --- 类型定义 ---

// 帐期生成 DTO
export interface FiscalPeriodGenerateFormData {
  fiscalYear: number;
  accountBookId: number;
  startYear: number;
  startMonth: number;
  startDay: number;
}

// 帐期列表项 VO
export interface FiscalPeriodListItem {
  id: number;
  code: string;
  fiscalYear: number;
  fiscalMonth: number;
  startDate: string;
  endDate: string;
  status: string;
  createdAt: string;
}

// 帐期列表响应
export interface FiscalPeriodListResponse {
  list: FiscalPeriodListItem[];
  total: number;
}

// 帐期查询参数
export interface FiscalPeriodQueryParams {
  pageNum: number;
  pageSize: number;
  fiscalYear?: number;
  status?: string;
  sort?: string;
}


// --- API 函数定义 ---

const BASE_URL = '/fin/periods';

/**
 * 获取帐期列表 (分页)
 * @param params 查询参数
 */
export function getFiscalPeriodPage(params: FiscalPeriodQueryParams): Promise<FiscalPeriodListResponse> {
  return request.get<FiscalPeriodListResponse>(`${BASE_URL}`, { params }) as unknown as Promise<FiscalPeriodListResponse>;
}

/**
 * 按年生成帐期
 * @param data 帐期生成数据
 */
export function generateFiscalPeriods(data: FiscalPeriodGenerateFormData) {
  return request.post<any>(`${BASE_URL}`, data);
}

/**
 * 更改帐期状态
 * @param id 帐期 ID
 * @param status 目标状态
 */
export function changeFiscalPeriodStatus(id: number, status: string) {
  return request.put<any>(`${BASE_URL}/${id}/status`, { status });
}
