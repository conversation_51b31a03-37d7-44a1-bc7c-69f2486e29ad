package middleware

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"backend/internal/service"
	"backend/pkg/constant"
	"backend/pkg/logger"
	"backend/pkg/util"

	"github.com/kataras/iris/v12"
)

// AuditEvent defines the structure for an audit event.
// This structure is passed to the AuditLogService.
type AuditEvent struct {
	Time         time.Time
	UserID       uint64 // UserID of the user performing the action.
	Username     string
	IP           string
	UserAgent    string
	RequestURI   string
	Method       string
	StatusCode   int
	Duration     int64 // Duration of the request in milliseconds.
	TraceID      string
	Status       string      // e.g., "success", "failure"
	Action       string      // e.g., "create_user", "login_success"
	ResourceType string      // e.g., "user", "role", "authentication"
	ResourceID   string      // Identifier of the resource affected.
	OldValue     string      // JSON string representing the state before change.
	NewValue     string      // JSON string representing the state after change.
	Details      interface{} // Additional details, can be string or other types. Service handles marshaling.
}

// AuditConfig holds the configuration for the AuditMiddleware.
// Ensure this struct is defined ONLY ONCE.
type AuditConfig struct {
	Enabled              bool
	LogRequestBody       bool
	LogResponseBody      bool
	MaxRequestBodySize   int
	MaxResponseBodySize  int
	ExcludedPaths        []string
	ExcludeGetRequests   bool
	ActionMappings       map[string]string
	ResourceIDRegex      string
	ResourceTypeRegex    string
	IgnoredResourceTypes []string
}

// defaultAuditConfig provides a default configuration for the AuditMiddleware.
var defaultAuditConfig = AuditConfig{
	Enabled:             true,
	LogRequestBody:      false,
	LogResponseBody:     false,
	MaxRequestBodySize:  2048,
	MaxResponseBodySize: 2048,
	ExcludeGetRequests:  true,
	ExcludedPaths: []string{
		"/metrics",
		"/health",
		"/swagger/*any",
		"/static/*path",
		"/favicon.ico",
	},
	ActionMappings: map[string]string{
		iris.MethodPost:   "create",
		iris.MethodPut:    "update",
		iris.MethodPatch:  "patch",
		iris.MethodDelete: "delete",
		iris.MethodGet:    "read",
	},
	ResourceIDRegex:      `(?:([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})|([0-9]+))\/?$`,
	ResourceTypeRegex:    `^/api/v[0-9]+/(?:[a-zA-Z0-9_-]+/)*([a-zA-Z0-9_-]+)`,
	IgnoredResourceTypes: []string{"swagger", "static", "metrics", "health"},
}

// AuditMiddleware is responsible for auditing HTTP requests and responses.
type AuditMiddleware struct {
	config            AuditConfig
	auditService      service.AuditLogService
	resourceIDRegex   *regexp.Regexp
	resourceTypeRegex *regexp.Regexp
}

// NewAuditMiddleware creates a new AuditMiddleware instance.
func NewAuditMiddleware(auditService service.AuditLogService, cfgOverrides ...AuditConfig) *AuditMiddleware {
	cfg := defaultAuditConfig
	if len(cfgOverrides) > 0 {
		cfg = cfgOverrides[0]
	}

	var newExcludedPaths []string
	loginPath := "/api/v1/auth/login"

	for _, p := range cfg.ExcludedPaths {
		if p == loginPath {
			continue
		}
		newExcludedPaths = append(newExcludedPaths, p)
	}
	cfg.ExcludedPaths = newExcludedPaths

	return &AuditMiddleware{
		config:            cfg,
		auditService:      auditService,
		resourceIDRegex:   regexp.MustCompile(cfg.ResourceIDRegex),
		resourceTypeRegex: regexp.MustCompile(cfg.ResourceTypeRegex),
	}
}

// Serve is the Iris middleware handler function for AuditMiddleware.
func (m *AuditMiddleware) Serve(ctx iris.Context) {
	if !m.config.Enabled {
		ctx.Next()
		return
	}

	if m.config.ExcludeGetRequests && ctx.Method() == iris.MethodGet {
		ctx.Next()
		return
	}

	requestPath := ctx.Path()
	for _, excludedPath := range m.config.ExcludedPaths {
		if util.PathMatch(requestPath, excludedPath) {
			ctx.Next()
			return
		}
	}

	startTime := time.Now()

	// Handle case where event might have been recorded manually (e.g., in login controller)
	// This is an optimization to avoid unnecessary work like recording the response body.
	if ctx.Values().GetBoolDefault(constant.AUDIT_EVENT_RECORDED_CTX_KEY, false) {
		ctx.Next()
		return
	}

	// Per plan, record the response body to allow capturing `NewValue` for C/U operations.
	// This must be called before `ctx.Next()`.
	ctx.Record()

	defer func() {
		reqCtxForDefer := ctx.Request().Context()                           // Get context within defer
		traceIDForPanic, _ := util.GetTraceIDFromStdContext(reqCtxForDefer) // Get TraceID for panic logging, defaults to "" if error or not found

		if r := recover(); r != nil {
			logger.WithContext(reqCtxForDefer).Error(
				"Panic recovered during audit middleware defer",
				logger.WithField("panic", fmt.Sprintf("%+v", r)), // Use fmt.Sprintf for potentially better panic info
				logger.WithField("traceId", traceIDForPanic),
			)
			panic(r) // Re-panic so Iris's default recovery (or other panic handlers) can process it
		}

		// Check again if it was recorded by a handler during ctx.Next()
		if ctx.Values().GetBoolDefault(constant.AUDIT_EVENT_RECORDED_CTX_KEY, false) {
			return
		}

		duration := time.Since(startTime)
		statusCode := ctx.GetStatusCode()

		eventTraceIDValue, _ := util.GetTraceIDFromStdContext(reqCtxForDefer) // Defaults to "" if error or not found
		event := &AuditEvent{
			Time:       startTime,
			UserAgent:  ctx.GetHeader("User-Agent"),
			RequestURI: requestPath,
			Method:     ctx.Method(),
			StatusCode: statusCode,
			Duration:   duration.Milliseconds(),
			TraceID:    eventTraceIDValue, // Use context from defer
			Status:     "success",         // Default, can be overridden
		}

		userIDFromCtx, _ := util.GetUserIDFromStdContext(reqCtxForDefer)
		event.UserID = userIDFromCtx
		event.Username, _ = util.GetUsernameFromStdContext(reqCtxForDefer)
		event.IP, _ = util.GetClientIPFromStdContext(reqCtxForDefer)

		if actionOverride := ctx.Values().GetString(constant.AUDIT_ACTION_CTX_KEY); actionOverride != "" {
			event.Action = actionOverride
		} else {
			event.Action = getAction(event.Method, m.config.ActionMappings)
		}

		if resTypeOverride := ctx.Values().GetString(constant.AUDIT_RESOURCE_TYPE_CTX_KEY); resTypeOverride != "" {
			event.ResourceType = resTypeOverride
		} else {
			event.ResourceType = getResourceType(event.RequestURI, m.resourceTypeRegex, m.config.IgnoredResourceTypes)
		}

		if resIDOverride := ctx.Values().GetString(constant.AUDIT_RESOURCE_ID_CTX_KEY); resIDOverride != "" {
			event.ResourceID = resIDOverride
		} else {
			event.ResourceID = getResourceID(event.RequestURI, m.resourceIDRegex)
		}

		if detailsOverride := ctx.Values().Get(constant.AUDIT_DETAILS_CTX_KEY); detailsOverride != nil {
			if detailsStr, ok := detailsOverride.(string); ok {
				event.Details = detailsStr
			} else {
				detailsBytes, err := json.Marshal(detailsOverride)
				if err == nil {
					event.Details = string(detailsBytes)
				} else {
					event.Details = fmt.Sprintf("%+v", detailsOverride)
					logger.WithContext(reqCtxForDefer).Warnf("Failed to marshal audit event details from context: %v. Falling back to fmt.Sprintf", err, logger.WithField("traceId", event.TraceID))
				}
			}
		}
		if oldValueFromCtx := ctx.Values().GetString(constant.AUDIT_OLD_VALUE_CTX_KEY); oldValueFromCtx != "" {
			event.OldValue = oldValueFromCtx
		}
		// Handle NewValue. Priority: 1. Context, 2. Response Body (for C/U), 3. Method-based inference (for Delete).
		if newValueFromCtx := ctx.Values().GetString(constant.AUDIT_NEW_VALUE_CTX_KEY); newValueFromCtx != "" {
			event.NewValue = newValueFromCtx
		} else {
			switch event.Method {
			case iris.MethodPost, iris.MethodPut, iris.MethodPatch:
				if statusCode >= 200 && statusCode < 300 {
					responseBody := ctx.Recorder().Body()
					if len(responseBody) > 0 {
						if len(responseBody) <= m.config.MaxResponseBodySize {
							var responseData struct {
								Data interface{} `json:"data"`
							}
							if err := json.Unmarshal(responseBody, &responseData); err == nil && responseData.Data != nil {
								newValueBytes, err := json.Marshal(responseData.Data)
								if err == nil {
									event.NewValue = string(newValueBytes)
								} else {
									event.NewValue = string(responseBody)
								}
							} else {
								event.NewValue = string(responseBody)
							}
						} else {
							event.NewValue = fmt.Sprintf("Response body too large (size: %d > max: %d)", len(responseBody), m.config.MaxResponseBodySize)
							logger.WithContext(reqCtxForDefer).Warn(
								"AuditMiddleware: Response body for NewValue exceeds max size, will not be fully logged.",
								logger.WithField("traceId", event.TraceID),
								logger.WithField("size", len(responseBody)),
								logger.WithField("maxSize", m.config.MaxResponseBodySize),
							)
						}
					}
				}
			case iris.MethodDelete:
				if statusCode >= 200 && statusCode < 300 {
					event.NewValue = "null" // Per plan, successful delete means new value is null.
				}
			}
		}

		if userIDOverrideContext := ctx.Values().Get(constant.AUDIT_USER_ID_CTX_KEY); userIDOverrideContext != nil {
			var uidToSet uint64
			switch v := userIDOverrideContext.(type) {
			case uint:
				uidToSet = uint64(v)
			case uint64:
				uidToSet = v
			case int:
				uidToSet = uint64(v)
			case int64:
				uidToSet = uint64(v)
			}
			if uidToSet > 0 {
				event.UserID = uidToSet
			}
		}

		if usernameOverride := ctx.Values().GetString(constant.AUDIT_USERNAME_CTX_KEY); usernameOverride != "" {
			event.Username = usernameOverride
		}

		if statusCode >= 400 {
			event.Status = "failure"
		}

		// Request/Response body logging placeholders as per plan (not implemented directly here)
		if m.config.LogRequestBody {
			// logger.WithContext(reqCtxForDefer).Debug("AuditMiddleware: Request body logging enabled but not implemented here.")
		}
		if m.config.LogResponseBody {
			// logger.WithContext(reqCtxForDefer).Debug("AuditMiddleware: Response body logging enabled but not implemented here.")
		}

		if m.auditService != nil {
			placeholderEvent := &service.AuditEventPlaceholder{
				Time:         event.Time,
				UserID:       event.UserID,
				Username:     event.Username,
				IP:           event.IP,
				UserAgent:    event.UserAgent,
				RequestURI:   event.RequestURI,
				Method:       event.Method,
				StatusCode:   event.StatusCode,
				Duration:     event.Duration,
				TraceID:      event.TraceID,
				Status:       event.Status,
				Action:       event.Action,
				ResourceType: event.ResourceType,
				ResourceID:   event.ResourceID,
				OldValue:     event.OldValue,
				NewValue:     event.NewValue,
				Details:      event.Details,
			}
			err := m.auditService.RecordEvent(placeholderEvent)
			if err != nil {
				logger.WithContext(reqCtxForDefer).Error("AuditMiddleware: Failed to record audit event via service", logger.WithError(err), logger.WithField("traceId", placeholderEvent.TraceID))
			}
		} else {
			logger.WithContext(reqCtxForDefer).Error("AuditMiddleware: AuditService is nil, cannot record event", logger.WithField("traceId", event.TraceID)) // event.TraceID is still valid here as it's from original event
		}
	}()

	ctx.Next()
}

// Helper functions
func getAction(method string, mappings map[string]string) string {
	action, ok := mappings[strings.ToUpper(method)]
	if ok {
		return action
	}
	return "access" // Default action
}

func getResourceType(uri string, typeRegex *regexp.Regexp, ignoredTypes []string) string {
	if typeRegex == nil {
		return ""
	}
	matches := typeRegex.FindStringSubmatch(uri)
	if len(matches) > 1 {
		resourceType := matches[len(matches)-1] // Last capturing group as per regex design in default config
		for _, ignored := range ignoredTypes {
			if resourceType == ignored {
				return ""
			}
		}
		return resourceType
	}
	return ""
}

func getResourceID(uri string, idRegex *regexp.Regexp) string {
	if idRegex == nil {
		return ""
	}
	matches := idRegex.FindStringSubmatch(uri)
	if len(matches) > 1 {
		for i := len(matches) - 1; i > 0; i-- { // Iterate backwards through capturing groups
			if matches[i] != "" {
				return matches[i]
			}
		}
	}
	return ""
}
