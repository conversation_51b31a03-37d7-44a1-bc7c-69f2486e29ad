/**
 * 状态流程管理钩子
 */
import { ref, computed, nextTick } from 'vue'
import type { Ref, ComputedRef } from 'vue'
import type { 
  StatusFlowConfig, 
  StatusOption, 
  StatusTransition, 
  StatusChangeRequest,
  StatusChangeResponse,
  UseStatusFlowReturn 
} from '../types'

// 预设配置注册表
const configRegistry = new Map<string, StatusFlowConfig>()

/**
 * 注册状态流程配置
 */
export const registerStatusFlowConfig = (config: StatusFlowConfig) => {
  configRegistry.set(config.businessType, config)
}

/**
 * 获取状态流程配置
 */
export const getStatusFlowConfig = (businessType: string): StatusFlowConfig | undefined => {
  return configRegistry.get(businessType)
}

/**
 * 状态流程管理钩子
 */
export const useStatusFlow = (
  businessType: string,
  initialStatus: string,
  customConfig?: Partial<StatusFlowConfig>
): UseStatusFlowReturn => {
  // 基础配置
  const baseConfig = getStatusFlowConfig(businessType)
  if (!baseConfig) {
    throw new Error(`未找到业务类型 "${businessType}" 的状态流程配置`)
  }

  // 合并自定义配置
  const config = ref<StatusFlowConfig>({
    ...baseConfig,
    ...customConfig,
    statusOptions: customConfig?.statusOptions || baseConfig.statusOptions,
    statusTransitions: customConfig?.statusTransitions || baseConfig.statusTransitions,
    displayConfig: {
      ...baseConfig.displayConfig,
      ...customConfig?.displayConfig
    },
    permissionConfig: {
      ...baseConfig.permissionConfig,
      ...customConfig?.permissionConfig
    }
  })

  // 当前状态
  const currentStatus = ref<string>(initialStatus)

  // 状态列表（按流程顺序）
  const statusList = computed<StatusOption[]>(() => {
    return config.value.statusOptions
  })

  // 可用的状态转换
  const availableTransitions = computed<StatusTransition[]>(() => {
    return config.value.statusTransitions.filter(
      transition => transition.from === currentStatus.value
    )
  })

  // 判断是否可以转换到指定状态
  const canTransitionTo = (targetStatus: string): boolean => {
    const transitions = availableTransitions.value
    return transitions.some(transition => 
      transition.to.includes(targetStatus)
    )
  }

  // 判断状态是否已完成
  const isStatusCompleted = (status: string): boolean => {
    // 获取状态在流程中的顺序
    const statusOrder = statusList.value.map(s => s.value)
    const currentIndex = statusOrder.indexOf(currentStatus.value)
    const targetIndex = statusOrder.indexOf(status)
    
    // 特殊状态处理
    if (currentStatus.value === 'CANCELLED' || currentStatus.value === 'CLOSED') {
      return status === currentStatus.value
    }
    
    // 基于顺序判断是否完成
    return targetIndex <= currentIndex && targetIndex !== -1
  }

  // 获取状态配置
  const getStatusConfig = (status: string): StatusOption | undefined => {
    return statusList.value.find(s => s.value === status)
  }

  // 获取状态转换的权限
  const getTransitionPermission = (from: string, to: string): string | undefined => {
    const transitionKey = `${from}->${to}`
    return config.value.permissionConfig[transitionKey]
  }

  // 状态转换中文标签映射
  const transitionLabelMap = computed<Record<string, string>>(() => {
    return (config.value as any).transitionLabels || {}
  })

  // 获取状态转换中文标签
  const getTransitionLabel = (from: string, to: string): string => {
    const key = `${from}->${to}`
    const label = transitionLabelMap.value[key]
    if (label) return label
    // fallback: 目标状态label
    const target = getStatusConfig(to)
    return `转为${target?.label || to}`
  }

  // 按钮类型映射
  const buttonTypeMap = computed<Record<string, 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger'>>(() => {
    return (config.value as any).buttonTypes || {}
  })

  // 获取按钮类型
  const getButtonType = (status: string): 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    return buttonTypeMap.value[status] || 'default'
  }

  // 按钮图标映射
  const buttonIconMap = computed<Record<string, string>>(() => {
    return (config.value as any).buttonIcons || {}
  })

  // 获取按钮图标名称
  const getButtonIconName = (status: string): string | undefined => {
    return buttonIconMap.value[status]
  }

  // 验证状态转换
  const validateTransition = (
    from: string, 
    to: string, 
    businessData?: Record<string, any>
  ): { valid: boolean; message?: string } => {
    // 检查是否允许转换
    if (!canTransitionTo(to)) {
      return { 
        valid: false, 
        message: `不能从 "${getStatusConfig(from)?.label}" 转换到 "${getStatusConfig(to)?.label}"` 
      }
    }

    // 检查转换条件
    const transition = config.value.statusTransitions.find(t => t.from === from)
    const targetTransition = transition?.to.find(target => target === to)
    
    if (transition && targetTransition) {
      // 如果有自定义条件验证
      if (transition.condition && !transition.condition(businessData)) {
        return { valid: false, message: '不满足状态转换条件' }
      }
    }

    return { valid: true }
  }

  // 执行状态转换
  const changeStatus = async (
    newStatus: string, 
    remark?: string,
    businessData?: Record<string, any>
  ): Promise<StatusChangeResponse> => {
    const oldStatus = currentStatus.value

    try {
      // 验证转换
      const validation = validateTransition(oldStatus, newStatus, businessData)
      if (!validation.valid) {
        return {
          success: false,
          message: validation.message
        }
      }

      // 构建请求数据
      const request: StatusChangeRequest = {
        businessId: businessData?.['id'] ?? '',
        newStatus,
        oldStatus,
        remark,
        extraData: businessData
      }

      // 这里可以调用API进行状态变更
      // const response = await statusChangeApi(request)
      
      // 模拟API调用（实际使用时替换为真实API调用）
      await new Promise(resolve => setTimeout(resolve, 500))

      // 更新当前状态
      currentStatus.value = newStatus

      return {
        success: true,
        newStatus,
        message: '状态更新成功'
      }

    } catch (error: any) {
      return {
        success: false,
        message: error.message || '状态更新失败'
      }
    }
  }

  // 重置状态
  const resetStatus = (status: string) => {
    currentStatus.value = status
  }

  // 更新配置
  const updateConfig = (newConfig: Partial<StatusFlowConfig>) => {
    config.value = {
      ...config.value,
      ...newConfig,
      displayConfig: {
        ...config.value.displayConfig,
        ...newConfig.displayConfig
      },
      permissionConfig: {
        ...config.value.permissionConfig,
        ...newConfig.permissionConfig
      }
    }
  }

  return {
    config,
    currentStatus,
    statusList,
    availableTransitions,
    canTransitionTo,
    isStatusCompleted,
    getStatusConfig,
    changeStatus,
    resetStatus: resetStatus,
    updateConfig,
    getTransitionPermission,
    getTransitionLabel,
    getButtonType,
    getButtonIconName,
    validateTransition
  }
}

/**
 * 状态流程工具函数
 */
export const statusFlowUtils = {
  // 格式化状态标签
  formatStatusLabel: (status: string, statusOptions: StatusOption[]): string => {
    const statusConfig = statusOptions.find(s => s.value === status)
    return statusConfig?.label || status
  },

  // 获取状态颜色
  getStatusColor: (status: string, statusOptions: StatusOption[]): { color: string; bgColor: string } => {
    const statusConfig = statusOptions.find(s => s.value === status)
    return {
      color: statusConfig?.color || '#909399',
      bgColor: statusConfig?.bgColor || '#f4f4f5'
    }
  },

  // 生成状态流程进度百分比
  calculateProgress: (currentStatus: string, statusOptions: StatusOption[]): number => {
    const statusValues = statusOptions.map(s => s.value)
    const currentIndex = statusValues.indexOf(currentStatus)
    
    if (currentIndex === -1) return 0
    
    // 取消和关闭状态特殊处理
    if (currentStatus === 'CANCELLED') return 0
    if (currentStatus === 'CLOSED') return 100
    
    return Math.round((currentIndex / (statusValues.length - 1)) * 100)
  },

  // 获取下一个可用状态
  getNextAvailableStatuses: (
    currentStatus: string, 
    transitions: StatusTransition[]
  ): string[] => {
    const transition = transitions.find(t => t.from === currentStatus)
    return transition?.to || []
  }
} 