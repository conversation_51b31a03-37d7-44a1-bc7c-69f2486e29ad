package entity

import (
	"time"
)

// WmsPutawayTaskDetail 对应数据库中的 wms_putaway_task_details 表
// 存储上架任务的明细行信息
type WmsPutawayTaskDetail struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	PutawayTaskID           uint `gorm:"column:putaway_task_id;not null;comment:关联上架任务ID" json:"putawayTaskId"`                      // 关联的 wms_putaway_tasks ID
	ReceivingRecordDetailID uint `gorm:"column:receiving_record_detail_id;not null;comment:关联收货明细ID" json:"receivingRecordDetailId"` // 关联的 wms_receiving_record_details ID (指明来源)
	AccountBookID           uint `gorm:"column:account_book_id;not null;default:0;comment:账套ID" json:"accountBookId"`                // 账套ID

	LineNo                int             `gorm:"column:line_no;not null;comment:行号" json:"lineNo"`                                                // 行号 (在同一任务单内唯一)
	ItemID                uint            `gorm:"column:item_id;not null;comment:物料ID" json:"itemId"`                                              // 物料ID (冗余, 但方便查询)
	PutawayQuantity       float64         `gorm:"column:putaway_quantity;type:numeric(12,4);not null;comment:需上架数量" json:"putawayQuantity"`        // 需要上架的数量
	UnitOfMeasure         string          `gorm:"column:unit_of_measure;type:varchar(20);not null;comment:数量单位" json:"unitOfMeasure"`              // 数量单位
	BatchNo               *string         `gorm:"column:batch_no;type:varchar(100);comment:批次号" json:"batchNo"`                                    // 批次号 (从收货明细带入)
	ProductionDate        *time.Time      `gorm:"column:production_date;type:date;comment:生产日期" json:"productionDate"`                             // 生产日期 (从收货明细带入)
	ExpiryDate            *time.Time      `gorm:"column:expiry_date;type:date;comment:过期日期" json:"expiryDate"`                                     // 过期日期 (从收货明细带入)
	SourceLocationID      uint            `gorm:"column:source_location_id;not null;comment:源库位ID" json:"sourceLocationId"`                        // 源库位ID (通常是收货库位)
	SuggestedLocationID   *uint           `gorm:"column:suggested_location_id;comment:建议库位ID" json:"suggestedLocationId"`                          // 系统建议库位ID (可选)
	ActualLocationID      *uint           `gorm:"column:actual_location_id;comment:实际库位ID" json:"actualLocationId"`                                // 实际放置库位ID (执行时确认)
	ActualPutawayQuantity float64         `gorm:"column:actual_putaway_quantity;type:numeric(12,4);comment:实上架数量" json:"actualPutawayQuantity"`    // 实际放置数量 (执行时确认)
	Status                string          `gorm:"column:status;type:wms_putaway_task_status;not null;default:'PENDING';comment:行状态" json:"status"` // 行状态 (使用数据库定义的 ENUM)
	ExceptionReason       *string         `gorm:"column:exception_reason;type:text;comment:异常原因" json:"exceptionReason"`                           // 异常原因 (如库位已满、货物损坏)
}

// TableName 指定数据库表名方法
func (WmsPutawayTaskDetail) TableName() string {
	return "wms_putaway_task_detail"
}
