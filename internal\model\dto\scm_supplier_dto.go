package dto

import (
	"backend/pkg/response"
	"time"

	"github.com/shopspring/decimal"
)

// ScmSupplierCreateReq 创建供应商请求
type ScmSupplierCreateReq struct {
	// 供应商基本信息
	SupplierCode        string           `json:"supplierCode" binding:"omitempty,max=50"`                     // 供应商编码，支持自动生成
	SupplierName        string           `json:"supplierName" binding:"required,max=255"`                     // 供应商名称，必填
	SupplierType        string           `json:"supplierType" binding:"omitempty,oneof=CORPORATE INDIVIDUAL"` // 供应商类型
	Industry            *string          `json:"industry" binding:"omitempty,max=100"`                        // 所属行业
	BusinessLicense     *string          `json:"businessLicense" binding:"omitempty,max=100"`                 // 营业执照号
	TaxNumber           *string          `json:"taxNumber" binding:"omitempty,max=100"`                       // 税务登记号
	LegalRepresentative *string          `json:"legalRepresentative" binding:"omitempty,max=100"`             // 法定代表人
	RegisteredCapital   *decimal.Decimal `json:"registeredCapital" binding:"omitempty"`                       // 注册资本

	// 联系信息
	ContactPerson *string `json:"contactPerson" binding:"omitempty,max=100"`      // 主要联系人
	ContactPhone  *string `json:"contactPhone" binding:"omitempty,max=50"`        // 联系电话
	ContactEmail  *string `json:"contactEmail" binding:"omitempty,email,max=255"` // 联系邮箱
	Website       *string `json:"website" binding:"omitempty,url,max=255"`        // 公司网站

	// 地址信息
	Country    *string `json:"country" binding:"omitempty,max=100"`   // 国家
	Province   *string `json:"province" binding:"omitempty,max=100"`  // 省份
	City       *string `json:"city" binding:"omitempty,max=100"`      // 城市
	District   *string `json:"district" binding:"omitempty,max=100"`  // 区县
	Address    *string `json:"address" binding:"omitempty"`           // 详细地址
	PostalCode *string `json:"postalCode" binding:"omitempty,max=20"` // 邮政编码

	// 财务信息
	CreditRating *string          `json:"creditRating" binding:"omitempty,max=20"` // 信用等级
	CreditLimit  *decimal.Decimal `json:"creditLimit" binding:"omitempty"`         // 信用额度
	PaymentTerms *string          `json:"paymentTerms" binding:"omitempty,max=50"` // 付款条件
	CurrencyCode string           `json:"currencyCode" binding:"omitempty,max=10"` // 结算币种

	// 业务信息
	SupplierLevel               string  `json:"supplierLevel" binding:"omitempty,oneof=A B C D"`      // 供应商级别
	SupplierCategory            *string `json:"supplierCategory" binding:"omitempty,max=50"`          // 供应商分类
	SupplierSource              *string `json:"supplierSource" binding:"omitempty,max=50"`            // 供应商来源
	ProcurementRepresentativeId *uint   `json:"procurementRepresentativeId" binding:"omitempty,gt=0"` // 采购员ID

	// 供应商特有字段
	QualificationCertificate *string          `json:"qualificationCertificate" binding:"omitempty"` // 资质证书
	QualityRating            *string          `json:"qualityRating" binding:"omitempty,max=50"`     // 质量评级
	DeliveryRating           *string          `json:"deliveryRating" binding:"omitempty,max=50"`    // 交期评级
	ServiceRating            *string          `json:"serviceRating" binding:"omitempty,max=50"`     // 服务评级
	AnnualSupplyCapacity     *decimal.Decimal `json:"annualSupplyCapacity" binding:"omitempty"`     // 年供货能力
	MainProducts             *string          `json:"mainProducts" binding:"omitempty,max=255"`     // 主要产品
	CertificationInfo        *string          `json:"certificationInfo" binding:"omitempty,max=255"`   // 认证

	// 状态信息
	Status              string  `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE BLACKLIST"` // 状态
	IsKeySupplier       *bool   `json:"isKeySupplier" binding:"omitempty"`                          // 是否重点供应商
	IsStrategicSupplier *bool   `json:"isStrategicSupplier" binding:"omitempty"`                    // 是否战略供应商
	Remark              *string `json:"remark" binding:"omitempty"`                                 // 备注

	// 联系人信息（可选）
	Contacts []ScmSupplierContactDTO `json:"contacts" binding:"omitempty,dive"` // 供应商联系人列表
}

// ScmSupplierUpdateReq 更新供应商请求
type ScmSupplierUpdateReq struct {
	ID uint `json:"id" binding:"required,gt=0"` // 供应商ID，必填

	// 供应商基本信息
	SupplierCode        *string          `json:"supplierCode" binding:"omitempty,max=50"`                     // 供应商编码
	SupplierName        *string          `json:"supplierName" binding:"omitempty,max=255"`                    // 供应商名称
	SupplierType        *string          `json:"supplierType" binding:"omitempty,oneof=CORPORATE INDIVIDUAL"` // 供应商类型
	Industry            *string          `json:"industry" binding:"omitempty,max=100"`                        // 所属行业
	BusinessLicense     *string          `json:"businessLicense" binding:"omitempty,max=100"`                 // 营业执照号
	TaxNumber           *string          `json:"taxNumber" binding:"omitempty,max=100"`                       // 税务登记号
	LegalRepresentative *string          `json:"legalRepresentative" binding:"omitempty,max=100"`             // 法定代表人
	RegisteredCapital   *decimal.Decimal `json:"registeredCapital" binding:"omitempty"`                       // 注册资本

	// 联系信息
	ContactPerson *string `json:"contactPerson" binding:"omitempty,max=100"`      // 主要联系人
	ContactPhone  *string `json:"contactPhone" binding:"omitempty,max=50"`        // 联系电话
	ContactEmail  *string `json:"contactEmail" binding:"omitempty,email,max=255"` // 联系邮箱
	Website       *string `json:"website" binding:"omitempty,url,max=255"`        // 公司网站

	// 地址信息
	Country    *string `json:"country" binding:"omitempty,max=100"`   // 国家
	Province   *string `json:"province" binding:"omitempty,max=100"`  // 省份
	City       *string `json:"city" binding:"omitempty,max=100"`      // 城市
	District   *string `json:"district" binding:"omitempty,max=100"`  // 区县
	Address    *string `json:"address" binding:"omitempty"`           // 详细地址
	PostalCode *string `json:"postalCode" binding:"omitempty,max=20"` // 邮政编码

	// 财务信息
	CreditRating *string          `json:"creditRating" binding:"omitempty,max=20"` // 信用等级
	CreditLimit  *decimal.Decimal `json:"creditLimit" binding:"omitempty"`         // 信用额度
	PaymentTerms *string          `json:"paymentTerms" binding:"omitempty,max=50"` // 付款条件
	CurrencyCode *string          `json:"currencyCode" binding:"omitempty,max=10"` // 结算币种

	// 业务信息
	SupplierLevel               *string `json:"supplierLevel" binding:"omitempty,oneof=A B C D"`      // 供应商级别
	SupplierCategory            *string `json:"supplierCategory" binding:"omitempty,max=50"`          // 供应商分类
	SupplierSource              *string `json:"supplierSource" binding:"omitempty,max=50"`            // 供应商来源
	ProcurementRepresentativeId *uint   `json:"procurementRepresentativeId" binding:"omitempty,gt=0"` // 采购员ID

	// 供应商特有字段
	QualificationCertificate *string          `json:"qualificationCertificate" binding:"omitempty"` // 资质证书
	QualityRating            *string          `json:"qualityRating" binding:"omitempty,max=50"`     // 质量评级
	DeliveryRating           *string          `json:"deliveryRating" binding:"omitempty,max=50"`    // 交期评级
	ServiceRating            *string          `json:"serviceRating" binding:"omitempty,max=50"`     // 服务评级
	AnnualSupplyCapacity     *decimal.Decimal `json:"annualSupplyCapacity" binding:"omitempty"`     // 年供货能力
	MainProducts             *string          `json:"mainProducts" binding:"omitempty,max=255"`     // 主要产品
	CertificationInfo        *string          `json:"certificationInfo" binding:"omitempty,max=255"`   // 认证

	// 状态信息
	Status              *string `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE BLACKLIST"` // 状态
	IsKeySupplier       *bool   `json:"isKeySupplier" binding:"omitempty"`                          // 是否重点供应商
	IsStrategicSupplier *bool   `json:"isStrategicSupplier" binding:"omitempty"`                    // 是否战略供应商
	Remark              *string `json:"remark" binding:"omitempty"`                                 // 备注
}

// ScmSupplierQueryReq 查询供应商请求（使用现有分页机制）
type ScmSupplierQueryReq struct {
	response.PageQuery                 // 直接嵌入现有分页结构
	SupplierCode                string `form:"supplierCode" json:"supplierCode" binding:"omitempty,max=50"`                     // 供应商编码
	SupplierName                string `form:"supplierName" json:"supplierName" binding:"omitempty,max=255"`                    // 供应商名称
	SupplierType                string `form:"supplierType" json:"supplierType" binding:"omitempty,oneof=CORPORATE INDIVIDUAL"` // 供应商类型
	Industry                    string `form:"industry" json:"industry" binding:"omitempty,max=100"`                            // 所属行业
	SupplierLevel               string `form:"supplierLevel" json:"supplierLevel" binding:"omitempty,oneof=A B C D"`            // 供应商级别
	SupplierCategory            string `form:"supplierCategory" json:"supplierCategory" binding:"omitempty,max=50"`             // 供应商分类
	Status                      string `form:"status" json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE BLACKLIST"`        // 状态
	ProcurementRepresentativeId *uint  `json:"procurementRepresentativeId" binding:"omitempty,gt=0"`                            // 采购员ID
	IsKeySupplier               *bool  `form:"isKeySupplier" json:"isKeySupplier"`                                              // 是否重点供应商
	QualityRating               string `form:"qualityRating" json:"qualityRating" binding:"omitempty,max=50"`                   // 质量评级
	Country                     string `form:"country" json:"country" binding:"omitempty,max=100"`                              // 国家
	Province                    string `form:"province" json:"province" binding:"omitempty,max=100"`                            // 省份
	City                        string `form:"city" json:"city" binding:"omitempty,max=100"`                                    // 城市
}

// ScmSupplierContactDTO 供应商联系人传输对象
type ScmSupplierContactDTO struct {
	ID           uint       `json:"id,omitempty"`                                 // 联系人ID，更新时需要
	ContactName  string     `json:"contactName" binding:"required,max=100"`       // 联系人姓名，必填
	ContactTitle *string    `json:"contactTitle" binding:"omitempty,max=100"`     // 职务
	Department   *string    `json:"department" binding:"omitempty,max=100"`       // 所属部门
	Phone        *string    `json:"phone" binding:"omitempty,max=50"`             // 电话
	Mobile       *string    `json:"mobile" binding:"omitempty,max=50"`            // 手机
	Email        *string    `json:"email" binding:"omitempty,email,max=255"`      // 邮箱
	QQ           *string    `json:"qq" binding:"omitempty,max=50"`                // QQ号
	Wechat       *string    `json:"wechat" binding:"omitempty,max=100"`           // 微信号
	Address      *string    `json:"address" binding:"omitempty"`                  // 联系地址
	PostalCode   *string    `json:"postalCode" binding:"omitempty,max=20"`        // 邮政编码
	Birthday     *time.Time `json:"birthday" binding:"omitempty"`                 // 生日
	Gender       *string    `json:"gender" binding:"omitempty,oneof=MALE FEMALE"` // 性别
	ContactRole  *string    `json:"contactRole" binding:"omitempty,max=50"`       // 联系人角色
	Remark       *string    `json:"remark" binding:"omitempty"`                   // 备注
}

// ScmSupplierContactCreateReq 创建供应商联系人请求
type ScmSupplierContactCreateReq struct {
	SupplierID   uint       `json:"supplierId" binding:"required,gt=0"`           // 供应商ID，必填
	ContactName  string     `json:"contactName" binding:"required,max=100"`       // 联系人姓名，必填
	ContactTitle *string    `json:"contactTitle" binding:"omitempty,max=100"`     // 职务
	Department   *string    `json:"department" binding:"omitempty,max=100"`       // 所属部门
	Phone        *string    `json:"phone" binding:"omitempty,max=50"`             // 电话
	Mobile       *string    `json:"mobile" binding:"omitempty,max=50"`            // 手机
	Email        *string    `json:"email" binding:"omitempty,email,max=255"`      // 邮箱
	QQ           *string    `json:"qq" binding:"omitempty,max=50"`                // QQ号
	Wechat       *string    `json:"wechat" binding:"omitempty,max=100"`           // 微信号
	Address      *string    `json:"address" binding:"omitempty"`                  // 联系地址
	PostalCode   *string    `json:"postalCode" binding:"omitempty,max=20"`        // 邮政编码
	Birthday     *time.Time `json:"birthday" binding:"omitempty"`                 // 生日
	Gender       *string    `json:"gender" binding:"omitempty,oneof=MALE FEMALE"` // 性别
	ContactRole  *string    `json:"contactRole" binding:"omitempty,max=50"`       // 联系人角色
	Remark       *string    `json:"remark" binding:"omitempty"`                   // 备注
}

// ScmSupplierContactUpdateReq 更新供应商联系人请求
type ScmSupplierContactUpdateReq struct {
	ID           uint       `json:"id" binding:"required,gt=0"`                   // 联系人ID，必填
	ContactName  *string    `json:"contactName" binding:"omitempty,max=100"`      // 联系人姓名
	ContactTitle *string    `json:"contactTitle" binding:"omitempty,max=100"`     // 职务
	Department   *string    `json:"department" binding:"omitempty,max=100"`       // 所属部门
	Phone        *string    `json:"phone" binding:"omitempty,max=50"`             // 电话
	Mobile       *string    `json:"mobile" binding:"omitempty,max=50"`            // 手机
	Email        *string    `json:"email" binding:"omitempty,email,max=255"`      // 邮箱
	QQ           *string    `json:"qq" binding:"omitempty,max=50"`                // QQ号
	Wechat       *string    `json:"wechat" binding:"omitempty,max=100"`           // 微信号
	Address      *string    `json:"address" binding:"omitempty"`                  // 联系地址
	PostalCode   *string    `json:"postalCode" binding:"omitempty,max=20"`        // 邮政编码
	Birthday     *time.Time `json:"birthday" binding:"omitempty"`                 // 生日
	Gender       *string    `json:"gender" binding:"omitempty,oneof=MALE FEMALE"` // 性别
	ContactRole  *string    `json:"contactRole" binding:"omitempty,max=50"`       // 联系人角色
	Remark       *string    `json:"remark" binding:"omitempty"`                   // 备注
}
