# VNBreadcrumb 组件

一个基于 Element Plus 的通用面包屑导航组件。

## Props

| Prop Name | Type               | Description                       | Default |
| --------- | ------------------ | --------------------------------- | ------- |
| `items`   | `BreadcrumbItem[]` | 面包屑项目配置数组，必须提供。 | `[]`    |

### BreadcrumbItem 接口

每个 `items` 数组中的对象应符合 `BreadcrumbItem` 接口：

```typescript
interface BreadcrumbItem {
  label: string; // 显示的文本
  to?: string | Record<string, any>; // 可选，vue-router 的 to prop，用于导航
  handler?: () => void; // 可选，点击事件处理函数（如果定义了 to，则 handler 通常不执行）
}
```

## 使用示例

```vue
<template>
  <div>
    <VNBreadcrumb :items="breadcrumbItems" />
    <!-- 其他页面内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNBreadcrumb from '@/components/VNBreadcrumb/index.vue'; // 确保路径正确
import type { BreadcrumbItem } from '@/components/VNBreadcrumb/types'; // 导入类型

const breadcrumbItems = ref<BreadcrumbItem[]>([
  { label: '首页', to: { name: 'Home' } }, // 使用命名路由
  { label: '用户管理', to: '/users' }, // 使用路径
  { label: '用户列表', handler: () => console.log('跳转到用户列表，但不改变路由') }, // 使用点击事件
  { label: '当前用户详情' } // 最后一项通常不设置 to 或 handler
]);
</script>
```

## 注意事项

* 组件依赖 `element-plus` 和 `@element-plus/icons-vue`。
* 如果同时提供了 `to` 和 `handler`，`el-breadcrumb-item` 会优先处理 `to` 进行路由导航。
* 组件的样式比较基础，可以通过 `style scoped` 或全局样式进行覆盖和定制。
* 建议在父组件或全局布局中为面包屑组件设置合适的 `margin` (例如 `margin-bottom`)。
