<template>
    <div class="validation-history" :class="{ compact: compact }">
      <!-- 搜索筛选区 -->
      <div class="history-filters" v-if="!hideFilters">
        <VNSearchForm
          :fields="filterFields"
          v-model="searchModel"
          @search="handleSearch"
          @reset="handleReset"
          :loading="loading"
          :compact="compact"
        />
      </div>
  
      <!-- 统计信息 -->
      <div class="history-stats" v-if="showStats && !compact">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic
              title="总验证次数"
              :value="stats.totalValidations"
              :value-style="{ color: '#409eff' }"
            >
              <template #suffix>次</template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="验证成功率"
              :value="stats.successRate"
              :precision="1"
              :value-style="{ color: '#67c23a' }"
            >
              <template #suffix>%</template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="平均响应时间"
              :value="stats.averageResponseTime"
              :precision="0"
              :value-style="{ color: '#e6a23c' }"
            >
              <template #suffix>ms</template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="异常验证数"
              :value="stats.exceptionCount"
              :value-style="{ color: '#f56c6c' }"
            >
              <template #suffix>次</template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>
  
      <!-- 历史记录表格 -->
      <div class="history-table">
        <VNTable
          ref="tableRef"
          :data="historyData"
          :columns="tableColumns"
          :loading="loading"
          :pagination="pagination"
          @sort-change="handleSortChange"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <!-- 验证时间列 -->
          <template #validationTime="{ row }">
            <div class="time-cell">
              <div class="date-time">{{ formatDateTime(row.createdAt) }}</div>
              <div class="relative-time">{{ getRelativeTime(row.createdAt) }}</div>
            </div>
          </template>
  
          <!-- 验证结果列 -->
          <template #validationResult="{ row }">
            <el-tag
              :type="row.isAllowed ? 'success' : 'danger'"
              size="small"
              effect="plain"
            >
              {{ row.isAllowed ? '允许' : '禁止' }}
            </el-tag>
          </template>
  
          <!-- 策略列 -->
          <template #strategy="{ row }">
            <el-tag
              :type="getStrategyTagType(row.strategy)"
              size="small"
              effect="plain"
            >
              {{ getStrategyName(row.strategy) }}
            </el-tag>
          </template>
  
          <!-- 验证参数列 -->
          <template #validationParams="{ row }">
            <div class="params-cell">
              <div class="param-item" v-if="row.warehouseName">
                <span class="param-label">仓库:</span>
                <span class="param-value">{{ row.warehouseName }}</span>
              </div>
              <div class="param-item" v-if="row.clientName">
                <span class="param-label">客户:</span>
                <span class="param-value">{{ row.clientName }}</span>
              </div>
              <div class="param-item" v-if="row.requestQuantity">
                <span class="param-label">数量:</span>
                <span class="param-value">{{ formatNumber(row.requestQuantity) }}</span>
              </div>
              <div class="param-item" v-if="row.itemName">
                <span class="param-label">商品:</span>
                <span class="param-value">{{ row.itemName }}</span>
              </div>
            </div>
          </template>
  
          <!-- 审批状态列 -->
          <template #approvalStatus="{ row }">
            <el-tag
              v-if="row.approvalStatus"
              :type="getApprovalStatusTagType(row.approvalStatus)"
              size="small"
              effect="plain"
            >
              {{ getApprovalStatusName(row.approvalStatus) }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
  
          <!-- 补录状态列 -->
          <template #supplementStatus="{ row }">
            <el-tag
              v-if="row.supplementStatus"
              :type="getSupplementStatusTagType(row.supplementStatus)"
              size="small"
              effect="plain"
            >
              {{ getSupplementStatusName(row.supplementStatus) }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
  
          <!-- 操作列 -->
          <template #actions="{ row }">
            <el-button-group>
              <el-button
                type="primary"
                :icon="View"
                size="small"
                @click="handleViewDetail(row)"
              >
                详情
              </el-button>
              <el-button
                type="success"
                :icon="Refresh"
                size="small"
                @click="handleRetryValidation(row)"
                :disabled="!canRetry(row)"
              >
                重试
              </el-button>
              <el-button
                type="danger"
                :icon="Delete"
                size="small"
                @click="handleDeleteRecord(row)"
                v-if="hasPermission('wms:blind-validation:delete')"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </VNTable>
      </div>
  
      <!-- 详情对话框 -->
      <el-dialog
        v-model="showDetailDialog"
        title="验证详情"
        width="800px"
        destroy-on-close
      >
        <ValidationDetail
          v-if="currentRecord"
          :record="currentRecord"
        />
      </el-dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { View, Refresh, Delete } from '@element-plus/icons-vue'
  import VNSearchForm from '@/components/VNSearchForm/index.vue'
  import VNTable from '@/components/VNTable/index.vue'
  import ValidationDetail from './ValidationDetail.vue'
  import { usePermission } from '@/hooks/usePermission'
  import {
    getBlindReceivingValidationHistory,
    deleteBlindReceivingValidationRecord,
    getBlindReceivingValidationStats
  } from '@/api/wms/blindReceivingConfig'
  import type {
    BlindReceivingValidationVO,
    BlindReceivingStrategy,
    ApprovalStatus,
    SupplementStatus
  } from '@/types/wms/blindReceivingConfig'
  import {
    STRATEGY_OPTIONS,
    APPROVAL_STATUS_OPTIONS,
    SUPPLEMENT_STATUS_OPTIONS
  } from '@/types/wms/blindReceivingConfig'
  
  // Props
  interface Props {
    filters?: Record<string, any>
    compact?: boolean
    hideFilters?: boolean
    showStats?: boolean
    maxHeight?: string
  }
  
  const props = withDefaults(defineProps<Props>(), {
    filters: () => ({}),
    compact: false,
    hideFilters: false,
    showStats: true
  })
  
  // Emits
  const emit = defineEmits<{
    'retry-validation': [record: BlindReceivingValidationVO]
    'record-deleted': [id: number]
  }>()
  
  // 权限检查
  const { hasPermission } = usePermission()
  
  // 响应式数据
  const loading = ref(false)
  const historyData = ref<BlindReceivingValidationVO[]>([])
  const currentRecord = ref<BlindReceivingValidationVO | null>(null)
  const showDetailDialog = ref(false)
  
  // 搜索模型
  const searchModel = reactive({
    warehouseId: undefined,
    clientId: undefined,
    userId: undefined,
    strategy: undefined,
    isAllowed: undefined,
    approvalStatus: undefined,
    supplementStatus: undefined,
    dateRange: undefined,
    keyword: ''
  })
  
  // 分页
  const pagination = reactive({
    pageNum: 1,
    pageSize: 20,
    total: 0
  })
  
  // 统计数据
  const stats = ref({
    totalValidations: 0,
    successRate: 0,
    averageResponseTime: 0,
    exceptionCount: 0
  })
  
  // 搜索字段配置
  const filterFields = computed(() => [
    {
      field: 'strategy',
      label: '验证策略',
      type: 'select' as const,
      options: STRATEGY_OPTIONS.map(item => ({
        label: item.label,
        value: item.value
      })),
      placeholder: '请选择验证策略'
    },
    {
      field: 'isAllowed',
      label: '验证结果',
      type: 'select' as const,
      options: [
        { label: '允许', value: true },
        { label: '禁止', value: false }
      ],
      placeholder: '请选择验证结果'
    },
    {
      field: 'approvalStatus',
      label: '审批状态',
      type: 'select' as const,
      options: APPROVAL_STATUS_OPTIONS.map(item => ({
        label: item.label,
        value: item.value
      })),
      placeholder: '请选择审批状态'
    },
    {
      field: 'supplementStatus',
      label: '补录状态',
      type: 'select' as const,
      options: SUPPLEMENT_STATUS_OPTIONS.map(item => ({
        label: item.label,
        value: item.value
      })),
      placeholder: '请选择补录状态'
    },
    {
      field: 'dateRange',
      label: '验证时间',
      type: 'daterange' as const,
      placeholder: '请选择时间范围'
    },
    {
      field: 'keyword',
      label: '关键词',
      type: 'input' as const,
      placeholder: '请输入用户名、仓库名或备注'
    }
  ])
  
  // 表格列配置
  const tableColumns = computed(() => [
    {
      prop: 'validationTime',
      label: '验证时间',
      width: 180,
      sortable: true,
      slot: 'validationTime'
    },
    {
      prop: 'userName',
      label: '验证用户',
      width: 120
    },
    {
      prop: 'validationParams',
      label: '验证参数',
      width: 200,
      slot: 'validationParams'
    },
    {
      prop: 'strategy',
      label: '策略',
      width: 100,
      slot: 'strategy'
    },
    {
      prop: 'validationResult',
      label: '结果',
      width: 80,
      slot: 'validationResult'
    },
    {
      prop: 'approvalStatus',
      label: '审批状态',
      width: 100,
      slot: 'approvalStatus'
    },
    {
      prop: 'supplementStatus',
      label: '补录状态',
      width: 100,
      slot: 'supplementStatus'
    },
    {
      prop: 'validationMessage',
      label: '验证信息',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'actions',
      label: '操作',
      width: 200,
      fixed: 'right',
      slot: 'actions'
    }
  ])
  
  // 工具函数
  const getStrategyName = (strategy: BlindReceivingStrategy) => {
    return STRATEGY_OPTIONS.find(item => item.value === strategy)?.label || strategy
  }
  
  const getStrategyTagType = (strategy: BlindReceivingStrategy) => {
    const typeMap = {
      'STRICT': 'danger',
      'SUPPLEMENT': 'warning',
      'FULL': 'success'
    }
    return typeMap[strategy] || 'info'
  }
  
  const getApprovalStatusName = (status: ApprovalStatus) => {
    return APPROVAL_STATUS_OPTIONS.find(item => item.value === status)?.label || status
  }
  
  const getApprovalStatusTagType = (status: ApprovalStatus) => {
    return APPROVAL_STATUS_OPTIONS.find(item => item.value === status)?.color || 'info'
  }
  
  const getSupplementStatusName = (status: SupplementStatus) => {
    return SUPPLEMENT_STATUS_OPTIONS.find(item => item.value === status)?.label || status
  }
  
  const getSupplementStatusTagType = (status: SupplementStatus) => {
    return SUPPLEMENT_STATUS_OPTIONS.find(item => item.value === status)?.color || 'info'
  }
  
  const formatNumber = (num: number) => {
    return num.toLocaleString()
  }
  
  const formatDateTime = (dateStr: string) => {
    return new Date(dateStr).toLocaleString()
  }
  
  const getRelativeTime = (dateStr: string) => {
    const now = new Date()
    const date = new Date(dateStr)
    const diff = now.getTime() - date.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days > 0) return `${days}天前`
    if (hours > 0) return `${hours}小时前`
    if (minutes > 0) return `${minutes}分钟前`
    return '刚刚'
  }
  
  const canRetry = (record: BlindReceivingValidationVO) => {
    // 只有失败的验证才能重试
    return !record.isAllowed
  }
  
  // 数据加载
  const loadData = async () => {
    try {
      loading.value = true
      
      const params = {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        ...searchModel,
        ...props.filters,
        dateFrom: searchModel.dateRange?.[0],
        dateTo: searchModel.dateRange?.[1]
      }
      
      const result = await getBlindReceivingValidationHistory(params)
      historyData.value = result.list
      pagination.total = result.total
    } catch (error) {
      console.error('加载验证历史失败:', error)
      ElMessage.error('加载验证历史失败')
    } finally {
      loading.value = false
    }
  }
  
  const loadStats = async () => {
    try {
      const params = {
        ...searchModel,
        ...props.filters,
        dateFrom: searchModel.dateRange?.[0],
        dateTo: searchModel.dateRange?.[1]
      }
      
      const result = await getBlindReceivingValidationStats(params)
      stats.value = result
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }
  
  // 事件处理
  const handleSearch = () => {
    pagination.pageNum = 1
    loadData()
    if (props.showStats) {
      loadStats()
    }
  }
  
  const handleReset = () => {
    Object.keys(searchModel).forEach(key => {
      searchModel[key as keyof typeof searchModel] = undefined
    })
    handleSearch()
  }
  
  const handleSortChange = (sort: { prop: string; order: string }) => {
    // 处理排序
    loadData()
  }
  
  const handlePageChange = (page: number) => {
    pagination.pageNum = page
    loadData()
  }
  
  const handlePageSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    loadData()
  }
  
  const handleViewDetail = (record: BlindReceivingValidationVO) => {
    currentRecord.value = record
    showDetailDialog.value = true
  }
  
  const handleRetryValidation = (record: BlindReceivingValidationVO) => {
    emit('retry-validation', record)
  }
  
  const handleDeleteRecord = async (record: BlindReceivingValidationVO) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除这条验证记录吗？`,
        '确认删除',
        {
          type: 'warning'
        }
      )
      
      await deleteBlindReceivingValidationRecord(record.id!)
      ElMessage.success('删除成功')
      emit('record-deleted', record.id!)
      loadData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }
  
  // 监听外部筛选条件变化
  watch(
    () => props.filters,
    () => {
      loadData()
      if (props.showStats) {
        loadStats()
      }
    },
    { deep: true }
  )
  
  // 初始化
  onMounted(() => {
    loadData()
    if (props.showStats) {
      loadStats()
    }
  })
  </script>
  
  <style scoped lang="scss">
  .validation-history {
    .history-filters {
      margin-bottom: 20px;
    }
    
    .history-stats {
      margin-bottom: 20px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 6px;
    }
    
    .history-table {
      .time-cell {
        .date-time {
          font-weight: 500;
          color: #303133;
        }
        
        .relative-time {
          font-size: 12px;
          color: #909399;
          margin-top: 2px;
        }
      }
      
      .params-cell {
        .param-item {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 2px;
          font-size: 13px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .param-label {
            color: #909399;
            min-width: 30px;
          }
          
          .param-value {
            color: #303133;
            font-weight: 500;
          }
        }
      }
    }
    
    .text-muted {
      color: #c0c4cc;
    }
    
    // 紧凑模式
    &.compact {
      .history-filters {
        margin-bottom: 16px;
      }
      
      .history-stats {
        margin-bottom: 16px;
        padding: 16px;
      }
      
      .history-table {
        :deep(.el-table) {
          font-size: 13px;
        }
        
        .time-cell {
          .date-time {
            font-size: 13px;
          }
          
          .relative-time {
            font-size: 11px;
          }
        }
        
        .params-cell .param-item {
          font-size: 12px;
          
          .param-label {
            min-width: 25px;
          }
        }
      }
    }
  }
  </style>