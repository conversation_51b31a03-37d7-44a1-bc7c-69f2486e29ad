package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsCycleCountTaskRepository 盘点任务数据访问接口
type WmsCycleCountTaskRepository interface {
	BaseRepository[entity.WmsCycleCountTask, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsCycleCountTaskQueryReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByTaskNo(ctx context.Context, taskNo string) (*entity.WmsCycleCountTask, error)
	FindByPlanID(ctx context.Context, planId uint) ([]*entity.WmsCycleCountTask, error)
	FindByLocationID(ctx context.Context, locationId uint) ([]*entity.WmsCycleCountTask, error)
	FindByItemID(ctx context.Context, itemId uint) ([]*entity.WmsCycleCountTask, error)
	FindByStatus(ctx context.Context, status entity.WmsCycleCountTaskStatus) ([]*entity.WmsCycleCountTask, error)
	FindByCounterUser(ctx context.Context, userId uint) ([]*entity.WmsCycleCountTask, error)
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsCycleCountTask, error)

	// 复杂查询
	FindPendingTasks(ctx context.Context, planId *uint, locationId *uint) ([]*entity.WmsCycleCountTask, error)
	FindTasksWithVariance(ctx context.Context, planId *uint, minVariancePercent float64) ([]*entity.WmsCycleCountTask, error)
	FindCompletedTasks(ctx context.Context, planId uint) ([]*entity.WmsCycleCountTask, error)
	FindTasksNeedingAdjustment(ctx context.Context, planId *uint) ([]*entity.WmsCycleCountTask, error)
	FindTasksByBatch(ctx context.Context, batchNo string) ([]*entity.WmsCycleCountTask, error)

	// 统计查询
	CountByStatus(ctx context.Context, status entity.WmsCycleCountTaskStatus) (int64, error)
	CountByPlan(ctx context.Context, planId uint) (int64, error)
	CountByCounterUser(ctx context.Context, userId uint, startDate, endDate time.Time) (int64, error)
	CountTasksWithVariance(ctx context.Context, planId uint) (int64, error)
	GetVarianceStatistics(ctx context.Context, planId uint) (map[string]interface{}, error)

	// 批量操作
	BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsCycleCountTaskStatus) error
	BatchAssignCounter(ctx context.Context, ids []uint, counterUserId uint) error
	BatchConfirmVariance(ctx context.Context, ids []uint, reason string) error
	BatchCreateFromPlan(planId uint, tasks []entity.WmsCycleCountTask) error

	// 事务支持
	BeginTx() *gorm.DB
}

// WmsCycleCountTaskQueryBuilder 盘点任务查询构建器接口
type WmsCycleCountTaskQueryBuilder interface {
	// 条件过滤
	WhereTaskNo(taskNo string) WmsCycleCountTaskQueryBuilder
	WherePlanID(planId uint) WmsCycleCountTaskQueryBuilder
	WhereLocationID(locationId uint) WmsCycleCountTaskQueryBuilder
	WhereItemID(itemId uint) WmsCycleCountTaskQueryBuilder
	WhereStatus(status entity.WmsCycleCountTaskStatus) WmsCycleCountTaskQueryBuilder
	WhereCounterUser(userId uint) WmsCycleCountTaskQueryBuilder
	WhereBatchNo(batchNo string) WmsCycleCountTaskQueryBuilder
	WhereVarianceRange(minPercent, maxPercent float64) WmsCycleCountTaskQueryBuilder
	WhereCountedDateRange(startDate, endDate time.Time) WmsCycleCountTaskQueryBuilder
	WhereCreatedDateRange(startDate, endDate time.Time) WmsCycleCountTaskQueryBuilder
	WhereAdjustmentRequired(required bool) WmsCycleCountTaskQueryBuilder

	// 关联查询
	WithPlan() WmsCycleCountTaskQueryBuilder
	WithLocation() WmsCycleCountTaskQueryBuilder
	WithItem() WmsCycleCountTaskQueryBuilder
	WithCounter() WmsCycleCountTaskQueryBuilder
	WithAdjustment() WmsCycleCountTaskQueryBuilder

	// 排序
	OrderByCreatedAt(desc bool) WmsCycleCountTaskQueryBuilder
	OrderByCountedAt(desc bool) WmsCycleCountTaskQueryBuilder
	OrderByVariancePercentage(desc bool) WmsCycleCountTaskQueryBuilder
	OrderByTaskNo(desc bool) WmsCycleCountTaskQueryBuilder

	// 执行查询
	Find() ([]entity.WmsCycleCountTask, error)
	First() (*entity.WmsCycleCountTask, error)
	Count() (int64, error)
	Paginate(pageNum, pageSize int) ([]entity.WmsCycleCountTask, int64, error)
}

// WmsCycleCountTaskRepositoryImpl 盘点任务数据访问实现
type WmsCycleCountTaskRepositoryImpl struct {
	BaseRepositoryImpl[entity.WmsCycleCountTask, uint]
}

// NewWmsCycleCountTaskRepository 创建盘点任务数据访问实例
func NewWmsCycleCountTaskRepository(db *gorm.DB) WmsCycleCountTaskRepository {
	return &WmsCycleCountTaskRepositoryImpl{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.WmsCycleCountTask, uint]{
			db: db,
		},
	}
}

// GetPage 分页查询盘点任务
func (r *WmsCycleCountTaskRepositoryImpl) GetPage(ctx context.Context, query *dto.WmsCycleCountTaskQueryReq) (*response.PageResult, error) {
	conditions := []QueryCondition{}

	// 应用查询条件
	if query.TaskNo != nil && *query.TaskNo != "" {
		conditions = append(conditions, NewLikeCondition("task_no", *query.TaskNo))
	}
	if query.PlanID != nil {
		conditions = append(conditions, NewEqualCondition("plan_id", *query.PlanID))
	}
	if query.LocationID != nil {
		conditions = append(conditions, NewEqualCondition("location_id", *query.LocationID))
	}
	if query.ItemID != nil {
		conditions = append(conditions, NewEqualCondition("item_id", *query.ItemID))
	}
	if query.ItemSku != nil && *query.ItemSku != "" {
		conditions = append(conditions, NewLikeCondition("item_sku", *query.ItemSku))
	}
	if query.BatchNo != nil && *query.BatchNo != "" {
		conditions = append(conditions, NewLikeCondition("batch_no", *query.BatchNo))
	}
	if query.Status != nil {
		conditions = append(conditions, NewEqualCondition("status", *query.Status))
	}
	if query.CounterUserID != nil {
		conditions = append(conditions, NewEqualCondition("counter_user_id", *query.CounterUserID))
	}
	if query.HasVariance != nil {
		if *query.HasVariance {
			conditions = append(conditions, NewNotEqualCondition("variance_quantity", 0))
		} else {
			conditions = append(conditions, NewEqualCondition("variance_quantity", 0))
		}
	}
	if query.CreatedStart != nil {
		conditions = append(conditions, NewGreaterThanEqualCondition("created_at", *query.CreatedStart))
	}
	if query.CreatedEnd != nil {
		conditions = append(conditions, NewLessThanEqualCondition("created_at", *query.CreatedEnd))
	}

	// 使用BaseRepository的分页查询
	return r.BaseRepositoryImpl.FindByPage(ctx, &query.PageQuery, conditions)
}

// CreateWithTx 在事务中创建盘点任务
func (r *WmsCycleCountTaskRepositoryImpl) CreateWithTx(tx *gorm.DB, task *entity.WmsCycleCountTask) error {
	return tx.Create(task).Error
}

// UpdateWithTx 在事务中更新盘点任务
func (r *WmsCycleCountTaskRepositoryImpl) UpdateWithTx(tx *gorm.DB, task *entity.WmsCycleCountTask) error {
	return tx.Save(task).Error
}

// GetByID 根据ID获取盘点任务
func (r *WmsCycleCountTaskRepositoryImpl) GetByID(id uint) (*entity.WmsCycleCountTask, error) {
	var task entity.WmsCycleCountTask
	err := r.db.First(&task, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &task, nil
}

// GetByIDWithAssociations 根据ID获取盘点任务（包含关联数据）
func (r *WmsCycleCountTaskRepositoryImpl) GetByIDWithAssociations(id uint) (*entity.WmsCycleCountTask, error) {
	var task entity.WmsCycleCountTask
	err := r.db.Preload("Plan").
		Preload("Location").
		Preload("Item").
		Preload("Counter").
		Preload("Adjustment").
		First(&task, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &task, nil
}

// FindByTaskNo 根据任务编号查询
func (r *WmsCycleCountTaskRepositoryImpl) FindByTaskNo(ctx context.Context, taskNo string) (*entity.WmsCycleCountTask, error) {
	var task entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("task_no = ?", taskNo).First(&task).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &task, nil
}

// FindByPlanID 根据计划ID查询盘点任务
func (r *WmsCycleCountTaskRepositoryImpl) FindByPlanID(ctx context.Context, planId uint) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("plan_id = ?", planId).
		Order("created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// FindByLocationID 根据库位ID查询盘点任务
func (r *WmsCycleCountTaskRepositoryImpl) FindByLocationID(ctx context.Context, locationId uint) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("location_id = ?", locationId).
		Order("created_at DESC").
		Find(&tasks).Error
	return tasks, err
}

// FindPendingTasks 查找待盘点的任务
func (r *WmsCycleCountTaskRepositoryImpl) FindPendingTasks(ctx context.Context, planId *uint, locationId *uint) ([]*entity.WmsCycleCountTask, error) {
	query := r.GetDB(ctx).Where("status = ?", entity.CountTaskStatusPending)

	if planId != nil {
		query = query.Where("plan_id = ?", *planId)
	}

	if locationId != nil {
		query = query.Where("location_id = ?", *locationId)
	}

	var tasks []*entity.WmsCycleCountTask
	err := query.Preload("Plan").
		Preload("Location").
		Preload("Item").
		Order("created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// FindTasksWithVariance 查找有差异的任务
func (r *WmsCycleCountTaskRepositoryImpl) FindTasksWithVariance(ctx context.Context, planId *uint, minVariancePercent float64) ([]*entity.WmsCycleCountTask, error) {
	query := r.GetDB(ctx).Where("variance_percentage IS NOT NULL AND ABS(variance_percentage) >= ?", minVariancePercent)

	if planId != nil {
		query = query.Where("plan_id = ?", *planId)
	}

	var tasks []*entity.WmsCycleCountTask
	err := query.Preload("Plan").
		Preload("Location").
		Preload("Item").
		Order("ABS(variance_percentage) DESC").
		Find(&tasks).Error
	return tasks, err
}

// BeginTx 开始事务
func (r *WmsCycleCountTaskRepositoryImpl) BeginTx() *gorm.DB {
	return r.db.Begin()
}

// NewQueryBuilder 创建查询构建器
func (r *WmsCycleCountTaskRepositoryImpl) NewQueryBuilder() WmsCycleCountTaskQueryBuilder {
	return &WmsCycleCountTaskQueryBuilderImpl{
		db:    r.db,
		query: r.db.Model(&entity.WmsCycleCountTask{}),
	}
}

// 实现其他缺失的方法
func (r *WmsCycleCountTaskRepositoryImpl) FindByItemID(ctx context.Context, itemId uint) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("item_id = ?", itemId).
		Order("created_at DESC").
		Find(&tasks).Error
	return tasks, err
}

func (r *WmsCycleCountTaskRepositoryImpl) FindByStatus(ctx context.Context, status entity.WmsCycleCountTaskStatus) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("status = ?", status).
		Order("created_at DESC").
		Find(&tasks).Error
	return tasks, err
}

func (r *WmsCycleCountTaskRepositoryImpl) FindByCounterUser(ctx context.Context, userId uint) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("counter_user_id = ?", userId).
		Order("created_at DESC").
		Find(&tasks).Error
	return tasks, err
}

func (r *WmsCycleCountTaskRepositoryImpl) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Order("created_at DESC").
		Find(&tasks).Error
	return tasks, err
}

func (r *WmsCycleCountTaskRepositoryImpl) FindCompletedTasks(ctx context.Context, planId uint) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("plan_id = ? AND status = ?", planId, entity.CountTaskStatusCompleted).
		Preload("Location").
		Preload("Item").
		Order("counted_at DESC").
		Find(&tasks).Error
	return tasks, err
}

func (r *WmsCycleCountTaskRepositoryImpl) FindTasksNeedingAdjustment(ctx context.Context, planId *uint) ([]*entity.WmsCycleCountTask, error) {
	query := r.GetDB(ctx).Where("adjustment_required = ?", true)

	if planId != nil {
		query = query.Where("plan_id = ?", *planId)
	}

	var tasks []*entity.WmsCycleCountTask
	err := query.Preload("Plan").
		Preload("Location").
		Preload("Item").
		Order("created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

func (r *WmsCycleCountTaskRepositoryImpl) FindTasksByBatch(ctx context.Context, batchNo string) ([]*entity.WmsCycleCountTask, error) {
	var tasks []*entity.WmsCycleCountTask
	err := r.GetDB(ctx).Where("batch_no = ?", batchNo).
		Order("created_at DESC").
		Find(&tasks).Error
	return tasks, err
}

func (r *WmsCycleCountTaskRepositoryImpl) CountByStatus(ctx context.Context, status entity.WmsCycleCountTaskStatus) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

func (r *WmsCycleCountTaskRepositoryImpl) CountByPlan(ctx context.Context, planId uint) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).Where("plan_id = ?", planId).Count(&count).Error
	return count, err
}

func (r *WmsCycleCountTaskRepositoryImpl) CountByCounterUser(ctx context.Context, userId uint, startDate, endDate time.Time) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).
		Where("counter_user_id = ? AND created_at BETWEEN ? AND ?", userId, startDate, endDate).
		Count(&count).Error
	return count, err
}

func (r *WmsCycleCountTaskRepositoryImpl) CountTasksWithVariance(ctx context.Context, planId uint) (int64, error) {
	var count int64
	err := r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).
		Where("plan_id = ? AND variance_quantity IS NOT NULL AND variance_quantity != 0", planId).
		Count(&count).Error
	return count, err
}

func (r *WmsCycleCountTaskRepositoryImpl) GetVarianceStatistics(ctx context.Context, planId uint) (map[string]interface{}, error) {
	var result struct {
		TotalTasks         int64   `json:"totalTasks"`
		CompletedTasks     int64   `json:"completedTasks"`
		TasksWithVariance  int64   `json:"tasksWithVariance"`
		TotalVarianceQty   float64 `json:"totalVarianceQty"`
		AvgVariancePercent float64 `json:"avgVariancePercent"`
	}

	// 获取总任务数
	r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).Where("plan_id = ?", planId).Count(&result.TotalTasks)

	// 获取已完成任务数
	r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).
		Where("plan_id = ? AND status = ?", planId, entity.CountTaskStatusCompleted).
		Count(&result.CompletedTasks)

	// 获取有差异的任务数
	r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).
		Where("plan_id = ? AND variance_quantity IS NOT NULL AND variance_quantity != 0", planId).
		Count(&result.TasksWithVariance)

	// 获取总差异数量和平均差异百分比
	r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).
		Where("plan_id = ? AND variance_quantity IS NOT NULL", planId).
		Select("COALESCE(SUM(variance_quantity), 0) as total_variance_qty, COALESCE(AVG(variance_percentage), 0) as avg_variance_percent").
		Scan(&result)

	return map[string]interface{}{
		"totalTasks":         result.TotalTasks,
		"completedTasks":     result.CompletedTasks,
		"tasksWithVariance":  result.TasksWithVariance,
		"totalVarianceQty":   result.TotalVarianceQty,
		"avgVariancePercent": result.AvgVariancePercent,
	}, nil
}

func (r *WmsCycleCountTaskRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsCycleCountTaskStatus) error {
	return r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).Where("id IN ?", ids).Update("status", status).Error
}

func (r *WmsCycleCountTaskRepositoryImpl) BatchAssignCounter(ctx context.Context, ids []uint, counterUserId uint) error {
	return r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).Where("id IN ?", ids).Update("counter_user_id", counterUserId).Error
}

func (r *WmsCycleCountTaskRepositoryImpl) BatchConfirmVariance(ctx context.Context, ids []uint, reason string) error {
	return r.GetDB(ctx).Model(&entity.WmsCycleCountTask{}).Where("id IN ?", ids).Updates(map[string]interface{}{
		"status":          entity.CountTaskStatusVarianceConfirmed,
		"variance_reason": reason,
	}).Error
}

func (r *WmsCycleCountTaskRepositoryImpl) BatchCreateFromPlan(planId uint, tasks []entity.WmsCycleCountTask) error {
	return r.db.CreateInBatches(tasks, 100).Error
}

// WmsCycleCountTaskQueryBuilderImpl 盘点任务查询构建器实现
type WmsCycleCountTaskQueryBuilderImpl struct {
	db    *gorm.DB
	query *gorm.DB
}

// WherePlanID 按计划ID过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WherePlanID(planId uint) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("plan_id = ?", planId)
	return b
}

// WhereTaskNo 按任务编号过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereTaskNo(taskNo string) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("task_no LIKE ?", "%"+taskNo+"%")
	return b
}

// WhereLocationID 按库位ID过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereLocationID(locationId uint) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("location_id = ?", locationId)
	return b
}

// WhereItemID 按物料ID过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereItemID(itemId uint) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("item_id = ?", itemId)
	return b
}

// WhereStatus 按状态过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereStatus(status entity.WmsCycleCountTaskStatus) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("status = ?", status)
	return b
}

// WhereCounterUserID 按盘点人ID过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereCounterUserID(userId uint) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("counter_user_id = ?", userId)
	return b
}

// WhereHasVariance 按是否有差异过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereHasVariance(hasVariance bool) WmsCycleCountTaskQueryBuilder {
	if hasVariance {
		b.query = b.query.Where("variance_quantity != 0")
	} else {
		b.query = b.query.Where("variance_quantity = 0 OR variance_quantity IS NULL")
	}
	return b
}

// WhereCreatedDateRange 按创建时间范围过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereCreatedDateRange(startDate, endDate time.Time) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereCounterUser 按盘点人过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereCounterUser(userId uint) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("counter_user_id = ?", userId)
	return b
}

// WhereBatchNo 按批次号过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereBatchNo(batchNo string) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("batch_no LIKE ?", "%"+batchNo+"%")
	return b
}

// WhereVarianceRange 按差异百分比范围过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereVarianceRange(minPercent, maxPercent float64) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("variance_percentage BETWEEN ? AND ?", minPercent, maxPercent)
	return b
}

// WhereCountedDateRange 按盘点时间范围过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereCountedDateRange(startDate, endDate time.Time) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("counted_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereAdjustmentRequired 按是否需要调整过滤
func (b *WmsCycleCountTaskQueryBuilderImpl) WhereAdjustmentRequired(required bool) WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Where("adjustment_required = ?", required)
	return b
}

// WithPlan 预加载计划信息
func (b *WmsCycleCountTaskQueryBuilderImpl) WithPlan() WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Preload("Plan")
	return b
}

// WithLocation 预加载库位信息
func (b *WmsCycleCountTaskQueryBuilderImpl) WithLocation() WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Preload("Location")
	return b
}

// WithItem 预加载物料信息
func (b *WmsCycleCountTaskQueryBuilderImpl) WithItem() WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Preload("Item")
	return b
}

// WithCounterUser 预加载盘点人信息
func (b *WmsCycleCountTaskQueryBuilderImpl) WithCounterUser() WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Preload("CounterUser")
	return b
}

// WithCounter 预加载盘点人信息（别名）
func (b *WmsCycleCountTaskQueryBuilderImpl) WithCounter() WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Preload("CounterUser")
	return b
}

// WithAdjustment 预加载调整信息
func (b *WmsCycleCountTaskQueryBuilderImpl) WithAdjustment() WmsCycleCountTaskQueryBuilder {
	b.query = b.query.Preload("Adjustment")
	return b
}

// OrderByCreatedAt 按创建时间排序
func (b *WmsCycleCountTaskQueryBuilderImpl) OrderByCreatedAt(desc bool) WmsCycleCountTaskQueryBuilder {
	if desc {
		b.query = b.query.Order("created_at DESC")
	} else {
		b.query = b.query.Order("created_at ASC")
	}
	return b
}

// OrderByTaskNo 按任务编号排序
func (b *WmsCycleCountTaskQueryBuilderImpl) OrderByTaskNo(desc bool) WmsCycleCountTaskQueryBuilder {
	if desc {
		b.query = b.query.Order("task_no DESC")
	} else {
		b.query = b.query.Order("task_no ASC")
	}
	return b
}

// OrderByCountedAt 按盘点时间排序
func (b *WmsCycleCountTaskQueryBuilderImpl) OrderByCountedAt(desc bool) WmsCycleCountTaskQueryBuilder {
	if desc {
		b.query = b.query.Order("counted_at DESC")
	} else {
		b.query = b.query.Order("counted_at ASC")
	}
	return b
}

// OrderByVariancePercentage 按差异百分比排序
func (b *WmsCycleCountTaskQueryBuilderImpl) OrderByVariancePercentage(desc bool) WmsCycleCountTaskQueryBuilder {
	if desc {
		b.query = b.query.Order("variance_percentage DESC")
	} else {
		b.query = b.query.Order("variance_percentage ASC")
	}
	return b
}

// Find 执行查询
func (b *WmsCycleCountTaskQueryBuilderImpl) Find() ([]entity.WmsCycleCountTask, error) {
	var tasks []entity.WmsCycleCountTask
	err := b.query.Find(&tasks).Error
	return tasks, err
}

// First 查询第一条记录
func (b *WmsCycleCountTaskQueryBuilderImpl) First() (*entity.WmsCycleCountTask, error) {
	var task entity.WmsCycleCountTask
	err := b.query.First(&task).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &task, nil
}

// Count 统计数量
func (b *WmsCycleCountTaskQueryBuilderImpl) Count() (int64, error) {
	var count int64
	err := b.query.Count(&count).Error
	return count, err
}

// Paginate 分页查询
func (b *WmsCycleCountTaskQueryBuilderImpl) Paginate(pageNum, pageSize int) ([]entity.WmsCycleCountTask, int64, error) {
	var tasks []entity.WmsCycleCountTask
	var total int64

	// 先统计总数
	countQuery := b.db.Model(&entity.WmsCycleCountTask{}).Where(b.query.Statement.SQL.String())
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (pageNum - 1) * pageSize
	err := b.query.Offset(offset).Limit(pageSize).Find(&tasks).Error
	return tasks, total, err
}
