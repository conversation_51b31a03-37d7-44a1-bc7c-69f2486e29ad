<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      show-index
      row-key="id"
      :selection-type="'multiple'"
      highlight-current-row
      operation-fixed="right"
      :operation-width="200"
      @refresh="loadData"
      @add="handleAdd"
      @batch-delete="handleBatchDelete"
      @filter-change="handleFilterChange"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <template #column-businessType="{ row }">
        <el-tag :type="getBusinessTypeTagType(row.businessType)">
          {{ getBusinessTypeLabel(row.businessType) }}
        </el-tag>
      </template>

      <template #column-status="{ row }">
        <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
          {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <template #column-isDefault="{ row }">
        <el-tag :type="row.isDefault ? 'warning' : 'info'">
          {{ row.isDefault ? '是' : '否' }}
        </el-tag>
      </template>

      <template #column-resetFrequency="{ row }">
        {{ getResetFrequencyLabel(row.resetFrequency) }}
      </template>

      <template #operation="{ row }">
        <el-tooltip content="查看" placement="top" v-if="hasPermission('sys:code-rule:list')">
          <el-button circle :icon="View" size="small" @click="handleView(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="设为默认" placement="top" v-if="!row.isDefault && hasPermission('sys:code-rule:setdefault')">
          <el-button circle :icon="Star" type="warning" size="small" @click="handleSetDefault(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top" v-if="hasPermission('sys:code-rule:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEdit(row)"></el-button>
        </el-tooltip>
        <el-tooltip content="删除" placement="top" v-if="hasPermission('sys:code-rule:delete')">
          <el-button circle :icon="Delete" type="danger" size="small" @click="handleDelete(row)"></el-button>
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="70%"
      align-center
      top="5vh"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="4"
        :label-width="'120px'"
        :loading="loading"
        group-title-type="h4"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >


        <template #form-item-sequenceLength="{ field, formData: formModel }">
          <el-input-number
            v-model="formModel[field.field]"
            :min="1"
            :max="10"
            style="width: 100%"
            :disabled="isViewing"
          />
          <div class="form-help-text" v-if="!isViewing">
            <el-text size="small" type="info">
              <el-icon><InfoFilled /></el-icon>
              与编码格式中的 {SEQ:n} 保持一致
            </el-text>
          </div>
        </template>

        <template #form-item-sequenceStart="{ field, formData: formModel }">
          <el-input-number
            v-model="formModel[field.field]"
            :min="0"
            style="width: 100%"
            :disabled="isViewing"
          />
          <div class="form-help-text" v-if="!isViewing">
            <el-text size="small" type="info">
              <el-icon><InfoFilled /></el-icon>
              序号重置时的起始值
            </el-text>
          </div>
        </template>

        <template #actions>
          <template v-if="isViewing">
            <el-button
              v-if="hasPermission('sys:code-rule:printpreview')"
              :icon="View"
              @click="handlePrintPreview"
            >
              打印预览
            </el-button>
            <el-button
              v-if="hasPermission('sys:code-rule:print')"
              type="primary"
              :icon="Printer"
              @click="handlePrint"
            >
              打印
            </el-button>
            <el-button @click="handleCloseDialog">关闭</el-button>
          </template>
          <template v-else>
            <div class="action-with-preview">
              <div class="preview-section">
                <el-button
                  v-if="hasPermission('sys:code-rule:preview')"
                  type="info"
                  size="small"
                  @click="handlePreviewCode"
                  :loading="previewLoading"
                >
                  预览效果
                </el-button>
                <span v-if="previewResult" class="preview-result">
                  预览：{{ previewResult }}
                </span>
              </div>
              <div class="action-buttons">
                <el-button 
                  type="primary" 
                  @click="submitForm" 
                  :loading="loading"
                  v-if="(formMode === 'add' && hasPermission('sys:code-rule:add')) || (formMode === 'edit' && hasPermission('sys:code-rule:edit'))"
                >
                  提交
                </el-button>
                <el-button @click="handleCloseDialog">取消</el-button>
              </div>
            </div>
          </template>
        </template>
      </VNForm>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, ElTag, ElButton, ElInputNumber, ElText } from 'element-plus'
import { View, Edit, Delete, Star, InfoFilled, Printer } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
import VNForm from '@/components/VNForm/index.vue'
import type { TableColumn, PaginationConfig, ActionButton } from '@/components/VNTable/types'
import type { HeaderField } from '@/components/VNForm/types'
import {
  getCodeRulePage,
  deleteCodeRule,
  setAsDefault,
  createCodeRule,
  updateCodeRule,
  previewCode,
  getBusinessTypeOptions,
  getResetFrequencyOptions,
  type CodeRule,
  type QueryCodeRuleRequest,
  type BusinessTypeOption,
  type ResetFrequencyOption,
  type CreateCodeRuleRequest,
  type UpdateCodeRuleRequest
} from '@/api/system/codeRule'
import { hasPermission } from '@/hooks/usePermission'

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const vnFormRef = ref<InstanceType<typeof VNForm>>()
const dialogVisible = ref(false)
const formMode = ref<'add' | 'edit' | 'view'>('add')
const isViewing = computed(() => formMode.value === 'view')
const previewLoading = ref(false)
const previewResult = ref('')

// --- Reactive State ---
const loading = ref(false)
const tableData = ref<CodeRule[]>([])
const pagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
})
const formData = ref<Record<string, any>>({})

// 选项数据
const businessTypeOptions = ref<BusinessTypeOption[]>([])
const resetFrequencyOptions = ref<ResetFrequencyOption[]>([])

// 过滤和排序状态
const currentFilters = ref<Record<string, any>>({})
const currentSort = ref<{ prop: string; order: 'ascending' | 'descending' | null } | null>(null)

// --- Table Configuration ---
const tableColumns = ref<TableColumn[]>([
  { prop: 'ruleCode', label: '规则编码', minWidth: 120, sortable: true, filterable: true, filterType: 'text' },
  { prop: 'ruleName', label: '规则名称', minWidth: 150, filterable: true, filterType: 'text' },
  { 
    prop: 'businessType', 
    label: '业务类型', 
    width: 120, 
    slot: true,
    filterable: true, 
    filterType: 'select',
    filterOptions: []
  },
  { prop: 'codeFormat', label: '编码格式', minWidth: 200 },
  { prop: 'separator', label: '分隔符', width: 80, formatter: (row) => row.separator || '' },
  { prop: 'resetFrequency', label: '重置频率', width: 100, slot: true },
  { prop: 'currentSequence', label: '当前序号', width: 100 },
  { 
    prop: 'status', 
    label: '状态', 
    width: 80, 
    slot: true,
    filterable: true, 
    filterType: 'select',
    filterOptions: [
      { label: '启用', value: 'ACTIVE' },
      { label: '禁用', value: 'INACTIVE' }
    ]
  },
  { 
    prop: 'isDefault', 
    label: '默认规则', 
    width: 100, 
    slot: true,
    filterable: true, 
    filterType: 'select',
    filterOptions: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
])

// --- Toolbar Configuration ---
const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('sys:code-rule:add'),
  batchDelete: hasPermission('sys:code-rule:batchdelete'),
  filter: hasPermission('sys:code-rule:search'),
  columnSetting: true,
  density: true,
  fullscreen: true,
  import: hasPermission('sys:code-rule:import'),
  export: hasPermission('sys:code-rule:export'),
}))

// --- Operation Buttons ---
const operationButtons = computed<ActionButton[]>(() => [
  { 
    label: '查看', 
    icon: 'View', 
    handler: (row) => handleView(row),
    hidden: !hasPermission('sys:code-rule:list')
  },
  {
    label: '设为默认',
    icon: 'Star',
    type: 'warning',
    handler: (row) => handleSetDefault(row),
    hidden: (row) => row.isDefault || !hasPermission('sys:code-rule:setdefault')
  },
  {
    label: '编辑',
    icon: 'Edit',
    type: 'primary',
    handler: (row) => handleEdit(row),
    hidden: !hasPermission('sys:code-rule:edit')
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    handler: (row) => handleDelete(row),
    hidden: !hasPermission('sys:code-rule:delete')
  }
])

// --- Form Configuration ---
const formFields = computed<HeaderField[]>(() => [
  // 基本信息分组
  { 
    field: 'ruleCode', 
    label: '规则编码', 
    rules: [
      { required: true, message: '请输入规则编码' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线' }
    ], 
    disabled: formMode.value === 'edit' || isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'ruleName', 
    label: '规则名称', 
    rules: [{ required: true, message: '请输入规则名称' }], 
    disabled: isViewing.value, 
    group: '基本信息' 
  },
  { 
    field: 'businessType', 
    label: '业务类型', 
    type: 'select', 
    options: businessTypeOptions.value.map(item => ({ label: item.label, value: item.value })),
    rules: [{ required: true, message: '请选择业务类型' }],
    disabled: formMode.value === 'edit' || isViewing.value, 
    group: '基本信息',
    placeholder: '请选择业务类型'
  },
  { 
    field: 'status', 
    label: '状态', 
    type: 'select', 
    options: [
      { label: '启用', value: 'ACTIVE' },
      { label: '禁用', value: 'INACTIVE' }
    ], 
    defaultValue: 'ACTIVE', 
    disabled: isViewing.value, 
    group: '基本信息',
    placeholder: '请选择状态'
  },

  // 编码格式分组
  { 
    field: 'codeFormat', 
    label: '编码格式', 
    placeholder: '请输入编码格式，如：{FIXED:C}{YYYY}{MM}{SEQ:4}',
    rules: [{ required: true, message: '请输入编码格式' }], 
    disabled: isViewing.value, 
    group: '编码格式',
    md: 24,
    lg: 24,
    xl: 24
  },
  { 
    field: 'separator', 
    label: '分隔符', 
    placeholder: '可选，如：-',
    disabled: isViewing.value, 
    group: '编码格式',
    md: 12,
    lg: 12,
    xl: 12
  },
  { 
    field: 'formatHelp', 
    label: '格式说明', 
    type: 'textarea',
    rows: 6,
    disabled: true,
    group: '编码格式',
    md: 24,
    lg: 24,
    xl: 24
  },

  // 序号设置分组
  { 
    field: 'sequenceLength', 
    label: '序号长度', 
    type: 'slot',
    rules: [{ required: true, message: '请输入序号长度' }], 
    disabled: isViewing.value, 
    group: '序号设置' 
  },
  { 
    field: 'sequenceStart', 
    label: '序号起始值', 
    type: 'slot',
    rules: [{ required: true, message: '请输入序号起始值' }], 
    disabled: isViewing.value, 
    group: '序号设置' 
  },
  { 
    field: 'resetFrequency', 
    label: '重置频率', 
    type: 'select', 
    options: resetFrequencyOptions.value.map(item => ({ label: item.label, value: item.value })),
    rules: [{ required: true, message: '请选择重置频率' }],
    disabled: isViewing.value, 
    group: '序号设置',
    placeholder: '请选择重置频率'
  },
  { 
    field: 'isDefault', 
    label: '是否默认', 
    type: 'switch', 
    disabled: isViewing.value, 
    group: '序号设置' 
  },

  // 其他信息分组
  { 
    field: 'remark', 
    label: '备注', 
    type: 'textarea', 
    rows: 3, 
    md: 24,
    lg: 24,
    xl: 24,
    disabled: isViewing.value, 
    group: '其他信息',
    placeholder: '请输入备注信息'
  },
])

// --- Dialog Title ---
const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增编码规则'
  if (formMode.value === 'edit') return '编辑编码规则'
  if (formMode.value === 'view') return '查看编码规则'
  return '编码规则管理'
})



// --- 监听器 ---
// 监听编码格式变化，自动同步序号长度
watch(
  () => formData.value['codeFormat'],
  (newFormat) => {
    if (newFormat && formMode.value !== 'view') {
      const seqMatch = newFormat.match(/\{SEQ:(\d+)\}/)
      if (seqMatch) {
        const seqLength = parseInt(seqMatch[1])
        if (seqLength !== formData.value['sequenceLength']) {
          formData.value['sequenceLength'] = seqLength
          ElMessage.info(`已自动同步序号长度为 ${seqLength} 位`)
        }
      }
    }
  }
)

// 监听序号长度变化，自动更新编码格式中的 {SEQ:n}
watch(
  () => formData.value['sequenceLength'],
  (newLength) => {
    if (formData.value['codeFormat'] && newLength && formMode.value !== 'view') {
      const seqRegex = /\{SEQ:\d+\}/g
      if (seqRegex.test(formData.value['codeFormat'])) {
        const oldFormat = formData.value['codeFormat']
        const newFormat = formData.value['codeFormat'].replace(seqRegex, `{SEQ:${newLength}}`)
        if (oldFormat !== newFormat) {
          formData.value['codeFormat'] = newFormat
          ElMessage.info(`已自动更新编码格式中的序号长度为 ${newLength} 位`)
        }
      }
    }
    // 如果已有预览结果，自动重新预览
    if (previewResult.value && formData.value['codeFormat']) {
      handlePreviewCode()
    }
  }
)

// 监听序号起始值变化，自动重新预览
watch(
  () => formData.value['sequenceStart'],
  () => {
    if (previewResult.value && formData.value['codeFormat']) {
      handlePreviewCode()
    }
  }
)

// 监听分隔符变化，自动重新预览
watch(
  () => formData.value['separator'],
  () => {
    if (previewResult.value && formData.value['codeFormat']) {
      handlePreviewCode()
    }
  }
)

// --- 初始化 ---
onMounted(() => {
  loadData()
  loadOptions()
})

// --- 方法 ---
// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    let sortString: string | undefined = undefined
    if (currentSort.value && currentSort.value.prop) {
      const direction = currentSort.value.order === 'descending' ? 'desc' : 'asc'
      sortString = `${currentSort.value.prop},${direction}`
    }

    const params: QueryCodeRuleRequest = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...currentFilters.value,
      sort: sortString
    }

    const response = await getCodeRulePage(params)
    tableData.value = response.list || []
    pagination.total = response.total || 0
  } catch (error) {
    ElMessage.error('查询失败')
    console.error('查询失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载选项数据
const loadOptions = async () => {
  try {
    const [businessRes, frequencyRes] = await Promise.all([
      getBusinessTypeOptions(),
      getResetFrequencyOptions()
    ])
    businessTypeOptions.value = businessRes
    resetFrequencyOptions.value = frequencyRes

    // 更新表格筛选选项
    const businessTypeColumn = tableColumns.value.find(col => col.prop === 'businessType')
    if (businessTypeColumn) {
      businessTypeColumn.filterOptions = businessRes.map(item => ({ label: item.label, value: item.value }))
    }
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

// 获取业务类型标签类型
const getBusinessTypeTagType = (businessType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    CUSTOMER: 'primary',
    SUPPLIER: 'success',
    MATERIAL: 'warning',
    LOCATION: 'info'
  }
  return typeMap[businessType] || 'primary'
}

// 获取业务类型标签文本
const getBusinessTypeLabel = (businessType: string): string => {
  const option = businessTypeOptions.value.find(item => item.value === businessType)
  return option ? option.label : businessType
}

// 获取重置频率标签文本
const getResetFrequencyLabel = (resetFrequency: string): string => {
  const option = resetFrequencyOptions.value.find(item => item.value === resetFrequency)
  return option ? option.label : resetFrequency
}

// --- 事件处理 ---
// 新增
const handleAdd = () => {
  formData.value = {
    status: 'ACTIVE',
    isDefault: false,
    sequenceLength: 4,
    sequenceStart: 1,
    resetFrequency: 'NEVER',
    remark: '',
    formatHelp: `可用组件：
• {FIXED:XXX} - 固定字符，如：{FIXED:C}
• {YYYY} - 四位年份，如：2024
• {YY} - 两位年份，如：24
• {MM} - 两位月份，如：03
• {DD} - 两位日期，如：15
• {YYYYMMDD} - 完整日期，如：20250110
• {YYMMDD} - 短日期，如：250110
• {FIELD:XXX} - 字段值，如：{FIELD:customerType} → CORP
• {SEQ:n} - n位序号，如：{SEQ:4} → 0001

示例：{FIXED:C}{YYYYMMDD}{FIELD:type}{SEQ:4} → C20250110CORP0001`
  }
  formMode.value = 'add'
  previewResult.value = ''
  dialogVisible.value = true
}

// 查看
const handleView = (row: CodeRule) => {
  formData.value = { 
    ...row,
    formatHelp: `可用组件：
• {FIXED:XXX} - 固定字符，如：{FIXED:C}
• {YYYY} - 四位年份，如：2024
• {YY} - 两位年份，如：24
• {MM} - 两位月份，如：03
• {DD} - 两位日期，如：15
• {YYYYMMDD} - 完整日期，如：20250110
• {YYMMDD} - 短日期，如：250110
• {FIELD:XXX} - 字段值，如：{FIELD:customerType} → CORP
• {SEQ:n} - n位序号，如：{SEQ:4} → 0001

示例：{FIXED:C}{YYYYMMDD}{FIELD:type}{SEQ:4} → C20250110CORP0001`
  }
  formMode.value = 'view'
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: CodeRule) => {
  formData.value = { 
    ...row,
    formatHelp: `可用组件：
• {FIXED:XXX} - 固定字符，如：{FIXED:C}
• {YYYY} - 四位年份，如：2024
• {YY} - 两位年份，如：24
• {MM} - 两位月份，如：03
• {DD} - 两位日期，如：15
• {YYYYMMDD} - 完整日期，如：20250110
• {YYMMDD} - 短日期，如：250110
• {FIELD:XXX} - 字段值，如：{FIELD:customerType} → CORP
• {SEQ:n} - n位序号，如：{SEQ:4} → 0001

示例：{FIXED:C}{YYYYMMDD}{FIELD:type}{SEQ:4} → C20250110CORP0001`
  }
  formMode.value = 'edit'
  previewResult.value = ''
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: CodeRule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除编码规则"${row.ruleName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteCodeRule(row.id!)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    }
  }
}

// 批量删除
const handleBatchDelete = async (rows: CodeRule[]) => {
  if (rows.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${rows.length} 条编码规则吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里需要实现批量删除API
    for (const row of rows) {
      await deleteCodeRule(row.id!)
    }
    ElMessage.success('批量删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除失败:', error)
    }
  }
}

// 设为默认
const handleSetDefault = async (row: CodeRule) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${row.ruleName}"设为默认规则吗？`,
      '确认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await setAsDefault(row.id!)
    ElMessage.success('设置成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('设置失败')
      console.error('设置失败:', error)
    }
  }
}

// 预览编码
const handlePreviewCode = async () => {
  if (!formData.value['codeFormat']) {
    ElMessage.warning('请先输入编码格式')
    return
  }

  previewLoading.value = true
  try {
    const response = await previewCode({
      codeFormat: formData.value['codeFormat'],
      contextData: {
        sequenceStart: formData.value['sequenceStart'] || 1,
        sequenceLength: formData.value['sequenceLength'] || 4,
        separator: formData.value['separator'] || ''
      }
    })
    previewResult.value = response.previewCode
  } catch (error) {
    ElMessage.error('预览失败')
    console.error('预览失败:', error)
  } finally {
    previewLoading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!vnFormRef.value) return

  try {
    // VNForm组件的验证方法可能不存在，我们跳过验证或使用其他方式
    // await vnFormRef.value.validate()
    loading.value = true

    if (formMode.value === 'add') {
      const createData: CreateCodeRuleRequest = {
        ruleCode: formData.value['ruleCode']!,
        ruleName: formData.value['ruleName']!,
        businessType: formData.value['businessType']!,
        codeFormat: formData.value['codeFormat']!,
        separator: formData.value['separator'],
        resetFrequency: formData.value['resetFrequency']!,
        sequenceLength: formData.value['sequenceLength']!,
        sequenceStart: formData.value['sequenceStart']!,
        status: formData.value['status']!,
        isDefault: formData.value['isDefault']!,
        remark: formData.value['remark']
      }
      await createCodeRule(createData)
      ElMessage.success('创建成功')
    } else if (formMode.value === 'edit') {
      const updateData: UpdateCodeRuleRequest = {
        ruleName: formData.value['ruleName']!,
        codeFormat: formData.value['codeFormat']!,
        separator: formData.value['separator'],
        resetFrequency: formData.value['resetFrequency']!,
        sequenceLength: formData.value['sequenceLength']!,
        sequenceStart: formData.value['sequenceStart']!,
        status: formData.value['status']!,
        isDefault: formData.value['isDefault']!,
        remark: formData.value['remark']
      }
      await updateCodeRule(formData.value['id']!, updateData)
      ElMessage.success('更新成功')
    }

    handleCloseDialog()
    loadData()
  } catch (error) {
    ElMessage.error('操作失败')
    console.error('操作失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleCloseDialog = () => {
  dialogVisible.value = false
  formData.value = {}
  previewResult.value = ''
  // VNForm组件的resetFields方法可能不存在，我们手动重置表单数据
  // if (vnFormRef.value) {
  //   vnFormRef.value.resetFields()
  // }
}

// --- 表格事件处理 ---
const handleFilterChange = (filters: Record<string, any>) => {
  currentFilters.value = filters
  pagination.currentPage = 1
  loadData()
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

const handleSortChange = (sort: { prop: string; order: 'ascending' | 'descending' | null }) => {
  currentSort.value = sort
  loadData()
}

const handleSelectionChange = (selection: CodeRule[]) => {
  // 处理选择变化
  console.log('选择变化:', selection)
}

// 打印预览
const handlePrintPreview = () => {
  ElMessage.info('打印预览功能待实现')
}

// 打印
const handlePrint = () => {
  ElMessage.info('打印功能待实现')
}
</script>

<style scoped>
.form-help-text {
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-help-text .el-icon {
  font-size: 12px;
}

/* 格式说明文本框样式 - 通过禁用状态来区分 */
:deep(.el-form-item .el-textarea.is-disabled .el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  background-color: #f8f9fa;
  color: #666;
}

/* 预览和操作按钮区域 */
.action-with-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.preview-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-result {
  color: #409eff;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.action-buttons {
  display: flex;
  gap: 10px;
}
</style> 