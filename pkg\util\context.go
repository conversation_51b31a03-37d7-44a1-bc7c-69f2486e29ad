package util

import (
	"context"
	"fmt"
	"time"

	"github.com/kataras/iris/v12"

	"backend/pkg/constant"
	"backend/pkg/errors" // 正确的错误包导入
	"backend/pkg/logger" // <<< 新增导入
)

// GetUserIDFromIrisContext 从 Iris Context 中安全地获取用户ID。
//
// 注意: 在标准的 Controller 实现中 (继承自 BaseControllerImpl)，推荐使用控制器实例的
// GetUserIDFromContext(ctx iris.Context) 方法，该方法内部会统一调用本包下的 GetUserIDFromStdContext。
// 本函数主要用于那些不方便或无法直接访问 BaseControllerImpl 实例的场景，
// 例如在某些 Iris 中间件的早期阶段，或在不属于标准控制器逻辑的辅助函数中直接操作 iris.Context。
//
// 如果用户ID不存在或类型不正确，则返回认证错误。
func GetUserIDFromIrisContext(irisCtx iris.Context) (uint64, error) {
	return GetUserIDFromStdContext(irisCtx.Request().Context())
}

// GetUsernameFromIrisContext 从 Iris Context 中安全地获取用户名。
//
// 注意: 在标准的 Controller 实现中，推荐使用控制器实例的 GetUsernameFromContext(ctx iris.Context) 方法。
// 本函数主要用于那些不方便或无法直接访问 BaseControllerImpl 实例的场景，例如 Iris 中间件。
func GetUsernameFromIrisContext(irisCtx iris.Context) (string, error) {
	return GetUsernameFromStdContext(irisCtx.Request().Context())
}

// GetRoleIDsFromIrisContext 从 Iris Context 中安全地获取角色ID列表。
//
// 注意: 在标准的 Controller 实现中，推荐使用控制器实例的 GetRoleIDsFromContext(ctx iris.Context) 方法。
// 本函数主要用于那些不方便或无法直接访问 BaseControllerImpl 实例的场景，例如 Iris 中间件。
func GetRoleIDsFromIrisContext(irisCtx iris.Context) ([]uint, error) {
	return GetRoleIDsFromStdContext(irisCtx.Request().Context())
}

// IsAdminFromIrisContext 从 Iris Context 中安全地判断用户是否为管理员。
//
// 注意: 在标准的 Controller 实现中，推荐使用控制器实例的 IsAdminFromContext(ctx iris.Context) 方法。
// 本函数主要用于那些不方便或无法直接访问 BaseControllerImpl 实例的场景，例如 Iris 中间件。
//
// 如果标识不存在，则返回 false, nil (非管理员，非错误状态)。
// 如果标识存在但类型不正确，则返回 false 和错误。
func IsAdminFromIrisContext(irisCtx iris.Context) (bool, error) {
	return IsAdminFromStdContext(irisCtx.Request().Context())
}

// GetAccountBookIDFromIrisContext 从 Iris Context 中安全地获取账套ID
//
// 注意: 在标准的 Controller 实现中，推荐使用控制器实例的 GetAccountBookIDFromContext(ctx iris.Context) 方法。
// 本函数主要用于那些不方便或无法直接访问 BaseControllerImpl 实例的场景，例如 Iris 中间件。
func GetAccountBookIDFromIrisContext(irisCtx iris.Context) (uint64, error) {
	return GetAccountBookIDFromStdContext(irisCtx.Request().Context())
}

// --- 新增：通用提取函数，基于标准 context.Context ---

// getUint64FromStdContext 是一个内部辅助函数，用于从标准 context.Context 中提取 uint64 类型的值
// key: 上下文中的键
// missingErrorCode, typeErrorCode, zeroErrorCode: 对应的 errors 代码 (类型为 int)
// missingMsg, typeMsg, zeroMsg: 对应的错误信息
func getUint64FromStdContext(ctx context.Context, key interface{},
	missingErrorCode int, typeErrorCode int, zeroErrorCode int,
	missingMsg string, typeMsg string, zeroMsg string) (uint64, error) {

	// 获取一个 logger 实例，这里假设 logger 包提供了一个全局的 GetLogger() 方法
	// 或者您可以根据项目结构调整如何获取 logger 实例
	log := logger.GetLogger() // <<< 尝试获取logger

	log.Debugf("[getUint64FromStdContext] Attempting to get value for key: %v", key)

	if ctx == nil {
		log.Errorf("[getUint64FromStdContext] Error: Standard context is nil for key: %v", key)
		return 0, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "standard context is nil")
	}

	value := ctx.Value(key)
	log.Debugf("[getUint64FromStdContext] For key: %v, got value: %v, type: %T", key, value, value)

	if value == nil {
		log.Warnf("[getUint64FromStdContext] Value not found in context for key: %v. Returning error: %s", key, missingMsg)
		return 0, errors.NewAuthError(missingErrorCode, missingMsg)
	}

	var id uint64
	var ok bool

	switch v := value.(type) {
	case uint64:
		id = v
		ok = true
		log.Debugf("[getUint64FromStdContext] Key %v: Matched type uint64, id set to %d", key, id)
	case uint:
		id = uint64(v)
		ok = true
		log.Debugf("[getUint64FromStdContext] Key %v: Matched type uint, id set to %d", key, id)
	case int:
		if v < 0 {
			ok = false
			log.Debugf("[getUint64FromStdContext] Key %v: Matched type int, but value %d is negative", key, v)
		} else {
			id = uint64(v)
			ok = true
			log.Debugf("[getUint64FromStdContext] Key %v: Matched type int, id set to %d", key, id)
		}
	case int64:
		if v < 0 {
			ok = false
			log.Debugf("[getUint64FromStdContext] Key %v: Matched type int64, but value %d is negative", key, v)
		} else {
			id = uint64(v)
			ok = true
			log.Debugf("[getUint64FromStdContext] Key %v: Matched type int64, id set to %d", key, id)
		}
	case float64: // Handle cases where it might be stored as float64
		if v < 0 || v != float64(uint64(v)) { // Check for loss of precision or negative
			ok = false
			log.Debugf("[getUint64FromStdContext] Key %v: Matched type float64, but value %f is negative or has precision loss", key, v)
		} else {
			id = uint64(v)
			ok = true
			log.Debugf("[getUint64FromStdContext] Key %v: Matched type float64, id set to %d", key, id)
		}
	default:
		ok = false
		log.Warnf("[getUint64FromStdContext] Key %v: Type assertion failed. Actual value: %v, type: %T", key, value, value)
	}

	log.Debugf("[getUint64FromStdContext] Key %v: Value after type assertion: id=%d, ok=%t", key, id, ok)

	if !ok {
		errToReturn := errors.NewAuthError(typeErrorCode, fmt.Sprintf("%s (actual type: %T)", typeMsg, value))
		log.Warnf("[getUint64FromStdContext] Key %v: Type assertion failed or invalid value. Returning id: 0, error: %v", key, errToReturn)
		return 0, errToReturn
	}

	if id == 0 { // Check for UserID == 0, as per original getUint64FromStdContext logic for zeroErrorCode
		errToReturn := errors.NewAuthError(zeroErrorCode, zeroMsg)
		log.Warnf("[getUint64FromStdContext] Key %v: ID is 0. Returning id: 0, error: %v", key, errToReturn)
		return 0, errToReturn
	}

	log.Debugf("[getUint64FromStdContext] Key %v: Successfully returning id: %d, error: nil", key, id)
	return id, nil
}

// GetUserIDFromStdContext 从标准 context.Context 中提取用户ID
// 返回用户ID (uint64) 和错误（如果发生）
// THIS FUNCTION IS NOW EFFECTIVELY REPLACED BY GetUserIDFromIrisContext FOR BaseController USAGE
func GetUserIDFromStdContext(ctx context.Context) (uint64, error) {
	return getUint64FromStdContext(ctx, constant.CONTEXT_USER_ID,
		errors.CODE_AUTH_UNAUTHORIZED,  // missingErrorCode
		errors.CODE_AUTH_TOKEN_INVALID, // typeErrorCode (修正)
		errors.CODE_AUTH_UNAUTHORIZED,  // zeroErrorCode (修正)
		"上下文中缺少用户ID或用户未授权",
		"用户ID类型无效",
		"从上下文中获取的用户ID无效(0)")
}

// GetAccountBookIDFromStdContext 从标准 context.Context 中提取账套ID
// 返回账套ID (uint64) 和错误（如果发生）
// THIS FUNCTION IS NOW EFFECTIVELY REPLACED BY GetAccountBookIDFromIrisContext FOR BaseController USAGE
func GetAccountBookIDFromStdContext(ctx context.Context) (uint64, error) {
	return getUint64FromStdContext(ctx, constant.CONTEXT_ACCOUNT_BOOK_ID,
		errors.CODE_AUTH_FORBIDDEN, // missingErrorCode
		errors.CODE_AUTH_FORBIDDEN, // typeErrorCode
		errors.CODE_AUTH_FORBIDDEN, // zeroErrorCode
		"无效或未找到账套信息，请检查请求或权限",
		"账套ID类型无效",
		"从上下文中获取的账套ID无效(0)")
}

// getStringFromStdContext 是一个内部辅助函数，用于从标准 context.Context 中提取 string 类型的值
func getStringFromStdContext(ctx context.Context, key interface{},
	missingErrorCode int, typeErrorCode int,
	missingMsg string, typeMsg string) (string, error) {

	if ctx == nil {
		return "", errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "standard context is nil")
	}

	value := ctx.Value(key)
	if value == nil {
		return "", errors.NewAuthError(missingErrorCode, missingMsg)
	}

	strVal, ok := value.(string)
	if !ok {
		return "", errors.NewAuthError(typeErrorCode, typeMsg+fmt.Sprintf(" (actual type: %T)", value))
	}

	return strVal, nil
}

// GetUsernameFromStdContext 从标准 context.Context 中提取用户名
// 返回用户名 (string) 和错误（如果发生）
// THIS FUNCTION IS NOW EFFECTIVELY REPLACED BY GetUsernameFromIrisContext FOR BaseController USAGE
func GetUsernameFromStdContext(ctx context.Context) (string, error) {
	username, err := getStringFromStdContext(ctx, constant.CONTEXT_USERNAME,
		errors.CODE_AUTH_UNAUTHORIZED,
		errors.CODE_AUTH_TOKEN_INVALID,
		"上下文中缺少用户名或用户未授权",
		"用户名类型无效")

	if err != nil {
		return "", err
	}
	if username == "" { // 用户名通常不应为空字符串，如果允许空字符串作为有效用户名，则移除此检查
		return "", errors.NewAuthError(errors.CODE_AUTH_TOKEN_INVALID, "从上下文中获取的用户名无效(空字符串)")
	}
	return username, nil
}

// getSliceUintFromStdContext 是一个内部辅助函数，用于从标准 context.Context 中提取 []uint 类型的值
func getSliceUintFromStdContext(ctx context.Context, key interface{},
	missingErrorCode int, typeErrorCode int,
	missingMsg string, typeMsg string) ([]uint, error) {

	if ctx == nil {
		return nil, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "standard context is nil")
	}

	value := ctx.Value(key)
	if value == nil {
		return nil, errors.NewAuthError(missingErrorCode, missingMsg)
	}

	sliceVal, ok := value.([]uint)
	if !ok {
		// 尝试处理 []interface{}，并将其中的每个元素转换为 uint
		ifaceSlice, isIfaceSlice := value.([]interface{})
		if isIfaceSlice {
			uIntSlice := make([]uint, 0, len(ifaceSlice))
			for i, item := range ifaceSlice {
				switch v := item.(type) {
				case uint:
					uIntSlice = append(uIntSlice, v)
				case float64: // JSON numbers often unmarshal as float64
					if v < 0 || v != float64(uint(v)) { // Check for loss of precision or negative
						return nil, errors.NewAuthError(typeErrorCode, fmt.Sprintf("%s (element at index %d is non-uint float64: %v)", typeMsg, i, v))
					}
					uIntSlice = append(uIntSlice, uint(v))
				case int:
					if v < 0 {
						return nil, errors.NewAuthError(typeErrorCode, fmt.Sprintf("%s (element at index %d is negative int: %v)", typeMsg, i, v))
					}
					uIntSlice = append(uIntSlice, uint(v))
				default:
					return nil, errors.NewAuthError(typeErrorCode, fmt.Sprintf("%s (element at index %d has unhandled type: %T)", typeMsg, i, item))
				}
			}
			return uIntSlice, nil
		}
		return nil, errors.NewAuthError(typeErrorCode, typeMsg+fmt.Sprintf(" (actual type: %T)", value))
	}

	// 如果 sliceVal 是 nil，根据业务需求可以返回空的 slice 或错误。这里返回它本身（可能为 nil）。
	// 如果要求非空，可以在调用方检查或在这里添加逻辑。
	// 对于角色ID，一个空的上下文值通常意味着没有角色，返回 nil 或空 slice 是合理的。
	return sliceVal, nil
}

// GetRoleIDsFromStdContext 从标准 context.Context 中提取角色ID列表
// 返回角色ID列表 ([]uint) 和错误（如果发生）
// THIS FUNCTION IS NOW EFFECTIVELY REPLACED BY GetRoleIDsFromIrisContext FOR BaseController USAGE
func GetRoleIDsFromStdContext(ctx context.Context) ([]uint, error) {
	roleIDs, err := getSliceUintFromStdContext(ctx, constant.CONTEXT_ROLE_IDS,
		errors.CODE_AUTH_UNAUTHORIZED,
		errors.CODE_AUTH_TOKEN_INVALID,
		"上下文中缺少角色ID列表或用户未授权",
		"角色ID列表类型无效")

	if err != nil {
		return nil, err
	}
	// 如果业务上要求角色ID列表不能是nil（即使是空列表[]uint{}），可以在这里处理
	// if roleIDs == nil {
	// 	 return []uint{}, nil // 或者返回一个特定的错误，如果 nil 不被接受
	// }
	return roleIDs, nil
}

// getBoolFromStdContext 是一个内部辅助函数，用于从标准 context.Context 中提取 bool 类型的值
// defaultIfMissing: 如果上下文中不存在对应的键，则返回此默认值，并且错误为nil。
// 如果键存在但类型不匹配，则返回错误。
func getBoolFromStdContext(ctx context.Context, key interface{},
	typeErrorCode int, typeMsg string, defaultIfMissing bool) (bool, error) {

	if ctx == nil {
		// 或者根据场景返回 defaultIfMissing 和一个表示上下文nil的错误
		return false, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "standard context is nil")
	}

	value := ctx.Value(key)
	if value == nil {
		// 键不存在，返回 defaultIfMissing 和 nil 错误
		return defaultIfMissing, nil
	}

	boolVal, ok := value.(bool)
	if !ok {
		return false, errors.NewAuthError(typeErrorCode, typeMsg+fmt.Sprintf(" (actual type: %T)", value))
	}

	return boolVal, nil
}

// IsAdminFromStdContext 从标准 context.Context 中提取用户是否为管理员的标识
// THIS FUNCTION IS NOW EFFECTIVELY REPLACED BY IsAdminFromIrisContext FOR BaseController USAGE
func IsAdminFromStdContext(ctx context.Context) (bool, error) {
	return getBoolFromStdContext(ctx, constant.CONTEXT_IS_ADMIN,
		errors.CODE_AUTH_TOKEN_INVALID, // typeErrorCode (如果类型错误，可能令牌本身有问题或被篡改)
		"用户Admin标识类型无效",                // typeMsg
		false)                          // defaultIfMissing (如果键不存在，则默认为非Admin，且不报错)
}

// GetClientIPFromStdContext 从标准 context.Context 中提取客户端IP
func GetClientIPFromStdContext(ctx context.Context) (string, error) {
	// 使用已有的 getStringFromStdContext 内部辅助函数
	// 错误代码的选择应符合项目 pkg/errors 规范。
	// 考虑使用更具体的错误码，如 errors.CODE_CONTEXT_VALUE_MISSING (如果已定义) 或一个通用的"上下文信息缺失"错误码，
	// 以更准确地反映"信息缺失"的本质，而非直接的"未授权"。
	return getStringFromStdContext(ctx, constant.CONTEXT_CLIENT_IP, // 使用 pkg/constant 中的键
		errors.CODE_AUTH_UNAUTHORIZED, // 示例：如果获取不到IP，可视为未授权或信息缺失
		errors.CODE_PARAMS_TYPE_ERROR, // 类型不匹配时的错误码
		"上下文中缺少客户端IP信息",
		"客户端IP类型无效")
}

// GetTraceIDFromStdContext 从标准 context.Context 中提取追踪ID
func GetTraceIDFromStdContext(ctx context.Context) (string, error) {
	return getStringFromStdContext(ctx, constant.CONTEXT_TRACE_ID, // 使用 pkg/constant 中的键
		errors.CODE_SYSTEM_INTERNAL, // 示例：追踪ID缺失可能是系统问题
		errors.CODE_PARAMS_TYPE_ERROR,
		"上下文中缺少追踪ID信息",
		"追踪ID类型无效")
}

// GetRequestStartTimeFromStdContext 从标准 context.Context 中提取请求开始时间
func GetRequestStartTimeFromStdContext(ctx context.Context) (time.Time, error) {
	if ctx == nil {
		return time.Time{}, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "standard context is nil")
	}
	value := ctx.Value(constant.CONTEXT_REQUEST_START_TIME) // 使用 pkg/constant 中的键
	if value == nil {
		// 根据业务需求，这里可能不应该是一个错误，或者是一个特定类型的"信息未找到"错误。
		// 例如，如果请求开始时间仅用于日志记录或可选的监控，那么记录一个调试信息并继续可能是合适的。
		return time.Time{}, errors.NewSystemError(errors.CODE_CONTEXT_VALUE_MISSING, "上下文中缺少请求开始时间") // 假设 CODE_CONTEXT_VALUE_MISSING 已定义
	}
	startTime, ok := value.(time.Time)
	if !ok {
		return time.Time{}, errors.NewSystemError(errors.CODE_PARAMS_TYPE_ERROR, fmt.Sprintf("请求开始时间类型无效 (actual type: %T)", value))
	}
	return startTime, nil
}
