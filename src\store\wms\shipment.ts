/**
 * 发运单状态管理 Store
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  WmsShipmentResp, 
  WmsShipmentCreateReq, 
  WmsShipmentUpdateReq,
  WmsShipmentSearchForm,
  WmsShipmentStatus
} from '@/types/wms/shipment'
import * as shipmentApi from '@/api/wms/shipment'

export const useShipmentStore = defineStore('wms-shipment', () => {
  // 状态数据
  const list = ref<WmsShipmentResp[]>([])
  const total = ref(0)
  const loading = ref(false)
  const currentRecord = ref<WmsShipmentResp | null>(null)

  // 搜索表单
  const searchForm = ref<WmsShipmentSearchForm>({
    pageNum: 1,
    pageSize: 10,
    shipmentNo: '',
    status: undefined,
    carrierId: undefined,
    shippingMethod: '',
    createdAtStart: '',
    createdAtEnd: ''
  })

  // 分页信息
  const pagination = computed(() => ({
    pageNum: searchForm.value.pageNum,
    pageSize: searchForm.value.pageSize,
    total: total.value
  }))

  // 状态统计
  const statusStats = ref<Record<WmsShipmentStatus, number>>({
    PREPARING: 0,
    PACKED: 0,
    READY_TO_SHIP: 0,
    SHIPPED: 0,
    IN_TRANSIT: 0,
    DELIVERED: 0,
    CANCELLED: 0
  })

  // 承运商缓存
  const carriers = ref<any[]>([])
  const shippingMethods = ref<any[]>([])

  // Actions
  const fetchList = async (params?: Partial<WmsShipmentSearchForm>) => {
    loading.value = true
    try {
      if (params) {
        Object.assign(searchForm.value, params)
      }
      
      const response = await shipmentApi.getShipmentPage(searchForm.value)
      list.value = response.list
      total.value = response.total
      
      // 更新状态统计
      updateStatusStats(response.list)
    } catch (error) {
      console.error('获取发运单列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchDetail = async (id: number): Promise<WmsShipmentResp> => {
    try {
      const response = await shipmentApi.getShipmentById(id)
      currentRecord.value = response
      return response
    } catch (error) {
      console.error('获取发运单详情失败:', error)
      throw error
    }
  }

  const create = async (data: WmsShipmentCreateReq): Promise<WmsShipmentResp> => {
    try {
      const response = await shipmentApi.createShipment(data)
      await fetchList() // 刷新列表
      return response
    } catch (error) {
      console.error('创建发运单失败:', error)
      throw error
    }
  }

  const update = async (id: number, data: WmsShipmentUpdateReq): Promise<WmsShipmentResp> => {
    try {
      const response = await shipmentApi.updateShipment(id, data)
      await fetchList() // 刷新列表
      return response
    } catch (error) {
      console.error('更新发运单失败:', error)
      throw error
    }
  }

  const remove = async (id: number): Promise<void> => {
    try {
      await shipmentApi.deleteShipment(id)
      await fetchList() // 刷新列表
    } catch (error) {
      console.error('删除发运单失败:', error)
      throw error
    }
  }

  // 状态变更操作
  const packShipment = async (id: number, data: any): Promise<void> => {
    try {
      await shipmentApi.packShipment(id, data)
      await fetchList()
    } catch (error) {
      console.error('打包发运单失败:', error)
      throw error
    }
  }

  const shipOut = async (id: number, data: any): Promise<void> => {
    try {
      await shipmentApi.shipOut(id, data)
      await fetchList()
    } catch (error) {
      console.error('发运失败:', error)
      throw error
    }
  }

  const confirmDelivery = async (id: number, data: any): Promise<void> => {
    try {
      await shipmentApi.confirmDelivery(id, data)
      await fetchList()
    } catch (error) {
      console.error('确认签收失败:', error)
      throw error
    }
  }

  const cancelShipment = async (id: number, reason: string): Promise<void> => {
    try {
      await shipmentApi.cancelShipment(id, { reason })
      await fetchList()
    } catch (error) {
      console.error('取消发运单失败:', error)
      throw error
    }
  }

  // 运费计算
  const calculateShippingCost = async (data: any): Promise<any> => {
    try {
      return await shipmentApi.calculateShippingCost(data)
    } catch (error) {
      console.error('计算运费失败:', error)
      throw error
    }
  }

  // 打印标签
  const printShippingLabel = async (id: number): Promise<void> => {
    try {
      await shipmentApi.printShippingLabel(id)
    } catch (error) {
      console.error('打印发运标签失败:', error)
      throw error
    }
  }

  // 跟踪信息
  const getTrackingInfo = async (id: number): Promise<any> => {
    try {
      return await shipmentApi.getTrackingInfo(id)
    } catch (error) {
      console.error('获取跟踪信息失败:', error)
      throw error
    }
  }

  // 批量操作
  const batchPack = async (ids: number[], data: any): Promise<void> => {
    try {
      await shipmentApi.batchPackShipments(ids, data)
      await fetchList()
    } catch (error) {
      console.error('批量打包失败:', error)
      throw error
    }
  }

  const batchShip = async (ids: number[], data: any): Promise<void> => {
    try {
      await shipmentApi.batchShipOut(ids, data)
      await fetchList()
    } catch (error) {
      console.error('批量发运失败:', error)
      throw error
    }
  }

  const batchCancel = async (ids: number[], reason: string): Promise<void> => {
    try {
      await shipmentApi.batchCancelShipments(ids, { reason })
      await fetchList()
    } catch (error) {
      console.error('批量取消失败:', error)
      throw error
    }
  }

  // 加载基础数据
  const loadCarriers = async (): Promise<void> => {
    try {
      const response = await shipmentApi.getCarrierList()
      carriers.value = response
    } catch (error) {
      console.error('加载承运商列表失败:', error)
    }
  }

  const loadShippingMethods = async (): Promise<void> => {
    try {
      const response = await shipmentApi.getShippingMethodList()
      shippingMethods.value = response
    } catch (error) {
      console.error('加载运输方式列表失败:', error)
    }
  }

  // 工具方法
  const updateStatusStats = (shipments: WmsShipmentResp[]) => {
    const stats: Record<WmsShipmentStatus, number> = {
      PREPARING: 0,
      PACKED: 0,
      READY_TO_SHIP: 0,
      SHIPPED: 0,
      IN_TRANSIT: 0,
      DELIVERED: 0,
      CANCELLED: 0
    }

    shipments.forEach(shipment => {
      if (stats.hasOwnProperty(shipment.status)) {
        stats[shipment.status]++
      }
    })

    statusStats.value = stats
  }

  const resetSearchForm = () => {
    searchForm.value = {
      pageNum: 1,
      pageSize: 10,
      shipmentNo: '',
      status: undefined,
      carrierId: undefined,
      shippingMethod: '',
      createdAtStart: '',
      createdAtEnd: ''
    }
  }

  const clearCurrentRecord = () => {
    currentRecord.value = null
  }

  // 返回状态和方法
  return {
    // 状态
    list,
    total,
    loading,
    currentRecord,
    searchForm,
    pagination,
    statusStats,
    carriers,
    shippingMethods,

    // 基础操作
    fetchList,
    fetchDetail,
    create,
    update,
    remove,

    // 状态变更
    packShipment,
    shipOut,
    confirmDelivery,
    cancelShipment,

    // 业务功能
    calculateShippingCost,
    printShippingLabel,
    getTrackingInfo,

    // 批量操作
    batchPack,
    batchShip,
    batchCancel,

    // 基础数据
    loadCarriers,
    loadShippingMethods,

    // 工具方法
    resetSearchForm,
    clearCurrentRecord
  }
})
