package entity

import (
	"time"
)

// FileMetadata 文件元数据实体
// 用于存储上传到对象存储的文件信息
type FileMetadata struct {
	BaseEntity             // 继承基础实体
	OriginalName string    `gorm:"column:original_name;size:255;not null" json:"originalName" sortable:"true"` // 原始文件名 (允许排序)
	StoragePath  string    `gorm:"column:storage_path;size:500;not null" json:"storagePath"`                   // 文件在存储服务中的完整路径或Key
	FileSize     int64     `gorm:"column:file_size" json:"fileSize" sortable:"true"`                           // 文件大小 (字节) (允许排序)
	FileType     string    `gorm:"column:file_type;size:100" json:"fileType"`                                  // 文件类型 (MIME Type)
	StorageType  string    `gorm:"column:storage_type;size:50;not null" json:"storageType"`                    // 存储类型 (例如: local, s3, oss)
	UploadTime   time.Time `gorm:"column:upload_time;default:CURRENT_TIMESTAMP" json:"uploadTime"`             // 上传时间 (使用 BaseEntity 的 CreatedAt)
	Hash         string    `gorm:"column:hash;size:64;index" json:"-"`                                         // 文件内容的哈希值 (例如 SHA256)，用于校验和去重
	BusinessID   uint      `gorm:"column:business_id;index" json:"businessId,omitempty"`                       // 关联的业务ID (可选)
	BusinessType string    `gorm:"column:business_type;size:50;index" json:"businessType,omitempty"`           // 关联的业务类型 (可选)
}

// TableName 指定 FileMetadata 实体对应的数据库表名
func (FileMetadata) TableName() string {
	return "app_file_metadata" // 数据库表名
}
