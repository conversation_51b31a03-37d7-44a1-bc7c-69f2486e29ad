package license

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
)

// SignedLicenseData holds the base64 encoded license data and its signature.
// This is the structure that will be written to the license file (usually as JSON).
type SignedLicenseData struct {
	Data      string `json:"data"`      // Base64 encoded LicenseInfo JSON
	Signature string `json:"signature"` // Base64 encoded RSA signature
}

// GenerateLicense generates the signed license data.
// It takes the license information and the private key as input.
func GenerateLicense(info *LicenseInfo, privateKey *rsa.PrivateKey) (*SignedLicenseData, error) {
	if info == nil {
		return nil, fmt.Errorf("license info cannot be nil")
	}
	if privateKey == nil {
		return nil, fmt.Errorf("private key cannot be nil")
	}

	// 1. Serialize the LicenseInfo to JSON
	jsonData, err := json.MarshalIndent(info, "", "  ") // Use Indent for readability if desired
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("无法序列化 license info 到 JSON: %w", err)
	}

	// 2. Sign the JSON data using the private key
	signatureBytes, err := sign(privateKey, jsonData) // Calls the shared sign function
	if err != nil {
		return nil, fmt.Errorf("无法签名 license 数据: %w", err)
	}

	// 3. Encode data and signature to Base64
	dataB64 := base64.StdEncoding.EncodeToString(jsonData)
	signatureB64 := base64.StdEncoding.EncodeToString(signatureBytes)

	// 4. Create the SignedLicenseData struct
	signedData := &SignedLicenseData{
		Data:      dataB64,
		Signature: signatureB64,
	}

	return signedData, nil
}

// GenerateLicenseFile generates a license file at the specified path.
// It loads the private key from the given path and generates the signed data.
func GenerateLicenseFile(info *LicenseInfo, privateKeyPath string, outputFilePath string) error {
	// 1. Load the private key
	privateKey, err := loadPrivateKey(privateKeyPath) // Calls the shared load function
	if err != nil {
		return fmt.Errorf("无法加载私钥 %s: %w", privateKeyPath, err)
	}

	// 2. Generate the signed license data
	signedData, err := GenerateLicense(info, privateKey)
	if err != nil {
		return fmt.Errorf("无法生成签名后的 license 数据: %w", err)
	}

	// 3. Serialize the SignedLicenseData to JSON
	outputJson, err := json.MarshalIndent(signedData, "", "  ")
	if err != nil {
		return fmt.Errorf("无法序列化签名后的数据到 JSON: %w", err)
	}

	// 4. Write the JSON to the output file
	err = os.WriteFile(outputFilePath, outputJson, 0644)
	if err != nil {
		return fmt.Errorf("无法写入授权文件 %s: %w", outputFilePath, err)
	}

	return nil
}
