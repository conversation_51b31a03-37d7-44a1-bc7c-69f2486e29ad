/**
 * Utility functions for handling authentication tokens using localStorage.
 */

const TOKEN_KEY = 'accessToken'; // Key used to store the token in localStorage

/**
 * Retrieves the access token from localStorage.
 * @returns The access token string, or null if not found.
 */
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

/**
 * Stores the access token in localStorage.
 * @param token - The access token string to store.
 */
export function setToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token);
}

/**
 * Removes the access token from localStorage.
 */
export function removeToken(): void {
  localStorage.removeItem(TOKEN_KEY);
}

// You can add similar functions for refreshToken if you need to persist it as well
// const REFRESH_TOKEN_KEY = 'refreshToken';
// export function getRefreshToken(): string | null { ... }
// export function setRefreshToken(token: string): void { ... }
// export function removeRefreshToken(): void { ... }
