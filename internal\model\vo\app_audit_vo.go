package vo

import (
	"time"

	"gorm.io/datatypes" // If OldValue/NewValue are still JSON in VO
)

// AuditLogVO 审计日志视图对象，用于API响应
type AuditLogVO struct {
	ID           uint64         `json:"id"`
	UserID       uint64         `json:"userId,omitempty"` // omitempty if can be 0 for system events
	Username     string         `json:"username,omitempty"`
	Action       string         `json:"action"`
	ResourceType string         `json:"resourceType,omitempty"`
	ResourceID   string         `json:"resourceId,omitempty"`
	Timestamp    time.Time      `json:"timestamp"`
	ClientIP     string         `json:"clientIp,omitempty"`
	UserAgent    string         `json:"userAgent,omitempty"`
	RequestURI   string         `json:"requestUri,omitempty"`
	Method       string         `json:"method,omitempty"`
	StatusCode   int            `json:"statusCode,omitempty"`
	DurationMs   int64          `json:"durationMs,omitempty"`
	TraceID      string         `json:"traceId,omitempty"`
	Status       string         `json:"status,omitempty"` // e.g., success, failure
	OldValue     datatypes.JSON `json:"oldValue,omitempty"`
	NewValue     datatypes.JSON `json:"newValue,omitempty"`
	Details      string         `json:"details,omitempty"`
	CreatedAt    time.Time      `json:"createdAt"`
}
