// ==================== 导入类型 ====================
import type { 
  WmsPickingTaskStatus,
  WmsPickingStrategy,
  WmsPickingTaskResp,
  WmsPickingTaskDetailResp,
  WmsWaveResp,
  WmsUserResp,
  WmsPickingTaskMobileResp,
  WmsPickingTaskMobileDetailResp
} from '@/api/wms/pickingTask'

// ==================== 基础类型 ====================

// 选择器选项类型
export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  color?: string
}

// ==================== 表单数据类型 ====================

// 拣货任务明细行数据（用于表单编辑）
export interface PickingTaskDetailRowData {
  id?: number
  lineNo: number
  outboundDetailId: number
  itemId: number
  itemCode?: string
  itemName?: string
  itemSpec?: string
  requiredQty: number
  pickedQty?: number
  sourceLocationId?: number
  sourceLocationCode?: string
  targetLocationId?: number
  targetLocationCode?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  status?: string
  remark?: string
  // 表单状态标识
  _isNew?: boolean
  _toDelete?: boolean
  _hasChanged?: boolean
  _editing?: boolean
}

// 拣货任务表单数据
export interface PickingTaskFormData {
  // 主表数据
  id?: number
  taskNo?: string
  notificationId: number | null
  notificationNo?: string
  pickingStrategy: WmsPickingStrategy | ''
  waveNo?: string
  assignedUserId?: number | null
  assignedUserName?: string
  status?: WmsPickingTaskStatus
  priority: number
  estimatedDuration?: number
  actualDuration?: number
  startTime?: string
  endTime?: string
  remark?: string
  
  // 明细表数据
  details: PickingTaskDetailRowData[]
  
  // 表单状态
  _mode?: 'create' | 'edit' | 'view'
  _loading?: boolean
  _submitting?: boolean
}

// 波次表单数据
export interface WaveFormData {
  id?: number
  waveNo?: string
  taskIds: number[]
  assignedUserId?: number | null
  assignedUserName?: string
  estimatedDuration?: number
  remark?: string
  status?: string
  
  // 表单状态
  _mode?: 'create' | 'edit' | 'view'
  _loading?: boolean
  _submitting?: boolean
}

// 移动端拣货表单数据
export interface MobilePickingFormData {
  taskId: number
  userId: number
  details: MobilePickingDetailFormData[]
  remark?: string
}

// 移动端拣货明细表单数据
export interface MobilePickingDetailFormData {
  detailId: number
  pickedQty: number
  actualLocationCode?: string
  actualBatchNo?: string
  remark?: string
}

// ==================== 组件Props类型 ====================

// 用户选择器Props
export interface UserSelectorProps {
  modelValue?: number | null
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  filterable?: boolean
  multiple?: boolean
  size?: 'large' | 'default' | 'small'
  roleFilter?: string[]
}

// 波次管理组件Props
export interface WaveManagerProps {
  visible?: boolean
  selectedTasks?: number[]
}

// 拣货执行组件Props
export interface PickingExecutionProps {
  taskId: number
  readonly?: boolean
}

// 移动端拣货组件Props
export interface MobilePickingProps {
  userId: number
  taskId?: number
}

// ==================== 常量定义 ====================

// 拣货任务状态选项
export const PICKING_TASK_STATUS_OPTIONS: SelectOption[] = [
  { value: 'PENDING', label: '待分配', color: '#909399' },
  { value: 'ASSIGNED', label: '已分配', color: '#409EFF' },
  { value: 'IN_PROGRESS', label: '进行中', color: '#E6A23C' },
  { value: 'COMPLETED', label: '已完成', color: '#67C23A' },
  { value: 'CANCELLED', label: '已取消', color: '#F56C6C' }
]

// 拣货策略选项
export const PICKING_STRATEGY_OPTIONS: SelectOption[] = [
  { value: 'BY_ORDER', label: '按订单拣货' },
  { value: 'BY_ITEM', label: '按物料拣货' },
  { value: 'BY_LOCATION', label: '按库位拣货' },
  { value: 'BATCH_PICKING', label: '批量拣货' }
]

// 优先级选项
export const PRIORITY_OPTIONS: SelectOption[] = [
  { value: 1, label: '最高', color: '#F56C6C' },
  { value: 2, label: '高', color: '#E6A23C' },
  { value: 3, label: '中', color: '#409EFF' },
  { value: 4, label: '低', color: '#909399' },
  { value: 5, label: '最低', color: '#C0C4CC' }
]

// 波次状态选项
export const WAVE_STATUS_OPTIONS: SelectOption[] = [
  { value: 'CREATED', label: '已创建', color: '#909399' },
  { value: 'ASSIGNED', label: '已分配', color: '#409EFF' },
  { value: 'IN_PROGRESS', label: '进行中', color: '#E6A23C' },
  { value: 'COMPLETED', label: '已完成', color: '#67C23A' },
  { value: 'CANCELLED', label: '已取消', color: '#F56C6C' }
]

// ==================== 工具函数类型 ====================

// 状态格式化函数类型
export type StatusFormatter = (status: WmsPickingTaskStatus) => string

// 状态颜色配置类型
export interface StatusColorConfig {
  color: string
  bgColor: string
  borderColor: string
}

// 状态颜色映射类型
export type StatusColorMap = Record<WmsPickingTaskStatus, StatusColorConfig>

// 表单验证规则类型
export interface FormValidationRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  validator?: (rule: any, value: any, callback: any) => void
}

// 表单验证规则映射类型
export type FormValidationRules = Record<string, FormValidationRule[]>

// 批量操作类型
export interface BatchOperation {
  type: 'delete' | 'assign' | 'cancel' | 'export'
  label: string
  icon?: string
  permission?: string
  disabled?: boolean
  handler: (selectedRows: any[]) => void | Promise<void>
}

// ==================== 业务逻辑类型 ====================

// 拣货路径优化配置
export interface PickingPathConfig {
  strategy: 'SHORTEST_PATH' | 'ZONE_BASED' | 'SERPENTINE'
  startLocation?: string
  endLocation?: string
  avoidCongestion?: boolean
}

// 拣货效率统计
export interface PickingEfficiencyStats {
  userId: number
  userName: string
  totalTasks: number
  completedTasks: number
  averageTime: number
  accuracy: number
  productivity: number
}

// 波次优化配置
export interface WaveOptimizationConfig {
  maxTasksPerWave: number
  maxItemsPerWave: number
  maxLocationsPerWave: number
  priorityWeight: number
  locationWeight: number
  itemWeight: number
}

// 拣货设备配置
export interface PickingDeviceConfig {
  deviceType: 'HANDHELD' | 'VOICE' | 'PICK_TO_LIGHT' | 'CART'
  deviceId?: string
  features: string[]
  settings: Record<string, any>
}

// ==================== 事件类型 ====================

// 表单事件
export interface FormEvents {
  submit: (data: PickingTaskFormData | WaveFormData) => void
  cancel: () => void
  reset: () => void
  validate: () => Promise<boolean>
}

// 列表事件
export interface ListEvents {
  refresh: () => void
  search: (params: any) => void
  sort: (column: string, order: 'asc' | 'desc') => void
  filter: (filters: Record<string, any>) => void
  select: (rows: any[]) => void
}

// 拣货执行事件
export interface PickingExecutionEvents {
  start: (taskId: number) => void
  pause: (taskId: number) => void
  resume: (taskId: number) => void
  complete: (taskId: number, data: any) => void
  cancel: (taskId: number, reason?: string) => void
}

// 移动端事件
export interface MobileEvents {
  scan: (barcode: string) => void
  confirm: (data: any) => void
  skip: (detailId: number, reason?: string) => void
  exception: (detailId: number, type: string, description?: string) => void
}

// ==================== 响应类型扩展 ====================

// 扩展的拣货任务响应类型（包含计算字段）
export interface ExtendedPickingTaskResp extends WmsPickingTaskResp {
  // 计算字段
  totalRequiredQty?: number
  totalPickedQty?: number
  pickingProgress?: number
  efficiency?: number
  
  // 状态标识
  canEdit?: boolean
  canDelete?: boolean
  canAssign?: boolean
  canStart?: boolean
  canComplete?: boolean
  canCancel?: boolean
  
  // 关联数据
  notification?: any
  assignedUser?: WmsUserResp
  wave?: WmsWaveResp
}

// 扩展的拣货任务明细响应类型
export interface ExtendedPickingTaskDetailResp extends WmsPickingTaskDetailResp {
  // 计算字段
  unpickedQty?: number
  pickingProgress?: number
  
  // 状态标识
  isCompleted?: boolean
  hasException?: boolean
  
  // 关联数据
  item?: any
  sourceLocation?: any
  targetLocation?: any
}

// 扩展的波次响应类型
export interface ExtendedWaveResp extends WmsWaveResp {
  // 计算字段
  totalRequiredQty?: number
  totalPickedQty?: number
  pickingProgress?: number
  efficiency?: number
  
  // 状态标识
  canEdit?: boolean
  canDelete?: boolean
  canAssign?: boolean
  canStart?: boolean
  canComplete?: boolean
  canCancel?: boolean
  
  // 关联数据
  assignedUser?: WmsUserResp
}

// ==================== 移动端特定类型 ====================

// 移动端界面配置
export interface MobileUIConfig {
  theme: 'light' | 'dark'
  fontSize: 'small' | 'medium' | 'large'
  showImages: boolean
  enableVoice: boolean
  enableVibration: boolean
  autoAdvance: boolean
}

// 移动端扫码结果
export interface ScanResult {
  type: 'ITEM' | 'LOCATION' | 'BATCH' | 'UNKNOWN'
  value: string
  data?: any
  isValid: boolean
  message?: string
}

// 移动端异常类型
export interface PickingException {
  type: 'ITEM_NOT_FOUND' | 'QUANTITY_MISMATCH' | 'LOCATION_ERROR' | 'DAMAGE' | 'OTHER'
  description: string
  detailId: number
  reportedBy: number
  reportedAt: string
  resolved?: boolean
  resolution?: string
}

// ==================== 导出类型 ====================
export type {
  WmsPickingTaskStatus,
  WmsPickingStrategy,
  WmsPickingTaskResp,
  WmsPickingTaskDetailResp,
  WmsWaveResp,
  WmsUserResp,
  WmsPickingTaskMobileResp,
  WmsPickingTaskMobileDetailResp
}
