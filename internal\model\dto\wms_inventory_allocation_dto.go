package dto

import (
	"backend/pkg/response"
	"time"
)

// WmsInventoryAllocationCreateReq 创建库存分配请求
type WmsInventoryAllocationCreateReq struct {
	OutboundDetailID   uint    `json:"outboundDetailId" validate:"required"`
	InventoryID        uint    `json:"inventoryId" validate:"required"`
	AllocatedQty       float64 `json:"allocatedQty" validate:"required,gt=0"`
	AllocationStrategy *string `json:"allocationStrategy"`
	AllocationReason   *string `json:"allocationReason"`
	Remark             *string `json:"remark"`
}

// WmsInventoryAllocationUpdateReq 更新库存分配请求
type WmsInventoryAllocationUpdateReq struct {
	ID                 uint    `json:"id" validate:"required"`
	AllocatedQty       float64 `json:"allocatedQty" validate:"required,gt=0"`
	AllocationStrategy *string `json:"allocationStrategy"`
	AllocationReason   *string `json:"allocationReason"`
	Remark             *string `json:"remark"`
}

// WmsInventoryAllocationQueryReq 查询库存分配请求
type WmsInventoryAllocationQueryReq struct {
	PageQuery           response.PageQuery `json:"-"` // 分页查询参数，由Controller填充
	OutboundDetailID    *uint              `json:"outboundDetailId"`
	InventoryID         *uint              `json:"inventoryId"`
	Status              *string            `json:"status"`
	AllocationStrategy  *string            `json:"allocationStrategy"`
	AllocationTimeStart *time.Time         `json:"allocationTimeStart"`
	AllocationTimeEnd   *time.Time         `json:"allocationTimeEnd"`
}

// WmsInventoryAllocationBatchCreateReq 批量创建库存分配请求
type WmsInventoryAllocationBatchCreateReq struct {
	Allocations []WmsInventoryAllocationCreateReq `json:"allocations" validate:"required,min=1,max=100,dive"`
}

// WmsInventoryAllocationReleaseReq 释放库存分配请求
type WmsInventoryAllocationReleaseReq struct {
	ID     uint    `json:"id" validate:"required"`
	Reason *string `json:"reason"`
}

// WmsInventoryAllocationBatchReleaseReq 批量释放库存分配请求
type WmsInventoryAllocationBatchReleaseReq struct {
	IDs    []uint  `json:"ids" validate:"required,min=1,max=100"`
	Reason *string `json:"reason"`
}

// WmsInventoryAllocationPickReq 拣货确认请求
type WmsInventoryAllocationPickReq struct {
	ID        uint    `json:"id" validate:"required"`
	PickedQty float64 `json:"pickedQty" validate:"required,gt=0"`
	Remark    *string `json:"remark"`
}

// WmsInventoryAllocationBatchPickReq 批量拣货确认请求
type WmsInventoryAllocationBatchPickReq struct {
	Picks []WmsInventoryAllocationPickReq `json:"picks" validate:"required,min=1,max=100,dive"`
}

// WmsInventoryAllocationAutoReq 自动分配库存请求
type WmsInventoryAllocationAutoReq struct {
	OutboundDetailID   uint   `json:"outboundDetailId" validate:"required"`
	AllocationStrategy string `json:"allocationStrategy" validate:"required,oneof=FIFO LIFO FEFO NEAREST"`
	ForceAllocate      bool   `json:"forceAllocate"`
	MaxAllocations     *int   `json:"maxAllocations"`
}

// WmsInventoryAllocationBatchAutoReq 批量自动分配库存请求
type WmsInventoryAllocationBatchAutoReq struct {
	OutboundDetailIDs  []uint `json:"outboundDetailIds" validate:"required,min=1,max=100"`
	AllocationStrategy string `json:"allocationStrategy" validate:"required,oneof=FIFO LIFO FEFO NEAREST"`
	ForceAllocate      bool   `json:"forceAllocate"`
	MaxAllocations     *int   `json:"maxAllocations"`
}

// WmsInventoryAllocationOptimizeReq 优化分配请求
type WmsInventoryAllocationOptimizeReq struct {
	OutboundNotificationID uint   `json:"outboundNotificationId" validate:"required"`
	OptimizeType           string `json:"optimizeType" validate:"required,oneof=DISTANCE EXPIRY BATCH"`
}

// WmsInventoryAllocationStatsReq 库存分配统计请求
type WmsInventoryAllocationStatsReq struct {
	DateStart          *time.Time `json:"dateStart"`
	DateEnd            *time.Time `json:"dateEnd"`
	GroupBy            string     `json:"groupBy" validate:"oneof=day week month status strategy"`
	AllocationStrategy *string    `json:"allocationStrategy"`
}

// WmsInventoryAllocationExportReq 库存分配导出请求
type WmsInventoryAllocationExportReq struct {
	WmsInventoryAllocationQueryReq
	ExportFormat string   `json:"exportFormat" validate:"required,oneof=excel pdf"`
	ExportFields []string `json:"exportFields"`
}

// WmsInventoryAvailabilityCheckReq 库存可用性检查请求
type WmsInventoryAvailabilityCheckReq struct {
	ItemID             uint    `json:"itemId" validate:"required"`
	RequiredQty        float64 `json:"requiredQty" validate:"required,gt=0"`
	RequiredBatchNo    *string `json:"requiredBatchNo"`
	RequiredExpiryDate *string `json:"requiredExpiryDate"`
	WarehouseID        *uint   `json:"warehouseId"`
}

// WmsInventoryAvailabilityBatchCheckReq 批量库存可用性检查请求
type WmsInventoryAvailabilityBatchCheckReq struct {
	Items []WmsInventoryAvailabilityCheckReq `json:"items" validate:"required,min=1,max=100,dive"`
}

// WmsInventoryReservationReq 库存预占请求
type WmsInventoryReservationReq struct {
	InventoryID     uint       `json:"inventoryId" validate:"required"`
	ReservedQty     float64    `json:"reservedQty" validate:"required,gt=0"`
	ReservationType string     `json:"reservationType" validate:"required,oneof=OUTBOUND TRANSFER ADJUSTMENT"`
	ReferenceID     uint       `json:"referenceId" validate:"required"`
	ExpiryTime      *time.Time `json:"expiryTime"`
	Remark          *string    `json:"remark"`
}

// WmsInventoryReservationReleaseReq 释放库存预占请求
type WmsInventoryReservationReleaseReq struct {
	ReservationID uint    `json:"reservationId" validate:"required"`
	Reason        *string `json:"reason"`
}
