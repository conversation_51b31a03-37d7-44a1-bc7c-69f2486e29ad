package middleware

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/base64"
	"io"
	"strings"
	"time"

	"github.com/kataras/iris/v12"

	"backend/pkg/constant"
	"backend/pkg/logger"
	"backend/pkg/util"
)

// LoggerConfig 日志中间件配置
type LoggerConfig struct {
	// 是否启用
	Enabled bool `json:"enabled" yaml:"enabled"`
	// 是否记录请求体
	LogRequestBody bool `json:"logRequestBody" yaml:"logRequestBody"`
	// 是否记录响应体
	LogResponseBody bool `json:"logResponseBody" yaml:"logResponseBody"`
	// 请求体最大记录长度
	MaxRequestBodySize int `json:"maxRequestBodySize" yaml:"maxRequestBodySize"`
	// 响应体最大记录长度
	MaxResponseBodySize int `json:"maxResponseBodySize" yaml:"maxResponseBodySize"`
	// 排除的路径
	ExcludedPaths []string `json:"excludedPaths" yaml:"excludedPaths"`
	// 敏感头部，不记录其值
	SensitiveHeaders []string `json:"sensitiveHeaders" yaml:"sensitiveHeaders"`
	// 敏感参数，不记录其值
	SensitiveParams []string `json:"sensitiveParams" yaml:"sensitiveParams"`

	// --- 新增字段以匹配 audit_and_logger_refactor_plan.md ---
	// Skipper 函数，用于自定义跳过某些请求的日志记录
	Skipper func(ctx iris.Context) bool `json:"-" yaml:"-"` // 通常不在配置文件中直接定义函数
	// 从 Iris Context Values 中提取并记录的键
	MessageContextKeys []string `json:"messageContextKeys" yaml:"messageContextKeys"`
	// 从请求头中提取并记录的键
	MessageHeaderKeys []string `json:"messageHeaderKeys" yaml:"messageHeaderKeys"`
	// --- 结束新增字段 ---
}

// 默认日志配置
var defaultLoggerConfig = LoggerConfig{
	Enabled:             true,
	LogRequestBody:      true,
	LogResponseBody:     false,
	MaxRequestBodySize:  10240, // 10KB
	MaxResponseBodySize: 10240, // 10KB
	ExcludedPaths:       []string{"/static", "/favicon.ico", "/health"},
	SensitiveHeaders:    []string{"Authorization", "Cookie", "Set-Cookie"},
	SensitiveParams:     []string{"password", "token", "access_token", "refresh_token", "secret"},
	Skipper:             nil, // 默认不跳过
	MessageContextKeys:  nil, // 默认不从 context values 中额外提取
	MessageHeaderKeys:   nil, // 默认不从 headers 中额外提取特定键 (X-Request-ID 已单独处理)
}

// LoggerMiddleware 日志中间件
// 参数:
//   - config: 可选的日志配置，不提供则使用默认配置
//
// 返回:
//   - iris.Handler: Iris中间件处理函数
func LoggerMiddleware(config ...LoggerConfig) iris.Handler {
	// 使用默认配置
	cfg := defaultLoggerConfig
	// 如果提供了配置，则使用提供的配置
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(ctx iris.Context) {
		// 如果未启用，则跳过
		if !cfg.Enabled {
			ctx.Next()
			return
		}

		// 检查是否为排除的路径
		for _, path := range cfg.ExcludedPaths {
			if ctx.Path() == path || (len(path) > 0 && path[len(path)-1] == '/' && strings.HasPrefix(ctx.Path(), path)) {
				ctx.Next()
				return
			}
		}

		// +++ 实现 Skipper 逻辑 +++
		if cfg.Skipper != nil && cfg.Skipper(ctx) {
			ctx.Next()
			return
		}
		// +++ 结束 Skipper 逻辑 +++

		// 请求开始时间
		startTime := time.Now()

		// 获取请求ID (作为 TraceID)
		traceID := ctx.GetHeader("X-Request-ID")
		if traceID == "" {
			traceID = generateRequestID()
			ctx.Header("X-Request-ID", traceID)
		}

		// 将 TraceID 和 RequestStartTime 存入标准 Go Context
		reqCtx := ctx.Request().Context()
		reqCtxWithTrace := context.WithValue(reqCtx, constant.CONTEXT_TRACE_ID, traceID)
		reqCtxWithTraceAndStart := context.WithValue(reqCtxWithTrace, constant.CONTEXT_REQUEST_START_TIME, startTime)
		ctx.ResetRequest(ctx.Request().WithContext(reqCtxWithTraceAndStart))

		// 记录请求信息
		// 尝试从标准上下文中获取 ClientIP 和 UserID
		var clientIPForLog string
		// 使用更新后的上下文 reqCtxWithTraceAndStart 来获取 clientIP 和 userID
		retrievedClientIP, errClientIP := util.GetClientIPFromStdContext(reqCtxWithTraceAndStart)
		if errClientIP == nil && retrievedClientIP != "" {
			clientIPForLog = retrievedClientIP
		} else {
			clientIPForLog = ctx.RemoteAddr() // Fallback
			if errClientIP != nil {
				logger.GetLogger().Debug(reqCtxWithTraceAndStart, "LoggerMiddleware: ClientIP not found in context, using RemoteAddr.", logger.WithError(errClientIP))
			}
		}

		fields := logger.Fields{
			constant.CONTEXT_TRACE_ID: traceID,
			"remote_addr":             clientIPForLog,
			"method":                  ctx.Method(),
			"path":                    ctx.Path(),
			"query":                   maskSensitiveParams(ctx.Request().URL.Query(), cfg.SensitiveParams),
			"user_agent":              ctx.GetHeader("User-Agent"),
			"referer":                 ctx.GetHeader("Referer"),
		}

		userID64, errUser := util.GetUserIDFromStdContext(reqCtxWithTraceAndStart)
		if errUser == nil {
			fields[constant.CONTEXT_USER_ID] = userID64
		} else {
			logger.GetLogger().Debug(reqCtxWithTraceAndStart, "LoggerMiddleware: UserID not available for logging.", logger.WithError(errUser))
		}

		// +++ 实现 MessageContextKeys 逻辑 +++
		if len(cfg.MessageContextKeys) > 0 {
			for _, key := range cfg.MessageContextKeys {
				if value := reqCtxWithTraceAndStart.Value(key); value != nil {
					// 尝试将值转换为字符串，如果不是字符串类型，可以记录其原始类型或使用 fmt.Sprintf
					if strValue, ok := value.(string); ok {
						fields["ctx_"+key] = strValue
					} else {
						fields["ctx_"+key] = value // 或者 fmt.Sprintf("%v", value)
					}
				}
			}
		}
		// +++ 结束 MessageContextKeys 逻辑 +++

		// 记录请求头
		headers := make(map[string]string)
		for name := range ctx.Request().Header {
			value := ctx.GetHeader(name)
			// 跳过敏感头部
			if isSensitiveHeader(name, cfg.SensitiveHeaders) {
				headers[name] = "***"
			} else {
				headers[name] = value
			}
		}

		// +++ 实现 MessageHeaderKeys 逻辑 (合并到现有 headers 记录中或作为补充) +++
		if len(cfg.MessageHeaderKeys) > 0 {
			for _, key := range cfg.MessageHeaderKeys {
				// 如果这个 header 尚未在上面的通用循环中记录 (例如，它不是一个标准请求头但我们想显式记录)
				// 或者，如果希望它覆盖/优先于通用记录 (不常见)
				// 为简单起见，这里假设我们希望显式记录这些key，即使它们可能已存在于headers中
				// 但要注意，如果这些key也是敏感头部，它们也应该被屏蔽
				value := ctx.GetHeader(key)
				if isSensitiveHeader(key, cfg.SensitiveHeaders) {
					headers[key] = "***" // 确保敏感性检查也应用于这些特定头部
				} else {
					headers[key] = value
				}
			}
		}
		// +++ 结束 MessageHeaderKeys 逻辑 +++
		fields["headers"] = headers

		// 记录请求体
		if cfg.LogRequestBody && ctx.Request().ContentLength > 0 && ctx.Request().ContentLength < int64(cfg.MaxRequestBodySize) {
			bodyBytes, err := ctx.GetBody()
			if err == nil && len(bodyBytes) > 0 {
				fields["request_body"] = string(bodyBytes)

				// 重置请求体，使其可以被后续的处理器重新读取
				ctx.Request().Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// 创建日志对象
		log := logger.WithFields(fields)

		// 请求日志
		log.Info("请求接收")

		// 启用响应体记录（如果需要）
		if cfg.LogResponseBody {
			ctx.Record()
		}

		// 处理请求
		ctx.Next()

		// 响应时间
		duration := time.Since(startTime)

		// 获取响应数据
		statusCode := ctx.GetStatusCode()
		responseFields := logger.Fields{
			"status":       statusCode,
			"duration_ms":  duration.Milliseconds(),
			"content_type": ctx.GetContentType(),
		}

		// 记录响应体（如果启用）
		if cfg.LogResponseBody {
			responseBody := ctx.Recorder().Body()
			if len(responseBody) > 0 && len(responseBody) < cfg.MaxResponseBodySize {
				responseFields["response_body"] = string(responseBody)
			}
		}

		responseLog := log.WithFields(responseFields)

		// 根据状态码记录不同级别的日志
		if statusCode >= 500 {
			responseLog.Error("请求完成，服务器错误")
		} else if statusCode >= 400 {
			responseLog.Warn("请求完成，客户端错误")
		} else {
			responseLog.Info("请求成功完成")
		}
	}
}

// generateRequestID 生成请求ID
// 返回:
//   - string: 唯一的请求ID
func generateRequestID() string {
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		return time.Now().Format("20060102150405") + "-" + randString(8)
	}
	return base64.URLEncoding.EncodeToString(b)
}

// randString 生成指定长度的随机字符串
// 参数:
//   - n: 字符串长度
//
// 返回:
//   - string: 随机字符串
func randString(n int) string {
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, n)
	for i := range result {
		// 使用当前时间纳秒作为随机源
		result[i] = chars[time.Now().UnixNano()%int64(len(chars))]
		// 小延迟，确保下一次循环时间不同
		time.Sleep(time.Nanosecond)
	}
	return string(result)
}

// isSensitiveHeader 检查是否为敏感头部
// 参数:
//   - header: 头部名称
//   - sensitiveHeaders: 敏感头部列表
//
// 返回:
//   - bool: 是否为敏感头部
func isSensitiveHeader(header string, sensitiveHeaders []string) bool {
	for _, h := range sensitiveHeaders {
		if strings.EqualFold(h, header) {
			return true
		}
	}
	return false
}

// maskSensitiveParams 掩码敏感参数
// 参数:
//   - values: 参数值映射
//   - sensitiveParams: 敏感参数列表
//
// 返回:
//   - map[string][]string: 掩码后的参数值映射
func maskSensitiveParams(values map[string][]string, sensitiveParams []string) map[string][]string {
	result := make(map[string][]string)
	for key, value := range values {
		if isSensitiveParam(key, sensitiveParams) {
			result[key] = []string{"***"}
		} else {
			result[key] = value
		}
	}
	return result
}

// isSensitiveParam 检查是否为敏感参数
// 参数:
//   - param: 参数名称
//   - sensitiveParams: 敏感参数列表
//
// 返回:
//   - bool: 是否为敏感参数
func isSensitiveParam(param string, sensitiveParams []string) bool {
	for _, p := range sensitiveParams {
		if strings.EqualFold(p, param) {
			return true
		}
	}
	return false
}
