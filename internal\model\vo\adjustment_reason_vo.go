package vo

// AdjustmentReasonVO 调整原因视图对象
type AdjustmentReasonVO struct {
	Code        string `json:"code"`        // 原因代码
	Name        string `json:"name"`        // 原因名称
	Description string `json:"description"` // 原因描述
	IsActive    bool   `json:"isActive"`    // 是否激活
}

// NewAdjustmentReasonVO 创建调整原因VO
func NewAdjustmentReasonVO(code, name, description string, isActive bool) *AdjustmentReasonVO {
	return &AdjustmentReasonVO{
		Code:        code,
		Name:        name,
		Description: description,
		IsActive:    isActive,
	}
}
