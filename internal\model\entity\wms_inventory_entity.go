package entity

import (
	"time"
)

// WmsInventoryStatus 定义库存状态常量
type WmsInventoryStatus string

const (
	InventoryStatusAvailable         WmsInventoryStatus = "AVAILABLE"          // 可用
	InventoryStatusQualityInspection WmsInventoryStatus = "QUALITY_INSPECTION" // 待检
	InventoryStatusHold              WmsInventoryStatus = "HOLD"               // 通用冻结
	InventoryStatusFrozenQC          WmsInventoryStatus = "FROZEN_QC"          // 质检冻结
	InventoryStatusFrozenCount       WmsInventoryStatus = "FROZEN_COUNT"       // 盘点冻结
	InventoryStatusFrozenCustomer    WmsInventoryStatus = "FROZEN_CUSTOMER"    // 客户冻结
	InventoryStatusDamaged           WmsInventoryStatus = "DAMAGED"            // 损坏
	InventoryStatusExpired           WmsInventoryStatus = "EXPIRED"            // 过期
	InventoryStatusAllocated         WmsInventoryStatus = "ALLOCATED"          // 已分配 (给拣货任务/订单)
	InventoryStatusPendingPutaway    WmsInventoryStatus = "PENDING_PUTAWAY"    // 待上架 (在暂存区)
	InventoryStatusPendingPick       WmsInventoryStatus = "PENDING_PICK"       // 待拣货 (同 ALLOCATED)
	InventoryStatusInTransit         WmsInventoryStatus = "IN_TRANSIT"         // 库内移动中
	InventoryStatusPacking           WmsInventoryStatus = "PACKING"            // 打包中
	// ... 根据 database_schema.md 或实际需要添加更多状态
)

// WmsInventory 对应数据库中的 wms_inventory 表
// 存储实时库存信息
type WmsInventory struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	WarehouseID    uint       `gorm:"column:warehouse_id;not null;index:idx_inv_loc_item_batch_status;comment:仓库ID" json:"warehouseId"`                           // 仓库ID
	LocationID     uint       `gorm:"column:location_id;not null;index:idx_inv_loc_item_batch_status;comment:库位ID" json:"locationId"`                             // 库位ID (实际存储货物的最小单元, 通常是 SLOT)
	ItemID         uint       `gorm:"column:item_id;not null;index:idx_inv_loc_item_batch_status;comment:物料ID" json:"itemId"`                                     // 物料ID
	ClientID       uint       `gorm:"column:client_id;not null;index:idx_inv_loc_item_batch_status;comment:客户ID" json:"clientId"`                                 // 客户/货主ID (重要维度)
	BatchNo        *string    `gorm:"column:batch_no;type:varchar(100);index:idx_inv_loc_item_batch_status;comment:批次号" json:"batchNo"`                           // 批次号 (允许为空)
	ProductionDate *time.Time `gorm:"column:production_date;type:date;comment:生产日期" json:"productionDate"`                                                        // 生产日期
	ExpiryDate     *time.Time `gorm:"column:expiry_date;type:date;index;comment:过期日期" json:"expiryDate"`                                                          // 过期日期
	Quantity       float64    `gorm:"column:quantity;type:numeric(12,4);not null;comment:现有数量" json:"quantity"`                                                   // 当前库位的实际数量 (可用 + 冻结 + 分配)
	AllocatedQty   float64    `gorm:"column:allocated_qty;type:numeric(12,4);not null;default:0;comment:已分配数量" json:"allocatedQty"`                               // 已被出库单等分配的数量
	FrozenQty      float64    `gorm:"column:frozen_qty;type:numeric(12,4);not null;default:0;comment:冻结数量" json:"frozenQty"`                                      // 因质检、盘点等原因冻结的数量
	UnitOfMeasure  string     `gorm:"column:unit_of_measure;type:varchar(20);not null;comment:数量单位" json:"unitOfMeasure"`                                         // 数量单位 (基本单位)
	Status         string     `gorm:"column:status;type:varchar(20);not null;index:idx_inv_loc_item_batch_status;default:'AVAILABLE';comment:库存状态" json:"status"` // 库存状态 (使用常量)
	Remark         *string    `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                                                           // 备注
	LastUpdated    time.Time  `gorm:"column:last_updated;autoUpdateTime;comment:最后更新时间" json:"lastUpdated"`                                                       // 记录最后更新时间
	// 定义复合唯一索引，确保同一库位、同一物料、同一批次、同一状态的库存只有一条记录
	// gorm:"uniqueIndex:idx_inv_unique,columns:warehouse_id,location_id,item_id,batch_no,status"

	// 关联字段 - 移除显式外键定义以避免GORM创建错误的约束
	// 这些关联关系可以在Service层通过手动查询来实现
	// Item      *MtlItem     `gorm:"foreignKey:ItemID;references:ID" json:"item,omitempty"`           // 关联的物料
	// Location  *WmsLocation `gorm:"foreignKey:LocationID;references:ID" json:"location,omitempty"`   // 关联的库位
	// Warehouse *WmsLocation `gorm:"foreignKey:WarehouseID;references:ID" json:"warehouse,omitempty"` // 关联的仓库
}

// TableName 指定数据库表名方法
func (WmsInventory) TableName() string {
	return "wms_inventory"
}

// AvailableQuantity 计算可用数量
// 可用数量 = 现有数量 - 已分配数量 - 冻结数量
func (inv *WmsInventory) AvailableQuantity() float64 {
	available := inv.Quantity - inv.AllocatedQty - inv.FrozenQty
	if available < 0 {
		return 0 // 避免负数
	}
	return available
}

// GetStatusName 获取库存状态名称
func (inv *WmsInventory) GetStatusName() string {
	switch WmsInventoryStatus(inv.Status) {
	case InventoryStatusAvailable:
		return "可用"
	case InventoryStatusQualityInspection:
		return "待检"
	case InventoryStatusHold:
		return "冻结"
	case InventoryStatusFrozenQC:
		return "质检冻结"
	case InventoryStatusFrozenCount:
		return "盘点冻结"
	case InventoryStatusFrozenCustomer:
		return "客户冻结"
	case InventoryStatusDamaged:
		return "损坏"
	case InventoryStatusExpired:
		return "过期"
	case InventoryStatusAllocated:
		return "已分配"
	case InventoryStatusPendingPutaway:
		return "待上架"
	case InventoryStatusPendingPick:
		return "待拣货"
	case InventoryStatusInTransit:
		return "移动中"
	case InventoryStatusPacking:
		return "打包中"
	default:
		return inv.Status
	}
}

// IsExpired 判断是否过期
func (inv *WmsInventory) IsExpired() bool {
	if inv.ExpiryDate == nil {
		return false
	}
	return inv.ExpiryDate.Before(time.Now())
}

// IsNearExpiry 判断是否临近过期（30天内）
func (inv *WmsInventory) IsNearExpiry() bool {
	if inv.ExpiryDate == nil {
		return false
	}
	return inv.ExpiryDate.Before(time.Now().AddDate(0, 0, 30))
}

// DaysToExpiry 计算距离过期的天数
func (inv *WmsInventory) DaysToExpiry() *int {
	if inv.ExpiryDate == nil {
		return nil
	}
	days := int(inv.ExpiryDate.Sub(time.Now()).Hours() / 24)
	return &days
}
