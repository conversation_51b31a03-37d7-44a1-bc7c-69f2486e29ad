package controller

import (
	"backend/pkg/errors"
	"fmt"
	"io"
	"mime/multipart" // Import for multipart.File
	"net/http"       // Import for http constants
	"strconv"

	"github.com/kataras/iris/v12"
)

// FileController 文件控制器 (修正名称为 FileControllerImpl)
type FileControllerImpl struct {
	*BaseControllerImpl // 嵌入基础控制器
	// ServiceManager 字段不再需要，可以通过 GetServiceManager() 获取
}

// NewFileController 创建文件控制器 (修正名称为 NewFileControllerImpl)
func NewFileControllerImpl(cm *ControllerManager) *FileControllerImpl {
	return &FileControllerImpl{
		BaseControllerImpl: NewBaseController(cm), // 初始化基础控制器
	}
}

// RegisterRoutes 方法移除，路由注册应集中处理
// func (fc *FileControllerImpl) RegisterRoutes(party iris.Party) {
// 	fileParty := party.Party("/files")
// 	fileParty.Post("/upload", fc.uploadFile)
// 	fileParty.Get("/{id:uint}/download", fc.downloadFile)
// }

// uploadFile 处理文件上传 (方法接收者改为 fc)
func (fc *FileControllerImpl) UploadFile(ctx iris.Context) {
	opName := "文件上传"

	// 从基础控制器获取 ServiceManager 和 FileService
	fileService := fc.GetServiceManager().GetFileService()
	if fileService == nil {
		fc.HandleError(ctx, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "文件服务不可用"), opName)
		return
	}

	// 0. 获取上传者ID (使用基础控制器辅助方法)
	uploaderIDUint64, authErr := fc.GetUserIDFromContext(ctx)
	if authErr != nil {
		fc.HandleError(ctx, authErr, opName)
		return
	}
	uploaderID := uint(uploaderIDUint64)

	// 1. 解析表单数据
	maxSize := int64(100 << 20) // 100MB (可以从配置读取)
	err := ctx.Request().ParseMultipartForm(maxSize)
	if err != nil {
		// 使用基础控制器方法处理错误
		fc.FailWithError(ctx, errors.NewParamError(errors.CODE_PARAMS_FORMAT_ERROR, "无法解析表单数据或文件过大").WithCause(err))
		return
	}

	// 2. 获取文件
	file, header, err := ctx.FormFile("file") // 表单字段名为 "file"
	if err != nil {
		if err == http.ErrMissingFile { // 特别处理缺少文件的情况
			fc.FailWithError(ctx, errors.NewParamError(errors.CODE_PARAMS_MISSING, "缺少文件字段 'file'"))
		} else {
			fc.FailWithError(ctx, errors.NewParamError(errors.CODE_PARAMS_INVALID, "获取上传文件失败").WithCause(err))
		}
		return
	}
	// 使用 defer 关闭文件，放在获取成功之后
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			// 使用基础控制器的 Logger
			fc.GetLogger().Errorf("关闭上传的文件失败: %v", err)
		}
	}(file)

	// 3. 获取业务类型和业务ID (从表单字段获取)
	businessType := ctx.FormValue("businessType")
	if businessType == "" {
		fc.FailWithError(ctx, errors.NewParamError(errors.CODE_PARAMS_MISSING, "缺少业务类型字段 'businessType'"))
		return
	}

	businessIDStr := ctx.FormValue("businessID") // 可选字段
	var businessIDPtr *uint
	if businessIDStr != "" {
		id, convErr := strconv.ParseUint(businessIDStr, 10, 64)
		if convErr != nil {
			fc.FailWithError(ctx, errors.NewParamError(errors.CODE_PARAMS_FORMAT_ERROR, "业务ID 'businessID' 格式无效").WithCause(convErr))
			return
		}
		idUint := uint(id)
		businessIDPtr = &idUint
	}

	// 4. 调用 FileService 上传文件
	metadata, serviceErr := fileService.UploadFile(ctx.Request().Context(), uploaderID, file, header, businessType, businessIDPtr)
	if serviceErr != nil {
		fc.HandleError(ctx, serviceErr, opName) // 使用 HandleError 处理 Service 错误
		return
	}

	// 5. 返回成功响应 (使用基础控制器方法)
	fc.Success(ctx, metadata)
}

// downloadFile 处理文件下载 (方法接收者改为 fc)
func (fc *FileControllerImpl) DownloadFile(ctx iris.Context) {
	opName := "文件下载"
	// 从基础控制器获取 ServiceManager 和 FileService
	fileService := fc.GetServiceManager().GetFileService()
	if fileService == nil {
		fc.HandleError(ctx, errors.NewSystemError(errors.CODE_SYSTEM_INTERNAL, "文件服务不可用"), opName)
		return
	}

	// 1. 获取 fileID
	fileID, err := ctx.Params().GetUint("id")
	if err != nil {
		fc.FailWithError(ctx, errors.NewParamError(errors.CODE_PARAMS_INVALID, "无效的文件ID").WithCause(err))
		return
	}

	// 2. 调用 fileService.DownloadFile
	metadata, fileStream, serviceErr := fileService.DownloadFile(ctx.Request().Context(), fileID)
	if serviceErr != nil {
		fc.HandleError(ctx, serviceErr, opName) // 使用 HandleError 处理 Service 错误
		return
	}
	// 使用 defer 关闭文件流
	defer func(fileStream io.ReadCloser) {
		err := fileStream.Close()
		if err != nil {
			fc.GetLogger().Errorf("关闭下载文件流失败 (ID: %d): %v", fileID, err)
		}
	}(fileStream)

	// 3. 设置响应头
	disposition := fmt.Sprintf("attachment; filename=\"%s\"", metadata.OriginalName)
	ctx.Header("Content-Disposition", disposition)

	contentType := metadata.FileType
	if contentType == "" {
		contentType = "application/octet-stream"
	}
	ctx.Header("Content-Type", contentType)

	if metadata.FileSize > 0 {
		ctx.Header("Content-Length", strconv.FormatInt(metadata.FileSize, 10))
	}

	// 4. 使用 ctx.StreamWriter 将文件流写入响应体
	err = ctx.StreamWriter(func(w io.Writer) error {
		_, copyErr := io.Copy(w, fileStream)
		return copyErr
	})

	if err != nil {
		// 使用基础控制器的 Logger
		fc.GetLogger().WithError(err).Error(ctx, opName+" - 文件流写入响应失败", "fileID", fileID)
		// 如果流写入过程中出错，通常无法再安全地向客户端发送错误响应
		// 因为可能已经发送了部分内容和响应头
		// Iris 通常会处理这种情况，记录错误即可
	}
}
