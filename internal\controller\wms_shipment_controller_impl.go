package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
)

// WmsShipmentController 定义发运单控制器接口
type WmsShipmentController interface {
	// 基础CRUD操作
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 批量操作
	BatchCreate(ctx iris.Context)
	BatchShip(ctx iris.Context)

	// 发运流程
	PackShipment(ctx iris.Context)
	ShipConfirm(ctx iris.Context)
	UpdateTrackingStatus(ctx iris.Context)
	DeliveryConfirm(ctx iris.Context)

	// 运费管理
	CalculateShippingCost(ctx iris.Context)

	// 标签打印
	PrintShippingLabel(ctx iris.Context)

	// 统计分析
	GetStats(ctx iris.Context)

	// 导出
	ExportToExcel(ctx iris.Context)

	// 业务查询
	GetByShipmentNo(ctx iris.Context)
	GetByTrackingNo(ctx iris.Context)
	GetByPickingTaskID(ctx iris.Context)
	GetPendingPacking(ctx iris.Context)
	GetReadyToShip(ctx iris.Context)
	GetInTransit(ctx iris.Context)
	GetOverdueShipments(ctx iris.Context)

	// 承运商管理
	GetCarriers(ctx iris.Context)
}

// wmsShipmentControllerImpl 发运单控制器实现
type WmsShipmentControllerImpl struct {
	BaseControllerImpl
	wmsShipmentService service.WmsShipmentService
}

// NewWmsShipmentControllerImpl 创建发运单控制器
func NewWmsShipmentControllerImpl(cm *ControllerManager) *WmsShipmentControllerImpl {
	return &WmsShipmentControllerImpl{
		BaseControllerImpl: *NewBaseController(cm),
		wmsShipmentService: cm.GetServiceManager().GetWmsShipmentService(),
	}
}

// Create 创建发运单
// POST /api/v1/wms/shipments
func (ctrl *WmsShipmentControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsShipmentService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新发运单
// PUT /api/v1/wms/shipments/{id:uint}
func (ctrl *WmsShipmentControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsShipmentUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsShipmentService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除发运单
// DELETE /api/v1/wms/shipments/{id:uint}
func (ctrl *WmsShipmentControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsShipmentService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个发运单
// GET /api/v1/wms/shipments/{id:uint}
func (ctrl *WmsShipmentControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsShipmentByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsShipmentService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取发运单分页
// GET /api/v1/wms/shipments
func (ctrl *WmsShipmentControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsShipmentsPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)
	req.PageQuery.PageNum = pageQueryFromBuild.PageNum
	req.PageQuery.PageSize = pageQueryFromBuild.PageSize
	req.PageQuery.Sort = pageQueryFromBuild.Sort

	pageResult, err := ctrl.wmsShipmentService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// PackShipment 打包确认
// POST /api/v1/wms/shipments/{id:uint}/pack
func (ctrl *WmsShipmentControllerImpl) PackShipment(ctx iris.Context) {
	const opName = "PackWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsShipmentPackReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsShipmentService.PackShipment(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// ShipConfirm 发运确认
// POST /api/v1/wms/shipments/{id:uint}/ship
func (ctrl *WmsShipmentControllerImpl) ShipConfirm(ctx iris.Context) {
	const opName = "ShipConfirmWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsShipmentShipReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsShipmentService.ShipConfirm(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// UpdateTrackingStatus 更新运输状态
// POST /api/v1/wms/shipments/{id:uint}/tracking
func (ctrl *WmsShipmentControllerImpl) UpdateTrackingStatus(ctx iris.Context) {
	const opName = "UpdateTrackingStatusWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsShipmentTrackReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsShipmentService.UpdateTrackingStatus(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// DeliveryConfirm 签收确认
// POST /api/v1/wms/shipments/{id:uint}/delivery
func (ctrl *WmsShipmentControllerImpl) DeliveryConfirm(ctx iris.Context) {
	const opName = "DeliveryConfirmWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsShipmentDeliverReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsShipmentService.DeliveryConfirm(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// CalculateShippingCost 计算运费
// POST /api/v1/wms/shipments/calculate-cost
func (ctrl *WmsShipmentControllerImpl) CalculateShippingCost(ctx iris.Context) {
	const opName = "CalculateShippingCostWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentCalculateCostReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsShipmentService.CalculateShippingCost(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// PrintShippingLabel 打印发运标签
// POST /api/v1/wms/shipments/print-label
func (ctrl *WmsShipmentControllerImpl) PrintShippingLabel(ctx iris.Context) {
	const opName = "PrintShippingLabelWmsShipment"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentLabelPrintReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsShipmentService.PrintShippingLabel(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetByShipmentNo 根据发运单号获取
// GET /api/v1/wms/shipments/by-shipment-no/{shipmentNo:string}
func (ctrl *WmsShipmentControllerImpl) GetByShipmentNo(ctx iris.Context) {
	const opName = "GetWmsShipmentByShipmentNo"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	shipmentNo := ctx.Params().Get("shipmentNo")
	if shipmentNo == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "发运单号不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsShipmentService.GetByShipmentNo(ctx.Request().Context(), shipmentNo)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// GetByTrackingNo 根据运单号获取
// GET /api/v1/wms/shipments/by-tracking-no/{trackingNo:string}
func (ctrl *WmsShipmentControllerImpl) GetByTrackingNo(ctx iris.Context) {
	const opName = "GetWmsShipmentByTrackingNo"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	trackingNo := ctx.Params().Get("trackingNo")
	if trackingNo == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "运单号不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsShipmentService.GetByTrackingNo(ctx.Request().Context(), trackingNo)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// GetByPickingTaskID 根据拣货任务ID获取
// GET /api/v1/wms/shipments/by-picking-task/{pickingTaskID:uint}
func (ctrl *WmsShipmentControllerImpl) GetByPickingTaskID(ctx iris.Context) {
	const opName = "GetWmsShipmentByPickingTaskID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	pickingTaskID, err := ctrl.GetPathUintParamWithError(ctx, "pickingTaskID")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	results, err := ctrl.wmsShipmentService.GetByPickingTaskID(ctx.Request().Context(), uint(pickingTaskID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetPendingPacking 获取待打包的发运单
// GET /api/v1/wms/shipments/pending-packing
func (ctrl *WmsShipmentControllerImpl) GetPendingPacking(ctx iris.Context) {
	const opName = "GetPendingPackingWmsShipments"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	results, err := ctrl.wmsShipmentService.GetPendingPacking(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetReadyToShip 获取待发运的发运单
// GET /api/v1/wms/shipments/ready-to-ship
func (ctrl *WmsShipmentControllerImpl) GetReadyToShip(ctx iris.Context) {
	const opName = "GetReadyToShipWmsShipments"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	results, err := ctrl.wmsShipmentService.GetReadyToShip(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// BatchCreate 批量创建发运单
// POST /api/v1/wms/shipments/batch-create
func (ctrl *WmsShipmentControllerImpl) BatchCreate(ctx iris.Context) {
	const opName = "BatchCreateWmsShipments"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentBatchCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsShipmentService.BatchCreate(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// BatchShip 批量发运确认
// POST /api/v1/wms/shipments/batch-ship
func (ctrl *WmsShipmentControllerImpl) BatchShip(ctx iris.Context) {
	const opName = "BatchShipWmsShipments"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentBatchShipReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsShipmentService.BatchShip(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetStats 获取发运统计数据
// GET /api/v1/wms/shipments/stats
func (ctrl *WmsShipmentControllerImpl) GetStats(ctx iris.Context) {
	const opName = "GetWmsShipmentStats"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentStatsReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsShipmentService.GetStats(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// ExportToExcel 导出发运单到Excel
// POST /api/v1/wms/shipments/export
func (ctrl *WmsShipmentControllerImpl) ExportToExcel(ctx iris.Context) {
	const opName = "ExportWmsShipmentsToExcel"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsShipmentExportReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	excelData, err := ctrl.wmsShipmentService.ExportToExcel(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", "attachment; filename=shipments.xlsx")
	ctx.Header("Content-Length", string(rune(len(excelData))))

	// 返回Excel文件
	ctx.Write(excelData)
}

// GetInTransit 获取运输中的发运单
// GET /api/v1/wms/shipments/in-transit
func (ctrl *WmsShipmentControllerImpl) GetInTransit(ctx iris.Context) {
	const opName = "GetInTransitWmsShipments"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	results, err := ctrl.wmsShipmentService.GetInTransit(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetOverdueShipments 获取过期的发运单
// GET /api/v1/wms/shipments/overdue
func (ctrl *WmsShipmentControllerImpl) GetOverdueShipments(ctx iris.Context) {
	const opName = "GetOverdueWmsShipments"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	results, err := ctrl.wmsShipmentService.GetOverdueShipments(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetCarriers 获取承运商列表
// GET /api/v1/wms/shipments/carriers
func (ctrl *WmsShipmentControllerImpl) GetCarriers(ctx iris.Context) {
	const opName = "GetWmsCarriers"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_shipment")

	var req dto.WmsCarrierQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	results, err := ctrl.wmsShipmentService.GetCarriers(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}
