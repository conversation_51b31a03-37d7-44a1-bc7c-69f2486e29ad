package repository

import (
	"context"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// WmsInventoryMovementRepository 库存移动数据访问接口
type WmsInventoryMovementRepository interface {
	BaseRepository[entity.WmsInventoryMovement, uint]

	// 分页查询
	GetPage(ctx context.Context, query *dto.WmsInventoryMovementQueryReq) (*response.PageResult, error)

	// 业务特定查询方法
	FindByMovementNo(ctx context.Context, movementNo string) (*entity.WmsInventoryMovement, error)
	FindByItemID(ctx context.Context, itemId uint) ([]entity.WmsInventoryMovement, error)
	FindByLocationID(ctx context.Context, locationId uint) ([]entity.WmsInventoryMovement, error)
	FindByOperatorID(ctx context.Context, operatorId uint) ([]entity.WmsInventoryMovement, error)
	FindByStatus(ctx context.Context, status entity.WmsInventoryMovementStatus) ([]entity.WmsInventoryMovement, error)
	FindByMovementType(ctx context.Context, movementType entity.WmsInventoryMovementType) ([]entity.WmsInventoryMovement, error)
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]entity.WmsInventoryMovement, error)

	// 复杂查询
	FindPendingMovements(ctx context.Context, warehouseId *uint) ([]entity.WmsInventoryMovement, error)
	FindCompletedMovements(ctx context.Context, startDate, endDate time.Time, warehouseId *uint) ([]entity.WmsInventoryMovement, error)
	FindMovementsByRoute(ctx context.Context, fromLocationId, toLocationId uint) ([]entity.WmsInventoryMovement, error)

	// 统计查询
	CountByStatus(ctx context.Context, status entity.WmsInventoryMovementStatus) (int64, error)
	CountByMovementType(ctx context.Context, movementType entity.WmsInventoryMovementType) (int64, error)
	CountByOperator(ctx context.Context, operatorId uint, startDate, endDate time.Time) (int64, error)

	// 批量操作
	BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsInventoryMovementStatus) error
	BatchStart(ctx context.Context, ids []uint, operatorId uint) error
	BatchComplete(ctx context.Context, ids []uint) error
}

// WmsInventoryMovementQueryBuilder 库存移动查询构建器接口
type WmsInventoryMovementQueryBuilder interface {
	// 条件过滤
	WhereMovementNo(movementNo string) WmsInventoryMovementQueryBuilder
	WhereItemID(itemId uint) WmsInventoryMovementQueryBuilder
	WhereFromLocationID(fromLocationId uint) WmsInventoryMovementQueryBuilder
	WhereToLocationID(toLocationId uint) WmsInventoryMovementQueryBuilder
	WhereOperatorID(operatorId uint) WmsInventoryMovementQueryBuilder
	WhereStatus(status entity.WmsInventoryMovementStatus) WmsInventoryMovementQueryBuilder
	WhereMovementType(movementType entity.WmsInventoryMovementType) WmsInventoryMovementQueryBuilder
	WhereBatchNo(batchNo string) WmsInventoryMovementQueryBuilder
	WhereQuantityRange(minQty, maxQty float64) WmsInventoryMovementQueryBuilder
	WhereDateRange(startDate, endDate time.Time) WmsInventoryMovementQueryBuilder
	WhereWarehouseID(warehouseId uint) WmsInventoryMovementQueryBuilder

	// 关联查询
	WithItem() WmsInventoryMovementQueryBuilder
	WithFromLocation() WmsInventoryMovementQueryBuilder
	WithToLocation() WmsInventoryMovementQueryBuilder
	WithOperator() WmsInventoryMovementQueryBuilder

	// 排序
	OrderByCreatedAt(desc bool) WmsInventoryMovementQueryBuilder
	OrderByStartedAt(desc bool) WmsInventoryMovementQueryBuilder
	OrderByCompletedAt(desc bool) WmsInventoryMovementQueryBuilder
	OrderByQuantity(desc bool) WmsInventoryMovementQueryBuilder

	// 执行查询
	Find() ([]entity.WmsInventoryMovement, error)
	First() (*entity.WmsInventoryMovement, error)
	Count() (int64, error)
	Paginate(pageNum, pageSize int) ([]entity.WmsInventoryMovement, int64, error)
}

// WmsInventoryMovementRepositoryImpl 库存移动数据访问实现
type WmsInventoryMovementRepositoryImpl struct {
	BaseRepository[entity.WmsInventoryMovement, uint]
	db *gorm.DB
}

// NewWmsInventoryMovementRepository 创建库存移动数据访问实例
func NewWmsInventoryMovementRepository(db *gorm.DB) WmsInventoryMovementRepository {
	baseRepo := NewBaseRepository[entity.WmsInventoryMovement, uint](db)
	return &WmsInventoryMovementRepositoryImpl{
		BaseRepository: baseRepo,
		db:             db,
	}
}

// GetPage 分页查询库存移动
func (r *WmsInventoryMovementRepositoryImpl) GetPage(ctx context.Context, query *dto.WmsInventoryMovementQueryReq) (*response.PageResult, error) {
	db := r.db.WithContext(ctx).Model(&entity.WmsInventoryMovement{})

	// 应用查询条件
	if query.MovementNo != nil && *query.MovementNo != "" {
		db = db.Where("movement_no LIKE ?", "%"+*query.MovementNo+"%")
	}
	if query.ItemID != nil {
		db = db.Where("item_id = ?", *query.ItemID)
	}
	if query.ItemSku != nil && *query.ItemSku != "" {
		db = db.Joins("JOIN wms_item ON wms_inventory_movement.item_id = wms_item.id").
			Where("wms_item.sku LIKE ?", "%"+*query.ItemSku+"%")
	}
	if query.FromLocationID != nil {
		db = db.Where("from_location_id = ?", *query.FromLocationID)
	}
	if query.ToLocationID != nil {
		db = db.Where("to_location_id = ?", *query.ToLocationID)
	}
	if query.BatchNo != nil && *query.BatchNo != "" {
		db = db.Where("batch_no LIKE ?", "%"+*query.BatchNo+"%")
	}
	if query.MovementType != nil {
		db = db.Where("movement_type = ?", *query.MovementType)
	}
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}
	if query.OperatorID != nil {
		db = db.Where("operator_id = ?", *query.OperatorID)
	}
	if query.CreatedStart != nil {
		db = db.Where("created_at >= ?", *query.CreatedStart)
	}
	if query.CreatedEnd != nil {
		db = db.Where("created_at <= ?", *query.CreatedEnd)
	}
	if query.WarehouseID != nil {
		db = db.Joins("JOIN wms_location fl ON wms_inventory_movement.from_location_id = fl.id").
			Where("fl.warehouse_id = ?", *query.WarehouseID)
	}

	// 预加载关联数据
	db = db.Preload("Item").
		Preload("FromLocation").
		Preload("ToLocation").
		Preload("Operator")

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, err
	}

	// 应用分页
	offset := (query.PageNum - 1) * query.PageSize
	db = db.Offset(offset).Limit(query.PageSize)

	// 应用排序
	if len(query.Sort) > 0 {
		for _, sort := range query.Sort {
			order := sort.Field
			if sort.Order == "desc" {
				order += " DESC"
			} else {
				order += " ASC"
			}
			db = db.Order(order)
		}
	} else {
		db = db.Order("created_at DESC")
	}

	// 执行查询
	var movements []entity.WmsInventoryMovement
	if err := db.Find(&movements).Error; err != nil {
		return nil, err
	}

	// 计算总页数
	pages := int((total + int64(query.PageSize) - 1) / int64(query.PageSize))

	return &response.PageResult{
		List:     movements,
		Total:    total,
		PageNum:  query.PageNum,
		PageSize: query.PageSize,
		Pages:    pages,
	}, nil
}

// CreateWithTx 在事务中创建库存移动记录
func (r *WmsInventoryMovementRepositoryImpl) CreateWithTx(tx *gorm.DB, movement *entity.WmsInventoryMovement) error {
	return tx.Create(movement).Error
}

// UpdateWithTx 在事务中更新库存移动记录
func (r *WmsInventoryMovementRepositoryImpl) UpdateWithTx(tx *gorm.DB, movement *entity.WmsInventoryMovement) error {
	return tx.Save(movement).Error
}

// GetByIDWithAssociations 根据ID获取库存移动记录（包含关联数据）
func (r *WmsInventoryMovementRepositoryImpl) GetByIDWithAssociations(id uint) (*entity.WmsInventoryMovement, error) {
	var movement entity.WmsInventoryMovement
	err := r.db.Preload("Item").
		Preload("FromLocation").
		Preload("ToLocation").
		Preload("Operator").
		First(&movement, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &movement, nil
}

// FindByMovementNo 根据移动单号查询
func (r *WmsInventoryMovementRepositoryImpl) FindByMovementNo(ctx context.Context, movementNo string) (*entity.WmsInventoryMovement, error) {
	var movement entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("movement_no = ?", movementNo).First(&movement).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &movement, nil
}

// FindByItemID 根据物料ID查询移动记录
func (r *WmsInventoryMovementRepositoryImpl) FindByItemID(ctx context.Context, itemId uint) ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("item_id = ?", itemId).
		Order("created_at DESC").
		Find(&movements).Error
	return movements, err
}

// FindPendingMovements 查找待执行的移动任务
func (r *WmsInventoryMovementRepositoryImpl) FindPendingMovements(ctx context.Context, warehouseId *uint) ([]entity.WmsInventoryMovement, error) {
	query := r.db.WithContext(ctx).Where("status = ?", entity.MovementStatusPending)

	if warehouseId != nil {
		query = query.Joins("JOIN wms_location fl ON wms_inventory_movement.from_location_id = fl.id").
			Where("fl.warehouse_id = ?", *warehouseId)
	}

	var movements []entity.WmsInventoryMovement
	err := query.Preload("Item").
		Preload("FromLocation").
		Preload("ToLocation").
		Order("created_at ASC").
		Find(&movements).Error
	return movements, err
}

// BeginTx 开始事务
func (r *WmsInventoryMovementRepositoryImpl) BeginTx() *gorm.DB {
	return r.db.Begin()
}

// NewQueryBuilder 创建查询构建器
func (r *WmsInventoryMovementRepositoryImpl) NewQueryBuilder() WmsInventoryMovementQueryBuilder {
	return &WmsInventoryMovementQueryBuilderImpl{
		db:    r.db,
		query: r.db.Model(&entity.WmsInventoryMovement{}),
	}
}

// 实现其他缺失的方法
func (r *WmsInventoryMovementRepositoryImpl) FindByLocationID(ctx context.Context, locationId uint) ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("from_location_id = ? OR to_location_id = ?", locationId, locationId).
		Order("created_at DESC").
		Find(&movements).Error
	return movements, err
}

func (r *WmsInventoryMovementRepositoryImpl) FindByOperatorID(ctx context.Context, operatorId uint) ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("operator_id = ?", operatorId).
		Order("created_at DESC").
		Find(&movements).Error
	return movements, err
}

func (r *WmsInventoryMovementRepositoryImpl) FindByStatus(ctx context.Context, status entity.WmsInventoryMovementStatus) ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("status = ?", status).
		Order("created_at DESC").
		Find(&movements).Error
	return movements, err
}

func (r *WmsInventoryMovementRepositoryImpl) FindByMovementType(ctx context.Context, movementType entity.WmsInventoryMovementType) ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("movement_type = ?", movementType).
		Order("created_at DESC").
		Find(&movements).Error
	return movements, err
}

func (r *WmsInventoryMovementRepositoryImpl) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Order("created_at DESC").
		Find(&movements).Error
	return movements, err
}

func (r *WmsInventoryMovementRepositoryImpl) FindCompletedMovements(ctx context.Context, startDate, endDate time.Time, warehouseId *uint) ([]entity.WmsInventoryMovement, error) {
	query := r.db.WithContext(ctx).Where("status = ? AND completed_at BETWEEN ? AND ?", entity.MovementStatusCompleted, startDate, endDate)

	if warehouseId != nil {
		query = query.Joins("JOIN wms_location fl ON wms_inventory_movement.from_location_id = fl.id").
			Where("fl.warehouse_id = ?", *warehouseId)
	}

	var movements []entity.WmsInventoryMovement
	err := query.Preload("Item").
		Preload("FromLocation").
		Preload("ToLocation").
		Order("completed_at DESC").
		Find(&movements).Error
	return movements, err
}

func (r *WmsInventoryMovementRepositoryImpl) FindMovementsByRoute(ctx context.Context, fromLocationId, toLocationId uint) ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := r.db.WithContext(ctx).Where("from_location_id = ? AND to_location_id = ?", fromLocationId, toLocationId).
		Order("created_at DESC").
		Find(&movements).Error
	return movements, err
}

func (r *WmsInventoryMovementRepositoryImpl) CountByStatus(ctx context.Context, status entity.WmsInventoryMovementStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.WmsInventoryMovement{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

func (r *WmsInventoryMovementRepositoryImpl) CountByMovementType(ctx context.Context, movementType entity.WmsInventoryMovementType) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.WmsInventoryMovement{}).Where("movement_type = ?", movementType).Count(&count).Error
	return count, err
}

func (r *WmsInventoryMovementRepositoryImpl) CountByOperator(ctx context.Context, operatorId uint, startDate, endDate time.Time) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.WmsInventoryMovement{}).
		Where("operator_id = ? AND created_at BETWEEN ? AND ?", operatorId, startDate, endDate).
		Count(&count).Error
	return count, err
}

func (r *WmsInventoryMovementRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []uint, status entity.WmsInventoryMovementStatus) error {
	return r.db.WithContext(ctx).Model(&entity.WmsInventoryMovement{}).Where("id IN ?", ids).Update("status", status).Error
}

func (r *WmsInventoryMovementRepositoryImpl) BatchStart(ctx context.Context, ids []uint, operatorId uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&entity.WmsInventoryMovement{}).Where("id IN ?", ids).Updates(map[string]interface{}{
		"status":     entity.MovementStatusInProgress,
		"started_at": now,
	}).Error
}

func (r *WmsInventoryMovementRepositoryImpl) BatchComplete(ctx context.Context, ids []uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&entity.WmsInventoryMovement{}).Where("id IN ?", ids).Updates(map[string]interface{}{
		"status":       entity.MovementStatusCompleted,
		"completed_at": now,
	}).Error
}

// WmsInventoryMovementQueryBuilderImpl 库存移动查询构建器实现
type WmsInventoryMovementQueryBuilderImpl struct {
	db    *gorm.DB
	query *gorm.DB
}

// WhereMovementNo 按移动单号过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereMovementNo(movementNo string) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("movement_no = ?", movementNo)
	return b
}

// WhereItemID 按物料ID过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereItemID(itemId uint) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("item_id = ?", itemId)
	return b
}

// WhereFromLocationID 按源库位ID过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereFromLocationID(fromLocationId uint) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("from_location_id = ?", fromLocationId)
	return b
}

// WhereToLocationID 按目标库位ID过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereToLocationID(toLocationId uint) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("to_location_id = ?", toLocationId)
	return b
}

// WhereOperatorID 按操作员ID过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereOperatorID(operatorId uint) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("operator_id = ?", operatorId)
	return b
}

// WhereStatus 按状态过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereStatus(status entity.WmsInventoryMovementStatus) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("status = ?", status)
	return b
}

// WhereMovementType 按移动类型过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereMovementType(movementType entity.WmsInventoryMovementType) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("movement_type = ?", movementType)
	return b
}

// WhereBatchNo 按批次号过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereBatchNo(batchNo string) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("batch_no = ?", batchNo)
	return b
}

// WhereQuantityRange 按数量范围过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereQuantityRange(minQty, maxQty float64) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("quantity BETWEEN ? AND ?", minQty, maxQty)
	return b
}

// WhereDateRange 按日期范围过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereDateRange(startDate, endDate time.Time) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	return b
}

// WhereWarehouseID 按仓库ID过滤
func (b *WmsInventoryMovementQueryBuilderImpl) WhereWarehouseID(warehouseId uint) WmsInventoryMovementQueryBuilder {
	b.query = b.query.Joins("JOIN wms_location fl ON wms_inventory_movement.from_location_id = fl.id").
		Where("fl.warehouse_id = ?", warehouseId)
	return b
}

// WithItem 包含物料信息
func (b *WmsInventoryMovementQueryBuilderImpl) WithItem() WmsInventoryMovementQueryBuilder {
	b.query = b.query.Preload("Item")
	return b
}

// WithFromLocation 包含源库位信息
func (b *WmsInventoryMovementQueryBuilderImpl) WithFromLocation() WmsInventoryMovementQueryBuilder {
	b.query = b.query.Preload("FromLocation")
	return b
}

// WithToLocation 包含目标库位信息
func (b *WmsInventoryMovementQueryBuilderImpl) WithToLocation() WmsInventoryMovementQueryBuilder {
	b.query = b.query.Preload("ToLocation")
	return b
}

// WithOperator 包含操作员信息
func (b *WmsInventoryMovementQueryBuilderImpl) WithOperator() WmsInventoryMovementQueryBuilder {
	b.query = b.query.Preload("Operator")
	return b
}

// OrderByCreatedAt 按创建时间排序
func (b *WmsInventoryMovementQueryBuilderImpl) OrderByCreatedAt(desc bool) WmsInventoryMovementQueryBuilder {
	if desc {
		b.query = b.query.Order("created_at DESC")
	} else {
		b.query = b.query.Order("created_at ASC")
	}
	return b
}

// OrderByStartedAt 按开始时间排序
func (b *WmsInventoryMovementQueryBuilderImpl) OrderByStartedAt(desc bool) WmsInventoryMovementQueryBuilder {
	if desc {
		b.query = b.query.Order("started_at DESC")
	} else {
		b.query = b.query.Order("started_at ASC")
	}
	return b
}

// OrderByCompletedAt 按完成时间排序
func (b *WmsInventoryMovementQueryBuilderImpl) OrderByCompletedAt(desc bool) WmsInventoryMovementQueryBuilder {
	if desc {
		b.query = b.query.Order("completed_at DESC")
	} else {
		b.query = b.query.Order("completed_at ASC")
	}
	return b
}

// OrderByQuantity 按数量排序
func (b *WmsInventoryMovementQueryBuilderImpl) OrderByQuantity(desc bool) WmsInventoryMovementQueryBuilder {
	if desc {
		b.query = b.query.Order("quantity DESC")
	} else {
		b.query = b.query.Order("quantity ASC")
	}
	return b
}

// Find 执行查询并返回结果列表
func (b *WmsInventoryMovementQueryBuilderImpl) Find() ([]entity.WmsInventoryMovement, error) {
	var movements []entity.WmsInventoryMovement
	err := b.query.Find(&movements).Error
	return movements, err
}

// First 执行查询并返回第一个结果
func (b *WmsInventoryMovementQueryBuilderImpl) First() (*entity.WmsInventoryMovement, error) {
	var movement entity.WmsInventoryMovement
	err := b.query.First(&movement).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &movement, nil
}

// Count 执行查询并返回记录数
func (b *WmsInventoryMovementQueryBuilderImpl) Count() (int64, error) {
	var count int64
	err := b.query.Count(&count).Error
	return count, err
}

// Paginate 执行分页查询
func (b *WmsInventoryMovementQueryBuilderImpl) Paginate(pageNum, pageSize int) ([]entity.WmsInventoryMovement, int64, error) {
	var movements []entity.WmsInventoryMovement
	var total int64

	// 先获取总数
	countQuery := b.db.Model(&entity.WmsInventoryMovement{})
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 执行分页查询
	offset := (pageNum - 1) * pageSize
	err := b.query.Offset(offset).Limit(pageSize).Find(&movements).Error
	return movements, total, err
}
