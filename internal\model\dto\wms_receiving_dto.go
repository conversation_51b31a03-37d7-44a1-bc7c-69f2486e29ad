package dto

import (
	"backend/internal/model/entity"
	"backend/pkg/response"
	"time"

	"github.com/shopspring/decimal"
)

// WmsReceivingRecordDetailConfirmReq 确认收货明细的请求
type WmsReceivingRecordDetailConfirmReq struct {
	ReceivedQty          decimal.Decimal    `json:"receivedQty" validate:"required"`               // 确认的实收数量
	ReceivedAtLocationID uint               `json:"receivedAtLocationId" validate:"required,gt=0"` // 收货暂存库位 ID
	BatchNo              *string            `json:"batchNo,omitempty"`                             // 批次号 (可能在此时确认或修改)
	ProductionDateStr    *string            `json:"productionDate,omitempty"`                      // 生产日期 (YYYY-MM-DD)
	ExpiryDateStr        *string            `json:"expiryDate,omitempty"`                          // 过期日期 (YYYY-MM-DD)
	InspectionStatus     string             `json:"inspectionStatus,omitempty"`                    // 质检状态 (可选, 如 PENDING, PASSED, FAILED)
	Remark               string             `json:"remark,omitempty"`                              // 备注
	Pagination           response.PageQuery `json:"-"`                                             // 分页参数，不参与JSON序列化
}

// WmsReceivingRecordQueryReq 查询收货记录的请求结构
type WmsReceivingRecordQueryReq struct {
	ReceivingNo      *string `json:"receivingNo,omitempty" form:"receivingNo"`           // 收货单号 (模糊查询)
	NotificationNo   *string `json:"notificationNo,omitempty" form:"notificationNo"`     // 关联ASN单号 (需要 JOIN 查询)
	Status           *string `json:"status,omitempty" form:"status"`                     // 状态
	InspectionStatus *string `json:"inspectionStatus,omitempty" form:"inspectionStatus"` // 质检状态
	ClientID         *uint   `json:"clientId,omitempty" form:"clientId"`                 // 客户ID
	WarehouseID      *uint   `json:"warehouseId,omitempty" form:"warehouseId"`           // 仓库ID
	ReceivedAtFrom   *string `json:"receivedAtFrom,omitempty" form:"receivedAtFrom"`     // 收货时间范围 - 开始 (YYYY-MM-DD)
	ReceivedAtTo     *string `json:"receivedAtTo,omitempty" form:"receivedAtTo"`         // 收货时间范围 - 结束 (YYYY-MM-DD)
	SortField        string  `json:"sortField,omitempty" form:"sortField"`               // 排序字段
	SortOrder        string  `json:"sortOrder,omitempty" form:"sortOrder"`               // 排序顺序 (asc/desc)

	Pagination response.PageQuery `json:"-"` // 分页参数，不参与JSON序列化
}

// WmsReceivingRecordDetailBatchCreateReq 批量创建收货记录明细的请求
type WmsReceivingRecordDetailBatchCreateReq struct {
	ReceivingRecordID uint                                `json:"receivingRecordId" validate:"required,gte=0"` // 收货记录ID
	Details           []WmsReceivingRecordDetailCreateReq `json:"details" validate:"required,min=1,dive"`      // 明细列表
}

// WmsReceivingRecordDetailBatchUpdateItem 批量更新项
type WmsReceivingRecordDetailBatchUpdateItem struct {
	ID         uint                              `json:"id" validate:"required,gt=0"` // 明细ID
	UpdateData WmsReceivingRecordDetailUpdateReq `json:"updateData"`                  // 更新数据
}

// WmsReceivingRecordDetailBatchUpdateReq 批量更新收货记录明细的请求
type WmsReceivingRecordDetailBatchUpdateReq struct {
	Updates []WmsReceivingRecordDetailBatchUpdateItem `json:"updates" validate:"required,min=1,dive"` // 更新列表
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	TotalCount     int      `json:"totalCount"`     // 总数量
	SuccessCount   int      `json:"successCount"`   // 成功数量
	FailureCount   int      `json:"failureCount"`   // 失败数量
	SuccessIDs     []uint   `json:"successIds"`     // 成功的ID列表
	FailureReasons []string `json:"failureReasons"` // 失败原因列表
}

// WmsReceivingRecordCreateReq 通用创建收货记录的请求
type WmsReceivingRecordCreateReq struct {
	ReceivingNo           string                              `json:"receivingNo"`
	NotificationID        *uint                               `json:"notificationId,omitempty"`
	WarehouseID           uint                                `json:"warehouseId"`
	ClientID              uint                                `json:"clientId"`
	SupplierShipper       *string                             `json:"supplierShipper,omitempty"`
	ActualArrivalDateStr  *string                             `json:"actualArrivalDate,omitempty"`
	ReceivingType         string                              `json:"receivingType"` // 收货类型 ASN/BLIND
	Status                string                              `json:"status"`
	SupplementDeadlineStr *string                             `json:"supplementDeadline,omitempty"`
	Remark                *string                             `json:"remark,omitempty"`
	SourceDocNo           *string                             `json:"sourceDocNo,omitempty"` // 来源单据号
	Details               []WmsReceivingRecordDetailCreateReq `json:"details" validate:"required,min=1,dive"`
}

// WmsReceivingRecordDetailCreateReq 创建收货记录明细的通用请求
type WmsReceivingRecordDetailCreateReq struct {
	ReceivingRecordID    uint       `json:"receivingRecordId"`
	NotificationDetailID *uint      `json:"notificationDetailId,omitempty"`
	ItemID               uint       `json:"itemId"`
	ExpectedQuantity     *float64   `json:"expectedQuantity,omitempty"`
	UnitOfMeasure        string     `json:"unitOfMeasure"`
	BatchNo              *string    `json:"batchNo,omitempty"`
	InternalBatchNo      *string    `json:"internalBatchNo,omitempty"`
	Remark               *string    `json:"remark,omitempty"`
	LineNo               int        `json:"lineNo"`                         // 行号
	ReceivedQuantity     float64    `json:"receivedQuantity,omitempty"`     // 实际收货数量
	ReceivedAtLocationID uint       `json:"receivedAtLocationId,omitempty"` // 收货库位ID (e.g., Staging Area / Dock Door)
	InspectionStatus     string     `json:"inspectionStatus,omitempty"`     // 检验状态
	InspectorID          *uint      `json:"inspectorId,omitempty"`          // 检验员ID (关联用户表)
	InspectionNotes      *string    `json:"inspectionNotes,omitempty"`      // 检验备注
	InspectionAt         *time.Time `json:"inspectionAt,omitempty"`
	QuantityDifference   *float64   `json:"quantityDifference,omitempty"` // 差异数量 (计算得到)
	DiscrepancyReason    *string    `json:"discrepancyReason,omitempty"`  // 差异原因
	PackageQty           *float64   `json:"packageQty,omitempty"`
	PackageUnit          *string    `json:"packageUnit,omitempty"`
	ProductionDateStr    *string    `json:"productionDate,omitempty"`
	ExpiryDateStr        *string    `json:"expiryDate,omitempty"`
	LineStatus           string     `gorm:"column:line_status;type:wms_receiving_record_status;not null;default:'PENDING';index;comment:状态" json:"lineStatus"` // 收货单明细行状态 (使用数据库定义的 ENUM)                                            // 过期日期 (实收)
}

// WmsReceivingRecordUpdateReq 更新收货记录的请求
type WmsReceivingRecordUpdateReq struct {
	// ReceivingNo 与 ReceivingType 不允许修改，因此不在此结构体出现
	NotificationID        *uint                               `json:"notificationId,omitempty"`     // 关联的 ASN ID，可选
	ActualArrivalDateStr  *string                             `json:"actualArrivalDate,omitempty"`  // 实际到达日期 (YYYY-MM-DD)
	SupplementDeadlineStr *string                             `json:"supplementDeadline,omitempty"` // 补货截止日期 (YYYY-MM-DD)
	ClientID              *uint                               `json:"clientId,omitempty,gt=0"`      // 客户ID
	WarehouseID           *uint                               `json:"warehouseId,omitempty,gt=0"`   // 仓库ID
	SupplierShipper       *string                             `json:"supplierShipper,omitempty"`    // 供应商/发货方
	SourceDocNo           *string                             `json:"sourceDocNo,omitempty"`        // 来源单据号
	Remark                *string                             `json:"remark,omitempty"`             // 备注
	Details               []WmsReceivingRecordDetailUpdateReq `json:"details,omitempty,dive"`       // 要更新的明细
}

// WmsReceivingRecordConfirmReq 确认收货的请求
type WmsReceivingRecordConfirmReq struct {
	Details []WmsReceivingRecordDetailConfirmReq `json:"details" validate:"required,min=1,dive"`
}

// WmsReceivingRecordDetailQueryReq 收货记录明细查询请求
type WmsReceivingRecordDetailQueryReq struct {
	ReceivingRecordID *uint   `json:"receivingRecordId,omitempty" form:"receivingRecordId"` // 收货记录ID
	ItemID            *uint   `json:"itemId,omitempty" form:"itemId"`                       // 物料ID
	InspectionStatus  *string `json:"inspectionStatus,omitempty" form:"inspectionStatus"`   // 检验状态
	BatchNo           *string `json:"batchNo,omitempty" form:"batchNo"`                     // 批次号
	HasDiscrepancy    *bool   `json:"hasDiscrepancy,omitempty" form:"hasDiscrepancy"`       // 是否有差异
	SortField         string  `json:"sortField,omitempty" form:"sortField"`                 // 排序字段
	SortOrder         string  `json:"sortOrder,omitempty" form:"sortOrder"`                 // 排序顺序 (asc/desc)

	Pagination response.PageQuery `json:"-"` // 分页参数，不参与JSON序列化
}

// ReceivingDetailSummary 收货明细汇总信息
type ReceivingDetailSummary struct {
	TotalLines             int             `json:"totalLines"`             // 总行数
	ReceivedLines          int             `json:"receivedLines"`          // 已收货行数
	TotalExpectedQuantity  decimal.Decimal `json:"totalExpectedQuantity"`  // 总预期数量
	TotalReceivedQuantity  decimal.Decimal `json:"totalReceivedQuantity"`  // 总实收数量
	DiscrepancyLines       int             `json:"discrepancyLines"`       // 有差异行数
	PendingInspectionLines int             `json:"pendingInspectionLines"` // 待检验行数
	PassedInspectionLines  int             `json:"passedInspectionLines"`  // 检验合格行数
	FailedInspectionLines  int             `json:"failedInspectionLines"`  // 检验不合格行数
}

// WmsReceivingRecordDetailUpdateReq 更新收货记录明细的请求
type WmsReceivingRecordDetailUpdateReq struct {
	ID                   uint             `json:"id" validate:"required,gt=0"`    // 明细行 ID
	NotificationDetailID *uint            `json:"notificationDetailId,omitempty"` // 关联 ASN 明细 ID
	LineNo               *int             `json:"lineNo,omitempty"`               // 行号
	ItemID               *uint            `json:"itemId,omitempty"`               // 物料 ID
	ReceivedQuantity     *decimal.Decimal `json:"receivedQuantity,omitempty"`     // 实收数量
	UnitOfMeasure        *string          `json:"unitOfMeasure,omitempty"`        // 数量单位
	PackageQty           *decimal.Decimal `json:"packageQty,omitempty"`           // 包装数量
	PackageUnit          *string          `json:"packageUnit,omitempty"`          // 包装单位
	ReceivedAtLocationID *uint            `json:"receivedAtLocationId,omitempty"` // 收货库位 ID
	ExpectedQuantity     *decimal.Decimal `json:"expectedQuantity,omitempty"`     // 预期数量
	BatchNo              *string          `json:"batchNo,omitempty"`              // 批次号
	QuantityDifference   *decimal.Decimal `json:"quantityDifference,omitempty"`   // 差异数量
	DiscrepancyReason    *string          `json:"discrepancyReason,omitempty"`    // 差异原因
	ProductionDateStr    *string          `json:"productionDate,omitempty"`       // 生产日期 (YYYY-MM-DD)
	ExpiryDateStr        *string          `json:"expiryDate,omitempty"`           // 过期日期 (YYYY-MM-DD)
	InspectionStatus     *string          `json:"inspectionStatus,omitempty"`     // 检验状态
	Remark               *string          `json:"remark,omitempty"`               // 备注
}

// WmsReceivingRecordUpdateStatusReq 更新收货记录状态的请求
type WmsReceivingRecordUpdateStatusReq struct {
	Status string `json:"status" validate:"required"` // 新状态值
	Remark string `json:"remark,omitempty"`           // 备注，可选
}

// InspectionResultUpdateReq 更新检验结果的请求
type InspectionResultUpdateReq struct {
	InspectionStatus  entity.WmsReceivingInspectionStatus `json:"inspectionStatus" binding:"required"`
	InspectionResult  *string                             `json:"inspectionResult,omitempty"` // e.g. "PASS", "FAIL"
	InspectorID       *uint                               `json:"inspectorId,omitempty"`
	InspectionNotes   *string                             `json:"inspectionNotes,omitempty"`
	DefectDescription *string                             `json:"defectDescription,omitempty"`
}

type WmsBlindReceivingCreateReq struct {
	ActualArrivalDateStr  *string                             `json:"actualArrivalDate,omitempty"`  // 实际到达日期 (YYYY-MM-DD)
	SupplementDeadlineStr *string                             `json:"supplementDeadline,omitempty"` // 补货截止日期 (YYYY-MM-DD，可选)
	ClientID              uint                                `json:"clientId" validate:"required,gt=0"`
	WarehouseID           uint                                `json:"warehouseId" validate:"required,gt=0"`
	SupplierShipper       *string                             `json:"supplierShipper,omitempty"`
	SourceDocNo           *string                             `json:"sourceDocNo,omitempty"`
	Remark                *string                             `json:"remark,omitempty"`
	Details               []WmsReceivingRecordDetailCreateReq `json:"details,omitempty,dive"` // 明细列表，可为空
}
