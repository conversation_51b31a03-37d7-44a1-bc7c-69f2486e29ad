package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
)

// WmsPutawayTaskController 定义上架任务控制器接口
type WmsPutawayTaskController interface {
	Create(ctx iris.Context)
	CreateFromReceiving(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)
	AssignToUser(ctx iris.Context)
	BatchAssignToUser(ctx iris.Context)
	CompleteTask(ctx iris.Context)
	GetPendingTasks(ctx iris.Context)
	GetTasksByUser(ctx iris.Context)
}

// wmsPutawayTaskControllerImpl 上架任务控制器实现
type WmsPutawayTaskControllerImpl struct {
	BaseControllerImpl
	wmsPutawayTaskService service.WmsPutawayTaskService
}

// NewWmsPutawayTaskControllerImpl 创建上架任务控制器
func NewWmsPutawayTaskControllerImpl(cm *ControllerManager) *WmsPutawayTaskControllerImpl {
	return &WmsPutawayTaskControllerImpl{
		BaseControllerImpl:    *NewBaseController(cm),
		wmsPutawayTaskService: cm.GetServiceManager().GetWmsPutawayTaskService(),
	}
}

// Create 创建上架任务
// POST /api/v1/wms/putaway-tasks
func (ctrl *WmsPutawayTaskControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsPutawayTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	var req dto.WmsPutawayTaskCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsPutawayTaskService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// CreateFromReceiving 从收货记录创建上架任务
// POST /api/v1/wms/putaway-tasks/from-receiving/{receivingId:uint}
func (ctrl *WmsPutawayTaskControllerImpl) CreateFromReceiving(ctx iris.Context) {
	const opName = "CreatePutawayTaskFromReceiving"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	receivingID, err := ctrl.GetPathUintParamWithError(ctx, "receivingId")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsPutawayTaskService.CreateFromReceiving(ctx.Request().Context(), uint(receivingID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新上架任务
// PUT /api/v1/wms/putaway-tasks/{id:uint}
func (ctrl *WmsPutawayTaskControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsPutawayTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPutawayTaskUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsPutawayTaskService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除上架任务
// DELETE /api/v1/wms/putaway-tasks/{id:uint}
func (ctrl *WmsPutawayTaskControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsPutawayTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsPutawayTaskService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个上架任务
// GET /api/v1/wms/putaway-tasks/{id:uint}
func (ctrl *WmsPutawayTaskControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsPutawayTaskByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsPutawayTaskService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取上架任务分页
// GET /api/v1/wms/putaway-tasks
func (ctrl *WmsPutawayTaskControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsPutawayTasksPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	var req dto.WmsPutawayTaskQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	pageResult, err := ctrl.wmsPutawayTaskService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// AssignToUser 分配任务给用户
// PUT /api/v1/wms/putaway-tasks/{id:uint}/assign
func (ctrl *WmsPutawayTaskControllerImpl) AssignToUser(ctx iris.Context) {
	const opName = "AssignPutawayTaskToUser"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPutawayTaskAssignReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsPutawayTaskService.AssignToUser(ctx.Request().Context(), uint(id), req.UserID)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// BatchAssignToUser 批量分配任务给用户
// PUT /api/v1/wms/putaway-tasks/batch-assign
func (ctrl *WmsPutawayTaskControllerImpl) BatchAssignToUser(ctx iris.Context) {
	const opName = "BatchAssignPutawayTasksToUser"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	var req struct {
		TaskIDs []uint `json:"taskIds" binding:"required"`
		UserID  uint   `json:"userId" binding:"required"`
	}
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPutawayTaskService.BatchAssignToUser(ctx.Request().Context(), req.TaskIDs, req.UserID)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// CompleteTask 完成任务
// PUT /api/v1/wms/putaway-tasks/{id:uint}/complete
func (ctrl *WmsPutawayTaskControllerImpl) CompleteTask(ctx iris.Context) {
	const opName = "CompletePutawayTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsPutawayTaskService.CompleteTask(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetPendingTasks 获取待处理任务
// GET /api/v1/wms/putaway-tasks/pending
func (ctrl *WmsPutawayTaskControllerImpl) GetPendingTasks(ctx iris.Context) {
	const opName = "GetPendingPutawayTasks"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	warehouseID := ctx.URLParamUint64("warehouseId")

	tasks, err := ctrl.wmsPutawayTaskService.GetPendingTasks(ctx.Request().Context(), warehouseID)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, tasks)
}

// GetTasksByUser 获取用户的任务
// GET /api/v1/wms/putaway-tasks/by-user/{userId:uint}
func (ctrl *WmsPutawayTaskControllerImpl) GetTasksByUser(ctx iris.Context) {
	const opName = "GetPutawayTasksByUser"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_putaway_task")

	userID, err := ctrl.GetPathUintParamWithError(ctx, "userId")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	tasks, err := ctrl.wmsPutawayTaskService.GetTasksByUser(ctx.Request().Context(), userID)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, tasks)
}
