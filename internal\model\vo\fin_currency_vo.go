package vo

import (
	"backend/internal/model/entity"
)

// FinCurrencyItemRes 单个币种响应
type FinCurrencyItemRes struct {
	BaseVO
	Code      string `json:"code"`
	Name      string `json:"name"`
	Symbol    string `json:"symbol"`
	Precision int    `json:"precision"`
	IsEnabled bool   `json:"isEnabled"`
}

func FromCurrencyEntity(e *entity.FinCurrency) *FinCurrencyItemRes {
	if e == nil {
		return nil
	}
	return &FinCurrencyItemRes{
		BaseVO: BaseVO{
			ID:        e.ID,
			CreatedAt: e.CreatedAt,
			UpdatedAt: e.UpdatedAt,
			CreatedBy: e.CreatedBy,
			UpdatedBy: e.UpdatedBy,
		},
		Code:      e.Code,
		Name:      e.Name,
		Symbol:    e.Symbol,
		Precision: e.Precision,
		IsEnabled: e.IsEnabled,
	}
}

func FromCurrencyEntities(entities []*entity.FinCurrency) []*FinCurrencyItemRes {
	res := make([]*FinCurrencyItemRes, len(entities))
	for i, e := range entities {
		res[i] = FromCurrencyEntity(e)
	}
	return res
}
