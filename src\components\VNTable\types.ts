import type { VNode } from 'vue';
import type { TableColumnCtx, FormItemRule } from 'element-plus';

/**
 * VNTable 组件类型定义
 */

// 表格列类型
export interface TableColumn {
  // 基本属性
  prop: string;
  label: string;
  
  // 布局相关
  width?: string | number;
  minWidth?: string | number;
  fixed?: boolean | 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  
  // 显示控制
  show?: boolean | ((row: any, column: TableColumn, index: number) => boolean);
  
  // 格式化
  formatter?: (row: any, column: TableColumn, cellValue: any, index: number) => string | VNode;
  
  // 样式
  className?: string;
  headerClassName?: string;
  
  // 数据控制
  sortable?: boolean | 'custom';
  sortMethod?: (a: any, b: any) => number;
  sortBy?: string | string[] | ((row: any) => string);
  sortOrders?: Array<'ascending' | 'descending' | null>;
  
  // 列过滤
  filters?: Array<{text: string; value: any}>;
  filterMethod?: (value: any, row: any, column: TableColumn) => boolean;
  filterMultiple?: boolean;
  filterable?: boolean; // 是否可筛选
  filterType?: 'text' | 'number' | 'date' | 'select'; // 筛选类型
  filterOptions?: Array<{label: string; value: any}>; // 筛选选项
  filterOperators?: string[]; // 新增：该列可用的筛选操作符
  
  // 插槽相关
  type?: 'selection' | 'index' | 'expand' | 'default';
  slot?: boolean; // 使用自定义插槽渲染
  children?: TableColumn[]; // 多级表头

  // 表格交互
  editable?: boolean | ((row: any) => boolean); // 列是否可编辑，可以是一个函数
  editComponent?: 'input' | 'input-number' | 'select' | 'datepicker' | 'switch' | 'custom'; // 编辑组件类型
  editComponentProps?: Record<string, any> | ((row: any) => Record<string, any>); // 编辑组件的属性，可以是一个返回属性对象的函数
  rules?: any; // el-form-item 的校验规则

  // 扩展列配置，添加聚合功能
  group?: string; // 列分组
  aggregate?: 'sum' | 'avg' | 'min' | 'max' | 'count' | string; // 聚合方式
  aggregateFormatter?: (value: any) => string; // 聚合格式化

  // 新增属性
  visible?: boolean; // 是否可见
  important?: boolean; // 是否是重要列(移动端显示)
  showOverflowTooltip?: boolean; // 新增：控制是否在内容溢出时显示 tooltip
}

// 操作按钮类型
export interface ActionButton {
  label: string;
  type?: string;
  icon?: string;
  hidden?: boolean | ((row: any, index: number) => boolean);
  disabled?: boolean | ((row: any, index: number) => boolean);
  handler?: (row: any, index: number) => void;
  action?: string;
}

// 分页配置
export interface PaginationConfig {
  pageSize: number;
  currentPage: number;
  total: number;
  pageSizes?: number[];
  layout?: string;
  background?: boolean;
  hideOnSinglePage?: boolean;
}

// 卡片视图配置
export interface CardConfig {
  column?: number; // 每行显示的卡片数量
  gutter?: number; // 卡片间距
  height?: number | string; // 卡片高度
  headerField?: string; // 卡片标题字段
  descriptionField?: string; // 卡片描述字段
  imageField?: string; // 卡片图片字段
  showHeader?: boolean; // 是否显示卡片标题
  showFooter?: boolean; // 是否显示卡片底部
  showImage?: boolean; // 是否显示图片
  imageHeight?: number | string; // 图片高度
  shadow?: 'always' | 'hover' | 'never'; // 卡片阴影效果
  bodyStyle?: Record<string, any>; // 卡片内容区域样式
  hoverable?: boolean; // 鼠标悬停效果
}

// 列表视图配置
export interface ListConfig {
  itemHeight?: number | string; // 列表项高度
  showImage?: boolean; // 是否显示图片
  imageField?: string; // 图片字段
  imageWidth?: number | string; // 图片宽度
  imageHeight?: number | string; // 图片高度
  imageRadius?: string; // 图片圆角
  titleField?: string; // 标题字段
  subtitleField?: string; // 副标题字段
  descriptionField?: string; // 描述字段
  showDivider?: boolean; // 是否显示分割线
  dense?: boolean; // 是否使用紧凑模式
}

// 布局模式类型
export type LayoutMode = 'table' | 'card' | 'list';

// 表格属性接口
export interface VNTableProps {
  // 数据源
  data: any[];
  
  // 列配置
  columns: TableColumn[];
  
  // 布局相关
  layoutMode?: LayoutMode;
  layoutModes?: LayoutMode[];
  showLayoutSwitch?: boolean;
  cardConfig?: CardConfig;
  listConfig?: ListConfig;
  autoSwitchLayout?: boolean; // 是否根据屏幕尺寸自动切换布局
  
  // 加载状态
  loading?: boolean;
  
  // 高度设置
  height?: string | number;
  maxHeight?: string | number;
  
  // 选择和展开
  rowKey?: string | ((row: any) => string);
  selectionType?: 'none' | 'single' | 'multiple';
  defaultExpandAll?: boolean;
  expandRowKeys?: string[];
  treeProps?: {
    children?: string;
    hasChildren?: string;
    indent?: number;
  };
  
  // 事件控制
  highlightCurrentRow?: boolean;
  rowClassName?: string | ((params: {row: any; rowIndex: number}) => string);
  
  // 分页配置
  pagination?: PaginationConfig | false;
  
  // 表格工具
  showToolbar?: boolean;
  toolbarConfig?: {
    refresh?: boolean;
    fullscreen?: boolean;
    columnSetting?: boolean;
    export?: boolean;
    import?: boolean;
    density?: boolean;
    add?: boolean;
    batchDelete?: boolean;
    expandAll?: boolean;
    collapseAll?: boolean;
    filter?: boolean;
    layoutSwitch?: boolean;
    filterDialogWidth?: string; // 新增：筛选对话框宽度
  };
  
  // 操作列
  showOperations?: boolean;
  operationWidth?: string | number;
  operationFixed?: boolean | 'left' | 'right';
  operationLabel?: string;
  operationButtons?: ActionButton[];
  
  // 空状态
  emptyText?: string;
  
  // 索引列配置
  showIndex?: boolean;
  indexLabel?: string;
  indexWidth?: string | number;
  
  // 边框和条纹
  border?: boolean;
  stripe?: boolean;
  
  // 编辑表格相关
  editable?: boolean;
  editMode?: 'row' | 'cell';
  
  // 表格头相关
  showHeader?: boolean;
  
  // 新增属性
  id?: string; // 表格ID，用于持久化配置

  // 添加 EnableDrag 和 DragSort 属性
  enableDrag?: boolean; // 是否启用拖拽
  dragSort?: boolean; // 是否启用拖拽排序
  resizableColumn?: boolean; // 列宽可调整
  
  // Excel 导入映射
  importColumnMapping?: Record<string, string>;

  // <<< 新增：用于区分数据获取方式 >>>
  fetchData?: boolean; // 是否由组件内部或父组件获取数据
  useFrontendPagination?: boolean; // 是否启用前端分页
  singleRowEdit?: boolean; // 新增：是否开启单行编辑模式
  cellClassName?: string | ((params: {row: any; column: any; rowIndex: number; columnIndex: number;}) => string); // 新增：单元格类名
}

// 表格组件方法接口
export interface VNTableMethods {
  // === Custom Methods ===
  Refresh: () => void;
  Reset: () => void;
  ImportTable: () => void;
  ExportTable: () => void;
  Add: () => void;
  BatchDelete: () => void;
  ExpandAll: () => void;
  CollapseAll: () => void;
  SwitchLayout: (mode: LayoutMode) => void;
  GetCurrentLayout: () => LayoutMode;
  ApplyFilters: () => void;
  ClearAllFilters: () => void;
  GetFilterValues: () => Record<string, any>;
  GetSelectionRows: () => any[];
  GetData: () => any[];
  resetData: () => void;
  ValidateTable: () => Promise<boolean>;
  HandleEditRow: (row: any) => void; // 新增：进入编辑模式方法

  // === Re-exposed ElTable Methods ===
  ClearSelection: () => void;
  ToggleRowSelection: (row: any, selected?: boolean) => void;
  ToggleAllSelection: () => void;
  ToggleRowExpansion: (row: any, expanded?: boolean) => void;
  SetCurrentRow: (row?: any) => void;
  ClearSort: () => void;
  ClearFilter: (columnKeys?: string | string[]) => void;
  DoLayout: () => void;
  Sort: (prop: string, order: 'ascending' | 'descending' | null) => void;
  ScrollToRow: (row: any) => void;
}

// 表格事件接口
export interface VNTableEmits {
  // 选择相关
  (e: 'selection-change', rows: any[]): void;
  (e: 'select', selection: any[], row: any): void;
  (e: 'select-all', selection: any[]): void;
  
  // 分页相关
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
  
  // 排序相关
  (e: 'sort-change', sortInfo: { column: any, prop: string, order: 'ascending' | 'descending' | null }): void;
  
  // 行相关
  (e: 'row-click', row: any, column: any, event: Event): void;
  (e: 'row-dblclick', row: any, column: any, event: Event): void;
  (e: 'row-contextmenu', row: any, column: any, event: Event): void;
  (e: 'expand-change', row: any, expanded: boolean): void;
  
  // 单元格相关
  (e: 'cell-click', row: any, column: any, cell: any, event: Event): void;
  (e: 'cell-dblclick', row: any, column: any, cell: any, event: Event): void;
  (e: 'cell-contextmenu', row: any, column: any, cell: any, event: Event): void;
  
  // 编辑相关
  (e: 'cell-edit', payload: { row: any, column: any, value: any, oldValue: any }): void;
  (e: 'row-save', row: any, index: number, oldRow: any): void;
  (e: 'row-cancel', row: any, index: number): void;
  (e: 'row-delete', row: any, index: number): void; // 新增：删除行事件

  // 工具栏相关
  (e: 'refresh'): void;
  (e: 'import'): void;
  (e: 'export'): void;
  (e: 'add'): void;
  (e: 'batch-delete', rows: any[]): void;
  (e: 'expand-all'): void;
  (e: 'collapse-all'): void;

  // 筛选相关
  (e: 'filter', filteredData: any[], filterValues: Record<string, any>): void;
  (e: 'filter-clear'): void;
  
  // 布局相关
  (e: 'layout-change', mode: LayoutMode): void;
  (e: 'density-change', density: 'default' | 'small' | 'large'): void;
  (e: 'column-change', columns: { prop: string; visible: boolean }[]): void;
  
  // 操作按钮相关
  (e: 'view', row: any, index: number): void;
  (e: 'edit', row: any, index: number): void;

  // 拖拽相关
  (e: 'row-drag-end', draggingRecord: any, newIndex: number, oldIndex: number): void;

  // 自定义操作事件 (通过 action 触发)
  [key: string]: (...args: any[]) => void;
} 