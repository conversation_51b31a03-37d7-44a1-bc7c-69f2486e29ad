package vo

import "time"

// UserVO 用户视图对象
// 用于向前端展示用户信息
// 不包含敏感信息如密码
type UserVO struct {
	TenantVO
	Username             string                 `json:"username"`                       // 用户名：用户登录系统的唯一标识
	Nickname             string                 `json:"nickname"`                       // 昵称：用户在系统中显示的名称
	RealName             string                 `json:"realName"`                       // 真实姓名：用户的实际姓名
	EmpCode              string                 `json:"empCode"`                        // 员工工号
	EmpName              string                 `json:"empName"`                        // 员工姓名
	Avatar               string                 `json:"avatar"`                         // 头像URL：用户头像的访问地址
	Gender               int                    `json:"gender"`                         // 性别：用户性别（0-未知，1-男，2-女）
	Email                string                 `json:"email"`                          // 电子邮箱：用户的邮箱地址
	Mobile               string                 `json:"mobile"`                         // 手机号码：用户的手机号
	Status               int                    `json:"status"`                         // 状态：用户状态（0-禁用，1-正常）
	LoginIP              string                 `json:"loginIp"`                        // 最后登录IP：记录用户最近一次登录的IP地址
	LoginTime            *time.Time             `json:"loginTime"`                      // 最后登录时间：记录用户最近一次登录的时间戳
	LoginCount           int                    `json:"loginCount"`                     // 登录次数：记录用户累计登录系统的次数
	Remark               string                 `json:"remark"`                         // 备注：关于用户的附加说明信息
	IsAdmin              bool                   `json:"isAdmin"`                        // 是否是管理员：标识用户是否具有管理员权限
	ExpireTime           *time.Time             `json:"expireTime"`                     // 账号过期时间：用户账号的有效期截止时间
	LockedTime           *time.Time             `json:"lockedTime"`                     // 账号锁定时间：用户账号被锁定的时间
	LockedCount          *int                   `json:"lockedCount"`                    // 锁定次数：记录用户账号被锁定的累计次数
	LastPwdTime          *time.Time             `json:"lastPwdTime"`                    // 最后修改密码时间：记录用户最近一次修改密码的时间
	Roles                []*RoleVO              `json:"roles"`                          // 角色列表：用户拥有的角色列表
	EmployeeID           *uint                  `json:"employeeId,omitempty"`           // 关联员工ID
	AccountBooks         []*AccountBookSimpleVO `json:"accountBooks,omitempty"`         // 添加：用户关联的账套列表
	DefaultAccountBookID *uint                  `json:"defaultAccountBookId,omitempty"` // 新增：默认账套ID
}

// UserSimpleVO 用户简要信息视图对象
// 用于列表展示或下拉选择等场景
// 只包含用户的基本信息
type UserSimpleVO struct {
	ID       uint   `json:"id"`       // ID：用户的唯一标识
	Username string `json:"username"` // 用户名：用户登录系统的唯一标识
	RealName string `json:"realName"` // 真实姓名：用户的实际姓名
	Gender   int    `json:"gender"`   // 性别：用户性别（1-男，2-女）
	Status   int    `json:"status"`   // 状态：用户状态（0-禁用，1-正常）
	IsAdmin  bool   `json:"isAdmin"`  // 是否是管理员：标识用户是否具有管理员权限
}

// UserProfileVO 用户个人信息视图对象
// 用于用户查看和编辑自己的个人信息
// 不包含管理相关的信息
type UserProfileVO struct {
	ID                   uint                   `json:"id"`                             // ID：用户的唯一标识
	Username             string                 `json:"username"`                       // 用户名：用户登录系统的唯一标识
	Nickname             string                 `json:"nickname"`                       // 昵称：用户在系统中显示的名称
	RealName             string                 `json:"realName"`                       // 真实姓名：用户的实际姓名
	Avatar               string                 `json:"avatar"`                         // 头像URL：用户头像的访问地址
	Gender               int                    `json:"gender"`                         // 性别：用户性别（0-未知，1-男，2-女）
	Email                string                 `json:"email"`                          // 电子邮箱：用户的邮箱地址
	Mobile               string                 `json:"mobile"`                         // 手机号码：用户的手机号
	IsAdmin              bool                   `json:"isAdmin"`                        // 是否是管理员：标识用户是否具有管理员权限
	LoginIP              string                 `json:"loginIp"`                        // 最后登录IP：记录用户最近一次登录的IP地址
	LoginTime            time.Time              `json:"loginTime"`                      // 最后登录时间：记录用户最近一次登录的时间戳
	EmployeeID           *uint                  `json:"employeeId,omitempty"`           // 关联员工ID
	Roles                []*RoleVO              `json:"roles,omitempty"`                // 关联的角色信息 (指针类型)
	AccountBooks         []*AccountBookSimpleVO `json:"accountBooks,omitempty"`         // 添加：用户关联的账套列表
	DefaultAccountBookID *uint                  `json:"defaultAccountBookId,omitempty"` // 新增：默认账套ID
}

// UserLoginVO 用户登录结果视图对象
// 用于登录成功后返回给前端的数据
type UserLoginVO struct {
	Token      string         `json:"token"`      // 访问令牌：用于后续请求的认证
	ExpireTime int64          `json:"expireTime"` // 过期时间：访问令牌的过期时间戳（毫秒）
	User       *UserProfileVO `json:"user"`       // 用户信息：登录用户的个人信息
}

// UserOnlineVO 在线用户视图对象
// 用于展示当前在线的用户信息
type UserOnlineVO struct {
	ID         string    `json:"id"`         // 会话ID：用户的会话唯一标识
	Username   string    `json:"username"`   // 用户名：用户登录系统的唯一标识
	Nickname   string    `json:"nickname"`   // 昵称：用户在系统中显示的名称
	IP         string    `json:"ip"`         // IP地址：用户当前的IP地址
	LoginTime  time.Time `json:"loginTime"`  // 登录时间：用户本次登录的时间
	LastActive time.Time `json:"lastActive"` // 最后活动时间：用户最后一次活动的时间
	Browser    string    `json:"browser"`    // 浏览器：用户使用的浏览器类型
	OS         string    `json:"os"`         // 操作系统：用户使用的操作系统
	Location   string    `json:"location"`   // 地理位置：根据IP解析的地理位置
}
