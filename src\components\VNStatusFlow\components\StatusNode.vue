<template>
  <div 
    class="status-node" 
    :class="[
      `is-${size}`,
      `is-${mode}`,
      {
        'is-active': active,
        'is-completed': completed,
        'is-disabled': disabled,
        'is-clickable': !disabled && !active
      }
    ]"
    @click="handleClick"
  >
    <div class="node-icon">
      <el-icon v-if="active && !completed">
        <Loading />
      </el-icon>
      <el-icon v-else-if="completed">
        <Check />
      </el-icon>
      <el-icon v-else-if="status.icon" :class="status.icon">
        <component :is="getIconComponent(status.icon)" />
      </el-icon>
      <el-icon v-else>
        <Clock />
      </el-icon>
    </div>
    
    <!-- 状态编号 -->
    <div v-if="showNumber" class="node-number">
      {{ nodeNumber }}
    </div>
    
    <!-- 悬浮提示 -->
    <el-tooltip
      v-if="status.description"
      :content="status.description"
      placement="top"
      :show-after="500"
    >
      <div class="tooltip-trigger" />
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Loading, 
  Check, 
  Clock,
  Edit,
  Calendar,
  Van,
  Goods,
  Warning,
  Lock,
  Close
} from '@element-plus/icons-vue'
import type { StatusNodeProps } from '../types'

// Props
const props = withDefaults(defineProps<StatusNodeProps>(), {
  size: 'medium',
  mode: 'horizontal'
})

// Emits
const emit = defineEmits<{
  click: [status: StatusNodeProps['status']]
}>()

// 图标组件映射
const iconComponents = {
  Loading,
  Check,
  Clock,
  Edit,
  Calendar,
  Truck: Van, // 使用Van替代Truck
  Box: Goods, // 使用Goods替代Box
  Van,
  Goods,
  Warning,
  Lock,
  Close
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || Clock
}

// 是否显示节点编号
const showNumber = computed(() => {
  return props.mode === 'vertical' && props.size !== 'small'
})

// 节点编号 (可以根据业务需要自定义)
const nodeNumber = computed(() => {
  // 这里可以传入序号或根据状态值计算
  return '1'
})

// 处理点击事件
const handleClick = () => {
  if (!props.disabled && !props.active) {
    emit('click', props.status)
  }
}

// 计算节点样式
const nodeStyle = computed(() => {
  const baseStyle: Record<string, string> = {}
  
  if (props.active) {
    baseStyle['backgroundColor'] = props.status.bgColor
    baseStyle['borderColor'] = props.status.color
    baseStyle['color'] = props.status.color
  } else if (props.completed) {
    baseStyle['backgroundColor'] = '#67c23a'
    baseStyle['borderColor'] = '#67c23a'
    baseStyle['color'] = 'white'
  } else if (props.disabled) {
    baseStyle['backgroundColor'] = '#f5f7fa'
    baseStyle['borderColor'] = '#dcdfe6'
    baseStyle['color'] = '#c0c4cc'
  } else {
    baseStyle['backgroundColor'] = 'white'
    baseStyle['borderColor'] = '#dcdfe6'
    baseStyle['color'] = '#909399'
  }
  
  return baseStyle
})
</script>

<style scoped lang="scss">
.status-node {
  position: relative;
  border-radius: 50%;
  border: 2px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: default;
  
  &.is-clickable {
    cursor: pointer;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  // 尺寸变体
  &.is-small {
    width: 32px;
    height: 32px;
    
    .node-icon {
      font-size: 14px;
    }
  }
  
  &.is-medium {
    width: 40px;
    height: 40px;
    
    .node-icon {
      font-size: 18px;
    }
  }
  
  &.is-large {
    width: 48px;
    height: 48px;
    
    .node-icon {
      font-size: 22px;
    }
  }
  
  // 状态变体
  &.is-active {
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
    
    .node-icon {
      color: inherit;
    }
  }
  
  &.is-completed {
    .node-icon {
      color: white;
    }
  }
  
  &.is-disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 模式变体
  &.is-vertical {
    .node-number {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 20px;
      height: 20px;
      background: #409eff;
      color: white;
      font-size: 12px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid white;
    }
  }
  
  .node-icon {
    transition: all 0.3s ease;
  }
  
  .tooltip-trigger {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
  }
}

// 根据传入的状态配置动态设置样式
.status-node {
  background-color: v-bind('nodeStyle["backgroundColor"]');
  border-color: v-bind('nodeStyle["borderColor"]');
  color: v-bind('nodeStyle["color"]');
}

// 激活状态的脉冲动画
@keyframes node-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.status-node.is-active {
  animation: node-pulse 2s infinite;
}

// 悬浮效果
.status-node.is-clickable:hover {
  .node-icon {
    transform: scale(1.1);
  }
}

// 加载动画
.status-node .node-icon :deep(.el-icon) {
  &.is-loading {
    animation: rotate 2s linear infinite;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 