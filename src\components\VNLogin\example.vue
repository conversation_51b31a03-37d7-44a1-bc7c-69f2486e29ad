<template>
  <div style="/* padding: 20px; REMOVE padding if component handles full page */">
    <!-- Example now just shows the login component -->
    <VNLogin
        :loading="isLoading"
        :show-forgot-password="true"
        :show-captcha="showCaptchaExample"
        :captcha-image-url="captchaUrlExample"
        :show-alternative-logins="showAltLoginsSection"
        :show-left-panel="showLeftPanelExample"
        :left-panel-background-image-url="leftPanelBgUrlExample"
        :system-title="systemTitleExample"
        :version="versionExample"
        :logo-url="logoUrlExample"
        @login="handleLogin"
        @forgot-password="handleForgotPassword"
        @refresh-captcha="handleRefresh"
     >
        <template #alternative-logins>
            <el-tooltip content="微信登录" placement="bottom" v-if="showWeChatLogin">
                 <el-icon class="alternative-login-icon" size="24"><ChatDotRound /></el-icon>
            </el-tooltip>
             <el-tooltip content="支付宝登录" placement="bottom" v-if="showAlipayLogin">
                 <el-icon class="alternative-login-icon" size="24"><Platform /></el-icon>
             </el-tooltip>
             <el-tooltip content="Github登录" placement="bottom" v-if="showGithubLogin">
                  <el-icon class="alternative-login-icon" size="24"><Promotion /></el-icon>
             </el-tooltip>
             <el-tooltip content="飞书登录" placement="bottom" v-if="showFeishuLogin">
                  <el-icon class="alternative-login-icon" size="24"><Connection /></el-icon>
             </el-tooltip>
             <el-tooltip content="手机登录" placement="bottom" v-if="showMobileLogin">
                  <el-icon class="alternative-login-icon" size="24"><Iphone /></el-icon>
             </el-tooltip>
             <el-tooltip content="钉钉登录" placement="bottom" v-if="showDingTalkLogin">
                  <el-icon class="alternative-login-icon" size="24"><Comment /></el-icon>
             </el-tooltip>
        </template>

        <!-- Example usage of left panel slots -->
        <!--
        <template #left-panel-header>
             <img src="/custom-logo.png" style="height:50px" alt="Custom Logo"/>
             <h2>Custom Title</h2>
        </template>
        <template #left-panel-footer>
             <span>My Custom Footer</span>
        </template>
         -->

     </VNLogin>

    <!-- Controls moved outside and positioned absolutely for demo -->
    <div class="example-controls">
        <h3>测试控件 (仅用于示例)</h3>
         <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">
            <el-button @click="toggleLoading">切换 Loading</el-button>
            <el-button @click="toggleCaptcha">切换验证码</el-button>
            <el-button @click="handleRefresh" :disabled="!showCaptchaExample">刷新验证码</el-button>
            <el-button @click="toggleAltLoginsSection">切换其他登录区域</el-button>
            <el-button @click="simulateErrorLogin">模拟登录失败</el-button>
            <el-button @click="toggleLeftPanel">切换左侧面板</el-button>
        </div>
         <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">
            <el-input v-model="leftPanelBgUrlExample" placeholder="左侧背景图 URL" size="small" clearable style="width: 250px;"/>
            <el-input v-model="systemTitleExample" placeholder="系统标题" size="small" clearable style="width: 150px;"/>
            <el-input v-model="versionExample" placeholder="版本号" size="small" clearable style="width: 100px;"/>
            <el-input v-model="logoUrlExample" placeholder="Logo URL" size="small" clearable style="width: 200px;"/>
        </div>
         <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">
             <span style="margin-right: 10px; font-weight: bold;">切换具体图标:</span>
            <el-checkbox v-model="showWeChatLogin" label="微信" border size="small"/>
            <el-checkbox v-model="showAlipayLogin" label="支付宝" border size="small"/>
            <el-checkbox v-model="showGithubLogin" label="Github" border size="small"/>
            <el-checkbox v-model="showFeishuLogin" label="飞书" border size="small"/>
            <el-checkbox v-model="showMobileLogin" label="手机" border size="small"/>
            <el-checkbox v-model="showDingTalkLogin" label="钉钉" border size="small"/>
        </div>
        <pre>Login Event Data: {{ loginData }}</pre>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNLogin from './index.vue';
import type { LoginFormModel } from './types';
import { ElMessage, ElButton, ElTooltip, ElIcon, ElCheckbox, ElInput } from 'element-plus';
import { ChatDotRound, Platform, Promotion, Connection, Iphone, Comment } from '@element-plus/icons-vue';

const isLoading = ref(false);
const loginData = ref<LoginFormModel | null>(null);

// --- Captcha Example State ---
const showCaptchaExample = ref(false);
const captchaUrlExample = ref('');
let captchaCounter = 0;

// --- Alternative Logins Example State ---
const showAltLoginsSection = ref(true);
const showWeChatLogin = ref(true);
const showAlipayLogin = ref(true);
const showGithubLogin = ref(true);
const showFeishuLogin = ref(false);
const showMobileLogin = ref(false);
const showDingTalkLogin = ref(false);

// --- Left Panel Example State ---
const showLeftPanelExample = ref(true);
const leftPanelBgUrlExample = ref('https://picsum.photos/seed/loginbg/1200/900'); // Default bg
const systemTitleExample = ref('VN 组件示例系统');
const versionExample = ref('v2.1.0-beta');
const logoUrlExample = ref(''); // No default logo

// Function to simulate getting a captcha URL
const getNewCaptchaUrl = () => {
    captchaCounter++;
    return `https://picsum.photos/seed/${captchaCounter}/100/40`;
};

// Handler for the refresh event from VNLogin
const handleRefresh = () => {
    if (!showCaptchaExample.value) return;
    console.log('Refreshing captcha...');
    captchaUrlExample.value = '';
    setTimeout(() => {
        captchaUrlExample.value = getNewCaptchaUrl();
        ElMessage.success('验证码已刷新！');
    }, 500);
};

// --- Login/Forgot Password Handlers ---\
const handleLogin = (data: LoginFormModel) => {
    console.log('Login event received in example:', data);
    loginData.value = data;
    let message = `Login event triggered! Username: ${data.username}`;
    if (data.captchaCode) {
        message += `, Captcha: ${data.captchaCode}`;
    }
    ElMessage.success(message);

    isLoading.value = true;
    setTimeout(() => {
        let loginSuccess = true;
        let errorMsgForMessage = '';

        if (data.username !== 'admin') {
            errorMsgForMessage = '模拟错误：用户名或密码无效。';
            loginSuccess = false;
        }
        if (showCaptchaExample.value && data.captchaCode !== '1234') {
            const captchaError = '模拟错误：验证码错误。';
            errorMsgForMessage = errorMsgForMessage
                ? errorMsgForMessage + ' 且验证码错误。'
                : captchaError;
             loginSuccess = false;
             handleRefresh();
        }

        if (loginSuccess) {
            ElMessage.info('模拟：登录成功！');
        } else {
            ElMessage.error(errorMsgForMessage);
        }

        isLoading.value = false;
    }, 1500);
};

const handleForgotPassword = () => {
    console.log('Forgot password event received in example.');
    ElMessage.info('"忘记密码" 事件触发！');
};

// --- Toggle Button Methods ---
const toggleLoading = () => {
    isLoading.value = !isLoading.value;
};

const toggleCaptcha = () => {
    showCaptchaExample.value = !showCaptchaExample.value;
    if (showCaptchaExample.value && !captchaUrlExample.value) {
        handleRefresh();
    }
};

const toggleAltLoginsSection = () => {
    showAltLoginsSection.value = !showAltLoginsSection.value;
};

const simulateErrorLogin = () => {
    ElMessage.error('这是一个直接触发的错误提示！');
};

const toggleLeftPanel = () => {
    showLeftPanelExample.value = !showLeftPanelExample.value;
};

</script>

<style scoped>
/* Remove padding from root if VNLogin handles full page */
/*
.div {
    padding: 20px;
}
*/

/* Style the controls panel */
.example-controls {
    position: fixed; /* Position controls separately */
    bottom: 10px;
    left: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 10; /* Ensure controls are above login */
    font-size: 12px;
}
.example-controls h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
}
pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 80px; /* Limit height of pre tag */
    overflow-y: auto;
    background-color: #f0f0f0;
    padding: 8px;
    margin-top: 10px;
}

/* Style for icons in the slot */
.alternative-login-icon {
    /* These styles might be better defined globally 
       or within the parent using :deep() if this slot 
       is intended for wide reuse with consistent styling. */
    /* color: #606266; */ /* Already styled via :slotted in index.vue */
    /* cursor: pointer; */
    /* font-size: 24px; */
}
</style> 