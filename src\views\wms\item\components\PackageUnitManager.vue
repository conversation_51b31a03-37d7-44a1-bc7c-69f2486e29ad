<template>
  <div>
    <!-- 包装单位管理表格 -->
    <VNTable
      ref="packageUnitTableRef"
      :data="packageUnitTableData"
      :columns="packageUnitTableColumns"
      :loading="packageUnitLoading"
      :pagination="packageUnitPagination"
      :toolbar-config="{ refresh: true, add: hasPermission('wms:item:add') }" 
      :operation-buttons="[]" 
      :show-operations="true"
      show-index
      row-key="id"
      operation-fixed="right"
      :operation-width="150" 
      @refresh="loadPackageUnits"
      @add="handleAddNewPackageUnit"
      @page-change="handlePackageUnitPageChange"
      @page-size-change="handlePackageUnitPageSizeChange"
      @sort-change="handlePackageUnitSortChange" 
      @filter-change="handlePackageUnitFilterChange"
    >
      <!-- 包装单位名称插槽 -->
      <template #column-unitName="{ row }">
        <span>{{ getDictLabel('unit', row.unitName) || row.unitName || '未设置' }}</span>
        <!-- 调试信息 -->
        <!-- {{ console.log('[插槽调试] row.unitName:', row.unitName, 'dictLabel:', getDictLabel('unit', row.unitName)) }} -->
      </template>

      <!-- 包装单位表格操作列插槽 -->
      <template #operation="{ row }">
        <el-tooltip content="编辑包装单位" placement="top" v-if="hasPermission('wms:item:edit')">
          <el-button circle :icon="Edit" type="primary" size="small" @click="handleEditPackageUnit(row)" />
        </el-tooltip>
        <el-tooltip content="删除包装单位" placement="top" v-if="hasPermission('wms:item:delete')">
          <el-button 
            circle 
            :icon="Delete" 
            type="danger" 
            size="small" 
            @click="handleDeletePackageUnit(row)" 
          />
        </el-tooltip>
      </template>
    </VNTable>

    <!-- 包装单位新增/编辑弹窗 -->
    <el-dialog
      v-model="packageUnitDialogVisible"
      :title="packageUnitDialogTitle"
      width="60%"
      draggable
      :close-on-click-modal="false"
      append-to-body 
      @close="handleClosePackageUnitDialog"
    >
      <!-- VNForm 用于编辑/新增包装单位 -->
      <VNForm
        v-if="packageUnitDialogVisible" 
        ref="packageUnitFormRef"
        :header-fields="packageUnitFormFields"
        v-model="currentEditingPackageUnit"
        :loading="packageUnitFormLoading" 
        @submit="submitPackageUnitForm"
        @cancel="handleClosePackageUnitDialog"
      />
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';
import { ElDialog, ElButton, ElMessage, ElMessageBox, ElTooltip } from 'element-plus';
// 导入图标
import { Edit, Delete } from '@element-plus/icons-vue';
// 导入组件
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
// 导入类型
type SortParams = { prop: string; order: 'ascending' | 'descending' | null };
type FilterParams = Record<string, any>;
import type { TableColumn, PaginationConfig } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
// 导入API和类型
import { 
  getMtlItemPackageUnits, 
  createMtlItemPackageUnit,
  updateMtlItemPackageUnit,
  deleteMtlItemPackageUnit
} from '@/api/wms/item';
import type { 
  MtlItemPackageUnitVO, 
  MtlItemPackageUnitCreateDTO,
  MtlItemPackageUnitUpdateDTO
} from '@/api/wms/item';
// 导入权限检查函数
import { hasPermission } from '@/hooks/usePermission';
// 导入字典hooks
import { useDictionary } from '@/hooks/useDictionary';

// --- 新增：辅助函数：获取并格式化API错误消息 ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  // 1. 顶层错误信息 (客户端或网络错误)
  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }

  // 2. 中间错误原因 (后端返回的整体业务错误描述)
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }

  // 3. 底层详细列表 (后端返回的校验详情等)
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }

  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  
  return '操作出错了，请稍后重试';
};

// --- Props --- 
interface Props {
  itemId: number;
  itemSku: string;
  itemName: string;
}
const props = defineProps<Props>();

// --- Refs ---
type VNTableInstance = InstanceType<typeof VNTable>;
type VNFormInstance = InstanceType<typeof VNForm>;

const packageUnitTableRef = ref<VNTableInstance>();
const packageUnitFormRef = ref<VNFormInstance>();

// --- State (包装单位管理) ---
const packageUnitTableData = ref<MtlItemPackageUnitVO[]>([]);
const packageUnitPagination = reactive<PaginationConfig>({
  total: 0,
  currentPage: 1,
  pageSize: 10,
});
const packageUnitLoading = ref(false);
const packageUnitDialogVisible = ref(false);
const currentEditingPackageUnit = ref<Partial<MtlItemPackageUnitCreateDTO | MtlItemPackageUnitUpdateDTO>>({});
const isPackageUnitEdit = ref(false);
const packageUnitFormLoading = ref(false);
const currentPackageUnitSort = ref<SortParams | null>(null);
const currentPackageUnitFilters = ref<FilterParams>({});

// 使用字典管理
const { loadMultipleDictData, getDictLabel } = useDictionary();

// 字典数据
const dictData = ref({
  unit: [] as Array<{ label: string; value: string }>
});

// --- Computed --- 
const packageUnitDialogTitle = computed(() => (isPackageUnitEdit.value ? '编辑包装单位' : '新增包装单位'));

// --- Table Columns (包装单位) --- 
const packageUnitTableColumns = ref<TableColumn[]>([
  { prop: 'unitName', label: '包装单位名称', minWidth: 120, sortable: true, filterable: true, filterType: 'text', slot: true },
  { prop: 'conversionFactor', label: '换算率', width: 100, sortable: true, formatter: (row) => row.conversionFactor?.toFixed(6) || '0' },
  { prop: 'packageWeightKg', label: '包装重量(kg)', width: 120, formatter: (row) => row.packageWeightKg?.toFixed(3) || '-' },
  { prop: 'packageVolumeM3', label: '包装体积(m³)', width: 120, formatter: (row) => row.packageVolumeM3?.toFixed(6) || '-' },
  { prop: 'packageLengthM', label: '包装长度(m)', width: 120, formatter: (row) => row.packageLengthM?.toFixed(3) || '-' },
  { prop: 'packageWidthM', label: '包装宽度(m)', width: 120, formatter: (row) => row.packageWidthM?.toFixed(3) || '-' },
  { prop: 'packageHeightM', label: '包装高度(m)', width: 120, formatter: (row) => row.packageHeightM?.toFixed(3) || '-' },
]);

// --- Form Fields (包装单位) --- 
const packageUnitFormFields = computed<HeaderField[]>(() => [
  { 
    field: 'unitName', 
    label: '包装单位名称', 
    type: 'select',
    options: dictData.value.unit,
    rules: [
      { required: true, message: '包装单位名称不能为空' }
    ],
    group: '基本信息'
  },
  { 
    field: 'conversionFactor', 
    label: '换算率', 
    type: 'number', 
    props: { precision: 6, min: 0.000001, step: 0.1 }, 
    rules: [
      { required: true, message: '换算率不能为空' },
      { type: 'number', min: 0.000001, message: '换算率必须大于0' }
    ],
    group: '基本信息'
  },
  { 
    field: 'packageWeightKg', 
    label: '包装重量(kg)', 
    type: 'number', 
    props: { precision: 3, min: 0, step: 0.1 },
    group: '包装信息'
  },
  { 
    field: 'packageVolumeM3', 
    label: '包装体积(m³)', 
    type: 'number', 
    props: { precision: 6, min: 0, step: 0.001 },
    group: '包装信息'
  },
  { 
    field: 'packageLengthM', 
    label: '包装长度(m)', 
    type: 'number', 
    props: { precision: 3, min: 0, step: 0.01 },
    group: '包装尺寸'
  },
  { 
    field: 'packageWidthM', 
    label: '包装宽度(m)', 
    type: 'number', 
    props: { precision: 3, min: 0, step: 0.01 },
    group: '包装尺寸'
  },
  { 
    field: 'packageHeightM', 
    label: '包装高度(m)', 
    type: 'number', 
    props: { precision: 3, min: 0, step: 0.01 },
    group: '包装尺寸'
  },
]);

// --- Lifecycle --- 
onMounted(() => {
  loadDictionaryData();
  loadPackageUnits();
});

// 监视 itemId 变化，如果变化则重新加载
watch(() => props.itemId, () => {
  // 重置状态并加载
  packageUnitPagination.currentPage = 1;
  packageUnitPagination.pageSize = 10;
  currentPackageUnitSort.value = null;
  currentPackageUnitFilters.value = {};
  loadPackageUnits();
});

// --- Methods (包装单位管理) ---

// 加载包装单位数据
const loadPackageUnits = async () => {
  if (!props.itemId) {
    console.warn('Cannot load package units without itemId');
    return;
  }
  packageUnitLoading.value = true;
  try {
    console.log('[loadPackageUnits] Fetching package units for item:', props.itemId);
    const response = await getMtlItemPackageUnits(props.itemId);
    
    if (Array.isArray(response)) {
      packageUnitTableData.value = response;
      packageUnitPagination.total = response.length;
      console.log('[loadPackageUnits] 包装单位数据加载成功:', response);
      console.log('[loadPackageUnits] 当前字典数据:', dictData.value.unit);
    } else {
      console.warn('getMtlItemPackageUnits 返回的数据格式不符合预期:', response);
      packageUnitTableData.value = [];
      packageUnitPagination.total = 0;
    }

  } catch (error) {
    console.error('Error loading package units:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
    packageUnitTableData.value = [];
    packageUnitPagination.total = 0;
  } finally {
    packageUnitLoading.value = false;
  }
};

// 新增包装单位
const handleAddNewPackageUnit = () => {
  isPackageUnitEdit.value = false;
  currentEditingPackageUnit.value = {
    unitName: '',
    conversionFactor: 1,
  };
  packageUnitDialogVisible.value = true;
  nextTick(() => {
    packageUnitFormRef.value?.clearValidate();
  });
};

// 编辑包装单位
const handleEditPackageUnit = (row: MtlItemPackageUnitVO) => {
  isPackageUnitEdit.value = true;
  // 深拷贝以避免直接修改表格数据
  currentEditingPackageUnit.value = JSON.parse(JSON.stringify(row));
  packageUnitDialogVisible.value = true;
  nextTick(() => {
    packageUnitFormRef.value?.clearValidate();
  });
};

// 删除包装单位
const handleDeletePackageUnit = async (row: MtlItemPackageUnitVO) => {
  try {
    await ElMessageBox.confirm(`确定删除包装单位 "${row.unitName}" 吗?`, '确认删除', { type: 'warning' });
    packageUnitLoading.value = true;
    console.log('[handleDeletePackageUnit] Calling deleteMtlItemPackageUnit for:', props.itemId, row.id);
    await deleteMtlItemPackageUnit(props.itemId, row.id);
    ElMessage.success('删除成功');
    loadPackageUnits(); // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting package unit:', error);
      const errorMessage = getApiErrorMessage(error);
      ElMessage({
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: errorMessage,
        showClose: true,
        duration: 5 * 1000
      });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    packageUnitLoading.value = false;
  }
};

// 提交包装单位表单
const submitPackageUnitForm = async () => {
  const formRef = packageUnitFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  packageUnitFormLoading.value = true;
  const payload = { ...currentEditingPackageUnit.value };

  try {
    if (isPackageUnitEdit.value) {
      const updatePayload: MtlItemPackageUnitUpdateDTO = {
        unitName: payload.unitName,
        conversionFactor: payload.conversionFactor,
        packageWeightKg: payload.packageWeightKg,
        packageVolumeM3: payload.packageVolumeM3,
        packageLengthM: payload.packageLengthM,
        packageWidthM: payload.packageWidthM,
        packageHeightM: payload.packageHeightM,
      };
      console.log('[submitPackageUnitForm] Calling updateMtlItemPackageUnit with:', updatePayload);
      await updateMtlItemPackageUnit(props.itemId, (payload as any).id, updatePayload);
      ElMessage.success('编辑成功');
    } else {
      const createPayload: MtlItemPackageUnitCreateDTO = {
        unitName: payload.unitName!,
        conversionFactor: payload.conversionFactor!,
        packageWeightKg: payload.packageWeightKg,
        packageVolumeM3: payload.packageVolumeM3,
        packageLengthM: payload.packageLengthM,
        packageWidthM: payload.packageWidthM,
        packageHeightM: payload.packageHeightM,
      };
      console.log('[submitPackageUnitForm] Calling createMtlItemPackageUnit with:', createPayload);
      await createMtlItemPackageUnit(props.itemId, createPayload);
      ElMessage.success('新增成功');
    }

    handleClosePackageUnitDialog();
    loadPackageUnits(); // 刷新列表

  } catch (error) {
    console.error('Error submitting package unit form:', error);
    const errorMessage = getApiErrorMessage(error);
    ElMessage({
      type: 'error',
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      showClose: true,
      duration: 5 * 1000
    });
  } finally {
    packageUnitFormLoading.value = false;
  }
};

// 加载字典数据
const loadDictionaryData = async () => {
  try {
    console.log('[PackageUnitManager] 开始加载字典数据...');
    const dictResults = await loadMultipleDictData(['unit']);
    dictData.value.unit = dictResults['unit'] || [];
    console.log('[PackageUnitManager] 字典数据加载完成:', dictData.value.unit);
  } catch (error) {
    console.error('加载字典数据失败:', error);
  }
};

// 关闭包装单位弹窗
const handleClosePackageUnitDialog = () => {
  packageUnitDialogVisible.value = false;
  currentEditingPackageUnit.value = {};
  isPackageUnitEdit.value = false;
  packageUnitFormLoading.value = false;
};

// 表格事件处理
const handlePackageUnitPageChange = (page: number) => {
  packageUnitPagination.currentPage = page;
  loadPackageUnits();
};

const handlePackageUnitPageSizeChange = (size: number) => {
  packageUnitPagination.pageSize = size;
  packageUnitPagination.currentPage = 1;
  loadPackageUnits();
};

const handlePackageUnitSortChange = (sortParams: SortParams) => {
  currentPackageUnitSort.value = sortParams;
  loadPackageUnits();
};

const handlePackageUnitFilterChange = (filterParams: FilterParams) => {
  currentPackageUnitFilters.value = filterParams;
  packageUnitPagination.currentPage = 1;
  loadPackageUnits();
};
</script>

<style scoped>
/* 添加必要的样式 */
</style> 