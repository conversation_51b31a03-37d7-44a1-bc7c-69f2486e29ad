package middleware

import (
	// Alias standard errors
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/kataras/iris/v12"

	"backend/internal/service"
	"backend/pkg/config"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors" // Alias application errors
	"backend/pkg/logger"
	"backend/pkg/response" // For sending error responses
	// Ensure util is imported for CustomClaims
)

// AuthMiddleware 结构体，持有依赖
type AuthMiddleware struct {
	tokenService service.TokenService
	// userService  service.UserService // TODO: Inject UserService if needed for status checks
	log logger.Logger
	cfg *config.Configuration
}

// NewAuthMiddleware 创建认证中间件实例
// Note: Needs TokenService passed in
func NewAuthMiddleware(tokenService service.TokenService, log logger.Logger, cfg *config.Configuration) *AuthMiddleware {
	if tokenService == nil {
		// Handle nil TokenService - perhaps panic or log fatal?
		log.Fatal("TokenService is required for AuthMiddleware")
	}
	return &AuthMiddleware{
		tokenService: tokenService,
		log:          log,
		cfg:          cfg,
	}
}

// Serve 是 Iris 中间件的处理函数
func (m *AuthMiddleware) Serve(ctx iris.Context) {
	m.log.Debugf("AuthMiddleware: Entering. Path: %s", ctx.Path())
	// 1. 获取 Token (Header > Query > Cookie)
	tokenString := ctx.GetHeader("Authorization") // Use standard header name
	if tokenString == "" {
		tokenString = ctx.URLParam("access_token") // Use default param name, should be configurable
		if tokenString != "" {
			m.log.Debug("AuthMiddleware: Token found in URL param 'access_token'.")
		}
	} else {
		m.log.Debug("AuthMiddleware: Token found in Authorization header.")
	}

	if tokenString == "" {
		tokenString = ctx.GetCookie("access_token") // Use default cookie name, should be configurable
		if tokenString != "" {
			m.log.Debug("AuthMiddleware: Token found in cookie 'access_token'.")
		}
	}

	if tokenString == "" {
		m.log.Warn("AuthMiddleware: Token not found in request.")
		response.FailWithError(ctx, apperrors.ErrAuthUnauthorized.WithDetail("location", "header/query/cookie", "缺少认证令牌"))
		ctx.StopExecution()
		return
	}
	m.log.Debugf("AuthMiddleware: Raw token string: '%s'", tokenString)

	// 去掉 Bearer 前缀 (如果存在)
	originalTokenString := tokenString
	if len(tokenString) > len(constant.TOKEN_PREFIX) && strings.HasPrefix(strings.ToLower(tokenString), strings.ToLower(constant.TOKEN_PREFIX)) {
		tokenString = tokenString[len(constant.TOKEN_PREFIX):]
		m.log.Debugf("AuthMiddleware: Token after stripping prefix: '%s'", tokenString)
	}

	// 2. 解析并验证 Token
	m.log.Debug("AuthMiddleware: Attempting to parse access token.")
	claims, err := m.tokenService.ParseAccessToken(ctx.Request().Context(), tokenString)

	if err != nil {
		m.log.Warnf("AuthMiddleware: ParseAccessToken failed. Error: %v", err)
		// 检查是否为令牌过期错误 (使用我们定义的错误类型)
		if apperrors.IsError(err, apperrors.CODE_AUTH_TOKEN_EXPIRED) {
			m.log.Info("AuthMiddleware: Access token expired, attempting refresh.")

			// 尝试从请求中获取刷新令牌 (调用本地定义的 helper)
			refreshTokenString := getRefreshTokenFromRequest(ctx.Request(), m.cfg)
			if refreshTokenString == "" {
				m.log.Warn("AuthMiddleware: Refresh token not found in request for expired access token.")
				response.FailWithError(ctx, apperrors.ErrAuthUnauthorized.WithDetail("token", "access_expired_refresh_missing", "访问令牌已过期且未找到刷新令牌"))
				ctx.StopExecution()
				return
			}
			m.log.Debugf("AuthMiddleware: Found refresh token: '%s'", refreshTokenString)

			// 解析刷新令牌以获取用户ID
			m.log.Debug("AuthMiddleware: Attempting to parse refresh token.")
			refreshedUserID, refreshErr := m.tokenService.ParseRefreshToken(ctx.Request().Context(), refreshTokenString)
			if refreshErr != nil {
				m.log.Warnf("AuthMiddleware: ParseRefreshToken failed. Error: %v", refreshErr)
				if apperrors.IsError(refreshErr, apperrors.CODE_AUTH_TOKEN_EXPIRED) {
					response.FailWithError(ctx, apperrors.ErrAuthUnauthorized.WithDetail("token", "refresh_expired", "会话已过期，请重新登录"))
				} else {
					response.FailWithError(ctx, apperrors.ErrAuthUnauthorized.WithDetail("token", "refresh_invalid", "无效的刷新令牌"))
				}
				ctx.StopExecution()
				return
			}
			m.log.Infof("AuthMiddleware: Refresh token parsed successfully. Refreshed UserID: %d", refreshedUserID)

			if claims == nil { // claims might be nil if ParseAccessToken returned (nil, expiredError)
				m.log.Error("AuthMiddleware: Original claims are nil after access token expired. Cannot proceed with refresh based on old claims data.")
				// Potentially, we could try to fetch user data using refreshedUserID if the design allows
				// For now, this is a hard stop as GenerateTokens relies on old claims for username, roles etc.
				response.FailWithError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "令牌刷新处理失败 (无法获取旧声明以生成新令牌)"))
				ctx.StopExecution()
				return
			}
			m.log.Debugf("AuthMiddleware: Old claims available for refresh. UserID: %d, Username: %s", claims.UserID, claims.Username)

			if claims.UserID != refreshedUserID {
				m.log.Errorf("AuthMiddleware: UserID mismatch between expired access token (%d) and refresh token (%d).", claims.UserID, refreshedUserID)
				response.FailWithError(ctx, apperrors.ErrAuthUnauthorized.WithDetail("token", "id_mismatch", "令牌不匹配"))
				ctx.StopExecution()
				return
			}

			m.log.Infof("AuthMiddleware: UserID matched. Proceeding to generate new tokens for UserID: %d", refreshedUserID)
			newTokens, tokenErr := m.tokenService.GenerateTokens(ctx.Request().Context(), refreshedUserID, claims.Username, claims.RoleIDs, claims.Remember, claims.IsAdmin)
			if tokenErr != nil {
				m.log.Errorf("AuthMiddleware: GenerateTokens failed during refresh. Error: %v", tokenErr)
				response.FailWithError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "令牌刷新失败").WithCause(tokenErr))
				ctx.StopExecution()
				return
			}
			m.log.Infof("AuthMiddleware: New tokens generated successfully for UserID: %d", refreshedUserID)

			// <<< Revoke the old refresh token from cache >>>
			if m.tokenService != nil {
				revokeErr := m.tokenService.RevokeRefreshToken(ctx.Request().Context(), refreshTokenString)
				if revokeErr != nil {
					m.log.Warn("AuthMiddleware: Failed to revoke old refresh token (non-critical). Error: %v", revokeErr)
				} else {
					m.log.Debug("AuthMiddleware: Old refresh token revoked successfully.")
				}
			} else {
				m.log.Warn("TokenService 在中间件中不可用，无法撤销旧的 Refresh Token")
			}
			// <<< End Revoke old token >>>

			// 将新令牌设置到响应中
			// 1. 设置响应头 (标准做法)
			ctx.Header("Authorization", fmt.Sprintf("%s%s", constant.TOKEN_PREFIX, newTokens.AccessToken)) // Use standard header name and constant prefix

			// 2. (可选) 设置新的 Refresh Token Cookie (如果使用 Cookie 存储刷新令牌)
			if "refresh_token" != "" { // Simplified check from original code
				refreshCookieExpire := time.Now().Add(time.Duration(m.cfg.JWT.Expire) * time.Hour)
				if claims.Remember { // Use old claims' Remember status
					refreshCookieExpire = time.Now().Add(time.Duration(m.cfg.JWT.RefreshExpire) * 24 * time.Hour)
				}
				ctx.SetCookie(&http.Cookie{
					Name:     "refresh_token",
					Value:    newTokens.RefreshToken,
					Expires:  refreshCookieExpire,
					HttpOnly: true,
					Secure:   ctx.Request().TLS != nil,
					Path:     "/",
				})
				m.log.Debug("AuthMiddleware: New refresh token cookie set.")
			}

			// 将新的用户信息设置到上下文中
			// 重新解析一次新令牌以获取最新的 claims (包含新的过期时间等)
			m.log.Debug("AuthMiddleware: Attempting to parse newly generated access token for setting context.")
			newClaims, parseErr := m.tokenService.ParseAccessToken(ctx.Request().Context(), newTokens.AccessToken)
			if parseErr != nil {
				m.log.Errorf("AuthMiddleware: Failed to parse newly generated access token. Error: %v. UserID from refresh was: %d", parseErr, refreshedUserID)
				response.FailWithError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "令牌刷新后处理失败 (无法解析新令牌)"))
				ctx.StopExecution()
				return
			}
			m.log.Infof("AuthMiddleware: Successfully parsed new access token. UserID: %d, Username: %s", newClaims.UserID, newClaims.Username)

			if newClaims.UserID == 0 {
				m.log.Error("AuthMiddleware: FATAL - New claims UserID is 0 after successful refresh and parse. Aborting.")
				response.FailWithError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "令牌刷新后用户ID无效"))
				ctx.StopExecution()
				return
			}

			// 将用户信息直接设置到标准的 context.Context 中
			standardCtxAfterRefresh := ctx.Request().Context() // 获取当前请求上下文（可能已被之前的中间件修改）
			standardCtxWithNewUser := context.WithValue(standardCtxAfterRefresh, constant.CONTEXT_USER_ID, newClaims.UserID)
			standardCtxWithNewUser = context.WithValue(standardCtxWithNewUser, constant.CONTEXT_USERNAME, newClaims.Username)
			standardCtxWithNewUser = context.WithValue(standardCtxWithNewUser, constant.CONTEXT_ROLE_IDS, newClaims.RoleIDs)
			standardCtxWithNewUser = context.WithValue(standardCtxWithNewUser, constant.CONTEXT_IS_ADMIN, newClaims.IsAdmin)
			refreshedReq := ctx.Request().WithContext(standardCtxWithNewUser)
			ctx.ResetRequest(refreshedReq)
			m.log.Infof("AuthMiddleware: UserID %d, Username '%s', RoleIDs %v, IsAdmin %t explicitly set into standard context (from REFRESHED token)", newClaims.UserID, newClaims.Username, newClaims.RoleIDs, newClaims.IsAdmin)

			m.log.Info("AuthMiddleware: Context set from refreshed token. Calling Next().")
			ctx.Next()
			return
		} else if apperrors.IsError(err, apperrors.CODE_AUTH_TOKEN_INVALID) {
			m.log.Warn("AuthMiddleware: Access token is invalid (but not expired).")
			response.FailWithError(ctx, apperrors.ErrAuthTokenInvalid)
			ctx.StopExecution()
			return
		} else {
			m.log.Warnf("AuthMiddleware: Access token parsing failed with an unexpected error type. Error: %v", err)
			response.FailWithError(ctx, apperrors.ErrAuthUnauthorized.WithCause(err))
			ctx.StopExecution()
			return
		}
	}

	// Token is valid (err == nil)
	if claims == nil { // Should not happen if err is nil
		m.log.Error("AuthMiddleware: FATAL - Claims are nil but ParseAccessToken returned no error. Aborting.")
		response.FailWithError(ctx, apperrors.NewSystemError(apperrors.CODE_SYSTEM_INTERNAL, "内部服务器错误 (claims nil post-parse)"))
		ctx.StopExecution()
		return
	}

	if claims.UserID == 0 {
		m.log.Error("AuthMiddleware: FATAL - UserID in valid claims is 0. Aborting. Token was: " + originalTokenString)
		response.FailWithError(ctx, apperrors.NewAuthError(apperrors.CODE_AUTH_TOKEN_INVALID, "认证令牌包含无效的用户ID (0)"))
		ctx.StopExecution()
		return
	}

	m.log.Infof("AuthMiddleware: Access token VERIFIED. UserID: %d, Username: %s", claims.UserID, claims.Username)
	// m.log.Infof("AuthMiddleware: Setting context from ORIGINAL token. UserID: %d, Username: %s, RoleIDs: %v, IsAdmin: %t", claims.UserID, claims.Username, claims.RoleIDs, claims.IsAdmin)
	// ctx.Values().Set(string(constant.CONTEXT_USER_ID), claims.UserID)
	// m.log.Infof("AuthMiddleware: UserID %d set to context with key '%s' (from ORIGINAL token)", claims.UserID, string(constant.CONTEXT_USER_ID))
	// ctx.Values().Set(string(constant.CONTEXT_USERNAME), claims.Username)
	// ctx.Values().Set(string(constant.CONTEXT_ROLE_IDS), claims.RoleIDs)
	// ctx.Values().Set(string(constant.CONTEXT_IS_ADMIN), claims.IsAdmin)

	// 将用户信息直接设置到标准的 context.Context 中
	standardCtx := ctx.Request().Context()
	standardCtxWithUser := context.WithValue(standardCtx, constant.CONTEXT_USER_ID, claims.UserID) // 使用定义的常量键
	standardCtxWithUser = context.WithValue(standardCtxWithUser, constant.CONTEXT_USERNAME, claims.Username)
	standardCtxWithUser = context.WithValue(standardCtxWithUser, constant.CONTEXT_ROLE_IDS, claims.RoleIDs)
	standardCtxWithUser = context.WithValue(standardCtxWithUser, constant.CONTEXT_IS_ADMIN, claims.IsAdmin)
	// **关键修复**: 将带有用户信息的上下文更新回请求中
	req := ctx.Request().WithContext(standardCtxWithUser)
	ctx.ResetRequest(req)

	m.log.Infof("AuthMiddleware: UserID %d, Username '%s', RoleIDs %v, IsAdmin %t explicitly set into standard context (from ORIGINAL token)", claims.UserID, claims.Username, claims.RoleIDs, claims.IsAdmin)

	m.log.Info("AuthMiddleware: Context set. Calling Next().")
	ctx.Next()
}

// getRefreshTokenFromRequest 从请求中提取刷新令牌
// Helper function (to be moved or copied from auth.go / pkg/util)
// This function is now defined within auth_middleware.go
func getRefreshTokenFromRequest(r *http.Request, cfg *config.Configuration) string {
	// 1. Check Header
	token := r.Header.Get("X-Refresh-Token") // Use common header name, should be configurable
	if token != "" {
		return token
	}
	// 2. Check Query Param
	token = r.URL.Query().Get("refresh_token") // Use default param name, should be configurable
	if token != "" {
		return token
	}
	// 3. Check Cookie
	cookie, err := r.Cookie("refresh_token") // Use default cookie name, should be configurable
	if err == nil && cookie.Value != "" {
		return cookie.Value
	}
	return ""
}
