package entity

import (
	"time"

	"gorm.io/gorm"
)

// WmsInboundNotificationDetail 对应数据库中的 wms_inbound_notification_details 表
// 存储入库通知单的明细行信息
type WmsInboundNotificationDetail struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	NotificationID uint `gorm:"column:notification_id;not null;comment:关联通知单头ID" json:"notificationId"` // 关联的 wms_inbound_notifications ID

	LineNo            int        `gorm:"column:line_no;not null;comment:行号" json:"lineNo"`                                          // 行号 (在同一通知单内唯一)
	ItemID            uint       `gorm:"column:item_id;not null;comment:物料ID" json:"itemId"`                                        // 物料ID (关联 wms_items)
	ExpectedQuantity  float64    `gorm:"column:expected_quantity;type:numeric(12,4);not null;comment:预期数量" json:"expectedQuantity"` // 预期数量
	UnitOfMeasure     string     `gorm:"column:unit_of_measure;type:varchar(20);not null;comment:数量单位" json:"unitOfMeasure"`        // 数量单位
	BatchNo           *string    `gorm:"column:batch_no;type:varchar(100);comment:批次号" json:"batchNo"`                              // 批次号 (可选)
	PackageQty        float64    `gorm:"column:package_qty;type:numeric(12,4);comment:包装数量" json:"packageQty"`                      // 包装数量
	PackageUnit       string     `gorm:"column:package_unit;type:varchar(20);comment:包装单位" json:"packageUnit"`                      // 包装单位
	ProductionDate    *time.Time `gorm:"column:production_date;type:date;comment:生产日期" json:"-"`                                    // 生产日期 (可选)
	ExpiryDate        *time.Time `gorm:"column:expiry_date;type:date;comment:过期日期" json:"-"`                                        // 过期日期 (可选)
	Remark            *string    `gorm:"column:remark;type:text;comment:行备注" json:"remark"`
	LineStatus        string     `gorm:"column:line_status;type:wms_inbound_notification_status;not null;default:'DRAFT';comment:通知单状态" json:"lineStatus"` // 通知单状态 (使用数据库定义的 ENUM)
	ProductionDateStr *string    `gorm:"-" json:"productionDate"`
	ExpiryDateStr     *string    `gorm:"-" json:"expiryDate"`
}

// TableName 指定数据库表名方法
func (WmsInboundNotificationDetail) TableName() string {
	return "wms_inbound_notification_detail"
}

func (w *WmsInboundNotificationDetail) BeforeSave(tx *gorm.DB) (err error) {
	if w.ProductionDateStr != nil && *w.ProductionDateStr != "" {
		productionDate, err := time.Parse("2006-01-02", *w.ProductionDateStr)
		if err != nil {
			return err
		}
		w.ProductionDate = &productionDate
	} else {
		w.ProductionDate = nil
	}

	if w.ExpiryDateStr != nil && *w.ExpiryDateStr != "" {
		expiryDate, err := time.Parse("2006-01-02", *w.ExpiryDateStr)
		if err != nil {
			return err
		}
		w.ExpiryDate = &expiryDate
	} else {
		w.ExpiryDate = nil
	}
	return
}

func (w *WmsInboundNotificationDetail) AfterFind(tx *gorm.DB) (err error) {
	if w.ProductionDate != nil {
		dateStr := w.ProductionDate.Format("2006-01-02")
		w.ProductionDateStr = &dateStr
	}

	if w.ExpiryDate != nil {
		dateStr := w.ExpiryDate.Format("2006-01-02")
		w.ExpiryDateStr = &dateStr
	}
	return
}
