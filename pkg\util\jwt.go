package util

import (
	std_errors "errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"backend/pkg/config"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"
)

// 令牌相关错误
var (
	ErrInvalidToken = apperrors.NewError(constant.INVALID_TOKEN, "无效令牌")
	ErrExpiredToken = apperrors.NewError(constant.TOKEN_EXPIRED, "令牌已过期")
)

// CustomClaims 自定义JWT声明结构
type CustomClaims struct {
	UserID   uint   `json:"userId"`
	Username string `json:"username"`
	RoleIDs  []uint `json:"roleIds"`
	Remember bool   `json:"remember"` // 记住我标记
	IsAdmin  bool   `json:"isAdmin"`  // 是否管理员标记
	jwt.RegisteredClaims
}

// getJWTSecret 获取JWT密钥
func getJWTSecret() []byte {
	return []byte(config.CONFIG.JWT.Secret)
}

// getJWTRefreshSecret 获取JWT刷新令牌密钥
func getJWTRefreshSecret() []byte {
	// 如果没有配置刷新令牌密钥，使用普通令牌密钥加上后缀
	return []byte(config.CONFIG.JWT.Secret + "-refresh")
}

// getTokenExpiration 获取令牌过期时间
func getTokenExpiration() time.Duration {
	return time.Duration(config.CONFIG.JWT.Expire) * time.Second
}

// getRefreshTokenExpiration 获取刷新令牌过期时间
func getRefreshTokenExpiration() time.Duration {
	// 刷新令牌过期时间通常是普通令牌的2-4倍
	return time.Duration(config.CONFIG.JWT.Expire*4) * time.Second
}

// GenerateToken 生成JWT令牌
func GenerateToken(userID uint, username string, roleIDs []uint, remember bool, isAdmin bool) (string, error) {
	claims := CustomClaims{
		UserID:   userID,
		Username: username,
		RoleIDs:  roleIDs,
		Remember: remember,
		IsAdmin:  isAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(getTokenExpiration())),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Subject:   fmt.Sprintf("%d", userID),
			Issuer:    config.CONFIG.JWT.Issuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(getJWTSecret())
	if err != nil {
		return "", apperrors.NewSystemError(constant.SYS_ERROR, "生成令牌失败").WithCause(err)
	}
	return tokenString, nil
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*CustomClaims, error) {
	// 去掉Bearer前缀
	if len(tokenString) > len(constant.TOKEN_PREFIX) && tokenString[:len(constant.TOKEN_PREFIX)] == constant.TOKEN_PREFIX {
		tokenString = tokenString[len(constant.TOKEN_PREFIX):]
	}

	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, apperrors.NewSystemError(constant.SYS_ERROR, fmt.Sprintf("非预期的签名算法: %v", token.Header["alg"]))
		}
		return getJWTSecret(), nil
	})

	if err != nil {
		// 记录原始错误信息
		logger.GetLogger().Warn("JWT 解析/验证失败", logger.WithError(err))
		// 检查是否是过期错误
		if expired := std_errors.Is(err, jwt.ErrTokenExpired); expired {
			// 如果是过期错误，尝试提取 claims 并返回 ErrExpiredToken
			// 注意：即使令牌过期，ParseWithClaims 也会填充 claims 结构体，但 token.Valid 会是 false
			if token != nil {
				if claims, ok := token.Claims.(*CustomClaims); ok {
					// 返回过期的 claims 和特定的错误类型
					return claims, ErrExpiredToken
				}
			}
			// 如果无法提取 claims，只返回错误
			return nil, ErrExpiredToken
		}
		// 其他解析错误
		return nil, apperrors.NewError(constant.INVALID_TOKEN, "无效令牌").WithCause(err)
	}

	// 验证并获取声明 (token.Valid 必须为 true)
	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}

	// 如果 token.Valid 为 false 但没有其他错误 (理论上不应该到这里，除非有未知的验证问题)
	return nil, apperrors.NewError(constant.INVALID_TOKEN, "无效令牌 (验证失败)")
}

// ValidateToken 验证JWT令牌
func ValidateToken(tokenString string) (bool, error) {
	_, err := ParseToken(tokenString)
	if err != nil {
		return false, err
	}
	return true, nil
}

// GenerateRefreshToken 生成刷新令牌
func GenerateRefreshToken(userID uint, remember bool) (string, error) {
	// 刷新令牌有效期基于是否记住登录状态
	expiration := getRefreshTokenExpiration()
	if remember {
		// 如果记住登录，使用更长的过期时间
		expiration = time.Duration(config.CONFIG.JWT.RefreshExpire) * time.Second
	}

	claims := jwt.RegisteredClaims{
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(expiration)),
		IssuedAt:  jwt.NewNumericDate(time.Now()),
		NotBefore: jwt.NewNumericDate(time.Now()),
		Subject:   fmt.Sprintf("%d", userID),
		Issuer:    config.CONFIG.JWT.Issuer,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(getJWTRefreshSecret())
	if err != nil {
		return "", apperrors.NewSystemError(constant.SYS_ERROR, "生成刷新令牌失败").WithCause(err)
	}
	return tokenString, nil
}

// ParseRefreshToken 解析刷新令牌
func ParseRefreshToken(tokenString string) (uint, error) {
	token, err := jwt.ParseWithClaims(tokenString, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, apperrors.NewSystemError(constant.SYS_ERROR, fmt.Sprintf("非预期的签名算法: %v", token.Header["alg"]))
		}
		return getJWTRefreshSecret(), nil
	})

	if err != nil {
		if expired := std_errors.Is(err, jwt.ErrTokenExpired); expired {
			return 0, ErrExpiredToken
		}
		return 0, apperrors.NewSystemError(constant.SYS_ERROR, "解析刷新令牌失败").WithCause(err)
	}

	// 获取用户ID
	if claims, ok := token.Claims.(*jwt.RegisteredClaims); ok && token.Valid {
		var userID uint
		_, scanErr := fmt.Sscanf(claims.Subject, "%d", &userID)
		if scanErr != nil {
			return 0, apperrors.NewSystemError(constant.SYS_ERROR, "解析用户ID失败").WithCause(scanErr)
		}
		return userID, nil
	}

	return 0, apperrors.NewError(constant.INVALID_TOKEN, "无效令牌")
}

// GenerateTokenPair 生成令牌对（访问令牌和刷新令牌）
func GenerateTokenPair(userID uint, username string, roleIDs []uint, remember bool, isAdmin bool) (tokenString string, refreshTokenString string, err error) {
	tokenString, err = GenerateToken(userID, username, roleIDs, remember, isAdmin)
	if err != nil {
		return "", "", err
	}

	refreshTokenString, err = GenerateRefreshToken(userID, remember)
	if err != nil {
		return "", "", err
	}

	return tokenString, refreshTokenString, nil
}

// RefreshTokenPair 刷新令牌对
func RefreshTokenPair(refreshTokenString string, username string, roleIDs []uint, remember bool, isAdmin bool) (newTokenString string, newRefreshTokenString string, err error) {
	userID, err := ParseRefreshToken(refreshTokenString)
	if err != nil {
		return "", "", err
	}

	return GenerateTokenPair(userID, username, roleIDs, remember, isAdmin)
}
