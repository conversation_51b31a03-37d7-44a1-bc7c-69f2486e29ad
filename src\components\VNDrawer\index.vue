<template>
  <el-drawer
    :model-value="modelValue"
    @update:model-value="handleUpdateModelValue"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleElClose" 
    @closed="handleClosed"
    :direction="direction"
    :size="size"
    :title="title"
    :with-header="withHeader"
    :modal="modal"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    :open-delay="openDelay"
    :close-delay="closeDelay"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="beforeClose"
    :destroy-on-close="destroyOnClose"
    :modal-class="modalClass"
    :z-index="zIndex"
  >
    <!-- Header 插槽 (优先级高于 title prop) -->
    <template #header v-if="$slots.header">
      <slot name="header"></slot>
    </template>
    <!-- 默认内容插槽 -->
    <slot></slot>

    <!-- Footer Logic -->
    <!-- Case 1: No default slot AND needs footer (via slot or default buttons) -->
    <template #footer v-if="!$slots.default && ($slots.footer || showCancelButton || showConfirmButton)">
      <div :style="footerStyle">
        <slot name="footer">
          <!-- Default buttons -->
          <el-button 
            v-if="showCancelButton"
            @click="handleCancel"
          >
            {{ cancelButtonText }}
          </el-button>
          <el-button 
            v-if="showConfirmButton" 
            type="primary" 
            @click="handleConfirm"
            :loading="confirmButtonLoading"
          >
            {{ confirmButtonText }}
          </el-button>
        </slot>
      </div>
    </template>

    <!-- Case 2: Default slot exists AND needs footer (via slot or default buttons) -->
    <div v-if="$slots.default && ($slots.footer || showCancelButton || showConfirmButton)" :style="footerStyle" class="drawer-footer-when-default-exists">
        <slot name="footer">
          <!-- Default buttons -->
          <el-button 
            v-if="showCancelButton"
            @click="handleCancel"
          >
            {{ cancelButtonText }}
          </el-button>
          <el-button 
            v-if="showConfirmButton" 
            type="primary" 
            @click="handleConfirm"
            :loading="confirmButtonLoading"
          >
            {{ confirmButtonText }}
          </el-button>
        </slot>
    </div>

  </el-drawer>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, withDefaults, computed } from 'vue';
import { ElDrawer, ElButton } from 'element-plus';
import type { VNDrawerProps, VNDrawerEmits } from './types';
import type { CSSProperties } from 'vue';

// 定义 Props，继承 ElDrawer 的 Props 并添加自定义 Props
const props = withDefaults(defineProps<VNDrawerProps>(), {
  // --- 自定义 Props 默认值 ---
  modelValue: false,
  showConfirmButton: false, // Drawer 通常不需要默认确认按钮
  showCancelButton: true,
  confirmButtonText: '确认',
  cancelButtonText: '取消',
  confirmButtonLoading: false,
  footerAlign: 'right',
  // --- 继承 ElDrawer Props 的常见默认值 ---
  direction: 'rtl', // 默认从右往左
  size: '30%',
  title: '',
  withHeader: true,
  modal: true,
  appendToBody: false,
  lockScroll: true,
  openDelay: 0,
  closeDelay: 0,
  closeOnClickModal: true,
  closeOnPressEscape: true,
  showClose: true,
  destroyOnClose: false,
});

// 定义 Emits
const emit = defineEmits<VNDrawerEmits>();

// 计算底部样式
const footerStyle = computed<CSSProperties>(() => {
  let justifyContent = 'flex-end'; // 默认右对齐
  if (props.footerAlign === 'left') {
    justifyContent = 'flex-start';
  } else if (props.footerAlign === 'center') {
    justifyContent = 'center';
  }
  return {
    display: 'flex',
    justifyContent: justifyContent,
    padding: '10px 20px', // 根据需要调整内边距
    borderTop: '1px solid var(--el-border-color-lighter)' // 添加分割线
  };
});

// 处理 v-model 更新
const handleUpdateModelValue = (value: boolean) => {
  emit('update:modelValue', value);
};

// 转发 ElDrawer 的事件
const handleOpen = () => emit('open');
const handleOpened = () => emit('opened');
const handleClosed = () => emit('closed');

// 特别处理 ElDrawer 的 close 事件，它在点击遮罩或关闭按钮时触发
const handleElClose = () => {
  // 我们统一通过 update:modelValue 来控制关闭
  // 但仍然需要转发 close 事件给父组件，以便监听
  emit('close');
  emit('update:modelValue', false);
};

// 处理默认按钮点击
const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
  // 通常点击取消也会关闭抽屉
  emit('update:modelValue', false);
};

</script>

<style scoped>
/* 如果抽屉内容需要滚动，可能需要对内容区域设置 overflow */
:deep(.el-drawer__body) {
  /* 示例：允许内容垂直滚动 */
  /* overflow-y: auto; */
}

/* 当默认插槽存在时，确保底部按钮容器有明确的样式 */
.drawer-footer-when-default-exists {
    /* position: sticky; */ /* 如果希望底部固定，可以考虑sticky定位，但需谨慎处理 */
    /* bottom: 0; */
    background-color: var(--el-bg-color); /* 确保背景色 */
}

/* 默认按钮间距 */
:deep(.el-drawer__footer .el-button + .el-button),
.drawer-footer-when-default-exists .el-button + .el-button {
  margin-left: 10px;
}

</style> 