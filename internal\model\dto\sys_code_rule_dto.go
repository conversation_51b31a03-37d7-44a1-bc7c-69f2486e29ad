package dto

import (
	"backend/pkg/response"
	"time"
)

// SysCodeRuleCreateReq 创建编码规则请求
type SysCodeRuleCreateReq struct {
	RuleCode       string  `json:"ruleCode" binding:"required,max=50" comment:"规则编码"`
	RuleName       string  `json:"ruleName" binding:"required,max=100" comment:"规则名称"`
	BusinessType   string  `json:"businessType" binding:"required,max=50" comment:"业务类型"`
	CodeFormat     string  `json:"codeFormat" binding:"required,max=200" comment:"编码格式模板"`
	Separator      *string `json:"separator" comment:"分隔符"`
	ResetFrequency string  `json:"resetFrequency" binding:"required,max=20" comment:"重置频率"`
	SequenceLength *int    `json:"sequenceLength" comment:"序号长度"`
	SequenceStart  *int    `json:"sequenceStart" comment:"序号起始值"`
	IsDefault      *bool   `json:"isDefault" comment:"是否默认规则"`
	Remark         *string `json:"remark" comment:"备注"`
}

// SysCodeRuleUpdateReq 更新编码规则请求
type SysCodeRuleUpdateReq struct {
	RuleCode       *string `json:"ruleCode" binding:"omitempty,max=50" comment:"规则编码"`
	RuleName       *string `json:"ruleName" binding:"omitempty,max=100" comment:"规则名称"`
	BusinessType   *string `json:"businessType" binding:"omitempty,max=50" comment:"业务类型"`
	CodeFormat     *string `json:"codeFormat" binding:"omitempty,max=200" comment:"编码格式模板"`
	Separator      *string `json:"separator" comment:"分隔符"`
	ResetFrequency *string `json:"resetFrequency" binding:"omitempty,max=20" comment:"重置频率"`
	SequenceLength *int    `json:"sequenceLength" comment:"序号长度"`
	SequenceStart  *int    `json:"sequenceStart" comment:"序号起始值"`
	IsDefault      *bool   `json:"isDefault" comment:"是否默认规则"`
	Status         *string `json:"status" binding:"omitempty,max=20" comment:"状态"`
	Remark         *string `json:"remark" comment:"备注"`
}

// SysCodeRuleQueryReq 编码规则查询请求
type SysCodeRuleQueryReq struct {
	response.PageQuery
	RuleCode     *string `json:"ruleCode" form:"ruleCode" comment:"规则编码"`
	RuleName     *string `json:"ruleName" form:"ruleName" comment:"规则名称"`
	BusinessType *string `json:"businessType" form:"businessType" comment:"业务类型"`
	Status       *string `json:"status" form:"status" comment:"状态"`
	IsDefault    *bool   `json:"isDefault" form:"isDefault" comment:"是否默认规则"`
}

// CodeGenerationReq 编码生成请求
type CodeGenerationReq struct {
	BusinessType string                 `json:"businessType" binding:"required" comment:"业务类型"`
	ContextData  map[string]interface{} `json:"contextData" comment:"上下文数据"`
}

// CodeGenerationRes 编码生成响应
type CodeGenerationRes struct {
	GeneratedCode  string `json:"generatedCode" comment:"生成的编码"`
	RuleID         uint   `json:"ruleId" comment:"使用的规则ID"`
	SequenceNumber int64  `json:"sequenceNumber" comment:"使用的序号"`
	BusinessType   string `json:"businessType" comment:"业务类型"`
}

// CodePreviewReq 编码预览请求
type CodePreviewReq struct {
	RuleID      *uint                  `json:"ruleId" comment:"规则ID"`
	CodeFormat  *string                `json:"codeFormat" comment:"编码格式模板"`
	ContextData map[string]interface{} `json:"contextData" comment:"上下文数据"`
}

// CodePreviewRes 编码预览响应
type CodePreviewRes struct {
	PreviewCode string `json:"previewCode" comment:"预览编码"`
	IsValid     bool   `json:"isValid" comment:"格式是否有效"`
	ErrorMsg    string `json:"errorMsg" comment:"错误信息"`
}

// SysCodeFormatComponentCreateReq 创建编码格式组件请求
type SysCodeFormatComponentCreateReq struct {
	ComponentCode string  `json:"componentCode" binding:"required,max=50" comment:"组件编码"`
	ComponentName string  `json:"componentName" binding:"required,max=100" comment:"组件名称"`
	ComponentType string  `json:"componentType" binding:"required,max=30" comment:"组件类型"`
	FormatPattern *string `json:"formatPattern" comment:"格式模式"`
	Description   *string `json:"description" comment:"描述说明"`
	ExampleValue  *string `json:"exampleValue" comment:"示例值"`
}

// SysCodeFormatComponentUpdateReq 更新编码格式组件请求
type SysCodeFormatComponentUpdateReq struct {
	ComponentCode *string `json:"componentCode" binding:"omitempty,max=50" comment:"组件编码"`
	ComponentName *string `json:"componentName" binding:"omitempty,max=100" comment:"组件名称"`
	ComponentType *string `json:"componentType" binding:"omitempty,max=30" comment:"组件类型"`
	FormatPattern *string `json:"formatPattern" comment:"格式模式"`
	Description   *string `json:"description" comment:"描述说明"`
	ExampleValue  *string `json:"exampleValue" comment:"示例值"`
	Status        *string `json:"status" binding:"omitempty,max=20" comment:"状态"`
}

// SysCodeFormatComponentQueryReq 编码格式组件查询请求
type SysCodeFormatComponentQueryReq struct {
	response.PageQuery
	ComponentCode *string `json:"componentCode" form:"componentCode" comment:"组件编码"`
	ComponentName *string `json:"componentName" form:"componentName" comment:"组件名称"`
	ComponentType *string `json:"componentType" form:"componentType" comment:"组件类型"`
	Status        *string `json:"status" form:"status" comment:"状态"`
}

// ResetSequenceReq 重置序号请求
type ResetSequenceReq struct {
	RuleID uint `json:"ruleId" binding:"required" comment:"规则ID"`
}

// SysCodeGenerationLogQueryReq 编码生成历史查询请求
type SysCodeGenerationLogQueryReq struct {
	response.PageQuery
	RuleID        *uint      `json:"ruleId" form:"ruleId" comment:"规则ID"`
	BusinessType  *string    `json:"businessType" form:"businessType" comment:"业务类型"`
	BusinessID    *uint      `json:"businessId" form:"businessId" comment:"业务记录ID"`
	GeneratedCode *string    `json:"generatedCode" form:"generatedCode" comment:"生成的编码"`
	CreatedBy     *uint      `json:"createdBy" form:"createdBy" comment:"创建人"`
	StartTime     *time.Time `json:"startTime" form:"startTime" comment:"开始时间"`
	EndTime       *time.Time `json:"endTime" form:"endTime" comment:"结束时间"`
}
