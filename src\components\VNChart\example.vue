<template>
  <div class="vnchart-examples-page">
    <h2>VNChart ECharts 组件示例</h2>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础柱状图</span>
        </div>
      </template>
      <VNChart :option="barChartOption" />
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>动态更新折线图</span>
           <el-button size="small" @click="updateLineChartData">更新数据</el-button>
        </div>
      </template>
      <VNChart :option="lineChartOption" height="350px" />
    </el-card>

     <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础饼图</span>
        </div>
      </template>
      <VNChart :option="pieChartOption" height="300px" />
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VNChart from './index.vue';
import type { EChartsOption } from './types';
import { ElCard, ElButton } from 'element-plus';

// 基础柱状图配置
const barChartOption = ref<EChartsOption>({
  title: {
    text: '服装销量统计',
    left: 'center'
  },
  tooltip: { trigger: 'axis' },
  legend: {
    data: ['销量'],
    top: 'bottom'
  },
  grid: { // 调整网格，给图例留空间
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"]
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '销量',
    type: 'bar', // 使用已引入的 bar chart
    data: [5, 20, 36, 10, 10, 20]
  }]
});

// 动态折线图配置
const lineChartOption = ref<EChartsOption>({
  title: {
    text: '模拟数据变化'
  },
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [820, 932, 901, 934, 1290, 1330, 1320],
    type: 'line',
    areaStyle: {} // 可选：添加面积图样式
  }]
});

// 基础饼图配置
const pieChartOption = ref<EChartsOption>({
    title: {
        text: '访问来源',
        left: 'center'
    },
    tooltip: {
        trigger: 'item'
    },
    legend: {
        orient: 'vertical',
        left: 'left'
    },
    series: [
        {
            name: '访问来源',
            type: 'pie',
            radius: '50%',
            data: [
                { value: 1048, name: '搜索引擎' },
                { value: 735, name: '直接访问' },
                { value: 580, name: '邮件营销' },
                { value: 484, name: '联盟广告' },
                { value: 300, name: '视频广告' }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }
    ]
});

// 更新折线图数据的方法
const updateLineChartData = () => {
  const newData = lineChartOption.value.series[0].data.map(() => Math.round(Math.random() * 1000 + 500));
  // 必须创建一个新的对象或修改现有对象的属性才能触发响应式更新
  // 直接修改数组元素可能不会触发 deep watch
  lineChartOption.value = {
    ...lineChartOption.value,
    series: [{
      ...lineChartOption.value.series[0],
      data: newData
    }]
  };
};

</script>

<style scoped>
.vnchart-examples-page {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}
</style> 