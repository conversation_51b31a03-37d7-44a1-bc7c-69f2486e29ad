package service

import (
	"context"
	"fmt"
	"time"

	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"backend/pkg/logger"

	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

// WmsPutawayTaskDetailService 定义上架任务明细服务接口
type WmsPutawayTaskDetailService interface {
	BaseService
	Create(ctx context.Context, req *dto.WmsPutawayTaskDetailCreateReq) (*vo.WmsPutawayTaskDetailVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsPutawayTaskDetailUpdateReq) (*vo.WmsPutawayTaskDetailVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsPutawayTaskDetailVO, error)
	GetByTaskID(ctx context.Context, taskID uint) ([]*vo.WmsPutawayTaskDetailVO, error)
	GetPage(ctx context.Context, req *dto.WmsPutawayTaskDetailQueryReq) (*vo.PageResult[vo.WmsPutawayTaskDetailVO], error)
	BatchCreate(ctx context.Context, req *dto.WmsPutawayTaskDetailBatchCreateReq) error
	BatchUpdate(ctx context.Context, req *dto.WmsPutawayTaskDetailBatchUpdateReq) error
	BatchDelete(ctx context.Context, ids []uint) error
	Execute(ctx context.Context, id uint, req *dto.WmsPutawayTaskDetailExecuteReq) (*vo.WmsPutawayTaskDetailVO, error)
	BatchExecute(ctx context.Context, executes []dto.PutawayTaskDetailExecuteItem) error
	GetDetailsSummary(ctx context.Context, taskID uint) (*dto.PutawayTaskDetailSummary, error)
	CreateFromReceivingDetails(ctx context.Context, taskID uint, receivingRecordID uint) error
	ValidateTaskCompletion(ctx context.Context, taskID uint) (bool, error)
	UpdateTaskStatusFromDetails(ctx context.Context, taskID uint) error
}

// 删除重复的类型定义，使用DTO中的定义

// wmsPutawayTaskDetailServiceImpl 上架任务明细服务实现
type wmsPutawayTaskDetailServiceImpl struct {
	BaseServiceImpl
}

// NewWmsPutawayTaskDetailService 创建上架任务明细服务
func NewWmsPutawayTaskDetailService(sm *ServiceManager) WmsPutawayTaskDetailService {
	return &wmsPutawayTaskDetailServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) Create(ctx context.Context, req *dto.WmsPutawayTaskDetailCreateReq) (*vo.WmsPutawayTaskDetailVO, error) {
	var detail *entity.WmsPutawayTaskDetail
	var voResult *vo.WmsPutawayTaskDetailVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()

		// 验证上架任务是否存在
		taskRepo := txRepoMgr.GetWmsPutawayTaskRepository()
		task, err := taskRepo.FindByID(ctx, req.PutawayTaskID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证上架任务失败").WithCause(err)
		}

		// 检查任务状态是否允许添加明细
		if task.Status == "COMPLETED" || task.Status == "CANCELLED" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已完成或已取消的任务不能添加明细")
		}

		// 验证收货明细是否存在
		if req.ReceivingRecordDetailID > 0 {
			receivingDetailRepo := txRepoMgr.GetWmsReceivingRecordDetailRepository()
			_, err := receivingDetailRepo.FindByID(ctx, req.ReceivingRecordDetailID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "收货记录明细不存在")
				}
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证收货记录明细失败").WithCause(err)
			}
		}

		// 创建明细
		detail = &entity.WmsPutawayTaskDetail{}
		copier.Copy(detail, req)
		detail.AccountBookID = task.AccountBookID
		detail.Status = string(entity.PutawayTaskStatusPending)

		if err := repo.Create(ctx, detail); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建上架任务明细失败").WithCause(err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, detail)
	return voResult, nil
}

// Update 更新上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsPutawayTaskDetailUpdateReq) (*vo.WmsPutawayTaskDetailVO, error) {
	var detail *entity.WmsPutawayTaskDetail
	var voResult *vo.WmsPutawayTaskDetailVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()
		var err error

		detail, err = repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务明细不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
		}

		// 检查状态是否允许修改
		if detail.Status == "COMPLETED" || detail.Status == "CANCELLED" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已完成或已取消的明细不能修改")
		}

		// 更新字段
		copier.Copy(detail, req)

		if err := repo.Update(ctx, detail); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新上架任务明细失败").WithCause(err)
		}

		// 检查是否需要更新主任务状态
		if req.Status != nil || req.ActualPutawayQuantity != nil {
			if err := s.UpdateTaskStatusFromDetails(ctx, detail.PutawayTaskID); err != nil {
				logger.WithContext(ctx).WithError(err).Errorf("更新主任务状态失败: taskID=%d", detail.PutawayTaskID)
				// 不影响主流程，只记录日志
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, detail)
	return voResult, nil
}

// Delete 删除上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()

		detail, err := repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务明细不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
		}

		// 检查状态是否允许删除
		if detail.Status != "PENDING" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待处理状态的明细才能删除")
		}

		if err := repo.Delete(ctx, id); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除上架任务明细失败").WithCause(err)
		}

		return nil
	})
}

// GetByID 根据ID获取上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsPutawayTaskDetailVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskDetailRepository()

	detail, err := repo.FindByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务明细不存在")
		}
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
	}

	var voResult *vo.WmsPutawayTaskDetailVO
	copier.Copy(&voResult, detail)
	return voResult, nil
}

// GetByTaskID 根据任务ID获取明细列表
func (s *wmsPutawayTaskDetailServiceImpl) GetByTaskID(ctx context.Context, taskID uint) ([]*vo.WmsPutawayTaskDetailVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskDetailRepository()

	details, err := repo.GetByTaskID(ctx, taskID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
	}

	var voResults []*vo.WmsPutawayTaskDetailVO
	copier.Copy(&voResults, details)
	return voResults, nil
}

// GetPage 分页查询上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) GetPage(ctx context.Context, req *dto.WmsPutawayTaskDetailQueryReq) (*vo.PageResult[vo.WmsPutawayTaskDetailVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskDetailRepository()

	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
	}

	var voResult *vo.PageResult[vo.WmsPutawayTaskDetailVO]
	copier.Copy(&voResult, pageResult)
	return voResult, nil
}

// BatchCreate 批量创建上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) BatchCreate(ctx context.Context, req *dto.WmsPutawayTaskDetailBatchCreateReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()

		// 验证上架任务是否存在
		taskRepo := txRepoMgr.GetWmsPutawayTaskRepository()
		task, err := taskRepo.FindByID(ctx, req.PutawayTaskID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证上架任务失败").WithCause(err)
		}

		// 检查任务状态
		if task.Status == "COMPLETED" || task.Status == "CANCELLED" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "已完成或已取消的任务不能添加明细")
		}

		// 批量创建明细
		var details []entity.WmsPutawayTaskDetail
		for _, detailReq := range req.Details {
			detail := entity.WmsPutawayTaskDetail{}
			copier.Copy(&detail, &detailReq)
			detail.PutawayTaskID = req.PutawayTaskID
			detail.AccountBookID = task.AccountBookID
			detail.Status = string(entity.PutawayTaskStatusPending)
			details = append(details, detail)
		}

		if err := repo.BatchCreate(ctx, details); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "批量创建上架任务明细失败").WithCause(err)
		}

		return nil
	})
}

// BatchUpdate 批量更新上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) BatchUpdate(ctx context.Context, req *dto.WmsPutawayTaskDetailBatchUpdateReq) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()

		for _, update := range req.Updates {
			detail, err := repo.FindByID(ctx, update.ID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("上架任务明细不存在: ID=%d", update.ID))
				}
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
			}

			// 检查状态是否允许修改
			if detail.Status == "COMPLETED" || detail.Status == "CANCELLED" {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("已完成或已取消的明细不能修改: ID=%d", update.ID))
			}

			// 更新字段
			copier.Copy(detail, &update.Detail)

			if err := repo.Update(ctx, detail); err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, fmt.Sprintf("更新上架任务明细失败: ID=%d", update.ID)).WithCause(err)
			}
		}

		return nil
	})
}

// BatchDelete 批量删除上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) BatchDelete(ctx context.Context, ids []uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()

		for _, id := range ids {
			detail, err := repo.FindByID(ctx, id)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					continue // 跳过不存在的记录
				}
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
			}

			// 检查状态是否允许删除
			if detail.Status != "PENDING" {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("只有待处理状态的明细才能删除: ID=%d", id))
			}

			if err := repo.Delete(ctx, id); err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, fmt.Sprintf("删除上架任务明细失败: ID=%d", id)).WithCause(err)
			}
		}

		return nil
	})
}

// Execute 执行上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) Execute(ctx context.Context, id uint, req *dto.WmsPutawayTaskDetailExecuteReq) (*vo.WmsPutawayTaskDetailVO, error) {
	var detail *entity.WmsPutawayTaskDetail
	var voResult *vo.WmsPutawayTaskDetailVO

	err := s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()
		var err error

		detail, err = repo.FindByID(ctx, id)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务明细不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
		}

		// 检查状态是否允许执行
		if detail.Status != "PENDING" && detail.Status != "IN_PROGRESS" {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "只有待处理或执行中的明细才能执行")
		}

		// 验证实际上架数量
		if req.ActualPutawayQuantity > detail.PutawayQuantity {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "实际上架数量不能超过计划数量")
		}

		// 验证库位是否存在
		locationRepo := txRepoMgr.GetWmsLocationRepository()
		_, err = locationRepo.FindByID(ctx, req.ActualLocationID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "目标库位不存在")
			}
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证目标库位失败").WithCause(err)
		}

		// 更新执行信息
		detail.ActualLocationID = &req.ActualLocationID
		detail.ActualPutawayQuantity = req.ActualPutawayQuantity
		detail.ExceptionReason = req.ExceptionReason
		detail.Status = string(entity.PutawayTaskStatusCompleted)

		if err := repo.Update(ctx, detail); err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新上架任务明细失败").WithCause(err)
		}

		// 更新主任务状态
		if err := s.UpdateTaskStatusFromDetails(ctx, detail.PutawayTaskID); err != nil {
			logger.WithContext(ctx).WithError(err).Errorf("更新主任务状态失败: taskID=%d", detail.PutawayTaskID)
			// 不影响主流程，只记录日志
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	copier.Copy(&voResult, detail)
	return voResult, nil
}

// BatchExecute 批量执行上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) BatchExecute(ctx context.Context, executes []dto.PutawayTaskDetailExecuteItem) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		repo := txRepoMgr.GetWmsPutawayTaskDetailRepository()
		locationRepo := txRepoMgr.GetWmsLocationRepository()

		var taskIDs []uint
		taskIDMap := make(map[uint]bool)

		for _, execute := range executes {
			detail, err := repo.FindByID(ctx, execute.ID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("上架任务明细不存在: ID=%d", execute.ID))
				}
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "查询上架任务明细失败").WithCause(err)
			}

			// 检查状态
			if detail.Status != "PENDING" && detail.Status != "IN_PROGRESS" {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("只有待处理或执行中的明细才能执行: ID=%d", execute.ID))
			}

			// 验证实际上架数量
			if execute.Execute.ActualPutawayQuantity > detail.PutawayQuantity {
				return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("实际上架数量不能超过计划数量: ID=%d", execute.ID))
			}

			// 验证库位
			_, err = locationRepo.FindByID(ctx, execute.Execute.ActualLocationID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, fmt.Sprintf("目标库位不存在: ID=%d", execute.ID))
				}
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "验证目标库位失败").WithCause(err)
			}

			// 更新执行信息
			detail.ActualLocationID = &execute.Execute.ActualLocationID
			detail.ActualPutawayQuantity = execute.Execute.ActualPutawayQuantity
			detail.ExceptionReason = execute.Execute.ExceptionReason
			detail.Status = string(entity.PutawayTaskStatusCompleted)

			if err := repo.Update(ctx, detail); err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, fmt.Sprintf("更新上架任务明细失败: ID=%d", execute.ID)).WithCause(err)
			}

			// 收集需要更新状态的主任务ID
			if !taskIDMap[detail.PutawayTaskID] {
				taskIDs = append(taskIDs, detail.PutawayTaskID)
				taskIDMap[detail.PutawayTaskID] = true
			}
		}

		// 批量更新主任务状态
		for _, taskID := range taskIDs {
			if err := s.UpdateTaskStatusFromDetails(ctx, taskID); err != nil {
				logger.WithContext(ctx).WithError(err).Errorf("更新主任务状态失败: taskID=%d", taskID)
				// 不影响主流程，只记录日志
			}
		}

		return nil
	})
}

// GetDetailsSummary 获取明细汇总信息
func (s *wmsPutawayTaskDetailServiceImpl) GetDetailsSummary(ctx context.Context, taskID uint) (*dto.PutawayTaskDetailSummary, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskDetailRepository()

	summary, err := repo.GetDetailsSummary(ctx, taskID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取明细汇总信息失败").WithCause(err)
	}

	return summary, nil
}

// CreateFromReceivingDetails 从收货明细创建上架任务明细
func (s *wmsPutawayTaskDetailServiceImpl) CreateFromReceivingDetails(ctx context.Context, taskID uint, receivingRecordID uint) error {
	return s.GetServiceManager().GetRepositoryManager().Transaction(ctx, func(txRepoMgr *repository.RepositoryManager) error {
		receivingDetailRepo := txRepoMgr.GetWmsReceivingRecordDetailRepository()
		putawayDetailRepo := txRepoMgr.GetWmsPutawayTaskDetailRepository()

		receivingDetails, err := receivingDetailRepo.FindByReceivingRecordID(ctx, receivingRecordID)

		if err != nil {
			return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取收货明细失败").WithCause(err)
		}

		// 验证上架任务
		taskRepo := txRepoMgr.GetWmsPutawayTaskRepository()
		task, err := taskRepo.FindByID(ctx, taskID)
		if err != nil {
			return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "上架任务不存在")
		}

		// 创建上架明细
		var putawayDetails []entity.WmsPutawayTaskDetail
		for i, receivingDetail := range receivingDetails {
			// 只有已完成状态的明细才能创建上架任务
			if receivingDetail.LineStatus != string(entity.ReceivingRecordStatusCompleted) {
				continue
			}

			receivedAtLocationID := receivingDetail.ReceivedAtLocationID

			putawayDetail := entity.WmsPutawayTaskDetail{
				PutawayTaskID:           taskID,
				ReceivingRecordDetailID: receivingDetail.ID,
				LineNo:                  i + 1,
				ItemID:                  receivingDetail.ItemID,
				PutawayQuantity:         receivingDetail.ReceivedQuantity,
				UnitOfMeasure:           receivingDetail.UnitOfMeasure,
				BatchNo:                 receivingDetail.BatchNo,
				ProductionDate:          receivingDetail.ProductionDate,
				ExpiryDate:              receivingDetail.ExpiryDate,
				SourceLocationID:        receivedAtLocationID,
				Status:                  string(entity.PutawayTaskStatusPending),
			}
			putawayDetail.AccountBookID = task.AccountBookID

			putawayDetails = append(putawayDetails, putawayDetail)
		}

		if len(putawayDetails) > 0 {
			if err := putawayDetailRepo.BatchCreate(ctx, putawayDetails); err != nil {
				return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建上架任务明细失败").WithCause(err)
			}
		}

		return nil
	})
}

// ValidateTaskCompletion 验证任务是否完成
func (s *wmsPutawayTaskDetailServiceImpl) ValidateTaskCompletion(ctx context.Context, taskID uint) (bool, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetWmsPutawayTaskDetailRepository()

	summary, err := repo.GetDetailsSummary(ctx, taskID)
	if err != nil {
		return false, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取明细汇总信息失败").WithCause(err)
	}

	// 如果没有明细或所有明细都已完成，则任务完成
	return summary.TotalDetailCount == 0 || summary.CompletedDetailCount == summary.TotalDetailCount, nil
}

// UpdateTaskStatusFromDetails 根据明细状态更新任务状态
func (s *wmsPutawayTaskDetailServiceImpl) UpdateTaskStatusFromDetails(ctx context.Context, taskID uint) error {
	repoMgr := s.GetServiceManager().GetRepositoryManager()
	taskRepo := repoMgr.GetWmsPutawayTaskRepository()
	detailRepo := repoMgr.GetWmsPutawayTaskDetailRepository()

	// 获取明细汇总
	summary, err := detailRepo.GetDetailsSummary(ctx, taskID)
	if err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取明细汇总信息失败").WithCause(err)
	}

	// 确定新的任务状态
	var newStatus string
	var completedAt *time.Time
	now := time.Now()

	if summary.TotalDetailCount == 0 {
		// 没有明细，保持当前状态
		return nil
	} else if summary.CompletedDetailCount == summary.TotalDetailCount {
		// 所有明细都已完成
		newStatus = string(entity.PutawayTaskStatusCompleted)
		completedAt = &now
	} else if summary.CompletedDetailCount > 0 {
		// 部分明细已完成
		newStatus = string(entity.PutawayTaskStatusInProgress)
	} else {
		// 没有明细完成，但有明细分配
		newStatus = string(entity.PutawayTaskStatusAssigned)
	}

	// 更新任务状态
	if completedAt != nil {
		return taskRepo.CompleteTask(ctx, taskID, *completedAt)
	} else {
		return taskRepo.UpdateStatus(ctx, taskID, newStatus)
	}
}
