import request from '@/utils/request'

// 通用分页结果类型
export interface PageResult<T> {
  list: T[]
  total: number
}

// ==================== 类型定义 ====================

// 收货记录状态枚举
export type WmsReceivingRecordStatus =
  | 'PENDING'
  | 'RECEIVING'
  | 'PENDING_INSPECTION'
  | 'INSPECTING'
  | 'PARTIALLY_COMPLETED'
  | 'COMPLETED'
  | 'CLOSED'
  | 'CANCELLED'

// 收货记录明细创建请求
export interface WmsReceivingRecordDetailCreateReq {
  notificationDetailId?: number
  lineNo: number
  itemId: number
  expectedQuantity: number
  receivedQuantity: number
  unitOfMeasure: string
  receivedAtLocationId: number
  packageQty?: number
  packageUnit?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  quantityDifference?: number
  discrepancyReason?: string
  remark?: string
}

// 收货记录明细更新请求
export interface WmsReceivingRecordDetailUpdateReq {
  id: number
  receivedQuantity?: number
  unitOfMeasure?: string
  packageQty?: number
  packageUnit?: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  quantityDifference?: number
  discrepancyReason?: string
  remark?: string
}

// 通用创建收货记录请求
export interface WmsReceivingRecordCreateReq {
  receivingNo?: string
  notificationId?: number
  receivingType: string
  actualArrivalDate?: string
  supplementDeadline?: string
  clientId: number
  warehouseId: number
  supplierShipper?: string
  sourceDocNo?: string
  remark?: string
  details: WmsReceivingRecordDetailCreateReq[]
}

// NOTE: 旧字段定义不再适配后端，已保留以便追溯。
// export interface WmsReceivingRecordBlindCreateReq {
//   receivingNo?: string
//   warehouseId: number
//   clientId: number
//   supplierShipper: string
//   receivedAt: string
//   remark?: string
//   details: WmsReceivingRecordDetailCreateReq[]
// }

// 新盲收创建请求，字段与后端 DTO `WmsBlindReceivingCreateReq` 完全对应
export interface WmsReceivingRecordBlindCreateReq {
  actualArrivalDate?: string // YYYY-MM-DD
  supplementDeadline?: string // YYYY-MM-DD
  clientId: number
  warehouseId: number
  supplierShipper?: string
  sourceDocNo?: string
  remark?: string
  details?: WmsReceivingRecordDetailCreateReq[]
}

// 从入库通知单创建收货记录的请求
export interface WmsReceivingRecordCreateFromASNReq {
	notificationId: number
	defaultReceivingLocationId: number
	remark?: string
}


// 收货记录更新请求
export interface WmsReceivingRecordUpdateReq {
  notificationId?: number
  actualArrivalDate?: string
  supplementDeadline?: string
  clientId?: number
  warehouseId?: number
  supplierShipper?: string
  sourceDocNo?: string
  remark?: string
  details?: WmsReceivingRecordDetailUpdateReq[]
}

// 收货记录查询请求
export interface WmsReceivingRecordQueryReq {
  pageNum?: number
  pageSize?: number
  receivingNo?: string
  notificationNo?: string
  status?: string
  inspectionStatus?: string
  clientId?: number
  warehouseId?: number
  receivedAtFrom?: string
  receivedAtTo?: string
  sortField?: string
  sortOrder?: string
}

// 收货记录明细响应
export interface WmsReceivingRecordDetailResp {
  id: number
  receivingRecordId: number
  notificationDetailId?: number
  lineNo: number
  itemId: number
  itemSku: string
  itemName: string
  specification?: string
  expectedQuantity: number
  receivedQuantity: number
  unitOfMeasure: string
  batchNo?: string
  productionDate?: string
  expiryDate?: string
  quantityDifference?: number
  discrepancyReason?: string
  status: WmsReceivingRecordStatus
  remark?: string
  locationCode?: string
  notificationQty?: number
  packageUnit?: string      // ← 新增
  packageQty?: number       // ← 如表格也用到包装数量，一并补充
  internalBatchNo?: string
}

// 收货记录响应
export interface WmsReceivingRecordResp {
  id: number
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  tenantId: number
  accountBookId: number
  receivingNo: string
  notificationId?: number
  notificationNo?: string
  warehouseId: number
  warehouseName: string
  clientId: number
  clientName: string
  supplierName?: string
  supplierShipper?: string
  actualArrivalDate?: string
  supplementDeadline?: string
  blindReceivingReason?: string
  sourceDocNo?: string
  status: WmsReceivingRecordStatus
  isBlindReceiving: boolean
  remark?: string
  details: WmsReceivingRecordDetailResp[]
}

// 收货记录简要响应（用于列表）
export interface WmsReceivingRecordSimpleResp {
  id: number
  receivingNo: string
  notificationId?: number
  notificationNo?: string
  clientId: number
  clientName: string
  warehouseId: number
  warehouseName: string
  supplierName?: string
  supplierShipper?: string
  actualArrivalDate?: string
  status: WmsReceivingRecordStatus
  createdAt: string
  isBlindReceiving: boolean
}

// 更新状态请求
export interface WmsReceivingRecordUpdateStatusReq {
  status: WmsReceivingRecordStatus
  remark?: string
}

// ==================== API接口函数 ====================

export const getReceivingRecordPage = (params: WmsReceivingRecordQueryReq): Promise<PageResult<WmsReceivingRecordSimpleResp>> => {
  return request<PageResult<WmsReceivingRecordSimpleResp>>({
    url: '/wms/receiving-records',
    method: 'get',
    params
  }) as unknown as Promise<PageResult<WmsReceivingRecordSimpleResp>>
}

export const getReceivingRecordDetail = (id: number): Promise<WmsReceivingRecordResp> => {
  return request<WmsReceivingRecordResp>({
    url: `/wms/receiving-records/${id}`,
    method: 'get'
  }) as unknown as Promise<WmsReceivingRecordResp>
}

export const createReceivingRecord = (data: WmsReceivingRecordCreateReq): Promise<WmsReceivingRecordResp> => {
  return request<WmsReceivingRecordResp>({
    url: '/wms/receiving-records',
    method: 'post',
    data
  }) as unknown as Promise<WmsReceivingRecordResp>
}

export const createBlindReceivingRecord = (data: WmsReceivingRecordBlindCreateReq): Promise<WmsReceivingRecordResp> => {
  return request<WmsReceivingRecordResp>({
    url: '/wms/receiving-records/blind-receiving',
    method: 'post',
    data
  }) as unknown as Promise<WmsReceivingRecordResp>
}

export const createReceivingRecordFromAsn = (data: WmsReceivingRecordCreateFromASNReq): Promise<WmsReceivingRecordResp> => {
	return request<WmsReceivingRecordResp>({
		url: `/wms/receiving-records/from-notification/${data.notificationId}`,
		method: 'post',
		data
	}) as unknown as Promise<WmsReceivingRecordResp>
}

export const updateReceivingRecord = (id: number, data: WmsReceivingRecordUpdateReq): Promise<WmsReceivingRecordResp> => {
  return request<WmsReceivingRecordResp>({
    url: `/wms/receiving-records/${id}`,
    method: 'put',
    data
  }) as unknown as Promise<WmsReceivingRecordResp>
}

export const deleteReceivingRecord = (id: number): Promise<void> => {
  return request<void>({
    url: `/wms/receiving-records/${id}`,
    method: 'delete'
  }) as unknown as Promise<void>
}

export const updateReceivingRecordStatus = (id: number, data: WmsReceivingRecordUpdateStatusReq): Promise<void> => {
  return request<void>({
    url: `/wms/receiving-records/${id}/status`,
    method: 'put',
    data
  }) as unknown as Promise<void>
} 