package vo

// ImportResultVO 导入结果视图对象
type ImportResultVO struct {
	TotalCount   int      `json:"totalCount"`   // 总记录数
	SuccessCount int      `json:"successCount"` // 成功导入数
	FailureCount int      `json:"failureCount"` // 失败数
	Errors       []string `json:"errors"`       // 错误信息列表
	Message      string   `json:"message"`      // 结果消息
}

// NewImportResultVO 创建导入结果VO
func NewImportResultVO(totalCount, successCount, failureCount int, errors []string) *ImportResultVO {
	message := "导入完成"
	if failureCount > 0 {
		message = "导入完成，但有部分记录失败"
	}
	
	return &ImportResultVO{
		TotalCount:   totalCount,
		SuccessCount: successCount,
		FailureCount: failureCount,
		Errors:       errors,
		Message:      message,
	}
}

// IsSuccess 是否全部成功
func (vo *ImportResultVO) IsSuccess() bool {
	return vo.FailureCount == 0
}

// HasErrors 是否有错误
func (vo *ImportResultVO) HasErrors() bool {
	return len(vo.Errors) > 0
}
