# Go 企业级后端应用模板

## 1. 项目简介

本项目是一个使用 Go 语言构建的企业级后端应用程序模板。它基于经典的分层架构，整合了常用的技术栈和最佳实践，旨在提供一个高可用、可扩展、易维护的基础框架，帮助开发者快速启动新的后端项目。

**核心特点:**

* **清晰的分层架构**: Controller -> Service -> Repository，职责分明。
* **依赖注入**: 通过管理器 (Manager) 模式实现服务和仓库的依赖注入。
* **配置驱动**: 使用 Viper 和 YAML 文件进行灵活的配置管理。
* **结构化日志**: 集成 Zap 实现高性能、结构化的日志记录。
* **统一错误处理**: 自定义错误码和错误类型，方便前后端交互和问题排查。
* **标准化 API 响应**: 封装统一的成功和失败响应结构。
* **数据库交互**: 使用 GORM 作为 ORM，提供基础的 CRUD 仓库实现和事务管理。
* **认证与授权**: 基于 JWT (Access Token + Refresh Token) 实现用户认证，并预留了 RBAC (基于角色的访问控制) 的扩展点。
* **缓存支持**: 集成 Redis 和内存缓存 (go-cache) 选项。
* **模型分离**: 使用 `DTO` 处理输入，`Entity` 映射数据库，`VO` 构造输出，保持各层职责单一。
* **中间件支持**: 提供常用的中间件，如日志、恢复、CORS、认证、IP 获取等。
* **数据库自动迁移与种子数据**: 支持通过配置自动完成数据库结构迁移和初始数据填充。

## 2. 技术栈

* **Web 框架**: [Iris v12](https://github.com/kataras/iris)
* **数据库 ORM**: [GORM](https://gorm.io/)
* **数据库驱动**: (根据配置选择) `gorm.io/driver/mysql`, `gorm.io/driver/postgres`
* **缓存**: [go-redis/redis](https://github.com/go-redis/redis) (推荐), [patrickmn/go-cache](https://github.com/patrickmn/go-cache) (内存)
* **配置管理**: [spf13/viper](https://github.com/spf13/viper)
* **日志**: [uber-go/zap](https://github.com/uber-go/zap)
* **JWT**: [golang-jwt/jwt](https://github.com/golang-jwt/jwt)
* **密码哈希**: [golang.org/x/crypto/bcrypt](https://pkg.go.dev/golang.org/x/crypto/bcrypt)
* **数据校验**: [go-playground/validator](https://github.com/go-playground/validator)
* **验证码**: [mojocn/base64Captcha](https://github.com/mojocn/base64Captcha)

## 3. 项目结构

```bash
.
├── cmd/                      # 程序入口目录
│   └── main.go               # Web 服务器主程序入口
├── configs/                  # 配置文件目录
│   └── config.yaml           # 主配置文件
├── internal/                 # 内部应用程序代码 (核心业务逻辑)
│   ├── controller/           # 控制器层 (处理HTTP请求)
│   ├── middleware/           # 中间件
│   ├── model/                # 数据模型 (dto, entity, vo)
│   │   ├── dto/              # Data Transfer Objects (请求数据校验)
│   │   ├── entity/           # 数据库实体 (表结构映射)
│   │   └── vo/               # View Objects (响应数据构造)
│   ├── repository/           # 数据仓库层 (数据库操作)
│   ├── router/               # API 路由定义
│   └── service/              # 服务层 (业务逻辑封装)
├── pkg/                      # 可共享的通用库代码
│   ├── audit/                # 操作审计日志
│   ├── cache/                # 缓存实现 (Redis, Memory)
│   ├── config/               # 配置加载与管理
│   ├── constant/             # 全局常量定义
│   ├── database/             # 数据库交互 (连接, 迁移, 事务)
│   ├── errors/               # 自定义错误处理
│   ├── logger/               # 日志库封装
│   ├── response/             # HTTP 响应封装
│   ├── security/             # 安全相关功能 (密码, CSRF/XSS 示例)
│   ├── storage/              # 文件存储 (本地, S3 示例)
│   ├── util/                 # 通用工具函数 (JWT, 文件, IP, Excel 等)
│   └── validator/            # 数据校验器
├── go.mod                    # Go 模块依赖定义
├── go.sum                    # Go 模块校验和
├── README.md                 # 项目说明文档 (本文档)
└── (其他编译输出或配置文件)
```

**各主要目录作用:**

* **`cmd/main.go`**: 应用程序的启动入口，负责初始化配置、日志、数据库连接、路由注册、启动 Web 服务器等。
* **`configs/`**: 存放应用程序的配置文件，通常使用 YAML 格式。`config.yaml` 定义了应用、数据库、缓存、JWT 等各项参数。
* **`internal/`**: 项目的核心代码，遵循经典的 MVC/分层架构思想。
    * `controller`: 接收 HTTP 请求，对请求参数进行初步校验，调用 `service` 处理业务逻辑，并将结果通过 `vo` 返回给客户端。
    * `service`: 实现具体的业务逻辑，可能调用一个或多个 `repository` 来操作数据，处理事务。
    * `repository`: 数据访问层，负责与数据库进行交互，执行 CRUD 操作，通常与 `entity` 配合使用。
    * `model`: 存放数据结构定义。
        * `dto`: 用于接收和校验来自客户端的请求数据。
        * `entity`: 定义与数据库表结构一一对应的 Go 结构体。
        * `vo`: 定义返回给客户端的视图对象，可能对 `entity` 进行裁剪或组合。
    * `middleware`: 实现各种 HTTP 中间件，用于处理请求管道中的通用逻辑，如认证、日志、跨域、错误恢复等。
    * `router`: 定义应用程序的 API 路由规则，将 URL 路径映射到具体的 `controller` 方法。
* **`pkg/`**: 存放项目通用的、可重用的功能包。这些包理论上可以被其他项目复用。
    * `database`: 封装了数据库连接（MySQL, PostgreSQL）、GORM 初始化、自动迁移、事务管理等功能。
    * `config`: 提供了加载和访问配置文件的能力。
    * `logger`: 封装了 Zap 日志库，提供结构化日志记录接口。
    * `errors`: 定义了自定义的错误类型和错误码，便于统一错误处理和前后端沟通。
    * `response`: 提供了标准化的 API 响应结构和辅助函数。
    * `util`: 包含各种通用工具函数，如密码加密/校验、JWT 生成/解析、IP 地址获取、文件操作等。
    * 其他包如 `cache`, `security`, `storage`, `validator`, `constant`, `audit` 提供相应领域的通用功能。

## 4. 环境准备

* **Go**: 版本 >= 1.18 (建议使用最新稳定版)
* **数据库**:
    *   MySQL (版本 >= 5.7) 或 PostgreSQL (版本 >= 10)
    *   需要提前创建好数据库实例。
* **缓存 (可选)**:
    *   Redis 服务器 (如果配置使用 Redis 缓存)
* **依赖管理**: Go Modules

## 5. 配置

应用程序的配置主要通过 `configs/config.yaml` 文件管理。请根据你的实际环境修改此文件中的配置项，特别是：

* **`database`**: 配置数据库类型 (`mysql` 或 `postgresql`)、连接地址、端口、用户名、密码、数据库名等。
    *   `auto_init`: 是否在启动时自动执行数据库迁移和填充种子数据 (首次运行或需要重置时设为 `true`)。
* **`redis`**: 配置 Redis 服务器地址、端口、密码、数据库编号。
* **`jwt`**: 配置 JWT 签名密钥 (`secret`)、令牌过期时间 (`expire_time`)、刷新令牌过期时间 (`refresh_expire_time`) 等。
* **`logger`**: 配置日志级别、输出格式、文件路径等。
* **`app`**: 配置应用名称、运行端口、运行模式 (`debug`, `release`) 等。
* **`cache`**: 配置默认缓存类型 (`redis` 或 `memory`)、过期时间等。
* **`captcha`**: 配置验证码参数。

**环境变量覆盖:** (如果实现)

某些敏感配置（如数据库密码、JWT 密钥）可以通过环境变量进行覆盖，以提高安全性。请查阅 `pkg/config/config.go` 的具体实现以了解支持哪些环境变量。

## 6. 运行项目

1. **克隆项目**: `git clone <repository_url>`
2. **进入项目目录**: `cd <project_directory>`
3. **配置**: 根据第 5 节修改 `configs/config.yaml` 文件。
4. **安装依赖**: `go mod tidy`
5. **数据库准备**:
    * 确保数据库服务已启动。
    * 确保 `configs/config.yaml` 中配置的数据库名对应的数据库实例已创建。
    * 如果 `database.auto_init` 设置为 `true`，程序启动时会自动尝试迁移和填充种子数据。
    * 如果 `database.auto_init` 设置为 `false`，你需要手动执行数据库迁移（例如使用数据库管理工具或单独的迁移脚本）。
6. **运行**:
    * 开发模式: `go run cmd/main.go`
    * 编译运行:

        ```bash
        go build -o myapp cmd/main.go
        ./myapp
        ```

7. 服务启动后，默认监听在 `configs/config.yaml` 中 `app.port` 配置的端口（例如 `8080`）。

## 7. API 文档

* **Swagger**: 本项目可能集成了 Swagger UI 来提供交互式的 API 文档。如果集成了：
    *   启动服务后，访问 `http://localhost:<port>/swagger/index.html` (具体路径可能不同，请查看 `internal/router/` 中的配置)。
    *   API 定义通常通过代码注释 (`swag init` 命令生成) 或单独的 `swagger.json`/`swagger.yaml` 文件维护。
* **其他文档**: 如果使用其他 API 文档工具 (如 Postman 集合、Markdown 文档等)，请在此说明访问方式。

## 8. 数据库迁移与种子数据

* **自动执行**: 当 `configs/config.yaml` 中 `database.auto_init` 设置为 `true` 时，应用程序在启动时会自动执行：
    *   **数据库迁移 (`runMigrations`)**: 使用 GORM 的 `AutoMigrate` 功能，根据 `internal/model/entity/` 下定义的实体结构，自动创建或更新数据库表结构（仅添加、不修改、不删除）。
    *   **种子数据填充 (`seedData`)**: 向数据库中插入初始数据，例如默认的管理员账户、角色、菜单等。此过程通常只在数据库为空或特定数据不存在时执行，以避免重复插入。
* **手动执行**: 如果 `auto_init` 为 `false`，你需要：
    *   **迁移**: 手动应用数据库结构变更，可以使用数据库管理工具，或者如果项目提供了单独的迁移工具/脚本，请使用它们。
    *   **种子数据**: 手动执行 SQL 脚本或运行特定的种子数据填充程序（如果提供）。

**重要**: `seedData` 函数中的默认管理员密码 (`defaultAdminPassword`) 应在生产环境中通过配置或环境变量设置，避免硬编码。

## 9. 主要功能模块 (`internal/`)

本项目包含以下核心业务模块（基于 `internal/` 目录结构推断）：

* **认证 (`auth_controller`, `auth_service`, `token_service`)**: 处理用户登录、JWT 令牌生成与刷新、验证码生成与校验。
* **用户管理 (`user_controller`, `user_service`, `user_repository`)**: 实现用户的增删改查、状态管理、密码修改/重置、个人信息管理、用户角色分配等。
* **角色管理 (`role_controller`, `role_service`, `role_repository`)**: 实现角色的增删改查、状态管理、角色菜单权限分配等。
* **菜单管理 (`menu_controller`, `menu_service`, `menu_repository`)**: 实现菜单（目录、菜单项、按钮）的增删改查、状态管理、获取菜单树、获取用户菜单/权限等。
* **帐套管理 (`accountbook_controller` 等)**: (推测) 管理财务或业务帐套。
* **组织架构/人事管理 (`organization_controller`, `employee_controller` 等)**: (推测) 管理部门、员工、职位等信息。
* **(其他模块)**: 根据 `internal/` 下的其他 `controller`, `service`, `repository` 添加说明。

## 10. 通用功能包 (`pkg/`)

`pkg/` 目录提供了项目范围内的可重用功能：

* **`database`**: 数据库连接、GORM 配置、自动迁移、事务管理。
* **`logger`**: 基于 Zap 的结构化日志记录器。
* **`config`**: 使用 Viper 加载 YAML 配置文件。
* **`errors`**: 定义业务错误码和自定义错误类型。
* **`response`**: 标准化 API 响应格式。
* **`util`**: 包含密码哈希、JWT 处理、IP 获取、文件操作等实用函数。
* **`cache`**: 提供 Redis 和内存缓存的接口和实现。
* **`security`**: (可能包含) 安全相关的辅助功能。
* **`storage`**: (可能包含) 文件上传/存储的抽象。
* **`validator`**: 用于 DTO 的数据校验。
* **`constant`**: 定义项目中使用的常量。
* **`audit`**: 用于记录用户操作的审计日志。

## 11. 贡献指南 (可选)

1. Fork 本仓库。
2. 创建新的功能分支 (`git checkout -b feature/your-feature-name`)。
3. 提交你的代码 (`git commit -m 'Add some feature'`)。
    * 请确保代码风格一致 (运行 `go fmt` 和 `go vet`)。
    * 为新功能添加必要的测试。
4. 将你的分支推送到你的 Fork (`git push origin feature/your-feature-name`)。
5. 创建一个 Pull Request。

## 12. 许可证 (可选)

本项目采用 [MIT 许可证](LICENSE)。

## 开发工具与规范

### Markdown 规范 (markdownlint)

本项目推荐使用 [markdownlint](https://marketplace.visualstudio.com/items?itemName=DavidAnson.vscode-markdownlint) (VS Code 扩展) 来确保项目内 Markdown 文件（如本文档）的格式一致性和规范性。

* **配置**: 遵循 markdownlint 的默认规则（可在 VS Code 设置或项目根目录的 `.markdownlint.{jsonc,json,yaml,yml,cjs}` 或 `.markdownlint-cli2.{jsonc,yaml,cjs}` 文件中自定义）。默认情况下，除行长度限制 (MD013) 外，所有规则均启用。
* **使用**: 建议开发者安装此 VS Code 扩展。可以通过编辑器的"格式化文档"功能、代码操作 (`Ctrl+.`) 的快速修复，或配置 `editor.formatOnSave`/`editor.codeActionsOnSave` 来自动修复大部分样式问题。

### Go 代码规范

* 遵循 Go 语言的官方编码规范。
* 提交代码前运行 `go fmt` 和 `go vet` 检查格式和潜在问题。
