# Frontend Project (Vue 3 + TypeScript + Vite + Element Plus)

这是一个与后端项目配套的前端应用程序，使用 Vue 3、TypeScript、Vite 和 Element Plus 构建。

## 主要技术栈

* **框架**: Vue 3 (Composition API)
* **语言**: TypeScript
* **构建工具**: Vite
* **UI 库**: Element Plus
* **状态管理**: Pinia (根据 `src/store/user.ts` 推断)
* **路由**: Vue Router
* **HTTP 客户端**: Axios (通常与 Vue 项目集成，需要确认是否已安装或将被使用)

## 项目结构

```bash
frontend/
├── public/                   # 静态资源目录 (不会被 Vite 处理)
│   └── ...                   # 例如 favicon.ico, robots.txt 等
├── src/                      # 主要源代码目录
│   ├── api/                  # API 请求模块 (用于封装后端接口调用)
│   ├── assets/               # 静态资源目录 (会被 Vite 处理)
│   │   └── css/              # CSS 文件
│   │   └── vue.svg           # Vue Logo
│   ├── components/           # 可复用 UI 组件
│   │   ├── VNLogin/            # 登录组件封装
│   │   │   ├── index.vue
│   │   │   ├── example.vue
│   │   │   ├── types.ts
│   │   │   └── README.md
│   │   ├── VNFrame/            # 主框架布局组件
│   │   │   └── ...
│   │   ├── VNTabs/             # Tab 标签页组件
│   │   │   └── ...
│   │   ├── VNTree/             # 树形组件
│   │   │   └── ...
│   │   ├── VNDrawer/           # 抽屉组件
│   │   │   └── ...
│   │   ├── VNDialog/           # 对话框组件
│   │   │   └── ...
│   │   ├── VNChart/            # 图表组件 (可能基于 ECharts 等)
│   │   │   └── ...
│   │   ├── EPExamples/         # Element Plus 示例组件
│   │   │   └── ...
│   │   ├── VNStatusBar/        # 状态栏组件
│   │   │   └── ...
│   │   ├── VNSidebar/          # 侧边栏导航组件
│   │   │   └── ...
│   │   ├── VNBreadcrumb/       # 面包屑导航组件
│   │   │   └── ...
│   │   ├── VNForm/             # 表单组件封装
│   │   │   └── ...
│   │   ├── VNTable/            # 表格组件封装
│   │   │   └── ...
│   │   ├── exampleNav.vue      # 组件示例导航
│   │   ├── exampleRoutes.ts    # 组件示例路由
│   │   └── README.md           # 组件库说明
│   ├── layouts/              # 页面布局组件 (组合 components 中的组件)
│   │   └── ...                 # 例如 DefaultLayout, AuthLayout 等
│   ├── router/               # 路由配置
│   │   └── index.ts          # Vue Router 配置和路由定义
│   ├── store/                # 状态管理 (Pinia)
│   │   └── user.ts           # 用户状态模块 (用户信息、Token 管理)
│   ├── styles/               # 全局样式或主题文件
│   │   └── ...
│   ├── utils/                # 通用工具函数
│   │   └── ...                 # 例如 request.ts (封装axios), helpers.ts 等
│   ├── views/                # 页面视图组件
│   │   └── system/           # 系统相关视图
│   │       └── login/          # 登录页面
│   │           └── Index.vue
│   ├── App.vue               # 根组件
│   ├── main.ts               # 应用入口文件 (初始化 Vue, Router, Store 等)
│   ├── shims-vue.d.ts        # Vue 单文件组件的 TypeScript 类型声明
│   ├── style.css             # 全局 CSS 样式
│   └── vite-env.d.ts       # Vite 环境变量的 TypeScript 类型声明
├── .gitignore                # Git 忽略配置
├── .vscode/                  # VS Code 编辑器配置 (可选)
├── index.html                # HTML 入口文件 (Vite 会注入打包后的资源)
├── package.json              # 项目依赖和脚本配置
├── package-lock.json         # 依赖版本锁定
├── README.md                 # 项目说明文档 (本文档)
├── tsconfig.app.json         # 应用相关的 TypeScript 配置 (供 Vite 使用)
├── tsconfig.json             # 基础 TypeScript 配置
├── tsconfig.node.json        # Node.js 环境的 TypeScript 配置 (例如 Vite 配置文件)
└── vite.config.ts            # Vite 构建和开发服务器配置

```

## 快速开始

1. **环境**: 确保已安装 Node.js (推荐 LTS 版本) 和 npm 或 yarn/pnpm。
2. **依赖**: 在 `frontend` 目录下运行 `npm install` (或 `yarn install` / `pnpm install`) 安装项目依赖。
3. **开发**: 运行 `npm run dev` (或 `yarn dev` / `pnpm dev`) 启动开发服务器，通常在 `http://localhost:5173` (端口可能变化)。
4. **构建**: 运行 `npm run build` (或 `yarn build` / `pnpm build`) 打包生产环境代码，输出到 `dist` 目录。
5. **预览**: 运行 `npm run preview` (或 `yarn preview` / `pnpm preview`) 在本地预览生产构建包。

## 注意事项

* **API 代理**: `vite.config.ts` 中可能配置了 API 代理 (`server.proxy`)，用于在开发环境中将前端的 API 请求转发到后端服务，解决跨域问题。请确保代理配置与后端服务的实际地址和端口匹配。
* **环境变量**: 可能使用 `.env` 文件 (如 `.env.development`, `.env.production`) 来管理不同环境下的配置 (例如 API 基础路径)。
* **代码风格**: 推荐使用 ESLint 和 Prettier 来统一代码风格和进行静态检查。
* **状态管理**: 使用 Pinia 进行状态管理，`src/store` 目录下按模块组织状态。
* **组件库**: 大量使用了自定义封装的 `VN` 系列组件，位于 `src/components` 目录下，使用前建议阅读对应组件的 README 或示例。
