package entity

import (
	"time"

	"gorm.io/datatypes"
)

// AuditLog 对应数据库中的 system_audit_log 表
type AuditLog struct {
	ID           uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID       uint64         `json:"userId"`                             // 操作用户ID，对于系统事件可能为0或特定系统用户ID
	Username     string         `gorm:"size:100;index" json:"username"`     // 操作用户名
	Action       string         `gorm:"size:100;index" json:"action"`       // 例如: login, create_user, update_role, delete_item
	ResourceType string         `gorm:"size:100;index" json:"resourceType"` // 例如: user, role, order, system_config
	ResourceID   string         `gorm:"size:255;index" json:"resourceId"`   // 被操作的资源ID，可以是数字或字符串
	Timestamp    time.Time      `gorm:"index" json:"timestamp"`             // 事件发生时间
	ClientIP     string         `gorm:"size:100" json:"clientIp"`           // 客户端IP地址
	UserAgent    string         `gorm:"size:512" json:"userAgent"`          // 客户端User-Agent
	RequestURI   string         `gorm:"size:1024" json:"requestUri"`        // 请求的URI
	Method       string         `gorm:"size:10" json:"method"`              // HTTP方法 (GET, POST, PUT, DELETE)
	StatusCode   int            `json:"statusCode"`                         // HTTP响应状态码
	DurationMs   int64          `json:"durationMs"`                         // 请求处理时长 (毫秒)
	TraceID      string         `gorm:"size:100;index" json:"traceId"`      // 追踪ID，关联日志
	Status       string         `gorm:"size:50" json:"status"`              // 操作结果状态 (e.g., success, failure)
	OldValue     datatypes.JSON `json:"oldValue,omitempty"`                 // 操作前的数据快照 (JSON格式)
	NewValue     datatypes.JSON `json:"newValue,omitempty"`                 // 操作后的数据快照 (JSON格式)
	Details      string         `gorm:"type:text" json:"details,omitempty"` // 其他详细信息或备注 (例如错误信息)
	CreatedAt    time.Time      `json:"createdAt"`                          // 记录创建时间 (GORM自动管理)
}

// TableName 指定 AuditLog 模型对应的数据库表名
func (AuditLog) TableName() string {
	return "sys_audit"
}
