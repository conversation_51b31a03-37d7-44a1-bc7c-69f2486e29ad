<template>
  <div class="shipment-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>发运单管理</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>仓库管理</el-breadcrumb-item>
          <el-breadcrumb-item>出库管理</el-breadcrumb-item>
          <el-breadcrumb-item>发运单</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          @click="handleCreate"
          v-if="checkPermission('shipment:create')"
        >
          <el-icon><Plus /></el-icon>
          新建发运单
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="4" v-for="(stat, key) in statusStats" :key="key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stat }}</div>
              <div class="stat-label">{{ getStatusLabel(key) }}</div>
            </div>
            <div class="stat-icon" :class="`stat-icon-${key.toLowerCase()}`">
              <el-icon><Van /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="main-content">
      <VNTable
        ref="vnTableRef"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :pagination="pagination"
        :toolbar-config="toolbarConfig"
        :show-operations="true"
        :operation-width="200"
        operation-fixed="right"
        row-key="id"
        :selection-type="'multiple'"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @selection-change="handleSelectionChange"
        @filter-change="handleFilterChange"
        @refresh="handleRefresh"
      >
        <!-- 自定义列插槽 -->
        <template #column-shipmentNo="{ row }">
          <el-link
            type="primary"
            @click="handleView(row)"
            :underline="false"
          >
            {{ row.shipmentNo }}
          </el-link>
        </template>

        <template #column-status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ formatStatus(row.status) }}
          </el-tag>
        </template>

        <template #column-shippingCost="{ row }">
          <span v-if="row.shippingCost">
            ¥{{ row.shippingCost.toFixed(2) }}
          </span>
          <span v-else class="text-muted">-</span>
        </template>

        <template #column-trackingNo="{ row }">
          <el-link
            v-if="row.trackingNo"
            type="primary"
            @click="handleTrackingInfo(row)"
            :underline="false"
          >
            {{ row.trackingNo }}
          </el-link>
          <span v-else class="text-muted">-</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="handleEdit(row)"
            v-if="canEdit(row) && checkPermission('shipment:edit')"
          >
            编辑
          </el-button>
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, row)"
            v-if="getDropdownItems(row).length > 0"
          >
            <el-button size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in getDropdownItems(row)"
                  :key="item.command"
                  :command="item.command"
                  :disabled="item.disabled"
                >
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </VNTable>
    </el-card>

    <!-- 表单对话框 -->
    <ShipmentForm
      ref="formDialogRef"
      @confirm="handleFormConfirm"
      @cancel="handleFormCancel"
    />

    <!-- 打包对话框 -->
    <PackingDialog
      ref="packingDialogRef"
      @confirm="handlePackingConfirm"
      @cancel="handlePackingCancel"
    />

    <!-- 发运对话框 -->
    <ShippingDialog
      ref="shippingDialogRef"
      @confirm="handleShippingConfirm"
      @cancel="handleShippingCancel"
    />

    <!-- 跟踪信息对话框 -->
    <TrackingDialog
      ref="trackingDialogRef"
    />

    <!-- 签收确认对话框 -->
    <DeliveryConfirmDialog
      ref="deliveryConfirmDialogRef"
      @confirm="handleDeliveryConfirm"
      @cancel="handleDeliveryCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElCard, ElRow, ElCol, ElButton, ElIcon, ElLink, ElTag, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessage, ElMessageBox, ElBreadcrumb, ElBreadcrumbItem } from 'element-plus'
import { Plus, Van, ArrowDown } from '@element-plus/icons-vue'
import VNTable from '@/components/VNTable/index.vue'
import ShipmentForm from './components/ShipmentForm.vue'
import PackingDialog from './components/PackingDialog.vue'
import ShippingDialog from './components/ShippingDialog.vue'
import TrackingDialog from './components/TrackingDialog.vue'
import DeliveryConfirmDialog from './components/DeliveryConfirmDialog.vue'
import type { TableColumn, PaginationConfig, ToolbarConfig } from '@/components/VNTable/types'
import type { WmsShipmentResp, WmsShipmentStatus } from '@/types/wms/shipment'
import { useShipmentStore } from '@/store/wms/shipment'

// Store
const shipmentStore = useShipmentStore()

// 响应式数据
const vnTableRef = ref<InstanceType<typeof VNTable>>()
const formDialogRef = ref<InstanceType<typeof ShipmentForm>>()
const packingDialogRef = ref<InstanceType<typeof PackingDialog>>()
const shippingDialogRef = ref<InstanceType<typeof ShippingDialog>>()
const trackingDialogRef = ref<InstanceType<typeof TrackingDialog>>()
const deliveryConfirmDialogRef = ref<InstanceType<typeof DeliveryConfirmDialog>>()

const selectedRows = ref<WmsShipmentResp[]>([])

// 计算属性
const list = computed(() => shipmentStore.list)
const total = computed(() => shipmentStore.total)
const loading = computed(() => shipmentStore.loading)
const statusStats = computed(() => shipmentStore.statusStats)

// 分页配置
const pagination = reactive<PaginationConfig>({
  total: total,
  currentPage: computed(() => shipmentStore.pagination.pageNum),
  pageSize: computed(() => shipmentStore.pagination.pageSize)
})

// 表格列配置
const tableColumns = computed<TableColumn[]>(() => [
  { prop: 'shipmentNo', label: '发运单号', minWidth: 140, slot: true },
  { prop: 'outboundNotificationNo', label: '出库单号', minWidth: 140 },
  { prop: 'carrierName', label: '承运商', width: 120 },
  { prop: 'shippingMethod', label: '运输方式', width: 100 },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'shippingCost', label: '运费', width: 100, slot: true },
  { prop: 'trackingNo', label: '运单号', width: 140, slot: true },
  { prop: 'consigneeName', label: '收货人', width: 100 },
  { prop: 'consigneePhone', label: '联系电话', width: 120 },
  { prop: 'estimatedDeliveryDate', label: '预计送达', width: 120 },
  { prop: 'createdAt', label: '创建时间', width: 150 }
])

// 工具栏配置
const toolbarConfig = computed<ToolbarConfig>(() => ({
  showRefresh: true,
  showFilter: true,
  showExport: checkPermission('shipment:export'),
  batchOperations: [
    {
      label: '批量打包',
      value: 'batchPack',
      type: 'primary',
      permission: 'shipment:pack'
    },
    {
      label: '批量发运',
      value: 'batchShip',
      type: 'success',
      permission: 'shipment:ship'
    },
    {
      label: '批量取消',
      value: 'batchCancel',
      type: 'danger',
      permission: 'shipment:cancel'
    }
  ],
  filterFields: [
    {
      prop: 'shipmentNo',
      label: '发运单号',
      type: 'input',
      placeholder: '请输入发运单号'
    },
    {
      prop: 'outboundNotificationNo',
      label: '出库单号',
      type: 'input',
      placeholder: '请输入出库单号'
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '准备中', value: 'PREPARING' },
        { label: '已打包', value: 'PACKED' },
        { label: '待发运', value: 'READY_TO_SHIP' },
        { label: '已发运', value: 'SHIPPED' },
        { label: '运输中', value: 'IN_TRANSIT' },
        { label: '已送达', value: 'DELIVERED' },
        { label: '已取消', value: 'CANCELLED' }
      ]
    },
    {
      prop: 'carrierId',
      label: '承运商',
      type: 'select',
      placeholder: '请选择承运商',
      options: [] // 这里应该从Store获取承运商列表
    },
    {
      prop: 'dateRange',
      label: '创建时间',
      type: 'daterange',
      placeholder: '请选择时间范围'
    }
  ]
}))

// 方法
const loadData = async () => {
  try {
    await shipmentStore.fetchList()
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  }
}

const handlePageChange = (page: number) => {
  shipmentStore.searchForm.pageNum = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  shipmentStore.searchForm.pageSize = size
  shipmentStore.searchForm.pageNum = 1
  loadData()
}

const handleSelectionChange = (rows: WmsShipmentResp[]) => {
  selectedRows.value = rows
}

const handleFilterChange = (filters: Record<string, any>) => {
  // 处理日期范围
  if (filters.dateRange && filters.dateRange.length === 2) {
    filters.createdAtStart = filters.dateRange[0]
    filters.createdAtEnd = filters.dateRange[1]
    delete filters.dateRange
  }
  
  Object.assign(shipmentStore.searchForm, filters)
  shipmentStore.searchForm.pageNum = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const handleCreate = () => {
  formDialogRef.value?.open('create')
}

const handleView = (row: WmsShipmentResp) => {
  formDialogRef.value?.open('view', row.id)
}

const handleEdit = (row: WmsShipmentResp) => {
  formDialogRef.value?.open('edit', row.id)
}

const handleFormConfirm = () => {
  loadData()
}

const handleFormCancel = () => {
  // 表单取消处理
}

// 权限检查
const checkPermission = (permission: string): boolean => {
  // 这里应该实现实际的权限检查逻辑
  return true // 临时返回true
}

// 工具方法
const canEdit = (row: WmsShipmentResp): boolean => {
  return ['PREPARING', 'PACKED'].includes(row.status)
}

const getStatusTagType = (status: WmsShipmentStatus) => {
  const typeMap = {
    PREPARING: 'info',
    PACKED: 'warning',
    READY_TO_SHIP: 'primary',
    SHIPPED: 'success',
    IN_TRANSIT: 'primary',
    DELIVERED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: WmsShipmentStatus) => {
  const labelMap = {
    PREPARING: '准备中',
    PACKED: '已打包',
    READY_TO_SHIP: '待发运',
    SHIPPED: '已发运',
    IN_TRANSIT: '运输中',
    DELIVERED: '已送达',
    CANCELLED: '已取消'
  }
  return labelMap[status] || '未知'
}

const getStatusLabel = (status: string) => {
  return formatStatus(status as WmsShipmentStatus)
}

const getDropdownItems = (row: WmsShipmentResp) => {
  const items = []
  
  if (row.status === 'PREPARING' && checkPermission('shipment:pack')) {
    items.push({ command: 'pack', label: '打包', disabled: false })
  }
  
  if (row.status === 'PACKED' && checkPermission('shipment:ship')) {
    items.push({ command: 'ship', label: '发运', disabled: false })
  }
  
  if (['SHIPPED', 'IN_TRANSIT'].includes(row.status) && checkPermission('shipment:track')) {
    items.push({ command: 'track', label: '跟踪信息', disabled: false })
  }
  
  if (row.status === 'IN_TRANSIT' && checkPermission('shipment:deliver')) {
    items.push({ command: 'deliver', label: '确认签收', disabled: false })
  }
  
  if (row.status !== 'DELIVERED' && row.status !== 'CANCELLED' && checkPermission('shipment:cancel')) {
    items.push({ command: 'cancel', label: '取消', disabled: false })
  }
  
  if (row.status !== 'PREPARING' && checkPermission('shipment:print')) {
    items.push({ command: 'print', label: '打印标签', disabled: false })
  }
  
  return items
}

const handleDropdownCommand = async (command: string, row: WmsShipmentResp) => {
  switch (command) {
    case 'pack':
      await handlePack(row)
      break
    case 'ship':
      await handleShip(row)
      break
    case 'track':
      await handleTrackingInfo(row)
      break
    case 'deliver':
      await handleDeliver(row)
      break
    case 'cancel':
      await handleCancel(row)
      break
    case 'print':
      await handlePrint(row)
      break
  }
}

const handlePack = (row: WmsShipmentResp) => {
  packingDialogRef.value?.open(row.id)
}

const handlePackingConfirm = () => {
  ElMessage.success('打包成功')
  loadData()
}

const handlePackingCancel = () => {
  // 打包取消处理
}

const handleShip = (row: WmsShipmentResp) => {
  shippingDialogRef.value?.open(row.id)
}

const handleShippingConfirm = () => {
  ElMessage.success('发运成功')
  loadData()
}

const handleShippingCancel = () => {
  // 发运取消处理
}

const handleTrackingInfo = (row: WmsShipmentResp) => {
  trackingDialogRef.value?.open(row.id)
}

const handleDeliver = (row: WmsShipmentResp) => {
  deliveryConfirmDialogRef.value?.open(row.id)
}

const handleDeliveryConfirm = () => {
  ElMessage.success('签收确认成功')
  loadData()
}

const handleDeliveryCancel = () => {
  // 签收取消处理
}

const handleCancel = async (row: WmsShipmentResp) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消发运单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '请输入取消原因'
    })
    
    await shipmentStore.cancelShipment(row.id, reason)
    ElMessage.success('取消成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消失败')
      console.error(error)
    }
  }
}

const handlePrint = async (row: WmsShipmentResp) => {
  try {
    await shipmentStore.printShippingLabel(row.id)
    ElMessage.success('打印标签成功')
  } catch (error) {
    ElMessage.error('打印标签失败')
    console.error(error)
  }
}

// 生命周期
onMounted(() => {
  loadData()
  shipmentStore.loadCarriers()
  shipmentStore.loadShippingMethods()
})
</script>

<style scoped>
.shipment-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.main-content {
  background: #fff;
}

.text-muted {
  color: #909399;
}
</style>
