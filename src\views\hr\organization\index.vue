<template>
  <div>
    <VNTable
      ref="vnTableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :toolbar-config="toolbarConfig"
      :operation-buttons="operationButtons"
      :show-operations="true"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      operation-fixed="right"
      :operation-width="200" 
      :show-pagination="false" 
      @refresh="refreshAllNodeData"
      @add="handleAdd()"
    >
      <!-- 节点名称列，带图标 -->
      <template #column-name="{ row }">
        <el-icon style="margin-right: 5px; vertical-align: middle;">
          <OfficeBuilding v-if="row.nodeType === 'company'" />
          <Tickets v-else-if="row.nodeType === 'department'" />
          <User v-else-if="row.nodeType === 'position'" />
        </el-icon>
        <span style="vertical-align: middle;">{{ row.name }}</span>
      </template>

      <!-- 节点类型列 -->
      <template #column-nodeType="{ row }">
        <el-tag :type="getNodeTypeTag(row.nodeType)">{{ formatNodeType(row.nodeType) }}</el-tag>
      </template>

      <!-- 状态列 -->
      <template #column-status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>

      <!-- 虚拟节点列 -->
      <template #column-isVirtual="{ row }">
        <el-tag :type="row.isVirtual ? 'primary' : 'info'" size="small">
            {{ row.isVirtual ? '是' : '否' }}
        </el-tag>
      </template>

      <!-- 可根据需要添加其他自定义列，例如公司ID (如果要在表格中显示) -->
      <!-- 
      <template #column-companyId="{ row }">
        <span>{{ row.companyId || '-' }}</span>
      </template>
      -->

    </VNTable>

    <!-- 新增/编辑 弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="60%"
      :close-on-click-modal="false"
      @close="handleCloseDialog"
      draggable
      align-center
    >
      <VNForm
        ref="vnFormRef"
        :header-fields="formFields"
        v-model="formData"
        :default-columns="2"
        :label-width="'120px'"
        :loading="loading"
        :show-actions="isViewing ? false : true"
        @submit="submitForm"
        @cancel="handleCloseDialog"
      >
        <!-- 父级节点插槽 -->
        <template #form-item-parentId="{ field, formData: formModel }">
          <el-tree-select
            v-model="formModel[field.field]"
            :data="parentNodeOptions"
            :props="{ label: 'name', children: 'children', value: 'id' }" 
            check-strictly
            clearable
            placeholder="选择父级节点 (不选为顶级)"
            style="width: 100%;"
            :render-after-expand="false"
            :disabled="field.disabled"
            @change="(value) => handleParentChange(value)"
          />
        </template>

        <!-- 节点类型插槽 -->
        <template #form-item-nodeType="{ field, formData: formModel }">
            <el-select 
                v-model="formModel[field.field]" 
                placeholder="请选择节点类型" 
                style="width: 100%;"
                :disabled="isViewing || (formMode === 'edit' && !!currentData?.id)" 
                @change="(value) => handleNodeTypeChange(value, formModel)"
            >
                <el-option label="公司" value="company" :disabled="isNodeTypeDisabled('company', formModel)"></el-option>
                <el-option label="部门" value="department" :disabled="isNodeTypeDisabled('department', formModel)"></el-option>
                <el-option label="岗位" value="position" :disabled="isNodeTypeDisabled('position', formModel)"></el-option>
            </el-select>
        </template>
        
        <!-- 表单操作按钮 -->
        <template #actions>
            <template v-if="isViewing">
                <!-- 查看模式下的按钮 -->
                <el-button
                  v-if="hasPermission('hr:org:printpreview')" 
                  :icon="View" 
                  @click="handlePrintPreview"
                >
                  打印预览
                </el-button>
                <el-button
                  v-if="hasPermission('hr:org:print')" 
                  type="primary"
                  :icon="Printer" 
                  @click="handlePrint"
                >
                  打印
                </el-button>
                <el-button @click="handleCloseDialog">关闭</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
                <el-button @click="handleCloseDialog">取消</el-button>
            </template>
        </template>

      </VNForm>
    </el-dialog>

    <!-- 图标选择器弹窗 (组织结构不需要，已移除) -->

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue';
import VNTable from '@/components/VNTable/index.vue';
import VNForm from '@/components/VNForm/index.vue';
import type { TableColumn, ActionButton } from '@/components/VNTable/types';
import type { HeaderField } from '@/components/VNForm/types';
import { ElDialog, ElMessage, ElMessageBox, ElTag, ElButton, ElTreeSelect, ElSelect, ElOption, ElIcon } from 'element-plus'; // 移除 ElIcon, ElInput, ElScrollbar, ElEmpty
import type { TagProps } from 'element-plus';
import { OfficeBuilding, Tickets, User, View, Printer } from '@element-plus/icons-vue'; // 引入所需图标 View, Printer

// --- 引入组织节点管理 API 函数 ---
import {
  getOrganizationNodeTree,
  addOrganizationNode,
  updateOrganizationNode,
  deleteOrganizationNode,
  getOrganizationNodeByID,
} from '@/api/hr/organizationNode'; // 修改路径
import type { OrganizationNodeTreeVO, OrganizationNodeVO, OrganizationNodeCreateDTO, OrganizationNodeUpdateDTO } from '@/api/hr/organizationNode'; // 修改路径

import { hasPermission } from '@/hooks/usePermission'; // 权限钩子保留
// import { Plus, Search, View, Printer } from '@element-plus/icons-vue'; // 移除不需要的图标

// --- 辅助函数：获取并格式化API错误消息 (复用menu的，保持不变) ---
const getApiErrorMessage = (error: any): string => {
  const messageParts: string[] = [];
  const apiError = error?.response?.data || error;

  if (error && typeof error.message === 'string' && error.message) {
    if (!apiError || typeof apiError.message !== 'string' || apiError.message !== error.message) {
      messageParts.push(`<strong>请求处理错误：</strong><div style="margin-left: 10px; margin-top: 3px;">${error.message}</div>`);
    }
  }
  if (apiError && typeof apiError.message === 'string' && apiError.message) {
    messageParts.push(`<strong>主要错误原因：</strong><div style="margin-left: 10px; margin-top: 3px;">${apiError.message}</div>`);
  }
  if (apiError && Array.isArray(apiError.details) && apiError.details.length > 0) {
    let detailsHtml = '<strong>详细信息：</strong><ul style="margin: 5px 0 0 15px; padding-left: 10px; list-style-type: decimal;">';
    apiError.details.forEach((detail: { field?: string; message: string; value?: any }) => {
      detailsHtml += `<li style="margin-bottom: 4px;">${detail.message}</li>`;
    });
    detailsHtml += '</ul>';
    messageParts.push(detailsHtml);
  }
  if (messageParts.length > 0) {
    return messageParts.map(part => `<div style="margin-bottom: 10px;">${part}</div>`).join('');
  }
  return '操作出错了，请稍后重试';
};

// --- Refs ---
const vnTableRef = ref<InstanceType<typeof VNTable>>();
const vnFormRef = ref<InstanceType<typeof VNForm>>();
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const currentData = ref<OrganizationNodeTreeVO | OrganizationNodeVO | null>(null); // currentMenu -> currentData

// --- Reactive State ---
const loading = ref(false);
const tableData = ref<OrganizationNodeTreeVO[]>([]); // MenuTreeVO -> OrganizationNodeTreeVO
const formData = ref<Partial<OrganizationNodeCreateDTO | OrganizationNodeUpdateDTO>>({}); // DTO 类型更新
const parentNodeOptions = ref<OrganizationNodeTreeVO[]>([]); // parentMenuOptions -> parentNodeOptions, 类型更新

// --- 计算属性 ---
const dialogTitle = computed(() => {
  if (formMode.value === 'add') return '新增节点'; // 新增菜单 -> 新增节点
  if (formMode.value === 'edit') return '编辑节点'; // 编辑菜单 -> 编辑节点
  if (formMode.value === 'view') return '查看节点'; // 查看菜单 -> 查看节点
  return '组织管理'; // 菜单管理 -> 组织管理
});
const isViewing = computed(() => formMode.value === 'view');

// --- 表格列配置 ---
const tableColumns = ref<TableColumn[]>([
  { prop: 'name', label: '节点名称', minWidth: 180, align: 'left', slot: true },
  { prop: 'nodeType', label: '节点类型', width: 120, slot: true },
  { prop: 'code', label: '节点代码', width: 120 },
  { prop: 'orderNum', label: '排序', width: 80 },
  { prop: 'status', label: '状态', width: 100, slot: true },
  {
    prop: 'isVirtual',
    label: '虚拟节点',
    width: 100,
    slot: true,
  },
  {
    prop: 'weight',
    label: '权重',
    width: 80,
    formatter: (row: OrganizationNodeVO): string => {
      if (row.nodeType === 'position') {
        return row.weight !== null && row.weight !== undefined ? String(row.weight) : '-';
      }
      return '-';
    }
  },
  {
    prop: 'standardHeadcount',
    label: '标配人数',
    width: 100,
    formatter: (row: OrganizationNodeVO): string => {
      if (!row.isVirtual) {
        return row.standardHeadcount !== null && row.standardHeadcount !== undefined ? String(row.standardHeadcount) : '-';
      }
      return '-';
    }
  },
  { prop: 'remarks', label: '备注', minWidth: 150 },
]);

// --- 工具栏配置 ---
const toolbarConfig = computed<Record<string, any>>(() => ({
  refresh: true,
  add: hasPermission('hr:org:add'), // system:menu:add -> hr:organization:add
  expandAll: true, // 保留
  collapseAll: true, // 保留
  density: true,
  columnSetting: true,
  fullscreen: true,
}));

// --- 行操作按钮配置 ---
const operationButtons = computed<ActionButton[]>(() => [
  {
    label: '查看',
    icon: 'View', // 图标来自 Element Plus Icons，确保已全局或局部注册
    handler: (row) => handleView(row),
    hidden: !hasPermission('hr:org:list') // 权限更新
  },
  {
    label: '新增下级', // 新增 -> 新增下级
    icon: 'Plus',
    type: 'success',
    handler: (row) => handleAdd(row),
    hidden: (row) => !hasPermission('hr:org:add') || row.nodeType === 'position' // 岗位下不能新增
  },
  {
    label: '编辑',
    icon: 'Edit',
    type: 'primary',
    handler: (row) => handleEdit(row),
    hidden: !hasPermission('hr:org:edit') // 权限更新
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    handler: (row) => handleDelete(row),
    hidden: !hasPermission('hr:org:delete') // 权限更新
  },
]);

// --- 表单字段配置 ---
const formFields = computed<HeaderField[]>(() => {
    const disabledInViewMode = isViewing.value;
    const isEditMode = formMode.value === 'edit';
    const isAddMode = formMode.value === 'add';
    const currentSelectedNodeType = formData.value.nodeType;
    const currentIsVirtual = formData.value.isVirtual;

    const fields: HeaderField[] = [
        {
            field: 'parentId',
            label: '父级节点',
            type: 'slot',
            md: 24, lg: 24, xl: 24,
            disabled: disabledInViewMode || isEditMode, // 编辑和查看时禁用父节点
        },
        {
            field: 'nodeType',
            label: '节点类型',
            type: 'slot',
            rules: [{ required: true, message: '请选择节点类型' }],
            disabled: disabledInViewMode || (isEditMode && !!currentData.value?.id),
            md: 24, lg: 12, xl: 12,
        },
        {
            field: 'name',
            label: '节点名称',
            rules: [{ required: true, message: '节点名称为必填项' }],
            disabled: disabledInViewMode,
            md: 24, lg: 12, xl: 12,
        },
        {
            field: 'code',
            label: '节点代码',
            rules: isAddMode ? [{ required: true, message: '节点代码为必填项' }] : [], // 新增时必填
            disabled: disabledInViewMode || isEditMode, // 编辑和查看时禁用代码
            md: 24, lg: 12, xl: 12,
        },
        {
            field: 'orderNum',
            label: '显示排序',
            type: 'number',
            rules: [{ required: true, message: '排序为必填项' }],
            disabled: disabledInViewMode,
            md: 24, lg: 12, xl: 12,
        },
        {
            field: 'status',
            label: '节点状态',
            type: 'radio',
            options: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }],
            disabled: disabledInViewMode,
            md: 24, lg: 12, xl: 12,
        },
        {
            field: 'isVirtual',
            label: '虚拟节点',
            type: 'switch',
            disabled: disabledInViewMode,
            md: 24, lg: 12, xl: 12,
        },
    ];

    if (currentSelectedNodeType === 'position') {
        fields.push({
            field: 'weight',
            label: '节点权重',
            type: 'number',
            disabled: disabledInViewMode,
            md: 24, lg: 12, xl: 12,
            placeholder: '数字，审批流用'
        });
    }

    if (currentSelectedNodeType === 'position' && !currentIsVirtual) {
        fields.push({
            field: 'standardHeadcount',
            label: '标配人数',
            type: 'number',
            disabled: disabledInViewMode,
            md: 24, lg: 12, xl: 12,
        });
    }

    fields.push({
        field: 'remarks',
        label: '备注',
        type: 'textarea',
        rows: 3,
        md: 24, lg: 24, xl: 24,
        disabled: disabledInViewMode
    });

    return fields;
});

const selectedParentType = ref<string | null>(null); 

// --- 生命周期 ---
onMounted(() => {
  refreshAllNodeData();
});

// --- 方法 ---

// 刷新表格数据和父节点选项
const refreshAllNodeData = async (filterParams?: any) => { // filterParams 用于支持按公司ID过滤
  loading.value = true;
  try {
    const query = { ...filterParams }; // 支持外部传入 companyId 等过滤条件
    const response = await getOrganizationNodeTree(query) as unknown as { data?: OrganizationNodeTreeVO[] } | OrganizationNodeTreeVO[];
    let resData: OrganizationNodeTreeVO[] = [];
    if (Array.isArray(response)) {
      resData = response;
    } else if (response && Array.isArray(response.data)) {
      resData = response.data;
    }

    if (resData) {
      tableData.value = resData;
      // 准备父节点选项数据 (移除自身及子孙，并且岗位不能作为父级)
      parentNodeOptions.value = filterNodesForParentOptions(resData, currentData.value?.id);
    } else {
      tableData.value = [];
      parentNodeOptions.value = [];
    }
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    tableData.value = [];
    parentNodeOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 递归过滤父节点选项
// 1. 移除自身及其子孙节点
// 2. 岗位类型的节点不能作为父节点
const filterNodesForParentOptions = (nodes: OrganizationNodeTreeVO[], currentNodeIdToExclude?: number): OrganizationNodeTreeVO[] => {
  const result: OrganizationNodeTreeVO[] = [];
  for (const node of nodes) {
    if (node.id === currentNodeIdToExclude) continue; // 跳过当前编辑的节点本身

    const children = node.children ? filterNodesForParentOptions(node.children, currentNodeIdToExclude) : [];
    
    if (node.nodeType !== 'position') { // 岗位不能作为父节点
        const newNode = { ...node, children };
        // 如果一个非岗位节点过滤后没有合法的子节点了，但它本身是合法的父节点，也要保留
        if (children.length > 0 || node.nodeType !== 'position') {
             result.push(newNode);
        }
    } else { // 如果是岗位节点，只处理其子节点 (理论上岗位不应该有子节点在父节点选项中)
        result.push(...children);
    }
  }
  return result;
};


// 打开新增/编辑/查看弹窗
const handleOpenForm = async (mode: 'add' | 'edit' | 'view', rowData?: OrganizationNodeTreeVO) => { 
  formMode.value = mode;
  selectedParentType.value = null; 

  if (mode === 'edit' || mode === 'view') {
    if (!rowData || !rowData.id) {
      ElMessage.error('编辑或查看模式下缺少节点数据或 ID');
      return;
    }
    loading.value = true;
    try {
       const detailData = await getOrganizationNodeByID(rowData.id) as unknown as OrganizationNodeVO;
       if (detailData) {
         formData.value = { 
            ...detailData, 
            parentId: detailData.parentId === 0 ? undefined : detailData.parentId,
            // code, isVirtual, weight, standardHeadcount 应该已经通过 ...detailData 复制过来了
            // 因为 OrganizationNodeVO 中的字段名和类型与 DTO 兼容 (string, bool, *int)
         };
         currentData.value = detailData; 
         if (detailData.parentId && detailData.parentId !== 0) {
            const parent = findNodeById(tableData.value, detailData.parentId);
            selectedParentType.value = parent?.nodeType || null;
         }
         parentNodeOptions.value = filterNodesForParentOptions(tableData.value, detailData.id);
         dialogVisible.value = true;
       } else {
          const errorMessage = getApiErrorMessage({ message: '获取节点详情失败或数据为空'});
          ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
       }
    } catch(error) {
        const errorMessage = getApiErrorMessage(error);
        ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    } finally {
        loading.value = false;
    }
  } else { // 新增模式
    formData.value = {
      status: 1, 
      orderNum: (rowData?.children?.length || 0) * 10 + 10, 
      parentId: rowData?.id ?? undefined,
      code: '', // 默认空字符串
      isVirtual: false, // 默认非虚拟
      weight: undefined, // 默认为 undefined, DTO 是 *int
      standardHeadcount: undefined, // 默认为 undefined, DTO 是 *int
    };
    if (rowData?.id) { 
        selectedParentType.value = rowData.nodeType;
        if (rowData.nodeType === 'company') formData.value.nodeType = 'department';
        else if (rowData.nodeType === 'department') formData.value.nodeType = 'department';
        // 如果父是岗位，此 add 不会被触发 (已在 handleAdd 中校验)
    } else { 
        formData.value.nodeType = 'company'; 
        selectedParentType.value = null; 
    }
    currentData.value = null;
    parentNodeOptions.value = filterNodesForParentOptions(tableData.value); 
    dialogVisible.value = true;
  }

  nextTick(() => {
    vnFormRef.value?.clearValidate();
  });
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  formData.value = {};
  currentData.value = null;
  selectedParentType.value = null;
};

// 提交表单
const submitForm = async () => {
  if (isViewing.value) {
    handleCloseDialog();
    return;
  }
  const formRef = vnFormRef.value;
  if (!formRef) return;

  const isValid = await formRef.validateForm();
  if (!isValid) {
    ElMessage.warning('表单校验失败，请检查输入项');
    return;
  }

  const dataToSend: Partial<OrganizationNodeCreateDTO | OrganizationNodeUpdateDTO> = { ...formData.value };
  if (!dataToSend.parentId) {
      dataToSend.parentId = 0; 
  }

  // 根据节点类型调整 weight 和 standardHeadcount
  if (dataToSend.nodeType !== 'position') {
    dataToSend.weight = undefined; // 非岗位，weight 无意义，设为 undefined (DTO *int)
    dataToSend.standardHeadcount = undefined; // 非岗位，standardHeadcount 无意义，设为 undefined (DTO *int)
  }
  if (dataToSend.nodeType === 'position' && dataToSend.isVirtual) {
    dataToSend.standardHeadcount = undefined; // 虚拟岗位，标配人数无意义，设为 undefined (DTO *int)
  }

  // 前端层级约束校验
  if (dataToSend.parentId && dataToSend.parentId !== 0) {
      const parentNode = findNodeById(parentNodeOptions.value, dataToSend.parentId);
      if (parentNode) {
          if (parentNode.nodeType === 'department') {
              if (dataToSend.nodeType !== 'department' && dataToSend.nodeType !== 'position') {
                  ElMessage.error('部门下级只能是部门或岗位'); return;
              }
          } else if (parentNode.nodeType === 'position') {
              ElMessage.error('岗位下不能有子节点'); return;
          }
      } else {
         ElMessage.error('未找到有效的父节点信息，请重新选择父节点'); return;
      }
  } else { 
      if (dataToSend.nodeType !== 'company') {
          ElMessage.error('顶级节点必须是公司类型'); return;
      }
  }

  loading.value = true;
  try {
    if (formMode.value === 'add') {
      const result = await addOrganizationNode(dataToSend as OrganizationNodeCreateDTO);
      console.log('addOrganizationNode result', result);
      ElMessage.success('新增成功');
    } else if (formMode.value === 'edit' && currentData.value?.id) {
      await updateOrganizationNode(currentData.value.id, dataToSend as OrganizationNodeUpdateDTO);
      ElMessage.success('编辑成功');
    }
    handleCloseDialog();
    refreshAllNodeData(); 
  } catch (error) {
    const errorMessage = getApiErrorMessage(error);
    ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
  } finally {
    loading.value = false;
  }
};

// 处理新增按钮点击 (顶级或子级)
const handleAdd = (parentNode?: OrganizationNodeTreeVO) => { // 类型更新
  if (parentNode && parentNode.nodeType === 'position') {
      ElMessage.warning('岗位类型的节点下不能添加子节点');
      return;
  }
  handleOpenForm('add', parentNode);
};

// 处理编辑按钮点击
const handleEdit = (row: OrganizationNodeTreeVO) => { // 类型更新
  handleOpenForm('edit', row);
};

// 处理删除按钮点击
const handleDelete = async (row: OrganizationNodeTreeVO) => { // 类型更新
  if (row.children && row.children.length > 0) {
    ElMessage.warning('请先删除子节点');
    return;
  }
  try {
    await ElMessageBox.confirm(`确定删除节点 "${row.name}" 吗?`, '确认删除', { type: 'warning' }); // title -> name
    loading.value = true;
    await deleteOrganizationNode(row.id);
    ElMessage.success('删除成功');
    refreshAllNodeData(); // 刷新数据
  } catch (error) {
    if (error !== 'cancel') {
      const errorMessage = getApiErrorMessage(error);
      ElMessage({ type: 'error', dangerouslyUseHTMLString: true, message: errorMessage, showClose: true, duration: 5000 });
    } else {
      ElMessage.info('已取消删除');
    }
  } finally {
    loading.value = false;
  }
};

// 处理查看按钮点击
const handleView = (row: OrganizationNodeTreeVO) => { // 类型更新
  handleOpenForm('view', row);
};

// --- 辅助函数 ---
const formatNodeType = (type: string | undefined): string => {
  if (type === 'company') return '公司';
  if (type === 'department') return '部门';
  if (type === 'position') return '岗位';
  return '未知';
};

const getNodeTypeTag = (type: string | undefined): TagProps['type'] => {
  if (type === 'company') return 'primary'; // Element Plus 的 Tag type
  if (type === 'department') return 'success';
  if (type === 'position') return 'warning';
  return 'info';
};

// 在树中查找节点 (保持不变)
const findNodeById = (nodes: OrganizationNodeTreeVO[], id: number): OrganizationNodeTreeVO | null => { // 类型更新
    for (const node of nodes) {
        if (node.id === id) {
            return node;
        }
        if (node.children) {
            const found = findNodeById(node.children, id);
            if (found) {
                return found;
            }
        }
    }
    return null;
};

// 新增：处理父节点选择变化的逻辑
const handleParentChange = (parentId?: number) => {
    if (parentId) {
        const parentNode = findNodeById(parentNodeOptions.value, parentId);
        selectedParentType.value = parentNode?.nodeType || null;
    } else {
        selectedParentType.value = null; // 顶级节点
        formData.value.nodeType = 'company'; // 顶级节点默认为公司，且不可更改
    }
    // 当父节点变化时，可能需要重置或调整当前节点类型
    if (formMode.value === 'add') { // 仅在新增时根据父节点调整类型
        if (selectedParentType.value === 'company') {
           formData.value.nodeType = 'department'; // 公司下默认新增部门
        } else if (selectedParentType.value === 'department') {
           formData.value.nodeType = 'department'; // 部门下默认新增部门
        } else if (selectedParentType.value === null) { // 顶级
            formData.value.nodeType = 'company';
        }
        // 如果父节点是岗位，这里不应发生，因为岗位不能作为父级选项
    }
};

// 新增：处理节点类型变化的逻辑 (主要用于表单校验联动)
const handleNodeTypeChange = (newNodeType: string, formModel: Partial<OrganizationNodeCreateDTO | OrganizationNodeUpdateDTO>) => {
    console.log('handleNodeTypeChange', newNodeType, formModel);
    // 如果当前选择的父节点是公司，而新的节点类型是岗位，这是不允许的
    // if (selectedParentType.value === 'company' && newNodeType === 'position') {
    //     ElMessage.warning('公司下级不能直接创建岗位，请先创建部门');
    //     // 重置 nodeType 或给出其他提示
    //     // formModel.nodeType = undefined; // 清空或设为上一个有效值
    // }
    // 其他层级约束可以在这里补充，或在提交时统一校验
};

// 新增：动态禁用节点类型选项的逻辑
const isNodeTypeDisabled = (typeToDisable: string, formModel: any): boolean => {
    const parentId = formModel.parentId;
    const currentParentNode = parentId ? findNodeById(parentNodeOptions.value, parentId) : null;
    
    if (formMode.value === 'edit' && currentData.value?.id) { // 编辑模式下，类型通常不允许修改
        return typeToDisable !== formModel.nodeType;
    }

    if (!parentId) { // 顶级节点
        return typeToDisable !== 'company'; // 顶级只能是公司
    }

    if (currentParentNode) {
        //if (currentParentNode.nodeType === 'company') {
        //    return typeToDisable === 'position'; // 公司下不能直接是岗位
        //}
        if (currentParentNode.nodeType === 'department') {
            // 部门下可以是部门或岗位，不能是公司
            return typeToDisable === 'company';
        }
        if (currentParentNode.nodeType === 'position') {
             return true; // 岗位下不能有任何子节点，所有类型都禁用
        }
    }
    return false; // 默认不禁用
};

// +++ 新增：打印处理函数（占位）+++
const handlePrintPreview = () => {
  if (!currentData.value) return;
  console.log('触发组织节点打印预览，节点:', currentData.value);
  ElMessage.info('组织节点打印预览功能待实现');
  // TODO: Implement print preview logic
};

const handlePrint = () => {
  if (!currentData.value) return;
  console.log('触发组织节点打印，节点:', currentData.value);
  ElMessage.info('组织节点打印功能待实现');
  // TODO: Implement print logic
};
// +++ 结束新增 +++

</script>

<style scoped>
:deep(.el-tree-select) {
  width: 100%;
}
/* 可根据需要添加其他样式 */
</style> 