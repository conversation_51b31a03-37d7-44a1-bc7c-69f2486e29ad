package dto

import "backend/pkg/response"

// MtlItemCreateReq 创建物料请求
type MtlItemCreateReq struct {
	Sku               string                  `json:"sku" binding:"omitempty,alphanum,max=100"`
	Name              string                  `json:"name" binding:"required,max=255"`
	Description       *string                 `json:"description" binding:"omitempty"`           // 描述
	Specification     *string                 `json:"specification" binding:"omitempty,max=255"` // 规格型号
	CategoryCode      *string                 `json:"categoryCode" binding:"omitempty,max=100"`  // 分类代码 (字典值)
	GroupCode         *string                 `json:"groupCode" binding:"omitempty,max=100"`     // 物料组代码 (字典值)
	BaseUnit          string                  `json:"baseUnit" binding:"required,max=20"`
	ShelfLifeDays     *int                    `json:"shelfLifeDays" binding:"omitempty,gte=0"`
	BatchManaged      *bool                   `json:"batchManaged" binding:"omitempty"`
	SerialManaged     *bool                   `json:"serialManaged" binding:"omitempty"`
	WeightKg          *float64                `json:"weightKg" binding:"omitempty,min=0"`
	VolumeM3          *float64                `json:"volumeM3" binding:"omitempty,min=0"`
	LengthM           *float64                `json:"lengthM" binding:"omitempty,min=0"`
	WidthM            *float64                `json:"widthM" binding:"omitempty,min=0"`
	HeightM           *float64                `json:"heightM" binding:"omitempty,min=0"`
	Status            *string                 `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE"`
	DefaultCustomerID *uint                   `json:"defaultCustomerId" binding:"omitempty,gt=0"` // 默认客户ID
	DefaultSupplierID *uint                   `json:"defaultSupplierId" binding:"omitempty,gt=0"` // 默认供应商ID
	DefaultLocationID *uint                   `json:"defaultLocationId" binding:"omitempty"`
	StorageCondition  *string                 `json:"storageCondition" binding:"omitempty"`
	ImageUrl          *string                 `json:"imageUrl" binding:"omitempty"`
	Remark            *string                 `json:"remark" binding:"omitempty"`
	PackageUnits      []MtlItemPackageUnitDTO `json:"packageUnits" binding:"omitempty,dive"`
}

// MtlItemUpdateReq 更新物料请求
type MtlItemUpdateReq struct {
	ID                uint     `json:"id" binding:"required,gt=0"`
	Sku               *string  `json:"sku" binding:"omitempty,alphanum,max=100"`
	Name              *string  `json:"name" binding:"omitempty,max=255"`
	Description       *string  `json:"description" binding:"omitempty"`
	Specification     *string  `json:"specification" binding:"omitempty,max=255"`
	CategoryCode      *string  `json:"categoryCode" binding:"omitempty,max=100"`
	GroupCode         *string  `json:"groupCode" binding:"omitempty,max=100"`
	ShelfLifeDays     *int     `json:"shelfLifeDays" binding:"omitempty,gte=0"`
	BatchManaged      *bool    `json:"batchManaged"`
	SerialManaged     *bool    `json:"serialManaged"`
	WeightKg          *float64 `json:"weightKg" binding:"omitempty,min=0"`
	VolumeM3          *float64 `json:"volumeM3" binding:"omitempty,min=0"`
	LengthM           *float64 `json:"lengthM" binding:"omitempty,min=0"`
	WidthM            *float64 `json:"widthM" binding:"omitempty,min=0"`
	HeightM           *float64 `json:"heightM" binding:"omitempty,min=0"`
	Status            *string  `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE"`
	DefaultCustomerID *uint    `json:"defaultCustomerId" binding:"omitempty"`
	DefaultSupplierID *uint    `json:"defaultSupplierId" binding:"omitempty"`
	DefaultLocationID *uint    `json:"defaultLocationId" binding:"omitempty"`
	StorageCondition  *string  `json:"storageCondition" binding:"omitempty"`
	ImageUrl          *string  `json:"imageUrl" binding:"omitempty"`
	Remark            *string  `json:"remark" binding:"omitempty"`
}

// MtlItemQueryReq 查询物料请求 (使用现有分页机制)
type MtlItemQueryReq struct {
	response.PageQuery         // 直接嵌入现有分页结构
	Sku                string  `form:"sku" json:"sku" binding:"omitempty,max=100"`
	Name               string  `form:"name" json:"name" binding:"omitempty,max=255"`
	CategoryCode       *string `form:"categoryCode" json:"categoryCode" binding:"omitempty,max=100"`
	GroupCode          *string `form:"groupCode" json:"groupCode" binding:"omitempty,max=100"`
	DefaultSupplierID  *uint   `form:"defaultSupplierId" json:"defaultSupplierId" binding:"omitempty,gt=0"`
	DefaultLocationID  *uint   `form:"defaultLocationId" json:"defaultLocationId" binding:"omitempty,gt=0"`
	Status             string  `form:"status" json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE"`
	BatchManaged       *bool   `form:"batchManaged" json:"batchManaged"`
	SerialManaged      *bool   `form:"serialManaged" json:"serialManaged"`
}

// MtlItemPackageUnitDTO 包装单位传输对象 (保留用于向后兼容)
type MtlItemPackageUnitDTO struct {
	ID               uint     `json:"id,omitempty"`
	UnitName         string   `json:"unitName" binding:"required,max=50"`
	ConversionFactor float64  `json:"conversionFactor" binding:"required,gt=0"`
	PackageWeightKg  *float64 `json:"packageWeightKg,omitempty" binding:"omitempty,min=0"`
	PackageVolumeM3  *float64 `json:"packageVolumeM3,omitempty" binding:"omitempty,min=0"`
	PackageLengthM   *float64 `json:"packageLengthM,omitempty" binding:"omitempty,min=0"`
	PackageWidthM    *float64 `json:"packageWidthM,omitempty" binding:"omitempty,min=0"`
	PackageHeightM   *float64 `json:"packageHeightM,omitempty" binding:"omitempty,min=0"`
}

// MtlItemPackageUnitCreateReq 创建包装单位请求 (新增)
type MtlItemPackageUnitCreateReq struct {
	UnitName         string   `json:"unitName" binding:"required,max=50"`
	ConversionFactor float64  `json:"conversionFactor" binding:"required,gt=0"`
	PackageWeightKg  *float64 `json:"packageWeightKg,omitempty" binding:"omitempty,min=0"`
	PackageVolumeM3  *float64 `json:"packageVolumeM3,omitempty" binding:"omitempty,min=0"`
	PackageLengthM   *float64 `json:"packageLengthM,omitempty" binding:"omitempty,min=0"`
	PackageWidthM    *float64 `json:"packageWidthM,omitempty" binding:"omitempty,min=0"`
	PackageHeightM   *float64 `json:"packageHeightM,omitempty" binding:"omitempty,min=0"`
}

// MtlItemPackageUnitUpdateReq 更新包装单位请求 (新增)
type MtlItemPackageUnitUpdateReq struct {
	UnitName         *string  `json:"unitName,omitempty" binding:"omitempty,max=50"`
	ConversionFactor *float64 `json:"conversionFactor,omitempty" binding:"omitempty,gt=0"`
	PackageWeightKg  *float64 `json:"packageWeightKg,omitempty" binding:"omitempty,min=0"`
	PackageVolumeM3  *float64 `json:"packageVolumeM3,omitempty" binding:"omitempty,min=0"`
	PackageLengthM   *float64 `json:"packageLengthM,omitempty" binding:"omitempty,min=0"`
	PackageWidthM    *float64 `json:"packageWidthM,omitempty" binding:"omitempty,min=0"`
	PackageHeightM   *float64 `json:"packageHeightM,omitempty" binding:"omitempty,min=0"`
}
