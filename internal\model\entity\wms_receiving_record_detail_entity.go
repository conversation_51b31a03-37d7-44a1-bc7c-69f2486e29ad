package entity

import (
	"time"

	"gorm.io/gorm"
)

// WmsReceivingInspectionStatus 定义收货检验状态常量
type WmsReceivingInspectionStatus string

const (
	ReceivingInspectionStatusNotRequired WmsReceivingInspectionStatus = "NOT_REQUIRED" // 无需检验
	ReceivingInspectionStatusPending     WmsReceivingInspectionStatus = "PENDING"      // 待检验
	ReceivingInspectionStatusInspecting  WmsReceivingInspectionStatus = "INSPECTING"   // 检验中 (新增)
	ReceivingInspectionStatusPass        WmsReceivingInspectionStatus = "PASS"         // 合格
	ReceivingInspectionStatusFail        WmsReceivingInspectionStatus = "FAIL"         // 不合格
)

// WmsReceivingRecordDetail 对应数据库中的 wms_receiving_record_details 表
// 存储收货单明细信息
type WmsReceivingRecordDetail struct {
	AccountBookEntity               // 嵌入账套实体，提供账套强绑定
	ReceivingRecordID    uint       `gorm:"column:receiving_record_id;not null;index;comment:关联收货单ID" json:"receivingRecordId"`                              // 关联的 wms_receiving_records ID
	NotificationDetailID *uint      `gorm:"column:notification_detail_id;index;comment:关联ASN明细ID" json:"notificationDetailId"`                               // 关联的 wms_inbound_notification_details ID (可选, 用于核对)
	LineNo               int        `gorm:"column:line_no;not null;comment:行号" json:"lineNo"`                                                                // 行号
	ItemID               uint       `gorm:"column:item_id;not null;index;comment:物料ID" json:"itemId"`                                                        // 物料ID
	ExpectedQuantity     *float64   `gorm:"column:expected_quantity;type:numeric(12,4);comment:预期数量" json:"expectedQuantity"`                                // 预期收货数量 (从ASN带入或计算)
	ReceivedQuantity     float64    `gorm:"column:received_quantity;type:numeric(12,4);not null;default:0;comment:实收数量" json:"receivedQuantity"`             // 实际收货数量
	UnitOfMeasure        string     `gorm:"column:unit_of_measure;type:varchar(20);not null;comment:数量单位" json:"unitOfMeasure"`                              // 数量单位
	ReceivedAtLocationID uint       `gorm:"column:received_at_location_id;not null;comment:收货库位ID" json:"receivedAtLocationId"`                              // 收货库位ID (e.g., Staging Area / Dock Door)
	BatchNo              *string    `gorm:"column:batch_no;type:varchar(100);index;comment:外部批次号" json:"batchNo"`                                            // 外部批次号 (前端输入)
	InternalBatchNo      *string    `gorm:"column:internal_batch_no;type:varchar(100);uniqueIndex:uk_int_batch_no_acc;comment:内部批次号" json:"internalBatchNo"` // 内部批次号 (系统生成)
	ProductionDate       *time.Time `gorm:"column:production_date;type:date;comment:生产日期" json:"-"`                                                          // 生产日期 (实收)
	ExpiryDate           *time.Time `gorm:"column:expiry_date;type:date;index;comment:过期日期" json:"-"`
	LineStatus           string     `gorm:"column:line_status;type:wms_receiving_record_status;not null;default:'PENDING';index;comment:状态" json:"lineStatus"`                  // 收货单明细行状态 (使用数据库定义的 ENUM)                                            // 过期日期 (实收)
	InspectionStatus     string     `gorm:"column:inspection_status;type:wms_receiving_inspection_status;default:'NOT_REQUIRED';comment:检验状态" json:"inspectionStatus"` // 检验状态
	InspectorID          *uint      `gorm:"column:inspector_id;comment:检验员ID" json:"inspectorId"`                                                                      // 检验员ID (关联用户表)
	InspectionNotes      *string    `gorm:"column:inspection_notes;type:text;comment:检验备注" json:"inspectionNotes"`                                                     // 检验备注
	InspectionAt         *time.Time `gorm:"column:inspection_at;comment:检验时间" json:"inspectionAt"`
	QuantityDifference   *float64   `gorm:"column:quantity_difference;type:numeric(12,4);comment:差异数量" json:"quantityDifference"` // 差异数量 (计算得到)
	DiscrepancyReason    *string    `gorm:"column:discrepancy_reason;type:text;comment:差异原因" json:"discrepancyReason"`            // 差异原因
	Remark               *string    `gorm:"column:remark;type:text;comment:备注" json:"remark"`                                     // 备注
	PackageQty           *float64   `gorm:"column:package_qty;type:numeric(12,4);comment:包装数量" json:"packageQty"`
	PackageUnit          *string    `gorm:"column:package_unit;type:varchar(20);comment:包装单位" json:"packageUnit"`
	ProductionDateStr    *string    `gorm:"-" json:"productionDate"`
	ExpiryDateStr        *string    `gorm:"-" json:"expiryDate"`
}

// TableName 指定数据库表名方法
func (WmsReceivingRecordDetail) TableName() string {
	return "wms_receiving_record_detail"
}

// BeforeSave 在保存前处理生产日期和过期日期
func (w *WmsReceivingRecordDetail) BeforeSave(tx *gorm.DB) (err error) {
	if w.ProductionDateStr != nil && *w.ProductionDateStr != "" {
		productionDate, err := time.Parse("2006-01-02", *w.ProductionDateStr)
		if err != nil {
			return err
		}
		w.ProductionDate = &productionDate
	} else {
		w.ProductionDate = nil
	}

	if w.ExpiryDateStr != nil && *w.ExpiryDateStr != "" {
		expiryDate, err := time.Parse("2006-01-02", *w.ExpiryDateStr)
		if err != nil {
			return err
		}
		w.ExpiryDate = &expiryDate
	} else {
		w.ExpiryDate = nil
	}
	return
}

// AfterFind 在查询后处理生产日期和过期日期
func (w *WmsReceivingRecordDetail) AfterFind(tx *gorm.DB) (err error) {
	if w.ProductionDate != nil {
		dateStr := w.ProductionDate.Format("2006-01-02")
		w.ProductionDateStr = &dateStr
	}

	if w.ExpiryDate != nil {
		dateStr := w.ExpiryDate.Format("2006-01-02")
		w.ExpiryDateStr = &dateStr
	}
	return
}
