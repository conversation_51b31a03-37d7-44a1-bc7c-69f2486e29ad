package entity

import (
	"fmt"
	"time"
)

// WmsInventoryAlertLogStatus 库存预警日志状态枚举
type WmsInventoryAlertLogStatus string

const (
	AlertLogStatusActive       WmsInventoryAlertLogStatus = "ACTIVE"       // 活跃
	AlertLogStatusAcknowledged WmsInventoryAlertLogStatus = "ACKNOWLEDGED" // 已确认
	AlertLogStatusResolved     WmsInventoryAlertLogStatus = "RESOLVED"     // 已解决
)

// WmsInventoryAlertLog 对应数据库中的 wms_inventory_alert_log 表
// 存储库存预警日志信息
type WmsInventoryAlertLog struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	RuleID         uint                         `gorm:"column:rule_id;not null;comment:预警规则ID" json:"ruleId"`
	InventoryID    uint                         `gorm:"column:inventory_id;not null;comment:库存ID" json:"inventoryId"`
	AlertType      WmsInventoryAlertType        `gorm:"column:alert_type;type:varchar(20);not null;comment:预警类型" json:"alertType"`
	AlertLevel     WmsInventoryAlertLevel       `gorm:"column:alert_level;type:varchar(20);not null;comment:预警级别" json:"alertLevel"`
	AlertMessage   string                       `gorm:"column:alert_message;type:text;not null;comment:预警消息" json:"alertMessage"`
	CurrentValue   *float64                     `gorm:"column:current_value;type:numeric(12,4);comment:当前值" json:"currentValue"`
	ThresholdValue *float64                     `gorm:"column:threshold_value;type:numeric(12,4);comment:阈值" json:"thresholdValue"`
	Status         WmsInventoryAlertLogStatus   `gorm:"column:status;type:varchar(20);default:'ACTIVE';comment:处理状态" json:"status"`
	AcknowledgedBy *uint                        `gorm:"column:acknowledged_by;comment:确认人ID" json:"acknowledgedBy"`
	AcknowledgedAt *time.Time                   `gorm:"column:acknowledged_at;comment:确认时间" json:"acknowledgedAt"`
	ResolvedAt     *time.Time                   `gorm:"column:resolved_at;comment:解决时间" json:"resolvedAt"`

	// 关联关系
	Rule         *WmsInventoryAlertRule `gorm:"foreignKey:RuleID;references:ID" json:"rule,omitempty"`
	Inventory    *WmsInventory          `gorm:"foreignKey:InventoryID;references:ID" json:"inventory,omitempty"`
	Acknowledger *User                  `gorm:"foreignKey:AcknowledgedBy;references:ID" json:"acknowledger,omitempty"`
}

// TableName 指定数据库表名方法
func (WmsInventoryAlertLog) TableName() string {
	return "wms_inventory_alert_log"
}

// CanAcknowledge 判断是否可以确认
func (w *WmsInventoryAlertLog) CanAcknowledge() bool {
	return w.Status == AlertLogStatusActive
}

// CanResolve 判断是否可以解决
func (w *WmsInventoryAlertLog) CanResolve() bool {
	return w.Status == AlertLogStatusActive || w.Status == AlertLogStatusAcknowledged
}

// Acknowledge 确认预警
func (w *WmsInventoryAlertLog) Acknowledge(userID uint) error {
	if !w.CanAcknowledge() {
		return fmt.Errorf("预警状态不允许确认")
	}
	
	now := time.Now()
	w.Status = AlertLogStatusAcknowledged
	w.AcknowledgedBy = &userID
	w.AcknowledgedAt = &now
	
	return nil
}

// Resolve 解决预警
func (w *WmsInventoryAlertLog) Resolve() error {
	if !w.CanResolve() {
		return fmt.Errorf("预警状态不允许解决")
	}
	
	now := time.Now()
	w.Status = AlertLogStatusResolved
	w.ResolvedAt = &now
	
	return nil
}

// GetAlertTypeName 获取预警类型中文名称
func (w *WmsInventoryAlertLog) GetAlertTypeName() string {
	switch w.AlertType {
	case AlertTypeLowStock:
		return "低库存预警"
	case AlertTypeHighStock:
		return "高库存预警"
	case AlertTypeExpiry:
		return "过期预警"
	case AlertTypeSlowMoving:
		return "呆滞库存预警"
	default:
		return "未知类型"
	}
}

// GetAlertLevelName 获取预警级别中文名称
func (w *WmsInventoryAlertLog) GetAlertLevelName() string {
	switch w.AlertLevel {
	case AlertLevelInfo:
		return "信息"
	case AlertLevelWarning:
		return "警告"
	case AlertLevelCritical:
		return "严重"
	default:
		return "未知级别"
	}
}

// GetStatusName 获取状态中文名称
func (w *WmsInventoryAlertLog) GetStatusName() string {
	switch w.Status {
	case AlertLogStatusActive:
		return "活跃"
	case AlertLogStatusAcknowledged:
		return "已确认"
	case AlertLogStatusResolved:
		return "已解决"
	default:
		return "未知状态"
	}
}

// GetSeverityScore 获取严重程度评分（用于排序）
func (w *WmsInventoryAlertLog) GetSeverityScore() int {
	levelScore := 0
	switch w.AlertLevel {
	case AlertLevelInfo:
		levelScore = 1
	case AlertLevelWarning:
		levelScore = 2
	case AlertLevelCritical:
		levelScore = 3
	}
	
	statusScore := 0
	switch w.Status {
	case AlertLogStatusActive:
		statusScore = 3
	case AlertLogStatusAcknowledged:
		statusScore = 2
	case AlertLogStatusResolved:
		statusScore = 1
	}
	
	return levelScore*10 + statusScore
}

// GetDurationSinceCreated 获取创建以来的持续时间（小时）
func (w *WmsInventoryAlertLog) GetDurationSinceCreated() float64 {
	return time.Since(w.CreatedAt).Hours()
}

// GetDurationSinceAcknowledged 获取确认以来的持续时间（小时）
func (w *WmsInventoryAlertLog) GetDurationSinceAcknowledged() *float64 {
	if w.AcknowledgedAt == nil {
		return nil
	}
	
	duration := time.Since(*w.AcknowledgedAt).Hours()
	return &duration
}

// IsOverdue 判断是否超时（根据预警级别判断）
func (w *WmsInventoryAlertLog) IsOverdue() bool {
	hours := w.GetDurationSinceCreated()
	
	switch w.AlertLevel {
	case AlertLevelCritical:
		return hours > 2 && w.Status == AlertLogStatusActive // 严重预警2小时内需要处理
	case AlertLevelWarning:
		return hours > 8 && w.Status == AlertLogStatusActive // 警告预警8小时内需要处理
	case AlertLevelInfo:
		return hours > 24 && w.Status == AlertLogStatusActive // 信息预警24小时内需要处理
	default:
		return false
	}
}

// GetVarianceDescription 获取差异描述
func (w *WmsInventoryAlertLog) GetVarianceDescription() string {
	if w.CurrentValue == nil || w.ThresholdValue == nil {
		return ""
	}
	
	switch w.AlertType {
	case AlertTypeLowStock:
		shortage := *w.ThresholdValue - *w.CurrentValue
		return fmt.Sprintf("缺少 %.2f", shortage)
	case AlertTypeHighStock:
		excess := *w.CurrentValue - *w.ThresholdValue
		return fmt.Sprintf("超出 %.2f", excess)
	case AlertTypeExpiry:
		return fmt.Sprintf("%.0f 天后过期", *w.CurrentValue)
	case AlertTypeSlowMoving:
		return fmt.Sprintf("已 %.0f 天未移动", *w.CurrentValue)
	default:
		return fmt.Sprintf("当前值: %.2f, 阈值: %.2f", *w.CurrentValue, *w.ThresholdValue)
	}
}

// GetPriorityLevel 获取优先级等级（1-5，5最高）
func (w *WmsInventoryAlertLog) GetPriorityLevel() int {
	baseLevel := 1
	
	// 根据预警级别调整
	switch w.AlertLevel {
	case AlertLevelCritical:
		baseLevel = 5
	case AlertLevelWarning:
		baseLevel = 3
	case AlertLevelInfo:
		baseLevel = 1
	}
	
	// 根据超时情况调整
	if w.IsOverdue() {
		baseLevel = min(5, baseLevel+1)
	}
	
	return baseLevel
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
