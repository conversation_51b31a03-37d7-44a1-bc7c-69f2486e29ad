package main

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/recover"
	"gorm.io/gorm"

	"backend/internal/controller"
	"backend/internal/middleware" // Assuming middleware package exists
	"backend/internal/repository"
	"backend/internal/router" // Import the new router package
	"backend/internal/service"
	"backend/pkg/config"
	appLogger "backend/pkg/logger" // Alias logger to avoid conflict with iris logger

	// Import gorm for DB type
	"backend/pkg/database" // Import the database package
	// Import other necessary packages like database
	"backend/pkg/storage"

	"github.com/robfig/cron/v3" // Add cron library import
)

func main() {
	// 1. 加载配置
	// TODO: Implement proper config path resolution
	cfg, err := config.LoadConfig("./configs/config.yaml")
	if err != nil {
		panic(fmt.Sprintf("加载配置失败: %v", err))
	}
	config.CONFIG = cfg // Assign to global config if needed by other packages

	// 2. 初始化日志
	// Map config.LogConfig to logger.LogConfig
	loggerCfg := &appLogger.LogConfig{
		Level:  cfg.App.Log.Level,
		Format: cfg.App.Log.Format,
		// Prefix:    cfg.App.Log.Prefix, // Prefix is not in config.LogConfig
		Director: cfg.App.Log.Filepath, // Map Filepath to Director
		// ShowLine:  cfg.App.Log.ShowLine, // ShowLine is not in config.LogConfig
		// EncodeTime: cfg.App.Log.EncodeTime, // EncodeTime is not in config.LogConfig
		// --- Restore original logic based on config.App.Log.Output ---
		LogInFile:    strings.Contains(cfg.App.Log.Output, "file"),    // Infer LogInFile based on Output
		LogInConsole: strings.Contains(cfg.App.Log.Output, "console"), // Infer LogInConsole based on Output
		// LogInFile:    false,       // Remove forced setting
		// LogInConsole: true,        // Remove forced setting
		MaxSize:    cfg.App.Log.MaxSize,
		MaxBackups: cfg.App.Log.MaxBackups,
		MaxAge:     cfg.App.Log.MaxAge,
		Compress:   cfg.App.Log.Compress,
	}
	appLogger.Init(loggerCfg)
	log := appLogger.GetLogger()
	log.Info("日志系统初始化完成") // Restore original message

	// +++ 步骤 2.2: 实例化自定义 LoggerMiddleware (根据 audit_and_logger_refactor_plan.md) +++
	// 实例化 LoggerMiddleware (用于控制台)
	loggerMiddlewareInstance := middleware.LoggerMiddleware(middleware.LoggerConfig{
		Enabled:             true,
		LogRequestBody:      true,                                                               // 根据需要调整
		LogResponseBody:     false,                                                              // 通常不在控制台记录响应体，除非调试特定问题
		MaxRequestBodySize:  1024,                                                               // 限制请求体大小 (bytes)
		MaxResponseBodySize: 0,                                                                  // 响应体大小 (0 表示不限制，但LogResponseBody为false时此项无效)
		ExcludedPaths:       []string{"/api/v1/captcha", "/metrics", "/favicon.ico", "/static"}, // 更正为 ExcludedPaths 以匹配 logger.go 中的定义
		Skipper: func(ctx iris.Context) bool { // 计划中的字段，现在 logger.go 已支持
			// 示例：跳过所有 OPTIONS 请求 (通常用于CORS预检)
			// if ctx.Method() == iris.MethodOptions {
			//     return true
			// }
			// 更多跳过逻辑可以根据您的需求添加
			return false // 默认不跳过任何额外请求
		},
		MessageContextKeys: []string{"user_id", "user_role"}, // 计划中的字段，现在 logger.go 已支持。示例值，请根据实际在context中设置的键调整
		// 注意：user_id 已经在 logger.go 中通过 constant.CONTEXT_USER_ID 尝试获取并记录，
		// 在此重复添加主要是为了演示 MessageContextKeys 的用法。
		// 如果 logger.go 的原生逻辑已覆盖，可以从这里移除或选择更具体的键。
		MessageHeaderKeys: []string{"X-Client-Version", "X-App-Name"}, // 计划中的字段，现在 logger.go 已支持。示例值，请根据需要记录的特定请求头调整
		// Output 和 Level 将由全局日志配置决定，这里不需要显式设置用于控制台输出的 LoggerMiddleware
		// 因为它会使用 appLogger.GetLogger() 或类似的全局/传递的 logger
	})
	log.Info("自定义 LoggerMiddleware (控制台) 实例化完成")
	// +++ 结束步骤 2.2 +++

	// 2.5. 初始化授权验证 (新增)
	log.Info("初始化授权验证...")
	// 确保 cfg.App.LicensePath 在您的配置结构体和文件中存在
	if cfg.App.LicensePath == "" {
		log.Warn("配置中未找到 app.licensePath, 跳过授权初始化")
		// 根据您的业务需求，如果许可证是强制性的，这里应该 log.Fatal 或 os.Exit(1)
	} else {
		middleware.InitLicense(cfg.App.LicensePath) // 调用初始化函数
		// 启动时检查许可证状态 (可选，但推荐)
		if middleware.GetLicenseInfo() == nil {
			// 如果 GetLicenseInfo 返回 nil, 说明初始化失败或文件无效/过期
			log.Error("授权验证失败、授权无效或已过期。系统功能可能受限或无法启动。请检查日志和授权文件。")
			// 如果要求严格的许可证检查，请取消下面的注释以在启动时失败
			// log.Fatal("强制许可证检查失败，应用程序无法启动。")
		}
	}
	log.Info("授权验证初始化步骤完成 (如果已配置)")

	// 3. 初始化数据库连接 (包括迁移和种子数据填充)
	log.Info("初始化数据库...")
	dbManager, err := database.InitializeDb(&cfg.Database) // Pass the entire config
	if err != nil {
		log.Errorf("数据库初始化失败: %v", err)
		os.Exit(1)
		// log.Warn("继续执行（可能在后续失败）...") // Remove or keep commented
	}

	// Get the gorm.DB instance from the manager
	var gormDB *gorm.DB
	if dbManager != nil {
		gormDB = dbManager.GetDB()
		if gormDB == nil {
			log.Error("初始化数据库后无法获取 gorm DB 实例 (dbManager is not nil, but GetDB returned nil)")
			os.Exit(1)
		}
	} else {
		// If dbManager is nil, InitializeDb must have failed and exited above,
		// but keep this else block for robustness, though it might be unreachable.
		log.Error("数据库初始化失败，dbManager 为 nil")
		os.Exit(1)
	}

	log.Info("数据库初始化完成")

	// --- 获取 *sql.DB --- 实现依赖注入
	var sqlDB *sql.DB
	var sqlDbErr error
	if dbManager != nil { // 优先使用 DBManager 获取
		sqlDB, sqlDbErr = dbManager.GetSQLDB()
		if sqlDbErr != nil {
			log.Fatalf("无法从 DBManager 获取 *sql.DB: %v", sqlDbErr)
		}
	} else if gormDB != nil { // 备选：直接从 gormDB 获取 (理论上不应执行)
		sqlDB, sqlDbErr = gormDB.DB()
		if sqlDbErr != nil {
			log.Fatalf("无法从 gormDB 获取 *sql.DB: %v", sqlDbErr)
		}
	} else {
		log.Fatal("无法获取 *sql.DB 实例 (dbManager 和 gormDB 都为 nil)")
	}
	if sqlDB == nil {
		log.Fatal("获取到的 *sql.DB 实例为 nil")
	}
	log.Info("成功获取 *sql.DB 实例")
	// --- 结束获取 *sql.DB ---

	// 4. 初始化 Storage 实例 (新增)
	storageCfg := storage.StorageConfig{ // <<< 直接从 config.Configuration.Storage 构建
		Type: cfg.Storage.Type,
		// 根据类型填充具体配置
	}
	if cfg.Storage.Type == storage.STORAGE_TYPE_LOCAL {
		storageCfg.RootDir = cfg.Storage.Local.Path
		storageCfg.BaseURL = cfg.Storage.Local.BaseURL
	} else if cfg.Storage.Type == storage.STORAGE_TYPE_S3 {
		// 填充 S3 配置
		storageCfg.Endpoint = cfg.Storage.S3.Endpoint
		storageCfg.Region = cfg.Storage.S3.Region
		storageCfg.Bucket = cfg.Storage.S3.Bucket
		storageCfg.AccessKey = cfg.Storage.S3.AccessKey
		storageCfg.SecretKey = cfg.Storage.S3.SecretKey
		storageCfg.SSL = strings.HasPrefix(cfg.Storage.S3.Endpoint, "https") // 简单判断
		storageCfg.BaseURL = cfg.Storage.S3.BaseURL
	}
	// 添加其他存储类型的判断... (例如 OSS)

	// 添加从 config.Configuration.Storage 读取 AllowedTypes 和 MaxFileSizeMB 到 storageCfg
	storageCfg.AllowedTypes = cfg.Storage.AllowedTypes
	storageCfg.MaxFileSize = cfg.Storage.MaxFileSizeMB * 1024 * 1024 // 转换为 Bytes

	storageInstance, storageErr := storage.NewStorage(storageCfg)
	if storageErr != nil {
		log.Fatalf("Storage 初始化失败: %v", storageErr)
	}
	log.Info("Storage 实例创建完成", appLogger.WithField("type", storageCfg.Type))

	// 5. 初始化 Redis 连接
	var redisClient *redis.Client // Declare redisClient
	// 检查 Redis 是否配置
	if cfg.Redis.Host != "" && cfg.Redis.Port > 0 {
		redisClient = redis.NewClient(&redis.Options{
			Addr:         fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
			Password:     cfg.Redis.Password,
			DB:           cfg.Redis.DB,
			PoolSize:     cfg.Redis.PoolSize,
			MinIdleConns: cfg.Redis.MinIdleConns,
			DialTimeout:  time.Duration(cfg.Redis.DialTimeout) * time.Second,
			ReadTimeout:  time.Duration(cfg.Redis.ReadTimeout) * time.Second,
			WriteTimeout: time.Duration(cfg.Redis.WriteTimeout) * time.Second,
		})
		_, redisErr := redisClient.Ping(context.Background()).Result()
		if redisErr != nil {
			log.Warn("Redis 连接失败, 将禁用 Redis 相关功能 (如验证码Redis存储)", appLogger.WithError(redisErr))
			redisClient = nil // Set client to nil on connection failure
		} else {
			log.Info("Redis 连接成功")
		}
	} else {
		log.Warn("Redis 未配置, 相关功能 (如验证码Redis存储) 可能不可用")
		redisClient = nil // Ensure client is nil if not configured
	}

	// 6. 创建 Managers
	var repoManager *repository.RepositoryManager
	if gormDB != nil {
		repoManager = repository.NewRepositoryManager(gormDB)
	} else {
		log.Error("无法创建 RepositoryManager，因为 gormDB 为 nil")
		os.Exit(1)
	}

	// --- Initialize AuditLog Components (as per plan Step 7) ---
	auditLogRepo := repository.NewAuditLogRepository(gormDB)
	log.Info("AuditLogRepository (自定义) 创建完成")
	auditLogServiceInstance := service.NewAuditLogService(auditLogRepo, log) // Use the global logger `log`
	log.Info("AuditLogService (自定义) 创建完成")
	// --- End Initialize AuditLog Components ---

	// 7. 创建 ServiceManager (修改：传入 sqlDB 和 storageInstance)
	serviceManager := service.NewServiceManager(repoManager, cfg, sqlDB, storageInstance)
	serviceManager.SetLogger(log)
	log.Info("ServiceManager 创建完成")

	// 8. 创建核心服务 (CacheService, CaptchaService)
	cacheService := service.NewCacheService(serviceManager, redisClient, cfg)
	log.Info("CacheService 创建完成")
	captchaService, captchaErr := service.NewCaptchaService(log, cfg, cacheService)
	if captchaErr != nil {
		log.Fatalf("CaptchaService 初始化失败: %v", captchaErr)
	}
	log.Info("CaptchaService 创建完成")

	// 9. 将 CaptchaService 设置回 ServiceManager
	serviceManager.SetCaptchaService(captchaService)
	log.Info("CaptchaService 已注入 ServiceManager")

	// REMOVED incorrect AuditLogService retrieval from serviceManager
	// log.Info("AuditLogService 获取完成 (如果可用)")

	// 11. 创建 ControllerManager
	controllerManager := controller.NewControllerManager(serviceManager)
	controllerManager.SetLogger(log)
	log.Info("ControllerManager 创建完成")

	// 12. 创建 Controllers (ControllerManager 内部会获取服务)
	authController := controller.NewAuthControllerImpl(controllerManager, cfg, serviceManager.GetAuthService(), auditLogServiceInstance)
	captchaController := controller.NewCaptchaControllerImpl(controllerManager, cfg, cacheService)
	roleController := controller.NewRoleControllerImpl(controllerManager)
	menuController := controller.NewMenuControllerImpl(controllerManager)
	userController := controller.NewUserControllerImpl(controllerManager)
	accountBookController := controller.NewAccountBookControllerImpl(controllerManager)
	employeeController := controller.NewEmployeeControllerImpl(controllerManager)
	orgNodeController := controller.NewOrganizationNodeControllerImpl(controllerManager)
	userAccountBookController := controller.NewUserAccountBookControllerImpl(controllerManager)
	dictController := controller.NewDictionaryControllerImpl(controllerManager)
	systemParameterController := controller.NewSystemParameterControllerImpl(controllerManager)
	sqlToolController := controller.NewSQLToolController(controllerManager, cfg)
	fileController := controller.NewFileControllerImpl(controllerManager)
	finTaxRateController := controller.NewFinTaxRateControllerImpl(controllerManager)
	finExchangeRateController := controller.NewFinExchangeRateControllerImpl(controllerManager)
	finCurrencyController := controller.NewFinCurrencyControllerImpl(controllerManager)
	fiscalPeriodController := controller.NewFiscalPeriodControllerImpl(controllerManager)
	wmsLocationController := controller.NewWmsLocationControllerImpl(controllerManager)
	mtlItemController := controllerManager.GetMtlItemController()
	sysCodeRuleController := controller.NewSysCodeRuleControllerImpl(controllerManager)
	crmCustomerController := controller.NewCrmCustomerController(controllerManager)
	scmSupplierController := controller.NewScmSupplierController(controllerManager)
	wmsInboundNotificationController := controller.NewWmsInboundNotificationControllerImpl(controllerManager)
	wmsReceivingRecordController := controller.NewWmsReceivingRecordControllerImpl(controllerManager)
	wmsPutawayTaskController := controller.NewWmsPutawayTaskControllerImpl(controllerManager)
	wmsBlindReceivingValidationController := controller.NewWmsBlindReceivingValidationController(controllerManager)
	// WMS配置Controller
	wmsBlindReceivingConfigController := controllerManager.GetWmsBlindReceivingConfigController()

	// 出库流程Controller
	wmsOutboundNotificationController := controllerManager.GetWmsOutboundNotificationController()
	wmsPickingTaskController := controllerManager.GetWmsPickingTaskController()
	wmsInventoryAllocationController := controllerManager.GetWmsInventoryAllocationController()
	wmsShipmentController := controllerManager.GetWmsShipmentController()

	// 库存管理模块Controller
	// wmsInventoryQueryController := controller.NewWmsInventoryQueryController(controllerManager.GetServiceManager().GetWmsInventoryQueryService()) // TODO: 暂时注释掉
	// wmsInventoryAdjustmentController := controller.NewWmsInventoryAdjustmentController(controllerManager.GetServiceManager().GetWmsInventoryAdjustmentService()) // TODO: 暂时注释掉
	wmsInventoryMovementController := controller.NewWmsInventoryMovementController(controllerManager.GetServiceManager().GetWmsInventoryMovementService())
	wmsCycleCountController := controller.NewWmsCycleCountControllerImpl(controllerManager)
	// wmsInventoryAlertController := controller.NewWmsInventoryAlertController(controllerManager.GetServiceManager().GetWmsInventoryAlertService()) // TODO: 暂时注释掉

	log.Info("所有标准 Controller 实例创建完成")

	licenseController := controllerManager.GetLicenseController()
	log.Info("LicenseController 创建完成")

	// --- Initialize AuditLogController (as per plan Step 7) ---
	// Ensure auditLogServiceInstance is already initialized earlier in main.go
	auditLogController := controller.NewAuditLogControllerImpl(controllerManager, auditLogServiceInstance)
	log.Info("AuditLogController (自定义) 创建完成")
	// --- End Initialize AuditLogController ---

	authMiddleware := middleware.NewAuthMiddleware(serviceManager.GetTokenService(), log, cfg)
	log.Info("AuthMiddleware 创建完成")

	// --- Initialize AuditMiddleware (as per plan Step 7) ---
	var auditMiddlewareInstance *middleware.AuditMiddleware
	if auditLogServiceInstance != nil {
		auditMiddlewareInstance = middleware.NewAuditMiddleware(auditLogServiceInstance)
		log.Info("AuditMiddleware (自定义) 实例创建完成")
	} else {
		log.Warn("AuditLogService is nil, AuditMiddleware instance not created, will not be functional.")
	}
	// --- End Initialize AuditMiddleware ---

	// 13. 初始化 Iris 应用
	app := iris.New()
	// app.Logger().SetLevel(cfg.App.IrisLogLevel) // 步骤 2.3: 移除或注释掉，如果决定完全替换 Iris logger

	// 步骤 2.3: 调整中间件注册顺序
	// 1. 恢复中间件 - 保持最先
	app.Use(recover.New())

	// 2. IP 中间件 (如果存在且需要全局) - 保持靠前
	// +++ 初始化并使用 IP 中间件 +++
	ipMiddleware := middleware.NewIPMiddleware()
	app.Use(ipMiddleware.Serve)
	log.Info("IPMiddleware 全局注册完成")
	// +++ 结束 IP 中间件 +++

	// 3. 注册自定义的 LoggerMiddleware (替换或补充 Iris Logger)
	// 移除 Iris 内置 logger.New()
	// app.Use(logger.New()) // <--- 步骤 2.3: 移除或注释掉这一行

	// 使用我们实例化的 LoggerMiddleware
	if cfg.App.Log.Output == "console" || strings.Contains(cfg.App.Log.Output, "both") { // 假设 console 或 both 时使用
		app.Use(loggerMiddlewareInstance)
		log.Info("自定义 LoggerMiddleware (控制台) 全局注册完成")
	}

	// 13.1. 注册 LicenseCheckMiddleware 全局中间件 (新增)

	// 14. 设置模板引擎 (如果需要)
	// ...

	// 15. 配置静态文件服务 (例如 /static -> ./static)
	// 修正：使用 iris.Dir 提供文件系统服务
	app.HandleDir("/static", iris.Dir("./static"))
	// 为上传的文件提供服务 (如果使用本地存储)
	if cfg.Storage.Type == storage.STORAGE_TYPE_LOCAL {
		// 确保 uploadPath 是相对于项目根目录的正确路径
		uploadPath := cfg.Storage.Local.Path
		if uploadPath == "" {
			uploadPath = "./uploads" // 提供一个默认值
		}
		// 新的计算方式：添加 /static 前缀
		relativePathPart := strings.TrimPrefix(strings.TrimPrefix(uploadPath, "."), "/")
		urlPath := "/static/" + relativePathPart
		// 确保 URL 路径规范，避免双斜杠等问题
		urlPath = "/" + strings.TrimPrefix(strings.ReplaceAll(urlPath, "//", "/"), "/")

		log.Infof("为本地上传文件配置静态服务: URL 路径 '%s' -> 物理路径 '%s'", urlPath, uploadPath)
		app.HandleDir(urlPath, iris.Dir(uploadPath), iris.DirOptions{IndexName: "/", Compress: true}) // <<< 添加 Compress: true
	}

	// 16. 设置路由
	// <<< 将所有 Controller 和 Middleware 传递给 SetupRoutes >>>
	router.SetupRoutes(
		app,
		repoManager,
		authController,
		captchaController,
		roleController,
		menuController,
		userController,
		accountBookController,
		orgNodeController,
		userAccountBookController,
		dictController,
		employeeController,
		authMiddleware,
		systemParameterController,
		sqlToolController,
		fileController,
		licenseController,
		auditLogController,
		auditMiddlewareInstance,
		fiscalPeriodController,
		finCurrencyController,
		finTaxRateController,
		finExchangeRateController,
		wmsLocationController,
		mtlItemController,
		sysCodeRuleController,
		crmCustomerController,
		scmSupplierController,
		wmsInboundNotificationController,
		wmsReceivingRecordController,
		wmsPutawayTaskController,
		wmsBlindReceivingConfigController,
		wmsBlindReceivingValidationController,
		wmsOutboundNotificationController,
		wmsPickingTaskController,
		wmsInventoryAllocationController,
		wmsShipmentController,
		// 库存管理模块控制器
		nil, // wmsInventoryQueryController, // TODO: 暂时注释掉
		nil, // wmsInventoryAdjustmentController, // TODO: 暂时注释掉
		wmsInventoryMovementController,
		wmsCycleCountController,
		nil, // wmsInventoryAlertController, // TODO: 暂时注释掉
	)
	log.Info("路由设置完成")

	// 17. 启动服务器
	serverAddr := ":" + strconv.Itoa(cfg.App.Port)
	log.Info("服务器启动中，监听地址: ", serverAddr)

	// ==========================================================
	// 在这里启动调度器
	startScheduler(serviceManager)
	// ==========================================================

	// 使用 go func 启动服务器，以便进行优雅关闭
	go func() {
		if err := app.Run(iris.Addr(serverAddr), iris.WithoutServerError(iris.ErrServerClosed)); err != nil {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 18. 实现优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info("收到关闭信号，开始优雅关闭服务器...")

	// 设置超时上下文
	ctxShutdown, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 关闭 Iris 应用
	if err := app.Shutdown(ctxShutdown); err != nil {
		log.Errorf("服务器关闭失败: %v", err)
	}

	// 关闭数据库连接
	if sqlDB != nil {
		if err := sqlDB.Close(); err != nil {
			log.Errorf("数据库连接关闭失败: %v", err)
		}
	}

	// 关闭 Redis 连接
	if redisClient != nil {
		if err := redisClient.Close(); err != nil {
			log.Errorf("Redis 连接关闭失败: %v", err)
		}
	}

	log.Info("服务器已优雅关闭")
}

// startScheduler 初始化并启动后台定时任务
func startScheduler(sm *service.ServiceManager) {
	log := appLogger.GetLogger()
	log.Info("正在检查并初始化后台定时任务...")

	// --- 汇率同步任务 ---
	go func() {
		// 1. 获取服务
		paramService := sm.GetSystemParameterService()
		rateService := sm.FinExchangeRateService()

		// 2. 检查功能是否启用
		enabledStr, err := paramService.GetParameterValueByKey(context.Background(), "ExchangeRate.AutoFetch.Enabled")
		if err != nil {
			log.Error("无法获取 'ExchangeRate.AutoFetch.Enabled' 参数，跳过汇率同步任务", appLogger.WithError(err))
			return
		}
		enabled, err := strconv.ParseBool(enabledStr)
		if err != nil {
			log.Error("无法将 'ExchangeRate.AutoFetch.Enabled' 参数值解析为布尔值", appLogger.WithError(err), appLogger.WithField("value", enabledStr))
			return
		}

		if !enabled {
			log.Info("自动汇率同步任务已禁用，跳过初始化")
			return
		}

		// 3. 初始化并启动 Cron 调度器
		// 使用秒级精度（可选）
		c := cron.New(cron.WithSeconds())
		// cron spec: "0 0 2 * * *" 表示每天凌晨2:00:00执行
		spec := "0 0 2 * * *"
		jobID, err := c.AddFunc(spec, func() {
			log.Info("开始执行预定的汇率同步任务...")
			// 使用一个新的后台上下文执行任务
			if err := rateService.SyncRates(context.Background()); err != nil {
				log.Error("预定的汇率同步任务执行失败", appLogger.WithError(err))
			} else {
				log.Info("预定的汇率同步任务执行成功")
			}
		})

		if err != nil {
			log.Error("添加汇率同步定时任务失败", appLogger.WithError(err))
			return
		}
		c.Start()
		log.Info("汇率同步定时任务已启动", appLogger.WithField("spec", spec), appLogger.WithField("jobID", jobID))

		// 4. 应用启动时，立即异步执行一次，以便快速获取最新数据
		log.Info("应用启动，立即执行一次汇率同步任务...")
		go func() {
			if err := rateService.SyncRates(context.Background()); err != nil {
				log.Error("启动时汇率同步任务执行失败", appLogger.WithError(err))
			} else {
				log.Info("启动时汇率同步任务执行成功")
			}
		}()
	}()

	// 在这里可以添加其他后台任务...
}
