package repository

import (
	"context"
	stdErrors "errors" // Import standard errors
	"reflect"          // Import reflect package

	"backend/internal/model/entity"
	apperrors "backend/pkg/errors" // Use alias
	"backend/pkg/logger"
	"backend/pkg/response"

	"gorm.io/gorm"
)

// UserAccountBookRepository 用户账套关联仓库接口
type UserAccountBookRepository interface {
	// Exists 检查用户是否有权访问指定的账套
	Exists(ctx context.Context, userID uint, accountBookID uint) (bool, error)

	// FindByUser 查询用户拥有的所有账套ID
	FindByUser(ctx context.Context, userID uint) ([]uint, error)

	// SetUserAccountBooks 批量设置用户可访问的账套 (覆盖式)
	SetUserAccountBooks(ctx context.Context, userID uint, accountBookIDs []uint) error

	// Create 创建一个新的用户账套关联记录
	Create(ctx context.Context, userAccountBook *entity.UserAccountBook) error

	// DeleteByUserIDAndAccountBookID 删除指定用户和账套的关联记录
	DeleteByUserIDAndAccountBookID(ctx context.Context, userID uint, accountBookID uint) error

	// FindByUserIDReturnModels 查询用户拥有的所有关联模型 (与 FindByUser 不同)
	FindByUserIDReturnModels(ctx context.Context, userID uint) ([]*entity.UserAccountBook, error)

	// FindUserIDsByAccountBookID 分页查询拥有指定账套的用户ID列表及总数
	FindUserIDsByAccountBookID(ctx context.Context, accountBookID uint, page int, pageSize int) ([]uint, int64, error)

	// CountByUserIDAndAccountBookID 计算指定用户和账套的关联记录数量
	CountByUserIDAndAccountBookID(ctx context.Context, userID uint, accountBookID uint) (int64, error)

	// FindUserAccountBooks 查询用户关联的账本
	FindUserAccountBooks(ctx context.Context, userID uint, sortInfos []response.SortInfo) ([]*entity.AccountBook, error)

	// FindAccountBookUsers 查询账本关联的用户
	FindAccountBookUsers(ctx context.Context, accountBookID uint, sortInfos []response.SortInfo) ([]*entity.User, error)

	// FindUserAccountBookAssociation 查询用户和账本的关联记录
	FindUserAccountBookAssociation(ctx context.Context, userID uint, accountBookID uint) (*entity.UserAccountBook, error)
}

// userAccountBookRepository 用户账套关联仓库实现
type userAccountBookRepository struct {
	db *gorm.DB
}

// NewUserAccountBookRepository 创建用户账套关联仓库
func NewUserAccountBookRepository(db *gorm.DB) UserAccountBookRepository {
	return &userAccountBookRepository{
		db: db,
	}
}

// getDB 获取带上下文的 DB 连接
func (r *userAccountBookRepository) getDB(ctx context.Context) *gorm.DB {
	return r.db.WithContext(ctx)
}

// Exists 检查用户是否有权访问指定的账套
func (r *userAccountBookRepository) Exists(ctx context.Context, userID uint, accountBookID uint) (bool, error) {
	var count int64
	result := r.getDB(ctx).Model(&entity.UserAccountBook{}).
		Where("user_id = ? AND account_book_id = ?", userID, accountBookID).
		Count(&count)

	if result.Error != nil {
		// 添加日志和错误包装
		logger.WithContext(ctx).WithError(result.Error).Errorf("检查用户账套关联是否存在失败 (UserID: %d, AccountBookID: %d)", userID, accountBookID)
		return false, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "检查用户账套关联是否存在失败")
	}
	return count > 0, nil
}

// FindByUser 查询用户拥有的所有账套ID
func (r *userAccountBookRepository) FindByUser(ctx context.Context, userID uint) ([]uint, error) {
	var accountBookIDs []uint
	result := r.getDB(ctx).Model(&entity.UserAccountBook{}).
		Where("user_id = ?", userID).
		Pluck("account_book_id", &accountBookIDs)

	if result.Error != nil {
		// 使用 stdErrors.Is
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return []uint{}, nil // Not an error if no records found
		}
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询用户账套ID列表失败 (UserID: %d)", userID)
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询用户账套列表失败")
	}
	return accountBookIDs, nil
}

// SetUserAccountBooks 批量设置用户可访问的账套 (覆盖式)
func (r *userAccountBookRepository) SetUserAccountBooks(ctx context.Context, userID uint, accountBookIDs []uint) error {
	db := r.getDB(ctx) // 获取可能包含事务的 DB 实例

	// 1. Delete existing associations for the user
	if err := db.Where("user_id = ?", userID).Delete(&entity.UserAccountBook{}).Error; err != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(err).Errorf("删除用户旧账套关联失败 (UserID: %d)", userID)
		return apperrors.WrapError(err, apperrors.CODE_DATA_DELETE_FAILED, "删除用户旧账套关联失败")
	}

	// 2. Insert new associations if the list is not empty
	if len(accountBookIDs) > 0 {
		newUserAccountBooks := make([]entity.UserAccountBook, 0, len(accountBookIDs)) // Optimize slice allocation
		processedIDs := make(map[uint]struct{}, len(accountBookIDs))
		for _, abID := range accountBookIDs {
			// 添加重复和无效 ID 检查
			if abID == 0 {
				logger.WithContext(ctx).Warnf("SetUserAccountBooks: 跳过无效的账套ID 0 (UserID: %d)", userID)
				continue
			}
			if _, exists := processedIDs[abID]; exists {
				logger.WithContext(ctx).Warnf("SetUserAccountBooks: 跳过重复的账套ID %d (UserID: %d)", abID, userID)
				continue
			}
			newUserAccountBooks = append(newUserAccountBooks, entity.UserAccountBook{
				UserID:        userID,
				AccountBookID: abID,
			})
			processedIDs[abID] = struct{}{}
		}
		// 检查 newUserAccountBooks 是否为空，避免空插入
		if len(newUserAccountBooks) > 0 {
			if err := db.Create(&newUserAccountBooks).Error; err != nil {
				// 添加日志记录
				logger.WithContext(ctx).WithError(err).Errorf("添加用户新账套关联失败 (UserID: %d)", userID)
				return apperrors.WrapError(err, apperrors.CODE_DATA_CREATE_FAILED, "添加用户新账套关联失败")
			}
		}
	}
	return nil // 操作成功
}

// Create 创建一个新的用户账套关联记录
func (r *userAccountBookRepository) Create(ctx context.Context, userAccountBook *entity.UserAccountBook) error {
	result := r.getDB(ctx).Create(userAccountBook)
	if result.Error != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("创建用户账套关联失败 (UserID: %d, AccountBookID: %d)", userAccountBook.UserID, userAccountBook.AccountBookID)
		return apperrors.WrapError(result.Error, apperrors.CODE_DATA_CREATE_FAILED, "创建用户账套关联失败")
	}
	return nil
}

// DeleteByUserIDAndAccountBookID 删除指定用户和账套的关联记录
func (r *userAccountBookRepository) DeleteByUserIDAndAccountBookID(ctx context.Context, userID uint, accountBookID uint) error {
	result := r.getDB(ctx).Where("user_id = ? AND account_book_id = ?", userID, accountBookID).Delete(&entity.UserAccountBook{})
	if result.Error != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("删除用户账套关联失败 (UserID: %d, AccountBookID: %d)", userID, accountBookID)
		return apperrors.WrapError(result.Error, apperrors.CODE_DATA_DELETE_FAILED, "删除用户账套关联失败")
	}
	if result.RowsAffected == 0 {
		logger.WithContext(ctx).Warnf("未找到要删除的用户账套关联记录 (UserID: %d, AccountBookID: %d)", userID, accountBookID)
		// 根据业务需求，可以返回 Not Found 错误
		// return apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "未找到要删除的用户账套关联记录")
	}
	return nil
}

// FindByUserIDReturnModels 查询用户拥有的所有关联模型
func (r *userAccountBookRepository) FindByUserIDReturnModels(ctx context.Context, userID uint) ([]*entity.UserAccountBook, error) {
	var userAccountBooks []*entity.UserAccountBook
	result := r.getDB(ctx).Where("user_id = ?", userID).Find(&userAccountBooks)
	if result.Error != nil {
		// 使用 stdErrors.Is
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			return []*entity.UserAccountBook{}, nil // 未找到记录不视为错误
		}
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询用户账套关联模型列表失败 (UserID: %d)", userID)
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询用户账套关联模型列表失败")
	}
	return userAccountBooks, nil
}

// FindUserIDsByAccountBookID 分页查询拥有指定账套的用户ID列表及总数
func (r *userAccountBookRepository) FindUserIDsByAccountBookID(ctx context.Context, accountBookID uint, page int, pageSize int) ([]uint, int64, error) {
	var userIDs []uint
	var total int64
	db := r.getDB(ctx).Model(&entity.UserAccountBook{}).Where("account_book_id = ?", accountBookID)

	// 1. 计算总数
	if err := db.Count(&total).Error; err != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(err).Errorf("查询用户账套关联总数失败 (AccountBookID: %d)", accountBookID)
		return nil, 0, apperrors.WrapError(err, apperrors.CODE_DATA_QUERY_FAILED, "查询用户账套关联总数失败")
	}

	// 如果总数为0，或请求的页码超出范围，提前返回
	if total == 0 || (page > 0 && pageSize > 0 && (page-1)*pageSize >= int(total)) {
		return []uint{}, total, nil
	}

	// 2. 查询当前页的用户ID
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10 // 或者使用默认值
	}
	offset := (page - 1) * pageSize

	result := db.Offset(offset).Limit(pageSize).Pluck("user_id", &userIDs)
	if result.Error != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("分页查询用户ID列表失败 (AccountBookID: %d, Page: %d, Size: %d)", accountBookID, page, pageSize)
		return nil, total, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "分页查询用户ID列表失败")
	}

	return userIDs, total, nil
}

// CountByUserIDAndAccountBookID 计算指定用户和账套的关联记录数量
func (r *userAccountBookRepository) CountByUserIDAndAccountBookID(ctx context.Context, userID uint, accountBookID uint) (int64, error) {
	var count int64
	result := r.getDB(ctx).Model(&entity.UserAccountBook{}).
		Where("user_id = ? AND account_book_id = ?", userID, accountBookID).
		Count(&count)

	if result.Error != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询用户账套关联数量失败 (UserID: %d, AccountBookID: %d)", userID, accountBookID)
		return 0, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询用户账套关联数量失败")
	}
	return count, nil
}

// FindUserAccountBooks 查询用户关联的账本
func (r *userAccountBookRepository) FindUserAccountBooks(ctx context.Context, userID uint, sortInfos []response.SortInfo) ([]*entity.AccountBook, error) {
	var accountBooks []*entity.AccountBook
	db := r.getDB(ctx).Model(&entity.UserAccountBook{}).
		Select("ab.*, sys_user_account_book.balance"). // 确保选择了 balance
		Joins("INNER JOIN sys_account_book ab ON ab.id = sys_user_account_book.account_book_id").
		Where("sys_user_account_book.user_id = ?", userID).
		Where("ab.deleted_at IS NULL") // 添加软删除过滤条件

	// 获取 AccountBook 实体的允许排序字段
	entityType := reflect.TypeOf(entity.AccountBook{})
	allowedColumns := getSortableColumns(entityType)
	// 添加表别名 'ab.'
	aliasedAllowedColumns := make(map[string]string)
	for k, v := range allowedColumns {
		aliasedAllowedColumns[k] = "ab." + v
	}
	db = applySortInfos(db, sortInfos, aliasedAllowedColumns) // 传递 allowedColumns

	result := db.Scan(&accountBooks)
	if result.Error != nil {
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询用户账本失败 (UserID: %d)", userID)
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询用户账本失败")
	}
	return accountBooks, nil
}

// FindAccountBookUsers 查询账本关联的用户
func (r *userAccountBookRepository) FindAccountBookUsers(ctx context.Context, accountBookID uint, sortInfos []response.SortInfo) ([]*entity.User, error) {
	var users []*entity.User
	db := r.getDB(ctx).Model(&entity.UserAccountBook{}).
		Select("u.*, sys_user_account_book.balance"). // 确保选择了 balance
		Joins("INNER JOIN sys_user u ON u.id = sys_user_account_book.user_id").
		Where("sys_user_account_book.account_book_id = ?", accountBookID)

	// 获取 User 实体的允许排序字段
	entityType := reflect.TypeOf(entity.User{})
	allowedColumns := getSortableColumns(entityType)
	// 添加表别名 'u.'
	aliasedAllowedColumns := make(map[string]string)
	for k, v := range allowedColumns {
		aliasedAllowedColumns[k] = "u." + v
	}
	db = applySortInfos(db, sortInfos, aliasedAllowedColumns) // 传递 allowedColumns

	result := db.Scan(&users)
	if result.Error != nil {
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询账本用户失败 (AccountBookID: %d)", accountBookID)
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询账本用户失败")
	}
	return users, nil
}

// FindUserAccountBookAssociation 查询用户和账本的关联记录
func (r *userAccountBookRepository) FindUserAccountBookAssociation(ctx context.Context, userID uint, accountBookID uint) (*entity.UserAccountBook, error) {
	var assoc entity.UserAccountBook
	result := r.getDB(ctx).
		Where("user_id = ? AND account_book_id = ?", userID, accountBookID).
		First(&assoc)

	if result.Error != nil {
		// 使用 stdErrors.Is
		if stdErrors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 添加日志记录 (可选, Debug 级别)
			logger.WithContext(ctx).Debugf("用户账套关联记录不存在 (UserID: %d, AccountBookID: %d)", userID, accountBookID)
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "用户账套关联不存在").WithCause(result.Error) // 添加 WithCause
		}
		// 添加日志记录
		logger.WithContext(ctx).WithError(result.Error).Errorf("查询用户账套关联失败 (UserID: %d, AccountBookID: %d)", userID, accountBookID)
		return nil, apperrors.WrapError(result.Error, apperrors.CODE_DATA_QUERY_FAILED, "查询用户账套关联失败")
	}
	return &assoc, nil
}
