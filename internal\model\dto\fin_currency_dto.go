package dto



// FinCurrencyPageReq 分页查询请求参数
type FinCurrencyPageReq struct {
	Code      string `json:"code" form:"code"`           // 币种代码
	Name      string `json:"name" form:"name"`           // 币种名称
	IsEnabled *bool  `json:"isEnabled" form:"isEnabled"` // 是否启用
	Page      int    `json:"page" form:"page"`           // 页码
	PageSize  int    `json:"pageSize" form:"pageSize"`   // 每页数量
}

// FinCurrencyCreateReq 创建请求参数
type FinCurrencyCreateReq struct {
	TenantDTO
	Code      string `json:"code" binding:"required,max=3"`
	Name      string `json:"name" binding:"required,max=50"`
	Symbol    string `json:"symbol" binding:"max=10"`
	Precision *int   `json:"precision" binding:"omitempty,gte=0,lte=8"`
	IsEnabled *bool  `json:"isEnabled" binding:"omitempty"`
}

// FinCurrencyUpdateReq 更新请求参数
type FinCurrencyUpdateReq struct {
	TenantDTO
	Code      string `json:"code" binding:"required,max=3"`
	Name      string `json:"name" binding:"required,max=50"`
	Symbol    string `json:"symbol" binding:"max=10"`
	Precision *int   `json:"precision" binding:"omitempty,gte=0,lte=8"`
	IsEnabled *bool  `json:"isEnabled" binding:"omitempty"`
}
