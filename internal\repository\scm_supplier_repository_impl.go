package repository

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
	"context"

	"gorm.io/gorm"
)

// ScmSupplierRepository 供应商仓库接口
type ScmSupplierRepository interface {
	BaseRepository[entity.ScmSupplier, uint]

	// 供应商专用方法
	FindBySupplierCode(ctx context.Context, supplierCode string) (*entity.ScmSupplier, error)
	FindByBusinessLicense(ctx context.Context, businessLicense string) (*entity.ScmSupplier, error)
	FindByTaxNumber(ctx context.Context, taxNumber string) (*entity.ScmSupplier, error)
	GetPage(ctx context.Context, req *dto.ScmSupplierQueryReq) (*response.PageResult, error)
	FindActiveSimpleList(ctx context.Context) ([]*entity.ScmSupplier, error)
	GetSupplierSummary(ctx context.Context) (*entity.ScmSupplier, error) // 返回统计数据用的结构体，这里暂时用entity

	// 供应商特有查询方法
	FindByQualityRating(ctx context.Context, qualityRating string) ([]*entity.ScmSupplier, error)
	FindBySupplierLevel(ctx context.Context, supplierLevel string) ([]*entity.ScmSupplier, error)
	FindKeySuppliers(ctx context.Context) ([]*entity.ScmSupplier, error)

	// 供应商联系人相关方法
	FindContactsBySupplierID(ctx context.Context, supplierID uint) ([]*entity.ScmSupplierContact, error)
	FindContactByID(ctx context.Context, contactID uint) (*entity.ScmSupplierContact, error)
	FindPrimaryContactBySupplierID(ctx context.Context, supplierID uint) (*entity.ScmSupplierContact, error)
	FindContactsByRole(ctx context.Context, supplierID uint, role string) ([]*entity.ScmSupplierContact, error)
	CreateContact(ctx context.Context, contact *entity.ScmSupplierContact) error
	UpdateContact(ctx context.Context, contact *entity.ScmSupplierContact) error
	DeleteContact(ctx context.Context, contactID uint) error
	SetPrimaryContact(ctx context.Context, supplierID uint, contactID uint) error
}

// scmSupplierRepositoryImpl 供应商仓库实现
type scmSupplierRepositoryImpl struct {
	BaseRepositoryImpl[entity.ScmSupplier, uint]
}

// NewScmSupplierRepository 创建供应商仓库
func NewScmSupplierRepository(db *gorm.DB) ScmSupplierRepository {
	return &scmSupplierRepositoryImpl{
		BaseRepositoryImpl: BaseRepositoryImpl[entity.ScmSupplier, uint]{db: db},
	}
}

// FindBySupplierCode 根据供应商编码查找供应商 (自动处理账套scope)
func (r *scmSupplierRepositoryImpl) FindBySupplierCode(ctx context.Context, supplierCode string) (*entity.ScmSupplier, error) {
	conditions := []QueryCondition{
		NewEqualCondition("supplier_code", supplierCode),
	}
	return r.FindOneByCondition(ctx, conditions)
}

// FindByBusinessLicense 根据营业执照号查找供应商 (自动处理账套scope)
func (r *scmSupplierRepositoryImpl) FindByBusinessLicense(ctx context.Context, businessLicense string) (*entity.ScmSupplier, error) {
	conditions := []QueryCondition{
		NewEqualCondition("business_license", businessLicense),
	}
	return r.FindOneByCondition(ctx, conditions)
}

// FindByTaxNumber 根据税务登记号查找供应商 (自动处理账套scope)
func (r *scmSupplierRepositoryImpl) FindByTaxNumber(ctx context.Context, taxNumber string) (*entity.ScmSupplier, error) {
	conditions := []QueryCondition{
		NewEqualCondition("tax_number", taxNumber),
	}
	return r.FindOneByCondition(ctx, conditions)
}

// GetPage 使用现有分页机制实现分页查询 (参考CrmCustomer实现)
func (r *scmSupplierRepositoryImpl) GetPage(ctx context.Context, req *dto.ScmSupplierQueryReq) (*response.PageResult, error) {
	var conditions []QueryCondition

	if req.SupplierCode != "" {
		conditions = append(conditions, NewLikeCondition("supplier_code", req.SupplierCode))
	}
	if req.SupplierName != "" {
		conditions = append(conditions, NewLikeCondition("supplier_name", req.SupplierName))
	}
	if req.SupplierType != "" {
		conditions = append(conditions, NewEqualCondition("supplier_type", req.SupplierType))
	}
	if req.Industry != "" {
		conditions = append(conditions, NewLikeCondition("industry", req.Industry))
	}
	if req.SupplierLevel != "" {
		conditions = append(conditions, NewEqualCondition("supplier_level", req.SupplierLevel))
	}
	if req.Status != "" {
		conditions = append(conditions, NewEqualCondition("status", req.Status))
	}
	if req.ProcurementRepresentativeId != nil {
		conditions = append(conditions, NewEqualCondition("purchaser_id", *req.ProcurementRepresentativeId))
	}
	if req.IsKeySupplier != nil {
		conditions = append(conditions, NewEqualCondition("is_key_supplier", *req.IsKeySupplier))
	}
	if req.QualityRating != "" {
		conditions = append(conditions, NewEqualCondition("quality_rating", req.QualityRating))
	}
	if req.Country != "" {
		conditions = append(conditions, NewLikeCondition("country", req.Country))
	}
	if req.Province != "" {
		conditions = append(conditions, NewLikeCondition("province", req.Province))
	}
	if req.City != "" {
		conditions = append(conditions, NewLikeCondition("city", req.City))
	}

	// 使用BaseRepository的FindByPage方法 (自动处理账套scope)
	return r.FindByPage(ctx, &req.PageQuery, conditions)
}

// FindActiveSimpleList 查询活跃供应商简单列表
func (r *scmSupplierRepositoryImpl) FindActiveSimpleList(ctx context.Context) ([]*entity.ScmSupplier, error) {
	conditions := []QueryCondition{
		NewEqualCondition("status", "ACTIVE"),
	}

	sorts := []response.SortInfo{
		{Field: "supplier_code", Order: "asc"},
	}

	return r.FindByCondition(ctx, conditions, sorts)
}

// GetSupplierSummary 获取供应商统计摘要 (用于仪表板)
func (r *scmSupplierRepositoryImpl) GetSupplierSummary(ctx context.Context) (*entity.ScmSupplier, error) {
	// 这里应该返回统计数据，但由于VO的统计结构比较复杂
	// 暂时返回一个空的供应商实体，实际使用时需要在Service层进行具体的统计查询
	// 或者定义专门的统计结构体
	return &entity.ScmSupplier{}, nil
}

// FindByQualityRating 根据质量评级查找供应商
func (r *scmSupplierRepositoryImpl) FindByQualityRating(ctx context.Context, qualityRating string) ([]*entity.ScmSupplier, error) {
	conditions := []QueryCondition{
		NewEqualCondition("quality_rating", qualityRating),
	}
	sortInfos := []response.SortInfo{
		{Field: "supplier_name", Order: "asc"},
	}
	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindBySupplierLevel 根据供应商级别查找供应商
func (r *scmSupplierRepositoryImpl) FindBySupplierLevel(ctx context.Context, supplierLevel string) ([]*entity.ScmSupplier, error) {
	conditions := []QueryCondition{
		NewEqualCondition("supplier_level", supplierLevel),
	}
	sortInfos := []response.SortInfo{
		{Field: "supplier_name", Order: "asc"},
	}
	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindKeySuppliers 查找重点供应商
func (r *scmSupplierRepositoryImpl) FindKeySuppliers(ctx context.Context) ([]*entity.ScmSupplier, error) {
	conditions := []QueryCondition{
		NewEqualCondition("is_key_supplier", true),
		NewEqualCondition("status", "ACTIVE"),
	}
	sortInfos := []response.SortInfo{
		{Field: "supplier_level", Order: "asc"},
		{Field: "supplier_name", Order: "asc"},
	}
	return r.FindByCondition(ctx, conditions, sortInfos)
}

// FindContactsBySupplierID 查询指定供应商的所有联系人
func (r *scmSupplierRepositoryImpl) FindContactsBySupplierID(ctx context.Context, supplierID uint) ([]*entity.ScmSupplierContact, error) {
	var contacts []*entity.ScmSupplierContact
	err := r.GetDB(ctx).Where("supplier_id = ?", supplierID).Order("id ASC").Find(&contacts).Error
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询供应商联系人失败").WithCause(err)
	}
	return contacts, nil
}

// FindContactByID 根据ID查询联系人
func (r *scmSupplierRepositoryImpl) FindContactByID(ctx context.Context, contactID uint) (*entity.ScmSupplierContact, error) {
	var contact entity.ScmSupplierContact
	err := r.GetDB(ctx).First(&contact, contactID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "联系人不存在")
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询联系人失败").WithCause(err)
	}
	return &contact, nil
}

// FindPrimaryContactBySupplierID 查询指定供应商的主要联系人
func (r *scmSupplierRepositoryImpl) FindPrimaryContactBySupplierID(ctx context.Context, supplierID uint) (*entity.ScmSupplierContact, error) {
	var contact entity.ScmSupplierContact
	err := r.GetDB(ctx).Where("supplier_id = ?", supplierID).First(&contact).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, apperrors.NewDataError(apperrors.CODE_DATA_NOT_FOUND, "主要联系人不存在")
		}
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询主要联系人失败").WithCause(err)
	}
	return &contact, nil
}

// FindContactsByRole 根据角色查找联系人
func (r *scmSupplierRepositoryImpl) FindContactsByRole(ctx context.Context, supplierID uint, role string) ([]*entity.ScmSupplierContact, error) {
	var contacts []*entity.ScmSupplierContact
	query := r.GetDB(ctx).Where("supplier_id = ?", supplierID)

	err := query.Order("contact_name ASC").Find(&contacts).Error
	if err != nil {
		return nil, apperrors.NewDataError(apperrors.CODE_DATA_QUERY_FAILED, "查询角色联系人失败").WithCause(err)
	}
	return contacts, nil
}

// CreateContact 创建联系人
func (r *scmSupplierRepositoryImpl) CreateContact(ctx context.Context, contact *entity.ScmSupplierContact) error {
	err := r.GetDB(ctx).Create(contact).Error
	if err != nil {
		return apperrors.NewDataError(apperrors.CODE_DATA_CREATE_FAILED, "创建联系人失败").WithCause(err)
	}
	return nil
}

// UpdateContact 更新联系人
func (r *scmSupplierRepositoryImpl) UpdateContact(ctx context.Context, contact *entity.ScmSupplierContact) error {
	err := r.GetDB(ctx).Save(contact).Error
	if err != nil {
		return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "更新联系人失败").WithCause(err)
	}
	return nil
}

// DeleteContact 删除联系人 (软删除)
func (r *scmSupplierRepositoryImpl) DeleteContact(ctx context.Context, contactID uint) error {
	err := r.GetDB(ctx).Delete(&entity.ScmSupplierContact{}, contactID).Error
	if err != nil {
		return apperrors.NewDataError(apperrors.CODE_DATA_DELETE_FAILED, "删除联系人失败").WithCause(err)
	}
	return nil
}

// SetPrimaryContact 设置主要联系人 (事务处理)
func (r *scmSupplierRepositoryImpl) SetPrimaryContact(ctx context.Context, supplierID uint, contactID uint) error {
	return r.GetDB(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 将该供应商的所有联系人的主要标识设为false
		err := tx.Model(&entity.ScmSupplierContact{}).
			Where("supplier_id = ?", supplierID).
			Update("is_primary", false).Error
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "清除主要联系人标识失败").WithCause(err)
		}

		// 2. 将指定联系人设为主要联系人
		err = tx.Model(&entity.ScmSupplierContact{}).
			Where("id = ? AND supplier_id = ?", contactID, supplierID).
			Update("is_primary", true).Error
		if err != nil {
			return apperrors.NewDataError(apperrors.CODE_DATA_UPDATE_FAILED, "设置主要联系人失败").WithCause(err)
		}

		return nil
	})
}
