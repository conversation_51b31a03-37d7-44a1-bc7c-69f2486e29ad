<template>
  <div class="sql-tool-container">
    <el-alert
      type="error"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
    >
      <template #title>
        <strong>高风险操作警告</strong>
      </template>
      <p>直接执行 SQL 语句可能对数据库造成不可逆的损害，包括数据丢失、损坏或泄露。</p>
      <p>请确保你完全理解你将要执行的 SQL 语句及其潜在影响。仅在绝对必要时使用此工具。</p>
      <p><strong>所有操作都将被记录。滥用此功能将导致严厉处理。</strong></p>
    </el-alert>

    <el-card shadow="never" class="input-card">
      <template #header>
        <span>SQL 编辑器</span>
      </template>
      <!-- 使用 Codemirror 5 组件 -->
      <Codemirror
        v-model:value="sqlQuery"
        :options="cmOptions"
        border
        placeholder="在此输入 SQL 语句..."
        class="sql-editor-cm5"
      />
      <!-- 执行按钮 -->
      <div style="margin-top: 15px;">
        <el-button
          type="primary"
          @click="executeQuery"
          :loading="isLoading"
          icon="VideoPlay"
        >
          执行 SQL
        </el-button>
      </div>
    </el-card>

    <el-card shadow="never" class="results-card">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>执行结果</span>
          <el-button 
            v-if="resultType === 'QueryResult' && tableData.length > 0"
            type="success"
            icon="Download"
            @click="handleExportResults"
            size="small"
          >
            导出结果 (Excel)
          </el-button>
        </div>
      </template>
      <!-- 结果展示区域 -->
      <div v-loading="isLoading" class="results-area">
        <div v-if="error" class="error-display">
          <el-alert type="error" :title="'执行出错'" :closable="false">
            <pre>{{ error }}</pre>
          </el-alert>
        </div>
        <div v-else-if="resultType">
          <el-alert 
            v-if="resultMessage" 
            :title="resultType === 'QueryResult' ? '查询信息' : '执行结果'" 
            type="info" 
            show-icon 
            :closable="false"
            style="margin-bottom: 15px;"
          >
          {{ resultMessage }}
          </el-alert>

          <div v-if="resultType === 'QueryResult'">
            <div v-if="tableData.length > 0">
              <el-table :data="tableData" border stripe style="width: 100%" class="result-table" max-height="600px">
                 <el-table-column type="index" width="50" label="#" fixed="left" />
                 <el-table-column 
                  v-for="col in columns" 
                  :key="col" 
                  :prop="col" 
                  :label="col" 
                  min-width="150" 
                  show-overflow-tooltip
                />
              </el-table>
            </div>
            <el-empty v-else description="查询成功，但没有返回数据行" />
          </div>
        </div>
        <el-empty v-else-if="!isLoading" description="暂无执行结果" />
      </div>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElAlert, ElButton, ElTable, ElTableColumn, ElEmpty, ElMessage, vLoading, ElCard } from 'element-plus';
import { VideoPlay, Download } from '@element-plus/icons-vue';
import { executeSql, type SqlResponseData } from '@/api/admin';
import * as XLSX from 'xlsx'; // Import XLSX library

// --- CodeMirror 5 Imports ---
import Codemirror from 'codemirror-editor-vue3';
// language
import 'codemirror/mode/sql/sql.js';
// theme
import 'codemirror/theme/dracula.css'; // 选择一个主题，例如 dracula
import 'codemirror/lib/codemirror.css'; // 核心 CSS
// --- Optional Addons (示例) ---
// import 'codemirror/addon/display/placeholder.js';
// --------------------------

const sqlQuery = ref('');
const isLoading = ref(false);
const resultType = ref<'QueryResult' | 'ExecResult' | null>(null);
const columns = ref<string[]>([]);
const rows = ref<any[][]>([]);
const resultMessage = ref('');
const error = ref<string | null>(null);

// --- Date Formatting Helper (similar to user/index.vue) ---
const formatDateTimeInternal = (dateString: string | null | undefined): string => {
  if (!dateString || (typeof dateString === 'string' && dateString.startsWith('0001-01-01'))) {
    return '-';
  }
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      // console.warn('formatDateTimeInternal received invalid date string:', dateString);
      return String(dateString); // Return original if not a valid date string
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    // console.error('Error formatting date:', dateString, '\nError:', e);
    return String(dateString); // Return original on error
  }
};

const tryFormatDateTime = (value: any): string => {
  if (value === null || value === undefined) return '';
  const sValue = String(value);
  if (sValue.trim() === '') return '';

  // Avoid formatting if it's a number that doesn't look like a timestamp
  if (typeof value === 'number' && (value < 1000000000 || (value > 9999999999999 && value < 100000000000000))) { // common s or ms timestamp ranges
      // Heuristic: if it's a number like 2023, 20240101, don't format as full date.
      // Only attempt to format if it seems like a Unix timestamp (seconds or milliseconds)
      // or if it was explicitly a string from backend that looks like a date.
      // This check is tricky. For now, let's be conservative with numbers.
      // If it's a number but outside typical timestamp ranges, treat as non-date.
      if (!(value >= 1000000000 && value <= 9999999999) && // common seconds range
          !(value >= 1000000000000 && value <= 99999999999999)) { // common milliseconds range
          return sValue;
      }
  }
  
  // Regex to check for date-like patterns (YYYY-MM-DD, with or without time)
  // This also helps identify strings that are likely dates even if new Date() parses other things.
  const datePattern = /^(\d{4}-\d{2}-\d{2}([T ]\d{2}:\d{2}(:\d{2}(\.\d+)?)?(Z|([+-]\d{2}:\d{2}))?)?|\d{10,13})$/;

  if (typeof sValue === 'string' && !datePattern.test(sValue) && isNaN(Number(sValue))) {
    // If it's a string, doesn't match common date patterns, and is not a number string, return as is.
    return sValue;
  }

  const date = new Date(value); // Try to parse
  if (!isNaN(date.getTime())) {
    // Check if the original string value itself contains characters typical of a date string,
    // or if the original value was a number (likely timestamp).
    // This helps avoid incorrectly formatting numbers like "2023" into "2023-01-01 08:00:00"
    // if 'value' was the number 2023 and system timezone caused such parsing.
    const  isPotentiallyDateTime = (typeof value === 'number' && datePattern.test(sValue)) || (typeof value === 'string' && (sValue.includes('-') || sValue.includes('/') || sValue.includes(':') || sValue.toUpperCase().includes('T')));
    
    if (isPotentiallyDateTime || (typeof value === 'number' && datePattern.test(sValue))) { // Ensure numeric timestamps are also formatted
        return formatDateTimeInternal(sValue);
    }
  }
  return sValue; // Default to string conversion
};
// -------------------------------------------------------

// --- CodeMirror 5 Options ---
const cmOptions = ref({
  mode: 'text/x-sql', // SQL 模式
  theme: 'dracula', // 主题，与导入的 CSS 对应
  lineNumbers: true, // 显示行号
  lineWrapping: true, // 自动换行
  // placeholder: "在此输入 SQL 语句...", // 通过 props 传递了
  // 可以添加更多 CodeMirror 5 的配置项
  // indentWithTabs: true,
  // smartIndent: true,
  // tabSize: 4,
  // indentUnit: 4,
});
// -------------------------

// 将二维数组结果转换为适用于 el-table 的对象数组
const tableData = computed(() => {
  if (resultType.value !== 'QueryResult' || !rows.value || !columns.value) {
    return [];
  }
  return rows.value.map(row => {
    const rowData: Record<string, any> = {};
    columns.value.forEach((col, index) => {
      rowData[col] = tryFormatDateTime(row[index]); // Apply formatting here
    });
    return rowData;
  });
});

const executeQuery = async () => {
  const originalQuery = sqlQuery.value.trim();
  if (!originalQuery) {
    ElMessage.warning('请输入要执行的 SQL 语句');
    return;
  }

  let queryToSend = originalQuery;
  // const lowerCaseQuery = originalQuery.toLowerCase();

  // REMOVED: Frontend auto-append LIMIT 100
  // if (lowerCaseQuery.startsWith('select')) {
  //   const hasLimitClause = /\slimit\b/i.test(lowerCaseQuery.replace(/;\s*$/, ''));
  //   if (!hasLimitClause) {
  //     console.log('Auto-appending LIMIT 100 to SELECT query.');
  //     const endsInSemicolon = originalQuery.endsWith(';');
  //     let queryWithoutSemicolon = endsInSemicolon ? originalQuery.slice(0, -1) : originalQuery;
  //     queryToSend = queryWithoutSemicolon.trimEnd() + ' LIMIT 100';
  //     if (endsInSemicolon) {
  //       queryToSend += ';';
  //     }
  //     ElMessage.info('已自动为 SELECT 语句添加 LIMIT 100 条件');
  //   }
  // }

  console.log('Executing SQL:', queryToSend);

  isLoading.value = true;
  error.value = null;
  resultType.value = null;
  columns.value = [];
  rows.value = [];
  resultMessage.value = '';

  try {
    // --- 使用处理后的 queryToSend 调用 API ---
    const response = await executeSql({ sql: queryToSend }); 
    
    resultType.value = response.resultType;
    resultMessage.value = response.message;

    if (response.resultType === 'QueryResult') {
      columns.value = response.columns || [];
      rows.value = response.rows || [];
    }

    ElMessage.success('SQL 执行成功');

  } catch (err: any) {
    console.error('SQL Execution Error:', err);
    error.value = err.message || '执行 SQL 时发生未知错误';
    // 确保 error.value 是字符串再调用 ElMessage.error
    if (error.value) { 
        ElMessage.error(error.value);
    }
  } finally {
    isLoading.value = false;
  }
};

// --- CSV Export Function --- (This will be replaced by Excel export logic)
// const downloadCSV = (csvContent: string, fileName: string) => { ... };

const handleExportResults = () => {
  if (resultType.value !== 'QueryResult' || tableData.value.length === 0) {
    ElMessage.warning('没有可导出的查询结果。');
    return;
  }

  try {
    const dataToExport = tableData.value; // This is already an array of objects
    const headers = columns.value; // These are the actual column names/keys

    // Create worksheet
    // We directly use tableData as it's an array of objects where keys are column names
    // XLSX.utils.json_to_sheet will use the keys of the first object as headers by default
    // or we can specify headers explicitly to ensure order and inclusion.
    const worksheet = XLSX.utils.json_to_sheet(dataToExport, { header: headers });
    
    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'SQL Results');

    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `sql_export_${timestamp}.xlsx`;

    // Trigger download
    XLSX.writeFile(workbook, fileName);
    ElMessage.success('结果已开始导出为 Excel 文件。');

  } catch (error) {
    console.error('Error exporting to Excel:', error);
    ElMessage.error('导出 Excel 文件失败。');
  }
};
// -------------------------

</script>

<style scoped>
.sql-tool-container {
  padding: 20px;
}

.input-card {
  margin-bottom: 20px;
}

/* 移除 CodeMirror 6 的样式 */
/*
.sql-editor-container {
  border: 1px solid var(--el-border-color);
  font-size: 14px;
  line-height: 1.5;
}
.sql-editor-container :deep(.cm-editor) {
  height: 200px;
}
.sql-editor-container :deep(.cm-scroller) {
  overflow: auto;
}
*/

/* 为 CodeMirror 5 组件添加样式 */
.sql-editor-cm5 {
  font-size: 14px;
  line-height: 1.5;
}

/* 应用 resize 样式到 CodeMirror 实例 */
.sql-editor-cm5 :deep(.CodeMirror) {
  min-height: 200px; /* 设置初始最小高度 */
  height: auto; /* 让高度自适应内容，并允许 resize */
  resize: vertical; /* 启用垂直调整大小 */
  overflow: auto !important; /* 确保滚动条可见，可能需要 !important 覆盖默认样式 */
}

.results-card {

}

.results-area {
  min-height: 150px;
}

.result-table {
  max-height: 400px;
}

.error-display pre {
  white-space: pre-wrap;
  word-break: break-all;
}
</style> 