package controller

import (
	"github.com/kataras/iris/v12"

	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/constant"
	apperrors "backend/pkg/errors"
	"backend/pkg/response"
)

// WmsPickingTaskController 定义拣货任务控制器接口
type WmsPickingTaskController interface {
	// 基础CRUD操作
	Create(ctx iris.Context)
	Update(ctx iris.Context)
	Delete(ctx iris.Context)
	GetByID(ctx iris.Context)
	GetPage(ctx iris.Context)

	// 任务管理
	AssignTask(ctx iris.Context)
	BatchAssignTask(ctx iris.Context)
	StartTask(ctx iris.Context)
	CompleteTask(ctx iris.Context)
	CancelTask(ctx iris.Context)

	// 拣货执行
	ExecutePicking(ctx iris.Context)
	BatchExecutePicking(ctx iris.Context)
	HandleException(ctx iris.Context)

	// 波次管理
	CreateWave(ctx iris.Context)
	ReleaseWave(ctx iris.Context)

	// 路径优化
	OptimizePickingPath(ctx iris.Context)
	BatchOptimizePickingPath(ctx iris.Context)

	// 统计分析
	GetStats(ctx iris.Context)

	// 导出
	ExportToExcel(ctx iris.Context)

	// 业务查询
	GetByTaskNo(ctx iris.Context)
	GetByNotificationID(ctx iris.Context)
	GetPendingAssignment(ctx iris.Context)
	GetInProgressTasks(ctx iris.Context)
	GetOverdueTasks(ctx iris.Context)

	// Mobile端接口
	GetMobileTaskList(ctx iris.Context)
	MobileExecutePicking(ctx iris.Context)
	MobileConfirmTask(ctx iris.Context)
}

// wmsPickingTaskControllerImpl 拣货任务控制器实现
type WmsPickingTaskControllerImpl struct {
	BaseControllerImpl
	wmsPickingTaskService service.WmsPickingTaskService
}

// NewWmsPickingTaskControllerImpl 创建拣货任务控制器
func NewWmsPickingTaskControllerImpl(cm *ControllerManager) *WmsPickingTaskControllerImpl {
	return &WmsPickingTaskControllerImpl{
		BaseControllerImpl:    *NewBaseController(cm),
		wmsPickingTaskService: cm.GetServiceManager().GetWmsPickingTaskService(),
	}
}

// Create 创建拣货任务
// POST /api/v1/wms/picking-tasks
func (ctrl *WmsPickingTaskControllerImpl) Create(ctx iris.Context) {
	const opName = "CreateWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	var req dto.WmsPickingTaskCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsPickingTaskService.Create(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Update 更新拣货任务
// PUT /api/v1/wms/picking-tasks/{id:uint}
func (ctrl *WmsPickingTaskControllerImpl) Update(ctx iris.Context) {
	const opName = "UpdateWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPickingTaskUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	vo, err := ctrl.wmsPickingTaskService.Update(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// Delete 删除拣货任务
// DELETE /api/v1/wms/picking-tasks/{id:uint}
func (ctrl *WmsPickingTaskControllerImpl) Delete(ctx iris.Context) {
	const opName = "DeleteWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	err = ctrl.wmsPickingTaskService.Delete(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByID 获取单个拣货任务
// GET /api/v1/wms/picking-tasks/{id:uint}
func (ctrl *WmsPickingTaskControllerImpl) GetByID(ctx iris.Context) {
	const opName = "GetWmsPickingTaskByID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	vo, err := ctrl.wmsPickingTaskService.GetByID(ctx.Request().Context(), uint(id))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, vo)
}

// GetPage 获取拣货任务分页
// GET /api/v1/wms/picking-tasks
func (ctrl *WmsPickingTaskControllerImpl) GetPage(ctx iris.Context) {
	const opName = "GetWmsPickingTasksPage"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	var req dto.WmsPickingTaskQueryReq
	if err := ctx.ReadQuery(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的查询参数").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	// 使用 response.BuildPageQuery 来规范化分页和排序参数
	pageQueryFromBuild := response.BuildPageQuery(ctx)
	req.PageQuery.PageNum = pageQueryFromBuild.PageNum
	req.PageQuery.PageSize = pageQueryFromBuild.PageSize
	req.PageQuery.Sort = pageQueryFromBuild.Sort

	pageResult, err := ctrl.wmsPickingTaskService.GetPage(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, pageResult)
}

// AssignTask 分配拣货任务
// POST /api/v1/wms/picking-tasks/{id:uint}/assign
func (ctrl *WmsPickingTaskControllerImpl) AssignTask(ctx iris.Context) {
	const opName = "AssignWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPickingTaskAssignReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsPickingTaskService.AssignTask(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// BatchAssignTask 批量分配拣货任务
// POST /api/v1/wms/picking-tasks/batch-assign
func (ctrl *WmsPickingTaskControllerImpl) BatchAssignTask(ctx iris.Context) {
	const opName = "BatchAssignWmsPickingTasks"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	var req dto.WmsPickingTaskBatchAssignReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPickingTaskService.BatchAssignTask(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// StartTask 开始拣货任务
// POST /api/v1/wms/picking-tasks/{id:uint}/start
func (ctrl *WmsPickingTaskControllerImpl) StartTask(ctx iris.Context) {
	const opName = "StartWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPickingTaskStartReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsPickingTaskService.StartTask(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// CompleteTask 完成拣货任务
// POST /api/v1/wms/picking-tasks/{id:uint}/complete
func (ctrl *WmsPickingTaskControllerImpl) CompleteTask(ctx iris.Context) {
	const opName = "CompleteWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPickingTaskCompleteReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsPickingTaskService.CompleteTask(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// CancelTask 取消拣货任务
// POST /api/v1/wms/picking-tasks/{id:uint}/cancel
func (ctrl *WmsPickingTaskControllerImpl) CancelTask(ctx iris.Context) {
	const opName = "CancelWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	id, err := ctrl.GetPathUintParamWithError(ctx, "id")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	var req dto.WmsPickingTaskCancelReq
	req.ID = uint(id)
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err = ctrl.wmsPickingTaskService.CancelTask(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// ExecutePicking 执行拣货
// POST /api/v1/wms/picking-tasks/execute-picking
func (ctrl *WmsPickingTaskControllerImpl) ExecutePicking(ctx iris.Context) {
	const opName = "ExecutePickingWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	var req dto.WmsPickingExecuteReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPickingTaskService.ExecutePicking(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// BatchExecutePicking 批量执行拣货
// POST /api/v1/wms/picking-tasks/batch-execute-picking
func (ctrl *WmsPickingTaskControllerImpl) BatchExecutePicking(ctx iris.Context) {
	const opName = "BatchExecutePickingWmsPickingTasks"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	var req dto.WmsPickingBatchExecuteReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPickingTaskService.BatchExecutePicking(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// HandleException 处理拣货异常
// POST /api/v1/wms/picking-tasks/handle-exception
func (ctrl *WmsPickingTaskControllerImpl) HandleException(ctx iris.Context) {
	const opName = "HandleExceptionWmsPickingTask"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	var req dto.WmsPickingExceptionReq
	if err := ctx.ReadJSON(&req); err != nil {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "无效的请求体").WithCause(err)
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	err := ctrl.wmsPickingTaskService.HandleException(ctx.Request().Context(), &req)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, nil)
}

// GetByTaskNo 根据任务号获取
// GET /api/v1/wms/picking-tasks/by-task-no/{taskNo:string}
func (ctrl *WmsPickingTaskControllerImpl) GetByTaskNo(ctx iris.Context) {
	const opName = "GetWmsPickingTaskByTaskNo"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	taskNo := ctx.Params().Get("taskNo")
	if taskNo == "" {
		paramErr := apperrors.NewParamError(apperrors.CODE_PARAMS_INVALID, "任务号不能为空")
		ctrl.HandleError(ctx, paramErr, opName)
		return
	}

	result, err := ctrl.wmsPickingTaskService.GetByTaskNo(ctx.Request().Context(), taskNo)
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, result)
}

// GetByNotificationID 根据出库通知单ID获取
// GET /api/v1/wms/picking-tasks/by-notification/{notificationID:uint}
func (ctrl *WmsPickingTaskControllerImpl) GetByNotificationID(ctx iris.Context) {
	const opName = "GetWmsPickingTaskByNotificationID"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	notificationID, err := ctrl.GetPathUintParamWithError(ctx, "notificationID")
	if err != nil {
		ctrl.FailWithError(ctx, err)
		return
	}

	results, err := ctrl.wmsPickingTaskService.GetByNotificationID(ctx.Request().Context(), uint(notificationID))
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}

// GetPendingAssignment 获取待分配的任务
// GET /api/v1/wms/picking-tasks/pending-assignment
func (ctrl *WmsPickingTaskControllerImpl) GetPendingAssignment(ctx iris.Context) {
	const opName = "GetPendingAssignmentWmsPickingTasks"
	// 手动设置审计日志的资源类型
	ctx.Values().Set(constant.AUDIT_RESOURCE_TYPE_CTX_KEY, "wms_picking_task")

	results, err := ctrl.wmsPickingTaskService.GetPendingAssignment(ctx.Request().Context())
	if err != nil {
		ctrl.HandleError(ctx, err, opName)
		return
	}
	ctrl.Success(ctx, results)
}
