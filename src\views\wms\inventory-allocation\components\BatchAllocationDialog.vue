<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量分配"
    width="60%"
    :close-on-click-modal="false"
    @open="handleOpen"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div class="batch-allocation-dialog-container">
      <!-- 分配列表 -->
      <el-card class="allocation-list-card" shadow="never">
        <template #header>
          <span>待分配列表 ({{ allocationIds.length }})</span>
        </template>
        
        <div class="allocation-items">
          <div
            v-for="(item, index) in allocationItems"
            :key="item.id"
            class="allocation-item"
          >
            <div class="item-info">
              <div class="item-no">{{ item.notificationNo }}</div>
              <div class="item-customer">{{ item.customerName }}</div>
              <div class="item-status">
                <el-tag :type="getStatusTagType(item.allocationStatus)" size="small">
                  {{ formatStatus(item.allocationStatus) }}
                </el-tag>
              </div>
            </div>
            <div class="item-stats">
              <span>{{ item.totalItems }}项 / {{ item.totalQty }}件</span>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 批量分配设置 -->
      <el-card class="batch-settings-card" shadow="never">
        <template #header>
          <span>批量分配设置</span>
        </template>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-form-item label="分配策略" prop="strategy">
            <el-select
              v-model="formData.strategy"
              placeholder="请选择分配策略"
              style="width: 100%"
            >
              <el-option
                v-for="option in strategyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="分配模式" prop="mode">
            <el-radio-group v-model="formData.mode">
              <el-radio label="SEQUENTIAL">顺序分配</el-radio>
              <el-radio label="PARALLEL">并行分配</el-radio>
            </el-radio-group>
            <div class="mode-description">
              <p v-if="formData.mode === 'SEQUENTIAL'">
                按顺序逐个分配，前一个分配完成后再分配下一个
              </p>
              <p v-if="formData.mode === 'PARALLEL'">
                同时分配所有选中项，可能存在库存竞争
              </p>
            </div>
          </el-form-item>
          
          <el-form-item label="失败处理" prop="failureHandling">
            <el-radio-group v-model="formData.failureHandling">
              <el-radio label="CONTINUE">继续分配</el-radio>
              <el-radio label="STOP">停止分配</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="分配备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入批量分配备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 分配进度 -->
      <el-card class="progress-card" shadow="never" v-if="isProcessing">
        <template #header>
          <span>分配进度</span>
        </template>
        
        <div class="progress-info">
          <div class="progress-stats">
            <span>总数: {{ allocationIds.length }}</span>
            <span>已完成: {{ processedCount }}</span>
            <span>成功: {{ successCount }}</span>
            <span>失败: {{ failureCount }}</span>
          </div>
          <el-progress
            :percentage="progressPercentage"
            :color="getProgressColor()"
            :stroke-width="8"
            :show-text="true"
          />
        </div>
        
        <div class="progress-details" v-if="processResults.length > 0">
          <div
            v-for="result in processResults"
            :key="result.id"
            class="result-item"
            :class="result.success ? 'success' : 'failure'"
          >
            <span class="result-no">{{ result.notificationNo }}</span>
            <span class="result-status">{{ result.success ? '成功' : '失败' }}</span>
            <span class="result-message" v-if="result.message">{{ result.message }}</span>
          </div>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="isProcessing">
          {{ isProcessing ? '处理中...' : '取消' }}
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="!canConfirm || isProcessing"
        >
          开始批量分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineExpose } from 'vue'
import { ElDialog, ElButton, ElMessage, ElCard, ElForm, ElFormItem, ElSelect, ElOption, ElRadioGroup, ElRadio, ElInput, ElTag, ElProgress } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useInventoryAllocationStore } from '@/store/wms/inventoryAllocation'

// 组件接口定义
interface BatchAllocationDialogEmits {
  confirm: []
  cancel: []
}

// Emits
const emit = defineEmits<BatchAllocationDialogEmits>()

// Store
const inventoryAllocationStore = useInventoryAllocationStore()

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const isProcessing = ref(false)
const allocationIds = ref<number[]>([])
const allocationItems = ref<any[]>([])
const processResults = ref<any[]>([])
const processedCount = ref(0)
const successCount = ref(0)
const failureCount = ref(0)

// 表单数据
const formData = reactive({
  strategy: '',
  mode: 'SEQUENTIAL',
  failureHandling: 'CONTINUE',
  remark: ''
})

// 表单验证规则
const formRules: FormRules = {
  strategy: [
    { required: true, message: '请选择分配策略', trigger: 'change' }
  ],
  mode: [
    { required: true, message: '请选择分配模式', trigger: 'change' }
  ],
  failureHandling: [
    { required: true, message: '请选择失败处理方式', trigger: 'change' }
  ]
}

// 策略选项
const strategyOptions = computed(() => inventoryAllocationStore.strategyOptions)

// 计算属性
const canConfirm = computed(() => {
  return formData.strategy && allocationIds.value.length > 0
})

const progressPercentage = computed(() => {
  return allocationIds.value.length > 0 
    ? Math.round((processedCount.value / allocationIds.value.length) * 100) 
    : 0
})

// 表单引用
const formRef = ref<FormInstance>()

// 方法
const loadAllocationItems = async (ids: number[]) => {
  try {
    // 这里应该调用API获取分配项的详细信息
    // 暂时使用模拟数据
    allocationItems.value = ids.map(id => ({
      id,
      notificationNo: `OUT${String(id).padStart(6, '0')}`,
      customerName: `客户${id}`,
      allocationStatus: 'PENDING',
      totalItems: Math.floor(Math.random() * 10) + 1,
      totalQty: Math.floor(Math.random() * 100) + 10
    }))
  } catch (error) {
    ElMessage.error('加载分配项失败')
    console.error(error)
  }
}

const handleOpen = () => {
  // 在open方法中会传入ids
}

const handleClose = () => {
  resetData()
}

const resetData = () => {
  allocationIds.value = []
  allocationItems.value = []
  processResults.value = []
  processedCount.value = 0
  successCount.value = 0
  failureCount.value = 0
  isProcessing.value = false
  Object.assign(formData, {
    strategy: '',
    mode: 'SEQUENTIAL',
    failureHandling: 'CONTINUE',
    remark: ''
  })
  formRef.value?.clearValidate()
}

const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    isProcessing.value = true
    
    if (formData.mode === 'SEQUENTIAL') {
      await processSequential()
    } else {
      await processParallel()
    }
    
    ElMessage.success(`批量分配完成，成功: ${successCount.value}，失败: ${failureCount.value}`)
    emit('confirm')
    
    // 延迟关闭对话框，让用户看到结果
    setTimeout(() => {
      dialogVisible.value = false
    }, 2000)
  } catch (error) {
    ElMessage.error('批量分配失败')
    console.error(error)
  } finally {
    submitting.value = false
    isProcessing.value = false
  }
}

const processSequential = async () => {
  for (const id of allocationIds.value) {
    try {
      await inventoryAllocationStore.executeAllocation(id, formData.strategy)
      
      const item = allocationItems.value.find(item => item.id === id)
      processResults.value.push({
        id,
        notificationNo: item?.notificationNo || `OUT${id}`,
        success: true,
        message: '分配成功'
      })
      successCount.value++
    } catch (error) {
      const item = allocationItems.value.find(item => item.id === id)
      processResults.value.push({
        id,
        notificationNo: item?.notificationNo || `OUT${id}`,
        success: false,
        message: '分配失败'
      })
      failureCount.value++
      
      if (formData.failureHandling === 'STOP') {
        break
      }
    } finally {
      processedCount.value++
    }
  }
}

const processParallel = async () => {
  const promises = allocationIds.value.map(async (id) => {
    try {
      await inventoryAllocationStore.executeAllocation(id, formData.strategy)
      
      const item = allocationItems.value.find(item => item.id === id)
      processResults.value.push({
        id,
        notificationNo: item?.notificationNo || `OUT${id}`,
        success: true,
        message: '分配成功'
      })
      successCount.value++
    } catch (error) {
      const item = allocationItems.value.find(item => item.id === id)
      processResults.value.push({
        id,
        notificationNo: item?.notificationNo || `OUT${id}`,
        success: false,
        message: '分配失败'
      })
      failureCount.value++
    } finally {
      processedCount.value++
    }
  })
  
  await Promise.all(promises)
}

const handleCancel = () => {
  if (isProcessing.value) {
    ElMessage.warning('正在处理中，请稍候...')
    return
  }
  
  emit('cancel')
  dialogVisible.value = false
}

// 工具方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING': 'info',
    'PARTIAL': 'warning',
    'ALLOCATED': 'success',
    'FAILED': 'danger'
  }
  return typeMap[status] || 'info'
}

const formatStatus = (status: string) => {
  const labelMap: Record<string, string> = {
    'PENDING': '待分配',
    'PARTIAL': '部分分配',
    'ALLOCATED': '已分配',
    'FAILED': '分配失败'
  }
  return labelMap[status] || '未知'
}

const getProgressColor = () => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 暴露方法
const open = (ids: number[]) => {
  allocationIds.value = ids
  dialogVisible.value = true
  loadAllocationItems(ids)
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<style scoped>
.batch-allocation-dialog-container {
  padding: 16px 0;
}

.allocation-list-card,
.batch-settings-card,
.progress-card {
  margin-bottom: 16px;
}

.allocation-list-card :deep(.el-card__header),
.batch-settings-card :deep(.el-card__header),
.progress-card :deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
}

.allocation-items {
  max-height: 200px;
  overflow-y: auto;
}

.allocation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-no {
  font-weight: 600;
  color: #303133;
}

.item-customer {
  color: #606266;
}

.item-stats {
  font-size: 12px;
  color: #909399;
}

.mode-description {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.progress-info {
  margin-bottom: 16px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.progress-details {
  max-height: 150px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
}

.result-item.success {
  background-color: #f0f9ff;
  color: #67c23a;
}

.result-item.failure {
  background-color: #fef0f0;
  color: #f56c6c;
}

.result-no {
  font-weight: 500;
  min-width: 100px;
}

.result-status {
  min-width: 40px;
}

.result-message {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
