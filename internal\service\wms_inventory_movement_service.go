package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	"context"
	"fmt"
	"time"
)

// WmsInventoryMovementService 库存移动服务接口
type WmsInventoryMovementService interface {
	BaseService

	// 基础CRUD操作
	Create(ctx context.Context, req *dto.WmsInventoryMovementCreateReq) (*vo.WmsInventoryMovementVO, error)
	Update(ctx context.Context, id uint, req *dto.WmsInventoryMovementUpdateReq) (*vo.WmsInventoryMovementVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.WmsInventoryMovementVO, error)
	GetPage(ctx context.Context, req *dto.WmsInventoryMovementQueryReq) (*vo.WmsInventoryMovementPageVO, error)

	// 业务操作
	CreateMovement(ctx context.Context, req *dto.WmsInventoryMovementCreateReq) (*vo.WmsInventoryMovementVO, error)
	StartMovement(ctx context.Context, id uint, operatorId uint) error
	CompleteMovement(ctx context.Context, id uint, operatorId uint) error
	CancelMovement(ctx context.Context, id uint, reason string) error

	// 批量操作
	BatchCreate(ctx context.Context, movements []*dto.WmsInventoryMovementCreateReq) ([]*vo.WmsInventoryMovementVO, error)
	BatchStart(ctx context.Context, ids []uint, operatorId uint) error
	BatchComplete(ctx context.Context, ids []uint, operatorId uint) error
	BatchCancel(ctx context.Context, ids []uint, reason string) error

	// 查询操作
	GetPendingMovements(ctx context.Context, warehouseId *uint, operatorId *uint) ([]*vo.WmsInventoryMovementVO, error)
	GetMovementHistory(ctx context.Context, req *dto.WmsInventoryMovementHistoryReq) ([]*vo.WmsInventoryMovementVO, error)
	GetMovementsByRoute(ctx context.Context, fromLocationId, toLocationId uint) ([]*vo.WmsInventoryMovementVO, error)

	// 统计分析
	GetMovementStatistics(ctx context.Context, req *dto.WmsInventoryMovementStatsReq) (*vo.WmsInventoryMovementStatsVO, error)
	GetMovementSummary(ctx context.Context, startDate, endDate time.Time, warehouseId *uint) (*vo.WmsInventoryMovementSummaryVO, error)

	// 验证操作
	ValidateMovement(ctx context.Context, req *dto.WmsInventoryMovementCreateReq) error
	CheckLocationCapacity(ctx context.Context, locationId uint, additionalQty float64) error
}

// wmsInventoryMovementServiceImpl 库存移动服务实现
type wmsInventoryMovementServiceImpl struct {
	BaseServiceImpl
}

// NewWmsInventoryMovementService 创建库存移动服务实例
func NewWmsInventoryMovementService(sm *ServiceManager) WmsInventoryMovementService {
	return &wmsInventoryMovementServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Helper methods to access repositories
func (s *wmsInventoryMovementServiceImpl) getMovementRepo() repository.WmsInventoryMovementRepository {
	return s.GetServiceManager().GetRepositoryManager().GetWmsInventoryMovementRepository()
}

func (s *wmsInventoryMovementServiceImpl) getInventoryRepo() repository.WmsInventoryRepository {
	return s.GetServiceManager().GetRepositoryManager().GetWmsInventoryRepository()
}

func (s *wmsInventoryMovementServiceImpl) getLocationRepo() repository.WmsLocationRepository {
	return s.GetServiceManager().GetRepositoryManager().GetWmsLocationRepository()
}

func (s *wmsInventoryMovementServiceImpl) getItemRepo() repository.MtlItemRepository {
	return s.GetServiceManager().GetRepositoryManager().GetMtlItemRepository()
}

func (s *wmsInventoryMovementServiceImpl) getTransactionRepo() repository.WmsInventoryTransactionLogRepository {
	return s.GetServiceManager().GetRepositoryManager().GetWmsInventoryTransactionLogRepository()
}

func (s *wmsInventoryMovementServiceImpl) getCodeGenerationService() CodeGenerationService {
	return s.GetServiceManager().GetCodeGenerationService()
}

// Create 创建库存移动记录
func (s *wmsInventoryMovementServiceImpl) Create(ctx context.Context, req *dto.WmsInventoryMovementCreateReq) (*vo.WmsInventoryMovementVO, error) {
	return s.CreateMovement(ctx, req)
}

// CreateMovement 创建库存移动
func (s *wmsInventoryMovementServiceImpl) CreateMovement(ctx context.Context, req *dto.WmsInventoryMovementCreateReq) (*vo.WmsInventoryMovementVO, error) {
	// 验证请求参数
	if err := s.ValidateMovement(ctx, req); err != nil {
		return nil, err
	}

	// 生成移动单号
	movementNo, err := s.generateMovementNo(ctx, req)
	if err != nil {
		return nil, err
	}

	// 创建移动记录
	movement := &entity.WmsInventoryMovement{
		AccountBookEntity: entity.AccountBookEntity{
			AccountBookID: req.AccountBookID,
		},
		MovementNo:     movementNo,
		ItemID:         req.ItemID,
		FromLocationID: req.FromLocationID,
		ToLocationID:   req.ToLocationID,
		BatchNo:        req.BatchNo,
		Quantity:       req.Quantity,
		UnitOfMeasure:  req.UnitOfMeasure,
		MovementType:   req.MovementType,
		MovementReason: &req.MovementReason,
		Status:         entity.MovementStatusPending,
		OperatorID:     req.OperatorID,
	}

	// 保存移动记录
	if err := s.getMovementRepo().Create(ctx, movement); err != nil {
		return nil, fmt.Errorf("创建移动记录失败: %w", err)
	}

	// 转换为VO并返回
	return s.convertToVO(movement)
}

// StartMovement 开始移动
func (s *wmsInventoryMovementServiceImpl) StartMovement(ctx context.Context, id uint, operatorId uint) error {
	// 获取移动记录
	movement, err := s.getMovementRepo().FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取移动记录失败: %w", err)
	}
	if movement == nil {
		return fmt.Errorf("移动记录不存在")
	}

	// 检查状态
	if !movement.CanStart() {
		return fmt.Errorf("当前状态不允许开始移动")
	}

	// 更新状态
	now := time.Now()
	movement.Status = entity.MovementStatusInProgress
	movement.StartedAt = &now
	movement.OperatorID = operatorId

	// 保存更新
	if err := s.getMovementRepo().Update(ctx, movement); err != nil {
		return fmt.Errorf("更新移动记录失败: %w", err)
	}

	return nil
}

// CompleteMovement 完成移动
func (s *wmsInventoryMovementServiceImpl) CompleteMovement(ctx context.Context, id uint, operatorId uint) error {
	// 使用事务管理器执行事务
	return s.GetServiceManager().WithTransaction(func(txServiceManager *ServiceManager) error {
		// 获取事务性的repository
		movementRepo := txServiceManager.GetRepositoryManager().GetWmsInventoryMovementRepository()
		inventoryRepo := txServiceManager.GetRepositoryManager().GetWmsInventoryRepository()
		transactionRepo := txServiceManager.GetRepositoryManager().GetWmsInventoryTransactionLogRepository()

		// 获取移动记录
		movement, err := movementRepo.FindByID(ctx, id)
		if err != nil {
			return fmt.Errorf("获取移动记录失败: %w", err)
		}
		if movement == nil {
			return fmt.Errorf("移动记录不存在")
		}

		// 检查状态
		if !movement.CanComplete() {
			return fmt.Errorf("当前状态不允许完成移动")
		}

		// 查找源库存
		sourceInventory, err := s.findInventoryByLocationWithRepo(ctx, inventoryRepo, movement.ItemID, movement.FromLocationID, movement.BatchNo)
		if err != nil {
			return fmt.Errorf("查找源库存失败: %w", err)
		}
		if sourceInventory == nil {
			return fmt.Errorf("源库存不存在")
		}

		// 检查源库存数量
		if sourceInventory.AvailableQuantity() < movement.Quantity {
			return fmt.Errorf("源库存可用数量不足")
		}

		// 查找或创建目标库存
		targetInventory, err := s.findOrCreateTargetInventoryWithRepo(ctx, inventoryRepo, movement, sourceInventory)
		if err != nil {
			return fmt.Errorf("处理目标库存失败: %w", err)
		}

		// 更新源库存
		sourceInventory.Quantity -= movement.Quantity
		if err := inventoryRepo.Update(ctx, sourceInventory); err != nil {
			return fmt.Errorf("更新源库存失败: %w", err)
		}

		// 更新目标库存
		targetInventory.Quantity += movement.Quantity
		if err := inventoryRepo.Update(ctx, targetInventory); err != nil {
			return fmt.Errorf("更新目标库存失败: %w", err)
		}

		// 创建库存事务日志
		if err := s.createMovementTransactionLogsWithRepo(ctx, transactionRepo, movement, sourceInventory, targetInventory, operatorId); err != nil {
			return fmt.Errorf("创建事务日志失败: %w", err)
		}

		// 更新移动记录状态
		now := time.Now()
		movement.Status = entity.MovementStatusCompleted
		movement.CompletedAt = &now

		if err := movementRepo.Update(ctx, movement); err != nil {
			return fmt.Errorf("更新移动记录失败: %w", err)
		}

		return nil
	})
}

// ValidateMovement 验证移动请求
func (s *wmsInventoryMovementServiceImpl) ValidateMovement(ctx context.Context, req *dto.WmsInventoryMovementCreateReq) error {
	// 验证源库位和目标库位不能相同
	if req.FromLocationID == req.ToLocationID {
		return fmt.Errorf("源库位和目标库位不能相同")
	}

	// 验证库位是否存在
	fromLocation, err := s.getLocationRepo().FindByID(ctx, req.FromLocationID)
	if err != nil {
		return fmt.Errorf("获取源库位信息失败: %w", err)
	}
	if fromLocation == nil {
		return fmt.Errorf("源库位不存在")
	}

	toLocation, err := s.getLocationRepo().FindByID(ctx, req.ToLocationID)
	if err != nil {
		return fmt.Errorf("获取目标库位信息失败: %w", err)
	}
	if toLocation == nil {
		return fmt.Errorf("目标库位不存在")
	}

	// 验证物料是否存在
	item, err := s.getItemRepo().FindByID(ctx, req.ItemID)
	if err != nil {
		return fmt.Errorf("获取物料信息失败: %w", err)
	}
	if item == nil {
		return fmt.Errorf("物料不存在")
	}

	// 验证源库存是否存在且数量充足
	sourceInventory, err := s.findInventoryByLocation(ctx, req.ItemID, req.FromLocationID, req.BatchNo)
	if err != nil {
		return fmt.Errorf("查找源库存失败: %w", err)
	}
	if sourceInventory == nil {
		return fmt.Errorf("源库存不存在")
	}
	if sourceInventory.AvailableQuantity() < req.Quantity {
		return fmt.Errorf("源库存可用数量不足")
	}

	// 验证目标库位容量
	if err := s.CheckLocationCapacity(ctx, req.ToLocationID, req.Quantity); err != nil {
		return err
	}

	return nil
}

// convertToVO 转换为VO对象
func (s *wmsInventoryMovementServiceImpl) convertToVO(movement *entity.WmsInventoryMovement) (*vo.WmsInventoryMovementVO, error) {
	movementVO := &vo.WmsInventoryMovementVO{
		ID:             movement.ID,
		MovementNo:     movement.MovementNo,
		ItemID:         movement.ItemID,
		FromLocationID: movement.FromLocationID,
		ToLocationID:   movement.ToLocationID,
		BatchNo:        movement.BatchNo,
		Quantity:       movement.Quantity,
		UnitOfMeasure:  movement.UnitOfMeasure,
		MovementType:   movement.MovementType,
		MovementReason: func() string {
			if movement.MovementReason != nil {
				return *movement.MovementReason
			}
			return ""
		}(),
		Status:     movement.Status,
		OperatorID: movement.OperatorID,
		CreatedAt:  movement.CreatedAt,
		UpdatedAt:  movement.UpdatedAt,
	}

	if movement.StartedAt != nil {
		movementVO.StartedAt = movement.StartedAt
	}
	if movement.CompletedAt != nil {
		movementVO.CompletedAt = movement.CompletedAt
	}

	// 设置业务状态
	movementVO.CanStart = movement.CanStart()
	movementVO.CanComplete = movement.CanComplete()
	movementVO.CanCancel = movement.CanCancel()

	return movementVO, nil
}

// findInventoryByLocation 根据库位查找库存
func (s *wmsInventoryMovementServiceImpl) findInventoryByLocation(ctx context.Context, itemId, locationId uint, batchNo *string) (*entity.WmsInventory, error) {
	return s.findInventoryByLocationWithRepo(ctx, s.getInventoryRepo(), itemId, locationId, batchNo)
}

// findInventoryByLocationWithRepo 根据库位查找库存（使用指定的repository）
func (s *wmsInventoryMovementServiceImpl) findInventoryByLocationWithRepo(ctx context.Context, inventoryRepo repository.WmsInventoryRepository, itemId, locationId uint, batchNo *string) (*entity.WmsInventory, error) {
	// 构建查询条件
	conditions := []repository.QueryCondition{
		repository.NewEqualCondition("item_id", itemId),
		repository.NewEqualCondition("location_id", locationId),
	}

	if batchNo != nil && *batchNo != "" {
		conditions = append(conditions, repository.NewEqualCondition("batch_no", *batchNo))
	}

	// 使用BaseRepository的FindOneByCondition方法
	return inventoryRepo.(repository.BaseRepository[entity.WmsInventory, uint]).FindOneByCondition(ctx, conditions)
}

// findOrCreateTargetInventory 查找或创建目标库存
func (s *wmsInventoryMovementServiceImpl) findOrCreateTargetInventory(ctx context.Context, movement *entity.WmsInventoryMovement, sourceInventory *entity.WmsInventory) (*entity.WmsInventory, error) {
	return s.findOrCreateTargetInventoryWithRepo(ctx, s.getInventoryRepo(), movement, sourceInventory)
}

// findOrCreateTargetInventoryWithRepo 查找或创建目标库存（使用指定的repository）
func (s *wmsInventoryMovementServiceImpl) findOrCreateTargetInventoryWithRepo(ctx context.Context, inventoryRepo repository.WmsInventoryRepository, movement *entity.WmsInventoryMovement, sourceInventory *entity.WmsInventory) (*entity.WmsInventory, error) {
	// 先查找是否已存在目标库存
	targetInventory, err := s.findInventoryByLocationWithRepo(ctx, inventoryRepo, movement.ItemID, movement.ToLocationID, movement.BatchNo)
	if err != nil {
		return nil, err
	}

	// 如果不存在，创建新的库存记录
	if targetInventory == nil {
		targetInventory = &entity.WmsInventory{
			AccountBookEntity: entity.AccountBookEntity{
				AccountBookID: sourceInventory.AccountBookID,
			},
			WarehouseID:    sourceInventory.WarehouseID, // 需要从目标库位获取正确的仓库ID
			ItemID:         movement.ItemID,
			ClientID:       sourceInventory.ClientID,
			LocationID:     movement.ToLocationID,
			BatchNo:        movement.BatchNo,
			Quantity:       0,
			AllocatedQty:   0,
			FrozenQty:      0,
			UnitOfMeasure:  movement.UnitOfMeasure,
			Status:         sourceInventory.Status,
			ExpiryDate:     sourceInventory.ExpiryDate,
			ProductionDate: sourceInventory.ProductionDate,
		}

		if err := inventoryRepo.Create(ctx, targetInventory); err != nil {
			return nil, err
		}
	}

	return targetInventory, nil
}

// createMovementTransactionLogs 创建移动事务日志
func (s *wmsInventoryMovementServiceImpl) createMovementTransactionLogs(ctx context.Context, movement *entity.WmsInventoryMovement, sourceInventory, targetInventory *entity.WmsInventory, operatorId uint) error {
	return s.createMovementTransactionLogsWithRepo(ctx, s.getTransactionRepo(), movement, sourceInventory, targetInventory, operatorId)
}

// createMovementTransactionLogsWithRepo 创建移动事务日志（使用指定的repository）
func (s *wmsInventoryMovementServiceImpl) createMovementTransactionLogsWithRepo(ctx context.Context, transactionRepo repository.WmsInventoryTransactionLogRepository, movement *entity.WmsInventoryMovement, sourceInventory, targetInventory *entity.WmsInventory, operatorId uint) error {
	// 创建源库存减少日志
	sourceLog := &entity.WmsInventoryTransactionLog{
		AccountBookEntity: entity.AccountBookEntity{
			AccountBookID: movement.AccountBookID,
		},
		TransactionTime:  time.Now(),
		TransactionType:  "MOVEMENT_OUT",
		ItemID:           movement.ItemID,
		WarehouseID:      sourceInventory.WarehouseID,
		LocationID:       movement.FromLocationID,
		ClientID:         sourceInventory.ClientID,
		BatchNo:          movement.BatchNo,
		InventoryStatus:  sourceInventory.Status,
		QuantityChange:   -movement.Quantity,
		UnitOfMeasure:    movement.UnitOfMeasure,
		BalanceBefore:    sourceInventory.Quantity + movement.Quantity,
		BalanceAfter:     sourceInventory.Quantity,
		ReferenceDocType: func() *string { s := "MOVEMENT"; return &s }(),
		ReferenceDocID:   &movement.ID,
		OperatorID:       &operatorId,
		Remark:           movement.MovementReason,
	}

	if err := transactionRepo.Create(ctx, sourceLog); err != nil {
		return err
	}

	// 创建目标库存增加日志
	targetLog := &entity.WmsInventoryTransactionLog{
		AccountBookEntity: entity.AccountBookEntity{
			AccountBookID: movement.AccountBookID,
		},
		TransactionTime:  time.Now(),
		TransactionType:  "MOVEMENT_IN",
		ItemID:           movement.ItemID,
		WarehouseID:      targetInventory.WarehouseID,
		LocationID:       movement.ToLocationID,
		ClientID:         targetInventory.ClientID,
		BatchNo:          movement.BatchNo,
		InventoryStatus:  targetInventory.Status,
		QuantityChange:   movement.Quantity,
		UnitOfMeasure:    movement.UnitOfMeasure,
		BalanceBefore:    targetInventory.Quantity - movement.Quantity,
		BalanceAfter:     targetInventory.Quantity,
		ReferenceDocType: func() *string { s := "MOVEMENT"; return &s }(),
		ReferenceDocID:   &movement.ID,
		OperatorID:       &operatorId,
		Remark:           movement.MovementReason,
	}

	return transactionRepo.Create(ctx, targetLog)
}

// CheckLocationCapacity 检查库位容量
func (s *wmsInventoryMovementServiceImpl) CheckLocationCapacity(ctx context.Context, locationId uint, additionalQty float64) error {
	// 获取库位信息
	location, err := s.getLocationRepo().FindByID(ctx, locationId)
	if err != nil {
		return fmt.Errorf("获取库位信息失败: %w", err)
	}
	if location == nil {
		return fmt.Errorf("库位不存在")
	}

	// TODO: 实现库位容量检查
	// 当前实体中没有 MaxCapacity 字段，暂时跳过容量检查

	return nil
}

// CancelMovement 取消移动
func (s *wmsInventoryMovementServiceImpl) CancelMovement(ctx context.Context, id uint, reason string) error {
	// 获取移动记录
	movement, err := s.getMovementRepo().FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取移动记录失败: %w", err)
	}
	if movement == nil {
		return fmt.Errorf("移动记录不存在")
	}

	// 检查状态
	if !movement.CanCancel() {
		return fmt.Errorf("当前状态不允许取消")
	}

	// 更新状态
	movement.Status = entity.MovementStatusCancelled
	movement.MovementReason = &reason

	// 保存更新
	if err := s.getMovementRepo().Update(ctx, movement); err != nil {
		return fmt.Errorf("更新移动记录失败: %w", err)
	}

	return nil
}

// BatchCancel 批量取消库存移动
func (s *wmsInventoryMovementServiceImpl) BatchCancel(ctx context.Context, ids []uint, reason string) error {
	// TODO: 实现批量取消功能
	return fmt.Errorf("批量取消功能暂未实现")
}

// BatchComplete 批量完成库存移动
func (s *wmsInventoryMovementServiceImpl) BatchComplete(ctx context.Context, ids []uint, operatorID uint) error {
	// TODO: 实现批量完成功能
	return fmt.Errorf("批量完成功能暂未实现")
}

// BatchCreate 批量创建库存移动
func (s *wmsInventoryMovementServiceImpl) BatchCreate(ctx context.Context, req []*dto.WmsInventoryMovementCreateReq) ([]*vo.WmsInventoryMovementVO, error) {
	// TODO: 实现批量创建功能
	return nil, fmt.Errorf("批量创建功能暂未实现")
}

// BatchStart 批量开始库存移动
func (s *wmsInventoryMovementServiceImpl) BatchStart(ctx context.Context, ids []uint, operatorID uint) error {
	// TODO: 实现批量开始功能
	return fmt.Errorf("批量开始功能暂未实现")
}

// Delete 删除库存移动
func (s *wmsInventoryMovementServiceImpl) Delete(ctx context.Context, id uint) error {
	// TODO: 实现删除功能
	return fmt.Errorf("删除功能暂未实现")
}

// GetByID 根据ID获取库存移动
func (s *wmsInventoryMovementServiceImpl) GetByID(ctx context.Context, id uint) (*vo.WmsInventoryMovementVO, error) {
	// TODO: 实现获取功能
	return nil, fmt.Errorf("获取功能暂未实现")
}

// GetMovementHistory 获取移动历史
func (s *wmsInventoryMovementServiceImpl) GetMovementHistory(ctx context.Context, req *dto.WmsInventoryMovementHistoryReq) ([]*vo.WmsInventoryMovementVO, error) {
	// TODO: 实现获取移动历史功能
	return nil, fmt.Errorf("获取移动历史功能暂未实现")
}

// GetMovementStatistics 获取移动统计
func (s *wmsInventoryMovementServiceImpl) GetMovementStatistics(ctx context.Context, req *dto.WmsInventoryMovementStatsReq) (*vo.WmsInventoryMovementStatsVO, error) {
	// TODO: 实现获取移动统计功能
	return nil, fmt.Errorf("获取移动统计功能暂未实现")
}

// GetMovementSummary 获取移动汇总
func (s *wmsInventoryMovementServiceImpl) GetMovementSummary(ctx context.Context, startDate time.Time, endDate time.Time, warehouseID *uint) (*vo.WmsInventoryMovementSummaryVO, error) {
	// TODO: 实现获取移动汇总功能
	return nil, fmt.Errorf("获取移动汇总功能暂未实现")
}

// GetMovementsByRoute 根据路径获取移动
func (s *wmsInventoryMovementServiceImpl) GetMovementsByRoute(ctx context.Context, fromLocationID uint, toLocationID uint) ([]*vo.WmsInventoryMovementVO, error) {
	// TODO: 实现根据路径获取移动功能
	return nil, fmt.Errorf("根据路径获取移动功能暂未实现")
}

// GetPage 分页查询库存移动
func (s *wmsInventoryMovementServiceImpl) GetPage(ctx context.Context, req *dto.WmsInventoryMovementQueryReq) (*vo.WmsInventoryMovementPageVO, error) {
	// TODO: 实现分页查询功能
	return nil, fmt.Errorf("分页查询功能暂未实现")
}

// Update 更新库存移动
func (s *wmsInventoryMovementServiceImpl) Update(ctx context.Context, id uint, req *dto.WmsInventoryMovementUpdateReq) (*vo.WmsInventoryMovementVO, error) {
	// TODO: 实现更新功能
	return nil, fmt.Errorf("更新功能暂未实现")
}

// GetPendingMovements 获取待处理的移动任务
func (s *wmsInventoryMovementServiceImpl) GetPendingMovements(ctx context.Context, warehouseId *uint, operatorId *uint) ([]*vo.WmsInventoryMovementVO, error) {
	movements, err := s.getMovementRepo().FindPendingMovements(ctx, warehouseId)
	if err != nil {
		return nil, fmt.Errorf("查询待处理移动任务失败: %w", err)
	}

	// 如果指定了操作员，进行过滤
	if operatorId != nil {
		var filteredMovements []entity.WmsInventoryMovement
		for _, movement := range movements {
			if movement.OperatorID == *operatorId {
				filteredMovements = append(filteredMovements, movement)
			}
		}
		movements = filteredMovements
	}

	// 转换为VO
	var result []*vo.WmsInventoryMovementVO
	for _, movement := range movements {
		movementVO, err := s.convertToVO(&movement)
		if err != nil {
			return nil, err
		}
		result = append(result, movementVO)
	}

	return result, nil
}

// generateMovementNo 生成库存移动单号
func (s *wmsInventoryMovementServiceImpl) generateMovementNo(ctx context.Context, req *dto.WmsInventoryMovementCreateReq) (string, error) {
	// 最大重试次数（编码生成失败时）
	const maxRetries = 3
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		// 简化日志记录
		fmt.Printf("开始生成库存移动单号，尝试次数: %d/%d\n", attempt+1, maxRetries)

		codeGenerationService := s.getCodeGenerationService()
		if codeGenerationService == nil {
			return "", fmt.Errorf("库存移动单号生成服务暂时不可用，请稍后重试或联系系统管理员")
		}

		contextData := map[string]interface{}{
			"itemId":         req.ItemID,
			"fromLocationId": req.FromLocationID,
			"toLocationId":   req.ToLocationID,
			"movementType":   req.MovementType,
			"movementReason": req.MovementReason,
		}

		generatedCode, err := codeGenerationService.GenerateCode(ctx, &dto.CodeGenerationReq{
			BusinessType: "WMS_INVENTORY_MOVEMENT",
			ContextData:  contextData,
		})
		if err != nil {
			fmt.Printf("生成库存移动单号失败，尝试次数: %d, 错误: %v\n", attempt+1, err)

			// 检查是否为配置问题（不需要重试）
			if isParamError(err) {
				return "", fmt.Errorf("库存移动单号自动生成失败。可能原因：1) 缺少库存移动编码规则配置 2) 编码模板格式错误。请联系系统管理员。原因: %w", err)
			}

			lastErr = err
			continue // 重试
		}

		fmt.Printf("库存移动单号生成成功: %s，尝试次数: %d\n", generatedCode.GeneratedCode, attempt+1)
		return generatedCode.GeneratedCode, nil
	}

	// 所有重试都失败了
	if lastErr != nil {
		return "", fmt.Errorf("库存移动单号生成失败，系统重试次数已达上限。请稍后重试或联系系统管理员。原因: %w", lastErr)
	}

	return "", fmt.Errorf("库存移动单号生成失败，编码生成重试次数已达上限。请联系系统管理员")
}

// isParamError 检查是否为参数错误（简化版本，实际应该使用apperrors包）
func isParamError(err error) bool {
	return err != nil && (fmt.Sprintf("%v", err) == "CODE_PARAMS_INVALID" ||
		fmt.Sprintf("%v", err) == "缺少编码规则" ||
		fmt.Sprintf("%v", err) == "编码模板格式错误")
}
