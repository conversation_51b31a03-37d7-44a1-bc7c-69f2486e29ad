package service

import (
	"backend/internal/model/dto"
	"backend/internal/model/entity"
	"backend/internal/model/vo"
	"backend/internal/repository"
	apperrors "backend/pkg/errors"
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
)

// CodeGenerationService 编码生成服务接口
type CodeGenerationService interface {
	BaseService

	// 生成编码
	GenerateCode(ctx context.Context, req *dto.CodeGenerationReq) (*dto.CodeGenerationRes, error)

	// 验证编码格式
	ValidateCodeFormat(ctx context.Context, format string) error

	// 预览编码效果
	PreviewCode(ctx context.Context, req *dto.CodePreviewReq) (*dto.CodePreviewRes, error)

	// 重置序号
	ResetSequence(ctx context.Context, req *dto.ResetSequenceReq) error
}

// codeGenerationServiceImpl 编码生成服务实现
type codeGenerationServiceImpl struct {
	BaseServiceImpl
}

// NewCodeGenerationService 创建编码生成服务
func NewCodeGenerationService(sm *ServiceManager) CodeGenerationService {
	return &codeGenerationServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// GenerateCode 生成编码
func (s *codeGenerationServiceImpl) GenerateCode(ctx context.Context, req *dto.CodeGenerationReq) (*dto.CodeGenerationRes, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()
	logRepo := s.GetServiceManager().GetRepositoryManager().GetSysCodeGenerationLogRepository()

	s.GetServiceManager().GetLogger().Info("开始生成编码",
		s.GetServiceManager().GetLogger().WithField("businessType", req.BusinessType))

	// 1. 获取业务类型的默认规则
	rule, err := repo.FindDefaultByBusinessType(ctx, req.BusinessType)
	if err != nil {
		s.GetServiceManager().GetLogger().Error("未找到业务类型的默认编码规则",
			s.GetServiceManager().GetLogger().WithField("businessType", req.BusinessType),
			s.GetServiceManager().GetLogger().WithError(err))
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "缺少编码规则，无法自动生成编码。请先在系统设置中为该业务类型配置编码规则。")
	}

	s.GetServiceManager().GetLogger().Info("找到编码规则",
		s.GetServiceManager().GetLogger().WithField("ruleCode", rule.RuleCode),
		s.GetServiceManager().GetLogger().WithField("ruleName", rule.RuleName),
		s.GetServiceManager().GetLogger().WithField("codeFormat", rule.CodeFormat))

	// 2. 检查是否需要重置序号
	if s.shouldResetSequence(rule) {
		if err := s.resetSequenceIfNeeded(ctx, rule); err != nil {
			return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "重置编码序号失败，请检查编码规则配置或联系系统管理员。").WithCause(err)
		}
	}

	// 3. 生成下一个序号
	nextSeq, err := repo.GetNextSequence(ctx, rule.ID)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取编码序号失败，可能是数据库连接问题，请稍后重试。").WithCause(err)
	}

	s.GetServiceManager().GetLogger().Info("获取下一个序号",
		s.GetServiceManager().GetLogger().WithField("nextSequence", nextSeq))

	// 4. 解析模板并生成编码
	code, err := s.parseTemplate(rule.CodeFormat, nextSeq, req.ContextData, rule.Separator)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "编码模板格式错误，无法生成编码。请检查编码规则中的格式配置。").WithCause(err)
	}

	s.GetServiceManager().GetLogger().Info("编码模板解析完成",
		s.GetServiceManager().GetLogger().WithField("generatedCode", code))

	// 5. 记录生成历史
	if err := s.logGeneration(ctx, rule, code, nextSeq, req.ContextData, logRepo); err != nil {
		// 记录日志失败不影响编码生成，只记录错误
		// TODO: 可以考虑使用异步日志记录
	}

	return &dto.CodeGenerationRes{
		GeneratedCode:  code,
		RuleID:         rule.ID,
		SequenceNumber: nextSeq,
		BusinessType:   req.BusinessType,
	}, nil
}

// ValidateCodeFormat 验证编码格式
func (s *codeGenerationServiceImpl) ValidateCodeFormat(ctx context.Context, format string) error {
	// 检查格式模板的基本语法
	if format == "" {
		return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "编码格式配置不能为空，请设置有效的编码格式模板。")
	}

	// 验证模板语法
	_, err := s.parseTemplate(format, 1, map[string]interface{}{
		"customer_type": "TEST",
		"industry":      "TEST",
	}, nil)
	if err != nil {
		return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "编码格式模板语法错误，请检查模板中的占位符是否正确。").WithCause(err)
	}

	return nil
}

// PreviewCode 预览编码效果
func (s *codeGenerationServiceImpl) PreviewCode(ctx context.Context, req *dto.CodePreviewReq) (*dto.CodePreviewRes, error) {
	var format string
	var err error

	// 确定使用的格式模板
	if req.CodeFormat != nil {
		format = *req.CodeFormat
	} else if req.RuleID != nil {
		repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()
		rule, err := repo.FindByID(ctx, *req.RuleID)
		if err != nil {
			return &dto.CodePreviewRes{
				IsValid:  false,
				ErrorMsg: "规则不存在",
			}, nil
		}
		format = rule.CodeFormat
	} else {
		return &dto.CodePreviewRes{
			IsValid:  false,
			ErrorMsg: "必须提供规则ID或编码格式",
		}, nil
	}

	// 确定预览序号
	sequence := int64(1) // 默认序号
	if req.ContextData != nil {
		if startVal, exists := req.ContextData["sequenceStart"]; exists {
			if startInt, ok := startVal.(float64); ok {
				sequence = int64(startInt)
			} else if startInt, ok := startVal.(int); ok {
				sequence = int64(startInt)
			}
		}
	}

	// 生成预览编码
	previewCode, err := s.parseTemplate(format, sequence, req.ContextData, nil)
	if err != nil {
		return &dto.CodePreviewRes{
			IsValid:  false,
			ErrorMsg: err.Error(),
		}, nil
	}

	return &dto.CodePreviewRes{
		PreviewCode: previewCode,
		IsValid:     true,
		ErrorMsg:    "",
	}, nil
}

// ResetSequence 重置序号
func (s *codeGenerationServiceImpl) ResetSequence(ctx context.Context, req *dto.ResetSequenceReq) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()

	return repo.ResetSequence(ctx, req.RuleID)
}

// shouldResetSequence 检查是否需要重置序号
func (s *codeGenerationServiceImpl) shouldResetSequence(rule *entity.SysCodeRule) bool {
	if rule.ResetFrequency == entity.ResetFrequencyNever {
		return false
	}

	if rule.LastResetTime == nil {
		return true // 从未重置过
	}

	now := time.Now()
	lastReset := *rule.LastResetTime

	switch rule.ResetFrequency {
	case entity.ResetFrequencyDaily:
		return !isSameDay(now, lastReset)
	case entity.ResetFrequencyMonthly:
		return !isSameMonth(now, lastReset)
	case entity.ResetFrequencyYearly:
		return !isSameYear(now, lastReset)
	default:
		return false
	}
}

// resetSequenceIfNeeded 如果需要则重置序号
func (s *codeGenerationServiceImpl) resetSequenceIfNeeded(ctx context.Context, rule *entity.SysCodeRule) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()
	return repo.ResetSequence(ctx, rule.ID)
}

// parseTemplate 解析模板并生成编码
func (s *codeGenerationServiceImpl) parseTemplate(template string, sequence int64, context map[string]interface{}, separatorPtr *string) (string, error) {
	result := template

	// 获取分隔符
	separator := ""
	if separatorPtr != nil {
		separator = *separatorPtr
	}

	// 替换日期组件
	now := time.Now()
	result = strings.ReplaceAll(result, "{YYYYMMDD}", now.Format("20060102")) // 20250110
	result = strings.ReplaceAll(result, "{YYMMDD}", now.Format("060102"))     // 250110
	result = strings.ReplaceAll(result, "{YYYY}", now.Format("2006"))
	result = strings.ReplaceAll(result, "{YY}", now.Format("06"))
	result = strings.ReplaceAll(result, "{MM}", now.Format("01"))
	result = strings.ReplaceAll(result, "{DD}", now.Format("02"))

	// 替换序号组件
	seqRegex := regexp.MustCompile(`\{SEQ:(\d+)\}`)
	result = seqRegex.ReplaceAllStringFunc(result, func(match string) string {
		matches := seqRegex.FindStringSubmatch(match)
		if len(matches) > 1 {
			if length, err := strconv.Atoi(matches[1]); err == nil {
				return fmt.Sprintf("%0*d", length, sequence)
			}
		}
		return fmt.Sprintf("%04d", sequence) // 默认4位
	})

	// 替换固定字符组件
	fixedRegex := regexp.MustCompile(`\{FIXED:([^}]+)\}`)
	result = fixedRegex.ReplaceAllString(result, "$1")

	// 替换字段值组件
	fieldRegex := regexp.MustCompile(`\{FIELD:([^}]+)\}`)
	result = fieldRegex.ReplaceAllStringFunc(result, func(match string) string {
		matches := fieldRegex.FindStringSubmatch(match)
		if len(matches) > 1 {
			fieldName := matches[1]
			if value, exists := context[fieldName]; exists {
				return fmt.Sprintf("%v", value)
			}
		}
		return "" // 字段不存在时返回空字符串
	})

	// 如果有分隔符，在组件之间插入分隔符
	if separator != "" {
		// 找到所有组件的位置并插入分隔符
		// 这里使用简单的方法：在每个}后面插入分隔符，然后清理多余的分隔符
		componentRegex := regexp.MustCompile(`(\{[^}]+\})`)
		components := componentRegex.FindAllString(template, -1)

		if len(components) > 1 {
			// 重新构建结果，在组件之间插入分隔符
			tempResult := template
			for i := 0; i < len(components)-1; i++ {
				// 在每个组件后插入分隔符（除了最后一个）
				tempResult = strings.Replace(tempResult, components[i], components[i]+separator, 1)
			}

			// 重新解析带分隔符的模板
			result = tempResult

			// 重新替换所有组件
			result = strings.ReplaceAll(result, "{YYYYMMDD}", now.Format("20060102")) // 20250110
			result = strings.ReplaceAll(result, "{YYMMDD}", now.Format("060102"))     // 250110
			result = strings.ReplaceAll(result, "{YYYY}", now.Format("2006"))
			result = strings.ReplaceAll(result, "{YY}", now.Format("06"))
			result = strings.ReplaceAll(result, "{MM}", now.Format("01"))
			result = strings.ReplaceAll(result, "{DD}", now.Format("02"))

			result = seqRegex.ReplaceAllStringFunc(result, func(match string) string {
				matches := seqRegex.FindStringSubmatch(match)
				if len(matches) > 1 {
					if length, err := strconv.Atoi(matches[1]); err == nil {
						return fmt.Sprintf("%0*d", length, sequence)
					}
				}
				return fmt.Sprintf("%04d", sequence)
			})

			result = fixedRegex.ReplaceAllString(result, "$1")

			result = fieldRegex.ReplaceAllStringFunc(result, func(match string) string {
				matches := fieldRegex.FindStringSubmatch(match)
				if len(matches) > 1 {
					fieldName := matches[1]
					if value, exists := context[fieldName]; exists {
						return fmt.Sprintf("%v", value)
					}
				}
				return ""
			})
		}
	}

	return result, nil
}

// logGeneration 记录生成历史
func (s *codeGenerationServiceImpl) logGeneration(ctx context.Context, rule *entity.SysCodeRule, code string, sequence int64, contextData map[string]interface{}, logRepo repository.SysCodeGenerationLogRepository) error {
	// 序列化上下文数据
	contextJSON := ""
	if contextData != nil {
		if jsonBytes, err := json.Marshal(contextData); err == nil {
			contextJSON = string(jsonBytes)
		}
	}

	// 获取当前用户ID
	userID64, err := s.GetUserIDFromContext(ctx)
	if err != nil {
		userID64 = 0 // 如果获取失败，使用默认值
	}
	userID := uint(userID64)

	log := &entity.SysCodeGenerationLog{
		TenantID:          rule.TenantID,
		AccountBookID:     rule.AccountBookID,
		RuleID:            rule.ID,
		BusinessType:      rule.BusinessType,
		GeneratedCode:     code,
		SequenceNumber:    sequence,
		GenerationContext: &contextJSON,
		CreatedBy:         userID,
	}

	return logRepo.Create(ctx, log)
}

// 时间比较辅助函数
func isSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

func isSameMonth(t1, t2 time.Time) bool {
	y1, m1, _ := t1.Date()
	y2, m2, _ := t2.Date()
	return y1 == y2 && m1 == m2
}

func isSameYear(t1, t2 time.Time) bool {
	return t1.Year() == t2.Year()
}

// SysCodeRuleService 编码规则管理服务接口
type SysCodeRuleService interface {
	BaseService

	Create(ctx context.Context, req *dto.SysCodeRuleCreateReq) (*vo.SysCodeRuleVO, error)
	Update(ctx context.Context, id uint, req *dto.SysCodeRuleUpdateReq) (*vo.SysCodeRuleVO, error)
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*vo.SysCodeRuleVO, error)
	GetPage(ctx context.Context, req *dto.SysCodeRuleQueryReq) (*vo.PageResult[vo.SysCodeRuleVO], error)
	SetAsDefault(ctx context.Context, id uint) error
}

// sysCodeRuleServiceImpl 编码规则管理服务实现
type sysCodeRuleServiceImpl struct {
	BaseServiceImpl
}

// NewSysCodeRuleService 创建编码规则管理服务
func NewSysCodeRuleService(sm *ServiceManager) SysCodeRuleService {
	return &sysCodeRuleServiceImpl{
		BaseServiceImpl: *NewBaseService(sm),
	}
}

// Create 创建编码规则
func (s *sysCodeRuleServiceImpl) Create(ctx context.Context, req *dto.SysCodeRuleCreateReq) (*vo.SysCodeRuleVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()

	// 检查规则编码是否已存在
	existing, err := repo.FindByRuleCode(ctx, req.RuleCode)
	if err == nil && existing != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "规则编码已存在")
	}

	// 验证编码格式
	codeGenService := NewCodeGenerationService(s.GetServiceManager())
	if err := codeGenService.ValidateCodeFormat(ctx, req.CodeFormat); err != nil {
		return nil, err
	}

	// 从Context获取账套ID
	accountBookID, err := s.GetAccountBookIDFromContext(ctx)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "无法获取当前账套ID").WithCause(err)
	}

	// 创建实体
	rule := &entity.SysCodeRule{
		AccountBookEntity: entity.AccountBookEntity{
			AccountBookID: uint(accountBookID),
		},
		RuleCode:       req.RuleCode,
		RuleName:       req.RuleName,
		BusinessType:   req.BusinessType,
		CodeFormat:     req.CodeFormat,
		ResetFrequency: req.ResetFrequency,
		Status:         entity.CodeRuleStatusActive,
	}

	// 复制其他字段
	if err := copier.Copy(rule, req); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "数据转换失败").WithCause(err)
	}

	// 处理默认值
	if req.SequenceLength != nil {
		rule.SequenceLength = req.SequenceLength
	}
	if req.SequenceStart != nil {
		rule.SequenceStart = req.SequenceStart
	}
	if req.IsDefault != nil && *req.IsDefault {
		rule.IsDefault = true
	}

	// 保存规则
	if err := repo.Create(ctx, rule); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "创建编码规则失败").WithCause(err)
	}

	// 如果设置为默认规则，需要清除其他默认规则
	if rule.IsDefault {
		if err := repo.SetAsDefault(ctx, rule.ID, rule.BusinessType); err != nil {
			// 这里可以考虑回滚，但为了简化，只记录错误
			return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "设置默认规则失败").WithCause(err)
		}
	}

	return s.entityToVO(rule), nil
}

// Update 更新编码规则
func (s *sysCodeRuleServiceImpl) Update(ctx context.Context, id uint, req *dto.SysCodeRuleUpdateReq) (*vo.SysCodeRuleVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()

	// 获取现有实体
	rule, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "编码规则不存在")
	}

	// 验证编码格式（如果有更新）
	if req.CodeFormat != nil {
		codeGenService := NewCodeGenerationService(s.GetServiceManager())
		if err := codeGenService.ValidateCodeFormat(ctx, *req.CodeFormat); err != nil {
			return nil, err
		}
		rule.CodeFormat = *req.CodeFormat
	}

	// 更新字段
	if req.RuleCode != nil {
		rule.RuleCode = *req.RuleCode
	}
	if req.RuleName != nil {
		rule.RuleName = *req.RuleName
	}
	if req.BusinessType != nil {
		rule.BusinessType = *req.BusinessType
	}
	if req.Separator != nil {
		rule.Separator = req.Separator
	}
	if req.ResetFrequency != nil {
		rule.ResetFrequency = *req.ResetFrequency
	}
	if req.SequenceLength != nil {
		rule.SequenceLength = req.SequenceLength
	}
	if req.SequenceStart != nil {
		rule.SequenceStart = req.SequenceStart
	}
	if req.Status != nil {
		rule.Status = *req.Status
	}
	if req.Remark != nil {
		rule.Remark = req.Remark
	}

	// 处理默认规则设置
	if req.IsDefault != nil && *req.IsDefault && !rule.IsDefault {
		if err := repo.SetAsDefault(ctx, rule.ID, rule.BusinessType); err != nil {
			return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "设置默认规则失败").WithCause(err)
		}
		rule.IsDefault = true
	}

	// 保存更新
	if err := repo.Update(ctx, rule); err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "更新编码规则失败").WithCause(err)
	}

	return s.entityToVO(rule), nil
}

// Delete 删除编码规则
func (s *sysCodeRuleServiceImpl) Delete(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()

	if err := repo.Delete(ctx, id); err != nil {
		return apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "删除编码规则失败").WithCause(err)
	}

	return nil
}

// GetByID 获取编码规则详情
func (s *sysCodeRuleServiceImpl) GetByID(ctx context.Context, id uint) (*vo.SysCodeRuleVO, error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()

	rule, err := repo.FindByID(ctx, id)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "编码规则不存在")
	}

	return s.entityToVO(rule), nil
}

// GetPage 分页查询
func (s *sysCodeRuleServiceImpl) GetPage(ctx context.Context, req *dto.SysCodeRuleQueryReq) (*vo.PageResult[vo.SysCodeRuleVO], error) {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()
	pageResult, err := repo.GetPage(ctx, req)
	if err != nil {
		return nil, apperrors.NewError(apperrors.CODE_SYSTEM_INTERNAL, "获取分页数据失败").WithCause(err)
	}

	voList := make([]*vo.SysCodeRuleVO, 0)
	if pageResult.List != nil {
		for _, rule := range pageResult.List.([]*entity.SysCodeRule) {
			voList = append(voList, s.entityToVO(rule))
		}
	}

	return &vo.PageResult[vo.SysCodeRuleVO]{
		List:     voList,
		Total:    pageResult.Total,
		PageSize: pageResult.PageSize,
		PageNum:  pageResult.PageNum,
	}, nil
}

// SetAsDefault 设置为默认规则
func (s *sysCodeRuleServiceImpl) SetAsDefault(ctx context.Context, id uint) error {
	repo := s.GetServiceManager().GetRepositoryManager().GetSysCodeRuleRepository()

	// 获取规则信息
	rule, err := repo.FindByID(ctx, id)
	if err != nil {
		return apperrors.NewError(apperrors.CODE_PARAMS_INVALID, "编码规则不存在")
	}

	return repo.SetAsDefault(ctx, id, rule.BusinessType)
}

// entityToVO 实体转VO
func (s *sysCodeRuleServiceImpl) entityToVO(rule *entity.SysCodeRule) *vo.SysCodeRuleVO {
	vo := &vo.SysCodeRuleVO{
		ID:              rule.ID,
		TenantID:        rule.TenantID,
		AccountBookID:   rule.AccountBookID,
		RuleCode:        rule.RuleCode,
		RuleName:        rule.RuleName,
		BusinessType:    rule.BusinessType,
		CodeFormat:      rule.CodeFormat,
		Separator:       rule.Separator,
		ResetFrequency:  rule.ResetFrequency,
		SequenceLength:  rule.SequenceLength,
		SequenceStart:   rule.SequenceStart,
		CurrentSequence: rule.CurrentSequence,
		LastResetTime:   rule.LastResetTime,
		Status:          rule.Status,
		IsDefault:       rule.IsDefault,
		Remark:          rule.Remark,
		CreatedAt:       rule.CreatedAt,
		UpdatedAt:       rule.UpdatedAt,
		CreatedBy:       rule.CreatedBy,
		UpdatedBy:       rule.UpdatedBy,
	}
	return vo
}
