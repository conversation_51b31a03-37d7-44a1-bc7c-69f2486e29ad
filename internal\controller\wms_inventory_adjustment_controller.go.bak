package controller

import (
	"backend/internal/model/dto"
	"backend/internal/service"
	"backend/pkg/response"
	"strconv"

	"github.com/kataras/iris/v12"
)

// WmsInventoryAdjustmentController 库存调整控制器
type WmsInventoryAdjustmentController struct {
	inventoryAdjustmentService service.WmsInventoryAdjustmentService
}

// NewWmsInventoryAdjustmentController 创建库存调整控制器实例
func NewWmsInventoryAdjustmentController(inventoryAdjustmentService service.WmsInventoryAdjustmentService) *WmsInventoryAdjustmentController {
	return &WmsInventoryAdjustmentController{
		inventoryAdjustmentService: inventoryAdjustmentService,
	}
}

// CreateAdjustment 创建库存调整
// @Summary 创建库存调整
// @Description 创建新的库存调整记录
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentCreateReq true "调整参数"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAdjustmentVO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment [post]
func (c *WmsInventoryAdjustmentController) CreateAdjustment(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryAdjustmentService.CreateAdjustment(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("创建失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetAdjustmentPage 分页查询库存调整
// @Summary 分页查询库存调整
// @Description 支持多维度条件查询库存调整记录
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentQueryReq true "查询条件"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAdjustmentPageVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/page [post]
func (c *WmsInventoryAdjustmentController) GetAdjustmentPage(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentQueryReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	result, err := c.inventoryAdjustmentService.GetAdjustmentPage(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetAdjustmentDetail 获取库存调整详情
// @Summary 获取库存调整详情
// @Description 根据调整ID获取详细信息
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param id path int true "调整ID"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAdjustmentDetailVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "调整记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/{id} [get]
func (c *WmsInventoryAdjustmentController) GetAdjustmentDetail(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的调整ID", ctx)
		return
	}

	result, err := c.inventoryAdjustmentService.GetAdjustmentDetail(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// UpdateAdjustment 更新库存调整
// @Summary 更新库存调整
// @Description 更新库存调整记录信息
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param id path int true "调整ID"
// @Param request body dto.WmsInventoryAdjustmentUpdateReq true "更新参数"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "调整记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/{id} [put]
func (c *WmsInventoryAdjustmentController) UpdateAdjustment(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的调整ID", ctx)
		return
	}

	var req dto.WmsInventoryAdjustmentUpdateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	err = c.inventoryAdjustmentService.UpdateAdjustment(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("更新成功", ctx)
}

// DeleteAdjustment 删除库存调整
// @Summary 删除库存调整
// @Description 删除库存调整记录
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param id path int true "调整ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "调整记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/{id} [delete]
func (c *WmsInventoryAdjustmentController) DeleteAdjustment(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的调整ID", ctx)
		return
	}

	err = c.inventoryAdjustmentService.DeleteAdjustment(ctx.Request().Context(), uint(id))
	if err != nil {
		response.FailWithMessage("删除失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("删除成功", ctx)
}

// ApproveAdjustment 审批库存调整
// @Summary 审批库存调整
// @Description 审批或拒绝库存调整申请
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param id path int true "调整ID"
// @Param request body dto.WmsInventoryAdjustmentApprovalReq true "审批参数"
// @Success 200 {object} response.Response "审批成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "调整记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/{id}/approve [post]
func (c *WmsInventoryAdjustmentController) ApproveAdjustment(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的调整ID", ctx)
		return
	}

	var req dto.WmsInventoryAdjustmentApprovalReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	err = c.inventoryAdjustmentService.ApproveAdjustment(ctx.Request().Context(), uint(id), &req)
	if err != nil {
		response.FailWithMessage("审批失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("审批成功", ctx)
}

// RejectAdjustment 拒绝库存调整
// @Summary 拒绝库存调整
// @Description 拒绝库存调整申请
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param id path int true "调整ID"
// @Param request body dto.WmsInventoryAdjustmentRejectReq true "拒绝参数"
// @Success 200 {object} response.Response "拒绝成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "调整记录不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/{id}/reject [post]
func (c *WmsInventoryAdjustmentController) RejectAdjustment(ctx iris.Context) {
	idStr := ctx.Params().Get("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的调整ID", ctx)
		return
	}

	var req dto.WmsInventoryAdjustmentRejectReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	err = c.inventoryAdjustmentService.RejectAdjustment(ctx.Request().Context(), uint(id), req.ApprovedBy, req.Reason)
	if err != nil {
		response.FailWithMessage("拒绝调整失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// ExecuteAdjustment 执行库存调整
// @Summary 执行库存调整
// @Description 批量执行已审批的库存调整
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentExecuteReq true "执行参数"
// @Success 200 {object} response.Response "执行成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/execute [post]
func (c *WmsInventoryAdjustmentController) ExecuteAdjustment(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentExecuteReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	err := c.inventoryAdjustmentService.ExecuteAdjustment(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("执行失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("执行成功", ctx)
}

// BatchCreateAdjustment 批量创建库存调整
// @Summary 批量创建库存调整
// @Description 批量创建多个库存调整记录
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentBatchCreateReq true "批量创建参数"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryAdjustmentVO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/batch [post]
func (c *WmsInventoryAdjustmentController) BatchCreateAdjustment(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentBatchCreateReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryAdjustmentService.BatchCreateAdjustment(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("批量创建失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetAdjustmentStats 获取库存调整统计
// @Summary 获取库存调整统计
// @Description 获取库存调整的统计分析数据
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentStatsReq true "统计参数"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAdjustmentStatsVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/stats [post]
func (c *WmsInventoryAdjustmentController) GetAdjustmentStats(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentStatsReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryAdjustmentService.GetAdjustmentStats(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// ExportAdjustment 导出库存调整数据
// @Summary 导出库存调整数据
// @Description 根据查询条件导出库存调整数据到Excel或CSV文件
// @Tags 库存调整
// @Accept json
// @Produce application/octet-stream
// @Param request body dto.WmsInventoryAdjustmentExportReq true "导出条件"
// @Success 200 {file} file "导出文件"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/export [post]
func (c *WmsInventoryAdjustmentController) ExportAdjustment(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentExportReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	fileData, err := c.inventoryAdjustmentService.ExportAdjustment(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("导出失败: "+err.Error(), ctx)
		return
	}

	// 设置响应头
	format := req.GetExportFormat()
	var contentType, fileName string
	if format == "csv" {
		contentType = "text/csv"
		fileName = "inventory_adjustment_export.csv"
	} else {
		contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		fileName = "inventory_adjustment_export.xlsx"
	}

	ctx.Header("Content-Type", contentType)
	ctx.Header("Content-Disposition", "attachment; filename="+fileName)
	ctx.Write(fileData)
}

// ImportAdjustment 导入库存调整数据
// @Summary 导入库存调整数据
// @Description 从Excel或CSV文件导入库存调整数据
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentImportReq true "导入参数"
// @Success 200 {object} response.Response{data=vo.ImportResultVO} "导入成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/import [post]
func (c *WmsInventoryAdjustmentController) ImportAdjustment(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentImportReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryAdjustmentService.ImportAdjustment(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("导入失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// GetAdjustmentReasons 获取调整原因列表
// @Summary 获取调整原因列表
// @Description 获取可用的库存调整原因列表
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentReasonReq true "查询参数"
// @Success 200 {object} response.Response{data=[]vo.WmsInventoryAdjustmentReasonVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/reasons [post]
func (c *WmsInventoryAdjustmentController) GetAdjustmentReasons(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentReasonReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryAdjustmentService.GetAdjustmentReasons(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}

// BatchApproveAdjustment 批量审批库存调整
// @Summary 批量审批库存调整
// @Description 批量审批库存调整申请
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentBatchApproveReq true "批量审批参数"
// @Success 200 {object} response.Response "审批成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/batch/approve [post]
func (c *WmsInventoryAdjustmentController) BatchApproveAdjustment(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentBatchApproveReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	err := c.inventoryAdjustmentService.BatchApproveAdjustment(ctx.Request().Context(), req.IDs, req.ApprovedBy)
	if err != nil {
		response.FailWithMessage("批量审批失败: "+err.Error(), ctx)
		return
	}

	response.Ok(ctx)
}

// GetAdjustmentStatistics 获取库存调整统计
// @Summary 获取库存调整统计
// @Description 获取库存调整统计分析数据
// @Tags 库存调整
// @Accept json
// @Produce json
// @Param request body dto.WmsInventoryAdjustmentStatsReq true "统计参数"
// @Success 200 {object} response.Response{data=vo.WmsInventoryAdjustmentStatsVO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /api/wms/inventory/adjustment/statistics [post]
func (c *WmsInventoryAdjustmentController) GetAdjustmentStatistics(ctx iris.Context) {
	var req dto.WmsInventoryAdjustmentStatsReq
	if err := ctx.ReadJSON(&req); err != nil {
		response.FailWithMessage("请求参数格式错误", ctx)
		return
	}

	result, err := c.inventoryAdjustmentService.GetAdjustmentStatistics(ctx.Request().Context(), &req)
	if err != nil {
		response.FailWithMessage("获取统计数据失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(result, ctx)
}
