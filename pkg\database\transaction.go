package database

import (
	"backend/pkg/logger"
	"context"
	"database/sql"
	"fmt"

	"gorm.io/gorm"
)

// 事务隔离级别常量
const (
	// LevelReadUncommitted 读未提交
	LevelReadUncommitted = "READ UNCOMMITTED"
	// LevelReadCommitted 读已提交
	LevelReadCommitted = "READ COMMITTED"
	// LevelRepeatableRead 可重复读
	LevelRepeatableRead = "REPEATABLE READ"
	// LevelSerializable 串行化
	LevelSerializable = "SERIALIZABLE"
)

// TransactionManager 事务管理器
type TransactionManager struct {
	db *gorm.DB
}

// NewTransactionManager 创建事务管理器
func NewTransactionManager(db *gorm.DB) *TransactionManager {
	if db == nil {
		// Log the error first, then panic
		errorMsg := "无法使用 nil *gorm.DB 初始化 TransactionManager"
		logger.Error(errorMsg) // Assuming logger has Error or Errorf
		panic(errorMsg)
	}
	return &TransactionManager{db: db}
}

// WithTransaction 在事务中执行函数
func (tm *TransactionManager) WithTransaction(txFunc TxFunc) error {
	return tm.db.Transaction(func(tx *gorm.DB) error {
		// 直接传递 txFunc，因为它的签名与 gorm.Transaction 需要的函数匹配
		return txFunc(tx)
	})
}

// WithTransactionByLevel 在指定隔离级别的事务中执行函数
func (tm *TransactionManager) WithTransactionByLevel(level string, txFunc TxFunc) error {
	// 设置事务隔离级别的SQL语句
	isolationSQL := ""
	switch level {
	case LevelReadUncommitted, LevelReadCommitted, LevelRepeatableRead, LevelSerializable:
		isolationSQL = fmt.Sprintf("SET TRANSACTION ISOLATION LEVEL %s", level)
	default:
		return fmt.Errorf("无效的事务隔离级别: %s", level)
	}

	return tm.db.Transaction(func(tx *gorm.DB) error {
		// 首先设置隔离级别
		if err := tx.Exec(isolationSQL).Error; err != nil {
			logger.Errorf("设置事务隔离级别失败: %v", err)
			return fmt.Errorf("设置事务隔离级别失败: %w", err)
		}
		// 执行用户定义的函数
		return txFunc(tx)
	}, &sql.TxOptions{Isolation: tm.mapIsolationLevel(level)})
}

// mapIsolationLevel 将字符串级别映射到 sql.IsolationLevel
// 注意：GORM v1.2x Transaction 方法的 options 参数现在接受 *sql.TxOptions
func (tm *TransactionManager) mapIsolationLevel(level string) sql.IsolationLevel {
	switch level {
	case LevelReadUncommitted:
		return sql.LevelReadUncommitted
	case LevelReadCommitted:
		return sql.LevelReadCommitted
	case LevelRepeatableRead:
		return sql.LevelRepeatableRead
	case LevelSerializable:
		return sql.LevelSerializable
	default:
		return sql.LevelDefault // 默认隔离级别
	}
}

// WithTransactionCtx 在带有上下文的事务中执行函数
func (tm *TransactionManager) WithTransactionCtx(ctx context.Context, txFunc TxFunc) error {
	return tm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return txFunc(tx)
	})
}

// Execute 执行事务，支持自动回滚和提交，并处理 panic
func (tm *TransactionManager) Execute(txFunc TxFunc) (err error) {
	tx := tm.db.Begin()
	if tx.Error != nil {
		logger.Errorf("开始事务失败: %v", tx.Error)
		return fmt.Errorf("开始事务失败: %w", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			// 将 panic 转换为错误或记录
			err = fmt.Errorf("事务执行期间发生 Panic: %v", r)
			logger.Errorf("事务执行期间发生 Panic: %v", r)
			// 根据需要，可能希望重新 panic: panic(r)
		} else if err != nil {
			// 如果 txFunc 返回错误，则回滚
			if rbErr := tx.Rollback().Error; rbErr != nil {
				logger.Errorf("事务回滚失败: %v (原始错误: %v)", rbErr, err)
				err = fmt.Errorf("事务回滚失败: %w (原始错误: %s)", rbErr, err.Error()) // 包装错误
			} else {
				logger.Warnf("事务因错误而回滚: %v", err)
			}
		} else {
			// 如果没有 panic 且没有错误，则提交
			if cmErr := tx.Commit().Error; cmErr != nil {
				logger.Errorf("事务提交失败: %v", cmErr)
				err = fmt.Errorf("事务提交失败: %w", cmErr)
			}
		}
	}()

	// 执行用户定义的事务函数
	err = txFunc(tx)

	return err // 返回 txFunc 的错误或 Commit/Rollback/Panic 错误
}

// NestedTransaction 支持嵌套事务的实现
// GORM 本身通过 SavePoint 支持嵌套事务
func (tm *TransactionManager) NestedTransaction(db *gorm.DB, txFunc TxFunc) error {
	// GORM 的 Transaction 方法会自动处理嵌套：
	// 如果当前 db 已经是事务，它会创建一个 SavePoint。
	// 如果不是，它会开始一个新的事务。
	return db.Transaction(func(tx *gorm.DB) error {
		return txFunc(tx)
	})
}

// LoggingMiddleware 日志记录中间件
// 参数 next 的类型改为 TxFunc
func LoggingMiddleware(db *gorm.DB, next TxFunc) error {
	logger.Info("事务中间件：开始执行")
	defer logger.Info("事务中间件：执行完成")
	err := next(db)
	if err != nil {
		logger.Errorf("事务中间件：执行出错: %v", err)
	}
	return err
}

// ErrorHandlingMiddleware 错误处理中间件
// 参数 next 的类型改为 TxFunc
func ErrorHandlingMiddleware(db *gorm.DB, next TxFunc) error {
	err := next(db)
	if err != nil {
		logger.Errorf("事务中间件：捕获到错误: %v", err)
	}
	return err
}

// WithMiddleware 使用中间件执行事务
// 确保 TxFunc 和 TransactionMiddleware 类型是从 interface.go 获取的
func (tm *TransactionManager) WithMiddleware(txFunc TxFunc, middlewares ...TransactionMiddleware) error {
	// 构建执行链
	wrapped := txFunc
	// 从后向前包装中间件
	for i := len(middlewares) - 1; i >= 0; i-- {
		middleware := middlewares[i]
		// 保留当前的 wrapped 函数供闭包捕获
		next := wrapped
		wrapped = func(tx *gorm.DB) error {
			// 调用当前中间件，传入 DB 连接和下一个处理函数 (next)
			// 注意：中间件的签名也需要是 TransactionMiddleware (db *gorm.DB, next TxFunc) error
			return middleware(tx, next)
		}
	}

	// 开始事务并执行最终包装后的函数
	// 使用 Execute 方法，因为它包含了 panic 恢复和自动提交/回滚逻辑
	return tm.Execute(wrapped)
}
