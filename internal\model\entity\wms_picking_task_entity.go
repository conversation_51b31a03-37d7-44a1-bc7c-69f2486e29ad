package entity

import (
	"time"
)

// 拣货任务状态枚举
type WmsPickingTaskStatus string

const (
	PickingTaskStatusPending    WmsPickingTaskStatus = "PENDING"     // 待分配
	PickingTaskStatusAssigned   WmsPickingTaskStatus = "ASSIGNED"    // 已分配
	PickingTaskStatusInProgress WmsPickingTaskStatus = "IN_PROGRESS" // 进行中
	PickingTaskStatusCompleted  WmsPickingTaskStatus = "COMPLETED"   // 已完成
	PickingTaskStatusCancelled  WmsPickingTaskStatus = "CANCELLED"   // 已取消
)

// 拣货策略枚举
type WmsPickingStrategy string

const (
	PickingStrategyByOrder WmsPickingStrategy = "BY_ORDER" // 按单拣货
	PickingStrategyBatch   WmsPickingStrategy = "BATCH"    // 批量拣货
	PickingStrategyWave    WmsPickingStrategy = "WAVE"     // 波次拣货
	PickingStrategyZone    WmsPickingStrategy = "ZONE"     // 分区拣货
)

// WmsPickingTask 对应数据库中的 wms_picking_task 表
// 存储拣货任务头信息
type WmsPickingTask struct {
	AccountBookEntity // 嵌入账套实体，提供账套强绑定

	TaskNo           string `gorm:"column:task_no;size:50;not null;uniqueIndex:idx_picking_task_no_account;comment:拣货任务号" json:"taskNo"`
	NotificationID   uint   `gorm:"column:notification_id;not null;comment:关联出库通知单ID" json:"notificationId"`
	PickingStrategy  string `gorm:"column:picking_strategy;size:20;not null;comment:拣货策略" json:"pickingStrategy"`
	WaveNo           *string `gorm:"column:wave_no;size:50;comment:波次号" json:"waveNo"`
	Priority         int    `gorm:"column:priority;not null;default:5;comment:优先级" json:"priority"`
	Status           string `gorm:"column:status;size:20;default:'PENDING';comment:任务状态" json:"status"`

	// 分配信息
	AssignedUserID *uint      `gorm:"column:assigned_user_id;comment:分配拣货员ID" json:"assignedUserId"`
	AssignedTime   *time.Time `gorm:"column:assigned_time;comment:分配时间" json:"assignedTime"`

	// 执行信息
	StartTime    *time.Time `gorm:"column:start_time;comment:开始时间" json:"startTime"`
	CompleteTime *time.Time `gorm:"column:complete_time;comment:完成时间" json:"completeTime"`

	// 统计信息
	TotalItems     int     `gorm:"column:total_items;default:0;comment:总物料种类数" json:"totalItems"`
	TotalQty       float64 `gorm:"column:total_qty;type:numeric(12,4);default:0;comment:总数量" json:"totalQty"`
	PickedItems    int     `gorm:"column:picked_items;default:0;comment:已拣物料种类数" json:"pickedItems"`
	PickedQty      float64 `gorm:"column:picked_qty;type:numeric(12,4);default:0;comment:已拣数量" json:"pickedQty"`

	// 路径优化信息
	EstimatedDistance *float64 `gorm:"column:estimated_distance;type:numeric(10,2);comment:预计拣货距离(米)" json:"estimatedDistance"`
	EstimatedTime     *int     `gorm:"column:estimated_time;comment:预计拣货时间(分钟)" json:"estimatedTime"`
	ActualDistance    *float64 `gorm:"column:actual_distance;type:numeric(10,2);comment:实际拣货距离(米)" json:"actualDistance"`
	ActualTime        *int     `gorm:"column:actual_time;comment:实际拣货时间(分钟)" json:"actualTime"`

	Remark *string `gorm:"column:remark;type:text;comment:备注" json:"remark"`

	// 关联字段
	OutboundNotification WmsOutboundNotification `gorm:"foreignKey:NotificationID;references:ID" json:"outboundNotification,omitempty"` // 关联的出库通知单
	Details              []WmsPickingTaskDetail  `gorm:"foreignKey:TaskID;references:ID" json:"details,omitempty"`                      // 关联的拣货任务明细
	Shipment             *WmsShipment            `gorm:"foreignKey:PickingTaskID;references:ID" json:"shipment,omitempty"`              // 关联的发运单
}

// TableName 指定数据库表名方法
func (WmsPickingTask) TableName() string {
	return "wms_picking_task"
}

// GetCompletionRate 获取完成率
func (w *WmsPickingTask) GetCompletionRate() float64 {
	if w.TotalQty == 0 {
		return 0
	}
	return (w.PickedQty / w.TotalQty) * 100
}

// IsCompleted 判断是否已完成
func (w *WmsPickingTask) IsCompleted() bool {
	return w.Status == string(PickingTaskStatusCompleted)
}

// CanStart 判断是否可以开始
func (w *WmsPickingTask) CanStart() bool {
	return w.Status == string(PickingTaskStatusAssigned) && w.AssignedUserID != nil
}

// CanComplete 判断是否可以完成
func (w *WmsPickingTask) CanComplete() bool {
	return w.Status == string(PickingTaskStatusInProgress) && w.PickedQty >= w.TotalQty
}
