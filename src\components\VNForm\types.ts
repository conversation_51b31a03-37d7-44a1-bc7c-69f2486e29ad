import type { FormItemRule } from 'element-plus';
import type { VNTableProps } from '../VNTable/types';

// 定义 Props 的类型以增强代码清晰度和类型安全
export interface HeaderField {
  field: string;                // 字段名，对应表单数据中的键
  label: string;               // 字段标签
  type?: 'text' | 'textarea' | 'number' | 'select' | 'date' | 'radio' | 'checkbox-group' | 'checkbox' | 'upload' | 'slot' | 'switch' | 'password';
  group?: string;              // NEW: 分组名称 (可选)
  placeholder?: string;        // 占位文本
  span?: number;               // 栅格宽度 (基础，会被md/lg/xl覆盖)
  xs?: number;                 // 响应式布局配置
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  disabled?: boolean;          // 是否禁用
  clearable?: boolean;         // 是否可清除
  options?: Array<{           // 选项数据（用于select、radio、checkbox等）
    label: string;
    value: any;
    disabled?: boolean;
  }> | import('vue').Ref<Array<{ label: string, value: any, disabled?: boolean }>>;
  props?: Record<string, any>; // 传递给底层组件的属性
  rules?: FormItemRule[];      // 验证规则
  visible?: boolean | ((form: Record<string, any>) => boolean); // 动态显示控制
  // 数字输入特定属性
  min?: number;
  max?: number;
  precision?: number;
  step?: number;
  // 文本域特定属性
  rows?: number;
  autosize?: boolean | { minRows?: number; maxRows?: number };
  // 日期选择器特定属性
  format?: string;
  valueFormat?: string;
  // 上传组件特定属性
  action?: string;
  headers?: Record<string, string>;
  multiple?: boolean;
  limit?: number;
  formatter?: (value: any, fieldConfig: HeaderField, formData: Record<string, any>) => string;
}

export interface DetailTableConfig {
  title?: string;
  tableProps: Partial<VNTableProps>;
}

// NEW: Status Tag Configuration Interface
export interface StatusTagConfig {
  visible: boolean;
  content: string;
  type: 'success' | 'info' | 'warning' | 'danger';
}

export interface VNFormProps {
  title?: string;                           // 表单标题
  headerFields: HeaderField[];              // 表单字段配置
  modelValue: Record<string, any>;          // v-model绑定值
  detailTables?: DetailTableConfig[];       // 明细表配置
  groupTitleType?: 'h4' | 'divider' | 'none'; // NEW: 分组标题样式 (默认 'h4')
  labelWidth?: string | number;             // 标签宽度
  labelPosition?: 'left' | 'right' | 'top'; // 标签位置
  size?: 'large' | 'default' | 'small';     // 表单尺寸
  inline?: boolean;                         // 行内表单模式
  disabled?: boolean;                       // 整体禁用 (外部传入)
  showSubmitButtons?: boolean;              // 是否显示提交按钮组
  submitButtonText?: string;                // 提交按钮文本
  cancelButtonText?: string;                // 取消按钮文本
  loading?: boolean;                        // 加载状态
  defaultColumns?: number;                  // 新增：默认布局列数 (例如 2 或 3)
  /**
   * NEW: Status tags to display in the header
   * The parent component provides the configuration for each tag.
   */
  statusTags?: StatusTagConfig[];

  /**
   * 当明细表需要添加新行时调用。
   * @param tableIndex 触发事件的明细表在其 detailTables 数组中的索引。
   * @returns Promise 或直接返回一个新行的数据对象 (应包含唯一的 rowKey)。如果返回 null/undefined 或 Promise reject，则不添加行。
   */
  onRowAdd?: (tableIndex: number) => Promise<Record<string, any> | null | undefined> | Record<string, any> | null | undefined;

  /**
   * 当明细表需要删除行时调用。
   * @param tableIndex 触发事件的明细表索引。
   * @param row 被删除的行数据。
   * @param rowIndex 被删除行的在当前显示数据中的索引 (可能受筛选/分页影响)。
   * @returns Promise 或直接返回一个布尔值，指示删除操作是否成功。
   */
  onRowDelete?: (tableIndex: number, row: any, rowIndex: number) => Promise<boolean> | boolean;
}

export interface VNFormEmits {
  (e: 'update:modelValue', value: Record<string, any>): void;
  (e: 'submit', value: Record<string, any>): void;
  (e: 'cancel'): void;
  (e: 'change', field: string, value: any, form: Record<string, any>): void;
  (e: 'edit', tableIndex: number, row: any): void;
  (e: 'cell-click', tableIndex: number, row: any, column: any, cell: any, event: Event): void;
  (e: 'cell-dblclick', tableIndex: number, row: any, column: any, cell: any, event: Event): void;
}

export interface VNFormMethods {
  resetForm: () => void;
  validateForm: () => Promise<boolean>; // This will now validate header AND details
  clearValidate: (fields?: string | string[]) => void;
  scrollToField: (field: string) => void;
  getDetailTable: (index: number) => any; // 新增：获取明细表实例
} 