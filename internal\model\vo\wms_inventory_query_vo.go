package vo

import (
	"backend/internal/model/entity"
	"time"
)

// WmsInventoryVO 库存视图对象
type WmsInventoryVO struct {
	ID             uint       `json:"id"`
	WarehouseID    uint       `json:"warehouseId"`
	WarehouseName  string     `json:"warehouseName"`
	LocationID     uint       `json:"locationId"`
	LocationCode   string     `json:"locationCode"`
	ItemID         uint       `json:"itemId"`
	ItemSku        string     `json:"itemSku"`
	ItemName       string     `json:"itemName"`
	ItemSpec       *string    `json:"itemSpec"`
	BatchNo        *string    `json:"batchNo"`
	Quantity       float64    `json:"quantity"`
	AllocatedQty   float64    `json:"allocatedQty"`
	FrozenQty      float64    `json:"frozenQty"`
	AvailableQty   float64    `json:"availableQty"`
	UnitOfMeasure  string     `json:"unitOfMeasure"`
	Status         string     `json:"status"`
	StatusName     string     `json:"statusName"`
	ProductionDate *time.Time `json:"productionDate"`
	ExpiryDate     *time.Time `json:"expiryDate"`
	LastUpdated    time.Time  `json:"lastUpdated"`

	// 扩展信息
	ItemCategory *string `json:"itemCategory"`
	ItemBrand    *string `json:"itemBrand"`
	LocationType *string `json:"locationType"`
	LocationZone *string `json:"locationZone"`

	// 业务状态
	IsExpired    bool `json:"isExpired"`
	IsNearExpiry bool `json:"isNearExpiry"`
	DaysToExpiry *int `json:"daysToExpiry"`
	IsLowStock   bool `json:"isLowStock"`
	IsOverStock  bool `json:"isOverStock"`
}

// WmsInventoryDetailVO 库存详情视图对象
type WmsInventoryDetailVO struct {
	WmsInventoryVO

	// 详细信息
	Item      *MtlItemVO     `json:"item"`
	Location  *WmsLocationVO `json:"location"`
	Warehouse *WmsLocationVO `json:"warehouse"`

	// 历史记录
	TransactionLogs []WmsInventoryTransactionLogVO  `json:"transactionLogs"`
	MovementHistory []WmsInventoryMovementHistoryVO `json:"movementHistory"`

	// 统计信息
	Stats *WmsInventoryStatsVO `json:"stats"`
}

// WmsInventoryMovementHistoryVO 库存移动历史视图对象
type WmsInventoryMovementHistoryVO struct {
	ID               uint      `json:"id"`
	MovementNo       string    `json:"movementNo"`
	FromLocationCode string    `json:"fromLocationCode"`
	ToLocationCode   string    `json:"toLocationCode"`
	Quantity         float64   `json:"quantity"`
	MovementType     string    `json:"movementType"`
	MovementTypeName string    `json:"movementTypeName"`
	MovementReason   *string   `json:"movementReason"`
	OperatorID       uint      `json:"operatorId"`
	OperatorName     string    `json:"operatorName"`
	CompletedAt      time.Time `json:"completedAt"`
}

// WmsInventoryStatsVO 库存统计视图对象
type WmsInventoryStatsVO struct {
	// 基础统计
	TotalInbound    float64 `json:"totalInbound"`
	TotalOutbound   float64 `json:"totalOutbound"`
	TotalAdjustment float64 `json:"totalAdjustment"`
	TotalMovement   float64 `json:"totalMovement"`

	// 时间统计
	FirstInboundAt *time.Time `json:"firstInboundAt"`
	LastInboundAt  *time.Time `json:"lastInboundAt"`
	LastOutboundAt *time.Time `json:"lastOutboundAt"`
	LastMovementAt *time.Time `json:"lastMovementAt"`

	// 周转统计
	TurnoverRate    *float64 `json:"turnoverRate"`
	DaysInStock     *int     `json:"daysInStock"`
	AverageStayDays *float64 `json:"averageStayDays"`
}

// WmsInventorySummaryVO 库存汇总视图对象
type WmsInventorySummaryVO struct {
	GroupKey      string  `json:"groupKey"`
	GroupName     string  `json:"groupName"`
	TotalItems    int64   `json:"totalItems"`
	TotalQuantity float64 `json:"totalQuantity"`
	AvailableQty  float64 `json:"availableQty"`
	AllocatedQty  float64 `json:"allocatedQty"`
	FrozenQty     float64 `json:"frozenQty"`
	TotalValue    float64 `json:"totalValue"`

	// 按状态分组
	StatusBreakdown []WmsInventoryStatusBreakdownVO `json:"statusBreakdown"`
}

// WmsInventoryStatusBreakdownVO 库存状态分解视图对象
type WmsInventoryStatusBreakdownVO struct {
	Status     string  `json:"status"`
	StatusName string  `json:"statusName"`
	Count      int64   `json:"count"`
	Quantity   float64 `json:"quantity"`
	Percentage float64 `json:"percentage"`
}

// WmsInventoryTurnoverVO 库存周转分析视图对象
type WmsInventoryTurnoverVO struct {
	ItemID        uint   `json:"itemId"`
	ItemSku       string `json:"itemSku"`
	ItemName      string `json:"itemName"`
	WarehouseID   uint   `json:"warehouseId"`
	WarehouseName string `json:"warehouseName"`

	// 周转指标
	TurnoverRate float64 `json:"turnoverRate"`
	TurnoverDays float64 `json:"turnoverDays"`
	AverageStock float64 `json:"averageStock"`
	TotalSales   float64 `json:"totalSales"`

	// 分类
	ABCCategory   string `json:"abcCategory"`
	VelocityLevel string `json:"velocityLevel"`

	// 趋势数据
	TrendData []WmsInventoryTurnoverTrendVO `json:"trendData"`
}

// WmsInventoryTurnoverTrendVO 库存周转趋势数据
type WmsInventoryTurnoverTrendVO struct {
	Period       string  `json:"period"`
	AverageStock float64 `json:"averageStock"`
	Sales        float64 `json:"sales"`
	TurnoverRate float64 `json:"turnoverRate"`
}

// WmsInventoryAvailableItemVO 可用库存项视图对象
type WmsInventoryAvailableItemVO struct {
	InventoryID    uint       `json:"inventoryId"`
	LocationCode   string     `json:"locationCode"`
	BatchNo        *string    `json:"batchNo"`
	AvailableQty   float64    `json:"availableQty"`
	ProductionDate *time.Time `json:"productionDate"`
	ExpiryDate     *time.Time `json:"expiryDate"`
	Priority       int        `json:"priority"`
}

// WmsInventoryAllocationSuggestionVO 库存分配建议视图对象
type WmsInventoryAllocationSuggestionVO struct {
	InventoryID  uint    `json:"inventoryId"`
	LocationCode string  `json:"locationCode"`
	BatchNo      *string `json:"batchNo"`
	SuggestedQty float64 `json:"suggestedQty"`
	Reason       string  `json:"reason"`
	Priority     int     `json:"priority"`
}

// WmsInventoryABCAnalysisVO ABC分析视图对象
type WmsInventoryABCAnalysisVO struct {
	ItemID               uint    `json:"itemId"`
	ItemSku              string  `json:"itemSku"`
	ItemName             string  `json:"itemName"`
	Category             string  `json:"category"`
	Value                float64 `json:"value"`
	Quantity             float64 `json:"quantity"`
	Frequency            int64   `json:"frequency"`
	Percentage           float64 `json:"percentage"`
	CumulativePercentage float64 `json:"cumulativePercentage"`
}

// WmsInventoryPageVO 库存分页视图对象
type WmsInventoryPageVO struct {
	List  []WmsInventoryVO `json:"list"`
	Total int64            `json:"total"`
}

// NewWmsInventoryVO 创建库存视图对象
func NewWmsInventoryVO(inventory *entity.WmsInventory) *WmsInventoryVO {
	vo := &WmsInventoryVO{
		ID:             inventory.ID,
		WarehouseID:    inventory.WarehouseID,
		LocationID:     inventory.LocationID,
		ItemID:         inventory.ItemID,
		BatchNo:        inventory.BatchNo,
		Quantity:       inventory.Quantity,
		AllocatedQty:   inventory.AllocatedQty,
		FrozenQty:      inventory.FrozenQty,
		AvailableQty:   inventory.AvailableQuantity(),
		UnitOfMeasure:  inventory.UnitOfMeasure,
		Status:         inventory.Status,
		StatusName:     inventory.GetStatusName(),
		ProductionDate: inventory.ProductionDate,
		ExpiryDate:     inventory.ExpiryDate,
		LastUpdated:    inventory.UpdatedAt,
		IsExpired:      inventory.IsExpired(),
		IsNearExpiry:   inventory.IsNearExpiry(), // 检查是否即将过期
		IsLowStock:     false,                    // 需要根据业务规则判断
		IsOverStock:    false,                    // 需要根据业务规则判断
	}

	// 计算到期天数
	if inventory.ExpiryDate != nil {
		days := int(time.Until(*inventory.ExpiryDate).Hours() / 24)
		vo.DaysToExpiry = &days
	}

	// 填充关联信息 - 由于移除了关联关系，这些信息需要在Service层单独查询并填充
	// if inventory.Item != nil {
	//     vo.ItemSku = inventory.Item.Sku
	//     vo.ItemName = inventory.Item.Name
	//     vo.ItemSpec = inventory.Item.Specification
	//     vo.ItemCategory = inventory.Item.CategoryCode
	//     vo.ItemBrand = nil // 实体中没有Brand字段
	// }

	// if inventory.Location != nil {
	//     vo.LocationCode = inventory.Location.Code
	//     locationTypeStr := string(inventory.Location.Type)
	//     vo.LocationType = &locationTypeStr
	//     vo.LocationZone = nil // WmsLocation实体中没有Zone字段

	//     // 仓库信息需要通过 WarehouseID 单独查询
	//     // WmsLocation 实体中没有 Warehouse 关联字段
	// }

	return vo
}

// NewWmsInventoryDetailVO 创建库存详情视图对象
func NewWmsInventoryDetailVO(inventory *entity.WmsInventory) *WmsInventoryDetailVO {
	vo := &WmsInventoryDetailVO{
		WmsInventoryVO: *NewWmsInventoryVO(inventory),
	}

	// 填充详细信息 - 由于移除了关联关系，这些信息需要在Service层单独查询并填充
	// if inventory.Item != nil {
	//     vo.Item = NewMtlItemVO(inventory.Item)
	// }

	// if inventory.Location != nil {
	//     vo.Location = NewWmsLocationVO(inventory.Location)

	//     // 仓库信息通过库位的仓库关联获取
	//     if inventory.Location.WarehouseID != 0 {
	//         // 这里需要查询仓库信息，暂时设置为nil
	//         // 实际使用时应该通过Repository查询仓库信息
	//         vo.Warehouse = nil
	//     }
	// }

	return vo
}
