# VN 组件库

此目录包含为项目开发的自定义可复用 Vue 组件，主要使用 Vue 3、TypeScript 构建，并利用了 Element Plus UI 库。

## 组件列表

以下是可用的 VN 组件摘要。每个组件位于其自己的子目录中，通常包含：

* `index.vue`: 主要的组件实现。
* `types.ts`: Props、Emits 及相关数据结构的 TypeScript 类型定义。
* `README.md`: 特定组件的详细文档（Props、Events、Slots、使用示例）。
* `example.vue`: 用于演示和测试的使用示例页面（可通过路由访问）。

| 组件名         | 目录              | 描述                                      | 文档链接                  |
| :------------- | :---------------- | :---------------------------------------- | :------------------------ |
| `VNLogin`      | [`./VNLogin/`]    | 处理用户登录界面及交互。                  | [`./VNLogin/README.md`]     |
| `VNFrame`      | [`./VNFrame/`]    | 主应用程序布局框架结构。                  | [`./VNFrame/README.md`]     |
| `VNTabs`       | [`./VNTabs/`]     | 提供标签页界面。                          | [`./VNTabs/README.md`]      |
| `VNTree`       | [`./VNTree/`]     | 以树状结构显示层级数据。                  | [`./VNTree/README.md`]      |
| `VNDrawer`     | [`./VNDrawer/`]   | 滑动面板组件（抽屉）。                    | [`./VNDrawer/README.md`]    |
| `VNDialog`     | [`./VNDialog/`]   | 模态对话框组件。                          | [`./VNDialog/README.md`]    |
| `VNChart`      | [`./VNChart/`]    | 用于集成图表的包装器。                    | [`./VNChart/README.md`]     |
| `VNStatusBar`  | [`./VNStatusBar/`] | 显示状态信息，通常位于底部。              | [`./VNStatusBar/README.md`]   |
| `VNSidebar`    | [`./VNSidebar/`]  | 侧边栏导航菜单组件。                      | [`./VNSidebar/README.md`]   |
| `VNBreadcrumb` | [`./VNBreadcrumb/`]| 面包屑导航组件。                          | [`./VNBreadcrumb/README.md`]|
| `VNForm`       | [`./VNForm/`]     | 通用表单处理组件。                        | [`./VNForm/README.md`]      |
| `VNTable`      | [`./VNTable/`]    | 通用数据表格组件。                        | [`./VNTable/README.md`]     |

*(注意：像 `AppNav.vue` 或 `EPExamples/` 这样的其他文件/目录可能用于特定的应用导航或演示目的，并非可复用 VN 组件库的一部分。)*
