# 软删除唯一索引问题解决方案

## 🚨 问题描述

在支持软删除的系统中，传统的唯一索引会导致以下问题：

```sql
-- 问题场景示例：
-- 1. 创建物料 SKU='ABC001'
INSERT INTO mtl_item (sku, name, deleted_at) VALUES ('ABC001', '物料A', NULL);

-- 2. 软删除该物料
UPDATE mtl_item SET deleted_at = NOW() WHERE sku = 'ABC001';

-- 3. 尝试创建相同SKU的新物料 (失败！)
INSERT INTO mtl_item (sku, name, deleted_at) VALUES ('ABC001', '新物料A', NULL);
-- ERROR: duplicate key value violates unique constraint "idx_tenant_account_sku"
```

**根本原因**: 传统唯一约束不区分软删除记录，已删除的记录仍占用唯一约束空间。

## ✅ 解决方案：部分唯一索引 (Partial Unique Index)

### 核心原理

使用 PostgreSQL 的**WHERE 条件过滤唯一索引**，只对未删除的记录(`deleted_at IS NULL`)建立唯一约束。

### 实施步骤

#### 1. 移除 GORM 层面的唯一索引定义

```go
// ❌ 修改前 - 会导致软删除问题
type MtlItem struct {
    AccountBookEntity
    Sku string `gorm:"column:sku;uniqueIndex:idx_tenant_account_sku,priority:3"`
}

// ✅ 修改后 - 只保留普通索引，唯一约束在数据库层面处理
type MtlItem struct {
    AccountBookEntity
    Sku string `gorm:"column:sku;index"`  // 只保留普通索引
}
```

#### 2. 在数据库初始化中创建部分唯一索引

```sql
-- mtl_item 的 SKU 唯一约束 (仅对未删除记录)
CREATE UNIQUE INDEX IF NOT EXISTS idx_mtl_item_active_tenant_account_sku
ON mtl_item (tenant_id, account_book_id, sku)
WHERE deleted_at IS NULL;

-- mtl_item_package_unit 的组合唯一约束 (仅对未删除记录)
CREATE UNIQUE INDEX IF NOT EXISTS idx_mtl_item_package_unit_active_tenant_account_item_unit
ON mtl_item_package_unit (tenant_id, account_book_id, item_id, unit_name)
WHERE deleted_at IS NULL;
```

## 🎯 完整修改记录

### 1. MtlItem 实体修改

```go
// 文件：backend/internal/model/entity/mtl_item_entity.go

type MtlItem struct {
    AccountBookEntity
    // 修改前：uniqueIndex导致软删除问题
    // Sku string `gorm:"column:sku;uniqueIndex:idx_tenant_account_sku,priority:3"`

    // 修改后：使用普通索引 + 数据库部分唯一索引
    Sku string `gorm:"column:sku;type:varchar(100);not null;index;comment:物料编码" json:"sku"`

    // 同时修正其他字段：
    // - 移除 sql.NullString，改用 *string
    // - 移除 sql.NullFloat64，改用 *float64
    // - 修正ENUM类型名：wms_item_status -> mtl_item_status
    Description  *string       `gorm:"column:description;type:text"`
    WeightKg     *float64      `gorm:"column:weight_kg;type:numeric(10,3)"`
    Status       MtlItemStatus `gorm:"column:status;type:mtl_item_status"`
    // ... 其他字段类似修改
}
```

### 2. MtlItemPackageUnit 实体修改

```go
// 文件：backend/internal/model/entity/mtl_item_package_unit_entity.go

type MtlItemPackageUnit struct {
    AccountBookEntity
    // 修改前：GORM复合唯一索引
    // ItemID   uint   `gorm:"uniqueIndex:idx_tenant_account_item_unit,priority:3"`
    // UnitName string `gorm:"uniqueIndex:idx_tenant_account_item_unit,priority:4"`

    // 修改后：普通索引 + 数据库部分唯一索引
    ItemID   uint   `gorm:"column:item_id;not null;index"`
    UnitName string `gorm:"column:unit_name;type:varchar(50);not null;index"`
}
```

### 3. 数据库初始化脚本更新

```go
// 文件：backend/pkg/database/init.go

// 在AutoMigrate中添加物料实体
err := db.AutoMigrate(
    // ... 其他实体
    &entity.MtlItem{},              // 新增
    &entity.MtlItemPackageUnit{},   // 新增
)

// 在部分唯一索引创建脚本中添加
partialIndexSQL := `
    -- mtl_item 部分唯一索引 (租户+账套+SKU，仅未删除记录)
    CREATE UNIQUE INDEX IF NOT EXISTS idx_mtl_item_active_tenant_account_sku
    ON mtl_item (tenant_id, account_book_id, sku)
    WHERE deleted_at IS NULL;

    -- mtl_item_package_unit 部分唯一索引 (租户+账套+物料+单位名，仅未删除记录)
    CREATE UNIQUE INDEX IF NOT EXISTS idx_mtl_item_package_unit_active_tenant_account_item_unit
    ON mtl_item_package_unit (tenant_id, account_book_id, item_id, unit_name)
    WHERE deleted_at IS NULL;
`
```

## 🔧 技术细节说明

### PostgreSQL 部分索引优势

1. **空间效率**: 只索引活跃记录，减少索引大小
2. **查询性能**: 提高针对活跃记录的查询速度
3. **唯一约束**: 完美解决软删除场景的唯一性问题
4. **维护简便**: 自动维护，无需手动干预

### 兼容性考虑

- ✅ **PostgreSQL**: 原生支持部分索引
- ⚠️ **MySQL**: 不支持部分索引，需要使用触发器或其他方案
- 当前项目使用 PostgreSQL，完美兼容

### 其他数据库的替代方案

```sql
-- MySQL替代方案1：在软删除时修改唯一字段
UPDATE mtl_item
SET sku = CONCAT(sku, '_deleted_', id),
    deleted_at = NOW()
WHERE id = ?;

-- MySQL替代方案2：使用复合唯一索引包含deleted_at
ALTER TABLE mtl_item
ADD UNIQUE KEY idx_sku_deleted (sku, deleted_at);
-- 但这种方案有局限性：已删除记录仍占用空间
```

## 📋 验证测试

### 测试场景 1：正常唯一约束

```sql
-- 成功：创建物料
INSERT INTO mtl_item (tenant_id, account_book_id, sku, name)
VALUES (1, 1, 'TEST001', '测试物料');

-- 失败：重复SKU
INSERT INTO mtl_item (tenant_id, account_book_id, sku, name)
VALUES (1, 1, 'TEST001', '重复物料');
-- ERROR: duplicate key violates unique constraint
```

### 测试场景 2：软删除后重用 SKU

```sql
-- 软删除原物料
UPDATE mtl_item SET deleted_at = NOW() WHERE sku = 'TEST001';

-- 成功：创建相同SKU的新物料
INSERT INTO mtl_item (tenant_id, account_book_id, sku, name)
VALUES (1, 1, 'TEST001', '新测试物料');
```

### 测试场景 3：多租户/多账套隔离

```sql
-- 租户1账套1的物料
INSERT INTO mtl_item (tenant_id, account_book_id, sku, name)
VALUES (1, 1, 'COMMON001', '物料A');

-- 租户1账套2的相同SKU物料 (允许)
INSERT INTO mtl_item (tenant_id, account_book_id, sku, name)
VALUES (1, 2, 'COMMON001', '物料B');

-- 租户2账套1的相同SKU物料 (允许)
INSERT INTO mtl_item (tenant_id, account_book_id, sku, name)
VALUES (2, 1, 'COMMON001', '物料C');
```

## 🎉 方案优势总结

1. ✅ **完美解决软删除唯一性问题**
2. ✅ **保持数据完整性和业务逻辑**
3. ✅ **符合现有项目架构标准**
4. ✅ **性能优化：只索引活跃数据**
5. ✅ **多租户多账套数据隔离**
6. ✅ **代码简洁：无需额外业务逻辑**
7. ✅ **自动维护：数据库层面自动处理**

这个解决方案是支持软删除系统中处理唯一约束的**最佳实践**。
